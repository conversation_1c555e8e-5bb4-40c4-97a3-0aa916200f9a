###########################################################
###################### General Setup ######################
###########################################################

### Paths #################################################

# Point to the path of your applications code on your host
APP_CODE_PATH_HOST=/var/www

# Point to where the `APP_CODE_PATH_HOST` should be in the container
APP_CODE_PATH_CONTAINER=/var/www

# You may add flags to the path `:cached`, `:delegated`. When using Docker Sync add `:nocopy`
APP_CODE_CONTAINER_FLAG=:cached

# Choose storage path on your machine. For all storage systems
DATA_PATH_HOST=/var/www/.storage/data

### Drivers ################################################

# All volumes driver
VOLUMES_DRIVER=local

# All Networks driver
NETWORKS_DRIVER=bridge

### Docker compose files ##################################

# Select which docker-compose files to include. If using docker-sync append `:docker-compose.sync.yml` at the end
COMPOSE_FILE=docker-compose.yml

# Change the separator from : to ; on Windows
COMPOSE_PATH_SEPARATOR=:

# Define the prefix of container names. This is useful if you have multiple projects that use laradock to have separate containers per project.
COMPOSE_PROJECT_NAME=higgses

### PHP Version ###########################################

# Select a PHP version of the Workspace and PHP-FPM containers (Does not apply to HHVM).
# Accepted values: 7.4 - 7.3 - 7.2 - 7.1 - 7.0 - 5.6
PHP_VERSION=7.4

### Phalcon Version ###########################################

# Select a Phalcon version of the Workspace and PHP-FPM containers (Does not apply to HHVM). Accepted values: 3.4.0+
PHALCON_VERSION=4.0.5

### PHP Interpreter #######################################

# Select the PHP Interpreter. Accepted values: hhvm - php-fpm
PHP_INTERPRETER=php-fpm

### Docker Host IP ########################################

# Enter your Docker Host IP (will be appended to /etc/hosts). Default is `*********`
DOCKER_HOST_IP=*********

### PHP DOWNGRADEOPENSSL TLS AND SECLEVEL #################

PHP_DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL=false

### Windows Path ##########################################

# A fix for Windows users, to ensure the application path works
COMPOSE_CONVERT_WINDOWS_PATHS=1

### Remote Interpreter ####################################

# Choose a Remote Interpreter entry matching name. Default is `laradock`
PHP_IDE_CONFIG=serverName=higgses

### Environment ###########################################

# If you need to change the sources (i.e. to China), set CHANGE_SOURCE to true
CHANGE_SOURCE=true
ORACLE_INSTANT_CLIENT_MIRROR=https://github.com/the-paulus/oracle-instantclient/raw/master/
ORACLE_INSTANT_CLIENT_ARCH=x86_64
ORACLE_INSTANT_CLIENT_MAJOR=18
ORACLE_INSTANT_CLIENT_MINOR=3
# Set CHANGE_SOURCE and UBUNTU_SOURCE option if you want to change the Ubuntu system sources.list file.
UBUNTU_SOURCE=tsinghua

### Install Oh My ZSH! ####################################

# If you want to use "Oh My ZSH!" with Laravel autocomplete plugin, set SHELL_OH_MY_ZSH to true.

SHELL_OH_MY_ZSH=false
SHELL_OH_MY_ZSH_AUTOSUGESTIONS=false
SHELL_OH_MY_ZSH_ALIASES=false

### NGINX #################################################

NGINX_HOST_HTTP_PORT=80
NGINX_HOST_HTTPS_PORT=443
NGINX_HOST_LOG_PATH=./logs/nginx/
NGINX_SITES_PATH=./nginx/sites/
NGINX_PHP_UPSTREAM_CONTAINER=php-fpm
NGINX_PHP_UPSTREAM_PORT=9000
NGINX_SSL_PATH=./nginx/ssl/


### PHP_FPM ###############################################

PHP_FPM_BASE_IMAGE_TAG_PREFIX=latest
PHP_FPM_INSTALL_BCMATH=true
PHP_FPM_INSTALL_MYSQLI=true
PHP_FPM_INSTALL_INTL=true
PHP_FPM_INSTALL_IMAGEMAGICK=true
PHP_FPM_INSTALL_OPCACHE=true
PHP_FPM_INSTALL_IMAGE_OPTIMIZERS=true
PHP_FPM_INSTALL_PHPREDIS=true
PHP_FPM_INSTALL_MEMCACHED=false
PHP_FPM_INSTALL_BZ2=false
PHP_FPM_INSTALL_GMP=true
PHP_FPM_INSTALL_XDEBUG=false
PHP_FPM_INSTALL_PCOV=false
PHP_FPM_INSTALL_XHPROF=false
PHP_FPM_INSTALL_PHPDBG=false
PHP_FPM_INSTALL_SMB=false
PHP_FPM_INSTALL_IMAP=false
PHP_FPM_INSTALL_MONGO=false
PHP_FPM_INSTALL_AMQP=false
PHP_FPM_INSTALL_CASSANDRA=false
PHP_FPM_INSTALL_GEARMAN=false
PHP_FPM_INSTALL_MSSQL=false
PHP_FPM_INSTALL_SSH2=false
PHP_FPM_INSTALL_SOAP=false
PHP_FPM_INSTALL_XSL=false
PHP_FPM_INSTALL_EXIF=false
PHP_FPM_INSTALL_AEROSPIKE=false
PHP_FPM_INSTALL_OCI8=false
PHP_FPM_INSTALL_PGSQL=false
PHP_FPM_INSTALL_GHOSTSCRIPT=false
PHP_FPM_INSTALL_LDAP=false
PHP_FPM_INSTALL_PHALCON=false
PHP_FPM_INSTALL_SWOOLE=true
PHP_FPM_INSTALL_TAINT=false
PHP_FPM_INSTALL_PG_CLIENT=false
PHP_FPM_INSTALL_POSTGIS=false
PHP_FPM_INSTALL_PCNTL=false
PHP_FPM_INSTALL_CALENDAR=false
PHP_FPM_INSTALL_FAKETIME=false
PHP_FPM_INSTALL_IONCUBE=false
PHP_FPM_INSTALL_RDKAFKA=false
PHP_FPM_INSTALL_GETTEXT=false
PHP_FPM_INSTALL_XMLRPC=false
PHP_FPM_FAKETIME=-0
PHP_FPM_INSTALL_APCU=false
PHP_FPM_INSTALL_CACHETOOL=false
PHP_FPM_INSTALL_YAML=false
PHP_FPM_INSTALL_ADDITIONAL_LOCALES=false
PHP_FPM_INSTALL_MYSQL_CLIENT=false
PHP_FPM_INSTALL_PING=false
PHP_FPM_INSTALL_SSHPASS=false
PHP_FPM_INSTALL_MAILPARSE=false
PHP_FPM_INSTALL_WKHTMLTOPDF=false
PHP_FPM_FFMPEG=false
PHP_FPM_AUDIOWAVEFORM=false
PHP_FPM_ADDITIONAL_LOCALES="en_US.UTF-8 es_ES.UTF-8 fr_FR.UTF-8"
PHP_FPM_DEFAULT_LOCALE=POSIX

PHP_FPM_PUID=1000
PHP_FPM_PGID=1000

PHP_FPM_INSTALL_ENCHANT=false
PHP_FPM_INSTALL_GNUPG=false
PHP_FPM_INSTALL_ZMQ=false
PHP_FPM_INSTALL_XLSWRITER=false
PHP_FPM_INSTALL_PHPDECIMAL=false
PHP_FPM_INSTALL_ZOOKEEPER=false
PHP_FPM_INSTALL_SSDB=false
PHP_FPM_INSTALL_TRADER=false
PHP_FPM_IMAGEMAGICK_VERSION=latest
PHP_FPM_NEW_RELIC=false
PHP_FPM_NEW_RELIC_KEY=0000
PHP_FPM_NEW_RELIC_APP_NAME=app_name
PHP_FPM_INSTALL_DOCKER_CLIENT=false
PHP_FPM_XDEBUG_PORT=9003

### PHP_WORKER ############################################

### PHP_WORKER ############################################

PHP_WORKER_INSTALL_BZ2=false
PHP_WORKER_INSTALL_GD=true
PHP_WORKER_INSTALL_XLSWRITER=false
PHP_WORKER_INSTALL_IMAGEMAGICK=false
PHP_WORKER_IMAGEMAGICK_VERSION=latest
PHP_WORKER_INSTALL_GMP=false
PHP_WORKER_INSTALL_GNUPG=false
PHP_WORKER_INSTALL_LDAP=false
PHP_WORKER_INSTALL_PGSQL=false
PHP_WORKER_INSTALL_MONGO=false
PHP_WORKER_INSTALL_BCMATH=true
PHP_WORKER_INSTALL_MEMCACHED=false
# PHP_WORKER_INSTALL_OCI8 Does not work in php5.6 version
PHP_WORKER_INSTALL_OCI8=false
PHP_WORKER_INSTALL_MSSQL=false
PHP_WORKER_INSTALL_PHALCON=false
PHP_WORKER_INSTALL_APCU=false
PHP_WORKER_INSTALL_SOAP=false
PHP_WORKER_INSTALL_ZIP_ARCHIVE=true
PHP_WORKER_INSTALL_MYSQL_CLIENT=false
PHP_WORKER_INSTALL_AMQP=true
PHP_WORKER_INSTALL_GHOSTSCRIPT=false
PHP_WORKER_INSTALL_SWOOLE=true
PHP_WORKER_INSTALL_TAINT=false
PHP_WORKER_INSTALL_FFMPEG=false
PHP_WORKER_INSTALL_AUDIOWAVEFORM=false
PHP_WORKER_INSTALL_CASSANDRA=false
PHP_WORKER_INSTALL_GEARMAN=false
PHP_WORKER_INSTALL_REDIS=true
PHP_WORKER_INSTALL_IMAP=true
PHP_WORKER_INSTALL_XMLRPC=false
PHP_WORKER_INSTALL_SSDB=false
PHP_WORKER_INSTALL_EVENT=false
PHP_WORKER_INSTALL_INTL=true
PHP_WORKER_INSTALL_POPPLER_UTILS=false

PHP_WORKER_PUID=1000
PHP_WORKER_PGID=1000

PHP_WORKER_WEBSOCKET_PORT=1215
PHP_FPM_WKHTMLTOPDF_VERSION=********-3
PHP_FPM_INSTALL_EVENT=false
PHP_FPM_INSTALL_DNSUTILS=true
PHP_FPM_INSTALL_POPPLER_UTILS=false
# Since OpenSSL 3 some ciphers are not available
PHP_LEGACY_OPENSSL=false
# Accepted values: 1.2 - 1.1 - 1.0
PHP_DOWNGRADE_OPENSSL_TLS_VERSION=1.2


###########################################################
################ Containers Customization #################
###########################################################

### WORKSPACE #############################################

WORKSPACE_BASE_IMAGE_TAG_PREFIX=latest
WORKSPACE_COMPOSER_GLOBAL_INSTALL=true
WORKSPACE_COMPOSER_AUTH=false
WORKSPACE_COMPOSER_REPO_PACKAGIST=https://mirrors.aliyun.com/composer/
WORKSPACE_NVM_NODEJS_ORG_MIRROR=https://mirrors.aliyun.com/nodejs-release/
WORKSPACE_INSTALL_NODE=true
WORKSPACE_NODE_VERSION=node
WORKSPACE_NPM_REGISTRY=https://registry.npmmirror.com/
WORKSPACE_INSTALL_PNPM=false
WORKSPACE_INSTALL_YARN=true
WORKSPACE_YARN_VERSION=latest
WORKSPACE_INSTALL_NPM_GULP=false
WORKSPACE_INSTALL_NPM_BOWER=false
WORKSPACE_INSTALL_NPM_VUE_CLI=true
WORKSPACE_INSTALL_NPM_ANGULAR_CLI=false
WORKSPACE_INSTALL_PHPREDIS=true
WORKSPACE_INSTALL_WORKSPACE_SSH=false
WORKSPACE_INSTALL_SUBVERSION=false
WORKSPACE_INSTALL_BZ2=false
WORKSPACE_INSTALL_GMP=true
WORKSPACE_INSTALL_XDEBUG=false
WORKSPACE_INSTALL_PCOV=false
WORKSPACE_INSTALL_PHPDBG=false
WORKSPACE_INSTALL_SSH2=false
WORKSPACE_INSTALL_LDAP=false
WORKSPACE_INSTALL_SOAP=false
WORKSPACE_INSTALL_XSL=false
WORKSPACE_INSTALL_SMB=false
WORKSPACE_INSTALL_IMAP=false
WORKSPACE_INSTALL_MONGO=false
WORKSPACE_INSTALL_AMQP=false
WORKSPACE_INSTALL_CASSANDRA=false
WORKSPACE_INSTALL_GEARMAN=false
WORKSPACE_INSTALL_MSSQL=false
WORKSPACE_INSTALL_DRUSH=false
WORKSPACE_DRUSH_VERSION=8.1.17
WORKSPACE_INSTALL_DRUPAL_CONSOLE=false
WORKSPACE_INSTALL_WP_CLI=false
WORKSPACE_INSTALL_AEROSPIKE=false
WORKSPACE_INSTALL_OCI8=false
WORKSPACE_INSTALL_V8JS=false
WORKSPACE_INSTALL_LARAVEL_ENVOY=false
WORKSPACE_INSTALL_LARAVEL_INSTALLER=false
WORKSPACE_INSTALL_DEPLOYER=false
WORKSPACE_INSTALL_PRESTISSIMO=false
WORKSPACE_INSTALL_LINUXBREW=false
WORKSPACE_INSTALL_MC=false
WORKSPACE_INSTALL_SYMFONY=false
WORKSPACE_INSTALL_PYTHON=false
WORKSPACE_INSTALL_PYTHON3=false
WORKSPACE_INSTALL_POWERLINE=false
WORKSPACE_INSTALL_SUPERVISOR=false
WORKSPACE_INSTALL_IMAGE_OPTIMIZERS=false
WORKSPACE_INSTALL_IMAGEMAGICK=false
WORKSPACE_INSTALL_TERRAFORM=false
WORKSPACE_INSTALL_DUSK_DEPS=false
WORKSPACE_INSTALL_PG_CLIENT=false
WORKSPACE_INSTALL_PHALCON=false
WORKSPACE_INSTALL_SWOOLE=true
WORKSPACE_INSTALL_TAINT=false
WORKSPACE_INSTALL_LIBPNG=false
WORKSPACE_INSTALL_GRAPHVIZ=false
WORKSPACE_INSTALL_IONCUBE=false
WORKSPACE_INSTALL_MYSQL_CLIENT=false
WORKSPACE_INSTALL_PING=false
WORKSPACE_INSTALL_SSHPASS=false
WORKSPACE_INSTALL_INOTIFY=false
WORKSPACE_INSTALL_FSWATCH=false
WORKSPACE_INSTALL_YAML=false
WORKSPACE_INSTALL_RDKAFKA=false
WORKSPACE_INSTALL_MAILPARSE=false
WORKSPACE_INSTALL_XMLRPC=false
WORKSPACE_PUID=1000
WORKSPACE_PGID=1000
WORKSPACE_CHROME_DRIVER_VERSION=2.42
WORKSPACE_TIMEZONE=PRC
WORKSPACE_SSH_PORT=2222
WORKSPACE_INSTALL_FFMPEG=false
WORKSPACE_INSTALL_AUDIOWAVEFORM=false
WORKSPACE_INSTALL_WKHTMLTOPDF=false
WORKSPACE_INSTALL_GNU_PARALLEL=false
WORKSPACE_INSTALL_AST=true
WORKSPACE_AST_VERSION=1.0.3
WORKSPACE_BROWSERSYNC_HOST_PORT=3000
WORKSPACE_BROWSERSYNC_UI_HOST_PORT=3001
WORKSPACE_VUE_CLI_SERVE_HOST_PORT=8080
WORKSPACE_VUE_CLI_UI_HOST_PORT=8001
WORKSPACE_ANGULAR_CLI_SERVE_HOST_PORT=4200
WORKSPACE_INSTALL_GIT_PROMPT=false
WORKSPACE_INSTALL_LNAV=false
WORKSPACE_INSTALL_PROTOC=false
WORKSPACE_PROTOC_VERSION=latest
WORKSPACE_INSTALL_GNUPG=false
WORKSPACE_INSTALL_ZMQ=false
WORKSPACE_NPM_FETCH_RETRIES=2
WORKSPACE_NPM_FETCH_RETRY_FACTOR=10
WORKSPACE_NPM_FETCH_RETRY_MINTIMEOUT=10000
WORKSPACE_NPM_FETCH_RETRY_MAXTIMEOUT=60000
WORKSPACE_INSTALL_PNPM=false
WORKSPACE_INSTALL_YARN=true
WORKSPACE_YARN_VERSION=latest
WORKSPACE_INSTALL_NPM_GULP=true
WORKSPACE_INSTALL_NPM_BOWER=false
WORKSPACE_INSTALL_NPM_VUE_CLI=true
WORKSPACE_INSTALL_NPM_ANGULAR_CLI=false
WORKSPACE_INSTALL_PHPREDIS=true
WORKSPACE_INSTALL_WORKSPACE_SSH=false
WORKSPACE_INSTALL_SUBVERSION=false
WORKSPACE_INSTALL_BZ2=false
WORKSPACE_INSTALL_GMP=false
WORKSPACE_COMPOSER_VERSION=2
WORKSPACE_INSTALL_XLSWRITER=false
WORKSPACE_IMAGEMAGICK_VERSION=latest
WORKSPACE_INSTALL_PHPDECIMAL=false
WORKSPACE_INSTALL_ZOOKEEPER=false
WORKSPACE_INSTALL_SSDB=false
WORKSPACE_INSTALL_TRADER=false
WORKSPACE_INSTALL_DOCKER_CLIENT=false
WORKSPACE_INSTALL_MEMCACHED=true
WORKSPACE_VITE_PORT=5173
WORKSPACE_INSTALL_JDK=true
WORKSPACE_INSTALL_GITHUB_CLI=false
WORKSPACE_INSTALL_EVENT=false
WORKSPACE_INSTALL_DNSUTILS=true
WORKSPACE_XDEBUG_PORT=9000
WORKSPACE_INSTALL_NPM_CHECK_UPDATES_CLI=false
WORKSPACE_COMPOSER_AUTH_JSON=false
WORKSPACE_INSTALL_APCU=false
WORKSPACE_WKHTMLTOPDF_VERSION=0.12.6-1
WORKSPACE_INSTALL_POPPLER_UTILS=false



### VARNISH ###############################################

VARNISH_CONFIG=/etc/varnish/default.vcl
VARNISH_PORT=6081
VARNISH_BACKEND_PORT=81
VARNISHD_PARAMS="-p default_ttl=3600 -p default_grace=3600"


### BLACKFIRE #############################################

# Create an account on blackfire.io. Don't enable blackfire and xDebug at the same time. # visit https://blackfire.io/docs/24-days/06-installation#install-probe-debian for more info.
INSTALL_BLACKFIRE=false
BLACKFIRE_CLIENT_ID="<client_id>"
BLACKFIRE_CLIENT_TOKEN="<client_token>"
BLACKFIRE_SERVER_ID="<server_id>"
BLACKFIRE_SERVER_TOKEN="<server_token>"

### MYSQL #################################################

MYSQL_VERSION=8.0
MYSQL_DATABASE=default
MYSQL_USER=default
MYSQL_PASSWORD=secret
MYSQL_PORT=4406
MYSQL_ROOT_PASSWORD=root
MYSQL_ENTRYPOINT_INITDB=./mysql/docker-entrypoint-initdb.d

### REDIS #################################################

REDIS_PORT=6379
REDIS_PASSWORD=Higs@Redis508

### REDIS CLUSTER #########################################

REDIS_CLUSTER_PORT_RANGE=7000-7005

### RABBITMQ ##############################################

RABBITMQ_NODE_HOST_PORT=5672
RABBITMQ_MANAGEMENT_HTTP_HOST_PORT=15672
RABBITMQ_MANAGEMENT_HTTPS_HOST_PORT=15671
RABBITMQ_WEB_STOMP_HOST_PORT=15674
RABBITMQ_STOMP_HOST_PORT=61613
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=Higs@Redis508

## 容器Registery密码：higgses@601a


### MYSQL #################################################

MYSQL_VERSION=latest
MYSQL_DATABASE=default
MYSQL_USER=default
MYSQL_PASSWORD=secret
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=root
MYSQL_ENTRYPOINT_INITDB=./mysql/docker-entrypoint-initdb.d



### POSTGRES ##############################################

POSTGRES_VERSION=alpine
POSTGRES_CLIENT_VERSION=15
POSTGRES_DB=default
POSTGRES_USER=default
POSTGRES_PASSWORD=secret
POSTGRES_PORT=5432
POSTGRES_ENTRYPOINT_INITDB=./postgres/docker-entrypoint-initdb.d

### KEEPALIVED ##############################################

KEEPALIVED_PATH=./keepalived/
export default function (apis) {
  return {

    data() {
      return {
        searchQuery: {
          page: 1,
          per_page: 15
        },
        loading: false,
        totalData: 0,
        list: [],
        total_count: {
          total: 0,
          total_closed: 0,
          total_complete: 0,
          total_failed: 0,
          total_pending: 0,
          total_fapiaos: 0
        }
      }
    },
    methods: {
      handleFilter() {
        this.searchQuery.page = 1
        this.getList()
      },
      handleReset() {
        this.searchQuery = {
          page: 1,
          per_page: 15
        }
        this.date = []
        this.getList()
      },

      handleSizeChange(val) {
        this.searchQuery.per_page = val
        this.getList()
      },
      handleCurrentChange(val) {
        this.searchQuery.page = val
        this.getList()
      },
      async getList() {
        this.loading = true
        // eslint-disable-next-line object-curly-spacing
        const { data: { data, meta } } = await apis(this.searchQuery)
        this.loading = false
        this.$nextTick(() => {
          this.list = data
          this.totalData = meta?.pagination?.total
          this.total_count = meta
        })
      }

    }

  }
}

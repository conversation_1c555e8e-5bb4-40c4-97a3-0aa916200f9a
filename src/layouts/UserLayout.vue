<template>
  <div id="userLayout" :class="['user-layout-wrapper', isMobile && 'mobile']">
    <div class="container">
      <div class="content-layout">
        <div class="left-panel">
          <div class="logo-container">
            <img src="@/assets/images/logo.png" class="logo" alt="logo">
            <span class="title">CC智慧出行管理系统</span>
            <div class="subtitle">— www.cczhaoche.com —</div>
          </div>
          <div class="platform-title">
            智慧出行网络平台
          </div>
          <div class="platform-desc">
            高效、便捷、安全的商户管理系统
          </div>
        </div>
        <div class="right-panel">
          <div class="welcome-title">
            欢迎登录
          </div>
          <div class="welcome-desc">
            CC智慧出行网络平台
          </div>

          <router-view />

          <div class="footer">
            <div class="copyright">
              登录即表示您同意 <a href="_self">服务条款</a> 和 <a href="_self">隐私政策</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { deviceMixin } from '@/store/device-mixin'
import SelectLang from '@/components/SelectLang'

export default {
  name: 'UserLayout',
  components: {
    SelectLang
  },
  mixins: [deviceMixin],
  mounted () {
    document.body.classList.add('userLayout')
  },
  beforeDestroy () {
    document.body.classList.remove('userLayout')
  }
}
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  height: 100%;
  background: #f5f7f9;

  &.mobile {
    .container {
      .content-layout {
        flex-direction: column;
      }
    }
  }

  .container {
    width: 100%;
    min-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;

    .content-layout {
      display: flex;
      width: 1000px;
      max-width: 100%;
      min-height: 500px;
      overflow: hidden;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      background: #fff;

      .left-panel {
        width: 40%;
        background: #0052CC;
        color: white;
        padding: 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        position: relative;

        .logo-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 120px;

          .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
          }

          .title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
          }

          .subtitle {
            font-size: 12px;
            opacity: 0.8;
          }
        }

        .platform-title {
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 16px;
        }

        .platform-desc {
          font-size: 16px;
          opacity: 0.8;
        }
      }

      .right-panel {
        width: 60%;
        padding: 40px 50px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        .welcome-title {
          font-size: 24px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .welcome-desc {
          font-size: 14px;
          color: #666;
          margin-bottom: 40px;
        }

        .footer {
          margin-top: auto;
          text-align: center;

          .copyright {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);

            a {
              color: #0052CC;
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
}
</style>

import Vue from 'vue'

import 'normalize.css/normalize.css'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
import '@/permission' // permission control
import '@/styles/index.scss' // global css
import Tablestatus from '@/components/Tablestatus'
import OrderRefund from '@/components/OrderRefund'
import App from './App'
import store from './store'
import router from './router'
import './styles/theme/index.css' // 主题
import './styles/base.scss'
import '@/icons' // icon
import ImgUpload from '@/components/ImgUpload'
Vue.component('ImgUpload', ImgUpload)
Vue.component('Tablestatus', Tablestatus)
Vue.component('OrderRefund', OrderRefund)

window._AMapSecurityConfig = {
  securityJsCode: '8e41824d9d53ba41aeaa5033857279a9' //*  安全密钥
}

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

// set ElementUI lang to EN
Vue.use(ElementUI, { zhLocale })
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})

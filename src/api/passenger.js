import request from '@/utils/request'

// GET
// /admin/passengers 获取乘客列表
export function getPassengers(params) {
  return request({
    url: `/admin/passengers`,
    method: 'get',
    params
  })
}

// GET
// /admin/passengers/{passenger_id} 获取乘客详细信息
export function getPassengerDetail(passenger_id) {
  return request({
    url: `/admin/passengers/${passenger_id}`,
    method: 'get'
  })
}

// PUT
// /admin/passengers/{passenger_id}/freeze 冻结/恢复乘客账号
export function toggleFreeze(passenger_id, data) {
  return request({
    url: `/admin/passengers/${passenger_id}/freeze`,
    method: 'put',
    data
  })
}

// /admin/passenger_wallets 获取乘客钱包交易记录
export function getPassengerWallets(params) {
  return request({
    url: `/admin/passenger_wallets`,
    method: 'get',
    params
  })
}

// /admin/passenger_wallets/export_excels 导出乘客钱包交易记录
export function exportPassengerWallets(params) {
  return request({
    url: `/admin/passenger_wallets/export_excels`,
    method: 'post',
    params
  })
}

// /admin/passenger_wallets/{passenger_wallet_id}/{action} 批准/拒绝乘客钱包操作
export function handlePassengerWalletAction(passenger_wallet_id, action, data) {
  return request({
    url: `/admin/passenger_wallets/${passenger_wallet_id}/actions/${action}`,
    method: 'post',
    data
  })
}

// PUT
// /admin/passengers/{passenger_id} 更新乘客信息
export function updatePassenger(passenger_id, data) {
  return request({
    url: `/admin/passengers/${passenger_id}`,
    method: 'put',
    data
  })
}

// PUT
// /admin/passengers/{passenger_id}/verify/approve 审核通过乘客认证
export function approvePassengerVerification(passenger_id, data) {
  return request({
    url: `/admin/passengers/${passenger_id}/verify/approve`,
    method: 'put',
    data
  })
}

// PUT
// /admin/passengers/{passenger_id}/verify/reject 拒绝乘客认证
export function rejectPassengerVerification(passenger_id, data) {
  return request({
    url: `/admin/passengers/${passenger_id}/verify/reject`,
    method: 'put',
    data
  })
}

// 新增：获取钱包统计数据
export function getPassengerWalletStats() {
  return request({
    url: '/admin/passenger_wallets',
    method: 'get',
    params: {
      type: 4, // 提现类型
      size: 1, // 只需要统计数据
      page: 1,
      stats_only: 'true' // 添加标记，表示只获取统计数据，不应用其他筛选条件
    }
  })
}


import request from '@/utils/request'

const api = {
  List: '/account-bin/api/admin/saas/merchants',
  Detail: '/account-bin/api/admin/saas/merchants',
  Create: '/account-bin/api/admin/saas/merchants',
  Update: '/account-bin/api/admin/saas/merchants',
  Delete: '/account-bin/api/admin/saas/merchants',
  ChangeStatus: '/account-bin/api/admin/saas/merchants',
  ChangePassword: '/account-bin/api/admin/saas/merchants',
  GetPermissions: '/account-bin/api/admin/saas/merchants',
  UpdatePermissions: '/account-bin/api/admin/saas/merchants',
  GetBranchTree: '/account-bin/api/admin/saas/merchants'
}

/**
 * 获取商户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMerchantList (params) {
  return request({
    url: api.List,
    method: 'get',
    params
  })
}

/**
 * 获取商户详情
 * @param {Number} id - 商户ID
 * @returns {Promise}
 */
export function getMerchantDetail (id) {
  return request({
    url: `${api.Detail}/${id}`,
    method: 'get'
  })
}

/**
 * 创建商户
 * @param {Object} data - 商户数据
 * @returns {Promise}
 */
export function createMerchant (data) {
  return request({
    url: api.Create,
    method: 'post',
    data
  })
}

/**
 * 更新商户信息
 * @param {Number} id - 商户ID
 * @param {Object} data - 商户数据
 * @returns {Promise}
 */
export function updateMerchant (id, data) {
  return request({
    url: `${api.Update}/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除商户
 * @param {Number} id - 商户ID
 * @returns {Promise}
 */
export function deleteMerchant (id) {
  return request({
    url: `${api.Delete}/${id}`,
    method: 'delete'
  })
}

/**
 * 修改商户状态（启用/禁用）
 * @param {Number} id - 商户ID
 * @param {Number} status - 状态值：0-正常，1-冻结
 * @returns {Promise}
 */
export function changeMerchantStatus (id, status) {
  return request({
    url: `${api.ChangeStatus}/${id}/toggle-freeze`,
    method: 'post',
    data: { status }
  })
}

/**
 * 获取商户权限
 * @param {Number} id - 商户ID
 * @returns {Promise}
 */
export function getMerchantPermissions (id) {
  return request({
    url: `${api.GetPermissions}/${id}/permissions`,
    method: 'get'
  })
}

/**
 * 更新商户权限
 * @param {Number} id - 商户ID
 * @param {Object} data - 权限数据
 * @returns {Promise}
 */
export function updateMerchantPermissions (id, data) {
  return request({
    url: `${api.UpdatePermissions}/${id}/update-permissions`,
    method: 'post',
    data
  })
}

/**
 * 修改商户密码
 * @param {Number} id - 商户ID
 * @param {Object} data - 密码数据
 * @returns {Promise}
 */
export function changeMerchantPassword (id, data) {
  return request({
    url: `${api.ChangePassword}/${id}/change-password`,
    method: 'post',
    data
  })
}

/**
 * 获取商户下分台树结构
 * @param {Number} id - 商户ID
 * @returns {Promise}
 */
export function getMerchantBranchTree (id) {
  return request({
    url: `${api.GetBranchTree}/${id}`,
    method: 'get'
  })
}

/**
 * 获取业务类型列表（静态数据）
 * @returns {Promise}
 */
export function getBusinessTypes () {
  // 返回静态业务类型数据
  const businessTypes = [
    { key: 'passenger', label: '客运业务' },
    { key: 'logistics', label: '物流业务' },
    { key: 'customized', label: '定制业务' },
    { key: 'tourism', label: '旅游业务' }
  ]
  return Promise.resolve(businessTypes)
}

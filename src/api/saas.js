import request from '@/utils/request'

/**
 * SaaS平台API接口
 */

const saasApi = {
  // 用户认证相关
  login: `/account-bin/api/admin/saas/login`,
  logout: `/account-bin/api/admin/saas/logout`,
  me: `/account-bin/api/admin/saas/me`,
  refreshToken: `/account-bin/api/admin/saas/refresh`,
  verifyToken: `/account-bin/api/admin/saas/verify`
}

/**
 * 平台账号登录
 * @param {Object} data - 登录参数
 * @returns {Promise}
 */
export function login (data) {
  console.log('准备发送登录请求到:', saasApi.login)
  return request({
    url: saasApi.login,
    method: 'post',
    data
  })
}

/**
 * 平台账号登出
 * @returns {Promise}
 */
export function logout () {
  console.log('准备发送登出请求到:', saasApi.logout)
  return request({
    url: saasApi.logout,
    method: 'post'
  })
}

/**
 * 获取当前登录账号信息
 * @returns {Promise}
 */
export function getCurrentUser () {
  console.log('准备发送获取用户信息请求到:', saasApi.me)
  return request({
    url: saasApi.me,
    method: 'get'
  })
}

/**
 * 刷新JWT Token
 * @returns {Promise}
 */
export function refreshToken () {
  return request({
    url: saasApi.refreshToken,
    method: 'post'
  })
}

/**
 * 验证JWT Token
 * @returns {Promise}
 */
export function verifyToken () {
  return request({
    url: saasApi.verifyToken,
    method: 'get'
  })
}

/**
 * 获取商户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMerchantList (params) {
  return request({
    url: saasApi.merchantList,
    method: 'get',
    params
  })
}

/**
 * 获取商户详情
 * @param {String|Number} id - 商户ID
 * @returns {Promise}
 */
export function getMerchantDetail (id) {
  return request({
    url: saasApi.merchantDetail.replace('{id}', id),
    method: 'get'
  })
}

/**
 * 切换商户冻结状态
 * @param {String|Number} id - 商户ID
 * @returns {Promise}
 */
export function toggleMerchantFreeze (id) {
  return request({
    url: saasApi.toggleFreeze.replace('{id}', id),
    method: 'post'
  })
}

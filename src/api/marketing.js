import request from '@/utils/request'

export function coupons(params) {
  return request({
    url: `/admin/coupons`,
    method: 'get',
    params
  })
}
export function delCoupon(coupon) {
  return request({
    url: `/admin/coupons/${coupon}`,
    method: 'delete'
  })
}
export function addCoupon(data) {
  return request({
    url: `/admin/coupons`,
    method: 'post',
    data
  })
}

export function couponsUpdate(data) {
  return request({
    url: `/admin/coupons/${data.coupon_id}`,
    method: 'put',
    data
  })
}
export function promotionsUpdate(data) {
  return request({
    url: `/admin/promotions/${data.market_activity_id}`,
    method: 'put',
    data
  })
}

export function promotions(params) {
  return request({
    url: `/admin/promotions`,
    method: 'get',
    params
  })
}
export function addPromotions(data) {
  return request({
    url: `/admin/promotions`,
    method: 'post',
    data
  })
}

export function delPromotions(promotion) {
  return request({
    url: `/admin/promotions/${promotion}`,
    method: 'delete'
  })
}

export function valCoupons(params) {
  return request({
    url: `/admin/promotion/coupons`,
    method: 'get',
    params
  })
}

export function validCoupons(params) {
  return request({
    url: `/admin/referral/coupons`,
    method: 'get',
    params
  })
}

export function couponsRecord(params) {
  return request({
    url: `/admin/promotions/${params.promotion}/coupons`,
    method: 'get',
    params
  })
}

export function delLogs(params) {
  return request({
    url: `/admin/promotions/${params.promotion}/coupons/${params.coupon}`,
    method: 'delete'
  })
}

export function provideCoupons(data) {
  return request({
    url: `/admin/promotions/${data.promotion}/coupons`,
    method: 'post',
    data
  })
}

export function referralSetups(params) {
  return request({
    url: `/admin/referral/setups`,
    method: 'get',
    params
  })
}

export function putReferralSetups(data) {
  return request({
    url: `/admin/referral/setups/${data?.id || 0}`,
    method: 'put',
    data
  })
}

export function trackings(params) {
  return request({
    url: `/admin/referral/trackings`,
    method: 'get',
    params
  })
}


export function coupon_usage_overview(params) {
  return request({
    url: `/admin/coupons/${params.coupon}/usage_overview`,
    method: 'get',
    params
  })
}

export function coupon_usage_excel(params) {
  return request({
    url: `/admin/coupons/${params.coupon}/usage_excel`,
    method: 'get',
    params
  })
}
export function coupon_activities(params) {
  return request({
    url: `/admin/coupons/${params}/activities`,
    method: 'get',
  })
}

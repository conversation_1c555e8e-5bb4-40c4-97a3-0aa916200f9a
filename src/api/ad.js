import request from '@/utils/request'

// 获取广告列表
export function getAdList(params) {
  return request({
    url: '/admin/ads',
    method: 'get',
    params
  })
}

// 添加广告
export function createAd(data) {
  return request({
    url: '/admin/ads',
    method: 'post',
    data
  })
}

// 更新广告
export function updateAd(id, data) {
  return request({
    url: `/admin/ads/${id}`,
    method: 'put',
    data
  })
}

// 删除广告
export function deleteAd(id) {
  return request({
    url: `/admin/ads/${id}`,
    method: 'delete'
  })
}

// 上架广告
export function upAd(id) {
  return request({
    url: `/admin/ads/${id}/up`,
    method: 'put'
  })
}

// 批量上架广告
export function batchUpAds(ids) {
  return request({
    url: '/admin/ads/up_batch',
    method: 'put',
    data: { ids }
  })
}

// 下架广告
export function downAd(id) {
  return request({
    url: `/admin/ads/${id}/down`,
    method: 'put'
  })
}

// 批量下架广告
export function batchDownAds(ids) {
  return request({
    url: '/admin/ads/down_batch',
    method: 'put',
    data: { ids }
  })
}

// 批量删除广告
export function batchDeleteAds(ids) {
  return request({
    url: '/admin/ads/batch_delete',
    method: 'delete',
    data: { ids }
  })
}

// /admin/lines
export function fetchLines(params) {
  return request({
    url: '/admin/lines',
    method: 'get',
    params
  })
}

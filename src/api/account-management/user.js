import { createServiceRequest } from '@/utils/request'

// 创建account服务的请求实例
const accountService = createServiceRequest('account-bin')

export function login(data) {
  return accountService({
    url: '/api/admin/merchant/login',
    method: 'post',
    data
  })
}

export function logout() {
  return accountService({
    url: '/api/admin/merchant/logout',
    method: 'post'
  })
}

// 获取当前登录用户的个人资料
export function getProfiles() {
  return accountService({
    url: '/api/admin/profiles',
    method: 'get'
  })
}

export function getAccountPermissionRules() {
  return accountService({
    url: '/api/admin/rules',
    method: 'get'
  })
}

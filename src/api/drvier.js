import { createServiceRequest } from '@/utils/request'

// 创建account服务的请求实例
const accountService = createServiceRequest('account-bin')

// GET
// /admin/driver_groups 分页查询
export function getDriverGroups(params) {
  return accountService({
    url: '/api/admin/driver_groups',
    method: 'get',
    params
  })
}

// GET
// /admin/driver_groups 查询所有司机组
export function getAllDriverGroups() {
  return accountService({
    url: '/api/admin/driver_groups',
    method: 'get'
  })
}

// POST
// /admin/driver_groups 创建
export function createDriverGroup(data) {
  return accountService({
    url: '/api/admin/driver_groups',
    method: 'post',
    data
  })
}

// PUT
// /admin/driver_groups/{id} 修改
export function updateDriverGroup(id, data) {
  return accountService({
    url: `/api/admin/driver_groups/${id}`,
    method: 'put',
    data
  })
}

// DELETE
// /admin/driver_groups/{id} 删除
export function deleteDriverGroup(id) {
  return accountService({
    url: `/api/admin/driver_groups/${id}`,
    method: 'delete'
  })
}

// DELETE
// /admin/driver_groups/batch_delete 批量删除
export function deleteDriverGroupBatch(data) {
  return accountService({
    url: `/api/admin/driver_groups/batch_delete`,
    method: 'delete',
    data
  })
}

// GET
// /admin/drivers 获取司机列表
export function getDrivers(params, type) {
  return accountService({
    url: `/api/admin/drivers/type/${type}`,
    method: 'get',
    params
  })
}

// GET
// /admin/drivers/frozen 获取已冻结司机列表
export function getFrozenDrivers(params, type) {
  return accountService({
    url: `/api/admin/drivers/frozen/type/${type}`,
    method: 'get',
    params
  })
}

// POST
// /admin/drivers 添加司机
export function addDriver(data) {
  return accountService({
    url: '/api/admin/drivers',
    method: 'post',
    data
  })
}

// PUT
// /admin/drivers/{id} 更新司机信息
export function updateDriver(id, data) {
  return accountService({
    url: `/api/admin/drivers/${id}`,
    method: 'put',
    data
  })
}

// DELETE
// /admin/drivers/{driver_id} 冻结司机
// export function freezeDriver(driver_id) {
//   return request({
//     url: `/admin/drivers/${driver_id}`,
//     method: 'delete'
//   })
// }

// GET
// /admin/drivers/{driver_id} 获取司机详细信息
export function getDriverDetail(driver_id) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}`,
    method: 'get'
  })
}

// PUT
// /admin/drivers/{driver_id}/states/{state} 司机开启 / 关闭接单
// 根据司机 ID 修改其接单状态。通过传递 pending 或 closed 设置司机为开启接单或关闭接单。
export function updateDriverState(driver_id, state) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/states/${state}`,
    method: 'put'
  })
}

// PATCH
// /admin/drivers/{driver_id}/type 切换司机类型
// 切换指定司机为普通司机或顺风车司机
export function updateDriverRoleType(driver_id, type) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/type`,
    method: 'patch',
    data: { driver_type: type }
  })
}

// GET
// /admin/drivers/{driver_id}/line_class_trains 获取司机线路
export function getDriverLineClassTrains(driver_id) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/line_class_trains`,
    method: 'get'
  })
}

// PATCH
// /admin/drivers/{driver_id}/password 修改司机密码
export function updateDriverPassword(driver_id, data) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/password`,
    method: 'patch',
    data
  })
}

// PATCH
// /admin/drivers/{driver_id}/freeze 冻结或解冻司机
export function freezeDriver(driver_id, is_freeze) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/freeze`,
    method: 'patch',
    data: { is_freeze }
  })
}

// POST
// /admin/drivers/report_to_monitoring 上报司机信息到省级定制客运监控服务平台
export function reportDriverToMonitoring(driver_ids) {
  return accountService({
    url: '/api/admin/drivers/report_to_monitoring',
    method: 'post',
    data: { driver_ids }
  })
}

// POST
// /admin/driver/{driverId}/qr_code 生成司机二维码
export function generateDriverQrcode(driver_id, type) {
  return accountService({
    url: `/api/admin/drivers/${driver_id}/qr_codes`,
    params: { type },
    method: 'post'
  })
}

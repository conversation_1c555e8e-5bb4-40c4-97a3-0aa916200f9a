import request from '@/utils/request'

export function kaipiaoList(params) {
  return request({
    url: `/admin/fapiao/index`,
    method: 'get',
    params
  })
}

export function fullConfig(params) {
  return request({
    url: `/admin/fapiao/config/${params}`,
    method: 'get'
  })
}

export function fapiaoDetail(params) {
  return request({
    url: `/admin/fapiao/${params}`,
    method: 'get'
  })
}
export function kaipiaoCoifgList(params) {
  return request({
    url: `/admin/fapiao/kaipiao`,
    method: 'get'
  })
}

export function businessValueTypes() {
  return request({
    url: `/admin/fapiao/kaipiao/business_value_types`,
    method: 'get'
  })
}

export function postConfig(data) {
  return request({
    url: `/admin/fapiao/new/settings`,
    method: 'post',
    data
  })
}

export function sendEmail(data) {
  return request({
    url: `/admin/fapiao/${data}/email`,
    method: 'post'
  })
}

export function cancellation(data) {
  return request({
    url: `/admin/fapiao/${data}/cancellation`,
    method: 'post'
  })
}

export function addKaipiao(data) {
  return request({
    url: `/admin/fapiao/kaipiao`,
    method: 'post',
    data
  })
}

export function putKaipiao(data) {
  return request({
    url: `/admin/fapiao/kaipiao/${data.id}`,
    method: 'put',
    data
  })
}

export function delKaipiao(id) {
  return request({
    url: `/admin/fapiao/kaipiao/${id}`,
    method: 'delete'
  })
}

export function excel_datas(params) {
  return request({
    url: `/admin/fapiao/excel_datas`,
    method: 'get',
    params
  })
}

export function export_excels(params) {
  return request({
    url: `/admin/fapiao/export_excels`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function successFapiao(data) {
  return request({
    url: `/admin/fapiao/${data.id}/success`,
    method: 'post',
    data
  })
}
export function failedFapiao(data) {
  return request({
    url: `/admin/fapiao/${data.id}/failed`,
    method: 'post',
    data
  })
}
export function retry(data) {
  return request({
    url: `/admin/fapiao/${data}/retry`,
    method: 'post'
  })
}

export function refresh(data) {
  return request({
    url: `/admin/fapiao/${data}/refresh`,
    method: 'post'
  })
}

export function close(data) {
  return request({
    url: `/admin/fapiao/${data}/closure`,
    method: 'post'
  })
}

export function vehiclesList(params) {
  return request({
    url: `/admin/vehicle/vehicles`,
    method: 'get',
    params
  })
}

export function carBrands(params) {
  return request({
    url: `/admin/vehicle/brands`,
    method: 'get',
    params
  })
}

export function carServices(params) {
  return request({
    url: `/admin/vehicle/brands/${params.brand}/series`,
    method: 'get',
    params
  })
}
export function properties(params) {
  return request({
    url: `/admin/vehicle/properties`,
    method: 'get',
    params
  })
}

export function carCities(params) {
  return request({
    url: `/admin/vehicle/cities`,
    method: 'get',
    params
  })
}

export function addCar(data) {
  return request({
    url: `/admin/vehicle/vehicles`,
    method: 'post',
    data
  })
}
export function putCar(data) {
  return request({
    url: `/admin/vehicle/vehicles/${data.id}`,
    method: 'put',
    data
  })
}

export function delCar(params) {
  return request({
    url: `/admin/vehicle/vehicles/${params}`,
    method: 'delete'
  })
}

export function reportVehicles(vehicleIds) {
  return request({
    url: `/admin/vehicle/vehicles/report`,
    method: 'post',
    data: { vehicle_ids: vehicleIds }
  })
}

export function monthlyOrderOverview(params) {
  return request({
    url: `/admin/overview/monthly/orders`,
    method: 'get',
    params
  })
}

export function dailyOrderOverview(params) {
  return request({
    url: `/admin/overview/daily/orders`,
    method: 'get',
    params
  })
}


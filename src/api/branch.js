import { createServiceRequest } from '@/utils/request'

// 创建account服务的请求实例
const accountService = createServiceRequest('account-bin')

// 渠道分台API
/**
 * 获取渠道分台列表
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码
 * @param {number} query.per_page - 每页数量
 * @param {string} query.branch_name - 分台名称
 * @param {string} query.cellphone - 手机号码
 * @param {number} query.branch_level - 分台层级ID
 * @param {number} query.status - 状态
 * @returns {Promise}
 */
export function fetchChannelBranches(query) {
  return accountService({
    url: '/api/admin/branch/channels',
    method: 'get',
    params: query
  })
}

export function fetchChannelBranchDetail(id) {
  return accountService({
    url: `/api/admin/branch/channels/${id}`,
    method: 'get'
  })
}

export function createChannelBranch(data) {
  return accountService({
    url: '/api/admin/branch/channels',
    method: 'post',
    data
  })
}

export function updateChannelBranch(id, data) {
  return accountService({
    url: `/api/admin/branch/channels/${id}`,
    method: 'put',
    data
  })
}

export function deleteChannelBranch(id) {
  return accountService({
    url: `/api/admin/branch/channels/${id}`,
    method: 'delete'
  })
}

export function generateChannelQrcode(branch_id, type) {
  return accountService({
    url: `/api/admin/branch/channels/${branch_id}/qr_codes/${type}`,
    method: 'post'
  })
}

// 线路分台API
/**
 * 获取线路分台列表
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码
 * @param {number} query.per_page - 每页数量
 * @param {string} query.branch_name - 分台名称
 * @param {string} query.cellphone - 手机号码
 * @param {number} query.branch_level - 分台层级ID
 * @param {number} query.status - 状态
 * @returns {Promise}
 */
export function fetchLineBranches(query) {
  return accountService({
    url: '/api/admin/branch/lines',
    method: 'get',
    params: query
  })
}

export function fetchLineBranchDetail(id) {
  return accountService({
    url: `/api/admin/branch/lines/${id}`,
    method: 'get'
  })
}

export function createLineBranch(data) {
  return accountService({
    url: '/api/admin/branch/lines',
    method: 'post',
    data
  })
}

export function updateLineBranch(id, data) {
  return accountService({
    url: `/api/admin/branch/lines/${id}`,
    method: 'put',
    data
  })
}

export function deleteLineBranch(id) {
  return accountService({
    url: `/api/admin/branch/lines/${id}`,
    method: 'delete'
  })
}

// 获取所有可作为上级的分台列表
export function fetchParentBranches(branch_type) {
  return accountService({
    url: '/api/admin/branch/parents',
    method: 'get',
    params: { branch_type }
  })
}

export function branchLogin(data) {
  return accountService({
    url: '/api/admin/branch/login',
    method: 'post',
    data
  })
}

export function getBranchInfo() {
  return accountService({
    url: '/api/admin/branch/me',
    method: 'get'
  })
}

export function branchLogout() {
  return accountService({
    url: '/api/admin/branch/logout',
    method: 'post'
  })
}

export function changePassword(branch_id, data) {
  return accountService({
    url: `/api/admin/branch/branches/${branch_id}/change-password`,
    method: 'post',
    data
  })
}

/**
 * 获取分台权限
 * @param {number} branch_id - 分台ID
 * @returns {Promise}
 */
export function getBranchPermissions(branch_id) {
  return accountService({
    url: `/api/admin/branch/branches/${branch_id}/permissions`,
    method: 'get'
  })
}

/**
 * 获取分台特定权限定义
 * @param {number} branch_id - 分台ID
 * @returns {Promise}
 */
export function getBranchPermissionDefinitions(branch_id) {
  return accountService({
    url: `/api/admin/branch/branches/${branch_id}/permissions`,
    method: 'get'
  })
}

/**
 * 更新分台权限
 * @param {number} branch_id - 分台ID
 * @param {Object} data - 权限数据
 * @returns {Promise}
 */
export function updateBranchPermissions(branch_id, data) {
  return accountService({
    url: `/api/admin/branch/branches/${branch_id}/permissions`,
    method: 'put',
    data
  })
}

import { createServiceRequest } from '@/utils/request'

// 创建account服务的请求实例
const accountService = createServiceRequest('account-bin')

/**
 * 获取账号列表
 * @param {Object} query - 查询参数
 * @param {number} query.page - 页码
 * @param {number} query.per_page - 每页数量
 * @param {string} query.username - 用户名
 * @param {string} query.cellphone - 手机号码
 * @param {number} query.merchant_id - 所属商户/分台ID
 * @param {number} query.role_id - 角色ID
 * @param {number} query.status - 状态
 * @returns {Promise}
 */
export function fetchAccounts(query) {
  return accountService({
    url: '/api/admin/accounts',
    method: 'get',
    params: query
  })
}

/**
 * 获取账号详情
 * @param {number} id - 账号ID
 * @returns {Promise}
 */
export function fetchAccountDetail(id) {
  return accountService({
    url: `/api/admin/accounts/${id}`,
    method: 'get'
  })
}

/**
 * 创建账号
 * @param {Object} data - 账号数据
 * @returns {Promise}
 */
export function createAccount(data) {
  return accountService({
    url: '/api/admin/accounts',
    method: 'post',
    data
  })
}

/**
 * 更新账号
 * @param {number} id - 账号ID
 * @param {Object} data - 账号数据
 * @returns {Promise}
 */
export function updateAccount(id, data) {
  return accountService({
    url: `/api/admin/accounts/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除账号
 * @param {number} id - 账号ID
 * @returns {Promise}
 */
export function deleteAccount(id) {
  return accountService({
    url: `/api/admin/accounts/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除账号
 * @param {Array} ids - 账号ID数组
 * @returns {Promise}
 */
export function batchDeleteAccounts(ids) {
  return accountService({
    url: '/api/admin/accounts/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 修改账号状态
 * @param {number} id - 账号ID
 * @param {number} is_freeze - 状态值：0-启用，1-禁用
 * @returns {Promise}
 */
export function changeAccountStatus(id, is_freeze) {
  return accountService({
    url: `/api/admin/accounts/${id}/status`,
    method: 'patch',
    data: { is_freeze }
  })
}

/**
 * 修改账号密码
 * @param {number} id - 账号ID
 * @param {Object} data - 密码数据
 * @returns {Promise}
 */
export function changeAccountPassword(id, data) {
  return accountService({
    url: `/api/admin/accounts/${id}/password`,
    method: 'post',
    data
  })
}

/**
 * 获取角色列表
 * @returns {Promise}
 */
export function fetchRoles() {
  return accountService({
    url: '/api/admin/roles',
    method: 'get'
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @returns {Promise}
 */
export function createRole(data) {
  return accountService({
    url: '/api/admin/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {number} id - 角色ID
 * @param {Object} data - 角色数据
 * @returns {Promise}
 */
export function updateRole(id, data) {
  return accountService({
    url: `/api/admin/roles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {number} id - 角色ID
 * @returns {Promise}
 */
export function deleteRole(id) {
  return accountService({
    url: `/api/admin/roles/${id}`,
    method: 'delete'
  })
}

/**
 * 获取角色的权限
 * @param {number} id - 角色ID
 * @returns {Promise}
 */
export function getRolePermissions(id) {
  return accountService({
    url: `/api/admin/roles/${id}/permissions`,
    method: 'get'
  })
}

/**
 * 更新角色的权限
 * @param {number} id - 角色ID
 * @param {Object} data - 权限数据
 * @returns {Promise}
 */
export function updateRolePermissions(id, data) {
  return accountService({
    url: `/api/admin/roles/${id}/permissions`,
    method: 'put',
    data
  })
}

/**
 * 获取可分配的商户或分台列表
 * @returns {Promise}
 */
export function fetchMerchantOptions() {
  return accountService({
    url: '/api/admin/accounts/merchant-options',
    method: 'get'
  })
}

/**
 * 获取系统配置信息（业态等）
 * @returns {Promise}
 */
export function getProfiles() {
  return accountService({
    url: '/api/admin/profiles',
    method: 'get'
  })
}

// admin/accounts/{id}
export function getAccount(id) {
  return accountService({
    url: `/api/admin/accounts/${id}`,
    method: 'get'
  })
}

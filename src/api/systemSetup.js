import request from '@/utils/request'

// 获取商户开票配置
export function getInvoiceMessage(type) {
  return request({
    url: `/admin/fapiao/config/${type}`,
    method: 'get'
  })
}

// 获取平台配置的商户权限
export function getInvoiceConfigAuthority() {
  return request({
    url: '/admin/fapiao/platform_config',
    method: 'get'
  })
}

// 商户配置保存（发票配置、保险配置、积分配置）
export function setInvoiceMessage(data) {
  return request({
    url: '/admin/fapiao/config',
    method: 'post',
    data: data
  })
}

// 商户配置开启、关闭
export function setChangeStatus(data) {
  return request({
    url: '/admin/fapiao/config_status',
    method: 'post',
    data: data
  })
}

// 获取发票列表
export function getInvoiceList(order_no, fapiao_no, status, per_page, page) {
  // const statusList = JSON.stringify(status)
  return request({
    url: '/admin/fapiao/index',
    method: 'get',
    params: {
      order_no: order_no, // 订单编号
      fapiao_no: fapiao_no, // 发票申请编号编号
      status: '', // 状态（1：开票中，2：开票完成，3:开票失败）
      per_page: per_page,
      page: page
    }
  })
}

// 开票失败
export function invoiceFailed(id, reason) {
  return request({
    url: '/admin/fapiao/failed',
    method: 'post',
    params: { id: id, reason: reason }
  })
}

// 开票成功
export function invoiceSuccess(id, bill_content) {
  return request({
    url: '/admin/fapiao/success',
    method: 'post',
    params: { id: id, bill_content: bill_content }
  })
}

// 获取发票订单数据
export function getInvoiceOrder(billId) {
  return request({
    url: `/admin/fapiao/order/${billId}`,
    method: 'get'
  })
}

// 积分商品新增
export function getPointsAdd(data) {
  return request({
    url: '/admin/points/add',
    method: 'post',
    data: data
  })
}

// 积分商品编辑
export function pointsEdit(spu_id, data) {
  return request({
    url: `/admin/points/edit/${spu_id}`,
    method: 'put',
    data: data
  })
}

// 积分商品删除
export function pointsDelete(spu_id) {
  return request({
    url: `/admin/points/delete/${spu_id}`,
    method: 'delete'
  })
}

// 积分商品列表
export function getPointsIndex(no, name, on_sale, per_page, page, branch_id) {
  // 过滤掉空字符串
  const params = {
    no: no,
    name: name,
    on_sale: on_sale,
    per_page: per_page,
    page: page,
    branch_id: branch_id
  }
  // 过滤掉空字符串
  Object.keys(params).forEach((key) => {
    if (params[key] === '') {
      delete params[key]
    }
  })
  return request({
    url: '/admin/points/index',
    method: 'get',
    params: params
  })
}

// 积分商品兑换订单列表
export function getPointsOrder(params) {
  // 过滤params中的空值
  Object.keys(params).forEach((key) => {
    if (params[key] === '') {
      delete params[key]
    }
  })
  return request({
    url: '/admin/points/order',
    method: 'get',
    params
  })
}

// 订单支付完成生成订单积分（兑换订单）
export function getOrderPoints(user, order) {
  return request({
    url: `/api/inner/users/${user}/orders/${order}/points`,
    method: 'post'
  })
}

// 订单退款完成退还订单积分（退回兑换）
export function deleteOrderPoints(user, order) {
  return request({
    url: `/api/inner/users/${user}/orders/${order}/points`,
    method: 'DELETE'
  })
}

// 用户积分明细
export function getPointsDetail() {
  return request({
    url: '/api/v1/points',
    method: 'GET'
  })
}

// 积分商品发货
export function shipped(data) {
  return request({
    url: '/admin/points/ship',
    method: 'post',
    data: data
  })
}

// 取消订单
export function cancelOrder(order_id) {
  return request({
    url: `/admin/points/order/cancel/${order_id}`,
    method: 'post'
  })
}

// 商品上下架
export function pointsStatus(spuId, onSale) {
  return request({
    url: `admin/points/status/${spuId}/${onSale}`,
    method: 'post'
  })
}

// 查询商品详情
export function getInvoiceDetail(spu_id) {
  return request({
    url: `/admin/points/info/${spu_id}`,
    method: 'get'
  })
}

// 查询待处理发票的总数
export function getInvoiceTotal() {
  return request({
    url: `/admin/fapiao/no_handle`,
    method: 'get'
  })
}

export function addElectronic(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts`,
    method: 'post',
    data
  })
}

// 创建新的数电发票账号
// 在数据库中存储新创建的数电发票账号。

export function editElectronic(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}`,
    method: 'put',
    data
  })
}

export function getElectronicList() {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts`,
    method: 'get'
  })
}

export function getElectPhoneCode(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/send-verification-code`,
    method: 'post'
  })
}
// 发送短信验证码
// 通过数电发票账号的ID获取二维码并发送验证码。

export function checkPhoneCode(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/check-verification-code`,
    method: 'post',
    data
  })
}
// 检查验证码
// 通过提供的数电发票账号ID和验证码来验证操作。

export function sendQrCode(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/send-qr-code`,
    method: 'post',
    data
  })
}
// 创建认证二维码
// 通过数电发票账号的ID获取二维码并发送验证码。

export function delAccount(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}`,
    method: 'delete'
  })
}
// 删除指定的数电发票账号
// 根据提供的ID删除一个数电发票账号。

export function checkQrCode(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/query-qr-code`,
    method: 'post',
    data
  })
}
// 查询二维码认证结果

export function refreshLoginStatus(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/refresh-login-status`,
    method: 'post',
    data
  })
}
// 刷新数电账号登录状态
// 根据数电账户的 ID，刷新其在诺诺平台的登录认证状态。

export function refreshAuthStatus(data) {
  return request({
    url: `/admin/fapiao/electronic-invoice-accounts/${data.id}/refresh-auth-status`,
    method: 'post',
    data
  })
}

// 刷新数电账号认证状态
// 根据数电账户的 ID，刷新其在诺诺平台的认证状态。

// 获取商品分类列表
export function getSpusTypes(params) {
  return request({
    url: '/admin/points/spus_types',
    method: 'get',
    params
  })
}

// 添加商品分类
export function addSpusTypes(data) {
  return request({
    url: '/admin/points/spus_types',
    method: 'post',
    data
  })
}

// 查询商品分类详情 /admin/points/spus_types/{id}
export function editSpusTypes(data) {
  return request({
    url: `/admin/points/spus_types/${data.id}`,
    method: 'put',
    data
  })
}

// 更新商品分类 /admin/points/spus_types/{id}
export function updateSpusTypes(data) {
  return request({
    url: `/admin/points/spus_types/${data.id}`,
    method: 'put',
    data
  })
}

// 查询商品分类详情 /admin/points/spus_types/{id}
export function getSpusTypesDetail(id) {
  return request({
    url: `/admin/points/spus_types/${id}`,
    method: 'get'
  })
}

// /admin/points/spus_types/{id} 删除指定的商品类型
export function deleteSpusTypes(id) {
  return request({
    url: `/admin/points/spus_types/${id}`,
    method: 'delete'
  })
}

// /admin/points/spus_types/batch 批量删除商品分类
export function batchDeleteSpusTypes(data) {
  return request({
    url: '/admin/points/spus_types/batch',
    method: 'delete',
    data
  })
}

// /admin/points/spus_types/tree 获取商品分类树
export function getSpusTypesTree() {
  return request({
    url: '/admin/points/spus_types/tree',
    method: 'get'
  })
}

// 核销订单
export function verifyOrder(data) {
  return request({
    url: `/admin/points/order/verify/${data.order_id}`,
    method: 'post',
    data
  })
}
// 获取短信发送记录
export function getSmsSendRecord(params) {
  return request({
    url: '/admin/sms',
    method: 'get',
    params
  })
}

// 短信配置说明
export function getSmsConfig() {
  return request({
    url: '/admin/sms_configs',
    method: 'get'
  })
}

// 更新短信配置状态
export function updateSmsConfig(status) {
  return request({
    url: `/admin/sms_configs/statues/${status}`,
    method: 'post'
  })
}

// GET
// /admin/points/overview 获取概览数据
export function getOverview() {
  return request({
    url: '/admin/points/overview',
    method: 'get'
  })
}

// 更新物流信息
export function updateLogistics(data) {
  return request({
    url: '/admin/points/order/updateLogistics',
    method: 'put',
    data
  })
}

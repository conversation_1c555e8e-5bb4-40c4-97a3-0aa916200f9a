import request from '@/utils/request'

export function getAdmindOrder (params) {
  return request({
    url: `/admin/biz/${params.biz}/orders`,
    method: 'get',
    params
  })
}

export function ticketRefunds (data) {
  console.log(data)
  return request({
    url: `/admin/tickets/${data.ticket}/refunds`,
    method: 'post',
    data
  })
}

export function ticRefundLog (data) {
  return request({
    url: `/admin/tickets/${data.ticket}/refunds`,
    method: 'get',
    data
  })
}

export function orderRefunds (data) {
  return request({
    url: `/api/inner/orders/${data.order}/refunds`,
    method: 'delete',
    data
  })
}

export function checkTicket (data) {
  return request({
    url: `/api/inner/orders/${data.order}/tickets/${data.ticket}/verify-eticket`,
    method: 'post',
    data
  })
}

export function exportExcels(params) {
  return request({
    url: `/admin/biz/${params.biz}/orders/export_excels`,
    method: 'post',
    data: params
  })
}

export function markOfflineTicketIssued(orderId) {
  return request({
    url: `/api/inner/orders/${orderId}/offline-ticket-issued`,
    method: 'post'
  })
}

// GET
// /api/inner/merchants/{merchant}/vehicle_nos/{vehicle_no} 获取车辆信息
export function getVehicleInfo(merchant, vehicle_no) {
  return request({
    url: `/api/inner/merchants/${merchant}/vehicle_nos/${vehicle_no}`,
    method: 'get'
  })
}

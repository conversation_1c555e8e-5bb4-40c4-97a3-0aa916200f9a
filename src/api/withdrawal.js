import { createServiceRequest } from '@/utils/request'

// 创建finance服务的请求实例
const financeService = createServiceRequest('finance-bin')

// /api/admin/settlement_batches
// 查询结算批次记录
export function settlement_batches(params) {
  return financeService({
    url: '/api/admin/settlement_batches',
    method: 'get',
    params
  })
}

export function settlements_driver(params) {
  console.log(params)
  return financeService({
    url: '/api/admin/settlements/drivers',
    method: 'get',
    params
  })
}

export function settlements_branches(params) {
  console.log(params)
  return financeService({
    url: '/api/admin/settlements/branches',
    method: 'get',
    params
  })
}

export function add_settlements(data) {
  return financeService({
    url: '/api/admin/settlement_batches',
    method: 'get',
    data
  })
}

export function settlements_count(params) {
  return financeService({
    url: '/api/admin/settlement_batches/pending/count',
    method: 'get',
    params
  })
}

export function del_settlements(id) {
  return financeService({
    url: `/api/admin/settlement_batches/${id}`,
    method: 'delete'
  })
}

export function export_excels_settlements(data) {
  return financeService({
    url: `/api/admin/settlement_batches/${data.settlement_batch_id}/settlement_sheets`,
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}

export function settlements_detail(params) {
  return financeService({
    url: `/api/admin/settlement_batches/${params.settlement_batch_id}/settlement_sheets`,
    method: 'get',
    params
  })
}

export function in_progress_settlements(ids) {
  return financeService({
    url: `/api/admin/settlement_batches/${ids}/states/in_progress`,
    method: 'put'
  })
}

export function completed_settlements(ids) {
  return financeService({
    url: `/api/admin/settlement_batches/${ids}/states/completed`,
    method: 'put'
  })
}

/**
 * 获取财务结算数据
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.branchId - 分公司ID
 * @returns {Promise}
 */
export function getFinancialSettlements(params) {
  return financeService({
    url: '/api/admin/financial/settlements',
    method: 'get',
    params
  })
}

/**
 * 获取财务结算分公司列表
 * @returns {Promise}
 */
export function getFinancialSettlementBranches() {
  return financeService({
    url: '/api/admin/financial/settlements/branches',
    method: 'get'
  })
}

/**
 * 导出财务结算报表
 * @param {Object} data - 查询参数
 * @param {string} data.startDate - 开始日期
 * @param {string} data.endDate - 结束日期
 * @param {string} data.branchId - 分公司ID（可选）
 * @returns {Promise}
 */
export function exportFinancialSettlements(data) {
  return financeService({
    url: '/api/admin/financial/settlements/export',
    method: 'post',
    data
  })
}

/**
 * 申请提现
 * @param {Object} data - 提现申请数据
 * @param {string} data.bankCardId - 银行卡ID
 * @param {number} data.amount - 提现金额
 * @param {string} data.remark - 备注信息
 * @returns {Promise}
 */
export function applyWithdrawal(data) {
  return financeService({
    url: '/api/admin/financial/settlements/withdraw',
    method: 'post',
    data
  })
}

/**
 * 获取账户信息
 * @returns {Promise}
 */
export function getAccounts(data) {
  return financeService({
    url: '/api/admin/withdrawal/accounts',
    method: 'get',
    params: data
  })
}

/**
 * 获取银行卡列表
 * @returns {Promise}
 */
export function getBanks(data) {
  return financeService({
    url: '/api/admin/withdrawal/banks',
    method: 'get',
    params: data
  })
}

/**
 * 添加银行卡
 * @param {Object} data - 银行卡信息
 * @param {string} data.bank_crad_number - 银行卡号
 * @param {string} data.bank_name - 银行名称
 * @param {string} data.account - 开户人姓名
 * @param {number} data.default - 是否默认 1-是 0-否
 * @returns {Promise}
 */
export function addBank(data) {
  return financeService({
    url: '/api/admin/withdrawal/banks',
    method: 'post',
    data
  })
}

/**
 * 更新银行卡
 * @param {number} bankId - 银行卡ID
 * @param {Object} data - 银行卡信息
 * @param {string} data.bank_crad_number - 银行卡号
 * @param {string} data.bank_name - 银行名称
 * @param {string} data.account - 开户人姓名
 * @param {number} data.default - 是否默认 1-是 0-否
 * @returns {Promise}
 */
export function updateBank(bankId, data) {
  return financeService({
    url: `/api/admin/withdrawal/banks/${bankId}`,
    method: 'put',
    data
  })
}

/**
 * 删除银行卡
 * @param {number} bankId - 银行卡ID
 * @returns {Promise}
 */
export function deleteBank(bankId, data) {
  return financeService({
    url: `/api/admin/withdrawal/banks/${bankId}`,
    method: 'delete',
    data
  })
}

/**
 * 获取提现记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise}
 */
export function getWithdrawals(status, params) {
  return financeService({
    url: `/api/admin/withdrawals/status/${status}`,
    method: 'get',
    params
  })
}

/**
 * 提交提现申请
 * @param {Object} data - 提现申请数据
 * @param {number} data.bank_id - 银行卡ID
 * @param {number} data.amount - 提现金额
 * @param {string} data.remark - 备注信息
 * @returns {Promise}
 */
export function submitWithdrawal(data) {
  return financeService({
    url: '/api/admin/withdrawals',
    method: 'post',
    data
  })
}

/**
 * 获取提现申请状态数量
 * @param {Object} params - 查询参数
 * @param {string} params.start_date - 开始日期
 * @param {string} params.end_date - 结束日期
 * @returns {Promise}
 */
export function getWithdrawalsBadge(params) {
  return financeService({
    url: '/api/admin/withdrawals/badges',
    method: 'get',
    params
  })
}

/**
 * 获取提现申请详情
 * @param {number} withdrawalId - 提现申请ID
 * @returns {Promise}
 */
export function getWithdrawalDetail(withdrawalId) {
  return financeService({
    url: `/api/admin/withdrawals/${withdrawalId}/settlement_sheets`,
    method: 'get'
  })
}

/**
 * 获取提现申请列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise}
 */
export function getApprovalsWithdrawals(status, params) {
  return financeService({
    url: `/api/admin/withdrawal/approvals/status/${status}`,
    method: 'get',
    params
  })
}

/**
 * 获取提现申请徽标
 * @returns {Promise}
 */
export function getApprovalsBadge(params) {
  return financeService({
    url: '/api/admin/withdrawal/approvals/badges',
    method: 'get',
    params
  })
}

// 获取提现统计数据
export function getWithdrawalStatistics(params) {
  return financeService({
    url: '/api/admin/withdrawal/approvals/statistics',
    method: 'get',
    params
  })
}

/**
 * 审核提现申请
 * @param {number} withdrawalId - 提现申请ID
 * @param {string} status - 审核状态
 * @param {Object} data - 审核数据
 * @returns {Promise}
 */
export function approveWithdrawal(withdrawalId, status, data) {
  return financeService({
    url: `/api/admin/withdrawal/approvals/status/${status}/${withdrawalId}`,
    method: 'post',
    data
  })
}

/**
 * 获取提现申请审核时间线
 * @param {number} withdrawalId - 提现申请ID
 * @returns {Promise}
 */
export function getApprovalTimelines(withdrawalId) {
  return financeService({
    url: `/api/admin/withdrawal/approvals/timelines/${withdrawalId}`,
    method: 'get'
  })
}

/**
 * 打款提现申请
 * @param {number} withdrawalId - 提现申请ID
 * @param {Object} data - 打款数据
 * @returns {Promise}
 */
export function paymentWithdrawal(withdrawalId, data) {
  return financeService({
    url: `/api/admin/withdrawal/approvals/payments/${withdrawalId}`,
    method: 'post',
    data
  })
}

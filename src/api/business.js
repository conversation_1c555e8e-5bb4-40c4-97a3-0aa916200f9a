import request from '@/utils/request'

export function operationOrders(params) {
  return request({
    url: `/admin/operation/orders`,
    method: 'get',
    params
  })
}
export function support_starting_points(params) {
  return request({
    url: `/admin/operation/banxian/support_starting_points`,
    method: 'get',
    params
  })
}
export function support_ending_points(params) {
  return request({
    url: `/admin/operation/banxian/support_ending_points`,
    method: 'get',
    params
  })
}

export function getLineClassTrains(params) {
  return request({
    url: `/admin/operation/banxian/support_schedules`,
    method: 'get',
    params
  })
}

export function proxy_orders(data) {
  return request({
    url: `/admin/operation/biz/banxian/proxy_orders`,
    method: 'post',
    data
  })
}

export function dispatchOrders(params) {
  return request({
    url: `/admin/dispatch/biz/${params.biz}/orders`,
    method: 'get',
    params
  })
}

export function dispatchDrivers(params) {
  return request({
    url: `/admin/dispatch/drivers`,
    method: 'get',
    params
  })
}

export function dispatchBranches(params) {
  return request({
    url: `/admin/dispatch/branches`,
    method: 'get',
    params
  })
}

export function dispatchBanXianLines(params) {
  return request({
    url: `/admin/operation/banxian/lines`,
    method: 'get',
    params
  })
}

export function postOrders(data) {
  return request({
    url: data?.is_drivers
      ? `/admin/dispatch/drivers/${data.driver}/transfer-orders`
      : `/admin/dispatch/drivers/${data.driver}/orders`,
    method: 'post',
    data: {
      ...data,
      assign_note: data.assign_note || ''
    }
  })
}

export function onLineOrders(params) {
  return request({
    url: `/admin/dispatch/drivers/${params.driver}/orders`,
    method: 'get',
    params
  })
}

export function cancelOrders(data) {
  return request({
    url: `/admin/dispatch/drivers/${data.driver}/orders`,
    method: 'delete',
    data
  })
}

export function timeoutOrders(params) {
  return request({
    url: `/admin/dispatch/timeout/orders`,
    method: 'get',
    params
  })
}

export function closeOrders(data) {
  return request({
    url: `/admin/dispatch/orders/${data.order}/close`,
    method: 'put',
    data
  })
}

export function orderInfo(params) {
  return request({
    url: `/admin/dispatch/orders/${params.order}`,
    method: 'get',
    params
  })
}

export function updateOrderInfo(data) {
  return request({
    url: `/admin/dispatch/orders/${data.order_id}/update`,
    method: 'put',
    data
  })
}

export function schedule_days(params) {
  return request({
    url: `/admin/operation/banxian/schedule_days`,
    method: 'get',
    params
  })
}

export function numbers(params) {
  return request({
    url: `/admin/dispatch/order/numbers`,
    method: 'get',
    params
  })
}
export function mapDrivers(params) {
  return request({
    url: `/admin/dispatch/map/drivers`,
    method: 'get',
    params
  })
}

export function driverDetail(params) {
  return request({
    url: `/admin/dispatch/drivers/${params.driver}`,
    method: 'get',
    params
  })
}

export function fetTimeShort(params) {
  return request({
    url: `/admin/dispatch/biz/${params.biz}/orders/counts`,
    method: 'get',
    params
  })
}

export function daily(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/statistics/daily`,
    method: 'get',
    params
  })
}

export function timetables(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/statistics/routes/timetable`,
    method: 'get',
    params
  })
}
// 获取班次时刻表
export function schedulesList(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules`,
    method: 'get',
    params
  })
}

// /admin/orders/listByDriver 获取司机订单列表
export function listByDriver(params) {
  return request({
    url: `/admin/orders/list_by_driver`,
    method: 'get',
    params
  })
}
export function schedulesCityCode(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/filters/${params.filter}/addresses`,
    method: 'get',
    params
  })
}

// 定制客运出发地与目的地筛选

export function schedulesDrivers(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/branches/${
      params?.branch || 0
    }/drivers`,
    method: 'get',
    params
  })
}

export function putDrivers(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/drivers`,
    method: 'put',
    params
  })
}
// 更新指定班次的司机信息，包括排序。

export function delDrivers(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/drivers`,
    method: 'delete',
    params
  })
}

// 取消指定班次的已指派司机。

export function sortDrivers(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/drivers/sort`,
    method: 'put',
    params
  })
}

export function schedulesBranches(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/branches`,
    method: 'get',
    params
  })
}

export function updateLineClassTrainStatus(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/status`,
    method: 'patch',
    params
  })
}

export function getVehicles() {
  return request({
    url: `/admin/dingzhikeyun/schedules/vehicles`,
    method: 'get'
  })
}

export function updateVehicles(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/drivers/${params.driver_id}/vehicles`,
    method: 'patch',
    params: {
      vehicle_id: params.vehicle_id
    }
  })
}

export function changeTickets(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/tickets`,
    method: 'patch',
    params: {
      amount: params.amount,
      action: 'increase'
    }
  })
}

export function updateTimings(params) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${params.line_class_train_id}/timings`,
    method: 'patch',
    params
  })
}

export function addTimeLine(data) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains`,
    method: 'post',
    data
  })
}

// PATCH
// /admin/dingzhikeyun/schedules/line_class_trains/{line_class_train_id}/pricing 更新班次价格
export function updatePricing(data) {
  return request({
    url: `/admin/dingzhikeyun/schedules/line_class_trains/${data.line_class_train_id}/pricing`,
    method: 'patch',
    data
  })
}

// GET
// /admin/operation/calculate_price 计算运费
export function calculatePrice(params) {
  return request({
    url: `/admin/operation/calculate_price`,
    method: 'get',
    params
  })
}
// POST
// /admin/operation/biz/{biz}/proxy_orders 创建不同业务类型代约订单【可调用】
export function createProxyOrders(data, biz) {
  return request({
    url: `/admin/operation/biz/${biz}/proxy_orders`,
    method: 'post',
    data
  })
}
// GET
// /admin/dingzhikeyun/schedules/filters/ {filter}/addresses 定制客运出发地与目的地筛选
export function schedulesFiltersAddresses(filter) {
  return request({
    url: `/admin/dingzhikeyun/schedules/filters/${filter}/addresses`,
    method: 'get'
  })
}

// /admin/manage_lines/{business_format}/list_region 获取线路区域
export function listRegion(params) {
  return request({
    url: `/admin/manage_lines/${params.business_format}/list_region`,
    method: 'get',
    params
  })
}

import request from '@/utils/request'

export function getAgreements (params) {
  return request({
    url: '/admin/agreements',
    method: 'get',
    params
  })
}

export function putAgreements(data) {
  return request({
    url: `/admin/agreements/${data.agreement_id}`,
    method: 'put',
    data
  })
}

export function addAgreements(data) {
  return request({
    url: `/admin/agreements`,
    method: 'post',
    data
  })
}

export function getProvincialReportConfig() {
  return request({
    url: '/admin/fapiao/config/provincial_reporting',
    method: 'get'
  })
}

export function updateProvincialReportConfig(data) {
  return request({
    url: '/admin/fapiao/config',
    method: 'post',
    data
  })
}

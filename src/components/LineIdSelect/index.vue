<template>
  <el-select
    v-model="innerValue"
    :size="size"
    clearable
    :placeholder="placeholder"
    :style="innerStyle"
    filterable
    remote
    :remote-method="remoteSearchLines"
    :loading="lineLoading"
    @focus="handleLineFocus"
    @change="handleChange"
  >
    <el-option v-for="item in lineOptions" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script>
import { fetchLines } from '@/api/ad'

export default {
  name: 'LineIdSelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    size: {
      type: String,
      default: 'small'
    },
    style: {
      type: [Object, String],
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请输入线路ID或名称搜索'
    }
  },
  data() {
    return {
      lineLoading: false,
      lineOptions: [],
      innerValue: this.value
    }
  },
  computed: {
    innerStyle() {
      return this.style
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.innerValue = newVal
      },
      immediate: true
    }
  },
  created() {
    // 初始化时如果有值，需要获取线路选项
    if (this.value) {
      this.getLineOptions()
    }
  },
  methods: {
    getLineOptions(keyword = '') {
      this.lineLoading = true
      fetchLines({ keyword: keyword, page_size: 50 })
        .then(response => {
          this.lineOptions = response && response.data && response.data.map(item => ({
            id: item.id,
            name: ` ${item.id} - ${item.start_name} → ${item.end_name}`
          })) || []
        })
        .catch(error => {
          console.error('获取线路选项失败:', error)
          this.lineOptions = []
        })
        .finally(() => {
          this.lineLoading = false
        })
    },
    remoteSearchLines(query) {
      if (query !== '') {
        this.getLineOptions(query)
      } else {
        this.getLineOptions()
      }
    },
    handleLineFocus() {
      if (this.lineOptions.length === 0 && !this.lineLoading) {
        this.getLineOptions()
      }
    },
    handleChange(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    }
  }
}
</script>
<template>
  <div v-loading="loading" class="refound-ticket">
    <el-table
      :data="logs"
      element-loading-text=""
      fit
      highlight-current-row
      size="small"
    >
      <el-table-column label="派单时间" width="185" prop="time" />
      <el-table-column label="派单类型" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.dispatch_way === 'manual'" size="mini">手动</el-tag>
          <el-tag v-else size="mini" type="info">自动</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="派单司机" width="170">
        <template slot-scope="scope">
          <p class="table-p">{{ scope.row?.driver?.name || "-" }}</p>
          <p class="table-p">
            {{ scope.row?.driver?.phone || "-" }}
            {{ scope.row?.driver?.driver_group_name || "-" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="接单状态" width="100">
        <template slot-scope="scope">
          <Tablestatus
            v-if="scope.row.status === 'success'"
            mode="success"
            word="已派单"
          />
          <Tablestatus
            v-else-if="scope.row.status === 'fail'"
            mode="error"
            word="未接单"
          />
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="155" prop="message" />
    </el-table>
    <div class="dialog-footer">
      <el-button
        type="primary"
        size="small"
        @click="cancel"
      >确认</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    logs: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
    }
  },
  computed: {
    ...mapGetters(['setting'])
    // totalAmount() {
    //   let final = 0
    //   if (this.multipleSelection.length > 0) {
    //     final = this.multipleSelection.map((o) => o.fee_amount).reduce(
    //       (a, b) => Number(a) + Number(b)
    //     )
    //   }
    //   return final
    // },
  },
  methods: {
    cancel() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.reason-txt {
  font-size: 14px;
  line-height: 30px;
  margin-top: 10px;
}
.final-word {
  font-size: 14px;
  line-height: 30px;
  margin-top: 20px;
  .err {
    color: #ff4d4f;
  }
}
</style>

<template>
  <div :id="id" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts/core'
import {
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { <PERSON><PERSON>hart, <PERSON><PERSON>hart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { monthlyOrderOverview } from '@/api/service'

echarts.use([
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON>hart,
  Line<PERSON>hart,
  CanvasRenderer,
  UniversalTransition
])

export default {
  props: {
    className: {
      type: String,
      default: 'monthly-chart'
    },
    id: {
      type: String,
      default: 'monthly-chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100px'
    },
    year: {
      type: Date,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      years: [],
      repurchaseRate: [],
      ticketSalesRate: [],
      totalOrders: [],
      totalPaidOrders: [],
      totalRefundOrders: [],
      totalOnlineOrders: [],
      totalOfflineOrders: []
    }
  },
  watch: {
    year: {
      handler(n, o) {
        this.fetchData(n)
      },
      immediate: true
    }
  },
  // mounted () {
  //   this.fetchData()
  // },

  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    fetchData(year) {
      monthlyOrderOverview({ year: year }).then((response) => {
        this.years = response.data.years
        this.repurchaseRate = response.data.repurchase_rate
        this.ticketSalesRate = response.data.ticket_sales_rate
        this.totalOrders = response.data.total_orders
        this.totalPaidOrders = response.data.total_paid_orders
        this.totalRefundOrders = response.data.total_refund_orders
        this.totalOnlineOrders = response.data.total_online_orders
        this.totalOfflineOrders = response.data.total_offline_orders
        this.initChart()
      })
    },
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: [
            '总订单数',
            '线上订单数',
            '线下订单数',
            '总支付订单数',
            '总退款订单数',
            '售票率',
            '复购率'
          ]
        },
        xAxis: [
          {
            type: 'category',
            data: this.years,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '订单量',
            minInterval: 1,

            // min: 0,
            // max: 400,
            // interval: 50,
            axisLabel: {
              formatter: '{value} 单'
            }
          }
          // {
          //   type: 'value',
          //   name: '出票率',
          //   // min: 0,
          //   // max: 500,
          //   // interval: 50,
          //   axisLabel: {
          //     formatter: '{value} %'
          //   }
          // }
        ],
        series: [
          {
            name: '总订单数',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 单'
              }
            },
            data: this.totalOrders
          },
          {
            name: '线上订单数',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 单'
              }
            },
            data: this.totalOnlineOrders
          },
          {
            name: '线下订单数',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 单'
              }
            },
            data: this.totalOfflineOrders
          },
          {
            name: '总支付订单数',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 单'
              }
            },
            data: this.totalPaidOrders
          },
          {
            name: '总退款订单数',
            type: 'bar',
            // yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 单'
              }
            },
            data: this.totalRefundOrders
          },
          {
            name: '售票率',
            type: 'line',
            // yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %'
              }
            },
            data: this.ticketSalesRate
          },
          {
            name: '复购率',
            type: 'line',
            // yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %'
              }
            },
            data: this.repurchaseRate
          }
        ]
      })
      // window.addEventListener("resize", () => {
      //   // 第六步，执行echarts自带的resize方法，即可做到让echarts图表自适应
      //   this.chart.resize();
      // });
    }
  }
}
</script>

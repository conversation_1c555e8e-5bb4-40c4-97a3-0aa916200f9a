<template>
  <a-dropdown v-if="currentUser && currentUser.name" placement="bottomRight">
    <span class="ant-pro-account-avatar">
      <a-avatar size="small" src="https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png" class="antd-pro-global-header-index-avatar" />
      <span>{{ currentUser.name }}</span>
    </span>
    <template v-slot:overlay>
      <a-menu class="ant-pro-drop-down menu" :selected-keys="[]">
        <a-menu-item key="logout" @click="handleLogout">
          <a-icon type="logout" />
          退出
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <span v-else>
    <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
  </span>
</template>

<script>
import { Modal, message } from 'ant-design-vue'
import storage from 'store'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
  name: 'AvatarDropdown',
  props: {
    currentUser: {
      type: Object,
      default: () => null
    },
    menu: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleLogout (e) {
      Modal.confirm({
        title: '确认退出',
        content: '确定要退出登录吗？',
        onOk: () => {
          console.log('用户确认退出登录')
          message.loading({ content: '正在退出...', duration: 0, key: 'logoutLoading' })

          // 先清除token信息，确保在登出过程中不会导致401错误
          localStorage.removeItem(ACCESS_TOKEN)
          storage.remove(ACCESS_TOKEN)

          // 使用根级别的Logout action，它会处理所有模块的登出
          this.$store.dispatch('Logout')
            .then(() => {
              message.success({ content: '退出成功', key: 'logoutLoading', duration: 1 })
              console.log('退出登录成功，准备跳转到登录页')

              // 不管是否成功，都直接跳转到登录页面
              setTimeout(() => {
                // 使用window.location.href确保完全重新加载页面
                window.location.href = '/user/login'
              }, 100)
            })
            .catch(err => {
              message.error({ content: '退出过程中出错，请重试', key: 'logoutLoading', duration: 2 })
              console.error('退出过程中出错:', err)

              // 即使出错也尝试跳转到登录页
              setTimeout(() => {
                window.location.href = '/user/login'
              }, 500)
            })
        },
        onCancel () {
          console.log('用户取消退出登录')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ant-pro-drop-down {
  :deep(.action) {
    margin-right: 8px;
  }
  :deep(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
}
</style>

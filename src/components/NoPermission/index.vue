<template>
  <div class="permission-container">
    <div class="permission-content">
      <div class="permission-icon">
        <svg-icon icon-class="lock" class="lock-icon" />
      </div>
      <h2 class="permission-title">暂无访问权限</h2>
      <p class="permission-text">{{ message }}</p>
      <el-button type="primary" class="permission-button" @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoPermission',
  props: {
    message: {
      type: String,
      default: '您没有相关权限，无法访问此功能'
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.permission-content {
  text-align: center;
  padding: 24px;
}

.permission-icon {
  margin-bottom: 16px;

  .lock-icon {
    font-size: 70px;
    color: #909399;
  }
}

.permission-title {
  font-size: 20px;
  color: #303133;
  margin-bottom: 8px;
  font-weight: 500;
}

.permission-text {
  font-size: 14px;
  color: #909399;
  margin-bottom: 24px;
}

.permission-button {
  padding: 10px 20px;
}
</style>

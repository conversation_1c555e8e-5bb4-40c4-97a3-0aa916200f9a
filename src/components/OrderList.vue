<template>
  <el-table
    :data="orderList"
    border
    height="480"
    :header-cell-style="{'text-align':'center'}"
    :cell-style="{'text-align':'center'}"
    style="width: 100%; overflow-y: auto"
  >
    <el-table-column label="订单类型" prop="type" width="80">
      <template v-slot="scope">
        {{ getOrderType(scope.row.type) }}
      </template>
    </el-table-column>
    <el-table-column label="所属分台" prop="branch_name" width="130" />
    <el-table-column label="乘客信息" prop="passenger" width="140">
      <template v-slot="scope">
        <div>{{ scope.row.passenger_name }}</div>
        <div>{{ scope.row.passenger_phone }}</div>
      </template>
    </el-table-column>
    <el-table-column label="线路" prop="line" width="260">
      <template v-slot="scope">
        <div>起： {{ scope.row.start_address }}</div>
        <div>终： {{ scope.row.end_address }}</div>
      </template>
    </el-table-column>
    <el-table-column label="司机信息" prop="driver_name" width="140">
      <template v-slot="scope">
        <div>{{ scope.row.driver_name }}</div>
        <div>{{ scope.row.driver_phone }}</div>
      </template>
    </el-table-column>
    <el-table-column label="行程票额（元）" prop="pay_price" width="120" />
    <el-table-column label="保险票额（元）" prop="insurance_price" width="120" />
    <el-table-column label="下单时间" prop="pay_time" width="180" />
  </el-table>
</template>

<script>
import { getInvoiceOrder } from '@/api/systemSetup'

export default {
  name: 'OrderList',
  props: {
    billId: {
      type: Number,
      require: true,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      orderList: []
    }
  },
  watch: {
    billId: {
      immediate: true,
      handler(to, from) {
        if (to) {
          this.fetchInvoiceOrder(to)
        } else {
          if (from) {
            this.orderData = []
          }
        }
      }
    }
  },
  methods: {
    getOrderType(type) {
      switch (type) {
        case 1:
          return '拼车'
        case 2:
          return '包车'
        case 3:
          return '带货'
        case 4:
          return '代办'
        case 5:
          return '班车'
        case 6:
          return '顺风车'
        case 7:
          return '快车'
        case 8:
          return '学生号'
        case 9:
          return '家长互助'
        case 10:
          return '学生号定制包车'
        case 11:
          return '出租车'
        default:
          return '未知'
      }
    },
    fetchInvoiceOrder(billId) {
      this.orderList = []
      getInvoiceOrder(billId).then((value) => {
        console.log(value.data)
        this.orderList = value.data
      })
    }
  }
}
</script>

<style scoped>

</style>

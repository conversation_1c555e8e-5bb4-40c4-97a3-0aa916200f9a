<template>
  <span class="status-view">
    <span :class="['badge-status',mode]" :style="'background-color:' + modeList[mode]" />
    <span :class="mode != 'canceled' ? 'status-text' : 'status-text-canceled'">{{ word }}</span>
  </span>
</template>

<script>
export default {
  name: 'Tablestatus',
  props: {
    mode: {
      type: String,
      default: 'default'
    },
    word: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      modeList: {
        default: '#d9d9d9',
        success: '#52c41a',
        error: '#ff4d4f',
        processing: '#1677ff',
        warning: '#faad14',
        canceled: '#f56c6c'
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.status-view {
  line-height: inherit;
  vertical-align: baseline;
  margin-right: 10px;
  .badge-status {
    position: relative;
    top: -1px;
    display: inline-block;
    width: 6px;
    height: 6px;
    vertical-align: middle;
    border-radius: 50%;
  }
  .status-text {
    margin-left: 6px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 12px;
  }
  .status-text-canceled {
    color: #f56c6c;
    margin-left: 6px;
  }

  .processing::after {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    border-width: 1px;
    border-style: solid;
    border-color: inherit;
    border-radius: 50%;
    animation-name: statusProcessing;
    animation-duration: 1.2s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
    content: "";
  }

  @keyframes statusProcessing {
    0% {
      transform: scale(0.8);
      opacity: 0.5;
    }

    100% {
      transform: scale(2.4);
      opacity: 0;
    }
  }
}
</style>

<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :style="{ width: width }"
    size="small"
    clearable
    @change="handleChange"
  >
    <el-option
      v-for="item in branchOptions"
      :key="item.admin_id"
      :label="getIndentedBranchName(item)"
      :value="item.admin_id"
    />
  </el-select>
</template>

<script>
import { getFinancialSettlementBranches } from '@/api/withdrawal'
import { mapGetters } from 'vuex'

/**
 * BranchSelect组件 - 线路分台选择器
 * 说明：
 * 1. 当选择"全部"选项时，组件内部值为"all"，但向父组件传递null
 * 2. 因为传递的是null，所以API请求中不会包含branch_id参数
 * 3. 根据登录账号类型过滤显示选项：
 *    - 总台账号：显示所有下级分台，不显示总台自己
 *    - 分台账号：只显示当前分台相关的选项
 */
export default {
  name: 'BranchSelect',
  props: {
    value: {
      type: [String, Number, null],
      default: 'all'
    },
    placeholder: {
      type: String,
      default: '全部'
    },
    width: {
      type: String,
      default: '200px'
    }
  },
  data() {
    return {
      branchOptions: [],
      selectedValue: this.value === null ? 'all' : this.value
    }
  },
  computed: {
    ...mapGetters(['loginType', 'info'])
  },
  watch: {
    value: {
      handler(val) {
        this.selectedValue = val === null ? 'all' : val
      },
      immediate: true
    }
  },
  created() {
    this.getBranches()
  },
  methods: {
    async getBranches() {
      try {
        const response = await getFinancialSettlementBranches()
        if (response.code === 200) {
          const branchData = response.data

          // 根据登录类型过滤数据
          let filteredOptions = []

          if (this.loginType === 'branch') {
            // 分台账号：只显示当前分台相关的选项
            const currentBranchId = this.info && this.info.admin_id
            filteredOptions = branchData.filter(item =>
              item.admin_id === currentBranchId ||
              item.parent_id === currentBranchId
            )
          } else {
            // 总台账号：显示所有分台，但过滤掉总台自己（level为0且不是人工添加的选项）
            const currentAdminId = this.info && this.info.admin_id
            filteredOptions = branchData.filter(item => {
              // 如果有level字段且为0，且是当前总台账号，则过滤掉
              if (item.level === 0 && item.admin_id === currentAdminId) {
                return false
              }
              return true
            })
          }

          this.branchOptions = filteredOptions

          // 添加"全部"选项
          this.branchOptions.unshift({
            admin_id: 'all',
            mchname: '全部',
            level: 0
          })
        } else {
          this.$message.error(response.message || '获取运营公司列表失败')
        }
      } catch (error) {
        console.error('获取运营公司列表失败:', error)
        this.$message.error('获取运营公司列表失败')
      }
    },
    handleChange(value) {
      // 当选择"全部"时，向父组件传递null而不是'all'
      const outputValue = value === 'all' ? null : value
      this.$emit('input', outputValue)
      this.$emit('change', outputValue)
    },
    getIndentedBranchName(item) {
      const level = typeof item.level === 'number' ? item.level : 0

      if (level === 0) {
        return item.mchname
      }

      const indent = '\u00A0\u00A0\u00A0\u00A0'.repeat(level)
      return indent + '└ ' + item.mchname
    }
  }
}
</script>

<style scoped>
:deep(.el-select-dropdown__item) {
  white-space: pre;
  font-family: monospace;
}
</style>

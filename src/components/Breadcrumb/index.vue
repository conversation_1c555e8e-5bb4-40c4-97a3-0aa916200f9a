<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span
          v-if="
            item.redirect === 'noRedirect' || index === levelList.length - 1
          "
          class="no-redirect"
          :class="{ active: item.meta.title === '待处理' }"
        >{{ item.meta.title }}</span>
        <span v-else>{{ item.meta.title }}</span>
        <span
          v-if="item.meta.title === '待处理' && pendingTotal !== 0"
          style="color: red"
        >
          ( {{ pendingTotal }} 条 )
        </span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { getInvoiceTotal } from '@/api/systemSetup'

export default {
  data() {
    return {
      levelList: null,
      pendingTotal: 0
    }
  },
  watch: {
    $route() {
      this.getBreadcrumb()
      // this.getInvoiceTotal()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  mounted() {
    // this.getInvoiceTotal()
  },
  methods: {
    // 查询待处理发票的总数
    // getInvoiceTotal() {
    //   getInvoiceTotal().then((value) => {
    //     this.pendingTotal = value.data.fapiao_no_handle
    //   })
    // },

    getBreadcrumb() {
      // only show routes with meta.title
      let matched = this.$route.matched.filter(
        (item) => item.meta && item.meta.title
      )
      const first = matched[0]

      if (!this.isDashboard(first)) {
        matched = [{ path: '/', meta: { title: 'CC招车商户管理系统' }}].concat(
          matched
        )
      }

      this.levelList = matched.filter(
        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false
      )
    },
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return (
        name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 45px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
  .active {
    color: red;
    cursor: text;
  }
}
</style>

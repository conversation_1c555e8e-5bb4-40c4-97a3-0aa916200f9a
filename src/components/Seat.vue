<template>
  <div class="seat-container">
    <el-checkbox-group
      v-model="checkseats"
      size="small"
      :disabled="!canCheck"
      @change="seatCheckseats"
    >
      <div v-for="(row, rindex) in seatsList" :key="rindex" class="seat-line">
        <el-checkbox-button
          v-for="(item, i) in row"
          :key="i"
          :label="item"
          :disabled="item.seat_id === 1 || item.optional === 1"
        >
          <p>{{ item.name }}</p>
          <p v-if="item.seat_id != 1" class="price-seat">{{ item.price }} <span style="font-size:12px;">元</span></p>
        </el-checkbox-button>
      </div>
    </el-checkbox-group>
  </div>
</template>
<script>
export default {
  props: {
    canCheck: {
      type: Boolean,
      default: true
    },
    seatPrice: {
      type: Array,
      default: () => []
    },
    seatLayout: {
      type: String,
      default: () => ''
    },
    check: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkseats: []
    }
  },
  computed: {
    seatsList() {
      if (this.seatLayout && this.seatPrice.length > 0) {
        const length = this.seatLayout.split('-')
        // eslint-disable-next-line no-array-constructor
        const seats_ = new Array()
        let index = 0
        for (let i = 0; i < length.length; i++) {
          if (i > 0) {
            index += Number(length[i - 1])
          }
          // eslint-disable-next-line no-array-constructor
          seats_[i] = new Array()
          for (let o = 0; o < Number(length[i]); o++) {
            seats_[i][o] = this.seatPrice[o + index]
          }
        }
        return seats_
      }
      return []
    }
  },
  watch: {
    check: {
      handler(n) {
        this.checkseats = n
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    seatCheckseats(e) {
      this.$emit('seatChange', e)
    }
  }
}
</script>
<style lang="scss">
.price-seat {
  margin-top: 5px;
  font-size: 14px;
}
.seat-line {
  display: flex;
  align-items: center;
  margin: 10px;
  .el-checkbox-button {
    flex: 1;
  }
  .el-checkbox-button__inner {
    height: 45px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  //   justify-content: space-between;
}
</style>

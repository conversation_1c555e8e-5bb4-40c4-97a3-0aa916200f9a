<template>
  <div class="Img-upload-container">
    <el-upload
      class="avatar-uploader"
      :action="actions"
      :headers="headers"
      name="file"
      :show-file-list="false"
      :on-success="handleImgSuccess"
      :on-error="handleImgError"
      drag
      :before-upload="beforeImgUpload"
    >
      <img v-if="value" :src="value" class="avatar">
      <i v-else class="el-icon-plus avatar-uploader-icon" />
    </el-upload>
    <p class="pic_mind">{{ text }}</p>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
export default {
  props: {
    value: {
      type: String,
      default: () => {
        return null
      }
    },
    text: {
      type: String,
      default: () => {
        return null
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_UPLOAD_API + '/admin/fapiao/attachments'
    }
  },

  methods: {
    handleImgError(res, file) {
      this.$message.error(JSON.parse(res.message).message)
    },
    handleImgSuccess(res, file) {
      this.$emit('input', res?.data?.url)
    },
    beforeImgUpload(file) {
      // const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 10
      // if (!isJPG) {
      //   this.$message.error('上传图片只能是 JPG或者png 格式!')
      //   return isJPG
      // }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10mb!')
        return isLt2M
      }
      return true
    }
  }
}
</script>
<style lang="sass">

</style>

<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}">
    <transition name="sidebarLogoFade">
      <a v-if="collapse" key="collapse" class="sidebar-logo-link" href="/">
        <img :src="(setting && setting.LOGO && setting.LOGO.logo_url) || logo" class="sidebar-logo">
        <h1 class="sidebar-title">{{ (setting && setting.LOGO && setting.LOGO.merchant_shortname) || title }} </h1>
      </a>
      <a v-else key="expand" class="sidebar-logo-link" href="/">
        <img :src="(setting && setting.LOGO && setting.LOGO.logo_url) || logo" class="sidebar-logo">
        <h1 class="sidebar-title">{{ (setting && setting.LOGO && setting.LOGO.merchant_shortname) || title }}</h1>
      </a>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Sidebar<PERSON>ogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: 'CC招车管理系统',
      logo: require('@/assets/headlogo.png')
    }
  },
  computed: {
    ...mapGetters(['setting'])
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 64px;
  line-height: 64px;
  background: rgb(36, 37, 37);
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
      object-fit: contain;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 64px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 160px;
      font-size: 15px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>

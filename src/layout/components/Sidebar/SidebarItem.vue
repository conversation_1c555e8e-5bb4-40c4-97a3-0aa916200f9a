<template>
  <div v-if="!item.sidebarHidden && (item.enabled !== false)">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
          (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
          !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)" :disabled="onlyOneChild.disabled"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <template v-if="onlyOneChild.meta.icon || (item.meta && item.meta.icon)">
            <i
              v-if="(onlyOneChild.meta.icon && onlyOneChild.meta.icon.includes('el-icon')) || (item.meta && item.meta.icon.includes('el-icon'))"
              :class="[
                onlyOneChild.meta.icon || (item.meta && item.meta.icon),
                'sub-el-icon',
              ]"
            />
            <svg-icon
              v-else class="menu-el-icon"
              :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
            />
          </template>
          <template #title>
            <span>{{ onlyOneChild.meta.title }}</span>
            <span
              v-if="
                unread &&
                  item.meta &&
                  item.meta.unread &&
                  unread[item.meta.unread]
              " id="unread"
            >{{ unread[item.meta.unread] }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <item
          v-if="item.meta" 
          :icon="item.meta && item.meta.icon" 
          :title="item.meta.title" 
          :unread="unread && item.meta && item.meta.unread && unread[item.meta.unread]"
        />
      </template>
      <sidebar-item
        v-for="child in item.children" :key="child.path" :is-nest="true" :item="child"
        :base-path="resolvePath(child.path)" class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  onlyOneChild: '',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  computed: {
    unread() {
      return this.$store.state.unread.unread
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hidden || item.enabled === false) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
<style>
#unread {
  background: red;
  border-radius: 7px;
  padding: 1px 4px;
  margin-left: 8px;
  font-size: 8px;
  color: white;
}

.sub-el-icon {
  color: currentColor;
  width: 16px !important;
  height: 16px;
  font-size: 16px !important;
}

.menu-el-icon {
  color: currentColor;
  width: 16px !important;
  height: 16px;
  font-size: 16px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  margin-top: -5px;
  vertical-align: middle !important;
}
</style>

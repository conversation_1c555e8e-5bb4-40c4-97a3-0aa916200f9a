<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <!-- <keep-alive :include="hadKeepAlive"> -->
      <router-view :key="key" />
      <!-- </keep-alive> -->
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    },
    hadKeepAlive() {
      return this.$store.state.tagsView.cachedViews.join(',')
    },
    hadKeepPaths() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  watch: {
    $route() {
      // this.addTags()
      // this.moveToCurrentTag()
      // console.log(this.$route)
    }
    // visible(value) {
    //   if (value) {
    //     document.body.addEventListener('click', this.closeMenu)
    //   } else {
    //     document.body.removeEventListener('click', this.closeMenu)
    //   }
    // }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  /* height: calc(100vh - 50px); */
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #eef3fc;
  padding: 12px 0;
}
.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>

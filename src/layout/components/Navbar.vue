<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <div class="right-menu-fill">
        <!-- WebSocket连接状态指示器 -->
        <div class="ws-connection-status">
          <el-tooltip
            :content="isWsConnected ? 'WebSocket连接正常' : 'WebSocket连接异常'"
            placement="bottom"
            effect="dark"
          >
            <i
              :class="isWsConnected ? 'el-icon-connection' : 'el-icon-warning-outline'"
              :style="{ color: isWsConnected ? '#409EFF' : '#F56C6C' }"
            />
          </el-tooltip>
        </div>

        <el-divider v-if="function_permission_definition && function_permission_definition.freshOrderNotice" direction="vertical" />

        <div
          v-if="function_permission_definition && function_permission_definition.freshOrderNotice"
          class="notification"
        >
          <el-popover placement="bottom" width="300" trigger="click" popper-class="top-action">
            <div class="notice-msg-warp-name">
              <i class="el-icon-message-solid" />
              <span>全部通知</span>
            </div>
            <div v-if="local_num" class="notice-msg-list" :class="{ isread: local_num.isread }" @click="jump">
              <el-badge
                class="item" type="primary" :hidden="local_num && local_num.num === 0" :value="local_num.num"
                :max="99"
              >
                <div class="item-meta">
                  <i class="el-icon-warning-outline" />
                  <span class="notice-title">新订单提醒</span>
                </div>
              </el-badge>
              <div class="notice-body">
                您收到了新的订单，请立即到调度中心处理
              </div>
            </div>
            <el-empty v-else :image-size="40" description="暂无通知" />
            <div class="notice-tabs-top">
              <div class="notice-action">
                <div class="left" @click="clearNotice">
                  <i class="el-icon-delete" /> 清空通知
                </div>
                <div class="right" @click="handleChangeBell">
                  {{ isNotification ? "关闭订单提醒" : "开启订单提醒" }}
                </div>
              </div>
            </div>
            <el-badge
              slot="reference" :hidden="local_num && local_num.num === 0" :value="local_num && local_num.num"
              :max="99" class="item" type="primary"
            >
              <el-tooltip
                class="item" effect="dark" :content="isNotification ? '订单提醒已开启' : '订单提醒已关闭'"
                placement="top-start"
              ><i
                :class="isNotification
                  ? 'el-icon-bell'
                  : 'el-icon-close-notification'
                "
              />
              </el-tooltip>
            </el-badge>
          </el-popover>
        </div>

        <el-divider direction="vertical" />
        <el-popover placement="bottom-end" width="200" trigger="click" popper-class="admin-popover">
          <div v-if="parent" class="admin-info">
            <div class="item-admin-info">
              <span class="label">所属总台</span>
              <span class="name">{{ parent }}</span>
            </div>
          </div>
          <div class="cell-action">
            <div class="itemOwnAction" @click="logout">退出</div>
          </div>
          <div slot="reference" class="avatar-wrapper">
            <img src="@/assets/default.png" class="user-avatar">
            <span v-if="account" class="user-name">{{ account }}</span>
            <el-tag v-if="userType" size="small" :type="getTagType">{{ userType }}</el-tag>
            <i class="el-icon-caret-bottom" />
          </div>
        </el-popover>
        <el-divider v-if="userType === '企业商户总台'" direction="vertical" />
        <el-button
          v-if="userType === '企业商户总台'" type="text" style="font-size: 14px; font-weight: 400" size="mini"
          @click="toMain"
        >返回主系统</el-button>
      </div>
      <!-- <span style="font-size: 14px;color: #606266;margin: 0 10px;cursor: pointer" @click="toMain">返回主系统</span> -->
    </div>
    <audio id="audioPlay" src="@/assets/new_order_voice_mute.mp3" hidden="true" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import { getProfiles } from '@/api/user'
import { Notification, MessageBox } from 'element-ui'

export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    return {
      account: '',
      parent: '',
      socket: null,
      userType: ''
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'isNotification', 'info', 'local_num', 'function_permission_definition', 'isWsConnected']),
    getTagType() {
      switch (this.userType) {
        case '企业商户总台':
          return 'success'
        case '企业商户分台':
          return 'warning'
        case '企业商户渠道分台':
          return 'info'
        default:
          return ''
      }
    }
  },
  mounted() {
    this.handleGetInfo()
    if (this.function_permission_definition && this.function_permission_definition.freshOrderNotice) {
      var audio = document.getElementById('audioPlay')
      var promiseaudio = audio.play()
      if (promiseaudio !== undefined) {
        promiseaudio.catch(() => {
          MessageBox.alert(
            '新订单语音提醒功能已开启，可在通知中心关闭提醒功能',
            '订单提醒',
            {
              confirmButtonText: '我知道了'
            }
          )
        })
      }
    }
  },
  methods: {
    handleChangeBell() {
      Notification({
        title: '操作成功',
        message: `新订单提醒功能已${this.isNotification ? '关闭' : '开启'}`,
        duration: 2000,
        type: this.isNotification ? 'warning' : 'success'
      })
      this.$store.commit('settings/SET_NOTIFICATION', !this.isNotification)
    },
    handleGetInfo() {
      getProfiles().then((res) => {
        if (res.code === 200) {
          this.account = res.data.account
          this.parent = res.data.parent
          // 根据group_id和branch_type判断用户类型
          if (res.data.group_id) {
            switch (res.data.group_id) {
              case 2:
                this.userType = '企业商户总台'
                break
              case 3:
                // 分台模式下根据branch_type判断
                if (res.data.branch_type === 0) {
                  this.userType = '企业商户分台'
                } else if (res.data.branch_type === 1) {
                  this.userType = '企业商户渠道分台'
                }
                break
              default:
                this.userType = ''
            }
          }
        }
      })
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    logout() {
      this.$confirm('您确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 显示loading
          const loading = this.$loading({
            lock: true,
            text: '退出登录中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 获取当前URL中的callback参数，如果存在则优先使用URL中的callback
          const urlParams = new URLSearchParams(window.location.search)
          const urlCallback = urlParams.get('callback')

          // 如果URL中有callback参数，更新localStorage中的值，确保使用最新的callback
          if (urlCallback) {
            localStorage.setItem('callback', urlCallback)
          }

          // 获取最新的callback参数
          const callback = localStorage.getItem('callback')

          // 调用登出接口
          await this.$store.dispatch('user/logout')

          // 关闭loading
          loading.close()

          // 跳转到登录页，如果有callback参数则带上
          if (callback) {
            this.$router.push(`/login?callback=${callback}`)
          } else {
            this.$router.push(`/login`)
          }

          // 显示成功信息
          this.$message({
            type: 'success',
            message: '退出登录成功!'
          })
        } catch (error) {
          console.error('退出过程中出错:', error)
          // 即使出错也跳转到登录页，同样携带callback参数
          // 获取当前URL中的callback参数，如果存在则优先使用
          const urlParams = new URLSearchParams(window.location.search)
          const urlCallback = urlParams.get('callback')

          // 如果URL中有callback参数，优先使用
          const callback = urlCallback || localStorage.getItem('callback')

          if (callback) {
            this.$router.push(`/login?callback=${callback}`)
          } else {
            this.$router.push(`/login`)
          }
          this.$message({
            type: 'warning',
            message: '已退出登录'
          })
        }
      }).catch(() => {
        // 用户取消退出
      })
    },
    toMain() {
      window.open(process.env.VUE_APP_BASE_HOST + '/admin_login')
    },
    jump() {
      this.$route.path !== '/operationCenter/dispatching' &&
        this.$router.push({ path: '/operationCenter/dispatching' })
      this.$store.commit('websocket/setLocalNum', { num: 0, isread: true })
      localStorage.setItem(
        this.info.admin_id,
        JSON.stringify({ num: 0, isread: true })
      )
      // this.clearNotice();
    },
    clearNotice() {
      localStorage.removeItem(this.info.admin_id)
      this.$store.commit('websocket/setLocalNum', null)
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-msg-warp-name {
  padding: 12px;
  font-size: 14px;
  color: #2e2e2e;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  span {
    margin-left: 6px;
  }
}

.el-icon-message-solid {
  color: #a6a6a6;
  font-size: 16px;
}

.item-meta {
  flex: 1;
  display: flex;
  align-items: center;

  i {
    font-size: 18px;
    color: #a6a6a6;
  }

  .notice-title {
    display: block;
    flex: 1;
    line-height: 20px;
    color: grey;
    font-size: 13px;
    margin-left: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.notice-body {
  color: #383838;
  font-size: 14px;
  max-height: 100px;
  overflow: hidden;
  line-height: 20px;
}

.notice-action {
  height: 44px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 44px;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 0 0 2px 2px;
  transition: all 0.3s;
  display: flex;
  align-items: center;

  .el-icon-delete {
    margin-right: 5px;
  }

  div {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .left {
    border-right: 1px solid rgba(0, 0, 0, 0.06);
  }
}

.itemOwnAction {
  height: 36px;
  padding-left: 12px;
  line-height: 36px;
  cursor: pointer;

  &:hover {
    background-color: #ebedf1;
  }
}

.navbar {
  height: 45px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 42px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .right-menu-fill {
    display: flex;
    align-items: center;
    margin-right: 15px;

    .ws-connection-status {
      display: flex;
      align-items: center;
      cursor: pointer;

      i {
        font-size: 18px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .notification {
      cursor: pointer;
      display: flex;
      align-items: center;

      span {
        font-size: 14px;
        color: #c0c4cc;
      }

      .isNotification {
        color: rgba(0, 0, 0, 0.85);
      }

      i {
        margin-right: 5px;
      }

      .el-icon-bell {
        color: rgb(166, 166, 166);
        font-size: 18px;
      }

      .el-icon-close-notification {
        color: #c0c4cc;
        font-size: 18px;
      }
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 30px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-wrapper {
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 45px;
      cursor: pointer;
      transition: all 0.3s;

      .user-name {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }

      .user-avatar {
        cursor: pointer;
        width: 24px;
        height: 24px;
        border-radius: 10px;
        margin-right: 8px;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        margin-left: 8px;
        font-size: 12px;
      }
    }
  }
}
</style>
<style lang="scss">
.notice-msg-list {
  display: flex;
  padding: 8px 12px;
  flex-direction: column;
  cursor: pointer;

  &:hover {
    background: #f7f7f7;
  }

  .el-badge__content.is-fixed {
    top: 10px;
    right: 15px;
  }
}

.isread {

  //   background: #efefef;
  .item-meta {
    .notice-title {
      color: #a6a6a6;
    }
  }

  .notice-body {
    color: #a6a6a6;
  }
}

.top-action {
  padding: 0 !important;
}

.cell-action {
  padding: 10px 0;
}

.el-badge {
  z-index: 1;
}

.right-menu .el-badge__content.is-fixed {
  right: 15px;
}

.admin-popover {
  padding: 0 !important;

  .admin-info {
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;

    .item-admin-info {
      display: flex;
      justify-content: space-between;
      padding: 5px 12px;
      color: #666f80;

      .label {
        font-size: 14px;
        min-width: 60px;
        margin-right: 20px;
      }

      .name {
        font-size: 14px;
        color: #464646;
      }
    }
  }
}
</style>
>

<template>
  <div class="config-container">
    <div class="suggestion-container">
      <div class="suggestion-item">
        <div class="suggestion-content">
          <div class="button-group">
            <a href="https://mp.weixin.qq.com/" target="_blank">
              <el-button type="primary" size="default">
                查看微信小程序反馈与投诉[功能异常、产品建议] →
              </el-button>
            </a>
            <a href="https://pay.weixin.qq.com/" target="_blank">
              <el-button type="primary" size="default">
                查看微信小程序反馈与投诉[交易投诉、违规举报] →
              </el-button>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.suggestion-container {
  margin: 0 12px;
    background: #fff;
    padding: 12px;
    border-radius: 4px;
    height: 80vh;

  .suggestion-item {
    .suggestion-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .suggestion-content {
      .button-group {
        display: flex;
        flex-direction: column;
        gap: 16px;

        a {
          text-decoration: none;
        }

      }
    }
  }
}

</style>

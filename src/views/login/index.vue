<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <div class="login-logo">
          <img v-if="merchantInfo.logo_url" :src="merchantInfo.logo_url" alt="Logo" class="logo-img">
          <img v-else src="@/assets/cclogo.png" alt="Logo" class="logo-img">
        </div>
        <div class="login-slogan">
          <h2>{{ merchantInfo.merchant_shortname || '智慧出行网络平台' }}</h2>
          <p>{{ merchantInfo.merchant_fullname || '高效、便捷、安全的商户管理系统' }}</p>
        </div>
      </div>
      <div class="login-right">
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <div class="title-container">
            <p class="title">欢迎登录</p>
            <p class="subtitle">CC招车智慧出行网络平台</p>
          </div>

          <el-tabs v-model="activeTab" class="login-tabs">
            <el-tab-pane label="企业商户总台登录" name="merchant">
              <el-form-item v-if="activeTab === 'merchant'" prop="account" :class="{ isfoucs: focusSign.account }">
                <span class="svg-container">
                  <svg-icon icon-class="user" />
                </span>
                <el-input
                  ref="account"
                  v-model="loginForm.account"
                  placeholder="账户"
                  name="account"
                  type="text"
                  tabindex="1"
                  auto-complete="on"
                  @focus="setFoucs('account', true)"
                  @blur="setFoucs('account', false)"
                />
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="企业商户分台登录" name="branch">
              <el-form-item v-if="activeTab === 'branch'" prop="branchAccount" :class="{ isfoucs: focusSign.branchAccount }">
                <span class="svg-container">
                  <svg-icon icon-class="user" />
                </span>
                <el-input
                  ref="branchAccount"
                  v-model="loginForm.branchAccount"
                  placeholder="分台账号/分台手机号码"
                  name="branchAccount"
                  type="text"
                  tabindex="1"
                  auto-complete="on"
                  @focus="setFoucs('branchAccount', true)"
                  @blur="setFoucs('branchAccount', false)"
                />
              </el-form-item>
              <div v-if="activeTab === 'branch'" class="branch-login-tip">
                <i class="el-icon-info" />
                <span>分台手机号码请联系商户总台管理员获取</span>
              </div>
            </el-tab-pane>
          </el-tabs>

          <el-form-item prop="password" :class="{ isfoucs: focusSign.password }">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
              @focus="setFoucs('password', true)"
              @blur="setFoucs('password', false)"
            />
            <span class="show-pwd" @click="showPwd">
              <svg-icon
                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
              />
            </span>
          </el-form-item>

          <div class="login-options">
            <!-- <el-checkbox v-model="rememberMe">记住我</el-checkbox> -->
            <!-- <a href="#" class="forgot-password">忘记密码?</a> -->
          </div>

          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click.native.prevent="handleLogin"
          >登录</el-button>

          <div class="login-footer">
            <p>登录即表示您同意 <a href="https://www.cczhaoche.com/passenger_agreement/callback/J2KFC221309746013AC554571FBD180E1C8L5I61236">服务条款</a> 和 <a href="#">隐私政策</a></p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { login } from '@/api/user.js'
import { getMerchantLogoByCallback } from '@/api/user.js'
import { branchLogin } from '@/api/branch.js'
import { Message } from 'element-ui'
import { Token } from '@/utils/token'
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入账户名'))
      } else {
        callback()
      }
    }
    const validateBranchAccount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入分台账号'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    return {
      activeTab: 'merchant',
      focusSign: {
        account: false,
        branchAccount: false,
        password: false
      },
      loginForm: {
        account: '',
        branchAccount: '',
        password: '',
        callback: this.$route.query.callback
      },
      loginRules: {
        account: [
          { required: true, trigger: 'blur', validator: validateUsername }
        ],
        branchAccount: [
          { required: true, trigger: 'blur', validator: validateBranchAccount }
        ],
        password: [
          { required: true, trigger: 'blur', validator: validatePassword }
        ]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      rememberMe: false,
      merchantInfo: {
        logo_url: '',
        merchant_shortname: '',
        merchant_fullname: ''
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
        // 监听路由变化，更新callback参数
        if (route.query && route.query.callback) {
          this.loginForm.callback = route.query.callback
          localStorage.setItem('callback', route.query.callback)
          console.log('路由变化，更新callback参数:', this.loginForm.callback)

          // 获取商户LOGO
          this.fetchMerchantLogo(route.query.callback)
        }
      },
      immediate: true
    }
  },
  created() {
    // 在组件创建时获取并设置URL中的callback参数
    if (this.$route.query.callback) {
      this.loginForm.callback = this.$route.query.callback
      localStorage.setItem('callback', this.$route.query.callback)
      console.log('获取到callback参数:', this.loginForm.callback)

      // 获取商户LOGO
      this.fetchMerchantLogo(this.$route.query.callback)
    }
  },
  methods: {
    fetchMerchantLogo(callback) {
      getMerchantLogoByCallback(callback)
        .then(res => {
          if (res.code === 200 && res.data) {
            // 更新商户信息对象
            this.merchantInfo = res.data
            console.log('获取到商户信息:', this.merchantInfo)
          }
        })
        .catch(error => {
          console.error('获取商户LOGO失败:', error)
        })
    },
    setFoucs(key, val) {
      this.focusSign[key] = val
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true

          // 根据当前激活的标签页决定使用哪个账号字段和登录API
          const loginData = {
            password: this.loginForm.password,
            callback: this.loginForm.callback
          }

          if (this.activeTab === 'merchant') {
            loginData.account = this.loginForm.account
            // 直接调用商户登录API
            login(loginData)
              .then((res) => {
                if (res.code === 200) {
                  Message({
                    message: '登录成功',
                    type: 'success'
                  })
                  Token.set(res.data.access_token)
                  // 存储登录类型，用于后续获取用户信息和登出操作
                  localStorage.setItem('loginType', this.activeTab)
                  this.$router.push('/#/dataOverview')
                }
              })
              .catch((error) => {
                console.error('登录失败:', error)
                let errorMsg = '登录失败，请检查账号和密码'
                if (error.response) {
                  const responseData = error.response.data
                  // 如果状态码是401，直接停止加载并返回登录页
                  if (error.response.status === 401) {
                    errorMsg = '账号或密码错误'
                    this.loading = false
                    return
                  }
                  // 优先使用后端返回的错误消息
                  if (responseData) {
                    if (responseData.message) {
                      errorMsg = responseData.message
                    }
                    // 如果有更详细的错误信息
                    if (responseData.data && responseData.data.error) {
                      errorMsg = responseData.data.error.message || errorMsg
                    }
                  }
                }
                Message({
                  message: errorMsg,
                  type: 'error',
                  duration: 5000
                })
              })
              .finally(() => {
                this.loading = false
              })
          } else if (this.activeTab === 'branch') {
            loginData.account = this.loginForm.branchAccount
            // 确保callback参数被正确传递
            console.log('分台登录数据:', loginData)
            // 直接调用分台登录API
            branchLogin(loginData)
              .then((res) => {
                if (res.code === 200) {
                  Message({
                    message: '登录成功',
                    type: 'success'
                  })
                  Token.set(res.data.access_token)
                  // 存储登录类型，用于后续获取用户信息和登出操作
                  localStorage.setItem('loginType', this.activeTab)
                  this.$router.push('/#/dataOverview')
                }
              })
              .catch((error) => {
                console.error('登录失败:', error)
                let errorMsg = '登录失败，请检查账号和密码'
                if (error.response) {
                  // 如果状态码是401，直接停止加载并返回登录页
                  if (error.response.status === 401) {
                    errorMsg = '账号或密码错误'
                    this.loading = false
                    return
                  }
                  const responseData = error.response.data
                  if (responseData) {
                    if (responseData.message) {
                      errorMsg = responseData.message
                    }
                    // 如果有更详细的错误信息
                    if (responseData.data && responseData.data.error) {
                      errorMsg = responseData.data.error.message || errorMsg
                    }
                  } else if (error.response.status === 422) {
                    errorMsg = '请填写完整的登录信息'
                  }
                }
                Message({
                  message: errorMsg,
                  type: 'error',
                  duration: 5000
                })
              })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: rgb(70, 70, 70);
$primary-color: #0067e1;
$text-color: #333;
$border-color: #e4e7ed;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-tabs__item {
    color: #606266;
    font-weight: 500;

    &.is-active {
      color: $primary-color;
    }

    &:hover {
      color: $primary-color;
    }
  }

  .el-tabs__active-bar {
    background-color: $primary-color;
  }

  .el-input {
    display: inline-block;
    flex: 1;

    input {
      background: transparent;
      border: 0;
      padding: 6.5px 11px;
      -webkit-appearance: none;
      border-radius: 0;
      color: $text-color;
      width: 100%;
      height: 25px;
      line-height: 25px;
      caret-color: $cursor;
      background-color: #fff;
      background-image: none;
      &:-webkit-autofill {
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid $border-color;
    border-radius: 4px;
    background: #fff;
    color: $text-color;
    padding: 0 11px;
    transition: all 0.3s;
    margin-bottom: 24px;
    position: relative;

    &:hover {
      border-color: $primary-color;
    }
  }

  .isfoucs {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba(0, 103, 225, 0.2);
    outline: 0;
  }

  .el-form-item__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .login-tabs {
    margin-bottom: 20px;
  }

  .help-icon {
    color: #909399;
    cursor: pointer;
    font-size: 16px;
    margin-right: 5px;
  }

  .branch-login-tip {
    margin-top: -10px;
    margin-bottom: 15px;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    background-color: #f4f6fa;
    padding: 8px 12px;
    border-radius: 4px;

    i {
      margin-right: 5px;
      color: #E6A23C;
      font-size: 14px;
    }
  }

  .el-checkbox__label {
    color: #606266;
  }

  .el-button--primary {
    background-color: $primary-color;
    border-color: $primary-color;

    &:hover, &:focus {
      background-color: lighten($primary-color, 10%);
      border-color: lighten($primary-color, 10%);
    }
  }

  .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 12px;
    margin-top: 3px;
  }

  .el-form-item.is-error {
    border-color: #f56c6c;
    margin-bottom: 30px;
  }

  .el-form-item.is-error .el-input__inner {
    border-color: transparent;
  }
}
</style>

<style lang="scss" scoped>
$bg: #f5f7fa;
$dark_gray: #889aa4;
$light_gray: #333;
$primary-color: #0067e1;

.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .login-box {
    display: flex;
    width: 900px;
    height: 560px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .login-left {
    width: 40%;
    background: linear-gradient(135deg, #0067e1, #004db3);
    color: white;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .login-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      .logo-img {
        width: 100px;
        height: 100px;
        object-fit: contain;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.15);
        padding: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.25);

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
      }
    }

    .login-slogan {
      margin-bottom: 60px;
      text-align: center;

      h2 {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      p {
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  .login-right {
    width: 60%;
    padding: 40px;
    display: flex;
    align-items: center;
  }

  .login-form {
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
  }

  .title-container {
    margin-bottom: 30px;

    .title {
      font-size: 28px;
      font-weight: 600;
      color: $light_gray;
      margin: 0 0 10px 0;
    }

    .subtitle {
      font-size: 16px;
      color: #606266;
      margin: 0;
    }
  }

  .svg-container {
    color: #889aa4;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
  }

  .show-pwd {
    font-size: 16px;
    color: #889aa4;
    cursor: pointer;
    user-select: none;
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .forgot-password {
      color: $primary-color;
      font-size: 14px;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-button {
    width: 100%;
    padding: 12px 15px;
    font-size: 16px;
    border-radius: 4px;
  }

  .login-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #606266;

    a {
      color: $primary-color;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    .login-box {
      width: 90%;
      flex-direction: column;
      height: auto;
    }

    .login-left {
      width: 100%;
      padding: 30px;

      .login-slogan {
        margin-bottom: 30px;
      }
    }

    .login-right {
      width: 100%;
      padding: 30px;
    }
  }
}
</style>

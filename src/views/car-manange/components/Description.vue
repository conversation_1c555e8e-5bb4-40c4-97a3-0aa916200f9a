<template>
  <div v-loading="loading" class="description-warp">
    <el-descriptions title="车辆登记证" direction="vertical" size="small" :column="3" border>
      <el-descriptions-item label="车辆所有人">{{ description.vehicle_owner }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆号码">{{ description.license_plate_number || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆类型">{{ description.vehicle_type ||'-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆品牌">{{ description.brand_name || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆型号">{{ description.series_name || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆颜色">{{ description.vehicle_color | formatColor }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆识别代号">{{ description.vehicle_vin || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="发动机号">{{ description.engine_no || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆燃料类型">{{ description.vehicle_fuel_type || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="发动机排量">{{ description.engine_capacity || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="使用性质">{{ description.usage || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="行驶总里程(km)">{{ description.total_miles || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆所在城市">{{ description.address || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆座位数（含司机座位数）">{{ description.vehicle_seats || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="行驶证" direction="vertical" size="small" :column="3" border>
      <el-descriptions-item label="行驶证-注册日期">{{ description.driving_license_registration_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="行驶证-发证日期">{{ description.driving_license_issue_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆检修状态">
        <Tablestatus v-if="description.vehicle_inspection_status === '未检修'" mode="warning" word="未检修" />
        <Tablestatus v-else-if="description.vehicle_inspection_status === '检修中'" mode="processing" word="检修中" />
        <Tablestatus v-else-if="description.vehicle_inspection_status === '已检修'" mode="success" word="已检修" />
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="车辆下次年检时间">{{ description.vehicle_next_annual_inspection_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆年审状态">
        <Tablestatus v-if="description.vehicle_annual_inspection_status === '待审核'" mode="warning" word="待审核" />
        <Tablestatus v-else-if="description.vehicle_annual_inspection_status === '已审核'" mode="success" word="已审核" />
        <span v-else>-</span>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="其他" direction="vertical" size="small" :column="4" border>
      <el-descriptions-item label="车型">{{ description.vehicle_comfort_property | formatComfort }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌颜色">{{ description.license_plate_color || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆登记证照片">
        <el-image
          style="width: 60px; height: 60px"
          fit="contain"
          :src="description.vehicle_registration_certificate_photo"
          :preview-src-list="[description.vehicle_registration_certificate_photo]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="行驶证照片">
        <el-image
          style="width: 60px; height: 60px"
          fit="contain"
          :src="description.driving_license_photo"
          :preview-src-list="[description.driving_license_photo]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="是否配备司机">
        <Tablestatus v-if="description.is_equipped == 1" mode="success" word="已配备" />
        <Tablestatus v-else-if="description.is_equipped ==0" mode="default" word="未配备" />
        <span v-else>-</span>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="发票信息" direction="vertical" size="small" :column="4" border>
      <el-descriptions-item label="发票打印设备序列号">{{ description.invoice_equipment_no || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="定位装置信息" direction="vertical" size="small" :column="4" border>
      <el-descriptions-item label="卫星定位装置品牌">{{ description.satellite_equipment_brand || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="卫星定位装置型号">{{ description.satellite_equipment_model || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="卫星定位装置IMEI号">{{ description.satellite_equipment_imei || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="卫星定位设备安装日期">{{ description.satellite_equipment_install_date || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="保险信息" direction="vertical" size="small" :column="3" border>
      <el-descriptions-item label="保险公司名称">{{ description.insurance_company_name || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="保险号">{{ description.insurance_no || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="保险类型">
        <span v-if="description.insurance_type">
          <el-tag v-for="tag in description.insurance_type.split(',')" :key="tag" size="mini" type="info">{{ tag }}</el-tag>
        </span>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="保险金额">{{ description.insurance_amount || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="保险生效时间">{{ description.insurance_effective_time || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="保险到期时间">{{ description.insurance_expiration_time || '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="道路运输证" direction="vertical" size="small" :column="3" border>
      <el-descriptions-item label="道路运输证发证机构全称">{{ description.vehicle_trans_agency || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="道路运输证号">{{ description.vehicle_trans_no || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="运价类型">{{ description.fare_type || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="道路经营区域">{{ description.vehicle_trans_area || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="道路运输证有效期起">{{ description.vehicle_trans_start_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="道路运输证有效期止">{{ description.vehicle_trans_stop_date || '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="驾驶证" direction="vertical" size="small" :column="5" border>
      <el-descriptions-item label="驾驶证号码">{{ description.driving_license_no || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="驾驶证姓名">{{ description.driving_license_name || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="驾驶证有效期起">{{ description.driving_license_start_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="驾驶证有效期止">{{ description.driving_license_stop_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="驾驶证照片">
        <el-image
          style="width: 60px; height: 60px"
          fit="contain"
          :src="description.driving_license_photos"
          :preview-src-list="[description.driving_license_photos]"
        />
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="从业人员资格证" direction="vertical" size="small" :column="4" border>
      <el-descriptions-item label="从业人员资格证有效期起">{{ description.practitioner_certificate_start_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="从业人员资格证有效期止">{{ description.practitioner_certificate_stop_date || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="从业人员资格证号">{{ description.practitioner_certificate_no || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="从业人员资格证照片">
        <el-image
          style="width: 60px; height: 60px"
          fit="contain"
          :src="description.practitioner_certificate_photo"
          :preview-src-list="[description.practitioner_certificate_photo]"
        />
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { properties } from '@/api/service'
let app = null
export default {
  filters: {
    formatColor(color) {
      return app.properties?.color_properties[color] || '-'
    },
    formatComfort(type) {
      return app.properties?.comfort_properties[type] || '-'
    }
  },
  props: {
    description: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      loading: false,
      properties: {
        color_properties: {},
        comfort_properties: {},
        vehicle_seats: {}
      }
    }
  },
  watch: {
    description: {
      handler (n) {
        if (n && Object.keys(n).length > 0) {
          this.fetchProperties()
        }
      },
      immediate: true
    }
  },
  beforeCreate() {
    // 将vue实例赋值给全局变量app
    app = this
  },
  methods: {
    async fetchProperties () {
      this.loading = true
      const { data } = await properties()
      this.loading = false
      this.properties = data
    }
  }
}
</script>

<template>
  <div class="from-container">
    <el-form
      ref="dataForm"
      :model="temp"
      :rules="rules"
      :inline="true"
      label-position="right"
      label-width="155px"
      size="medium"
    >
      <div class="item-warp-form">
        <h6 class="item-title">车辆登记证</h6>
        <div class="item-warp-form-content">
          <el-form-item label="车辆所有人" prop="vehicle_owner">
            <el-input
              v-model="temp.vehicle_owner"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="车辆号码" prop="license_plate_number">
            <el-input
              v-model="temp.license_plate_number"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="车辆类型" prop="vehicle_type">
            <el-select
              v-model="temp.vehicle_type"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="新能源" value="新能源" />
              <el-option label="轿车" value="轿车" />
              <el-option label="SUV" value="SUV" />
              <el-option label="MPV" value="MPV" />
              <el-option label="跑车" value="跑车" />
              <el-option label="商用车" value="商用车" />
            </el-select>
          </el-form-item>
          <el-form-item label="车辆品牌" prop="brand_name">
            <el-select
              ref="selectScroll"
              v-model="temp.brand_name"
              v-el-select-loadmore="loadmore"
              filterable
              remote
              class="formItemWarp"
              :remote-method="getRemoteData"
              placehgitolder="请选择"
              @change="(val) => brandChange(val)"
            >
              <el-option
                v-for="item in brandList"
                :key="item.external_id"
                :label="item.name"
                :value="item.name"
              >
                {{ item.first_letter }} - {{ item.name }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆型号" prop="series_name">
            <el-select
              ref="selectScroll"
              v-model="temp.series_name"
              v-el-select-loadmore="loadmoreService"
              :disabled="!temp.brand_name"
              filterable
              remote
              class="formItemWarp"
              :remote-method="getSeriesData"
              placehgitolder="请选择"
              @change="seriesChange"
            >
              <el-option
                v-for="item in serviceList"
                :key="item.external_id"
                :label="item.name + '-' + item.series_place"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="车辆颜色" prop="vehicle_color">
            <el-select
              v-model="temp.vehicle_color"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option
                v-for="item in properties.color_properties"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="车辆识别代号">
            <el-input
              v-model="temp.vehicle_vin"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="发动机号">
            <el-input
              v-model="temp.engine_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="车辆燃料类型">
            <el-select
              v-model="temp.vehicle_fuel_type"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="汽油" value="汽油" />
              <el-option label="柴油" value="柴油" />
              <el-option label="气体燃料" value="气体燃料" />
              <el-option label="甲醇燃料" value="甲醇燃料" />
              <el-option label="纯电动" value="纯电动" />
              <el-option label="混动" value="混动" />
            </el-select>
          </el-form-item>
          <el-form-item label="发动机排量">
            <el-select
              v-model="temp.engine_capacity"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="1.0L及以下" value="1.0L及以下" />
              <el-option label="1.1-1.6L" value="1.1-1.6L" />
              <el-option label="1.7-2.0L" value="1.7-2.0L" />
              <el-option label="2.1-2.5L" value="2.1-2.5L" />
              <el-option label="2.6-3.0L" value="2.6-3.0L" />
              <el-option label="3.1-4.0L" value="3.1-4.0L" />
              <el-option label="4.0L及以上" value="4.0L及以上" />
            </el-select>
          </el-form-item>
          <el-form-item label="使用性质">
            <el-select
              v-model="temp.usage"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="营运车辆" value="营运车辆" />
              <el-option label="租赁车辆" value="租赁车辆" />
              <el-option label="非营运车辆" value="非营运车辆" />
            </el-select>
          </el-form-item>
          <el-form-item label="行驶总里程(km)" prop="total_miles">
            <el-input
              v-model="temp.total_miles"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="车辆所在城市" prop="address">
            <el-select
              ref="selectScroll"
              v-model="temp.address"
              v-el-select-loadmore="loadmoreCity"
              filterable
              remote
              class="formItemWarp"
              :remote-method="getCityData"
              placehgitolder="请选择"
              @change="cityChange"
            >
              <el-option
                v-for="item in cityList"
                :key="item.adcode"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="车辆座位数（含司机座位数）" prop="vehicle_seats">
            <!-- <el-select v-model="temp.vehicle_seats" placeholder="请选择" class="formItemWarp">
              <el-option v-for="item in properties.vehicle_seats" :key="item.value" :label="item.name+'座'" :value="item.value" />
            </el-select> -->
            <el-input
              v-model="temp.vehicle_seats"
              class="formItemWarp"
              placeholder="请输入车辆座位数"
            />
          </el-form-item>
        </div>
      </div>
      <div class="item-warp-form">
        <h6 class="item-title">行驶证</h6>
        <div class="item-warp-form-content">
          <el-form-item
            label="行驶证-注册日期"
            prop="driving_license_registration_date"
          >
            <el-date-picker
              v-model="temp.driving_license_registration_date"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item
            label="行驶证-发证日期"
            prop="driving_license_issue_date"
          >
            <el-date-picker
              v-model="temp.driving_license_issue_date"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="车辆检修状态" prop="vehicle_inspection_status">
            <el-select
              v-model="temp.vehicle_inspection_status"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="已检修" value="已检修" />
              <el-option label="检修中" value="检修中" />
              <el-option label="未检修" value="未检修" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="车辆下次年检时间"
            prop="vehicle_next_annual_inspection_date"
          >
            <el-date-picker
              v-model="temp.vehicle_next_annual_inspection_date"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item
            label="车辆年审状态"
            prop="vehicle_annual_inspection_status"
          >
            <el-select
              v-model="temp.vehicle_annual_inspection_status"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="待审核" value="待审核" />
              <el-option label="已审核" value="已审核" />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <!-- 其他 -->
      <div class="item-warp-form">
        <h6 class="item-title">其他</h6>
        <div class="item-warp-form-content">
          <el-form-item label="车型" prop="vehicle_comfort_property">
            <el-select
              v-model="temp.vehicle_comfort_property"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option
                v-for="item in properties.comfort_properties"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="车牌颜色" prop="license_plate_color">
            <el-select
              v-model="temp.license_plate_color"
              placeholder="请选择"
              class="formItemWarp"
            >
              <el-option label="蓝牌" value="蓝牌" />
              <el-option label="绿牌" value="绿牌" />
              <el-option label="黄牌" value="黄牌" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="车辆登记证照片"
            prop="vehicle_registration_certificate_photo"
            class="cover_url_content"
          >
            <img-upload v-model="temp.vehicle_registration_certificate_photo" />
          </el-form-item>
          <el-form-item
            label="行驶证照片"
            prop="driving_license_photo"
            class="cover_url_content"
          >
            <img-upload v-model="temp.driving_license_photo" />
          </el-form-item>
        </div>
      </div>
      <div class="item-warp-form">
        <h6 class="item-title">发票信息</h6>
        <div class="item-warp-form-content">
          <el-form-item label="发票打印设备序列号">
            <el-input
              v-model="temp.invoice_equipment_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
        </div>
      </div>
      <!-- 定位装置信息 -->
      <div class="item-warp-form">
        <h6 class="item-title">定位装置信息</h6>
        <div class="item-warp-form-content">
          <el-form-item label="卫星定位装置品牌">
            <el-input
              v-model="temp.satellite_equipment_brand"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="卫星定位装置型号">
            <el-input
              v-model="temp.satellite_equipment_model"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="卫星定位装置IMEI号">
            <el-input
              v-model="temp.satellite_equipment_imei"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item
            label="卫星定位设备安装日期"
            prop="satellite_equipment_install_date"
          >
            <el-date-picker
              v-model="temp.satellite_equipment_install_date"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
        </div>
      </div>
      <!-- 保险信息 -->
      <div class="item-warp-form">
        <h6 class="item-title">保险信息</h6>
        <div class="item-warp-form-content">
          <el-form-item label="保险公司名称">
            <el-input
              v-model="temp.insurance_company_name"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="保险号">
            <el-input
              v-model="temp.insurance_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="保险类型(可多选)">
            <el-select
              v-model="temp.insurance_type"
              multiple
              placeholder="请选择"
              clearable
              class="formItemWarp"
            >
              <el-option label="交强险" value="交强险" />
              <el-option label="商业三者" value="商业三者" />
              <el-option label="车损险" value="车损险" />
              <el-option label="座位险" value="座位险" />
            </el-select>
          </el-form-item>
          <el-form-item label="保险金额">
            <el-input
              v-model="temp.insurance_amount"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="保险生效时间" prop="insurance_effective_time">
            <el-date-picker
              v-model="temp.insurance_effective_time"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="保险到期时间" prop="insurance_expiration_time">
            <el-date-picker
              v-model="temp.insurance_expiration_time"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
        </div>
      </div>
      <!-- 道路运输证 -->
      <div class="item-warp-form">
        <h6 class="item-title">道路运输证</h6>
        <div class="item-warp-form-content">
          <el-form-item
            label="道路运输证发证机构全称"
            prop="vehicle_trans_agency"
          >
            <el-input
              v-model="temp.vehicle_trans_agency"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="道路运输证号" prop="vehicle_trans_no">
            <el-input
              v-model="temp.vehicle_trans_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="运价类型" prop="fare_type">
            <el-input
              v-model="temp.fare_type"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="道路经营区域" prop="vehicle_trans_area">
            <el-input
              v-model="temp.vehicle_trans_area"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item
            label="道路运输证有效期起"
            prop="vehicle_trans_start_date"
          >
            <el-date-picker
              v-model="temp.vehicle_trans_start_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item
            label="道路运输证有效期止"
            prop="vehicle_trans_stop_date"
          >
            <el-date-picker
              v-model="temp.vehicle_trans_stop_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="是否配备司机" prop="is_equipped">
            <el-select
              v-model="temp.is_equipped"
              placeholder="请选择"
              clearable
              class="formItemWarp"
            >
              <el-option label="已配备" :value="true" />
              <el-option label="未配备" :value="false" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 驾驶证 -->
      <div class="item-warp-form">
        <h6 class="item-title">驾驶证</h6>
        <div class="item-warp-form-content">
          <el-form-item label="驾驶证号码" prop="driving_license_no">
            <el-input
              v-model="temp.driving_license_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="驾驶证姓名" prop="driving_license_name">
            <el-input
              v-model="temp.driving_license_name"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item
            label="驾驶证有效期起"
            prop="driving_license_start_date"
          >
            <el-date-picker
              v-model="temp.driving_license_start_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="驾驶证有效期止" prop="driving_license_stop_date">
            <el-date-picker
              v-model="temp.driving_license_stop_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item
            label="驾驶证上传"
            prop="driving_license_photos"
            class="cover_url_content"
          >
            <img-upload v-model="temp.driving_license_photos" />
          </el-form-item>
        </div>
      </div>

      <!-- 从业资格证信息 -->
      <div class="item-warp-form">
        <h6 class="item-title">从业资格证</h6>
        <div class="iterm-warp-form-content">
          <el-form-item
            label="从业人员资格证有效期起"
            prop="practitioner_certificate_start_date"
          >
            <el-date-picker
              v-model="temp.practitioner_certificate_start_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item
            label="从业人员资格证有效期止"
            prop="practitioner_certificate_stop_date"
          >
            <el-date-picker
              v-model="temp.practitioner_certificate_stop_date"
              type="date"
              class="formItemWarp"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="从业资格证号">
            <el-input
              v-model="temp.practitioner_certificate_no"
              class="formItemWarp"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item
            label="从业资格证照片"
            prop="practitioner_certificate_photo"
            class="cover_url_content"
          >
            <img-upload v-model="temp.practitioner_certificate_photo" />
          </el-form-item>
        </div>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('input', false)"> 取消 </el-button>
      <el-button type="primary" @click="setData"> 确认 </el-button>
    </div>
  </div>
</template>

<script>
import {
  carBrands,
  carServices,
  properties,
  carCities,
  addCar,
  putCar
} from '@/api/service'

export default {
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        // console.log("el=================", el);
        // console.log("binding=============", binding);
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight

          if (condition) {
            binding.value()
          }
        })
      }
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    dialogStatus: {
      default: () => '',
      type: String
    },
    temp: {
      default: () => {},
      type: Object
    }
  },

  data() {
    return {
      properties: {}, // 相关配置
      brandList: [], // 品牌数据源
      serviceList: [], // 品牌车型数据
      cityList: [],
      city_query: {
        keywords: '',
        page: 1
      },
      brand_query: {
        keywords: '',
        page: 1
      },
      totalPage: 0,
      totalCity: 0,
      series_query: {
        keywords: '',
        page: 1,
        brand: ''
      },
      totalSeries: 0,

      rules: {
        vehicle_owner: [{ required: true, message: '请输入', trigger: 'blur' }],
        license_plate_number: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        // total_miles: [{ required: true, message: '请输入', trigger: 'blur' }],
        // vehicle_trans_area: [{ required: true, message: '请输入', trigger: 'blur' }],
        // fare_type: [{ required: false, message: '请输入', trigger: 'blur' }],
        vehicle_trans_no: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        driving_license_no: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        driving_license_name: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        vehicle_type: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        brand_name: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        series_name: [{ required: true, message: '请选择', trigger: 'change' }],
        // vehicle_color: [{ required: true, message: '请选择', trigger: 'change' }],
        vehicle_seats: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
        // address: [{ required: true, message: '请选择', trigger: 'change' }],
        // driving_license_registration_date: [{ required: true, message: '请选择', trigger: 'change' }],
        // vehicle_comfort_property: [{ required: true, message: '请选择', trigger: 'change' }],
        // license_plate_color: [{ required: true, message: '请选择', trigger: 'change' }],
        // driving_license_photo: [{ required: true, message: '请上传', trigger: 'change' }],
        // vehicle_registration_certificate_photo: [{ required: true, message: '请上传', trigger: 'change' }],
        // vehicle_trans_start_date: [{ required: true, message: '请选择', trigger: 'change' }],
        // vehicle_trans_stop_date: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.getRemoteData()
    this.getCityData()
    this.fetchProperties()
    this.$nextTick(() => {
      this.$refs['dataForm'].clearValidate()
    })
  },
  methods: {
    brandChange(val) {
      const brand_id = this.brandList.find((o) => o.name === val).external_id
      this.temp['brand_id'] = brand_id
      this.series_query.brand = brand_id
      this.$set(this.temp, 'series_id', '')
      this.$set(this.temp, 'series_name', '')
      this.getSeriesData()
    },
    cityChange(val) {
      const address_code = this.cityList.find((o) => o.name === val).adcode
      this.temp['address_code'] = address_code
    },
    seriesChange(val) {
      const series_id = this.serviceList.find(
        (o) => o.name === val
      ).external_id
      this.temp['series_id'] = series_id
    },
    async fetchProperties() {
      const { data } = await properties()
      const obj_ = data || {}
      const properties_ = {}
      for (const o in obj_) {
        const items = []
        for (const i in obj_[o]) {
          items.push({
            name: obj_[o][i],
            value: i
          })
        }
        properties_[o] = items
      }
      this.properties = properties_
    },
    loadmore() {
      if (this.brand_query.page >= this.totalPage) {
        return
      }
      this.brand_query.page++
      this.fetchBrandList('more')
    },
    loadmoreCity() {
      if (this.city_query.page >= this.totalCity) {
        return
      }
      this.city_query.page++
      this.fetchCityList('more')
    },
    loadmoreService() {
      if (this.series_query.page >= this.totalSeries) {
        return
      }
      this.series_query.page++
      this.fetchServiceList('more')
    },
    // clearKeyword(val) {
    //   if (!val) {
    //     this.brand_query.keywords = "";
    //     this.brand_query.page = 1;
    //     this.fetchBrandList("fresh");
    //   }
    // },
    // clearSeriesword(val) {
    //   if (!val) {
    //     this.series_query.keywords = "";
    //     this.series_query.page = 1;
    //     this.fetchServiceList("fresh");
    //   }
    // },
    async fetchBrandList(freshtype = 'fresh') {
      const { data } = await carBrands(this.brand_query)
      this.brandList =
        freshtype === 'fresh' ? data.data : [...this.brandList, ...data.data]
      this.totalPage = data.meta.pagination.total_pages
    },
    async fetchCityList(freshtype = 'fresh') {
      const { data } = await carCities(this.city_query)
      this.cityList =
        freshtype === 'fresh' ? data.data : [...this.cityList, ...data.data]
      this.totalCity = data.meta.pagination.total_pages
    },
    async fetchServiceList(freshtype = 'fresh') {
      if (!this.series_query.brand) {
        this.serviceList = []
        this.totalSeries = 0
        return
      }
      const { data } = await carServices(this.series_query)
      this.serviceList =
        freshtype === 'fresh' ? data.data : [...this.serviceList, ...data.data]
      this.totalSeries = data.meta.pagination.total_pages
    },
    async getRemoteData(val = '') {
      this.brand_query.page = 1
      this.brand_query.keywords = val
      this.fetchBrandList('fresh')
    },
    async getCityData(val = '') {
      this.city_query.page = 1
      this.city_query.keywords = val
      this.fetchCityList('fresh')
    },
    async getSeriesData(val = '') {
      this.series_query.page = 1
      this.series_query.keywords = val
      if (!this.series_query.brand) {
        this.serviceList = []
        this.totalSeries = 0
        return
      }
      this.fetchServiceList('fresh')
    },
    async setData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData['insurance_type'] = this.temp.insurance_type.join(',')
          await (this.dialogStatus === 'create'
            ? addCar(tempData)
            : putCar(tempData))
          this.$emit('input', false)
          this.$emit('refresh')
          this.$notify({
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.item-title {
  position: relative;
  font-size: 14px;
  margin-bottom: 20px;
  &:before {
    width: 4px;
    border-radius: 5px;
    height: 60%;
    background: #0067e1;
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    margin: auto;
    content: "";
    display: block;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>

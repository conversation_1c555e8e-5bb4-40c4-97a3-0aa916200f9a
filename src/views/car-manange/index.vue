<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button
          type="primary"
          size="small"
          @click="handleCreate"
        >添加车辆</el-button>
      </div>
      <div class="filter-container">
        <el-select
          v-model="searchQuery.filter_key"
          placeholder="关键词类型"
          size="small"
          class="mini-condition"
        >
          <el-option label="车牌号码" value="license_plate_number" />
          <el-option label="车牌尾号" value="car_tail_number" />
          <el-option label="车辆所有人" value="vehicle_owner" />
          <el-option label="发票代码" value="invoice_code" />
          <el-option label="车系名称" value="series_name" />
        </el-select>
        <el-input
          v-model="searchQuery.filter_keyword"
          clearable
          size="mini"
          class="filter-item mini-condition"
          placeholder="请输入关键字"
        />
        <span class="label-key">添加日期</span>
        <el-date-picker
          v-model="date"
          size="small"
          type="daterange"
          value-format="yyyy-MM-dd"
          class="daterange-condition"
          range-separator="至"
          start-placeholder="请选择日期"
          end-placeholder="请选择日期"
        />
        <!-- <span class="label-key">从业人员资格证有效期</span>
        <el-date-picker v-model="practitionerCertificateDate" size="small" type="daterange" value-format="yyyy-MM-dd" class="daterange-condition" range-separator="至" start-placeholder="请选择日期" end-placeholder="请选择日期" /> -->
        <el-select
          v-model="searchQuery.is_equipped"
          placeholder="配备司机状态"
          clearable
          size="small"
          class="mini-condition"
        >
          <el-option label="已配备" value="1" />
          <el-option label="未配备" value="0" />
        </el-select>
        <el-button
          size="small"
          type="primary"
          @click="handleLogFilter"
        >查询</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        stripe
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        :data="list"
        fit
        highlight-current-row
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="车辆ID" prop="id" width="90" />
        <el-table-column
          label="车牌号码"
          prop="license_plate_number"
          width="170"
        />
        <el-table-column
          label="品牌/车型/颜色"
          prop="user_mail"
          min-width="190"
        >
          <template slot-scope="scope">
            {{ scope.row.brand_name || "-" }}/{{
              scope.row.series_name || "-"
            }}/{{ scope.row.vehicle_color | formatColor }}
          </template>
        </el-table-column>
        <el-table-column label="车辆座位" prop="vehicle_seats" width="130" />
        <el-table-column label="车辆类型" prop="vehicle_type" width="130" />
        <el-table-column
          label="车辆所有人"
          prop="vehicle_owner"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="添加时间" prop="created_at" width="190">
          <template slot-scope="scope">
            {{ scope.row.created_at || "/" }}
          </template>
        </el-table-column>
        <el-table-column label="是否配备司机" prop="is_equipped" width="120">
          <template slot-scope="scope">
            <Tablestatus
              v-if="scope.row.is_equipped == 1"
              mode="success"
              word="已配备"
            />
            <Tablestatus
              v-else-if="scope.row.is_equipped == 0"
              mode="default"
              word="未配备"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="检修状态" prop="status" width="100">
          <template slot-scope="scope">
            <Tablestatus
              v-if="scope.row.vehicle_inspection_status === '未检修'"
              mode="warning"
              word="未检修"
            />
            <Tablestatus
              v-else-if="scope.row.vehicle_inspection_status === '检修中'"
              mode="processing"
              word="检修中"
            />
            <Tablestatus
              v-else-if="scope.row.vehicle_inspection_status === '已检修'"
              mode="success"
              word="已检修"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="160">
          <template v-slot="scope">
            <el-button
              type="text"
              size="small"
              @click="preView(scope.row)"
            >详情</el-button>
            <el-divider direction="vertical" />
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-divider direction="vertical" />
            <el-popconfirm
              hide-icon
              title="确认删除当前车辆？"
              @confirm="toDel(scope.row)"
            >
              <el-button
                slot="reference"
                type="text"
                size="small"
              >删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottom-container">
      <div class="left-content">
        <!-- <el-button class="filter-item" size="small" type="primary" icon="el-icon-printer" @click="exporList">导出</el-button> -->
        <el-button
          v-if="function_permission_definition && function_permission_definition.provincialReportConfig"
          class="filter-item"
          size="small"
          type="primary"
          :disabled="!multipleSelection.length"
          @click="handleReportVehicles"
        >上报省级定制客运车辆监控服务平台</el-button>
        <span v-if="multipleSelection.length" class="selected-count">已选择 {{ multipleSelection.length }} 辆车辆</span>
      </div>
      <el-pagination
        small
        background
        :current-page="searchQuery.page"
        :page-size="searchQuery.per_page"
        :page-sizes="[10, 15, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalData"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog :visible.sync="dialogDes" title="车辆详情">
      <Description :description="description" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogFormVisible"
      :title="textMap[dialogStatus]"
      :close-on-click-modal="false"
      width="900px"
    >
      <create-form
        v-if="dialogFormVisible"
        v-model="dialogFormVisible"
        :dialog-status="dialogStatus"
        :temp="temp"
        @refresh="handleReset"
      />
    </el-dialog>
  </div>
</template>

<script>
import listMixin from '@/mixins/list.js'
import { vehiclesList, properties, delCar, reportVehicles } from '@/api/service'
import Description from './components/Description'
import CreateForm from './components/CreateForm'
import { getInfo } from '@/api/user'
import { mapGetters } from 'vuex'
let app = null

export default {
  name: 'VehicleManage',
  components: { Description, CreateForm },
  filters: {
    formatColor(color) {
      if (!app || !app.properties || !app.properties.color_properties) {
        return '-'
      }
      return app.properties.color_properties[color] || '-'
    }
  },
  mixins: [listMixin(vehiclesList)],
  computed: {
    ...mapGetters(['function_permission_definition'])
  },
  data() {
    return {
      properties: {},
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '修改车辆',
        create: '添加车辆'
      },
      temp: {},
      userInfo: {},
      date: [],
      practitionerCertificateDate: [],
      dialogDes: false,
      description: '',
      rules: {
        reason: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      multipleSelection: []
    }
  },
  watch: {
    date(n, o) {
      const [created_start_at, created_end_at] = n
      this.searchQuery = {
        ...this.searchQuery,
        created_start_at,
        created_end_at
      }
    },
    practitionerCertificateDate(n, o) {
      const [
        practitioner_certificate_start_date,
        practitioner_certificate_stop_date
      ] = n
      this.searchQuery = {
        ...this.searchQuery,
        practitioner_certificate_start_date,
        practitioner_certificate_stop_date
      }
    }
  },
  created() {
    this.fetchProperties()
    // 检查URL参数中是否包含车牌尾号
    const query = this.$route.query
    if (query.car_tail_number) {
      this.searchQuery.filter_key = 'license_plate_number'
      this.searchQuery.filter_keyword = decodeURIComponent(query.car_tail_number)
      this.handleLogFilter()
    }
  },
  beforeCreate() {
    // 将vue实例赋值给全局变量app
    app = this
  },
  mounted() {
    this.getList()
    this.getInfo()
  },
  methods: {
    async getInfo() {
      const { data } = await getInfo()
      this.userInfo = data
    },
    async fetchProperties() {
      this.loading = true
      const { data } = await properties()
      this.loading = false
      this.properties = data
    },
    handleEdit(row) {
      this.temp = row
      if (this.temp.insurance_type == null) {
        this.temp.insurance_type = []
      }
      if (this.temp.insurance_type === undefined) {
        this.temp.insurance_type = []
      } else {
        if (Array.isArray(this.temp.insurance_type) === false) {
          this.temp.insurance_type = this.temp.insurance_type.split(',')
        }
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    handleCreate() {
      this.temp = {}
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    async toDel(params) {
      await delCar(params.id)
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.getList()
    },

    // async exporList () {
    //   const data = await export_excels(this.searchQuery)
    //   console.log(data)
    //   const blob = new Blob([data])
    //   const objectURL = URL.createObjectURL(blob)
    //   let btn = document.createElement('a')
    //   btn.download = '车辆.csv'
    //   btn.href = objectURL
    //   btn.click()
    //   URL.revokeObjectURL(objectURL)
    //   btn = null
    // },

    handleLogFilter() {
      if (this.searchQuery.filter_keyword && !this.searchQuery.filter_key) {
        this.$message({
          message: '请先选择搜索类型',
          type: 'warning'
        })
        return
      }
      if (this.searchQuery.filter_key && !this.searchQuery.filter_keyword) {
        this.$message({
          message: '请输入搜索关键词',
          type: 'warning'
        })
        return
      }
      this.handleFilter()
    },
    preView(params) {
      this.description = params
      this.dialogDes = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async handleReportVehicles() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: '请先选择要上报的车辆',
          type: 'warning'
        })
        return
      }

      try {
        this.loading = true
        const vehicleIds = this.multipleSelection.map(item => item.id)
        await reportVehicles(vehicleIds)
        this.$notify({
          title: '上报成功',
          message: '已成功上报所选车辆到省级定制客运车辆监控服务平台',
          type: 'success'
        })
      } catch (error) {
        this.$notify({
          title: '上报失败',
          message: error.message || '上报车辆信息失败，请稍后重试',
          type: 'error'
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.tableTitle {
  width: 160px;
  padding: 8px;
}

.tableContent {
  width: 400px;
}

#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  text-align: right;
}

table {
  margin: 0 auto;
  border: 1px solid #000000;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid #000000;
  text-align: center;
}

.upload-demo /deep/ .el-upload--picture-card {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.upload-demo /deep/ .el-upload-list--picture-card .el-upload-list__item {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.passInput {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}

.option {
  margin-right: 4px;
  margin-left: 0;
}
.label-key{
  margin-left: 3px;
}

.bottom-container {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
}

.left-content {
  display: flex;
  align-items: center;
}

.selected-count {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.app-container {
  padding: 20px;
}

.hanldle-container {
  margin-bottom: 15px;
}

.table-container {
  margin-bottom: 15px;
  min-height: 200px;
}
</style>

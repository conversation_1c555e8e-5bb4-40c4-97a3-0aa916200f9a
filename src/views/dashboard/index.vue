<template>
  <div class="dashboard-editor-container">
    <div class="page-title">数据分析仪表盘</div>

    <div v-if="chartsLoaded" class="chart-card">
      <div class="chart-header">
        <div class="chart-title">
          <i class="el-icon-data-analysis" /> 月订单交易概况
        </div>
        <div class="chart-filter">
          <span class="filter-label">选择年份：</span>
          <el-date-picker
            v-model="year"
            size="small"
            type="year"
            value-format="yyyy"
            placeholder="选择年"
            class="date-picker"
          />
        </div>
      </div>
      <div class="chart-content">
        <monthly-chart ref="monthlyChart" :year="year" height="100%" width="100%" />
      </div>
    </div>

    <div class="chart-card">
      <div class="chart-header">
        <div class="chart-title">
          <i class="el-icon-data-line" /> 日订单交易概况
        </div>
        <div class="chart-filter">
          <span class="filter-label">选择日期：</span>
          <el-date-picker
            v-model="date"
            type="daterange"
            size="small"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="date-picker"
          />
        </div>
      </div>
      <div class="chart-content">
        <daily-chart ref="dailyChart" :day="date" height="100%" width="100%" />
      </div>
    </div>
  </div>
</template>

<script>
import MonthlyChart from '@/components/Charts/MonthlyChart'
import DailyChart from '@/components/Charts/DailyChart'

export default {
  name: 'DataOverview',
  components: { MonthlyChart, DailyChart },
  data() {
    return {
      listLoading: false,
      year: new Date(),
      date: [],
      chartsLoaded: false
    }
  },
  mounted() {
    // 延迟加载图表，避免与WebSocket连接竞争资源
    this.$nextTick(() => {
      setTimeout(() => {
        this.chartsLoaded = true
      }, 1000)
    })

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },
  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
  },
  methods: {
    handleVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        // 页面隐藏时暂停不必要的操作
        console.log('页面已隐藏，暂停数据更新')
      } else if (document.visibilityState === 'visible') {
        // 页面可见时恢复操作
        console.log('页面已显示，恢复数据更新')
        this.refreshData()
      }
    },
    refreshData() {
      // 刷新图表数据的方法
      if (this.$refs.monthlyChart) {
        this.$refs.monthlyChart.fetchData(this.year)
      }
      if (this.$refs.dailyChart) {
        this.$refs.dailyChart.fetchData(this.date)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dashboard-editor-container {
  padding: 24px;
  background-color: #f5f7fa;
  position: relative;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 24px;
  animation: fadeIn 0.5s ease-out;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  position: relative;
  padding-left: 12px;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom, #2A57FE, #45A6FF);
    border-radius: 2px;
  }
}

.chart-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(100, 149, 237, 0.05);
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-color: rgba(100, 149, 237, 0.1);
  }
}

.chart-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(100, 149, 237, 0.1);
  background: linear-gradient(135deg, rgba(240, 248, 255, 0.9), rgba(230, 244, 255, 0.9));
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #2A57FE;
    font-size: 20px;
  }
}

.chart-filter {
  display: flex;
  align-items: center;
}

.filter-label {
  margin-right: 8px;
  color: #606266;
  font-weight: 500;
}

.date-picker {
  width: 220px;
}

.chart-content {
  padding: 20px;
  height: 400px;
  position: relative;
}

@media (max-width: 1024px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-filter {
    width: 100%;
  }

  .date-picker {
    width: 100%;
  }

  .chart-content {
    height: 300px;
  }
}
</style>

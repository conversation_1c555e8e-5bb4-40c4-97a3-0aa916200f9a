<template>
  <div class="app-container">
    <div class="page-title">数据概览</div>
    <div class="overview-container">
      <div class="overview-item first-item">
        <div class="overview-th" />
        <div class="overview-td">
          <i class="el-icon-money" /> 销售金额
        </div>
        <div class="overview-td">
          <i class="el-icon-shopping-cart-full" /> 销售订单数
        </div>
        <div class="overview-td">
          <i class="el-icon-refresh" /> 退款金额
        </div>
        <div class="overview-td">
          <i class="el-icon-document" /> 退款订单数
        </div>
      </div>
      <div v-for="item in overviewData" :key="item.id" class="overview-item">
        <div class="overview-th">
          {{ item.name }}
        </div>
        <div class="overview-td">
          <span class="value-text">{{ item.value }}</span>元
        </div>
        <div class="overview-td">
          <span class="value-text">{{ item.order }}</span>单
        </div>
        <div class="overview-td">
          <span class="value-text">{{ item.refund }}</span>元
        </div>
        <div class="overview-td">
          <span class="value-text">{{ item.refundOrder }}</span>单
        </div>
      </div>
    </div>
    <div class="overview-container2">
      <div class="dispatch-container">
        <div class="dispatch-item">
          <div class="dispatch-item-th" />
          <div class="dispatch-item-td">
            <i class="el-icon-truck" /> 待发货
          </div>
          <div class="dispatch-item-td">
            <i class="el-icon-check" /> 核销数
          </div>
        </div>
        <div v-for="item in overviewData" :key="item.id" class="dispatch-item">
          <div class="dispatch-item-th">
            {{ item.name }}
          </div>
          <div class="dispatch-item-td">
            <span class="value-text">{{ item.wait }}</span>单
          </div>
          <div class="dispatch-item-td">
            <span class="value-text">{{ item.verify }}</span>单
          </div>
        </div>
      </div>
      <div class="other-container">
        <div v-for="item in otherData" :key="item.id" class="other-item">
          <div class="other-item-icon">
            <i :class="getIconClass(item.title)" />
          </div>
          <div class="other-item-content">
            <div class="other-item-title">{{ item.title }}</div>
            <div class="other-item-value">{{ item.value }} <span class="unit">
              {{ item.title === '乘客用户数' ? '人' : '个' }}
            </span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="overview-container3">
      <div class="overview-container3-item">
        <div class="overview-container3-item-title">
          <i class="el-icon-s-grid" /> 热门分类TOP10
        </div>
        <div class="overview-container3-item-box">
          <div v-for="(item, index) in top10HotCategoryList" :key="item.id" class="top-item">
            <span class="top-rank" :class="{'top-three': index < 3}">{{ index + 1 }}</span>
            <span class="top-name">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="overview-container3-item">
        <div class="overview-container3-item-title">
          <i class="el-icon-goods" /> 热门商品TOP10
        </div>
        <div class="overview-container3-item-box">
          <div v-for="(item, index) in top10HotProductList" :key="item.id" class="top-item">
            <span class="top-rank" :class="{'top-three': index < 3}">{{ index + 1 }}</span>
            <span class="top-name">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="overview-container3-item">
        <div class="overview-container3-item-title">
          <i class="el-icon-office-building" /> 交易量分台TOP10
        </div>
        <div class="overview-container3-item-box">
          <div v-for="(item, index) in top10BranchList" :key="item.id" class="top-item">
            <span class="top-rank" :class="{'top-three': index < 3}">{{ index + 1 }}</span>
            <span class="top-name">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOverview } from '@/api/systemSetup'
export default {
  name: 'PointsOverview',
  data() {
    return {
      overviewData: [
        {
          id: 1,
          name: '今日',
          value: 0.00,
          order: 1,
          refund: 0.00,
          refundOrder: 0
        },
        {
          id: 2,
          name: '昨日',
          value: 0.00,
          order: 1,
          refund: 0.00,
          refundOrder: 0
        },
        {
          id: 3,
          name: '本周',
          value: 0.00,
          order: 1,
          refund: 0.00,
          refundOrder: 0
        },
        {
          id: 4,
          name: '本月',
          value: 0.00,
          order: 1,
          refund: 0.00,
          refundOrder: 0
        },
        {
          id: 5,
          name: '累计',
          value: 0.00,
          order: 1,
          refund: 0.00,
          refundOrder: 0
        }
      ],
      otherData: [
        {
          title: '商品总数',
          value: 0
        },
        {
          title: '乘客用户数',
          value: 0
        },
        {
          title: '线路分台数',
          value: 0
        },
        {
          title: '渠道分台数',
          value: 0
        }
      ],
      top10BranchList: [],
      top10HotCategoryList: [],
      top10HotProductList: []
    }
  },
  created() {
    this.getOverview()
  },
  methods: {
    async getOverview() {
      const { data } = await getOverview()
      console.log(data)
      this.top10BranchList = data.top10BranchList
      this.top10HotCategoryList = data.top10HotCategoryList
      this.top10HotProductList = data.top10HotProductList
      this.otherData = [
        {
          title: '商品总数',
          value: data.spuCount
        },
        {
          title: '乘客用户数',
          value: data.passengerUserCount
        },
        {
          title: '线路分台数',
          value: data.lineStationCount
        },
        {
          title: '渠道分台数',
          value: data.channelStationCount
        }
      ]
      this.overviewData = [
        {
          id: 1,
          name: '今日',
          value: data.today.sales_amount,
          order: data.today.sales_order_count,
          refund: data.today.refund_amount,
          refundOrder: data.today.refund_order_count,
          verify: data.today.verified_count,
          wait: data.today.wait_delivery_count
        },
        {
          id: 2,
          name: '昨日',
          value: data.yesterday.sales_amount,
          order: data.yesterday.sales_order_count,
          refund: data.yesterday.refund_amount,
          refundOrder: data.yesterday.refund_order_count,
          verify: data.yesterday.verified_count,
          wait: data.yesterday.wait_delivery_count
        },
        {
          id: 3,
          name: '本周',
          value: data.week.sales_amount,
          order: data.week.sales_order_count,
          refund: data.week.refund_amount,
          refundOrder: data.week.refund_order_count,
          verify: data.week.verified_count,
          wait: data.week.wait_delivery_count
        },
        {
          id: 4,
          name: '本月',
          value: data.thisMonth.sales_amount,
          order: data.thisMonth.sales_order_count,
          refund: data.thisMonth.refund_amount,
          refundOrder: data.thisMonth.refund_order_count,
          verify: data.thisMonth.verified_count,
          wait: data.thisMonth.wait_delivery_count
        },
        {
          id: 5,
          name: '累计',
          value: data.total.sales_amount,
          order: data.total.sales_order_count,
          refund: data.total.refund_amount,
          refundOrder: data.total.refund_order_count,
          verify: data.total.verified_count,
          wait: data.total.wait_delivery_count
        }
      ]
    },
    getIconClass(title) {
      const iconMap = {
        '商品总数': 'el-icon-goods',
        '乘客用户数': 'el-icon-user',
        '线路分台数': 'el-icon-location',
        '渠道分台数': 'el-icon-share'
      }
      return iconMap[title] || 'el-icon-data-analysis'
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.app-container {
  padding: 24px;
  background-color: #f5f7fa;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 24px;
  animation: fadeIn 0.5s ease-out;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  position: relative;
  padding-left: 12px;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom, #2A57FE, #45A6FF);
    border-radius: 2px;
  }
}

.overview-container2 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 24px;
}

.overview-container {
  width: 100%;
  background: linear-gradient(135deg, rgba(100, 149, 237, 0.5), rgba(135, 206, 250, 0.5));
  background-size: 200% 200%;
  background-position: 0% 0%;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 8px 16px rgba(100, 149, 237, 0.1);
  position: relative;
  overflow: hidden;
  color: #303133;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(100, 149, 237, 0.15);
    background-position: 100% 100%;
  }

  &:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover:after {
    opacity: 1;
  }
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  z-index: 1;

  &:last-child {
    padding-right: 80px;
  }
}

.overview-th {
  height: 36px;
  font-weight: 700;
  font-size: 20px;
  display: flex;
  align-items: center;
  color: #303133;
}

.overview-td {
  height: 36px;
  font-size: 16px;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    font-size: 18px;
  }
}

.value-text {
  font-weight: 700;
  font-size: 18px;
  margin-right: 4px;
}

.first-item {
  font-weight: 600;
}

.dispatch-container {
  width: 67%;
  background: #fff;
  color: #303133;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  gap: 24px;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #2A57FE;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

.dispatch-item {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &:last-child {
    padding-right: 80px;
  }
}

.dispatch-item-th {
  height: 36px;
  font-weight: 700;
  font-size: 18px;
  color: #2A57FE;
  display: flex;
  align-items: center;
}

.dispatch-item-td {
  height: 36px;
  font-size: 16px;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #2A57FE;
  }
}

.other-container {
  width: 33%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 16px;

  .other-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 12px;
    background: #fff;
    color: #303133;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
  }

  .other-item-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(42, 87, 254, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    i {
      font-size: 24px;
      color: #2A57FE;
    }
  }

  .other-item-content {
    flex: 1;
  }

  .other-item-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 4px;
  }

  .other-item-value {
    font-size: 20px;
    font-weight: 700;
    color: #303133;

    .unit {
      font-size: 14px;
      font-weight: 400;
      color: #909399;
    }
  }
}

.overview-container3 {
  width: 100%;
  display: flex;
  gap: 24px;

  .overview-container3-item {
    width: 33%;
    background: #fff;
    color: #303133;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
  }

  .overview-container3-item-title {
    font-size: 18px;
    font-weight: 700;
    color: #303133;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #2A57FE;
    }
  }

  .overview-container3-item-box {
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-height: 220px;
    overflow-y: auto;
    flex: 1;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  .top-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-align: left;

    &:hover {
      background: rgba(42, 87, 254, 0.05);
    }
  }

  .top-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f0f2f5;
    color: #606266;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    margin-right: 12px;

    &.top-three {
      background: linear-gradient(135deg, rgba(100, 149, 237, 0.7), rgba(135, 206, 250, 0.7));
      color: #fff;
    }
  }

  .top-name {
    flex: 1;
    font-size: 14px;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

@media screen and (max-width: 1200px) {
  .overview-container2 {
    flex-direction: column;
  }

  .dispatch-container, .other-container {
    width: 100%;
    margin-right: 0;
  }

  .overview-container3 {
    flex-direction: column;

    .overview-container3-item {
      width: 100%;
    }
  }
}
</style>

<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container" />
      <div class="filter-container">
        <span class="label-key">手机号：</span>
        <el-input v-model="phone" placeholder="请输入" size="small" class="filter-item condition" />
        <span class="label-key" style="margin-left: 6px;">商品类型：</span>
        <el-select v-model="spu_type" placeholder="请选择" size="small" class="query-select-long" filterable clearable>
          <el-option label="普通/实体商品" :value="1" />
          <el-option label="虚拟/服务/券/O2O类" :value="2" />
        </el-select>
        <span class="label-key" style="margin-left: 6px;">所属分台：</span>
        <el-select v-model="branch_id" placeholder="请选择" size="small" class="query-select-long" filterable clearable>
          <el-option v-for="item in branchs" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <span class="label-key" style="margin-left: 6px;">兑换订单编号：</span>
        <el-input v-model="orderID" placeholder="请输入" size="small" class="filter-item condition" />
        <span class="label-key" style="margin-left: 6px;">状态：</span>
        <el-select v-model="orderStatus" placeholder="请选择" size="small" class="query-select-long">
          <el-option label="待发货" value="pending" />
          <el-option label="已发货" value="delivered" />
          <el-option label="已完成" value="success" />
          <el-option label="已关闭" value="closed" />
          <el-option label="已取消" value="caneled" />
        </el-select>
        <el-button size="small" type="primary" @click="getPointIndex">查询</el-button>
        <el-button size="small" @click="resetQueryParams">重置</el-button>
      </div>
    </div>
    <div>
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        stripe
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        fit
        highlight-current-row
        height="calc(100vh - 185px)"
        :data="orderData"
        style="width: 100%;"
      >
        <el-table-column label="兑换订单编号" prop="no" width="170" />
        <el-table-column label="商品名称" prop="name" min-width="250">
          <template v-slot="scope">
            {{ scope.row.order_items[0].spu.name }}
          </template>
        </el-table-column>
        <el-table-column label="商品类型" prop="type" width="160">
          <template slot-scope="scope">
            <span class="label-key">{{ scope.row.order_items[0].spu.type === 1 ? '普通/实体商品' : '虚拟/服务/券/O2O类' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="兑换积分" prop="price" width="120">
          <template slot-scope="scope">
            {{ scope.row.order_items[0].price ?? '-' }}
          </template>
        </el-table-column>
        <el-table-column label="剩余积分" prop="user_total_point" width="120" />

        <el-table-column label="销售金额" prop="price_cash" width="120">
          <template v-slot="scope">
            {{ scope.row.order_items[0].price_cash / 100 ?? '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="logistics_status" width="120">
          <template slot-scope="scope">
            <Tablestatus :word="getStatus(scope.row.status, scope.row.logistics_status).status" :mode="getStatus(scope.row.status, scope.row.logistics_status).mode" />
          </template>
        </el-table-column>
        <el-table-column label="用户" prop="contact" width="120" />
        <el-table-column label="手机号" prop="phone" width="200" />
        <el-table-column label="物流单号" prop="logistics_no" width="200" />
        <el-table-column label="备注" prop="remark" width="120" />
        <el-table-column label="兑换时间" prop="created_at" width="200" />
        <el-table-column fixed="right" label="操作" width="180">
          <template v-slot="scope">
            <div style="">
              <el-button v-if="scope.row.order_items[scope.row.order_items.length - 1].spu.type === 1" :disabled="!canShipOrVerify(scope.row)" type="text" size="medium" @click="shipBut(scope.row)">
                发货
              </el-button>
              <el-button v-if="scope.row.order_items[scope.row.order_items.length - 1].spu.type === 2" :disabled="!canShipOrVerify(scope.row)" type="text" size="medium" @click="verifyBut(scope.row)">
                核销
              </el-button>
              <el-divider direction="vertical" />
              <el-button :disabled="!isDelivered(scope.row.status, scope.row.logistics_status)" type="text" size="medium" @click="onCancelOrder(scope.row)">
                取消
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                size="medium"
                :disabled="scope.row.order_items[scope.row.order_items.length - 1].spu.type === 2"
                @click="detail(scope.row)"
              >详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination style="margin-top: 10px" small background :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 15, 20, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper" :total="totalOrder" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    <el-dialog :visible.sync="dialogDetailVisible" title="查看详情">
      <el-form ref="detailForm" :model="detailForm" label-width="120px" label-position="right" style="padding-right: 24px">
        <el-form-item label="兑换订单编号:" prop="account">
          <el-input v-model="detailForm.no" />
        </el-form-item>
        <el-form-item label="商品名称:" prop="name">
          <el-input v-model="detailForm.name" />
        </el-form-item>
        <el-form-item label="兑换积分:" prop="price">
          <el-input v-model="detailForm.price" />
        </el-form-item>
        <el-form-item label="剩余积分:" prop="user_total_point">
          <el-input v-model="detailForm.user_total_point" />
        </el-form-item>
        <el-form-item label="物流公司:" prop="logistics_name">
          <el-input v-model="detailForm.logistics_name" />
        </el-form-item>
        <el-form-item label="物流单号:" prop="logistics_no">
          <el-input v-model="detailForm.logistics_no" />
        </el-form-item>
        <el-form-item label="收件人:" prop="contact">
          <el-input v-model="detailForm.contact" />
        </el-form-item>
        <el-form-item label="联系电话:" prop="phone">
          <el-input v-model="detailForm.phone" />
        </el-form-item>
        <el-form-item label="收货地址:" prop="address">
          <el-input v-model="detailForm.address" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailVisible = false">取消</el-button>
        <el-button type="primary" @click="infoUpdate">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog :before-close="beforeClose" :visible.sync="dialogShipmentVisible" title="填写发货信息">
      <el-form ref="ruleFormRef" v-model="shipmentForm" label-width="80px" label-position="right">
        <el-form-item label="物流公司:" prop="logistics_name ">
          <el-input v-model="shipmentForm.logistics_name " />
        </el-form-item>
        <el-form-item label="物流单号:" prop="logistics_no">
          <el-input v-model="shipmentForm.logistics_no" autocomplete="off" />
        </el-form-item>
        <el-form-item label="收件人:" prop="contact">
          <el-input v-model="shipmentForm.contact " autocomplete="off" />
        </el-form-item>
        <el-form-item label="联系电话:" prop="phone">
          <el-input v-model="shipmentForm.phone" autocomplete="off" />
        </el-form-item>
        <el-form-item label="收货地址:" prop="address">
          <el-input v-model="shipmentForm.address" autocomplete="off" />
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input v-model="shipmentForm.remark" autocomplete="off" />
        </el-form-item>
        <el-form-item>
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="confirmForm()">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog :visible.sync="dialogVerifyVisible" title="核销订单" width="600px" @close="cancelVerify">
      <el-form ref="verifyFormRef" v-model="verifyForm" label-width="80px" label-position="right">
        <el-form-item label="验证码:" prop="verification_code">
          <el-input v-model="verifyForm.verification_code" autocomplete="off" />
        </el-form-item>
        <el-form-item>
          <el-button @click="cancelVerify()">取消</el-button>
          <el-button type="primary" @click="confirmVerify()">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>

import { cancelOrder, getPointsOrder, shipped, verifyOrder, updateLogistics } from '@/api/systemSetup'
import { dispatchBranches } from '@/api/business'

export default {
  name: 'Exchange',
  data () {
    return {
      branch_id: '',
      branchs: [],
      spu_type: '',
      phone: '',
      dialogVerifyVisible: false,
      verifyForm: {
        order_id: '',
        verification_code: ''
      },
      loading: false,
      orderID: '',
      orderStatus: '',
      orderData: [],
      currentPage: 1,
      pageSize: 15,
      totalOrder: 0,
      detailForm: [],
      shipmentForm: [],
      orderId: '',
      dialogDetailVisible: false,
      dialogShipmentVisible: false
    }
  },
  mounted () {
    this.getPointIndex()
    this.fetchBranch()
  },
  methods: {
    async infoUpdate () {
      await updateLogistics({
        order_id: this.orderId,
        ...this.detailForm
      })
      this.$notify.success('更新成功')
      this.dialogDetailVisible = false
      this.resetQueryParams()
    },
    // 是否在已发货前的状态
    isDelivered(status, logistics_status) {
      return status === 'pending' || (status === 'payed' && logistics_status === 'pending')
    },
    // 发货/核销是否启用
    canShipOrVerify(row) {
      return row.status === 'payed' && row.logistics_status === 'pending'
    },
    // 获取状态
    getStatus(orderStatus, logisticsStatus) {
      const OrderStatus = {
        PENDING: 'pending', // 待支付
        PAYED: 'payed', // 已支付
        SUCCESS: 'success', // 已完成
        REFUNDING: 'refunding', // 退款中
        CANCELED: 'canceled' // 已取消(已退款)
      }

      const LogisticsStatus = {
        PENDING: 'pending', // 待发货
        DELIVERED: 'delivered', // 已发货
        SUCCESS: 'success' // 已完成（例如配送完成）
      }
      switch (orderStatus) {
        case OrderStatus.PENDING:
          return {
            status: '待支付',
            mode: 'warning'
          }
        case OrderStatus.PAYED:
          switch (logisticsStatus) {
            case LogisticsStatus.PENDING:
              return {
                status: '待发货',
                mode: 'warning'
              }
            case LogisticsStatus.DELIVERED:
              return {
                status: '已发货',
                mode: 'processing'
              }
            default:
              return {
                status: '已完成',
                mode: 'success'
              }
          }
        case OrderStatus.SUCCESS:
          return {
            status: '已完成',
            mode: 'success'
          }
        case OrderStatus.REFUNDING:
          return {
            status: '退款中',
            mode: 'danger'
          }
        case OrderStatus.CANCELED:
          return {
            status: '已取消',
            mode: 'default'
          }
        default:
          return {
            status: '--',
            mode: 'default'
          }
      }
    },
    async fetchBranch () {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
        if (this.info && this.info.group_id === 2) {
          this.branchs = [
            {
              label: '全部',
              value: this.info.admin_id
            },
            ...this.branchs
          ]
        }
        this.$set(this.searchQuery, 'branch_id', this.branchs[0].value)
        this.fetchScode()
        this.fetchEcode()
      }
    },
    // 状态赋值
    stateFormat (state) {
      switch (state) {
        case 'pending':
          return '待发货'
        case 'delivered':
          return '已发货'
        case 'success':
          return '已完成'
        case 'closed':
          return '已关闭'
        case 'caneled':
          return '已取消'
      }
    },
    detail (row) {
      this.dialogDetailVisible = true
      this.detailForm = row
      this.detailForm.name = this.detailForm.order_items[0].spu.name
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getPointIndex()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getPointIndex()
    },
    // 获取积分商品兑换订单(查询订单信息)
    getPointIndex () {
      this.loading = true
      const params = {
        phone: this.phone,
        spu_type: this.spu_type,
        branch_id: this.branch_id,
        no: this.orderID,
        logistics_status: this.orderStatus,
        per_page: this.pageSize,
        page: this.currentPage
      }
      getPointsOrder(params).then((value) => {
        this.orderData = value.data
        this.totalOrder = value.data.length
      })
        .finally(() => {
          this.loading = false
        })
    },
    // 重置查询信息
    resetQueryParams () {
      this.orderID = ''
      this.orderStatus = ''
      this.phone = ''
      this.spu_type = ''
      this.branch_id = ''
      this.getPointIndex()
    },
    // 核销
    verifyBut (row) {
      this.dialogVerifyVisible = true
      this.verifyForm.order_id = row.id
    },
    // 取消核销
    cancelVerify () {
      this.dialogVerifyVisible = false
    },
    // 关闭核销弹窗
    closeVerify () {
      this.verifyForm = {
        order_id: 0,
        verification_code: ''
      }
      this.dialogVerifyVisible = false
    },
    // 确认核销
    async confirmVerify () {
      await verifyOrder(this.verifyForm)
      this.dialogVerifyVisible = false
      this.$notify.success('核销成功')
      this.getPointIndex()
    },
    // 发货
    shipBut (row) {
      this.dialogShipmentVisible = true
      this.orderId = row.id
    },
    /** 准备取消 */
    onCancelOrder (row) {
      this.$confirm('确认取消订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelOrder(row.id)
          .then(() => {
            this.getPointIndex()
            this.$notify.success('订单取消成功')
          })
          .catch(() => {
            this.$notify.error('订单取消失败')
          })
      }).catch(() => {
        this.$notify.error('已取消操作')
      })
    },
    // 发货
    confirmForm () {
      shipped({
        order_id: this.orderId,
        ...this.shipmentForm
      }).then(() => {
        this.dialogShipmentVisible = false
        this.resetQueryParams()
        this.$notify.success('发货成功')
      }).catch(() => {
        this.$notify.error('发货失败')
      })
    },
    beforeClose (done) {
      this.shipmentForm = []
      done()
    },
    // 取消
    cancel () {
      this.shipmentForm = []
      this.dialogShipmentVisible = false
    }
  }
}
</script>

<style scoped>
#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  text-align: right;
}
</style>

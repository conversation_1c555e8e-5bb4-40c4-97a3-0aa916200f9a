<template>
  <div style="margin: 20px 20px;">
    <div id="container">
      <el-form ref="addProductForm" :model="addProductForm" label-width="120px" label-position="right" :rules="addProductRule">
        <el-form-item label="所属分台:" prop="branch_id">
          <el-select
            v-model="addProductForm.branch_id"
            placeholder="选择分台"
          >
            <el-option v-for="item in branchs" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="商品名称:" prop="name">
          <el-input v-model="addProductForm.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="商品类型:" prop="type">
          <el-radio-group v-model="addProductForm.type">
            <el-radio :label="1">普通/实体商品</el-radio>
            <el-radio :label="2">虚拟/服务/券/O2O类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="商品分类:" prop="spus_type_id">
          <el-cascader
            v-model="addProductForm.spus_type_id"
            :options="cascaderOptions"
            :props="cascaderProps"
            collapse-tags
            clearable
          />
        </el-form-item>
        <el-form-item label="销售价格:" prop="on_sale_price">
          <el-input v-model="addProductForm.on_sale_price" autocomplete="off">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="兑换积分:" prop="limit_point">
          <el-input v-model="addProductForm.limit_point" autocomplete="off" />
        </el-form-item>
        <el-form-item label="商品描述:" prop="desc">
          <el-input v-model="addProductForm.desc" type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" autocomplete="off" />
        </el-form-item>
        <el-form-item label="上传商品图片:" prop="imgFileList">
          <el-upload
            style="margin-top: 15px"
            :action="actions"
            name="file"
            :headers="headers"
            list-type="picture-card"
            :file-list="imgFileList"
            :on-preview="handlePictureCardPreview"
            :on-change="onChange"
          >
            <i class="el-icon-plus" />
          </el-upload>
          <div slot="tip" style="color: #909399; font-size: 10px; margin-top: 10px">图片尺寸推荐750px * 375px，且不超过512kb</div>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item>
        <!-- 配送方式： 自提、快递邮寄、同城配送 -->
        <el-form-item label="配送方式:" prop="delivery_type">
          <el-radio-group v-model="addProductForm.delivery_type">
            <el-radio :label="1">自提</el-radio>
            <el-radio :label="2">快递邮寄</el-radio>
            <el-radio :label="3">同城配送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="库存:" prop="total_stock">
          <el-input-number v-model="addProductForm.total_stock" controls-position="left" :min="1" :max="1000000" />
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="!invoiceId" type="primary" size="small" style="margin-right: 20px" @click="submitInvoice()">提交</el-button>
          <el-button v-if="invoiceId" type="primary" size="small" style="margin-right: 20px" @click="saveInvoice()">保存</el-button>
          <el-button size="small" style="margin-right: 20px" @click="returnProductList">返回</el-button>
        </el-form-item> -->
        <div class="dialog-footer">
          <el-button @click="$emit('input', false)"> 取消 </el-button>
          <el-button type="primary" @click="invoiceId?saveInvoice():submitInvoice()"> 确认 </el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import { getInvoiceDetail, getPointsAdd, pointsEdit, getSpusTypesTree } from '@/api/systemSetup'
import { dispatchBranches } from '@/api'
import { getToken } from '@/utils/auth'

const defaultProduct = {
  name: null,
  limit_point: null,
  desc: null,
  total_stock: 0,
  branch_id: '',
  delivery_type: 1,
  spus_type_id: [],
  type: 1,
  on_sale_price: '0.00'
}
export default {
  name: 'AddProduct',
  props: {
    invoiceId: {
      type: String,
      default: () => ''
    },
    rowData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    const validateFileUrl = (rule, value, callback) => {
      if (this.imgFileList.length > 0) {
        callback()
      } else {
        callback(new Error(rule.message))
      }
    }
    const addTotalStock = (rule, value, callback) => {
      if (value > 0) {
        return callback()
      } else {
        callback(new Error('请输入商品库存'))
      }
    }
    const validatePrice = (rule, value, callback) => {
      const regex = /^\d+(\.\d{1,2})?$/
      if (regex.test(value)) {
        callback()
      } else {
        callback(new Error('请填写有效的商品销售价格，最多两位小数'))
      }
    }
    return {
      cascaderOptions: [],
      cascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        multiple: true
      },
      branchs: [],
      addProductRule: {
        on_sale_price: [{ required: true, trigger: 'change', message: '请填写商品销售价格' }, { validator: validatePrice, trigger: 'change' }],
        name: [{ required: true, trigger: 'change', message: '请填写商品名称' }],
        spus_type_id: [{ required: true, trigger: 'change', message: '请选择商品分类' }],
        type: [{ required: true, trigger: 'change', message: '请选择商品类型' }],
        delivery_type: [{ required: true, trigger: 'change', message: '请选择配送方式' }],
        desc: [{ required: true, trigger: 'change', message: '请填写商品描述' }],
        limit_point: [{ required: true, trigger: 'change', message: '请填写商品兑换积分' }],
        imgFileList: [{ required: true, message: '请上传商品图片', validator: validateFileUrl, trigger: 'change' }],
        total_stock: [{ required: true, type: 'number', message: '请填写商品库存', validator: addTotalStock, trigger: 'change' }]
      },
      addProductForm: {
        ...defaultProduct,
        branch_id: this.$store.state.user.info.group_id === 3 ? this.$store.state.user.info.admin_id : null
      },
      dialogVisible: false,
      accept: 'image/bmp, image/jpg, image/png, image/gif, image/jpeg',
      imgFileList: [],
      dialogImageUrl: false
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_API + '/admin/fapiao/attachments'
    }
  },
  watch: {
    rowData: {
      handler(val) {
        console.log('val', val)
        if (val) {
          this.addProductForm = {
            ...defaultProduct,
            name: val.name,
            limit_point: val.limit_point,
            desc: val.desc,
            total_stock: val.total_stock,
            branch_id: val.branch_id,
            delivery_type: val.delivery_type,
            spus_type_id: val.type_id ? [[...new Set(val.type_id.split(',').map(id => parseInt(id)))]] : [],
            type: val.type,
            on_sale_price: val.on_sale_price_yuan
          }
          console.log('this.addProductForm', this.addProductForm)
          if (val.images && val.images.length > 0) {
            this.imgFileList = val.images.map(url => ({
              url,
              response: { data: { url }}
            }))
          }
        } else {
          this.addProductForm = {
            ...defaultProduct,
            branch_id: this.$store.state.user.info.group_id === 3 ? this.$store.state.user.info.admin_id : null
          }
          this.imgFileList = []
        }
      },
      immediate: true
    },
    invoiceId: {
      handler(n) {
        if (n && !this.rowData) {
          this.fetchDetail()
        }
      },
      immediate: true
    }
  },
  created() {
    this.fetchBranch()
    this.fetchSpusTypesTree()
  },
  methods: {
    async fetchSpusTypesTree() {
      const { data } = await getSpusTypesTree()
      this.cascaderOptions = data
    },
    async fetchBranch() {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
      }
    },
    onChange(file, fileList) {
      console.log('onChange', fileList)
      this.imgFileList = fileList
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogVisible = true
      this.dialogImageUrl = file.url
    },

    // 清空
    cancelProduct() {
      this.addProductForm = []
      this.imgFileList = []
    },

    // 保存
    saveInvoice() {
      // 销售价格和兑换积分至少有一个大于0
      if (this.addProductForm.on_sale_price === '0.00' && this.addProductForm.limit_point === 0) {
        this.$notify.warning('销售价格和兑换积分至少有一个大于0')
        return false
      }
      this.$refs['addProductForm'].validate((valid) => {
        if (valid) {
          if (this.invoiceId) {
            pointsEdit(this.invoiceId, {
              type: this.addProductForm.type,
              type_id: this.addProductForm.spus_type_id.join(','),
              delivery_type: this.addProductForm.delivery_type,
              name: this.addProductForm.name,
              point: this.addProductForm.limit_point,
              desc: this.addProductForm.desc,
              branch_id: this.addProductForm.branch_id,
              images: this.imgFileList.map((a) => {
                return a.response.data && a.response.data.url || ''
              }),
              total_stock: this.addProductForm.total_stock,
              on_sale_price: this.addProductForm.on_sale_price * 100
            }).then(() => {
              this.$notify.success('保存商品成功')
              this.cancelProduct()
              this.$emit('refresh')
            }).catch(() => {
              this.$notify.error('保存商品失败')
            })
          }
        } else {
          this.$notify.warning('未按要求填写表单')
          return false
        }
      })
    },

    // 提交
    submitInvoice() {
      // 销售价格和兑换积分至少有一个大于0
      if (this.addProductForm.on_sale_price === '0.00' && this.addProductForm.limit_point === '0') {
        this.$notify.warning('销售价格和兑换积分至少有一个大于0')
        return false
      }
      this.$refs['addProductForm'].validate((valid) => {
        if (valid) {
          getPointsAdd({
            type_id: this.addProductForm.spus_type_id.join(','),
            type: this.addProductForm.type,
            delivery_type: this.addProductForm.delivery_type,
            name: this.addProductForm.name,
            point: this.addProductForm.limit_point,
            desc: this.addProductForm.desc,
            branch_id: this.addProductForm.branch_id,
            on_sale_price: this.addProductForm.on_sale_price * 100,
            images: this.imgFileList.map((a) => {
              // return a.response.data.url
              return a.response.data && a.response.data.url || ''
            }),
            total_stock: this.addProductForm.total_stock
          }).then(() => {
            this.$notify.success('添加商品成功')
            this.cancelProduct()
            this.$emit('refresh')
          }).catch(() => {
            this.$notify.error('添加商品失败')
          })
        } else {
          this.$notify.warning('未按要求填写表单')
          return false
        }
      })
    },

    // 回显数据
    fetchDetail() {
      getInvoiceDetail(this.invoiceId).then((value) => {
        this.addProductForm = value.data
        this.addProductForm.branch_id = this.$store.state.user.info.group_id === 3 ? this.$store.state.user.info.admin_id : value.data.branch_id
        value.data.images.forEach((item) => {
          this.imgFileList.push({
            url: item,
            response: {
              data: {
                url: item
              }
            }
          })
        })
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>

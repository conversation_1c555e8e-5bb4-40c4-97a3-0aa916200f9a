<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button type="primary" size="small" @click="addBtn"><i class="el-icon-plus" style="margin-right: 10px" />新增</el-button>
      </div>
      <div class="filter-container">
        <span v-if="userInfo && userInfo.group_id !== 3" class="label-key">所属分台：</span>
        <el-select
          v-if="userInfo && userInfo.group_id !== 3"
          v-model="branch_id"
          placeholder="选择分台"
          size="small"
        >
          <el-option v-for="item in branchs" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <span class="label-key">商品编号：</span>
        <el-input v-model="productID" placeholder="请输入" size="small" class="query-select-long" />
        <span class="label-key">商品名称：</span>
        <el-input v-model="productName" placeholder="请输入" size="small" class="query-select-long" />
        <span class="label-key">状态：</span>
        <el-select v-model="productStatus" placeholder="请选择" size="small" class="query-select-long">
          <el-option label="未上架" value="0" />
          <el-option label="已上架" value="1" />
        </el-select>
        <el-button type="primary" size="small" @click="selectPointIndex">查询</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </div>
    </div>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      stripe
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      height="calc(100vh - 240px)"
      :data="productData"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="商品编号" prop="no" width="200" />
      <el-table-column label="商品名称" prop="name" show-overflow-tooltip />
      <el-table-column label="商品类型" prop="type" width="160">
        <template slot-scope="scope">
          <span class="label-key">{{ scope.row.type === 1 ? '普通/实体商品' : '虚拟/服务/券/O2O类' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品分类" prop="type_name" width="200">
        <template slot-scope="scope">
          <span class="label-key">{{ scope.row.type_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换积分" prop="limit_point" width="120" />
      <el-table-column label="销售价格" prop="on_sale_price" width="120">
        <template slot-scope="scope">
          <span>{{ (scope.row.on_sale_price / 100) || '0.00' }}元</span>
        </template>
      </el-table-column>
      <el-table-column label="上架状态" prop="on_sale" width="120">
        <template slot-scope="scope">
          <Tablestatus v-if="scope.row.on_sale===0" mode="warning" word="未上架" />
          <Tablestatus v-else-if="scope.row.on_sale===1" mode="processing" word="已上架" />
          <Tablestatus v-else mode="default" word="错误" />
        </template>
      </el-table-column>
      <el-table-column label="总库存" prop="total_stock" width="120" />
      <el-table-column label="剩余库存" prop="stock" width="120" />
      <el-table-column label="创建时间" prop="created_at" width="160" />
      <el-table-column fixed="right" label="操作" width="200">
        <template v-slot="scope">
          <el-button type="text" size="medium" plain @click="upShelf(scope.row);"> {{ scope.row.on_sale === 1 ? '下架' : '上架' }} </el-button>
          <el-divider direction="vertical" />
          <el-button type="text" size="medium" @click="editMessage(scope.row);">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" size="medium" @click="deleteMessage(scope.row);">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination small background :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 15, 20, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper" :total="totalOrder" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    <el-dialog
      :visible.sync="dialogFormVisible"
      :title="textMap[dialogStatus]"
      :close-on-click-modal="false"
      width="700px"
    >
      <create-form
        v-if="dialogFormVisible"
        v-model="dialogFormVisible"
        :dialog-status="dialogStatus"
        :invoice-id="editId"
        :row-data="currentRowData"
        @refresh="resetSearch"
      />
    </el-dialog>

  </div>
</template>

<script>
import { getPointsIndex, pointsDelete, pointsStatus } from '@/api/systemSetup'
import CreateForm from './components/CreateForm'
import { dispatchBranches } from '@/api'

export default {
  name: 'Commodity',
  components: { CreateForm },
  data () {
    const store = this.$store || {}
    const state = store.state || {}
    const user = state.user || {}
    const info = user.info || {}
    return {
      branchs: [],
      editId: '',
      dialogFormVisible: false,
      dialogStatus: '',
      currentRowData: null,
      textMap: {
        update: '修改商品',
        create: '添加商品'
      },
      temp: {},
      loading: false,
      flowStatus: '已上架',
      productID: '',
      productName: '',
      productStatus: '',
      branch_id: info.group_id === 3 ? info.admin_id : null,
      productData: [],
      addProductForm: [],
      addProductShow: false,
      currentPage: 1,
      pageSize: 15,
      totalOrder: 0,
      userInfo: info
    }
  },
  mounted () {
    this.getPointIndex()
  },
  created() {
    this.fetchBranch()
  },
  methods: {
    async fetchBranch() {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
      }
    },
    deleteMessage (row) {
      this.$confirm('此操作将永久删除该商品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        pointsDelete(row.id).then(() => {
          this.$notify.success('删除商品成功')
          this.getPointIndex()
        }).catch(() => {
          this.$notify.error('删除商品失败')
        })
      })
    },
    findOnSale (onSale) {
      switch (onSale) {
        case 0:
          return '未上架'
        case 1:
          return '已上架'
        default:
          return '错误'
      }
    },
    editMessage (row) {
      this.dialogFormVisible = true
      this.editId = ''
      this.currentRowData = row
      this.$nextTick(() => {
        this.editId = row.id.toString()
        this.dialogStatus = 'update'
      })
    },
    addBtn () {
      this.dialogFormVisible = true
      this.editId = ''
      this.currentRowData = null
      this.dialogStatus = 'create'
    },

    // 查询积分商品列表
    getPointIndex () {
      this.loading = true
      getPointsIndex(this.productID, this.productName, this.productStatus, this.pageSize, this.currentPage, this.branch_id)
        .then((value) => {
          this.productData = value.data
          this.totalOrder = value.data.length
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getPointIndex()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getPointIndex()
    },

    selectPointIndex () {
      this.getPointIndex()
    },

    // 重置所属信息
    resetSearch () {
      const store = this.$store || {}
      const state = store.state || {}
      const user = state.user || {}
      const info = user.info || {}
      this.productID = ''
      this.productName = ''
      this.productStatus = ''
      this.branch_id = info.group_id === 3 ? info.admin_id : null
      this.getPointIndex()
      this.dialogFormVisible = false
    },
    // 上架
    upShelf (row) {
      this.$confirm('确认' + (row.on_sale === 1 ? '下架' : '上架') + row.name + '吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (row.on_sale === 0) {
          pointsStatus(row.id, 1).then(() => {
            row.on_sale = 1
            this.$notify.success('商品上架成功')
          }).catch(() => {
            this.$notify.success('商品上架失败')
          })
        } else {
          pointsStatus(row.id, 0).then(() => {
            row.on_sale = 0
            this.$notify.success('商品下架成功')
          }).catch(() => {
            this.$notify.success('商品下架失败')
          })
        }
      }).catch(() => {
        this.$notify.error('取消操作')
      })
    }
  }
}
</script>

<style scoped>
.label-key{
    margin-left: 10px;
}
#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  margin-top: 10px;
  text-align: right;
}
</style>

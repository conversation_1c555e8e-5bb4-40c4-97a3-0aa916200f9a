<template>
  <div class="app-container">
    <div class="config-warp">
      <el-form ref="reqiredForm" v-loading="loading" :model="integralData" label-width="130px" size="small" style="margin-top: 30px">
        <el-form-item label="积分兑换开关" prop="special_column_rate">
          <el-switch v-model="integralSwitch" active-color="#0067E1" :active-value="1" :inactive-value="0" @change="setConfigStatus" />
          <span class="label-key"> 关闭后，则无法使用积分兑换</span>
        </el-form-item>
        <el-form-item label="积分增加" prop="points_ratio">
          <span class="point-label-text">订单已实付1元可积</span>
          <el-input v-model="integralData.points_ratio" :disabled="integralSwitch === 0" class="persentInput point-label-input" placeholder="请输入" />
          <span class="point-label-text">分</span>
          <span class="label-key"> （下单1元获得多少积分（不满1元按1元计算），仅订单实付金额可以积分）</span>
        </el-form-item>
        <el-form-item label="订单积分抵扣" prop="points_deduction_ratio">
          <span class="point-label-text">订单支付时</span> <el-input v-model="integralData.points_deduction_ratio" :disabled="integralSwitch === 0" class="persentInput point-label-input" placeholder="请输入" /> <span class="point-label-text">积分可以抵扣1元</span>

          <span class="label-key"> （订单支付时可以使用积分抵扣支付）</span>
        </el-form-item>
      </el-form>
      <div class="button-bottom">
        <el-button size="small" type="primary" @click="handleSave">保存配置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInvoiceMessage,
  setChangeStatus,
  setInvoiceMessage
} from '@/api/systemSetup'
export default {
  name: 'CommoditySetting',
  data () {
    return {
      loading: false,
      integralSwitch: 0,
      rules: {
        points_ratio: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ]
      },
      integralData: {}
    }
  },
  async mounted () {
    this.fetchConfig()
  },
  methods: {
    setConfigStatus () {
      setChangeStatus({ type: 'points', is_enabled: this.integralSwitch }).then(
        () => {
          this.$notify.success(
            this.integralSwitch === 1 ? '开启积分配置' : '关闭积分兑换'
          )
        }
      )
    },

    handleSave() {
      let data = null
      let ref = null
      data = this.integralData
      ref = 'reqiredForm'
      this.$refs[ref].validate((valid) => {
        if (valid) {
          setInvoiceMessage({ type: 'points', ...data }).then(() => {
            this.$notify.success('保存成功')
          })
        } else {
          this.$notify.warning('未按要求填写表单')
          return false
        }
      })
    },

    async fetchConfig () {
      this.loading = true
      const {
        data: { value, is_enabled }
      } = await getInvoiceMessage('points')
      this.loading = false
      this.integralData = value
      this.integralSwitch = is_enabled
    }
  }
}
</script>
<style lang="scss" scoped>
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
.config-warp {
  width: 50%;
}
.point-label-text {
  color: #888888;
}
.point-label-input {
  font-size: 16px;
  width: 70px;
}
</style>

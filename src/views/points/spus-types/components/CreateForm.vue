<template>
  <div style="margin: 20px 20px;">
    <div id="container">
      <el-form ref="addProductForm" :model="addProductForm" label-width="120px" label-position="right" :rules="addProductRule">
        <el-form-item label="分类名称:" prop="name">
          <el-input v-model="addProductForm.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="分类层级:" prop="level">
          <el-radio-group v-model="addProductForm.level">
            <el-radio :label="1">一级分类</el-radio>
            <el-radio :label="2">二级分类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addProductForm.level === 2" label="所属上级分类:" prop="pid">
          <el-select v-model="addProductForm.pid" placeholder="请选择">
            <el-option v-for="item in spusTypesList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <div class="dialog-footer">
          <el-button @click="$emit('input', false)"> 取消 </el-button>
          <el-button type="primary" @click="id?submitEdit():submitCreate()"> 确认 </el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import { getSpusTypesDetail, addSpusTypes, updateSpusTypes, getSpusTypes } from '@/api/systemSetup'
import { getToken } from '@/utils/auth'

const defaultProduct = {
  name: '',
  level: 1,
  pid: ''
}
export default {
  name: 'AddProduct',
  props: {
    id: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      spusTypesList: [],
      addProductRule: {
        name: [{ required: true, trigger: 'change', message: '请填写分类名称' }],
        level: [{ required: true, trigger: 'change', message: '请选择分类层级' }],
        pid: [{ required: true, trigger: 'change', message: '请选择所属上级分类' }]
      },
      addProductForm: {
        ...defaultProduct
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_API + '/admin/fapiao/attachments'
    }
  },
  watch: {
    id: {
      handler(n) {
        if (n) {
          this.fetchDetail()
        }
      }
    },
    immediate: true
  },
  created() {
    this.getSpusTypesList()
  },
  methods: {
    async getSpusTypesList() {
      const params = {
        page: 1,
        limit: 10000,
        level: 1
      }
      const { data } = await getSpusTypes(params)
      this.spusTypesList = data.map((item) => ({
        id: item.id,
        name: item.name,
        level: item.level
      }))
      console.log(this.spusTypesList)
    },
    onChange(file, fileList) {
      console.log('onChange', fileList)
      this.imgFileList = fileList
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogVisible = true
      this.dialogImageUrl = file.url
    },

    // 清空
    cancelProduct() {
      this.addProductForm = []
      this.imgFileList = []
    },

    // 保存
    submitCreate() {
      addSpusTypes({
        name: this.addProductForm.name,
        pid: this.addProductForm.level === 1 ? 0 : this.addProductForm.pid
      }).then(() => {
        this.$notify.success('添加分类成功')
        this.$emit('refresh')
      }).catch(() => {
        this.$notify.error('添加分类失败')
      })
    },

    // 提交
    submitEdit() {
      updateSpusTypes({
        id: this.id,
        name: this.addProductForm.name,
        pid: this.addProductForm.level === 1 ? 0 : this.addProductForm.pid
      }).then(() => {
        this.$notify.success('添加分类成功')
        this.$emit('refresh')
      }).catch(() => {
        this.$notify.error('添加分类失败')
      })
    },

    // 回显数据
    fetchDetail() {
      getSpusTypesDetail(this.id).then((value) => {
        this.addProductForm = value.data
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>

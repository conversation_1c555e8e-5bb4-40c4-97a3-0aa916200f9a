<template>
  <div style="margin: 20px 20px;">
    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      stripe
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      height="calc(100vh - 240px)"
      :data="spusTypesList"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="二级分类名" prop="name" show-overflow-tooltip>
        <template v-slot="scope">
          <span v-if="scope.row.level === 1" class="label-key" style="cursor: pointer;color: #0067E1" @click="viewSub(scope.row)">{{ scope.row.name }}</span>
          <span v-else class="label-key">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template v-slot="scope">
          <el-button type="text" size="medium" @click="$emit('delete', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { addSpusTypes, updateSpusTypes, getSpusTypes } from '@/api/systemSetup'
import { getToken } from '@/utils/auth'

const defaultProduct = {
  name: '',
  level: 1,
  pid: ''
}
export default {
  name: 'AddProduct',
  props: {
    id: {
      type: Number,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      spusTypesList: [],
      addProductRule: {
        name: [{ required: true, trigger: 'change', message: '请填写分类名称' }],
        level: [{ required: true, trigger: 'change', message: '请选择分类层级' }],
        pid: [{ required: true, trigger: 'change', message: '请选择所属上级分类' }]
      },
      addProductForm: {
        ...defaultProduct
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_API + '/admin/fapiao/attachments'
    }
  },
  watch: {
    id: {
      handler(n) {
        if (n) {
          console.log(n)
          this.fetchDetail()
        }
      }
    },
    immediate: true
  },
  mounted() {
    this.fetchDetail()
  },
  methods: {
    onChange(file, fileList) {
      console.log('onChange', fileList)
      this.imgFileList = fileList
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogVisible = true
      this.dialogImageUrl = file.url
    },

    // 清空
    cancelProduct() {
      this.addProductForm = []
      this.imgFileList = []
    },

    // 保存
    submitCreate() {
      addSpusTypes({
        name: this.addProductForm.name,
        pid: this.addProductForm.level === 1 ? 0 : this.addProductForm.pid
      }).then(() => {
        this.$notify.success('添加分类成功')
        this.$emit('refresh')
      }).catch(() => {
        this.$notify.error('添加分类失败')
      })
    },

    // 提交
    submitEdit() {
      updateSpusTypes({
        id: this.id,
        name: this.addProductForm.name,
        pid: this.addProductForm.level === 1 ? 0 : this.addProductForm.pid
      }).then(() => {
        this.$notify.success('添加分类成功')
        this.$emit('refresh')
      }).catch(() => {
        this.$notify.error('添加分类失败')
      })
    },

    // 回显数据
    fetchDetail() {
      const params = {
        page: 1,
        limit: 10000,
        level: 2,
        pid: this.id
      }
      Object.keys(params).forEach((key) => {
        if (params[key] === '' || params[key] === null) {
          delete params[key]
        }
      })
      getSpusTypes(params)
        .then((value) => {
          this.spusTypesList = value.data
        })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>

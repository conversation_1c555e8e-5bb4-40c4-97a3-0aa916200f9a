<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button type="primary" size="small" @click="addBtn"><i class="el-icon-plus" style="margin-right: 10px" />添加分类</el-button>
        <el-button type="danger" size="small" @click="deleteBtn"><i class="el-icon-delete" style="margin-right: 10px" />批量删除</el-button>
      </div>
      <div class="filter-container">
        <span class="label-key">商品分类名称：</span>
        <el-input v-model="searchParams.name" placeholder="请输入" size="small" class="query-select-long" />
        <span class="label-key">商品分类层级：</span>
        <el-select v-model="searchParams.level" placeholder="请选择" size="small" class="query-select-long">
          <el-option label="全部" value="" />
          <el-option label="一级分类" :value="1" />
          <el-option label="二级分类" :value="2" />
        </el-select>
        <span class="label-key">创建时间：</span>
        <el-date-picker
          v-model="date"
          size="small"
          type="daterange"
          value-format="yyyy-MM-dd"
          class="daterange-condition"
          range-separator="至"
          start-placeholder="请选择日期"
          end-placeholder="请选择日期"
        />
        <el-button type="primary" size="small" @click="selectPointIndex">查询</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </div>
    </div>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      stripe
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      height="calc(100vh - 240px)"
      :data="productData"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="id" prop="id" width="300" />
      <el-table-column label="商品分类名（点击查看所有二级分类）" prop="name" show-overflow-tooltip>
        <template v-slot="scope">
          <span v-if="scope.row.level === 1" class="label-key" style="cursor: pointer;color: #0067E1" @click="viewSub(scope.row)">{{ scope.row.name }}</span>
          <span v-else class="label-key">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属层级" prop="level" width="120">
        <template v-slot="scope">
          <span v-if="scope.row.level === 1">一级分类</span>
          <span v-else-if="scope.row.level === 2">二级分类</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="所属上级分类" prop="parent.name" width="120" />
      <el-table-column label="创建日期" prop="created_at" width="240" />
      <el-table-column fixed="right" label="操作" width="200">
        <template v-slot="scope">
          <el-button type="text" size="medium" @click="editMessage(scope.row);">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" size="medium" @click="deleteMessage(scope.row);">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination small background :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 15, 20, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper" :total="totalOrder" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    <el-dialog
      :visible.sync="dialogFormVisible"
      :title="textMap[dialogStatus]"
      :close-on-click-modal="false"
      width="700px"
    >
      <create-form
        v-if="dialogFormVisible"
        :id="editId"
        v-model="dialogFormVisible"
        :dialog-status="dialogStatus"
        @refresh="resetSearch"
      />
    </el-dialog>
    <el-dialog
      :visible.sync="dialogFormVisible2"
      title="二级分类"
      :close-on-click-modal="false"
      width="1000px"
    >
      <DialogList :id="editId" ref="DialogList" @delete="deleteMessage" />
    </el-dialog>
  </div>
</template>

<script>
import { getSpusTypes, deleteSpusTypes, batchDeleteSpusTypes } from '@/api/systemSetup'
import CreateForm from './components/CreateForm'
import DialogList from './components/DialogList'

export default {
  name: 'Commodity',
  components: { CreateForm, DialogList },
  data () {
    const store = this.$store || {}
    const state = store.state || {}
    const user = state.user || {}
    const info = user.info || {}
    return {
      branchs: [],
      editId: '',
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '修改分类',
        create: '添加分类'
      },
      temp: {},
      loading: false,
      flowStatus: '已上架',
      productID: '',
      productName: '',
      productStatus: '',
      branch_id: info.group_id === 3 ? info.admin_id : null,
      productData: [],
      addProductForm: [],
      addProductShow: false,
      currentPage: 1,
      pageSize: 15,
      totalOrder: 0,
      date: '',
      dialogFormVisible2: false,
      searchParams: {
        page: 1,
        limit: 10,
        name: '',
        level: '',
        created_at_start: '',
        created_at_end: ''
      }
    }
  },
  mounted () {
    this.getSpusTypes()
  },
  created() {
    this.fetchBranch()
  },
  methods: {
    viewSub (row) {
      this.editId = row.id
      this.dialogFormVisible2 = true
    },
    deleteMessage (row) {
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSpusTypes(row.id).then(() => {
          this.$notify.success('删除分类成功')
          this.getSpusTypes()
          this.dialogFormVisible2 = false
        }).catch(() => {
          this.$notify.error('删除分类失败')
        })
      })
    },
    findOnSale (onSale) {
      switch (onSale) {
        case 0:
          return '未上架'
        case 1:
          return '已上架'
        default:
          return '错误'
      }
    },
    editMessage (row) {
      this.dialogFormVisible = true
      this.editId = ''
      this.$nextTick(() => {
        this.editId = row.id.toString()
        this.dialogStatus = 'update'
      })
    },
    addBtn () {
      this.dialogFormVisible = true
      this.editId = ''
      this.dialogStatus = 'create'
    },

    // 查询积分商品列表
    getSpusTypes () {
      this.loading = true
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        name: this.searchParams.name,
        level: this.searchParams.level,
        created_at_start: this.date ? this.date[0] : '',
        created_at_end: this.date ? this.date[1] : ''
      }
      Object.keys(params).forEach((key) => {
        if (params[key] === '' || params[key] === null) {
          delete params[key]
        }
      })
      getSpusTypes(params)
        .then((value) => {
          this.productData = value.data
          this.totalOrder = value.data.length
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getSpusTypes()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getSpusTypes()
    },

    selectPointIndex () {
      this.getSpusTypes()
    },

    // 重置所属信息
    resetSearch () {
      const store = this.$store || {}
      const state = store.state || {}
      const user = state.user || {}
      const info = user.info || {}
      this.productID = ''
      this.productName = ''
      this.productStatus = ''
      this.date = ''
      this.searchParams.name = ''
      this.searchParams.level = ''
      this.searchParams.created_at_start = ''
      this.searchParams.created_at_end = ''
      this.branch_id = info.group_id === 3 ? info.admin_id : null
      this.getSpusTypes()
      this.dialogFormVisible = false
    },

    // 批量删除
    deleteBtn () {
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.$refs.multipleTableRef.selection.map((item) => item.id)
        batchDeleteSpusTypes({ ids }).then(() => {
          this.$notify.success('删除分类成功')
          this.getSpusTypes()
        }).catch(() => {
          this.$notify.error('删除分类失败')
        })
      })
    }
  }
}
</script>

  <style scoped>
  .label-key{
      margin-left: 10px;
  }
  #container {
    padding: 20px;
    width: 100%;
  }

  .el-pagination {
    margin-top: 10px;
    text-align: right;
  }
  </style>

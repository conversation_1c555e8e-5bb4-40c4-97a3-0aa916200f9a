<template>
  <div class="app-container">
    <template v-if="permissions && permissions.length > 0">
      <el-radio-group v-model="activeName" size="small" @change="change">
        <el-radio-button
          v-for="item in permissions"
          :key="item.permission"
          :label="item.permission"
        >{{ item.meta.title }}</el-radio-button>
      </el-radio-group>
      <class-list v-if="activeName === 'LineClass'" />
      <schedule-list v-if="activeName === 'LineClassTrain'" />
      <time-line v-if="activeName === 'ScheduledLineClassTrain'" />
    </template>
    <el-empty v-else description="暂无访问权限" />
  </div>
</template>

<script>
import ClassList from './components/ClassList'
import ScheduleList from './components/ScheduleList'
import TimeLine from './components/TimeLine'

export default {
  name: 'LineManage',
  components: { ScheduleList, ClassList, TimeLine },
  data() {
    return {
      activeName: ''
    }
  },

  computed: {
    permissions() {
      return this.$route && this.$route.meta && this.$route.meta.page.filter((o) => o.enabled)
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.activeName = route.query.active || (this.permissions && this.permissions[0] && this.permissions[0].permission)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.activeName = this.permissions && this.permissions[0] && this.permissions[0].permission
  },
  methods: {
    change(e) {
      this.$router.push({
        name: 'dingzhikeyunLineManage',
        query: {
          active: e
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form {
  width: 50%;
  padding-left: 8px;
}
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

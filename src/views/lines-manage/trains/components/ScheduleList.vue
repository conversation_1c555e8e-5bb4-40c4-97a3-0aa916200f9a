<template>
  <div class="component-container">
    <div class="hanldle-container query-container">
      <div class="filter-container form-query">
        <div class="item-filter">
          <span class="label-key">所属分台</span>
          <branch-select
            v-model="listQuery.branch_id"
            size="small"
            placeholder="请选择"
            @change="queryChange"
          />
        </div>
        <div class="item-filter">
          <span class="label-key">线路状态</span>
          <el-select
            v-model="listQuery.status"
            size="small"
            clearable
            placeholder="请选择"
            @change="queryChange"
          >
            <el-option label="停运中" value="stop" />
            <el-option label="运营中" value="running" />
          </el-select>
        </div>
        <div class="item-filter">
          <span class="label-key">班次编号</span>
          <el-input
            v-model="listQuery.line_class_train_no"
            placeholder="请输入"
            size="small"
            @keyup.enter.native="handleFilter"
          />
        </div>
        <div class="item-filter">
          <span class="label-key">线路ID</span>
          <el-input
            v-model="listQuery.id"
            placeholder="请输入"
            size="small"
            @keyup.enter.native="handleFilter"
          />
        </div>
        <div class="item-filter">
          <span class="label-key">起止城市</span>
          <el-col :span="11">
            <el-select
              v-model="listQuery.region"
              size="small"
              placeholder="出发城市"
            >
              <el-option label="区域一" value="shanghai" />
              <el-option label="区域二" value="beijing" />
            </el-select>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-select
              v-model="listQuery.region"
              size="small"
              placeholder="目的城市"
            >
              <el-option label="区域一" value="shanghai" />
              <el-option label="区域二" value="beijing" />
            </el-select>
          </el-col>
        </div>
        <div class="item-filter">
          <span class="label-key">起止名称</span>
          <el-col :span="11">
            <el-select
              v-model="listQuery.start_name"
              size="small"
              placeholder="出发点名称"
            >
              <el-option label="区域一" value="shanghai" />
              <el-option label="区域二" value="beijing" />
            </el-select>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-select
              v-model="listQuery.end_name"
              size="small"
              placeholder="目的点名称"
            >
              <el-option label="区域一" value="shanghai" />
              <el-option label="区域二" value="beijing" />
            </el-select>
          </el-col>
        </div>

        <div class="item-filter search-warp">
          <el-button
            class="filter-item"
            type="primary"
            size="small"
            @click.stop="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            class="filter-item"
            size="small"
            type="primary"
            plain
            @click.stop="handleReset"
          >
            重置
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      ref="orderMultiple"
      v-loading="isLoading"
      stripe
      class="line-table"
      :data="list"
      fit
      highlight-current-row
      :height="`calc(100vh - 330px)`"
      :cell-style="{ padding: '6px 0' }"
      size="small"
      @sort-change="sortChange"
    >
      <el-table-column
        label="发车日期"
        align="left"
        prop="start_date"
        width="100"
        fixed
      />
      <el-table-column label="发车时间" align="left" width="200">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.start_time_type === 2" size="mini">滚动</el-tag>
          <el-tag v-else type="info" size="mini">固定</el-tag>
          <span
            v-if="scope.row.start_time_type === 2"
            class="fb ml"
          >{{ scope.row?.start_earliest_time }}-{{
              scope.row?.end_latest_time
          }}</span><span v-else class="fb ml">{{ scope.row?.start_time }}</span>
          <p v-if="scope.row.start_time_type === 1"> {{ scope.row | leaveTime }} </p>
        </template>
      </el-table-column>
      <el-table-column
        label="线路ID"
        align="left"
        prop="line_class.id"
        width="70"
        fixed
      />
      <el-table-column
        align="left"
        label="出发地"
        width="200"
        show-overflow-tooltip
        class-name="customcss"
      >
        <template slot-scope="scope">
          <span class="line-label">城市</span>
          <span class="badge-status-start" />{{ scope.row?.line_class?.start_city?.name }}
          <p>
            <span class="line-label">名称</span>
            <el-tag effect="dark" size="mini">
              {{ scope.row?.line_class?.start_name }}
            </el-tag>
          </p>
          <p v-if="scope.row.start_address_type === 2">
            <span class="line-label">上车点</span><el-link
              style="font-size: 12px"
              :underline="false"
              @click.stop="preViewMap('custom', 'start', scope.row)"
            >围栏内任意点
              <i class="el-icon-view el-icon--right" />
            </el-link>
          </p>
          <p v-else>
            <span class="line-label">上车点</span><el-link
              style="font-size: 12px"
              :underline="false"
              @click.stop="preViewMap('fix', 'start', scope.row)"
            >固定上车点
              <i class="el-icon-view el-icon--right" />
            </el-link>
          </p>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="目的地"
        width="200"
        show-overflow-tooltip
        class-name="customcss"
      >
        <template slot-scope="scope">
          <span class="line-label">城市</span>
          <span class="badge-status-end" />
          <span>{{ scope.row?.line_class?.end_city.name }}</span>
          <p>
            <span class="line-label">名称</span>
            <el-tag effect="dark" size="mini">{{ scope.row?.line_class?.end_name }}</el-tag>
          </p>
          <p v-if="scope.row.end_address_type === 2">
            <span class="line-label">下车点</span>
            <el-link
              style="font-size: 12px"
              :underline="false"
              @click.stop="preViewMap('custom', 'end', scope.row)"
            >围栏内任意点<i
              class="el-icon-view el-icon--right"
            /></el-link>
          </p>
          <p v-else>
            <span class="line-label">下车点</span><el-link
              style="font-size: 12px"
              :underline="false"
              @click.stop="preViewMap('fix', 'end', scope.row)"
            >固定下车点
              <i class="el-icon-view el-icon--right" />
            </el-link>
          </p>
        </template>
      </el-table-column>
      <el-table-column label="座位设置" align="left" width="140">
        <template slot-scope="scope">
          <p v-if="scope.row.car_seats" class="table-p">
            {{ scope.row?.car_seats }}座车
          </p>
          <div v-if="scope.row.is_seat_selection === 1" class="un-seat is-seat">
            <img src="@/assets/seat.svg">
            可选座
          </div>
          <div v-else class="un-seat">
            <img src="@/assets/un-seat.svg">
            不可选座
          </div>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="left" width="140">
        <template slot-scope="scope">
          <template v-if="scope.row.is_seat_selection === 1">
            <!-- <p v-for="(item,i) in scope.row.seat_price" :key="i" class="table-p">{{ item.name }}:{{ item.price }}元</p> -->
            <el-popover placement="top" width="300" trigger="click">
              <Seat
                :can-check="false"
                :seat-price="scope.row.seat_price"
                :seat-layout="
                  scope.row.seat_layout
                    ? scope.row.seat_layout
                    : scope.row.seat_price.length === 5
                      ? '2-3'
                      : scope.row.seat_price.length === 7
                        ? '2-2-3'
                        : ''
                "
              />
              <el-button
                slot="reference"
                type="text"
              >座位价格<i
                class="el-icon-view el-icon--right"
              /></el-button>
            </el-popover>
          </template>
          <template v-else>
            <p class="table-p">售价：{{ scope.row.price }}元</p>
            <p class="table-p">渠道：{{ scope.row.channel_price }}元</p>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="所属分台"
        align="left"
        min-width="150"
      >
        <template slot-scope="scope">
          <p class="table-p">{{ scope.row?.branch?.mchname }}</p>
        </template>
      </el-table-column>
      <el-table-column label="余票/总票数" align="left" width="120">
        <template slot-scope="scope">
          {{ scope.row.remain_tickets }}/{{ scope.row.total_tickets }}
        </template>
      </el-table-column>
      <el-table-column label="线路状态" align="left" width="100">
        <template slot-scope="scope">
          <Tablestatus
            v-if="scope.row.status === 1"
            mode="processing"
            word="运行中"
          />
          <Tablestatus v-else mode="default" word="已停运" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="left" label="操作" width="160">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="preView(scope.row.order_id)"
          >售票列表</el-button>
          <el-divider direction="vertical" />
          <el-dropdown
            size="mini"
            trigger="hover"
            @command="(command) => actionClose(command, scope.row)"
          >
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item disabled command="close">编辑</el-dropdown-item>
              <el-dropdown-item
                disabled
                command="finish"
              >删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.per_page"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :visible.sync="dialogDes"
      title="订单详情"
      append-to-body
      width="850px"
    >
      <!-- <Description :order="orderDetailId" /> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            size="small"
            @click="dialogDes = false"
          >确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogMap"
      :title="map_title"
      class="noPadding pointMap"
      append-to-body
      width="600px"
    >
      <!-- <Description :order="orderDetailId" /> -->
      <map-view ref="pointMap" :map-params="mapParams" />
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            size="small"
            @click="dialogMap = false"
          >确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { dispatchBranches, closeOrders } from '@/api/business'
import { ScheduleList } from '@/api/lines'
import Seat from '@/components/Seat'
import MapView from './MapView'
import BranchSelect from '@/components/BranchSelect'
// import moment from 'moment'
import { mapGetters } from 'vuex'

export default {
  components: {
    MapView,
    Seat,
    BranchSelect
  },

  filters: {
    leaveTime(row) {
      const day_ = row.stop_sell_number > 0 ? row.stop_sell_number + '天' : ''
      const hour_ = row.stop_sell_time.slice(0, 2) !== '00' ? row.stop_sell_time.slice(0, 2) + '小时' : ''
      const minute_ = row.stop_sell_time.slice(3, 5) !== '00' ? row.stop_sell_time.slice(2, 4) + '分钟' : ''
      return (day_ || hour_ || minute_) ? `发车前${day_}${hour_}${minute_}停售` : ''
    }
  },
  data() {
    return {
      map_title: '',
      mapParams: {},
      dialogMap: false,
      dialogDes: false,
      currentOrder: {},
      orderDetailId: '',
      list: null,
      isLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        per_page: 15
      },
      branchs: []
    }
  },
  computed: {
    ...mapGetters(['info'])
  },
  watch: {
    $route: {
      handler: function(route) {
        console.log(route.query)
        this.listQuery.line = route.query.line_class_id
        this.fetchData(true)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.fetchBranch()
  },
  methods: {
    preViewMap(type, tag, row) {
      this.mapParams = {
        type: type,
        tag: tag,
        params: row
      }
      const dia_txt = {
        custom: {
          start: '上车点围栏范围',
          end: '下车点围栏范围'
        },
        fix: {
          start: '查看上车点',
          end: '查看下车点'
        }
      }
      this.map_title = dia_txt[type][tag]
      this.dialogMap = true
      this.$nextTick(async () => {
        await this.$refs.pointMap.initMap()
        if (type === 'custom') {
          this.$refs.pointMap.setPolygon(tag, row)
        } else {
          this.$refs.pointMap.setPoints(tag, row)
        }
      })
    },
    queryChange() {
      // 自动筛选
      this.handleFilter()
    },
    async sortChange(e) {
      const { prop, order } = e
      this.listQuery['sort_by'] = order ? prop : null
      this.listQuery['sort'] =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.listQuery.page = 1
      this.fetchData(true)
    },
    async fetchBranch() {
      // 不需要手动获取分台列表，BranchSelect组件会自动处理
      // 设置默认值为null (表示"全部")
      this.$set(this.listQuery, 'branch_id', null)
    },
    preView(id) {
      this.orderDetailId = id.toString()
      this.dialogDes = true
    },
    actionClose(_, order) {
      switch (_) {
        case 'refund':
          this.currentOrder = order
          console.log(order)
          this.$nextTick(() => {
            this.dialogRefound = true
          })
          break
        default:
          this.$confirm(
            `确认关闭当前订单？关闭后无法恢复，请谨慎操作`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            closeOrders({
              order: order.order_id
            }).then(() => {
              this.$notify({
                message: '操作成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            })
          })
      }
    },

    async fetchData(loading = true) {
      if (loading) this.isLoading = true
      const query = Object.assign({}, this.listQuery)
      const { data } = await ScheduleList(query)
      if (loading) this.isLoading = false
      this.list = data.data || []
      this.total = data.meta.pagination.total
    },

    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        branch_id: null // 重置为null，表示"全部"
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    }
  }
}
</script>
<style lang="scss" scoped>
.query-container {
  align-items: flex-end;
}

.line-label {
  display: inline-block;
  width: 46px;

  &::after {
    content: ":";
    position: relative;
    top: -0.5px;
    margin: 0 6px 0 2px;
  }
}

.badge-status-start {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #52c41a;
}
.badge-status-end {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #ff4d4f;
}

.un-seat {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #bebebe;

  img {
    width: 12px;
    display: block;
    margin-right: 3px;
    object-fit: contain;
  }
}

.is-seat {
  color: #0dbe9b;
}

.form-query {
  flex: 1;
  flex-wrap: wrap;
}

.search-warp {
  margin-left: 25%;
  justify-content: flex-end;
}

.line {
  text-align: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}

.surplus {
  color: #67c23a;
}

.nosurplus {
  color: #f56c6c;
}

.refund-gray {
  color: gray;
}

.line-show-name {
  background-color: #3399ff;
  color: #fff;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 12px;
  transform: scale(0.9);
}

.line-show-warp {
  display: flex;
  align-items: center;
}

.item-filter {
  display: flex;
  align-items: center;
  line-height: 44px;
  padding: 0 12px;
  flex: 0 0 25%;
  max-width: 25%;

  .label-key {
    flex: 0 0 90px;
    text-align: right;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    font-size: 13px;

    &::after {
      content: ":";
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
    }
  }
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-left {
  text-align: left;
}
</style>

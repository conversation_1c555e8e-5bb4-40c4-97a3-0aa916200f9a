<template>
  <div  class="description-warp">
    <!-- <el-skeleton v-if="loading" style="height: 400px" /> -->
    <h6 class="item-description-title">线路基本信息</h6>
    <el-descriptions v-if="Object.keys(lineDetail).length" :column="2" size="small" border>
      <el-descriptions-item label="路线ID"
        >{{ lineDetail.id }}
      </el-descriptions-item>
      <el-descriptions-item label="线路状态">
        <Tablestatus
          v-if="lineDetail.status === 1"
          mode="processing"
          word="运行中"
        />
        <Tablestatus v-else mode="default" word="已停运" />
      </el-descriptions-item>
      <el-descriptions-item label="出发城市">
          <span class="fb">{{ lineDetail.start_city.name }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="目的城市">
          <span class="fb">{{ lineDetail.end_city.name }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="出发地">
        <el-tag  type="success" size="mini">
          {{ lineDetail.start_name }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="目的地">
        <el-tag  type="danger"  size="mini">{{ lineDetail.end_name }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="上车点">
        <el-link style="font-size: 12px"  type="primary"  :underline="false"  @click.stop="preViewMap(lineDetail.start_address_type === 2?'custom':'fix', 'start', lineDetail)" >
          {{lineDetail.start_address_type === 2?'查看电子围栏':'查看固定上车点'}}<i class="el-icon-view el-icon--right" />
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item label="下车点">
        <el-link style="font-size: 12px"  type="primary"  :underline="false"  @click.stop="preViewMap(lineDetail.end_address_type === 2?'custom':'fix', 'end', lineDetail)" >
          {{lineDetail.end_address_type === 2?'查看电子围栏':'查看固定下车点'}}<i class="el-icon-view el-icon--right" />
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item label="出发时间">
        <el-tag v-if="lineDetail.start_time_type === 2" size="mini"
          >滚动发车</el-tag
        >
        <el-tag v-else type="info" size="mini">固定发车</el-tag>
        <span v-if="lineDetail.start_time_type === 2" class="fb ml"
          >{{ lineDetail?.start_earliest_time.slice(0, 5) }}-{{
            lineDetail?.end_latest_time.slice(0, 5)
          }}</span
        >
        <span v-else class="fb ml">{{ lineDetail?.start_time }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="座位设置">
        {{ lineDetail?.car_seats }}座车
      </el-descriptions-item>
      <el-descriptions-item label="是否支持选座">
        <div v-if="lineDetail.is_seat_selection === 1" class="un-seat is-seat">
          <img src="@/assets/seat.svg" />
          可选座
        </div>
        <div v-else class="un-seat">
          <img src="@/assets/un-seat.svg" />
          不可选座
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="座位价格">
        <template v-if="lineDetail.is_seat_selection === 1">
          <Seat
            :can-check="false"
            :seat-price="lineDetail.seat_price"
            :seat-layout="
              lineDetail.seat_layout
                ? lineDetail.seat_layout
                : lineDetail.seat_price.length === 5
                ? '2-3'
                : lineDetail.seat_price.length === 7
                ? '2-2-3'
                : ''
            "
          />
        </template>
        <template v-else>
          <p class="table-p">售价：{{ lineDetail.price }}元</p>
          <p class="table-p">渠道：{{ lineDetail.channel_price }}元</p>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="所属分台"
        >{{ lineDetail?.branch?.mchname }}
      </el-descriptions-item>
      <el-descriptions-item label="当前排班类型">
        <el-tag
          v-if="lineDetail.scheduling_type === 2"
          size="mini"
          effect="plain"
          >自动排班</el-tag
        >
        <el-tag v-else type="danger" size="mini" effect="plain"
          >手动排班</el-tag
        >
      </el-descriptions-item>
      <el-descriptions-item label="最新排班时间"
        >{{ lineDetail.last_scheduling_time.slice(0, 10) || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="路线说明"
        >{{ lineDetail.seat_title || "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <el-dialog
      :visible.sync="dialogMap"
      :title="map_title"
      class="noPadding pointMap"
      append-to-body
      width="600px"
    >
      <map-view ref="pointMap" :map-params="mapParams" />
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            size="small"
            @click="dialogMap = false"
          >确认</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
<script>
import Seat from '@/components/Seat'
import MapView from './MapView'

export default {
    components: {
    Seat,
    MapView
  },
  props: {
    lineDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      map_title:'',
      mapParams:{},
      dialogMap:false
    };
  },
  methods: {
    preViewMap(type, tag, row) {
      this.mapParams = {
        type: type,
        tag: tag,
        params: row
      }
      const dia_txt = {
        custom: {
          start: '上车点围栏范围',
          end: '下车点围栏范围'
        },
        fix: {
          start: '查看上车点',
          end: '查看下车点'
        }
      }
      this.map_title = dia_txt[type][tag]
      this.dialogMap = true
      this.$nextTick(async () => {
        await this.$refs.pointMap.initMap()
        if (type === 'custom') {
          this.$refs.pointMap.setPolygon(tag, row)
        } else {
          this.$refs.pointMap.setPoints(tag, row)
        }
      })
    },

  },
};
</script>
<style lang="scss" scoped>
.un-seat {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #bebebe;

  img {
    width: 12px;
    display: block;
    margin-right: 3px;
    object-fit: contain;
  }
}

.is-seat {
  color: #0dbe9b;
}
.description-warp {
  .el-descriptions--small.is-bordered .el-descriptions-item__cell {
    min-width: 80px;
  }
  .el-descriptions-item__content {
    min-width: 280px;
  }
}
.el-descriptions__header {
  margin-bottom: 10px;
  margin-top: 10px;
}
.item-description-title {
  position: relative;
  font-size: 15px;
  margin:0 0 16px 0;
  &:before {
    width: 4px;
    border-radius: 5px;
    height: 60%;
    background: #0067e1;
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    margin: auto;
    content: "";
    display: block;
  }
}
</style>

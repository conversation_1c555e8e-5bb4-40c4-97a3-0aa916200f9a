<template>
  <div>
    <div v-if="direction==='forward'" class="table-header">
      <span class="table-header-title">{{ title }}</span>
    </div>
    <div v-else class="table-header">
      <span class="table-header-title">{{ title }}</span>
    </div>
    <el-table
      ref="orderMultiple" class="line-table el-table-time" :data="list"
      :border="false"
      fit highlight-current-row :cell-style="{ padding: '6px 0' }"
      size="small"
      style="width: 100%;"
      @sort-change="sortChange"
    >

      <el-table-column sortable label="出发时间" align="left" prop="start_time" width="150">
        <template v-if="scope.row.line_class_id" slot-scope="scope">
          <el-tooltip v-if="scope.row.is_overtime_trip" content="加班车" placement="top">
            <el-tag style="margin-right: 2px;" size="mini" round type="warning">加</el-tag>
          </el-tooltip>
          <el-tooltip v-if="scope.row.is_manually_modified" content="发车时间已改" placement="top">
            <el-tag style="margin-right: 2px;" size="mini" round type="danger">改</el-tag>
          </el-tooltip>
          <el-tag style="cursor: pointer;" effect="plain" type="primary" size="mini" round class="fb" @click="openTimeDialog(scope.row)">  {{ scope.row.start_time_type===2?( (scope.row.start_earliest_time ? scope.row.start_earliest_time.slice(0,5):'') +'-'+ (scope.row.end_latest_time ? scope.row.end_latest_time.slice(0,5) : '') ): (scope.row.start_time ? scope.row.start_time.slice(0,5) : '') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="当班司机[排序][车牌·座型][已接单]" align="left" min-width="320">
        <template v-if="scope.row.drivers" slot-scope="scope">
          <el-collapse v-if="scope.row.drivers && scope.row.drivers.length>1">
            <el-collapse-item>
              <template slot="title">
                <div v-for="(item ,i) in (scope.row.drivers && scope.row.drivers.length > 0 ? [scope.row.drivers[0]] : [])" :key="i" class="driver-line">
                  <el-dropdown size="mini" trigger="click" @command="(_)=>handleSetDriver(_,scope.row,item)">
                    <span class="el-dropdown-link">
                      <el-tag effect="dark" type="" size="mini">更换</el-tag>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="edit">更改司机</el-dropdown-item>
                      <el-dropdown-item command="changeVehicles">更改车辆</el-dropdown-item>
                      <el-dropdown-item command="del">取消排班</el-dropdown-item>
                      <el-dropdown-item command="changeTickets">修改票数</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-popover
                    :ref="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                    :popper-class="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                    placement="top"
                    @show="showPopover(item)"
                  >
                    <div class="edit-warp">
                      排序： <el-input-number slot="content" v-model="openSort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
                      <el-button type="primary" size="mini" @click.stop="handleCheckSort(scope.row,item)">确定</el-button>
                    </div>
                    <span slot="reference" class="cell-label cursor">[ {{ (item && item.pivot && item.pivot.sort)||'-' }} ]</span>
                  </el-popover>
                  <span class="cell-label cell-name cursor color-primary" @click.stop="handleSetDriver('edit',scope.row,item)">{{ item && item.name }}</span>
                  <span class="cell-label">[ <span class="color-primary" @click.stop="handleChangeVehicles(scope.row,item)">{{ item && item.car_tail_number }}</span>·{{ item && item.total_seating }}座 ]</span> <span class="cell-label">[ {{ item && item.booked_orders }} ]</span>
                  <span v-if="scope.row.is_seat_selection" class="seat">选座</span>
                </div>
              </template>

              <div v-for="(item ,i) in (scope.row.drivers ? scope.row.drivers.slice(1) : [])" :key="i" class="driver-line">
                <el-dropdown size="mini" trigger="click" @command="(_)=>handleSetDriver(_,scope.row,item)">
                  <span class="el-dropdown-link">
                    <el-tag effect="dark" type="" size="mini">更换</el-tag>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit">更改司机</el-dropdown-item>
                    <el-dropdown-item command="changeVehicles">更改车辆</el-dropdown-item>
                    <el-dropdown-item command="del">取消排班</el-dropdown-item>
                    <el-dropdown-item command="changeTickets">修改票数</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-popover
                  :ref="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                  :popper-class="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                  placement="top"
                  @show="showPopover(item)"
                >
                  <div class="edit-warp">
                    排序： <el-input-number slot="content" v-model="openSort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
                    <el-button type="primary" size="mini" @click.stop="handleCheckSort(scope.row,item)">确定</el-button>
                  </div>
                  <span slot="reference" class="cell-label cursor">[ {{ (item && item.pivot && item.pivot.sort)||'-' }} ]</span>
                </el-popover>
                <span class="cell-label cell-name cursor color-primary" @click.stop="handleSetDriver('edit',scope.row,item)">{{ item && item.name }}</span>
                <span class="cell-label">[ <span class="color-primary" @click.stop="handleChangeVehicles(scope.row,item)">{{ item && item.car_tail_number }}</span>·{{ item && item.total_seating }}座 ]</span> <span class="cell-label" @click.stop="handleCheckOrder(scope.row,item)">[ <span :class="{'color-primary': item && item.booked_orders}">{{ item && item.booked_orders }}</span> ]</span>
                <span v-if="scope.row.is_seat_selection" class="seat">选座</span>
              </div>
            </el-collapse-item>
          </el-collapse>
          <template v-else>
            <div v-for="(item ,i) in (scope.row.drivers && scope.row.drivers.length > 0 ? [scope.row.drivers[0]] : [])" :key="i" class="driver-line">
              <el-dropdown size="mini" trigger="click" @command="(_)=>handleSetDriver(_,scope.row,item)">
                <span class="el-dropdown-link">
                  <el-tag effect="dark" type="" size="mini">更换</el-tag>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="edit">更改司机</el-dropdown-item>
                  <el-dropdown-item command="changeVehicles">更改车辆</el-dropdown-item>
                  <el-dropdown-item command="changeTickets">修改票数</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-popover
                :ref="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                :popper-class="'popoverRef_' + scope.row.line_class_train_id +'_'+ (item && item.driver_id)"
                placement="top"
                @show="showPopover(item)"
              >
                <div class="edit-warp">
                  排序： <el-input-number slot="content" v-model="openSort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
                  <el-button type="primary" size="mini" @click.stop="handleCheckSort(scope.row,item)">确定</el-button>
                </div>
                <span slot="reference" class="cell-label cursor">[ {{ (item && item.pivot && item.pivot.sort)||'-' }} ]</span>
              </el-popover>
              <span class="cell-label cell-name cursor color-primary" @click.stop="handleSetDriver('edit',scope.row,item)">{{ item && item.name }}</span>
              <span class="cell-label">[ <span class="color-primary" @click.stop="handleChangeVehicles(scope.row,item)">{{ item && item.car_tail_number }}</span>·{{ item && item.total_seating }}座 ]</span> <span class="cell-label" @click.stop="handleCheckOrder(scope.row,item)">[ <span :class="{'color-primary': item && item.booked_orders}">{{ item && item.booked_orders }}</span> ]</span>
              <span v-if="scope.row.is_seat_selection" class="seat">选座</span>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="订/余/总" align="left" prop="total_tickets" width="90" show-overflow-tooltip>
        <template v-if="scope.row.line_class_id" slot-scope="scope">
          <span class="color-primary" @click.stop="handleCheckOrder(scope.row,item)">{{ scope.row.booked_tickets }}</span> / <span class="color-primary" @click.stop="handleCheckOrder(scope.row,item)">{{ scope.row.remain_tickets===-1?'不限':scope.row.remain_tickets }}</span> /  <span class="color-primary" @click.stop="handleCheckOrder(scope.row,item)">{{ scope.row.total_tickets===-1?'不限':scope.row.total_tickets }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出发地" align="left" prop="start_name" min-width="90" show-overflow-tooltip>
        <template v-if="scope.row.line_class_id" slot-scope="scope">
          {{ scope.row.start_name||'未知' }}
        </template>dw
      </el-table-column>
      <el-table-column label="目的地" align="left" prop="end_name" min-width="90" show-overflow-tooltip>
        <template v-if="scope.row.line_class_id" slot-scope="scope">
          {{ scope.row.end_name||'未知' }}
        </template>
      </el-table-column>
      <el-table-column label="运行状态" align="left" prop="line_class_train_id" width="75">
        <template slot-scope="scope">
          {{
            {
              0:'已停运',
              1:'运行中',
            }[scope.row.status] || ''
          }}
        </template>
      </el-table-column>
      <!-- cn_schedule_status -->
      <el-table-column label="发班状态" align="left" prop="line_class_train_id" width="75">
        <template slot-scope="scope">
          {{ scope.row.cn_schedule_status }}
        </template>
      </el-table-column>
      <el-table-column label="班次编号" align="left" prop="line_class_train_id" width="75">
        <template slot-scope="scope">
          {{ scope.row.line_class_train_id }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="80">
        <template v-if="scope.row.line_class_train_id" slot-scope="scope">
          <div class="table-action">
            <el-tag effect="dark" :type="scope.row.status===1 ? 'danger' : 'success'" size="mini" @click="handleUpdateStatus(scope.row)">{{ scope.row.status===1 ? '停运' : '开班' }}</el-tag>
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <span style="font-size: 12px;">更多</span>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="toOldSystem(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">路线</el-tag>
                </el-dropdown-item>
                <el-dropdown-item @click.native="toOldSystem(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">站点</el-tag>
                </el-dropdown-item>
                <el-dropdown-item @click.native="toOldSystem(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">区域</el-tag>
                </el-dropdown-item>
                <el-dropdown-item @click.native="openPricingDialog(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">调价</el-tag>
                </el-dropdown-item>
                <el-dropdown-item @click.native="toOldSystemLineList(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">票价</el-tag>
                </el-dropdown-item>
                <el-dropdown-item @click.native="toAppointment(scope.row)">
                  <el-tag effect="dark" type="primary" size="mini">留座</el-tag>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="更换车辆" :visible.sync="dialogVehicles" width="420px">
      <el-select v-model="car_tail_number" style="width: 100%">
        <el-option v-for="item in vehicles" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" plain @click="dialogVehicles = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleUpdateVehicles">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="修改时间" :visible.sync="timeDialog" width="420px">
      <el-time-picker
        v-show="currentLineClass && currentLineClass.start_time_type===1"
        v-model="timeinfo.start_time"
        arrow-control
        value-format="HH:mm:ss"
        placeholder="发车时间"
      />
      <el-time-picker
        v-show="currentLineClass && currentLineClass.start_time_type===2"
        v-model="timeinfo.time"
        arrow-control
        value-format="HH:mm:ss"
        is-range
        range-separator="至"
        start-placeholder="最早时间"
        end-placeholder="最晚时间"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" plain @click="timeDialog = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleUpdateTime">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="调价" :visible.sync="pricingDialog" :width="(currentLineClass && currentLineClass.is_seat_selection) ? '680px' : '420px'">
      <el-form v-if="!currentLineClass || !currentLineClass.is_seat_selection" ref="dataForm" :model="priceInfo" :rules="rules" :inline="true" label-position="left" label-width="90px" size="small">
        <el-form-item label="销售" prop="price">
          <el-input v-model="priceInfo.price" />
        </el-form-item>
        <el-form-item label="渠道价格" prop="channel_price">
          <el-input v-model="priceInfo.channel_price" />
        </el-form-item>
      </el-form>
      <el-form v-else ref="dataForm" :model="priceInfo" :rules="rules" :inline="true" label-position="left" label-width="90px" size="small">
        <el-form-item v-for="item in seatInfo" :key="item.seat_id" :label="item.name" prop="price">
          <el-input v-if="item.seat_id!==1" v-model="item.price" />
          <el-input v-else disabled value="司机端" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" plain @click="pricingDialog = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleUpdatePricing">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getVehicles, updateVehicles, updateTimings, updatePricing } from '@/api/business'
export default {
  props: {
    direction: {
      type: String,
      default: 'forward'
    },
    listQuery: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    const validatePrice = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入价格'))
        return
      }
      if (!/^\d+(\.\d{1,2})?$/.test(value)) {
        callback(new Error('请输入数字，且最多两位小数'))
        return
      }
      callback()
    }
    return {
      pricingDialog: false,
      dialogVehicles: false,
      car_tail_number: '',
      vehicles: [],
      rowTitle: '',
      openSort: null,
      currentLineClassId: null,
      currentDriverId: null,
      currentLineClass: null,
      timeDialog: false,
      timeinfo: {
        start_time: '',
        time: ['', '']
      },
      priceInfo: {
        price: '0',
        channel_price: '0'
      },
      seatInfo: [{ seat_id: 1, name: '司机位', price: '0', optional: 0 }, { seat_id: 2, name: '一排中', price: '0.1', optional: 0 }],
      rules: {
        price: [{ required: true, message: '请输入价格', trigger: 'blur' }, { validator: validatePrice, trigger: 'blur' }],
        channel_price: [{ required: true, message: '请输入渠道价格', trigger: 'blur' }, { validator: validatePrice, trigger: 'blur' }]
      }
    }
  },
  watch: {
    listQuery: {
      handler(val, old) {
        if (val.start_city_name && val.end_city_name) {
          if ((this.direction === 'backward' && val.start_city_name !== val.end_city_name && !val.line_class_id) || this.direction === 'forward') {
            const title = this.direction === 'forward' ? '往：' : '返：'
            const text = this.direction === 'forward' ? (val.start_city_name + ' → ' + val.end_city_name) : (val.end_city_name + ' → ' + val.start_city_name)
            this.rowTitle = title + text
          } else {
            this.rowTitle = '-'
          }
        } else {
          this.rowTitle = '-'
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    getVehicles().then(res => {
      this.vehicles = res.data.map(o => ({
        value: o.id,
        label: `${o.license_plate_number} - ${o.series_name} · ${o.vehicle_seats}座`
      }))
    })
  },
  methods: {
    openTimeDialog(row) {
      this.timeDialog = true
      this.currentLineClass = row
      this.timeinfo.start_time = row.start_time
      this.timeinfo.time = [row.start_earliest_time, row.end_latest_time]
      console.log(this.timeinfo)
    },
    handleUpdateTime() {
      const params = {
        line_class_train_id: this.currentLineClass.line_class_train_id
      }
      if (this.currentLineClass && this.currentLineClass.start_time_type === 1) {
        params.start_time = this.timeinfo.start_time
      } else {
        params.start_earliest_time = this.timeinfo.time[0] || ''
        params.end_latest_time = this.timeinfo.time[1] || ''
      }
      updateTimings(params).then(res => {
        this.$message.success('修改时间成功')
        this.timeDialog = false
        this.$emit('refresh')
      })
    },
    openPricingDialog(row) {
      this.pricingDialog = true
      this.currentLineClass = row
      if (row.is_seat_selection) {
        this.seatInfo = row.seat_price
      } else {
        this.priceInfo.price = row.price
        this.priceInfo.channel_price = row.channel_price
      }
    },
    async handleUpdatePricing() {
      if (!this.currentLineClass || !this.currentLineClass.is_seat_selection) {
        this.$refs.dataForm.validate(async (valid) => {
          if (!valid) {
            return
          }
          const params = {
            line_class_train_id: this.currentLineClass.line_class_train_id,
            price: this.priceInfo.price,
            channel_price: this.priceInfo.channel_price
          }
          await updatePricing(params)
          this.$message.success('修改价格成功')
          this.pricingDialog = false
          this.$emit('refresh')
        })
      } else {
        // 手动校验
        let valid = true
        this.seatInfo.forEach(item => {
          if (!item.price) {
            valid = false
          }
          if (!/^\d+(\.\d{1,2})?$/.test(item.price)) {
            valid = false
          }
        })
        if (!valid) {
          this.$message.error('请确保每一个座位设置正确的价格')
          return
        }
        const params = {
          line_class_train_id: this.currentLineClass.line_class_train_id,
          seat_price: this.seatInfo.map(v => ({
            seat_id: v.seat_id,
            price: v.price
          }))
        }
        await updatePricing(params)
        this.$message.success('修改价格成功')
        this.pricingDialog = false
        this.$emit('refresh')
      }
    },
    toOldSystem(row) {
      window.open(process.env.VUE_APP_BASE_HOST + `/line_class_edit?line_class_id=${row.line_class_id}`)
    },
    toOldSystemLineList(row) {
      window.open(process.env.VUE_APP_BASE_HOST + `/line_class_train_list?line_class_id=${row.line_class_id}&line_class_train_no=${row.line_class_train_no}`)
    },
    toAppointment(row) {
      this.$router.push({
        path: '/operationCenter/appointment',
        query: {
          train_id: row.line_class_train_id,
          day: row.start_date
        }
      })
    },
    async handleUpdateVehicles() {
      if (!this.currentLineClassId || !this.currentDriverId) {
        return
      }
      await updateVehicles({
        line_class_train_id: this.currentLineClassId,
        driver_id: this.currentDriverId,
        vehicle_id: this.car_tail_number
      })
      this.$message.success('更换车辆成功')
      this.dialogVehicles = false
      this.$emit('refresh')
    },
    handleCheckOrder(row, item) {
      if (row.line_class_train_no) {
        window.open(process.env.VUE_APP_BASE_HOST + `/line_class_train_sell_order_list?line_class_train_no=${row.line_class_train_no}`)
      }
    },
    handleChangeVehicles(row, item) {
      this.dialogVehicles = true
      this.car_tail_number = item.car_tail_number
      this.currentLineClassId = row.line_class_train_id
      this.currentDriverId = item.driver_id
    },
    sortChange() {

    },

    handleSetDriver(_, row, item) {
      if (_ === 'changeVehicles') {
        this.handleChangeVehicles(row, item)
      } else {
        this.$emit('handleSetDriver', _, row, item)
      }
    },
    handleUpdateStatus(row) {
      // 弹窗是否打开/关闭
      this.$confirm(`您确定要${row.status === 1 ? '停运' : '启用'}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('handleUpdateStatus', row)
      })
    },
    showPopover(driver) {
      this.openSort = driver && driver.pivot && driver.pivot.sort
    },
    handleCheckSort(row, driver) {
      this.$emit('handleCheckSort', row, driver, this.openSort)
    }

  }
}
</script>

<style lang="scss" scoped>
.el-table-time{
     ::v-deep {
        .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
            border-right:none !important;
        }
           td{
               height: 40px;
               line-height: 40px;
            }
             .cell{
                font-size: 12px;
                padding-right: 0;
                white-space: nowrap;
             }
     .el-table__cell {
        padding: 2px 0 !important;
        font-size: 12px;
     }
     thead tr th .cell{
        font-size: 12px;
     }
     .el-tag--mini{
        font-size: 11px;
     }
     }
}
.label-word{
    margin-bottom: 10px;
    font-size: 13px;
    color: #8B96A6;
    font-weight: 400;
}
.seat{
    font-size: 10px;
    color: #68a948;
    margin-left: 3px;
}
.driver-list{
    ::v-deep {
     .el-input-number--mini {
            width: 95px;
        }
         .el-checkbox-group{
        .el-checkbox{
            margin-bottom: 10px;
        }

         }
    }
}

.icon-edit{
  width: 16px;
  height: 16px;
  margin-right: 5px;
margin-bottom: -2px;
}
.cursor{
    cursor: pointer;
}
.edit-warp{
    display: flex;
    align-items: center;
    .el-button{
        margin-left: 8px;
    }
}

.line {
  text-align: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
  display: inline-block;
  &.mb-5{
    margin-bottom: 5px;
  }
}

.driver-line,
.table-action {
  .cell-label {
    font-size: 12px;
    color: #555;
  }
  .cell-name {
    margin: 0 3px;
}
.el-tag {
    font-size: 10px;
    margin-right: 5px;
    padding: 0 3px;
    height: 18px;
    line-height: 18px;
}
}
::v-deep{

.el-table__row{

    .el-collapse{
        border: none;
    }
    .el-collapse-item__arrow{
        display: block;
    }
    .el-collapse-item__header{
        height: auto;
        line-height: 25px;
        background-color:inherit;
        color:#555;
        border-bottom:none;
        font-weight:normal;
    }
    .el-collapse-item__wrap{
        background-color: inherit;
    }
  .el-collapse-item__arrow {
    width: 16px;
    margin-right: 0;
  }
  .el-icon-arrow-right:before {
    content: "";
    width: 12px;
    height: 12px;
    background: url('../../../../assets/down.svg') no-repeat;
    background-size: 100%;
    font-family: 'heiti';
    display: block;
    color: #0067e1;
  }
  .el-collapse-item__arrow.is-active {
    transform: none;
  }
  .el-collapse-item__arrow.is-active::before {
     content: "";
    width: 12px;
    height: 12px;
    background: url('../../../../assets/down.svg') no-repeat;
    transform: rotate(180deg);
    background-size: 100%;
    font-family: 'heiti';
    display: block;
    color: #0067e1;
  }
}
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-left {
  text-align: left;
}
.color-primary{
  color: #0067e1 !important;
  cursor: pointer;
}

.table-header {
  color: #464646;
  font-size: 14px;
  font-weight: bold;
  height: 40px;
  background-color: #f0f2f5;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: none;
}

::v-deep{
  .el-table__body-wrapper{
    height: auto;
    min-height: 200px;
    max-height: calc(100vh - 380px);
    overflow-y: auto;
  }
  .el-table__empty-block{
    min-height: 200px;
  }
  .el-table__header-wrapper {
    border-bottom: none;
  }
  .el-table::before {
    height: 0;
  }
}
</style>


<template>
  <div class="component-container">
    <div class="item-filter-warp-container">
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="item-filter-warp">
            <span class="col-label-key">所属分台</span>
            <BranchSelect
              v-model="listQuery.branch_id" placeholder="全部" width="180px" class="filter-item"
              @change="handleFilter"
            />
          </div>
        </el-col>
        <el-col :span="4">
          <div class="item-filter-warp">
            <span class="col-label-key">班次编号</span>
            <el-input v-model="listQuery.line_class_train_no" placeholder="请输入" size="mini" @keyup.enter.native="handleFilter" />
          </div>
        </el-col>
        <el-col :span="4">
          <div class="item-filter-warp">
            <span class="col-label-key">司机信息</span>

            <el-select v-model="listQuery.driver_ids" clearable size="mini" filterable placeholder="选择司机" @change="handleFilter">
              <el-option
                v-for="item in searchDrivers"
                :key="item.driver_id"
                :label="item.name+' · '+item.car_tail_number"
                :value="item.driver_id"
              />
            </el-select>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="item-filter-warp">
            <span class="col-label-key">线路编号</span>
            <el-input v-model="listQuery.line_class_id" placeholder="请输入" size="mini" @keyup.enter.native="handleFilter" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="item-filter-warp end-search-warp">
            <el-button class="filter-item" type="primary" size="mini" @click.stop="handleFilter">搜索</el-button>
            <el-button class="filter-item" size="mini" type="primary" plain @click.stop="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <line-select
            ref="lineSelect"
            :branch-id="listQuery.branch_id"
            :show-add-time-line="true"
            @change="handleLineChange"
            @exchange="exchange"
            @refresh="fetchData"
          />
        </el-col>
      </el-row>
    </div>
    <div class="item-filter-warp-container">
      <el-row :gutter="10">
        <el-col :span="24">
          <div class="item-filter-warp">
            <span class="col-label-key">日期选择</span>
            <div class="tag-selected-warp">
              <el-tag v-for="(o,i) in dayData" :key="i" :type="listQuery.start_date===o.date?'':'info'" size="small" :effect="listQuery.start_date===o.date?'dark':'plain'" @click="handleCheckItem('start_date',o.date)">{{ o.date | formatDate }} <strong>{{ o.count?('('+o.count+')'):'' }}</strong></el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <div class="item-filter-warp">
            <span class="col-label-key">派单方式</span>
            <div class="tag-selected-warp">
              <el-tag v-for="(mode,i) in dispatch_modes" :key="i" :type="listQuery.dispatch_mode===mode.value?'':'info'" size="small" :effect="listQuery.dispatch_mode===mode.value?'dark':'plain'" @click="handleCheckItem('dispatch_mode',mode.value)">{{ mode.label }}</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <div class="item-filter-warp">
            <span class="col-label-key">状态筛选</span>
            <div class="tag-selected-warp">
              <el-tag v-for="(mode,i) in status_modes" :key="i" :type="listQuery.schedule_status===mode.value?'':'info'" size="small" :effect="listQuery.schedule_status===mode.value?'dark':'plain'" @click="handleCheckItem('schedule_status',mode.value)">{{ mode.label }}</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="table-content">
      <div class="table-half-container left-table">
        <table-warp :list="dataList.new_left" :list-query="listQuery" direction="forward" :title="left_title" @handleCheckSort="handleCheckSort" @showPopover="showPopover" @handleSetDriver="handleSetDriver" @handleUpdateStatus="handleUpdateStatus" @refresh="fetchData" />
      </div>
      <div class="table-half-container right-table">
        <table-warp :list="dataList.new_right" :list-query="listQuery" direction="backward" :title="right_title" @handleCheckSort="handleCheckSort" @showPopover="showPopover" @handleSetDriver="handleSetDriver" @handleUpdateStatus="handleUpdateStatus" @refresh="fetchData" />
      </div>
    </div>

    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.per_page"
        :total="total"
        :page-sizes="[50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :visible.sync="dialogDriver"
      title="选择司机"
      append-to-body
      width="850px"
    >
      <template v-if="is_seat_selection">
        <p class="label-word">请选择你需要更换的司机及其排序（单选）：</p>
        <div class="driver-list">
          <div style="margin: 15px 0;" />
          <el-radio-group v-model="checkedRadioDriver">
            <template v-for="driver in drivers">
              <el-radio :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
              </el-radio>
            </template>
          </el-radio-group>
        </div>
      </template>
      <template v-else>
        <p class="label-word">请选择你需要更换的司机及其排序（可多选）：</p>
        <div class="driver-list">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-model="checkedDrivers" @change="handleCheckedCitiesChange">
            <template v-for="driver in drivers">
              <el-checkbox :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
              </el-checkbox>
            </template>
          </el-checkbox-group>
        </div>
      </template>

      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            plain
            size="small"
            @click="dialogDriver = false"
          >取消</el-button>
          <el-button
            type="primary"
            size="small"
            @click="comfirmCheckDriver"
          >确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogChangeTickets"
      title="增加/减少余票数"
      append-to-body
      width="500px"
    >
      <div class="item-abc-container">
        <div class="label-abc">当前班次总票数：{{ ticketsInfo.total_tickets === -1 ? '不限' : ticketsInfo.total_tickets }}</div>
        <div class="label-abc">当前班次已售票数：{{ ticketsInfo.booked_tickets }} （车票状态包含：出票中、出票成功）</div>
        <div class="label-abc">当前班次余票数：{{ ticketsInfo.remain_tickets === -1 ? '不限' : ticketsInfo.remain_tickets }}</div>

        <div class="label-box-container">
          <div>增加/减少的余票数：</div>
          <el-input-number v-model="currentAmount" size="medium" :min="-999" :max="999" label="请输入票数" />
        </div>
        <div class="label-box2">填写的数字正整数为增加票数,填写负整数为扣除票数</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" plain size="small" @click="dialogChangeTickets = false">取消</el-button>
          <el-button type="primary" size="small" @click="comfirmChangeTickets">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogAddTimeLine"
      title="临时增加班次"
      append-to-body
      width="850px"
    >
      <template v-if="is_seat_selection">
        <p class="label-word">请选择司机及其排序（单选）：</p>
        <div class="driver-list">
          <div style="margin: 15px 0;" />
          <el-radio-group v-model="checkedRadioDriver">
            <template v-for="driver in drivers">
              <el-radio :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
              </el-radio>
            </template>
          </el-radio-group>
        </div>
      </template>
      <template v-else>
        <p class="label-word">请选择司机及其排序（可多选）：</p>
        <div class="driver-list">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-model="checkedDrivers" @change="handleCheckedCitiesChange">
            <template v-for="driver in drivers">
              <el-checkbox :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
              </el-checkbox>
            </template>
          </el-checkbox-group>
        </div>
      </template>
      <div style="margin-top: 20px;">
        <p class="label-word">请选择日期</p>
        <el-date-picker
          v-model="addTimeLineForm.selected_date"
          type="date"
          placeholder="选择日期"
          :picker-options="{disabledDate(time) { return time.getTime() < Date.now() - 8.64e7; }}"
        />
      </div>
      <div style="margin-top: 20px;">
        <p v-if="addTimeLineInfo.start_time_type === 1" class="label-word">请选择出发时间</p>
        <p v-else class="label-word">请选择时间范围</p>
      </div>

      <el-time-picker
        v-show="addTimeLineInfo.start_time_type===1"
        v-model="addTimeLineForm.start_time"
        arrow-control
        value-format="HH:mm:ss"
        placeholder="发车时间"
      />
      <el-time-picker
        v-show="addTimeLineInfo.start_time_type===2"
        v-model="addTimeLineForm.time"
        arrow-control
        value-format="HH:mm:ss"
        is-range
        range-separator="至"
        start-placeholder="最早时间"
        end-placeholder="最晚时间"
      />

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" plain size="small" @click="dialogAddTimeLine = false">取消</el-button>
          <el-button type="primary" size="small" @click="handleAddTimeLine">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import { mapGetters } from 'vuex'
import TableWarp from './TableWarp.vue'
import { addTimeLine } from '@/api'
import BranchSelect from '@/components/BranchSelect'
import LineSelect from '@/views/components/lineSelect/index.vue'

import {
  schedulesBranches,
  schedulesDrivers,
  daily,
  timetables,
  schedulesCityCode,
  sortDrivers,
  putDrivers,
  delDrivers,
  schedulesList,
  updateLineClassTrainStatus,
  changeTickets
} from '@/api'
export default {
  components: {
    TableWarp,
    BranchSelect,
    LineSelect
  },

  filters: {
    leaveTime(row) {
      const day_ = row.stop_sell_number > 0 ? row.stop_sell_number + '天' : ''
      const hour_ = row.stop_sell_time.slice(0, 2) !== '00' ? row.stop_sell_time.slice(0, 2) + '小时' : ''
      const minute_ = row.stop_sell_time.slice(3, 5) !== '00' ? row.stop_sell_time.slice(2, 4) + '分钟' : ''
      return (day_ || hour_ || minute_) ? `发车前${day_}${hour_}${minute_}停售` : ''
    },
    formatDate(dateString) {
      const today = new Date()
      const targetDate = new Date(dateString)
      const diffTime = targetDate - today
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天 ' + targetDate.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
      } else if (diffDays === 1) {
        return '明天 ' + targetDate.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
      } else if (diffDays === 2) {
        return '后天 ' + targetDate.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
      } else {
        return targetDate.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }).replace(/\//g, '月').concat('日')
      }
    }
  },
  data() {
    return {
      currentAmount: 0,
      dialogChangeTickets: false,
      ticketsInfo: {},
      is_seat_selection: false,
      dataList: {},
      openSort: '',
      start_area: '',
      end_area: '',
      startCode: [],
      endCode: [],
      dispatch_modes: [
        { value: '', label: '全部' },
        { value: 1, label: '手动派单' },
        { value: 0, label: '自动派单[顺序]' },
        { value: 2, label: '自动派单[距离及顺序]' }
      ],
      status_modes: [
        { value: '', label: '全部' },
        { value: 'departed', label: '已发班' },
        { value: 'upcoming', label: '待发班' },
        { value: 'disabled', label: '已停班' }
      ],
      visible: false,
      dayData: [],
      timetables_list: [],
      dialogDriver: false,
      checkAll: false,
      checkedDrivers: [],
      checkedRadioDriver: '',
      drivers: [],
      isIndeterminate: true,
      open_line_class_train_id: '',
      searchDrivers: [],
      list: null,
      isLoading: false,
      total: 0,
      listQuery: {
        schedule_status: '',
        line_class_id: '',
        start_city_name: '',
        end_city_name: '',
        start_date: moment().format(
          'YYYY-MM-DD'
        ),
        page: 1,
        per_page: 50,
        dispatch_mode: '',
        time: '',
        branch_id: '',
        sort_order: '',
        start_address_code: '',
        end_address_code: ''
      },
      branchs: [],
      props1: {
        checkStrictly: true,
        expandTrigger: 'hover'
      },
      dialogAddTimeLine: false,
      addTimeLineInfo: {},
      addTimeLineForm: {
        start_time: '',
        time: ['', ''],
        selected_date: new Date()
      },
      left_title: '',
      right_title: ''
    }
  },
  computed: {
    ...mapGetters(['info'])
  },
  watch: {
    $route: {
      handler: function(route) {
        console.log(route.query)
        // this.listQuery.line = route.query.line_class_id
        // this.fetchData(true)
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    await this.fetchBranch()
    // await this.fetchDaily()
  },
  methods: {
    //     {
    //   "line_class_id": 101,
    //   "start_date": "2024-12-25",
    //   "start_time": "08:00",
    //   "start_earliest_time": "07:00",
    //   "end_latest_time": "09:00",
    //   "drivers": [
    //     {
    //       "driver_id": 1,
    //       "order": 0
    //     }
    //   ]
    // }
    handleLineChange({ line_class_id, time, start_address_code, end_address_code, start_city_name, end_city_name, start_city_code, end_city_code }) {
      this.listQuery.line_class_id = line_class_id
      this.listQuery.time = time
      this.listQuery.start_address_code = start_address_code
      this.listQuery.end_address_code = end_address_code
      this.listQuery.start_city_code = start_city_code
      this.listQuery.end_city_code = end_city_code
      this.listQuery.start_city_name = start_city_name
      this.listQuery.end_city_name = end_city_name
    },
    async handleAddTimeLine() {
      if (this.is_seat_selection) {
        if (!this.checkedRadioDriver) {
          this.$message.error('请选择司机')
          return
        }
      } else {
        if (!this.checkedDrivers.length) {
          this.$message.error('请选择司机')
          return
        }
      }

      if (!this.addTimeLineForm.selected_date) {
        this.$message.error('请选择日期')
        return
      }

      if (this.addTimeLineInfo.start_time_type === 1) {
        if (!this.addTimeLineForm.start_time) {
          this.$message.error('请选择出发时间')
          return
        }
      } else {
        if (!this.addTimeLineForm.time[0] || !this.addTimeLineForm.time[1]) {
          this.$message.error('请选择时间范围')
          return
        }
        if (this.addTimeLineForm.time[0] >= this.addTimeLineForm.time[1]) {
          this.$message.error('最早时间不能大于最晚时间')
          return
        }
      }

      let drivers = []
      if (this.is_seat_selection) {
        drivers = [{
          driver_id: this.checkedRadioDriver,
          order: this.drivers.find(d => d.driver_id === this.checkedRadioDriver) && this.drivers.find(d => d.driver_id === this.checkedRadioDriver).sort
        }]
      } else {
        drivers = this.checkedDrivers.map(o => ({
          driver_id: o,
          order: this.drivers.find(d => d.driver_id === o) && this.drivers.find(d => d.driver_id === o).sort
        }))
      }
      const params = {
        line_class_id: this.addTimeLineInfo.line_class_id,
        start_date: moment(this.addTimeLineForm.selected_date).format('YYYY-MM-DD'),
        drivers
      }
      if (this.addTimeLineInfo.start_time_type === 1) {
        params.start_time = this.addTimeLineForm.start_time
      } else {
        params.start_earliest_time = this.addTimeLineForm.time[0]
        params.end_latest_time = this.addTimeLineForm.time[1]
      }
      await addTimeLine(params)
      this.$message.success('操作成功')
      this.dialogAddTimeLine = false
      this.fetchData()
    },
    async handleAddLine(lineItem) {
      this.addTimeLineInfo = lineItem
      this.is_seat_selection = lineItem.is_seat_selection
      this.checkedDrivers = []
      this.addTimeLineForm = {
        start_time: '',
        time: ['', '']
      }
      await this.fetchDrivers(lineItem.branchid)
      this.dialogAddTimeLine = true
    },
    async comfirmChangeTickets() {
      await changeTickets({
        line_class_train_id: this.open_line_class_train_id,
        amount: this.currentAmount
      })
      this.$message.success('班次售票数已调整')
      this.dialogChangeTickets = false
      this.fetchData()
    },
    async handleUpdateStatus(row) {
      const status = row.status === 1 ? 'disabled' : 'enabled'
      await updateLineClassTrainStatus({ line_class_train_id: row.line_class_train_id, status })
      this.$message.success('操作成功')
      this.fetchData()
    },
    showPopover(driver) {
      this.openSort = driver && driver.sort
    },

    handleCheckSort(row, driver, sort) {
    //   this.$set(this.list[aindex].drivers[bindex], 'sort', this.openSort)
      sortDrivers({
        line_class_train_id: row.line_class_train_id,
        'drivers': JSON.stringify([
          {
            'id': driver.driver_id,
            'sort': sort
          }
        ])
      }).then(res => {
        this.$notify({
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        const ref = 'popoverRef_' + row.line_class_train_id + '_' + driver.driver_id
        this.$nextTick(() => {
          console.log(this.$refs)
          console.log(this.$refs.ref)
          console.log(this.$refs['popoverRef_' + row.line_class_train_id + '_' + driver.driver_id])
        })
        setTimeout(() => {
          console.log(ref)
          console.log(this.$refs.ref)
          if (this.$refs && this.$refs['popoverRef_' + row.line_class_train_id + '_' + driver.driver_id]) {
            this.$refs[ref].doClose()
          }
        }, 0)

        this.fetchData()
      })
    },

    handleCheckAllChange(val) {
      this.checkedDrivers = val ? this.drivers.map(o => o.driver_id) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.drivers.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.drivers.length
    },
    async handleSetDriver(_, params, driver) {
      //  line_class_train_id
      console.log('___', _)
      const { line_class_train_id, branchid = '', drivers } = params
      this.open_line_class_train_id = line_class_train_id
      if (_ === 'edit') {
        await this.fetchDrivers(branchid)
        this.is_seat_selection = params && params.is_seat_selection
        this.drivers = this.drivers.map(o => {
          const foundDriver = drivers.find((d) => d.driver_id === o.driver_id)
          o['sort'] = foundDriver && foundDriver.pivot && foundDriver.pivot.sort
          return o
        }).sort((a, b) => {
          if (a.sort && b.sort) {
            return a.sort - b.sort // 有值的按sort顺序从小到大
          } else if (a.sort) {
            return -1 // 有值的放前面
          } else if (b.sort) {
            return 1 // 有值的放前面
          } else {
            return 0 // 都没有值则不变
          }
        })
        if (params && params.is_seat_selection) {
          this.checkedRadioDriver = driver.driver_id
        } else {
          this.checkedDrivers = drivers.map(o => o.driver_id) || []
          this.checkAll = this.checkedDrivers.length === this.drivers.length
          this.isIndeterminate = this.checkedDrivers.length > 0 && this.checkedDrivers.length < this.drivers.length
        }
        this.dialogDriver = true
      } else if (_ === 'changeTickets') {
        this.dialogChangeTickets = true
        this.ticketsInfo = params
      } else {
        this.$confirm(
          `确认将该司机从班次中取消排班？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          delDrivers({
            line_class_train_id: this.open_line_class_train_id,
            drivers: JSON.stringify([driver.driver_id])
          }).then(() => {
            this.$notify({
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
            this.fetchData()
          })
        })
      }
    },
    comfirmCheckDriver() {
      console.log(this.drivers)
      let params = []
      if (!this.is_seat_selection) {
        if (!this.checkedDrivers.length) {
          this.$message({
            message: '至少需要选择一个司机',
            type: 'warning'
          })
          return
        }

        const arr = []
        this.drivers.forEach((o) => {
          if (this.checkedDrivers.includes(o.driver_id)) {
            arr.push(o)
          }
        })
        params = arr.map((o) => {
          return {
            id: o.driver_id,
            sort: o.sort
          }
        })
      } else {
        const arr = []
        this.drivers.forEach((o) => {
          if (this.checkedRadioDriver === o.driver_id) {
            arr.push(o)
          }
        })
        params = arr.map((o) => {
          return {
            id: o.driver_id,
            sort: o.sort
          }
        })
      }

      putDrivers({
        line_class_train_id: this.open_line_class_train_id,
        drivers: JSON.stringify(params)
      }).then((e) => {
        this.dialogDriver = false
        this.$notify({
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        this.fetchData()
      })
    },

    branchChange(e) {
      this.listQuery.start_city_name = ''
      this.listQuery.end_city_name = ''
      this.listQuery.start_address_code = ''
      this.listQuery.end_address_code = ''
      this.line_class_id = ''
      this.dispatch_mode = ''
      this.driver_ids = ''
      this.listQuery.time = ''
      this.fetchCode('origin')
      this.fetchDrivers(e, true)
    },

    handleCheckItem(key, val, a_val = '') {
      console.log(key, val, a_val)
      this.listQuery.time = a_val || ''
      this.$set(this.listQuery, key, val)
      if (key === 'line_class_id' || a_val) {
        this.dispatch_mode = ''
        this.listQuery.start_date = moment().format('YYYY-MM-DD')
        if (val) {
          const lineItem = this.timetables_list.find(o => o.line_class_id === val)
          this.left_title = '往：' + (lineItem && lineItem.start_name || '') + ' → ' + (lineItem && lineItem.end_name || '')
          this.right_title = '返：' + (lineItem && lineItem.end_name || '') + ' → ' + (lineItem && lineItem.start_name || '')
        }
        this.fetchData()
      }
      if (key === 'start_date') {
        this.dispatch_mode = ''
        this.listQuery.page = 1
        this.fetchData()
      }

      if (key === 'dispatch_mode') {
        this.listQuery.page = 1
        this.fetchData()
      }

      if (key === 'schedule_status') {
        this.listQuery.page = 1
        this.fetchData()
      }
    },

    exchange(item) {
      // 使用解构赋值和对象字面量简化交换逻辑
      [this.start_area, this.end_area] = [this.end_area, this.start_area]

      this.listQuery = {
        ...item,
        ...this.listQuery
      }

      console.log('交换后的listQuery:', this.listQuery)
    },
    async fetchCode(filter) {
      const { data } = await schedulesCityCode({
        filter,
        start_address_code: this.listQuery.start_address_code || ''
      })
      if (data && data.length > 0) {
        if (filter === 'origin') {
          this.startCode = this.formatStartCode(data)
          this.fetchCode('destination')
        } else {
          this.endCode = this.formatStartCode(data)
          this.fetchTimetables()
        }
      }
    },
    formatStartCode(e) {
      return e.map(o => ({
        value: o.city_id,
        label: o.city_name,
        children: (o && o.city_attributes) ? o.city_attributes.map(c => ({
          value: c.area_id,
          label: c.area_name
        })) : []
      }))
    },
    async sortChange(e) {
      const { prop, order } = e
      this.listQuery['sort_by'] = order ? prop : null
      this.listQuery['sort'] =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.listQuery.page = 1
      this.fetchData(true)
    },
    async fetchBranch() {
      const { data } = await schedulesBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
        if (this.info.group_id === 2) {
          this.branchs = [
            {
              label: '全部',
              value: ''
            },
            ...this.branchs
          ]
        }
        this.$set(this.listQuery, 'branch_id', this.branchs[0].value)
        this.fetchCode('origin')
        this.fetchDrivers(this.branchs[0].value, true)
      }
    },

    async fetchDrivers(branch_id = '', isSearch = false) {
      const res = await schedulesDrivers({
        branch: branch_id

      })
      if (isSearch) {
        this.searchDrivers = res && res.data || []
        return
      }

      this.drivers = res && res.data || []
    },

    async fetchData(loading = true) {
      if (loading) this.isLoading = true
      const query = Object.assign({}, this.listQuery)
      if (!this.listQuery.line_class_id) {
        this.left_title = this.listQuery.start_city_name + ' → ' + this.listQuery.end_city_name
        this.right_title = this.listQuery.end_city_name + ' → ' + this.listQuery.start_city_name
      }
      const { data } = await schedulesList(query)
      if (!this.listQuery.line_class_id && this.listQuery.start_city_name !== this.listQuery.end_city_name) {
        // console.log('groupByStartTimegroupByStartTime', this.groupByStartTime(data.data))
        const groupByStartTime = this.groupByStartTime(data.data)
        const dataList = { new_left: [], new_right: [] }
        groupByStartTime.forEach((e) => {
          if (!e.left.length) {
            dataList.new_left.push(...Array.from({ length: e.right.length }, () => ({})))
          } else {
            dataList.new_left.push(...e.left)
          }
          if (!e.right.length) {
            dataList.new_right.push(...Array.from({ length: e.left.length }, () => ({})))
          } else {
            dataList.new_right.push(...e.right)
          }
        })
        this.dataList = dataList
      } else {
        this.dataList = { new_left: data && data.data || [], new_right: null }
      }
      if (loading) this.isLoading = false
      this.total = data.meta.pagination.total
    },

    groupByStartTime(data) {
      const time_arr = Object.values(data.reduce((acc, item) => {
        const key = item.start_time_type === 2 ? (item.start_earliest_time + '-' + item.end_latest_time) : item.start_time
        if (!acc[key]) {
          acc[key] = []
        }
        acc[key].push(item)
        return acc
      }, {}))
      return time_arr.map((o) => {
        const n = { left: [], right: [] }
        for (const item of o) {
          // console.log(item)
          if (item.start_city_code === this.listQuery.start_city_code) {
            n['left'].push(item)
          } else {
            n['right'].push(item)
          }
        }
        return n
      })
    },
    async fetchDaily() {
      const res = await daily({
        start_address_code: this.listQuery.start_address_code,
        end_address_code: this.listQuery.end_address_code,
        line_class_train_no: this.listQuery.line_class_train_no,
        line_class_id: this.listQuery.line_class_id,
        driver_query: this.listQuery.driver_query,
        branch_id: this.listQuery.branch_id

      })
      this.dayData = res && res.data || []
      this.listQuery.start_date = res && res.data && res.data[0] && res.data[0].date || ''
      this.listQuery.page = 1
      this.fetchData()
    },

    async fetchTimetables() {
      const res = await timetables({
        start_address_code: this.listQuery.start_address_code,
        end_address_code: this.listQuery.end_address_code
      })
      this.timetables_list = res && res.data || []
      await this.fetchDaily()
    },
    reconstructData(data) {
      const result = []
      let i = 0

      while (i < data.length) {
        const current = { ...data[i] }
        if (i + 1 < data.length && current.start_city_code === data[i + 1].end_city_code) {
          current.return = data[i + 1]
          i += 2 // Skip the next item as it's already included in the current object
        } else {
          i += 1
        }
        result.push(current)
      }

      return result
    },

    // timetables
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleReset() {
      this.listQuery = {
        start_date: moment().format(
          'YYYY-MM-DD'),
        page: 1,
        start_city_name: '',
        end_city_name: '',
        per_page: 50,
        line_class_id: '',
        dispatch_mode: '',
        time: '',
        branch_id: '',
        sort_order: '',
        start_address_key_list: [],
        end_address_key_list: [],
        start_address_code: '',
        end_address_code: ''
      }
      this.fetchBranch()
      this.$refs.lineSelect.resetAll()
      this.$refs.orderMultiple.clearSort()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    }
  }
}
</script>
<style lang="scss" scoped>
.item-abc-container{
  display: flex;
  align-items: baseline;
  justify-content: start;
  flex-direction: column;
}
.label-box-container{
  display: flex;
  align-items: baseline;
  justify-content: start;
  font-size: 18px;
  font-weight: 500;
}
.label-abc{
  margin-right: 10px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.label-box{
  margin-bottom: 6px;
  margin-top: 20px;
  font-size: 14px;
}
.label-box2{
  margin-top: 6px;
  font-size: 14px;
}
.over-flow-txt{
  width: 190px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    margin-top: -3px;
}
.table-content{
    display: flex;
    height: calc(100vh - 280px);
    min-height: 400px;
    border: 1px solid #ebeef5;
    border-bottom: none;
}
.table-half-container {
    width: 50%;
    height: 100%;
    overflow: auto;
}
.left-table {
    // 移除右侧边框
}
.label-word{
    margin-bottom: 10px;
    font-size: 13px;
    color: #8B96A6;
    font-weight: 400;
}
.driver-list{
    ::v-deep {
     .el-input-number--mini {
            width: 95px;
        }
         .el-checkbox-group{
        .el-checkbox{
            margin-bottom: 6px;
        }

         }
         .el-radio-group{
        .el-radio{
            margin-bottom: 6px;
        }

         }
         .el-checkbox__label{
          font-size: 12px;
         }
         .el-radio__label{
          font-size: 12px;
         }
    }
}

.cursor{
    cursor: pointer;
}
.edit-warp{
    display: flex;
    align-items: center;
    .el-button{
        margin-left: 8px;
    }
}
.query-container {
  align-items: center;
}

.line-label {
  display: inline-block;
  width: 46px;

  &::after {
    content: ":";
    position: relative;
    top: -0.5px;
    margin: 0 6px 0 2px;
  }
}

.form-query {
  flex: 1;
  flex-wrap: wrap;
}

.search-warp {
  margin-left: 25%;
  justify-content: flex-end;
}

.line {
  text-align: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}

.driver-line{
.cell-label{
font-size: 14px;
color: #555;
}
.el-tag {
    font-size: 10px;
    margin-left: 10px;
}
}

.item-filter {
  display: flex;
  align-items: center;
  line-height: 44px;
  margin-right: 12px;
  flex: 0 0 20%;
  max-width: 20%;

  .label-key {
    flex: 0 0 75px;
    text-align: right;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    font-size: 13px;

    &::after {
      content: ":";
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
    }
  }
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-left {
  text-align: left;
}

.item-filter-warp-container{
    background: #f0f2f5;
    border-radius:6px;
    padding: 8px 0;
    margin-bottom: 10px;
}
.search-city {
  display: flex;
  position: relative;
  align-items: center;
  .icon-change {
    cursor: pointer;
    width: 24px;
    height: 24px;
    min-width: 24px;
    margin: 0 12px;
    background: url("../../../../assets/icon-chage.png") no-repeat;
    background-size: cover;
  }
  .from-city,.to-city {
    width: 50%;
  }
}
.item-filter-warp{
    display: flex;
    align-items: flex-start;
    ::v-deep .el-tag{
        cursor: pointer;
    }
}
.end-search-warp{
justify-content: flex-end;
margin-right: 10px;
}
.col-label-key{
    flex: 0 0 75px;
    text-align: right;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    font-size: 13px;
    line-height: 30px;

    &::after {
      content: ":";
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
    }
}
.bottom-container {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-top: none;
}
</style>

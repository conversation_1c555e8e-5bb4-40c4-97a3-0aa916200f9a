<template>
  <div id="map-container" />
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader'
import { distanceGetTime } from '@/utils'
export default {
  props: {
    mapParams: {
      type: Object,
      default: () => { }
    },
    currentLine: {
      type: Object,
      default: () => null
    }
  },
  emits: ['priceChange'],
  data () {
    return {
      driving: null,
      smarker: null,
      emarker: null,
      mapPoints: {
        start_obj: null,
        end_obj: null
      },
      start_polygon: null,
      end_polygon: null,
      pickup_polygons: null,
      dropoff_polygons: null
    }
  },
  watch: {
    mapPoints: {
      handler (n) {
        console.log(n)
        if (!this.map) {
          return
        }
        const this_ = this
        this.map.clearMap()
        this.setPolygon()
        const { start_obj, end_obj } = n

        // 计算价格调整
        let totalPriceAdjustment = 0

        if (start_obj) {
          this.smarker = new AMap.Marker({
            map: this.map,
            position: [start_obj.lng, start_obj.lat],
            offset: new AMap.Pixel(-12, -40), // 相对于基点的偏移位置
            icon: new AMap.Icon({
              size: new AMap.Size(26, 41), // 图标大小
              image: require('@/assets/startmarker.png'),
              // imageOffset: new AMap.Pixel(8, 2), //图标相对于marker的偏移量
              imageSize: new AMap.Size(26, 41)
            })
          })
          this.map.setFitView(
            [this.smarker], // 覆盖物数组
            true, // 动画过渡到制定位置
            [120, 0, 60, 60], // 周围边距，上、下、左、右
            16
          )

          // 检查上车点是否在加价区域内
          const startPriceAdjustment = this.checkPointInPriceArea(start_obj, 'start')
          console.log('上车点加价:', startPriceAdjustment)
          totalPriceAdjustment += startPriceAdjustment
        }

        if (end_obj) {
          this.emarker = new AMap.Marker({
            map: this.map,
            position: [end_obj.lng, end_obj.lat],
            offset: new AMap.Pixel(-12, -40), // 相对于基点的偏移位置
            icon: new AMap.Icon({
              size: new AMap.Size(26, 41), // 图标大小
              image: require('@/assets/endmarker.png'),
              // imageOffset: new AMap.Pixel(8, 2), //图标相对于marker的偏移量
              imageSize: new AMap.Size(26, 41)
            })
          })

          // 检查下车点是否在加价区域内
          const endPriceAdjustment = this.checkPointInPriceArea(end_obj, 'end')
          console.log('下车点加价:', endPriceAdjustment)
          totalPriceAdjustment += endPriceAdjustment
        }

        // 触发价格变更事件，同时传递是哪个点导致的价格调整
        console.log('总价格调整:', totalPriceAdjustment)

        // 只有当价格有变化时才触发事件
        if (totalPriceAdjustment !== 0) {
          // 创建一个包含更详细信息的对象传递给父组件
          const priceChangeInfo = {
            total: totalPriceAdjustment,
            startAdjustment: start_obj ? this.checkPointInPriceArea(start_obj, 'start') : 0,
            endAdjustment: end_obj ? this.checkPointInPriceArea(end_obj, 'end') : 0
          }
          this.$emit('priceChange', priceChangeInfo)
        }

        if (start_obj && end_obj) {
          this.map.clearMap()
          this.setPolygon()
          if (!this.driving) {
            this.driving = new AMap.Driving({ map: this.map })
          }
          this.driving.search(
            new AMap.LngLat(start_obj.lng, start_obj.lat),
            new AMap.LngLat(end_obj.lng, end_obj.lat),
            function (status, result) {
              const { routes } = result
              if (!routes || routes.length === 0) {
                return
              }
              let distanceView = ''
              let info_content = ''
              const distance = routes[0].distance
              const timeView = distanceGetTime(routes[0].time)

              distanceView = (Math.round(distance / 100) / 10).toFixed(1)
              info_content = `<div>
              <p  class="infoMat">大约<span>${distanceView}</span>公里</p>
              <p class="infoMat">预计耗时<span>${timeView}</span> </p>
              </div>`
              var infoWindow = new AMap.InfoWindow({
                anchor: 'left',
                content: info_content,
                offset: new AMap.Pixel(30, -40)
              })

              infoWindow.open(this_.map, [start_obj.lng, start_obj.lat])
              // 未出错时，result即是对应的路线规划方案
            }
          )
          this.map.setFitView(
            [this_.smarker, this_.emarker], // 覆盖物数组
            true, // 动画过渡到制定位置
            [120, 0, 60, 60] // 周围边距，上、下、左、右
          )
        }
        if (!start_obj && !end_obj && this.driving) {
          this.driving.clear()
        }
      },
      immediate: true,
      deep: true
    },

    mapParams: {
      handler (n) {
        if (n) {
          const {
            customEndPoint,
            customStartPoint,
            start_address_type,
            end_address_type,
            start_point,
            end_point,
            start_polygon,
            end_polygon
          } = n
          // 统一位置坐标结构
          this.start_polygon = start_polygon
          this.end_polygon = end_polygon
          if (start_address_type && start_address_type === 2) {
            if (customStartPoint) {
              // 任意出发点
              const { lat, lng } = customStartPoint.location
              this.mapPoints.start_obj = { lat, lng }
            } else {
              this.mapPoints.start_obj = null
            }
          }
          if (start_address_type && start_address_type === 1) {
            if (start_point) {
              // 固定出发点
              const { longitude, latitude } = JSON.parse(start_point)
              this.mapPoints.start_obj = { lat: latitude, lng: longitude }
            } else {
              this.mapPoints.start_obj = null
            }
          }

          if (end_address_type && end_address_type === 2) {
            if (customEndPoint) {
              // 任意出发点
              const { lat, lng } = customEndPoint.location
              this.mapPoints.end_obj = { lat, lng }
            } else {
              this.mapPoints.end_obj = null
            }
          }
          if (end_address_type && end_address_type === 1) {
            if (end_point) {
              // 固定出发点
              const { longitude, latitude } = JSON.parse(end_point)
              this.mapPoints.end_obj = { lat: latitude, lng: longitude }
            } else {
              this.mapPoints.end_obj = null
            }
          }
        }
      },
      deep: true,
      immediate: true
    },
    currentLine: {
      handler(line) {
        if (line && this.map) {
          // 清除之前的标记
          this.map.clearMap()

          console.log('当前线路:', line)

          // 设置上下车范围多边形
          if (line.line_class) {
            console.log('线路类型信息:', {
              start_address_type: line.line_class.start_address_type, // 1-固定, 2-任意
              end_address_type: line.line_class.end_address_type,
              has_start_polygon: !!line.line_class.start_polygon,
              has_end_polygon: !!line.line_class.end_polygon,
              has_pickup_polygons: !!line.line_class.pickup_polygons,
              has_pickup_surcharge_areas: !!line.line_class.pickup_surcharge_areas,
              has_dropoff_polygons: !!line.line_class.dropoff_polygons,
              has_dropoff_surcharge_areas: !!line.line_class.dropoff_surcharge_areas
            })

            // 根据上车点类型决定展示方式
            if (line.line_class.start_address_type === 1) { // 固定上车点
              console.log('固定上车点:', line.line_class.fixed_boarding_point)
              if (line.line_class.fixed_boarding_point && line.line_class.fixed_boarding_point.length > 0) {
                this.renderFixedPoints(line.line_class.fixed_boarding_point, true)
              }
            } else if (line.line_class.start_address_type === 2) { // 任意上车点
              // 设置上车点多边形区域
              this.start_polygon = line.line_class.start_polygon
              console.log('任意上车点多边形:', this.start_polygon)
            }

            // 根据下车点类型决定展示方式
            if (line.line_class.end_address_type === 1) { // 固定下车点
              console.log('固定下车点:', line.line_class.fixed_dropoff_point)
              if (line.line_class.fixed_dropoff_point && line.line_class.fixed_dropoff_point.length > 0) {
                this.renderFixedPoints(line.line_class.fixed_dropoff_point, false)
              }
            } else if (line.line_class.end_address_type === 2) { // 任意下车点
              // 设置下车点多边形区域
              this.end_polygon = line.line_class.end_polygon
              console.log('任意下车点多边形:', this.end_polygon)
            }

            // 设置加价区域多边形（直接获取所有可能的字段）
            const pickup = line.line_class.pickup_polygons || line.line_class.pickup_surcharge_areas || []
            const dropoff = line.line_class.dropoff_polygons || line.line_class.dropoff_surcharge_areas || []

            // 打印详细的原始数据
            console.log('上车加价区域原始数据类型:', typeof pickup)
            console.log('上车加价区域原始数据:', JSON.stringify(pickup, null, 2))
            console.log('下车加价区域原始数据类型:', typeof dropoff)
            console.log('下车加价区域原始数据:', JSON.stringify(dropoff, null, 2))

            // 直接将原始数据提供给渲染函数
            this.pickup_polygons = pickup
            this.dropoff_polygons = dropoff

            // 渲染多边形
            this.setPolygon()

            // 根据多边形自适应地图视野
            this.map.setFitView()
          }
        }
      },
      immediate: true
    }
  },
  created () {
    // DOM初始化完成进行地图初始化
    this.initMap()
  },
  methods: {
    setPolygon () {
      console.log('开始绘制多边形')

      // 渲染上车点基础范围
      if (this.start_polygon) {
        console.log('绘制上车点多边形:', this.start_polygon)
        try {
          let start_polygons
          if (typeof this.start_polygon === 'string') {
            try {
              start_polygons = JSON.parse(this.start_polygon).map(o => o.split(','))
            } catch (e) {
              start_polygons = this.start_polygon.split('|').map(o => o.split(','))
            }
          } else if (Array.isArray(this.start_polygon)) {
            start_polygons = this.start_polygon.map(o =>
              typeof o === 'string' ? o.split(',') : o
            )
          }

          console.log('处理后的上车点多边形坐标:', start_polygons)

          if (start_polygons && start_polygons.length > 0) {
            const polygonStart = new AMap.Polygon({
              path: start_polygons,
              fillColor: '#1c73e2',
              strokeOpacity: 1,
              fillOpacity: 0.3,
              strokeColor: '#2b8cbe',
              strokeWeight: 1,
              strokeStyle: 'dashed',
              strokeDasharray: [5, 5]
            })
            this.map.add(polygonStart)
            console.log('已添加上车点多边形')
          }
        } catch (error) {
          console.error('上车点多边形绘制失败:', error)
        }
      }

      // 渲染下车点基础范围
      if (this.end_polygon) {
        console.log('绘制下车点多边形:', this.end_polygon)
        try {
          let end_polygons
          if (typeof this.end_polygon === 'string') {
            try {
              end_polygons = JSON.parse(this.end_polygon).map(o => o.split(','))
            } catch (e) {
              end_polygons = this.end_polygon.split('|').map(o => o.split(','))
            }
          } else if (Array.isArray(this.end_polygon)) {
            end_polygons = this.end_polygon.map(o =>
              typeof o === 'string' ? o.split(',') : o
            )
          }

          console.log('处理后的下车点多边形坐标:', end_polygons)

          if (end_polygons && end_polygons.length > 0) {
            const polygonEnd = new AMap.Polygon({
              path: end_polygons,
              fillColor: '#e90000',
              strokeOpacity: 1,
              fillOpacity: 0.3,
              strokeColor: '#e90000',
              strokeWeight: 1,
              strokeStyle: 'dashed',
              strokeDasharray: [5, 5]
            })
            this.map.add(polygonEnd)
            console.log('已添加下车点多边形')
          }
        } catch (error) {
          console.error('下车点多边形绘制失败:', error)
        }
      }

      // 渲染上车点加价区域多边形
      if (this.pickup_polygons) {
        console.log('上车加价区域类型:', typeof this.pickup_polygons)
        console.log('上车加价区域详情:', this.pickup_polygons)

        try {
          // 处理不同格式的pickup_polygons
          if (Array.isArray(this.pickup_polygons)) {
            // 如果是数组，直接遍历
            this.pickup_polygons.forEach((polygon, index) => {
              console.log(`处理上车加价区域[${index}]:`, polygon)
              if (polygon) {
                this.renderPricePolygon(polygon, '#4CAF50')
              }
            })
          } else if (this.pickup_polygons && typeof this.pickup_polygons === 'object' && !Array.isArray(this.pickup_polygons)) {
            // 如果是对象，处理对象格式
            Object.keys(this.pickup_polygons).forEach(key => {
              console.log(`处理上车加价区域[${key}]:`, this.pickup_polygons[key])
              const polygon = this.pickup_polygons[key]
              if (polygon) {
                this.renderPricePolygon(polygon, '#4CAF50')
              }
            })
          } else if (typeof this.pickup_polygons === 'string') {
            // 如果是字符串，尝试解析
            try {
              const parsed = JSON.parse(this.pickup_polygons)
              console.log('解析后的上车加价区域:', parsed)
              if (Array.isArray(parsed)) {
                parsed.forEach((polygon, index) => {
                  console.log(`处理解析后的上车加价区域[${index}]:`, polygon)
                  if (polygon) {
                    this.renderPricePolygon(polygon, '#4CAF50')
                  }
                })
              } else if (parsed && typeof parsed === 'object') {
                this.renderPricePolygon(parsed, '#4CAF50')
              }
            } catch (e) {
              console.error('解析上车加价区域失败:', e)
            }
          }
        } catch (error) {
          console.error('处理上车加价区域出错:', error)
        }
      }

      // 渲染下车点加价区域多边形
      if (this.dropoff_polygons) {
        console.log('下车加价区域类型:', typeof this.dropoff_polygons)
        console.log('下车加价区域详情:', this.dropoff_polygons)

        try {
          // 处理不同格式的dropoff_polygons
          if (Array.isArray(this.dropoff_polygons)) {
            // 如果是数组，直接遍历
            this.dropoff_polygons.forEach((polygon, index) => {
              console.log(`处理下车加价区域[${index}]:`, polygon)
              if (polygon) {
                this.renderPricePolygon(polygon, '#FF9800')
              }
            })
          } else if (this.dropoff_polygons && typeof this.dropoff_polygons === 'object' && !Array.isArray(this.dropoff_polygons)) {
            // 如果是对象，处理对象格式
            Object.keys(this.dropoff_polygons).forEach(key => {
              console.log(`处理下车加价区域[${key}]:`, this.dropoff_polygons[key])
              const polygon = this.dropoff_polygons[key]
              if (polygon) {
                this.renderPricePolygon(polygon, '#FF9800')
              }
            })
          } else if (typeof this.dropoff_polygons === 'string') {
            // 如果是字符串，尝试解析
            try {
              const parsed = JSON.parse(this.dropoff_polygons)
              console.log('解析后的下车加价区域:', parsed)
              if (Array.isArray(parsed)) {
                parsed.forEach((polygon, index) => {
                  console.log(`处理解析后的下车加价区域[${index}]:`, polygon)
                  if (polygon) {
                    this.renderPricePolygon(polygon, '#FF9800')
                  }
                })
              } else if (parsed && typeof parsed === 'object') {
                this.renderPricePolygon(parsed, '#FF9800')
              }
            } catch (e) {
              console.error('解析下车加价区域失败:', e)
            }
          }
        } catch (error) {
          console.error('处理下车加价区域出错:', error)
        }
      }

      // 自适应地图视野
      this.map.setFitView()
    },

    // 渲染固定上下车点
    renderFixedPoints(points, isPickup) {
      if (!Array.isArray(points) || points.length === 0) return

      points.forEach(point => {
        if (!point.latitude || !point.longitude) return

        // 创建标记
        const marker = new AMap.Marker({
          position: [point.longitude, point.latitude],
          offset: new AMap.Pixel(-13, -30),
          content: `<div class="station-marker ${isPickup ? 'pickup' : 'dropoff'}">
                      <div class="marker-icon"></div>
                      <div class="marker-label">${point.alias || (isPickup ? '上车点' : '下车点')}</div>
                    </div>`
        })

        this.map.add(marker)
      })
    },

    // 渲染价格区域多边形
    renderPricePolygon(polygon, color) {
      try {
        console.log('开始渲染价格区域多边形:', polygon)

        // 确保polygon.polygon是数组，兼容多种属性名
        let polygonData = polygon.polygon || polygon.path || polygon.points || polygon.polygons

        // 处理价格信息，确保取绝对值
        const priceValue = parseFloat(polygon.price || polygon.surcharge || '0')
        const price = Math.abs(priceValue).toString() // 取绝对值，确保显示正数

        console.log('价格区域多边形数据类型:', typeof polygonData)
        console.log('价格区域多边形数据:', polygonData)
        console.log('价格信息:', price)

        if (!polygonData) {
          console.warn('没有找到多边形数据')
          return
        }

        // 处理字符串格式的多边形数据
        if (typeof polygonData === 'string') {
          let parsedData
          try {
            // 尝试作为JSON解析
            parsedData = JSON.parse(polygonData)
            console.log('解析后的JSON数据:', parsedData)
          } catch (e) {
            // 非JSON格式，进行字符串分割处理

            // 检查是否使用连字符"-"分隔点
            if (polygonData.includes('-')) {
              console.log('发现连字符分隔的多边形数据')
              // 处理用连字符"-"分隔点的格式
              parsedData = polygonData.split('-').map(point => point.split(','))
              console.log('按连字符分隔后的数据:', parsedData)
            } else if (polygonData.includes('|')) {
              parsedData = polygonData.split('|').map(point => point.split(','))
            } else if (polygonData.includes(';')) {
              parsedData = polygonData.split(';').map(point => point.split(','))
            } else {
              // 可能是单个坐标字符串，尝试直接分割
              parsedData = [polygonData.split(',')]
            }
            console.log('按分隔符解析后的数据:', parsedData)
          }
          polygonData = parsedData
        }

        // 根据polygon数据类型进行处理
        let paths = []

        if (Array.isArray(polygonData)) {
          // 处理多种可能的数组结构
          if (polygonData.length > 0) {
            if (typeof polygonData[0] === 'string') {
              // 如果是字符串数组，每个字符串可能是坐标
              paths = polygonData.map(point => {
                if (typeof point === 'string') {
                  return point.split(',').map(coord => parseFloat(coord))
                }
                return null
              }).filter(Boolean)
            } else if (Array.isArray(polygonData[0])) {
              // 如果已经是坐标数组
              paths = polygonData
            } else if (typeof polygonData[0] === 'object') {
              // 如果是对象数组，可能每个对象包含lng/lat
              paths = polygonData.map(point => {
                if (point.lng && point.lat) {
                  return [parseFloat(point.lng), parseFloat(point.lat)]
                } else if (point.longitude && point.latitude) {
                  return [parseFloat(point.longitude), parseFloat(point.latitude)]
                }
                return null
              }).filter(Boolean)
            }
          }
        } else if (typeof polygonData === 'object') {
          // 如果是单个对象，可能包含坐标数组
          if (polygonData.coordinates && Array.isArray(polygonData.coordinates)) {
            paths = polygonData.coordinates
          } else if (polygonData.points && Array.isArray(polygonData.points)) {
            paths = polygonData.points
          }
        }

        console.log('处理后的路径数据:', paths)

        if (paths.length > 0) {
          // 确保所有点都是有效坐标
          const validPaths = paths.filter(point => {
            return Array.isArray(point) && point.length >= 2 &&
                   !isNaN(parseFloat(point[0])) && !isNaN(parseFloat(point[1]))
          })

          console.log('有效的路径点数量:', validPaths.length)
          console.log('有效的路径点:', validPaths)

          if (validPaths.length > 2) { // 至少需要3个点才能形成多边形
            // 将所有坐标转换为数字
            const numericPaths = validPaths.map(point =>
              point.map(coord => typeof coord === 'string' ? parseFloat(coord) : coord)
            )

            console.log('数值化后的路径点:', numericPaths)

            const pricePolygon = new AMap.Polygon({
              path: numericPaths,
              fillColor: color,
              strokeOpacity: 0.8,
              fillOpacity: 0.4,
              strokeColor: color,
              strokeWeight: 2,
              strokeStyle: 'solid'
            })

            // 创建价格标记 - 不管价格正负都显示
            if (priceValue !== 0 || polygon.title) {
              const center = this.getPolygonCenter(numericPaths)
              console.log('价格标记位置:', center)

              // 负数表示优惠，正数表示加价
              const pricePrefix = priceValue < 0 ? '' : '+'
              const title = polygon.title ? `<div class="area-title">${polygon.title}</div>` : ''
              const priceInfo = priceValue !== 0 ? `<div class="price-value">${pricePrefix}${price}元/人</div>` : ''
              const priceContent = `<div class="price-bubble">
                  ${title}
                  ${priceInfo}
                </div>`

              const priceMarker = new AMap.Marker({
                position: center,
                offset: new AMap.Pixel(0, 0),
                content: priceContent,
                anchor: 'center'
              })

              this.map.add(priceMarker)
              console.log('添加了价格标记:', center, price)
            }

            this.map.add(pricePolygon)
            console.log('成功添加价格区域多边形')
          } else {
            console.warn('有效点数不足，无法形成多边形:', validPaths.length)
          }
        } else {
          console.warn('没有有效的路径点')
        }
      } catch (error) {
        console.error('渲染价格区域失败:', error)
      }
    },

    // 获取多边形中心点
    getPolygonCenter(path) {
      let lng = 0
      let lat = 0
      for (let i = 0; i < path.length; i++) {
        lng += parseFloat(path[i][0])
        lat += parseFloat(path[i][1])
      }
      return [lng / path.length, lat / path.length]
    },

    // 判断点是否在多边形内
    isPointInPolygon(point, polygon) {
      // 射线法判断点是否在多边形内
      let inside = false
      const x = point[0]
      const y = point[1]

      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = parseFloat(polygon[i][0])
        const yi = parseFloat(polygon[i][1])
        const xj = parseFloat(polygon[j][0])
        const yj = parseFloat(polygon[j][1])

        const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)
        if (intersect) inside = !inside
      }

      return inside
    },

    // 检查点是否在加价区域内并计算价格调整
    checkPointInPriceArea(point, pointType) {
      if (!point || !point.lng || !point.lat) return 0

      let priceAdjustment = 0
      const pointCoord = [point.lng, point.lat]

      // 只检查对应类型的加价区域
      if (pointType === 'start' && this.pickup_polygons) {
        // 上车点只检查上车加价区域
        this.processPolygons(this.pickup_polygons, pointCoord, (price) => {
          priceAdjustment += parseFloat(price)
        })
      } else if (pointType === 'end' && this.dropoff_polygons) {
        // 下车点只检查下车加价区域
        this.processPolygons(this.dropoff_polygons, pointCoord, (price) => {
          priceAdjustment += parseFloat(price)
        })
      }

      return priceAdjustment
    },

    // 处理多边形数据的通用方法
    processPolygons(polygons, point, callback) {
      try {
        // 处理数组格式
        if (Array.isArray(polygons)) {
          polygons.forEach(polygon => {
            if (!polygon) return

            const polygonData = polygon.polygon || polygon.path || polygon.points || polygon.polygons
            if (!polygonData) return

            const paths = this.parsePolygonData(polygonData)
            if (paths.length > 2 && this.isPointInPolygon(point, paths)) {
              const price = polygon.price || polygon.surcharge || 0
              callback(price)
            }
          })
        } else if (typeof polygons === 'object') {
          Object.keys(polygons).forEach(key => {
            const polygon = polygons[key]
            if (!polygon) return

            const polygonData = polygon.polygon || polygon.path || polygon.points || polygon.polygons
            if (!polygonData) return

            const paths = this.parsePolygonData(polygonData)
            if (paths.length > 2 && this.isPointInPolygon(point, paths)) {
              const price = polygon.price || polygon.surcharge || 0
              callback(price)
            }
          })
        } else if (typeof polygons === 'string') {
          try {
            const parsed = JSON.parse(polygons)
            this.processPolygons(parsed, point, callback)
          } catch (e) {
            console.error('解析多边形数据失败:', e)
          }
        }
      } catch (error) {
        console.error('处理多边形数据出错:', error)
      }
    },

    // 解析多边形数据
    parsePolygonData(polygonData) {
      let paths = []

      if (typeof polygonData === 'string') {
        try {
          // 尝试作为JSON解析
          paths = JSON.parse(polygonData)
        } catch (e) {
          // 非JSON格式，进行字符串分割处理
          if (polygonData.includes('-')) {
            paths = polygonData.split('-').map(point => point.split(','))
          } else if (polygonData.includes('|')) {
            paths = polygonData.split('|').map(point => point.split(','))
          } else if (polygonData.includes(';')) {
            paths = polygonData.split(';').map(point => point.split(','))
          } else {
            paths = [polygonData.split(',')]
          }
        }
      } else if (Array.isArray(polygonData)) {
        paths = polygonData.map(point => {
          if (typeof point === 'string') {
            return point.split(',').map(coord => parseFloat(coord))
          } else if (Array.isArray(point)) {
            return point
          } else if (typeof point === 'object' && point.lng && point.lat) {
            return [parseFloat(point.lng), parseFloat(point.lat)]
          } else if (typeof point === 'object' && point.longitude && point.latitude) {
            return [parseFloat(point.longitude), parseFloat(point.latitude)]
          }
          return null
        }).filter(Boolean)
      }

      return paths
    },
    initMap () {
      AMapLoader.load({
        key: 'ae07d204562de06e95f7ed805770b18f', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.Driving', 'AMap.InfoWindow'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          this.map = new AMap.Map('map-container', {
            // 设置地图容器id
            viewMode: '3D', // 是否为3D地图模式
            zoom: 5, // 初始化地图级别
            center: [105.602725, 37.076636] // 初始化地图中心点位置
          })
        })
        .catch((e) => {
          console.log(e)
        })
    }
  }
}
</script>

<style scoped lang="scss">
  #map-container {
    padding: 0px;
    width: 100%;
    height: 270px;
  }
</style>
<style lang="scss">
  .infoMat {
    font-size: 14px;
    span {
      color: #0067e1;
      margin: 0 3px;
    }
  }
  .amap-info-close {
    display: none;
  }

  /* 价格气泡样式 */
  .price-bubble {
    background-color: #fff;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-align: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    border: 1px solid #f56c6c;
    white-space: nowrap;
  }

  .price-bubble .area-title {
    color: #333;
    font-weight: bold;
    margin-bottom: 2px;
  }

  .price-bubble .price-value {
    color: #f56c6c;
    font-weight: bold;
  }

  /* 站点标记样式 */
  .station-marker {
    position: relative;
    display: inline-block;
    text-align: center;

    .marker-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin: 0 auto;
    }

    .marker-label {
      background-color: rgba(255, 255, 255, 0.9);
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 12px;
      margin-top: 3px;
      white-space: nowrap;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    }

    &.pickup {
      .marker-icon {
        background-color: #1c73e2;
        border: 2px solid white;
      }

      .marker-label {
        color: #1c73e2;
        border: 1px solid #1c73e2;
      }
    }

    &.dropoff {
      .marker-icon {
        background-color: #e90000;
        border: 2px solid white;
      }

      .marker-label {
        color: #e90000;
        border: 1px solid #e90000;
      }
    }
  }

  .price-label {
    background-color: rgba(255, 255, 255, 0.8);
    color: #f56c6c;
    font-weight: bold;
    padding: 2px 5px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  }
</style>

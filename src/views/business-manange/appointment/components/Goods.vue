<template>
  <div id="business-container" class="express-delivery-container">
    <el-form ref="dataForm" :model="form" :rules="rules" label-position="left" label-width="100px" size="small">
      <div class="express-form-wrapper">
        <!-- 寄件信息 -->
        <div class="info-section sender-info">
          <div class="section-header">寄件信息</div>
          <div class="form-content">
            <el-form-item label="详细地址" prop="senderAddress">
              <el-autocomplete
                v-model="form.senderAddress"
                :fetch-suggestions="querySenderAddress"
                placeholder="请输入寄件详细地址"
                style="width: 100%"
                @select="handleSenderAddressSelect"
              >
                <template slot-scope="{ item }">
                  <div>{{ item.name }}<span style="color: #909399; font-size: 12px; margin-left: 8px;">{{ item.district }}</span></div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="姓名" prop="senderName">
              <el-input v-model="form.senderName" placeholder="请输入寄件人姓名" />
            </el-form-item>
            <el-form-item label="联系电话" prop="senderPhone">
              <el-input v-model="form.senderPhone" placeholder="请输入寄件人联系电话" />
            </el-form-item>
          </div>
        </div>

        <!-- 收件信息 -->
        <div class="info-section receiver-info">
          <div class="section-header">收件信息</div>
          <div class="form-content">
            <el-form-item label="详细地址" prop="receiverAddress">
              <el-autocomplete
                v-model="form.receiverAddress"
                :fetch-suggestions="queryReceiverAddress"
                placeholder="请输入收件详细地址"
                style="width: 100%"
                @select="handleReceiverAddressSelect"
              >
                <template slot-scope="{ item }">
                  <div>{{ item.name }}<span style="color: #909399; font-size: 12px; margin-left: 8px;">{{ item.district }}</span></div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="姓名" prop="receiverName">
              <el-input v-model="form.receiverName" placeholder="请输入收件人姓名" />
            </el-form-item>
            <el-form-item label="联系电话" prop="receiverPhone">
              <el-input v-model="form.receiverPhone" placeholder="请输入收件人联系电话" />
            </el-form-item>
          </div>
        </div>

        <!-- 包裹信息 -->
        <div class="info-section package-info">
          <div class="section-header">包裹信息</div>
          <div class="form-content">
            <el-form-item label="总重量(kg)" prop="weight">
              <div class="weight-control">
                <el-input-number v-model="form.weight" size="medium" :min="1" :max="100" :step="1" :precision="0" placeholder="请输入重量" @change="calculateShippingCost" />
              </div>
            </el-form-item>
            <el-form-item label="备注信息" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
              <div class="quick-tags">
                <el-tag
                  v-for="item in quickTags"
                  :key="item"
                  type="info"
                  size="small"
                  effect="plain"
                  @click="handleTag(item)"
                >
                  {{ item }}
                </el-tag>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 费用信息 -->
        <div class="info-section cost-info">
          <div class="section-header">费用信息</div>
          <div class="form-content">
            <el-form-item label="带货时间" prop="startTime">
              <el-radio-group v-model="startTimeType">
                <el-radio label="1">立即带货</el-radio>
                <el-radio label="2">预约带货</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="startTimeType === '2'">
              <el-form-item label="预约时间" prop="appointmentTime">
                <el-date-picker
                  v-model="form.appointmentTime"
                  type="datetime"
                  placeholder="请选择预约时间"
                  style="width: 220px;"
                />
              </el-form-item>
            </div>
            <el-form-item label="运费价格" prop="price">
              <div class="price-control">
                <el-input-number
                  v-model="form.price"
                  size="medium"
                  :min="0.00"
                  :precision="2"
                  placeholder="请输入运费价格"
                  controls-position="right"
                />
                <span class="price-unit">元</span>
              </div>
            </el-form-item>
          </div>
          <div class="form-cost">
            <div class="cost-item">
              <span class="cost-label">运费价格</span>
              <span class="cost-value">￥{{ form.price }}</span>
            </div>
          </div>
          <!-- 提交按钮 -->
          <div class="submit-section">
            <el-button type="primary" :loading="submitting" @click="submitOrder">代下单</el-button>
          </div>
        </div>

      </div>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
import { createProxyOrders } from '@/api/business'
export default {
  data() {
    return {
      startTimeType: '1',
      startTimeTypeMap: [
        {
          label: '立即带货',
          value: '1'
        },
        {
          label: '预约带货',
          value: '2'
        }
      ],
      form: {
        start_time: '',
        estimatedCost: '--',
        price: 0.00,
        senderAddress: '',
        senderName: '',
        senderPhone: '',
        receiverAddress: '',
        receiverName: '',
        receiverPhone: '',
        weight: 1,
        remark: '',
        // 添加经纬度字段
        senderLocation: {
          lng: null,
          lat: null,
          name: null,
          address_code: null
        },
        receiverLocation: {
          lng: null,
          lat: null,
          name: null,
          address_code: null
        }
      },
      quickTags: ['易碎品', '宠物', '上门联系', '需冷藏', '文件', '贵重物品'],
      submitting: false,
      rules: {
        senderAddress: [
          { required: true, message: '请输入寄件详细地址', trigger: 'blur' }
        ],
        senderName: [
          { required: true, message: '请输入寄件人姓名', trigger: 'blur' }
        ],
        senderPhone: [
          { required: true, message: '请输入寄件人联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        receiverAddress: [
          { required: true, message: '请输入收件详细地址', trigger: 'blur' }
        ],
        receiverName: [
          { required: true, message: '请输入收件人姓名', trigger: 'blur' }
        ],
        receiverPhone: [
          { required: true, message: '请输入收件人联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入包裹重量', trigger: 'blur' },
          { type: 'number', min: 0.1, message: '重量必须大于0', trigger: 'blur', transform: value => Number(value) }
        ],
        price: [
          { required: true, message: '请输入运费价格', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
  },
  methods: {

    handleTag(tag) {
      this.form.remark += tag + ' '
    },
    // 查询寄件人地址
    querySenderAddress(queryString, callback) {
      if (queryString.length > 0) {
        this.searchAddress(queryString, callback)
      } else {
        callback([])
      }
    },

    // 查询收件人地址
    queryReceiverAddress(queryString, callback) {
      if (queryString.length > 0) {
        this.searchAddress(queryString, callback)
      } else {
        callback([])
      }
    },
    // https://restapi.amap.com/v3/assistant/inputtips?platform=JS&s=rsv3&logversion=2.0&key=ae07d204562de06e95f7ed805770b18f&sdkversion=*******&appname=https%253A%252F%252Fc.higgses.com%252Fpassenger_index%252Fcallback%252FMFC221309746013AC554571FBD180E1C8H3593&csid=9E898BB1-C1B9-421D-B941-C81CA766E976&jscode=8e41824d9d53ba41aeaa5033857279a9&city=%E6%88%90%E9%83%BD%E5%B8%82&type=&citylimit=true&datatype=all&language=zh_cn&s=rsv3&key=ae07d204562de06e95f7ed805770b18f&keywords=%E4%B8%96%E7%BA%AA%E5%9F%8E&callback=jsonp_129327_1743588543081_
    // 调用高德地图API搜索地址
    searchAddress(keyword, callback, city = '') {
      const key = 'ae07d204562de06e95f7ed805770b18f'
      const cityParam = city ? `&city=${city}&citylimit=true` : '&citylimit=false'
      const url = `https://restapi.amap.com/v3/assistant/inputtips?platform=JS&s=rsv3&logversion=2.0&key=${key}&sdkversion=*******&appname=https%253A%252F%252Fc.higgses.com%252Fpassenger_index%252Fcallback%252FMFC221309746013AC554571FBD180E1C8H3593&csid=9E898BB1-C1B9-421D-B941-C81CA766E976&jscode=8e41824d9d53ba41aeaa5033857279a9${cityParam}&type=&datatype=all&language=zh_cn&s=rsv3&key=${key}&keywords=${encodeURIComponent(keyword)}&callback=jsonp_129327_1743588543081_`

      // 使用JSONP方式请求高德地图API
      const script = document.createElement('script')
      script.src = url

      // 定义全局回调函数
      window.jsonp_129327_1743588543081_ = (data) => {
        if (data.status === '1' && data.tips && data.tips.length > 0) {
          const suggestions = data.tips.map(poi => {
            return {
              name: poi.name,
              address: poi.address,
              district: poi.district,
              location: poi.location,
              adcode: poi.adcode,
              address_code: poi.adcode
            }
          })
          callback(suggestions)
        } else {
          callback([])
        }

        // 清理：移除script标签和全局回调
        document.body.removeChild(script)
        delete window.jsonp_129327_1743588543081_
      }

      // 处理错误情况
      script.onerror = () => {
        console.error('地址搜索失败: JSONP请求错误')
        callback([])
        document.body.removeChild(script)
        delete window.jsonp_129327_1743588543081_
      }

      // 添加到文档中执行请求
      document.body.appendChild(script)
    },

    // 处理寄件人地址选择
    handleSenderAddressSelect(item) {
      this.form.senderAddress = `${item.name}`
      const location = item.location.split(',')
      this.form.senderLocation = {
        lng: location[0],
        lat: location[1],
        name: item.name,
        address_code: item.adcode
      }
      this.calculateShippingCost()
    },

    // 处理收件人地址选择
    handleReceiverAddressSelect(item) {
      this.form.receiverAddress = `${item.name}`
      const location = item.location.split(',')
      this.form.receiverLocation = {
        lng: location[0],
        lat: location[1],
        name: item.name,
        address_code: item.adcode
      }
      this.calculateShippingCost()
    },
    calculateShippingCost() {
      // 暂时不需要计算
    },
    submitOrder() {
      if (this.form.price === 0) {
        this.$message.error('请设置运费价格')
        return
      }
      this.$refs.dataForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          const orderData = {
            weight: this.form.weight,
            start_time: this.startTimeType === '1' ? moment().format('YYYY-MM-DD HH:mm') : moment(this.form.appointmentTime).format('YYYY-MM-DD HH:mm'),
            reseverd_phone: this.form.receiverPhone,
            reseverd_person: this.form.receiverName,
            delivery_person: this.form.senderName,
            delivery_phone: this.form.senderPhone,
            reseverd_info: this.form.remark,
            start_longitude: this.form.senderLocation.lng,
            start_latitude: this.form.senderLocation.lat,
            end_longitude: this.form.receiverLocation.lng,
            end_latitude: this.form.receiverLocation.lat,
            start_address_code: this.form.senderLocation.address_code,
            end_address_code: this.form.receiverLocation.address_code,
            start_address_remark: this.form.senderLocation.name,
            end_address_remark: this.form.receiverLocation.name,
            is_custom: 1,
            custom_price: this.form.price
          }
          // 业务类型，banxian-班线，baoche-包车，daihuo-带货
          const res = await createProxyOrders(orderData, 'daihuo')
          if (res.code === 200) {
            this.$message.success('订单提交成功!')
          } else {
            this.$message.error('订单提交失败!')
          }
          this.submitting = false
          // 重置表单
          this.$refs.dataForm.resetFields()
          // 重置经纬度
          this.form.senderLocation = { lng: null, lat: null }
          this.form.receiverLocation = { lng: null, lat: null }

          // 跳转订单管理
          this.$router.push('/orderManage/daihuoManage')
        } else {
          this.$message.error('请完善表单信息')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.express-delivery-container {
  padding: 20px;
  background-color: #f5f7fa;

  .express-form-wrapper {
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;

    .section-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #409EFF;
      display: flex;
      align-items: center;

      i {
        margin-right: 10px;
      }
    }

    .info-section {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      height: 100%;

      .section-header {
        padding: 12px 20px;
        font-size: 16px;
        font-weight: bold;
        background-color: #f2f6fc;
        border-bottom: 1px solid #ebeef5;
      }

      .form-content {
        padding: 20px;
      }

      &.sender-info {
        width: 49%;
        border-top: 3px solid #67c23a;
        margin-bottom: 2%;
      }

      &.receiver-info {
        width: 49%;
        border-top: 3px solid #409eff;
        margin-bottom: 1%;
      }

      &.package-info {
        height: 280px;
        width: 49%;
        border-top: 3px solid #e6a23c;
      }

      &.cost-info {
        position: relative;
        height: 280px;
        width: 49%;
        border-top: 3px solid #f56c6c;

        .cost-item {
          display: flex;
          align-items: center;

          .cost-label {
            font-size: 14px;
            color: #606266;
            font-weight: bold;
          }

          .cost-value {
            font-size: 24px;
            color: #f56c6c;
            font-weight: bold;
            margin-left: 40px;
          }
        }
      }
    }

    .weight-control {
      display: flex;
      align-items: center;

      .el-input {
        margin: 0 10px;
        width: 120px;

        ::v-deep .el-input__inner {
          text-align: center;
        }
      }
    }

    .quick-tags {
      margin-top: 10px;

      .el-tag {
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .submit-section {
      position: absolute;
      bottom: 20px;
      right: 20px;

      .el-button {
        padding: 12px 40px;
        font-size: 16px;
      }
    }
  }
}
.form-cost{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: #fff;
}

/* 修复日期选择器样式 */
::v-deep .el-date-editor.el-input {
  width: 220px !important;
}

::v-deep .el-date-editor .el-input__inner {
  padding-left: 30px;
  font-size: 13px;
}
</style>

<template>
  <div id="business-container" class="item-business">
    <el-form
      ref="dataForm" :model="temp" :rules="rules" :inline="true"
      label-position="left" label-width="90px"
      size="small"
    >
      <div class="item-warp-form">
        <div class="lines-box">
          <div class="search-city-wrap">
            <div class="search-branch">
              <label class="label-departStation">分台</label>
              <branch-select
                v-model="searchQuery.branch_id" placeholder="选择分台" size="small" class="condition"
                @change="branchChange"
              />
            </div>
            <div class="search-sub-business-type">
              <label class="label-departStation">业态</label>
              <el-select v-model="searchQuery.sub_business_type" placeholder="选择业态" clearable size="small" class="filter-item">
                <el-option v-for="item in subBusinessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="search-sub-business-type">
              <label class="label-departStation">线路</label>
              <line-select
                v-model="searchQuery.line_class_id" placeholder="选择线路" size="mini"
                @change="handleLineChange"
              />
            </div>
            <div class="search-city">
              <div class="from-city">
                <label class="label-departStation">出发</label>
                <el-select
                  v-model="start_area" filterable placeholder="请选择" size="small"
                  clearable
                  @change="startCodeChange"
                >
                  <el-option
                    v-for="item in startCode" :key="item.value + '-'" :label="item.label"
                    :value="item.label"
                  />
                </el-select>
              </div>
              <i class="icon-change" @click="exchange" />
              <div class="to-city">
                <label class="label-departStation">到达</label>
                <el-select
                  v-model="end_area" filterable placeholder="请选择" size="small"
                  clearable
                  @change="endCodeChange"
                >
                  <el-option v-for="item in endCode" :key="item.value + '-'" :label="item.label" :value="item.label" />
                </el-select>
              </div>
            </div>
            <div class="search-date">
              <label class="label-departStation">日期</label>
              <el-date-picker
                v-model="searchQuery.day" align="right" size="small" value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期" :picker-options="pickerOptions" @change="dateChange"
              />
            </div>
            <button class="btn-light-blue btn-search" @click="dateChange()">
              <i class="el-icon-search" />
              搜索
            </button>
          </div>
        </div>
        <el-tabs v-model="searchQuery.day" @tab-click="tabChange">
          <el-tab-pane v-for="(item, i) in dates" :key="i" :name="item.date">
            <div slot="label" class="custom-date">
              <el-badge is-dot type="success" :hidden="item.total_line_class_trains === 0">
                <p class="week">{{ item.display_week }}</p>
                <p class="date-view">{{ item.display_date }}</p>
              </el-badge>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div v-loading="linesLoading" class="lines-full-content">
          <div class="item-full-action">
            <label class="label-departStation">路线班次</label>
            <div class="check-list">
              <div class="check-scrollbar">
                <div class="check-scrollbar_wrap">
                  <ul v-if="lineList.length > 0" class="check-select_list">
                    <li
                      v-for="line in lineList" :key="line.line_class_train_id + '-'" class="check-select_item" :class="{
                        selected:
                          temp.line_class_train_id === line.line_class_train_id,
                        'is-disabled': line.remain_tickets === 0 ||
                          line.is_expire === 1 ||
                          (line.cn_schedule_status && (
                            line.cn_schedule_status.includes('已发车') ||
                            line.cn_schedule_status.includes('已出发')
                          )),
                      }"
                      @click="
                        checkLine(
                          'line_class_train_id',
                          line.line_class_train_id,
                          line
                        )
                      "
                    >
                      <p class="line-name">
                        <span>{{ line.line_class.start_name }}-{{
                          line.line_class.end_name
                        }}</span>
                      </p>
                      <p class="sub">
                        <span v-if="line.is_seat_selection === 1">¥{{ line.min_seat_price }}起</span>
                        <span v-else>¥{{ line.price }}</span>
                        <span v-if="line.cn_schedule_status">{{ line.cn_schedule_status }}</span>
                        <span v-else-if="line.is_expire === 1">已出发</span>
                        <span v-else>{{
                          line.remain_tickets > 0
                            ? `剩余${line.remain_tickets}张`
                            : "已售完"
                        }}</span>
                      </p>
                      <p class="time-info">
                        <span v-if="line.line_class.start_time_type === 1">
                          <i class="el-icon-time" /> {{ line.start_time.slice(0, 5) }}
                        </span>
                        <span v-else-if="line.line_class.start_time_type === 2">
                          <i class="el-icon-time" /> {{ line.start_earliest_time.slice(0, 5) }}-{{ line.end_latest_time.slice(0, 5) }}
                        </span>
                      </p>
                    </li>
                  </ul>
                  <el-empty v-else description="暂无数据" :image-size="70" />
                </div>
              </div>
            </div>
          </div>
          <div class="item-full-action">
            <label class="label-departStation">出发时间
              <span v-if="currentLine">
                [{{ currentLine && currentLine.line_class && currentLine.line_class.start_time_type === 1 ? "固定"
                  : currentLine && currentLine.line_class && currentLine.line_class.start_time_type === 2 ? `滚动(${currentLine.start_earliest_time && currentLine.start_earliest_time.slice(0, 5)}-${currentLine.end_latest_time && currentLine.end_latest_time.slice(0, 5)})`
                    : '自定义'
                }}]</span></label>
            <div class="check-list">
              <div class="check-scrollbar">
                <div v-if="currentLine" class="check-scrollbar_wrap">
                  <template v-if="currentLine && currentLine.line_class && currentLine.line_class.start_time_type === 1">
                    <ul class="check-select_list">
                      <li
                        class="check-select_item" :class="{
                          selected:
                            temp.start_time &&
                            currentLine &&
                            currentLine.start_time &&
                            temp.start_time === currentLine.start_time,
                        }"
                      >
                        <span>{{ currentLine.start_date }}
                          {{ currentLine.start_time }}</span>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <ul class="check-select_list">
                      <li class="check-select_item selected">
                        <div class="customDay-warp">
                          <span>{{ currentLine.start_date }}</span>
                          <div class="time-input-group">
                            <el-input v-model="customhour" size="small" class="timeInput" placeholder="小时" />
                            <span class="time-separator">:</span>
                            <el-input v-model="custommin" class="timeInput" size="small" placeholder="分钟" />
                          </div>
                        </div>
                      </li>
                    </ul>
                  </template>
                </div>
                <el-empty v-else description="暂无数据" :image-size="70" />
              </div>
            </div>
          </div>
          <div class="item-full-action">
            <label class="label-departStation">上车点<span v-if="currentLine">
              [{{
                currentLine.line_class.start_address_type === 1
                  ? "固定"
                  : `围栏内任意`
              }}]</span></label>
            <div class="check-list">
              <div class="check-scrollbar">
                <div v-if="currentLine" class="check-scrollbar_wrap">
                  <template v-if="currentLine.line_class.start_address_type === 1">
                    <ul class="check-select_list">
                      <li
                        v-for="(item, i) in currentLine.line_class
                          .fixed_boarding_point" :key="i" class="check-select_item" :class="{
                          selected: temp.start_point == JSON.stringify(item),
                        }"
                        @click="checkLine('start_point', JSON.stringify(item))"
                      >
                        <span>{{ item.alias }}</span>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <div class="address-input">
                      <el-select
                        v-model="temp.customStartPoint" filterable style="width: 100%" loading-text="加载中.."
                        remote value-key="id" placeholder="请输入上车点" :remote-method="(_) => remotePoint('start', _)"
                        :loading="loadingStart"
                      >
                        <el-option v-for="(item, i) in startMapPoint" :key="i" :value="item" :label="item.name">
                          <p class="custom-point-name">
                            <span>{{ item.name }}</span>
                            <span v-if="item.district" class="district" style="color: #999; font-size: 13px">{{
                              item.district }}</span>
                          </p>
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </div>
                <el-empty v-else description="暂无数据" :image-size="70" />
              </div>
            </div>
          </div>
          <div class="item-full-action">
            <label class="label-departStation">下车点<span v-if="currentLine">
              [{{
                currentLine.line_class.end_address_type === 1
                  ? "固定"
                  : `围栏内任意`
              }}]</span></label>
            <div class="check-list">
              <div class="check-scrollbar">
                <div v-if="currentLine" class="check-scrollbar_wrap">
                  <template v-if="currentLine.line_class.end_address_type === 1">
                    <ul class="check-select_list">
                      <li
                        v-for="(item, i) in currentLine.line_class
                          .fixed_dropoff_point" :key="i" class="check-select_item" :class="{
                          selected: temp.end_point == JSON.stringify(item),
                        }"
                        @click="checkLine('end_point', JSON.stringify(item))"
                      >
                        <span>{{ item.alias }}</span>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <div class="address-input">
                      <el-select
                        v-model="temp.customEndPoint" filterable style="width: 100%" loading-text="加载中.."
                        remote value-key="id" placeholder="请输入下车点" :remote-method="(_) => remotePoint('end', _)"
                        :loading="loadingEnd"
                      >
                        <el-option v-for="(item, i) in endMapPoint" :key="i" :value="item" :label="item.name">
                          <p class="custom-point-name">
                            <span>{{ item.name }}</span>
                            <span v-if="item.district" class="district" style="color: #999; font-size: 13px">{{
                              item.district }}</span>
                          </p>
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </div>
                <el-empty v-else description="暂无数据" :image-size="70" />
              </div>
            </div>
          </div>
        </div>
        <div class="bottom-content">
          <div class="item-warp-form-content">
            <el-form-item label="预留乘车人">
              <el-input v-model="temp.reseverd_username" placeholder="请输入" class="trainsInput" />
            </el-form-item>
            <el-form-item label="联系电话" prop="reseverd_phone">
              <el-input v-model="temp.reseverd_phone" class="trainsInput" placeholder="请输入" />
            </el-form-item>
            <el-form-item v-if="currentLine && currentLine.is_seat_selection === 1" label="座位" prop="seats">
              <el-popover placement="top" width="300" trigger="click">
                <Seat
                  :seat-price="typeof currentLine.seat_price === 'string' ? JSON.parse(currentLine.seat_price) : currentLine.seat_price"
                  :seat-layout="currentLine.seat_layout
                    ? currentLine.seat_layout
                    : (typeof currentLine.seat_price === 'string'
                      ? (JSON.parse(currentLine.seat_price).length === 5 ? '2-3' : JSON.parse(currentLine.seat_price).length === 7 ? '2-2-3' : '')
                      : (currentLine.seat_price.length === 5 ? '2-3' : currentLine.seat_price.length === 7 ? '2-2-3' : ''))
                  "
                  :check="temp.seats"
                  @seatChange="seatChange"
                />
                <el-button slot="reference" size="mini" type="primary">选座</el-button>
              </el-popover>
              <p v-if="temp.seats && temp.seats.length > 0" class="checkd-seat">
                已选：{{ temp.seats.map((o) => o.name).join(",") }}
              </p>
            </el-form-item>
            <el-form-item v-else label="人数/票数" prop="number">
              <el-input-number
                v-model="temp.number" :min="1" :max="10" label="请输入"
                style="width: 150px"
                class="trainsInput"
                @change="handleNumberChange"
              />
            </el-form-item>
            <el-form-item label="价格" prop="custom_price">
              <el-input v-model="temp.custom_price" class="trainsInput" placeholder="请输入" />
            </el-form-item>
            <el-form-item v-if="currentLine && currentLine.line_class && currentLine.line_class.is_required_flight_number === 1" label="航班号" prop="flight_number">
              <el-input v-model="temp.flight_number" placeholder="请输入航班号" />
            </el-form-item>
            <el-form-item label="备注" style="display: flex; width: 100%">
              <el-input v-model="temp.reseverd_info" type="textarea" placeholder="请输入备注信息" />
              <div class="quick-tags">
                <el-tag
                  v-for="item in quickTags" :key="item" type="info" size="small"
                  style="cursor: pointer"
                  effect="plain" @click="handleTag(item)"
                >
                  {{ item }}
                </el-tag>
              </div>
            </el-form-item>
            <div class="form-footer">
              <el-button type="primary" @click="subData"> 提交订单 </el-button>
            </div>
          </div>
          <div class="item-warp-form-content">
            <MapContainer
              :map-params="mapParams"
              :current-line="currentLine"
              @priceChange="handlePriceChange"
            />
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>
<script>
import moment from 'moment'
import MapContainer from './MapContainer'
import { Notification } from 'element-ui'
import { mapGetters } from 'vuex'
import LineSelect from '@/views/components/lineIdSelect/index.vue'
import Seat from '@/components/Seat'
import BranchSelect from '@/components/BranchSelect'
import {
  support_starting_points,
  support_ending_points,
  getLineClassTrains,
  proxy_orders,
  schedule_days,
  dispatchBranches
} from '@/api/business'
import AMapLoader from '@amap/amap-jsapi-loader'
export default {
  components: { BranchSelect, MapContainer, Seat, LineSelect },
  props: {
    params: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    var seatValidate = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请选择座位'))
      } else {
        callback()
      }
    }
    return {
      start_area: '',
      end_area: '',
      customhour: moment().add(10, 'minutes').format('HH'),
      custommin: moment().add(10, 'minutes').format('mm'),
      dates: [],
      lineList: [],
      currentLine: null,
      searchQuery: {},
      basePrice: 0,
      subBusinessTypeOptions: [
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ],
      quickTags: [
        '有儿童',
        '有大件行李',
        '赶飞机',
        '赶火车',
        '携带有宠物',
        '有孕妇、老人'
      ],

      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            }
          },
          {
            text: '明天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() + 3600 * 1000 * 24)
              picker.$emit('pick', date)
            }
          },
          {
            text: '后天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() + 3600 * 1000 * 24 * 2)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      branchs: [],
      startMapPoint: [],
      endMapPoint: [],
      loadingEnd: false,
      loadingStart: false,
      linesLoading: false,
      mapObj: null,
      startCode: [],
      endCode: [],
      temp: {
        customEndPoint: '',
        customStartPoint: '',
        reseverd_info: '',
        custom_price: '',
        number: 1,
        line_class_train_id: '',
        start_point: '',
        end_point: '',
        start_address_type: '',
        end_address_type: '',
        seats: [],
        end_polygon: '',
        start_polygon: '',
        flight_number: '',
        reseverd_username: '',
        reseverd_phone: ''
      },
      rules: {
        reseverd_phone: [
          { required: true, message: '请输入乘客联系电话', trigger: 'blur' }
        ],
        custom_price: [
          { required: true, message: '请输入价格', trigger: 'blur' }
        ],
        number: [{ required: true, message: '请输入票数', trigger: 'blur' }],
        seats: [{ required: true, validator: seatValidate, trigger: 'blur' }]
      }
    }
  },
  computed: {
    // canculatePrice() {
    //   if (this.temp.is_seat_selection === 1) {
    //   }
    // },
    ...mapGetters(['info']),
    mapParams() {
      const {
        customEndPoint,
        customStartPoint,
        start_address_type,
        end_address_type,
        start_point,
        end_point,
        start_polygon,
        end_polygon
      } = this.temp

      return {
        customEndPoint,
        customStartPoint,
        start_address_type,
        end_address_type,
        start_point,
        end_point,
        end_polygon,
        start_polygon
      }
    }
  },

  watch: {
    params: {
      handler(n) {
        // 创建 params 的副本，避免直接修改
        const paramsClone = { ...n }
        const { reseverd_phone, start_address_code, end_address_code } = paramsClone

        if (reseverd_phone && start_address_code && end_address_code) {
          if (
            this.startCode.map((o) => o.value).includes(start_address_code) &&
            this.endCode.map((o) => o.value).includes(end_address_code)
          ) {
            this.searchQuery.start_area_code = start_address_code
            this.searchQuery.end_area_code = end_address_code
            this.temp.reseverd_phone = reseverd_phone
            this.dateChange()
            this.$nextTick(() => {
              const item = document.getElementById('business-container') // 指定的元素
              window.scrollTo(0, item.offsetTop - 40) // 竖向
            })
          } /* else {
            this.$message('该班次已停运或未排班，请选择其他班次')
          }*/
        }
      },
      immediate: true,
      deep: true
    },
    'temp.seats': {
      handler(n) {
        if (n && n.length > 0 && this.temp.is_seat_selection === 1) {
          this.temp.custom_price = n
            .map((o) => Number(o.price))
            .reduce((a, b) => {
              return a + b
            })
        }
      }
    },
    'temp.number': {
      handler(n) {
        if (n && this.temp.is_seat_selection === 0) {
          this.temp.custom_price = (
            this.temp.number * Number(this.currentLine.price)
          ).toFixed(2)
        } else {
          this.temp.custom_price = ''
        }
      }
    },
    'temp.line_class_train_id': {
      handler(n) {
        if (n) {
          const default_line = this.lineList.find(
            (o) => o.line_class_train_id === n
          )
          this.$set(this, 'currentLine', default_line)
          console.log('this.currentLine', this.currentLine)
          if (default_line) {
            this.temp.start_address_type =
              default_line.line_class.start_address_type
            this.temp.end_address_type =
              default_line.line_class.end_address_type
            this.temp['is_seat_selection'] = default_line.is_seat_selection
            this.temp['start_polygon'] = default_line.line_class.start_polygon
            this.temp['end_polygon'] = default_line.line_class.end_polygon
            this.temp.customEndPoint = ''
            this.temp.customStartPoint = ''
            this.temp['start_time_type'] =
              default_line.line_class.start_time_type

            // 默认注入选座
            if (default_line.is_seat_selection === 1) {
              // 处理可能是字符串的seat_price
              const seatPrice = typeof default_line.seat_price === 'string'
                ? JSON.parse(default_line.seat_price)
                : default_line.seat_price

              // 查找可选座位
              const default_seat_index = seatPrice.findIndex(
                (o) => {
                  return o.optional === 0 && o.seat_id !== 1
                }
              )
              if (default_seat_index !== -1) {
                this.temp.seats = [seatPrice[default_seat_index]]
                this.temp.custom_price = this.temp.seats
                  .map((o) => Number(o.price))
                  .reduce((a, b) => {
                    return a + b
                  })
              } else {
                this.temp.custom_price = ''
              }
            } else {
              this.temp.seats = []
              // 设置基础价格
              this.basePrice = Number(this.currentLine.price)
              this.temp.custom_price = (
                this.temp.number * this.basePrice
              ).toFixed(2)
            }

            // 当默认路线存在，默认注入时间，地点参数
            if (default_line.line_class.start_time_type === 1) {
              // 固定出发时间
              if (default_line.start_time) {
                console.log('默认设置固定时间:', default_line.start_time)
                this.temp.start_time = default_line.start_time
              }
            } else if (default_line.line_class.start_time_type === 2) {
              // 滚动出发时间
              if (default_line.start_earliest_time) {
                // 设置自定义输入框时间为滚动时间范围的起始时间
                this.customhour = default_line.start_earliest_time.slice(0, 2)
                this.custommin = default_line.start_earliest_time.slice(3, 5)
              }
            }

            if (default_line.line_class.start_address_type === 1) {
              // 固定上车点
              this.temp['start_point'] = JSON.stringify(
                default_line.line_class.fixed_boarding_point[0]
              )
            } else {
              this.temp['start_point'] = ''

              this.remotePoint(
                'start',
                ['市辖区', '县'].includes(
                  default_line.line_class.start_city.name
                )
                  ? default_line.line_class.start_province.name
                  : default_line.line_class.start_city.name
              )
            }
            if (default_line.line_class.end_address_type === 1) {
              // 固定下车点
              this.temp['end_point'] = JSON.stringify(
                default_line.line_class.fixed_dropoff_point[0]
              )
            } else {
              this.temp['end_point'] = ''

              this.remotePoint(
                'end',
                ['市辖区', '县'].includes(default_line.line_class.end_city.name)
                  ? default_line.line_class.end_province.name
                  : default_line.line_class.end_city.name
              )
            }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.initMapObject()
    // 确保params总是可用，防止编译后的代码中出现引用错误
    if (typeof this.params === 'undefined') {
      this.$set(this, 'params', {})
    }
  },
  mounted() {
    this.fetchBranch()
    if (this.$route.query.day) {
      this.searchQuery.day = moment(this.$route.query.day).format('YYYY-MM-DD')
    } else {
      this.searchQuery.day = moment().format('YYYY-MM-DD')
    }

    this.dateChange(moment().format('YYYY-MM-DD'))
  },
  methods: {
    branchChange() {
      this.start_area = ''
      this.end_area = ''
      this.fetchScode()
      this.fetchEcode()
      this.searchQuery.start_area_code = ''
      this.searchQuery.end_area_code = ''
    },
    exchange() {
      const temp = this.start_area
      this.start_area = this.end_area
      this.end_area = temp
      const temp1 = this.searchQuery.start_area_code
      this.searchQuery.start_area_code = this.searchQuery.end_area_code
      this.searchQuery.end_area_code = temp1
    },
    async fetchBranch() {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
        this.$set(this.searchQuery, 'branch_id', null)
        this.fetchScode()
        this.fetchEcode()
      }
    },
    setOrder(order) {
      this.temp = {
        reseverd_username: order.reseverd_username || '',
        reseverd_phone: order.userMobile || '',
        custom_price: order.custom_price || '',
        number: order.number || 1,
        reseverd_info: order.reseverd_info || '',
        flight_number: order.flight_number || '',
        line_class_train_id: order.line_class_train_id || '',
        start_point: order.start_point || '',
        end_point: order.end_point || '',
        start_address_type: order.start_address_type || '',
        end_address_type: order.end_address_type || '',
        seats: order.seats || [],
        start_polygon: order.start_polygon || '',
        end_polygon: order.end_polygon || '',
        start_time_type: order.start_time_type || '',
        start_time: order.start_time || '',
        end_time: order.end_time || '',
        start_earliest_time: order.start_earliest_time || '',
        end_latest_time: order.end_latest_time || '',
        customStartPoint: order.customStartPoint || '',
        customEndPoint: order.customEndPoint || ''
      }
      this.temp.flight_number = ''
      this.temp.custom_price = ''
      this.$nextTick(() => {
        const item = document.getElementById('business-container')
        window.scrollTo(0, item.offsetTop - 40)
      })
    },
    async dateChange(val = '') {
      const { data } = await schedule_days({
        date: val || this.searchQuery.day,
        start_area_code: this.searchQuery.start_area_code,
        end_area_code: this.searchQuery.end_area_code
      })
      this.dates = data
      this.getLineList()
    },
    async subData() {
      try {
        this.$refs['dataForm'].validate(async (valid) => {
          if (valid) {
            const tempData = Object.assign({}, this.temp)
            if (
              tempData.start_address_type === 2 &&
              tempData.customStartPoint === ''
            ) {
              this.$message('请输入上车位置')
              return
            }
            console.log(tempData)
            if (
              tempData.end_address_type === 2 &&
              tempData.customEndPoint === ''
            ) {
              this.$message('请输入下车位置')
              return
            }
            const {
              custom_price,
              is_seat_selection,
              line_class_train_id,
              number,
              reseverd_phone,
              reseverd_username,
              reseverd_info,
              flight_number
            } = tempData
            // 创建一个全新的参数对象而不是修改现有对象
            const newParams = {
              custom_price,
              is_seat_selection,
              line_class_train_id,
              number,
              reseverd_phone,
              reseverd_username,
              reseverd_info,
              flight_number,
              is_custom: 1 // 添加自定义价格标志
            }

            if (tempData.start_address_type === 2) {
              newParams['start_longitude'] = tempData.customStartPoint.location.lng
              newParams['start_latitude'] = tempData.customStartPoint.location.lat
              newParams['start_address_remark'] = tempData.customStartPoint.name
            } else {
              newParams['start_longitude'] = JSON.parse(
                tempData.start_point
              ).longitude
              newParams['start_latitude'] = JSON.parse(
                tempData.start_point
              ).latitude
              newParams['start_address_remark'] = JSON.parse(
                tempData.start_point
              ).alias
            }
            if (tempData.end_address_type === 2) {
              newParams['end_longitude'] = tempData.customEndPoint.location.lng
              newParams['end_latitude'] = tempData.customEndPoint.location.lat
              newParams['end_address_remark'] = tempData.customEndPoint.name
            } else {
              newParams['end_longitude'] = JSON.parse(tempData.end_point).longitude
              newParams['end_latitude'] = JSON.parse(tempData.end_point).latitude
              newParams['end_address_remark'] = JSON.parse(tempData.end_point).alias
            }
            if (tempData.is_seat_selection === 1) {
              newParams['seat_ids'] = tempData.seats.map((o) => o.seat_id).join(',')
            }
            if (tempData.start_time_type === 1) {
              newParams['start_time'] =
                this.searchQuery.day + ' ' + tempData.start_time
            } else {
              if (!this.customhour || !this.custommin) {
                this.$message('请输入出发时间')
                return
              } else {
                newParams['start_time'] =
                  this.searchQuery.day +
                  ' ' +
                  this.customhour +
                  ':' +
                  this.custommin +
                  ':' +
                  '00'
              }
            }
            const loading = this.$loading({
              lock: true,
              text: '订单生成中..',
              spinner: 'el-icon-loading'
            })
            try {
              await proxy_orders(newParams)
              loading.close()
              Notification({
                title: '操作成功',
                message: '代约下单已成功',
                type: 'success',
                duration: 2000
              })
              this.getLineList()
              this.$store.dispatch(
                'unread/getOrderCount',
                this.info.admin_id || ''
              )
            } catch (error) {
              loading.close()
              console.error('订单提交失败:', error)
              this.$message.error(`订单提交失败: ${error.message || '未知错误'}`)
            }
          }
        })
      } catch (outerError) {
        console.error('subData方法出错:', outerError)
        this.$message.error(`提交过程中出错: ${outerError.message || '未知错误'}`)
      }
    },
    seatChange(e) {
      this.temp.seats = e
    },
    tabChange(e) {
      this.getLineList()
    },
    handleNumberChange(value) {
      console.log('人数变化:', value)

      // 如果没有选择线路，不进行调整
      if (!this.currentLine) return

      // 非选座模式，重新计算价格
      if (this.currentLine.is_seat_selection !== 1) {
        this.temp.custom_price = (value * this.basePrice).toFixed(2)
      }
    },

    handlePriceChange(priceChangeInfo) {
      console.log('收到价格调整:', priceChangeInfo)

      // 如果没有选择线路或价格为空，不进行调整
      if (!this.currentLine || !this.temp.custom_price) return

      // 从价格调整信息对象中获取总调整值
      const totalAdjustment = priceChangeInfo.total

      // 保存价格调整值到组件实例上，便于人数变化时重新计算
      this.priceAdjustment = totalAdjustment

      // 获取基础价格
      let basePrice

      if (this.currentLine.is_seat_selection === 1 && this.temp.seats && this.temp.seats.length > 0) {
        // 如果是选座模式，基础价格从座位价格计算
        basePrice = this.temp.seats
          .map((o) => Number(o.price))
          .reduce((a, b) => a + b, 0)
      } else {
        // 非选座模式，基础价格为单价
        basePrice = this.basePrice
      }

      // 应用价格调整（调整值可能是负数，表示优惠）
      const adjustedPrice = (parseFloat(basePrice) * this.temp.number + parseFloat(totalAdjustment) * this.temp.number).toFixed(2)

      // 更新价格 - 使用Vue的方式更新对象属性
      this.$set(this.temp, 'custom_price', adjustedPrice)

      // 显示提示信息
      if (totalAdjustment !== 0) {
        // 确定是哪个点触发了价格变化
        let pointType = ''
        if (priceChangeInfo.startAdjustment !== 0 && priceChangeInfo.endAdjustment !== 0) {
          pointType = '上下车点'
        } else if (priceChangeInfo.startAdjustment !== 0) {
          pointType = '上车点'
        } else if (priceChangeInfo.endAdjustment !== 0) {
          pointType = '下车点'
        }

        const priceType = totalAdjustment > 0 ? '加价' : '优惠'
        const message = `${pointType}在${priceType}区域内，已自动调整价格 ${totalAdjustment > 0 ? '+' : ''}${totalAdjustment}元/人`

        this.$notify({
          title: '价格已调整',
          message: message,
          type: totalAdjustment > 0 ? 'warning' : 'success',
          duration: 3000
        })
      }
    },
    async getLineList() {
      this.linesLoading = true
      this.searchQuery.ignore_stop_sale_minutes = 1
      const { data } = await getLineClassTrains(this.searchQuery)
      this.linesLoading = false
      this.lineList = data

      console.log('data', data)
      if (data && data.length > 0) {
        const currentIndex = data.findIndex((o) => {
          return o.remain_tickets > 0 && o.is_expire !== 1
        })

        console.log('currentIndex', currentIndex)

        if (currentIndex !== -1) {
          this.temp.line_class_train_id =
            data[currentIndex].line_class_train_id
        }

        if (this.$route.query.train_id) {
          this.temp.line_class_train_id = this.$route.query.train_id
          const line = this.lineList.find(line => line.line_class_train_id === Number(this.$route.query.train_id))
          this.checkLine('line_class_train_id', line.line_class_train_id, line)
        }
      } else {
        this.currentLine = null
        this.temp = {
          ...this.temp,
          ...{
            customEndPoint: '',
            customStartPoint: '',
            reseverd_info: '',
            custom_price: '',
            number: 1,
            line_class_train_id: '',
            start_point: '',
            end_point: '',
            start_address_type: '',
            end_address_type: '',
            seats: []
          }
        }
      }
    },
    initMapObject() {
      AMapLoader.load({
        key: 'ae07d204562de06e95f7ed805770b18f', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.AutoComplete'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      }).then((AMap) => {
        this.mapObj = new AMap.Autocomplete()
      })
    },
    async remotePoint(type = 'start', query) {
      if (type === 'start') {
        this.loadingStart = true
      } else {
        this.loadingEnd = true
      }
      this.mapObj.setCity(
        type === 'start'
          ? this.currentLine.line_class.start_city_code
          : this.currentLine.line_class.end_city_code
      )
      const placeSearch_ = this.mapObj
      placeSearch_.search(query, (status, result) => {
        console.log(result)
        if (type === 'start') {
          this.loadingStart = false
        } else {
          this.loadingEnd = false
        }
        if (status === 'complete') {
          if (type === 'start') {
            this.startMapPoint = result.tips.filter(o => o.location)
          } else {
            this.endMapPoint = result.tips.filter(o => o.location)
          }
        }
      })
    },
    startCodeChange(e) {
      this.searchQuery['start_area_code'] = this.startCode.find(
        (o) => o.label === e
      ).value
      this.fetchEcode(true)
    },
    endCodeChange(e) {
      this.searchQuery['end_area_code'] = this.endCode.find(
        (o) => o.label === e
      ).value
    },
    async fetchScode() {
      const { data } = await support_starting_points({
        branch_id: this.searchQuery.branch_id
      })
      if (data && data.length > 0) {
        this.startCode = data.map((o) => {
          return {
            label: `${[110000, 120000, 310000, 500000].includes(
              o.start_province.address_id
            )
              ? o.start_province.name
              : o.start_city.name
            }-${o.start_area.name}`,
            value: o.start_area.address_id
          }
        })
      } else {
        this.startCode = []
      }
    },
    async fetchEcode(isdefault = false) {
      const { data } = await support_ending_points({
        start_area_code: this.searchQuery.start_area_code,
        branch_id: this.searchQuery.branch_id
      })
      if (data && data.length > 0) {
        this.endCode = data.map((o) => {
          return {
            label: `${[110000, 120000, 310000, 500000].includes(
              o.end_province.address_id
            )
              ? o.end_province.name
              : o.end_city.name
            }-${o.end_area.name}`,
            value: o.end_area.address_id
          }
        })
        if (isdefault) {
          this.$set(this.searchQuery, 'end_area_code', this.endCode[0].value)
        }
      } else {
        this.endCode = []
      }
    },

    checkLine(key, val, item = null) {
      console.log('key', key)
      console.log('val', val)
      console.log('item', item)
      if (item && (
        item.remain_tickets === 0 ||
          item.is_expire === 1 ||
          (item.cn_schedule_status && (
            item.cn_schedule_status.includes('已发车') ||
            item.cn_schedule_status.includes('已出发')
          ))
      )) {
        return
      }
      this.$set(this.temp, key, val)

      // 当选择班次时
      if (key === 'line_class_train_id' && item) {
        if (item.line_class.start_time_type === 2) {
          // 滚动发车时间
          if (item.start_earliest_time) {
            this.customhour = item.start_earliest_time.slice(0, 2)
            this.custommin = item.start_earliest_time.slice(3, 5)
          }
        } else if (item.line_class.start_time_type === 1) {
          // 固定发车时间，设置temp.start_time
          if (item.start_time) {
            console.log('设置固定时间:', item.start_time)
            this.$set(this.temp, 'start_time', item.start_time)
          }
        }
      }
    },
    handleTag(tag) {
      const tag_ = this.temp.reseverd_info ? `，${tag}` : tag
      this.temp.reseverd_info += tag_
    },
    handleLineChange(val) {
      // 处理线路变更事件
      if (val) {
        this.searchQuery.line_class_id = val
        this.getLineList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-footer {
  width: 100%;
}

.checkd-seat {
  font-size: 14px;
  line-height: 1;
  margin-top: 5px;
  color: #0067e1;
}

.address-input {
  padding: 10px;
}

.bottom-content {
  display: flex;

  .item-warp-form-content {
    flex: 1;
  }
}

.quick-tags {
  margin-top: 10px;
}

.customDay-warp {
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 700;
  margin: 0;
  padding: 0;

  span {
    margin-right: 5px;
  }

  .time-input-group {
    display: flex;
    align-items: center;

    .time-separator {
      margin: 0 2px;
    }
  }

  .timeInput {
    flex: 1;
    margin: 0 4px;
    max-width: 60px;
  }

  .el-input__inner {
    padding: 0 6px;
    text-align: center;
    font-size: 14px;
  }
}

.lines-full-content {
  display: flex;

  .item-full-action {
    flex: 1;
    background: #f8f8f8;
    border-radius: 8px;
    padding: 6px 12px;
    margin-left: 8px;

    &:first-child {
      margin-left: 0;
    }

    .check-list {
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      .check-scrollbar {
        overflow: hidden;
        position: relative;

        .check-scrollbar_wrap {
          overflow: scroll;
          height: 100%;
          max-height: 250px;

          .check-select_list {
            list-style: none;
            margin: 0;
            padding: 3px 0;

            .check-select_item {
              padding: 6px 8px;
              padding-right: 35px;
              margin-bottom: 8px;
              font-size: 13px;
              position: relative;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #606266;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              cursor: pointer;

              .line-name {
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                max-width: 280px;
              }

              .sub {
                font-size: 12px;
                margin-top: 2px;
                color: #666;
                font-weight: normal;
                display: flex;
                align-items: center;
                justify-content: space-between;
              }

              .time-info {
                font-size: 11px;
                margin-top: 2px;
                color: #409EFF;
                font-weight: normal;
              }

              &:hover {
                background-color: #f5f7fa;
              }
            }

            .selected {
              color: #0067e1;
              font-weight: 700;
              background-color: #f5f7fa;

              &::after {
                position: absolute;
                margin: auto;
                top: 0;
                bottom: 0;
                right: 20px;
                font-family: element-icons;
                content: "\e6da";
                font-size: 12px;
                font-weight: 700;
                height: 12px;
                -webkit-font-smoothing: antialiased;
              }

              .sub {
                color: #0067e1;
              }
            }

            .is-disabled {
              color: #c0c4cc;
              cursor: not-allowed;

              .sub {
                color: #c0c4cc;
              }
            }
          }
        }
      }
    }

    .label-departStation {
      color: #999;
      font-size: 14px;
      display: block;
      font-weight: normal;
      line-height: 18px;
      margin-bottom: 5px;
    }
  }
}

.custom-date {
  padding: 10px 0;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  p {
    margin: 0;
    padding: 0;
    text-align: center;
    line-height: 1;
  }

  .date-view {
    font-size: 15px;
    font-weight: bold;

    .week {
      margin-left: 5px;
    }
  }

  .price {
    font-weight: normal;
    font-size: 13px;
    // color: #666;
  }
}

.search-city-wrap {
  margin-bottom: 8px;

  .btn-search {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    border-radius: 8px;
    height: 70px;
    border: 0;
    outline: 0;
    padding: 0;
    cursor: pointer;
    font-weight: bold;
    width: 80px;

    i {
      font-weight: 400;
      font-size: 14px;
      margin: 0 3px 0 0;
    }
  }

  .btn-light-blue {
    background: #f2f8fe;
    color: #0067e1;
  }

  .label-departStation {
    color: #999;
    font-size: 13px;
    display: block;
    font-weight: normal;
    line-height: 18px;
    margin-bottom: 3px;
  }

  display: flex;
  align-items: center;

  .search-date {
    background: #f8f8f8;
    border-radius: 8px;
    padding: 6px 15px;
    margin-left: 8px;
  }

  .search-branch {
    background: #f8f8f8;
    border-radius: 8px;
    padding: 6px 15px;
    margin-right: 8px;
  }

  .search-sub-business-type {
    background: #f8f8f8;
    border-radius: 8px;
    padding: 6px 15px;
    margin-right: 8px;
  }

  .search-city {
    display: flex;
    background: #f8f8f8;
    border-radius: 8px;
    position: relative;
    min-width: 320px;

    .icon-change {
      cursor: pointer;
      width: 20px;
      height: 20px;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      background: url("../../../../assets/icon-chage.png") no-repeat;
      background-size: cover;
    }

    .from-city {
      width: 50%;
      padding: 6px 15px;
      border-right: 1px solid #ddd;
    }

    .to-city {
      width: 50%;
      padding: 6px 15px;
    }
  }

  .condition, .filter-item {
    width: 150px !important;
  }
}

.item-warp-form {
  .lines-box {
    margin-bottom: 8px;
  }
}

.el-tabs__nav-wrap {
  padding: 0 5px;
}

.search-city-wrap .el-date-editor.el-input,
.search-city-wrap .el-date-editor.el-input__inner {
  width: 160px;
}

.search-city-wrap .el-select {
  width: 100%;
}
</style>
<style lang="scss">
.custom-point-name {
  max-width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  span {
    margin-right: 10px;
  }
}

.item-business {
  .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:after {
    content: "*";
    color: #f56c6c;
    margin-left: 4px;
  }

  .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
    display: none;
  }

  .customDay-warp {
    padding: 10px 0 8px 10px;

    .el-input__inner {
      padding: 0 6px;
      text-align: center;
      font-size: 14px;
    }
  }

  .radio-list {
    .el-radio {
      display: block;
      margin: 5px 0;
    }
  }

  .is-active {
    .price {
      color: #0067e1;
    }
  }

  .el-tabs__item {
    height: 48px;
    padding: 0 12px;
  }

  .date-condition {
    min-width: 140px;
    width: 140px;
  }

  .item-warp-form-content {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;

    .el-form-item {
      margin-bottom: 12px;
      width: 48%;
      display: flex;

      .el-form-item__label {
        min-width: 85px;
      }
    }
  }
}

.el-input__inner, .el-select .el-input__inner {
  height: 30px;
  line-height: 30px;
}

.el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 130px;
}

.search-city-wrap .el-select {
  width: 100%;
}

.lines-full-content {
  margin-top: 5px;
}

.el-tabs__nav {
  display: flex;
  justify-content: space-around;
}

.custom-date {
  padding: 8px 0;
  .week {
    font-size: 12px;
  }
  .date-view {
    font-size: 13px;
  }
}
</style>

<template>
  <div id="business-container" class="express-delivery-container">
    <el-form ref="dataForm" :model="form" :rules="rules" label-position="left" label-width="100px" size="small">
      <div class="express-form-wrapper">
        <!-- 上车位置 -->
        <div class="info-section sender-info">
          <div class="section-header">上车位置</div>
          <div class="form-content">
            <el-form-item label="详细地址" prop="senderAddress">
              <el-input
                v-model="form.senderAddress"
                placeholder="请输入上车详细地址"
                @input="querySenderInput"
              />
              <el-autocomplete
                v-if="false"
                v-model="form.senderAddress"
                :fetch-suggestions="querySenderAddress"
                placeholder="请输入上车位置"
                style="width: 100%"
                @select="handleSenderAddressSelect"
              >
                <template slot-scope="{ item }">
                  <div>{{ item.name }}<span style="color: #909399; font-size: 12px; margin-left: 8px;">{{ item.district }}</span></div>
                </template>
                <el-cascader
                  slot="prepend"
                  v-model="startCity"
                  :options="startCityMap"
                  :props="{ checkStrictly: true }"
                  placeholder="请选择上车位置"
                  style="width: 180px;"
                />
              </el-autocomplete>
              <div v-if="senderSuggestions.length > 0" class="address-suggestions">
                <div
                  v-for="(item, index) in senderSuggestions"
                  :key="index"
                  class="suggestion-item"
                  @click="handleSenderAddressSelect(item)"
                >
                  <div>{{ item.name }}<span>{{ item.fullAddress || item.district }}</span></div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 下车位置 -->
        <div class="info-section receiver-info">
          <div class="section-header">下车位置</div>
          <div class="form-content">
            <el-form-item label="详细地址" prop="receiverAddress">
              <el-input
                v-model="form.receiverAddress"
                placeholder="请输入下车详细地址"
                @input="queryReceiverInput"
              />
              <el-autocomplete
                v-if="false"
                v-model="form.receiverAddress"
                :fetch-suggestions="queryReceiverAddress"
                placeholder="请输入下车位置"
                style="width: 100%"
                @select="handleReceiverAddressSelect"
              >
                <template slot-scope="{ item }">
                  <div>{{ item.name }}<span style="color: #909399; font-size: 12px; margin-left: 8px;">{{ item.district }}</span></div>
                </template>
                <el-cascader
                  slot="prepend"
                  v-model="endCity"
                  :options="endCityMap"
                  :props="{ checkStrictly: true }"
                  placeholder="请选择下车位置"
                  style="width: 180px;"
                />
              </el-autocomplete>
              <div v-if="receiverSuggestions.length > 0" class="address-suggestions">
                <div
                  v-for="(item, index) in receiverSuggestions"
                  :key="index"
                  class="suggestion-item"
                  @click="handleReceiverAddressSelect(item)"
                >
                  <div>{{ item.name }}<span>{{ item.fullAddress || item.district }}</span></div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 乘客信息 -->
        <div class="info-section package-info">
          <div class="section-header">乘客信息</div>
          <div class="form-content">
            <el-form-item label="电话号码" prop="passengerPhone">
              <el-input v-model="form.passengerPhone" placeholder="请输入乘客电话号码" />
            </el-form-item>
            <el-form-item label="司机留言" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入司机留言"
              />
              <div class="quick-tags">
                <el-tag
                  v-for="item in quickTags"
                  :key="item"
                  type="info"
                  size="small"
                  effect="plain"
                  @click="handleTag(item)"
                >
                  {{ item }}
                </el-tag>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 出行信息 -->
        <div class="info-section cost-info">
          <div class="section-header">出行信息</div>
          <div class="form-content">
            <el-form-item label="出行时间" prop="start_time">
              <el-radio-group v-model="startTimeType">
                <el-radio label="1">立即出行</el-radio>
                <el-radio label="2">预约出行</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="startTimeType === '2'">
              <el-form-item label="预约时间" prop="appointmentTime">
                <el-date-picker
                  v-model="form.appointmentTime"
                  type="datetime"
                  placeholder="请选择预约时间"
                  style="width: 220px;"
                />
              </el-form-item>
            </div>
            <el-form-item label="包车价格" prop="price">
              <div class="price-control">
                <el-input-number
                  v-model="form.price"
                  size="medium"
                  :min="0.00"
                  :precision="2"
                  placeholder="请输入包车价格"
                  controls-position="right"
                />
                <span class="price-unit">元</span>
              </div>
            </el-form-item>
          </div>
          <div class="form-cost">
            <div class="cost-item">
              <span class="cost-label">包车价格</span>
              <span class="cost-value">￥{{ form.price || '--' }}</span>
            </div>
          </div>
          <!-- 提交按钮 -->
          <div class="submit-section">
            <el-button type="primary" :loading="submitting" @click="submitOrder">代下单</el-button>
          </div>
        </div>

      </div>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
import { createProxyOrders } from '@/api/business'
export default {
  data() {
    return {
      startTimeType: '1',
      startTimeTypeMap: [
        {
          label: '立即出行',
          value: '1'
        },
        {
          label: '预约出行',
          value: '2'
        }
      ],
      form: {
        start_time: moment().format('YYYY-MM-DD HH:mm'),
        price: 0.00,
        senderAddress: '',
        receiverAddress: '',
        passengerPhone: '',
        remark: '',
        // 添加经纬度字段
        senderLocation: {
          lng: null,
          lat: null,
          name: null,
          address_code: null
        },
        receiverLocation: {
          lng: null,
          lat: null,
          name: null,
          address_code: null
        }
      },
      quickTags: ['带行李', '多人出行', '需要儿童座椅', '老人出行', '接机', '送机'],
      submitting: false,
      rules: {
        start_time: [
          { required: true, message: '请选择出行时间', trigger: 'blur' }
        ],
        senderAddress: [
          { required: true, message: '请输入上车位置', trigger: 'blur' }
        ],
        receiverAddress: [
          { required: true, message: '请输入下车位置', trigger: 'blur' }
        ],
        passengerPhone: [
          { required: true, message: '请输入乘客电话号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入包车价格', trigger: 'blur' }
        ]
      },
      senderSuggestions: [],
      receiverSuggestions: []
    }
  },
  mounted() {
  },
  methods: {
    handleTag(tag) {
      this.form.remark += tag + ' '
    },
    querySenderInput(queryString) {
      if (queryString && queryString.length > 0) {
        this.searchAddressSender(queryString)
      } else {
        this.senderSuggestions = []
      }
    },
    queryReceiverInput(queryString) {
      if (queryString && queryString.length > 0) {
        this.searchAddressReceiver(queryString)
      } else {
        this.receiverSuggestions = []
      }
    },
    searchAddressSender(keyword) {
      const key = 'ae07d204562de06e95f7ed805770b18f'
      const url = `https://restapi.amap.com/v3/assistant/inputtips?platform=JS&s=rsv3&logversion=2.0&key=${key}&sdkversion=*******&appname=https%253A%252F%252Fc.higgses.com%252Fpassenger_index%252Fcallback%252FMFC221309746013AC554571FBD180E1C8H3593&csid=9E898BB1-C1B9-421D-B941-C81CA766E976&jscode=8e41824d9d53ba41aeaa5033857279a9&type=&citylimit=false&datatype=all&language=zh_cn&s=rsv3&key=${key}&keywords=${encodeURIComponent(keyword)}`

      fetch(url)
        .then(response => response.json())
        .then(data => {
          if (data.status === '1' && data.tips && data.tips.length > 0) {
            const suggestions = data.tips
              .filter(poi => {
                // 只保留有location且格式正确的结果
                return poi.location &&
                  typeof poi.location === 'string' &&
                  poi.location.includes(',') &&
                  poi.location.split(',').length === 2 &&
                  !isNaN(parseFloat(poi.location.split(',')[0])) &&
                  !isNaN(parseFloat(poi.location.split(',')[1]))
              })
              .map(poi => {
                return {
                  name: poi.name || '',
                  address: poi.address || '',
                  district: poi.district || '',
                  location: poi.location || '',
                  adcode: poi.adcode || '',
                  address_code: poi.adcode || '',
                  fullAddress: `${poi.province || ''}${poi.city || ''}${poi.district || ''}`
                }
              })
            this.senderSuggestions = suggestions
          } else {
            this.senderSuggestions = []
          }
        })
        .catch(error => {
          console.error('地址搜索失败:', error)
          this.senderSuggestions = []
        })
    },
    searchAddressReceiver(keyword) {
      const key = 'ae07d204562de06e95f7ed805770b18f'
      const url = `https://restapi.amap.com/v3/assistant/inputtips?platform=JS&s=rsv3&logversion=2.0&key=${key}&sdkversion=*******&appname=https%253A%252F%252Fc.higgses.com%252Fpassenger_index%252Fcallback%252FMFC221309746013AC554571FBD180E1C8H3593&csid=9E898BB1-C1B9-421D-B941-C81CA766E976&jscode=8e41824d9d53ba41aeaa5033857279a9&type=&citylimit=false&datatype=all&language=zh_cn&s=rsv3&key=${key}&keywords=${encodeURIComponent(keyword)}`

      fetch(url)
        .then(response => response.json())
        .then(data => {
          if (data.status === '1' && data.tips && data.tips.length > 0) {
            const suggestions = data.tips
              .filter(poi => {
                // 只保留有location且格式正确的结果
                return poi.location &&
                  typeof poi.location === 'string' &&
                  poi.location.includes(',') &&
                  poi.location.split(',').length === 2 &&
                  !isNaN(parseFloat(poi.location.split(',')[0])) &&
                  !isNaN(parseFloat(poi.location.split(',')[1]))
              })
              .map(poi => {
                return {
                  name: poi.name || '',
                  address: poi.address || '',
                  district: poi.district || '',
                  location: poi.location || '',
                  adcode: poi.adcode || '',
                  address_code: poi.adcode || '',
                  fullAddress: `${poi.province || ''}${poi.city || ''}${poi.district || ''}`
                }
              })
            this.receiverSuggestions = suggestions
          } else {
            this.receiverSuggestions = []
          }
        })
        .catch(error => {
          console.error('地址搜索失败:', error)
          this.receiverSuggestions = []
        })
    },
    handleSenderAddressSelect(item) {
      if (!item || !item.name) {
        return
      }

      this.form.senderAddress = `${item.name}`

      // 更严格地检查location并确保它是有效的字符串
      if (item.location && typeof item.location === 'string' && item.location.includes(',')) {
        const location = item.location.split(',')
        if (location.length === 2 && !isNaN(parseFloat(location[0])) && !isNaN(parseFloat(location[1]))) {
          this.form.senderLocation = {
            lng: parseFloat(location[0]),
            lat: parseFloat(location[1]),
            name: item.name,
            address_code: item.adcode || ''
          }
        } else {
          // 无效的经纬度格式
          this.form.senderLocation = {
            lng: null,
            lat: null,
            name: item.name,
            address_code: item.adcode || ''
          }
          console.warn('上车位置经纬度格式无效:', item.location)
        }
      } else {
        // 如果没有location，设置一个默认值
        this.form.senderLocation = {
          lng: null,
          lat: null,
          name: item.name,
          address_code: item.adcode || ''
        }
        console.warn('上车位置没有经纬度信息')
      }

      this.senderSuggestions = []
    },
    handleReceiverAddressSelect(item) {
      if (!item || !item.name) {
        return
      }

      this.form.receiverAddress = `${item.name}`

      // 更严格地检查location并确保它是有效的字符串
      if (item.location && typeof item.location === 'string' && item.location.includes(',')) {
        const location = item.location.split(',')
        if (location.length === 2 && !isNaN(parseFloat(location[0])) && !isNaN(parseFloat(location[1]))) {
          this.form.receiverLocation = {
            lng: parseFloat(location[0]),
            lat: parseFloat(location[1]),
            name: item.name,
            address_code: item.adcode || ''
          }
        } else {
          // 无效的经纬度格式
          this.form.receiverLocation = {
            lng: null,
            lat: null,
            name: item.name,
            address_code: item.adcode || ''
          }
          console.warn('下车位置经纬度格式无效:', item.location)
        }
      } else {
        // 如果没有location，设置一个默认值
        this.form.receiverLocation = {
          lng: null,
          lat: null,
          name: item.name,
          address_code: item.adcode || ''
        }
        console.warn('下车位置没有经纬度信息')
      }

      this.receiverSuggestions = []
    },
    submitOrder() {
      if (this.form.price === 0) {
        this.$message.error('请设置包车价格')
        return
      }

      // 检查经纬度是否有效
      const hasValidStartLocation = this.form.senderLocation &&
        !isNaN(parseFloat(this.form.senderLocation.lng)) &&
        !isNaN(parseFloat(this.form.senderLocation.lat))

      const hasValidEndLocation = this.form.receiverLocation &&
        !isNaN(parseFloat(this.form.receiverLocation.lng)) &&
        !isNaN(parseFloat(this.form.receiverLocation.lat))

      if (!hasValidStartLocation) {
        this.$message.error('上车位置的地理坐标无效，请重新选择上车位置')
        return
      }

      if (!hasValidEndLocation) {
        this.$message.error('下车位置的地理坐标无效，请重新选择下车位置')
        return
      }

      this.$refs.dataForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true

          const orderData = {
            start_time: this.startTimeType === '1' ? moment().format('YYYY-MM-DD HH:mm') : moment(this.form.appointmentTime).format('YYYY-MM-DD HH:mm'),
            reseverd_phone: this.form.passengerPhone,
            reseverd_info: this.form.remark,
            start_longitude: this.form.senderLocation.lng,
            start_latitude: this.form.senderLocation.lat,
            end_longitude: this.form.receiverLocation.lng,
            end_latitude: this.form.receiverLocation.lat,
            start_address_code: this.form.senderLocation.address_code,
            end_address_code: this.form.receiverLocation.address_code,
            start_address_remark: this.form.senderLocation.name,
            end_address_remark: this.form.receiverLocation.name,
            custom_price: this.form.price,
            is_custom: 1
          }
          const res = await createProxyOrders(orderData, 'baoche').catch(() => {
            this.submitting = false
          })
          if (res.code === 200) {
            this.$message.success('订单提交成功!')
          } else {
            this.$message.error('订单提交失败!')
          }
          this.submitting = false
          this.$refs.dataForm.resetFields()
          this.form.senderLocation = { lng: null, lat: null, name: null, address_code: null }
          this.form.receiverLocation = { lng: null, lat: null, name: null, address_code: null }

          this.$router.push('/orderManage/baocheManage')
        } else {
          this.$message.error('请完善表单信息')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.express-delivery-container {
  padding: 20px;
  background-color: #f5f7fa;

  .express-form-wrapper {
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;

    .section-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #409EFF;
      display: flex;
      align-items: center;

      i {
        margin-right: 10px;
      }
    }

    .info-section {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      height: 100%;

      .section-header {
        padding: 12px 20px;
        font-size: 16px;
        font-weight: bold;
        background-color: #f2f6fc;
        border-bottom: 1px solid #ebeef5;
      }

      .form-content {
        padding: 20px;
      }

      &.sender-info {
        width: 49%;
        border-top: 3px solid #67c23a;
        margin-bottom: 2%;
      }

      &.receiver-info {
        width: 49%;
        border-top: 3px solid #409eff;
        margin-bottom: 1%;
      }

      &.package-info {
        height: 280px;
        width: 49%;
        border-top: 3px solid #e6a23c;
      }

      &.cost-info {
        position: relative;
        height: 280px;
        width: 49%;
        border-top: 3px solid #f56c6c;

        .cost-item {
          display: flex;
          align-items: center;

          .cost-label {
            font-size: 14px;
            color: #606266;
            font-weight: bold;
          }

          .cost-value {
            font-size: 24px;
            color: #f56c6c;
            font-weight: bold;
            margin-left: 40px;
          }
        }
      }
    }

    .weight-control, .price-control {
      display: flex;
      align-items: center;

      .el-input {
        margin: 0 10px;
        width: 120px;

        ::v-deep .el-input__inner {
          text-align: center;
        }
      }

      .price-unit {
        margin-left: 10px;
        color: #606266;
      }
    }

    .quick-tags {
      margin-top: 10px;

      .el-tag {
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .submit-section {
      position: absolute;
      bottom: 20px;
      right: 20px;

      .el-button {
        padding: 12px 40px;
        font-size: 16px;
      }
    }
  }
}

.form-cost {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: #fff;
}

.address-suggestions {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 2000;

  .suggestion-item {
    padding: 8px 10px;
    cursor: pointer;
    line-height: 1.5;

    &:hover {
      background-color: #f5f7fa;
    }

    span {
      display: inline-block;
      color: #909399;
      font-size: 12px;
      margin-left: 8px;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

/* 确保级联选择器下拉菜单完整显示 */
::v-deep .el-cascader-menus {
  z-index: 3000 !important; /* 提高级联菜单的层级 */
  min-width: 220px !important; /* 确保菜单宽度足够 */
}

::v-deep .el-cascader-menu__item {
  padding: 8px 20px 8px 15px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 修复日期选择器样式 */
::v-deep .el-date-editor.el-input {
  width: 220px !important;
}

::v-deep .el-date-editor .el-input__inner {
  padding-left: 30px;
  font-size: 13px;
}
</style>

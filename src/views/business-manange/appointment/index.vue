<template>
  <div>
    <div v-loading="isLoading" class="app-container" element-loading-text="查询中...">
      <div class="hanldle-container">
        <div class="filter-container">
          <h5 class="warp-title">客户信息</h5>
          <el-input
            v-model="userMobile" clearable size="small" class="filter-item mini-condition"
            placeholder="乘客电话/呼入电话"
          />
          <el-button size="small" type="primary" @click="fetchOrders">查询</el-button>
          <el-input
            v-model="reseverd_username" clearable size="small" class="filter-item mini-condition"
            placeholder="客户姓名"
          />
          <el-button size="small" icon="el-icon-document-copy" @click="nameCopy">复制</el-button>
        </div>
        <div class="filter-container">
          <el-button size="small" icon="el-icon-warning-outline" type="warning" disabled>客户投诉</el-button>
        </div>
      </div>
      <div>
        <el-descriptions v-if="userInfo" size="medium" :column="7" :content-style="{ color: '#0067e1' }">
          <el-descriptions-item label="用户等级">{{
            list && list.length > 0 ? "普通用户" : "未注册用户"
          }}</el-descriptions-item>
          <el-descriptions-item label="订单总数">{{
            userInfo.total_orders
          }}</el-descriptions-item>
          <el-descriptions-item label="完成数">{{
            userInfo.total_completed_orders
          }}</el-descriptions-item>
          <el-descriptions-item label="取消">{{
            userInfo.total_canceled_orders
          }}</el-descriptions-item>
          <el-descriptions-item label="电话归属地">{{
            userInfo.mobile_belongto
          }}</el-descriptions-item>
        </el-descriptions>

        <el-collapse v-model="collapse" accordion>
          <el-collapse-item name="table">
            <template slot="title">
              <div class="collapse-title">
                <div class="left">
                  历史订单<i class="header-icon el-icon-tickets" />
                </div>
                <el-button
                  type="text" style="color: #606266" :icon="collapse === 'table'
                    ? 'el-icon-arrow-up'
                    : 'el-icon-arrow-down'
                  "
                >{{ collapse === "table" ? "收起" : "展开" }}</el-button>
              </div>
            </template>
            <template slot="icon">
              <div class="collapse-title">
                历史订单<i class="header-icon el-icon-tickets" />
              </div>
            </template>
            <el-table
              ref="multipleTableRef" stripe height="260px" :data="list"
              fit highlight-current-row
              :header-cell-style="{ 'text-align': 'center' }"
              :cell-style="{ 'text-align': 'center', padding: '8px 0' }"
            >
              <el-table-column label="订单编号" prop="order_no" width="160" show-overflow-tooltip />
              <el-table-column label="出发时间" prop="completed_at" width="180">
                <template slot-scope="scope">
                  {{ scope.row.start_time || "/" }}
                </template>
              </el-table-column>
              <el-table-column label="订单类型" size="small" width="100">
                <template slot-scope="scope">
                  <el-tag size="small" type="primary" class="order-type-tag">
                    {{ getOrderTypeText(scope.row) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="人数/票数" size="small" prop="book_seating" width="100" />
              <el-table-column label="联系电话" size="small" prop="reseverd_phone" width="130" />
              <el-table-column label="所属路线" size="small" width="240">
                <template slot-scope="scope">
                  {{
                    (scope.row.line_class_train &&
                      scope.row.start_gb_region &&
                      (!['市辖区', '县'].includes(scope.row.start_gb_region.name)
                        ? scope.row.start_gb_region.name
                        : (scope.row.start_gb_region.city && scope.row.start_gb_region.city.name))) +
                      "|" +
                      (scope.row.line_class_train && scope.row.line_class_train.line_class && scope.row.line_class_train.line_class.start_name) ||
                      "/"
                  }}
                  <i class="el-icon-right" />
                  {{
                    (scope.row.line_class_train &&
                      scope.row.end_gb_region &&
                      (!['市辖区', '县'].includes(scope.row.end_gb_region.name)
                        ? scope.row.end_gb_region.name
                        : (scope.row.end_gb_region.city && scope.row.end_gb_region.city.name))) +
                      "|" +
                      (scope.row.line_class_train && scope.row.line_class_train.line_class && scope.row.line_class_train.line_class.end_name) ||
                      "/"
                  }}
                </template>
              </el-table-column>
              <el-table-column label="出发地/上车点" size="small" prop="start_address_remark" width="180">
                <template slot-scope="scope">
                  {{ scope.row.start_address_remark || "/" }}
                </template>
              </el-table-column>
              <el-table-column label="目的地/下车点" size="small" prop="end_address_remark" width="180">
                <template slot-scope="scope">
                  {{ scope.row.end_address_remark || "/" }}
                </template>
              </el-table-column>
              <el-table-column label="订单金额(元)" size="small" prop="real_price" width="100" />
              <el-table-column label="所属分台" size="small" prop="branch.mchname" width="200" />
              <el-table-column label="接单车辆" size="small" prop="driver.car_tail_number" width="140">
                <template slot-scope="scope">
                  {{
                    (scope.row.driver && scope.row.driver.car_tail_number) ||
                      "/"
                  }}
                </template>
              </el-table-column>
              <el-table-column label="司机信息" size="small" width="140">
                <template slot-scope="scope">
                  <p>
                    {{
                      (scope.row.driver && (scope.row.driver.nick_name || scope.row.driver.name)) || "/"
                    }}
                  </p>
                  <p>
                    {{
                      (scope.row.driver && scope.row.driver.cellphone) || "/"
                    }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="状态" size="small" width="100">
                <template slot-scope="scope">
                  <Tablestatus v-if="scope.row.state === 6" mode="success" word="已完成" />
                  <Tablestatus v-else-if="scope.row.state === 7" mode="default" word="已取消" />
                  <Tablestatus v-else-if="scope.row.state === 8" mode="error" word="已关闭" />
                  <Tablestatus v-else mode="processing" word="进行中" />
                </template>
              </el-table-column>
              <el-table-column label="购票信息" size="small" width="140">
                <template slot-scope="scope">
                  <p>
                    {{
                      (scope.row.passenger && (scope.row.passenger.user_name || scope.row.passenger.name)) ||
                        "/"
                    }}
                  </p>
                  <p>
                    {{
                      (scope.row.passenger && scope.row.passenger.cellphone) ||
                        "/"
                    }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="代约名称/渠道代约" size="small" width="140">
                <template slot-scope="scope">
                  <p>
                    {{
                      (scope.row.temp_apply_branch &&
                        scope.row.temp_apply_branch.mchname) ||
                        "/"
                    }}
                  </p>
                  <p>
                    {{
                      (scope.row.temp_apply_branch &&
                        scope.row.temp_apply_branch.cellphone) ||
                        "/"
                    }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="下单时间" prop="create_time" width="190" />
              <el-table-column fixed="right" label="操作" width="100">
                <template v-slot="scope">
                  <!-- <el-button
                    type="text"
                    size="small"
                    @click="preView(scope.row)"
                    >详情</el-button
                  > -->
                  <el-button type="text" size="small" @click="copyOrder(scope.row)">复制</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
        <div class="pagination-container">
          <el-pagination
            background
            :current-page.sync="pagination.current"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <!-- <div style="margin-top: 20px"> -->
      <!-- <el-dialog :visible.sync="dialogDes" title="发票详情">
        <Description :description="description" />
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="dialogDes = false"
              >确认</el-button
            >
          </span>
        </template>
      </el-dialog> -->
    </div>
    <div class="app-container warp-container">
      <div class="hanldle-container">
        <div class="filter-container">
          <h5 class="warp-title">新增订单</h5>
          <el-radio-group v-model="type" size="medium">
            <el-radio-button v-for="item in navList" :key="item.value" :disabled="!item.allow" :label="item.label" :value="item.value" />
          </el-radio-group>
        </div>
      </div>
      <div class="warp-type-container">
        <div v-if="type === '定制班线车'" class="warp-form">
          <Trains ref="trains" :params="params" />
        </div>
        <div v-if="type === '带货'" class="warp-form">
          <Goods ref="goods" :params="params" />
        </div>
        <div v-if="type === '包车'" class="warp-form">
          <CharteredCars ref="car" :params="params" />
        </div>
        <!-- <div class="warp-map">
          <MapContainer />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { operationOrders } from '@/api/business.js'
// import MapContainer from './components/MapContainer'
import Trains from './components/Trains'
import Goods from './components/Goods'
import CharteredCars from './components/CharteredCars'
export default {
  name: 'Appointment',
  components: { Trains, Goods, CharteredCars },
  data() {
    return {
      collapse: '',
      type: '定制班线车',
      reseverd_username: '',
      userMobile: '',
      userInfo: '',
      isLoading: false,

      searchQuery: {},
      loading: false,
      list: null,
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },

      description: '',
      params: {},
      navList: [
        {
          label: '拼车'
        },
        {
          label: '包车',
          allow: true
        },
        {
          label: '快车'
        },
        {
          label: '快车-摆渡车'
        },
        {
          label: '定制班线车',
          allow: true
        },
        {
          label: '顺风车'
        },
        {
          label: '租车'
        },
        {
          label: '出租车'
        },
        {
          label: '带货',
          allow: true
        },
        {
          label: '代办'
        },
        {
          label: '代驾'
        }
      ]
    }
  },
  methods: {
    // 获取订单类型文本，过滤掉不需要的字符
    getOrderTypeText(row) {
      if (row.type === 7) {
        return [1, 2].includes(row.ferry_type) ? '摆渡车' : '快车'
      }
      return row.order_type_string ? row.order_type_string.replace(/[.·]/g, '') : ''
    },
    async fetchOrders() {
      if (!this.userMobile) {
        this.$message('请输入用户手机号码')
        return
      }
      this.isLoading = true
      this.collapse = 'table'
      try {
        const response = await operationOrders({
          mobile: this.userMobile,
          type: 5,
          page: this.pagination.current,
          size: this.pagination.size
        })

        const { data, meta } = response.data

        // 检查并处理数据结构
        console.log('API返回数据:', data)

        // 正确处理返回的数据结构
        if (data && Array.isArray(data.data)) {
          // 新版API返回的结构
          this.list = data.data
        } else if (data && Array.isArray(data)) {
          // 旧版API直接返回数组的情况
          this.list = data
        } else {
          this.list = []
        }

        // 从meta.pagination中获取分页信息
        if (meta && meta.pagination) {
          this.pagination.total = meta.pagination.total || 0
          this.pagination.current = meta.pagination.current_page || 1
          this.pagination.size = parseInt(meta.pagination.per_page) || 10
        } else {
          this.pagination.total = meta.total_orders || 0
        }

        this.userInfo = meta || {}
        this.reseverd_username = meta.reseverd_username || ''
      } catch (error) {
        console.error('获取订单列表失败:', error)
        this.$message.error('获取订单列表失败')
        this.list = []
      } finally {
        this.isLoading = false
      }
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.fetchOrders()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.fetchOrders()
    },
    copyOrder(item) {
      this.params = item
      // 添加延时确保数据已更新后再调用setOrder
      this.$nextTick(() => {
        if (this.$refs['trains']) {
          // 确保传递足够的订单信息到Trains组件
          this.$refs['trains'].setOrder({
            userMobile: item.passenger ? item.passenger.cellphone : (item.reseverd_phone || ''),
            reseverd_username: item.passenger ? item.passenger.user_name : (item.reseverd_username || ''),
            custom_price: item.real_price || '',
            number: item.book_seating || 1,
            reseverd_info: item.reseverd_info || ''
          })
        }
      })
    },
    nameCopy() {
      if (this.$refs['trains']) {
        this.$refs['trains'].setOrder({
          userMobile: this.userMobile,
          reseverd_username: this.reseverd_username
        })
      } else {
        this.$message.error('组件未正确加载，请稍后再试')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.warp-title {
  font-size: 16px;
  font-weight: 700;
  position: relative;
  margin-left: 10px;

  &:before {
    width: 4px;
    border-radius: 5px;
    height: 80%;
    background: #0067e1;
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    margin: auto;
    content: "";
    display: block;
  }

  margin-right: 20px;
}

.collapse-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .left {
    font-size: 14px;
    font-weight: bold;
  }

  i {
    margin-left: 5px;
    font-size: 15px;
  }
}

.warp-container {
  margin-top: 20px;

  .warp-type-container {
    display: flex;

    .warp-form,
    .warp-map {
      flex: 1;
    }

    .warp-map {
      margin-left: 20px;
    }
  }
}

.tableTitle {
  width: 160px;
  padding: 8px;
}

.tableContent {
  width: 400px;
}

#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  text-align: right;
}

table {
  margin: 0 auto;
  border: 1px solid #000000;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid #000000;
  text-align: center;
}

.passInput {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}

.option {
  margin-right: 4px;
  margin-left: 0;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.order-type-tag {
  /* 防止类型后面有额外字符 */
  &::after {
    display: none !important;
  }
}
</style>

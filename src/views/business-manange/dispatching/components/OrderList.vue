<template>
  <div class="order-container" :style="customStyle">
    <div class="top-warp">
      <h5 class="warp-title">
        订单列表<el-tag
          v-if="timeoutCount > 0" type="danger" class="crosstime" size="mini"
          @click="fetchOutTimeOrder"
        >超时待派订单({{ timeoutCount }}单)</el-tag>
        <el-tooltip
          v-if="timeoutCount > 0" class="item" :content="`[超时待派订单]:出发时间在${nowTime}之前的待指派订单`"
          placement="top-start"
        >
          <i class="el-icon-question" />
        </el-tooltip>
        <!-- 添加返回按钮，只在超时待派状态下显示 -->
        <el-button
          v-if="searchQuery.is_timeout"
          type="text"
          size="mini"
          icon="el-icon-back"
          class="back-to-normal"
          @click="backToNormalList"
        >
          返回待派单列表
        </el-button>
      </h5>
      <!-- <hamburger
        :is-active="orderOpened"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      /> -->
    </div>
    <div class="search-order-list">
      <!-- 删除搜索区域 -->
      <div class="list-warp-box">
        <!-- <el-radio-group v-model="searchQuery.biz" size="mini" @change="handleSearch">
          <el-radio-button label="undispatched">待派单 ({{ meta && meta.total_undispatched_orders || 0 }})
          </el-radio-button>
          <el-radio-button label="dispatched1" disabled>自动调度池(0)</el-radio-button>
          <el-radio-button label="dispatched">已派单({{ meta && meta.total_dispatched_orders || 0 }})</el-radio-button>
        </el-radio-group> -->
        <el-tabs v-model="searchQuery.biz" type="card" @tab-click="handleSearch">
          <el-tab-pane name="undispatched">
            <template slot="label">
              <div class="tab-label">
                <span>待派单</span>
                <el-tag v-if="meta && meta.total_undispatched_orders" size="mini" type="danger" class="count-tag">
                  {{ meta.total_undispatched_orders }}</el-tag>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane disabled name="dispatched1">
            <template slot="label">
              <div class="tab-label">
                <span>自动调度池</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane name="dispatched">
            <template slot="label">
              <div class="tab-label">
                <span>已派未完</span>
                <el-tag v-if="meta && meta.total_dispatched_orders" size="mini" type="info" class="count-tag">
                  {{ meta.total_dispatched_orders }}</el-tag>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
        <!-- 添加固定高度容器包裹时间选择区域 -->
        <div
          v-if="query.biz === 'undispatched' && !searchQuery.is_timeout && orderCountsByTime && Object.keys(orderCountsByTime).length > 0"
          class="time-slots-container"
        >
          <el-tag
            v-for="(count, time) in orderCountsByTime" :key="time" class="time-slot-tag"
            :type="selectedTimes.includes(time) ? 'primary' : 'info'" @click="$emit('time-slot-click', time)"
          >
            {{ time }} ({{ count }})
          </el-tag>
          <el-button
            v-if="selectedTimes.length > 0" type="text" size="mini"
            @click="$emit('time-slot-click', null)"
          >
            清空时刻
          </el-button>
        </div>

        <!-- 添加固定高度容器包裹快速时间选择区域 -->
        <div
          v-if="['undispatched', 'dispatched'].includes(searchQuery.biz) && searchQuery.order_type.includes(5)"
          class="quick-time"
        >
          <el-tag
            v-for="(item, i) in shortcutsTime" :key="i" size="mini"
            :effect="handleSelectShort.includes(item.time) ? 'dark' : 'plain'" style="cursor: pointer"
            :type="handleSelectShort.includes(item.time) ? '' : 'info'" @click="command(item.time)"
          >{{ item.time }}（{{
            item.total
          }}）</el-tag>
        </div>

        <el-table
          ref="orderMultiple" stripe class="order-table flex-table" :data="order"
          fit highlight-current-row
          :row-class-name="tableRowClassName" :cell-style="{ padding: '8px 0' }"
          size="small" :header-row-style="{height: '43px'}"
          :header-cell-style="{height: '43px'}"
          @row-click="rowClick"
        >
          <!-- <el-table-column
            type="selection"
            width="55"
          /> -->
          <el-table-column label="序号" width="60" align="center" class-name="el-table-column--selection">
            <div slot="header" class="custom-header">
              <span style="font-size: 12px; margin-right: 3px">全选</span>
              <el-checkbox v-model="checkedAll" :indeterminate="isIndeterminate" @change="handleCheckedAll" />
            </div>
            <template slot-scope="scope">
              <sup v-if="scope.row.is_temp === 1" class="temp_order">代</sup>
              <span style="margin-right: 5px; margin-left: 10px">{{
                scope.$index + 1
              }}</span>
              <el-checkbox :value="order[scope.$index].checked" @change="(_) => checkboxChange(scope.$index, _)" />
            </template>
          </el-table-column>
          <el-table-column align="left" label="出发时间/路线" size="small" min-width="230">
            <template slot-scope="scope">
              <el-tooltip placement="top">
                <div slot="content">
                  <template v-if="scope.row.type !== 4">
                    {{ scope.row | filterStart }}
                    <i class="el-icon-right" />
                    {{ scope.row | filterEnd }}
                    <p class="table-p">
                      上车点：{{ scope.row.start_address_remark || "-" }}
                    </p>
                    <p class="table-p">
                      下车点： {{ scope.row.end_address_remark || "-" }}
                    </p>
                  </template>
                  <p v-if="scope.row.reseverd_info" class="table-p">
                    备注：{{ scope.row.reseverd_info }}
                  </p>
                </div>
                <div>
                  <p class="table-p">
                    {{ scope.row.start_time.slice(0, 16) || "/" }}
                    <template v-if="scope.row.type !== 4">
                      <span style="margin: 0 5px;">|</span>
                      {{ scope.row | filSname }}
                      <i class="el-icon-right" />
                      {{ scope.row | filEname }}
                    </template>
                  </p>
                  <p v-if="scope.row.type === 4" class="table-p">
                    {{ scope.row && scope.row.agency && scope.row.agency.name }}
                  </p>
                  <template v-else>
                    <!-- 所有业态都显示上下车点 -->
                    <p class="table-p space-line small-text">
                      <span class="badge-status-start" /> 上车点：{{ scope.row.start_address_remark || "-" }}
                    </p>
                    <p class="table-p space-line small-text">
                      <span class="badge-status-end" /> 下车点：{{ scope.row.end_address_remark || "-" }}
                    </p>
                  </template>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="人数/金额" align="left" size="small" width="120">
            <template slot-scope="scope">
              <el-tag size="mini" style="transform: scale(0.85); margin: 0 0 0 0" effect="dark">{{
                scope.row.type === 7
                  ? [1, 2].includes(scope.row.ferry_type)
                    ? "摆渡车"
                    : "快车"
                  : scope.row.order_type_string
              }}</el-tag>/ {{ scope.row | formatSeat }}
              <p>
                <span
                  :class="scope.row.is_pay > 0 || scope.row.is_pre_pay > 0 ? 'surplus'
                    : 'nosurplus'
                  "
                >￥{{ scope.row.real_price }}</span>
                <el-tag
                  v-if="scope.row.is_pay === 1 || scope.row.is_pre_pay === 1" size="mini" hit
                  style="transform: scale(0.85); margin: 0 0 0 0" type="success"
                >已支付</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="订单编号" size="small" align="center" show-overflow-tooltip width="120">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click.stop="preView(scope.row.order_id)">{{ scope.row.order_no
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="联系电话" size="small" align="center" prop="reseverd_phone" width="120" />
          <el-table-column label="乘客备注" size="small" align="left" width="120">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.reseverd_info || ''" placement="top">
                <span class="readonly-text">
                  {{ (scope.row.reseverd_info || '-').slice(0, 8) }}{{ scope.row.reseverd_info && scope.row.reseverd_info.length > 8 ? '...' : '' }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="航班号" size="small" align="left" width="120">
            <template slot-scope="scope">
              <span
                v-if="!scope.row.editing_ft_number"
                class="editable-text"
                @click="startEditing(scope.row, 'ft_number')"
              >
                {{ scope.row.ft_number || '-' }}
              </span>
              <el-input
                v-else
                ref="ftNumberInput"
                v-model="scope.row.ft_number"
                size="mini"
                placeholder="请输入航班号"
                @blur="finishEditing(scope.row, 'ft_number', scope.row.order_id, scope.$index)"
                @keyup.enter.native="finishEditing(scope.row, 'ft_number', scope.row.order_id, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="派单备注" size="small" align="left" width="120">
            <template slot-scope="scope">
              <el-tooltip v-if="!scope.row.editing_dispatch_notes" :content="scope.row.dispatch_notes || ''" placement="top">
                <span
                  class="editable-text"
                  @click="startEditing(scope.row, 'dispatch_notes')"
                >
                  {{ (scope.row.dispatch_notes || '-').slice(0, 8) }}{{ scope.row.dispatch_notes && scope.row.dispatch_notes.length > 8 ? '...' : '' }}
                </span>
              </el-tooltip>
              <el-input
                v-else
                ref="dispatchNotesInput"
                v-model="scope.row.dispatch_notes"
                size="mini"
                type="textarea"
                placeholder="请输入派单备注"
                @blur="finishEditing(scope.row, 'dispatch_notes', scope.row.order_id, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="65">
            <template v-slot="scope">
              <el-button
                type="text" size="small" :disabled="selection && Object.keys(selection).length > 0"
                @click.stop="
                  sendOrder(searchQuery.biz, {
                    order_ids: scope.row.order_id,
                    type: scope.row.type,
                    lnglats: [
                      {
                        longitude: scope.row.start_longitude,
                        latitude: scope.row.start_latitude,
                      },
                    ],
                    checkOrderInfo: scope.row,
                  })
                "
              >{{ searchQuery.biz === "undispatched" ? "派单" : "撤销" }}
              </el-button>
              <el-dropdown
                size="mini" :disabled="selection && Object.keys(selection).length > 0"
                @command="(command) => actionClose(command, scope.row.order_id)"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-arrow-down el-icon--right" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="close">关闭订单</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <div class="bottom-container">
          <div class="left-content">
            <el-button class="filter-item" size="mini" @click="cancelSelection">取消选择</el-button>
            <el-button
              class="filter-item" size="mini" :disabled="!selection" type="primary"
              @click="sendOrder(searchQuery.biz, selection)"
            >{{
              searchQuery.biz === "undispatched" ? "批量派单" : "批量撤销"
            }}</el-button>
            <el-button
              v-if="searchQuery.biz === 'dispatched' && selection && Object.keys(selection).length > 0"
              class="filter-item" size="mini" type="primary" plain
              @click="ReaOrder(selection)"
            >批量改派</el-button>
            <p v-if="selection" class="selection">
              <span>已选择 {{ selection.count }} 笔订单</span><span>共计：{{ selection.seat }}人</span>
            </p>
          </div>
          <el-pagination
            v-if="order && order.length > 0"
            small
            background
            :current-page="searchQuery.page || 1"
            :page-size="searchQuery.per_page || 10"
            layout="prev, pager, next"
            :total="meta && meta.pagination && meta.pagination.total || (order ? order.length : 0)"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogDes" title="订单详情" append-to-body width="850px">
      <Description :order="orderDetailId" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="small" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { cancelOrders, closeOrders, fetTimeShort, updateOrderInfo } from '@/api/business.js'
import { formatStartName, formatEndName } from '@/utils/filters'
import Description from './Description'
import moment from 'moment'

export default {
  components: {
    Description
  },
  filters: {
    filterStart(val) {
      return (
        formatStartName(val).start_city +
        ' | ' +
        formatStartName(val).start_name
      )
    },
    filterEnd(val) {
      return formatEndName(val).end_city + ' | ' + formatEndName(val).end_name
    },
    filSname(val) {
      return formatStartName(val).start_name
    },
    filEname(val) {
      return formatEndName(val).end_name
    },
    formatSeat(_) {
      let re_ = '-'
      if ([3, 4].includes(_.type)) {
        re_ = '-'
      } else if (_.type === 5) {
        re_ = _.total_booking_seats + '票'
      } else if (_.type === 2) {
        re_ = _.book_seating === 4 ? '5座' : _.book_seating === 6 ? '7座' : '9座'
      } else {
        re_ = _.book_seating + '人'
      }
      return re_
    }
  },
  props: {
    timeoutCount: {
      type: Number,
      default: 0
    },
    order: {
      type: Array,
      default: () => []
    },
    meta: {
      type: Object,
      default: () => { return {} }
    },
    query: {
      type: Object,
      default: () => { return {} }
    },
    customWidth: {
      type: Number,
      default: 0
    },
    branch_id: {
      default: () => ''
    },
    orderCountsByTime: {
      type: Object,
      default: () => ({})
    },
    selectedTimes: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      handleSelectShort: [],
      driverOpened: true,
      isIndeterminate: false,
      checkedAll: false,
      orderDetailId: '',
      dialogDes: false,
      orderOpened: true,
      selection: null,
      list: [],
      typeOptions: [
        { value: 1, label: '拼车' },
        { value: 2, label: '包车' },
        { value: 3, label: '带货' },
        { value: 4, label: '代办' },
        { value: 5, label: '定制客运' },
        { value: 6, label: '顺风车' },
        { value: 7, label: '快车' },
        { value: 11, label: '出租车' },
        { value: 20, label: '摆渡车' },
        { value: 30, label: '代驾' },
        { value: 40, label: '租车' }
      ],
      searchQuery: {},
      shortcutsTime: [],
      currentDate: moment(moment().startOf('day').valueOf()).format(
        'YYYY-MM-DD'
      ),
      tempEditData: {}
    }
  },
  computed: {
    nowTime() {
      return moment(moment().valueOf()).format('YYYY/MM/DD HH:mm')
    },
    customStyle() {
      return this.customWidth ? `width:${this.customWidth}px;` : 'width:0'
    }
  },
  watch: {
    branch_id: {
      handler(n) {
        if (n) {
          this.handleSearch()
        }
      },
      immediate: true
    },
    query: {
      handler(n) {
        this.searchQuery = n
      },
      immediate: true
    },
    order: {
      handler(n) {
        if (n && n.length > 0) {
          const checkArray = []
          n.map((o) => {
            if (o.checked) {
              checkArray.push(o)
            }
          })
          if (checkArray.length > 0) {
            const no_latlng = checkArray.find((o_) => {
              return o_.start_longitude === 0 || !o_.start_longitude
            })
            if (no_latlng) {
              this.$message(
                '当前选择的订单中包含未填写上车位置订单，请确认后派单'
              )
            }
            this.selection = {
              is_drivers: checkArray.every((o) => o.driver_id),
              count: checkArray.length,
              lnglats: checkArray.map((item) => {
                return {
                  longitude: item.start_longitude,
                  latitude: item.start_latitude
                }
              }),
              order_ids: checkArray.map((item) => item.order_id).join(','),
              seat: checkArray
                .map((item) => (item.book_seating ? item.book_seating : 0))
                .reduce((a, b) => {
                  return a + b
                }),
              driver_id: checkArray.length > 0 && checkArray[0].driver_id ? checkArray[0].driver_id : '',
              checkOrderInfo: checkArray.length > 0 ? checkArray[0] : null
            }
            if (checkArray.length === n.length) {
              this.checkedAll = true
              this.isIndeterminate = false
            } else {
              this.isIndeterminate = true
            }
          } else {
            this.selection = null
            this.isIndeterminate = false
            this.checkedAll = false
          }
          this.$emit('setPoints', this.selection)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    //   this.shortcutsTime = shortcutsTime()
  },
  methods: {
    changeDate(date) {
      this.searchQuery.start_time = moment(date).format('YYYY-MM-DD')
      this.shortcutsTime = []
      this.handleSearch()
    },
    typeChange(val, type) {
      if (val) {
        this.searchQuery.order_type.push(type)
      } else {
        this.searchQuery.order_type.splice(
          this.searchQuery.order_type.indexOf(type),
          1
        )
      }
      if (this.searchQuery.order_type.length === 11) {
        this.typeAll = true
        this.typeIndeterminate = false
        this.typeOptions.map((o) => {
          o['checked'] = false
          return o
        })
        this.searchQuery.order_type = [3, 2, 1, 4, 5, 6, 7, 11, 20, 30, 40]
      } else if (this.searchQuery.order_type.length === 0) {
        this.typeAll = false
        this.typeIndeterminate = false
      } else {
        this.typeAll = false
        this.typeIndeterminate = true
      }
      this.handleSearch()
    },
    checkedAllType(val) {
      this.typeIndeterminate = false
      this.typeOptions.map((o) => {
        o['checked'] = false
        return o
      })
      // this.searchQuery.order_type = [];
      this.searchQuery.order_type = val
        ? [3, 2, 1, 4, 5, 6, 7, 11, 20, 30, 40]
        : []
      this.handleSearch()
    },
    handleCheckedAll(val) {
      this.$emit('setOrder', -1, val)
    },
    checkboxChange(index, checked) {
      this.$emit('setOrder', index)
    },
    preView(id) {
      this.orderDetailId = id.toString()
      this.dialogDes = true
    },
    actionClose(_, order_id) {
      this.$confirm(`确认关闭当前订单？关闭后无法恢复，请谨慎操作`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        closeOrders({
          order: order_id
        }).then(() => {
          this.$notify({
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
          this.getOrderList()
        })
      })
    },
    fetchOutTimeOrder() {
      // 清空时间相关数据
      this.handleSelectShort = []
      this.searchQuery = {
        ...this.searchQuery,
        page: 1,
        per_page: 10,
        start_time: '',
        search_key: 'passenger_phone',
        biz: 'undispatched',
        is_timeout: 1,
        time: [] // 清空时间筛选
      }
      this.handleSearch()
    },
    // 添加返回普通待派单列表的方法
    backToNormalList() {
      // 重置所有筛选条件
      this.handleSelectShort = []

      // 完全参照父组件handleReset方法
      const resetQuery = {
        page: 1,
        per_page: 10,
        order_type: [],
        sub_business_type: '',
        start_time: new Date().toISOString().slice(0, 10),
        search_key: 'passenger_phone',
        search_value: '',
        biz: 'undispatched',
        start_address_key_list: [],
        end_address_key_list: [],
        start_address_code: '',
        end_address_code: '',
        start_city_name: '',
        end_city_name: '',
        line_class_id: [],
        branch_id: null,
        passenger_phone: '',
        driver_phone: '',
        order_no: '',
        time: []
      }

      // 传递重置事件到父组件执行完整的重置操作
      this.$emit('resetQuery', resetQuery)
    },
    command(command) {
      if (!this.handleSelectShort.includes(command)) {
        this.handleSelectShort.push(command)
      } else {
        // 如果存在则去掉
        this.handleSelectShort.splice(this.handleSelectShort.indexOf(command), 1)
      }

      const params = []
      this.handleSelectShort.forEach(item => {
        params.push(moment(moment(this.currentDate + ' ' + item)).format('YYYY/MM/DD HH:mm:ss'))
      })

      this.searchQuery['time'] = params
      this.handleSearch()
    },
    toggleSideBar() { },
    tableRowClassName({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    rowClick(row, column, event) {
      this.$emit('setOrder', row.row_index)
    },
    handleSearch() {
      this.searchQuery.page = 1
      this.getOrderList()
      this.fetTimeOrdershort()
    },

    async fetTimeOrdershort() {
      const params = Object.assign({}, this.searchQuery)
      delete params.start_time

      try {
        const { data } = await fetTimeShort(params)
        if (data && Object.keys(data).length) {
          if (Object.keys(data).length === 1) {
            const shorts = Object.values(data)
            this.shortcutsTime = shorts[0]
          } else {
            this.shortcutsTime = []
          }
        } else {
          this.shortcutsTime = []
        }
      } catch (error) {
        console.error('获取时间快捷方式失败:', error)
        this.shortcutsTime = []
      }
    },
    cancelSelection() {
      this.$emit('setOrder')
    },
    handleSizeChange(val) {
      this.searchQuery.per_page = val
      this.getOrderList()
    },
    handleCurrentChange(val) {
      this.searchQuery.page = val
      this.getOrderList()
    },
    getOrderList() {
      this.$emit('getOrder', this.searchQuery)
    },
    sendOrder(type, params) {
      if (type === 'dispatched') {
        this.$confirm(
          `确认撤销指派当前订单？撤销后订单将返回到待派单`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          // 获取司机ID
          let driver_id = ''

          // 如果是单个订单撤销
          if (params.checkOrderInfo && params.checkOrderInfo.driver_id) {
            driver_id = params.checkOrderInfo.driver_id
          } else if (params.driver_id) { // 如果是批量撤销，直接从params中获取
            driver_id = params.driver_id
          }

          cancelOrders({
            driver: driver_id,
            order_ids: params.order_ids
          }).then(() => {
            this.$notify({
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
            this.getOrderList()
            this.$emit('updateDriver') // 更新地图司机信息
          })
        })
        return
      }
      this.$emit('openDriver', params)
    },
    ReaOrder(params) {
      this.$emit('openDriver', params)
    },
    async updateOrderField(order_id, field, value, index) {
      this.$set(this.order[index], field, value)

      // 隐藏编辑状态
      this.$set(this.order[index], `editing_${field}`, false)

      try {
        const data = {
          order_id: order_id
        }
        data[field] = value

        await updateOrderInfo(data)

        this.$notify({
          message: '更新成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        this.$notify({
          message: '更新失败',
          type: 'error',
          duration: 2000
        })
        console.error('更新订单信息失败:', error)
      }
    },
    startEditing(row, field) {
      this.$set(row, `editing_${field}`, true)
    },
    finishEditing(row, field, order_id, index) {
      this.updateOrderField(order_id, field, row[field], index)
      this.$set(row, `editing_${field}`, false)
    }
  }
}
</script>
<style lang="scss" scoped>
.icon-change {
  cursor: pointer;
  width: 24px;
  height: 24px;
  background: url("../../../../assets/icon-chage.png") no-repeat;
  background-size: cover;
  content: '';
  display: inline-block;
}

::v-deep .el-date-editor.el-input {
  width: auto;
}

.badge-status-start {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #52c41a;
}

.badge-status-end {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #ff4d4f;
}

.quick-time {
  margin-bottom: 5px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.temp_order {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 3px;
  left: 3px;
  background-color: #0067e1;
  border-radius: 10px;
  color: #fff;
  display: block;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #999;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.el-icon-question {
  color: #999;
}

.cut-dot {
  margin: 0 5px;
}

.top-warp {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0 8px 0;
}

.hamburger-container {
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;
}

.crosstime {
  cursor: pointer;
}

.action-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .left-action {
    display: flex;
    align-items: center;
  }
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  font-size: 14px;
}

.list-warp-box {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.order-container {
  width: 1000px;
  height: 100%;
  max-height: 100%;
  box-shadow: 0 0px 5px 0px rgb(0 0 0 / 15%);
  padding: 0;
  z-index: 1;
  position: relative;
  overflow: hidden;
  font-size: 14px;
  display: flex;
  flex-direction: column;
}

.label-departStation {
  color: #999;
  font-size: 12px;
  display: block;
  font-weight: normal;
  line-height: 18px;
  margin-right: 5px;
  white-space: nowrap;
}

.warp-title {
  font-size: 16px;
  font-weight: 700;
  position: relative;
  margin-left: 10px;

  &:before {
    width: 4px;
    border-radius: 5px;
    height: 80%;
    background: #0067e1;
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    margin: auto;
    content: "";
    display: block;
  }

  /* 添加返回按钮样式 */
  .back-to-normal {
    margin-left: 10px;
    font-size: 13px;
    color: #409EFF;
    border: 1px solid #d9ecff;
    background-color: #ecf5ff;
    padding: 7px 15px;
    border-radius: 3px;
    line-height: 1;
    white-space: nowrap;

    &:hover {
      background-color: #409EFF;
      color: #fff;
      border-color: #409EFF;
    }
  }
}

.space-line {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: left;
  line-height: 18px;
}

.small-text {
  font-size: 13px;
  color: #666;
  line-height: 17px;
  margin-top: 3px;
}

.bottom-container {
  margin-top: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  height: 40px;
  flex-shrink: 0; /* Prevent pagination from shrinking */

  .left-content {
    display: flex;
    align-items: center;

    .selection {
      display: inline-flex;
      align-items: center;
      margin-left: 10px;

      span {
        font-size: 14px !important;
        margin: 0 10px;
        color: #0067e1;
      }
    }
  }
}

.filter-item {
  margin-right: 5px;
}

.el-button--mini {
  padding: 7px 10px;
}

.time-slots-container {
  background: #f9fafc;
  padding: 0px 5px;
  margin-top: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 13px;
  border: 1px solid #e4e7ed;
}

.time-slot-label {
  margin-right: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.time-slot-tag {
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;
}

.order-table.flex-table {
  /* 确保表格位置稳定且能正确滚动 */
  margin-top: 5px;
  height: 100%;
}

/* 恢复表格内部布局和滚动特性 */
::v-deep .order-table.flex-table .el-table__body-wrapper {
  overflow-y: auto !important;
  max-height: unset !important;
}
</style>
<style lang="scss">
.surplus {
  color: #67c23a;
}

.nosurplus {
  color: #f56c6c;
}

.search-order-list {
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 确保flex子项可以收缩 */
}

.order-table {
  .el-table-column--selection>.cell {
    padding-left: 5px !important;
  }

  .cell {
    padding-left: 5px;
    padding-right: 5px;
    font-size: 14px;
  }

  .el-table__row {
    cursor: pointer;
  }

  .el-table__header .el-table__cell {
    padding: 0 !important;
  }

  .el-table__body .el-table__cell {
    padding: 4px 0 !important;
  }

  thead tr th {
    height: 35px;
  }
}

th.el-table__cell>.cell {
  line-height: 18px;
  padding-left: 5px;
  padding-right: 5px;
  font-size: 14px !important;
}

.custom-header .el-checkbox__label {
  padding-left: 5px;
}

.el-pagination {
  padding: 0;
  margin: 0;
  text-align: right;
}

.el-tabs--card > .el-tabs__header {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;

  .el-tabs__nav {
    border: none;
  }

  .el-tabs__item {
    border: 1px solid #e4e7ed;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: 32px;
    line-height: 32px;
    padding: 0 20px;
    margin-right: -1px;

    &.is-active {
      border-bottom-color: #fff;
    }
  }
}

.count-tag {
  margin-left: 5px;
  height: 18px;
  line-height: 16px;
  padding: 0 5px;
  font-size: 11px;
  border-radius: 9px;
}

.tab-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  .count-tag {
    margin-left: 4px;
    transform: scale(0.8);
  }
}

.editable-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
  cursor: pointer;
  position: relative;

  .edit-icon {
    visibility: hidden;
    color: #409EFF;
    font-size: 12px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  &:hover .edit-icon {
    visibility: visible;
  }
}

.editable-text {
  padding: 0 5px;
  cursor: pointer;
  position: relative;
  display: inline-block;
  border-bottom: 1px dashed transparent;

  &:hover {
    border-bottom-color: #409EFF;
    color: #409EFF;
  }
}

.readonly-text {
  padding: 0 5px;
  position: relative;
  display: inline-block;
}

.order-table.flex-table {
  /* Ensure table itself doesn't have conflicting height styles */
  height: 100% !important; /* 修改为100%高度以允许表格占满容器 */

  .el-table__body-wrapper {
    /* Ensure body wrapper respects flex sizing */
    overflow-y: auto !important; /* 确保垂直滚动生效 */
  }
}

.flex-table {
  flex: 1;
  min-height: 0; /* Crucial for flex shrinking */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 调整表格顶部距离，超时待派模式时应用 */
.is-timeout .order-table.flex-table {
  margin-top: 10px;
}

</style>

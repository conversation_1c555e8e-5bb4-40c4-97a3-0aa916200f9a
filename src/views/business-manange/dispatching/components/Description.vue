<template>
  <div v-loading="loading" class="description-warp">
    <el-skeleton v-if="!orderDetail" style="height: 400px" />
    <el-descriptions
      v-if="orderDetail"
      title="基本信息"
      :column="2"
      size="small"
      border
    >
      <el-descriptions-item
        label="订单编号"
      >{{ orderDetail.order_no }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        <el-tag size="mini" effect="plain">
          {{
            orderDetail.type === 7
              ? [1, 2].includes(orderDetail.ferry_type)
                ? "摆渡车"
                : "快车"
              : orderDetail.order_type_string
          }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="线路">
        <span v-if="orderDetail.type === 4" class="table-p">
          {{ orderDetail && orderDetail.agency && orderDetail.agency.name }}
        </span>
        <span v-else class="table-p">
          {{ orderDetail | filSname }}
          <i class="el-icon-right" />
          {{ orderDetail | filEname }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item
        label="出发时间"
      >{{ orderDetail.start_time }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        <Tablestatus mode="default" :word="orderDetail.cn_state" />
      </el-descriptions-item>
      <el-descriptions-item label="支付状态">
        <Tablestatus
          v-if="orderDetail.is_pay === 1 || orderDetail.is_pre_pay === 1"
          mode="success"
          word="已支付"
        />
        <Tablestatus v-else mode="warning" word="待支付" />
      </el-descriptions-item>
      <el-descriptions-item
        label="乘车人数"
      >{{ orderDetail.book_seating }}
      </el-descriptions-item>
      <el-descriptions-item
        label="购票人"
      >{{ (orderDetail.passenger && orderDetail.passenger.name) || "-" }}
      </el-descriptions-item>
      <el-descriptions-item
        label="联系人"
      >{{ orderDetail.reseverd_phone }}
      </el-descriptions-item>
      <el-descriptions-item
        label="座位信息"
      >{{ orderDetail.seat_title || "-" }}
      </el-descriptions-item>
      <el-descriptions-item
        label="上车点"
      >{{ orderDetail.start_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item
        label="下车点"
      >{{ orderDetail.end_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item
        label="订单总价"
      >{{ orderDetail.price }}元
      </el-descriptions-item>
      <el-descriptions-item
        label="优惠金额"
      >{{ orderDetail.coupon_price }}元
      </el-descriptions-item>
      <el-descriptions-item
        label="实付金额"
      >{{ orderDetail.real_price }}元
      </el-descriptions-item>
      <el-descriptions-item
        label="渠道所得"
      >{{ orderDetail.offer_channel_price }}元
      </el-descriptions-item>
      <el-descriptions-item
        label="司机所得"
      >{{ orderDetail.offer_price }}元
      </el-descriptions-item>
      <el-descriptions-item
        label="平台对司机提成"
      >{{ orderDetail.split }}
      </el-descriptions-item>
      <el-descriptions-item
        label="下单时间"
      >{{ orderDetail.create_time }}
      </el-descriptions-item>
      <el-descriptions-item
        label="所属分台"
      >{{ orderDetail.branch.mchname || "-" }}
      </el-descriptions-item>
      <el-descriptions-item
        label="代约分台"
      >{{
        (orderDetail.temp_apply_branch &&
          orderDetail.temp_apply_branch.mchname) ||
          "-"
      }}
      </el-descriptions-item>
      <el-descriptions-item
        label="渠道名称"
      >{{ orderDetail.channel_branch_name || "-" }}
      </el-descriptions-item>
      <el-descriptions-item
        label="乘客备注"
      >{{ orderDetail.reseverd_info || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="航班号">
        <div class="editable-field">
          <span>{{ orderDetail.ft_number || "-" }}</span>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="startEditing('ft_number')"
          ></el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="派单备注">
        <div class="editable-field">
          <span>{{ orderDetail.dispatch_notes || "-" }}</span>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="startEditing('dispatch_notes')"
          ></el-button>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <br>
    <el-descriptions
      v-if="orderDetail && orderDetail.driver"
      title="司机信息"
      :column="2"
      size="small"
      border
    >
      <el-descriptions-item
        label="司机名称"
      >{{ orderDetail.driver.name }}
      </el-descriptions-item>
      <el-descriptions-item
        label="司机电话"
      >{{ orderDetail.driver.cellphone }}
      </el-descriptions-item>
      <el-descriptions-item
        label="车辆品牌"
      >{{ orderDetail.driver.car_brand }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌号码">
        <el-tag size="mini" type="info" effect="dark">
          {{ orderDetail.driver.car_tail_number }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    
    <!-- 添加编辑弹窗 -->
    <el-dialog
      :title="`编辑${editingFieldLabel}`"
      :visible.sync="editDialogVisible"
      width="30%"
      append-to-body
    >
      <el-input
        v-if="editingField === 'ft_number'"
        v-model="editValue"
        placeholder="请输入航班号"
      ></el-input>
      <el-input
        v-else
        v-model="editValue"
        type="textarea"
        :rows="4"
        :placeholder="`请输入${editingFieldLabel}`"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { orderInfo, updateOrderInfo } from '@/api/business'
import { formatStartName, formatEndName } from '@/utils/filters'

export default {
  filters: {
    filterStart(val) {
      return (
        formatStartName(val).start_city +
        ' | ' +
        formatStartName(val).start_name
      )
    },
    filterEnd(val) {
      return formatEndName(val).end_city + ' | ' + formatEndName(val).end_name
    },
    filSname(val) {
      return formatStartName(val).start_name
    },
    filEname(val) {
      return formatEndName(val).end_name
    }
  },
  props: {
    order: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      orderDetail: null,
      loading: false,
      editDialogVisible: false,
      editingField: null,
      editingFieldLabel: '',
      editValue: ''
    }
  },
  watch: {
    order: {
      handler(n) {
        if (n) {
          this.fetchDetail()
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchDetail() {
      this.loading = true
      const { data } = await orderInfo({ order: this.order })
      this.loading = false
      this.orderDetail = data
    },
    startEditing(field) {
      this.editingField = field
      this.editingFieldLabel = field === 'ft_number' ? '航班号' : '派单备注'
      this.editValue = this.orderDetail[field] || ''
      this.editDialogVisible = true
    },
    async saveEdit() {
      try {
        const data = {
          order_id: this.order
        }
        data[this.editingField] = this.editValue
        
        await updateOrderInfo(data)
        
        this.orderDetail[this.editingField] = this.editValue
        this.editDialogVisible = false
        
        this.$notify({
          message: '更新成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        this.$notify({
          message: '更新失败',
          type: 'error',
          duration: 2000
        })
        console.error('更新订单信息失败:', error)
        this.editDialogVisible = false
      }
    }
  }
}
</script>

<style lang="scss">
.description-warp {
  .el-descriptions--small.is-bordered .el-descriptions-item__cell {
    min-width: 80px;
  }
  .el-descriptions-item__content {
    min-width: 280px;
  }

  .editable-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>

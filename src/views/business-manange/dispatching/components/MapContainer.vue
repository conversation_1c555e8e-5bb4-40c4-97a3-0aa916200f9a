<template>
  <div class="map-component">
    <div id="map-container" />
    <el-dialog :visible.sync="showModal" width="750px" class="driverModal">
      <h5 v-if="dialoginfo" slot="title" class="warp-title">
        {{ dialoginfo && dialoginfo.name || "司机详情" }}
        <span> <i class="el-icon-phone" /> {{ dialoginfo && dialoginfo.cellphone }}</span>
      </h5>
      <div v-loading="driverInfoLoading" class="full-detail-warp">
        <el-descriptions v-if="driverInfo" direction="horizontal" :column="2" border size="small">
          <el-descriptions-item label="账号状态">
            <el-tag effect="plain" size="mini" type="success">正常</el-tag>
            <span>
              <el-tag v-if="driverInfo.state === 3" size="mini" type="info">暂停接单</el-tag>
              <el-tag v-else size="mini">接单中</el-tag>
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="当前车辆">
            <el-tag size="mini" type="info" effect="dark">
              {{ driverInfo.car_tail_number }}
            </el-tag>
            [{{ driverInfo.total_seating }}座]
          </el-descriptions-item>
          <el-descriptions-item label="所属分台">{{
            driverInfo.branch.mchname
          }}</el-descriptions-item>
          <el-descriptions-item label="车辆状态">
            行驶中，速度：0km/h，方向：-，停留时长：-，经度纬度：-，地址：-，当前时间：-，定位方式：GPS
            GT18-45688，电量：-%
          </el-descriptions-item>
          <el-descriptions-item label="派单情况">
            <span class="item-num">{{
              driverInfo.realtime_seat.total_pinche_realtime_seats
            }}拼</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_baoche_realtime_seats
            }}包</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_banxian_realtime_seats
            }}定</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_shunfengche_realtime_seats
            }}顺</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_kuaiche_realtime_seats
            }}快</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_chuzuche_realtime_seats
            }}出租车</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_daihuo_realtime_seats
            }}货</span>
            <span class="item-num">{{
              driverInfo.realtime_seat.total_daiban_realtime_seats
            }}代</span>
          </el-descriptions-item>
          <el-descriptions-item label="实载情况">
            可载{{ driverInfo.total_seating - 1 }}人 [实时 | 已载
            {{ Number(driverInfo.realtime_seat.total_realtime_seats) + "人" }}
            <span
              v-if="
                Number(driverInfo.realtime_seat.total_realtime_seats) >
                  driverInfo.total_seating - 1
              "
              :class="
                driverInfo.total_seating -
                  1 -
                  Number(driverInfo.realtime_seat.total_realtime_seats) >
                  0
                  ? ''
                  : 'nosurplus'
              "
            >{{
              `(超${
                Number(driverInfo.realtime_seat.total_realtime_seats) -
                driverInfo.total_seating +
                1
              }人)`
            }}</span>
            /余座<span>{{
              driverInfo.total_seating -
                1 -
                Number(driverInfo.realtime_seat.total_realtime_seats) >
                0
                ? driverInfo.total_seating -
                  1 -
                  Number(driverInfo.realtime_seat.total_realtime_seats)
                : 0
            }}</span>]</el-descriptions-item>
          <el-descriptions-item label="当前位置">{{ driverInfo.address || "-" }}（最新定位时间{{
            driverInfo.update_time || "-"
          }}）</el-descriptions-item>
        </el-descriptions>
        <el-checkbox-group v-model="checkboxGroup" size="small" style="margin-top: 10px">
          <el-checkbox-button label="ing" value="ing">进行中订单({{ onWayOrder.length || 0 }})</el-checkbox-button>
        </el-checkbox-group>
        <el-table
          v-if="driverInfo"
          ref="multipleTableRef"
          stripe
          class="order-table"
          :data="onWayOrder"
          fit
          highlight-current-row
          height="250px"
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center', padding: '8px 0' }"
          size="mini"
        >
          <el-table-column label="订单编号" size="small" width="180">
            <template slot-scope="scope">
              <p class="table-p">
                <el-tag size="mini" effect="plain">
                  {{
                    scope.row.type === 7
                      ? [1, 2].includes(scope.row.ferry_type)
                        ? "摆渡车"
                        : "快车"
                      : scope.row.order_type_string
                  }}
                </el-tag>
                {{ scope.row.order_no }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="出发时间/路线/联系电话" size="small">
            <template slot-scope="scope">
              <p class="table-p">
                {{ scope.row.start_time.slice(0, 16) || "/" }}
              </p>
              <p class="table-p">
                {{ scope.row | filterStart }}
                <i class="el-icon-right" />
                {{ scope.row | filterEnd }}
              </p>
              <p class="table-p">
                {{ scope.row.reseverd_phone || "/" }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="总票/实票/实付金额" size="small" width="120">
            <template slot-scope="scope">
              {{ scope.row.total_booking_seats }}张/{{ scope.row.book_seating }}张/￥{{ scope.row.real_price }}
            </template>
          </el-table-column>
          <el-table-column label="订单状态" size="small" width="90">
            <template slot-scope="scope">
              <!-- <el-tag size="mini" effect="plain" type="info">{{
              scope.row.cn_state
            }}</el-tag> -->
              <Tablestatus v-if="scope.row.state === 1" mode="warning" word="待接单" />
              <Tablestatus v-else-if="scope.row.state === 2" mode="processing" word="已接单" />
              <Tablestatus v-else-if="scope.row.state === 3" mode="processing" word="已上车" />
              <Tablestatus v-else-if="scope.row.state === 4" mode="processing" word="在路上" />
              <Tablestatus v-else-if="scope.row.state === 5" mode="success" word="已送达" />
              <Tablestatus v-else-if="scope.row.state === 6" mode="success" word="已完成" />
              <Tablestatus v-else-if="scope.row.state === 7" mode="default" word="已取消" />
              <Tablestatus v-else mode="default" :word="scope.row.cn_state" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="50">
            <template v-slot="scope">
              <el-button type="text" size="small" @click="delOrder(scope.row)">撤销</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="mini" @click="showModal = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog :visible.sync="showDriverModal" width="950px" class="driverModal">
      <div slot="title" class="pop-top-warp">
        <h5 class="warp-title">接单司机列表</h5>
        <!-- <p class="line-checkd">当前订单：</p> -->
        <div
          v-if="
            tabSendQuery &&
              tabSendQuery.checkOrderInfo &&
              (!points || (points && points.lnglats.length === 0))
          "
          class="line-checked"
        >
          <span>当前订单：</span>
          <div v-if="tabSendQuery.checkOrderInfo.type !== 4">
            {{ tabSendQuery.checkOrderInfo | filterStart }}
            <i class="el-icon-right" />
            {{ tabSendQuery.checkOrderInfo | filterEnd }}
            （{{ tabSendQuery.checkOrderInfo.start_time.slice(0, 16) || "/" }}）
            <p class="table-p">
              上车点：{{
                tabSendQuery.checkOrderInfo.start_address_remark || "-"
              }}
            </p>
            <p class="table-p">
              下车点：
              {{ tabSendQuery.checkOrderInfo.end_address_remark || "-" }}
            </p>
          </div>
        </div>
      </div>
      <el-checkbox v-if="!points || (points && points.lnglats.length === 0)" v-model="tabQuery.checked" @change="handleSearch">
        仅看符合该业态+该路线方向司机
      </el-checkbox>
      <div class="filter-container" style="margin-top: 15px">
        <el-select v-if="!points || (points && points.lnglats.length === 0)" v-model="tabQuery.sort_by" size="mini" style="margin-right: 10px" placeholder="请选择排序方式" @change="handleSearch">
          <el-option label="推荐" value="recommend" />
          <el-option label="距离从近到远" value="distance" />
          <el-option label="排序小到大" value="sort" />
        </el-select>
        <el-select v-model="tabQuery.business_type" size="mini" style="margin-right: 10px" placeholder="请选择业态" @change="handleBizTypeChange">
          <el-option v-for="item in bizTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-if="Number(tabQuery.business_type) === 5" v-model="tabQuery.sub_business_type" size="mini" style="margin-right: 10px" placeholder="请选择子业态" @change="handleSearch">
          <el-option v-for="item in subBizTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model="tabQuery.search_value" placeholder="请输入" size="mini" style="width: 40%">
          <el-select slot="prepend" v-model="tabQuery.search_key" placeholder="请选择">
            <el-option label="司机工号" value="job_number" />
            <el-option label="司机车牌号" value="car_tail_number" />
            <el-option label="司机手机号" value="cellphone" />
            <el-option label="司机姓名" value="name" />
          </el-select>
        </el-input>
        <el-button type="primary" style="margin-left: 5px" size="mini" icon="el-icon-search" @click="handleSearch">
          搜索
        </el-button>
      </div>
      <el-table
        ref="multipleTableRef"
        v-loading="tableLoading"
        stripe
        class="order-table"
        :data="tabDriver"
        fit
        highlight-current-row
        :row-key="
          (row) => {
            return row.driver_id;
          }
        "
        :expand-row-keys="expands"
        height="calc(100vh - 450px)"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center', padding: '8px 0' }"
        size="small"
      >
        <el-table-column type="expand" width="1">
          <template slot-scope="scope">
            <div class="lines-show-warp">
              授权路线：
              <template
                v-if="
                  scope.row.line_class_trains &&
                    scope.row.line_class_trains.length > 0
                "
              >
                <p v-for="item in scope.row.line_class_trains" :key="item.line_class_id" class="table-p" style="line-height: 18px">
                  [{{ item.cn_line_class_train_no }}]
                  {{ item.line_class.start_city.name }} -
                  {{ item.line_class.end_city.name }}
                </p>
              </template>
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" size="small" width="60" />
        <el-table-column label="昵称" size="small" width="90" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="drive-table-name" @click="showDetailDriver(scope.row)">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="司机手机号" size="small" width="120" prop="cellphone" />
        <el-table-column label="车辆状态" size="small" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.state === 3" size="mini" type="info">暂停接单</el-tag>
            <el-tag v-else size="mini">接单中</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="当前车辆" size="small" width="100" prop="car_tail_number">
          <template slot-scope="scope">
            <el-tag size="mini" type="info" effect="dark">
              {{ scope.row.car_tail_number }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="车型/座位" size="small" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.car_brand || "-" }}/{{
              scope.row.total_seating || "-"
            }}座
          </template>
        </el-table-column>
        <el-table-column label="距离" size="small" prop="distance" width="80">
          <template slot-scope="scope">
            <span v-if="!points || (points && points.lnglats.length === 0)">{{ scope.row.distance.toFixed(2) }}km</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column size="small" width="220">
          <div slot="header" class="driver-custom-header">
            <span>已接单/已载/余座</span>
            <el-popover class="item" trigger="hover" placement="top-start">
              <div>
                <p>已接单：司机接单但未结束订单数[未取消/未关闭/未完成]</p>
                <p>已载：当前已上车的乘客人数/座位数</p>
                <p>余座：当前车辆剩余座位数</p>
              </div>
              <i slot="reference" class="el-icon-question" style="margin-left: 5px" />
            </el-popover>
          </div>
          <template slot-scope="scope">
            {{ scope.row.total_dispatched_orders }}单 /[实时 | 已载{{
              Number(scope.row.realtime_seat.total_realtime_seats)
            }}人
            <span
              v-if="
                Number(scope.row.realtime_seat.total_realtime_seats) >
                  scope.row.total_seating - 1
              "
              class="nosurplus"
            >{{
              `(超${
                Number(scope.row.realtime_seat.total_realtime_seats) -
                scope.row.total_seating +
                1
              }人)`
            }}</span>/余座
            <span
              :class="
                scope.row.total_seating -
                  1 -
                  Number(scope.row.realtime_seat.total_realtime_seats) >
                  0
                  ? 'surplus'
                  : 'nosurplus'
              "
            >{{
              scope.row.total_seating -
                1 -
                Number(scope.row.realtime_seat.total_realtime_seats) >
                0
                ? scope.row.total_seating -
                  1 -
                  Number(scope.row.realtime_seat.total_realtime_seats)
                : 0
            }}</span>
            ]
          </template>
        </el-table-column>
        <el-table-column label="授权路线" size="small" width="80">
          <template slot-scope="scope">
            <el-link :underline="false" @click="openRow(scope.row.driver_id)"><span style="font-size: 12px">
              {{ expands.includes(scope.row.driver_id) ? "收起" : "查看"
              }}<i
                :class="
                  expands.includes(scope.row.driver_id)
                    ? 'el-icon-caret-right'
                    : 'el-icon-caret-bottom'
                "
              />
            </span>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="50">
          <template v-slot="scope">
            <el-button type="text" size="small" :disabled="scope.row.state === 3 || scope.row.state !== 0" @click="tablesendOrder(scope.row)">指派</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom-container">
        <div class="left-content" />
        <el-pagination v-if="tabDriver && tabDriver.length > 0" small background :current-page="tabQuery.page" :page-size="tabQuery.per_page" layout="prev, pager, next" :total="totals" @current-change="handleCurrentChange" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="mini" @click="showDriverModal = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <div class="refresh-btn">
      <!-- <i class="el-icon-refresh-right"></i> -->
      <el-tooltip class="item" effect="dark" content="查看并更新所有司机位置" placement="top-start">
        <el-button :icon="loading ? 'el-icon-loading' : 'el-icon-refresh-right'" size="small" @click="showAllDriver">
          查找司机
        </el-button>
      </el-tooltip>
    </div>
    <div class="driver-list-warp" :style="{ width: (driverOpened ? 250 : 0) + 'px' }">
      <div class="code_arrow" @click="driverOpened = !driverOpened">
        <i :class="driverOpened ? 'el-icon-arrow-right' : 'el-icon-arrow-left'" />
      </div>
      <div class="list-container-arrow">
        <el-table
          ref="multipleTableRef"
          v-loading="loading"
          :row-class-name="tableRowClassName"
          stripe
          class="order-table"
          :data="driverList"
          height="calc(100vh - 445px)"
          fit
          highlight-current-row
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center', padding: '8px 0' }"
          size="mini"
          @row-click="rowClick"
        >
          <el-table-column label="排序" prop="sort" size="mini" width="50" />
          <el-table-column label="昵称" size="mini" width="90" show-overflow-tooltip>
            <template slot-scope="scope">
              <!-- <el-button type="text" size="mini"> -->
              <span class="drive-table-name">{{ scope.row.name }}</span>
              <!-- </el-button> -->
            </template>
          </el-table-column>
          <el-table-column label="当前车辆" size="mini" width="90" prop="car_tail_number">
            <template slot-scope="scope">
              <el-tag size="mini" type="info" effect="dark">
                {{ scope.row.car_tail_number }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader'
import { formatStartName, formatEndName } from '@/utils/filters'

import {
  dispatchDrivers,
  mapDrivers,
  postOrders,
  onLineOrders,
  cancelOrders,
  driverDetail
} from '@/api/business.js'
import { getProfiles } from '@/api/account.js'
// import { distanceGetTime } from '@/utils'
import Vue from 'vue'
export default {
  filters: {
    filterStart (val) {
      return (
        formatStartName(val).start_city +
        ' | ' +
        formatStartName(val).start_name
      )
    },
    filterEnd (val) {
      return formatEndName(val).end_city + ' | ' + formatEndName(val).end_name
    }
  },
  props: {
    mainQuery: {
      type: Object,
      default: () => { }
    },
    order: {
      type: Array,
      default: () => []
    },
    points: {
      type: Object,
      default: () => { }
    },
    branch_id: {
      default: () => ''
    }
  },
  data () {
    return {
      expands: [],
      driverOpened: false,
      showDriverModal: false,
      driverInfo: null,
      showModal: false,
      driverList: [],
      tabDriver: [],
      driverQuery: {},
      tabQuery: {
        search_key: 'name',
        page: 1,
        per_page: 15,
        business_type: '',
        sub_business_type: null,
        checked: true
      },
      bizTypes: [
        { label: '全部', value: '' }
      ],
      subBizTypes: [
        { value: null, label: '全部' },
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ],
      businessTypes: [], // 存储API返回的业态数据
      orderMarkers: [],
      driverMarkers: [],
      circle: null,
      dialoginfo: null,
      tabSendQuery: null,
      tableLoading: false,
      onWayOrder: [],
      checkboxGroup: ['ing'],
      loading: false,
      driverInfoLoading: false,
      totals: 0,
      userInfo: null
    }
  },
  watch: {
    order: {
      handler (n) {
        if (n) {
          this.initOrderSeat(n)
        } else {
          this.initOrderSeat([])
        }
      },
      immediate: true
    },
    points: {
      handler (n) {
        if (!this.order || this.order.length === 0) {
          if (this.circle) {
            this.circle.destroy()
            this.circle = null
            this.map.setFitView(this.orderMarkers, true, [60, 60, 100, 60])
          }
          return
        }
        if (
          n &&
          n.lnglats &&
          n.lnglats.length > 0 &&
          n.lnglats.some((item) => {
            return item.longitude !== 0 && item.latitude
          })
        ) {
          this.map.setCenter(this.getPointsCenter(n.lnglats))
          this.setPolygon(n.lnglats)
        } else {
          if (this.circle) {
            this.circle.destroy()
            this.circle = null
            this.map.setFitView(this.orderMarkers, true, [60, 60, 100, 60])
          }
        }
      },
      immediate: true
    },
    branch_id: {
      handler (n) {
        if (n && n !== '') {
          this.getDriverList()
        }
      },
      immediate: true
    }
  },
  created () {
    // DOM初始化完成进行地图初始化
    this.initMap()
    // 获取业态数据
    this.fetchBusinessTypes()
  },
  methods: {
    async fetchBusinessTypes() {
      try {
        const res = await getProfiles()
        if (res.data) {
          this.userInfo = res.data

          if (this.userInfo.business_types) {
            // 构建业态选项
            this.bizTypes = [
              { label: '全部', value: '' },
              ...Object.keys(this.userInfo.business_types).map(key => ({
                label: this.userInfo.business_types[key],
                value: Number(key)
              }))
            ]
          }
        }
      } catch (error) {
        console.error('获取业态数据失败', error)
      }
    },
    handleBizTypeChange() {
      // 根据选择的业态更新子业态列表
      this.tabQuery.sub_business_type = null

      // 只有当业态值为5时才显示子业态选择
      // 将业态值转为数字类型
      if (this.tabQuery.business_type !== '' && this.tabQuery.business_type !== null && !isNaN(this.tabQuery.business_type)) {
        this.tabQuery.business_type = Number(this.tabQuery.business_type)
      }

      this.handleSearch()
    },
    openRow (id) {
      if (this.expands.includes(id)) {
        this.expands.splice(
          this.expands.findIndex((item) => item === id),
          1
        )
        return
      }
      this.expands.push(id)
    },
    showDetailDriver (params) {
      this.dialoginfo = { name: params.name, cellphone: params.cellphone }
      this.viewDetail(params.driver_id)
      this.showModal = true
      this.getOnWayOrder(params.driver_id)
    },
    async viewDetail (driver) {
      this.driverInfoLoading = true
      const { data } = await driverDetail({ driver: driver })
      this.driverInfoLoading = false
      this.driverInfo = data
    },
    handleSearch () {
      this.getTableDriver()
    },
    handleCurrentChange (val) {
      this.tabQuery.page = val
      this.getTableDriver()
    },

    tableRowClassName ({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    rowClick (row, column, event) {
      //   this.$emit("setOrder", row.row_index);
      const markers = this.map.getAllOverlays()
      const click_driver_id = this.driverList[row.row_index].driver_id
      const click_driver_marker = markers.find((o) => {
        return (
          o._opts.extData.driver_id &&
          o._opts.extData.driver_id === click_driver_id
        )
      })
      if (click_driver_marker) {
        click_driver_marker.setTop(true)
        this.map.setFitView(click_driver_marker, true, [100, 100, 100, 260])
      }
    },
    showAllDriver () {
      this.driverOpened = true
      this.getDriverList(true)
    },
    toggleSideBar () { },
    delOrder (row) {
      this.$confirm(`确认撤销指派当前订单？撤销后订单将返回到待派单`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelOrders({
          driver: row.driver_id,
          order_ids: row.order_id
        }).then(() => {
          this.$notify({
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
          this.$emit('fetchOrders')
          this.getDriverList() // 更新地图司机信息
          this.getOnWayOrder(row.driver_id)
        })
      })
    },
    async getOnWayOrder (driver) {
      const { data = [] } = await onLineOrders({ driver: driver })
      this.onWayOrder = (Array.isArray(data) && data) || []
    },
    tablesendOrder (driver) {
      const is_drivers = this.tabSendQuery.checkOrderInfo && this.tabSendQuery.checkOrderInfo.driver_id
      const loading = this.$loading({
        lock: true,
        text: '派单中..',
        spinner: 'el-icon-loading'
      })
      postOrders({
        is_drivers: is_drivers,
        driver: driver.driver_id,
        order_ids: this.tabSendQuery.order_ids,
        assign_note: ''
      }).then(() => {
        loading.close()
        this.$notify({
          message: `${is_drivers ? '改派' : '指派'}成功`,
          type: 'success',
          duration: 2000
        })
        this.$emit('fetchOrders')
        this.getDriverList() // 更新地图司机信息
        this.showDriverModal = false
      }).catch(() => {
        loading.close()
      })
    },
    setPolygon (points) {
      let distance_init = 0
      let center = []
      points = points.filter(
        (o) => o.longitude && o.latitude && o.longitude !== 0
      )
      if (points.length > 1) {
        const p1 = new AMap.LngLat(points[0].longitude, points[0].latitude)
        points.map((o, index) => {
          if (index > 0) {
            var p2 = new AMap.LngLat(o.longitude, o.latitude)
            var distance = Math.round(p1.distance(p2))
            if (distance_init < distance) {
              distance_init = distance
            }
          }
        })
        distance_init = distance_init / 2 + 1500
        center = this.getPointsCenter(points)
      } else {
        distance_init = 3000
        center = [points[0].longitude, points[0].latitude]
      }

      if (this.circle) {
        this.circle.destroy()
        this.circle = null
      }
      this.circle = new AMap.Circle({
        center: center,
        radius: distance_init, // 半径
        borderWeight: 3,
        strokeColor: '#0067e1',
        strokeWeight: 6,
        strokeOpacity: 0.2,
        fillOpacity: 0.4,
        strokeStyle: 'dashed',
        strokeDasharray: [10, 10],
        // 线样式还支持 'dashed'
        fillColor: '#1791fc',
        zIndex: 50
      })

      this.map.add(this.circle)
      // 缩放地图到合适的视野级别
      this.map.setFitView([this.circle], true, [100, 100, 100, 260])
    },
    getPointsCenter (points) {
      var point_num = points.length // 坐标点个数
      var X = 0
      var Y = 0
      var Z = 0
      for (let i = 0; i < points.length; i++) {
        var lat, lng, x, y, z
        lat = (parseFloat(points[i].latitude) * Math.PI) / 180
        lng = (parseFloat(points[i].longitude) * Math.PI) / 180
        x = Math.cos(lat) * Math.cos(lng)
        y = Math.cos(lat) * Math.sin(lng)
        z = Math.sin(lat)
        X += x
        Y += y
        Z += z
      }
      X = X / point_num
      Y = Y / point_num
      Z = Z / point_num

      var tmp_lng = Math.atan2(Y, X)
      var tmp_lat = Math.atan2(Z, Math.sqrt(X * X + Y * Y))
      return [(tmp_lng * 180) / Math.PI, (tmp_lat * 180) / Math.PI]
    },
    async handleDriver (obj_) {
      this.tabSendQuery = obj_
      this.tabQuery = {
        search_key: 'name',
        page: 1,
        per_page: 15,
        checked: true
      }
      this.showDriverModal = true
      if (obj_.lnglats.length > 1) {
        await this.getTableDriver()
      } else {
        console.log(obj_)
        await this.getTableDriver({
          biz_number: obj_.type,
          order_id: obj_.order_ids,
          ...obj_.lnglats[0]
        })
      }
    },
    initOrderSeat (list) {
      const this_ = this
      const orderMarkers = []
      if (this.orderMarkers.length > 0) {
        this_.map.remove(this.orderMarkers)
        this.orderMarkers = []
      }
      list.map((o, index) => {
        var MyComponent = Vue.extend({
          data: function () {
            return {
              getExtData: o,
              index: index,
              checked: false
            }
          },
          methods: {
            checkedChange (id, index) {
              this_.$emit('changeCheckey', { id: id, index: index })
            }
          },
          template: `<div class="order_ticket">
          <el-checkbox-button v-model="getExtData.checked" :true-label="getExtData.order_id" false-label="" @change="(_)=>checkedChange(_,index)" size="mini">
          <span>订单{{index+1}}</span>
             <span>({{getExtData.book_seating||'-'}}人)</span>
          </el-checkbox-button>
             <i class="el-icon-caret-bottom ticket__arrow" :class="getExtData.checked?'arrow_check':''"></i>
          </div>`
        })
        var component = new MyComponent().$mount()
        o.start_longitude !== 0 &&
          o.start_longitude &&
          orderMarkers.push(
            new AMap.Marker({
              map: this.map,
              position: [o.start_longitude, o.start_latitude],
              offset: new AMap.Pixel(-55, -40), // 相对于基点的偏移位置
              content: component.$el
            })
          )
      })
      this.orderMarkers = orderMarkers
      if (this.orderMarkers.length > 0) {
        this_.map.setFitView(this.orderMarkers, true, [120, 120, 120, 120])
      }
    },
    initDriverSeat (list, isSetfit = false) {
      const this_ = this
      const driverMarkers = []
      if (this.driverMarkers.length > 0) {
        this.map.remove(this.driverMarkers)
        this.driverMarkers = []
      }
      list.map((o, dkey) => {
        var MyComponent = Vue.extend({
          data: function () {
            return {
              getExtData: o,
              driver_index: dkey,
              points_: this_.points
            }
          },
          methods: {
            showDriver (o) {
              this_.dialoginfo = { name: o.name, cellphone: o.cellphone }
              this_.viewDetail(o.driver_id)
              this_.showModal = true
              this_.getOnWayOrder(o.driver_id)
            },
            setOnTop (index) {
              this_.rowClick({ row_index: index })
            },
            sendOrder (data) {
              if (
                !this_.points ||
                (this_.points.lnglats && this_.points.lnglats.length === 0)
              ) {
                this.$message('请先选择需要派单的订单')
                return
              }

              // 移除弹框，直接执行派单操作
              const loading = this.$loading({
                lock: true,
                text: '派单中..',
                spinner: 'el-icon-loading'
              })
              postOrders({
                is_drivers: this_.points.is_drivers || false,
                driver: data.driver_id,
                order_ids: this_.points.order_ids,
                assign_note: ''
              }).then(() => {
                loading.close()
                this.$notify({
                  message: `${this_.points.is_drivers ? '改派' : '指派'}成功`,
                  type: 'success',
                  duration: 2000
                })
                this_.$emit('fetchOrders')
                this_.getDriverList() // 更新地图司机信息
              }).catch(() => {
                loading.close()
              })
            }
          },
          template: `<div class="route_ticket" @click="setOnTop(driver_index)">
            <div class="flex-item">
              <el-tag size="mini" type="info" effect="plain">{{getExtData.sort}}</el-tag>
              <span class="item-name" @click.stop="showDriver(getExtData)">{{getExtData.name}}</span>
            </div>
            <div class="baseInfo">
               <el-tag size="mini" type="info" effect="dark">
                  {{ getExtData.car_tail_number }}
               </el-tag>
              <span class="item-info">[{{getExtData.total_seating}}座]</span>
            </div>
            <div class="order-info baseInfo">
              <div>已接 <span class="num">{{getExtData.total_dispatched_orders}}</span> 单</div>
              <el-tag v-if="getExtData.state === 3" size="mini" type="info">休息中</el-tag>
              <el-tag v-else size="mini">接单中</el-tag>
            </div>
            <div class="order-info">
              [实时 | 已载{{getExtData.total_realtime_seats}}人<span
              v-if="getExtData.total_realtime_seats>getExtData.total_seating -1"
              class="nosurplus"
              >(超{{getExtData.total_realtime_seats-getExtData.total_seating +1}}人)</span>/余座 <span class="recode">{{(getExtData.total_seating -1-getExtData.total_realtime_seats>0)?(getExtData.total_seating -1-getExtData.total_realtime_seats):0}}</span> ]
            </div>
          <el-button type="primary" :disabled="getExtData.state === 3" @click.stop="sendOrder(getExtData)" size="mini" block>{{getExtData.total_seating - 1 <=
                getExtData.total_realtime_seats?'座位已满,继续派单':'指派'}}</el-button>
          </div>`
        })
        var component = new MyComponent().$mount()

        const key = o.total_seating > 5 ? 3 : Math.floor(Math.random() * 2) + 1
        driverMarkers.push(
          new AMap.Marker({
            map: this.map,
            position: [o.longitude, o.latitude],
            offset: new AMap.Pixel(-12, -30), // 相对于基点的偏移位置
            icon: new AMap.Icon({
              size: new AMap.Size(18, 34), // 图标大小
              image: require(`@/assets/mapcar${key}.png`),
              // imageOffset: new AMap.Pixel(8, 2), //图标相对于marker的偏移量
              imageSize: new AMap.Size(18, 34)
            }),
            zIndex: 12 + dkey,
            angle: Math.floor(Math.random() * 180) - 90
          }),
          new AMap.Marker({
            map: this.map,
            extData: {
              driver_id: o.driver_id
            },
            zIndex: 12 + dkey + list.length,
            position: [o.longitude, o.latitude],
            offset: new AMap.Pixel(-80, -165), // 相对于基点的偏移位置
            content: component.$el
          })
        )
        // InfoWindow无法同时展开多个，放弃InfoWindow方法显示司机位置
        // const infoWindow = new AMap.InfoWindow({
        //   anchor: 'top-left',
        //   offset: new AMap.Pixel(-20, -35),
        //   content: component.$el
        // })
        // infoWindow.open(this.map, [o.longitude, o.latitude])
      })
      this.driverMarkers = driverMarkers
      if (isSetfit) {
        this.map.setFitView(this.driverMarkers, true, [100, 100, 100, 360])
      }
    },
    async getDriverList (isfeat = false) {
      if (isfeat) this.loading = true
      const { data = [] } = await mapDrivers({
        branch_id: this.branch_id
      })
      if (isfeat) this.loading = false
      this.driverList =
        (data &&
          data.length > 0 &&
          data.filter((o) => o.longitude && o.latitude)) ||
        []
      this.initDriverSeat(this.driverList, isfeat)
    },
    async getTableDriver (params = {}) {
      this.tableLoading = true
      this.tabQuery = { ...this.tabQuery, ...params }

      // 处理业态参数
      let businessType
      let subBusinessType

      if (this.tabQuery.business_type !== '' && this.tabQuery.business_type !== null && !isNaN(this.tabQuery.business_type)) {
        businessType = Number(this.tabQuery.business_type)

        if (businessType === 5 && this.tabQuery.sub_business_type !== null && !isNaN(this.tabQuery.sub_business_type)) {
          subBusinessType = Number(this.tabQuery.sub_business_type)
        }
      }

      const { data } = await dispatchDrivers({
        branch_id: this.branch_id,
        ...this.tabQuery,
        biz_number: this.tabQuery.checked ? this.tabQuery.biz_number : '',
        business_type: businessType,
        sub_business_type: subBusinessType
      })
      this.tableLoading = false
      this.tabDriver = data.data || []
      this.totals = data.meta && data.meta.pagination && data.meta.pagination.total || 0
    },
    initMap () {
      AMapLoader.load({
        key: 'ae07d204562de06e95f7ed805770b18f', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          'AMap.Driving',
          'AMap.InfoWindow',
          'AMap.ToolBar',
          'AMap.Geolocation',
          'AMap.Scale',
          'AMap.ControlBar'
        ] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          this.map = new AMap.Map('map-container', {
            // 设置地图容器id
            viewMode: '3D', // 是否为3D地图模式
            zoom: 10, // 初始化地图级别
            center: [105.602725, 37.076636] // 初始化地图中心点位置
          })
          var styleName = 'amap://styles/light'
          this.map.setMapStyle(styleName)
          var toolbar = new AMap.ToolBar({
            position: {
              bottom: '10px',
              right: '40px'
            }
          })
          this.map.addControl(toolbar)
          var scale = new AMap.Scale()
          this.map.addControl(scale)
          var controlBar = new AMap.ControlBar({
            position: {
              bottom: '110px',
              right: '10px'
            }
          })
          this.map.addControl(controlBar)
          // var geolocation = new AMap.Geolocation({
          //   enableHighAccuracy: true, // 是否使用高精度定位，默认:true
          //   timeout: 10000, // 超过10秒后停止定位，默认：5s
          //   position: 'RB', // 定位按钮的停靠位置
          //   offset: [40, 75], // 定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
          //   zoomToAccuracy: true // 定位成功后是否自动调整地图视野到定位点
          // })
          // this.map.addControl(geolocation)
        })
        .catch((e) => {
          console.log(e)
        })
    }
  }
}
</script>

<style scoped lang="scss">
  .line-checked {
    background-color: #f5f7fa;
    padding: 5px;
    border-radius: 3px;
    font-size: 12px;
    color: #0067e1;
    display: flex;
  }
  .lines-show-warp {
    margin-left: 750px;
  }
  .drive-table-name {
    color: #0067e1;
    cursor: pointer;
    font-size: 12px;
  }
  .full-detail-warp {
    min-height: 400px;
  }
  .code_arrow {
    width: 16px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    cursor: pointer;
    top: 0;
    bottom: 0;
    text-align: center;
    border-radius: 4px 0 0 4px;
    background: #0067e1;
    visibility: visible;
    left: -16px;
    margin: auto;
    box-shadow: 0 0px 5px 0px rgb(0 0 0 / 15%);
    i {
      color: #fff;
      font-size: 12px;
    }
  }
  .driver-list-warp {
    height: 100%;
    background: #fff;
    transition: all 0.3s linear;
    top: 60px;
    right: 0;
    position: absolute;
    z-index: 10;
    margin: 0;
    border-radius: 10px 0 0px 10px;
    box-shadow: 0 0px 5px 0px rgb(0 0 0 / 15%);
    height: calc(100vh - 425px);
    .list-container-arrow {
      padding: 10px;
    }
  }
  .item-num {
    margin-right: 3px;
  }
  .map-component {
    padding: 0px;
    flex: 1;
    height: 100%;
    max-height: 100%;
    position: relative;
    overflow: hidden;

    .refresh-btn {
      border-radius: 50%;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 1;
      box-shadow: 0px 0px 3px 0 rgba(40, 32, 32, 0.2);
    }
  }
  .table-p {
    margin: 0;
    padding: 0;
    line-height: 15px;
  }
  .warp-title {
    font-size: 17px;
    font-weight: 700;
    position: relative;
    margin-left: 10px;
    margin-bottom: 10px;
    span {
      color: #8b96a6;
      font-weight: normal;
      font-size: 15px;
      margin-left: 10px;
    }

    &:before {
      width: 4px;
      border-radius: 5px;
      height: 80%;
      background: #0067e1;
      position: absolute;
      left: -10px;
      top: 0;
      bottom: 0;
      margin: auto;
      content: "";
      display: block;
    }
  }
  #map-container {
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
  }
</style>
<style lang="scss">
  .infoMat {
    font-size: 14px;

    span {
      color: #0067e1;
      margin: 0 3px;
    }
  }

  .amap-info-close {
    display: none;
  }
  .order_ticket {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    align-items: center;
    background-color: #ffffff;
    box-shadow: 0px 0px 3px 0 rgba(40, 32, 32, 0.2);
    position: relative;
    .el-checkbox-button__inner {
      border-radius: 4px !important;
      border: none !important;
      background-color: #ebb563;
      color: #fff;
      padding: 8px 10px;
      font-size: 13px;
    }
    .ticket__arrow {
      position: absolute;
      bottom: -10px;
      color: #ebb563;
      font-size: 16px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
    .arrow_check {
      color: #0067e1;
    }
  }
  .route_ticket {
    width: 165px;
    padding: 2px 6px 6px 6px;
    background-color: #ffffff;
    box-shadow: 0px 0px 3px 0 rgba(40, 32, 32, 0.2);
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
  .flex-item {
    display: flex;
    align-items: center;
  }

  .order-info {
    font-size: 12px;
    margin-bottom: 5px;
    color: #606266;
    .num {
      color: #0067e1;
    }

    .recode {
      color: #67c23a;
    }
  }

  .baseInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;

    .item-info {
      margin: 4px 0;
      white-space: nowrap;
      display: block;
      text-align: center;
      color: #606266;
    }
    .el-tag {
      margin: 0;
      font-size: 12px;
      line-height: 18px;
    }

    .item-info {
      font-size: 12px;
      color: #606266;
    }
  }
  .item-name {
    color: #0067e1;
    font-size: 12px;
    border-bottom: 1px solid #0067e1;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: 5px;
  }
  .surplus {
    color: #67c23a;
  }

  .nosurplus {
    color: #f56c6c;
  }

  .driverModal .el-dialog__body {
    padding: 15px 0;
  }
  .driverModal .el-dialog__footer {
    padding: 0 0 20px;
  }
  .el-input-group__prepend {
    width: 115px !important;
  }

  .driverModal {
    .el-descriptions--small.is-bordered .el-descriptions-item__cell {
      min-width: 80px;
    }
    .el-descriptions--small.is-bordered .el-descriptions-item__content {
      min-width: 280px;
    }
  }

  .order-table td {
    height: 40px;
  }
</style>

<style lang="scss" scoped>
.assign-note-container {
  margin: 15px 0;
  padding: 0 20px;
}

.assign-note-label {
  font-size: 14px;
  margin-bottom: 5px;
  color: #606266;
}
</style>

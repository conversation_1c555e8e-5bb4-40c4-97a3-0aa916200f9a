<template>
  <div class="app-container dispatching-container">
    <div class="hanldle-container">
      <div class="item-filter-warp-container">
        <el-row :gutter="0">
          <el-col :span="3">
            <div class="item-filter-warp">
              <span class="col-label-key">所属业态</span>
              <el-select v-model="business_type" clearable placeholder="全部" size="small" @change="typesChange">
                <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </el-col>

          <el-col v-if="business_type === 5" :span="3">
            <div class="item-filter-warp">
              <span class="col-label-key">子业态</span>
              <el-select v-model="searchQuery.sub_business_type" clearable placeholder="选择子业态" size="small" @change="subBusinessTypeChange">
                <el-option v-for="item in subBusinessTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </el-col>

          <el-col v-if="business_type === 5" :span="3">
            <div class="item-filter-warp">
              <span class="col-label-key">线路方向</span>
              <el-select v-model="searchQuery.direction" clearable placeholder="选择方向" size="small" @change="directionChange">
                <el-option v-for="item in directions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </el-col>

          <el-col v-if="business_type === 5" :span="3">
            <div class="item-filter-warp">
              <span class="col-label-key">所属线路</span>
              <line-select
                v-model="searchQuery.line_class_id" placeholder="选择线路" size="small"
                multiple
              />
            </div>
          </el-col>

          <el-col :span="3">
            <div class="item-filter-warp">
              <span class="col-label-key">所属分台</span>
              <branch-select
                v-model="searchQuery.branch_id" placeholder="选择分台" size="small"
                @change="branchChange"
              />
            </div>
          </el-col>

          <el-col :span="4">
            <div class="item-filter-warp">
              <span class="col-label-key">出发时间</span>
              <el-date-picker
                v-model="searchQuery.start_time" type="date" size="small"
                class="filter-item date-condition" value-format="yyyy-MM-dd"
                placeholder="选择日期"
              />
            </div>
          </el-col>

          <el-col :span="5">
            <div class="item-filter-warp">
              <span class="col-label-key">关键词</span>
              <el-input v-model="searchQuery.search_value" placeholder="请输入搜索内容" size="small">
                <el-select slot="prepend" v-model="searchQuery.search_key" placeholder="请选择">
                  <el-option label="乘客手机号" value="passenger_phone" />
                  <el-option label="订单号" value="order_no" />
                  <el-option label="乘客昵称" value="passenger_name" />
                  <el-option label="司机车牌号" value="driver_card_no" />
                  <el-option label="司机手机号" value="driver_phone" />
                  <el-option label="司机昵称" value="driver_name" />
                </el-select>
              </el-input>
            </div>
          </el-col>

          <el-col :span="3">
            <el-button class="filter-item" type="primary" size="small" @click="handleSearch">
              搜索
            </el-button>
            <el-button class="filter-item" size="small" type="primary" plain @click="handleReset">
              重置
            </el-button>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="0" v-if="business_type === 5">
          <el-col :span="24">
            <line-select
              ref="lineSelect"
              :branch-id="searchQuery.branch_id"
              @change="handleLineChange"
            />
          </el-col>
        </el-row> -->
      </div>
    </div>
    <div ref="map_container" class="map-container-warp">
      <order-list
        v-if="isShowList" ref="left" v-loading="isLoading" :branch_id="searchQuery.branch_id"
        :timeout-count="timeoutCount" :order="order" :meta="meta" :query="searchQuery"
        :custom-width="leftWidth"
        :order-counts-by-time="orderCountsByTime"
        :selected-times="searchQuery.time"
        @openDriver="handleDriver" @getOrder="getOrder" @setOrder="setOrder" @updateDriver="updateDriver"
        @setPoints="setPoints"
        @time-slot-click="handleTimeSlotClick"
        @resetQuery="handleResetFromChild"
      />
      <div class="code_arrow" :style="leftWidth ? `left:${leftWidth}px;` : ''" @click="handleLeft">
        <i :class="listOpened ? 'el-icon-arrow-left' : 'el-icon-arrow-right'" />
      </div>
      <div class="line-drag" :style="`left:${moveLineLeft}px;`" @mousedown="dragLine" />
      <MapContainer
        ref="MapContainer" :branch_id="searchQuery.branch_id" :check-order-info="checkOrderInfo"
        :points="points" :order="searchQuery.biz === 'undispatched' ? order : []" :main-query="searchQuery"
        :style="rightWidth ? `width:${rightWidth}px;` : ''" @fetchOrders="fetchOrders" @changeCheckey="changeCheckey"
      />
    </div>
  </div>
</template>
<script>
import MapContainer from './components/MapContainer'
import OrderList from './components/OrderList'
import { noticeview } from '@/utils'
import BranchSelect from '@/components/BranchSelect'
import LineSelect from '@/views/components/lineIdSelect/index.vue'

import {
  dispatchBranches,
  dispatchOrders,
  fetTimeShort
} from '@/api/business'
import { mapGetters } from 'vuex'
export default {
  name: 'Dispatching',
  components: { MapContainer, OrderList, BranchSelect, LineSelect },
  data() {
    return {
      listOpened: true,
      rightWidth: 0,
      leftWidth: 1000,
      moveLineLeft: 1000,
      isShowList: true,
      checkOrderInfo: {},
      business_type: null,
      searchQuery: {
        page: 1,
        per_page: 10,
        order_type: [],
        sub_business_type: '',
        start_time: new Date().toISOString().slice(0, 10),
        time: [],
        search_key: 'passenger_phone',
        search_value: '',
        biz: 'undispatched',
        start_address_key_list: [],
        end_address_key_list: [],
        start_address_code: '',
        end_address_code: '',
        start_city_name: '',
        end_city_name: '',
        line_class_id: [],
        direction: '',
        branch_id: null,
        passenger_phone: '',
        driver_phone: '',
        order_no: ''
      },
      points: null,
      multipleSelection: [],
      isLoading: false,
      branchs: [],
      order: [],
      meta: {},
      timer: null,
      orderCountsByTime: {}
    }
  },
  computed: {
    ...mapGetters(['info', 'timeoutCount', 'orderCount']),
    types() {
      const arr = []
      for (const val in (this.info && this.info.business_types ? this.info.business_types : {})) {
        arr.push({
          value: Number(val),
          label: this.info && this.info.business_types ? this.info.business_types[val] : ''
        })
      }
      return arr
    },
    subBusinessTypes() {
      return [
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ]
    },
    directions() {
      return [
        { value: 1, label: '上行' },
        { value: 2, label: '下行' }
      ]
    }
  },
  watch: {
    types: {
      handler(n) {
        if (n && n.length) {
          console.log(n)
          this.fetchBranch()
        }
      },
      immediate: true
    }
  },
  async created() {
    window.getOrderList = this.getOrderList

    // 优先从localStorage获取订单号，这是从其他页面跳转过来的情况
    const dispatchOrderNo = localStorage.getItem('dispatchOrderNo')
    const dispatchOrderStartTime = localStorage.getItem('dispatchOrderStartTime')
    console.log(dispatchOrderNo)
    if (dispatchOrderNo) {
      this.searchQuery.search_key = 'order_no'
      this.searchQuery.search_value = dispatchOrderNo
      this.searchQuery.start_time = dispatchOrderStartTime
      this.$nextTick(() => {
        this.handleSearch()
      })
      // 清除localStorage中的订单号，避免影响下次操作
      localStorage.removeItem('dispatchOrderNo')
    }
    await this.fetchBranch()
  },
  mounted() {
    noticeview().setTitle()
    if (this.info && this.info.admin_id) {
      this.$store.commit('websocket/setLocalNum', { num: 0, isread: true })
      localStorage.setItem(
        this.info.admin_id,
        JSON.stringify({ num: 0, isread: true })
      )
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // listOpened = !listOpened
    handleLeft() {
      const width = this.$refs.map_container.clientWidth
      if (this.listOpened) {
        this.leftWidth = 0
        this.moveLineLeft = 0
        this.rightWidth = width
      } else {
        this.leftWidth = 1000
        this.moveLineLeft = 1000
        this.rightWidth = width - this.moveLineLeft
      }

      this.listOpened = !this.listOpened
    },
    dragLine() {
      const width = this.$refs.map_container.clientWidth// 获取盒子的宽
      // 绑定onmousemove
      document.onmousemove = (e) => { // 鼠标点击拖动时
        const m_width = e.clientX - 240
        if (m_width <= 1000 && m_width > 375) {
          this.moveLineLeft = m_width
          this.leftWidth = this.moveLineLeft
        } else if (m_width < 375) {
          console.log(m_width < 375)
          this.moveLineLeft = 375
          this.leftWidth = 375
        } else {
          this.moveLineLeft = 1000
          this.leftWidth = 1000
        }
        this.rightWidth = width - this.moveLineLeft
      }
      document.onmouseup = () => { // 松开鼠标时
        // 解绑
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    branchChange() {
      this.searchQuery.start_area_code = ''
      this.searchQuery.end_area_code = ''
      this.fetchOrders()
    },
    changeCheckey(params) {
      this.$set(this.order[params.index], 'checked', !!params.id)
    },
    setOrder(key = -1, checkAll = false) {
      const orders = Object.assign([], this.order)
      if (key > -1) {
        // 手动选中
        orders.map((o, index) => {
          if (index === key) {
            o.checked = !o.checked
          }
          return o
        })
      } else {
        orders.map((o) => {
          o.checked = false
          return o
        })
      }
      if (checkAll) {
        orders.map((o) => {
          o.checked = true
          return o
        })
      }
      // this.order=[...this.order,...]
      this.order.map((_, i) => {
        this.$set(this.order, i, orders[i])
      })
    },
    setPoints(points) {
      this.points = points
    },
    handleDriver(params) {
      this.$refs['MapContainer'].handleDriver(params)
    },
    updateDriver() {
      this.$refs['MapContainer'].getDriverList()
    },
    fetchOrders() {
      this.getOrderList()
    },
    getOrder(params) {
      this.searchQuery = { ...this.searchQuery, ...params }
      this.getOrderList()
    },

    async getOrderList(loading = true) {
      if (loading) this.isLoading = true
      console.log(this.searchQuery, 'searchQuery')
      const query = Object.assign({}, this.searchQuery)

      // 确保 start_time 是字符串, 如果为空则置空
      if (query.start_time && typeof query.start_time !== 'string') {
        // 尝试转换为 'yyyy-MM-dd' 格式
        try {
          const d = new Date(query.start_time)
          query.start_time = `${d.getFullYear()}-${('0' + (d.getMonth() + 1)).slice(-2)}-${('0' + d.getDate()).slice(-2)}`
        } catch (e) {
          query.start_time = '' // 格式化失败则置空
        }
      } else if (!query.start_time) {
        query.start_time = '' // 如果为空或 undefined，确保是空字符串
      }

      this.$store.dispatch('unread/getOrderCount', query)
      const { data } = await dispatchOrders(query)
      if (loading) this.isLoading = false
      this.order = data.data || []
      this.meta = data.meta
      // Fetch time slot counts after getting orders
      if (this.searchQuery.biz === 'undispatched') {
        this.fetchOrderCounts()
      } else {
        this.orderCountsByTime = {} // Clear counts if not on undispatched tab
      }
    },
    async fetchOrderCounts() {
      try {
        const query = {
          ...this.searchQuery,
          biz: this.searchQuery.biz, // Pass the current biz type
          // Remove pagination params as they are not needed for counts
          page: undefined,
          per_page: undefined
        }
        // Remove time[] from the count query itself
        delete query.time

        const { data } = await fetTimeShort(query)
        // Process the data if it's in the expected format { date: [{ time: 'HH:mm', total: count }] }
        const counts = {}
        if (data && typeof data === 'object') {
          // Assuming data structure is { "YYYY-MM-DD": [ { time: "HH:mm", total: N }, ... ] }
          // Or potentially just { time: "HH:mm", total: N } if grouped only by time
          const dateKey = query.start_time || Object.keys(data)[0] // Use selected date or first key
          if (data[dateKey] && Array.isArray(data[dateKey])) {
            data[dateKey].forEach(item => {
              counts[item.time] = item.total
            })
          } else if (Array.isArray(data)) { // Handle if API returns flat array [{ time: "HH:mm", total: N }]
            data.forEach(item => {
              counts[item.time] = item.total
            })
          }
        }
        this.orderCountsByTime = counts
      } catch (error) {
        console.error('Error fetching order counts by time:', error)
        this.orderCountsByTime = {} // Reset on error
      }
    },
    handleTimeSlotClick(time) {
      if (time === null) {
        // Clear selection
        this.searchQuery.time = []
      } else {
        const index = this.searchQuery.time.indexOf(time)
        if (index > -1) {
          // Remove if already selected
          this.searchQuery.time.splice(index, 1)
        } else {
          // Add if not selected
          this.searchQuery.time.push(time)
        }
      }
      // Refetch orders with the new time filter
      this.getOrderList()
    },
    typesChange(type) {
      this.business_type = type
      this.searchQuery.order_type = [type]
      this.searchQuery.sub_business_type = ''
      this.searchQuery.direction = ''
      this.searchQuery.time = []
      this.fetchOrders()
    },
    subBusinessTypeChange(type) {
      this.searchQuery.sub_business_type = type
      this.fetchOrders()
    },
    directionChange() {
      this.fetchOrders()
    },
    async fetchBranch() {
      // 获取分台同时显示默认业态
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label:
              o.loads === 0 ? o.mchname : `${o.mchname}-待指派${o.loads}单`,
            value: o.admin_id
          }
        })

        // 不需要手动添加"全部"选项，BranchSelect组件会自动添加
        // 直接设置branch_id为null (表示"全部")
        this.$set(this.searchQuery, 'branch_id', null)
        this.fetchOrders()
      }
    },
    formatStartCode(e) {
      return e.map((o) => ({
        value: o.city_id,
        label: o.city_name,
        children: o.city_attributes ? o.city_attributes.map((c) => ({
          value: c.area_id,
          label: c.area_name
        })) : []
      }))
    },
    handleSearch() {
      this.fetchOrders()
    },
    handleReset() {
      this.business_type = null
      this.searchQuery = {
        page: 1,
        per_page: 10,
        order_type: [],
        sub_business_type: '',
        start_time: new Date().toISOString().slice(0, 10),
        search_key: 'passenger_phone',
        search_value: '',
        biz: 'undispatched',
        start_address_key_list: [],
        end_address_key_list: [],
        start_address_code: '',
        end_address_code: '',
        start_city_name: '',
        end_city_name: '',
        line_class_id: [],
        direction: '',
        branch_id: null,
        passenger_phone: '',
        driver_phone: '',
        order_no: '',
        time: []
      }
      // 重置方向选择和线路选择
      if (this.$refs.lineSelect) {
        this.$refs.lineSelect.resetSelection()
      }
      this.fetchBranch()
    },
    // 处理来自子组件的重置请求
    handleResetFromChild(resetQuery) {
      this.business_type = null
      this.searchQuery = resetQuery
      this.fetchBranch()
    }
  }
}
</script>
<style lang="scss" scoped>
.dispatching-container {
  font-size: 14px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;

  .el-select, .el-input, .el-button, .el-date-picker {
    font-size: 14px;
  }
}

.hanldle-container {
  width: 100%;
  padding: 0 10px 0 0;
  flex-shrink: 0;
}

.item-filter-warp-container {
  background: #f0f2f5;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 5px;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;

  .el-row {
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    display: flex;
    flex-wrap: wrap;

    .el-col {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}

.app-container {
  padding: 10px 10px 0 10px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.item-filter-warp {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;

  ::v-deep .el-tag {
    cursor: pointer;
  }
}

.col-label-key {
  flex: 0 0 75px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  line-height: 30px;

  &::after {
    content: ':';
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}

.search-input-container {
  display: flex;
  align-items: center;
  width: 100%;

  .el-button {
    margin-left: 5px;
  }
}

.line-drag {
  position: absolute;
  width: 20px;
  height: 100%;
  cursor: col-resize;
  overflow: hidden;
  z-index: 4;
  user-select: none;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    display: block;
    margin: auto;
    width: 0;
    border: 1px dashed #ccc;
  }
}

.map-container-warp {
  display: flex;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 180px);
  max-height: calc(100vh - 180px);
}

.code_arrow {
  width: 16px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  cursor: pointer;
  z-index: 5;
  top: 0;
  bottom: 0;
  text-align: center;
  border-radius: 0 4px 4px 0;
  background: #0067e1;
  visibility: visible;
  margin: auto;
  box-shadow: 0 0px 5px 0px rgb(0 0 0 / 15%);

  i {
    color: #fff;
    font-size: 12px;
  }
}

.filter-item {
  margin-left: 8px;
}

@media screen and (max-width: 1200px) {
  .item-filter-warp {
    margin-bottom: 10px;
  }
}

.time-slot-tag {
  margin-right: 5px;
  cursor: pointer;
}
</style>

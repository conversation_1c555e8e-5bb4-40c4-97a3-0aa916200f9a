<template>
  <div class="main">
    <a-form
      id="formLogin"
      class="user-layout-login"
      ref="formLogin"
      :form="form"
      @submit="handleSubmit"
    >
      <h3 class="form-title">平台账号登录</h3>
      <a-alert v-if="isLoginError" type="error" showIcon style="margin-bottom: 24px;" message="账号或密码错误" />
      <a-form-item>
        <a-input
          size="large"
          type="text"
          placeholder="账户"
          v-decorator="[
            'account',
            {rules: [{ required: true, message: '请输入账户名' }, { validator: handleUsernameOrEmail }], validateTrigger: 'change'}
          ]"
        >
          <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }"/>
        </a-input>
      </a-form-item>

      <a-form-item>
        <a-input-password
          size="large"
          placeholder="密码"
          v-decorator="[
            'password',
            {rules: [{ required: true, message: '请输入密码' }], validateTrigger: 'blur'}
          ]"
        >
          <a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }"/>
        </a-input-password>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-decorator="['rememberMe', { valuePropName: 'checked' }]">记住我</a-checkbox>
      </a-form-item>

      <a-form-item>
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="login-button"
          :loading="state.loginBtn"
          :disabled="state.loginBtn"
        >登录</a-button>
      </a-form-item>
    </a-form>

    <two-step-captcha
      v-if="requiredTwoStepCaptcha"
      :visible="stepCaptchaVisible"
      @success="stepCaptchaSuccess"
      @cancel="stepCaptchaCancel"
    ></two-step-captcha>
  </div>
</template>

<script>
// import md5 from 'md5'
import TwoStepCaptcha from '@/components/tools/TwoStepCaptcha'
import { mapActions } from 'vuex'
import { timeFix } from '@/utils/util'

export default {
  components: {
    TwoStepCaptcha
  },
  data () {
    return {
      loginBtn: false,
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      isLoginError: false,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        smsSendBtn: false
      }
    }
  },
  created () {
    // 禁用两步验证码功能
    this.requiredTwoStepCaptcha = false
  },
  methods: {
    // 使用saas命名空间的login以及公共的Login方法
    ...mapActions(['Logout']),
    ...mapActions('saas', ['login']),
    // handler
    handleUsernameOrEmail (rule, value, callback) {
      const { state } = this
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        state.loginType = 0
      } else {
        state.loginType = 1
      }
      callback()
    },
    handleSubmit (e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state,
        login
      } = this

      state.loginBtn = true

      const validateFieldsKey = ['account', 'password']

      validateFields(validateFieldsKey, { force: true }, (err, values) => {
        if (!err) {
          console.log('登录表单数据:', values)
          // 构建登录参数
          const loginParams = {
            account: values.account,
            password: values.password,
            remember_me: values.rememberMe
          }
          // 使用saas模块的login方法
          console.log('开始调用login方法，参数:', loginParams)
          login(loginParams)
            .then((res) => {
              console.log('登录API响应:', res)
              if (res && res.status === 'success' && res.code === 200) {
                console.log('登录成功，token:', res.data.access_token)
                this.loginSuccess()
              } else {
                console.error('登录响应格式不符合预期:', res)
                this.requestFailed(new Error('登录失败，服务器返回格式不符合预期'))
              }
            })
            .catch(err => {
              console.error('登录API错误:', err)
              this.requestFailed(err)
            })
            .finally(() => {
              state.loginBtn = false
            })
        } else {
          console.error('表单验证错误:', err)
          setTimeout(() => {
            state.loginBtn = false
          }, 600)
        }
      })
    },
    stepCaptchaSuccess () {
      this.loginSuccess()
    },
    stepCaptchaCancel () {
      this.Logout().then(() => {
        this.loginBtn = false
        this.stepCaptchaVisible = false
      })
    },
    loginSuccess () {
      console.log('登录成功，准备跳转到商户列表页')
      this.$router.push({ path: '/merchant/list' })
      // 延迟 1 秒显示欢迎信息
      setTimeout(() => {
        this.$notification.success({
          message: '欢迎',
          description: `${timeFix()}，欢迎回来`
        })
      }, 1000)
      this.isLoginError = false
    },
    requestFailed (err) {
      console.error('登录请求失败:', err)
      this.isLoginError = true
      let errorMessage = '未知错误'

      if (err && err.response && err.response.data) {
        errorMessage = err.response.data.message || '请求出现错误，请稍后再试'
      } else if (err && err.message) {
        errorMessage = err.message
      }

      console.error('错误信息:', errorMessage)

      this.$notification['error']({
        message: '登录失败',
        description: errorMessage,
        duration: 4
      })
    }
  }
}
</script>

<style lang="less" scoped>
.user-layout-login {
  label {
    font-size: 14px;
  }

  .form-title {
    font-size: 18px;
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-weight: 600;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
    border-radius: 4px;
    background: #0052CC;
    border-color: #0052CC;

    &:hover, &:focus {
      background: #1565C0;
      border-color: #1565C0;
    }
  }

  .ant-input-affix-wrapper .ant-input {
    height: 40px;
    padding-left: 40px;
  }

  .ant-input-affix-wrapper .ant-input-prefix {
    left: 15px;
  }
}
</style>

<template>
  <div id="map-container" class="points-views" />
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mapParams: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      smarker: null,
      emarker: null,
      mapPoints: {
        start_obj: null,
        end_obj: null
      }
    }
  },
  methods: {
    setPoints(tag, params) {
      this.map.clearMap()
      const ponitsMarkers = []
      const points_ = tag === 'start' ? params.fixed_boarding_point : params.fixed_dropoff_point

      points_.map((o) => {
        ponitsMarkers.push(
          new AMap.Marker({
            map: this.map,
            position: [o.longitude, o.latitude],
            offset: new AMap.Pixel(-12, -30), // 相对于基点的偏移位置
            title: o.alias,
            label: {
              offset: new AMap.Pixel(0, -5), // 设置文本标注偏移量
              content: "<div class='info'>" + o.alias + '</div>', // 设置文本标注内容
              direction: 'top' // 设置文本标注方位
            }
          })
        )
      })
      this.map.setFitView(ponitsMarkers)
    },
    setPolygon(tag, params) {
      if (!this.map) {
        return
      }
      this.map.clearMap()
      const _polygons = tag === 'start' ? params.start_polygon : params.end_polygon

      const polygonView = new AMap.Polygon({
        path: _polygons.map((o) =>
          o.split(',')
        ),
        fillColor: tag === 'start' ? '#1c73e2' : '#e90000',
        strokeOpacity: 1,
        fillOpacity: 0.3,
        strokeColor: tag === 'start' ? '#2b8cbe' : '#e90000',
        strokeWeight: 1,
        strokeStyle: 'dashed',
        strokeDasharray: [5, 5]
      })
      this.map.add(polygonView)
      this.map.setFitView()
    },
    initMap() {
      return new Promise((resolve, reject) => {
        AMapLoader.load({
          key: 'ae07d204562de06e95f7ed805770b18f', // 申请好的Web端开发者Key，首次调用 load 时必填
          version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
          plugins: ['AMap.Driving', 'AMap.InfoWindow'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        })
          .then((AMap) => {
            this.map = new AMap.Map('map-container', {
              // 设置地图容器id
              viewMode: '3D', // 是否为3D地图模式
              zoom: 5, // 初始化地图级别
              center: [105.602725, 37.076636] // 初始化地图中心点位置
            })
            resolve()
          })
          .catch((e) => {
            reject()
            console.log(e)
          })
      })
    }
  }
}
</script>

<style scoped lang="scss">
#map-container {
  padding: 0px;
  width: 100%;
  height: 270px;
}
</style>
<style lang="scss">
.infoMat {
  font-size: 14px;

  span {
    color: #0067e1;
    margin: 0 3px;
  }
}

.amap-info-close {
  display: none;
}
</style>

<template>
  <div class="seat-progress">
    <div
      v-for="(item, index) in segmentCount"
      :key="index"
      :class="['progress-segment', index < activeSegments ? 'active' : '']"
    />
  </div>
</template>

<script>
export default {
  name: 'SeatProgress',
  props: {
    // 当前值
    currentValue: {
      type: Number,
      default: 3
    },
    // 最大值
    maxValue: {
      type: Number,
      default: 6
    },
    // 段落数量
    segmentCount: {
      type: Number,
      default: 6
    }
  },
  computed: {
    // 计算当前应激活的段落数
    activeSegments() {
      // 确保当前值不超过最大值
      const normalizedValue = Math.min(this.currentValue, this.maxValue)
      // 根据当前值与最大值的比例计算应该激活的段落数
      return Math.ceil((normalizedValue / this.maxValue) * this.segmentCount)
    }
  }
}
</script>

<style lang="scss" scoped>
.seat-progress {
  display: flex;
  gap: 2px;
  flex: 1;
  width: 100%; // 使用100%宽度以适应父容器

  .progress-segment {
    flex: 1; // 每个段落平均分配空间
    height: 6px;
    background-color: #ebeef5;
    border-radius: 3px;

    &.active {
      background-color: #409eff;
    }
  }
}
</style>

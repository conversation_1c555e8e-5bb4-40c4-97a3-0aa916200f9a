<template>
  <div>
    <div ref="printArea" class="print-container" style="display: none;">
      <!-- 顶部信息栏 -->
      <div class="print-item" :style="dateStyle">{{ formatDate(data.orders[0]?.start_time) }}</div>
      <div class="print-item" :style="lineNoStyle">{{ getLineInfo() }}</div>
      <div class="print-item" :style="timeStyle">{{ getStartTime() }}</div>
      <div class="print-item" :style="driverStyle">{{ getDriverInfo() }}</div>

      <!-- 表格内容 -->
      <div v-for="(order, index) in data.orders" :key="order.order_id">
        <div class="print-item" :style="getOrderCellphoneStyle(index)">{{ order.passenger.cellphone }}</div>
        <div class="print-item" :style="getOrderStartPointStyle(index)">{{ data.line_class.start_name }}</div>
        <div class="print-item" :style="getOrderEndPointStyle(index)">{{ data.line_class.end_name }}</div>
        <div class="print-item" :style="getOrderSeatNoStyle(index)">{{ getTicketSeatNo(order) }}</div>
        <div class="print-item" :style="getOrderPriceStyle(index)">¥ {{ order.real_price }}</div>
      </div>

      <!-- 底部合计 -->
      <div class="print-item" :style="totalPriceStyle">{{ calculateTotalPrice() }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PrecisePrintComponent',
  props: {
    // 打印数据
    data: {
      type: Object,
      required: true
    },
    // 页面边距设置
    pageMargin: {
      type: Object,
      default: () => ({
        top: '10mm',
        right: '10mm',
        bottom: '10mm',
        left: '10mm'
      })
    }
  },

  computed: {
    // 顶部信息样式
    dateStyle() {
      return {
        position: 'absolute',
        left: '15mm',
        top: '15mm',
        fontSize: '10pt',
        fontWeight: 'bold'
      }
    },
    lineNoStyle() {
      return {
        position: 'absolute',
        left: '60mm',
        top: '15mm',
        fontSize: '10pt',
        fontWeight: 'bold'
      }
    },
    timeStyle() {
      return {
        position: 'absolute',
        left: '100mm',
        top: '15mm',
        fontSize: '10pt',
        fontWeight: 'bold'
      }
    },
    driverStyle() {
      return {
        position: 'absolute',
        left: '140mm',
        top: '15mm',
        fontSize: '10pt',
        fontWeight: 'bold'
      }
    },

    // 底部合计样式
    totalPriceStyle() {
      return {
        position: 'absolute',
        right: '20mm',
        bottom: '20mm',
        fontSize: '12pt',
        fontWeight: 'bold'
      }
    }
  },

  methods: {
    // 生成订单行电话样式（动态定位）
    getOrderCellphoneStyle(index) {
      return {
        position: 'absolute',
        left: '15mm',
        top: `${30 + (index * 10)}mm`,
        fontSize: '10pt'
      }
    },

    // 生成订单上车点样式
    getOrderStartPointStyle(index) {
      return {
        position: 'absolute',
        left: '60mm',
        top: `${30 + (index * 10)}mm`,
        fontSize: '10pt'
      }
    },

    // 生成订单下车点样式
    getOrderEndPointStyle(index) {
      return {
        position: 'absolute',
        left: '100mm',
        top: `${30 + (index * 10)}mm`,
        fontSize: '10pt'
      }
    },

    // 生成座位号样式
    getOrderSeatNoStyle(index) {
      return {
        position: 'absolute',
        left: '140mm',
        top: `${30 + (index * 10)}mm`,
        fontSize: '10pt'
      }
    },

    // 生成价格样式
    getOrderPriceStyle(index) {
      return {
        position: 'absolute',
        right: '20mm',
        top: `${30 + (index * 10)}mm`,
        fontSize: '10pt',
        textAlign: 'right'
      }
    },

    // 格式化日期 (从 "2025-03-27 00:13:00" 到 "2025 03 19")
    formatDate(dateString) {
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year} ${month} ${day}`
    },

    // 获取线路信息 (A05 闪灯)
    getLineInfo() {
      // 这里仅为示例，实际上需要根据你的数据结构进行调整
      return 'A05 闪灯'
    },

    // 获取发车时间
    getStartTime() {
      // 从 start_time 中提取时间部分
      return this.data?.start_time || '08:00'
    },

    // 获取司机信息
    getDriverInfo() {
      // 提取排序为1的司机
      const mainDriver = this.data.drivers.find(driver => driver.pivot.sort === 1) || this.data.drivers[0]
      return mainDriver ? `1号${mainDriver.name}` : ''
    },

    // 获取座位号信息
    getTicketSeatNo(order) {
      if (order.dingzhikeyun_tickets && order.dingzhikeyun_tickets.length > 0) {
        // 这里假设座位号存在于tickets中，根据实际数据结构调整
        return order.dingzhikeyun_tickets[0].seat_number || '1'
      }
      return '1' // 默认座位号
    },

    // 计算总价
    calculateTotalPrice() {
      const total = this.data.orders.reduce((sum, order) => {
        return sum + parseFloat(order.real_price)
      }, 0)
      return `${total.toFixed(2)}`
    },

    print() {
      // 获取打印区域引用
      const printArea = this.$refs.printArea

      // 保存原始显示状态
      const originalDisplay = printArea.style.display

      // 添加打印专用样式
      const printStyle = document.createElement('style')
      printStyle.setAttribute('type', 'text/css')
      printStyle.innerHTML = `
        @page {
          size: A4 portrait;
          margin: ${this.pageMargin.top} ${this.pageMargin.right} ${this.pageMargin.bottom} ${this.pageMargin.left};
        }
        @media print {
          body * { visibility: hidden; }
          .print-container, .print-container * { visibility: visible; }
          .print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `
      document.head.appendChild(printStyle)

      // 显示打印区域
      printArea.style.display = 'block'

      // 确保DOM已更新
      this.$nextTick(() => {
        // 触发打印
        window.print()

        // 打印完成后的清理
        setTimeout(() => {
          // 移除打印样式
          document.head.removeChild(printStyle)
          // 恢复原始显示状态
          printArea.style.display = originalDisplay
          // 通知打印完成
          this.$emit('printed')
        }, 100)
      })
    }
  }
}
</script>

<style scoped>
.print-container {
  position: relative;
  width: 210mm; /* A4宽度 */
  min-height: 297mm; /* A4高度 */
  margin: 0 auto;
  padding: 0;
  background-color: white;
  box-sizing: border-box;
  font-family: "SimSun", "宋体", Arial, sans-serif;
}

/* 打印时的样式 */
@media print {
  .print-container {
    width: 100%;
    height: auto;
  }

  /* 避免分页 */
  .print-container {
    page-break-after: avoid;
    page-break-before: avoid;
  }
}
</style>

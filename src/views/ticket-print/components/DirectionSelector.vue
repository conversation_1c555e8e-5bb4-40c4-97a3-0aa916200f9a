<template>
  <div class="item-filter-warp" style="margin: 5px 0">
    <span class="col-label-key">方向选择</span>
    <div class="search-city">
      <div class="from-city">
        <el-cascader
          ref="startCascader"
          v-model="startAddressKeyList"
          :options="startCode"
          clearable
          size="mini"
          :props="props"
          @change="onStartCodeChange"
        />
      </div>
      <i class="icon-change" @click="onExchange" />
      <div class="to-city">
        <el-cascader
          ref="endCascader"
          v-model="endAddressKeyList"
          :options="endCode"
          clearable
          size="mini"
          :props="props"
          @change="onEndCodeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DirectionSelector',
  props: {
    value: {
      type: Object,
      default: () => ({
        start_address_key_list: [],
        end_address_key_list: [],
        start_address_code: '',
        end_address_code: '',
        start_city_name: '',
        end_city_name: ''
      })
    },
    startCode: {
      type: Array,
      default: () => []
    },
    endCode: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      props: {
        checkStrictly: true,
        expandTrigger: 'hover'
      }
    }
  },
  computed: {
    startAddressKeyList: {
      get() {
        return this.value.start_address_key_list
      },
      set(val) {
        this.$emit('input', { ...this.value, start_address_key_list: val })
      }
    },
    endAddressKeyList: {
      get() {
        return this.value.end_address_key_list
      },
      set(val) {
        this.$emit('input', { ...this.value, end_address_key_list: val })
      }
    }
  },
  methods: {
    onStartCodeChange(e) {
      if (!e || e.length === 0) return
      const startCityName = this.spliceAddressName(e, this.startCode)
      const startAddressCode = e[e.length - 1]
      this.$emit('input', {
        ...this.value,
        start_address_key_list: e,
        start_address_code: startAddressCode,
        start_city_name: startCityName
      })
      this.$emit('startChange', e)
    },
    onEndCodeChange(e) {
      if (!e || e.length === 0) return
      const endCityName = this.spliceAddressName(e, this.endCode)
      const endAddressCode = e[e.length - 1]
      this.$emit('input', {
        ...this.value,
        end_address_key_list: e,
        end_address_code: endAddressCode,
        end_city_name: endCityName
      })
      this.$emit('endChange', e)
    },
    onExchange() {
      const temp = { ...this.value }
      this.$emit('input', {
        ...this.value,
        start_address_key_list: temp.end_address_key_list,
        end_address_key_list: temp.start_address_key_list,
        start_address_code: temp.end_address_code,
        end_address_code: temp.start_address_code,
        start_city_name: temp.end_city_name,
        end_city_name: temp.start_city_name
      })
      this.$emit('exchange')
    },
    spliceAddressName(keys, options) {
      const nameArr = []
      for (const item of keys) {
        let name = ''
        for (const option of options) {
          if (option.value === item) {
            name = option.label
            break
          }
          if (option.children) {
            const child = option.children.find((c) => c.value === item)
            if (child) {
              name = child.label
              break
            }
          }
        }
        nameArr.push(name)
      }
      return nameArr.join('-')
    }
  }
}
</script>

<style lang="scss" scoped>
.item-filter-warp {
  display: flex;
  align-items: flex-start;
}

.col-label-key {
  flex: 0 0 75px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  line-height: 30px;

  &::after {
    content: ':';
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}

.search-city {
  display: flex;
  position: relative;
  align-items: center;

  .icon-change {
    cursor: pointer;
    width: 24px;
    height: 24px;
    min-width: 24px;
    margin: 0 12px;
    background: url('../../../assets/icon-chage.png') no-repeat;
    background-size: cover;
  }

  .from-city,
  .to-city {
    width: 50%;
  }
}
</style>

<template>
  <div class="item-filter-warp">
    <span class="col-label-key">线路选择</span>
    <div class="tag-selected-warp">
      <el-tag
        :type="!selectedLineClassId ? '' : 'info'"
        size="small"
        :effect="!selectedLineClassId ? 'dark' : 'plain'"
        @click="onSelectLine('', '')"
      >
        全部
      </el-tag>
      <template v-for="(lineItem, i) in validTimetablesList">
        <el-dropdown
          v-if="lineItem.times && lineItem.times.length > 1"
          :key="i"
          size="small"
          trigger="click"
          @command="(time) => onSelectLine(lineItem.line_class_id, time)"
        >
          <el-tag
            size="small"
            :type="selectedLineClassId === lineItem.line_class_id ? '' : 'info'"
            :effect="selectedLineClassId === lineItem.line_class_id ? 'dark' : 'plain'"
            @click="onSelectLine(lineItem.line_class_id, '')"
          >
            {{ lineItem.start_name }}→{{ lineItem.end_name }}
            <el-divider direction="vertical" />
            {{ selectedTime }}
            <i class="el-icon-arrow-down el-icon--right" />
          </el-tag>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="tim in lineItem.times" :key="tim" :command="tim">
              {{ tim }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tag
          v-else
          :key="i"
          :type="selectedLineClassId === lineItem.line_class_id ? '' : 'info'"
          size="small"
          :effect="selectedLineClassId === lineItem.line_class_id ? 'dark' : 'plain'"
          @click="onSelectLine(lineItem.line_class_id, lineItem.times && lineItem.times.length ? lineItem.times[0] : '')"
        >
          {{ lineItem.start_name }}→{{ lineItem.end_name }}
          {{ lineItem.times && lineItem.times.length ? lineItem.times[0] : '' }}
        </el-tag>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RouteSelector',
  props: {
    value: {
      type: Object,
      default: () => ({
        line_class_id: '',
        time: ''
      })
    },
    timetablesList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    selectedLineClassId() {
      return this.value.line_class_id
    },
    selectedTime() {
      return this.value.time
    },
    validTimetablesList() {
      // 确保timetablesList是数组
      if (!this.timetablesList || !Array.isArray(this.timetablesList)) {
        console.warn('RouteSelector: timetablesList is not an array!', this.timetablesList)
        return []
      }
      return this.timetablesList
    }
  },
  methods: {
    onSelectLine(lineClassId, time) {
      this.$emit('input', {
        line_class_id: lineClassId,
        time: time
      })
      this.$emit('change', { line_class_id: lineClassId, time: time })
    }
  }
}
</script>

<style lang="scss" scoped>
.item-filter-warp {
  display: flex;
  align-items: flex-start;

  ::v-deep .el-tag {
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.col-label-key {
  flex: 0 0 75px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  line-height: 30px;

  &::after {
    content: ':';
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}

.tag-selected-warp {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
</style>

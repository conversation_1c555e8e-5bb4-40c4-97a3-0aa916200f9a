<template>
  <div class="app-container">
    <time-line />
  </div>
</template>

<script>
import TimeLine from './components/TimeLine'

export default {
  name: 'TicketPrint',
  components: { TimeLine },
  data() {
    return {
    }
  },

  computed: {

  },
  watch: {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.el-form {
  width: 50%;
  padding-left: 8px;
}

.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

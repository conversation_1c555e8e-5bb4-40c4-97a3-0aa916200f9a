<template>
  <!---->
  <div class="app-container">
    <el-radio-group v-model="activeName" size="small">
      <el-radio-button v-for="item in permissions" :key="item.permission" :label="item.permission">{{ item.meta.title
      }}</el-radio-button>
    </el-radio-group>
    <!-- <data-view v-if="activeName === 'statistics'" /> -->
    <logs v-if="activeName === 'reward'" />
    <setting v-if="activeName === 'setting'" />
    <!-- <el-empty v-else description="暂无可配置权限" /> -->
  </div>
</template>

<script>
import DataView from './components/DataView'
import Logs from './components/Logs'
import Setting from './components/Setting'
export default {
  name: 'MarketingTool',
  components: { DataView, Logs, Setting },
  data() {
    return {
      activeName: '',
      loading: false,
      insuranceSwitch: 0,
      insuranceData: {},
      permissions: [
        // {
        //   meta: { title: "数据统计" },
        //   permission: "statistics",
        // },
        {
          meta: { title: '奖励记录' },
          permission: 'reward'
        },
        {
          meta: { title: '邀请有礼设置' },
          permission: 'setting'
        }
      ]
    }
  },
  mounted() {
    this.activeName = this.permissions && this.permissions[0].permission
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.el-form {
  width: 50%;
  padding-left: 8px;
}

.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

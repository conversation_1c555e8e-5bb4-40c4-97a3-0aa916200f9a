<template>
  <div class="config-container component-container">

    <div class="hanldle-container">
      <div class="filter-container">
        <span class="label-key">时间</span>
        <el-date-picker v-model="searchQuery.create_time" size="small" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" unlink-panels />
      </div>

      <el-button size="mini" type="primary" @click="fetchOrders">查询</el-button>
    </div>
    <el-table v-loading="userLoading" :data="recordList" element-loading-text="" fit highlight-current-row size="small">
      <el-table-column label="统计时间" min-width="160" prop="receive_time" />

      <el-table-column label="当天奖励发放次数" min-width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.start_time }} - {{ scope.row.end_time }}
        </template>
      </el-table-column>
      <el-table-column label="当天奖励发放情况" min-width="160" prop="receive_time" />
    </el-table>
    <div class="bottom-container">
      <div class="left-content" />
      <el-pagination v-if="usertotal > 0" layout="prev, pager, next" background :page-size="userQuery.per_page"
        :total="usertotal" :current-page="userQuery.page" style="text-align: right" @current-change="userChanges" />
    </div>
  </div>
</template>
<script>
import { couponsRecord, delLogs } from '@/api/marketing'
export default {
  props: {
    promotion: {
      type: Number
    }
  },
  data() {
    return {
      searchQuery: {},
      userQuery: {
        page: 1,
        per_page: 15,
        promotion: ''
      },
      data: {},
      userLoading: false,
      recordList: [],
      usertotal: 0,
      recordVisible: false
    }
  },
  methods: {
    userChanges(val) {
      this.userQuery.page = val
      this.getUsers()
    },
    selectUser() {
      this.recordVisible = true
      this.userQuery.promotion = this.promotion
      this.getUsers()
    },
    getUsers() {
      this.userLoading = true
      couponsRecord(this.userQuery).then((response) => {
        console.log(response)
        if (response.code === 200) {
          this.recordList = Array.isArray(response.data) ? response.data : []
          //   this.usertotal = response.data.meta.total
        }
        this.userLoading = false
      })
    },
    async logDelete(params) {
      await this.$confirm('确认删除当前领取记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await delLogs({
        promotion: params.market_activity_id,
        coupon: params.coupon_record_id
      })
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.getUsers()
    },
    cancel() {
      this.recordVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.fix-input {}

.label-key {
  margin: 0 10px;
}

.user-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;

  .user_num {
    display: flex;
    justify-content: flex-start;

    p {
      margin-right: 12px;
      font-style: 14px;
    }
  }
}

.head_ico {
  width: 24px;
  height: 24px;
  object-fit: cover;
}
</style>

<template>
  <div class="config-container">
    <el-form ref="referralData" v-loading="loading" label-width="110px" :rules="rules" :model="referralData">
      <el-form-item label="是否开启活动" class="label-bg">
        <el-switch
          v-model="referralData.enabled"
          class="switch"
          inline-prompt
          :inactive-value="false"
          :active-value="true"
        />
        <span class="label-key">开启后，成功邀请新人注册下单，邀请人可以得到不同的平台奖励</span>
      </el-form-item>
      <div class="item-warp-form">
        <h6 class="item-title">邀请有礼活动入口</h6>
        <div class="item-warp-form-content">
          <el-form-item label="" prop="vehicle_owner">
            <p class="link-box">
              <strong>H5链接</strong>：{{ `${baseLink}/micro-apps/invitation/?callback=${info.ciphertext}` }}
              <el-button
                class="filter-item"
                size="medium"
                type="text"
                @click="handleCopy(`${baseLink}/micro-apps/invitation/?callback=${info.ciphertext}`)"
              >
                复制链接
              </el-button>
            </p>
            <p class="link-box">
              <strong>小程序链接（暂未开通）</strong>：<del>{{ '#小程序://乘乘出行平台/5dO3FJKpDQX2tEq' }}</del>
              <!-- <el-button
                  class="filter-item"
                  size="medium"
                  type="text"
                  @click="handleCopy(`#小程序://乘乘出行平台/5dO3FJKpDQX2tEq`)"
                >
                  复制链接
                </el-button> -->
            </p>
          </el-form-item>
        </div>
      </div>

      <div class="item-warp-form">
        <h6 class="item-title">基础设置</h6>
        <div class="item-warp-form-content">
          <el-form-item label="活动时间" prop="timeSlot">
            <el-date-picker
              v-model="referralData.timeSlot"
              size="small"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="活动开始时间"
              end-placeholder="活动结束时间"
              unlink-panels
            />
          </el-form-item>
          <el-form-item label="活动标题" prop="setup.title">
            <el-input v-model="referralData.setup.title" placeholder="请输入" style="width: 400px" />
          </el-form-item>
          <el-form-item label="活动规则" prop="setup.rule">
            <el-input
              v-model="referralData.setup.rule"
              :autosize="{ minRows: 4, }"
              class="trainsInput"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </div>
      </div>
      <div class="item-warp-form">
        <h6 class="item-title">分享设置</h6>

        <div class="item-warp-form-content">
          <el-form-item label="分享标题" prop="setup.share_tile">
            <el-input v-model="referralData.setup.share_tile" placeholder="请输入" style="width: 400px" />
          </el-form-item>
          <el-form-item label="分享说明" prop="setup.share_desc">
            <el-input v-model="referralData.setup.share_desc" placeholder="请输入" style="width: 400px" />
          </el-form-item>
          <el-form-item label="分享缩略图" class="cover_url_content" prop="setup.share_image">
            <img-upload v-model="referralData.setup.share_image" />
          </el-form-item>
          <el-form-item label="新人领取成功弹窗" class="cover_url_content" prop="setup.invitee_user_image">
            <img-upload v-model="referralData.setup.invitee_user_image" />
          </el-form-item>
        </div>
      </div>
      <div class="item-warp-form">
        <h6 class="item-title">邀请人礼包</h6>
        <div class="flex-form">
          <div v-if="referralData.setup" class="item-warp-form-content">
            <el-switch
              v-model="referralData.fresh_user_enabled"
              class="switch"
              inline-prompt
              :inactive-value="false"
              :active-value="true"
              @change="(_) => changeSwitch(_, 'fresh_user_reward')"
            />
            <span class="switch-label">新用户注册后发放</span>
            <el-form-item label="奖励设置" prop="license_plate_number" class="block-item-form">
              <el-radio-group
                v-model="referralData.setup.fresh_user_reward.reward_type"
                :disabled="!referralData.fresh_user_enabled"
              >
                <el-radio disabled :label="4"><span class="checkbox-text">现金</span><span class="label-warp">成功邀请新会员后，赠送
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  元现金</span> </el-radio>
                <el-radio disabled :label="2"><span class="checkbox-text">积分</span><span class="label-warp">成功邀请新会员后，赠送
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  积分</span>
                </el-radio>
                <el-radio :label="1"><span class="checkbox-text">优惠券</span><span class="label-warp">成功邀请新会员后，赠送优惠券</span>
                </el-radio>
              </el-radio-group>
              <div class="coupon-warp-select">
                <el-select
                  v-model="referralData.setup.fresh_user_reward.reward_value"
                  :disabled="!referralData.fresh_user_enabled"
                  filterable
                  remote
                  reserve-keyword
                  :placeholder="coupon_list.length === 0 ? '暂无优惠券，请先添加优惠券' : '请选择'
                  "
                  style="width: 350px"
                  :remote-method="remoteMethod"
                  :loading="loading"
                >
                  <el-option v-for="item in coupon_list" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button
                  type="primary"
                  :disabled="!referralData.fresh_user_enabled"
                  size="small"
                  style="margin-left: 10px"
                  plain
                  @click="$router.push({ name: 'coupon' })"
                >添加优惠券</el-button>
              </div>
            </el-form-item>
          </div>
          <div v-if="referralData.setup" class="item-warp-form-content">
            <el-switch
              v-model="referralData.fresh_order_enabled"
              class="switch"
              inline-prompt
              :inactive-value="false"
              :active-value="true"
              @change="(_) => changeSwitch(_, 'fresh_order_reward')"
            />
            <span class="switch-label">新用户完成首单后发放</span>
            <el-form-item label="奖励设置" prop="license_plate_number" class="block-item-form">
              <el-radio-group
                v-model="referralData.setup.fresh_order_reward.reward_type"
                :disabled="!referralData.fresh_order_enabled"
              >
                <el-radio disabled :label="4"><span class="checkbox-text">现金</span><span class="label-warp">成功邀请新会员后，赠送
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  元现金</span> </el-radio>
                <el-radio disabled :label="2"><span class="checkbox-text">积分</span><span class="label-warp">成功邀请新会员后，赠送
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  积分</span>
                </el-radio>
                <el-radio :label="1"><span class="checkbox-text">优惠券</span><span
                  class="label-warp"
                >成功邀请新会员，并且新会员完成首单后赠送</span>
                </el-radio>
              </el-radio-group>
              <div class="coupon-warp-select">
                <el-select
                  v-model="referralData.setup.fresh_order_reward.reward_value"
                  :disabled="!referralData.fresh_order_enabled"
                  filterable
                  remote
                  reserve-keyword
                  :placeholder="coupon_list.length === 0 ? '暂无优惠券，请先添加优惠券' : '请选择'
                  "
                  style="width: 350px"
                  :remote-method="remoteMethod"
                  :loading="couloading"
                  @change="reward_value_change"
                >
                  <el-option v-for="item in coupon_list" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button
                  type="primary"
                  :disabled="!referralData.fresh_order_enabled"
                  size="small"
                  style="margin-left: 10px"
                  plain
                  @click="$router.push({ name: 'coupon' })"
                >添加优惠券</el-button>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="item-warp-form">
        <h6 class="item-title">被邀请人礼包</h6>
        <div class="flex-form">
          <div v-if="referralData.setup" class="item-warp-form-content">
            <el-switch
              v-model="referralData.fresh_invitee_enabled"
              class="switch"
              inline-prompt
              :inactive-value="false"
              :active-value="true"
              @change="(_) => changeSwitch(_, 'invitee_user_reward')"
            />
            <span class="switch-label">被邀请用户注册后获得</span>
            <el-form-item label="奖励设置" prop="license_plate_number" class="block-item-form">
              <el-radio-group
                v-model="referralData.setup.invitee_user_reward.reward_type"
                :disabled="!referralData.fresh_invitee_enabled"
              >
                <el-radio disabled :label="4"><span class="checkbox-text">现金</span><span class="label-warp">被邀请成为新会员后，获得
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  元现金</span> </el-radio>
                <el-radio disabled :label="2"><span class="checkbox-text">积分</span><span class="label-warp">被邀请成为新会员后，获得
                  <el-input v-model="temp.name" size="small" disabled placeholder="请输入" style="width:70px" />
                  积分</span>
                </el-radio>
                <el-radio :label="1"><span class="checkbox-text">优惠券</span><span class="label-warp">被邀请成为新会员后，获得优惠券</span>
                </el-radio>
              </el-radio-group>
              <div class="coupon-warp-select">
                <el-select
                  v-model="referralData.setup.invitee_user_reward.reward_value"
                  :disabled="!referralData.fresh_invitee_enabled"
                  filterable
                  remote
                  reserve-keyword
                  :placeholder="coupon_list.length === 0 ? '暂无优惠券，请先添加优惠券' : '请选择'
                  "
                  style="width: 350px"
                  :remote-method="remoteMethod"
                  :loading="loading"
                >
                  <el-option v-for="item in coupon_list" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button
                  type="primary"
                  :disabled="!referralData.fresh_invitee_enabled"
                  size="small"
                  style="margin-left: 10px"
                  plain
                  @click="$router.push({ name: 'coupon' })"
                >添加优惠券</el-button>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="button-bottom">
        <el-button type="primary" size="small" @click="setConfigMessage">保存配置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { referralSetups, validCoupons, putReferralSetups } from '@/api/marketing'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      couloading: false,
      loading: false,
      referralData: {
        setup: {
          fresh_user_reward: {},
          fresh_order_reward: {},
          invitee_user_reward: {}
        }
      },
      rules: {
        setup: {
          title: [{ required: false, message: '请输入', trigger: 'blur' }],
          rule: [{ required: false, message: '请输入', trigger: 'blur' }],
          share_tile: [{ required: false, message: '请输入', trigger: 'blur' }],
          share_desc: [{ required: false, message: '请输入', trigger: 'blur' }],
          share_image: [{ required: false, message: '请上传', trigger: 'change' }],
          invitee_user_image: [{ required: false, message: '请上传', trigger: 'change' }]
        },
        timeSlot: [{ required: false, message: '请选择活动时间', trigger: 'blur' }]
      },
      coupon_list: [],
      temp: {
      }
    }
  },
  computed: {
    ...mapGetters(['info']),
    baseLink() {
      return process.env.NODE_ENV !== 'production' ? 'https://c.higgses.com' : 'https://cczhaoche.com'
    }
  },
  async mounted() {
    await this.remoteMethod()
    this.getConfigMessage()
  },
  methods: {
    changeSwitch(_, key) {
      if (_) {
        this.referralData.setup[key]['reward_type'] = 1
      } else {
        this.referralData.setup[key] = {}
      }
    },
    // changeStatus(e) {
    //   this.$confirm(`确认${!e ? '关闭' : '开启'}邀请有礼活动？`, '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(async () => {
    //     if (e) {
    //       await this.$refs['referralData'].validate()
    //       await this.validate()
    //     }
    //     this.setConfigMessage()
    //   }).catch(() => {
    //     this.referralData.enabled = !e
    //   })
    // },

    handleCopy(url) {
      var aux = document.createElement('input')
      aux.setAttribute('value', url)
      document.body.appendChild(aux)
      aux.select()
      document.execCommand('copy')
      document.body.removeChild(aux)
      this.$notify({
        message: '复制成功',
        type: 'success',
        duration: 2000
      })
    },
    reward_value_change(e) {
      console.log(e)
    },

    validate() {
      return new Promise((resolve, reject) => {
        if (!this.referralData.fresh_user_enabled && !this.referralData.fresh_order_enabled) {
          // this.$message.warning('开启活动需要至少设置一种邀请人礼包')
          // reject()
        } else if (!this.referralData.fresh_invitee_enabled) {
          // this.$message.warning('开启活动需要设置被邀请人礼包')
          // reject()
        } else if (this.referralData.fresh_user_enabled && !this.referralData.setup.fresh_user_reward.reward_value || this.referralData.fresh_order_enabled && !this.referralData.setup.fresh_order_reward.reward_value ||
          this.referralData.fresh_invitee_enabled && !this.referralData.setup.invitee_user_reward.reward_value
        ) {
          // this.$message.warning('开启礼包功能后必须选择对应优惠券')
          // reject()
        } else {
          // resolve()
        }
        resolve()
      })
    },

    // 获取配置信息
    async getConfigMessage() {
      this.loading = true
      const res = await referralSetups()
      this.loading = false
      const { fresh_user_reward, fresh_order_reward, invitee_user_reward, start_at, end_at } = res.data.value.setup
      this.$set(this.referralData, 'enabled', res.data.value.enabled || false)
      this.$set(this.referralData, 'fresh_user_enabled', !!fresh_user_reward)
      this.$set(this.referralData, 'fresh_order_enabled', !!fresh_order_reward)
      this.$set(this.referralData, 'fresh_invitee_enabled', !!invitee_user_reward)
      this.$set(this.referralData, 'timeSlot', start_at ? [start_at, end_at] : null)
      const fill_ = { fresh_user_reward: fresh_user_reward || {}, fresh_order_reward: fresh_order_reward || {}, invitee_user_reward: invitee_user_reward || {}}
      this.$set(this.referralData, 'setup', { ...res.data.value.setup, ...fill_ })
    },
    async remoteMethod(query = undefined) {
      this.couloading = true
      const { data } = await validCoupons({ keywords: query })
      this.couloading = false
      if (Array.isArray(data)) {
        this.coupon_list = data.map((o) => {
          return { label: `${o.name} （${Number(o.rule) > 0 ? `满${o.rule}元` : '无门槛'},${o.type === 1 ? `立减${o.value}元` : `立享${o.value}折`}）`, value: o.coupon_id }
        })
      } else {
        this.coupon_list = []
      }
    },
    async setConfigMessage() {
      if (this.referralData.enabled) {
        const res = await this.$refs['referralData'].validate().catch(err => err) || false
        console.log(typeof (res))
        if (typeof (res) === 'object') {
          document.documentElement.scrollTop = 0
          return
        }

        await this.validate()
      }
      const { id, title, share_desc, rule, share_tile, share_image, invitee_user_image } = this.referralData.setup
      const params = {
        id: id,
        general_settings: {
          start_at: this.referralData.timeSlot ? this.referralData.timeSlot[0] : null,
          end_at: this.referralData.timeSlot ? this.referralData.timeSlot[1] : null,
          referral_service_enabled: this.referralData.enabled,
          title, share_desc, rule, share_tile, share_image, invitee_user_image, invitee_user_image
        },
        fresh_user_reward: !this.referralData.fresh_user_enabled ? null : this.referralData.setup.fresh_user_reward,
        fresh_order_reward: !this.referralData.fresh_order_enabled ? null : this.referralData.setup.fresh_order_reward,
        invitee_user_reward: !this.referralData.fresh_invitee_enabled ? null : this.referralData.setup.invitee_user_reward
      }
      await putReferralSetups(params)
      this.$notify.success('操作成功')
      this.getConfigMessage()
    }
  }
}
</script>

<style lang="scss">
.coupon-selected {
  margin-top: 15px;

  th.el-table__cell {
    background: #f8f8f8;
  }
}

.block-item-form {
  .el-radio {
    display: block;
    margin: 13px 0;
  }

  .checkbox-text {
    width: 80px;
    display: inline-block;
  }

  .el-radio__input.is-checked+.el-radio__label {
    .label-warp {
      color: #606266;
    }
  }

  .label-warp {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 400;
    max-width: 400px;
    display: inline-block;
    word-break: break-all;
    white-space: break-spaces;
  }
}

.custom-area {
  textarea {
    max-height: calc(100vh - 550px) !important;
  }
}

.dubleLine {
  .el-form-item__label {
    line-height: 20px;
  }
}
</style>
<style lang="scss" scoped>
.coupon-warp-select {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: 14px;
  color: #606266;
  margin-left: 10px;
}

.item-warp-form {
  padding: 0 16px;

  .flex-form {
    display: flex;

    .item-warp-form-content {
      flex: 1;
      margin-right: 30px;
    }
  }
}

.item-title {
  position: relative;
  font-size: 14px;
  margin-bottom: 20px;

  &:before {
    width: 4px;
    border-radius: 5px;
    height: 60%;
    background: #0067e1;
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    margin: auto;
    content: "";
    display: block;
  }
}

.label-key {
  margin-left: 10px;
}

.label-bg {
  background: #f8f8f8;
  padding: 10px 0;
}

.config-container {
  margin-top: 30px;
  // height: calc(100vh - 230px);

  .el-form {
    max-width: 1100px;
  }
}

.link-box {}

.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

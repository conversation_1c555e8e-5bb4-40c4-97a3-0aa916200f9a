<template>
  <div class="component-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <!-- <span class="label-key">邀请人</span> -->
        <el-input v-model="userQuery.inviter" clearable size="small" class="fix-input" placeholder="邀请人昵称或者手机号" />
      </div>
      <div class="filter-container">
        <!-- <span class="label-key">被邀请人</span> -->
        <el-input v-model="userQuery.invitee" clearable size="small" class="fix-input" placeholder="被邀请人昵称或者手机号" />
      </div>
      <div class="filter-container">
        <!-- <span class="label-key">时间</span> -->
        <el-date-picker
          v-model="userQuery.create_time"
          size="small"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="邀请开始时间"
          end-placeholder="邀请结束时间"
          unlink-panels
        />
      </div>
      <div class="filter-container">
        <el-button class="filter-item" type="primary" size="small" @click.stop="handleFilter">
          搜索
        </el-button>
        <el-button class="filter-item" size="small" type="primary" plain @click.stop="handleReset">
          重置
        </el-button>
      </div>
    </div>
    <el-table v-loading="userLoading" :data="recordList" element-loading-text="" fit highlight-current-row size="small" :height="`calc(100vh - 240px)`">
      <el-table-column label="邀请人信息" width="250" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="baseflex">
            <el-avatar shape="square" size="large" :src="scope.row.inviter.third_party ? scope.row.inviter.third_party.third_avatar : ''" @error="errorHandler">
              <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png">
            </el-avatar>
            <div style="margin-left:8px;">
              <p class="table-p">
                昵称：{{ scope.row.inviter ? scope.row.inviter.name || "-" : "-" }}
              </p>
              <p class="table-p">
                手机号：{{ scope.row.inviter ? scope.row.inviter.cellphone || "-" : "-" }}
              </p>
              <p class="table-p">
                ID：{{ scope.row.inviter ? scope.row.inviter.passenger_id || "-" : "-" }}
              </p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="被邀请人信息" width="250" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="baseflex">
            <el-avatar shape="square" size="large" :src="scope.row.invitee.third_party ? scope.row.invitee.third_party.third_avatar : ''" @error="errorHandler">
              <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png">
            </el-avatar>
            <div style="margin-left:8px;">
              <p class="table-p">
                昵称：{{ scope.row.invitee ? scope.row.invitee.name || "-" : "-" }}
              </p>
              <p class="table-p">
                手机号：{{ scope.row.invitee ? scope.row.invitee.cellphone || "-" : "-" }}
              </p>
              <p class="table-p">
                ID：{{ scope.row.invitee ? scope.row.invitee.passenger_id || "-" : "-" }}
              </p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="奖励规则" min-width="100" prop="cn_reward_claim_rule" />
      <el-table-column label="邀请链接" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link type="success">{{ scope.row.share_url }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="邀请渠道" min-width="120" prop="cn_channel" />
      <el-table-column label="奖励发放情况" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          优惠券（ {{ scope.row.face_value }}）
        </template>
      </el-table-column>
      <el-table-column label="奖励发放时间" min-width="120" prop="claim_at" />
      <el-table-column label="邀请时间" min-width="120" prop="created_at" />
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="userQuery.per_page"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="userQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>
<script>
import { trackings } from '@/api/marketing'
export default {
  props: {
    promotion: {
      type: Number
    }
  },
  data() {
    return {
      userQuery: {
        page: 1,
        per_page: 15
      },
      total: 0,
      userLoading: false,
      recordList: []
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    errorHandler() {
      return true
    },
    handleFilter() {
      this.userQuery.page = 1
      this.fetchData()
    },
    handleReset() {
      this.userQuery = {
        page: 1,
        per_page: 15
      }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.userQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.userQuery.page = val
      this.fetchData()
    },
    fetchData() {
      this.userLoading = true
      trackings(this.userQuery).then((response) => {
        console.log(response)
        if (response.code === 200) {
          this.recordList = Array.isArray(response.data.data) ? response.data.data : []
          this.total = response.data.meta.pagination.total
        } else {
          this.recordList = []
          this.total = 0
        }
        this.userLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hanldle-container {
  justify-content: end;
}
.filter-container {
  margin: 0 5px;
}
.user-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;

  .user_num {
    display: flex;
    justify-content: flex-start;

    p {
      margin-right: 12px;
      font-style: 14px;
    }
  }
}
.head_ico {
  width: 24px;
  height: 24px;
  object-fit: cover;
}
</style>

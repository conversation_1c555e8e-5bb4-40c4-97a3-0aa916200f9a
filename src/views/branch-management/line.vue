<template>
  <div class="app-container">
    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.branch_name" placeholder="请输入" clearable size="small"
          class="filter-item mini-condition" @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">分台名称</template>
        </el-input>

        <el-input
          v-model="listQuery.cellphone" placeholder="请输入" clearable size="small"
          class="filter-item mini-condition" @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">手机号码</template>
        </el-input>

        <branch-select
          v-model="listQuery.branch_level" placeholder="请选择分台层级" class="filter-item mini-condition"
          @change="handleFilter"
        />

        <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button class="filter-item" type="primary" size="small" plain icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button class="filter-item" type="primary" size="small" icon="el-icon-plus" @click="handleCreate">
          新增线路分台
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="未冻结分台" name="all" />
      <el-tab-pane label="已冻结" name="frozen" />
    </el-tabs>

    <div v-if="multipleSelection.length > 0" class="batch-operation-container">
      <div class="selected-count">已选择 <span class="count">{{ multipleSelection.length }}</span> 项</div>
      <div class="operation-buttons">
        <el-button size="small" type="danger" plain icon="el-icon-delete" @click="handleBatchDelete">
          批量删除
        </el-button>
        <el-button
          size="small" :type="hasDisabledItems ? 'success' : 'warning'" plain
          :icon="hasDisabledItems ? 'el-icon-unlock' : 'el-icon-lock'" @click="handleBatchStatusChange"
        >
          {{ hasDisabledItems ? '批量启用' : '批量禁用' }}
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="listLoading" :data="list" element-loading-text="" fit
      highlight-current-row size="mini"
      style="width: 100%" @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column label="分台编号" prop="admin_id" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="分台名称" prop="mchname" min-width="260" align="left" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="branch-tree-item">
            <template v-if="scope.row.branch_tree && scope.row.branch_tree.split(',').length > 1">
              <span v-for="i in (scope.row.branch_tree.split(',').length - 1)" :key="i" class="branch-level-indent">
                <i
                  v-if="i === scope.row.branch_tree.split(',').length - 1"
                  class="el-icon-caret-right branch-tree-arrow"
                />
                <i v-else class="el-icon-d-caret branch-tree-arrow" style="visibility: hidden;" />
              </span>
            </template>
            <span class="branch-name" @click="showBranchHierarchy(scope.row)">{{ scope.row.mchname }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="公司全称" prop="company_name" min-width="260" align="left" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.company_name || '暂无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分台层级" min-width="180" align="center">
        <template slot-scope="scope">
          <el-popover placement="right" width="400" trigger="hover">
            <div class="branch-hierarchy-tree">
              <div
                v-for="(branch, index) in getBranchHierarchy(scope.row.branch_tree)" :key="index"
                class="branch-hierarchy-item"
              >
                <span :style="{ paddingLeft: index * 20 + 'px', display: 'flex', alignItems: 'center' }">
                  <span
                    v-if="index > 0" :style="{
                      display: 'inline-block',
                      width: ((index - 1) * 20) + 'px',
                      borderLeft: '1px dashed #dcdfe6',
                      position: 'absolute',
                      height: '20px',
                      marginTop: '-20px',
                      marginLeft: '-' + ((index - 1) * 20 + 20) + 'px'
                    }"
                  />
                  <i
                    :class="index === getBranchHierarchy(scope.row.branch_tree).length - 1 ? 'el-icon-caret-right' : 'el-icon-d-arrow-right'"
                    class="branch-icon"
                  />
                  <span
                    :style="{
                      fontWeight: index === getBranchHierarchy(scope.row.branch_tree).length - 1 ? 'bold' : 'normal',
                      color: index === getBranchHierarchy(scope.row.branch_tree).length - 1 ? '#409EFF' : '#606266'
                    }"
                  >{{ branch.name }}</span>
                </span>
              </div>
            </div>
            <div slot="reference" class="branch-hierarchy-reference">
              <el-tag size="mini" type="info">{{ scope.row.branch_tree ? scope.row.branch_tree.split(',').length : 1
              }}级</el-tag>
              <el-button type="text" size="mini">查看</el-button>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="手机号码" prop="cellphone" width="140" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.cellphone || '暂无' }}</span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="交易总额" width="120" align="center">
        <template slot-scope="scope">
          <span class="amount-text">{{ scope.row.total_amount ? '¥' + Number(scope.row.total_amount).toFixed(2) :
            '¥0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账户余额" width="120" align="center">
        <template slot-scope="scope">
          <span class="balance-text">{{ scope.row.balance ? '¥' + Number(scope.row.balance).toFixed(2) : '¥0.00'
          }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.create_time | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="120" align="center">
        <template slot-scope="scope">
          <div
            class="status-tag" :class="{
              'success': scope.row.is_freeze === 0,
              'danger': scope.row.is_freeze === 1
            }"
          >
            <i v-if="scope.row.is_freeze === 0" class="el-icon-circle-check" />
            <i v-if="scope.row.is_freeze === 1" class="el-icon-circle-close" />
            <span>{{
              {
                0: '正常',
                1: '已冻结'
              }[scope.row.is_freeze]
            }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <el-dropdown trigger="click" placement="bottom-end" @command="handleCommand($event, scope.row)">
            <el-button type="primary" size="mini" plain class="operation-button">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown" class="operation-dropdown-menu">
              <el-dropdown-item command="edit">
                <span class="dropdown-item-content edit">
                  <i class="el-icon-edit" /> 编辑
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="status">
                <span :class="['dropdown-item-content', scope.row.is_freeze === 0 ? 'disable' : 'enable']">
                  <i :class="scope.row.is_freeze === 0 ? 'el-icon-lock' : 'el-icon-unlock'" />
                  {{ scope.row.is_freeze === 0 ? '禁用' : '启用' }}
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="password">
                <span class="dropdown-item-content password">
                  <i class="el-icon-key" /> 修改密码
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="permissions">
                <span class="dropdown-item-content permission">
                  <i class="el-icon-s-tools" /> 权限分配
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="delete">
                <span class="dropdown-item-content delete">
                  <i class="el-icon-delete" /> 删除
                </span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        layout="sizes, total, prev, pager, next" background :page-size="listQuery.size"
        :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        @current-change="handleCurrentChange" @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增线路分台' : '编辑线路分台'" :visible.sync="dialogFormVisible"
      class="branch-dialog" :close-on-click-modal="false" width="700px"
    >
      <el-form
        ref="dataForm" :rules="rules" :model="temp" label-position="right"
        label-width="120px" size="medium"
        inline
      >
        <el-form-item label="分台名称" prop="mchname">
          <el-input v-model="temp.mchname" placeholder="请输入分台名称" style="width: 260px" />
        </el-form-item>
        <el-form-item label="公司全称" prop="company_name">
          <el-input v-model="temp.company_name" placeholder="请输入公司全称" style="width: 260px" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="账号" prop="account">
          <el-input v-model="temp.account" placeholder="请输入账号" style="width: 260px" />
        </el-form-item>
        <el-form-item label="手机号码" prop="cellphone">
          <el-input v-model="temp.cellphone" placeholder="请输入手机号码" style="width: 260px" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="temp.email" placeholder="请输入邮箱" style="width: 260px" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input v-model="temp.password" type="password" placeholder="请输入密码" show-password style="width: 260px" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="上级分台" prop="parent_admin_id">
          <el-cascader
            v-model="temp.parent_admin_id" :options="branchTreeOptions" :props="{
              checkStrictly: true,
              value: 'admin_id',
              label: 'mchname',
              emitPath: false,
              expandTrigger: 'hover'
            }" placeholder="请选择上级分台"
            style="width: 260px" clearable filterable
          />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'update'" label="上级分台" prop="parent_admin_id">
          <el-cascader
            v-model="temp.parent_admin_id" :options="branchTreeOptions" :props="{
              checkStrictly: true,
              value: 'admin_id',
              label: 'mchname',
              emitPath: false,
              expandTrigger: 'hover'
            }" placeholder="请选择上级分台"
            style="width: 260px" clearable filterable
          />
          <div class="form-tip">修改上级分台将影响当前分台及其所有下级分台的层级关系</div>
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'update'" label="当前层级" prop="branch_tree">
          <div class="branch-tree-display" style="width: 260px">
            <el-tag
              v-for="(branch, index) in branchTreeNodes" :key="index" type="info" effect="plain"
              class="branch-tree-node"
            >
              {{ branch.name }}
              <i v-if="index < branchTreeNodes.length - 1" class="el-icon-arrow-right branch-tree-arrow" />
            </el-tag>
          </div>
        </el-form-item>

        <!-- 平台服务费率 -->
        <el-form-item label="平台服务费率" prop="rate_platform_service">
          <el-input v-model="temp.rate_platform_service" placeholder="请输入平台服务费率" style="width: 260px">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码" :visible.sync="passwordDialogVisible" class="password-dialog" :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        ref="passwordForm" :rules="passwordRules" :model="passwordTemp" label-position="right"
        label-width="120px" size="medium"
      >
        <!-- <el-form-item label="旧密码" prop="old_password">
          <el-input v-model="passwordTemp.old_password" type="password" placeholder="请输入旧密码" show-password style="width: 260px" />
        </el-form-item> -->
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordTemp.new_password" type="password" placeholder="请输入新密码" show-password
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirm_password">
          <el-input
            v-model="passwordTemp.confirm_password" type="password" placeholder="请再次输入新密码" show-password
            style="width: 260px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changePassword">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      title="权限分配" :visible.sync="permissionsDialogVisible" :close-on-click-modal="false" width="800px"
      custom-class="permissions-dialog"
    >
      <div class="permission-dialog-title">
        <i class="el-icon-s-custom" />
        <span>{{ permissionsTemp.mchname }}</span><span class="title-separator">-</span><span>权限分配</span>
      </div>
      <el-tabs v-model="permissionsActiveTab" class="permission-tabs">
        <el-tab-pane label="菜单权限" name="menu">
          <div class="permission-tree-container">
            <el-tree
              ref="menuPermissionTree" :data="menuPermissionsTree" :props="{
                label: 'label',
                children: 'children'
              }" show-checkbox
              node-key="permission" default-expand-all class="permission-tree"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <el-tag v-if="data.unread" size="mini" type="danger" class="permission-badge">{{ data.unread }}</el-tag>
              </span>
            </el-tree>
          </div>
        </el-tab-pane>
        <el-tab-pane label="功能权限" name="function">
          <div class="permission-list">
            <el-checkbox
              v-for="func in functionPermissions" :key="func.key" v-model="func.value"
              class="permission-checkbox"
            >
              {{ func.label }}
            </el-checkbox>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" plain @click="permissionsDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="medium" @click="savePermissions">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchLineBranches, createLineBranch, updateLineBranch, deleteLineBranch } from '@/api/branch'
import { fetchParentBranches } from '@/api/branch'
import { changePassword } from '@/api/branch'
import { getBranchPermissions, updateBranchPermissions, getBranchPermissionDefinitions } from '@/api/branch'
import { getAccountPermissionRules } from '@/api/user'
import BranchSelect from '@/components/BranchSelect'

// 支持2位的小数
const validateRatePlatformService = (rule, value, callback) => {
  // 是否数字
  if (isNaN(value)) {
    callback(new Error('平台服务费率必须为数字'))
  } else if (value < 0) {
    callback(new Error('平台服务费率不能为负数'))
  } else {
    // 检查小数位数是否超过2位
    const decimalPart = String(value).split('.')[1]
    if (decimalPart && decimalPart.length > 2) {
      callback(new Error('平台服务费率最多支持2位小数'))
    } else {
      callback()
    }
  }
}
export default {
  name: 'LineBranch',
  components: {
    BranchSelect
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: 'success',
        1: 'info',
        2: 'danger'
      }
      return statusMap[status]
    },
    parseTime(time, format) {
      if (time == null) {
        return null
      }
      const date = new Date(time)
      return format
        .replace('{y}', date.getFullYear())
        .replace('{m}', (date.getMonth() + 1).toString().padStart(2, '0'))
        .replace('{d}', date.getDate().toString().padStart(2, '0'))
        .replace('{h}', date.getHours().toString().padStart(2, '0'))
        .replace('{i}', date.getMinutes().toString().padStart(2, '0'))
        .replace('{s}', date.getSeconds().toString().padStart(2, '0'))
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      multipleSelection: [],
      listQuery: {
        page: 1,
        page_size: 15,
        branch_name: undefined,
        cellphone: undefined,
        branch_level: undefined,
        status: null,
        is_freeze: null
      },
      parentOptions: [], // 上级分台选项
      temp: {
        admin_id: undefined,
        account: '',
        cellphone: '',
        email: '',
        password: '',
        mchname: '',
        company_name: '',
        parent_admin_id: undefined,
        status: 0,
        total_amount: 0,
        balance: 0,
        total_turnover_on_amount: 0,
        total_turnover_down_amount: 0,
        branch_tree: '',
        rate_platform_service: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        mchname: [{ required: true, message: '分台名称不能为空', trigger: 'blur' }],
        company_name: [{ required: false, message: '公司全称不能为空', trigger: 'blur' }],
        account: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
        cellphone: [{ required: true, message: '手机号码不能为空', trigger: 'blur' }],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
        parent_admin_id: [{ required: false, message: '请选择上级分台', trigger: 'change' }],
        rate_platform_service: [{ required: true, message: '平台服务费率不能为空', trigger: 'blur' }, { validator: validateRatePlatformService, trigger: 'blur' }]
      },
      passwordDialogVisible: false,
      passwordTemp: {
        // old_password: '',
        new_password: '',
        confirm_password: ''
      },
      passwordRules: {
        // old_password: [{ required: true, message: '旧密码不能为空', trigger: 'blur' }],
        new_password: [{ required: true, message: '新密码不能为空', trigger: 'blur' }],
        confirm_password: [{ required: true, message: '确认新密码不能为空', trigger: 'blur' }]
      },
      permissionsDialogVisible: false,
      permissionsActiveTab: 'menu',
      permissionsTemp: {
        admin_id: undefined,
        mchname: ''
      },
      menuPermissionsTree: [],
      menuPermissionsMap: {},
      functionPermissions: [],
      permissionDefinitions: null,
      activeTab: 'all'
    }
  },
  computed: {
    hasDisabledItems() {
      // 检查是否有被禁用的项目（状态不为0的项目）
      return this.multipleSelection.some(item => item.status !== 0)
    },
    branchTreeNodes() {
      if (!this.temp.branch_tree) return []

      // 解析branch_tree字符串，获取分台ID数组
      const branchIds = this.temp.branch_tree.split(',')

      // 从parentOptions中查找对应的分台名称
      return branchIds.map(id => {
        const branch = this.parentOptions.find(item => item.admin_id === parseInt(id))
        return {
          id: parseInt(id),
          name: branch ? branch.mchname : `企业ID (${id})`
        }
      })
    },
    branchTreeOptions() {
      // 将平面的parentOptions转换为树形结构
      return this.buildBranchTree(this.parentOptions)
    }
  },
  created() {
    this.getList()
    // 获取上级分台选项
    this.getParentOptions()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 1) {
        return 'row-even'
      }
      return ''
    },
    getList() {
      this.listLoading = true

      // 根据当前标签页设置冻结状态
      if (this.activeTab === 'frozen') {
        this.listQuery.is_freeze = 1
      } else {
        this.listQuery.is_freeze = 0
      }

      fetchLineBranches({
        page: this.listQuery.page,
        size: this.listQuery.size,
        branch_name: this.listQuery.branch_name,
        cellphone: this.listQuery.cellphone,
        branch_level: this.listQuery.branch_level,
        status: this.listQuery.status,
        is_freeze: this.listQuery.is_freeze
      }).then(response => {
        this.list = response.data.data
        // 修复分页数据处理
        if (response.data.meta && response.data.meta.pagination) {
          this.total = response.data.meta.pagination.total || 0
        } else {
          this.total = response.data.total || this.list.length || 0
        }
        this.listLoading = false
      })
    },
    getParentOptions() {
      // 调用获取所有可作为上级的线路分台列表的API，传递branch_type=0参数
      return fetchParentBranches(0).then(response => {
        this.parentOptions = response.data || []
        return this.parentOptions
      }).catch(() => {
        // 如果API调用失败，使用模拟数据
        this.parentOptions = [
          { admin_id: 1, mchname: '总台', branch_tree: '1', branch_type: 1 },
          { admin_id: 2, mchname: '北京分台', branch_tree: '1,2', branch_type: 1 },
          { admin_id: 3, mchname: '上海分台', branch_tree: '1,3', branch_type: 1 },
          { admin_id: 4, mchname: '北京朝阳分台', branch_tree: '1,2,4', branch_type: 1 },
          { admin_id: 5, mchname: '北京海淀分台', branch_tree: '1,2,5', branch_type: 1 },
          { admin_id: 6, mchname: '上海浦东分台', branch_tree: '1,3,6', branch_type: 1 }
        ]
        return this.parentOptions
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        page_size: 15,
        branch_name: undefined,
        cellphone: undefined,
        branch_level: undefined,
        status: null,
        is_freeze: null
      }
      this.activeTab = 'all'
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    resetTemp() {
      this.temp = {
        admin_id: undefined,
        account: '',
        cellphone: '',
        email: '',
        password: '',
        mchname: '',
        company_name: '',
        parent_admin_id: undefined,
        status: 0,
        total_amount: 0,
        balance: 0,
        total_turnover_on_amount: 0,
        total_turnover_down_amount: 0,
        branch_tree: '',
        rate_platform_service: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 创建分台时不需要手动设置branch_tree字段，后端会自动处理
          createLineBranch(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)

          // 如果修改了上级分台，需要特殊处理
          if (tempData.parent_admin_id !== undefined) {
            // 这里可以添加确认提示，因为修改上级分台会影响整个层级结构
            this.$confirm('修改上级分台将影响当前分台及其所有下级分台的层级关系，是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              // 继续更新操作
              updateLineBranch(tempData.admin_id, tempData).then(() => {
                this.dialogFormVisible = false
                this.$notify({
                  title: '成功',
                  message: '更新成功',
                  type: 'success',
                  duration: 2000
                })
                this.getList()
              })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消修改上级分台'
              })
            })
          } else {
            // 没有修改上级分台，直接更新
            updateLineBranch(tempData.admin_id, tempData).then(() => {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            })
          }
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该线路分台吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteLineBranch(row.admin_id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleModifyStatus(row, is_freeze) {
      const statusText = is_freeze === 0 ? '启用' : '禁用'
      this.$confirm(`确认${statusText}该线路分台吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateLineBranch(row.admin_id, { is_freeze }).then(() => {
          this.$notify({
            title: '成功',
            message: `${statusText}成功`,
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${statusText}`
        })
      })
    },
    // 批量删除
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }

      this.$confirm(`确认删除选中的 ${this.multipleSelection.length} 个线路分台吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用批量删除的API，这里模拟批量删除
        const deletePromises = this.multipleSelection.map(item => {
          return deleteLineBranch(item.admin_id)
        })

        Promise.all(deletePromises).then(() => {
          this.$notify({
            title: '成功',
            message: '批量删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '错误',
            message: '批量删除过程中出现错误',
            type: 'error',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消批量删除'
        })
      })
    },
    // 批量禁用/启用
    handleBatchStatusChange() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }

      const targetStatus = this.hasDisabledItems ? 0 : 2 // 如果有禁用项则启用，否则禁用
      const actionText = this.hasDisabledItems ? '启用' : '禁用'

      this.$confirm(`确认${actionText}选中的 ${this.multipleSelection.length} 个线路分台吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用批量修改状态的API，这里模拟批量操作
        const statusChangePromises = this.multipleSelection.map(item => {
          return updateLineBranch(item.admin_id, { status: targetStatus })
        })

        Promise.all(statusChangePromises).then(() => {
          this.$notify({
            title: '成功',
            message: `批量${actionText}成功`,
            type: 'success',
            duration: 2000
          })
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '错误',
            message: `批量${actionText}过程中出现错误`,
            type: 'error',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消批量${actionText}`
        })
      })
    },
    // 新增方法：构建分台树形结构
    buildBranchTree(branches) {
      if (!branches || branches.length === 0) return []

      // 创建一个映射表，用于快速查找节点
      const map = {}
      branches.forEach(branch => {
        map[branch.admin_id] = { ...branch, children: [] }
      })

      // 构建树形结构
      const treeData = []
      branches.forEach(branch => {
        const node = map[branch.admin_id]

        if (branch.branch_tree) {
          const ids = branch.branch_tree.split(',')
          if (ids.length === 1) {
            // 根节点
            treeData.push(node)
          } else {
            // 找到父节点
            const parentId = parseInt(ids[ids.length - 2])
            const parent = map[parentId]
            if (parent) {
              parent.children.push(node)
            }
          }
        } else {
          // 没有branch_tree的情况，作为根节点处理
          treeData.push(node)
        }
      })

      return treeData
    },

    // 新增方法：获取分台层级
    getBranchLevel(branchTree) {
      if (!branchTree) return [0]

      const levels = branchTree.split(',').map(id => parseInt(id))
      return levels
    },

    // 新增方法：获取需要显示的缩进层级（已过滤掉第一级）
    getBranchLevelIndents(branchTree) {
      if (!branchTree) return []

      const branchIds = branchTree.split(',')
      // 返回除第一级以外的所有层级
      if (branchIds.length <= 1) return []

      // 创建一个与层级数量相匹配的数组（不包括第一级）
      return Array.from({ length: branchIds.length - 1 }, (_, i) => i + 1)
    },

    // 新增方法：获取分台层级结构
    getBranchHierarchy(branchTree) {
      if (!branchTree) return [{ id: 0, name: '未知分台' }]

      const branchIds = branchTree.split(',')
      const hierarchy = []

      for (let i = 0; i < branchIds.length; i++) {
        const id = parseInt(branchIds[i])
        // 查找当前ID对应的分台信息
        const branch = this.parentOptions.find(item => item.admin_id === id)

        // 如果找到了分台信息，使用其名称；否则使用默认名称
        hierarchy.push({
          id: id,
          name: branch ? branch.mchname : `分台(${id})`,
          level: i + 1 // 添加层级信息
        })
      }

      return hierarchy
    },

    // 新增方法：显示分台层级对话框
    showBranchHierarchy(row) {
      this.$alert(
        `<div class="branch-hierarchy-dialog">
          ${this.getBranchHierarchy(row.branch_tree).map((branch, index) =>
    `<div class="branch-hierarchy-dialog-item">
              <span style="padding-left: ${index * 20}px; display: flex; align-items: center;">
                ${index > 0 ? '<span style="display: inline-block; width: ' + ((index - 1) * 20) + 'px; border-left: 1px dashed #dcdfe6; position: absolute; height: 20px; margin-top: -20px; margin-left: -' + ((index - 1) * 20 + 20) + 'px;"></span>' : ''}
                <i class="${index === this.getBranchHierarchy(row.branch_tree).length - 1 ? 'el-icon-caret-right' : 'el-icon-d-arrow-right'}" style="color: #409EFF; margin-right: 8px;"></i>
                <span style="font-weight: ${index === this.getBranchHierarchy(row.branch_tree).length - 1 ? 'bold' : 'normal'}; color: ${index === this.getBranchHierarchy(row.branch_tree).length - 1 ? '#409EFF' : '#606266'};">${branch.name}</span>
              </span>
            </div>`
  ).join('')}
        </div>`,
        '分台层级结构',
        {
          dangerouslyUseHTMLString: true,
          customClass: 'branch-hierarchy-dialog-container',
          confirmButtonText: '确定'
        }
      )
    },
    handleChangePassword(row) {
      this.temp = Object.assign({}, row) // 保存当前行数据到temp对象
      this.passwordTemp = {
        new_password: '',
        confirm_password: ''
      }
      this.passwordDialogVisible = true
    },
    changePassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          // 调用修改密码的API
          changePassword(this.temp.admin_id, {
            new_password: this.passwordTemp.new_password,
            confirm_password: this.passwordTemp.confirm_password
          }).then(() => {
            this.$notify({
              title: '成功',
              message: '密码修改成功',
              type: 'success',
              duration: 2000
            })
            this.passwordDialogVisible = false
          }).catch(error => {
            // 处理错误情况
            let errorMessage = '修改密码失败，请重试'
            if (error.response && error.response.data && error.response.data.message) {
              errorMessage = error.response.data.message
            }
            this.$notify({
              title: '错误',
              message: errorMessage,
              type: 'error',
              duration: 2000
            })
          })
        }
      })
    },
    // 处理权限分配
    handlePermissions(row) {
      this.permissionsTemp = Object.assign({}, row)
      this.permissionsDialogVisible = true
      this.permissionsActiveTab = 'menu'
      // 使用分支特定的权限定义
      this.loadPermissionDefinitionsForBranch(row.admin_id)
    },

    // 使用分支特定的权限定义
    loadPermissionDefinitionsForBranch(branchId) {
      getBranchPermissionDefinitions(branchId).then(response => {
        if (response.data) {
          this.permissionDefinitions = response.data

          // 处理菜单权限
          this.menuPermissionsTree = this.transformMenuPermissions(
            this.permissionDefinitions.menu_permissions_definition
          )

          // 处理功能权限
          this.functionPermissions = this.transformFunctionPermissions(
            this.permissionDefinitions.function_permission_definition
          )

          // 加载分台权限
          this.loadBranchPermissions(branchId)
        }
      }).catch(error => {
        console.error('加载分台权限定义失败', error)
        this.$notify({
          title: '错误',
          message: '加载分台权限定义失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 将菜单权限定义转换为树形结构
    transformMenuPermissions(menuPermissionsDefinition) {
      this.menuPermissionsMap = {} // 清空映射

      const transformNode = (node) => {
        const result = {
          label: node.meta.title,
          permission: node.permission,
          children: []
        }

        // 保存到映射中以便快速查找
        this.menuPermissionsMap[node.permission] = result

        // 处理子菜单
        if (node.sub_menu && node.sub_menu.length > 0) {
          result.children = node.sub_menu.map(subNode => transformNode(subNode))
        } else if (node.page && node.page.length > 0) {
          result.children = node.page.map(subNode => transformNode(subNode))
        }

        return result
      }

      return menuPermissionsDefinition.map(node => transformNode(node))
    },

    // 将功能权限定义转换为列表
    transformFunctionPermissions(functionPermissionDefinition) {
      return Object.keys(functionPermissionDefinition).map(key => {
        return {
          key,
          label: this.getFunctionPermissionLabel(key),
          value: functionPermissionDefinition[key]
        }
      })
    },

    // 获取功能权限的显示标签
    getFunctionPermissionLabel(key) {
      const labelMap = {
        'freshOrderNotice': '新订单通知',
        'provincialReportConfig': '省级定制客运监管服务配置'
      }
      return labelMap[key] || key
    },

    // 加载分台权限
    loadBranchPermissions(branchId) {
      getBranchPermissions(branchId).then(response => {
        if (response.data) {
          // 重置所有选中状态
          this.$nextTick(() => {
            if (this.$refs.menuPermissionTree) {
              this.$refs.menuPermissionTree.setCheckedKeys([])

              // 收集应该选中的菜单权限
              const checkedKeys = []
              const leafEnabledKeys = []

              // 递归处理menu_permissions_definition，找出enabled为true的项
              const collectEnabledPermissions = (permissions) => {
                if (!permissions) return

                permissions.forEach(item => {
                  const isLeaf = (!item.sub_menu || item.sub_menu.length === 0) && (!item.page || item.page.length === 0)
                  if (item.enabled === true && isLeaf) {
                    leafEnabledKeys.push(item.permission)
                  } else if (item.enabled === true) {
                    checkedKeys.push(item.permission)
                  }

                  // 递归处理子菜单
                  if (item.sub_menu) {
                    collectEnabledPermissions(item.sub_menu)
                  }

                  // 递归处理页面
                  if (item.page) {
                    collectEnabledPermissions(item.page)
                  }
                })
              }

              // 如果API返回了menu_permissions_definition，则优先使用它
              if (response.data.menu_permissions_definition) {
                collectEnabledPermissions(response.data.menu_permissions_definition)

                // 设置选中状态之前，检查树形组件是否已经创建
                if (this.$refs.menuPermissionTree) {
                  // 先清空所有选中项
                  this.$refs.menuPermissionTree.setCheckedKeys([])

                  // 明确只设置 enabled 为 true 的叶子节点为选中状态
                  this.$refs.menuPermissionTree.setCheckedKeys(leafEnabledKeys)
                  console.log('设置选中的叶子节点权限:', leafEnabledKeys)

                  // 强制更新视图
                  this.$forceUpdate()
                }
              } else if (response.data.menu_permissions && response.data.menu_permissions.length > 0) {
                // 否则使用之前的方式
                const allNodesMap = new Map()
                const buildNodeMap = (nodes) => {
                  nodes.forEach(node => {
                    allNodesMap.set(node.permission, node)
                    if (node.children && node.children.length > 0) {
                      buildNodeMap(node.children)
                    }
                  })
                }
                buildNodeMap(this.menuPermissionsTree) // Build map from the tree structure

                const legacyCheckedKeys = response.data.menu_permissions
                const legacyLeafCheckedKeys = legacyCheckedKeys.filter(key => {
                  const node = allNodesMap.get(key)
                  // Check if node exists and is a leaf node in the current tree structure
                  return node && (!node.children || node.children.length === 0)
                })
                this.$refs.menuPermissionTree.setCheckedKeys(legacyLeafCheckedKeys)
                console.log('设置选中的遗留叶子节点权限:', legacyLeafCheckedKeys)
              }
            }

            // 处理功能权限
            if (response.data.function_permission_definition) {
              // 如果API返回了function_permission_definition，则优先使用它
              this.functionPermissions.forEach(func => {
                func.value = response.data.function_permission_definition[func.key] === true
              })
            } else if (response.data.function_permissions) {
              // 否则使用之前的方式
              this.functionPermissions.forEach(func => {
                func.value = response.data.function_permissions.includes(func.key)
              })
            }
          })
        }
      }).catch(error => {
        console.error('加载分台权限失败', error)
        this.$notify({
          title: '错误',
          message: '加载分台权限失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 保存权限设置
    savePermissions() {
      // 获取选中的菜单权限ID列表（使用getCheckedKeys而不是getHalfCheckedKeys，因为我们设置了check-strictly）
      const checkedKeys = this.$refs.menuPermissionTree.getCheckedKeys()
      const halfCheckedKeys = this.$refs.menuPermissionTree.getHalfCheckedKeys()
      const selectedMenuPermissionIds = [...checkedKeys, ...halfCheckedKeys]

      // 创建要提交的menu_permissions_definition副本
      let menuPermissionsDefinition = []

      // 如果已经有从API获取的权限定义
      if (this.permissionDefinitions && this.permissionDefinitions.menu_permissions_definition) {
        // 递归更新权限的enabled状态
        const updateEnabledStatus = (permissions) => {
          if (!permissions) return permissions

          return permissions.map(item => {
            // 创建当前项目的深拷贝
            const newItem = JSON.parse(JSON.stringify(item))

            // 更新enabled状态 - 只有在选中列表中的项才设为true
            newItem.enabled = selectedMenuPermissionIds.includes(item.permission)

            // 递归处理子菜单
            if (newItem.sub_menu) {
              newItem.sub_menu = updateEnabledStatus(newItem.sub_menu)
            }

            // 递归处理页面
            if (newItem.page) {
              newItem.page = updateEnabledStatus(newItem.page)
            }

            return newItem
          })
        }

        // 更新menu_permissions_definition
        menuPermissionsDefinition = updateEnabledStatus(this.permissionDefinitions.menu_permissions_definition)
      }

      // 创建要提交的function_permission_definition结构
      const functionPermissionDefinition = {}

      // 设置功能权限的值
      this.functionPermissions.forEach(func => {
        functionPermissionDefinition[func.key] = func.value
      })

      // 提交数据 - 只提交必要参数
      updateBranchPermissions(this.permissionsTemp.admin_id, {
        // 只提交这两个字段，避免多余参数
        menu_permissions_definition: menuPermissionsDefinition,
        function_permission_definition: functionPermissionDefinition
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '权限设置成功',
          type: 'success',
          duration: 2000
        })
        this.permissionsDialogVisible = false
      }).catch(error => {
        let errorMessage = '权限设置失败，请重试'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        }
        this.$notify({
          title: '错误',
          message: errorMessage,
          type: 'error',
          duration: 2000
        })
      })
    },
    // 新增：处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleUpdate(row)
          break
        case 'status':
          this.handleModifyStatus(row, row.is_freeze === 0 ? 1 : 0)
          break
        case 'password':
          this.handleChangePassword(row)
          break
        case 'permissions':
          this.handlePermissions(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },
    // 保留原有方法作为备用
    loadPermissionDefinitions() {
      return getAccountPermissionRules().then(response => {
        if (response.data && response.data.PERMISSION) {
          this.permissionDefinitions = response.data.PERMISSION

          // 处理菜单权限
          this.menuPermissionsTree = this.transformMenuPermissions(
            this.permissionDefinitions.menu_permissions_definition
          )

          // 处理功能权限
          this.functionPermissions = this.transformFunctionPermissions(
            this.permissionDefinitions.function_permission_definition
          )
        }
      }).catch(error => {
        console.error('加载权限定义失败', error)
        this.$notify({
          title: '错误',
          message: '加载权限定义失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.getList()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.handle-container {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 15px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.mini-condition {
  width: 220px;
}

.batch-operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f9eb;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px solid #e1f3d8;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.selected-count .count {
  font-weight: bold;
  color: #67c23a;
  margin: 0 3px;
}

.operation-buttons .el-button {
  margin-left: 10px;
}

.pagination-container {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
  text-align: right;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  min-height: 50px;
}

.row-even {
  background-color: #fafafa;
}

.balance-text {
  color: #67c23a;
  font-weight: bold;
}

.branch-name {
  font-weight: bold;
  color: #409EFF;
  cursor: pointer;
}

.branch-name:hover {
  text-decoration: underline;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-tag i {
  margin-right: 4px;
  font-size: 14px;
}

.status-tag.success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.status-tag.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.status-tag.danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.status-tag.info {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.branch-dialog .el-form-item {
  margin-bottom: 20px;
}

.branch-tree-display {
  display: flex;
  flex-wrap: wrap;
}

.branch-tree-node {
  margin-right: 5px;
  margin-bottom: 5px;
}

.branch-tree-arrow {
  margin-left: 5px;
  font-size: 12px;
}

.branch-tree-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.branch-level-indent {
  display: inline-flex;
  align-items: center;
  color: #909399;
  width: 16px;
  margin-right: 2px;
}

.branch-tree-arrow {
  font-size: 14px;
  color: #409EFF;
}

.branch-hierarchy-tree {
  max-height: 300px;
  overflow-y: auto;
}

.branch-hierarchy-item {
  padding: 10px 0;
  border-bottom: 1px dashed #ebeef5;
  position: relative;
}

.branch-hierarchy-item:last-child {
  border-bottom: none;
}

.branch-hierarchy-reference {
  display: flex;
  align-items: center;
  justify-content: center;
}

.branch-hierarchy-reference .el-button {
  margin-left: 5px;
}

.branch-icon {
  margin-right: 5px;
  color: #409EFF;
}

.form-tip {
  font-size: 12px;
  color: #E6A23C;
  margin-top: 5px;
}

.branch-hierarchy-dialog {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.branch-hierarchy-dialog-item {
  padding: 10px 0;
  border-bottom: 1px dashed #ebeef5;
  position: relative;
}

.branch-hierarchy-dialog-item:last-child {
  border-bottom: none;
}

.branch-hierarchy-dialog-container {
  min-width: 450px;
}

.password-dialog .el-form-item {
  margin-bottom: 20px;
}

.password-dialog .dialog-footer {
  text-align: right;
}

.permission-dialog-title {
  font-size: 16px;
  color: #409EFF;
  margin-bottom: 20px;
  font-weight: bold;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
}

.permission-dialog-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.permission-dialog-title span {
  font-weight: normal;
  color: #303133;
}

.permission-dialog-title span:first-of-type {
  color: #409EFF;
  font-weight: bold;
}

.title-separator {
  margin: 0 8px;
  color: #909399;
}

.permission-tree-container {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.permission-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
  height: 400px;
  overflow-y: auto;
}

.permission-checkbox {
  margin-bottom: 12px;
  height: 32px;
  display: flex;
  align-items: center;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  padding: 4px 0;
}

.custom-tree-node .el-tag {
  margin-left: 10px;
}

.permission-badge {
  padding: 2px 6px;
  height: 20px;
  line-height: 16px;
}

.permission-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.permission-tabs .el-tabs__nav {
  font-weight: bold;
}

.permission-tabs .el-tabs__item {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.permission-tabs .el-tabs__item.is-active {
  color: #409EFF;
}

.permission-tree {
  font-size: 14px;
}

.permission-tree .el-tree-node__content {
  height: 32px;
}

.permission-tree .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409EFF;
  border-color: #409EFF;
}

.permissions-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.permissions-dialog .el-dialog__title {
  font-weight: bold;
  font-size: 18px;
}

.permissions-dialog .el-dialog__body {
  padding: 20px;
}

.permissions-dialog .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  transition: all 0.3s;
}

.el-dropdown-link:hover {
  background-color: #F5F7FA;
  border-color: #C0C4CC;
}

.el-dropdown-menu__item i {
  margin-right: 5px;
  font-size: 16px;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-buttons .el-button {
  margin-left: 0;
  width: 100%;
  justify-content: flex-start;
}

.operation-popover.el-popover {
  min-width: 120px;
  padding: 10px;
}

.operation-button {
  padding: 6px 12px;
}

.operation-dropdown-menu {
  min-width: 130px;
}

.dropdown-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
}

.dropdown-item-content i {
  margin-right: 8px;
  font-size: 16px;
}

.dropdown-item-content.edit {
  color: #2B85E4;
}

.dropdown-item-content.enable {
  color: #19BE6B;
}

.dropdown-item-content.disable {
  color: #19BE6B;
}

.dropdown-item-content.password {
  color: #909399;
}

.dropdown-item-content.permission {
  color: #2B85E4;
}

.dropdown-item-content.delete {
  color: #F56C6C;
}
</style>

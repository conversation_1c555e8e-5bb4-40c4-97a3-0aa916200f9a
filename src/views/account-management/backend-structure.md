# 账号管理系统 - 后端结构设计

## 数据库设计

### 1. 账号表 (accounts)

```sql
CREATE TABLE `accounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(64) NOT NULL COMMENT '密码',
  `cellphone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '所属商户/分台ID',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  `is_freeze` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态：0正常，1冻结',
  `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `merchant_id` (`merchant_id`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账号表';
```

### 2. 角色表 (roles)

```sql
CREATE TABLE `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `merchant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '所属商户ID，0表示系统角色',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

### 3. 角色权限表 (role_permissions)

```sql
CREATE TABLE `role_permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  `permission_json` text COMMENT '权限JSON数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限表';
```

### 4. 权限操作日志表 (permission_logs)

```sql
CREATE TABLE `permission_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` bigint(20) unsigned NOT NULL COMMENT '操作者ID',
  `target_id` bigint(20) unsigned NOT NULL COMMENT '目标ID（角色ID或账号ID）',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型（role或account）',
  `before_json` text COMMENT '修改前的权限JSON',
  `after_json` text COMMENT '修改后的权限JSON',
  `ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `target_id` (`target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限操作日志表';
```

## API接口设计

### 账号管理接口

1. 获取账号列表
   - 请求：`GET /api/admin/accounts`
   - 参数：
     - `page`: 页码，默认1
     - `per_page`: 每页数量，默认15
     - `username`: 用户名关键字
     - `cellphone`: 手机号
     - `merchant_id`: 所属商户/分台ID
     - `role_id`: 角色ID
   - 响应：账号列表数据

2. 获取账号详情
   - 请求：`GET /api/admin/accounts/{id}`
   - 响应：账号详情数据

3. 创建账号
   - 请求：`POST /api/admin/accounts`
   - 参数：
     - `username`: 用户名
     - `password`: 密码
     - `cellphone`: 手机号
     - `email`: 邮箱
     - `merchant_id`: 所属商户/分台ID
     - `role_id`: 角色ID
   - 响应：创建结果

4. 更新账号
   - 请求：`PUT /api/admin/accounts/{id}`
   - 参数：
     - `username`: 用户名
     - `cellphone`: 手机号
     - `email`: 邮箱
     - `merchant_id`: 所属商户/分台ID
     - `role_id`: 角色ID
   - 响应：更新结果

5. 删除账号
   - 请求：`DELETE /api/admin/accounts/{id}`
   - 响应：删除结果

6. 批量删除账号
   - 请求：`DELETE /api/admin/accounts/batch`
   - 参数：
     - `ids`: 账号ID数组
   - 响应：批量删除结果

7. 修改账号状态
   - 请求：`PATCH /api/admin/accounts/{id}/status`
   - 参数：
     - `is_freeze`: 状态值，0启用，1禁用
   - 响应：修改结果

8. 修改账号密码
   - 请求：`POST /api/admin/accounts/{id}/password`
   - 参数：
     - `new_password`: 新密码
     - `confirm_password`: 确认密码
   - 响应：修改结果

9. 获取可分配的商户或分台列表
   - 请求：`GET /api/admin/accounts/merchant-options`
   - 响应：商户/分台列表数据

### 角色管理接口

1. 获取角色列表
   - 请求：`GET /api/admin/roles`
   - 参数：
     - `page`: 页码，默认1
     - `per_page`: 每页数量，默认15
     - `name`: 角色名称关键字
     - `merchant_id`: 所属商户ID
   - 响应：角色列表数据

2. 创建角色
   - 请求：`POST /api/admin/roles`
   - 参数：
     - `name`: 角色名称
     - `description`: 角色描述
     - `merchant_id`: 所属商户ID
   - 响应：创建结果

3. 更新角色
   - 请求：`PUT /api/admin/roles/{id}`
   - 参数：
     - `name`: 角色名称
     - `description`: 角色描述
   - 响应：更新结果

4. 删除角色
   - 请求：`DELETE /api/admin/roles/{id}`
   - 响应：删除结果

### 权限管理接口

1. 获取权限规则定义
   - 请求：`GET /api/admin/rules`
   - 响应：权限规则定义数据

2. 获取角色权限
   - 请求：`GET /api/admin/roles/{id}/permissions`
   - 响应：角色权限数据

3. 更新角色权限
   - 请求：`PUT /api/admin/roles/{id}/permissions`
   - 参数：
     - `menu_permissions`: 菜单权限数组
     - `function_permissions`: 功能权限数组
   - 响应：更新结果

## 后端代码结构（Laravel微服务）

```
app/
  |-- Http/
  |   |-- Controllers/
  |   |   |-- AccountController.php      # 账号管理控制器
  |   |   |-- RoleController.php         # 角色管理控制器
  |   |   |-- PermissionController.php   # 权限管理控制器
  |   |
  |   |-- Requests/
  |   |   |-- Account/
  |   |   |   |-- StoreAccountRequest.php   # 创建账号验证
  |   |   |   |-- UpdateAccountRequest.php  # 更新账号验证
  |   |   |
  |   |   |-- Role/
  |   |   |   |-- StoreRoleRequest.php      # 创建角色验证
  |   |   |   |-- UpdateRoleRequest.php     # 更新角色验证
  |   |
  |   |-- Resources/
  |   |   |-- AccountResource.php        # 账号资源
  |   |   |-- RoleResource.php           # 角色资源
  |
  |-- Models/
  |   |-- Account.php                    # 账号模型
  |   |-- Role.php                       # 角色模型
  |   |-- RolePermission.php             # 角色权限模型
  |   |-- PermissionLog.php              # 权限日志模型
  |
  |-- Services/
  |   |-- AccountService.php             # 账号服务
  |   |-- RoleService.php                # 角色服务
  |   |-- PermissionService.php          # 权限服务
  |
  |-- Repositories/
  |   |-- AccountRepository.php          # 账号仓库
  |   |-- RoleRepository.php             # 角色仓库
  |   |-- PermissionRepository.php       # 权限仓库

routes/
  |-- api.php                            # API路由定义
```

## 路由配置（Laravel）

```php
Route::prefix('admin')->group(function () {
    // 账号管理
    Route::get('accounts', 'AccountController@index');
    Route::get('accounts/{id}', 'AccountController@show');
    Route::post('accounts', 'AccountController@store');
    Route::put('accounts/{id}', 'AccountController@update');
    Route::delete('accounts/{id}', 'AccountController@destroy');
    Route::delete('accounts/batch', 'AccountController@batchDestroy');
    Route::patch('accounts/{id}/status', 'AccountController@changeStatus');
    Route::post('accounts/{id}/password', 'AccountController@changePassword');
    Route::get('accounts/merchant-options', 'AccountController@merchantOptions');

    // 角色管理
    Route::get('roles', 'RoleController@index');
    Route::post('roles', 'RoleController@store');
    Route::put('roles/{id}', 'RoleController@update');
    Route::delete('roles/{id}', 'RoleController@destroy');
    
    // 权限管理
    Route::get('rules', 'PermissionController@rules');
    Route::get('roles/{id}/permissions', 'PermissionController@rolePermissions');
    Route::put('roles/{id}/permissions', 'PermissionController@updateRolePermissions');
});
``` 

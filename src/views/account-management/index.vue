<template>
  <div class="app-container">
    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.username" placeholder="请输入" clearable size="small"
          class="filter-item mini-condition" @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">用户名</template>
        </el-input>

        <el-input
          v-model="listQuery.cellphone" placeholder="请输入" clearable size="small"
          class="filter-item mini-condition" @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">手机号码</template>
        </el-input>

        <branch-select
          v-model="listQuery.merchant_id" placeholder="请选择所属商户/分台" class="filter-item mini-condition"
          @change="handleFilter"
        />

        <el-select
          v-model="listQuery.role_id" placeholder="请选择角色" clearable size="small"
          class="filter-item mini-condition" @change="handleFilter"
        >
          <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>

        <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button class="filter-item" type="primary" size="small" plain icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button class="filter-item" type="primary" size="small" icon="el-icon-plus" @click="handleCreate">
          新增账号
        </el-button>
        <el-button class="filter-item" type="primary" size="small" icon="el-icon-s-tools" @click="handleRoleManage">
          角色管理
        </el-button>
      </div>
    </div>

    <div v-if="multipleSelection.length > 0" class="batch-operation-container">
      <div class="selected-count">已选择 <span class="count">{{ multipleSelection.length }}</span> 项</div>
      <div class="operation-buttons">
        <el-button size="small" type="danger" plain icon="el-icon-delete" @click="handleBatchDelete">
          批量删除
        </el-button>
        <el-button
          size="small" :type="hasDisabledItems ? 'success' : 'warning'" plain
          :icon="hasDisabledItems ? 'el-icon-unlock' : 'el-icon-lock'" @click="handleBatchStatusChange"
        >
          {{ hasDisabledItems ? '批量启用' : '批量禁用' }}
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="listLoading" :data="list" element-loading-text="" fit
      highlight-current-row size="mini"
      style="width: 100%" @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column label="账号ID" prop="id" width="80" align="center" />
      <el-table-column label="用户名" prop="username" min-width="120" align="center" />
      <el-table-column label="手机号码" prop="cellphone" width="120" align="center" />
      <el-table-column label="邮箱" prop="email" min-width="160" align="center" />
      <el-table-column label="所属商户/分台" min-width="160" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" type="success">{{ scope.row.merchant ? scope.row.merchant.mchname :
            scope.row.merchant_name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="角色" width="120" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" type="primary">{{ scope.row.role ? scope.row.role.name : scope.row.role_name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.created_at | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <div
            class="status-tag" :class="{
              'success': scope.row.is_freeze === 0,
              'danger': scope.row.is_freeze === 1
            }"
          >
            <i v-if="scope.row.is_freeze === 0" class="el-icon-circle-check" />
            <i v-if="scope.row.is_freeze === 1" class="el-icon-circle-close" />
            <span>{{
              {
                0: '正常',
                1: '已冻结'
              }[scope.row.is_freeze]
            }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <el-dropdown trigger="click" placement="bottom-end" @command="handleCommand($event, scope.row)">
            <el-button type="primary" size="mini" plain class="operation-button">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown" class="operation-dropdown-menu">
              <el-dropdown-item command="edit">
                <span class="dropdown-item-content edit">
                  <i class="el-icon-edit" /> 编辑
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="status">
                <span :class="['dropdown-item-content', scope.row.is_freeze === 0 ? 'disable' : 'enable']">
                  <i :class="scope.row.is_freeze === 0 ? 'el-icon-lock' : 'el-icon-unlock'" />
                  {{ scope.row.is_freeze === 0 ? '禁用' : '启用' }}
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="password">
                <span class="dropdown-item-content password">
                  <i class="el-icon-key" /> 修改密码
                </span>
              </el-dropdown-item>
              <el-dropdown-item command="delete">
                <span class="dropdown-item-content delete">
                  <i class="el-icon-delete" /> 删除
                </span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-if="total > 0" layout="sizes, total, prev, pager, next" background :page-size="listQuery.limit"
        :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        @current-change="handleCurrentChanges" @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增账号' : '编辑账号'" :visible.sync="dialogFormVisible"
      class="account-dialog" :close-on-click-modal="false" width="700px"
    >
      <el-form
        ref="dataForm" :rules="rules" :model="temp" label-position="right"
        label-width="120px" size="medium"
        inline
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" placeholder="请输入用户名" style="width: 260px" />
        </el-form-item>
        <el-form-item label="手机号码" prop="cellphone">
          <el-input v-model="temp.cellphone" placeholder="请输入手机号码" style="width: 260px" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="temp.email" placeholder="请输入邮箱" style="width: 260px" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input v-model="temp.password" type="password" placeholder="请输入密码" show-password style="width: 260px" />
        </el-form-item>
        <el-form-item label="所属商户/分台" prop="merchant_id">
          <branch-select v-model="temp.merchant_id" placeholder="请选择所属商户/分台" width="260px" />
        </el-form-item>
        <el-form-item label="角色" prop="role_id">
          <el-select v-model="temp.role_id" placeholder="请选择角色" style="width: 260px" filterable>
            <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="管理线路" prop="line_ids">
          <el-select v-model="temp.line_ids" placeholder="请选择线路" style="width: 440px" filterable multiple>
            <el-option v-for="item in lineOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_freeze">
          <el-switch
            v-model="temp.is_freeze" active-text="启用" inactive-text="禁用" :active-value="0"
            :inactive-value="1"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码" :visible.sync="passwordDialogVisible" class="password-dialog" :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        ref="passwordForm" :rules="passwordRules" :model="passwordTemp" label-position="right"
        label-width="120px" size="medium"
      >
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordTemp.new_password" type="password" placeholder="请输入新密码" show-password
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirm_password">
          <el-input
            v-model="passwordTemp.confirm_password" type="password" placeholder="请再次输入新密码" show-password
            style="width: 260px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changePassword">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      title="权限分配" :visible.sync="permissionsDialogVisible" :close-on-click-modal="false" width="800px"
      custom-class="permissions-dialog"
    >
      <div class="permission-dialog-title">
        <i class="el-icon-s-custom" />
        <span>{{ permissionsTemp.username }}</span><span class="title-separator">-</span><span>权限分配</span>
      </div>
      <el-tabs v-model="permissionsActiveTab" class="permission-tabs">
        <el-tab-pane label="菜单权限" name="menu">
          <div class="permission-tree-container">
            <el-tree
              ref="menuPermissionTree" :data="menuPermissionsTree" :props="{
                label: 'label',
                children: 'children'
              }" show-checkbox
              node-key="permission" default-expand-all class="permission-tree"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <el-tag v-if="data.unread" size="mini" type="danger" class="permission-badge">{{ data.unread }}</el-tag>
              </span>
            </el-tree>
          </div>
        </el-tab-pane>
        <el-tab-pane label="功能权限" name="function">
          <div class="permission-list">
            <el-checkbox
              v-for="func in functionPermissions" :key="func.key" v-model="func.value"
              class="permission-checkbox"
            >
              {{ func.label }}
            </el-checkbox>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" plain @click="permissionsDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="medium" @click="savePermissions">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 角色管理对话框 -->
    <el-dialog
      title="角色管理" :visible.sync="roleDialogVisible" :close-on-click-modal="false" width="900px"
      custom-class="role-dialog"
    >
      <div class="role-dialog-header">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleCreateRole">
          新增角色
        </el-button>
      </div>
      <el-table :data="roleList" style="width: 100%" size="mini" border>
        <el-table-column label="角色ID" prop="id" width="80" align="center" />
        <el-table-column label="角色名称" prop="name" min-width="150" align="center" />
        <el-table-column label="描述" prop="description" min-width="200" align="center" />
        <el-table-column label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.created_at | parseTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template slot-scope="scope">
            <div class="role-operation-buttons">
              <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="handleUpdateRole(scope.row)">
                编辑
              </el-button>
              <el-button
                type="success" size="mini" icon="el-icon-s-tools" plain
                @click="handleRolePermissions(scope.row)"
              >
                权限
              </el-button>
              <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="handleDeleteRole(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 新增/编辑角色对话框 -->
    <el-dialog
      :title="roleDialogStatus === 'create' ? '新增角色' : '编辑角色'" :visible.sync="roleFormDialogVisible"
      width="500px" :close-on-click-modal="false"
    >
      <el-form
        ref="roleForm" :rules="roleRules" :model="roleTemp" label-position="right"
        label-width="100px"
        size="medium"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleTemp.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="roleTemp.description" type="textarea" :rows="3" placeholder="请输入角色描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleFormDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="roleDialogStatus === 'create' ? createRole() : updateRole()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 角色权限分配对话框 -->
    <el-dialog
      title="角色权限分配" :visible.sync="rolePermissionsDialogVisible" :close-on-click-modal="false" width="800px"
      custom-class="permissions-dialog"
    >
      <div v-if="selectedRole" class="permission-dialog-title">
        <i class="el-icon-s-tools" />
        <span>{{ selectedRole.name }}</span><span class="title-separator">-</span><span>权限分配</span>
      </div>
      <el-tabs v-model="permissionsActiveTab" class="permission-tabs">
        <el-tab-pane label="菜单权限" name="menu">
          <div class="permission-tree-container">
            <el-tree
              ref="roleMenuPermissionTree" :data="menuPermissionsTree" :props="{
                label: 'label',
                children: 'children'
              }" show-checkbox
              node-key="permission" default-expand-all class="permission-tree"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <el-tag v-if="data.unread" size="mini" type="danger" class="permission-badge">{{ data.unread }}</el-tag>
              </span>
            </el-tree>
          </div>
        </el-tab-pane>
        <el-tab-pane label="功能权限" name="function">
          <div class="permission-list">
            <el-checkbox
              v-for="func in functionPermissions" :key="func.key" v-model="func.value"
              class="permission-checkbox"
            >
              {{ func.label }}
            </el-checkbox>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" plain @click="rolePermissionsDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="medium" @click="saveRolePermissions">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import {
  fetchAccounts,
  createAccount,
  updateAccount,
  deleteAccount,
  batchDeleteAccounts,
  changeAccountStatus,
  changeAccountPassword,
  fetchRoles,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  updateRolePermissions,
  getAccount
} from '@/api/account'
import { getAccountPermissionRules } from '@/api/user'
import { fetchLines } from '@/api/ad'
import BranchSelect from '@/components/BranchSelect'

export default {
  name: 'AccountManagement',
  components: {
    BranchSelect
  },
  filters: {
    parseTime(time) {
      return parseTime(time)
    }
  },
  data() {
    return {
      lineOptions: [], // 线路选项
      list: [], // 数据列表
      total: 0, // 总数
      listLoading: true, // 加载状态
      multipleSelection: [], // 多选列表
      listQuery: {
        page: 1, // 当前页码
        limit: 15, // 每页条数
        username: undefined, // 用户名
        cellphone: undefined, // 手机号
        merchant_id: undefined, // 所属商户/分台ID
        role_id: undefined // 角色ID
      },
      merchantOptions: [], // 商户/分台选项
      roleOptions: [], // 角色选项

      // 账号表单对话框相关
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        username: '',
        cellphone: '',
        email: '',
        password: '',
        merchant_id: undefined,
        role_id: undefined,
        is_freeze: 0,
        line_ids: []
      },
      rules: {
        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        cellphone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
        merchant_id: [{ required: true, message: '请选择所属商户/分台', trigger: 'change' }],
        role_id: [{ required: true, message: '请选择角色', trigger: 'change' }]
      },

      // 密码修改对话框相关
      passwordDialogVisible: false,
      passwordTemp: {
        new_password: '',
        confirm_password: ''
      },
      passwordRules: {
        new_password: [{ required: true, message: '新密码不能为空', trigger: 'blur' }],
        confirm_password: [{ required: true, message: '确认密码不能为空', trigger: 'blur' }]
      },

      // 权限分配对话框相关
      permissionsDialogVisible: false,
      permissionsActiveTab: 'menu',
      permissionsTemp: {
        id: undefined,
        username: ''
      },
      menuPermissionsTree: [],
      menuPermissionsMap: {},
      functionPermissions: [],

      // 角色管理对话框相关
      roleDialogVisible: false,
      roleTemp: {
        id: undefined,
        name: '',
        description: ''
      },
      roleRules: {
        name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }]
      },
      roleList: [],
      roleDialogStatus: '',
      roleFormDialogVisible: false,

      // 角色权限分配对话框相关
      rolePermissionsDialogVisible: false,
      selectedRole: null,
      fullPermissions: null
    }
  },
  computed: {
    // 是否有被禁用的项目
    hasDisabledItems() {
      return this.multipleSelection.some(item => Number(item.is_freeze) === 1)
    },

    // 用于Switch组件的值转换
    tempSwitchValue: {
      get() {
        return this.temp.is_freeze === 0
      },
      set(val) {
        this.temp.is_freeze = val ? 0 : 1
      }
    }
  },
  created() {
    // 获取数据列表
    this.getList()

    // 获取角色选项
    this.getRoleOptions()
    // 加载权限定义
    this.loadPermissionDefinitions()
    // 获取线路选项
    this.getLineOptions()
  },
  methods: {
    // 获取账号
    async getAccountInfo(id) {
      const response = await getAccount(id)
      this.temp = {
        ...this.temp,
        line_ids: response.data.relations.map(item => item.relation_id)
      }
    },
    // 获取线路选项
    getLineOptions() {
      fetchLines().then(response => {
        this.lineOptions = response?.data?.map(item => ({
          id: item.id,
          name: item.start_name + ' → ' + item.end_name
        }))
      })
    },
    // 获取数据列表
    getList() {
      this.listLoading = true
      fetchAccounts(this.listQuery).then(response => {
        if (response.data && response.data.data) {
          this.list = response.data.data
          // 确保is_freeze字段是数字类型
          this.list.forEach(item => {
            item.is_freeze = Number(item.is_freeze)
          })
          // 处理分页信息
          if (response.data.meta && response.data.meta.pagination) {
            this.total = response.data.meta.pagination.total
          } else {
            this.total = response.data.data.length
          }
        } else {
          this.list = []
          this.total = 0
          console.error('账号数据格式不正确', response)
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取账号数据失败', error)
        this.listLoading = false
      })
    },

    // 获取角色选项
    getRoleOptions() {
      fetchRoles().then(response => {
        if (response.data && response.data.data) {
          this.roleOptions = response.data.data
          this.roleList = [...response.data.data]
        } else {
          console.error('角色数据格式不正确', response)
          this.$notify({
            title: '错误',
            message: '获取角色数据失败，响应格式不正确',
            type: 'error',
            duration: 2000
          })
        }
      }).catch(error => {
        console.error('获取角色数据失败', error)
        this.$notify({
          title: '错误',
          message: '获取角色数据失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 搜索
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 重置搜索条件
    handleReset() {
      this.listQuery = {
        page: 1,
        limit: 15,
        username: undefined,
        cellphone: undefined,
        merchant_id: undefined,
        role_id: undefined
      }
      this.getList()
    },

    // 改变每页条数
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },

    // 改变页码
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.getList()
    },

    // 表格多选变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleUpdate(row)
          break
        case 'status':
          this.handleModifyStatus(row, row.is_freeze === 0 ? 1 : 0)
          break
        case 'password':
          this.handleChangePassword(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    // 重置临时表单数据
    resetTemp() {
      this.temp = {
        id: undefined,
        username: '',
        cellphone: '',
        email: '',
        password: '',
        merchant_id: undefined,
        role_id: undefined,
        is_freeze: 0
      }
    },

    // 创建账号
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 提交创建账号
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = {
            ...this.temp,
            relations: this.temp.line_ids.map(line_id => ({
              relation_type: 1,
              relation_id: line_id
            }))
          }
          // 确保is_freeze字段是数字类型
          tempData.is_freeze = Number(tempData.is_freeze)
          delete tempData.line_ids
          createAccount(tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },

    // 编辑账号
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      // 处理嵌套数据
      if (row.merchant && row.merchant.admin_id) {
        this.temp.merchant_id = row.merchant.admin_id
      }
      if (row.role && row.role.id) {
        this.temp.role_id = row.role.id
      }
      // 确保is_freeze字段正确设置为数字类型
      this.temp.is_freeze = Number(row.is_freeze)
      this.getAccountInfo(row.id)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 提交编辑账号
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = {
            ...this.temp,
            relations: this.temp.line_ids.map(line_id => ({
              relation_type: 1,
              relation_id: line_id
            }))
          }
          delete tempData.line_ids
          // 确保is_freeze字段是数字类型
          tempData.is_freeze = Number(tempData.is_freeze)
          updateAccount(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },

    // 删除账号
    handleDelete(row) {
      this.$confirm('确认删除该账号吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAccount(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 修改账号状态
    handleModifyStatus(row, is_freeze) {
      // 确保is_freeze是数字类型
      is_freeze = Number(is_freeze)
      const statusText = is_freeze === 0 ? '启用' : '禁用'
      this.$confirm(`确认${statusText}该账号吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeAccountStatus(row.id, is_freeze).then(() => {
          this.$notify({
            title: '成功',
            message: `${statusText}成功`,
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${statusText}`
        })
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }

      this.$confirm(`确认删除选中的 ${this.multipleSelection.length} 个账号吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.id)
        batchDeleteAccounts(ids).then(() => {
          this.$notify({
            title: '成功',
            message: '批量删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消批量删除'
        })
      })
    },

    // 批量修改状态
    handleBatchStatusChange() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }

      const targetStatus = this.hasDisabledItems ? 0 : 1
      const actionText = this.hasDisabledItems ? '启用' : '禁用'

      this.$confirm(`确认${actionText}选中的 ${this.multipleSelection.length} 个账号吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.id)
        // 确保targetStatus是数字类型
        Promise.all(ids.map(id => changeAccountStatus(id, Number(targetStatus)))).then(() => {
          this.$notify({
            title: '成功',
            message: `批量${actionText}成功`,
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消批量${actionText}`
        })
      })
    },

    // 修改密码
    handleChangePassword(row) {
      this.temp = Object.assign({}, row)
      this.passwordTemp = {
        new_password: '',
        confirm_password: ''
      }
      this.passwordDialogVisible = true
    },

    // 提交修改密码
    changePassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          if (this.passwordTemp.new_password !== this.passwordTemp.confirm_password) {
            this.$message({
              message: '两次输入的密码不一致',
              type: 'error'
            })
            return
          }

          changeAccountPassword(this.temp.id, this.passwordTemp).then(() => {
            this.$notify({
              title: '成功',
              message: '密码修改成功',
              type: 'success',
              duration: 2000
            })
            this.passwordDialogVisible = false
          }).catch(error => {
            // 处理错误情况
            let errorMessage = '修改密码失败，请重试'
            if (error.response && error.response.data && error.response.data.message) {
              errorMessage = error.response.data.message
            }
            this.$notify({
              title: '错误',
              message: errorMessage,
              type: 'error',
              duration: 2000
            })
          })
        }
      })
    },

    // 加载权限定义
    loadPermissionDefinitions() {
      return getAccountPermissionRules().then(response => {
        if (response.data && response.data.PERMISSION) {
          this.permissionDefinitions = response.data.PERMISSION
        }
      }).catch(error => {
        console.error('加载权限定义失败', error)
        this.$notify({
          title: '错误',
          message: '加载权限定义失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 将菜单权限定义转换为树形结构
    transformMenuPermissions(menuPermissionsDefinition) {
      this.menuPermissionsMap = {} // 清空映射

      const transformNode = (node) => {
        const result = {
          label: node.meta.title,
          permission: node.permission,
          children: []
        }

        // 保存到映射中以便快速查找
        this.menuPermissionsMap[node.permission] = result

        // 处理子菜单
        if (node.sub_menu && node.sub_menu.length > 0) {
          result.children = node.sub_menu.map(subNode => transformNode(subNode))
        } else if (node.page && node.page.length > 0) {
          result.children = node.page.map(subNode => transformNode(subNode))
        }

        return result
      }

      return menuPermissionsDefinition.map(node => transformNode(node))
    },

    // 将功能权限定义转换为列表
    transformFunctionPermissions(functionPermissionDefinition) {
      return Object.keys(functionPermissionDefinition).map(key => {
        return {
          key,
          label: this.getFunctionPermissionLabel(key),
          value: functionPermissionDefinition[key]
        }
      })
    },

    // 获取功能权限的显示标签
    getFunctionPermissionLabel(key) {
      const labelMap = {
        'freshOrderNotice': '新订单通知',
        'provincialReportConfig': '省级定制客运监管服务配置',
        'operateLineBranch': '线路分台操作权限'
      }
      return labelMap[key] || key
    },

    // 处理权限分配
    handlePermissions(row) {
      this.permissionsTemp = Object.assign({}, row)
      this.permissionsDialogVisible = true
      this.permissionsActiveTab = 'menu'

      // 加载该账号的权限
      this.loadAccountPermissions(row.id)
    },

    // 加载账号权限
    loadAccountPermissions(accountId) {
      getRolePermissions(accountId).then(response => {
        if (response.data) {
          // 使用API返回的权限数据
          const menuPermissions = response.data.menu_permissions_definition || []
          const functionPermissions = response.data.function_permission_definition || {}

          // 转换菜单权限为树形结构
          this.menuPermissionsTree = this.transformMenuPermissions(menuPermissions)

          // 转换功能权限为列表
          this.functionPermissions = this.transformFunctionPermissions(functionPermissions)

          // 保存完整的权限定义，以便后续提交时维持结构
          this.fullPermissions = response.data

          // 设置菜单权限选中状态
          this.$nextTick(() => {
            if (this.$refs.menuPermissionTree) {
              // 根据enabled属性来确定哪些节点被选中
              const selectedMenuPermissions = this.getSelectedMenuPermissions(menuPermissions)
              this.$refs.menuPermissionTree.setCheckedKeys(selectedMenuPermissions)
            }
          })
        }
      }).catch(error => {
        console.error('加载账号权限失败', error)
        this.$notify({
          title: '错误',
          message: '加载账号权限失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 递归获取已选中的菜单权限
    getSelectedMenuPermissions(menuPermissions) {
      const selectedKeys = []

      const processNode = (node) => {
        if (node.enabled === true) {
          selectedKeys.push(node.permission)
        }

        if (node.sub_menu && node.sub_menu.length > 0) {
          node.sub_menu.forEach(subNode => processNode(subNode))
        } else if (node.page && node.page.length > 0) {
          node.page.forEach(subNode => processNode(subNode))
        }
      }

      menuPermissions.forEach(node => processNode(node))
      return selectedKeys
    },

    // 保存权限设置
    savePermissions() {
      // 获取选中的菜单权限
      const selectedPermissions = this.$refs.menuPermissionTree.getCheckedKeys()

      // 更新完整权限结构中的enabled属性
      this.updateMenuPermissionsEnabledStatus(this.fullPermissions.menu_permissions_definition, selectedPermissions)

      // 更新功能权限
      this.functionPermissions.forEach(func => {
        this.fullPermissions.function_permission_definition[func.key] = func.value
      })

      updateRolePermissions(this.permissionsTemp.id, this.fullPermissions).then(() => {
        this.$notify({
          title: '成功',
          message: '权限设置成功',
          type: 'success',
          duration: 2000
        })
        this.permissionsDialogVisible = false
      }).catch(error => {
        let errorMessage = '权限设置失败，请重试'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        }
        this.$notify({
          title: '错误',
          message: errorMessage,
          type: 'error',
          duration: 2000
        })
      })
    },

    // 递归更新菜单权限的enabled状态
    updateMenuPermissionsEnabledStatus(menuPermissions, selectedKeys) {
      const updateNode = (node) => {
        node.enabled = selectedKeys.includes(node.permission)

        if (node.sub_menu && node.sub_menu.length > 0) {
          node.sub_menu.forEach(subNode => updateNode(subNode))
        } else if (node.page && node.page.length > 0) {
          node.page.forEach(subNode => updateNode(subNode))
        }
      }

      menuPermissions.forEach(node => updateNode(node))
    },

    // 角色管理
    handleRoleManage() {
      // 打开角色管理弹框前先刷新角色数据
      this.getRoleOptions()
      this.roleDialogVisible = true
    },

    // 创建角色
    handleCreateRole() {
      this.roleTemp = {
        id: undefined,
        name: '',
        description: ''
      }
      this.roleDialogStatus = 'create'
      this.roleFormDialogVisible = true
      this.$nextTick(() => {
        this.$refs['roleForm'].clearValidate()
      })
    },

    // 编辑角色
    handleUpdateRole(row) {
      this.roleTemp = Object.assign({}, row)
      this.roleDialogStatus = 'update'
      this.roleFormDialogVisible = true
      this.$nextTick(() => {
        this.$refs['roleForm'].clearValidate()
      })
    },

    // 提交创建角色
    createRole() {
      this.$refs['roleForm'].validate((valid) => {
        if (valid) {
          createRole(this.roleTemp).then(() => {
            this.roleFormDialogVisible = false
            this.$notify({
              title: '成功',
              message: '角色创建成功',
              type: 'success',
              duration: 2000
            })
            this.getRoleOptions()
          })
        }
      })
    },

    // 提交编辑角色
    updateRole() {
      this.$refs['roleForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.roleTemp)
          updateRole(tempData.id, tempData).then(() => {
            this.roleFormDialogVisible = false
            this.$notify({
              title: '成功',
              message: '角色更新成功',
              type: 'success',
              duration: 2000
            })
            this.getRoleOptions()
          })
        }
      })
    },

    // 处理角色权限
    handleRolePermissions(row) {
      this.selectedRole = Object.assign({}, row)
      this.rolePermissionsDialogVisible = true
      this.permissionsActiveTab = 'menu'

      // 加载角色的权限
      getRolePermissions(row.id).then(response => {
        if (response.data) {
          // 使用API返回的权限数据
          const menuPermissions = response.data.menu_permissions_definition || []
          const functionPermissions = response.data.function_permission_definition || {}

          // 转换菜单权限为树形结构
          this.menuPermissionsTree = this.transformMenuPermissions(menuPermissions)

          // 转换功能权限为列表
          this.functionPermissions = this.transformFunctionPermissions(functionPermissions)

          // 保存完整的权限定义，以便后续提交时维持结构
          this.fullPermissions = response.data

          // 设置菜单权限选中状态
          this.$nextTick(() => {
            if (this.$refs.roleMenuPermissionTree) {
              // 根据enabled属性来确定哪些节点被选中
              const selectedMenuPermissions = this.getSelectedMenuPermissions(menuPermissions)
              this.$refs.roleMenuPermissionTree.setCheckedKeys(selectedMenuPermissions)
            }
          })
        }
      }).catch(error => {
        console.error('加载角色权限失败', error)
        this.$notify({
          title: '错误',
          message: '加载角色权限失败，请重试',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 保存角色权限
    saveRolePermissions() {
      if (!this.selectedRole) return

      // 获取选中的菜单权限
      const selectedPermissions = this.$refs.roleMenuPermissionTree.getCheckedKeys()

      // 更新完整权限结构中的enabled属性
      this.updateMenuPermissionsEnabledStatus(this.fullPermissions.menu_permissions_definition, selectedPermissions)

      // 更新功能权限
      this.functionPermissions.forEach(func => {
        this.fullPermissions.function_permission_definition[func.key] = func.value
      })

      updateRolePermissions(this.selectedRole.id, this.fullPermissions).then(() => {
        this.$notify({
          title: '成功',
          message: '角色权限设置成功',
          type: 'success',
          duration: 2000
        })
        this.rolePermissionsDialogVisible = false
      }).catch(error => {
        let errorMessage = '角色权限设置失败，请重试'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        }
        this.$notify({
          title: '错误',
          message: errorMessage,
          type: 'error',
          duration: 2000
        })
      })
    },

    // 删除角色
    handleDeleteRole(row) {
      this.$confirm('确认删除该角色吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRole(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '角色删除成功',
            type: 'success',
            duration: 2000
          })
          this.getRoleOptions()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.handle-container {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 15px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.mini-condition {
  width: 220px;
}

.batch-operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f9eb;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px solid #e1f3d8;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.selected-count .count {
  font-weight: bold;
  color: #67c23a;
  margin: 0 3px;
}

.operation-buttons .el-button {
  margin-left: 10px;
}

.pagination-container {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
  text-align: right;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-tag i {
  margin-right: 4px;
  font-size: 14px;
}

.status-tag.success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.status-tag.danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.operation-button {
  padding: 6px 12px;
}

.operation-dropdown-menu {
  min-width: 130px;
}

.dropdown-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
}

.dropdown-item-content i {
  margin-right: 8px;
  font-size: 16px;
}

.dropdown-item-content.edit {
  color: #2B85E4;
}

.dropdown-item-content.enable {
  color: #19BE6B;
}

.dropdown-item-content.disable {
  color: #19BE6B;
}

.dropdown-item-content.password {
  color: #909399;
}

.dropdown-item-content.permission {
  color: #2B85E4;
}

.dropdown-item-content.delete {
  color: #F56C6C;
}

/* 对话框样式 */
.account-dialog .el-form-item {
  margin-bottom: 20px;
}

.password-dialog .el-form-item {
  margin-bottom: 20px;
}

.permission-dialog-title {
  font-size: 16px;
  color: #409EFF;
  margin-bottom: 20px;
  font-weight: bold;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
}

.permission-dialog-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.permission-dialog-title span {
  font-weight: normal;
  color: #303133;
}

.permission-dialog-title span:first-of-type {
  color: #409EFF;
  font-weight: bold;
}

.title-separator {
  margin: 0 8px;
  color: #909399;
}

.permission-tree-container {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.permission-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
  height: 400px;
  overflow-y: auto;
}

.permission-checkbox {
  margin-bottom: 12px;
  height: 32px;
  display: flex;
  align-items: center;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  padding: 4px 0;
}

.custom-tree-node .el-tag {
  margin-left: 10px;
}

.permission-badge {
  padding: 2px 6px;
  height: 20px;
  line-height: 16px;
}

.permission-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.permission-tabs .el-tabs__nav {
  font-weight: bold;
}

.permission-tabs .el-tabs__item {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.permission-tabs .el-tabs__item.is-active {
  color: #409EFF;
}

.permission-tree {
  width: 100%;
}

.permission-tree .el-tree-node__content {
  height: 32px;
}

.permission-tree .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409EFF;
  border-color: #409EFF;
}

/* 角色管理相关样式 */
.role-dialog-header {
  margin-bottom: 15px;
}

.role-dialog .role-operation-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.role-dialog .role-operation-buttons .el-button {
  margin: 0 2px;
  padding: 4px 5px;
  font-size: 12px;
}

/* 给表格中的操作列增加足够的宽度 */
.role-dialog .el-table .el-table__header th:last-child,
.role-dialog .el-table .el-table__body td:last-child {
  min-width: 240px !important;
  width: 240px !important;
}
</style>

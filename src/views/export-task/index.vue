<template>
  <div class="app-container">
    <el-card>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchParams" size="small" class="demo-form-inline" @submit.native.prevent>
        <el-form-item label="状态">
          <el-select v-model="searchParams.status" placeholder="请选择状态" clearable size="small">
            <el-option label="待处理" value="pending"></el-option>
            <el-option label="处理中" value="processing"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="失败" value="failed"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            size="small"
            v-model="searchParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" size="small">查询</el-button>
          <el-button @click="resetSearch" size="small">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="taskList"
        style="width: 100%; margin-bottom: 20px;"
        border
      >
        <el-table-column
          prop="id"
          label="任务ID"
          width="180"
        />
        <el-table-column
          prop="title"
          label="任务名称"
          width="180"
        />
        <el-table-column
          prop="status"
          label="状态"
        >
          <template slot-scope="scope">
            <span>{{ formatStatus(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createdAt"
          label="创建时间"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === 'completed' && scope.row.file_path"
              size="mini"
              type="primary"
              style="margin-right: 10px;"
              @click="handleDownload(scope.row.file_path)"
            >下载</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件容器 (更新结构) -->
      <div class="bottom-container">
        <div class="left-content">
          <!-- 左侧内容区域，可以留空或添加其他元素 -->
        </div>
        <el-pagination
          small
          background
          :current-page.sync="currentPage" 
          :page-size="pageSize" 
          :page-sizes="pageSizes" 
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { getExportTasks, deleteExportTask } from '@/api/exportTask'

export default {
  name: 'ExportTask',
  data() {
    return {
      loading: false,
      taskList: [],
      total: 0,
      pageSize: 15,
      pageSizes: [15, 30, 45, 60],
      currentPage: 1,
      searchParams: { // 新增搜索参数
        status: '',
        dateRange: [],
        startDate: '',
        endDate: ''
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const params = {
        page: this.currentPage,
        per_page: this.pageSize,
        status: this.searchParams.status, // 添加 status 参数
        start_date: this.searchParams.startDate, // 添加 start_date 参数
        end_date: this.searchParams.endDate // 添加 end_date 参数
      }
      getExportTasks(params).then(response => {
        // 打印完整的响应对象以供调试
        console.log('API Response:', response)

        // 后端返回的数据结构是 { data: { data: [...], meta: { pagination: { total: ..., current_page: ..., per_page: ... } } } }
        // 根据实际返回结构进行调整
        if (response && response.data) {
          // 添加保护：确保 response.data.data 是一个数组，如果不是，则赋值为空数组
          this.taskList = Array.isArray(response.data.data) ? response.data.data.map(item => ({
            ...item,
            createdAt: item.created_at // Map created_at if needed by table
          })) : []
          // 同样为 meta.pagination 添加保护
          if (response.data.meta && response.data.meta.pagination) {
            const pagination = response.data.meta.pagination
            this.total = pagination.total || 0
            this.pageSize = pagination.per_page || 15
            this.currentPage = pagination.current_page || 1
          } else {
            // 如果 pagination 信息不存在，设置默认值
            this.total = 0
            this.pageSize = 15
            this.currentPage = 1
          }
        } else {
          // 如果 response.data 不存在，则设置默认空状态
          this.taskList = []
          this.total = 0
          this.pageSize = 15
          this.currentPage = 1
        }
        this.loading = false
      }).catch(error => {
        console.error('获取导出任务失败:', error)
        this.$message.error('获取导出任务失败')
        this.loading = false
      })
    },
    // Method to format status to Chinese
    formatStatus(status) {
      const statusMap = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        failed: '失败'
        // Add other statuses as needed
      }
      return statusMap[status] || status // Return original status if not found in map
    },
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchData()
    },
    // 新增：处理每页显示条数变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 切换每页条数时，重置到第一页
      this.fetchData()
    },
    // 新增：处理搜索
    handleSearch() {
      // 处理日期范围
      if (this.searchParams.dateRange && this.searchParams.dateRange.length === 2) {
        this.searchParams.startDate = this.searchParams.dateRange[0]
        this.searchParams.endDate = this.searchParams.dateRange[1]
      } else {
        this.searchParams.startDate = ''
        this.searchParams.endDate = ''
      }
      this.currentPage = 1 // 搜索后重置到第一页
      this.fetchData()
    },
    // 新增：重置搜索
    resetSearch() {
      this.searchParams = {
        status: '',
        dateRange: [],
        startDate: '',
        endDate: ''
      }
      this.currentPage = 1 // 重置后也回到第一页
      this.fetchData()
    },
    // Method to handle download
    handleDownload(filePath) {
      // window.open(filePath, '_blank') // 原有方法
      // 尝试使用创建 <a> 标签的方式下载
      const link = document.createElement('a');
      link.href = filePath;
      // 尝试从路径中提取文件名，或者你可以让后端直接返回文件名
      const filename = filePath.substring(filePath.lastIndexOf('/') + 1);
      link.setAttribute('download', filename || 'download'); // download 属性提示浏览器下载
      link.style.display = 'none'; // 隐藏元素
      document.body.appendChild(link);
      link.click(); // 模拟点击
      document.body.removeChild(link); // 清理 DOM
    },
    handleDelete(index, row) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true // 添加加载状态
        deleteExportTask(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          // 删除成功后重新获取数据，以确保分页和总数正确
          // Consider edge case: deleting the last item on a page > 1
          if (this.taskList.length === 1 && this.currentPage > 1) {
            this.currentPage--
          }
          this.fetchData()
        }).catch(error => {
          console.error('删除任务失败:', error)
          this.$message.error('删除任务失败')
          this.loading = false // 确保在出错时也取消加载状态
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right; /* 使分页组件靠右 */
}
/* 添加来自 car-manage 的样式 */
.bottom-container {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left-content {
  display: flex;
  align-items: center;
}
.selected-count {
  margin-left: 10px;
  color: #909399;
  font-size: 13px;
}

.demo-form-inline .el-form-item { /* 调整搜索表单间距 */
  margin-bottom: 10px;
}
</style> 

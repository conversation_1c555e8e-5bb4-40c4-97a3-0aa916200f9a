<template>
  <!--系统设置-省级定制客运监管服务配置-->
  <div class="config-container">
    <el-form
      ref="dataForm"
      v-loading="loading"
      label-width="240px"
      :inline="false"
      :model="configData"
    >
      <div class="form-content">
        <el-form-item label="上报车辆数据">
          <el-switch
            v-model="configData.vehicle_data_report"
            active-color="#409EFF"
            inactive-color="#DCDFE6"
            :active-value="1"
            :inactive-value="0"
          />
          <span class="tip-text">开启后，系统将自动上报车辆数据到省客运管理平台</span>
        </el-form-item>

        <el-form-item label="上报驾驶员数据">
          <el-switch
            v-model="configData.driver_data_report"
            active-color="#409EFF"
            inactive-color="#DCDFE6"
            :active-value="1"
            :inactive-value="0"
          />
          <span class="tip-text">开启后，系统将自动上报驾驶员数据到省客运管理平台</span>
        </el-form-item>

        <el-form-item label="上报订单车票数据">
          <el-switch
            v-model="configData.order_ticket_report"
            active-color="#409EFF"
            inactive-color="#DCDFE6"
            :active-value="1"
            :inactive-value="0"
          />
          <span class="tip-text">开启后，系统将自动上报订单车票数据到省客运管理平台</span>
        </el-form-item>

        <el-form-item label="上报订单车票退票数据">
          <el-switch
            v-model="configData.order_refund_report"
            active-color="#409EFF"
            inactive-color="#DCDFE6"
            :active-value="1"
            :inactive-value="0"
          />
          <span class="tip-text">开启后，系统将自动上报订单车票退票数据到省客运管理平台</span>
        </el-form-item>

        <div class="button-bottom">
          <el-button
            type="primary"
            size="small"
            @click="saveConfig"
          >保存配置</el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getProvincialReportConfig, updateProvincialReportConfig } from '@/api/admin'

export default {
  name: 'ProvincialReportConfig',
  data() {
    return {
      loading: false,
      configData: {
        type: 'provincial_reporting',
        vehicle_data_report: 0,
        driver_data_report: 0,
        order_ticket_report: 0,
        order_refund_report: 0
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    // 获取配置
    async getConfig() {
      this.loading = true
      try {
        const response = await getProvincialReportConfig()
        console.log('获取到的配置数据:', response)

        // 检查响应结构
        let data = {}
        if (response && response.data && response.data.value) {
          // 直接使用value字段中的数据
          data = response.data.value
        }

        // 确保数据正确映射
        this.configData = {
          type: 'provincial_reporting',
          vehicle_data_report: data.vehicle_data_report !== undefined ? parseInt(data.vehicle_data_report) : 0,
          driver_data_report: data.driver_data_report !== undefined ? parseInt(data.driver_data_report) : 0,
          order_ticket_report: data.order_ticket_report !== undefined ? parseInt(data.order_ticket_report) : 0,
          order_refund_report: data.order_refund_report !== undefined ? parseInt(data.order_refund_report) : 0
        }

        console.log('处理后的配置数据:', this.configData)
      } catch (error) {
        console.error('获取配置失败', error)
        // 如果获取失败，使用默认值
        this.configData = {
          type: 'provincial_reporting',
          vehicle_data_report: 0,
          driver_data_report: 0,
          order_ticket_report: 0,
          order_refund_report: 0
        }
      } finally {
        this.loading = false
      }
    },
    // 保存配置
    async saveConfig() {
      this.loading = true
      try {
        console.log('提交的配置数据:', this.configData)

        // 确保数据类型正确
        const submitData = {
          type: 'provincial_reporting',
          vehicle_data_report: parseInt(this.configData.vehicle_data_report),
          driver_data_report: parseInt(this.configData.driver_data_report),
          order_ticket_report: parseInt(this.configData.order_ticket_report),
          order_refund_report: parseInt(this.configData.order_refund_report)
        }

        await updateProvincialReportConfig(submitData)
        this.$notify.success('保存成功')
      } catch (error) {
        console.error('保存配置失败', error)
        this.$notify.error('保存失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  margin-top: 20px;
  height: calc(100vh - 200px);
  width: 100%;

  .el-form {
    width: 100%;
    max-width: none;
  }
}

.form-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.tip-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>

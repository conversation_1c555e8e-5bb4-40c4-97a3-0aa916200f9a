<template>
  <!--系统设置-功能设置-->
  <div class="config-container">
    <el-form
      ref="insuranceData"
      v-loading="loading"
      label-width="140px"
      :rules="insuranceRules"
      :model="insuranceData"
    >
      <el-form-item label="保险服务开关">
        <el-switch
          v-model="insuranceSwitch"
          class="switch"
          inline-prompt
          :inactive-value="0"
          :active-value="1"
          @change="setConfigStatus"
        />
        <span class="label-key">关闭后，则乘客无法使用保险功能</span>
      </el-form-item>
      <el-form-item label="保险名称" prop="insurance_name">
        <el-input
          v-model="insuranceData.insurance_name"
          class="addInputLength"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item label="保险金额（元）" prop="insurance_amount">
        <el-input
          v-model="insuranceData.insurance_amount"
          class="persentInput"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item label="保险描述：" prop="insurance_desc">
        <el-input
          v-model="insuranceData.insurance_desc"
          type="textarea"
          :autosize="{ minRows: 6}"
          class="custom-area"
          autocomplete="off"
        />
      </el-form-item>
      <div class="button-bottom">
        <el-button type="primary" size="small" @click="setConfigMessage">保存配置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  getInvoiceMessage,
  setChangeStatus,
  setInvoiceMessage
} from '@/api/systemSetup'
import { insuranceRules } from '@/rules'

export default {
  data() {
    return {
      loading: false,
      insuranceRules: insuranceRules,
      insuranceSwitch: 0,
      insuranceData: {}
    }
  },
  mounted() {
    this.getConfigMessage()
  },
  methods: {
    // 获取商户发票配置信息
    getConfigMessage() {
      this.loading = true
      getInvoiceMessage('insurance').then((value) => {
        this.loading = false
        // 如果开票设置、积分配置、保险设置开启
        if (!value.data.is_enabled) {
          this.insuranceSwitch = 0
        } else {
          this.insuranceSwitch = 1
        }
        this.insuranceData = value.data.value ? value.data.value : {}
      })
    },

    // 配置信息（发票、积分、保险）
    setConfigMessage() {
      let data = null
      let ref = null
      data = this.insuranceData
      ref = 'insuranceData'
      this.$refs[ref].validate((valid) => {
        if (valid) {
          setInvoiceMessage({ type: 'insurance', ...data }).then(() => {
            this.$notify.success('保存成功')
          })
        } else {
          this.$notify.warning('未按要求填写表单')
          return false
        }
      })
    },

    // 商户配置开启、关闭
    setConfigStatus() {
      setChangeStatus({
        type: 'insurance',
        is_enabled: this.insuranceSwitch
      }).then(() => {
        if (this.insuranceSwitch) {
          this.$notify.success('开启保险配置')
        } else {
          this.$notify.success('关闭保险配置')
        }
      })
    }
  }
}
</script>

<style lang="scss" >
.custom-area{
  textarea{
    max-height:calc(100vh - 550px) !important;
  }
}
.dubleLine{
  .el-form-item__label{
    line-height: 20px;
  }
}
</style>
<style lang="scss" scoped>
.config-container {
  margin-top: 40px;
  height: calc(100vh - 230px);
  .el-form{
    max-width: 1100px;
  }

}
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

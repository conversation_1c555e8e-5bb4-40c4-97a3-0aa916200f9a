<template>
  <!--系统设置-功能设置-->
  <div class="config-container">
    <el-tabs v-model="activeName" tab-position="left">
      <el-tab-pane
        v-for="item in agreements"
        :key="item.type"
        :name="item.type"
        :label="item.name"
      >
        <el-form
          :ref="'dataForm' + item.type"
          v-loading="loading"
          label-width="140px"
          :inline="false"
          :rules="agrules"
          :model="current_temp"
        >
          <el-form-item v-if="activeName === '4'" label="定制客运所属子业态">
            <el-radio-group v-model="sub_business_type" size="small" @change="getConfigMessage">
              <el-radio-button label="0">购票须知</el-radio-button>
              <el-radio-button label="1">班线客运 [班]</el-radio-button>
              <el-radio-button label="2">定制客运 [班-定制]</el-radio-button>
              <el-radio-button label="3">城市公共交通运输 [公交]</el-radio-button>
              <el-radio-button label="4">旅游班线客运 [班-旅游]</el-radio-button>
              <el-radio-button label="5">包车客运[包]</el-radio-button>
              <el-radio-button label="6">城乡/农村客运[班-农村]</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="activeName === '4'" label="所属子业态" prop="sub_business_type">
            <el-select
              v-model="current_temp.sub_business_type"
              placeholder="请选择所属子业态"
              class="addInputLength"
            >
              <el-option :value="0" label="请选择所属子业" />
              <el-option :value="1" label="班线客运 [班]" />  
              <el-option :value="2" label="定制客运 [班-定制]" />
              <el-option :value="3" label="城市公共交通运输 [公交]" />
              <el-option :value="4" label="旅游班线客运 [班-旅游]" />
              <el-option :value="5" label="包车客运[包]" />
              <el-option :value="6" label="城乡/农村客运[班-农村]" />
            </el-select>
          </el-form-item>
          <el-form-item label="协议自定义名称" prop="agreement_name_alias">
            <el-input
              v-model="current_temp.agreement_name_alias"
              class="addInputLength"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item label="协议内容" prop="agreement_content">
            <el-input
              v-model="current_temp.agreement_content"
              type="textarea"
              :autosize="{ minRows: 8 }"
              class="agreement-area"
              autocomplete="off"
            />
          </el-form-item>
          <div class="button-bottom">
            <el-button
              type="primary"
              size="small"
              @click="setConfigMessage(item.type)"
            >保存协议</el-button>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getAgreements, putAgreements, addAgreements } from '@/api/admin'
export default {
  data() {
    return {
      activeName: '',
      agreements: [
        { type: '1', name: '隐私协议' },
        { type: '2', name: '用户协议' },
        { type: '3', name: '乘车协议' },
        { type: '4', name: '购票须知' }
      ],
      loading: false,
      sub_business_type: '1',
      current_temp: {},
      agrules: {
        sub_industry: [{ required: true, message: '请选择所属子业态', trigger: 'change' }],
        agreement_name_alias: [{ required: true, message: '请输入协议自定义名称', trigger: 'blur' }],
        agreement_content: [{ required: true, message: '请输入协议内容', trigger: 'blur' }]
      }
    }
  },
  watch: {
    activeName: {
      handler(n) {
        if (n) {
          this.getConfigMessage()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.activeName = this.agreements[0].type
  },
  methods: {
    // 获取
    async getConfigMessage() {
      try {
        await this.$refs['dataForm' + this.activeName][0].clearValidate()
      } catch (error) {
        console.log(error)
      }
      this.loading = true
      const params = {
        type: this.activeName
      }
      if (this.activeName === '4') {
        params.sub_business_type = Number(this.sub_business_type)
      }
      const { data } = await getAgreements(params)
      this.current_temp = data && data[0] || {}
      this.loading = false
    },
    // 保存
    async setConfigMessage(type) {
      await this.$refs['dataForm' + type][0].validate()
      if (this.activeName === '4') {
        this.current_temp.sub_business_type = Number(this.sub_business_type)
      }
      if (this.current_temp && this.current_temp.agreement_id) {
        await putAgreements({ ...this.current_temp, type: this.activeName })
      } else {
        await addAgreements({ ...this.current_temp, type: this.activeName })
      }
      this.$notify.success('保存成功')
    }
  }
}
</script>

<style lang="scss" >
.agreement-area {
  textarea {
    max-height: calc(100vh - 380px) !important;
  }
}
.dubleLine {
  .el-form-item__label {
    line-height: 20px;
  }
}
</style>
<style lang="scss" scoped>
.config-container {
  margin-top: 40px;
  height: calc(100vh - 230px);
  .el-form {
    max-width: 1100px;
  }
}
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

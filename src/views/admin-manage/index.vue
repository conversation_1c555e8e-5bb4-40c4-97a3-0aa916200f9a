<template>
  <!--系统设置-功能设置-->
  <div class="app-container">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane
        v-for="item in filteredPermissions"
        :key="item.permission"
        :label="item.meta.title"
        :name="item.permission"
      />
    </el-tabs>

    <div class="tab-content">
      <InsuranceManage v-if="activeName === 'insuranceManage'" />
      <AgreementManage v-if="activeName === 'agreementManage'" />
      <ProvincialReportConfig v-if="activeName === 'provincialReportConfig' && isProvincialReportConfigEnabled" />
    </div>

    <el-empty v-if="!filteredPermissions || filteredPermissions.length === 0" description="暂无可配置权限" />
  </div>
</template>

<script>
import { insuranceRules } from '@/rules'
import InsuranceManage from './components/InsuranceManage'
import AgreementManage from './components/AgreementManage'
import ProvincialReportConfig from './components/ProvincialReportConfig'

export default {
  name: 'AdminManage',
  components: { InsuranceManage, AgreementManage, ProvincialReportConfig },
  data() {
    return {
      activeName: '',
      loading: false,
      insuranceRules: insuranceRules,
      insuranceSwitch: 0,
      insuranceData: {}
    }
  },
  computed: {
    permissions() {
      // 获取路由中的权限配置
      const routePermissions = this.$route && this.$route.meta && this.$route.meta.page ? this.$route.meta.page.filter((o) => o.enabled) : []

      // 获取全局功能权限配置
      const functionPermissions = this.$store.state.user.function_permission_definition || {}

      // 过滤掉没有权限的provincialReportConfig
      const filteredRoutePermissions = routePermissions.filter(item => {
        if (item.permission === 'provincialReportConfig') {
          return functionPermissions.provincialReportConfig === true
        }
        return true
      })

      // 只有当全局功能权限配置允许时，才添加provincialReportConfig权限
      const hasProvincialReportConfig = filteredRoutePermissions.some(item => item.permission === 'provincialReportConfig')
      const provincialReportConfigEnabled = functionPermissions.provincialReportConfig === true

      if (!hasProvincialReportConfig && provincialReportConfigEnabled) {
        filteredRoutePermissions.push({
          permission: 'provincialReportConfig',
          enabled: true,
          meta: {
            title: '上报省客运管理平台数据功能设置'
          }
        })
      }

      return filteredRoutePermissions
    },
    // 过滤掉重复的权限
    filteredPermissions() {
      const uniquePermissions = []
      const permissionMap = {}

      this.permissions.forEach(item => {
        if (!permissionMap[item.permission]) {
          permissionMap[item.permission] = true
          uniquePermissions.push(item)
        }
      })

      return uniquePermissions
    },
    isProvincialReportConfigEnabled() {
      // 获取全局功能权限配置
      const functionPermissions = this.$store.state.user.function_permission_definition || {}
      // 当provincialReportConfig没有定义或者值为false时，返回false
      return functionPermissions.provincialReportConfig === true
    }
  },
  watch: {
    // 监听权限变化，确保当provincialReportConfig权限被禁用时，自动切换到其他可用的标签页
    isProvincialReportConfigEnabled(newVal) {
      if (!newVal && this.activeName === 'provincialReportConfig' && this.filteredPermissions.length > 0) {
        // 如果当前选中的是provincialReportConfig，但权限被禁用，则切换到第一个可用的标签页
        this.activeName = this.filteredPermissions[0].permission
      }
    }
  },
  mounted() {
    if (this.filteredPermissions && this.filteredPermissions.length > 0) {
      this.activeName = this.filteredPermissions[0].permission
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content {
  flex: 1;
  width: 100%;
  overflow: auto;
}

.el-form {
  width: 100%;
  padding-left: 0;
}

.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
</style>

<template>
  <div class="line-select-container">
    <!-- 方向选择部分保持不变 -->
    <div class="item-filter-warp" style="margin: 5px 0">
      <span class="col-label-key">方向选择</span>
      <div class="search-city">
        <div class="from-city">
          <el-cascader
            ref="startCascader"
            v-model="startAddressKeyList"
            :options="startCode"
            clearable
            size="mini"
            :props="props"
            @change="onStartCodeChange"
          />
        </div>
        <i class="icon-change" @click="onExchange" />
        <div class="to-city">
          <el-cascader
            ref="endCascader"
            v-model="endAddressKeyList"
            :options="endCode"
            clearable
            size="mini"
            :props="props"
            @change="onEndCodeChange"
          />
        </div>
      </div>
    </div>

    <!-- 线路选择部分 -->
    <div v-if="filteredTimetablesList.length > 0" class="item-filter-warp">
      <span class="col-label-key">线路选择</span>
      <div class="tag-selected-warp-container">
        <div class="tag-selected-warp" :class="{ 'tag-collapsed': !isExpanded }">
          <el-tag
            :type="!selectedLineClassId ? '' : 'info'"
            size="small"
            :effect="!selectedLineClassId ? 'dark' : 'plain'"
            @click="onSelectLine('', '')"
          >
            全部
          </el-tag>
          <template v-for="(lineItem, i) in filteredTimetablesList">
            <!-- 使用不同的key前缀来确保v-if/else分支有唯一的key -->
            <div :key="'line-item-' + i">
              <el-dropdown
                v-if="lineItem.times && lineItem.times.length > 1"
                :key="'dropdown-' + i"
                size="small"
                trigger="click"
                @command="(time) => onSelectLine(lineItem.line_class_id, time)"
              >
                <el-tag
                  size="small"
                  :type="selectedLineClassId === lineItem.line_class_id ? '' : 'info'"
                  :effect="selectedLineClassId === lineItem.line_class_id ? 'dark' : 'plain'"
                  @click="onSelectLine(lineItem.line_class_id, '')"
                >
                  {{ lineItem.start_name }}→{{ lineItem.end_name }}
                  <div v-if="selectedLineClassId === lineItem.line_class_id" style="display: inline-block;width: 1px;height: 12px;background-color: #dcdfe6; transform: translate(3px, 1px);" />
                  <span v-if="selectedLineClassId === lineItem.line_class_id" style="font-size: 12px;color: #8B96A6;font-weight: 400; margin-left: 10px; color: #dcdfe6; font-weight: 500;">{{ selectedTime }}</span>
                  <i
                    class="el-icon-arrow-down el-icon--right"
                  />
                </el-tag>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="tim in lineItem.times" :key="tim" :command="tim">
                    {{ tim }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-tag
                v-else
                :key="'tag-' + i"
                :type="selectedLineClassId === lineItem.line_class_id ? '' : 'info'"
                size="small"
                :effect="selectedLineClassId === lineItem.line_class_id ? 'dark' : 'plain'"
                @click="onSelectLine(lineItem.line_class_id, lineItem.times && lineItem.times.length ? lineItem.times[0] : '')"
              >
                {{ lineItem.start_name }}→{{ lineItem.end_name }}
                {{ lineItem.times && lineItem.times.length ? lineItem.times[0] : '' }}
              </el-tag>
              <el-button v-if="showAddTimeLine" :key="i" size="mini" icon="el-icon-plus" style="width: 24px;height: 24px;transform: translate(-9px, -1px);padding: 0;margin-left: 0px;border-top-left-radius: 0px; border-bottom-left-radius: 0px; border-left: none;" @click="handleAddLine(lineItem)" />
            </div>
          </template>
        </div>

        <!-- 展开/折叠按钮 -->
        <!-- <div v-if="shouldShowExpandToggle" class="expand-toggle" @click="toggleExpand">
          <i :class="['el-icon', isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          <span>{{ isExpanded ? '收起' : '展开' }}</span>
        </div> -->

        <el-dialog
          :visible.sync="dialogAddTimeLine"
          title="临时增加班次"
          append-to-body
          width="850px"
        >
          <template v-if="is_seat_selection">
            <p class="label-word">请选择司机及其排序（单选）：</p>
            <div class="driver-list">
              <div style="margin: 15px 0;" />
              <el-radio-group v-model="checkedRadioDriver">
                <template v-for="driver in drivers">
                  <el-radio :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
                  </el-radio>
                </template>
              </el-radio-group>
            </div>
          </template>
          <template v-else>
            <p class="label-word">请选择司机及其排序（可多选）：</p>
            <div class="driver-list">
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
              <div style="margin: 15px 0;" />
              <el-checkbox-group v-model="checkedDrivers" @change="handleCheckedCitiesChange">
                <template v-for="driver in drivers">
                  <el-checkbox :key="driver.driver_id" :label="driver.driver_id"><span class="over-flow-txt">{{ driver.name }} · [{{ driver.car_tail_number }}]（{{ driver.total_seating }}座）</span> 排序：<el-input-number :key="driver.driver_id" v-model="driver.sort" size="mini" :min="0" :max="999" label="请输入排序，如001" />
                  </el-checkbox>
                </template>
              </el-checkbox-group>
            </div>
          </template>
          <div style="margin-top: 20px;">
            <p class="label-word">请选择日期</p>
            <el-date-picker
              v-model="addTimeLineForm.selected_date"
              type="date"
              placeholder="选择日期"
              :picker-options="{disabledDate(time) { return time.getTime() < Date.now() - 8.64e7; }}"
            />
          </div>
          <div style="margin-top: 20px;">
            <p v-if="addTimeLineInfo.start_time_type === 1" class="label-word">请选择出发时间</p>
            <p v-else class="label-word">请选择时间范围</p>
          </div>

          <el-time-picker
            v-show="addTimeLineInfo.start_time_type===1"
            v-model="addTimeLineForm.start_time"
            arrow-control
            value-format="HH:mm:ss"
            placeholder="发车时间"
          />
          <el-time-picker
            v-show="addTimeLineInfo.start_time_type===2"
            v-model="addTimeLineForm.time"
            arrow-control
            value-format="HH:mm:ss"
            is-range
            range-separator="至"
            start-placeholder="最早时间"
            end-placeholder="最晚时间"
          />

          <template #footer>
            <span class="dialog-footer">
              <el-button type="primary" plain size="small" @click="dialogAddTimeLine = false">取消</el-button>
              <el-button type="primary" size="small" @click="handleAddTimeLine">确认</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'
import { addTimeLine, schedulesDrivers } from '@/api'

export default {
  name: 'LineSelect',
  props: {
    // 分台ID
    branchId: {
      type: [String, Number],
      default: ''
    },
    // 是否在初始化时自动加载数据
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 是否显示临时增加班次
    showAddTimeLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      drivers: [],
      dialogAddTimeLine: false,
      dialogChangeTickets: false,
      ticketsInfo: {},
      addTimeLineInfo: {},
      is_seat_selection: false,
      checkedRadioDriver: '',
      checkedDrivers: [],
      checkAll: false,
      isIndeterminate: true,
      addTimeLineForm: {
        start_time: '',
        time: ['', ''],
        selected_date: new Date()
      },
      // 方向选择器配置
      props: {
        checkStrictly: true,
        expandTrigger: 'hover'
      },
      // 内部状态数据
      startAddressKeyList: [],
      endAddressKeyList: [],
      startAddressCode: '',
      endAddressCode: '',
      startCityName: '',
      endCityName: '',
      // 线路选择数据
      selectedLineClassId: '',
      selectedTime: '',
      // 新增：控制标签展开/折叠
      isExpanded: true
    }
  },
  computed: {
    ...mapGetters([
      'startCode',
      'endCode',
      'timetablesList'
    ]),
    // 根据当前选择的出发地和目的地过滤线路列表
    filteredTimetablesList() {
      // 确保timetablesList是数组
      if (!this.timetablesList || !Array.isArray(this.timetablesList)) {
        return []
      }

      return this.timetablesList
    },
    // 新增：是否应该显示展开/折叠按钮
    shouldShowExpandToggle() {
      // 如果标签太多（比如超过10个），才显示展开/折叠按钮
      return this.filteredTimetablesList.length > 10
    }
  },
  watch: {
    branchId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) { // 只在真正变化时重新加载
          this.$store.dispatch('line/setBranchId', newVal)
          this.loadStartData() // 重新加载出发地数据
        }
      },
      immediate: true // 组件创建时立即执行
    }
  },
  created() {
    // 初始化时自动加载数据
    if (this.autoLoad) {
      this.loadInitialData()
    }
  },
  methods: {
    // 手动清空所有选择的数据，然后重新调用
    resetAll() {
      this.resetSelection()
      this.loadInitialData()
    },
    async handleAddTimeLine() {
      if (this.is_seat_selection) {
        if (!this.checkedRadioDriver) {
          this.$message.error('请选择司机')
          return
        }
      } else {
        if (!this.checkedDrivers.length) {
          this.$message.error('请选择司机')
          return
        }
      }

      if (!this.addTimeLineForm.selected_date) {
        this.$message.error('请选择日期')
        return
      }

      if (this.addTimeLineInfo.start_time_type === 1) {
        if (!this.addTimeLineForm.start_time) {
          this.$message.error('请选择出发时间')
          return
        }
      } else {
        if (!this.addTimeLineForm.time[0] || !this.addTimeLineForm.time[1]) {
          this.$message.error('请选择时间范围')
          return
        }
        if (this.addTimeLineForm.time[0] >= this.addTimeLineForm.time[1]) {
          this.$message.error('最早时间不能大于最晚时间')
          return
        }
      }

      let drivers = []
      if (this.is_seat_selection) {
        drivers = [{
          driver_id: this.checkedRadioDriver,
          order: this.drivers.find(d => d.driver_id === this.checkedRadioDriver) && this.drivers.find(d => d.driver_id === this.checkedRadioDriver).sort
        }]
      } else {
        drivers = this.checkedDrivers.map(o => ({
          driver_id: o,
          order: this.drivers.find(d => d.driver_id === o) && this.drivers.find(d => d.driver_id === o).sort
        }))
      }
      const params = {
        line_class_id: this.addTimeLineInfo.line_class_id,
        start_date: moment(this.addTimeLineForm.selected_date).format('YYYY-MM-DD'),
        drivers
      }
      if (this.addTimeLineInfo.start_time_type === 1) {
        params.start_time = this.addTimeLineForm.start_time
      } else {
        params.start_earliest_time = this.addTimeLineForm.time[0]
        params.end_latest_time = this.addTimeLineForm.time[1]
      }
      await addTimeLine(params)
      this.$message.success('操作成功')
      this.dialogAddTimeLine = false
      this.$emit('refresh')
    },
    async handleAddLine(lineItem) {
      this.addTimeLineInfo = lineItem
      this.is_seat_selection = lineItem.is_seat_selection
      this.checkedDrivers = []
      this.addTimeLineForm = {
        start_time: '',
        time: ['', ''],
        selected_date: new Date()
      }
      await this.fetchDrivers(lineItem.branchid)
      this.dialogAddTimeLine = true
    },
    async fetchDrivers(branch_id = '', isSearch = false) {
      const res = await schedulesDrivers({
        branch: branch_id

      })
      if (isSearch) {
        this.searchDrivers = res && res.data || []
        return
      }

      this.drivers = res && res.data || []
    },
    // 新增：切换展开/折叠状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },

    emitChange() {
      this.$emit('change', {
        start_address_code: this.startAddressCode,
        end_address_code: this.endAddressCode,
        line_class_id: this.selectedLineClassId,
        time: this.selectedTime,
        sub_business_type: this.selectedSubBusinessType,
        line_keyword: this.lineSearchKeyword,
        start_city_name: this.startCityName,
        end_city_name: this.endCityName,
        start_city_code: this.startCityCode,
        end_city_code: this.endCityCode
      })
    },

    // 其他方法保持不变...
    // 加载出发地数据
    async loadStartData() {
      // 清空选择状态
      this.resetSelection()

      this.loadInitialData()
    },

    // 初始化加载数据
    async loadInitialData() {
    // 先加载出发地和目的地选项
      await this.$store.dispatch('line/fetchStartCode')
      await this.$store.dispatch('line/fetchEndCode', '')

      // 直接加载全部线路列表，不传任何筛选条件
      await this.$store.dispatch('line/fetchTimetables', {
        startAddressCode: '',
        endAddressCode: '',
        branch_id: this.branchId
      })
    },

    // 重置选择状态
    resetSelection() {
      this.startAddressKeyList = []
      this.endAddressKeyList = []
      this.startAddressCode = ''
      this.endAddressCode = ''
      this.startCityName = ''
      this.endCityName = ''
      this.selectedLineClassId = ''
      this.selectedTime = ''
      this.selectedSubBusinessType = ''
      this.lineSearchKeyword = ''
    },

    // 出发地变更处理
    async onStartCodeChange(e) {
      // 设置出发地状态
      if (!e || e.length === 0) {
        this.startAddressKeyList = []
        this.startAddressCode = ''
        this.startCityName = ''
        this.startCityCode = ''
      } else {
        const startCityName = this.spliceAddressName(e, this.startCode)
        const startAddressCode = e[e.length - 1]
        const startCityCode = e[0]

        this.startAddressKeyList = e
        this.startAddressCode = startAddressCode
        this.startCityName = startCityName
        this.startCityCode = startCityCode
      }
      this.emitChange()
      // 不管有没有选择，都获取线路列表
      await this.$store.dispatch('line/fetchTimetables', {
        startAddressCode: this.startAddressCode,
        endAddressCode: this.endAddressCode
      })
    },

    // 目的地变更处理
    async onEndCodeChange(e) {
      // 设置目的地状态
      if (!e || e.length === 0) {
        this.endAddressKeyList = []
        this.endAddressCode = ''
        this.endCityName = ''
        this.endCityCode = ''
      } else {
        const endCityName = this.spliceAddressName(e, this.endCode)
        const endAddressCode = e[e.length - 1]
        const endCityCode = e[0]

        this.endAddressKeyList = e
        this.endAddressCode = endAddressCode
        this.endCityName = endCityName
        this.endCityCode = endCityCode
      }
      this.emitChange()
      // 不管有没有选择，都获取线路列表
      await this.$store.dispatch('line/fetchTimetables', {
        startAddressCode: this.startAddressCode,
        endAddressCode: this.endAddressCode
      })
    },

    // 交换出发地和目的地
    async onExchange() {
      // 使用解构赋值和对象字面量简化交换逻辑
      [
        this.startAddressKeyList,
        this.startAddressCode,
        this.startCityName,
        this.startCityCode,
        this.endAddressKeyList,
        this.endAddressCode,
        this.endCityName,
        this.endCityCode
      ] = [
        this.endAddressKeyList,
        this.endAddressCode,
        this.endCityName,
        this.endCityCode,
        this.startAddressKeyList,
        this.startAddressCode,
        this.startCityName,
        this.startCityCode
      ]

      this.emitChange()

      // 交换后获取线路列表
      await this.$store.dispatch('line/fetchTimetables', {
        startAddressCode: this.startAddressCode,
        endAddressCode: this.endAddressCode
      })

      // 触发交换事件
      this.$emit('exchange', {
        startAddressCode: this.startAddressCode,
        endAddressCode: this.endAddressCode,
        startCityName: this.startCityName,
        endCityName: this.endCityName,
        startCityCode: this.startCityCode,
        endCityCode: this.endCityCode,
        startAddressKeyList: this.startAddressKeyList,
        endAddressKeyList: this.endAddressKeyList
      })
    },

    // 拼接地址名称
    spliceAddressName(keys, options) {
      const option = options.find(o => o.value === keys[0])
      return option ? option.label : ''
      // 下面是备用实现，如果有需要可以启用
      // const nameArr = []
      // for (const item of keys) {
      //   let name = ''
      //   for (const option of options) {
      //     if (option.value === item) {
      //       name = option.label
      //       break
      //     }
      //     if (option.children) {
      //       const child = option.children.find((c) => c.value === item)
      //       if (child) {
      //         name = child.label
      //         break
      //       }
      //     }
      //   }
      //   nameArr.push(name)
      // }
      // return nameArr.join('-')
    },

    // 选择线路
    onSelectLine(lineClassId, time) {
      this.selectedLineClassId = lineClassId
      this.selectedTime = time

      this.emitChange()
    },

    // 选择子业态
    onSelectSubBusinessType(type) {
      this.selectedSubBusinessType = type
      this.emitChange()
      // 更新线路列表
      this.$store.dispatch('line/fetchTimetables', {
        startAddressCode: this.startAddressCode,
        endAddressCode: this.endAddressCode,
        subBusinessType: type
      })
    },

    // 线路搜索输入变化
    onLineSearchInput() {
      // 当清空搜索框时，重置搜索
      if (!this.lineSearchKeyword) {
        this.$store.dispatch('line/fetchTimetables', {
          startAddressCode: this.startAddressCode,
          endAddressCode: this.endAddressCode,
          subBusinessType: this.selectedSubBusinessType
        })
      }
    },

    // 线路搜索按回车或点击搜索按钮
    onLineSearchEnter() {
      if (this.lineSearchKeyword) {
        this.$store.dispatch('line/fetchTimetables', {
          startAddressCode: this.startAddressCode,
          endAddressCode: this.endAddressCode,
          subBusinessType: this.selectedSubBusinessType,
          lineKeyword: this.lineSearchKeyword
        })
      }
    },

    // 处理全选复选框变化
    handleCheckAllChange(val) {
      // 全选时将所有司机ID添加到checkedDrivers
      this.checkedDrivers = val ? this.drivers.map(item => item.driver_id) : []
      this.isIndeterminate = false
    },

    // 处理司机复选框组变化
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.drivers.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.drivers.length
    }
  }
}
</script>

<style lang="scss" scoped>
.line-select-container {
  display: flex;
  flex-direction: column;
}

.item-filter-warp {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;

  ::v-deep .el-tag {
    cursor: pointer;
    margin-right: 4px;
    margin-bottom: 4px;
  }
}

.col-label-key {
  flex: 0 0 75px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  line-height: 30px;

  &::after {
    content: ':';
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}
.label-word{
    margin-bottom: 10px;
    font-size: 13px;
    color: #8B96A6;
    font-weight: 400;
}
.driver-list{
    ::v-deep {
     .el-input-number--mini {
            width: 95px;
        }
         .el-checkbox-group{
        .el-checkbox{
            margin-bottom: 6px;
        }

         }
         .el-radio-group{
        .el-radio{
            margin-bottom: 6px;
        }

         }
         .el-checkbox__label{
          font-size: 12px;
         }
         .el-radio__label{
          font-size: 12px;
         }
    }
}

.cursor{
    cursor: pointer;
}

.search-city {
  display: flex;
  position: relative;
  align-items: center;

  .icon-change {
    cursor: pointer;
    width: 24px;
    height: 24px;
    min-width: 24px;
    margin: 0 12px;
    background: url('../../../assets/icon-chage.png') no-repeat;
    background-size: cover;
  }

  .from-city,
  .to-city {
    width: 50%;
  }
}

// 新样式：标签容器
.tag-selected-warp-container {
  position: relative;
  width: 100%;
}

.tag-selected-warp {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  transition: height 0.3s ease;
  overflow: hidden;
  height: 100%;

  &.tag-collapsed {
    height: 62px; /* 大约两排标签的高度 */
  }
}

// 展开/折叠按钮样式
.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px 0;
  color: #409EFF;
  font-size: 12px;
  width: 100%;
  position: relative;
  margin-top: 4px;

  i {
    margin-right: 4px;
  }

  &:hover {
    color: #66b1ff;
  }
}
</style>

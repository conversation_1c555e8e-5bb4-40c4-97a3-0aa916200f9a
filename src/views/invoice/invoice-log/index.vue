<template>
  <div class="app-container">
    <el-tabs v-model="searchQuery.status" @tab-click="handleLogFilter">
      <el-tab-pane :label="'全部(' + total_count.total_fapiaos + ')'" name="" />
      <el-tab-pane :label="'开票中(' + total_count.total_pending + ')'" name="1" />
      <el-tab-pane :label="'开票失败(' + total_count.total_failed + ')'" name="3" />
      <el-tab-pane :label="'已关闭(' + total_count.total_closed + ')'" name="8" />
      <el-tab-pane :label="'开票完成(' + total_count.total_complete + ')'" name="2" />
    </el-tabs>

    <div class="handle-container">
      <div class="filter-container">
        <div class="filter-group">
          <!-- 分台 -->
          <el-select v-model="searchQuery.branch_id" multiple size="small" placeholder="选择分台" class="filter-item">
            <el-option v-for="item in branchs" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <!-- 线路 -->
          <el-select v-model="searchQuery.line_class_id" multiple size="small" placeholder="选择线路" class="filter-item">
            <el-option v-for="item in lineClasses" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <!-- 日期类型 -->
          <el-select v-model="dateType" clearable size="small" placeholder="选择日期类型" class="filter-item">
            <el-option label="开票申请日期" value="created" />
            <el-option label="开票完成日期" value="completed" />
          </el-select>
          <!-- 日期 -->
          <el-date-picker
            v-model="date"
            size="small"
            type="daterange"
            value-format="yyyy-MM-dd"
            class="filter-item date-condition"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
          <div class="filter-buttons">
            <el-button size="small" type="primary" icon="el-icon-search" @click="handleLogFilter">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="handleReset2">重置</el-button>
            <el-button type="text" size="small" @click="collapse = collapse === 'more' ? '' : 'more'">
              {{ collapse === "more" ? "收起" : "展开" }}
              <i :class="collapse === 'more' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
            </el-button>
          </div>
        </div>
      </div>

      <div v-show="collapse === 'more'" class="filter-more">
        <div class="filter-container">
          <div class="filter-group">
            <!-- 票据标志 -->
            <el-select v-model="searchQuery.invoice_type" clearable size="small" placeholder="选择票据标志" class="filter-item">
              <el-option label="蓝票" value="1" />
              <el-option label="红票" value="2" />
            </el-select>
            <!-- 抬头类型 -->
            <el-select v-model="searchQuery.header_type" size="small" clearable placeholder="选择抬头类型" class="filter-item">
              <el-option label="企业单位" value="1" />
              <el-option label="个人/非企业" value="2" />
            </el-select>
            <!-- 开票方式 -->
            <el-select v-model="searchQuery.invoice_mode" size="small" clearable placeholder="选择开票方式" class="filter-item">
              <el-option label="自动开票" value="auto" />
              <el-option label="手动上传" value="manual" />
            </el-select>
            <!-- 关键词搜索 -->
            <el-select v-model="searchQuery.filter_key" size="small" placeholder="选择关键词类型" class="filter-item">
              <el-option label="发票流水号" value="fapiao_no" />
              <el-option label="外部平台发票编号" value="new_invoice_serial_num" />
              <el-option label="发票代码" value="invoice_code" />
              <el-option label="发票号码" value="invoice_no" />
              <el-option label="购方名称" value="user_name" />
              <el-option label="购方手机号" value="user_mobile" />
              <el-option label="购方邮箱" value="user_mail" />
              <el-option label="业务订单号" value="order_no" />
            </el-select>
            <el-input
              v-model="searchQuery.filter_keyword"
              clearable
              size="small"
              class="filter-item mini-condition"
              placeholder="请输入关键字"
            />
          </div>
        </div>
      </div>
    </div>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      class="multiline-action-table "
      stripe
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      fit
      highlight-current-row
      :height="`calc(100vh - ${leftheight})`"
      :data="list"
    >
      <el-table-column fixed="left" label="类型" prop="invoice_type" width="65" class-name="noLeftPading">
        <template slot-scope="scope">
          <el-tag size="mini" type="warning">{{ scope.row.cn_invoice_style }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column fixed="left" label="发票流水号" width="120">
        <template #default="{ row }">
          <el-button type="text" @click="preView(row)">{{ row.fapiao_no }}</el-button>
        </template>
      </el-table-column>

      <el-table-column label="开票方式" width="100">
        <template #default="{ row }">
          <!-- 使用 Element UI 的按钮组件展示开票方式 -->
          <el-button type="text">
            {{ row.invoice_mode_string }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="开票申请时间" prop="created_at" width="140">
        <template slot-scope="scope">
          <p class="table-p">
            {{ scope.row.created_at.slice(0, 11) || "/" }}
          </p>
          <p class="table-p">
            {{ scope.row.created_at.slice(11, 20) || "/" }}
          </p>
        </template>
      </el-table-column>

      <el-table-column label="乘客备注" prop="remark" width="100">
        <template slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.remark || '无备注'"
            placement="top"
          >
            <span class="table-remark">
              <template v-if="scope.row.remark">
                <!-- 如果有备注，显示部分文字 -->
                {{ scope.row.remark.length > 20 ? scope.row.remark.slice(0, 20) + '...' : scope.row.remark }}
              </template>
              <template v-else>
                <!-- 如果没有备注，显示图标 -->
                <i class="el-icon-info" style="color: #909399;" />
              </template>
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="开票总金额(元)" prop="price" width="120">
        <template slot-scope="scope">
          <!-- 美化金额展示，负数时显示为红色 -->
          <span :style="{ color: scope.row.price < 0 ? 'red' : 'black' }">
            {{ formatPrice(scope.row.price) }}
          </span></template>
      </el-table-column>

      <el-table-column label="票据标志" prop="invoice_type" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.invoice_type === 1" size="mini">蓝票</el-tag>
          <el-tag v-else-if="scope.row.invoice_type === 2" size="mini" type="danger">红票</el-tag>
          <span v-else>/</span>
        </template>
      </el-table-column>

      <el-table-column label="开票状态" prop="status" width="120">
        <template slot-scope="scope">
          <Tablestatus v-if="scope.row.status === 1" mode="processing" word="开票中" />
          <Tablestatus v-else-if="scope.row.status === 2" mode="success" word="开票完成" />
          <Tablestatus v-else-if="scope.row.status === 3" mode="error" word="开票失败" />
          <Tablestatus v-else-if="scope.row.status === 4" mode="warning" word="发票作废中" />
          <Tablestatus v-else-if="scope.row.status === 5" mode="success" word="发票已作废" />
          <Tablestatus v-else-if="scope.row.status === 6" mode="success" word="开票成功签章中" />
          <Tablestatus v-else-if="scope.row.status === 7" mode="error" word="开票成功签章失败" />
          <Tablestatus v-else-if="scope.row.status === 8" mode="error" word="已关闭" />
          <Tablestatus v-else mode="default" word="错误" />

          <!-- 如果开票状态为失败或已关闭，显示问号图标，并提示原因 -->
          <el-tooltip
            v-if="scope.row.status === 3 || scope.row.status === 5 || scope.row.status === 7 || scope.row.status === 8"
            class="item"
            effect="dark"
            :content="scope.row.reason || '无原因'"
            placement="top"
          >
            <i class="el-icon-question" style="color: #f56c6c;" />
          </el-tooltip>

        </template>
      </el-table-column>

      <el-table-column label="交付邮件" width="180">
        <template slot-scope="scope">
          <div>
            <Tablestatus v-if="scope.row.email_pushed_at" mode="success" word="推送成功" />
            <span v-else> - </span>

            <!-- 如果推送成功，显示格式化后的隐藏邮箱地址 -->
            <div style="margin-top: 5px;">
              {{ formatEmail(scope.row.user_mail) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="关联业务" size="small" prop="order_type_string" width="120" />

      <el-table-column label="购票手机号" prop="user_mobile" width="100">
        <template slot-scope="scope">
          {{ formatMobile(scope.row.user_mobile) }}
        </template>
      </el-table-column>

      <el-table-column label="发票抬头" prop="header" width="200">
        <template slot-scope="scope">
          <p style="width: 200px;line-height:24px;">{{ scope.row.header }}</p>
        </template>
      </el-table-column>

      <el-table-column label="发票代码" prop="invoice_code" width="125" />
      <el-table-column label="发票号码" prop="invoice_no" width="120" />

      <el-table-column label="开票完成时间" prop="completed_at" width="100">
        <template slot-scope="scope">
          {{ scope.row.completed_at || "-" }}
        </template>
      </el-table-column>

      <el-table-column label="订单" prop="user_mail" width="60">
        <template slot-scope="scope">
          <el-button slot="reference" type="text" size="small" @click="detail(scope.row)">查看</el-button>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="180" style="white-space: nowrap;">
        <template v-slot="scope">

          <template v-if="scope.row.status === 1 && scope.row.invoice_mode !== 'auto'">
            <el-divider direction="vertical" />
            <el-dropdown trigger="click" @command="((command) => { handleCommand(command, scope.row) })">
              <el-button class="filter-item handleCheck" type="text" size="small">
                审核<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="pass">通过</el-dropdown-item>
                <el-dropdown-item command="refuse">不通过</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>

          <template v-if="scope.row.status === 1">
            <el-popconfirm hide-icon title="确认刷新当前开发结果？" @confirm="toRefresh(scope.row)">
              <el-button slot="reference" type="text" size="small">刷新</el-button>
            </el-popconfirm>
          </template>

          <template v-if="scope.row.invoice_type === 1 && scope.row.status === 2 && scope.row.invoice_mode !== 'manual'">
            <el-popconfirm hide-icon title="确认冲红当前发票？" @confirm="toviod(scope.row)">
              <el-button slot="reference" type="text" size="small">冲红</el-button>
            </el-popconfirm>
          </template>

          <template v-if="scope.row.status === 2">
            <el-button type="text" size="small" @click="fileLinkToStreamDownload(scope.row)">下载</el-button>
          </template>

          <template v-if="scope.row.status === 2">
            <el-tooltip content="重新发送邮件" placement="top">
              <span>
                <el-popconfirm hide-icon title="确认重新发送邮件？" @confirm="tosend(scope.row)">
                  <el-button slot="reference" type="text" size="small">重邮</el-button>
                </el-popconfirm>
              </span>
            </el-tooltip>
          </template>

          <template v-if="scope.row.status === 3 && scope.row.invoice_mode !== 'manual'">
            <!-- 重试按钮 -->
            <el-popconfirm hide-icon title="确认重新开具当前发票？" @confirm="toReset(scope.row)">
              <el-button slot="reference" type="text" size="small">重试</el-button>
            </el-popconfirm>
            <!-- 检查错误码并显示认证跳转按钮 -->
            <el-button
              v-if="scope.row.show_cert_btn === true"
              type="text"
              size="small"
              @click="toCertification"
            >
              认证
            </el-button>
            <!-- 检查错误码并显示登录跳转按钮 -->
            <el-button
              v-if="scope.row.show_login_btn === true"
              type="text"
              size="small"
              @click="toLogin"
            >
              登录
            </el-button>
          </template>

          <template v-if="scope.row.status != 8 && scope.row.status != 2 ">
            <!-- 关闭按钮 -->
            <el-popconfirm hide-icon title="确认关闭开票申请？" @confirm="toClose(scope.row)">
              <el-button slot="reference" type="text" size="small">关闭</el-button>
            </el-popconfirm>
          </template>

        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div class="left-content">
        <el-button
          :loading="downloadLoading"
          class="filter-item"
          size="small"
          type="primary"
          icon="el-icon-printer"
          @click="handleDownload"
        >
          导出Excel
        </el-button>
      </div>
      <el-pagination
        small
        background
        :current-page="searchQuery.page"
        :page-size="searchQuery.per_page"
        :page-sizes="[10, 15, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalData"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog :visible.sync="dialogDes" title="发票详情" width="65%">
      <Description :description="description" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="dialogCancelVisible" title="开票失败" center width="450px">
      <el-form ref="failForm" :model="failForm" :rules="rules" label-width="80">
        <el-form-item label="原因：" prop="reason">
          <el-input v-model="failForm.reason" type="textarea" clearable :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogCancelVisible = false">取消</el-button>
          <el-button type="primary" @click="invoiceFailed">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog top="30px" :visible.sync="dialogPassVisible" :before-close="beforeClose" center title="确认发票并上传">
      <el-descriptions title="发票基本信息" direction="vertical" size="small" :column="4" border>
        <el-descriptions-item label="发票抬头">{{ description.header }}
        </el-descriptions-item>
        <el-descriptions-item label="购买方手机号">{{ description.user_mobile }}
        </el-descriptions-item>
        <el-descriptions-item label="购买方税号">{{ description.tax_id || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="销售方名称">{{ description.saler_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="销售方税号">{{ description.saler_tax_num || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总不含税金额(元)">{{ description.ex_tax_amount || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总税额(元)">{{ description.tax || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="开票总金额(元)">{{ description.price || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总价税合计(元)">{{ description.tax_amount || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin: 20px 0 20px 0">上传发票</div>
      <div style="width:50%">
        <el-upload
          class="upload-demo"
          :on-change="handleChange"
          name="file"
          :action="actions"
          :headers="headers"
          :file-list="imgFileList"
          :on-preview="handlePictureCardPreview"
        >
          <el-button size="small" icon="el-icon-plus" type="primary">点击上传</el-button>
        </el-upload>
      </div>
      <template #footer>
        <el-button @click="cancelPass">取 消</el-button>
        <el-button type="primary" @click="invoiceSuccess">确 定</el-button>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="dialogDetailVisible" title="订单详情" width="65%">
      <order-list :bill-id="billId" />
    </el-dialog>
  </div>
</template>

<script>
import listMixin from '@/mixins/list.js'
import { getToken } from '@/utils/auth'
// import pdf from 'vue-pdf'
import {
  kaipiaoList,
  sendEmail,
  cancellation,
  excel_datas,
  export_excels,
  successFapiao,
  failedFapiao,
  retry,
  refresh,
  close
} from '@/api/service'
import { dispatchBranches, dispatchBanXianLines } from '@/api/business'
import Description from './components/Description'
import OrderList from '@/components/OrderList'
// import { mapGetters } from 'vuex'
export default {
  name: 'InvoiceLog',
  components: { OrderList, Description },
  mixins: [listMixin(kaipiaoList)],
  data() {
    return {

      date: [],
      dateType: '',
      dialogDes: false,
      description: '',
      rules: {
        reason: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      downloadLoading: false,
      id: '',
      passBuyerData: [],
      passSellData: [],
      invoiceId: '',
      orderId: '',
      pendingData: [],
      failForm: {},
      OrderData: [],
      dialogPassVisible: false,
      dialogCancelVisible: false,
      dialogDetailVisible: false,
      dialogVisible: false,
      dialogImageUrl: '',
      imgFileList: [],
      billId: null,
      invoice: '',
      branchs: [],
      lineClasses: [],
      collapse: ''
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_API + '/admin/fapiao/attachments'
    },
    leftheight () {
      return this.collapse === 'more' ? '375px' : '325px'
    }
  },

  watch: {
    date(n, o) {
      if (this.dateType === 'created') {
        const [created_start_at, created_end_at] = n
        this.searchQuery = {
          ...this.searchQuery,
          created_start_at,
          created_end_at
        }
      } else {
        const [completed_start_at, completed_end_at] = n
        this.searchQuery = {
          ...this.searchQuery,
          completed_start_at,
          completed_end_at
        }
      }
    }
  },

  mounted() {
    this.getList()
  },

  async created() {
    await this.fetchBranch()
    await this.fetchBanXianLines()
  },

  methods: {

    formatEmail(email) {
      // 分割邮箱地址为用户部分和域名部分
      const [user, domain] = email.split('@')
      if (user.length <= 2) {
        // 如果用户名部分太短，直接返回邮箱地址
        return `${user}@${domain}`
      }
      // 隐藏用户部分的中间字符，只显示前两位和域名
      const hiddenUser = user.slice(0, 2) + '****'
      return `${hiddenUser}@${domain}`
    },

    formatMobile(mobile) {
      if (!mobile) {
        return '' // 如果手机号为空，返回空字符串
      }
      // 使用正则表达式将手机号的中间四位替换为星号
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    formatPrice(value) {
      if (value == null || value === '') {
        return '0.00'
      }
      return Number(value).toLocaleString('zh-CN', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    fileLinkToStreamDownload(row) {
      const bill_content = row && row.bill_content || []
      bill_content.map((o) => {
        window.open(o, '_blank')
      })
      return
    },
    handleReset2() {
      this.searchQuery = {
        page: 1,
        per_page: 15
      }
      this.date = []
      this.fetchBanXianLines()
      this.getList()
    },
    branchChange() {
      this.lineClasses = []
      this.searchQuery.line_class_id = ''
      this.fetchBanXianLines()
    },

    async toReset(params) {
      await retry(params.id)
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.getList()
    },

    async toRefresh(params) {
      await refresh(params.id)
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.getList()
    },

    async fetchBranch() {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            label: o.mchname,
            value: o.admin_id
          }
        })
      }
    },

    async fetchBanXianLines() {
      const { data } = await dispatchBanXianLines({
        branch_id: this.searchQuery.branch_id
      })
      if (data && data.length > 0) {
        this.lineClasses = data.map((o) => {
          return {
            label: `线路${o.id}：${o.start_name} → ${o.end_name}`,
            value: o.id
          }
        })
      }
    },

    async toClose(params) {
      await close(params.id)
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.getList()
    },

    toCertification() {
    // 使用 Vue Router 跳转到 /kaipiaoManage/invoiceSetting 并选择指定的标签
      this.$router.push({
        path: '/kaipiaoManage/invoiceSetting',
        query: { tab: '4' } // 通过 query 参数传递选中的标签页
      })
    },

    toLogin() {
    // 使用 Vue Router 跳转到 /kaipiaoManage/invoiceSetting 并选择指定的标签
      this.$router.push({
        path: '/kaipiaoManage/invoiceSetting',
        query: { tab: '4' } // 通过 query 参数传递选中的标签页
      })
    },

    handleCommand(command, params) {
      command === 'pass' ? this.handlePass(params) : this.handleRefuse(params)
    },
    handlePass(params) {
      this.dialogPassVisible = true
      this.description = params
    },
    handleRefuse(params) {
      this.dialogCancelVisible = true
      this.description = params
      this.$nextTick(() => {
        this.$refs['failForm'].clearValidate()
      })
    },

    cancelPass() {
      this.dialogPassVisible = false
      this.imgFileList = []
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/utils/Export2Excel').then(async (excel) => {
        const tHeader = ['发票订单编号', '票据标志', '购买人', '购买人手机号', '发票抬头', '电子邮箱', '发票代码', '发票号码', '开票总金额', '开票申请时间', '开票完成时间', '开票状态', '邮件发送时间', '开票失败原因']
        const filterVal = await excel_datas(this.searchQuery)
        const data = filterVal.data
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '发票明细数据Excel',
          autoWidth: true,
          bookType: 'xlsx'
        })
        this.downloadLoading = false
      })
    },

    async exporList() {
      const data = await export_excels(this.searchQuery)
      const blob = new Blob([data])
      const objectURL = URL.createObjectURL(blob)
      let btn = document.createElement('a')
      btn.download = 'fapiao.xls'
      btn.href = objectURL
      btn.click()
      URL.revokeObjectURL(objectURL)
      btn = null
    },
    async tosend(params) {
      const { id } = params
      await sendEmail(id)
      this.$notify({
        title: '操作成功',
        message: '重发邮件成功，请注意查收',
        type: 'success'
      })
      this.getList()
    },
    async toviod(params) {
      const { id } = params
      await cancellation(id)
      this.$notify({
        title: '操作成功',
        message: '当前发票作废成功',
        type: 'success'
      })
      this.getList()
    },
    getDay() {
      const time = new Date()
      const year = time.getFullYear()
      const month = time.getMonth() + 1
      const day = time.getDate()
      const timeStem = time.getTime()
      return `${year}/${month}/${day}/${timeStem}.jpg`
    },

    downloadFileUrl(url, fileName) {
      const elink = document.createElement('a')

      elink.href = url

      elink.setAttribute('download', fileName)

      elink.style.display = 'none'

      document.body.appendChild(elink)

      setTimeout(() => {
        elink.click()

        document.body.removeChild(elink)
      }, 66)
    },

    downloadExportFile(blob, tagFileName) {
      const downloadElement = document.createElement('a')
      let href = blob
      if (typeof blob === 'string') {
        downloadElement.target = '_blank'
      } else {
        href = window.URL.createObjectURL(blob) // 创建下载的链接
      }
      downloadElement.href = href
      downloadElement.download =
        tagFileName +
        // 下载后文件名
        document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      if (typeof blob !== 'string') {
        window.URL.revokeObjectURL(href) // 释放掉blob对象
      }
    },
    handleLogFilter() {
      if (this.searchQuery.filter_keyword && !this.searchQuery.filter_key) {
        this.$message('请选择关键词类型')
        return
      }
      this.handleFilter()
    },
    preView(params) {
      this.description = params
      this.dialogDes = true
    },

    beforeClose(done) {
      this.imgFileList = []
      done()
    },
    invoiceSuccess() {
      const bill_content = this.imgFileList.map((a) => {
        return a.response.data.url
      })
      if (bill_content.length === 0) {
        this.$notify.error('请上传正确的发票')
        return
      }
      successFapiao({ id: this.description.id, bill_content: bill_content })
        .then(() => {
          this.$notify.success('开票成功')
          this.dialogPassVisible = false
          this.imgFileList = []
          this.getList()
        })
    },
    detail(row) {
      this.dialogDetailVisible = true
      this.billId = row.id
    },
    handlePictureCardPreview(file) {
      const dialogImageUrl = file && file.response && file.response.data && file.response.data.url
      window.open(dialogImageUrl, '_blank')
    },
    async invoiceFailed() {
      const valid = await this.$refs['failForm'].validate()
      if (!valid) return
      failedFapiao({ id: this.description.id, reason: this.failForm.reason })
        .then(() => {
          this.getList()
          this.dialogCancelVisible = false
          this.$notify.success('操作成功')
        })
    },
    // 上传图片之前回调
    beforeAvatarUpload(file) {
      return file
    },
    handleChange(file, fileList) {
      this.imgFileList = fileList
    }
  }
}
</script>

<style scoped>
.tableTitle {
  width: 160px;
  padding: 8px;
}

.tableContent {
  width: 400px;
}

#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  text-align: right;
}

table {
  margin: 0 auto;
  border: 1px solid #000000;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid #000000;
  text-align: center;
}

.upload-demo /deep/ .el-upload--picture-card {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.upload-demo /deep/ .el-upload-list--picture-card .el-upload-list__item {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.passInput {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}

.option {
  margin-right: 4px;
  margin-left: 0;
}

.collapse-filrter .el-collapse {
  border: none;
}

.label-key {
  margin: 0 5px;
  font-weight: 500;
  font-size: 13px;
}

.table-remark {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

.table-remark i {
  font-size: 14px;
  vertical-align: middle;
}

/* 新增搜索栏样式 */
.handle-container {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* 只在主搜索栏应用两端对齐 */
.handle-container > .filter-container:first-child {
  justify-content: space-between;
}

.filter-group {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  align-items: center;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  flex: 1;
}

.filter-item {
  margin-right: 10px;
  min-width: 150px;
  background-color: #fff;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item.mini-condition {
  min-width: 150px;
}

.filter-item.date-condition {
  min-width: 280px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 10px;
  flex-shrink: 0;
}

.filter-more {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.filter-more .filter-container {
  justify-content: flex-start;
}

.filter-more .filter-group {
  flex: none;
  width: auto;
}

.query-select {
  width: 120px;
}

.query-select-long {
  width: 200px;
}

.daterange-condition {
  width: 360px;
}

/* 美化tab样式 */
.el-tabs {
  margin-bottom: 16px;
}

.el-tabs__item {
  height: 36px;
  line-height: 36px;
}

/* 美化按钮样式 */
.el-button--small {
  padding: 8px 15px;
}

.el-button--text {
  padding: 8px;
}

/* 美化select和input样式 */
.el-select .el-input__inner,
.el-input .el-input__inner {
  border-radius: 4px;
}

/* 美化日期选择器样式 */
.el-date-editor.el-input {
  width: 280px;
}

/* 响应式布局 */
@media screen and (max-width: 1400px) {
  .filter-item {
    min-width: 130px;
  }

  .filter-item.date-condition {
    min-width: 260px;
  }
}
</style>

<template>
  <div v-loading="loading" class="description-warp">
    <el-descriptions title="发票基本信息" direction="vertical" size="small" :column="4" border>
      <el-descriptions-item label="发票编号">{{ description.id }}
      </el-descriptions-item>
      <el-descriptions-item label="发票流水号">{{ description.fapiao_no }}
      </el-descriptions-item>
      <el-descriptions-item label="开票状态">
        <Tablestatus v-if="description.status === 1" mode="processing" word="开票中" />
        <Tablestatus v-else-if="description.status === 2" mode="success" word="开票完成" />
        <Tablestatus v-else-if="description.status === 3" mode="error" word="开票失败" />
        <Tablestatus v-else-if="description.status === 4" mode="warning" word="发票作废中" />
        <Tablestatus v-else-if="description.status === 5" mode="success" word="发票已作废" />
        <Tablestatus v-else-if="description.status === 6" mode="success" word="开票成功签章中" />
        <Tablestatus v-else-if="description.status === 7" mode="error" word="开票成功签章失败" />
        <Tablestatus v-else-if="description.status === 8" mode="error" word="已关闭" />
        <Tablestatus v-else mode="default" word="错误" />
      </el-descriptions-item>

      <el-descriptions-item label="外部发票编号">{{ description.new_invoice_serial_num || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="抬头类型">{{ description.header_type === 1 ? '企业单位' : '个人/非企业' }}
      </el-descriptions-item>

      <el-descriptions-item label="发票号码">{{ description.invoice_no || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="发票代码">{{ description.invoice_code || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="开票申请时间">{{ description.created_at }}
      </el-descriptions-item>

      <el-descriptions-item label="开票成功时间">{{ description.completed_at || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="发票抬头">{{ description.header }}
      </el-descriptions-item>

      <el-descriptions-item label="电子邮箱">{{ description.user_mail }}
      </el-descriptions-item>

      <el-descriptions-item label="购买方手机号">{{ description.user_mobile }}
      </el-descriptions-item>

      <el-descriptions-item label="购买方税号">{{ description.tax_id || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="销售方名称">{{ description.saler_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="销售方税号">{{ description.saler_tax_num || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="总不含税金额(元)">{{ description.ex_tax_amount || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="总税额(元)">{{ description.tax || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="开票总金额(元)">{{ description.price || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="总价税合计(元)">{{ description.tax_amount || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="开票类型">{{ description.invoice_type === 1 ? '蓝票' : '红票' }}
      </el-descriptions-item>

      <el-descriptions-item label="发票形式">{{ description.type | filtertype }}
      </el-descriptions-item>

      <el-descriptions-item label="交付邮件推送状态">{{ description.email_pushed_at ? '推送成功' : '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="备注/原因">{{ description.reason }}</el-descriptions-item>

      <el-descriptions-item label="交付类型">电子邮箱</el-descriptions-item>

      <el-descriptions-item label="PDF下载">
        <el-link v-for="(item,i) in description.bill_content" :key="i" style="margin-right:5px" type="primary" :disabled="description.status !== 2" :href="item" target="_blank">附件{{ i+1 }}</el-link>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="orderDetail.mch_config" title="发票明细" direction="vertical" :column="3" border>
      <el-descriptions-item label="商品名称">{{ orderDetail.mch_config.goodsName }}
      </el-descriptions-item>

      <el-descriptions-item label="商品单位">{{ orderDetail.mch_config.unit }}
      </el-descriptions-item>

      <el-descriptions-item label="单价(元)">{{ orderDetail.mch_config.price }}
      </el-descriptions-item>

      <el-descriptions-item label="商品数量">{{ orderDetail.mch_config.num }}
      </el-descriptions-item>

      <el-descriptions-item label="金额(元)">{{ orderDetail.mch_config.taxIncludedAmount }}
      </el-descriptions-item>

      <el-descriptions-item label="税额(元)">{{ orderDetail.mch_config.tax }}
      </el-descriptions-item>

      <el-descriptions-item label="税控商品编码">{{ orderDetail.mch_config.goodsCode }}
      </el-descriptions-item>

      <el-descriptions-item label="税率(%)">{{ Number(orderDetail.mch_config.taxRate) * 100 }}%
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { fapiaoDetail } from '@/api/service'

export default {
  filters: {
    filtertype (type) {
      switch (type) {
        case 1:
          return '增值税电子普通发票'
        case 2:
          return '增值税普通发票'
        case 3:
          return '专用发票(电子)'
        default:
          return '增值税专用发票'
      }
    }
  },
  props: {
    description: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      orderDetail: {},
      loading: false
    }
  },
  watch: {
    description: {
      handler (n) {
        if (n && Object.keys(n).length > 0) {
          this.fetchDetail()
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchDetail () {
      this.loading = true
      const { id } = this.description
      const { data } = await fapiaoDetail(id)
      this.loading = false

      this.orderDetail = data
    }
  }
}
</script>

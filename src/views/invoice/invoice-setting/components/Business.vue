<template>
  <div class="table-config">
    <el-button type="primary" size="small" @click="handleCreate"><i
      class="el-icon-plus"
      style="margin-right: 10px"
    />添加开票业务</el-button>
    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      style="margin-top: 20px"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      :data="list"
      fit
      highlight-current-row
      :header-cell-style="{ 'text-align': 'center' }"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column label="税控商品编码" prop="tax_goods_sn" width="240" />
      <el-table-column label="税收商品名称" prop="tax_goods_name" width="200" show-overflow-tooltip />
      <el-table-column label="开票服务名称" prop="kaipiao_name" />
      <el-table-column label="税率（%）" prop="tax_rate_percent" />
      <el-table-column label="税率优惠政策" prop="tax_policy_string" />
      <el-table-column label="商品名称" prop="goods_name" />
      <el-table-column label="关联业务" prop="business_value_type_string" />

      <el-table-column fixed="right" label="操作" width="140">
        <template v-slot="scope">
          <el-button type="text" size="small" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-popconfirm hide-icon title="确认删除当前开票业务？" @confirm="handleDelete(scope.row.id)">
            <el-button slot="reference" type="text" size="small">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <div class="bottom-container" style="padding-bottom: 10px">
      <div class="left-content" />
      <el-pagination
        small
        background
        :current-page="searchQuery.page"
        :page-size="searchQuery.per_page"
        :page-sizes="[10, 15, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalData"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      width="550px"
    >
      <el-form ref="dataForm" :model="temp" label-position="left" :rules="rules" size="small" label-width="110px">
        <el-form-item label="税收商品名称" prop="tax_goods_name">
          <el-input v-model="temp.tax_goods_name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="税收商品编码" prop="tax_goods_sn">
          <el-input v-model="temp.tax_goods_sn" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="税率" prop="tax_rate">
          <el-input v-model="temp.tax_rate" class="persentInput" placeholder="请输入" />
          <span class="label-key middle-label">若费率为6%，请填写0.06</span>
        </el-form-item>

        <el-form-item label="税率优惠政策" prop="tax_policy">
          <el-select v-model="temp.tax_policy" placeholder="请选择">
            <el-option v-for="item in tax_policies" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="商品名称" prop="goods_name">
          <el-input v-model="temp.goods_name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="商品单位" prop="unit">
          <el-input v-model="temp.unit" class="persentInput" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="开票服务名称" prop="kaipiao_name">
          <el-input v-model="temp.kaipiao_name" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="关联业务" prop="business_value_type">
          <el-select v-model="temp.business_value_type" placeholder="请选择关联业务">
            <el-option v-for="item in business_value_types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  kaipiaoCoifgList,
  addKaipiao,
  delKaipiao,
  putKaipiao
  // businessValueTypes
} from '@/api/service'
import listMixin from '@/mixins/list.js'

export default {
  mixins: [listMixin(kaipiaoCoifgList)],

  data() {
    return {
      tax_policies: [{
        value: 1,
        label: '不征税（非税）'
      }, {
        value: 2,
        label: '免税期'
      }, {
        value: 3,
        label: '普通零征税'
      }, {
        value: 4,
        label: '普通征税'
      }],
      business_value_types: [{
        value: 1,
        label: '拼车'
      }, {
        value: 2,
        label: '包车'
      }, {
        value: 3,
        label: '带货'
      }, {
        value: 4,
        label: '代办'
      }, {
        value: 5,
        label: '定制客运'
      }, {
        value: 6,
        label: '顺风车'
      }, {
        value: 7,
        label: '快车'
      }, {
        value: 8,
        label: '学生号'
      }, {
        value: 9,
        label: '家长互助'
      }, {
        value: 10,
        label: '学生号定制包车'
      }, {
        value: 11,
        label: '出租车'
      }
      ],
      rules: {
        tax_goods_sn: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        tax_goods_name: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        kaipiao_name: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        tax_rate: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        goods_name: [
          {
            required: false,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        unit: [
          {
            required: false,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        business_value_type: [
          {
            required: true,
            message: '请选择业务类型',
            trigger: 'blur'
          }
        ]
      },
      dialogStatus: '',
      dialogFormVisible: false,
      textMap: {
        update: '修改开票业务',
        create: '添加开票业务'
      },
      temp: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {

    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          addKaipiao(tempData).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$notify({
              message: '添加成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(id) {
      delKaipiao(id).then(() => {
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          putKaipiao(tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    resetTemp() {
      this.temp = {}
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-config {
    margin-top: 30px;

    .add-warp {
        display: flex;
        justify-content: flex-end;
    }
}
</style>

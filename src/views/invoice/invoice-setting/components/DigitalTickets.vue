<template>
  <div class="component-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button type="primary" size="small" style="margin-left: 0;" @click="handleCreate">绑定数电账号</el-button>
        <el-button size="small" @click="fetList">刷新</el-button>
      </div>
      <div class="filter-container" />
    </div>
    <div>
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        stripe
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        height="calc(100vh - 260px)"
        :data="list"
        fit
        highlight-current-row
        class="multiline-table multiline-action-table"
      >
        <el-table-column label="分机号" prop="extension_number" width="100" />
        <el-table-column label="企业名称" width="200">
          <template #default="{ row }">
            <!-- 如果公司名称为空，则显示 '-'，否则显示公司名称 -->
            {{ row.company_name ? row.company_name : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="数电账号" prop="ele_account" width="180" />
        <el-table-column label="姓名" prop="name" width="150" />
        <el-table-column label="密码" prop="vehicle_owner" width="150">
          <template>********</template>
        </el-table-column>
        <el-table-column label="角色" prop="role" width="160">
          <template slot-scope="scope">
            {{ scope.row.role | filterRole }}
          </template>
        </el-table-column>
        <el-table-column label="登录状态" prop="is_equipped" width="140">
          <template slot-scope="scope">
            <Tablestatus v-if="scope.row.is_loggin == 1" mode="success" word="已登录" />
            <Tablestatus v-else-if="scope.row.is_loggin == 0" mode="default" word="未登录" />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="上次登录成功时间" prop="last_login_at" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.last_login_at || "/" }}
          </template>
        </el-table-column>
        <el-table-column label="认证状态" prop="is_auth" width="140">
          <template slot-scope="scope">
            <Tablestatus v-if="scope.row.is_auth === 0" mode="warning" word="未认证" />
            <Tablestatus v-else-if="scope.row.is_auth === 2" mode="processing" word="认证中" />
            <Tablestatus v-else-if="scope.row.is_auth === 1" mode="success" word="已认证" />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="上次认证成功时间" prop="last_auth_at" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.last_auth_at || "/" }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="220">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.is_loggin === 1 && scope.row.is_auth !== 1"
              type="text"
              size="small"
              @click="loginView(scope.row)"
            >认证</el-button>
            <el-button
              v-if="scope.row.is_loggin !== 1"
              type="text"
              size="small"
              @click="loginView(scope.row)"
            >登录</el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-dropdown
              @command="(command) => handleRefrsh(command, scope.row)"
            >
              <el-button type="text" size="small">刷新状态<i class="el-icon-arrow-down el-icon--right" /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="login">刷新登录状态</el-dropdown-item>
                <el-dropdown-item command="auth">刷新认证状态</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-popconfirm hide-icon title="确认解绑当前数电账号？" @confirm="toDel(scope.row)">
              <el-button slot="reference" type="text" size="small">解绑</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div style="margin-top: 20px"> -->
    <div class="bottom-container">
      <div class="left-content">
        <!-- <el-button class="filter-item" size="small" type="primary" icon="el-icon-printer" @click="exporList">导出</el-button> -->
      </div>
      <el-pagination
        small
        background
        :current-page="searchQuery.page"
        :page-size="searchQuery.per_page"
        layout="total, prev, pager, next"
        :total="totalData"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="dialogStatus"
      :title="elecForm.id ? '编辑数电账号' : '绑定数电账号'"
      class="customDialog"
      width="450px"
      append-to-body
    >
      <el-form
        ref="ruleFormRef"
        :model="elecForm"
        size="small"
        class="no_error_form"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="数电账号" prop="ele_account">
          <el-input v-model="elecForm.ele_account" />
          <p class="txt">电子税务局的新版登录手机号</p>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="elecForm.name" />
          <p class="txt">数电账号所属人姓名</p>
        </el-form-item>
        <el-form-item label="密码" prop="ele_password">
          <el-input v-model="elecForm.ele_password" type="password" autocomplete="off" />
          <p class="txt">电子税务局的新版登录密码</p>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="elecForm.role" placeholder="请选择">
            <el-option v-for="item in roleList" :key="item.val" :label="item.name" :value="item.val" />
          </el-select>
          <p class="txt">登录电子税务局时的角色</p>
        </el-form-item>
        <el-form-item label="企业名称" prop="company_name">
          <el-input v-model="elecForm.company_name" />
          <p class="txt">企业完整名称</p>
        </el-form-item>

        <el-form-item label="分机号" prop="extension_number">
          <el-input v-model="elecForm.extension_number" :disabled="!!elecForm.id" autocomplete="off" />
        </el-form-item>
        <div class="flex-end el-dialog__footer">
          <el-button size="small" @click="dialogStatus = false">取消</el-button>
          <el-button type="primary" size="small" @click="confirmForm('verification')">保存并校验密码</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog :visible.sync="dialoglogin" title="电子税务局登录/认证" class="customDialog" width="400px" append-to-body>
      <el-descriptions size="medium" direction="direction" :column="1" colon content-class-name="contentClassName">
        <el-descriptions-item label="数电账号">{{ elecForm.ele_account | mobileFilter }} <i
          :class="iconloading ? 'el-icon-loading' : 'el-icon-refresh-right'"
          class="cma cursor ml"
          @click="refreshPhone"
        />
        </el-descriptions-item>
        <el-descriptions-item label="账号状态">
          <Tablestatus v-if="elecForm.is_loggin == 1" mode="success" word="已登录" />
          <Tablestatus v-else mode="default" word="未登录" />

          <Tablestatus v-if="elecForm.is_auth === 0" mode="warning" word="未认证" />
          <Tablestatus v-else-if="elecForm.is_auth === 2" mode="processing" word="认证中" />
          <Tablestatus v-else mode="success" word="已认证" />
        </el-descriptions-item>
        <el-descriptions-item label="登录时间">{{ elecForm.last_login_at || '-' }}</el-descriptions-item>
        <el-descriptions-item label="上次认证成功时间">{{ elecForm.last_auth_at || '-' }}</el-descriptions-item>
      </el-descriptions>

      <div class="flex-end el-dialog__footer">
        <el-button
          v-if="elecForm.is_loggin === 1"
          type="primary"
          plain
          size="small"
          @click="handleClick('qrcode')"
        >认证</el-button>
        <el-button v-else type="primary" plain size="small" @click="handleClick('login')">登录</el-button>
        <el-button
          v-if="elecForm.is_loggin === 1 && elecForm.is_auth === 1"
          type="primary"
          plain
          size="small"
          @click="handleClick('qrcode')"
        >刷新状态</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogCode" title="短信验证码" class="customDialog" width="400px" append-to-body>

      <div class="dialog-title fz15 tc">
        <i class="el-icon-message cma fz15" /> <span class="cma fz15">短信验证</span> 登录电子发票服务平台
      </div>
      <p class="descriptions tc">数电账号：<span class="cff"> {{ verification.ele_account | mobileFilter }} </span></p>
      <p v-if="sendStaus === 'success'" class="descriptions">已向<span class="cff"> {{ verification.ele_account |
        mobileFilter }}
      </span>发送验证码，有效时间5分钟</p>
      <p v-if="sendStaus === 'fail'" class="descriptions"><i class="el-icon-warning-outline cma fz15" />
        发送验证码失败，请重试</p>

      <el-form ref="codeFormRef" :model="verification" class="no_error_form" :rules="rules" label-position="right">
        <el-form-item prop="code">
          <el-input v-model="verification.code" placeholder="请输入验证码" maxlength="6" clearable />
        </el-form-item>
      </el-form>
      <div class="flex-end el-dialog__footer">
        <el-button
          :type="countdown < 120 ? 'info' : 'primary'"
          :disabled="countdown < 120"
          plain
          size="small"
          @click="sendCode"
        >{{
          textContent }}</el-button>
        <el-button
          type="primary"
          :icon="codeLoding ? 'el-icon-loading' : ''"
          size="small"
          @click="hanldeCheck('code')"
        >提交验证
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogQrCode"
      title="税务APP扫码认证"
      class="customDialog"
      width="400px"
      append-to-body
      @closed="handleQrCodeClose"
    >

      <div class="dialog-title fz15 tc">
        <i class="el-icon-full-screen cma fz15 mr" />请使用 <span class="cma fz15">税务APP</span> 扫码完成认证
      </div>
      <p class="descriptions tc">数电账号：<span class="cff"> {{ verification.ele_account | mobileFilter }} </span></p>
      <div v-if="!qrCodeUrl" class="qrcode">
        <el-result v-if="qrCodeStatus === 'error'" icon="error" title="" sub-title="二维码获取失败">
          <template slot="extra">
            <el-button type="primary" plain size="mini" icon="el-icon-refresh" @click="showQrCode">重试</el-button>
          </template>
        </el-result>
        <template v-else>
          <i class="el-icon-loading fz15 icon-loding" />
        </template>
      </div>
      <img v-else ref="qrcode" class="qrcode-img" :src="qrCodeUrl">

      <div class="used-time-less tc">
        <p v-if="timedown > 0">二维码有效时间 <span class="cma fz15">{{ timeText.minute }}</span> 分 <span class="cma fz15">{{
          timeText.second
        }}</span>
          秒
        </p>
        <p v-if="showRetry" class="descriptions"><i class="el-icon-warning-outline cma fz15" />
          二维码已过期，<el-button size="mini" type="text" @click="showQrCode">重新获取</el-button></p>

      </div>

      <p class="tc"><i class="el-icon-warning-outline fz15" />
        如果APP上未提示认证成功，请不要关闭弹窗。</p>
      <p class="tc">如果扫码失败请刷新二维码重试 </p>
      <div class="flex-end el-dialog__footer">
        <el-button type="primary" plain size="small" @click="dialogQrCode = false">关闭</el-button>
        <el-button
          type="primary"
          :icon="codeLoding ? 'el-icon-loading' : ''"
          size="small"
          @click="handleCheckQrcode"
        >我已认证
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getElectronicList, getElectPhoneCode, checkPhoneCode, sendQrCode, delAccount, addElectronic, editElectronic, checkQrCode, refreshLoginStatus,
  refreshAuthStatus
} from '@/api'
const roleList = [
  { val: 1, name: '开票员' },
  { val: 2, name: '办税员' },
  { val: 3, name: '管理员' },
  { val: 4, name: '财务负责人' },
  { val: 5, name: '法定代表人' }
]
export default {
  name: 'ElectronicList',
  filters: {
    filterRole(role) {
      return roleList.find(o => o.val === role).name || '/'
    },
    mobileFilter(tel) {
      if (!tel) return tel
      return tel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
  },
  data() {
    return {
      timeText: {},
      showRetry: false,
      timedown: 0,
      qrCodeStatus: '',
      dialogQrCode: false,
      qrCodeUrl: null,
      codeLoding: false,
      sendStaus: 'fail',
      countdown: 120, // 2分钟，共120秒
      searchQuery: {
        page: 1,
        per_page: 15
      },
      totalData: 0,
      loading: false,
      dialogFormVisible: false,
      dialogStatus: false,
      dialoglogin: false,
      iconloading: false,
      dialogCode: false,
      roleList: roleList,
      list: null,
      verification: {},
      elecForm: {},
      rules: {
        code: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ],
        ele_account: [
          { required: true, message: '请输入数电账号', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        ele_password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        extension_number: [
          { required: true, message: '请输入分机号', trigger: 'blur' }
        ],
        role: [{ required: true, message: '请选择角色', trigger: 'change' }]
      },
      countdownInterval: null,
      diffInterval: null,
      textContent: '发送验证码'
    }
  },
  watch: {},
  beforeCreate() { },
  mounted() {
    this.fetList()
  },
  methods: {
    async handleRefrsh(type, row) {
      type === 'login' ? await refreshLoginStatus({
        id: row.id
      })
        : refreshAuthStatus({ id: row.id })
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.fetList()
    },
    handleClick(type) {
      this.verification = {
        ele_account: this.elecForm.ele_account,
        id: this.elecForm.id
      }
      this.dialoglogin = false
      if (type === 'login') {
        this.sendCode()
        this.dialogCode = true
        this.$nextTick(() => {
          this.$refs['codeFormRef'].clearValidate()
        })
      } else if (type === 'qrcode') {
        this.showQrCode()
      }
    },
    async handleCheckQrcode() {
      const res = await checkQrCode({ id: this.verification.id })
      if (res && res.code === 200) {
        this.dialogQrCode = false
        this.$notify({
          title: '操作成功',
          type: 'success'
        })
        this.fetList()
      }
    },

    handleQrCodeClose() {
      this.timeText = {}
      this.showRetry = false
      this.timedown = 0
      this.qrCodeUrl = null
      clearInterval(this.diffInterval)
      this.diffInterval = null
    },
    showQrCode() {
      this.timeText = {}
      this.showRetry = false
      this.timedown = 0
      this.qrCodeStatus = 'loding'
      this.qrCodeUrl = null
      this.dialogQrCode = true
      sendQrCode({ id: this.verification.id }).then((res) => {
        if (res && res.code === 200) {
          this.qrCodeUrl = res.data && res.data.data && res.data.data.result && res.data.data.result.qr_code_url
          const diff = res.data && res.data.data && res.data.data.result && res.data.data.result.endTime ? new Date(res.data.data.result.endTime) - new Date() : 0

          if (diff > 0) {
            this.timedown = Math.floor(diff / 1000)
            this.timeUpdatedown()
            this.diffInterval = setInterval(() => {
              this.timeUpdatedown()
            }, 1000)
          }

          this.qrCodeStatus = 'success'
        } else {
          this.qrCodeStatus = 'error'
        }
      }).catch(() => {
        console.log('请求失败')
        this.qrCodeStatus = 'error'
      })
    },

    timeUpdatedown() {
      if (this.timedown > 0) {
        this.timedown--
        this.timeText = {
          minute: Math.floor(this.timedown / 60) < 10 ? '0' + Math.floor(this.timedown / 60) : Math.floor(this.timedown / 60),
          second: this.timedown % 60 < 10 ? '0' + this.timedown % 60 : this.timedown % 60
        }
      } else {
        clearInterval(this.diffInterval)
        this.diffInterval = null
        this.showRetry = true
        this.timedown = 0
      }
    },

    sendCode() {
      getElectPhoneCode({ id: this.verification.id }).then((res) => {
        if (res) {
          this.sendStaus = 'success'
          this.startCountdown()
        } else {
          this.sendStaus = 'fail'
        }
      })
    },

    startCountdown() {
      this.countdownInterval = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
          this.textContent = `发送验证码(${Math.floor(this.countdown / 60)}分钟${this.countdown % 60}秒)`
        } else {
          clearInterval(this.countdownInterval)
          this.countdownInterval = null
          this.textContent = '重新发送'
          this.countdown = 120
        }
      }, 1000)
    },
    async hanldeCheck() {
      await this.$refs['codeFormRef'].validate()
      this.codeLoding = true
      checkPhoneCode({ id: this.verification.id, code: this.verification.code }).then((res) => {
        setTimeout(() => {
          if (res) {
            this.codeLoding = false
            this.dialogCode = false
            this.fetList()
          } else {
            this.codeLoding = false
          }
          this.codeLoding = false
          this.dialogCode = false
          this.fetList()
        }, 1000)
      })
    },
    refreshPhone() {
      this.iconloading = true
      setTimeout(() => {
        this.iconloading = false
      }, 500)
    },
    loginView(row) {
      this.elecForm = row
      this.dialoglogin = true
    },
    handleEdit(row) {
      this.elecForm = row
      this.dialogStatus = true
      this.$nextTick(() => {
        this.$refs['ruleFormRef'].clearValidate()
      })
    },
    handleCreate() {
      this.elecForm = {}
      this.dialogStatus = true
      this.$nextTick(() => {
        this.$refs['ruleFormRef'].clearValidate()
      })
    },

    async toDel(params) {
      await delAccount({ id: params.id })
      this.$notify({
        title: '操作成功',
        type: 'success'
      })
      this.fetList()
    },
    async fetList() {
      this.loading = true
      const { data } = await getElectronicList(this.searchQuery)
      setTimeout(() => {
        this.loading = false
      }, 500)
      this.list = data && data.data
      this.totalData = data && data.meta && data.meta.pagination && data.meta.pagination.total
    },
    handleCurrentChange(val) {
      this.searchQuery.page = val
      this.fetList()
    },
    preView(params) {
      this.description = params
      this.dialogDes = true
    },
    confirmForm() {
      this.$refs['ruleFormRef'].validate(async (valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.elecForm)
          tempData.id ? await editElectronic(tempData) : await addElectronic(tempData)
          this.$notify({
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
          this.dialogStatus = false
          this.fetList()
        }
      })
    }
  }
}
</script>

<style scoped>
.txt {
  font-size: 12px;
  color: #999;
  line-height: 24px;
}

.used-time-less {
  margin-bottom: 20px;
}

.icon-loding {
  font-size: 24px;
}

.qrcode-img {
  margin: 0 auto;
  display: block;
  width: 250px;
  height: 250px;
  object-fit: contain;
}

.qrcode {
  min-width: 140px;
  min-height: 140px;
  background-color: #f5f6f7;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 20px auto;

}

.descriptions {
  margin-top: 20px;
  line-height: 34px;
}

.tableTitle {
  width: 160px;
  padding: 8px;
}

.tableContent {
  width: 400px;
}

#container {
  padding: 20px;
  width: 100%;
}

.el-pagination {
  text-align: right;
}

table {
  margin: 0 auto;
  border: 1px solid #000000;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid #000000;
  text-align: center;
}

.upload-demo /deep/ .el-upload--picture-card {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.upload-demo /deep/ .el-upload-list--picture-card .el-upload-list__item {
  height: 80px;
  width: 80px;
  line-height: 80px;
}

.passInput {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}

.option {
  margin-right: 4px;
  margin-left: 0;
}

.label-key {
  margin-left: 3px;
}
</style>

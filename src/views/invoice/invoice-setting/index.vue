<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" v-loading="loading">
      <el-tab-pane label="基础配置" name="1">
        <el-form label-width="130px" style="margin-top: 30px">
          <el-form-item label="发票服务开关" prop="fapiao_service_enabled">
            <el-switch v-model="general_settings.fapiao_service_enabled" active-color="#0067E1" />
            <span class="label-key  middle-label">关闭后，则乘客无法开取发票</span>
          </el-form-item>
          <el-form-item class="switchStyle" label="开票方式" prop="invoice_mode">
            <el-switch
              v-model="general_settings.invoice_mode"
              active-text="自动"
              inactive-value="manual"
              active-value="auto"
              inactive-text="手动"
              active-color="#0067E1"
            />
            <span
              class="label-key middle-label"
            >切换为“手动开票”模式，需要审核并上传发票附件。切换为“自动开票”模式，申请开票记录全自动开票。（说明：“自动开票”权限需单独购买与对接后才能生效，未购买切换为自动开票会开票失败，请联系客户经理开通。）</span>
          </el-form-item>
          <el-form-item label="发票形式" prop="fapiao_style">
            <el-radio-group v-model="general_settings.fapiao_style">
              <el-radio label="e-invoice">普通发票（电子）</el-radio>
              <el-radio label="e-shudian">数电普票（电子）</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 根据不同的选择动态展示相关说明 -->
          <el-form-item>
            <template v-if="general_settings.fapiao_style === 'e-invoice'">
              <el-alert
                title="选择“普通发票（电子）”形式必须要结合使用开票公司的金税盘或税控盘或UKey（托管或本地模式），才能开票。"
                type="info"
                show-icon
              />
            </template>

            <template v-if="general_settings.fapiao_style === 'e-shudian'">
              <el-alert
                title="选择“数电普票（电子）”形式必须要结合使用开票企业税局电子税务平台的开票员账号，在数电票模式下按税局要求需要开票企业每天在本后台数电票账号管理模块进行24h认证和24h登陆一次。"
                type="info"
                show-icon
              />
            </template>
          </el-form-item>

          <el-form-item label="开票有效期" prop="effective_days">
            <el-radio-group v-model="general_settings.effective_days">
              <el-radio :label="2">2天</el-radio>
              <el-radio :label="30">30天</el-radio>
              <el-radio :label="90">90天</el-radio>
              <el-radio :label="365">1年</el-radio>
            </el-radio-group>
            <span class="label-key  middle-label">订单交易完成后N天可以申请开票（过了有效期不允许乘客主动申请）</span>
          </el-form-item>
          <el-form-item label="开票须知" prop="address">
            <tinymce v-model="intro_settings.remark" :toolbar="toolbar" :width="700" :height="250" />
          </el-form-item>

        </el-form>
      </el-tab-pane>
      <el-tab-pane label="开票企业信息" name="2">
        <el-form
          ref="reqiredForm"
          label-width="130px"
          size="medium"
          :model="enterprise_settings"
          :rules="rules"
          style="margin-top: 40px"
        >
          <el-form-item label="企业名称" prop="name">
            <el-input v-model="enterprise_settings.name" class="addInputLength" placeholder="请输入企业名称" />
            <span class="label-key   middle-label">企业名称填写车企实际开票的公司名称</span>
          </el-form-item>
          <el-form-item label="开户行" prop="bank_name">
            <el-input v-model="enterprise_settings.bank_name" class="addInputLength" placeholder="请输入开户行" />
          </el-form-item>
          <el-form-item label="银行账号" prop="account">
            <el-input v-model="enterprise_settings.account" class="addInputLength" placeholder="请输入银行账号" />
          </el-form-item>
          <el-form-item label="纳税人识别号" prop="tax_no">
            <el-input v-model="enterprise_settings.tax_no" class="addInputLength" placeholder="请输入纳税人识别号" />
            <span class="label-key   middle-label">确认后不支持再修改</span>
          </el-form-item>
          <el-form-item label="开票人" prop="clerk">
            <el-input v-model="enterprise_settings.clerk" class="addInputLength" placeholder="请输入开票人" />
          </el-form-item>
          <el-form-item label="收款人" prop="payee">
            <el-input v-model="enterprise_settings.payee" class="addInputLength" placeholder="请输入收款人" />
          </el-form-item>
          <el-form-item label="复核人" prop="checker">
            <el-input v-model="enterprise_settings.checker" class="addInputLength" placeholder="请输入复核人" />
          </el-form-item>
          <el-form-item label="财政负责人电话" prop="tel">
            <el-input v-model="enterprise_settings.tel" class="addInputLength" placeholder="请输入财政负责人电话" />
          </el-form-item>
          <el-form-item label="统一社会信用码" prop="tax_no">
            <el-input v-model="enterprise_settings.tax_no" class="addInputLength" placeholder="请输入统一社会信用码" />
          </el-form-item>
          <el-form-item label="税盘号" prop="tax_tray_code">
            <el-input v-model="enterprise_settings.tax_tray_code" class="addInputLength" placeholder="请输入税盘号" />
            <span class="label-key   middle-label">航信主盘号，百旺传12位盘号（前面33-或44-不用输入），Ukey传盘背后的12位盘号</span>
          </el-form-item>
          <el-form-item label="企业地址" prop="address">
            <el-input
              v-model="enterprise_settings.address"
              class="addInputLength"
              type="textarea"
              placeholder="请输入企业地址"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="开票业务配置" name="3">
        <Business />
      </el-tab-pane>
      <el-tab-pane label="数电票账号管理" name="4">
        <DigitalTickets />
      </el-tab-pane>
      <!-- <el-tab-pane label="使用说明" name="4">
        <el-form style="margin-top: 30px">
          <el-form-item prop="address">
            <tinymce v-model="intro_settings.remark" :toolbar="toolbar" :width="700" :height="250" />
          </el-form-item>
        </el-form>
      </el-tab-pane> -->
    </el-tabs>
    <div v-if="!['3', '4'].includes(activeName)" class="button-bottom">
      <el-button type="primary" size="small" @click="handleSave">保存配置</el-button>
    </div>
  </div>
</template>

<script>
import { fullConfig, postConfig } from '@/api/service'
import Tinymce from '@/components/Tinymce'
import Business from './components/Business'
import DigitalTickets from './components/DigitalTickets'

export default {
  name: 'InvoiceSetting',
  components: { Tinymce, Business, DigitalTickets },

  data() {
    return {
      loading: false,
      rules: {
        name: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        bank_name: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ],
        account: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        tax_no: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        clerk: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        payee: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        checker: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        tel: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        tax_tray_code: [
          { required: true, message: '该项为必填项', trigger: 'blur' }
        ],
        address: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      toolbar: [
        'bold italic alignleft aligncenter alignright link charmap preview anchor pagebreak   forecolor  fullscreen'
      ],

      activeName: '1',
      general_settings: {},
      enterprise_settings: {},
      intro_settings: {}
    }
  },
  watch: {
    // 如果路由参数改变，更新 active tab
    '$route.query.tab'(newTab) {
      if (newTab) {
        this.activeName = newTab
      }
    }
  },

  created() { },
  async mounted() {
    // 在组件挂载时，检查路由的查询参数，并设置 active tab
    const tab = this.$route.query.tab
    if (tab) {
      this.activeName = tab // 设置到传递的 tab
    }
    this.fetchConfig()
  },
  methods: {
    handleSave() {
      this.$refs['reqiredForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign(
            {},
            {
              general_settings: this.general_settings,
              enterprise_settings: this.enterprise_settings,
              intro_settings: this.intro_settings
            }
          )
          postConfig(tempData).then(() => {
            this.fetchConfig()
            this.$notify({
              message: '保存成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    async fetchConfig() {
      this.loading = true
      // eslint-disable-next-line no-unused-vars
      const {
        data: { value }
      } = await fullConfig('fapiao')
      this.loading = false
      const { general_settings, enterprise_settings, intro_settings } = value
      this.general_settings = general_settings
      this.enterprise_settings = enterprise_settings
      this.intro_settings = intro_settings
    }
  }
}
</script>
<style lang="scss" scoped>
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
}

.middle-label {
  vertical-align: middle;
  margin-left: 15px;
}
</style>

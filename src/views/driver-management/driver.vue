<template>
  <div class="app-container">
    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.driver_id"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">司机编号</template>
        </el-input>

        <el-input
          v-model="listQuery.job_number"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">司机工号</template>
        </el-input>

        <el-input
          v-model="listQuery.name"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">司机姓名</template>
        </el-input>

        <el-input
          v-model="listQuery.cellphone"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">手机号</template>
        </el-input>

        <el-input
          v-model="listQuery.car_brand"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">汽车品牌</template>
        </el-input>

        <el-input
          v-model="listQuery.total_seating"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">座位数</template>
        </el-input>

        <el-button
          class="filter-item"
          type="primary"
          size="small"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          icon="el-icon-refresh"
          @click="handleReset"
        >
          重置
        </el-button>
        <el-button
          class="filter-item"
          type="text"
          size="small"
          @click="showMore = !showMore"
        >
          更多<i :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加司机
        </el-button>
      </div>
    </div>
    <div v-show="showMore" class="handle-container">
      <div class="filter-container">
        <el-select
          v-model="listQuery.driver_type"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">司机类型</template>
          <el-option label="全部" :value="null" />
          <el-option label="顺风车" :value="1" />
          <el-option label="全职司机" :value="0" />
        </el-select>

        <el-select
          v-model="listQuery.driver_role_type"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">司机角色</template>
          <el-option label="全部" :value="null" />
          <el-option label="普通司机" :value="0" />
          <el-option label="顺风车司机" :value="1" />
        </el-select>

        <el-select
          v-model="listQuery.status"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">司机状态</template>
          <el-option v-for="item in driverStatus" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>

        <branch-select
          v-model="listQuery.branchid"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">所属分台</template>
        </branch-select>

        <el-select
          v-model="listQuery.business_type"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
          @change="handleSearchBusinessTypeChange"
        >
          <template slot="prepend">业态</template>
          <el-option label="全部" :value="null" />
          <el-option v-for="item in businessTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-select
          v-if="Number(listQuery.business_type) === 5"
          v-model="listQuery.sub_business_type"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">子业态</template>
          <el-option label="全部" :value="null" />
          <el-option v-for="item in subBusinessTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-select
          v-model="listQuery.group_id"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="未冻结司机" name="all" />
      <el-tab-pane label="已冻结" name="frozen" />
    </el-tabs>

    <el-table
      ref="userTable" v-loading="listLoading" :data="list || []" element-loading-text=""
      fit highlight-current-row
      :max-height="tableHeight" size="mini" style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" align="center" />

      <el-table-column label="司机编号" prop="driver_id" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="司机工号" prop="job_number" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="排序" prop="sort" width="80" align="center" />
      <el-table-column label="昵称" prop="name" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="driver-name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" prop="car_tail_number" width="100" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button type="text" @click="handleCarNumberClick(scope.row.car_tail_number)">
            {{ scope.row.car_tail_number }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="接单状态" prop="state">
        <template slot-scope="scope">
          <el-tag
            :type="{
              0: 'success',
              1: 'warning',
              2: 'info',
              3: 'danger'
            }[scope.row.state]" size="small"
          >
            {{
              {
                0: '接单中',
                1: '在路上',
                2: '已送达',
                3: '隐身中'
              }[scope.row.state]
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="账号状态" prop="status">
        <template slot-scope="scope">
          <div
            class="status-tag" :class="{
              'success': scope.row.status === 0,
              'warning': scope.row.status === 1,
              'danger': scope.row.status === 2,
              'info': scope.row.status === 3
            }"
          >
            <i v-if="scope.row.status === 0" class="el-icon-circle-check" />
            <i v-if="scope.row.status === 1" class="el-icon-loading" />
            <i v-if="scope.row.status === 2" class="el-icon-circle-close" />
            <i v-if="scope.row.status === 3" class="el-icon-edit-outline" />
            <span>{{
              {
                0: '正常',
                1: '审核中',
                2: '审核未通过',
                3: '资料提交中'
              }[scope.row.status]
            }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="冻结状态" prop="is_freeze">
        <template slot-scope="scope">
          <div class="freeze-status">
            <el-tag :type="scope.row.is_freeze === 0 ? 'primary' : 'warning'" size="small">
              <i :class="scope.row.is_freeze === 0 ? 'el-icon-unlock' : 'el-icon-lock'" />
              <span>{{ scope.row.is_freeze === 0 ? '未冻结' : '已冻结' }}</span>
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="司机类型" prop="driver_role_type" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.driver_role_type === 0 ? 'info' : 'success'" size="small">
            {{ scope.row.driver_role_type === 0 ? '普通司机' : '顺风车司机' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="cellphone" width="140" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.cellphone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="座位数" prop="total_seating">
        <template slot-scope="scope">
          <div>
            <i class="el-icon-user" />
            <span>{{ scope.row.total_seating }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="余额" prop="balance">
        <template slot-scope="scope">
          <span>{{ scope.row.balance ? '¥' + Number(scope.row.balance).toFixed(2) : '¥0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证号码" prop="id_number" width="160" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <span>{{ scope.row.id_number }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="驾驶证号码" prop="driver_license" width="140" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <span>{{ scope.row.driver_license }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属分台" prop="branch.mchname" width="160" show-overflow-tooltip />
      <el-table-column label="司机归属分组" prop="driver_group_attribute_value.name" width="140" />
      <el-table-column label="车品牌" prop="car_brand" width="120" />
      <el-table-column label="公众号二维码" width="150" align="center">
        <template slot-scope="scope">
          <div class="qrcode-container">
            <el-image
              v-if="getQrCodeByType(scope.row.driver_qr_attachment, 1)"
              :src="getQrCodeByType(scope.row.driver_qr_attachment, 1)" fit="contain" class="qrcode-thumbnail"
              @click="showQrcodeDialog(scope.row, 1)"
            >
              <div slot="error" class="image-slot">
                <el-button type="text" @click="generateQrcode(scope.row, 1)">生成二维码</el-button>
              </div>
            </el-image>
            <el-button v-else type="text" @click="generateQrcode(scope.row, 1)">生成二维码</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="小程序二维码" width="150" align="center">
        <template slot-scope="scope">
          <div class="qrcode-container">
            <el-image
              v-if="getQrCodeByType(scope.row.driver_qr_attachment, 2)"
              :src="getQrCodeByType(scope.row.driver_qr_attachment, 2)" fit="contain" class="qrcode-thumbnail"
              @click="showQrcodeDialog(scope.row, 2)"
            >
              <div slot="error" class="image-slot">
                <el-button type="text" @click="generateQrcode(scope.row, 2)">生成二维码</el-button>
              </div>
            </el-image>
            <el-button v-else type="text" @click="generateQrcode(scope.row, 2)">生成二维码</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="司机最后更新位置" prop="map_address" width="150">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.map_address" placement="top">
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">
              {{ scope.row.map_address }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="操作" width="160" fixed="right">
        <template slot-scope="scope">
          <el-button class="filter-item" type="text" size="medium" @click="openUpdate(scope.row)">
            修改
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            class="filter-item" type="text" size="medium"
            @click="deleteItem(scope.row.driver_id, scope.row.is_freeze)"
          >
            {{ scope.row.is_freeze === 0 ? '删除' : '恢复' }}
          </el-button>
          <el-divider direction="vertical" />
          <el-dropdown trigger="click">
            <el-button type="text" size="medium">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native="updateDriverRoleType(scope.row.driver_id, scope.row.driver_role_type === 0 ? 1 : 0)"
              >
                {{ scope.row.driver_role_type === 0 ? '转为顺风车司机' : '转为默认司机' }}
              </el-dropdown-item>
              <el-dropdown-item
                v-if="scope.row.state === 3"
                @click.native="updateDriverState(scope.row.driver_id, 'pending')"
              >
                开启接单
              </el-dropdown-item>
              <el-dropdown-item
                v-if="scope.row.state !== 3"
                @click.native="updateDriverState(scope.row.driver_id, 'closed')"
              >
                暂停接单
              </el-dropdown-item>
              <el-dropdown-item @click.native="openDriverLine(scope.row.driver_id)">
                司机线路
              </el-dropdown-item>
              <el-dropdown-item @click.native="updateDriverPassword(scope.row.driver_id)">
                修改密码
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div class="left-content">
        <el-button
          v-if="function_permission_definition && function_permission_definition.provincialReportConfig"
          class="filter-item"
          size="small"
          type="primary"
          icon="el-icon-upload2"
          :disabled="selectedDrivers.length === 0"
          @click="handleReportToMonitoring"
        >上报省级定制客运司机监控服务平台</el-button>
        <span v-if="selectedDrivers.length > 0" class="selected-info">已选择 <span class="selected-count">{{ selectedDrivers.length }}</span> 名司机</span>
      </div>
      <el-pagination
        v-if="total > 0" layout="sizes, total, prev, pager, next" background :page-size="listQuery.size"
        :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        @current-change="handleCurrentChanges" @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增司机' : '修改司机'"
      :visible.sync="dialogVisible"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" size="medium" inline>
        <el-form-item label="车牌号码" prop="car_tail_number">
          <el-input v-model="temp.car_tail_number" placeholder="如：川A12345" style="width: 260px" @change="checkVehicleInfo(temp.car_tail_number)" />
          <span class="tips">（支持输入车牌号码后自动绑定已有车辆）</span>
          <span class="tips_button" @click="toAddCar">添加车辆</span>
        </el-form-item>
        <el-form-item label="车品牌" prop="car_brand">
          <el-input v-model="temp.car_brand" placeholder="请输入车品牌" style="width: 260px" />
        </el-form-item>
        <el-form-item label="手机号" prop="cellphone">
          <el-input v-model="temp.cellphone" placeholder="请输入手机号" style="width: 260px" />
        </el-form-item>
        <el-form-item label="昵称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入昵称" style="width: 260px" />
        </el-form-item>
        <el-form-item label="司机工号" prop="job_number">
          <el-input v-model="temp.job_number" placeholder="请输入司机工号" style="width: 260px" />
        </el-form-item>
        <el-form-item label="身份证号码" prop="id_number">
          <el-input v-model="temp.id_number" placeholder="请输入身份证号码" style="width: 260px" />
        </el-form-item>
        <el-form-item label="驾驶证号码" prop="driver_license">
          <el-input v-model="temp.driver_license" placeholder="请输入驾驶证号码" style="width: 260px" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input v-model="temp.password" placeholder="默认密码为手机号码后6位" style="width: 260px" show-password type="password" />
        </el-form-item>
        <el-form-item label="总座位数" prop="total_seating">
          <el-input v-model="temp.total_seating" placeholder="请输入总座位数" style="width: 260px">
            <template #append>包含司机</template>
          </el-input>
        </el-form-item>
        <el-form-item label="上户时间" prop="car_register_time">
          <el-date-picker v-model="temp.car_register_time" type="date" value-format="yyyy-MM-dd" placeholder="请选择上户时间" style="width: 260px" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="temp.sort" placeholder="请输入排序" style="width: 260px" />
        </el-form-item>
        <el-form-item label="所属分台" prop="branchid">
          <branch-select v-model="temp.branchid" placeholder="请选择分台" style="width: 260px" />
        </el-form-item>
        <el-form-item label="司机归属分组" prop="driver_group_attribute_id">
          <el-select v-model="temp.driver_group_attribute_id" placeholder="请选择分组" style="width: 260px">
            <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="司机类型" prop="driver_type">
          <el-radio-group v-model="temp.driver_type">
            <el-radio v-for="item in driverTypes" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="业态" prop="business_type">
          <el-select v-model="temp.business_type" placeholder="请选择业态" style="width: 260px" @change="handleBusinessTypeChange">
            <el-option v-for="item in businessTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="Number(temp.business_type) === 5" label="子业态" prop="sub_business_type">
          <el-select v-model="temp.sub_business_type" placeholder="请选择子业态" style="width: 260px">
            <el-option v-for="item in subBusinessTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="tips">注：</div>
      <div class="tips-content">兼职司机(即司机自带车辆)：司机完成的每一笔单的费用都会与商户进行分成，分成比例即商户后台设定的分成比例；</div>
      <div class="tips-content">全职司机(即商户提供车辆给司机，司机只领工资)：订单的费用全部结算到商户的余额中；</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? handleAdd() : handleUpdate()">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog title="更换分组" :visible.sync="changeGroupDialog" width="500px">
      <el-form ref="dataForm" :model="temp" :rules="rules" label-position="left" label-width="120px" size="medium">
        <el-form-item label="分组名称" prop="name">
          <el-select v-model="temp.name" placeholder="请选择分组">
            <el-option v-for="item in driverGroups" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-dialog>
    <LineInfo ref="lineInfo" />
    <el-dialog title="修改密码" :visible.sync="passwordDialog" width="480px">
      <el-form
        ref="dataForm" :model="passwordForm" :rules="rules" label-position="left"
        label-width="120px"
        size="medium"
      >
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password" placeholder="请输入新密码" style="width: 260px" show-password
            type="password"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password" placeholder="请确认新密码" style="width: 260px" show-password
            type="password"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialog = false"> 取消 </el-button>
        <el-button type="primary" @click="handleUpdatePassword"> 确认 </el-button>
      </div>
    </el-dialog>

    <!-- 二维码放大对话框 -->
    <el-dialog
      :title="qrcodeDialogType === 1 ? '公众号二维码' : '小程序二维码'" :visible.sync="qrcodeDialogVisible"
      width="400px" center
    >
      <div class="qrcode-dialog-content">
        <el-image v-if="currentQrcode" :src="currentQrcode" fit="contain" class="qrcode-large" />
        <div v-if="currentChannelLink" class="qrcode-link-container">
          <el-input v-model="currentChannelLink" readonly class="channel-link-input">
            <el-button slot="append" icon="el-icon-copy-document" @click="copyChannelLink(currentChannelLink)" />
          </el-input>
        </div>
        <div class="qrcode-dialog-footer">
          <el-button type="primary" size="small" @click="downloadQrcode">下载二维码</el-button>
          <el-button type="success" size="small" @click="regenerateQrcode">重新生成</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDriverGroups, getDrivers, updateDriverGroup, freezeDriver, addDriver, updateDriver, updateDriverState, updateDriverRoleType, getDriverDetail, updateDriverPassword, reportDriverToMonitoring, generateDriverQrcode } from '@/api/drvier'
import { getVehicleInfo } from '@/api/order'
import { dispatchBranches } from '@/api/business'
import LineInfo from './lineInfo.vue'
import { getProfiles } from '@/api/user'
import { mapGetters } from 'vuex'
import BranchSelect from '@/components/BranchSelect'

export default {
  name: 'GroupManagement',
  components: {
    LineInfo,
    BranchSelect
  },
  data() {
    const validateSort = (rule, value, callback) => {
      if (!/^\d+$/.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    return {
      tableHeight: window.innerHeight - 270,
      passwordForm: {
        new_password: '',
        confirm_password: ''
      },
      selectedDrivers: [],
      currentDriverId: null,
      passwordDialog: false,
      showMore: false,
      driverTypes: [{ id: 0, name: '兼职司机' }, { id: 1, name: '全职司机' }],
      changeGroupDialog: false,
      driverGroups: [],
      branchs: [],
      temp: {
        car_tail_number: '',
        car_brand: '',
        cellphone: '',
        password: '',
        total_seating: '',
        car_register_time: '',
        name: '',
        sort: '',
        branchid: '',
        driver_group_attribute_id: '',
        driver_type: null,
        id_number: '',
        driver_license: '',
        job_number: '',
        business_type: 5,
        sub_business_type: null
      },
      rules: {
        car_tail_number: [
          {
            required: true,
            message: '请输入车牌号码',
            trigger: 'blur'
          }
        ],
        car_brand: [
          {
            required: true,
            message: '请输入车品牌',
            trigger: 'blur'
          }
        ],
        cellphone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ],
        total_seating: [
          {
            required: true,
            message: '请输入总座位数',
            trigger: 'blur'
          }
        ],
        car_register_time: [
          {
            required: true,
            message: '请选择上户时间',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' },
          { validator: validateSort, trigger: 'blur' }
        ],
        branchid: [
          { required: true, message: '请选择分台', trigger: 'blur' }
        ]
      },
      typeOptions: [
        { value: 1, label: '拼车' },
        { value: 2, label: '包车' },
        { value: 3, label: '带货' },
        { value: 4, label: '分组' },
        { value: 5, label: '定制客运', enable: true },
        { value: 6, label: '顺风车' },
        { value: 7, label: '快车' },
        { value: 11, label: '出租车' },
        { value: 20, label: '摆渡车' }
      ],
      driverStatus: [
        { id: 0, name: '正常' },
        { id: 1, name: '审核中' },
        { id: 2, name: '审核未通过' },
        { id: 3, name: '资料提交中' }
      ],
      businessTypes: [],
      subBusinessTypes: [
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ],
      total: '',
      dialogStatus: 'update',
      dialogVisible: false,
      list: [],
      groupOptions: [],
      listLoading: false,
      listQuery: {
        page: 1,
        size: 15,
        keyword: undefined,
        type: undefined,
        valid_category: undefined,
        status: undefined,
        driver_id: undefined,
        name: undefined,
        cellphone: undefined,
        car_brand: undefined,
        branchid: undefined,
        total_seating: undefined,
        driver_type: undefined,
        driver_role_type: undefined,
        job_number: undefined,
        business_type: undefined,
        sub_business_type: undefined
      },
      // 二维码相关
      qrcodeDialogVisible: false,
      currentQrcode: null,
      currentChannelLink: null,
      currentDriverRow: null,
      qrcodeDialogType: 1, // 1:公众号 2:小程序
      activeTab: 'all'
    }
  },
  created() {
    // 检查URL参数中是否有司机ID
    const { driverId } = this.$route.query
    if (driverId) {
      this.listQuery.driver_id = driverId
      // 在mounted钩子中会自动执行fetchList，所以这里不需要再次调用
    }
  },
  mounted() {
    this.fetchDriverGroups()
    this.fetchList()
    window.addEventListener('resize', this.handleResize)
    this.fetchProfiles()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async fetchDriverGroups() {
      const params = {
        page: 1,
        per_page: 100
      }
      const { data } = await getDriverGroups(params)
      this.groupOptions = data && data.data && data.data.map((item) => {
        return {
          id: item.id,
          name: item.name
        }
      })
    },
    async fetchProfiles() {
      const { data } = await getProfiles()
      this.userInfo = data
      console.log('获取用户资料:', this.userInfo)
      // 初始化业态数据
      this.initBusinessTypes()
    },
    initBusinessTypes() {
      if (this.userInfo && this.userInfo.business_types) {
        this.businessTypes = Object.keys(this.userInfo.business_types).map(key => {
          return {
            value: Number(key),
            label: this.userInfo.business_types[key]
          }
        })
        console.log('业态数据:', this.businessTypes)
      }
    },
    handleBusinessTypeChange() {
      // 当业态改变时，重置子业态
      this.temp.sub_business_type = null
      // 确保业态是数字类型
      if (this.temp.business_type) {
        this.temp.business_type = Number(this.temp.business_type)
      }
    },
    handleSearchBusinessTypeChange() {
      // 当搜索条件中的业态变更时，重置子业态筛选
      this.listQuery.sub_business_type = undefined
      // 确保业态是数字类型
      if (this.listQuery.business_type) {
        this.listQuery.business_type = Number(this.listQuery.business_type)
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 270
    },
    async updateDriverPassword(driver_id) {
      this.$nextTick(() => {
        this.$refs['passwordForm'].clearValidate()
      })
      this.passwordDialog = true
      this.currentDriverId = driver_id
    },
    async handleUpdatePassword() {
      await updateDriverPassword(this.currentDriverId, this.passwordForm)
      this.passwordDialog = false
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
    },
    async updateDriverRoleType(driver_id, type) {
      await updateDriverRoleType(driver_id, type)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    openDriverLine(driverId) {
      this.$refs.lineInfo.show(driverId)
    },
    async updateDriverState(driver_id, state) {
      await updateDriverState(driver_id, state)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    toAddCar() {
      this.$router.push('/vehicleManage/index')
    },
    async fetchBranch() {
      const { data } = await dispatchBranches()
      if (data && data.length > 0) {
        this.branchs = data.map((o) => {
          return {
            name: o.mchname,
            id: o.admin_id
          }
        })
      }
    },
    openChangeGroup(row) {
      this.changeGroupDialog = true
      this.temp = Object.assign({}, {
        ...row
      })
    },
    isfull(scope) {
      return scope && scope.split(',').length === 9
    },
    async deleteItem(id, is_freeze) {
      await this.$confirm(`确认${is_freeze === 0 ? '恢复' : '删除'}当前司机?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await freezeDriver(id, is_freeze === 0 ? 1 : 0)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async updateAgencyLineStatus(row) {
      const params = {
        id: row.agency_id,
        status: row.status === 0 ? 1 : 0,
        type: 1
      }
      await updateDriverGroup(params)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async openUpdate(row) {
      this.dialogStatus = 'update'
      this.resetForm()
      const { data } = await getDriverDetail(row.driver_id)

      // 确保业态和子业态的值是数字类型
      if (data.business_type) {
        data.business_type = Number(data.business_type)
      }
      if (data.sub_business_type) {
        data.sub_business_type = Number(data.sub_business_type)
      }

      this.temp = Object.assign({}, {
        ...data,
        driver_group_attribute_id: data && data.driver_group_attribute_value && data.driver_group_attribute_value.group_attribute_id || '',
        business_type: data.business_type || 5, // 如果没有业态，默认为5
        sub_business_type: data.sub_business_type || null
      })

      console.log('编辑司机信息:', this.temp)

      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleScope(val) {
      const arr = val.split(',')
      const arrTag = this.typeOptions.filter((ele) => {
        const isExist = arr.some((Element) => {
          return Number(Element) === ele.value
        })
        if (isExist) {
          return ele
        }
      })
      if (arrTag && arrTag.length > 0) {
        return arrTag
      }
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.temp = {
        car_tail_number: '',
        car_brand: '',
        cellphone: '',
        password: '',
        total_seating: '',
        car_register_time: '',
        name: '',
        sort: '',
        branchid: '',
        driver_group_attribute_id: '',
        driver_type: null,
        id_number: '',
        driver_license: '',
        job_number: '',
        business_type: 5,
        sub_business_type: null
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleAdd() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({
        ...this.temp,
        password: this.temp.password || this.temp.cellphone.slice(-6)
      })
      await addDriver(params)
      this.dialogVisible = false
      this.$notify({
        message: '添加成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async handleUpdate() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({
        ...this.temp
      })
      await updateDriver(this.temp.driver_id, params)
      this.dialogVisible = false
      this.$notify({
        message: '编辑成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        size: 15,
        keyword: undefined,
        type: undefined,
        valid_category: undefined,
        status: undefined,
        driver_id: undefined,
        name: undefined,
        cellphone: undefined,
        car_brand: undefined,
        branchid: undefined,
        total_seating: undefined,
        driver_type: undefined,
        driver_role_type: undefined,
        job_number: undefined,
        business_type: undefined,
        sub_business_type: undefined
      }

      // 根据当前标签页设置冻结状态
      if (this.activeTab === 'frozen') {
        this.listQuery.is_freeze = 1
      } else {
        this.listQuery.is_freeze = 0
      }

      this.$refs.userTable.clearSort()
      this.date = []
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      try {
        // 根据当前标签页设置查询参数
        if (this.activeTab === 'frozen') {
          // 已冻结标签页
          this.listQuery.is_freeze = 1
        } else if (this.activeTab === 'all') {
          // 全部标签页，设置为未冻结状态
          this.listQuery.is_freeze = 0
        }

        const { data } = await getDrivers(this.listQuery, this.listQuery.driver_type)
        this.list = (data && data.data) || []
        this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total || 0
      } catch (error) {
        console.error('获取司机列表失败:', error)
        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },
    resetForm() {
      this.temp = {
        car_tail_number: '',
        car_brand: '',
        cellphone: '',
        password: '',
        total_seating: '',
        car_register_time: '',
        name: '',
        sort: '',
        branchid: '',
        driver_group_attribute_id: '',
        driver_type: '',
        id_number: '',
        driver_license: '',
        job_number: '',
        business_type: 5,
        sub_business_type: null
      }
    },
    async checkVehicleInfo(vehicleNo) {
      try {
        if (!this.userInfo || !this.userInfo.ciphertext) {
          await this.fetchProfiles()
        }
        const response = await getVehicleInfo(this.userInfo.ciphertext, vehicleNo)
        if (response.data) {
          // 处理返回的车辆信息
          console.log('车辆信息:', response.data)
          // 例如：将返回的车辆信息赋值给temp对象的其他属性
          this.temp.car_brand = response.data.brand_name
          this.temp.total_seating = response.data.vehicle_seats
          this.temp.car_register_time = response.data.driving_license_registration_date
        }
      } catch (error) {
        console.error('查询车辆信息失败:', error)
      }
    },
    handleCarNumberClick(carNumber) {
      this.$router.push({
        path: '/vehicleManage/index',
        query: {
          car_tail_number: carNumber
        }
      })
    },
    handleSelectionChange(selection) {
      this.selectedDrivers = selection
    },
    async handleReportToMonitoring() {
      if (this.selectedDrivers.length === 0) {
        this.$message({
          message: '请至少选择一名司机',
          type: 'warning'
        })
        return
      }

      try {
        await this.$confirm('确认将选中的司机信息上报到省级定制客运司机监控服务平台?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          distinguishCancelAndClose: true // 区分取消与关闭
        })

        // 只有用户点击确定按钮时才执行上报操作
        const driverIds = this.selectedDrivers.map(driver => driver.driver_id)
        await reportDriverToMonitoring(driverIds)
        this.$notify({
          message: '上报成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        // 只有在实际发生错误时才显示错误消息，不处理用户取消的情况
        if (error !== 'cancel') {
          this.$notify({
            message: '上报失败: ' + (error.message || '未知错误'),
            type: 'error',
            duration: 2000
          })
        }
      }
    },
    getQrCodeByType(qrAttachments, type) {
      if (!qrAttachments) return null

      // 如果qrAttachments是对象而不是数组，直接处理对象
      if (!Array.isArray(qrAttachments)) {
        if (type === 1 && qrAttachments.qr_url) {
          return qrAttachments.qr_url
        } else if (type === 2 && qrAttachments.mini_program_qr_url) {
          return qrAttachments.mini_program_qr_url
        }
        return null
      }

      // 如果是数组，保持原有逻辑
      const attachment = qrAttachments.find(item => item.type === type)
      return attachment ? attachment.qr_code : null
    },
    findQrAttachmentByType(qrAttachments, type) {
      if (!qrAttachments) return null

      // 如果qrAttachments是对象而不是数组，为其创建一个包含URL的对象
      if (!Array.isArray(qrAttachments)) {
        let qrUrl = null
        let miniProgramQrUrl = null

        if (qrAttachments.qr_url) {
          qrUrl = qrAttachments.qr_url
        }

        if (qrAttachments.mini_program_qr_url) {
          miniProgramQrUrl = qrAttachments.mini_program_qr_url
        }

        if (type === 1 && qrUrl) {
          return { type: 1, qr_code: qrUrl, qr_url: qrUrl }
        } else if (type === 2 && miniProgramQrUrl) {
          return { type: 2, qr_code: miniProgramQrUrl, qr_url: miniProgramQrUrl }
        }

        return null
      }

      // 如果是数组，保持原有逻辑
      return qrAttachments.find(item => item.type === type)
    },
    showQrcodeDialog(row, type) {
      this.qrcodeDialogType = type
      const qrAttachment = this.findQrAttachmentByType(row.driver_qr_attachment, type)
      if (qrAttachment) {
        this.currentQrcode = qrAttachment.qr_code
        this.currentChannelLink = qrAttachment.qr_url
      } else {
        this.currentQrcode = null
        this.currentChannelLink = null
      }
      this.currentDriverRow = row
      this.qrcodeDialogVisible = true
    },
    generateQrcode(row, type) {
      const typeText = type === 1 ? '公众号' : '小程序'
      this.$confirm(`确认为该司机生成${typeText}二维码吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.listLoading = true
        // 调用生成二维码的API
        generateDriverQrcode(row.driver_id, type).then(response => {
          // 处理API响应数据
          if (response.data) {
            // 确保driver_qr_attachment存在
            if (!row.driver_qr_attachment) {
              this.$set(row, 'driver_qr_attachment', {})
            }

            // 根据类型更新对应的URL
            const qrUrl = response.data.qr_url || response.data.qr_code

            if (type === 1) {
              this.$set(row.driver_qr_attachment, 'qr_url', qrUrl)
            } else if (type === 2) {
              this.$set(row.driver_qr_attachment, 'mini_program_qr_url', qrUrl)
            }

            // 更新弹窗数据
            this.currentQrcode = qrUrl
            this.currentChannelLink = qrUrl
          }

          this.$notify({
            title: '成功',
            message: `${typeText}二维码生成成功`,
            type: 'success',
            duration: 2000
          })
          this.listLoading = false
        }).catch(error => {
          console.error(`生成${typeText}二维码失败:`, error)
          this.listLoading = false
          let errorMessage = `生成${typeText}二维码失败，请重试`
          if (error.response && error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          }
          this.$notify({
            title: '错误',
            message: errorMessage,
            type: 'error',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消生成${typeText}二维码`
        })
      })
    },
    downloadQrcode() {
      if (!this.currentQrcode) return

      // 创建a标签实现下载
      const link = document.createElement('a')
      link.href = this.currentQrcode
      const typeText = this.qrcodeDialogType === 1 ? '公众号二维码' : '小程序二维码'
      link.download = `${typeText}_${this.currentDriverRow.name}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    copyChannelLink(link) {
      if (!link) return

      // 创建一个临时输入框来实现复制功能
      const input = document.createElement('input')
      input.value = link
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)

      this.$message({
        message: '链接已复制到剪贴板',
        type: 'success',
        duration: 1500
      })
    },
    regenerateQrcode() {
      if (!this.currentDriverRow) return

      const type = this.qrcodeDialogType
      const typeText = type === 1 ? '公众号' : '小程序'

      this.$confirm(`确认重新生成${typeText}二维码吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示加载状态
        this.$set(this, 'currentQrcode', null)

        // 调用生成二维码的API
        generateDriverQrcode(this.currentDriverRow.driver_id, type).then(response => {
          // 处理API响应数据
          if (response.data) {
            // 确保driver_qr_attachment存在
            if (!this.currentDriverRow.driver_qr_attachment) {
              this.$set(this.currentDriverRow, 'driver_qr_attachment', {})
            }

            // 根据类型更新对应的URL
            const qrUrl = response.data.qr_url || response.data.qr_code

            if (type === 1) {
              this.$set(this.currentDriverRow.driver_qr_attachment, 'qr_url', qrUrl)
            } else if (type === 2) {
              this.$set(this.currentDriverRow.driver_qr_attachment, 'mini_program_qr_url', qrUrl)
            }

            // 更新弹窗数据
            this.currentQrcode = qrUrl
            this.currentChannelLink = qrUrl
          }

          // 关闭弹窗
          this.qrcodeDialogVisible = false

          this.$notify({
            title: '成功',
            message: `${typeText}二维码重新生成成功`,
            type: 'success',
            duration: 2000
          })

          // 重新获取列表数据，刷新整个列表
          this.fetchList()
        }).catch(error => {
          console.error(`重新生成${typeText}二维码失败:`, error)
          let errorMessage = `重新生成${typeText}二维码失败，请重试`
          if (error.response && error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          }
          this.$notify({
            title: '错误',
            message: errorMessage,
            type: 'error',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消重新生成${typeText}二维码`
        })
      })
    },
    handleTabClick(tab) {
      // 切换标签页时重置页码
      this.listQuery.page = 1

      if (tab.name === 'frozen') {
        // 已冻结标签页
        this.listQuery.is_freeze = 1
      } else {
        // 全部标签页，设置为未冻结状态
        this.listQuery.is_freeze = 0
      }

      this.activeTab = tab.name
      this.fetchList()
    }
  },
  computed: {
    ...mapGetters(['function_permission_definition'])
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.handle-container {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;

    .filter-item {
      margin-right: 0;
      margin-bottom: 0;

      &.mini-condition {
        width: 200px;
      }

      &.add {
        color: #409EFF;
        margin-bottom: 10px;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
}

/* 标签页样式 */
.el-tabs {
  margin-bottom: 15px;
}

.el-tabs__item {
  font-size: 15px;
  font-weight: 500;
  height: 40px;
  line-height: 40px;
}

.el-tabs__item.is-active {
  color: #409EFF;
}

::v-deep {
  .el-input-group__prepend {
    background-color: #f5f7fa;
    border-color: #dcdfe6;
    color: #606266;
    padding: 0 12px;
    min-width: 70px;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-select {
    width: 200px;

    .el-input {
      width: 100%;
    }
  }
}

.component-container {
  margin-top: 24px;
}

.middle-label {
  vertical-align: middle;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;

  .left-content {
    display: flex;
    align-items: center;

    .selected-info {
      margin-left: 10px;
      font-size: 14px;
      color: #606266;

      .selected-count {
        font-weight: bold;
        color: #409EFF;
        margin: 0 3px;
      }
    }
  }
}

/* 移除不再需要的report-action-bar样式 */
.report-action-bar {
  display: none;
}
</style>

<style lang="scss">
.scope_type {
  .el-checkbox {
    margin-right: 12px;
  }

  .el-checkbox__label {
    font-size: 12px;
    padding-left: 5px;
  }
}

.label-key {
  margin-left: 4px;
  margin-right: 8px;
}

.tips {
  font-size: 14px;
  color: #F56C6C;
}

.tips_button {
  font-size: 14px;
  color: #409EFF;
  cursor: pointer;
}

.tips-content {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}

/* 二维码相关样式 */
.qrcode-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-thumbnail {
  width: 60px;
  height: 60px;
  cursor: pointer;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: all 0.3s;
}

.qrcode-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.qrcode-large {
  width: 280px;
  height: 280px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.qrcode-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qrcode-link-container {
  width: 100%;
  margin: 15px 0;
}

.qrcode-dialog-footer {
  margin-top: 15px;
  text-align: center;
}

.channel-link-input {
  width: 100%;
}
</style>

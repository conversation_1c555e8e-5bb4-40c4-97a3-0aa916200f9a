<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button
          class="filter-item add"
          style="margin-left: 0"
          type="text"
          icon="el-icon-circle-plus-outline"
          @click="handleCreate"
        >
          添加分组
        </el-button>
      </div>
      <div class="filter-container">
        <span class="label-key">分组名称</span>
        <el-input
          v-model="listQuery.name"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          @click="handleReset"
        >
          重置
        </el-button>
      </div>
    </div>
    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      height="calc(100vh - 240px)"
      size="small"
      style="width: 100%"
    >
      <el-table-column label="分组编号" prop="id" />
      <el-table-column label="分组排序" prop="sort" />
      <el-table-column label="分组名称" prop="name" />
      <el-table-column label="添加时间">
        <template slot-scope="scope">
          {{ scope.row.created_at }}
        </template>
      </el-table-column>
      <el-table-column
        class-name="status-col"
        label="操作"
        width="200"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="openUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="deleteDriverGroup(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.per_page"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增分组' : '修改分组'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="120px"
        size="medium"
      >
        <el-form-item label="分组排序" prop="sort">
          <el-input
            v-model="temp.sort"
            placeholder="请输入名称"
            style="width: 260px"
          />
        </el-form-item>

        <el-form-item label="分组名称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入名称"
            style="width: 260px"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : handleUpdate()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDriverGroups, createDriverGroup, updateDriverGroup, deleteDriverGroup } from '@/api/drvier'
export default {
  name: 'GroupManagement',
  components: {

  },
  data() {
    const validateSort = (rule, value, callback) => {
      if (!/^\d+$/.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    return {
      branchs: [],
      temp: {
        name: '',
        branch_id: '',
        summary: '',
        price: 0,
        channel_price: 0,
        rate: 0,
        status: 0,
        start_appointment_time: '',
        end_appointment_time: '',
        business_time_type: 0,
        appointment_time: '',
        set_order_time: 0
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' },
          { validator: validateSort, trigger: 'blur' }
        ]
      },
      typeOptions: [
        { value: 1, label: '拼车' },
        { value: 2, label: '包车' },
        { value: 3, label: '带货' },
        { value: 4, label: '分组' },
        { value: 5, label: '定制客运', enable: true },
        { value: 6, label: '顺风车' },
        { value: 7, label: '快车' },
        { value: 11, label: '出租车' },
        { value: 20, label: '摆渡车' }
      ],
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: null,
      listLoading: false,
      listQuery: {
        page: 1,
        per_page: 15,
        name: null
      }
    }
  },
  created() {
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    isfull(scope) {
      return scope.split(',').length === 9
    },
    async deleteDriverGroup(id) {
      await this.$confirm('确认删除当前分组?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await deleteDriverGroup(id)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async updateAgencyLineStatus(row) {
      const params = {
        id: row.agency_id,
        status: row.status === 0 ? 1 : 0,
        type: 1
      }
      await updateDriverGroup(params)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    openUpdate(row) {
      this.dialogStatus = 'update'
      this.resetForm()
      this.temp = Object.assign({}, {
        ...row
      })
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleScope(val) {
      const arr = val.split(',')
      const arrTag = this.typeOptions.filter((ele) => {
        const isExist = arr.some((Element) => {
          return Number(Element) === ele.value
        })
        if (isExist) {
          return ele
        }
      })
      if (arrTag.length > 0) {
        return arrTag
      }
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetForm()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleAdd() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({
        name: this.temp.name,
        sort: this.temp.sort
      })
      await createDriverGroup(params)
      this.dialogVisible = false
      this.$notify({
        message: '添加成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async handleUpdate() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({
        name: this.temp.name,
        sort: this.temp.sort
      })
      await updateDriverGroup(this.temp.id, params)
      this.dialogVisible = false
      this.$notify({
        message: '编辑成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        keyword: undefined,
        type: undefined,
        valid_category: undefined,
        status: undefined
      }
      this.$refs.userTable.clearSort()
      this.date = []
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      const { data } = await getDriverGroups(this.listQuery)
      this.list = data && data.data
      this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total
      this.listLoading = false
    },
    resetForm() {
      this.temp = {
        name: '',
        branch_id: '',
        summary: '',
        price: 0,
        channel_price: 0,
        business_time_type: 0,
        appointment_time: '',
        set_order_time: 0
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.component-container {
  margin-top: 24px;
}
.middle-label {
  vertical-align: middle;
}
</style>

<style lang="scss">
.scope_type {
  .el-checkbox {
    margin-right: 12px;
  }
  .el-checkbox__label {
    font-size: 12px;
    padding-left: 5px;
  }
}
.label-key{
  margin-left: 3px;
}
</style>

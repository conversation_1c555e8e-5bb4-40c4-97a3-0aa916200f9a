<template>
  <el-dialog
    title="定制班线车线路"
    :visible.sync="dialogVisible"
    width="900px"
  >
    <div class="line-container">
      <div v-for="item in data" :key="item.line_class_id" class="line-item">
        [定制班线车  线路编号：{{ item.line_class_id }}]{{ item.start_name }} - {{ item.end_name }}
        <div v-for="detail in item.details" :key="detail.line_class_train_id" class="line-info">
          班次编号：{{ detail.line_class_train_no }}，出发时间：{{ detail.start_date }} {{ detail.start_time }}
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDriverLineClassTrains } from '@/api/drvier'
export default {
  name: 'LineInfo',
  data() {
    return {
      dialogVisible: false,
      driverId: '',
      data: []
    }
  },
  methods: {
    async show(driverId) {
      this.dialogVisible = true
      this.driverId = driverId
      if (!driverId) {
        this.$message.error('司机ID不存在')
        return
      }
      const { data } = await getDriverLineClassTrains(this.driverId)
      this.data = data
    }
  }
}
</script>

<style lang="scss" scoped>
.line-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.line-item {
  width: 48%;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
}
.line-info{
  font-size: 12px;
}
</style>

<template>
  <div class="app-container">
    <div class="config-warp">
      <el-form ref="reqiredForm" v-loading="loading" :model="smsData" label-width="130px" size="small" style="margin-top: 30px">
        <el-form-item label="短信功能开关" prop="is_enabled">
          <el-switch v-model="smsSwitch" active-color="#0067E1" :active-value="1" :inactive-value="0" @change="setConfigStatus" />
          <span class="label-key"> 关闭后，则无法使用短信功能</span>
        </el-form-item>

        <el-form-item label="">
          <div class="rule-description">
            <p>短信扣除数量根据运营商规则计算：</p>
            <ol>
              <li>短信内容长度 = 短信签名长度 + 短信正文长度；</li>
              <li>汉字、数字、英文、标点符号均计为1个字；</li>
              <li>短信长度≤70字为普通短信，扣除数量为1条；</li>
              <li>短信长度>70字为长短信，每67字计为1条（例如：134字扣除2条）；</li>
              <li>短信发送失败不扣短信数量。</li>
            </ol>
          </div>
        </el-form-item>
      </el-form>
      <div class="button-bottom">
        <el-button size="small" type="primary" @click="handleSave">保存配置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getSmsConfig,
  updateSmsConfig
} from '@/api/systemSetup'
export default {
  name: 'SmsConfig',
  data() {
    return {
      loading: false,
      smsSwitch: 0,
      smsData: {}
    }
  },
  async mounted() {
    this.fetchConfig()
  },
  methods: {
    setConfigStatus() {
      // 根据API接口，直接传递状态值
      updateSmsConfig(this.smsSwitch).then(
        () => {
          this.$notify.success(
            this.smsSwitch === 1 ? '开启短信配置' : '关闭短信功能'
          )
        }
      ).catch(error => {
        console.error('更新短信配置状态失败:', error)
        this.$notify.error('更新短信配置状态失败')
        // 恢复原来的状态
        this.smsSwitch = this.smsSwitch === 1 ? 0 : 1
      })
    },

    handleSave() {
      this.$notify.success('保存成功')
    },

    async fetchConfig() {
      this.loading = true
      try {
        const response = await getSmsConfig()
        this.loading = false

        // 根据API返回的实际数据结构进行处理
        if (response && response.data) {
          this.smsSwitch = response.data.sms_config || 0
        }
      } catch (error) {
        this.loading = false
        console.error('获取短信配置失败:', error)
        this.$notify.error('获取短信配置失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.button-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
.config-warp {
  width: 70%;
}
.label-key {
  color: #888888;
}
.rule-description {
  color: #606266;
  line-height: 1.6;

  p {
    margin-bottom: 10px;
    font-weight: bold;
  }

  ol {
    padding-left: 20px;
    margin: 0;

    li {
      margin-bottom: 5px;
    }
  }
}
</style>

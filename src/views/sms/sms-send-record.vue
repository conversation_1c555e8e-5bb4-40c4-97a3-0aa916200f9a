<template>
  <div class="app-container">
    <!-- 添加短信剩余条数卡片 -->
    <el-card v-loading="balanceLoading" class="sms-balance-card">
      <div class="sms-balance-content">
        <i class="el-icon-message" />
        <div class="sms-balance-info">
          <div class="sms-balance-title">短信剩余条数</div>
          <div class="sms-balance-count">{{ smsBalance }}</div>
        </div>
      </div>
    </el-card>

    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.cellphone"
          placeholder="请输入手机号"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">手机号</template>
        </el-input>

        <el-input
          v-model="listQuery.order_no"
          placeholder="请输入订单号"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">订单号</template>
        </el-input>

        <el-select
          v-model="listQuery.receiver_type"
          placeholder="请选择接收对象"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <template slot="prepend">接收对象</template>
          <el-option label="全部" :value="null" />
          <el-option label="乘客" :value="receiverTypes.passenger" />
          <el-option label="司机" :value="receiverTypes.driver" />
          <el-option label="商户" :value="receiverTypes.merchant" />
          <el-option label="分台" :value="receiverTypes.branch" />
        </el-select>

        <el-date-picker
          v-model="listQuery.send_time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          value-format="yyyy-MM-dd"
          class="filter-item date-picker"
        />

        <el-button
          class="filter-item"
          type="primary"
          size="small"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          size="small"
          plain
          icon="el-icon-refresh"
          @click="handleReset"
        >
          重置
        </el-button>
      </div>
    </div>

    <el-table
      ref="smsTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)"
      fit
      highlight-current-row
      :max-height="tableHeight"
      size="mini"
      style="width: 100%"
      stripe
    >
      <el-table-column
        label="短信ID"
        prop="id"
        width="100"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="订单号"
        prop="order_no"
        width="120"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.order_no"
            type="text"
            @click="goToOrderDetail(scope.row.order_no)"
          >
            {{ scope.row.order_no }}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发送时间"
        prop="send_time"
        width="160"
        align="center"
        sortable
      />
      <el-table-column
        label="接收手机号"
        prop="accept_tel"
        width="120"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="phone-container">
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.accept_tel || '暂无' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="接收对象"
        prop="account_type"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag size="mini" :type="getReceiverTypeTagType(scope.row.account_type)">
            {{ getReceiverTypeText(scope.row.account_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="对象ID"
        prop="account_id"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.account_id && scope.row.account_type"
            type="text"
            @click="goToUserDetail(scope.row.account_id, scope.row.account_type)"
          >
            {{ scope.row.account_id }}
          </el-button>
          <span v-else>{{ scope.row.account_id || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="短信内容"
        prop="accept_content"
        min-width="400"
        align="center"
      >
        <template slot-scope="scope">
          <el-popover
            placement="top"
            width="400"
            trigger="hover"
            popper-class="content-popover"
          >
            <div style="max-height: 400px; overflow-y: auto; padding: 10px; line-height: 1.5;">{{ scope.row.accept_content }}</div>
            <div slot="reference" class="ellipsis" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: left;">{{ scope.row.accept_content }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        label="内容字符数"
        prop="content_lenth"
        width="100"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <span class="length-count">{{ scope.row.content_lenth }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="扣除数量"
        prop="deduct_count"
        min-width="100"
        align="center"
        show-overflow-tooltip
        fixed="right"
      >
        <template slot-scope="scope">
          <span class="deduct-count">{{ scope.row.deduct_count }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.size"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right; padding: 10px 0;"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>
<script>
import { getSmsSendRecord } from '@/api/systemSetup'
import { getProfiles } from '@/api/user' // 导入获取商户信息的方法

export default {
  name: 'SmsSendRecord',
  data() {
    return {
      showMore: false,
      tableHeight: window.innerHeight - 300,
      total: 0,
      list: [],
      listLoading: false,
      smsBalance: 0, // 短信剩余条数
      balanceLoading: false, // 短信余额加载状态
      receiverTypes: {
        passenger: 'passenger',
        driver: 'driver',
        merchant: 'merchant',
        branch: 'branch'
      },
      listQuery: {
        page: 1,
        size: 15,
        cellphone: null,
        type: null,
        status: null,
        receiver_type: null,
        send_time: null
      }
    }
  },
  mounted() {
    this.fetchList()
    this.fetchSmsBalance() // 获取短信余额
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 获取短信余额
    async fetchSmsBalance() {
      this.balanceLoading = true
      try {
        const { data } = await getProfiles()
        if (data && data.shortmessage) {
          this.smsBalance = data.shortmessage
        }
      } catch (error) {
        console.error('获取短信余额失败:', error)
        this.$message.error('获取短信余额失败，请稍后重试')
      } finally {
        this.balanceLoading = false
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 300
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        size: 15,
        cellphone: null,
        order_no: null,
        receiver_type: null,
        send_time: null
      }
      this.showMore = false
      this.$refs.smsTable.clearSort()
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      try {
        // 构建查询参数
        const params = { ...this.listQuery }

        // 处理日期范围 - 修改判断逻辑，避免访问null的length属性
        if (Array.isArray(this.listQuery.send_time) && this.listQuery.send_time.length === 2) {
          params.start_date = this.listQuery.send_time[0]
          params.end_date = this.listQuery.send_time[1]
          delete params.send_time
        }

        // 不需要转换接收对象类型参数，直接使用 receiver_type

        const { data } = await getSmsSendRecord(params)
        this.list = (data && data.data) || [] // 确保list始终是数组
        this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total || 0 // 确保total始终是数字
      } catch (error) {
        console.error('获取短信发送记录失败', error)
        this.$message.error('获取短信发送记录失败')
        this.list = [] // 出错时确保list是空数组
      } finally {
        this.listLoading = false
      }
    },
    getReceiverTypeText(type) {
      const typeMap = {
        'driver': '司机',
        'passenger': '乘客',
        'merchant': '商户',
        'branch': '分台',
        null: '验证码'
      }
      return typeMap[type] || '未知对象'
    },
    getReceiverTypeTagType(type) {
      const typeMap = {
        'driver': 'success',
        'passenger': 'primary',
        'merchant': 'warning',
        'branch': 'info',
        null: 'danger'
      }
      return typeMap[type] || ''
    },
    goToOrderDetail(order_no) {
      // 跳转到订单列表页面并自动填充订单号进行查询
      this.$router.push({
        path: '/orderManage/tarinsManage',
        query: {
          orderNo: order_no
        }
      })
    },
    goToUserDetail(account_id, account_type) {
      // 根据对象类型跳转到不同的页面
      if (account_type === 'driver') {
        // 司机 - 跳转到司机管理页面
        this.$router.push({
          path: '/userManage/driverPermission/driver',
          query: {
            driverId: account_id
          }
        })
      } else if (account_type === 'passenger') {
        // 乘客 - 跳转到乘客管理页面
        this.$router.push({
          path: '/userManage/passengerManage/passenger',
          query: {
            passengerId: account_id
          }
        })
      } else {
        // 其他类型暂不处理
        this.$message.info(`暂不支持查看${this.getReceiverTypeText(account_type)}详情`)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.sms-balance-card {
  margin-bottom: 20px;
  width: 220px;

  ::v-deep .el-card__body {
    padding: 15px;
  }
}

.sms-balance-content {
  display: flex;
  align-items: center;

  i {
    font-size: 28px;
    color: #409EFF;
    margin-right: 12px;
  }

  .sms-balance-info {
    .sms-balance-title {
      font-size: 13px;
      color: #606266;
      margin-bottom: 3px;
    }

    .sms-balance-count {
      font-size: 20px;
      font-weight: bold;
      color: #409EFF;
    }
  }
}

.handle-container {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;

  .filter-item {
    margin-right: 0;
    margin-bottom: 0;

    &.mini-condition {
      width: 200px;
    }

    &.date-picker {
      width: 260px;
    }
  }

  ::v-deep {
    .el-input-group__prepend {
      background-color: #f5f7fa;
      border-color: #dcdfe6;
      color: #606266;
      padding: 0 12px;
    }

    .el-button {
      padding: 9px 15px;
    }

    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }

    .el-range-editor.el-input__inner {
      padding: 0 10px;
    }

    .el-select .el-input {
      width: 200px;
    }
  }
}

.component-container {
  margin-top: 24px;
}

.middle-label {
  vertical-align: middle;
}

.status-tag {
  display: inline-flex;
  align-items: center;

  i {
    margin-right: 4px;
  }
}

.phone-container {
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    margin-right: 5px;
    color: #409EFF;
  }
}

.deduct-count {
  font-weight: bold;
  color: #F56C6C;
}

.length-count {
  font-weight: bold;
  color: #409EFF;
}

.box-card {
  margin-top: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  ::v-deep .el-card__header {
    padding: 12px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }

  ::v-deep .el-card__body {
    padding: 20px;
  }
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;

  i {
    margin-right: 5px;
    color: #409EFF;
  }
}

.search-form {
  .el-form {
    margin-bottom: 0;
  }

  ::v-deep .el-form-item {
    margin-bottom: 15px;
  }
}

.form-item-content {
  display: flex;
  flex-direction: column;
}

.form-item-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  i {
    font-size: 16px;
    color: #909399;
    margin-right: 5px;
  }

  span {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }
}

.more-conditions {
  margin-top: 5px;
  padding-top: 15px;
  border-top: 1px dashed #ebeef5;
}

.search-buttons {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px dashed #ebeef5;

  .el-button {
    min-width: 100px;
    margin: 0 10px;
  }
}
</style>

<style lang="scss">
.label-key{
  margin-left: 4px;
  margin-right: 8px;
  font-weight: 500;
}

.mini-condition {
  .el-input-group__prepend {
    min-width: 64px;
    text-align: center;
  }
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.content-popover {
  max-width: 400px;
  word-break: break-all;
}
</style>

<template>
  <el-dialog
    title="乘客详情"
    :visible.sync="dialogVisible"
    width="1100px"
    :close-on-click-modal="false"
    class="passenger-detail-dialog"
    :destroy-on-close="true"
  >
    <div v-loading="loading" class="dialog-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-user" />
          <span>基本信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">乘客编号：</span>
            <span class="value text-ellipsis">{{ passengerInfo.passenger_id || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value text-ellipsis">{{ passengerInfo.cellphone || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">微信昵称：</span>
            <span class="value text-ellipsis">{{ passengerInfo.name || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">账户状态：</span>
            <span class="value">
              <el-tag
                :type="passengerInfo.is_freeze ? 'danger' : 'success'"
                effect="plain"
                size="mini"
                class="status-tag"
              >
                <i v-if="passengerInfo.is_freeze" class="el-icon-warning" />
                <i v-else class="el-icon-success" />
                {{ passengerInfo.is_freeze ? '已冻结' : '正常' }}
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">注册时间：</span>
            <span class="value text-ellipsis">{{ passengerInfo.create_time || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后登录：</span>
            <span class="value text-ellipsis">{{ passengerInfo.last_login_time || '暂无' }}</span>
          </div>
        </div>
      </div>

      <!-- 账户信息 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-wallet" />
          <span>账户信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">账户余额：</span>
            <span class="value highlight text-ellipsis">{{ passengerInfo.cash_balances ? '¥' + Number(passengerInfo.cash_balances).toFixed(2) : '¥0.00' }}</span>
          </div>
          <div class="info-item">
            <span class="label">积分：</span>
            <span class="value text-ellipsis">{{ passengerInfo.user_extra && passengerInfo.user_extra.points || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">优惠券数量：</span>
            <span class="value text-ellipsis">{{ passengerInfo.coupon_count || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 关联信息 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-connection" />
          <span>关联信息</span>
        </div>
        <div class="info-grid id-grid">
          <div class="info-item id-item">
            <span class="label">微信公众号OpenID：</span>
            <el-tooltip :content="passengerInfo.openid || '暂无'" placement="top" :disabled="!passengerInfo.openid">
              <span class="value text-ellipsis">{{ passengerInfo.openid || '暂无' }}</span>
            </el-tooltip>
          </div>
          <div class="info-item id-item">
            <span class="label">微信小程序OpenID：</span>
            <el-tooltip :content="passengerInfo.openid_mini || '暂无'" placement="top" :disabled="!passengerInfo.openid_mini">
              <span class="value text-ellipsis">{{ passengerInfo.openid_mini || '暂无' }}</span>
            </el-tooltip>
          </div>
          <div class="info-item id-item">
            <span class="label">微信UnionID：</span>
            <el-tooltip :content="passengerInfo.unionid || '暂无'" placement="top" :disabled="!passengerInfo.unionid">
              <span class="value text-ellipsis">{{ passengerInfo.unionid || '暂无' }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 认证信息 -->
      <div class="info-section" v-if="hasVerificationInfo">
        <div class="section-header">
          <i class="el-icon-document-checked" />
          <span>认证信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">真实姓名：</span>
            <span class="value text-ellipsis">{{ passengerInfo.real_name || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份证号：</span>
            <span class="value text-ellipsis">{{ passengerInfo.ID_number || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别：</span>
            <span class="value text-ellipsis">{{ passengerInfo.gender === 1 ? '男' : passengerInfo.gender === 2 ? '女' : '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">出生日期：</span>
            <span class="value text-ellipsis">{{ passengerInfo.birthday || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">认证状态：</span>
            <span class="value">
              <el-tag
                :type="getExamineStatusType(passengerInfo.examine_status)"
                effect="plain"
                size="mini"
              >
                {{ getExamineStatusText(passengerInfo.examine_status) }}
              </el-tag>
            </span>
          </div>
        </div>
      </div>

      <!-- 学生认证信息（如果有） -->
      <div v-if="hasStudentInfo" class="info-section">
        <div class="section-header">
          <i class="el-icon-reading" />
          <span>学生认证信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">学校：</span>
            <span class="value text-ellipsis">{{ passengerInfo.school || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">班级：</span>
            <span class="value text-ellipsis">{{ passengerInfo.student_class || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">学生证号：</span>
            <span class="value text-ellipsis">{{ passengerInfo.student_id_number || '暂无' }}</span>
          </div>
        </div>
      </div>

      <!-- 证件照片 -->
      <div class="info-section" v-if="hasPhotoInfo">
        <div class="section-header">
          <i class="el-icon-picture" />
          <span>证件照片</span>
        </div>
        <div class="photo-container">
          <div v-if="passengerInfo.ID_img" class="photo-item">
            <div class="photo-title">身份证照片</div>
            <el-image
              :src="passengerInfo.ID_img"
              fit="cover"
              :preview-src-list="[passengerInfo.ID_img]"
            >
              <div slot="error" class="image-error">
                <i class="el-icon-picture-outline"></i>
                <span>暂无照片</span>
              </div>
            </el-image>
          </div>
          <div v-if="passengerInfo.residence_booklet" class="photo-item">
            <div class="photo-title">户口簿照片</div>
            <el-image
              :src="passengerInfo.residence_booklet"
              fit="cover"
              :preview-src-list="[passengerInfo.residence_booklet]"
            >
              <div slot="error" class="image-error">
                <i class="el-icon-picture-outline"></i>
                <span>暂无照片</span>
              </div>
            </el-image>
          </div>
        </div>
      </div>

    
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button
        v-if="passengerInfo.passenger_id"
        type="primary"
        @click="goToWalletDetail"
      >
        查看钱包明细
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPassengerDetail } from '@/api/passenger'

export default {
  name: 'PassengerDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      passengerInfo: {}
    }
  },
  computed: {
    hasStudentInfo() {
      return !!(this.passengerInfo.school || this.passengerInfo.student_class || this.passengerInfo.student_id_number)
    },
    hasVerificationInfo() {
      return !!(this.passengerInfo.real_name || this.passengerInfo.ID_number || typeof this.passengerInfo.examine_status !== 'undefined')
    },
    hasPhotoInfo() {
      return !!(this.passengerInfo.ID_img || this.passengerInfo.residence_booklet)
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerId) {
        this.fetchPassengerDetail()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    async fetchPassengerDetail() {
      if (!this.passengerId) return

      this.loading = true
      try {
        const { data } = await getPassengerDetail(this.passengerId)
        this.passengerInfo = data || {}
      } catch (error) {
        console.error('获取乘客详情失败:', error)
        this.$notify({
          title: '错误',
          message: '获取乘客详情失败',
          type: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    goToWalletDetail() {
      this.$router.push({
        path: '/userManage/passengerManage/passengerWalletProfile',
        query: {
          passenger_id: this.passengerId
        }
      })
      this.dialogVisible = false
    },
    getExamineStatusType(status) {
      switch (status) {
        case 0: return 'success' // 正常/已认证
        case 1: return 'warning' // 审核中
        case 2: return 'danger'  // 审核未通过
        case 3: return 'info'    // 资料提交中
        default: return 'info'
      }
    },
    getExamineStatusText(status) {
      switch (status) {
        case 0: return '已认证'
        case 1: return '审核中'
        case 2: return '未通过'
        case 3: return '资料提交中'
        default: return '未认证'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.passenger-detail-dialog {
  ::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  ::v-deep .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  ::v-deep .el-dialog__headerbtn {
    top: 15px;
  }

  .dialog-content {
    max-height: 65vh;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .info-section {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;

    i {
      margin-right: 8px;
      color: #409EFF;
      font-size: 16px;
    }

    span {
      font-size: 15px;
      font-weight: 500;
      color: #303133;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px;

    &.id-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .info-item {
    display: flex;
    align-items: center;
    height: 32px;

    &.id-item {
      height: auto;
    }

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 140px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      flex: 1;

      &.text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &.highlight {
        color: #409EFF;
        font-weight: 500;
      }
    }
  }

  .status-tag {
    display: inline-flex;
    align-items: center;

    i {
      margin-right: 3px;
    }
  }

  .photo-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;

    .photo-item {
      width: 250px;

      .photo-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }

      ::v-deep .el-image {
        width: 100%;
        height: 180px;
        border-radius: 4px;
        border: 1px solid #ebeef5;
      }

      .image-error {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        background-color: #f5f7fa;
        color: #909399;

        i {
          font-size: 30px;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>

<template>
  <div class="app-container">
    <div class="search-wrapper">
      <div class="filter-container">
        <div class="filter-item">
          <el-input
            v-model="listQuery.cellphone"
            placeholder="手机号"
            clearable
            size="small"
          />
        </div>
        <div class="filter-item">
          <el-select
            v-model="listQuery.is_freeze"
            placeholder="账户状态"
            clearable
            size="small"
          >
            <el-option label="正常" :value="0" />
            <el-option label="已冻结" :value="1" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="listQuery.examine_status"
            placeholder="认证状态"
            clearable
            size="small"
          >
            <el-option label="正常" :value="0" />
            <el-option label="审核中" :value="1" />
            <el-option label="审核未通过" :value="2" />
            <el-option label="资料提交中" :value="3" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="listQuery.create_time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            value-format="yyyy-MM-dd"
          />
        </div>
        <div class="buttons">
          <el-button
            type="primary"
            size="small"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            size="small"
            @click="handleReset"
          >
            重置
          </el-button>
        </div>
      </div>
    </div>

    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      :max-height="tableHeight"
      size="mini"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        width="40"
        align="center"
      />

      <el-table-column
        label="乘客编号"
        prop="passenger_id"
        width="140"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="手机号"
        prop="cellphone"
        width="140"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div>
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.cellphone || '暂无' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="微信昵称"
        prop="name"
        width="140"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="driver-name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="微信公众号OpenID"
        prop="openid"
        width="250"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.openid || '暂无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="微信小程序OpenID"
        prop="openid_mini"
        width="250"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.openid_mini || '暂无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="积分"
        prop="user_extra.points"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.user_extra && scope.row.user_extra.points || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="余额"
        prop="cash_balances"
        align="center"
      >
        <template slot-scope="scope">
          <span
            class="balance-link"
            @click="goToBalanceDetail(scope.row.passenger_id)"
          >{{ scope.row.cash_balances ? '¥' + Number(scope.row.cash_balances).toFixed(2) : '¥0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="账户状态"
        prop="is_freeze"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.is_freeze ? 'danger' : 'success'"
            effect="plain"
            size="mini"
            class="status-tag"
          >
            <i v-if="scope.row.is_freeze" class="el-icon-warning" />
            <i v-else class="el-icon-success" />
            {{ scope.row.is_freeze ? '已冻结' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="认证状态"
        prop="examine_status"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="getExamineStatusType(scope.row.examine_status)"
            effect="plain"
            size="mini"
            class="status-tag"
          >
            {{ getExamineStatusText(scope.row.examine_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="注册时间"
        prop="create_time"
        width="160"
        align="center"
      />
      <el-table-column
        class-name="status-col"
        label="操作"
        width="200"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="openDetail(scope.row)"
          >
            详情
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            v-if="scope.row.examine_status === 1"
            class="filter-item"
            type="text"
            size="medium"
            @click="openVerify(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-else
            class="filter-item"
            type="text"
            size="medium"
            @click="openUpdatePhone(scope.row)"
          >
            修改手机号
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="deleteItem(scope.row.passenger_id, scope.row.is_freeze)"
          >
            {{ scope.row.is_freeze === 0 ? '删除' : '恢复' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.size"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增乘客' : '修改乘客'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="900px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="right"
        label-width="120px"
        size="medium"
        inline
      >
        <el-form-item label="手机号" prop="cellphone">
          <el-input
            v-model="temp.cellphone"
            placeholder="请输入手机号"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入昵称"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input
            v-model="temp.password"
            placeholder="默认密码为手机号码后6位"
            style="width: 260px"
            show-password
            type="password"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : handleUpdate()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改手机号对话框 -->
    <el-dialog
      title="修改手机号"
      :visible.sync="phoneDialogVisible"
      class="phone-dialog"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        ref="phoneForm"
        :model="phoneTemp"
        :rules="phoneRules"
        label-position="right"
        label-width="120px"
        size="medium"
      >
        <el-form-item label="当前手机号">
          <span>{{ phoneTemp.oldCellphone || '未绑定' }}</span>
        </el-form-item>
        <el-form-item label="新手机号" prop="cellphone">
          <el-input
            v-model="phoneTemp.cellphone"
            placeholder="请输入新手机号"
            style="width: 260px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="phoneDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleUpdatePhone"
        >
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 乘客详情弹窗 -->
    <passenger-detail-dialog
      :visible.sync="detailDialogVisible"
      :passenger-id="currentPassengerId"
    />

    <!-- 认证审核弹窗 -->
    <passenger-verify-dialog
      :visible.sync="verifyDialogVisible"
      :passenger-id="currentPassengerId"
      @refresh="fetchList"
    />
  </div>
</template>
<script>
import { getPassengers, toggleFreeze, updatePassenger } from '@/api/passenger'
import PassengerDetailDialog from './passengerDetailDialog.vue'
import PassengerVerifyDialog from './passengerVerifyDialog.vue'

export default {
  name: 'PassengerManagement',
  components: {
    PassengerDetailDialog,
    PassengerVerifyDialog
  },
  data() {
    return {
      showMore: false,
      tableHeight: window.innerHeight - 270,
      temp: {
        cellphone: '',
        password: '',
        name: ''
      },
      // 手机号修改表单
      phoneDialogVisible: false,
      phoneTemp: {
        passenger_id: '',
        oldCellphone: '',
        cellphone: ''
      },
      // 详情弹窗
      detailDialogVisible: false,
      // 审核弹窗
      verifyDialogVisible: false,
      currentPassengerId: '',
      phoneRules: {
        cellphone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号格式',
            trigger: 'blur'
          }
        ]
      },
      rules: {
        cellphone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ]
      },
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: [],
      listLoading: false,
      listQuery: {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        is_freeze: null,
        examine_status: null
      }
    }
  },
  created() {
    // 检查URL参数中是否有乘客ID
    const { passengerId } = this.$route.query
    if (passengerId) {
      this.listQuery.passenger_id = passengerId
      // 在mounted钩子中会自动执行fetchList，所以这里不需要再次调用
    }
  },
  mounted() {
    this.fetchList()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.tableHeight = window.innerHeight - 270
    },
    // 获取审核状态类型
    getExamineStatusType(status) {
      switch (status) {
        case 0: return 'success' // 正常
        case 1: return 'warning' // 审核中
        case 2: return 'danger'  // 审核未通过
        case 3: return 'info'    // 资料提交中
        default: return 'info'
      }
    },
    // 获取审核状态文本
    getExamineStatusText(status) {
      switch (status) {
        case 0: return '正常'
        case 1: return '审核中'
        case 2: return '审核未通过'
        case 3: return '资料提交中'
        default: return '未知状态'
      }
    },
    // 打开详情弹窗
    openDetail(row) {
      this.currentPassengerId = row.passenger_id
      this.detailDialogVisible = true
    },
    // 打开审核弹窗
    openVerify(row) {
      this.currentPassengerId = row.passenger_id
      this.verifyDialogVisible = true
    },
    async deleteItem(id, is_freeze) {
      await this.$confirm(`确认${is_freeze === 1 ? '恢复' : '删除'}当前乘客?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await toggleFreeze(id, {
        is_freeze: is_freeze === 1 ? 0 : 1
      })
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        is_freeze: null,
        examine_status: null
      }
      this.showMore = false
      this.$refs.userTable.clearSort()
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      const { data } = await getPassengers(this.listQuery)
      this.list = data && data.data
      this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total
      this.listLoading = false
    },
    resetForm() {
      this.temp = {
        cellphone: '',
        password: '',
        name: ''
      }
    },
    goToBalanceDetail(passengerId) {
      this.$router.push({
        path: '/userManage/passengerManage/passengerWalletProfile',
        query: {
          passenger_id: passengerId
        }
      })
    },
    // 打开修改手机号对话框
    openUpdatePhone(row) {
      this.phoneTemp = {
        passenger_id: row.passenger_id,
        oldCellphone: row.cellphone,
        cellphone: ''
      }
      this.phoneDialogVisible = true
      this.$nextTick(() => {
        this.$refs['phoneForm'].clearValidate()
      })
    },
    // 处理修改手机号
    async handleUpdatePhone() {
      try {
        await this.$refs.phoneForm.validate()
        await updatePassenger(this.phoneTemp.passenger_id, {
          cellphone: this.phoneTemp.cellphone
        })
        this.phoneDialogVisible = false
        this.$notify({
          message: '手机号修改成功',
          type: 'success',
          duration: 2000
        })
        this.fetchList()
      } catch (error) {
        console.error('修改手机号失败:', error)
        // 不在这里显示错误通知，因为请求失败时会自动显示错误提示
        // 避免重复显示错误提示
      }
    },
    // 根据状态筛选乘客
    filterByStatus(status) {
      this.listQuery.examine_status = status
      this.handleFilter()
    }
  }
}
</script>
<style lang="scss" scoped>
.search-wrapper {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;

    .filter-item {
      display: flex;
      align-items: center;

      .el-input {
        width: 200px;
      }

      .el-date-editor {
        width: 260px !important;
      }

      .el-select {
        width: 200px;
      }
    }
  }

  .buttons {
    display: flex;
    justify-content: center;
    padding-top: 4px;
    border-top: 1px solid #f0f0f0;

    .el-button {
      padding: 8px 16px;
      font-size: 13px;

      &--primary {
        background: #0052cc;
        border-color: #0052cc;
      }
    }
  }
}

.component-container {
  margin-top: 16px;
}

.middle-label {
  vertical-align: middle;
}

.balance-link {
  color: #409EFF;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>

<style lang="scss">
.label-key{
  margin-left: 4px;
  margin-right: 8px;
}
.tips{
  font-size: 14px;
  color: #F56C6C;
}
.tips_button{
  font-size: 14px;
  color: #409EFF;
  cursor: pointer;
}
.tips-content{
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}
</style>

<template>
  <el-dialog
    title="乘客认证审核"
    :visible.sync="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    class="passenger-verify-dialog"
    :destroy-on-close="true"
  >
    <div v-loading="loading" class="dialog-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-user" />
          <span>基本信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">乘客编号：</span>
            <span class="value">{{ passengerInfo.passenger_id || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ passengerInfo.cellphone || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">微信昵称：</span>
            <span class="value">{{ passengerInfo.name || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">审核状态：</span>
            <span class="value">
              <el-tag
                :type="getExamineStatusType(passengerInfo.examine_status)"
                effect="plain"
                size="mini"
              >
                {{ getExamineStatusText(passengerInfo.examine_status) }}
              </el-tag>
            </span>
          </div>
        </div>
      </div>

      <!-- 认证信息 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-document-checked" />
          <span>认证信息</span>
          <div class="section-actions" v-if="passengerInfo.examine_status !== 1">
            <el-popconfirm
              title="确定要重置认证状态吗？这将允许用户重新提交认证材料。"
              @confirm="handleReset"
            >
              <el-button slot="reference" type="text" size="mini" :loading="resetLoading">
                <i class="el-icon-refresh-right"></i> 重置认证状态
              </el-button>
            </el-popconfirm>
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">真实姓名：</span>
            <span class="value">{{ passengerInfo.real_name || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份证号：</span>
            <span class="value">{{ passengerInfo.ID_number || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别：</span>
            <span class="value">{{ passengerInfo.gender === 1 ? '男' : passengerInfo.gender === 2 ? '女' : '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">出生日期：</span>
            <span class="value">{{ passengerInfo.birthday || '暂无' }}</span>
          </div>
        </div>
      </div>

      <!-- 学生认证信息（如果有） -->
      <div v-if="hasStudentInfo" class="info-section">
        <div class="section-header">
          <i class="el-icon-reading" />
          <span>学生认证信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">学校：</span>
            <span class="value">{{ passengerInfo.school || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">班级：</span>
            <span class="value">{{ passengerInfo.student_class || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">学生证号：</span>
            <span class="value">{{ passengerInfo.student_id_number || '暂无' }}</span>
          </div>
        </div>
      </div>

      <!-- 证件照片 -->
      <div class="info-section">
        <div class="section-header">
          <i class="el-icon-picture" />
          <span>证件照片</span>
        </div>
        <div class="photo-container">
          <div v-if="passengerInfo.ID_img" class="photo-item">
            <div class="photo-title">身份证照片</div>
            <el-image
              :src="passengerInfo.ID_img"
              fit="cover"
              :preview-src-list="[passengerInfo.ID_img]"
            >
              <div slot="error" class="image-error">
                <i class="el-icon-picture-outline"></i>
                <span>暂无照片</span>
              </div>
            </el-image>
          </div>
          <div v-if="passengerInfo.residence_booklet" class="photo-item">
            <div class="photo-title">户口簿照片</div>
            <el-image
              :src="passengerInfo.residence_booklet"
              fit="cover"
              :preview-src-list="[passengerInfo.residence_booklet]"
            >
              <div slot="error" class="image-error">
                <i class="el-icon-picture-outline"></i>
                <span>暂无照片</span>
              </div>
            </el-image>
          </div>
        </div>
      </div>

      <!-- 审核意见 -->
      <div v-if="passengerInfo.examine_status === 1" class="info-section">
        <div class="section-header">
          <i class="el-icon-chat-dot-square" />
          <span>审核意见</span>
        </div>
        <div class="remark-container">
          <el-form ref="remarkForm" :model="remarkForm" label-width="0">
            <el-form-item v-if="isRejecting" prop="remark" :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
              <el-input
                v-model="remarkForm.remark"
                type="textarea"
                placeholder="请输入拒绝原因（必填）"
                :rows="3"
              />
            </el-form-item>
            <el-form-item v-else>
              <el-input
                v-model="remarkForm.remark"
                type="textarea"
                placeholder="请输入审核备注（选填）"
                :rows="3"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
      <template v-if="passengerInfo.examine_status === 1">
        <el-button
          type="danger"
          @click="handleReject"
          :loading="rejectLoading"
        >
          拒绝认证
        </el-button>
        <el-button
          type="primary"
          @click="handleApprove"
          :loading="approveLoading"
        >
          通过认证
        </el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import { 
  getPassengerDetail, 
  approvePassengerVerification, 
  rejectPassengerVerification,
  resetPassengerVerification
} from '@/api/passenger'

export default {
  name: 'PassengerVerifyDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      approveLoading: false,
      rejectLoading: false,
      resetLoading: false,
      isRejecting: false,
      passengerInfo: {},
      remarkForm: {
        remark: ''
      }
    }
  },
  computed: {
    hasStudentInfo() {
      return !!(this.passengerInfo.school || this.passengerInfo.student_class || this.passengerInfo.student_id_number)
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerId) {
        this.fetchPassengerDetail()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
        this.remarkForm.remark = ''
        this.isRejecting = false
      }
    }
  },
  methods: {
    async fetchPassengerDetail() {
      if (!this.passengerId) return

      this.loading = true
      try {
        const { data } = await getPassengerDetail(this.passengerId)
        this.passengerInfo = data || {}
      } catch (error) {
        console.error('获取乘客详情失败:', error)
        this.$notify({
          title: '错误',
          message: '获取乘客详情失败',
          type: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    getExamineStatusType(status) {
      switch (status) {
        case 0: return 'success' // 正常
        case 1: return 'warning' // 审核中
        case 2: return 'danger'  // 审核未通过
        case 3: return 'info'    // 资料提交中
        default: return 'info'
      }
    },
    getExamineStatusText(status) {
      switch (status) {
        case 0: return '正常'
        case 1: return '审核中'
        case 2: return '审核未通过'
        case 3: return '资料提交中'
        default: return '未知状态'
      }
    },
    closeDialog() {
      this.dialogVisible = false
    },
    async handleApprove() {
      this.approveLoading = true
      try {
        await approvePassengerVerification(this.passengerId, {
          remark: this.remarkForm.remark
        })
        this.$notify({
          title: '成功',
          message: '已通过乘客认证',
          type: 'success'
        })
        this.dialogVisible = false
        this.$emit('refresh')
      } catch (error) {
        console.error('审核操作失败:', error)
      } finally {
        this.approveLoading = false
      }
    },
    handleReject() {
      this.isRejecting = true
      // 验证拒绝理由是否填写
      this.$refs.remarkForm.validate(async (valid) => {
        if (valid) {
          this.rejectLoading = true
          try {
            await rejectPassengerVerification(this.passengerId, {
              remark: this.remarkForm.remark
            })
            this.$notify({
              title: '成功',
              message: '已拒绝乘客认证',
              type: 'success'
            })
            this.dialogVisible = false
            this.$emit('refresh')
          } catch (error) {
            console.error('审核操作失败:', error)
          } finally {
            this.rejectLoading = false
            this.isRejecting = false
          }
        }
      })
    },
    async handleReset() {
      this.resetLoading = true
      try {
        await resetPassengerVerification(this.passengerId, {})
        this.$notify({
          title: '成功',
          message: '已重置乘客认证状态',
          type: 'success'
        })
        await this.fetchPassengerDetail()
        this.$emit('refresh')
      } catch (error) {
        console.error('重置认证状态失败:', error)
      } finally {
        this.resetLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.passenger-verify-dialog {
  ::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  ::v-deep .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .dialog-content {
    max-height: 65vh;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .info-section {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    position: relative;

    i {
      margin-right: 8px;
      color: #409EFF;
      font-size: 16px;
    }

    span {
      font-size: 15px;
      font-weight: 500;
      color: #303133;
    }

    .section-actions {
      position: absolute;
      right: 15px;
      
      .el-button {
        padding: 3px 0;
        margin-left: 10px;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 80px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      flex: 1;
      word-break: break-all;
    }
  }

  .photo-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
  }

  .photo-item {
    width: 250px;

    .photo-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }

    ::v-deep .el-image {
      width: 100%;
      height: 180px;
      border-radius: 4px;
      border: 1px solid #ebeef5;
    }

    .image-error {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      background-color: #f5f7fa;
      color: #909399;

      i {
        font-size: 30px;
        margin-bottom: 8px;
      }
    }
  }

  .remark-container {
    padding: 20px;
  }
}
</style> 

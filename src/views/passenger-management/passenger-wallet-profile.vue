<template>
  <div class="app-container">
    <div class="hanldle-container">
      <!-- <div class="filter-container">
          <el-button
            class="filter-item add"
            style="margin-left: 0"
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleCreate"
          >
            添加乘客
          </el-button>
        </div> -->
    </div>

    <div class="search-wrapper">
      <div class="filter-container">
        <div class="filter-item">
          <el-input
            v-model="listQuery.passenger_id"
            placeholder="乘客编号"
            clearable
            size="small"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="listQuery.name"
            placeholder="乘客昵称"
            clearable
            size="small"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="listQuery.cellphone"
            placeholder="乘客手机"
            clearable
            size="small"
          />
        </div>
        <div class="filter-item">
          <el-select
            v-model="listQuery.type"
            placeholder="交易类型"
            clearable
            size="small"
          >
            <el-option label="全部" :value="null" />
            <el-option label="充值" :value="1" />
            <el-option label="消费" :value="2" />
            <el-option label="退款" :value="3" />
            <el-option label="提现" :value="4" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="listQuery.create_time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            value-format="yyyy-MM-dd"
          />
        </div>
        <div class="filter-item">
          <el-button
            type="primary"
            size="small"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            size="small"
            @click="handleReset"
          >
            重置
          </el-button>
          <el-button
            size="small"
            type="success"
            @click.stop="exportSelectedOrders"
          >
            导出所选
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <el-card class="box-card compact-card">
      <el-row :gutter="10">
        <el-col :span="6">
          <div class="grid-content bg-blue">
            <span>乘客余额总充值</span>
            <strong>¥ {{ statistics.recharge }} 元</strong>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-blue-light">
            <span>乘客余额总消费</span>
            <strong>¥ {{ statistics.expense }} 元</strong>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-blue-dark">
            <span>乘客余额总余额</span>
            <strong>¥ {{ statistics.balance }} 元</strong>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-blue-grey">
            <span>乘客余额总退款</span>
            <strong>¥ {{ statistics.refund }} 元</strong>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      :max-height="tableHeight"
      size="mini"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        width="40"
        align="center"
      />
      <el-table-column
        label="ID"
        prop="id"
        width="60"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="乘客昵称"
        prop="name"
        width="140"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="driver-name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="乘客手机"
        prop="cellphone"
        width="120"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div>
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.cellphone || '暂无' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="交易类型"
        prop="type_text"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.type_text === '充值' ? 'success' : scope.row.type_text === '消费' ? 'danger' : scope.row.type_text === '退款' ? 'warning' : 'info'"
          >
            {{ scope.row.type_text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="交易金额（元）"
        prop="formatted_amount"
        width="120"
        align="center"
      />
      <el-table-column
        label="当前余额"
        prop="after_balance"
        width="100"
        align="center"
      />

      <el-table-column
        label="交易状态"
        prop="status"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'danger' : 'warning'"
          >
            {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '失败' : '处理中' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="交易时间"
        prop="created_at"
        width="160"
        align="center"
      />
      <el-table-column
        label="交易备注"
        prop="remark"
        align="left"
      >
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.remark" placement="top">
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">
              {{ scope.row.remark }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.size"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增乘客' : '修改乘客'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="900px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="right"
        label-width="120px"
        size="medium"
        inline
      >
        <el-form-item label="手机号" prop="cellphone">
          <el-input
            v-model="temp.cellphone"
            placeholder="请输入手机号"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入昵称"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input
            v-model="temp.password"
            placeholder="默认密码为手机号码后6位"
            style="width: 260px"
            show-password
            type="password"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : handleUpdate()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPassengerWallets, exportPassengerWallets, handlePassengerWalletAction } from '@/api/passenger'

export default {
  name: 'PassengerManagement',
  data() {
    return {
      showMore: false,
      tableHeight: window.innerHeight - 270,
      temp: {
        cellphone: '',
        password: '',
        name: ''
      },
      rules: {
        cellphone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ]
      },
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: null,
      listLoading: false,
      listQuery: {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        driver_id: null,
        is_freeze: null,
        passenger_id: this.$route.query.passenger_id || null // 从URL中获取passenger_id
      },
      statistics: {
        recharge: 0,
        expense: 0,
        balance: 0,
        refund: 0
      },
      selectedOrders: [] // 初始化为空数组
    }
  },
  mounted() {
    this.fetchList()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    confirmApprove(row) {
      this.$confirm('确认同意该笔提现吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleApprove(row)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    confirmReject(row) {
      this.$prompt('请输入拒绝原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '拒绝原因不能为空'
      }).then(({ value }) => {
        this.handleReject(row, value)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    async handleApprove(row) {
      try {
        console.log('同意提现:', row)
        const response = await handlePassengerWalletAction(row.id, 'approve')
        if (response.code === 200) {
          this.$message.success('提现已同意')
          this.fetchList()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('同意提现失败:', error)
        this.$message.error('操作失败，请稍后重试')
      }
    },
    async handleReject(row, reason) {
      try {
        console.log('拒绝提现:', row, '原因:', reason)
        const response = await handlePassengerWalletAction(row.id, 'reject', { approve_reason: reason })
        if (response.code === 200) {
          this.$message.success('提现已拒绝')
          this.fetchList()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('拒绝提现失败:', error)
        this.$message.error('操作失败，请稍后重试')
      }
    },
    // 导出所选订单
    async exportSelectedOrders() {
      if (!this.selectedOrders.length && Object.keys(this.listQuery).length === 0) {
        this.$message.warning('请选择交易或设置筛选条件后再导出。')
        return
      }

      const confirmationMessage = this.selectedOrders.length
        ? `确认导出已选中的 ${this.selectedOrders.length} 条订单吗？`
        : '确认导出当前筛选条件下的所有交易吗？'

      await this.$confirm(confirmationMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const { data } = await exportPassengerWallets({
        ...this.listQuery,
        ids: this.selectedOrders
      })
      if (data && data.url) {
        window.open(data.url)
        this.$message.success('导出成功，文件正在下载。')
      } else {
        this.$message.error('导出失败，未获取到文件链接。')
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 270
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        driver_id: null,
        is_freeze: null,
        passenger_id: this.$route.query.passenger_id || null // 重置时也从URL中获取passenger_id
      }
      this.showMore = false
      this.$refs.userTable.clearSort()
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      const { data } = await getPassengerWallets(this.listQuery)
      this.list = data && data.data
      this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total

      // 更新统计信息
      if (data && data.meta) {
        this.statistics.recharge = data.meta.total_recharge
        this.statistics.expense = data.meta.total_consume
        this.statistics.balance = data.meta.total_balance
        this.statistics.refund = data.meta.total_refund
      }

      this.listLoading = false
    },
    resetForm() {
      this.temp = {
        cellphone: '',
        password: '',
        name: ''
      }
    }
  }
}
</script>
  <style lang="scss" scoped>
  .component-container {
    margin-top: 24px;
  }
  .middle-label {
    vertical-align: middle;
  }
  .filter-item {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    .label-key {
      min-width: 70px;
      text-align: right;
      margin-right: 8px;
    }
    .filter-input {
      flex: 1;
    }
  }
  .filter-buttons {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .filter-item {
      margin-right: 10px;
    }
  }
  </style>

  <style lang="scss">
  .label-key{
    margin-left: 4px;
    margin-right: 8px;
  }
  .tips{
    font-size: 14px;
    color: #F56C6C;
  }
  .tips_button{
    font-size: 14px;
    color: #409EFF;
    cursor: pointer;
  }
  .tips-content{
    font-size: 14px;
    color: #606266;
    margin-top: 10px;
  }
  </style>

  <style lang="scss" scoped>
  .compact-card {
    margin-bottom: 10px;
  }

  .grid-content {
    text-align: center;
    border-radius: 4px;
    min-height: 40px;
    line-height: 40px;
    color: #fff;
    font-size: 16px;
    span {
      display: block;
      font-size: 12px;
    }
  }
  .bg-blue {
    background: #67C23A;
  }
  .bg-blue-light {
    background:#E6A23C;
  }
  .bg-blue-dark {
    background: #F56C6C;
  }
  .bg-blue-grey {
    background:#909399;
  }
  </style>

  <style lang="scss" scoped>
  .search-wrapper {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;

    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 16px;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-input {
          width: 200px;
        }

        .el-date-editor {
          width: 260px !important;
        }

        .el-select {
          width: 200px;
        }

        .el-button {
          margin: 0;
          padding: 8px 16px;
          font-size: 13px;

          &--primary {
            background: #0052cc;
            border-color: #0052cc;
          }
        }
      }
    }
  }
  </style>

<template>
  <div class="app-container">
    <div class="hanldle-container">
      <!-- <div class="filter-container">
            <el-button
              class="filter-item add"
              style="margin-left: 0"
              type="text"
              icon="el-icon-circle-plus-outline"
              @click="handleCreate"
            >
              添加乘客
            </el-button>
          </div> -->
    </div>

    <div class="hanldle-container">
      <div class="filter-container">
        <span class="label-key">乘客编号</span>
        <el-input
          v-model="listQuery.passenger_id"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <span class="label-key">乘客昵称</span>
        <el-input
          v-model="listQuery.name"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <span class="label-key">乘客手机</span>
        <el-input
          v-model="listQuery.cellphone"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />

        <span class="label-key">交易时间</span>
        <el-date-picker
          v-model="listQuery.create_time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          value-format="yyyy-MM-dd"
          style="width: 260px"
        />

        <el-button
          class="filter-item"
          type="primary"
          size="small"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          @click="handleReset"
        >
          重置
        </el-button>

      </div>
    </div>

    <div v-show="showMore" class="hanldle-container">
      <div class="filter-container" />
    </div>

    <!-- 统计信息卡片 -->
    <el-card class="box-card compact-card">
      <el-row :gutter="10">
        <el-col :span="8">
          <div class="grid-content" style="background-color: #67C23A;"> <!-- 移除了clickable类和点击事件 -->
            <span>提现总通过金额</span>
            <strong>¥ {{ statistics.totalApprovedAmount }} 元</strong>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content clickable" style="background-color: #E6A23C;" @click="handleShowPending()"> <!-- 橙色 -->
            <span>提现总待审核金额</span>
            <strong class="underline-text">¥ {{ statistics.totalPendingAmount }} 元</strong> <!-- 添加了underline-text类 -->
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content" style="background-color: #F56C6C;"> <!-- 移除了clickable类和点击事件 -->
            <span>提现总驳回金额</span>
            <strong>¥ {{ statistics.totalRejectedAmount }} 元</strong>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 新增：审核状态Tab切换 -->
    <el-tabs v-model="activeTabName" type="card" class="status-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name="all" />
      <el-tab-pane label="待审核" name="pending">
        <span slot="label"><el-badge v-if="pendingCount > 0" :value="pendingCount" :max="99">待审核</el-badge><span v-else>待审核</span></span>
      </el-tab-pane>
      <el-tab-pane label="审核通过" name="approved" />
      <el-tab-pane label="审核失败" name="rejected" />
    </el-tabs>

    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      :max-height="tableHeight"
      size="mini"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        width="60"
        align="center"
      />

      <el-table-column
        label="ID"
        prop="id"
        width="60"
        align="center"
      />

      <el-table-column
        label="乘客昵称"
        prop="name"
        width="140"
        align="center"
      >
        <template slot-scope="scope">
          <span class="driver-name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="乘客手机"
        prop="cellphone"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <div>
            <i class="el-icon-mobile-phone" />
            <span>{{ scope.row.cellphone || '暂无' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="交易类型"
        prop="type_text"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.type_text === '充值' ? 'success' : scope.row.type_text === '消费' ? 'danger' : scope.row.type_text === '退款' ? 'warning' : 'info'"
          >
            {{ scope.row.type_text }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="交易金额（元）"
        prop="amount"
        width="120"
        align="center"
      />

      <el-table-column
        label="审核状态"
        prop="approve_status"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.approve_status === 1 ? 'success' : scope.row.approve_status === 2 ? 'danger' : 'warning'"
          >
            {{ scope.row.approve_status === 1 ? '审核通过' : scope.row.approve_status === 2 ? '审核失败' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="审核时间"
        prop="approve_at"
        width="160"
        align="center"
      />

      <el-table-column
        label="拒绝原因"
        prop="approve_reason"
        align="left"
      >
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.approve_reason" placement="top">
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">
              {{ scope.row.approve_reason }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column
        label="审核人"
        prop="approve_user"
        width="120"
        align="center"
      />

      <el-table-column
        class-name="status-col"
        label="操作"
        width="180"
        fixed="right"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.type === 4 && scope.row.approve_status === 0">
            <el-button
              class="filter-item"
              type="text"
              size="medium"
              @click="confirmApprove(scope.row)"
            >
              同意提现
            </el-button>
            <el-divider direction="vertical" />
            <el-button
              class="filter-item"
              type="text"
              size="medium"
              @click="confirmReject(scope.row)"
            >
              拒绝提现
            </el-button>
          </template>
        </template>
      </el-table-column>

    </el-table>

    <div class="bottom-container">
      <div class="bottom-container-left" />
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.size"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog
      :title="dialogStatus === 'create' ? '新增乘客' : '修改乘客'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="900px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="right"
        label-width="120px"
        size="medium"
        inline
      >
        <el-form-item label="手机号" prop="cellphone">
          <el-input
            v-model="temp.cellphone"
            placeholder="请输入手机号"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入昵称"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input
            v-model="temp.password"
            placeholder="默认密码为手机号码后6位"
            style="width: 260px"
            show-password
            type="password"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : handleUpdate()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPassengerWallets, exportPassengerWallets, handlePassengerWalletAction, getPassengerWalletStats } from '@/api/passenger'

export default {
  name: 'PassengerManagement',
  data() {
    return {
      showMore: false,
      tableHeight: window.innerHeight - 320,
      activeTabName: 'all',
      pendingCount: 0,
      temp: {
        cellphone: '',
        password: '',
        name: ''
      },
      rules: {
        cellphone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ]
      },
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: [],
      listLoading: false,
      listQuery: {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        type: 4,
        passenger_id: this.$route.query.passenger_id || null,
        approve_status: null
      },
      statistics: {
        totalApprovedAmount: 0,
        totalPendingAmount: 0,
        totalRejectedAmount: 0
      },
      selectedOrders: []
    }
  },
  mounted() {
    this.fetchStatistics()
    this.fetchList()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleTabClick(tab) {
      const tabNameMap = {
        all: null,
        pending: 0,
        approved: 1,
        rejected: 2
      }
      this.listQuery.approve_status = tabNameMap[this.activeTabName]
      this.listQuery.page = 1
      this.fetchList()
    },
    handleShowPending() {
      this.activeTabName = 'pending'
      this.handleTabClick()
    },
    async fetchStatistics() {
      try {
        const { data } = await getPassengerWalletStats()

        if (data && data.meta) {
          this.statistics.totalApprovedAmount = data.meta.total_approve_amount || 0
          this.statistics.totalPendingAmount = data.meta.total_pending_amount || 0
          this.statistics.totalRejectedAmount = data.meta.total_reject_amount || 0

          if (data.meta.total_pending_amount > 0) {
            const avgAmount = 0.5
            const estimatedCount = Math.ceil(data.meta.total_pending_amount / avgAmount)
            this.pendingCount = Math.min(estimatedCount, 99)
          } else {
            this.pendingCount = 0
          }
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },
    confirmApprove(row) {
      this.$confirm('确认同意该笔提现吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleApprove(row)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    confirmReject(row) {
      this.$prompt('请输入拒绝原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '拒绝原因不能为空'
      }).then(({ value }) => {
        this.handleReject(row, value)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    async handleApprove(row) {
      try {
        console.log('同意提现:', row)
        const response = await handlePassengerWalletAction(row.id, 'approve')
        if (response.code === 200) {
          this.$message.success('提现已同意')
          this.fetchList()
          this.fetchStatistics()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('同意提现失败:', error)
        this.$message.error('操作失败，请稍后重试')
      }
    },
    async handleReject(row, reason) {
      try {
        console.log('拒绝提现:', row, '原因:', reason)
        const response = await handlePassengerWalletAction(row.id, 'reject', { approve_reason: reason })
        if (response.code === 200) {
          this.$message.success('提现已拒绝')
          this.fetchList()
          this.fetchStatistics()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('拒绝提现失败:', error)
        this.$message.error('操作失败，请稍后重试')
      }
    },
    async exportSelectedOrders() {
      if (!this.selectedOrders.length && Object.keys(this.listQuery).length === 0) {
        this.$message.warning('请选择交易或设置筛选条件后再导出。')
        return
      }

      const confirmationMessage = this.selectedOrders.length
        ? `确认导出已选中的 ${this.selectedOrders.length} 条订单吗？`
        : '确认导出当前筛选条件下的所有交易吗？'

      await this.$confirm(confirmationMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const { data } = await exportPassengerWallets({
        ...this.listQuery,
        ids: this.selectedOrders
      })
      if (data && data.url) {
        window.open(data.url)
        this.$message.success('导出成功，文件正在下载。')
      } else {
        this.$message.error('导出失败，未获取到文件链接。')
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 320
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        size: 15,
        name: null,
        cellphone: null,
        type: 4,
        passenger_id: this.$route.query.passenger_id || null,
        approve_status: null
      }
      this.activeTabName = 'all'
      this.showMore = false
      this.$refs.userTable.clearSort()
      this.fetchList()
      this.fetchStatistics()
    },
    async fetchList() {
      this.listLoading = true

      const queryParams = { ...this.listQuery }

      const { data } = await getPassengerWallets(queryParams)
      this.list = data && data.data
      this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total

      this.listLoading = false
    },
    resetForm() {
      this.temp = {
        cellphone: '',
        password: '',
        name: ''
      }
    }
  }
}
</script>
    <style lang="scss" scoped>
    .component-container {
      margin-top: 24px;
    }
    .middle-label {
      vertical-align: middle;
    }
    </style>

    <style lang="scss">
    .label-key{
      margin-left: 4px;
      margin-right: 8px;
    }
    .tips{
      font-size: 14px;
      color: #F56C6C;
    }
    .tips_button{
      font-size: 14px;
      color: #409EFF;
      cursor: pointer;
    }
    .tips-content{
      font-size: 14px;
      color: #606266;
      margin-top: 10px;
    }
    </style>

    <style lang="scss" scoped>
    .compact-card {
      margin-bottom: 10px;
    }

    .grid-content {
      text-align: center;
      border-radius: 4px;
      min-height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 16px;
      span {
        display: block;
        font-size: 12px;
      }
    }
    .bg-blue {
      background: #67C23A;
    }
    .bg-blue-light {
      background:#E6A23C;
    }
    .bg-blue-dark {
      background: #F56C6C;
    }
    .bg-blue-grey {
      background:#909399;
    }
    .underline-text {
      text-decoration: underline;
      cursor: pointer;
    }

    .status-tabs {
      margin-bottom: 10px;
    }
    </style>

<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button
          class="filter-item add"
          style="margin-left: 0"
          type="text"
          icon="el-icon-circle-plus-outline"
          @click="handleCreate"
        >
          添加活动
        </el-button>
      </div>
      <div class="filter-container">
        <span class="label-key">活动状态</span>
        <el-select
          v-model="listQuery.enabled"
          placeholder="请选择"
          class="mini-condition"
          clearable
          size="small"
        >
          <el-option label="开启" :value="true" />
          <el-option label="关闭" :value="false" />
        </el-select>
        <span class="label-key">关键字搜索</span>
        <el-input
          v-model="listQuery.keywords"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          @click="handleReset"
        >
          重置
        </el-button>
      </div>
    </div>
    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      height="calc(100vh - 240px)"
      size="small"
      style="width: 100%"
      align="center"
    >
      <el-table-column
        label="活动ID"
        width="100"
        prop="market_activity_id"
        class-name="isFirst"
      />
      <el-table-column label="活动名称" width="180" prop="name" />
      <el-table-column label="活动类型" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.type === 5"
            size="mini"
            type="success"
          >用户首单</el-tag>
          <el-tag v-else size="mini" type="info">通用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="活动奖励" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row?.coupon?.name }}
        </template>
      </el-table-column>
      <el-table-column label="活动时间" width="290" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.start_time }} - {{ scope.row.end_time }}
        </template>
      </el-table-column>
      <el-table-column label="活动状态" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <Tablestatus
            v-if="scope.row.enabled"
            mode="processing"
            word="开启中"
          />
          <Tablestatus v-else word="已关闭" />
        </template>
      </el-table-column>
      <el-table-column
        label="活动说明"
        min-width="180"
        prop="summary"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope?.row?.summary || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="乘客限领次数" width="110" prop="rule" />
      <el-table-column label="发放方式" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.give_out_type === 1"
            size="mini"
            type="info"
          >所有用户</el-tag>
          <el-tag v-else size="mini" type="warning">平台指定用户</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="领取方式" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.claim_reward_way === 1"
            size="mini"
            effect="plain"
            type="danger"
          >手动领取</el-tag>
          <el-tag v-else size="mini" type="" effect="plain">自动领取</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发放总数" width="100" prop="total_count" />
      <el-table-column label="已领取总数" width="100" prop="recieve_count" />
      <el-table-column label="已使用总数" width="100" prop="use_count" />
      <el-table-column label="创建时间" width="180" prop="create_time" />
      <el-table-column label="更新时间" width="180" prop="update_time" />
      <el-table-column
        class-name="status-col"
        label="操作"
        width="205"
        fixed="right"
      >
        <template slot-scope="scope">
          <record :promotion="scope.row.market_activity_id" />
          <el-divider direction="vertical" />
          <Provide :params="scope.row" @refresh="fetchList" />
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="small"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="small"
            @click="couponsDelete(scope.row.market_activity_id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.per_page"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增活动' : '修改活动'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="800px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="right"
        label-width="100px"
        size="medium"
      >
        <el-form-item label="活动名称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item label="活动类型" prop="type">
          <el-radio-group v-model="temp.type" size="small" @change="typeChange">
            <el-radio
              v-for="item in typeOptions"
              :key="item.value"
              :disabled="item.disabled"
              :label="item.value"
              border
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="奖励方式" prop="award_type">
          <el-select
            v-model="temp.award_type"
            placeholder="请选择"
            style="width: 400px"
          >
            <el-option
              v-for="item in award_types"
              :key="item.value"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励内容" prop="coupon_id">
          <el-select
            v-model="temp.coupon_id"
            filterable
            remote
            reserve-keyword
            :placeholder="
              coupon_list.length === 0 ? '暂无优惠券，请先添加优惠券' : '请选择'
            "
            style="width: 400px"
            :remote-method="remoteMethod"
            :loading="loading"
          >
            <el-option
              v-for="item in coupon_list"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            type="primary"
            size="small"
            style="margin-left: 10px"
            icon="el-icon-circle-plus-outline"
            @click="$router.push({ name: 'coupon' })"
          >添加优惠券</el-button>
        </el-form-item>
        <el-form-item label="发放方式" prop="give_out_type">
          <el-radio-group
            v-model="temp.give_out_type"
            :disabled="dialogStatus !== 'create'"
          >
            <el-radio :label="1">所有用户</el-radio>
            <el-radio :label="2">平台指定用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="领取方式" prop="claim_reward_way">
          <el-radio-group
            v-model="temp.claim_reward_way"
            :disabled="dialogStatus !== 'create'"
          >
            <el-radio :label="1">手动领取</el-radio>
            <el-radio :label="2">自动领取</el-radio>
          </el-radio-group>
          <span
            class="label-key middle-label"
          >（*发放方式、领取方式一旦保存无法后无法修改）</span>
        </el-form-item>
        <el-form-item label="乘客每人限领次数" prop="rule" class="dubleLine">
          <el-input-number
            v-model="temp.rule"
            style="width: 150px"
            placeholder="请输入"
            controls-position="right"
            :min="1"
            :max="100"
          />
        </el-form-item>
        <el-form-item label="营销经费" prop="type">
          <el-radio-group v-model="funds" size="small">
            <el-radio :label="1">商户承担优惠费用</el-radio>
            <el-radio
              disabled
              :label="2"
            >司机承担优惠费用(暂未开放)</el-radio>
          </el-radio-group>
          <el-popover class="item" trigger="hover" placement="top-start">
            <div>
              <p>商户承担优惠费用：优惠券减免金额由商户补贴，司机订单收入不变。</p>
              <p>司机承担优惠费用：优惠券减免金额由司机承担，司机订单收入受影响。</p>
            </div>
            <i
              slot="reference"
              class="el-icon-question"
              style="margin-left: 10px"
            />
          </el-popover>
        </el-form-item>
        <el-form-item label="是否开启" prop="enabled">
          <el-radio-group v-model="temp.enabled">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="活动时间" prop="valid_time">
          <el-date-picker
            v-model="temp.valid_time"
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="活动说明" prop="summary">
          <el-input
            v-model="temp.summary"
            class="trainsInput"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : updateCoupon()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import user from './user'
import Record from './components/Record'
import Provide from './components/Provide'
import { promotions, delPromotions, addPromotions, promotionsUpdate, valCoupons } from '@/api/marketing'
const normal_awards = [
  { value: 4, label: '直接发放一张面值N元的优惠券' },
  { value: 6, label: '订单金额满足时，赠送N个积分', disabled: true }
]
const first_awards = [
  { value: 4, label: '当用户首次下单时，赠送一张面值N元的优惠券' }
]
export default {
  name: 'Activities',
  components: {
    Record,
    Provide
  },
  data() {
    const validateValid = (rule, value, callback) => {
      if (!this.temp.valid_time || !this.temp.valid_time.length === 0) {
        return callback(new Error('请设置活动时间'))
      } else {
        return callback()
      }
    }
    return {
      funds: 1, // 营销经费默认
      loading: false,
      temp: {
        type: -1
      },
      rules: {
        // give_out_type: [{ required: true, message: '请选择', trigger: 'blur' }],
        // claim_reward_way: [{ required: true, message: '请选择', trigger: 'blur' }],
        name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        rule: [{ required: true, message: '请输入', trigger: 'blur' }],
        valid_time: [{ validator: validateValid, trigger: 'blur', required: true }],
        award_type: [{ required: true, message: '请选择', trigger: 'change' }],
        coupon_id: [{ required: true, message: '请选择', trigger: 'change' }],
        type: [{ required: true, message: '请选择活动类型', trigger: 'change' }]
      },
      typeOptions: [
        { value: -1, label: '通用' },
        { value: 5, label: '用户首单' },
        { value: 6, label: '新用户注册', disabled: true },
        { value: 7, label: '邀请新用户下单', disabled: true }
      ],
      award_types: normal_awards,
      coupon_list: [],
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: null,
      listLoading: false,
      listQuery: {
        page: 1,
        per_page: 15,
        keywords: undefined
      }
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    typeChange(e) {
      this.award_types = e === -1 ? normal_awards : first_awards
      this.$set(this.temp, 'award_type', this.award_types[0]?.value)
    },
    async remoteMethod(query = undefined) {
      this.loading = true
      const { data } = await valCoupons({ keywords: query })
      this.loading = false
      if (Array.isArray(data)) {
        this.coupon_list = data.map((o) => {
          return { label: `${o.name} （${Number(o.rule) > 0 ? `满${o.rule}元` : '无门槛'},${o.type === 1 ? `立减${o.value}元` : `立享${o.value}折`}）`, value: o.coupon_id }
        })
      } else {
        this.coupon_list = []
      }
    },
    async couponsDelete(id) {
      await this.$confirm('确认删除当前活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await delPromotions(id)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.remoteMethod()
      this.temp = Object.assign({}, row)
      this.$set(this.temp, 'valid_time', row.start_time ? [row.start_time, row.end_time] : [])
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleScope(val) {
      const arr = val.split(',')
      const arrTag = this.typeOptions.filter((ele) => {
        const isExist = arr.some((Element) => {
          return Number(Element) === ele.value
        })
        if (isExist) {
          return ele
        }
      })
      if (arrTag.length > 0) {
        return arrTag
      }
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.temp = {
        type: -1,
        award_type: 4,
        enabled: 1,
        give_out_type: 1,
        rule: 1,
        claim_reward_way: 1
      }
      this.remoteMethod()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleAdd() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({}, this.temp)
      params.start_time = params.valid_time[0]
      params.end_time = params.valid_time[1]
      await addPromotions(params)
      this.dialogVisible = false
      this.$notify({
        message: '添加成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async updateCoupon() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({}, this.temp)
      params.start_time = params.valid_time[0]
      params.end_time = params.valid_time[1]
      await promotionsUpdate(params)
      this.dialogVisible = false
      this.$notify({
        message: '编辑成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        keyword: undefined,
        type: undefined,
        valid_category: undefined,
        status: undefined
      }
      this.$refs.userTable.clearSort()
      this.date = []
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      const { data } = await promotions(this.listQuery)
      this.list = data?.data
      this.total = data?.meta?.pagination?.total
      this.listLoading = false
    },
    positive(val) {
      const reg = /^[+]{0,1}(\d+)$/
      const len = val.length
      let str = ''
      for (var i = 0; i < len; i++) {
        if (reg.test(val[i])) {
          str += val[i]
        }
      }
      return Number(str)
    }
  }
}
</script>
<style lang="scss" scoped>
.component-container {
  margin-top: 24px;
}
</style>

<style lang="scss">
.scope_type {
  .el-checkbox {
    margin-right: 12px;
  }
  .el-checkbox__label {
    font-size: 12px;
    padding-left: 5px;
  }
}
.dubleLine {
  .el-form-item__label {
    line-height: 20px;
  }
}
.label-key{
  margin-left: 3px;
}
</style>

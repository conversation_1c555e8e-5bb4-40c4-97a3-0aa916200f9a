<template>
  <div style="display: inline-block;">
    <el-button
      type="text"
      size="small"
      class="filter-item"
      @click="selectUser"
    >
      发放
    </el-button>
    <el-dialog
      :title="params.give_out_type===1?'发放优惠券':'发放优惠券至指定用户'"
      :visible.sync="recordVisible"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="cancel"
    >
      <el-descriptions v-if="params.give_out_type===2">
        <el-descriptions-item label="下载模板"> <el-link type="primary" icon="el-icon-download" :underline="false" @click="downloadDoc">下载模板</el-link></el-descriptions-item>
      </el-descriptions>
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="90px"
        size="medium"
      >
        <el-form-item v-if="params.give_out_type===1" label="发放总数" prop="nums">
          <el-input-number v-model="temp.nums" style="width: 150px" placeholder="请输入" controls-position="right" :min="1" :max="10000" /><span class="label-key middle-label">（*最大10000张）</span>
        </el-form-item>
        <el-form-item v-else label="导入文件" prop="excel">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            name="excel"
            :auto-upload="false"
            :file-list="fileList"
            :action="actions"
            :headers="headers"
            :on-success="upSuccess"
            :on-error="upError"
            :on-change="handleChange"
            :before-upload="beforeUpload"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">
              *只能上传模板样式Excel文件
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="el-upload__tip">
        <p class="table-p">1.手机号已经是注册过的用户</p>
        <p class="table-p">2.发放总数 = 发放人数 × 限领次数</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel"> 取消 </el-button>
        <el-button type="primary" size="small" @click="handleSend">  确认 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { provideCoupons } from '@/api/marketing'
import { getToken } from '@/utils/auth'

export default {
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      fileList: [],
      temp: {},
      rules: {
        nums: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      recordVisible: false
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer' + getToken()
      }
    },
    actions() {
      return process.env.VUE_APP_BASE_API + `/admin/promotions/${this.params.market_activity_id}/excels`
    }
  },
  methods: {
    downloadDoc() {
      const a = document.createElement('a')
      a.href = './documents/coupon_user_template.xlsx'
      a.download = 'coupon_user_template.xlsx'
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      a.remove()
    },

    beforeUpload(file) {
    // 后缀是xls、xlsx
      const extension = file.name.split('.')[1] === 'xls'
      const extension2 = file.name.split('.')[1] === 'xlsx'
      // 文件大小
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!extension && !extension2) {
        this.$message.error('上传文件只能是 xls、xlsx 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
    },
    handleChange (file, fileList) {
      this.fileList = fileList
    },
    async  handleSend() {
      if (this.params?.give_out_type === 1) {
        await this.$refs.dataForm.validate()
        await provideCoupons({ ...this.temp, promotion: this.params.market_activity_id })
        this.$notify({
          message: '发放成功',
          type: 'success',
          duration: 2000
        })
        this.$emit('refresh')
        this.recordVisible = false
        return
      }
      if (this.fileList.length === 0) {
        this.$message.error('请选择上传文件')
        return
      }
      this.$refs.upload.submit()
    },
    upSuccess(response, file, fileList) {
      console.log(response)
      this.$notify({
        message: '发放成功',
        type: 'success',
        duration: 2000
      })
      this.$emit('refresh')
      this.recordVisible = false
    },
    upError(err, file, fileList) {
      console.log(err)
      this.$notify({
        message: '发放失败请选择正确的文件',
        type: 'error',
        duration: 2000
      })
    },
    selectUser() {
      this.recordVisible = true
      this.temp = {}
      this.fileList = []
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    cancel() {
      this.recordVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.user-container{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  .user_num {
    display: flex;
    justify-content: flex-start;
    p {
      margin-right: 12px;
      font-style: 14px;
    }
  }
}
.head_ico {
  width: 24px;
  height: 24px;
  object-fit: cover;
}
.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
}
</style>

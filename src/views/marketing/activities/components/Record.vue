<template>
  <div style="display: inline-block">
    <el-button type="text" size="small" class="filter-item" @click="selectUser">
      领取记录
    </el-button>
    <el-dialog
      title="用户领取记录"
      :visible.sync="recordVisible"
      width="1000px"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="cancel"
    >
      <el-table
        v-loading="userLoading"
        :data="recordList"
        element-loading-text=""
        :height="`600px`"
        fit
        highlight-current-row
        size="small"
      >
        <el-table-column label="编号" width="80" prop="coupon_record_id" />
        <el-table-column
          label="活动名称"
          width="180"
          show-overflow-tooltip
          prop="market_activity.name"
        />
        <el-table-column label="优惠券名称" width="200" prop="name" />
        <el-table-column label="领取用户" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <p class="table-p">
              昵称：{{ scope.row.passenger?.name || "-" }}
            </p>
            <p class="table-p">
              手机号：{{ scope.row.passenger?.cellphone || "-" }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <Tablestatus
              v-if="scope.row.status === 0"
              mode="warning"
              word="未使用"
            />
            <Tablestatus
              v-else-if="scope.row.status === 1"
              mode="default"
              word="已使用"
            />
            <Tablestatus v-else word="已过期" />
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="290" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.start_time }} - {{ scope.row.end_time }}
          </template>
        </el-table-column>
        <el-table-column label="领取时间" width="160" prop="receive_time" />
        <el-table-column
          class-name="status-col"
          label="操作"
          width="55"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              class="filter-item"
              type="text"
              size="small"
              @click="logDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom-container">
        <div class="left-content" />
        <el-pagination
          v-if="usertotal > 0"
          layout="prev, pager, next"
          background
          :page-size="userQuery.per_page"
          :total="usertotal"
          :current-page="userQuery.page"
          style="text-align: right"
          @current-change="userChanges"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { couponsRecord, delLogs } from '@/api/marketing'
export default {
  props: {
    promotion: {
      type: Number
    }
  },
  data() {
    return {
      userQuery: {
        page: 1,
        per_page: 15,
        promotion: ''
      },
      data: {},
      userLoading: false,
      recordList: [],
      usertotal: 0,
      recordVisible: false
    }
  },
  methods: {
    userChanges(val) {
      this.userQuery.page = val
      this.getUsers()
    },
    selectUser() {
      this.recordVisible = true
      this.userQuery.promotion = this.promotion
      this.getUsers()
    },
    getUsers() {
      this.userLoading = true
      couponsRecord(this.userQuery).then((response) => {
        console.log(response)
        if (response.code === 200) {
          this.recordList = Array.isArray(response.data) ? response.data : []
          //   this.usertotal = response.data.meta.total
        }
        this.userLoading = false
      })
    },
    async logDelete(params) {
      await this.$confirm('确认删除当前领取记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await delLogs({
        promotion: params.market_activity_id,
        coupon: params.coupon_record_id
      })
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.getUsers()
    },
    cancel() {
      this.recordVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.user-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  .user_num {
    display: flex;
    justify-content: flex-start;
    p {
      margin-right: 12px;
      font-style: 14px;
    }
  }
}
.head_ico {
  width: 24px;
  height: 24px;
  object-fit: cover;
}
</style>

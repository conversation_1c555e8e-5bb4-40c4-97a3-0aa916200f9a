<template>
  <div style="display: inline-block">
    <el-button type="text" size="small" class="filter-item" @click="selectUser">
      查看详情
    </el-button>
    <el-dialog title="优惠券详情" :visible.sync="recordVisible" width="1100px" :close-on-click-modal="false"
      :append-to-body="true" @close="cancel">
      <div class="hanldle-container">
        <div class="filter-container">
          <el-button class="filter-item" size="mini" type="primary" icon="el-icon-printer" @click="handleDownload">
            导出Excel
          </el-button>
        </div>
        <div class="filter-container">
          <div class="item-filter">
            <span class="label-key">参与活动</span>
            <el-select v-model="logQuery.marketing_activity_id" placeholder="请选择" class="mini-condition" clearable
              size="small" @change="handleFilter">
              <el-option v-for="(item, i) in options" :key="i" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="item-filter">
            <span class="label-key">使用状态</span>
            <el-select v-model="logQuery.status" placeholder="请选择" class="mini-condition" clearable size="small"
              @change="handleFilter">
              <el-option label="未使用" :value="0" />
              <el-option label="已使用" :value="1" />
              <el-option label="已过期" :value="2" />
            </el-select>
          </div>
          <div class="item-filter">
            <span class="label-key">有效期</span>
            <el-date-picker v-model="logQuery.valid_at" size="small" clearable @change="handleFilter" type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss" class="daterange-condition" range-separator="至" start-placeholder="日期开始"
              end-placeholder="日期结束" unlink-panels />
          </div>
        </div>
      </div>
      <div class="hanldle-container">
        <div class="filter-container"></div>
        <div class="filter-container">
          <div class="item-filter">
            <span class="label-key">领取时间</span>
            <el-date-picker v-model="logQuery.receive_time" size="small" type="daterange" clearable @change="handleFilter"
              value-format="yyyy-MM-dd HH:mm:ss" class="daterange-condition" range-separator="至" start-placeholder="日期开始"
              end-placeholder="日期结束" unlink-panels />
          </div>
          <div class="item-filter">
            <span class="label-key">使用时间</span>
            <el-date-picker v-model="logQuery.use_time" size="small" clearable type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss" class="daterange-condition" range-separator="至" start-placeholder="日期开始"
              end-placeholder="日期结束" unlink-panels @change="handleFilter" />
          </div>

          <el-button class="filter-item" type="primary" size="small" @click="handleFilter">
            搜索
          </el-button>
          <el-button class="filter-item" type="primary" size="small" plain @click="handleReset">
            重置
          </el-button>
        </div>
      </div>
      <el-table v-loading="userLoading" :data="recordList" element-loading-text="" :height="`600px`" fit
        highlight-current-row size="small">
        <el-table-column label="优惠券编号" width="120" prop="ticket_no" />
        <el-table-column label="优惠券名称" width="150" prop="name" />
        <el-table-column label="参与活动名称" width="180" prop="market_activity.name" />
        <el-table-column label="优惠券面值" width="120">
          <template slot-scope="scope">
            {{
              scope.row.type === 1
              ? `${scope.row.value}元`
              : `${scope.row.value}折`
            }}
          </template>
        </el-table-column>
        <el-table-column label="领取用户" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.passenger">
              <p class="table-p">
                昵称：{{ scope.row.passenger?.name || "-" }}
              </p>
              <p class="table-p">
                手机号：{{ scope.row.passenger?.cellphone || "-" }}
              </p>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <Tablestatus v-if="scope.row.status === 0" mode="warning" word="未使用" />
            <Tablestatus v-else-if="scope.row.status === 1" mode="default" word="已使用" />
            <Tablestatus v-else word="已过期" />
          </template>
        </el-table-column>
        <el-table-column label="有效期" min-width="290" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.start_time }} - {{ scope.row.end_time }}
          </template>
        </el-table-column>
        <el-table-column label="领取时间" prop="use_time" width="180">
          <template slot-scope="scope">
            {{ scope.row?.receive_time || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="使用时间" prop="use_time" width="180">
          <template slot-scope="scope">
            {{ scope.row?.use_time || "-" }}
          </template>
        </el-table-column>
      </el-table>
      <div class="bottom-container">
        <div class="left-content" />
        <el-pagination v-if="usertotal > 0" layout="total,prev, pager, next" background :page-size="logQuery.per_page"
          :total="usertotal" :current-page="logQuery.page" style="text-align: right" @current-change="userChanges" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { coupon_usage_overview, coupon_usage_excel, coupon_activities } from "@/api/marketing";
export default {
  props: {
    coupon: {
      type: Number,
    },
  },
  data() {
    return {
      logQuery: {
        page: 1,
        per_page: 15,
        coupon: "",
      },
      options: [],
      userLoading: false,
      recordList: [],
      usertotal: 0,
      recordVisible: false,
    };
  },
  methods: {
    async fetchOption() {
      const { data } = await coupon_activities(this.coupon)
      console.log(data)
      this.options = data.map((o) => {
        return {
          value: o.market_activity_id,
          label: o.cn_market_activity
        }
      })
    },
    handleFilter() {
      this.logQuery.page = 1;
      this.fetchLogs();
    },
    handleReset() {
      this.logQuery = {
        page: 1,
        per_page: 15,
        coupon: this.coupon
      };
      this.fetchLogs();
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'value') {
          return v.type === 1 ? `${v.value}元` : `${v.value}折`
        } else if (j === 'passenger') {
          return v.passenger ? `${v.passenger.name}-${v.passenger.cellphone}` : '-'

        } else if (j === 'status') {
          return v.status === 1 ? '已使用' : v.status === 0 ? '未使用' : '已过期'

        } else {
          return v[j]
        }
      }))
    },
    handleDownload() {
      this.downloadLoading = true;
      import("@/utils/Export2Excel").then(async (excel) => {
        const tHeader = [
          "名称",
          "ID",
          "编号",
          "面值",
          "领取用户",
          "使用状态",
          "领取时间",
          "使用时间",
          "有效期开始时间",
          "有效期结束时间",
        ];
        const filterVal = [
          "name",
          "coupon_record_id",
          "ticket_no",
          "value",
          "passenger",
          "status",
          "receive_time",
          "use_time",
          "start_time",
          "end_time",
        ];
        const res = await coupon_usage_excel({
          coupon: this.coupon,
          ...this.logQuery,
        });
        const list = Array.isArray(res.data) ? res.data : [];
        if (!list.length) {
          this.$notify({
            title: "提示",
            message: "暂无可导出数据",
            type: "info",
            duration: 2000,
          });
          return;
        }
        const data = this.formatJson(filterVal, list);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: "优惠券明细数据Excel",
          autoWidth: true,
          bookType: "xlsx",
        });
        this.$notify({
          title: "提示",
          message: "导出成功，请在浏览器下载内容查看",
          type: "success",
          duration: 2000,
        });

        this.downloadLoading = false;
      });
    },
    userChanges(val) {
      this.logQuery.page = val;
      this.fetchLogs();
    },
    selectUser() {
      this.recordVisible = true;
      this.logQuery.coupon = this.coupon;
      this.fetchOption()
      this.fetchLogs();
    },
    fetchLogs() {
      this.userLoading = true;
      coupon_usage_overview(this.logQuery).then((response) => {
        console.log(response);
        if (response.code === 200) {
          this.recordList = response?.data?.data;
          this.usertotal = response?.data?.meta?.pagination?.total;
        }
        this.userLoading = false;
      });
    },
    cancel() {
      this.recordVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.label-key {
  flex: 0 0 90px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  margin-left: 15px;

  &::after {
    content: ":";
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}
</style>

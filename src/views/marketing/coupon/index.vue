<template>
  <div class="app-container">
    <div class="hanldle-container">
      <div class="filter-container">
        <el-button
          class="filter-item add"
          style="margin-left: 0"
          type="text"
          icon="el-icon-circle-plus-outline"
          @click="handleCreate"
        >
          新增优惠券
        </el-button>
      </div>
      <div class="filter-container">
        <span class="label-key">优惠券类型</span>

        <el-select
          v-model="listQuery.type"
          placeholder="请选择"
          class="mini-condition"
          clearable
          size="small"
        >
          <el-option label="满减券" :value="1" />
          <el-option label="折扣券" :value="2" />
        </el-select>
        <span class="label-key">关键字搜索</span>
        <el-input
          v-model="listQuery.keywords"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          @click="handleReset"
        >
          重置
        </el-button>
      </div>
    </div>
    <el-table
      ref="userTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text=""
      fit
      highlight-current-row
      height="calc(100vh - 240px)"
      size="small"
      style="width: 100%"
    >
      <el-table-column
        label="优惠券ID"
        width="125"
        prop="coupon_id"
        class-name="isFirst"
      />
      <el-table-column label="优惠券名称" width="180"  prop="name" class-name="isFirst" />
      <el-table-column label="优惠券类型" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" size="mini" type="success">满减券</el-tag>
          <el-tag v-if="scope.row.type === 2" type="danger" size="mini">折扣券</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="优惠券面值" width="120">
        <template slot-scope="scope">
          {{ scope.row.type===1?`${scope.row.value}元`:`${scope.row.value}折` }}
        </template>
      </el-table-column>
      <el-table-column label="优惠券内容" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ Number(scope.row.rule)>0?`满${scope.row.rule}元`:'无门槛' }}, {{ scope.row.type===1?`立减${scope.row.value}元`:`立享${scope.row.value}折` }}
        </template>
      </el-table-column>
      <el-table-column label="适用范围" min-width="160">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.scope" size="mini" style="transform: scale(0.9); margin: 0 3px" effect="plain" type="info">通用</el-tag>
          <span v-else>
            <el-tag v-for="(tag, index) in handleScope(scope.row.scope)" :key="index" style="transform: scale(0.9); margin: 0 3px" size="mini" effect="plain" type="info">
              {{ tag.label }}
            </el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="优惠券说明"
        min-width="180"
        prop="summary"
      >
        <template slot-scope="scope">
          {{ scope?.row?.summary||'-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        width="180"
        prop="create_time"
      />
      <el-table-column
        label="更新时间"
        width="180"
        prop="update_time"
      />
      <el-table-column
        class-name="status-col"
        label="操作"
        width="165"
        fixed="right"
      >
        <template slot-scope="scope">
        <record :coupon="scope.row.coupon_id" />
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            class="filter-item"
            type="text"
            size="medium"
            @click="couponsDelete(scope.row.coupon_id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0"
        layout="sizes, total, prev, pager, next"
        background
        :page-size="listQuery.per_page"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="text-align: right"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>
    <el-dialog
      :title="dialogStatus === 'create' ? '新增优惠券' : '修改优惠券'"
      :visible.sync="dialogVisible"
      class="banner-dialog"
      :close-on-click-modal="false"
      width="800px"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="120px"
        size="medium"
      >
        <el-form-item label="优惠券名称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item label="优惠券类型" prop="type">
          <el-select
            v-model="temp.type"
            placeholder="优惠券类型"
            style="width: 150px"
          >
            <el-option label="满减券" :value="1" />
            <el-option label="折扣券" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="优惠金额/折扣" prop="value">
          <el-input
            v-model="temp.value"
            style="width: 150px"
            placeholder="请输入"
          >
            <template slot="append">{{ temp.type===1?'元':'折' }}</template>
          </el-input>
          <span
            class="label-key middle-label"
          >*满减券请输入优惠的金额（元）,折扣券请输入应支付折扣（1~9.9折）</span>
        </el-form-item>
        <el-form-item label="最低消费金额" prop="rule">
          <el-input
            v-model="temp.rule"
            style="width: 150px"
            placeholder="请输入"
          >
            <template slot="append">元</template>
          </el-input>
          <span
            class="label-key middle-label"
          >*请填写此组优惠券最低消费金额为多少时才能使用</span>
        </el-form-item>
        <el-form-item label="适用范围(多选)" prop="scope">
          <div class="scope_type">
            <el-checkbox-group v-model="temp.scope">
              <el-checkbox
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.value"
                :disabled="!item.enable"
                size="small"
              >{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <!-- <el-form-item label="优惠券有效期" prop="valid_category">
          <el-date-picker
            v-model="temp.date"
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item> -->
        <el-form-item label="优惠券说明" prop="summary">
          <el-input
            v-model="temp.summary"
            class="trainsInput"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? handleAdd() : updateCoupon()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Record from './components/Record'
import { coupons, delCoupon, addCoupon, couponsUpdate ,coupon_usage_excel} from '@/api/marketing'
export default {
  name: 'Coupon',
    components: {
    Record
  },
  data() {
    const validateValid = (rule, value, callback) => {
      if (!this.temp.date) {
        return callback(new Error('请输入优惠券有效期'))
      } else {
        return callback()
      }
    }
    const validateRule = (rule, value, callback) => {
      if (!/(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/.test(value) || !value) {
        return callback(new Error('请输入正确的最低消费金额'))
      } else {
        return callback()
      }
    }
    const validateValue = (rule, value, callback) => {
      if (this.temp.type === 1) {
        if (value <= 0) {
          callback(new Error('优惠金额需大于0'))
        } else if (!/(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/.test(value)) {
          callback(new Error('请输入正确的优惠金额'))
        } else {
          callback()
        }
      } else {
        if (value <= 0) {
          callback(new Error('折扣需大于0'))
        } else if (!/(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/.test(value) || value > 9.99) {
          callback(new Error('请输入正确的折扣（1~9.9折）'))
        } else {
          callback()
        }
      }
    }
    const validateScope = (rule, value, callback) => {
      if (this.temp.scope.length > 0) {
        return callback()
      } else {
        return callback(new Error('请选择优惠券适用范围'))
      }
    }
    return {
      temp: {
        type: 1,
        scope: []
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入优惠券名称',
            trigger: 'blur'
          }
        ],
        valid_category: [{ validator: validateValid, trigger: 'blur' }],
        type: [
          { required: true, message: '请选择优惠券类型', trigger: 'change' }
        ],
        scope: [{ validator: validateScope, trigger: 'change', required: true }],
        rule: [
          {
            required: true,
            validator: validateRule,
            trigger: 'change'
          }
        ],
        value: [
          {
            required: true,
            validator: validateValue,
            trigger: 'change'
          }
        ]
      },
      typeOptions: [
        { value: 1, label: '拼车' },
        { value: 2, label: '包车' },
        { value: 3, label: '带货' },
        { value: 4, label: '代办' },
        { value: 5, label: '定制客运', enable: true },
        { value: 6, label: '顺风车' },
        { value: 7, label: '快车' },
        { value: 11, label: '出租车' },
        { value: 20, label: '摆渡车' }
      ],
      total: '',
      dialogStatus: 'create',
      dialogVisible: false,
      list: null,
      listLoading: false,
      listQuery: {
        page: 1,
        per_page: 15,
        keywords: undefined,
        type: undefined
      }
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    isfull(scope) {
      return scope.split(',').length === 9
    },
    async couponsDelete(id) {
      await this.$confirm('确认删除当前优惠券?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await delCoupon(id)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.temp = Object.assign({}, row)
      this.$set(this.temp, 'scope', row.scope ? row.scope.split(',').map((ele) => Number(ele)) : [])
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleScope(val) {
      const arr = val.split(',')
      const arrTag = this.typeOptions.filter((ele) => {
        const isExist = arr.some((Element) => {
          return Number(Element) === ele.value
        })
        if (isExist) {
          return ele
        }
      })
      if (arrTag.length > 0) {
        return arrTag
      }
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.temp = {
        type: 1,
        scope: [5]
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleAdd() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({}, this.temp)
      params.scope = params.scope.join(',')
      await addCoupon(params)
      this.dialogVisible = false
      this.$notify({
        message: '添加成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    async updateCoupon() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({}, this.temp)
      params.scope = params.scope.join(',')
      await couponsUpdate(params)
      this.dialogVisible = false
      this.$notify({
        message: '编辑成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        keyword: undefined,
        type: undefined,
        valid_category: undefined,
        status: undefined
      }
      this.$refs.userTable.clearSort()
      this.date = []
      this.fetchList()
    },
    async fetchList() {
      this.listLoading = true
      const { data } = await coupons(this.listQuery)
      this.list = data?.data
      this.total = data?.meta?.pagination?.total
      this.listLoading = false
    },
    positive(val) {
      const reg = /^[+]{0,1}(\d+)$/
      const len = val.length
      let str = ''
      for (var i = 0; i < len; i++) {
        if (reg.test(val[i])) {
          str += val[i]
        }
      }
      return Number(str)
    }
  }
}
</script>
<style lang="scss" scoped>
.component-container {
  margin-top: 24px;
}
.middle-label {
  vertical-align: middle;
}
</style>

<style lang="scss">
.scope_type {
  .el-checkbox {
    margin-right: 12px;
  }
  .el-checkbox__label {
    font-size: 12px;
    padding-left: 5px;
  }
}
.label-key{
  margin-left: 3px;
}
</style>

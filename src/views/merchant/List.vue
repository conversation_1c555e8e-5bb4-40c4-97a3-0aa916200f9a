<template>
  <page-header-wrapper>
    <a-card :bordered="false" class="merchant-card">
      <!-- 状态标签页 -->
      <a-tabs v-model="statusActiveTab" @change="handleStatusTabChange" class="status-tabs">
        <a-tab-pane key="all" tab="全部" />
        <a-tab-pane key="normal" tab="未冻结" />
        <a-tab-pane key="frozen" tab="已冻结" />
      </a-tabs>

      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :md="8" :sm="24">
              <a-form-item label="商户名称">
                <a-input v-model="queryParam.mchname" placeholder="请输入商户名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="手机号码">
                <a-input v-model="queryParam.cellphone" placeholder="请输入手机号码" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.status" placeholder="请选择状态" allowClear>
                  <a-select-option :value="0">正常</a-select-option>
                  <a-select-option :value="1">已冻结</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :md="24" :sm="24" class="table-page-search-buttons">
              <a-button type="primary" @click="handleSearch">
                <a-icon type="search" />查询
              </a-button>
              <a-button @click="handleReset">
                <a-icon type="reload" />重置
              </a-button>
              <!-- <a-button type="primary" @click="handleAddMerchant">
                <a-icon type="plus" />新增商户
              </a-button> -->
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div class="selected-alert" v-if="selectedRowKeys.length > 0">
        <a-alert type="info" show-icon>
          <template slot="message">
            已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a> 项
            <a style="margin-left: 8px" @click="clearSelected">取消选择</a>
          </template>
        </a-alert>
      </div>

      <s-table ref="table" size="default" rowKey="admin_id" :columns="columns" :data="loadData" :alert="false"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :scroll="{ x: 1200 }"
        :pagination="{ showSizeChanger: true, showQuickJumper: true }" bordered class="merchant-table">
        <span slot="status" slot-scope="text">
          <a-badge :status="text === 0 ? 'success' : 'error'" :text="text === 0 ? '正常' : '已冻结'" />
        </span>
        <span slot="amount" slot-scope="text">
          <span class="amount-text">{{ text ? `¥${text.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}` : '¥0.00' }}</span>
        </span>
        <span slot="balance" slot-scope="text">
          <span class="balance-text">{{ text ? `¥${text.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}` : '¥0.00' }}</span>
        </span>
        <span slot="createTime" slot-scope="text">{{ text && text.length > 0 ? text : '-' }}</span>
        <span slot="action" slot-scope="text, record">
          <a-dropdown>
            <a-button type="primary" size="small" ghost>
              操作 <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay" class="action-menu">
              <!-- <a-menu-item @click="handleEdit(record)">
                <span class="menu-item-with-icon">
                  <a-icon type="edit" class="icon-blue" /> 编辑
                </span>
              </a-menu-item> -->
              <a-menu-item @click="handleChangeStatus(record)">
                <span class="menu-item-with-icon">
                  <a-icon :type="record.is_freeze === 0 ? 'lock' : 'unlock'"
                    :class="record.is_freeze === 0 ? 'icon-red' : 'icon-green'" />
                  {{ record.is_freeze === 0 ? '禁用' : '启用' }}
                </span>
              </a-menu-item>
              <a-menu-item @click="handleChangePassword(record)">
                <span class="menu-item-with-icon">
                  <a-icon type="key" class="icon-orange" /> 修改密码
                </span>
              </a-menu-item>
              <a-menu-item @click="handlePermissions(record)">
                <span class="menu-item-with-icon">
                  <a-icon type="setting" class="icon-blue" /> 权限分配
                </span>
              </a-menu-item>
              <!-- <a-menu-item @click="handleDelete(record)">
                <span class="menu-item-with-icon">
                  <a-icon type="delete" class="icon-red" /> 删除
                </span>
              </a-menu-item> -->
            </a-menu>
          </a-dropdown>
        </span>
      </s-table>

      <!-- 新增/编辑商户对话框 -->
      <a-modal :title="modalType === 'add' ? '新增商户' : '编辑商户'" :visible="visible" :width="750" @ok="handleSubmit"
        @cancel="handleCancel" okText="确定" cancelText="取消" :maskClosable="false" class="merchant-modal">
        <a-form layout="vertical" :form="form" class="merchant-form">
          <a-form-item label="商户名称">
            <a-input placeholder="请输入商户名称"
              v-decorator="['mchname', { rules: [{ required: true, message: '请输入商户名称' }] }]" />
          </a-form-item>
          <a-form-item v-if="modalType === 'add'" label="手机号码">
            <a-input placeholder="请输入手机号码"
              v-decorator="['cellphone', { rules: [{ required: true, message: '请输入手机号码' }, { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确' }] }]" />
          </a-form-item>
          <a-form-item label="邮箱地址">
            <a-input placeholder="请输入邮箱"
              v-decorator="['email', { rules: [{ type: 'email', message: '邮箱地址格式不正确' }] }]" />
          </a-form-item>
          <a-form-item label="业务类型">
            <a-checkbox-group v-model="form.businessTypes" class="business-type-group">
              <a-checkbox v-for="item in businessTypeOptions" :key="item.key" :value="item.key">
                {{ item.label }}
              </a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 修改密码对话框 -->
      <a-modal title="修改密码" :visible="passwordVisible" @ok="handlePasswordSubmit" @cancel="handlePasswordCancel"
        okText="确定" cancelText="取消" :maskClosable="false" class="password-modal">
        <div class="password-form">
          <a-form-item label="新密码">
            <a-input-password v-model="passwordForm.new_password" placeholder="请输入新密码" @change="validatePassword" />
          </a-form-item>
          <a-form-item label="确认密码">
            <a-input-password v-model="passwordForm.confirm_password" placeholder="请再次输入新密码"
              @change="validateConfirmPassword" />
          </a-form-item>
          <a-alert v-if="passwordError" type="error" :message="passwordError" banner />
        </div>
      </a-modal>

      <!-- 权限分配对话框 -->
      <a-modal title="权限分配" :visible="permissionsVisible" :width="800" :maskClosable="false" :keyboard="false"
        :footer="null" @cancel="handlePermissionsCancel">
        <template v-if="currentRecord">
          <div class="permissions-header">
            <a-avatar :size="48" icon="user" class="mr-3" />
            <div class="merchant-info">
              <h3>{{ currentRecord.mchname }} 权限分配</h3>
              <p class="text-muted small">ID: {{ currentRecord.admin_id }}</p>
            </div>
          </div>

          <div class="permissions-content">
            <a-alert v-if="loadingPermissions" message="正在加载权限数据，请稍候..." type="info" show-icon />

            <a-spin v-if="loadingPermissions" :spinning="true" tip="加载中..." class="mt-4" />

            <template v-else>
              <!-- 菜单权限部分 -->
              <div class="permission-section">
                <h4 class="permission-section-title">
                  <a-icon type="menu" />
                  菜单权限
                </h4>
                <div class="permission-section-content">
                  <a-alert v-if="!menuPermissionsTree || menuPermissionsTree.length === 0" message="没有可用的菜单权限"
                    type="warning" show-icon class="mb-3" />

                  <a-tree v-else ref="menuPermissionTree" checkable showIcon
                    :treeData="menuPermissionsTreeWithFunctions" :checkedKeys="allCheckedKeys"
                    :expandedKeys="expandedMenuKeys" :autoExpandParent="true" @check="onPermissionCheck"
                    @expand="onExpandChange">
                    <template slot="title" slot-scope="{title, dataRef}">
                      <span :class="dataRef.isFunction ? 'function-permission-title' : 'menu-permission-title'">
                        {{ title }}
                      </span>
                    </template>
                    <template slot="icon" slot-scope="{icon, dataRef}">
                      <a-icon v-if="dataRef.isFunction" type="setting" class="function-icon" />
                      <i v-else-if="icon && !icon.startsWith('el-icon')" :class="[icon, 'menu-icon']"></i>
                      <a-icon v-else type="folder" class="menu-icon-default" />
                    </template>
                  </a-tree>
                </div>
              </div>
            </template>
          </div>

          <div class="ant-modal-footer" style="margin-top: 24px;">
            <a-button key="back" @click="handlePermissionsCancel">取消</a-button>
            <a-button key="submit" type="primary" :loading="permissionSubmitting" @click="handlePermissionsSubmit">
              确定
            </a-button>
          </div>
        </template>
      </a-modal>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { STable } from '@/components'
import {
  getMerchantList,
  createMerchant,
  updateMerchant,
  deleteMerchant,
  changeMerchantStatus,
  changeMerchantPassword,
  getMerchantPermissions,
  updateMerchantPermissions,
  getBusinessTypes
} from '@/api/merchant'

export default {
  name: 'MerchantList',
  components: {
    STable
  },
  data() {
    return {
      // 状态标签页
      statusActiveTab: 'all',

      // 查询参数
      queryParam: {
        page: 1,
        per_page: 10,
        mchname: '',
        cellphone: '',
        sort_field: 'created_at',
        sort_order: 'desc',
        status: undefined
      },
      // 表格列定义
      columns: [
        {
          title: '商户编号',
          dataIndex: 'admin_id',
          align: 'center',
          width: 100
        },
        {
          title: '商户名称',
          dataIndex: 'mchname',
          align: 'left',
          width: 240,
          ellipsis: true
        },
        {
          title: '手机号码',
          dataIndex: 'cellphone',
          align: 'center',
          width: 130
        },
        {
          title: '交易总额',
          dataIndex: 'total_amount',
          align: 'right',
          scopedSlots: { customRender: 'amount' }
        },
        {
          title: '账户余额',
          dataIndex: 'balance',
          align: 'right',
          scopedSlots: { customRender: 'balance' }
        },
        {
          title: '状态',
          dataIndex: 'is_freeze',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建时间',
          dataIndex: 'create_time',
          align: 'center',
          width: 220,
          scopedSlots: { customRender: 'createTime' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 选中的行
      selectedRowKeys: [],
      selectedRows: [],

      // 新增/编辑商户相关
      visible: false,
      modalType: 'add', // 'add' 或 'edit'
      form: this.initForm(),
      rules: {
        mchname: [
          { required: true, message: '请输入商户名称', trigger: 'blur' }
        ],
        account: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        cellphone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        email: [
          { required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '邮箱地址格式不正确', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
        ],
        businessTypes: [
          { required: true, type: 'array', min: 1, message: '请至少选择一种业务类型', trigger: 'change' }
        ]
      },
      currentRecord: null,
      businessTypeOptions: [],

      // 修改密码相关
      passwordVisible: false,
      passwordForm: {
        new_password: '',
        confirm_password: ''
      },
      passwordError: '', // 密码错误信息
      passwordRules: {
        new_password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
        ],
        confirm_password: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.passwordForm.new_password) {
                callback(new Error('两次输入的密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },

      // 权限分配相关
      permissionsVisible: false,
      permissionsActiveTab: 'menu',
      menuPermissionsTree: [],
      menuPermissionsTreeWithFunctions: [], // 包含功能权限的菜单树
      checkedMenuKeys: [],
      expandedMenuKeys: [],
      functionPermissions: [],
      checkedFunctionKeys: [],
      allCheckedKeys: [], // 所有选中的键（菜单+功能）
      permissionSubmitting: false,
      loadingPermissions: false,
      originalPermissionsData: null,
      permissionKeyMap: new Map(),

      // 表单值
      formValues: {},

      // 全局错误处理
      errorNotified: false
    }
  },
  created() {
    // 获取业务类型选项
    this.getBusinessTypeOptions()
    this.form = this.$form.createForm(this, {
      name: 'merchant_form',
      onValuesChange: (props, values) => {
        this.formValues = { ...this.formValues, ...values }
      }
    })

    // 添加全局错误监听
    window.addEventListener('error', this.handleGlobalError)
  },
  beforeDestroy() {
    // 移除错误监听
    window.removeEventListener('error', this.handleGlobalError)
  },
  methods: {
    // 初始化表单
    initForm() {
      return {
        admin_id: undefined,
        mchname: '',
        account: '',
        cellphone: '',
        email: '',
        password: '',
        businessTypes: []
      }
    },

    // 表单值
    resetFormFields() {
      this.formValues = this.initForm()
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },

    // 获取业务类型选项
    getBusinessTypeOptions() {
      getBusinessTypes().then(res => {
        this.businessTypeOptions = res || []
      }).catch(err => {
        console.error('获取业务类型失败', err)
        this.businessTypeOptions = []
      })
    },

    // 加载表格数据
    loadData(parameter) {
      return getMerchantList({
        ...parameter,
        ...this.queryParam
      }).then(res => {
        // 确保返回值包含必要的字段
        const pagination = res.data && res.data.meta && res.data.meta.pagination ? res.data.meta.pagination : {}
        return {
          data: res.data && res.data.data ? res.data.data : [],
          pageNo: pagination.current_page || 1, // STable组件期望的字段名
          pageSize: pagination.per_page || 15, // STable组件期望的字段名
          totalCount: pagination.total || 0, // STable组件期望的字段名
          totalPage: pagination.total_pages || 1
        }
      }).catch(err => {
        console.error('加载商户列表失败', err)
        return {
          data: [],
          pageNo: 1, // STable组件期望的字段名
          pageSize: 15, // STable组件期望的字段名
          totalCount: 0, // STable组件期望的字段名
          totalPage: 1
        }
      })
    },

    // 查询
    handleSearch() {
      this.queryParam.page = 1
      this.$refs.table.refresh(true)
    },

    // 重置查询条件
    handleReset() {
      this.queryParam = {
        page: 1,
        per_page: 15,
        mchname: '',
        cellphone: '',
        sort_field: 'created_at',
        sort_order: 'desc',
        status: undefined
      }
      this.$refs.table.refresh(true)
    },

    // 处理状态标签页切换
    handleStatusTabChange(activeKey) {
      this.statusActiveTab = activeKey
      // 根据标签页设置状态筛选
      switch (activeKey) {
        case 'all':
          this.queryParam.status = undefined
          break
        case 'normal':
          this.queryParam.status = 0
          break
        case 'frozen':
          this.queryParam.status = 1
          break
      }
      // 重新加载数据
      this.queryParam.page = 1
      this.$refs.table.refresh(true)
    },

    // 表格选择行变化
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },

    // 清除选中行
    clearSelected() {
      this.selectedRowKeys = []
      this.selectedRows = []
    },

    // 新增商户
    handleAddMerchant() {
      this.modalType = 'add'
      this.visible = true
      this.resetFormFields()
    },

    // 查看商户详情
    handleViewDetail(record) {
      this.$router.push({ path: `/merchant/detail/${record.admin_id}` })
    },

    // 编辑商户
    handleEdit(record) {
      this.modalType = 'edit'
      this.visible = true
      this.currentRecord = record
      this.formValues = {
        admin_id: record.admin_id,
        mchname: record.mchname,
        cellphone: record.cellphone,
        email: record.email,
        businessTypes: record.businessTypes || []
      }

      this.$nextTick(() => {
        this.form.setFieldsValue({
          mchname: record.mchname,
          cellphone: record.cellphone,
          email: record.email
        })
      })
    },

    // 提交表单（新增/编辑商户）
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const submitData = {
            ...values,
            businessTypes: this.formValues.businessTypes,
            admin_id: this.currentRecord ? this.currentRecord.admin_id : undefined
          }

          if (this.modalType === 'add') {
            // 新增商户
            createMerchant(submitData)
              .then(() => {
                this.$message.success('商户创建成功')
                this.visible = false
                this.$refs.table.refresh()
              })
              .catch(err => {
                this.$message.error(`商户创建失败：${err.message || '未知错误'}`)
              })
          } else {
            // 编辑商户
            updateMerchant(submitData.admin_id, submitData)
              .then(() => {
                this.$message.success('商户信息更新成功')
                this.visible = false
                this.$refs.table.refresh()
              })
              .catch(err => {
                this.$message.error(`商户信息更新失败：${err.message || '未知错误'}`)
              })
          }
        }
      })
    },

    // 取消表单
    handleCancel() {
      this.visible = false
      this.resetFormFields()
    },

    // 修改密码
    handleChangePassword(record) {
      this.passwordVisible = true
      this.currentRecord = record
      this.passwordForm = {
        new_password: '',
        confirm_password: ''
      }
      this.passwordError = ''
    },

    // 验证密码
    validatePassword() {
      if (this.passwordForm.new_password.length < 6) {
        this.passwordError = '密码长度不能少于6个字符'
      } else {
        this.passwordError = ''
        this.validateConfirmPassword()
      }
    },

    // 验证确认密码
    validateConfirmPassword() {
      if (this.passwordForm.confirm_password &&
        this.passwordForm.new_password !== this.passwordForm.confirm_password) {
        this.passwordError = '两次输入的密码不一致'
      } else if (this.passwordForm.confirm_password &&
        this.passwordForm.new_password === this.passwordForm.confirm_password) {
        this.passwordError = ''
      }
    },

    // 提交修改密码
    handlePasswordSubmit() {
      // 先进行验证
      if (!this.passwordForm.new_password) {
        this.passwordError = '请输入新密码'
        return
      }
      if (this.passwordForm.new_password.length < 6) {
        this.passwordError = '密码长度不能少于6个字符'
        return
      }
      if (!this.passwordForm.confirm_password) {
        this.passwordError = '请再次输入新密码'
        return
      }
      if (this.passwordForm.new_password !== this.passwordForm.confirm_password) {
        this.passwordError = '两次输入的密码不一致'
        return
      }

      // 验证通过，提交修改
      changeMerchantPassword(this.currentRecord.admin_id, this.passwordForm)
        .then(() => {
          this.$message.success('密码修改成功')
          this.passwordVisible = false
        })
        .catch(err => {
          this.passwordError = `密码修改失败：${err.message || '未知错误'}`
        })
    },

    // 取消修改密码
    handlePasswordCancel() {
      this.passwordVisible = false
      this.passwordForm = {
        new_password: '',
        confirm_password: ''
      }
      this.passwordError = ''
    },

    // 修改商户状态（启用/禁用）
    handleChangeStatus(record) {
      const newStatus = record.is_freeze === 0 ? 1 : 0
      const statusText = newStatus === 0 ? '启用' : '禁用'

      this.$confirm({
        title: `确定要${statusText}该商户吗？`,
        content: statusText === '禁用' ? '禁用后，该商户将无法登录系统。' : '启用后，该商户可以正常登录系统。',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          changeMerchantStatus(record.admin_id, newStatus)
            .then(() => {
              this.$message.success(`${statusText}成功`)
              this.$refs.table.refresh()
            })
            .catch(err => {
              this.$message.error(`${statusText}失败：${err.message || '未知错误'}`)
            })
        }
      })
    },

    // 删除商户
    handleDelete(record) {
      this.$confirm({
        title: '确定要删除该商户吗？',
        content: '删除后数据将无法恢复，请谨慎操作。',
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          deleteMerchant(record.admin_id)
            .then(() => {
              this.$message.success('删除成功')
              this.$refs.table.refresh()
            })
            .catch(err => {
              this.$message.error(`删除失败：${err.message || '未知错误'}`)
            })
        }
      })
    },

    // 打开权限分配弹窗
    handlePermissions(record) {
      this.permissionsVisible = true
      this.permissionsActiveTab = 'menu'
      this.currentRecord = record
      this.loadingPermissions = true

      // 清空现有数据
      this.menuPermissionsTree = []
      this.menuPermissionsTreeWithFunctions = []
      this.checkedMenuKeys = []
      this.expandedMenuKeys = []
      this.functionPermissions = []
      this.checkedFunctionKeys = []
      this.allCheckedKeys = []

      // 存储原始权限数据
      this.originalPermissionsData = null

      // 添加详细的调试信息
      console.log('当前记录:', record)

      // 获取商户权限
      getMerchantPermissions(record.admin_id)
        .then(res => {
          if (!res || !res.data) {
            this.$message.error('获取权限数据失败：返回数据格式无效')
            this.permissionsVisible = false
            this.loadingPermissions = false
            return
          }

          // 保存原始权限数据的完整副本
          this.originalPermissionsData = JSON.parse(JSON.stringify(res.data))
          console.log('原始权限数据:', JSON.stringify(this.originalPermissionsData, null, 2))

          try {
            // 处理菜单权限
            if (res.data.menu_permissions_definition && Array.isArray(res.data.menu_permissions_definition)) {
              this.processMenuPermissions(res.data.menu_permissions_definition || [])
              console.log('菜单权限树(处理后):', JSON.stringify(this.menuPermissionsTree, null, 2))
              console.log('选中的菜单权限:', this.checkedMenuKeys)
            } else {
              console.error('菜单权限数据格式无效')
              this.$message.warning('菜单权限数据格式无效，请联系技术支持')
            }

            // 处理功能权限
            if (res.data.function_permission_definition && typeof res.data.function_permission_definition === 'object') {
              this.processFunctionPermissions(res.data.function_permission_definition || {})
              console.log('功能权限:', this.functionPermissions)
              console.log('选中的功能权限:', this.checkedFunctionKeys)

              // 合并菜单权限和功能权限到一个树中
              this.buildCombinedPermissionTree()
            } else {
              console.error('功能权限数据格式无效')
              this.$message.warning('功能权限数据格式无效，请联系技术支持')
            }

            // 强制同步选中和展开状态
            this.$nextTick(() => {
              if (this.$refs.menuPermissionTree) {
                console.log('同步树的选中状态...')
                this.$refs.menuPermissionTree.setCheckedKeys(this.checkedMenuKeys)
                // 保证树的展开状态
                this.expandedMenuKeys = [...new Set([...this.expandedMenuKeys, ...this.checkedMenuKeys])]
              }
            })
          } catch (error) {
            console.error('处理权限数据出错:', error)
            this.$message.error(`处理权限数据出错: ${error.message || '未知错误'}`)
          } finally {
            this.loadingPermissions = false
          }

          // 强制重新渲染
          this.$nextTick(() => {
            console.log('渲染后的树组件引用:', this.$refs.menuPermissionTree)
          })
        })
        .catch(err => {
          console.error('获取商户权限失败', err)
          this.$message.error(`获取权限数据失败: ${err.message || '未知错误'}`)
          this.permissionsVisible = false
          this.loadingPermissions = false
        })
    },

    // 处理菜单权限
    processMenuPermissions(menuPermissions) {
      // 确保初始化为空数组
      this.menuPermissionsTree = []
      this.checkedMenuKeys = []
      this.expandedMenuKeys = []
      this.permissionKeyMap = new Map() // 存储键到路径的映射

      try {
        if (!menuPermissions || !Array.isArray(menuPermissions)) {
          console.error('menuPermissions不是有效数组:', menuPermissions)
          return
        }

        console.log('处理前的menuPermissions:', JSON.stringify(menuPermissions, null, 2))

        // 构建树形结构和获取已选中的权限
        const buildTreeData = (menus, parentPath = []) => {
          if (!menus || !Array.isArray(menus)) {
            console.warn('菜单数据不是数组:', menus)
            return []
          }

          return menus.map((menu, index) => {
            if (!menu || typeof menu !== 'object') {
              console.error('无效的菜单项:', menu)
              return null
            }

            // 只使用中文标题显示
            const menuTitle = menu.meta && menu.meta.title ? menu.meta.title : '未命名'
            // 使用唯一ID作为key，不要显示
            const nodeKey = menu.permission || `key_${Math.random().toString(36).substr(2, 9)}`

            // 构建当前节点的路径
            const currentPath = [...parentPath, index]

            // 记录节点信息，用于调试
            console.log(`节点 ${menuTitle}(${nodeKey}) 路径:`, JSON.stringify(currentPath))

            // 存储节点键到路径的映射，用于后续更新enabled状态
            this.permissionKeyMap.set(nodeKey, {
              path: currentPath,
              type: 'menu'
            })

            // 清理图标，不显示el-icon前缀
            let iconClass = null
            if (menu.meta && menu.meta.icon) {
              // 如果是以el-icon开头的，则不使用该图标，而是使用默认文件夹图标
              iconClass = menu.meta.icon.startsWith('el-icon') ? null : menu.meta.icon
            }

            // 构建节点
            const node = {
              title: menuTitle, // 只显示中文标题
              key: nodeKey, // 仅用于树节点内部处理
              isLeaf: !(menu.sub_menu || menu.page),
              icon: iconClass, // 只有非el-icon的图标才会被使用
              // 添加path属性用于调试
              path: currentPath.join('.')
            }

            // 如果已启用，加入选中列表
            if (menu.enabled) {
              this.checkedMenuKeys.push(nodeKey)
              console.log(`节点 ${menuTitle} 已启用，路径: ${currentPath.join('.')}`)
            }

            let hasChildren = false

            // 处理子菜单
            if (menu.sub_menu && Array.isArray(menu.sub_menu) && menu.sub_menu.length > 0) {
              const childPath = [...currentPath, 'sub_menu']
              const children = buildTreeData(menu.sub_menu, childPath)
              if (children && children.length > 0) {
                node.children = children
                hasChildren = true
                this.expandedMenuKeys.push(nodeKey)
                console.log(`节点 ${menuTitle} 有子菜单，路径: ${childPath.join('.')}`)
              }
            }

            // 处理页面权限
            if (!hasChildren && menu.page && Array.isArray(menu.page) && menu.page.length > 0) {
              const pagePath = [...currentPath, 'page']
              const pageChildren = buildTreeData(menu.page, pagePath)
              if (pageChildren && pageChildren.length > 0) {
                node.children = pageChildren
                this.expandedMenuKeys.push(nodeKey)
                console.log(`节点 ${menuTitle} 有页面，路径: ${pagePath.join('.')}`)
              }
            }

            return node
          }).filter(Boolean) // 过滤掉null节点
        }

        this.checkedMenuKeys = []
        this.expandedMenuKeys = []
        const treeData = buildTreeData(menuPermissions)
        console.log('构建的树数据:', JSON.stringify(treeData, null, 2))

        // 记录路径映射表
        console.log('路径映射表:')
        this.permissionKeyMap.forEach((value, key) => {
          console.log(`键: ${key}, 路径: ${value.path.join('.')}, 类型: ${value.type}`)
        })

        this.menuPermissionsTree = treeData
      } catch (error) {
        console.error('处理菜单权限出错:', error)
        this.menuPermissionsTree = []
        this.checkedMenuKeys = []
        this.expandedMenuKeys = []
      }
    },

    // 处理功能权限
    processFunctionPermissions(functionPermissions) {
      // 确保初始化为空数组
      this.functionPermissions = []
      this.checkedFunctionKeys = []

      try {
        if (!functionPermissions || typeof functionPermissions !== 'object') {
          console.error('functionPermissions不是有效对象:', functionPermissions)
          return
        }

        console.log('处理前的functionPermissions:', JSON.stringify(functionPermissions, null, 2))

        // 构建功能权限列表
        Object.entries(functionPermissions).forEach(([key, enabled]) => {
          this.functionPermissions.push({
            key,
            label: this.getFunctionPermissionLabel(key)
          })

          if (enabled) {
            this.checkedFunctionKeys.push(key)
          }
        })

        // 确保返回时两个数组都已正确初始化
        console.log('处理后的功能权限列表:', this.functionPermissions)
        console.log('选中的功能权限:', this.checkedFunctionKeys)
      } catch (error) {
        console.error('处理功能权限出错:', error)
        // 出错时确保重置为空数组
        this.functionPermissions = []
        this.checkedFunctionKeys = []
      }
    },

    // 获取功能权限标签
    getFunctionPermissionLabel(key) {
      const labelMap = {
        freshOrderNotice: '新订单通知',
        provincialReportConfig: '省际包车报备配置',
        exportData: '导出数据',
        batchOperations: '批量操作',
        advancedRefundPrivilege: '退款操作权限'
      }
      return labelMap[key] || key
    },

    // 构建合并的权限树（菜单权限 + 功能权限）
    buildCombinedPermissionTree() {
      // 深拷贝菜单权限树
      this.menuPermissionsTreeWithFunctions = JSON.parse(JSON.stringify(this.menuPermissionsTree))

      // 为每个菜单项添加其对应的功能权限
      const addFunctionPermissionsToMenu = (menuItems, originalMenuData) => {
        if (!menuItems || !Array.isArray(menuItems)) return

        menuItems.forEach((menuItem, index) => {
          // 查找原始菜单数据中对应的项
          const originalItem = this.findOriginalMenuByKey(menuItem.key, originalMenuData)

          if (originalItem && originalItem.functional_permissions) {
            // 为当前菜单项添加功能权限子节点
            if (!menuItem.children) {
              menuItem.children = []
            }

            Object.entries(originalItem.functional_permissions).forEach(([funcKey, funcData]) => {
              const functionNode = {
                key: `func_${funcKey}`,
                title: funcData.title || this.getFunctionPermissionLabel(funcKey),
                isLeaf: true,
                isFunction: true,
                icon: 'setting'
              }
              menuItem.children.push(functionNode)

              // 如果功能权限已启用，添加到选中列表
              if (funcData.enabled) {
                this.checkedFunctionKeys.push(funcKey)
              }
            })
          }

          // 递归处理子菜单
          if (menuItem.children && menuItem.children.length > 0) {
            const childrenWithoutFunctions = menuItem.children.filter(child => !child.isFunction)
            if (childrenWithoutFunctions.length > 0) {
              addFunctionPermissionsToMenu(childrenWithoutFunctions, originalMenuData)
            }
          }
        })
      }

      // 添加功能权限到菜单树
      if (this.originalPermissionsData && this.originalPermissionsData.menu_permissions_definition) {
        addFunctionPermissionsToMenu(this.menuPermissionsTreeWithFunctions, this.originalPermissionsData.menu_permissions_definition)
      }

      // 更新所有选中的键
      this.updateAllCheckedKeys()

      console.log('合并后的权限树:', JSON.stringify(this.menuPermissionsTreeWithFunctions, null, 2))
      console.log('所有选中的键:', this.allCheckedKeys)
    },

    // 查找原始菜单数据中对应的项
    findOriginalMenuByKey(key, menuData, path = []) {
      if (!menuData || !Array.isArray(menuData)) return null

      for (let i = 0; i < menuData.length; i++) {
        const item = menuData[i]
        if (item.permission === key) {
          return item
        }

        // 递归搜索子菜单
        if (item.sub_menu) {
          const found = this.findOriginalMenuByKey(key, item.sub_menu, [...path, i, 'sub_menu'])
          if (found) return found
        }

        // 递归搜索页面
        if (item.page) {
          const found = this.findOriginalMenuByKey(key, item.page, [...path, i, 'page'])
          if (found) return found
        }
      }

      return null
    },

    // 更新所有选中的键
    updateAllCheckedKeys() {
      this.allCheckedKeys = [
        ...this.checkedMenuKeys,
        ...this.checkedFunctionKeys.map(key => `func_${key}`)
      ]
    },

    // 权限选中变化（包括菜单和功能权限）
    onPermissionCheck(checkedKeys) {
      let keys = []
      if (Array.isArray(checkedKeys)) {
        keys = checkedKeys
      } else if (checkedKeys && checkedKeys.checked) {
        keys = checkedKeys.checked
      } else {
        console.warn('接收到的checkedKeys格式不正确:', checkedKeys)
        keys = []
      }

      // 分离菜单权限和功能权限
      this.checkedMenuKeys = keys.filter(key => !key.startsWith('func_'))
      this.checkedFunctionKeys = keys.filter(key => key.startsWith('func_')).map(key => key.replace('func_', ''))
      this.allCheckedKeys = keys

      console.log('菜单权限选中:', this.checkedMenuKeys)
      console.log('功能权限选中:', this.checkedFunctionKeys)
    },

    // 菜单权限选中变化（保留兼容性）
    onMenuPermissionCheck(checkedKeys) {
      this.onPermissionCheck(checkedKeys)
    },

    // 提交权限设置
    handlePermissionsSubmit() {
      if (!this.currentRecord) {
        this.$message.error('当前操作的商户信息不存在')
        return
      }

      if (!this.originalPermissionsData) {
        this.$message.error('权限数据不完整，请刷新页面重试')
        return
      }

      try {
        // 确保checkedFunctionKeys和checkedMenuKeys始终是数组
        if (!Array.isArray(this.checkedFunctionKeys)) {
          this.checkedFunctionKeys = []
        }

        if (!Array.isArray(this.checkedMenuKeys)) {
          this.checkedMenuKeys = []
        }

        console.log('准备提交权限, 选中的菜单键:', this.checkedMenuKeys)

        // 创建原始数据的深拷贝
        const updatedPermissionsData = JSON.parse(JSON.stringify(this.originalPermissionsData))

        // 使用新的安全方法更新菜单权限
        console.log('使用新的安全方法更新菜单权限...')
        if (updatedPermissionsData.menu_permissions_definition) {
          updatedPermissionsData.menu_permissions_definition = this.safeUpdateMenuPermissions(
            updatedPermissionsData.menu_permissions_definition,
            this.checkedMenuKeys
          )
        }

        // 更新功能权限状态
        if (updatedPermissionsData.function_permission_definition) {
          Object.keys(updatedPermissionsData.function_permission_definition).forEach(key => {
            const isEnabled = this.checkedFunctionKeys.includes(key)
            updatedPermissionsData.function_permission_definition[key] = isEnabled
            console.log(`功能权限 ${key} 状态更新为: ${isEnabled ? '启用' : '禁用'}`)
          })
        }

        // 提交数据，使用原始字段名称
        const submitData = {
          menu_permissions_definition: updatedPermissionsData.menu_permissions_definition,
          function_permission_definition: updatedPermissionsData.function_permission_definition
        }

        // 数据校验
        let hasEnabledMenu = false
        const validateMenus = (items) => {
          if (!items || !Array.isArray(items)) return
          for (const item of items) {
            if (item.enabled) {
              hasEnabledMenu = true
              return
            }
            if (item.sub_menu) validateMenus(item.sub_menu)
            if (item.page) validateMenus(item.page)
          }
        }

        validateMenus(submitData.menu_permissions_definition)

        // 未找到启用的菜单但有选中项，可能是数据问题
        if (!hasEnabledMenu && this.checkedMenuKeys.length > 0) {
          console.warn('严重警告: 未找到任何启用状态的菜单，但有选中的菜单项')
          this.$confirm({
            title: '数据异常警告',
            content: '系统检测到权限数据可能存在问题，您选择的菜单权限可能无法正确保存。是否继续提交？',
            okText: '继续提交',
            cancelText: '取消',
            onOk: () => {
              this.executePermissionSubmit(submitData)
            }
          })
        } else {
          this.executePermissionSubmit(submitData)
        }
      } catch (error) {
        console.error('提交权限设置时出错:', error)
        this.$message.error(`提交权限设置时出错: ${error.message || '未知错误'}`)
        this.permissionSubmitting = false
      }
    },

    // 执行权限提交
    executePermissionSubmit(submitData) {
      console.log('提交权限数据:', JSON.stringify(submitData, null, 2))

      // 显示提交中状态
      this.$set(this, 'permissionSubmitting', true)

      updateMerchantPermissions(this.currentRecord.admin_id, submitData)
        .then(() => {
          // 成功反馈
          this.$notification.success({
            message: '权限分配成功',
            description: `已成功更新 ${this.currentRecord.mchname} 的权限设置，新的权限设置将在商户下次登录时生效。`,
            duration: 4
          })
          this.permissionsVisible = false
          this.permissionSubmitting = false
        })
        .catch(err => {
          this.$message.error(`权限设置失败: ${err.message || '未知错误'}`)
          this.permissionSubmitting = false
        })
    },

    // 取消权限分配
    handlePermissionsCancel() {
      this.permissionsVisible = false
      this.menuPermissionsTree = []
      this.checkedMenuKeys = []
      this.expandedMenuKeys = []
      this.functionPermissions = []
      this.checkedFunctionKeys = []
      this.originalPermissionsData = null
      this.permissionKeyMap = new Map()
    },

    // 全局错误处理
    handleGlobalError(event) {
      // 检查是否是导航路径错误
      if (event && event.message && (
        event.message.includes('Cannot read properties') ||
        event.message.includes('无法导航到路径') ||
        event.message.includes('无法在数组中导航')
      )) {
        console.warn('捕获到路径导航错误:', event.message)
        // 防止连续出现太多错误提示
        if (!this.errorNotified) {
          this.errorNotified = true
          this.$message.warning('权限数据存在问题，部分功能可能无法正常工作')
          setTimeout(() => { this.errorNotified = false }, 5000)
        }
      }
    },

    // 安全地更新菜单权限，新增
    safeUpdateMenuPermissions(menuStructure, checkedKeys) {
      if (!menuStructure || !Array.isArray(menuStructure)) {
        console.warn('菜单结构无效:', menuStructure)
        return menuStructure
      }

      if (!checkedKeys || !Array.isArray(checkedKeys)) {
        console.warn('选中的键无效:', checkedKeys)
        checkedKeys = []
      }

      console.log('更新菜单权限, 当前选中的键:', JSON.stringify(checkedKeys))

      const updateMenuEnabled = (menuItems) => {
        if (!menuItems || !Array.isArray(menuItems)) return menuItems

        return menuItems.map(item => {
          if (!item || typeof item !== 'object') return item

          // 创建副本避免修改原对象
          const updatedItem = { ...item }

          // 更新enabled状态
          if (updatedItem.permission) {
            const isEnabled = checkedKeys.includes(updatedItem.permission)
            updatedItem.enabled = isEnabled
            console.log(`更新节点 ${updatedItem.meta && updatedItem.meta.title || updatedItem.permission} 状态: ${isEnabled ? '启用' : '禁用'}`)
          }

          // 递归处理子菜单
          if (updatedItem.sub_menu && Array.isArray(updatedItem.sub_menu)) {
            updatedItem.sub_menu = updateMenuEnabled(updatedItem.sub_menu)
          }

          // 递归处理页面
          if (updatedItem.page && Array.isArray(updatedItem.page)) {
            updatedItem.page = updateMenuEnabled(updatedItem.page)
          }

          return updatedItem
        })
      }

      const result = updateMenuEnabled(menuStructure)

      // 验证是否成功更新了权限
      let foundEnabled = false
      const validateEnabled = (items) => {
        if (!items || !Array.isArray(items)) return

        for (const item of items) {
          if (item.enabled) {
            foundEnabled = true
            console.log(`找到启用的节点: ${item.meta && item.meta.title || item.permission}`)
            return
          }

          if (item.sub_menu) validateEnabled(item.sub_menu)
          if (item.page) validateEnabled(item.page)
        }
      }

      validateEnabled(result)
      if (!foundEnabled && checkedKeys.length > 0) {
        console.warn('未能找到任何启用状态的节点，可能更新失败')
      }

      return result
    },

    // 菜单展开变化
    onExpandChange(expandedKeys) {
      this.expandedMenuKeys = expandedKeys
      console.log('展开的节点已更新:', expandedKeys)
    }
  }
}
</script>

<style lang="less" scoped>
.merchant-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
}

.table-page-search-wrapper {
  margin-bottom: 24px;
  padding: 12px 24px;
  background-color: #fafafa;
  border-radius: 4px;

  .ant-form-item {
    margin-bottom: 16px;
  }
}

.table-page-search-buttons {
  text-align: right;

  .ant-btn {
    margin-left: 8px;
    margin-bottom: 0;

    &:first-child {
      margin-left: 0;
    }
  }
}

.selected-alert {
  margin-bottom: 16px;
}

.merchant-table {
  .ant-table-thead>tr>th {
    background-color: #f5f5f5;
    font-weight: 600;
  }
}

.amount-text {
  color: #52c41a;
  font-weight: 500;
}

.balance-text {
  color: #1890ff;
  font-weight: 500;
}

// 操作按钮样式
.action-menu {
  .menu-item-with-icon {
    display: flex;
    align-items: center;

    .anticon {
      margin-right: 8px;
      font-size: 14px;

      &.icon-blue {
        color: #1890ff;
      }

      &.icon-green {
        color: #52c41a;
      }

      &.icon-red {
        color: #f5222d;
      }

      &.icon-orange {
        color: #fa8c16;
      }
    }
  }
}

// 商户表单样式
.merchant-form {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .business-type-group {
    display: flex;
    flex-wrap: wrap;

    .ant-checkbox-wrapper {
      width: 50%;
      margin-left: 0;
      margin-bottom: 8px;
    }
  }
}

// 密码表单样式
.password-form {
  .ant-form-item {
    margin-bottom: 16px;
  }
}

// 权限分配样式
.permissions-header {
  margin-bottom: 24px;
  font-size: 16px;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #eef5fe;
  border-radius: 4px;
  border-left: 4px solid #409EFF;

  .permissions-icon {
    margin-right: 8px;
    color: #409EFF;
    font-size: 18px;
  }

  .permissions-merchant {
    font-weight: 600;
    color: #409EFF;
  }

  .permissions-separator {
    margin: 0 8px;
    color: #d9d9d9;
  }
}

.permissions-tabs {
  .ant-tabs-bar {
    margin-bottom: 16px;
    border-bottom: 2px solid #f0f0f0;
  }

  .ant-tabs-tab {
    padding: 12px 16px;

    &.ant-tabs-tab-active {
      color: #1890ff;
      font-weight: 500;
    }
  }

  .ant-tabs-ink-bar {
    background-color: #1890ff;
    height: 3px;
  }
}

.permissions-tree-container {
  height: 450px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .tree-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
}

// 树组件样式
/deep/ .permissions-tree {
  .ant-tree-node-content-wrapper {
    padding: 6px 8px;
    margin: 2px 0;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.ant-tree-node-selected {
      background-color: #e6f7ff;
    }
  }

  .ant-tree-checkbox {
    margin-right: 8px;
  }

  .ant-tree-treenode-switcher-open,
  .ant-tree-treenode-switcher-close {
    margin-bottom: 4px;
  }

  .tree-node-title {
    display: flex;
    align-items: center;

    .menu-icon,
    .menu-icon-default {
      margin-right: 8px;
      font-size: 14px;
      width: 16px;
      text-align: center;
      color: #1890ff;
    }

    .menu-icon-default {
      color: #bfbfbf;
    }
  }
}

.permissions-list {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #e8e8e8;
  background-color: #ffffff;
  border-radius: 4px;
  height: 450px;
  overflow-y: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .function-checkboxes {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .function-checkbox-item {
      width: 50%;
      margin-left: 0;
      margin-bottom: 16px;
      height: 32px;
      line-height: 32px;
      padding: 0 16px;
      transition: all 0.3s;
      border-radius: 4px;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

// 权限分配样式
.permissions-tips {
  margin-top: 24px;

  .ant-alert {
    border-color: #91d5ff;
    background-color: #e6f7ff;

    .ant-alert-message {
      color: #1890ff;

      div:first-child {
        font-weight: 500;
        margin-bottom: 4px;
      }

      div {
        line-height: 1.8;
      }
    }
  }
}

// 模态框通用样式
.merchant-modal,
.password-modal,
.permissions-modal {
  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;

    .ant-btn {
      margin-left: 8px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

// 模态框按钮样式优化
.permissions-modal {
  .ant-modal-header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e8e8e8;

    .ant-modal-title {
      color: #409EFF;
      font-weight: 500;
    }
  }

  .ant-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    background-color: #f5f7fa;

    .ant-btn-primary {
      background-color: #409EFF;
      border-color: #409EFF;
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);

      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
    }
  }
}

.page-header-wrapper {
  padding: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-buttons {
  display: flex;
  gap: 8px;
}

.user-item {
  margin-bottom: 16px;
}

.user-action {
  margin-left: 8px;
}

.filter-container {
  margin-bottom: 16px;
}

.list-status-badge {
  &.status-enabled {
    color: #52c41a;
    background-color: #f6ffed;
    border-color: #b7eb8f;
  }

  &.status-disabled {
    color: #ff4d4f;
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
}

.permissions-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.merchant-info {
  h3 {
    margin: 0 0 4px 0;
    font-size: 18px;
  }

  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.45);
  }
}

.permission-tabs {
  min-height: 300px;
}

.function-permissions {
  display: flex;
  flex-wrap: wrap;
}

.w100 {
  width: 50%;
  margin-bottom: 12px;
}

.mb-3 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 20px;
}

.mr-3 {
  margin-right: 16px;
}

.text-muted {
  color: rgba(0, 0, 0, 0.45);
}

.small {
  font-size: 12px;
}

.function-checkbox-item {
  width: 50%;
  margin-left: 0;
  margin-bottom: 12px;
  padding: 8px 12px;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

/deep/ .ant-tree-treenode {
  padding: 4px 0;

  .ant-tree-node-content-wrapper {
    padding: 4px 8px;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .ant-tree-switcher {
    width: 22px;
  }

  .ant-tree-checkbox {
    margin-right: 6px;
  }
}

/deep/ .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #e6f7ff;
}

.node-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 4px;
  transition: color 0.3s;
}

/deep/ .ant-tree-node-content-wrapper:hover .node-title {
  color: #1890ff;
}

.menu-icon {
  font-size: 14px;
  width: 16px;
  text-align: center;
  color: #1890ff;
  margin-right: 4px;
}

.menu-icon-default {
  font-size: 14px;
  width: 16px;
  text-align: center;
  color: #bfbfbf;
  margin-right: 4px;
}
</style>

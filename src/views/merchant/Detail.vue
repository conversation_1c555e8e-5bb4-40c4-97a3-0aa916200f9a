<template>
  <page-header-wrapper
    :title="merchantInfo.mchname"
    :tab-list="tabList"
    :tab-active-key="tabActiveKey"
    @tabChange="handleTabChange"
  >
    <template slot="extra">
      <a-button type="primary" @click="handleEdit">
        <a-icon type="edit" />编辑
      </a-button>
      <a-button @click="handleBack" style="margin-left: 8px">
        <a-icon type="arrow-left" />返回
      </a-button>
    </template>

    <!-- 基本信息 -->
    <div v-if="tabActiveKey === 'basic'">
      <a-card :bordered="false">
        <a-descriptions title="商户基本信息" :column="2">
          <a-descriptions-item label="商户编号">{{ merchantInfo.admin_id }}</a-descriptions-item>
          <a-descriptions-item label="商户名称">{{ merchantInfo.mchname }}</a-descriptions-item>
          <a-descriptions-item label="手机号码">{{ merchantInfo.cellphone }}</a-descriptions-item>
          <a-descriptions-item label="邮箱">{{ merchantInfo.email }}</a-descriptions-item>
          <a-descriptions-item label="交易总额">
            <span style="color: #52c41a">¥{{ merchantInfo.total_amount || '0.00' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="账户余额">
            <span style="color: #1890ff">¥{{ merchantInfo.balance || '0.00' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge
              :status="merchantInfo.is_freeze === 0 ? 'success' : 'error'"
              :text="merchantInfo.is_freeze === 0 ? '正常' : '已冻结'"
            />
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ merchantInfo.create_time }}</a-descriptions-item>
          <a-descriptions-item label="业务类型">
            <a-tag
              v-for="type in merchantInfo.businessTypes"
              :key="type"
              color="blue"
            >
              {{ getBusinessTypeLabel(type) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 分台管理 -->
    <div v-if="tabActiveKey === 'branch'">
      <a-card :bordered="false">
        <a-tree
          showLine
          :tree-data="branchTreeData"
          :defaultExpandAll="true"
        />
      </a-card>
    </div>

    <!-- 权限管理 -->
    <div v-if="tabActiveKey === 'permission'">
      <a-card :bordered="false">
        <a-tabs v-model="permissionsActiveTab">
          <a-tab-pane key="menu" tab="菜单权限">
            <div class="permissions-tree-container">
              <a-tree
                checkable
                :tree-data="menuPermissionsTree"
                :checked-keys="checkedMenuKeys"
                :auto-expand-parent="true"
                :selectable="false"
                :default-expanded-keys="expandedMenuKeys"
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="function" tab="功能权限">
            <div class="permissions-list">
              <a-row>
                <a-col :span="8" v-for="func in functionPermissions" :key="func.key">
                  <a-checkbox
                    disabled
                    :checked="checkedFunctionKeys.includes(func.key)"
                  >
                    {{ func.label }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>
        </a-tabs>
        <div class="permission-actions">
          <a-button type="primary" @click="handlePermissions">
            <a-icon type="setting" />修改权限
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 修改商户对话框 -->
    <a-modal
      title="编辑商户"
      :visible="visible"
      :width="750"
      :maskClosable="false"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-model-item label="商户名称" prop="mchname">
          <a-input v-model="form.mchname" placeholder="请输入商户名称" :maxLength="100" />
        </a-form-model-item>
        <a-form-model-item label="手机号码" prop="cellphone">
          <a-input v-model="form.cellphone" placeholder="请输入手机号码" :maxLength="11" />
        </a-form-model-item>
        <a-form-model-item label="邮箱" prop="email">
          <a-input v-model="form.email" placeholder="请输入邮箱地址" :maxLength="100" />
        </a-form-model-item>
        <a-form-model-item label="业务类型" prop="businessTypes">
          <a-checkbox-group v-model="form.businessTypes">
            <a-checkbox
              v-for="item in businessTypeOptions"
              :key="item.key"
              :value="item.key"
            >
              {{ item.label }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 权限分配对话框 -->
    <a-modal
      title="权限分配"
      :visible="permissionsVisible"
      :width="800"
      :maskClosable="false"
      @ok="handlePermissionsSubmit"
      @cancel="handlePermissionsCancel"
    >
      <div class="permissions-header">
        <a-icon type="user" />
        <span>{{ merchantInfo.mchname }}</span>
        <span class="permissions-separator">-</span>
        <span>权限分配</span>
      </div>
      <a-tabs v-model="permissionsEditActiveTab">
        <a-tab-pane key="menu" tab="菜单权限">
          <div class="permissions-tree-container">
            <a-tree
              ref="menuPermissionTree"
              checkable
              :tree-data="menuPermissionsEditTree"
              :checked-keys="checkedMenuEditKeys"
              :auto-expand-parent="true"
              :default-expanded-keys="expandedMenuEditKeys"
              @check="onMenuPermissionCheck"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane key="function" tab="功能权限">
          <div class="permissions-list">
            <a-checkbox-group v-model="checkedFunctionEditKeys">
              <a-checkbox
                v-for="func in functionPermissions"
                :key="func.key"
                :value="func.key"
              >
                {{ func.label }}
              </a-checkbox>
            </a-checkbox-group>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import {
  getMerchantDetail,
  updateMerchant,
  getMerchantPermissions,
  updateMerchantPermissions,
  getMerchantBranchTree,
  getBusinessTypes
} from '@/api/merchant'

export default {
  name: 'MerchantDetail',
  data () {
    return {
      // 商户 ID
      merchantId: null,

      // 商户详情信息
      merchantInfo: {},

      // 标签页配置
      tabList: [
        { key: 'basic', tab: '基本信息' },
        { key: 'branch', tab: '分台管理' },
        { key: 'permission', tab: '权限管理' }
      ],
      tabActiveKey: 'basic',

      // 编辑商户相关
      visible: false,
      form: {},
      rules: {
        mchname: [
          { required: true, message: '请输入商户名称', trigger: 'blur' }
        ],
        cellphone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        email: [
          { required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '邮箱地址格式不正确', trigger: 'blur' }
        ],
        businessTypes: [
          { required: true, type: 'array', min: 1, message: '请至少选择一种业务类型', trigger: 'change' }
        ]
      },
      businessTypeOptions: [],

      // 分台树数据
      branchTreeData: [],

      // 权限管理相关
      permissionsActiveTab: 'menu',
      menuPermissionsTree: [],
      checkedMenuKeys: [],
      expandedMenuKeys: [],
      functionPermissions: [],
      checkedFunctionKeys: [],

      // 权限编辑相关
      permissionsVisible: false,
      permissionsEditActiveTab: 'menu',
      menuPermissionsEditTree: [],
      checkedMenuEditKeys: [],
      expandedMenuEditKeys: [],
      checkedFunctionEditKeys: []
    }
  },
  created () {
    // 从路由参数获取商户ID
    this.merchantId = this.$route.params.id

    // 获取业务类型选项
    this.getBusinessTypeOptions()

    // 获取商户详情
    this.fetchMerchantDetail()

    // 获取商户权限
    this.fetchMerchantPermissions()

    // 获取商户分台树
    this.fetchMerchantBranchTree()

    // 添加全局错误处理
    window.addEventListener('error', this.handleGlobalError)
  },
  beforeDestroy () {
    // 移除全局错误处理
    window.removeEventListener('error', this.handleGlobalError)
  },
  methods: {
    // 全局错误处理
    handleGlobalError (event) {
      // 检查是否是导航路径错误
      if (event && event.message && event.message.includes('Cannot read properties')) {
        console.warn('捕获到可能的导航路径错误:', event.message)
        // 不阻止默认行为，只记录信息
      }
    },
    // 获取业务类型选项
    getBusinessTypeOptions () {
      getBusinessTypes().then(res => {
        this.businessTypeOptions = res
      })
    },

    // 获取业务类型显示标签
    getBusinessTypeLabel (key) {
      const item = this.businessTypeOptions.find(item => item.key === key)
      return item ? item.label : key
    },

    // 获取商户详情
    fetchMerchantDetail () {
      getMerchantDetail(this.merchantId).then(res => {
        this.merchantInfo = res || {}
      })
    },

    // 获取商户分台树
    fetchMerchantBranchTree () {
      getMerchantBranchTree(this.merchantId).then(res => {
        // 确保res是数组，如果不是则尝试从res.data中获取
        let treeData = []
        if (Array.isArray(res)) {
          treeData = res
        } else if (res && res.data && Array.isArray(res.data)) {
          treeData = res.data
        } else if (res && Array.isArray(res.children)) {
          treeData = res.children
        }

        this.branchTreeData = this.formatBranchTreeData(treeData)
      }).catch(err => {
        console.error('获取商户分台树失败:', err)
        this.branchTreeData = []
      })
    },

    // 格式化分台树数据
    formatBranchTreeData (data) {
      // 确保data是数组
      if (!data || !Array.isArray(data)) {
        console.warn('商户分台树数据不是数组类型:', data)
        return []
      }

      const formatNode = node => {
        return {
          title: node.mchname,
          key: node.admin_id,
          children: node.children && node.children.length > 0 ? node.children.map(formatNode) : []
        }
      }

      return data.map(formatNode)
    },

    // 获取商户权限
    fetchMerchantPermissions () {
      getMerchantPermissions(this.merchantId).then(res => {
        if (!res) {
          console.warn('获取商户权限返回数据为空')
          return
        }

        try {
          // 添加数据检查
          if (!res.menu_permissions_definition) {
            console.warn('菜单权限定义不存在:', res)
          } else {
            // 处理菜单权限
            this.processMenuPermissions(res.menu_permissions_definition)
          }

          if (!res.function_permission_definition) {
            console.warn('功能权限定义不存在:', res)
          } else {
            // 处理功能权限
            this.processFunctionPermissions(res.function_permission_definition)
          }
        } catch (err) {
          console.error('处理权限数据出错:', err)
        }
      }).catch(err => {
        console.error('获取商户权限失败:', err)
      })
    },

    // 处理菜单权限树
    processMenuPermissions (menuPermissionDefinition) {
      this.checkedMenuKeys = []
      this.expandedMenuKeys = []

      // 检查输入数据有效性
      if (!menuPermissionDefinition || !Array.isArray(menuPermissionDefinition)) {
        console.warn('菜单权限定义数据无效:', menuPermissionDefinition)
        this.menuPermissionsTree = []
        return
      }

      // 添加输出日志
      console.log('处理菜单权限数据:', JSON.stringify(menuPermissionDefinition, null, 2))

      this.menuPermissionsTree = this.transformToTreeData(menuPermissionDefinition)

      // 递归查找选中的菜单权限
      const findCheckedKeys = (menus, currentPath = '') => {
        if (!menus || !Array.isArray(menus)) return

        menus.forEach((menu, index) => {
          if (!menu) return

          // 构建当前节点路径
          const menuPath = currentPath ? `${currentPath}.${index}` : `${index}`

          if (menu.enabled) {
            // 确保权限键有效
            if (menu.permission) {
              this.checkedMenuKeys.push(menu.permission)
              this.expandedMenuKeys.push(menu.permission)
              // 记录路径映射
              console.log(`找到启用的菜单: ${menu.permission}, 路径: ${menuPath}`)
            }
          }

          // 递归处理子菜单
          if (menu.sub_menu && Array.isArray(menu.sub_menu)) {
            findCheckedKeys(menu.sub_menu, `${menuPath}.sub_menu`)
          }

          // 递归处理页面权限
          if (menu.page && Array.isArray(menu.page)) {
            findCheckedKeys(menu.page, `${menuPath}.page`)
          }
        })
      }

      findCheckedKeys(menuPermissionDefinition)

      console.log('选中的菜单键:', this.checkedMenuKeys)
      console.log('展开的菜单键:', this.expandedMenuKeys)

      // 复制给权限编辑使用
      this.menuPermissionsEditTree = JSON.parse(JSON.stringify(this.menuPermissionsTree))
      this.checkedMenuEditKeys = [...this.checkedMenuKeys]
      this.expandedMenuEditKeys = [...this.expandedMenuKeys]
    },

    // 将后端数据转换为Tree组件所需的数据结构
    transformToTreeData (menuPermissions) {
      if (!menuPermissions || !Array.isArray(menuPermissions)) return []

      const transformWithPath = (items, path = '') => {
        if (!items || !Array.isArray(items)) return []

        return items.map((item, index) => {
          if (!item || typeof item !== 'object') return null

          // 构建当前节点路径
          const currentPath = path ? `${path}.${index}` : `${index}`
          const nodeKey = item.permission || `node_${Math.random().toString(36).substr(2, 9)}`

          // 创建节点对象
          const node = {
            key: nodeKey,
            title: item.meta && item.meta.title ? item.meta.title : (item.permission || '未命名'),
            // 存储路径信息，用于调试
            path: currentPath,
            children: []
          }

          // 处理子菜单
          if (item.sub_menu && Array.isArray(item.sub_menu) && item.sub_menu.length > 0) {
            const subMenuPath = `${currentPath}.sub_menu`
            const subMenuNodes = transformWithPath(item.sub_menu, subMenuPath)
            node.children = [...node.children, ...subMenuNodes]
          }

          // 处理页面
          if (item.page && Array.isArray(item.page) && item.page.length > 0) {
            const pagePath = `${currentPath}.page`
            const pageNodes = transformWithPath(item.page, pagePath)
            node.children = [...node.children, ...pageNodes]
          }

          return node
        }).filter(Boolean)
      }

      // 添加控制台输出，用于调试
      console.log('原始菜单权限数据:', JSON.stringify(menuPermissions, null, 2))
      const result = transformWithPath(menuPermissions)
      console.log('转换后的菜单树数据:', JSON.stringify(result, null, 2))

      return result
    },

    // 处理功能权限列表
    processFunctionPermissions (functionPermissionDefinition) {
      this.checkedFunctionKeys = []
      this.functionPermissions = []

      if (!functionPermissionDefinition) return

      // 转换功能权限数据结构
      this.functionPermissions = Object.keys(functionPermissionDefinition).map(key => {
        // 如果启用了该功能权限，添加到已选中列表
        if (functionPermissionDefinition[key]) {
          this.checkedFunctionKeys.push(key)
        }

        return {
          key,
          label: this.getFunctionPermissionLabel(key),
          value: functionPermissionDefinition[key]
        }
      })

      // 复制给权限编辑使用
      this.checkedFunctionEditKeys = [...this.checkedFunctionKeys]
    },

    // 获取功能权限的显示名称
    getFunctionPermissionLabel (key) {
      const labelMap = {
        'freshOrderNotice': '新订单通知',
        'provincialReportConfig': '省级定制客运监管服务配置',
        'exportData': '导出数据',
        'batchOperations': '批量操作'
      }
      return labelMap[key] || key
    },

    // 切换标签页
    handleTabChange (key) {
      this.tabActiveKey = key
    },

    // 返回列表
    handleBack () {
      this.$router.push('/merchant/list')
    },

    // 编辑商户
    handleEdit () {
      this.visible = true
      this.form = {
        admin_id: this.merchantInfo.admin_id,
        mchname: this.merchantInfo.mchname,
        cellphone: this.merchantInfo.cellphone,
        email: this.merchantInfo.email,
        businessTypes: this.merchantInfo.businessTypes || []
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },

    // 提交编辑表单
    handleSubmit () {
      this.$refs.form.validate(valid => {
        if (valid) {
          updateMerchant(this.merchantId, this.form)
            .then(() => {
              this.$message.success('商户信息更新成功')
              this.visible = false
              this.fetchMerchantDetail()
            })
            .catch(err => {
              this.$message.error(`商户信息更新失败：${err.message || '未知错误'}`)
            })
        }
      })
    },

    // 取消编辑
    handleCancel () {
      this.visible = false
    },

    // 打开权限编辑
    handlePermissions () {
      this.permissionsVisible = true
      this.permissionsEditActiveTab = 'menu'

      // 复制当前权限数据供编辑使用
      this.menuPermissionsEditTree = JSON.parse(JSON.stringify(this.menuPermissionsTree))
      this.checkedMenuEditKeys = [...this.checkedMenuKeys]
      this.expandedMenuEditKeys = [...this.expandedMenuKeys]
      this.checkedFunctionEditKeys = [...this.checkedFunctionKeys]
    },

    // 菜单权限选择变化
    onMenuPermissionCheck (checkedKeys) {
      this.checkedMenuEditKeys = checkedKeys
    },

    // 提交权限分配
    handlePermissionsSubmit () {
      // 构建菜单权限数据
      const menuPermissionsData = this.buildMenuPermissionsData()

      // 构建功能权限数据
      const functionPermissionData = {}
      this.functionPermissions.forEach(func => {
        functionPermissionData[func.key] = this.checkedFunctionEditKeys.includes(func.key)
      })

      // 提交数据
      updateMerchantPermissions(this.merchantId, {
        menu_permissions_definition: menuPermissionsData,
        function_permission_definition: functionPermissionData
      })
        .then(() => {
          this.$message.success('权限设置成功')
          this.permissionsVisible = false
          this.fetchMerchantPermissions()
        })
        .catch(err => {
          this.$message.error(`权限设置失败：${err.message || '未知错误'}`)
        })
    },

    // 构建菜单权限数据结构
    buildMenuPermissionsData () {
      // 用于调试的日志
      console.log('构建菜单权限数据 - 已选中的键:', this.checkedMenuEditKeys)
      console.log('构建菜单权限数据 - 编辑树:', JSON.stringify(this.menuPermissionsEditTree, null, 2))

      const findOriginMenuPermission = (permission, path) => {
        let found = null
        console.log('查找权限:', permission, '路径:', path)

        const search = (menus, currentPath = '') => {
          if (!menus || !Array.isArray(menus)) return

          for (let i = 0; i < menus.length; i++) {
            const menu = menus[i]
            if (!menu) continue

            // 构建路径字符串
            const menuPath = currentPath ? `${currentPath}.${i}` : `${i}`

            // 检查permission匹配或者路径匹配
            if (menu.permission === permission || (path && menuPath === path)) {
              found = menu
              console.log('找到匹配权限:', menu.permission, '路径:', menuPath)
              return
            }

            // 搜索子菜单
            if (menu.sub_menu && Array.isArray(menu.sub_menu)) {
              search(menu.sub_menu, menuPath + '.sub_menu')
              if (found) return
            }

            // 搜索页面权限
            if (menu.page && Array.isArray(menu.page)) {
              search(menu.page, menuPath + '.page')
              if (found) return
            }
          }
        }

        // 从原始权限定义中查找
        const originalPermissions = this.merchantInfo.permissions &&
                                   this.merchantInfo.permissions.menu_permissions_definition

        if (originalPermissions && Array.isArray(originalPermissions)) {
          search(originalPermissions)
        }

        return found
      }

      const buildMenuData = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return []

        return nodes.map(node => {
          if (!node) return null

          // 查找原始权限定义（优先使用路径查找）
          const originMenu = findOriginMenuPermission(node.key, node.path) || {
            permission: node.key,
            meta: { title: node.title },
            enabled: false
          }

          // 创建新的权限定义
          const menuData = {
            permission: node.key,
            meta: originMenu.meta || { title: node.title },
            enabled: this.checkedMenuEditKeys.includes(node.key)
          }

          // 处理子菜单
          if (node.children && node.children.length > 0) {
            // 区分原始数据中是子菜单还是页面
            const childrenData = buildMenuData(node.children)

            // 根据路径来确定是子菜单还是页面
            if (node.path && node.path.includes('.page.')) {
              menuData.page = childrenData
            } else {
              menuData.sub_menu = childrenData
            }
          }

          return menuData
        }).filter(Boolean) // 过滤掉无效节点
      }

      const result = buildMenuData(this.menuPermissionsEditTree)
      console.log('构建的菜单权限数据:', JSON.stringify(result, null, 2))
      return result
    },

    // 取消权限分配
    handlePermissionsCancel () {
      this.permissionsVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.permissions-header {
  margin-bottom: 24px;
  font-size: 16px;
  display: flex;
  align-items: center;

  .anticon {
    margin-right: 8px;
    color: #1890ff;
  }

  .permissions-separator {
    margin: 0 8px;
    color: #d9d9d9;
  }
}

.permissions-tree-container {
  height: 350px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 2px;
}

.permissions-list {
  padding: 16px;
  border: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 2px;

  .ant-checkbox-wrapper {
    margin-bottom: 12px;
    height: 32px;
    line-height: 32px;
    white-space: nowrap;
  }
}

.permission-actions {
  margin-top: 16px;
  text-align: right;
}
</style>

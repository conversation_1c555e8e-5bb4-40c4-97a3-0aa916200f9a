<template>
  <div class="component-container">
    <div class="withdrawal-application">
      <!-- 搜索表单 -->
      <div class="withdrawal-form">
        <div class="withdrawal-header">
          <div class="withdrawal-title">
            <i class="el-icon-search" /> 结算查询
          </div>
        </div>

        <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="small">
          <el-form-item label="线路分台">
            <branch-select
              v-model="searchForm.branch_id"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 统计卡片 -->
      <div class="balance-box">
        <div class="balance-item">
          <div class="balance-label">
            <i class="el-icon-time" /> 待审核提现
          </div>
          <div class="balance-value total-balance">{{ statistics.pending_count }}</div>
        </div>

        <div class="balance-item">
          <div class="balance-label">
            <i class="el-icon-money" /> 待审核金额
          </div>
          <div class="balance-value available-balance">¥{{ statistics.pending_amount }}</div>
        </div>

        <div class="balance-item">
          <div class="balance-label">
            <i class="el-icon-finished" /> 今日已审核
          </div>
          <div class="balance-value">{{ statistics.today_reviewed_count }}</div>
        </div>

        <div class="balance-item">
          <div class="balance-label">
            <i class="el-icon-success" /> 今日已通过
          </div>
          <div class="balance-value frozen-balance">¥{{ statistics.today_approved_amount }}</div>
        </div>
      </div>

      <!-- 提现列表 -->
      <div class="withdrawal-history">
        <div class="history-title">
          <i class="el-icon-time" /> 结算申请列表
        </div>

        <!-- 添加标签切换 -->
        <div class="history-tabs">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="全部" name="all">
              <span slot="label">
                全部
              </span>
            </el-tab-pane>
            <el-tab-pane label="待审核" name="pending">
              <span slot="label">
                待审核
                <el-badge v-if="pendingCount > 0" :value="pendingCount" class="tab-badge" />
              </span>
            </el-tab-pane>
            <el-tab-pane label="打款中" name="in_progress">
              <span slot="label">
                打款中
                <el-badge v-if="reviewingCount > 0" :value="reviewingCount" class="tab-badge" />
              </span>
            </el-tab-pane>
            <el-tab-pane label="已完成" name="completed">
              <span slot="label">
                已完成
                <el-badge v-if="completedCount > 0" :value="completedCount" class="tab-badge" />
              </span>
            </el-tab-pane>
          </el-tabs>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredWithdrawalList"
          border
          style="width: 100%"
          size="mini"
          :cell-style="{padding: '5px 0'}"
          :header-cell-style="{background:'#f5f7fa', color:'#606266', padding: '8px 0'}"
        >
          <el-table-column prop="date" label="申请时间" width="150" />
          <el-table-column prop="branchName" label="分台名称" min-width="120" />
          <el-table-column prop="amount" label="提现金额" width="100">
            <template slot-scope="scope">
              <span style="color: #f56c6c; font-weight: 500;">¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="提现周期" min-width="120" />
          <el-table-column prop="bankInfo" label="收款账户" min-width="180">
            <template slot-scope="scope">
              <div class="bank-info-cell">
                <div class="bank-name">{{ scope.row.bankInfo.bankName }}</div>
                <div class="account-name">{{ scope.row.bankInfo.accountName }}</div>
                <div class="card-number">{{ scope.row.bankInfo.cardNumber }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag size="mini" :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <div class="operation-btns">
                <el-button
                  type="text"
                  size="mini"
                  @click="viewWithdrawalDetail(scope.row)"
                >查看</el-button>
                <el-button
                  v-if="scope.row.status === '待审核'"
                  type="primary"
                  size="mini"
                  @click="handleReview(scope.row)"
                >审核</el-button>
                <el-button
                  v-if="scope.row.status === '打款中'"
                  type="success"
                  size="mini"
                  @click="handlePayment(scope.row)"
                >打款</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 详情对话框 -->
      <withdrawal-detail-dialog
        :visible.sync="detailDialogVisible"
        :withdrawal.sync="selectedWithdrawal"
      />

      <!-- 审核对话框 -->
      <el-dialog
        title="提现审核"
        :visible.sync="approvalDialogVisible"
        width="500px"
      >
        <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules">
          <el-form-item label="审核结果" prop="type">
            <el-radio-group v-model="approvalForm.type">
              <el-radio label="approve">通过</el-radio>
              <el-radio label="reject">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="approvalForm.type === 'reject'"
            label="驳回原因"
            prop="reason"
          >
            <el-input
              v-model="approvalForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入驳回原因"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitApproval"
          >确 定</el-button>
        </div>
      </el-dialog>

      <!-- 打款对话框 -->
      <el-dialog
        title="确认打款"
        :visible.sync="paymentDialogVisible"
        width="500px"
      >
        <el-form ref="paymentForm" :model="paymentForm" :rules="paymentRules" label-width="100px">
          <el-form-item label="提现金额">
            <span class="payment-amount">¥{{ selectedWithdrawal ? selectedWithdrawal.amount : '0.00' }}</span>
          </el-form-item>
          <el-form-item label="收款账户">
            <div v-if="selectedWithdrawal" class="bank-info-preview">
              <p>{{ selectedWithdrawal.bankInfo.accountName }}</p>
              <p>{{ selectedWithdrawal.bankInfo.bankName }}</p>
              <p>{{ selectedWithdrawal.bankInfo.cardNumber }}</p>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="paymentForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入打款备注信息"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="paymentDialogVisible = false">取 消</el-button>
          <el-button type="primary" :loading="submittingPayment" @click="submitPayment">确认打款</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import BranchSelect from '@/components/BranchSelect'
import { mapState } from 'vuex'
import {
  getApprovalsWithdrawals,
  getApprovalsBadge,
  getWithdrawalDetail,
  approveWithdrawal,
  paymentWithdrawal,
  getWithdrawalStatistics,
  getApprovalTimelines
} from '@/api/withdrawal'
import WithdrawalDetailDialog from './components/withdrawal-detail-dialog.vue'
import NoPermission from '@/components/NoPermission'
export default {
  name: 'WithdrawalReview',
  components: {
    BranchSelect,
    WithdrawalDetailDialog,
    NoPermission
  },
  data() {
    return {
      searchForm: {
        branch_id: 'all',
        status: '',
        start_date: '',
        end_date: ''
      },
      withdrawalList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      detailDialogVisible: false,
      selectedWithdrawal: null,
      approvalDialogVisible: false,
      approvalForm: {
        type: 'approve',
        reason: '',
        expectedTime: '',
        remark: ''
      },
      approvalRules: {
        type: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入驳回原因', trigger: 'blur' }
        ]
      },
      submitting: false,
      activeTab: 'all',
      pendingCount: 0,
      reviewingCount: 0,
      completedCount: 0,
      statistics: {
        pending_count: 0,
        pending_amount: 0,
        today_reviewed_count: 0,
        today_approved_amount: 0
      },
      paymentDialogVisible: false,
      paymentForm: {
        remark: ''
      },
      paymentRules: {
        remark: [
          { required: true, message: '请输入打款备注信息', trigger: 'blur' }
        ]
      },
      submittingPayment: false
    }
  },
  computed: {
    ...mapState({
      loginType: state => state.user.loginType
    }),
    filteredWithdrawalList() {
      return this.withdrawalList
    }
  },
  created() {
    this.getApprovalsWithdrawals()
    this.getStatistics()
  },
  methods: {
    getStatusType(status) {
      switch (status) {
        case 'pending': return 'warning'
        case 'in_progress': return 'warning'
        case 'completed': return 'success'
        case 'rejected': return 'danger'
        default: return 'info'
      }
    },
    handleSearch() {
      this.currentPage = 1
      this.getApprovalsWithdrawals()
      this.updateStatusCounts()
    },
    resetSearch() {
      this.searchForm = {
        branch_id: 'all',
        status: '',
        start_date: '',
        end_date: ''
      }
      this.handleSearch()
    },
    async getApprovalsWithdrawals() {
      try {
        this.loading = true
        const params = {
          page: this.currentPage,
          per_page: this.pageSize,
          branch_id: this.searchForm.branch_id !== 'all' ? this.searchForm.branch_id : undefined,
          start_date: this.searchForm.dateRange ? this.searchForm.dateRange[0] : undefined,
          end_date: this.searchForm.dateRange ? this.searchForm.dateRange[1] : undefined
        }

        // 当选择"全部"时，不传递status参数
        const status = this.activeTab === 'all' ? 'all' : this.activeTab
        const res = await getApprovalsWithdrawals(status, params)

        if (res.code === 200) {
          this.withdrawalList = res.data.data.map(item => ({
            id: item.id,
            date: item.created_at,
            branchName: item.branch_name,
            amount: item.total_payable_amount,
            period: item.start_date && item.end_date ? `${item.start_date} 至 ${item.end_date}` : '全部',
            bankInfo: {
              accountName: item.account,
              bankName: item.bank_name,
              cardNumber: item.bank_crad_number.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2'),
              branchName: item.branch_name
            },
            status: this.getStatusText(item.status),
            approveTime: item.payment_at,
            expectedTime: item.expected_time,
            rejectReason: item.reject_reason,
            remark: item.remarks
          }))
          this.total = res.data.meta.pagination.total
          await this.updateStatusCounts()
        }
      } catch (error) {
        this.$message.error('获取提现记录失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'in_progress': '打款中',
        'completed': '已完成',
        'failed': '已驳回'
      }
      return statusMap[status] || '未知'
    },
    async viewWithdrawalDetail(row) {
      try {
        // 获取结算单列表和时间线数据
        const [detailRes, timelineRes] = await Promise.all([
          getWithdrawalDetail(row.id, {
            page: 1,
            per_page: 10
          }),
          getApprovalTimelines(row.id)
        ])

        if (detailRes.code === 200 && timelineRes.code === 200) {
          // 使用时间线API的数据
          const timelineData = timelineRes.data || {}
          const detailData = detailRes.data || {}

          // 构建完整的时间线数据
          const timeline = []

          // 1. 添加提现申请记录（一定会有）
          if (timelineData.created_at) {
            timeline.push({
              timestamp: timelineData.created_at,
              content: '提交提现申请',
              type: 'primary'
            })
          }

          // 2. 添加审核记录
          if (timelineData.approved_at) {
            timeline.push({
              timestamp: timelineData.approved_at,
              content: timelineData.failure_reason
                ? `审核驳回：${timelineData.failure_reason}（审核人：${(timelineData.approver && timelineData.approver.account) || '系统'}）`
                : `审核通过 （审核人：${(timelineData.approver && timelineData.approver.account) || '系统'}）`,
              type: timelineData.failure_reason ? 'danger' : 'success'
            })
          }

          // 3. 添加打款记录（如果有）
          if (timelineData.payment_at) {
            timeline.push({
              timestamp: timelineData.payment_at,
              content: `打款完成（打款人：${(timelineData.payer && timelineData.payer.account) || '系统'}）`,
              type: 'success'
            })
          }

          // 处理银行信息
          const bankInfo = {
            accountName: detailData.account || timelineData.account || (row.bankInfo && row.bankInfo.accountName) || '',
            bankName: detailData.bank_name || timelineData.bank_name || (row.bankInfo && row.bankInfo.bankName) || '',
            cardNumber: (detailData.bank_crad_number || timelineData.bank_crad_number || (row.bankInfo && row.bankInfo.cardNumber) || '')
              .replace(/(\d{4})(?=\d)/g, '$1 ') // 每4位数字后添加空格
          }

          this.selectedWithdrawal = {
            ...row,
            timeline,
            approveTime: timelineData.approved_at,
            rejectReason: timelineData.failure_reason,
            status: timelineData.cn_status || this.getStatusText(timelineData.status),
            remark: timelineData.remarks || '无',
            totalOrders: timelineData.total_order_count || detailData.total_order_count,
            settlement_sheets: detailRes.data,
            bankInfo
          }
          this.detailDialogVisible = true
        }
      } catch (error) {
        console.error('获取提现详情失败:', error)
        this.$message.error('获取提现详情失败，请稍后重试')
      }
    },
    handleReview(row) {
      this.selectedWithdrawal = row
      this.approvalForm = {
        type: 'approve',
        reason: '',
        expectedTime: '',
        remark: ''
      }
      this.approvalDialogVisible = true
    },
    async submitApproval() {
      this.$refs.approvalForm.validate(async (valid) => {
        if (valid) {
          try {
            this.submitting = true
            const data = {
              expected_time: this.approvalForm.expectedTime,
              remark: this.approvalForm.remark
            }

            let res
            if (this.approvalForm.type === 'approve') {
              res = await approveWithdrawal(this.selectedWithdrawal.id, 'approve', data)
            } else {
              res = await approveWithdrawal(this.selectedWithdrawal.id, 'failed', {
                ...data,
                failure_reason: this.approvalForm.reason
              })
            }

            if (res.code === 200) {
              this.$message.success(
                this.approvalForm.type === 'approve'
                  ? '提现申请已通过'
                  : '提现申请已驳回'
              )
              this.approvalDialogVisible = false
              await this.getApprovalsWithdrawals()
            }
          } catch (error) {
            console.error('审核提现申请失败:', error)
            this.$message.error('审核提现申请失败，请稍后重试')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getApprovalsWithdrawals()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getApprovalsWithdrawals()
    },
    handleTabClick(tab) {
      this.currentPage = 1
      this.getApprovalsWithdrawals()
    },
    async updateStatusCounts() {
      try {
        const params = {
          branch_id: this.searchForm.branch_id !== 'all' ? this.searchForm.branch_id : undefined,
          start_date: this.searchForm.dateRange ? this.searchForm.dateRange[0] : undefined,
          end_date: this.searchForm.dateRange ? this.searchForm.dateRange[1] : undefined
        }

        const res = await getApprovalsBadge(params)
        if (res.code === 200) {
          this.pendingCount = res.data.total_pending
          this.reviewingCount = res.data.total_in_progress
          this.completedCount = res.data.total_completed
        }
      } catch (error) {
        console.error('获取状态数量失败:', error)
      }
    },
    async getStatistics() {
      try {
        const res = await getWithdrawalStatistics()
        if (res.code === 200) {
          this.statistics = res.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败，请稍后重试')
      }
    },
    getTimelineIcon(type) {
      const iconMap = {
        primary: 'el-icon-edit',
        success: 'el-icon-check',
        warning: 'el-icon-warning',
        danger: 'el-icon-close'
      }
      return iconMap[type] || 'el-icon-edit'
    },
    getTimelineColor(type) {
      const colorMap = {
        primary: '#409EFF',
        success: '#67C23A',
        warning: '#E6A23C',
        danger: '#F56C6C',
        info: '#909399'
      }
      return colorMap[type] || '#409EFF'
    },
    getTimelineType(action) {
      const typeMap = {
        'submit': 'primary',
        'approve': 'success',
        'reject': 'danger',
        'payment': 'success'
      }
      return typeMap[action] || 'info'
    },
    handlePayment(row) {
      this.selectedWithdrawal = row
      this.paymentForm = {
        remark: ''
      }
      this.paymentDialogVisible = true
    },
    async submitPayment() {
      try {
        this.submittingPayment = true
        const data = {
          pay_remark: this.paymentForm.remark
        }

        const res = await paymentWithdrawal(this.selectedWithdrawal.id, data)

        if (res.code === 200) {
          this.$message.success('打款确认成功')
          this.paymentDialogVisible = false
          await this.getApprovalsWithdrawals()
        }
      } catch (error) {
        console.error('确认打款失败:', error)
        this.$message.error('确认打款失败，请稍后重试')
      } finally {
        this.submittingPayment = false
      }
    }
  }
}
</script>

<style scoped>
.balance-box {
  display: flex;
  flex-wrap: nowrap;
  gap: 24px;
  margin-bottom: 24px;
  overflow-x: auto;
}

.balance-item {
  flex: 1;
  min-width: 200px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
}

.balance-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.balance-label {
  font-size: 15px;
  color: #606266;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.balance-label i {
  margin-right: 8px;
  font-size: 16px;
}

.balance-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

.total-balance {
  color: #409eff;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.available-balance {
  color: #f56c6c;
  background: linear-gradient(135deg, #ff4d4f 0%, #f56c6c 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.frozen-balance {
  color: #67c23a;
  background: linear-gradient(135deg, #52c41a 0%, #67c23a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.withdrawal-form {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.withdrawal-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

.withdrawal-history {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  margin-bottom: 0;
}

.history-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
  display: flex;
  align-items: center;
}

.history-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

.history-tabs {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 0 15px;
  border-radius: 4px;
}

.tab-badge {
  margin-left: 8px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 500;
}

:deep(.el-badge__content) {
  background-color: #f56c6c;
}

.detail-content {
  padding: 20px;
}

.bank-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.bank-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.bank-row:last-child {
  margin-bottom: 0;
}

.bank-label {
  color: #606266;
  margin-right: 10px;
  width: 80px;
}

.bank-value {
  color: #303133;
  font-weight: 500;
}

.timeline-wrapper {
  margin-top: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #303133;
}

:deep(.el-timeline) {
  padding-left: 16px;
}

:deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
}

:deep(.el-timeline-item__node--large) {
  width: 16px;
  height: 16px;
}

:deep(.el-timeline-item__content) {
  color: #303133;
  font-size: 14px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-text {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
}

.timeline-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.withdrawal-application {
  padding: 24px 10px;
  min-height: 100vh;
  background-color: #fff;
}

.pagination-container {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.bank-info-cell {
  padding: 4px 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bank-name {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.account-name {
  font-size: 12px;
  color: #606266;
}

.card-number {
  font-size: 12px;
  color: #909399;
}

.operation-btns {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.operation-btns .el-button {
  margin: 0;
  padding: 3px 8px;
}

.el-table {
  font-size: 13px;
}

.el-table th {
  font-weight: 500;
}

.el-table td {
  height: auto;
}

.payment-amount {
  font-size: 24px;
  color: #f56c6c;
  font-weight: bold;
}

.bank-info-preview {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.bank-info-preview p {
  margin: 8px 0;
  color: #606266;
}

.payment-proof-uploader {
  margin-bottom: 8px;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}
</style>

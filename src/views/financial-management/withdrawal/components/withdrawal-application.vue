<template>
  <div class="withdrawal-application" :compact="compact ? 'true' : null">
    <!-- 只在不是onlyShowHistory时显示这些内容 -->
    <template v-if="!onlyShowHistory">
      <!-- 总台标题 -->
      <div class="merchant-header">
        <div class="merchant-title">
          <i class="fas fa-building" />  {{ merchantName || '商户信息加载中...' }}
        </div>
      </div>

      <!-- 账户余额信息 -->
      <div class="balance-box">
        <div class="balance-item">
          <div class="balance-label">
            <i class="fas fa-wallet" /> 账户总交易金额
          </div>
          <div class="balance-value total-balance">¥{{ accountBalance.total }}</div>
        </div>

        <div class="balance-item">
          <div class="balance-label">
            <i class="fas fa-money-bill-wave" /> 待结算金额
          </div>
          <div class="balance-value available-balance">¥{{ accountBalance.available }}</div>
        </div>

        <div class="balance-item">
          <div class="balance-label">
            <i class="fas fa-lock" /> 已结算金额
          </div>
          <div class="balance-value frozen-balance">¥{{ accountBalance.frozen || '0.00' }}</div>
        </div>
      </div>

      <!-- 提现表单 -->
      <div class="withdrawal-form">
        <div class="withdrawal-header">
          <div class="withdrawal-title">
            <i class="el-icon-bank-card" /> 提现申请
          </div>
        </div>

        <el-form ref="withdrawalForm" :model="withdrawalForm" label-width="100px" :rules="rules" class="withdrawal-form-content">
          <!-- 选择银行卡 -->
          <el-form-item label="到账银行卡" prop="bankCardId">
            <el-select v-model="withdrawalForm.bankCardId" placeholder="请选择到账银行卡" style="width: 100%;">
              <el-option
                v-for="card in bankCards"
                :key="card.id"
                :label="card.bankName + ' (' + card.cardNumber + ')'"
                :value="card.id"
              />
            </el-select>

            <!-- 银行卡信息展示 -->
            <div v-if="selectedCard" class="bank-info-section">
              <div class="bank-title">
                <div class="bank-title-text">{{ selectedCard.bankName }}</div>
                <div class="bank-actions">
                  <el-button type="text" @click="editCard(selectedCard)">
                    <i class="el-icon-edit" /> 编辑
                  </el-button>
                  <el-button type="text" style="color: #f56c6c;" @click="deleteCard(selectedCard)">
                    <i class="el-icon-delete" /> 删除
                  </el-button>
                </div>
              </div>
              <div class="bank-info">
                <div class="bank-info-item">
                  <span class="bank-label">开户名：</span>
                  <span class="bank-value">{{ selectedCard.accountName }}</span>
                </div>
                <div class="bank-info-item">
                  <span class="bank-label">银行卡号：</span>
                  <span class="bank-value">{{ selectedCard.cardNumber }}</span>
                </div>
              </div>
            </div>

            <el-button type="text" class="add-card-btn" @click="showAddCardDialog">
              <i class="el-icon-plus" /> 添加新的银行卡
            </el-button>
          </el-form-item>

          <!-- 提现金额 -->
          <el-form-item label="提现金额" prop="amount" class="form-item-gap">
            <el-input v-model="accountBalance.available" placeholder="" type="number" disabled>
              <template slot="prepend">¥</template>
            </el-input>
            <div v-if="Number(accountBalance.available) === 0" class="error-tip" style="color: #F56C6C; font-size: 12px; margin-top: 5px;">
              <i class="el-icon-warning" /> 暂无可提现金额
            </div>
          </el-form-item>

          <!-- 提现周期 -->
          <el-form-item label="提现周期" class="form-item-gap">
            <el-input :value="withdrawalPeriod" disabled />
          </el-form-item>

          <!-- 备注信息 -->
          <el-form-item label="备注信息" class="form-item-gap">
            <el-input v-model="withdrawalForm.remarks" type="textarea" placeholder="请输入备注信息（选填）" :rows="3" />
          </el-form-item>

          <!-- 提交按钮 -->
          <el-form-item class="form-actions">
            <el-button type="primary" :loading="submitting" :disabled="Number(accountBalance.available) === 0" @click="submitWithdrawal">提交申请</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </template>

    <!-- 提现记录，只在不隐藏历史记录时显示 -->
    <div v-if="!hideHistory" class="withdrawal-history">
      <div class="history-title">
        <i class="el-icon-time" /> 最近结算申请记录
      </div>

      <!-- 添加标签切换 -->
      <div class="history-tabs">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="全部" name="all">
            <span slot="label">
              全部
            </span>
          </el-tab-pane>
          <el-tab-pane label="待审核" name="pending">
            <span slot="label">
              待审核
              <el-badge v-if="pendingCount > 0" :value="pendingCount" class="tab-badge" />
            </span>
          </el-tab-pane>
          <el-tab-pane label="打款中" name="in_progress">
            <span slot="label">
              打款中
              <el-badge v-if="reviewingCount > 0" :value="reviewingCount" class="tab-badge" />
            </span>
          </el-tab-pane>
          <el-tab-pane label="已完成" name="completed">
            <span slot="label">
              已完成
              <el-badge v-if="completedCount > 0" :value="completedCount" class="tab-badge" />
            </span>
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-table :data="filteredWithdrawalHistory" border style="width: 100%" size="mini">
        <el-table-column prop="date" label="申请时间" width="180" />
        <el-table-column prop="amount" label="申请金额" width="120">
          <template slot-scope="scope">
            <span style="color: #f56c6c;">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="提现周期" min-width="120" />
        <el-table-column prop="bankInfo" label="收款账户" min-width="200">
          <template slot-scope="scope">
            <div class="bank-info-cell">
              <span class="bank-name">{{ scope.row.bankInfo.bankName }}</span>
              <span class="separator">|</span>
              <span class="account-name">{{ scope.row.bankInfo.accountName }}</span>
              <span class="separator">|</span>
              <span class="card-number">{{ scope.row.bankInfo.cardNumber && scope.row.bankInfo.cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewWithdrawalDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加银行卡对话框 -->
    <el-dialog
      :title="isEditCard ? '编辑银行卡' : '添加银行卡'"
      :visible.sync="addCardDialogVisible"
      width="500px"
      custom-class="bank-card-dialog"
      append-to-body
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <el-form ref="newCardForm" :model="newCardForm" label-width="100px" :rules="cardRules" class="bank-card-form">
        <el-form-item label="开户银行" prop="bankName" class="bank-form-item">
          <el-input v-model="newCardForm.bankName" placeholder="请输入开户银行" />
        </el-form-item>

        <el-form-item label="开户名" prop="account" class="bank-form-item">
          <el-input v-model="newCardForm.account" placeholder="请输入开户名" />
        </el-form-item>

        <el-form-item label="银行卡号" prop="bank_crad_number" class="bank-form-item">
          <el-input v-model="newCardForm.bank_crad_number" placeholder="请输入银行卡号" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="addCardDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveNewCard">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 提现详情对话框 -->
    <withdrawal-detail-dialog
      :visible.sync="detailDialogVisible"
      :withdrawal.sync="selectedWithdrawal"
    />
  </div>
</template>

<script>
import {
  getAccounts,
  getBanks,
  addBank,
  updateBank,
  deleteBank,
  getWithdrawals,
  submitWithdrawal,
  getWithdrawalsBadge,
  getWithdrawalDetail,
  getApprovalTimelines
} from '@/api/withdrawal'
import WithdrawalDetailDialog from './withdrawal-detail-dialog.vue'

export default {
  name: 'WithdrawalApplication',
  components: {
    WithdrawalDetailDialog
  },
  props: {
    // 添加接收查询参数的prop
    queryParams: {
      type: Object,
      default: () => ({})
    },
    // 是否只显示提现记录
    onlyShowHistory: {
      type: Boolean,
      default: false
    },
    // 是否隐藏提现记录
    hideHistory: {
      type: Boolean,
      default: false
    },
    // 是否使用紧凑模式（适用于弹窗）
    compact: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 账户余额信息
      accountBalance: {
        total: '0.00',
        available: '0.00',
        frozen: '0.00'
      },
      // 提现表单
      withdrawalForm: {
        bankCardId: '',
        amount: '',
        remarks: ''
      },
      // 是否免手续费
      isFreeWithdrawal: true,
      // 手续费率(%)
      feeRate: 0.3,
      // 提交状态
      submitting: false,
      // 银行卡列表
      bankCards: [],
      // 提现记录
      withdrawalHistory: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 对话框
      addCardDialogVisible: false,
      detailDialogVisible: false,
      selectedWithdrawal: null,
      // 新银行卡表单
      newCardForm: {
        id: null,
        bankName: '',
        account: '',
        bank_crad_number: ''
      },
      // 是否编辑模式
      isEditCard: false,
      // 表单验证规则
      rules: {
        bankCardId: [
          { required: true, message: '请选择到账银行卡', trigger: 'change' }
        ]
      },
      // 银行卡表单验证规则
      cardRules: {
        bankName: [
          { required: true, message: '请选择开户银行', trigger: 'change' }
        ],
        account: [
          { required: true, message: '请输入开户名', trigger: 'blur' }
        ],
        bank_crad_number: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' },
          { pattern: /^\d[\d\s]{10,20}$/, message: '请输入正确的银行卡号', trigger: 'blur' }
        ]
      },
      // 商户名称
      merchantName: '',
      // 标签切换
      activeTab: 'all',
      // 过滤后的提现记录
      filteredWithdrawalHistory: [],
      // 待审核、打款中、已完成提现记录的数量
      pendingCount: 0,
      reviewingCount: 0,
      completedCount: 0,
      // 订单列表分页
      orderListCurrentPage: 1,
      orderListPageSize: 10
    }
  },
  computed: {
    // 选中的银行卡
    selectedCard() {
      if (!this.withdrawalForm.bankCardId) return null
      return this.bankCards.find(card => card.id === this.withdrawalForm.bankCardId)
    },
    // 提现周期
    withdrawalPeriod() {
      // 优先从props获取日期参数
      const { start_date, end_date } = this.queryParams || this.$route.query
      if (start_date && end_date) {
        return `${start_date} 至 ${end_date}`
      }

      // 如果没有参数，返回空字符串
      return ''
    },
    // 提现手续费
    withdrawalFee() {
      if (!this.withdrawalForm.amount || this.isFreeWithdrawal) return '0.00'
      const amount = parseFloat(this.withdrawalForm.amount)
      if (isNaN(amount)) return '0.00'
      return (amount * this.feeRate / 100).toFixed(2)
    },
    // 实际到账金额
    computedActualAmount() {
      if (!this.withdrawalForm.amount) return '0.00'
      const amount = parseFloat(this.withdrawalForm.amount)
      if (isNaN(amount)) return '0.00'
      if (this.isFreeWithdrawal) return amount.toFixed(2)
      return (amount - parseFloat(this.withdrawalFee)).toFixed(2)
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        // 优先从props获取参数，然后从路由获取日期参数
        const params = this.queryParams || this.$route.query
        const { start_date, end_date, branch_id } = params

        // 只在非仅显示历史记录模式时检查日期参数
        if (!this.onlyShowHistory && (!start_date || !end_date)) {
          // 如果不是仅历史记录模式，且没有日期参数，则不加载账户信息和银行卡，只加载提现记录
          await this.loadWithdrawalHistory()
          return
        }

        // 如果有日期参数或是仅显示历史记录模式，正常加载数据
        if (!this.onlyShowHistory && start_date && end_date) {
          // 获取账户信息
          const accountRes = await getAccounts({
            start_date,
            end_date,
            branch_id
          })
          if (accountRes.code === 200) {
            const accountData = accountRes.data
            this.accountBalance = {
              total: accountData.total_amount || '0.00',
              available: accountData.withdrawal_amount || '0.00'
            }
            // 更新商户名称
            this.merchantName = accountData.mchname || ''
          }

          // 获取银行卡列表
          const banksRes = await getBanks({ branch_id })
          if (banksRes.code === 200) {
            this.bankCards = banksRes.data.map(bank => ({
              id: bank.id,
              bankName: bank.bank_name,
              accountName: bank.account,
              originalCardNumber: bank.bank_crad_number,
              cardNumber: bank.bank_crad_number.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2'),
              branchName: bank.branch_name || '',
              isDefault: bank.default === 1
            }))
          }
        }

        // 始终加载提现记录
        await this.loadWithdrawalHistory()
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('获取数据失败，请稍后重试')
      }
    },

    // 加载提现记录
    async loadWithdrawalHistory() {
      try {
        const params = {
          page: this.currentPage,
          per_page: this.pageSize
        }
        const status = this.activeTab === 'all' ? 'all' : this.activeTab
        const res = await getWithdrawals(status, params)
        if (res.code === 200) {
          this.withdrawalHistory = res.data.data.map(item => ({
            id: item.id,
            date: item.created_at,
            amount: item.total_payable_amount,
            period: item.start_date && item.end_date ? `${item.start_date} 至 ${item.end_date}` : '全部',
            bankInfo: {
              accountName: item.account || '未知',
              bankName: item.bank_name || '未知',
              cardNumber: item.bank_crad_number ? item.bank_crad_number.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2') : '未知',
              branchName: item.branch_name
            },
            status: this.getStatusText(item.status),
            approveTime: item.payment_at,
            expectedTime: item.expected_time,
            rejectReason: item.reject_reason,
            remarks: item.remarks,
            batch_number: item.batch_number,
            batch_title: item.batch_title,
            total_order_count: item.total_order_count,
            ...item
          }))
          this.total = res.data.meta.pagination.total
          this.filteredWithdrawalHistory = this.withdrawalHistory
          await this.updateStatusCounts()
        }
      } catch (error) {
        console.error('获取提现记录失败:', error)
        this.$message.error('获取提现记录失败，请稍后重试')
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'in_progress': '打款中',
        'completed': '已完成',
        'failed': '已驳回'
      }
      return statusMap[status] || '未知'
    },

    // 提交提现申请
    async submitWithdrawal() {
      this.$refs.withdrawalForm.validate(async (valid) => {
        if (valid) {
          try {
            this.submitting = true
            // 优先从props获取参数，然后从路由获取日期参数
            const params = this.queryParams || this.$route.query
            const { start_date, end_date, branch_id } = params
            const data = {
              bank_id: this.withdrawalForm.bankCardId,
              remarks: this.withdrawalForm.remarks,
              start_date: start_date,
              end_date: end_date,
              branch_id: branch_id
            }

            const res = await submitWithdrawal(data)
            if (res.code === 200) {
              this.$message.success('提现申请提交成功！')
              this.resetForm()
              await this.loadWithdrawalHistory()
              // 触发提交成功事件，通知父组件
              this.$emit('submit-success')
            }
          } catch (error) {
            console.error('提交提现申请失败:', error)
          } finally {
            this.submitting = false
          }
        } else {
          return false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.withdrawalForm.resetFields()
    },

    // 显示添加银行卡对话框
    showAddCardDialog() {
      this.isEditCard = false
      this.newCardForm = {
        id: null,
        bankName: '',
        account: '',
        bank_crad_number: ''
      }
      this.addCardDialogVisible = true
    },

    // 编辑银行卡
    editCard(card) {
      this.isEditCard = true
      this.newCardForm = {
        id: card.id,
        bankName: card.bankName,
        account: card.accountName,
        bank_crad_number: card.originalCardNumber || card.cardNumber // 优先使用原始卡号
      }
      this.addCardDialogVisible = true
    },

    // 删除银行卡
    async deleteCard(card) {
      try {
        await this.$confirm('确定要删除该银行卡吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await deleteBank(card.id, { branch_id: this.$route.query.branch_id })
        if (res.code === 200) {
          this.$message.success('银行卡删除成功！')
          await this.initData() // 重新加载银行卡列表
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除银行卡失败:', error)
          this.$message.error('删除银行卡失败，请稍后重试')
        }
      }
    },

    // 保存新银行卡
    async saveNewCard() {
      this.$refs.newCardForm.validate(async (valid) => {
        if (valid) {
          try {
            const data = {
              bank_crad_number: this.newCardForm.bank_crad_number,
              bank_name: this.newCardForm.bankName,
              account: this.newCardForm.account,
              branch_id: this.$route.query.branch_id
            }

            let res
            if (this.isEditCard) {
              res = await updateBank(this.newCardForm.id, data)
            } else {
              res = await addBank(data)
            }

            if (res.code === 200) {
              this.$message.success(this.isEditCard ? '银行卡更新成功！' : '银行卡添加成功！')
              this.addCardDialogVisible = false
              this.$refs.newCardForm.resetFields()
              await this.initData() // 重新加载银行卡列表
            }
          } catch (error) {
            console.error(this.isEditCard ? '更新银行卡失败:' : '添加银行卡失败:', error)
            this.$message.error(this.isEditCard ? '更新银行卡失败，请稍后重试' : '添加银行卡失败，请稍后重试')
          }
        } else {
          return false
        }
      })
    },

    // 查看提现详情
    async viewWithdrawalDetail(row) {
      try {
        // 获取结算单列表和时间线数据
        const [detailRes, timelineRes] = await Promise.all([
          getWithdrawalDetail(row.id, {
            page: 1,
            per_page: 10
          }),
          getApprovalTimelines(row.id)
        ])

        if (detailRes.code === 200 && timelineRes.code === 200) {
          // 使用时间线API的数据
          const timelineData = timelineRes.data || {}
          const detailData = detailRes.data || {}

          // 构建完整的时间线数据
          const timeline = []

          // 1. 添加提现申请记录（一定会有）
          if (timelineData.created_at) {
            timeline.push({
              timestamp: timelineData.created_at,
              content: '提交提现申请',
              type: 'primary'
            })
          }

          // 2. 添加审核记录
          if (timelineData.approved_at) {
            timeline.push({
              timestamp: timelineData.approved_at,
              content: timelineData.failure_reason ? `审核驳回：${timelineData.failure_reason}（审核人：${(timelineData.approver && timelineData.approver.account) || '系统'}）` : `审核通过 （审核人：${(timelineData.approver && timelineData.approver.account) || '系统'}）`,
              type: timelineData.failure_reason ? 'danger' : 'success'
            })
          }

          // 3. 添加打款记录（如果有）
          if (timelineData.payment_at) {
            timeline.push({
              timestamp: timelineData.payment_at,
              content: `打款完成（打款人：${(timelineData.payer && timelineData.payer.account) || '系统'}）`,
              type: 'success'
            })
          }

          // 处理银行信息，添加空值检查
          const bankCardNumber = detailData.bank_crad_number || timelineData.bank_crad_number || row.bankInfo.cardNumber || ''
          const bankInfo = {
            accountName: detailData.account || timelineData.account || row.bankInfo.accountName || '',
            bankName: detailData.bank_name || timelineData.bank_name || row.bankInfo.bankName || '',
            cardNumber: bankCardNumber ? bankCardNumber.replace(/(\d{4})(?=\d)/g, '$1 ') : '' // 每4位数字后添加空格，添加空值检查
          }

          this.selectedWithdrawal = {
            ...row,
            timeline,
            approveTime: timelineData.approved_at,
            rejectReason: timelineData.failure_reason,
            status: timelineData.cn_status || this.getStatusText(timelineData.status),
            remarks: timelineData.remarks || '无',
            totalOrders: timelineData.total_order_count || detailData.total_order_count,
            settlement_sheets: detailRes.data,
            bankInfo
          }
          this.detailDialogVisible = true
        }
      } catch (error) {
        console.error('获取提现详情失败:', error)
        this.$message.error('获取提现详情失败，请稍后重试')
      }
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 取消提现申请
    async cancelWithdrawal(row) {
      try {
        await this.$confirm('确定要取消该笔提现申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await deleteBank(row.id)
        if (res.code === 200) {
          this.$message.success('提现申请已取消！')
          await this.loadWithdrawalHistory()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消提现申请失败:', error)
          this.$message.error('取消提现申请失败，请稍后重试')
        }
      }
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 'pending': return 'info'
        case 'in_progress': return 'warning'
        case 'completed': return 'success'
        case 'failed': return 'danger'
        default: return 'info'
      }
    },

    // 分页大小变化
    async handleSizeChange(val) {
      this.pageSize = val
      await this.loadWithdrawalHistory()
    },

    // 分页页码变化
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.loadWithdrawalHistory()
    },

    // 过滤提现记录
    filterWithdrawalHistory() {
      this.filteredWithdrawalHistory = this.withdrawalHistory
    },

    // 标签切换
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.loadWithdrawalHistory()
    },

    // 更新各状态数量
    async updateStatusCounts() {
      try {
        // 不再强制依赖路由参数中的日期
        const params = {}
        const { start_date, end_date } = this.$route.query

        // 如果有日期参数则添加到请求中
        if (start_date) params.start_date = start_date
        if (end_date) params.end_date = end_date

        const res = await getWithdrawalsBadge(params)
        if (res.code === 200) {
          this.pendingCount = res.data.total_pending
          this.reviewingCount = res.data.total_in_progress
          this.completedCount = res.data.total_completed

          this.accountBalance.frozen = res.data.total_completed
        }
      } catch (error) {
        console.error('获取状态数量失败:', error)
      }
    },

    // 订单列表分页大小变化
    async handleOrderListSizeChange(val) {
      this.orderListPageSize = val
      await this.loadOrderList()
    },

    // 订单列表分页页码变化
    async handleOrderListCurrentChange(val) {
      this.orderListCurrentPage = val
      await this.loadOrderList()
    },

    // 加载订单列表
    async loadOrderList() {
      if (!this.selectedWithdrawal) return
      try {
        const res = await getWithdrawalDetail(this.selectedWithdrawal.id, {
          page: this.orderListCurrentPage,
          per_page: this.orderListPageSize
        })
        if (res.code === 200) {
          this.selectedWithdrawal.settlement_sheets = res.data
        }
      } catch (error) {
        console.error('获取订单列表失败:', error)
        this.$message.error('获取订单列表失败，请稍后重试')
      }
    }
  }
}
</script>

<style scoped>
.withdrawal-application {
  padding: 12px;
  min-height: 100vh;
  background-color: #fff;
  border-radius: 8px;
}

/* 紧凑模式样式 */
.withdrawal-application[compact] {
  padding: 12px;
  min-height: auto;
}

.merchant-header {
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
  margin-bottom: 20px;
  color: #fff;
}

.withdrawal-application[compact] .merchant-header {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 8px;
}

.merchant-title {
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.withdrawal-application[compact] .merchant-title {
  font-size: 16px;
}

.merchant-title i {
  margin-right: 12px;
  font-size: 24px;
}

.withdrawal-application[compact] .merchant-title i {
  margin-right: 8px;
  font-size: 18px;
}

.balance-box {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.withdrawal-application[compact] .balance-box {
  gap: 10px;
  margin-bottom: 15px;
}

.balance-item {
  flex: 1;
  min-width: 300px;
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
}

.withdrawal-application[compact] .balance-item {
  min-width: 200px;
  padding: 12px;
  border-radius: 8px;
}

.balance-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.withdrawal-application[compact] .balance-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.balance-label i {
  margin-right: 10px;
  font-size: 20px;
}

.withdrawal-application[compact] .balance-label i {
  margin-right: 6px;
  font-size: 16px;
}

.balance-value {
  font-size: 30px;
  font-weight: 600;
  line-height: 1.2;
}

.withdrawal-application[compact] .balance-value {
  font-size: 22px;
}

.total-balance {
  color: #f56c6c;
  background: linear-gradient(135deg, #ff4d4f 0%, #f56c6c 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.available-balance {
  color: #67c23a;
  background: linear-gradient(135deg, #52c41a 0%, #67c23a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.frozen-balance {
  color: #e6a23c;
  background: linear-gradient(135deg, #faad14 0%, #e6a23c 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.withdrawal-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.withdrawal-application[compact] .withdrawal-form {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.withdrawal-application[compact] .withdrawal-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.withdrawal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-application[compact] .withdrawal-title {
  font-size: 16px;
}

.withdrawal-title i {
  margin-right: 10px;
  font-size: 20px;
  color: #1890ff;
}

.withdrawal-application[compact] .withdrawal-title i {
  margin-right: 6px;
  font-size: 16px;
}

.bank-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  padding: 20px;
  border-radius: 12px;
  margin: 16px 0;
  border: 1px solid #ebeef5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.withdrawal-application[compact] .bank-card {
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
}

.bank-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.withdrawal-application[compact] .bank-title {
  font-size: 14px;
  margin-bottom: 10px;
}

.bank-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.bank-info-item {
  flex: 1;
  min-width: 200px;
  padding: 8px 0;
}

.bank-label {
  color: #606266;
  margin-right: 8px;
  font-size: 14px;
}

.bank-value {
  color: #303133;
  font-weight: 500;
  font-size: 15px;
}

.add-card-btn {
  margin-top: 12px;
  color: #1890ff;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.add-card-btn i {
  margin-right: 4px;
}

.add-card-btn:hover {
  color: #40a9ff;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.withdrawal-application[compact] .form-actions {
  margin-top: 15px;
}

.form-actions .el-button {
  padding: 12px 24px;
  font-size: 15px;
}

.withdrawal-application[compact] .form-actions .el-button {
  padding: 9px 20px;
  font-size: 14px;
}

.withdrawal-history {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
}

.history-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
  display: flex;
  align-items: center;
}

.history-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

.history-tabs {
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  border-bottom: none;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  padding: 0 20px;
  color: #606266;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 500;
}

:deep(.el-tabs__active-bar) {
  background-color: #409EFF;
  height: 2px;
}

.tab-badge {
  margin-left: 6px;
  transform: translateY(-1px) scale(0.8);
}

:deep(.el-badge__content) {
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 9px;
  font-size: 12px;
  border: none;
  background-color: #f56c6c;
}

.pagination-container {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
}

:deep(.el-button--text) {
  padding: 4px 8px;
  font-weight: 500;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-descriptions) {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions-item__content) {
  color: #303133;
}

.bank-info-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bank-name {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.account-name {
  color: #303133;
  font-size: 14px;
}

.card-number {
  color: #303133;
  font-size: 14px;
}

.separator {
  color: #909399;
  display: inline-block;
  margin: 0 4px;
}

/* 表单项间距 */
.form-item-gap {
  margin-bottom: 20px;
}

.withdrawal-application[compact] .form-item-gap {
  margin-bottom: 15px;
}

/* 银行卡对话框样式 */
:deep(.bank-card-dialog) {
  border-radius: 8px;
  overflow: hidden;
  z-index: 3050 !important;
}

:deep(.bank-card-dialog .el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

:deep(.bank-card-dialog .el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.bank-card-dialog .el-dialog__body) {
  padding: 20px;
}

.bank-card-form {
  padding: 0 10px;
}

.bank-form-item {
  margin-bottom: 22px;
}

:deep(.bank-card-dialog .el-form-item__label) {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

:deep(.bank-card-dialog .el-input__inner) {
  height: 40px;
  line-height: 40px;
}

:deep(.bank-card-dialog .el-dialog__footer) {
  padding: 15px 20px 20px;
  text-align: center;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

:deep(.bank-card-dialog .el-dialog__footer .el-button) {
  padding: 10px 20px;
  min-width: 100px;
}

/* 修复弹框显示 */
:deep(.bank-card-dialog + .v-modal) {
  z-index: 3049 !important;
}

/* 修改为只针对银行卡弹框限制滚动，而不是所有弹框 */
:deep(.bank-card-dialog .el-dialog__wrapper) {
  overflow: hidden;
}

/* 确保提现详情弹框可以滚动 */
:deep(.el-dialog__wrapper) {
  overflow: auto;
}

/* 银行卡信息显示 */
.bank-info-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  border: 1px solid #e4e7ed;
}

.bank-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.bank-title-text {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.bank-title-text:before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

.bank-actions {
  display: flex;
  gap: 10px;
}

.bank-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.bank-info-item {
  flex: 1;
  min-width: 200px;
  padding: 5px 0;
}

.bank-label {
  color: #606266;
  margin-right: 8px;
  font-size: 14px;
}

.bank-value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}
</style>

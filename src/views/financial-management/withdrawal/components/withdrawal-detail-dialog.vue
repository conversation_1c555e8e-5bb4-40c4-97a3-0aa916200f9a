<template>
  <el-dialog
    title="提现详情"
    :visible.sync="visible"
    width="800px"
    @close="handleClose"
  >
    <div v-if="withdrawal" class="detail-content">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="申请时间">
          {{ withdrawal.date }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.branchName" label="分台名称">
          {{ withdrawal.branchName }}
        </el-descriptions-item>
        <el-descriptions-item label="提现金额">
          <span style="color: #f56c6c; font-weight: bold;">
            ¥{{ withdrawal.amount }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_order_count" label="订单总数量">
          {{ withdrawal.total_order_count }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_order_amount" label="订单总金额">
          {{ withdrawal.total_order_amount }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_fee" label="退票手续费">
          {{ withdrawal.total_fee }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_payable_amount" label="应付金额">
          {{ withdrawal.total_payable_amount }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_platform_fee" label="平台服务费">
          {{ withdrawal.total_platform_fee }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.total_third_fee" label="第三方费用">
          {{ withdrawal.total_third_fee }}
        </el-descriptions-item>
        <el-descriptions-item label="提现周期">
          {{ withdrawal.period }}
        </el-descriptions-item>
        <el-descriptions-item label="收款银行">
          <div class="bank-info">
            <div class="bank-row">
              <span class="bank-label">开户名：</span>
              <span class="bank-value">{{ withdrawal.bankInfo.accountName }}</span>
            </div>
            <div class="bank-row">
              <span class="bank-label">开户行：</span>
              <span class="bank-value">{{ withdrawal.bankInfo.bankName }}</span>
            </div>
            <div class="bank-row">
              <span class="bank-label">银行卡号：</span>
              <span class="bank-value">{{ withdrawal.bankInfo.cardNumber }}</span>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getStatusType(withdrawal.status)">
            {{ withdrawal.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.approveTime" label="审核时间">
          {{ withdrawal.approveTime }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.expectedTime" label="预计到账时间">
          {{ withdrawal.expectedTime }}
        </el-descriptions-item>
        <el-descriptions-item v-if="withdrawal.rejectReason" label="驳回原因">
          {{ withdrawal.rejectReason }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ withdrawal.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审核记录时间线 -->
      <div class="timeline-wrapper">
        <div class="timeline-title">审核记录</div>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in withdrawal.timeline"
            :key="index"
            :type="activity.type"
            :color="getTimelineColor(activity.type)"
            :timestamp="activity.timestamp"
            placement="top"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 提现订单列表 -->
      <div class="order-list" style="margin-top: 20px;">
        <div class="title" style="font-size: 16px; font-weight: bold; margin-bottom: 15px;">提现订单列表</div>
        <el-table
          v-loading="loading"
          :data="orderList"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="order_no"
            label="订单号"
            min-width="180"
          />
          <el-table-column
            prop="order_no"
            label="订单类型"
            min-width="180"
          >
            <template slot-scope="scope">
              {{ {
                income: '订单收入',
                refund: '退款手续费'
              }[scope.row.order_amount_type] || '--' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="order_amount"
            label="订单金额"
            min-width="120"
          >
            <template slot-scope="scope">
              ¥{{ scope.row.order_amount }}
            </template>
          </el-table-column>
          <el-table-column
            prop="created_at"
            label="结算时间"
            min-width="160"
          >
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 订单列表分页 -->
        <div class="pagination-container" style="margin-top: 20px; text-align: right;">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getWithdrawalDetail } from '@/api/withdrawal'

export default {
  name: 'WithdrawalDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    withdrawal: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      orderList: []
    }
  },
  watch: {
    visible(val) {
      if (val && this.withdrawal) {
        this.currentPage = 1
        this.loadOrderList()
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.currentPage = 1
      this.orderList = []
      this.total = 0
    },
    getStatusType(status) {
      switch (status) {
        case '待审核': return 'info'
        case '审核中': return 'warning'
        case '已完成': return 'success'
        default: return 'info'
      }
    },
    getTimelineColor(type) {
      switch (type) {
        case 'success': return '#67C23A'
        case 'warning': return '#E6A23C'
        case 'danger': return '#F56C6C'
        default: return '#909399'
      }
    },
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    async handleSizeChange(val) {
      this.pageSize = val
      await this.loadOrderList()
    },
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.loadOrderList()
    },
    async loadOrderList() {
      if (!this.withdrawal) return
      try {
        this.loading = true
        const res = await getWithdrawalDetail(this.withdrawal.id, {
          page: this.currentPage,
          per_page: this.pageSize
        })
        if (res.code === 200) {
          this.orderList = res.data.data || []
          this.total = res.data.meta.pagination.total || 0
        }
      } catch (error) {
        console.error('获取订单列表失败:', error)
        this.$message.error('获取订单列表失败，请稍后重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.detail-content {
  padding: 20px;
}

.bank-info {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.bank-row {
  margin-bottom: 8px;
}

.bank-row:last-child {
  margin-bottom: 0;
}

.bank-label {
  color: #606266;
  margin-right: 8px;
}

.bank-value {
  color: #303133;
  font-weight: 500;
}

.timeline-wrapper {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.timeline-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

:deep(.el-timeline-item__node) {
  background-color: #909399;
}

:deep(.el-timeline-item__content) {
  color: #606266;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-descriptions) {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions-item__content) {
  color: #303133;
}
</style>

<template>
  <div style="display: inline-block">
    <el-button type="text" size="small" class="filter-item" @click="selectDetail">
      详情
    </el-button>
    <el-dialog
      title="订单明细"
      :visible.sync="recordVisible"
      width="1000px"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="cancel"
    >
      <el-table
        v-loading="detailLoading"
        :data="recordList"
        element-loading-text=""
        :height="`500px`"
        fit
        highlight-current-row
        size="small"
      >
        <el-table-column label="交易时间" width="170" prop="order.create_time" />
        <el-table-column label="订单编号" width="180" prop="order_no">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click.stop="jumpOrder(scope.row.order_no)">{{ scope.row.order_no }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="线路分台" width="180" prop="order.branch.mchname" />
        <el-table-column label="付款账户名称" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.passenger && scope.row.order.passenger.name) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="司机名称" prop="driver" width="120">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.driver && scope.row.order.driver.name) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="司机所得" prop="driver" width="100">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.offer_price) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="平台所得及比例" prop="driver" width="140">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.offer_boss_price) || "-" }}/{{ (scope.row.order && scope.row.order.split) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="退款金额" prop="refund_amount" width="100">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.refund_amount) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="优惠金额" prop="coupon_price" width="100">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.coupon_price) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="订单类型" prop="driver" width="100">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.driver_job_type === 1) ? '全职' : (scope.row.order && scope.row.order.driver_job_type === 2) ? '兼职' : "未知" }}
          </template>
        </el-table-column>

        <el-table-column label="订单总金额（元）" prop="order_amount" width="140">
          <template slot-scope="scope">
            {{ (scope.row.order_amount) || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="实付金额" prop="real_price" width="100">
          <template slot-scope="scope">
            {{ (scope.row.order && scope.row.order.real_price) || "-" }}
          </template>
        </el-table-column>

      </el-table>

      <div v-if="recordList&&recordList.length" class="total-amount-content">
        <span>司机所得金额合计 <b class="cma">{{ total_offer_price }}</b> 元</span>
      </div>
      <div class="bottom-container">
        <div class="left-content" />
        <el-pagination
          v-if="detailtotal > 0"
          layout="total,prev, pager, next"
          background
          :page-size="logQuery.per_page"
          :total="detailtotal"
          :current-page="logQuery.page"
          style="text-align: right"
          @current-change="detailChanges"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { settlements_detail } from '@/api'
export default {
  props: {
    detail: {
      type: Number
    }
  },
  data() {
    return {
      logQuery: {
        page: 1,
        per_page: 15,
        settlement_batch_id: ''
      },
      detailLoading: false,
      recordList: [],
      detailtotal: 0,
      recordVisible: false,
      total_offer_price: 0
    }
  },
  methods: {
    jumpOrder (no_) {
      this.$router.push({
        path: '/orderManage/tarinsManage', query: {
          orderNo: no_
        }
      })
    },
    handleFilter() {
      this.logQuery.page = 1
      this.fetchLogs()
    },
    handleReset() {
      this.logQuery = {
        page: 1,
        per_page: 15,
        settlement_batch_id: this.detail
      }
      this.fetchLogs()
    },
    detailChanges(val) {
      this.logQuery.page = val
      this.fetchLogs()
    },
    selectDetail() {
      this.recordVisible = true
      this.logQuery.settlement_batch_id = this.detail
      this.fetchLogs()
    },
    fetchLogs() {
      this.detailLoading = true
      settlements_detail(this.logQuery).then((response) => {
        if (response.code === 200) {
          this.recordList = response.data && response.data.data
          this.detailtotal = response.data && response.data.meta && response.data.meta.pagination && response.data.meta.pagination.total
          this.total_offer_price = (response.data && response.data.meta && response.data.meta.total_offer_price) || 0
        }
        this.detailLoading = false
      })
    },
    cancel() {
      this.recordVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.total-amount-content{
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
}
.label-key {
  flex: 0 0 90px;
  text-align: right;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  margin-left: 15px;

  &::after {
    content: ":";
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}
</style>

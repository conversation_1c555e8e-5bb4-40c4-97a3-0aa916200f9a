<template>
  <div class="component-container">
    <div class="driver-settlement">

      <!-- 整合的搜索和操作区域 -->
      <div class="withdrawal-form">
        <div class="withdrawal-header">
          <div class="withdrawal-title">
            <div class="title-content">
              <i class="el-icon-search" /> 全职司机结算管理
            </div>
          </div>
        </div>

        <el-form :inline="true" :model="listQuery" label-width="80px" size="small" class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">线路分台：</span>
                <BranchSelect
                  v-model="listQuery.branchid"
                  style="width: calc(100% - 90px);"
                  @change="searchBranchChange"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">结算司机：</span>
                <el-select v-model="listQuery.driver_id" size="small" clearable placeholder="全部" style="width: calc(100% - 90px);">
                  <el-option v-for="item in searchDrivers" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">结算单号：</span>
                <el-input
                  v-model="listQuery.batch_number"
                  placeholder="请输入结算单号"
                  clearable
                  size="small"
                  style="width: calc(100% - 90px);"
                  @keyup.enter.native="handleFilter"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">批次名称：</span>
                <el-input
                  v-model="listQuery.batch_title"
                  placeholder="请输入批次名称"
                  clearable
                  size="small"
                  style="width: calc(100% - 90px);"
                  @keyup.enter.native="handleFilter"
                />
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="search-item">
                <span class="search-label no-wrap">结算时间：</span>
                <el-date-picker
                  v-model="searchDate"
                  size="small"
                  type="daterange"
                  clearable
                  value-format="yyyy-MM-dd"
                  style="width: calc(100% - 90px);"
                  range-separator="至"
                  start-placeholder="请选择开始日期"
                  end-placeholder="请选择结束日期"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item search-buttons">
                <el-button type="primary" size="small" icon="el-icon-search" @click="handleFilter">查询</el-button>
                <el-button size="small" icon="el-icon-refresh" @click="handleReset">重置</el-button>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="search-item action-buttons">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-circle-plus-outline"
                  @click="handleCreate"
                >
                  新增结算单
                </el-button>
                <el-button
                  size="small"
                  icon="el-icon-circle-check"
                  :disabled="!multipleSelection.length"
                  @click="handleBatch('in_progress')"
                >
                  批量去确认
                </el-button>
                <el-button
                  size="small"
                  icon="el-icon-circle-check"
                  :disabled="!multipleSelection.length"
                  @click="handleBatch('completed')"
                >
                  批量已打款
                </el-button>
                <el-button
                  size="small"
                  icon="el-icon-download"
                  @click="handleExport"
                >
                  按筛选条件导出
                </el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 状态标签页 -->
      <div class="table-container">
        <div class="table-title">
          <i class="el-icon-time" /> 结算单列表
        </div>

        <el-tabs v-model="listQuery.status" @tab-click="handleFilter">
          <el-tab-pane label="全部" name="all" />
          <el-tab-pane name="pending">
            <el-badge slot="label" :value="pendingCount" :hidden="pendingCount === 0" class="item">待确认</el-badge>
          </el-tab-pane>
          <el-tab-pane label="打款中" name="in_progress" />
          <el-tab-pane label="已打款" name="completed" />
        </el-tabs>

        <el-table
          ref="userTable"
          v-loading="listLoading"
          :data="list"
          element-loading-text=""
          fit
          highlight-current-row
          height="calc(100vh - 520px)"
          size="medium"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="ID" width="125" prop="id" class-name="isFirst" />
          <el-table-column label="批次名称" width="160" prop="batch_title">
            <template slot-scope="scope">
              {{ scope.row && scope.row.batch_title || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="结算单号" width="160" prop="batch_number" />
          <el-table-column label="结算时间" min-width="220" prop="start_date">
            <template slot-scope="scope">
              {{ scope.row.start_date || '/' }} 至 {{ scope.row.end_date || '/' }}
            </template>
          </el-table-column>
          <el-table-column label="结算订单数" width="100" prop="total_order_count" />
          <el-table-column label="结算金额" width="100" prop="total_order_amount" />
          <el-table-column label="结算司机" width="120" prop="driver.name" />
          <el-table-column label="线路分台" width="200" prop="branch.mchname" />
          <el-table-column label="抽成比例" width="100" prop="commission_rate" />
          <el-table-column label="状态" size="small" align="left" width="170">
            <template slot-scope="scope">
              <Tablestatus v-if="scope.row.status === 'pending'" mode="warning" word="待确认" />
              <Tablestatus v-else-if="scope.row.status === 'in_progress'" mode="processing" word="打款中" />
              <Tablestatus v-else-if="scope.row.status === 'completed'" mode="success" word="已打款" />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180" prop="created_at" />
          <el-table-column label="更新时间" width="180" prop="updated_at" />
          <el-table-column class-name="status-col" label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <template v-if="scope.row.status !== 'completed'">
                <el-button
                  class="filter-item"
                  type="text"
                  size="medium"
                  @click="putAgreementStatus(scope.row.id, scope.row.status === 'in_progress' ? 'completed' : 'in_progress')"
                >
                  {{ scope.row.status === 'in_progress' ? '已打款' : '去确认' }}
                </el-button>
                <el-divider direction="vertical" />
              </template>
              <record :detail="scope.row.id" />
              <el-divider direction="vertical" />
              <el-button class="filter-item" type="text" size="medium" @click="handleDel(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-if="total > 0"
            layout="sizes, total, prev, pager, next"
            background
            :page-size="listQuery.per_page"
            :total="total"
            :page-sizes="[15, 50, 100, 200]"
            :current-page="listQuery.page"
            @current-change="handleCurrentChanges"
            @size-change="handleSizeChange"
          />
        </div>
      </div>

      <!-- 新建结算单对话框 -->
      <el-dialog
        title="新建结算单"
        :visible.sync="dialogVisible"
        class="banner-dialog"
        :close-on-click-modal="false"
        width="800px"
      >
        <div class="dialog-content">
          <el-form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="150px" size="medium">
            <el-form-item label="结算单名称" prop="title">
              <el-input v-model="temp.title" placeholder="请输入" clearable style="width: 200px" />
              <span class="label-key middle-label">例：输入24年9月打款批次，可方便筛选</span>
            </el-form-item>
            <el-form-item label="线路分台" prop="branch_id">
              <BranchSelect
                v-model="temp.branch_id"
                class="mini-condition"
                @change="branchChange"
              />
            </el-form-item>
            <el-form-item label="结算司机" prop="driver_ids">
              <div v-loading="driverLoading" class="driver-list">
                <el-empty v-if="!driverLoading && !drivers.length" description="暂无可选司机" :image-size="60" />
                <div v-else>
                  <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                  >全选</el-checkbox>
                  <div style="margin: 15px 0;" />
                  <el-checkbox-group v-model="temp.driver_ids" @change="handleCheckedCitiesChange">
                    <el-checkbox v-for="driver in drivers" :key="driver.value" :label="driver.value">{{ driver.label
                    }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="结算期属" required>
              <el-col :span="7">
                <el-form-item prop="start_date">
                  <el-date-picker
                    v-model="temp.start_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择开始日期"
                    style="width: 100%"
                    @change="handleStartDateChange"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="1">-</el-col>
              <el-col :span="7">
                <el-form-item prop="end_date">
                  <el-date-picker
                    v-model="temp.end_date"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="选择结束日期"
                    :default-value="defaultEndDate"
                    style="width: 100%"
                    :picker-options="endDatePickerOptions"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item label="结算期间订单类型" prop="order_type">
              <el-checkbox
                v-model="temp.order_type"
                :true-label="'full_time'"
                :false-label="''"
                disabled
              >
                全职类订单
              </el-checkbox>
            </el-form-item>
            <el-form-item label="平台管理抽佣比例" prop="commission_strategy">
              <el-checkbox
                v-model="temp.order_type"
                :true-label="'default'"
                :false-label="''"
                disabled
              >
                按订单已生成比例执行
              </el-checkbox>
            </el-form-item>
            <el-form-item label="平台综合手续费率" prop="commission_rate">
              <el-input v-model="temp.commission_rate" placeholder="请输入" style="width: 150px"><template
                slot="append"
              >%</template></el-input>
              <span class="label-key middle-label">%（可综合加权计算后输入。如付款渠道平台的手续费率、
                付到平台用户零钱/银行卡手续费率、发票税率、个税率）仅对本次生成的结算单有效。</span>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false"> 取消 </el-button>
          <el-button type="primary" @click="handleAdd()">
            确认生成
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { mapState } from 'vuex'
import Record from './components/Record'
import BranchSelect from '@/components/BranchSelect'
import { settlements_driver, settlement_batches, add_settlements, settlements_count, export_excels_settlements, in_progress_settlements, completed_settlements, del_settlements } from '@/api'
export default {
  name: 'Withdrawal',
  components: {
    Record,
    BranchSelect
  },
  data() {
    const validateValue = (rule, value, callback) => {
      if (value < 0) {
        callback(new Error('比例需大于等于0'))
      } else if (!/(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/.test(value)) {
        callback(new Error('请输入正确的比例'))
      } else {
        callback()
      }
    }
    return {
      driverLoading: false,
      pendingCount: 0,
      searchDate: [],
      multipleSelection: [],
      isIndeterminate: true,
      checkAll: false,
      temp: {
        driver_ids: [],
        order_type: 'full_time', // 默认选中全职类订单
        commission_strategy: 'default', // 默认选中按订单已生成比例执行
        end_date: moment().format('YYYY-MM-DD') // 默认结束日期为今日
      },
      endDatePickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() // 禁止选择今日之后的日期
        }
      },
      rules: {
        branch_id: [{
          required: true,
          message: '请选择',
          trigger: 'change'
        }],
        driver_ids: [{
          required: true,
          message: '请选择司机',
          trigger: 'change'
        }],
        end_date: [{
          required: true,
          message: '请选择日期',
          trigger: 'change'
        }],
        start_date: [{
          required: true,
          message: '请选择日期',
          trigger: 'change'
        }],
        title: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        commission_rate: [
          {
            required: true,
            validator: validateValue,
            trigger: 'change'
          }
        ]
      },
      drivers: [], // 新建数据分台下司机列表
      searchDrivers: [], // 表格筛选司机列表
      total: '',
      dialogVisible: false,
      list: null,
      listLoading: false,
      listQuery: {
        page: 1,
        per_page: 15,
        status: 'all',
        driver_id: ''
      },
      branchs: []
    }
  },
  computed: {
    ...mapState({
      loginType: state => state.user.loginType
    }),
    defaultEndDate() {
      const oneMonthLater = this.addMonths(this.temp.start_date, 1)
      return oneMonthLater || ''
    }
  },
  watch: {
    searchDate(n, o) {
      if (n) {
        const [start_date, end_date] = n
        this.listQuery = {
          ...this.listQuery,
          start_date,
          end_date
        }
      } else {
        this.listQuery = {
          ...this.listQuery,
          start_date: '',
          end_date: ''
        }
      }
    }
  },
  mounted() {
    this.fetchList()
    // 默认获取所有司机数据用于搜索筛选
    this.fetchDrivers('', true)
  },
  methods: {
    handleStartDateChange(value) {
      if (value) {
        this.temp.end_date = moment().format('YYYY-MM-DD') // 默认结束日期为今日
      }
    },
    addMonths(date, months) {
      const result = new Date(date)
      result.setMonth(result.getMonth() + months)
      return result
    },
    handleBatch(status) {
      const ids = this.multipleSelection.map(item => item.id)
      this.putAgreementStatus(ids, status)
    },
    async putAgreementStatus(ids, status) {
      await this.$confirm(`确认将当前选中结算批次变更为${status === 'in_progress' ? '打款中' : '已打款'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      status === 'in_progress' ? await in_progress_settlements(ids) : await completed_settlements(ids)
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.fetchList()
    },
    async handleExport() {
      // settlement_batch_id

      await this.$confirm(`确认导出当前${this.multipleSelection.length ? '选中' : '筛选条件下所有'}结算单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info '
      })

      const { data } = await export_excels_settlements({ ...this.listQuery, status: this.listQuery.status === 'all' ? '' : this.listQuery.status, settlement_batch_id: this.multipleSelection.map(item => item.id).join(',') })
      console.log('url', data.url)
      // 在新的窗口打开url地址
      window.open(data.url)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    searchBranchChange() {
      this.searchDrivers = []
      this.listQuery.driver_id = ''
      this.fetchDrivers(this.listQuery.branchid, true)
    },
    branchChange() {
      // 重置选择状态
      this.temp.driver_ids = []
      this.checkAll = false
      this.isIndeterminate = false
      // 获取对应分台的司机，如果没有选择分台则获取全部司机
      this.fetchDrivers(this.temp.branch_id || '')
    },
    handleCheckAllChange(val) {
      this.temp.driver_ids = val ? this.drivers.map(o => o.value) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      console.log('valuevalue', value)
      const checkedCount = value.length
      this.checkAll = checkedCount === this.drivers.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.drivers.length
    },
    async fetchDrivers(id, isTable = false) {
      this.driverLoading = true
      // 如果id为空字符串，则获取所有司机数据
      const params = id ? { branchid: id } : {}
      const { data } = await settlements_driver(params)
      let options = []
      if (data && data.length > 0) {
        options = data.map((o) => {
          return {
            label: o.name,
            value: o.driver_id
          }
        })
      }
      if (isTable) {
        this.searchDrivers = options
      } else {
        this.drivers = options
      }
      this.driverLoading = false
    },

    isfull(scope) {
      return scope.split(',').length === 9
    },
    async handleDel(id) {
      await this.$confirm('删除后不可恢复，确认删除当前结算单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await del_settlements(id)
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.temp = Object.assign({}, row)
      this.$set(this.temp, 'scope', row.scope ? row.scope.split(',').map((ele) => Number(ele)) : [])
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.temp = {
        driver_ids: [],
        commission_rate: 0,
        end_date: '',
        start_date: ''
      }
      this.dialogVisible = true
      // 重置司机相关状态
      this.drivers = []
      this.checkAll = false
      this.isIndeterminate = false
      // 默认查询全部司机
      this.fetchDrivers('', false)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleAdd() {
      await this.$refs.dataForm.validate()
      const params = Object.assign({}, this.temp)
      params.start_date = moment(params.start_date).format('YYYY-MM-DD')
      params.end_date = moment(params.end_date).format('YYYY-MM-DD')
      params.driver_ids = params.driver_ids.join(',')
      await add_settlements(params)
      this.dialogVisible = false
      this.$notify({
        message: '添加成功',
        type: 'success',
        duration: 2000
      })
      this.fetchList()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        status: 'all'
      }
      this.$refs.userTable.clearSort()
      this.fetchList()
    },
    async fetchCount() {
      const { data } = await settlements_count()
      this.pendingCount = data.count || 0
    },
    async fetchList() {
      this.listLoading = true
      this.fetchCount()
      const { data } = await settlement_batches({ ...this.listQuery, status: this.listQuery.status === 'all' ? '' : this.listQuery.status })
      this.list = data && data.data || []
      this.total = data && data.meta && data.meta.pagination && data.meta.pagination.total || 0
      this.listLoading = false
    },
    positive(val) {
      const reg = /^[+]{0,1}(\d+)$/
      const len = val.length
      let str = ''
      for (var i = 0; i < len; i++) {
        if (reg.test(val[i])) {
          str += val[i]
        }
      }
      return Number(str)
    }
  }
}
</script>
<style lang="scss" scoped>
.component-container {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  background-color: #f8f8fa;
}

.driver-settlement {
  .withdrawal-header {
    margin-bottom: 10px;
  }

  .withdrawal-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-content {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;

      i {
        margin-right: 6px;
        color: #409EFF;
      }
    }
  }
}

.search-form {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.search-label {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  width: 90px;
  text-align: right;
  padding-right: 10px;
}

.search-label.no-wrap {
  white-space: nowrap;
}

.search-buttons {
  display: flex;
  justify-content: flex-start;
  margin-left: 90px;

  .el-button {
    margin-right: 10px;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 10px;
  border-top: 1px dashed #e8e8e8;
  margin-top: 10px;

  .el-button {
    margin-right: 10px;
  }
}

.withdrawal-form {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.table-container {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  margin-bottom: 24px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    font-size: 20px;
    color: #1890ff;
  }
}

.pagination-container {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.dialog-content {
  padding: 20px;
}

.driver-list {
  ::v-deep .el-empty {
    width: 60%;
  }
}

::v-deep .el-badge__content.is-fixed {
  top: 8px;
}

::v-deep .el-badge {
  vertical-align: auto;
}

::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

::v-deep .el-table td {
  padding: 12px 0;
}

::v-deep .el-tag {
  border-radius: 4px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
}

::v-deep .el-button--text {
  padding: 4px 8px;
  font-weight: 500;
}

::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  max-width: 680px;
  margin-top: 15vh !important;

  .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f9fafb;
  }

  .el-dialog__body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background-color: #fff;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .el-dialog__headerbtn {
    top: 20px;

    .el-dialog__close {
      font-size: 18px;
      font-weight: 600;
      color: #909399;

      &:hover {
        color: #409EFF;
      }
    }
  }
}

.line {
  text-align: center;
}

.middle-label {
  vertical-align: middle;
}

.label-key {
  margin: 5px 0px 5px 24px;
  font-weight: 500;
  font-size: 13px;
}
</style>

<style lang="scss">
.scope_type {
  .el-checkbox {
    margin-right: 12px;
  }

  .el-checkbox__label {
    font-size: 12px;
    padding-left: 5px;
  }
}
</style>

<template>
  <div class="withdrawal-records">
    <!-- 搜索栏 -->
    <div class="search-form">
      <el-form :inline="true" size="small">
        <el-form-item label="线路分台">
          <el-select v-model="searchForm.company" placeholder="请选择线路分台" clearable @change="$emit('search')">
            <el-option
              v-for="item in companyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="withdrawal-header">
      <div class="withdrawal-title">
        <i class="fas fa-list" /> 提现申请记录
      </div>
      <div class="header-actions">
        <el-button size="small" type="primary" icon="el-icon-download" @click="$emit('export')">导出Excel</el-button>
      </div>
    </div>
    <el-table
      v-loading="loading"
      element-loading-text="加载中..."
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column prop="date" label="申请提现时间" width="180" />
      <el-table-column prop="amount" label="申请提现金额" width="180">
        <template slot-scope="scope">
          <span>¥{{ formatAmount(scope.row.amount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="period" label="提现周期" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column prop="status" label="审核状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="approveTime" label="审核通过时间" width="180" />
      <el-table-column prop="rejectReason" label="审核驳回原因" show-overflow-tooltip />
      <el-table-column v-if="isAdmin" label="操作" width="100" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === '审核中'"
            size="mini"
            type="primary"
            @click="$emit('approve', scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'WithdrawalList',
  props: {
    isAdmin: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    },
    companyOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchForm: {
        company: ''
      }
    }
  },
  watch: {
    'searchForm.company'(val) {
      this.$emit('company-change', val)
    }
  },
  methods: {
    getStatusType(status) {
      switch (status) {
        case '审核中': return 'warning'
        case '已通过': return 'success'
        case '已驳回': return 'danger'
        default: return 'info'
      }
    },
    formatAmount(amount) {
      return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  }
}
</script>

<style scoped>
.withdrawal-records {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.search-form {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.withdrawal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

.header-actions {
  float: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
}

:deep(.el-button--text) {
  padding: 4px 8px;
  font-weight: 500;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 16px 0;
}

:deep(.el-table__empty-block) {
  min-height: 160px;
}

:deep(.el-table__empty-text) {
  color: #909399;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-select) {
  width: 200px;
}
</style>

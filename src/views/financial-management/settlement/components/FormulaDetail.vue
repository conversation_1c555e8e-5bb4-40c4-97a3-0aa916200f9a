<template>
  <div class="withdrawal-form">
    <div class="withdrawal-header">
      <div class="withdrawal-title">
        <i class="fas fa-calculator" /> 计算公式明细
      </div>
    </div>
    <div class="formula-section">
      <div class="formula-title">平台服务费</div>
      <div class="formula-box">
        (已完成定制客运订单金额+已完成包车订单金额+已完成货物订单金额+退票手续费-第三方费用) *平台百分点
      </div>

      <div class="formula-title">应收金额</div>
      <div class="formula-box">
        已完成定制客运订单金额+已完成包车订单金额+已完成货物订单金额+退票手续费-第三方费用-平台服务费-折扣券
      </div>

      <div class="balance-box">
        <div class="balance-item">
          <div class="balance-label">应收金额</div>
          <div class="balance-value total-balance">¥{{ formatAmount(totalAmount) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormulaDetail',
  props: {
    totalAmount: {
      type: Number,
      default: 0
    }
  },
  methods: {
    formatAmount(amount) {
      return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  }
}
</script>

<style scoped>
.withdrawal-form {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.withdrawal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

.formula-section {
  padding: 20px;
}

.formula-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  margin-top: 20px;
}

.formula-title:first-child {
  margin-top: 0;
}

.formula-box {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.balance-box {
  margin-top: 24px;
}

.balance-item {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
}

.balance-label {
  font-size: 15px;
  color: #606266;
  margin-bottom: 12px;
}

.balance-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

.total-balance {
  color: #f56c6c;
  background: linear-gradient(135deg, #ff4d4f 0%, #f56c6c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>

<template>
  <div class="withdrawal-form">
    <div class="withdrawal-header">
      <div class="withdrawal-title">
        <i class="fas fa-building" /> {{ isAdmin ? '运营公司汇总' : '辉南县客运公司-松林路分公司' }}
      </div>
    </div>
    <el-table :data="companyData" border style="width: 100%">
      <el-table-column v-if="isAdmin" prop="name" label="所属运营公司" width="220" />
      <el-table-column prop="orderAmount" label="已完成定制客运订单金额" />
      <el-table-column prop="packageAmount" label="已完成包车订单金额" />
      <el-table-column prop="expressFee" label="已完成货物订单金额" />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'SettlementSummary',
  props: {
    isAdmin: {
      type: Boolean,
      default: false
    },
    companyData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.withdrawal-form {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.withdrawal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}
</style>

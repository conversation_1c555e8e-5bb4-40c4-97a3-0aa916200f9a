<template>
  <div class="withdrawal-form">
    <div class="withdrawal-header">
      <div class="withdrawal-title">
        <i class="fas fa-exchange-alt" /> 手续费信息
      </div>
    </div>
    <el-table :data="refundData" border style="width: 100%">
      <el-table-column prop="refundFee" label="退票手续费" />
      <el-table-column prop="thirdPartyFee" label="第三方费用((总流水-优惠券)*0.006)" />
      <el-table-column prop="platformFee" label="平台服务费" />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'FeeInfo',
  props: {
    refundData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.withdrawal-form {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.withdrawal-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.withdrawal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.withdrawal-title i {
  margin-right: 8px;
  font-size: 20px;
  color: #1890ff;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}
</style>

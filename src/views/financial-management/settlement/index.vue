<template>
  <div class="component-container">
    <div class="withdrawal-application">

      <!-- 搜索表单 -->
      <div class="withdrawal-form">
        <div class="withdrawal-header">
          <div class="withdrawal-title with-action">
            <div class="title-content">
              <i class="el-icon-search" /> 结算查询
            </div>
            <div class="action-buttons">
              <el-button
                v-if="isBranchAccount"
                size="small"
                type="primary"
                @click="handleWithdraw"
              >
                申请提现
              </el-button>
            </div>
          </div>
        </div>

        <el-form :inline="true" :model="searchForm" label-width="80px" size="small" class="search-form">
          <el-row :gutter="20">

            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">所属业态：</span>
                <el-select v-model="business_type" clearable placeholder="全部" size="small" style="width: calc(100% - 90px);" @change="typesChange">
                  <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </el-col>

            <el-col v-if="business_type === 5" :span="6">
              <div class="search-item">
                <span class="search-label">子业态：</span>
                <el-select v-model="searchForm.sub_business_type" clearable placeholder="全部" size="small" style="width: calc(100% - 90px);" @change="subBusinessTypeChange">
                  <el-option v-for="item in subBusinessTypes" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">所属分台：</span>
                <branch-select
                  v-model="adminSearchForm.branch_id"
                  style="width: calc(100% - 90px);"
                  @change="handleAdminSearch"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">司机信息：</span>
                <el-input v-model="searchForm.keyword_driver" clearable placeholder="请输入司机信息" size="small" style="width: calc(100% - 90px);" />
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="search-item">
                <span class="search-label no-wrap">结算开始日期：</span>
                <el-date-picker
                  v-model="searchForm.start_date"
                  type="date"
                  size="small"
                  placeholder="选择开始日期"
                  value-format="yyyy-MM-dd"
                  style="width: calc(100% - 90px);"
                  :picker-options="{
                    disabledDate: time => time.getTime() > Date.now()
                  }"
                  :default-value="getFirstDayOfCurrentMonth()"
                  @change="handleStartDateChange"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item">
                <span class="search-label no-wrap">结算结束日期：</span>
                <el-date-picker
                  v-model="searchForm.end_date"
                  type="date"
                  placeholder="选择结束日期"
                  value-format="yyyy-MM-dd"
                  size="small"
                  style="width: calc(100% - 90px);"
                  :picker-options="{
                    disabledDate: time => time.getTime() > Date.now() || (searchForm.start_date && time.getTime() < new Date(searchForm.start_date).getTime())
                  }"
                  :default-value="getLastDayOfCurrentMonth()"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item">
                <span class="search-label">所属线路：</span>
                <line-select
                  v-model="searchForm.line_class_id"
                  size="small"
                  style="width: calc(100% - 90px);"
                  multiple
                  @change="handleSearch"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item search-buttons">
                <el-button type="primary" size="small" icon="el-icon-search" @click="handleSearch">查询</el-button>
                <el-button size="small" icon="el-icon-refresh" @click="resetSearch">重置</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 结算汇总信息 -->
      <div v-loading="loading" class="settlement-container">
        <div class="settlement-section income-section">
          <div class="section-header">
            <div class="header-title">
              <i class="el-icon-money" />
              <span>收入汇总(线上)</span>
              <span class="sub-title">按照订单的完成时间进行汇总 (只记录线上支付订单)</span>
            </div>
            <div class="header-extra">
              <span class="total-amount">总收入：¥{{ totalAmount }}</span>
              <span class="order-count">总订单：{{ totalOrders }}</span>
            </div>
          </div>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="income-card custom">
                <div class="card-icon">
                  <i class="el-icon-s-custom" />
                </div>
                <div class="card-content">
                  <div class="card-title">定制客运收入</div>
                  <div class="card-amount">{{ metaData.total_custom_orders_amount }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_custom_orders | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="income-card charter">
                <div class="card-icon">
                  <i class="el-icon-truck" />
                </div>
                <div class="card-content">
                  <div class="card-title">包车收入</div>
                  <div class="card-amount">{{ metaData.total_baoche_orders_amount }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_baoche_orders | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="income-card express">
                <div class="card-icon">
                  <i class="el-icon-box" />
                </div>
                <div class="card-content">
                  <div class="card-title">货运收入</div>
                  <div class="card-amount">{{ metaData.total_daihuo_orders_amount }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_daihuo_orders | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 费用明细 -->
        <div class="settlement-section fee-section">
          <div class="section-header">
            <div class="header-title">
              <i class="el-icon-bank-card" />
              <span>费用明细</span>
            </div>
          </div>
          <el-row :gutter="16" class="fee-row">
            <el-col
              v-for="(item, index) in [{
                title: '退票手续费',
                amount: metaData.total_refund_fee,
                type: 'positive',
                icon: 'el-icon-money',
                tooltip: '退票产生的手续费收入'
              }, {
                title: '第三方费用',
                amount: metaData.total_third_fee,
                type: 'negative',
                icon: 'el-icon-bank-card',
                tooltip: '订单金额的0.6%需作为交易手续费'
              }, {
                title: '平台服务费',
                amount: metaData.total_platform_fee,
                type: 'negative',
                icon: 'el-icon-office-building',
                tooltip: '(定制客运+包车+货运+退票手续费-第三方费用)*费率'
              }]" :key="index" :span="8"
            >
              <div class="fee-card">
                <div class="fee-header">
                  <i :class="item.icon" />
                  <span>{{ item.title }}</span>
                  <el-tooltip :content="item.tooltip" placement="top" effect="light">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </div>
                <div class="fee-amount" :class="item.type">
                  {{ item.type === 'positive' ? '+' : '-' }}¥{{ item.amount }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 最终应结算 -->
        <div class="settlement-section final-section">
          <div class="section-header">
            <div class="header-title">
              <i class="el-icon-wallet" />
              <span>最终应结算</span>
              <span class="sub-title">只结算线上订单</span>
            </div>
          </div>
          <div class="calculation-steps">
            <div class="step-item">
              <span>收入合计（不含退票手续费）</span>
              <span>¥{{ metaData.total_amount }}</span>
            </div>
            <div class="step-item">
              <span>退票手续费</span>
              <span class="positive">+¥{{ metaData.total_refund_fee }}</span>
            </div>
            <div class="step-item">
              <span>第三方费用</span>
              <span class="negative">-¥{{ metaData.total_third_fee }}</span>
            </div>
            <div class="step-item">
              <span>平台服务费</span>
              <span class="negative">-¥{{ metaData.total_platform_fee }}</span>
            </div>
            <div class="final-amount">
              <span>应结算金额</span>
              <span>¥{{ metaData.total_amount_settlement }}</span>
            </div>
          </div>
        </div>

        <div class="settlement-section income-section compact">
          <div class="section-header">
            <div class="header-title">
              <i class="el-icon-money" />
              <span>收入汇总(线下)</span>
              <span class="sub-title">按照订单的完成时间进行汇总(只记录线下支付订单，不进入应结算金额)</span>
            </div>
            <div class="header-extra">
              <span class="total-amount">总收入：¥{{ totalAmountOffline }}</span>
              <span class="order-count">总订单：{{ totalOrdersOffline }}</span>
            </div>
          </div>
          <el-row :gutter="12">
            <el-col :span="8">
              <div class="income-card custom small">
                <div class="card-icon">
                  <i class="el-icon-s-custom" />
                </div>
                <div class="card-content">
                  <div class="card-title">定制客运收入</div>
                  <div class="card-amount">{{ metaData.total_custom_orders_amount_offline }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_custom_orders_offline | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="income-card charter small">
                <div class="card-icon">
                  <i class="el-icon-truck" />
                </div>
                <div class="card-content">
                  <div class="card-title">包车收入</div>
                  <div class="card-amount">{{ metaData.total_baoche_orders_amount_offline }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_baoche_orders_offline | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="income-card express small">
                <div class="card-icon">
                  <i class="el-icon-box" />
                </div>
                <div class="card-content">
                  <div class="card-title">货运收入</div>
                  <div class="card-amount">{{ metaData.total_daihuo_orders_amount_offline }}</div>
                  <div class="card-footer">
                    <span class="order-count">订单：{{ metaData.total_daihuo_orders_offline | int }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 提现申请弹窗 -->
    <el-dialog
      title="提现申请"
      :visible.sync="withdrawalDialogVisible"
      width="750px"
      :before-close="closeWithdrawalDialog"
      custom-class="withdrawal-dialog"
    >
      <div v-if="withdrawalDialogVisible" class="withdrawal-dialog-content">
        <withdrawal-application
          :query-params="withdrawalQueryParams"
          :only-show-history="false"
          :hide-history="true"
          :compact="true"
          @submit-success="handleWithdrawalSuccess"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFinancialSettlements, getFinancialSettlementBranches } from '@/api/withdrawal'
import BranchSelect from '@/components/BranchSelect'
import WithdrawalApplication from '../withdrawal/components/withdrawal-application.vue'
import { mapGetters } from 'vuex'
import moment from 'moment'
import LineSelect from '@/views/components/lineIdSelect/index.vue'

export default {
  name: 'FinancialSettlement',
  components: {
    BranchSelect,
    WithdrawalApplication,
    LineSelect
  },
  filters: {
    int(value) {
      return Number(value).toFixed(0)
    }
  },
  data() {
    return {
      metaData: {
        total_custom_orders_amount: '0.00',
        total_custom_orders: 0,
        total_custom_orders_amount_offline: '0.00',
        total_custom_orders_offline: 0,
        total_baoche_orders_amount: '0.00',
        total_baoche_orders: 0,
        total_baoche_orders_amount_offline: '0.00',
        total_baoche_orders_offline: 0,
        total_daihuo_orders_amount: '0.00',
        total_daihuo_orders: 0,
        total_daihuo_orders_amount_offline: '0.00',
        total_daihuo_orders_offline: 0,
        total_platform_fee: '0.00',
        total_refund_fee: '0.00'
      },
      isAdmin: true,
      adminActiveTab: 'query',
      adminSearchForm: {
        branch_id: ''
      },
      searchForm: {
        start_date: moment().startOf('month').format('YYYY-MM-DD'),
        end_date: moment().endOf('month').format('YYYY-MM-DD'),
        line_class_id: '',
        start_address_code: '',
        end_address_code: '',
        sub_business_type: ''
      },
      lineParams: {
        line_class_id: '',
        time: '',
        start_address_code: '',
        end_address_code: ''
      },
      companyData: [{
        name: '辉南县客运公司-松林路分公司',
        orderAmount: '0.00',
        packageAmount: '0.00',
        expressFee: '0.00'
      }],
      refundData: [{
        refundFee: '0.00',
        thirdPartyFee: '0.00',
        platformFee: '0.00'
      }],
      orderList: [],
      page: 1,
      page_size: 10,
      total: 0,
      branchOptions: [],
      loading: false,
      summaryData: {
        totalAmount: '0.00',
        totalOrders: 0,
        customOrders: 0,
        charterOrders: 0,
        expressOrders: 0
      },
      business_type: null,
      withdrawalDialogVisible: false,
      withdrawalQueryParams: {}
    }
  },
  computed: {
    ...mapGetters([
      'loginType',
      'info',
      'isBranchAccount'
    ]),
    totalAmount() {
      const totalCustomAmount = parseFloat(this.metaData && this.metaData.total_custom_orders_amount || 0)
      const totalBaocheAmount = parseFloat(this.metaData && this.metaData.total_baoche_orders_amount || 0)
      const totalDaihuoAmount = parseFloat(this.metaData && this.metaData.total_daihuo_orders_amount || 0)
      return (totalCustomAmount + totalBaocheAmount + totalDaihuoAmount).toFixed(2)
    },
    totalAmountOffline() {
      const totalCustomAmountOffline = parseFloat(this.metaData && this.metaData.total_custom_orders_amount_offline || 0)
      const totalBaocheAmountOffline = parseFloat(this.metaData && this.metaData.total_baoche_orders_amount_offline || 0)
      const totalDaihuoAmountOffline = parseFloat(this.metaData && this.metaData.total_daihuo_orders_amount_offline || 0)
      return (totalCustomAmountOffline + totalBaocheAmountOffline + totalDaihuoAmountOffline).toFixed(2)
    },
    totalOrders() {
      const totalCustomOrders = parseInt(this.metaData && this.metaData.total_custom_orders || 0)
      const totalBaocheOrders = parseInt(this.metaData && this.metaData.total_baoche_orders || 0)
      const totalDaihuoOrders = parseInt(this.metaData && this.metaData.total_daihuo_orders || 0)
      return (totalCustomOrders + totalBaocheOrders + totalDaihuoOrders)
    },
    totalOrdersOffline() {
      const totalCustomOrdersOffline = parseInt(this.metaData && this.metaData.total_custom_orders_offline || 0)
      const totalBaocheOrdersOffline = parseInt(this.metaData && this.metaData.total_baoche_orders_offline || 0)
      const totalDaihuoOrdersOffline = parseInt(this.metaData && this.metaData.total_daihuo_orders_offline || 0)
      return (totalCustomOrdersOffline + totalBaocheOrdersOffline + totalDaihuoOrdersOffline)
    },
    types() {
      const arr = []
      for (const val in (this.info && this.info.business_types ? this.info.business_types : {})) {
        arr.push({
          value: Number(val),
          label: this.info && this.info.business_types ? this.info.business_types[val] : ''
        })
      }
      return arr
    },
    subBusinessTypes() {
      return [
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ]
    },
    calculateFinalAmount() {
      const refundFee = parseFloat(this.refundData[0].refundFee || 0)
      const thirdPartyFee = parseFloat(this.refundData[0].thirdPartyFee || 0)
      const platformFee = parseFloat(this.refundData[0].platformFee || 0)

      const finalAmount = parseFloat(this.totalAmount) + refundFee - thirdPartyFee - platformFee

      return finalAmount.toFixed(2)
    },
    isBranchAccount() {
      console.log('loginType', this.loginType)
      return this.loginType === 'branch'
    }
  },
  created() {
    this.ensureLoginType()
    this.getBranches()
    this.initializeDates()
    this.fetchSettlementData()
  },
  methods: {
    ensureLoginType() {
      const savedLoginType = localStorage.getItem('loginType')
      if (savedLoginType && this.loginType !== savedLoginType) {
        console.log('修正loginType:', savedLoginType)
        this.$store.commit('user/SET_LOGIN_TYPE', savedLoginType)
      }
    },
    initializeDates() {
      this.searchForm.start_date = this.formatDate(this.getFirstDayOfCurrentMonth())
      this.searchForm.end_date = this.formatDate(this.getLastDayOfCurrentMonth())
    },
    async getBranches() {
      try {
        this.loading = true
        const response = await getFinancialSettlementBranches()
        if (response.code === 200) {
          const branchData = response.data
          this.branchOptions = branchData
          this.branchOptions.unshift({
            admin_id: '',
            mchname: '全部',
            level: 0
          })
        } else {
          this.$message.error(response.message || '获取运营公司列表失败')
        }
      } catch (error) {
        console.error('获取运营公司列表失败:', error)
        this.$message.error('获取运营公司列表失败')
      } finally {
        this.loading = false
      }
    },
    async fetchSettlementData() {
      this.loading = true
      try {
        const params = {
          page: this.page,
          page_size: this.page_size,
          start_date: this.searchForm.start_date,
          end_date: this.searchForm.end_date,
          branch_id: this.adminSearchForm.branch_id,
          order_type: this.business_type ? [this.business_type] : [],
          sub_business_type: this.searchForm.sub_business_type,
          keyword_driver: this.searchForm.keyword_driver,
          line_class_id: this.searchForm.line_class_id
        }
        if (this.lineParams.time) {
          params.time = this.lineParams.time
        }
        if (this.lineParams.start_address_code) {
          params.start_address_code = this.lineParams.start_address_code
        }
        if (this.lineParams.end_address_code) {
          params.end_address_code = this.lineParams.end_address_code
        }
        const response = await getFinancialSettlements(params)
        if (response.code === 200) {
          const apiData = response.data
          this.orderList = (apiData.data || []).map(item => {
            const payTime = item.paid_history ? item.paid_history.paid_at : ''
            return {
              orderNo: item.order_no,
              orderType: item.cn_type || '定制班线车',
              createTime: item.create_time,
              payTime: payTime,
              amount: item.merge_price,
              refundFee: item.refund_fee,
              thirdPartyFee: '0.00',
              platformFee: '0.00',
              cn_state: item.cn_state || (item.cn_refund_status || '未退款')
            }
          })
          this.total = apiData.meta.pagination.total || 0
          this.companyData = [{
            name: '当前线路分台',
            orderAmount: apiData.meta.total_custom_orders_amount || '0.00',
            packageAmount: apiData.meta.total_baoche_orders_amount || '0.00',
            expressFee: apiData.meta.total_daihuo_orders_amount || '0.00',
            orderAmount_offline: apiData.meta.total_custom_orders_amount_offline || '0.00',
            packageAmount_offline: apiData.meta.total_baoche_orders_amount_offline || '0.00',
            expressFee_offline: apiData.meta.total_daihuo_orders_amount_offline || '0.00'
          }]
          this.refundData = [{
            refundFee: apiData.meta.total_refund_fee || '0.00',
            thirdPartyFee: '0.00',
            platformFee: apiData.meta.total_platform_fee || '0.00'
          }]
          this.metaData = {
            ...(apiData && apiData.meta || {})
          }
        } else {
          this.$message.error(response.message || '获取结算数据失败')
        }
      } catch (error) {
        console.error('获取结算数据失败:', error)
        this.$message.error('获取结算数据失败')
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      if (this.searchForm.start_date && !this.searchForm.end_date) {
        this.$message({
          message: '请选择结束日期',
          type: 'warning'
        })
        return
      }
      this.page = 1
      this.fetchSettlementData()
    },
    handleAdminSearch() {
      this.page = 1
      this.fetchSettlementData()
    },
    handleSizeChange(val) {
      this.page_size = val
      this.fetchSettlementData()
    },
    handleCurrentChange(val) {
      this.page = val
      this.fetchSettlementData()
    },
    handleWithdraw() {
      const queryParams = {
        start_date: this.searchForm.start_date,
        end_date: this.searchForm.end_date,
        branch_id: this.adminSearchForm.branch_id,
        line_class_id: this.lineParams.line_class_id,
        time: this.lineParams.time,
        start_address_code: this.lineParams.start_address_code,
        end_address_code: this.lineParams.end_address_code
      }

      this.withdrawalQueryParams = queryParams
      this.withdrawalDialogVisible = true
    },
    closeWithdrawalDialog() {
      this.withdrawalDialogVisible = false
      this.fetchSettlementData()
    },
    handleWithdrawalSuccess() {
      this.$message.success('提现申请已成功提交，请等待审核')
      this.withdrawalDialogVisible = false
      this.fetchSettlementData()
    },
    handleStartDateChange(value) {
      if (value) {
        this.searchForm.end_date = ''
        this.$message({
          message: '请选择结束日期',
          type: 'warning'
        })
      }
    },
    getYesterday() {
      const date = new Date()
      date.setDate(date.getDate() - 1)
      return date
    },
    getFirstDayOfCurrentMonth() {
      const date = new Date()
      date.setDate(1)
      return date
    },
    getLastDayOfCurrentMonth() {
      const date = new Date()
      const year = date.getFullYear()
      const month = date.getMonth()
      const lastDay = new Date(year, month + 1, 0)
      return lastDay
    },
    getIndentedBranchName(item) {
      const level = typeof item.level === 'number' ? item.level : 0
      if (level === 0) {
        return item.mchname
      }
      const indent = '\u00A0\u00A0\u00A0\u00A0'.repeat(level)
      return indent + '└ ' + item.mchname
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    handleLineChange(data) {
      this.lineParams = {
        line_class_id: data.line_class_id || '',
        time: data.time || '',
        start_address_code: data.start_address_code || '',
        end_address_code: data.end_address_code || ''
      }
    },
    typesChange(type) {
      this.business_type = type
      this.searchForm.sub_business_type = ''
      this.handleSearch()
    },
    subBusinessTypeChange(type) {
      this.searchForm.sub_business_type = type
      this.handleSearch()
    },
    resetSearch() {
      this.searchForm = {
        start_date: this.getFirstDayOfCurrentMonth(),
        end_date: this.getLastDayOfCurrentMonth(),
        keyword_driver: '',
        line_class_id: null,
        sub_business_type: null
      }
      this.business_type = null
      this.adminSearchForm.branch_id = null
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.component-container {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  background-color: #f8f8fa;
}

.search-form {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.search-label {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  width: 90px;
  text-align: right;
  padding-right: 10px;
}

.search-label.no-wrap {
  white-space: nowrap;
}

.search-buttons {
  display: flex;
  justify-content: flex-start;
  margin-left: 90px;

  .el-button {
    margin-right: 10px;
  }
}

.withdrawal-application {
  .withdrawal-header {
    margin-bottom: 10px;
  }

  .withdrawal-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-content {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;

      i {
        margin-right: 6px;
        color: #409EFF;
      }
    }
  }
}

.settlement-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  &.compact {
    padding: 16px;
    margin-bottom: 16px;
    filter: grayscale(100%);

    .section-header {
      margin-bottom: 12px;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;

    i {
      margin-right: 6px;
      color: #409EFF;
      font-size: 18px;
    }

    .sub-title {
      font-size: 12px;
      color: #909399;
      font-weight: 400;
      margin-left: 8px;
    }
  }

  .header-extra {
    .total-amount {
      font-size: 16px;
      font-weight: 600;
      color: #f56c6c;
      margin-right: 16px;
    }

    .order-count {
      font-size: 14px;
      color: #606266;
    }
  }
}

.income-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  height: 100px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;

  &.small {
    padding: 12px;
    height: 80px;

    .card-icon {
      width: 40px;
      height: 40px;

      i {
        font-size: 20px;
      }
    }

    .card-content {
      .card-title {
        font-size: 13px;
        margin-bottom: 4px;
      }

      .card-amount {
        font-size: 18px;
        margin-bottom: 4px;
      }

      .card-footer {
        font-size: 12px;
      }
    }
  }

  &.custom {
    background: linear-gradient(135deg, #e6f7ff 0%, #b3e0ff 100%);

    .card-icon {
      background-color: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }
  }

  &.charter {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);

    .card-icon {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
  }

  &.express {
    background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);

    .card-icon {
      background-color: rgba(250, 173, 20, 0.1);
      color: #faad14;
    }
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;

    i {
      font-size: 24px;
    }
  }

  .card-content {
    flex: 1;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .card-amount {
      font-size: 22px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }

    .card-footer {
      font-size: 13px;
      color: #909399;
    }
  }
}

.fee-section {
  .fee-row {
    margin-top: 16px;
  }

  .fee-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    height: 100%;

    .fee-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 8px;
      }

      span {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }

      .el-icon-question {
        margin-left: 4px;
        color: #909399;
        cursor: pointer;
        font-size: 14px;
      }
    }

    .fee-amount {
      font-size: 20px;
      font-weight: 600;

      &.positive {
        color: #52c41a;
      }

      &.negative {
        color: #f56c6c;
      }
    }
  }
}

.final-section {
  .calculation-steps {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;

    .step-item {
      display: flex;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px dashed #e8e8e8;

      span {
        font-size: 14px;
        color: #606266;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #f56c6c;
        }
      }
    }

    .final-amount {
      display: flex;
      justify-content: space-between;
      padding-top: 16px;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        &:last-child {
          color: #f56c6c;
          font-size: 20px;
        }
      }
    }
  }
}

:deep(.withdrawal-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.withdrawal-dialog .el-dialog__header) {
  padding: 15px 20px;
  text-align: left;
}

:deep(.withdrawal-dialog .el-dialog__title) {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

:deep(.withdrawal-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #909399;
  font-size: 16px;
}

:deep(.withdrawal-dialog .el-dialog__body) {
  padding: 0;
}

.withdrawal-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.search-form {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.search-label {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  width: 90px;
  text-align: right;
  padding-right: 10px;
}

.search-label.no-wrap {
  white-space: nowrap;
}

.search-buttons {
  display: flex;
  justify-content: flex-start;
  margin-left: 90px;

  .el-button {
    margin-right: 10px;
  }
}
</style>

<template>
  <div v-loading="isLoading" class="refound-ticket">
    <el-table :data="logs" element-loading-text="" fit highlight-current-row size="small">
      <el-table-column label="退款编号" width="185" prop="refund_no" />
      <el-table-column label="退款金额" width="100" prop="refunded_amount" />
      <el-table-column label="退款手续费" width="100" prop="refund_fee" />
      <el-table-column label="退款状态" width="100">
        <template slot-scope="scope">
          <Tablestatus v-if="scope.row.status === 'success'" mode="success" word="已退回" />
          <Tablestatus v-else-if="scope.row.status === 'fail'" mode="error" word="退款失败" />
          <Tablestatus v-else-if="scope.row.status === 'invalid'" mode="success" word="已作废" />
          <Tablestatus v-else mode="warning" word="退款中" />
        </template>
      </el-table-column>
      <el-table-column label="操作时间" width="180" prop="created_at" />
      <el-table-column label="到账时间" width="180" prop="refuned_at" />
      <el-table-column label="退款原因" min-width="155" prop="reason" />
    </el-table>
    <div class="dialog-footer">
      <el-button type="primary" size="small" @click="cancel">确认</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { ticRefundLog } from '@/api/order'

export default {
  props: {
    ticket: {
      type: Number,
      default: () => 0
    },
    logstatus: {
      type: Boolean,
      default: () => false
    }
  },

  data () {
    return {
      logs: null,
      isLoading: false
    }
  },
  computed: {
    ...mapGetters(['setting'])
  },
  watch: {
    logstatus: {
      handler (n) {
        console.log('n', n)
        if (!n) return
        this.logs = null
        this.fetchLogs()
      },
      immediate: true
    }
  },
  methods: {
    cancel () {
      this.$emit('close')
    },
    async fetchLogs (loading = true) {
      if (loading) this.isLoading = true
      // const query = Object.assign({}, this.logQuery)
      const query = { ticket: this.ticket }
      const data = await ticRefundLog(query)
      console.log(data)
      if (loading) this.isLoading = false
      this.logs = data || []
    }
  }
}
</script>
<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .table-p {
    margin: 0;
    padding: 0;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .reason-txt {
    font-size: 14px;
    line-height: 30px;
    margin-top: 10px;
  }
  .final-word {
    font-size: 14px;
    line-height: 30px;
    margin-top: 20px;
    .err {
      color: #ff4d4f;
    }
  }
</style>

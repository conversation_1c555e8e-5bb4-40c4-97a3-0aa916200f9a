<template>
  <div v-loading="loading" class="description-warp">
    <el-skeleton v-if="!orderDetail" style="height: 400px" />
    <h6 class="item-description-title">基本信息</h6>
    <el-descriptions v-if="orderDetail" :column="3" size="small" border>
      <el-descriptions-item label="订单编号">{{ orderDetail.order_no }} / {{ orderDetail.order_id }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        <el-tag size="mini" effect="plain">
          {{
            orderDetail.type === 7
              ? [1, 2].includes(orderDetail.ferry_type)
                ? "摆渡车"
                : "快车"
              : orderDetail.order_type_string
          }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="线路">
        <span v-if="orderDetail.type === 4" class="table-p">
          {{ orderDetail.agency && orderDetail.agency.name }}
        </span>
        <span v-else class="table-p">
          {{ orderDetail | filSname }}
          <i class="el-icon-right" />
          {{ orderDetail | filEname }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="班次编号">{{ orderDetail.line_class_train && orderDetail.line_class_train.line_class_train_no || "/" }}
      </el-descriptions-item>
      <el-descriptions-item label="出发时间">{{ orderDetail.start_time }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        <Tablestatus mode="default" :word="orderDetail.cn_state" />
      </el-descriptions-item>
      <el-descriptions-item label="支付状态">
        <Tablestatus v-if="orderDetail.is_pay === 1 || orderDetail.is_pre_pay === 1" mode="success" word="已支付" />
        <Tablestatus v-else mode="warning" word="待支付" />
      </el-descriptions-item>
      <el-descriptions-item label="线下车票">
        <el-tag v-if="orderDetail.offline_ticket_issued" type="success" size="mini">已开具纸质车票</el-tag>
        <el-tag v-else type="info" size="mini">未开具纸质车票</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="预定车票数">{{ orderDetail.total_booking_seats }} 张
      </el-descriptions-item>
      <el-descriptions-item label="实际购票数">{{ orderDetail.book_seating }} 张
      </el-descriptions-item>
      <el-descriptions-item label="购票人">{{ orderDetail.passenger && orderDetail.passenger.name || "-" }}/{{ orderDetail.passenger && orderDetail.passenger.cellphone || "/" }}
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ orderDetail.reseverd_phone }}
      </el-descriptions-item>
      <el-descriptions-item label="座位信息">{{ orderDetail.seat_title || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="上车点">{{ orderDetail.start_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item label="下车点">{{ orderDetail.end_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item label="订单总价">¥ {{ orderDetail.price }}元
      </el-descriptions-item>
      <el-descriptions-item label="优惠金额">
        <span v-if=" Number(orderDetail.coupon_price)>0" class="refund-text"> ¥ {{ orderDetail.coupon_price }}元</span>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="积分抵扣金额">
        <span v-if=" Number(orderDetail.point_redeemable_amount)>0" class="refund-text"> ¥ {{ orderDetail.point_redeemable_amount }}元 {{ orderDetail.cn_point_redeemable_ratio }} </span>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="实付金额">¥ {{ orderDetail.real_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="渠道所得">¥ {{ orderDetail.offer_channel_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="司机所得">¥ {{ orderDetail.offer_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="平台对司机提成">{{ orderDetail.split * 100 }} %
      </el-descriptions-item>
      <el-descriptions-item label="退款比例">{{ orderDetail.refund_ticket_ratio * 100 }} %
      </el-descriptions-item>
      <el-descriptions-item label="已退款金额">¥ {{ orderDetail.refund_amount }}元
      </el-descriptions-item>
      <el-descriptions-item label="退款手续费">¥ {{ orderDetail.refund_fee }}元
      </el-descriptions-item>
      <el-descriptions-item label="下单时间">{{ orderDetail.create_time }}
      </el-descriptions-item>
      <el-descriptions-item label="所属分台">{{ orderDetail.branch.mchname || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="代约分台">{{
        (orderDetail.temp_apply_branch &&
          orderDetail.temp_apply_branch.mchname) ||
          "-"
      }}
      </el-descriptions-item>
      <el-descriptions-item label="渠道名称">{{ orderDetail.channel_branch_name || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="乘客留言">{{ orderDetail.reseverd_info || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="航班号">{{ orderDetail.ft_number || "-" }}
      </el-descriptions-item>
      <el-descriptions-item v-if="orderDetail.ch_id" label="关联摆渡车订单">
        <el-button v-for="(item, i) in orderDetail.sub_order_nos" :key="i" type="warning" plain size="mini" @click.stop="jumpOrder(item)">{{ item }}<i class="el-icon-right" /></el-button>
      </el-descriptions-item>
    </el-descriptions>
    <h6 class="item-description-title">车票信息</h6>
    <template v-if="orderDetail && orderDetail.type === 5">
      <el-descriptions v-for="(item, index) in orderDetail.dingzhikeyun_tickets" :key="index" :column="3" size="small" border>
        <el-descriptions-item label="票号">{{ item.ticket_no }}
        </el-descriptions-item>
        <el-descriptions-item label="乘车人">{{ item.passenger_name || "-" }} /
          {{ item.passenger_id_no || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="座位">{{ item.seat_label || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="车票状态">
          <Tablestatus v-if="item.status === 'success'" mode="success" :word="item.cn_status" />
          <Tablestatus v-else-if="item.status === 'checked'" mode="success" :word="item.cn_status" />
          <Tablestatus v-else-if="item.status === 'refunded'" mode="default" :word="item.cn_status" />
          <Tablestatus v-else mode="processing" word="出票中" />
        </el-descriptions-item>
        <el-descriptions-item label="全部退款">{{ item.refund_type == 2 ? "是" : "否" }}
        </el-descriptions-item>
        <el-descriptions-item label="车票金额">{{ item.actual_amount }}
        </el-descriptions-item>
        <el-descriptions-item label="上车时间">{{ item.boarding_time }}
        </el-descriptions-item>
        <el-descriptions-item label="上车点">{{ item.boarding_point }}
        </el-descriptions-item>
        <el-descriptions-item label="下车点">{{ item.drop_off_point }}
        </el-descriptions-item>
        <el-descriptions-item label="退票原因">
          {{ item.reason || "-" }}
        </el-descriptions-item>

        <el-descriptions-item label="电子客票号">
          {{ item.eticket_no || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="电子客票状态">
          {{ item.cn_eticket_status || "-" }}
        </el-descriptions-item>

      </el-descriptions>
    </template>
    <br>
    <el-descriptions v-if="orderDetail && orderDetail.driver" title="司机信息" :column="2" size="small" border>
      <el-descriptions-item label="司机名称">{{ orderDetail.driver.name }}
      </el-descriptions-item>
      <el-descriptions-item label="司机归属">{{ orderDetail.driver && orderDetail.driver.driver_group_name || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="派单时间">{{ orderDetail.dispatched_at }}
      </el-descriptions-item>
      <el-descriptions-item label="司机电话">{{ orderDetail.driver.cellphone }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆品牌">{{ orderDetail.driver.car_brand }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌号码">
        <el-tag size="mini" type="info" effect="dark">
          {{ orderDetail.driver.car_tail_number }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { orderInfo } from '@/api/business'
import { formatStartName, formatEndName } from '@/utils/filters'

export default {
  filters: {
    filterStart (val) {
      return (
        formatStartName(val).start_city +
        ' | ' +
        formatStartName(val).start_name
      )
    },
    filterEnd (val) {
      return formatEndName(val).end_city + ' | ' + formatEndName(val).end_name
    },
    filSname (val) {
      return formatStartName(val).start_name
    },
    filEname (val) {
      return formatEndName(val).end_name
    }
  },
  props: {
    order: {
      type: String,
      default: ''
    },
    desStatus: {
      type: Boolean,
      default: () => false
    }
  },
  data () {
    return {
      orderDetail: null,
      loading: false
    }
  },
  watch: {
    // order: {
    //   handler (n) {
    //     if (n) {
    //       this.fetchDetail()
    //     }
    //   },
    //   immediate: true
    // },
    desStatus: {
      handler (n) {
        if (n) {
          this.orderDetail = null
          this.fetchDetail()
        }
      },
      immediate: true
    }
  },
  methods: {
    jumpOrder (no_) {
      this.$router.push({
        path: '/orderManage/kuiacheManage', query: {
          oid: no_
        }
      })
    },
    async fetchDetail () {
      this.loading = true
      const { data } = await orderInfo({ order: this.order })
      this.loading = false
      this.orderDetail = data
    }
  }
}
</script>
<style lang="scss">
  .description-warp {
    .el-descriptions--small.is-bordered .el-descriptions-item__cell {
      min-width: 80px;
    }

    .el-descriptions-item__content {
      min-width: 280px;
    }
  }

  .el-descriptions__header {
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .item-description-title {
    position: relative;
    font-size: 15px;
    margin: 16px 0;

    &:before {
      width: 4px;
      border-radius: 5px;
      height: 60%;
      background: #0067e1;
      position: absolute;
      left: -10px;
      top: 0;
      bottom: 0;
      margin: auto;
      content: "";
      display: block;
    }
  }
</style>

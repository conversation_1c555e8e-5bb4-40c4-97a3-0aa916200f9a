<template>
  <el-dialog
    :visible.sync="visible"
    :title="`航班信息查询: ${flightNumber}`"
    width="600px"
    append-to-body
    center
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @click.native.stop
  >
    <div class="flight-info-container">
      <div class="flight-external-search">
        <i class="el-icon-info" />
        <p>请前往第三方网站查询航班详情</p>
        <el-button type="primary" @click="openExternalSearch">
          前往查询
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FlightInfoDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    flightNumber: {
      type: String,
      default: ''
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.$emit('close')
      }
    }
  },
  methods: {
    openExternalSearch() {
      // 使用航旅纵横查询航班信息
      const url = `https://flights.ctrip.com/actualtime/detail.html?flightNo=${this.flightNumber}`
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.flight-info-container {
  min-height: 200px;
  padding: 10px 0;
}

.flight-external-search {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #909399;

  i {
    font-size: 40px;
    margin-bottom: 15px;
  }

  p {
    margin-bottom: 20px;
    font-size: 16px;
  }
}
</style>

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="路线详情"
    width="500px"
    custom-class="route-dialog"
    append-to-body
    @closed="handleClose"
  >
    <div class="route-dialog-content">
      <!-- 路线信息头部 -->
      <div class="route-header">
        <div class="route-title">
          <div class="route-from">
            <div class="city">{{ startCity }}</div>
            <div class="location">{{ startName }}</div>
          </div>
          <div class="route-arrow">
            <i class="el-icon-right" />
          </div>
          <div class="route-to">
            <div class="city">{{ endCity }}</div>
            <div class="location">{{ endName }}</div>
          </div>
        </div>
      </div>

      <!-- 站点信息 -->
      <div class="route-stations">
        <div class="timeline">
          <div class="timeline-item start-point">
            <div class="point-icon">
              <i class="point-dot" />
            </div>
            <div class="point-info">
              <div class="point-title">上车点</div>
              <div class="point-address">{{ startAddressRemark || "未知" }}</div>
            </div>
          </div>
          <div class="timeline-line" />
          <div class="timeline-item end-point">
            <div class="point-icon">
              <i class="point-dot" />
            </div>
            <div class="point-info">
              <div class="point-title">下车点</div>
              <div class="point-address">{{ endAddressRemark || "未知" }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单信息 -->
      <div v-if="showOrderInfo" class="order-info">
        <div class="order-section">
          <h4 class="section-title">订单信息</h4>
          <div class="info-item">
            <span class="label">订单编号:</span>
            <span class="value">{{ orderNo }}</span>
          </div>
          <div class="info-item">
            <span class="label">出发时间:</span>
            <span class="value">{{ startTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话:</span>
            <span class="value">{{ passengerPhone }}</span>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">关闭</el-button>
      <el-button v-if="showOrderInfo" size="small" type="primary" @click="checkOrderDetail">查看订单详情</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'RouteDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    startCity: {
      type: String,
      default: ''
    },
    startName: {
      type: String,
      default: ''
    },
    endCity: {
      type: String,
      default: ''
    },
    endName: {
      type: String,
      default: ''
    },
    startAddressRemark: {
      type: String,
      default: ''
    },
    endAddressRemark: {
      type: String,
      default: ''
    },
    showOrderInfo: {
      type: Boolean,
      default: true
    },
    orderNo: {
      type: String,
      default: ''
    },
    startTime: {
      type: String,
      default: ''
    },
    passengerPhone: {
      type: String,
      default: ''
    },
    orderId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: this.visible
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    checkOrderDetail() {
      this.$emit('view-order', this.orderId)
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.route-dialog-content {
  padding: 10px;
}

.route-header {
  padding: 15px;
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  border-radius: 8px;
  color: white;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.route-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.route-from, .route-to {
  text-align: center;
  flex: 1;
}

.route-arrow {
  padding: 0 15px;
  font-size: 20px;
}

.city {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.location {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.route-stations {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.timeline {
  position: relative;
}

.timeline-line {
  position: absolute;
  left: 10px;
  top: 25px;
  bottom: 25px;
  width: 2px;
  background-color: #dcdfe6;
}

.timeline-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.point-icon {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  position: relative;
  z-index: 1;
}

.point-dot {
  display: block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  top: 4px;
  left: 4px;
}

.start-point .point-dot {
  background-color: #52c41a;
  box-shadow: 0 0 0 5px rgba(82, 196, 26, 0.2);
}

.end-point .point-dot {
  background-color: #ff4d4f;
  box-shadow: 0 0 0 5px rgba(255, 77, 79, 0.2);
}

.point-info {
  flex: 1;
}

.point-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #606266;
}

.point-address {
  color: #606266;
  line-height: 1.5;
  padding-right: 10px;
}

.order-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #606266;
  flex: 1;
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
}
</style>

<style>
.route-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.route-dialog .el-dialog__title {
  font-size: 16px;
  font-weight: bold;
}

.route-dialog .el-dialog__body {
  padding: 20px;
}

.route-dialog .el-dialog__footer {
  padding: 10px 20px 15px;
  border-top: 1px solid #ebeef5;
}
</style>

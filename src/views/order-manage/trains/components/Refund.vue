<template>
  <div v-loading="loading" class="refound-ticket">
    <h6 class="item-description-title">订单车票</h6>
    <el-card shadow="hover" :body-style="{ padding: '0' }">
      <el-table v-if="showTable" :data="tickets" class="refund-table" element-loading-text="" :row-class-name="tableRowClassName" fit size="small" @selection-change="handleSelectionChange">
        <el-table-column v-if="!readonly" type="selection" width="55" :selectable="selectable" />
        <el-table-column label="票号/乘客" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <p class="table-p">{{ scope.row?.ticket_no || "-" }}</p>
            <p class="table-p">{{ scope.row?.passenger_name || "-" }}/{{ scope.row?.passenger_id_no || "-" }}</p>
          </template>
        </el-table-column>
        <el-table-column label="座位" width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row?.seat_label || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="车票状态" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <Tablestatus v-if="scope.row.status === 'success'" mode="success" :word="scope.row.cn_status" />
            <Tablestatus v-else-if="scope.row.status === 'checked'" mode="warning" :word="scope.row.cn_status" />
            <Tablestatus v-else-if="scope.row.status === 'refunded'" mode="default" :word="scope.row.cn_status" />
            <Tablestatus v-else mode="processing" word="出票中" />
          </template>
        </el-table-column>
        <el-table-column label="车票金额" width="80" prop="actual_amount" />
        <el-table-column label="已退金额" width="95">
          <template slot-scope="scope">
            <template v-if="order.is_pay === 1">
              {{ scope.row?.refunded_amount || "0" }}
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="可退金额" min-width="105" show-overflow-tooltip>
          <template slot-scope="scope">
            <template v-if="order.is_pay === 1">
              {{ scope.row?.refundable_amount || "0" }}
              <!-- <el-tag :type="scope.row?.refundable_amount_rate === 0 ? 'info' : 'success'" size="mini" effect="plain" style="transform: scale(0.9);"> {{
                scope.row?.refundable_amount_rate || "0"
              }}%</el-tag> -->
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column v-if="!readonly" label="修改退款金额/比例" width="160">
          <template slot-scope="scope">
            <template v-if="order.is_pay === 1&&Number(scope.row.refunded_amount_rate)!=100">
              {{ scope.row?.fee_amount || "0" }} /
              <template v-if="Number(scope.row.refundable_amount) > 0">
                <el-input v-model="tickets[scope.$index].fee" style="width: 60px" size="mini" placeholder="比例" @input="(_) => feeChange(_, scope.$index)" />
                %
              </template>
              <span v-else>100%</span>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="left" label="操作" width="90">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showLog(scope.row)">退款记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <template v-if="order.ferry_order&&order.ferry_order.length">
      <h6 class="item-description-title" style="margin-top:30px">关联摆渡车订单</h6>
      <el-card class="box-card" shadow="hover" :body-style="{ padding: '0'}">
        <el-table :data="order.ferry_order" class="refund-table" element-loading-text="" :row-class-name="subRowClassName" fit size="small" @selection-change="handleSubChange">
          <el-table-column type="selection" width="55" :selectable="subSelectable" />
          <el-table-column label="编号" size="small" align="left" prop="order_no" width="120" />
          <el-table-column label="摆渡车类型" size="small" align="left" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.ferry_type === 1" effect="plain" type="primary" size="mini">
                上门接
              </el-tag>
              <el-tag v-else type="warning" size="mini" effect="plain">
                送到家
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="left" label="上下车点" size="small" min-width="280">
            <template slot-scope="scope">
              <p class="table-p cutmore">
                <span class="badge-status" style="background-color: #52c41a" />上：{{ scope.row.start_address_remark || "-" }}
              </p>
              <p class="table-p cutmore">
                <span class="badge-status" style="background-color: #ff4d4f" />下：{{ scope.row.end_address_remark || "-" }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="订单状态" size="small" align="left" width="120">
            <template slot-scope="scope">
              <Tablestatus v-if="scope.row.state === 1 && scope.row.appoint == 0" mode="warning" word="待派单" />
              <Tablestatus v-if="scope.row.state === 1 && scope.row.appoint == 1" mode="processing" word="已派单" />
              <Tablestatus v-else-if="scope.row.state === 2" mode="processing" word="已接单" />
              <Tablestatus v-else-if="scope.row.state === 3" mode="processing" word="已上车" />
              <Tablestatus v-else-if="scope.row.state === 4" mode="processing" word="在路上" />
              <Tablestatus v-else-if="scope.row.state === 5" mode="success" word="已送达" />
              <Tablestatus v-else-if="scope.row.state === 6" mode="success" word="已完成" />
              <Tablestatus v-else-if="scope.row.state === 7" mode="default" word="已取消" />
            </template>
          </el-table-column>
          <el-table-column label="订单金额" align="left" size="small" width="100" prop="real_price" />
          <el-table-column label="退款状态" size="small" align="left" width="130">
            <template slot-scope="scope">
              <div
                v-if="scope.row.is_pay == 1 &&scope.row.refund_status
                " class="column-flex"
              >
                <div class="status-line-top">
                  <span class="refund-text">-￥{{ scope.row.refund_amount }}</span>
                </div>
                <div class="refund-gray">
                  {{ scope.row.refund_status==='success'?'退款成功':scope.row.refund_status==='processing'?'退款中':'退款失败' }}
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="已退金额" min-width="125">
            <template slot-scope="scope">
              <template v-if="order.is_pay === 1">
                {{ scope.row?.refund_total_amount || "0.00" }}
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column> -->
        </el-table>
      </el-card>
    </template>
    <p v-if="multipleSelection && multipleSelection.length > 0" class="final-word">
      退款车票 <span>{{ multipleSelection.length }}</span> 张，
      <span v-if="order.is_pay === 1">
        退款金额 <span class="err">{{ totalAmount }}</span> 元
      </span>
      <span v-else class="err">订单未支付，无退款</span>
    </p>
    <p v-if="subSelection && subSelection.length > 0" class="final-word">
      退款子订单 <span>{{ showids }}</span> ，
      退款金额 <span class="err">{{ totalSub }}</span> 元
    </p>
    <p v-if="(subSelection && subSelection.length > 0)||multipleSelection && multipleSelection.length > 0" class="final-word">
      合计退款金额 <span class="err">{{ Number(totalAmount)+Number(totalSub) }}</span> 元
    </p>
    <template v-if="!readonly">
      <p class="reason-txt" style="margin:30px 0 10px 0">退票原因（选填）</p>
      <el-input v-model="reason" type="textarea" maxlength="40" style="width: 50%" :rows="2" placeholder="请输入" />
      <div class="dialog-footer">
        <el-button size="small" @click="cancel"> 取消 </el-button>
        <el-button type="primary" size="small" @click="handleRefund">确认</el-button>
      </div>
    </template>
    <div v-else class="dialog-footer">
      <el-button type="primary" size="small" @click="cancel">关闭</el-button>
    </div>
    <el-dialog :visible.sync="dialogLogs" :title="ticketTitle" append-to-body width="850px" class="refound-dialog">
      <TicketLog :ticket="ticket" :logstatus="dialogLogs" @close="dialogLogs = false" />
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { ticketRefunds, orderRefunds } from '@/api/order'
import TicketLog from './TicketLog'
import { orderInfo } from '@/api/business'

import moment from 'moment'
export default {
  components: {
    TicketLog
  },
  props: {
    readonly: {
      type: Boolean,
      default: () => false
    },
    orderid: {
      type: Number,
      default: () => 0
    },
    dialogStatus: {
      type: Boolean,
      default: () => false
    }
  },
  data () {
    return {
      multipleSelection: [],
      subSelection: [],
      tickets: null,
      reason: '',
      totalAmount: 0,
      totalSub: 0,
      loading: false,
      dialogLogs: false,
      ticket: '',
      ticketTitle: '',
      order: {},
      showTable: true
    }
  },
  computed: {
    ...mapGetters(['setting'])
    // totalAmount() {
    //   let final = 0
    //   if (this.multipleSelection.length > 0) {
    //     final = this.multipleSelection.map((o) => o.fee_amount).reduce(
    //       (a, b) => Number(a) + Number(b)
    //     )
    //   }
    //   return final
    // },
  },
  watch: {
    multipleSelection: {
      handler (n) {
        if (n.length > 0) {
          this.totalAmount = n.map((o) => o.fee_amount).reduce(
            (a, b) => Number(a) + Number(b)
          )
        } else {
          this.totalAmount = 0
        }
      },
      immediate: true,
      deep: true
    },
    subSelection: {
      handler (n) {
        if (n.length > 0) {
          this.totalSub = n.map((o) => o.real_price).reduce(
            (a, b) => Number(a) + Number(b)
          )
          this.showids = Object.assign([], n).map((o) => o.order_no).join('，')
        } else {
          this.totalSub = 0
          this.showids = ''
        }
      },
      immediate: true,
      deep: true
    },
    dialogStatus: {
      handler (n) {
        if (!n) return
        this.order = {}
        this.multipleSelection = []
        this.tickets = null
        this.subSelection = []
        this.showTable = false
        this.$nextTick(() => {
          this.showTable = true
        })
        this.fetchDetail()
      },
      immediate: true
    }
  },
  methods: {
    async fetchDetail () {
      this.loading = true
      const { data } = await orderInfo({ order: this.orderid })
      this.tickets = Object.assign({}, data).dingzhikeyun_tickets.map((o, i) => {
        this.$set(o, 'fee', this.checkStartTime(this.order.start_time))
        // o['fee'] = this.checkStartTime(this.order.start_time)
        o['fee_amount'] = (Number(o.refundable_amount) * this.checkStartTime(this.order.start_time) * 0.01).toFixed(2)
        return o
      })
      this.reason = ''
      this.loading = false
      this.order = data
    },
    showLog (params) {
      this.ticket = params.id
      this.ticketTitle = `车票${params.ticket_no}退款记录`
      this.dialogLogs = true
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.refundable_amount == 0) {
        return 'dis-row'
      }
      return ''
    },
    subRowClassName ({ row, rowIndex }) {
      if (row.refund_status) {
        return 'dis-row'
      }
      return ''
    },

    selectable (row, index) {
      return row.refundable_amount > 0
    },
    subSelectable (row, index) {
      return row.is_pay === 1 && !row.refund_status
    },
    feeChange (_, index) {
      console.log(_)
      console.log(index)
      this.$set(this.tickets[index], 'fee_amount', (Number(this.tickets[index].refundable_amount) * Number(_) * 0.01).toFixed(2))

      this.$forceUpdate()
    },
    checkStartTime (time) {
      const baseSet = this.setting.REFUND_CONF.sort((a, b) => a.refund_ticket_time - b.refund_ticket_time)
      const time_less = moment(time).diff(
        moment(),
        'minutes'
      )
      let rate = 0
      for (let i = 0; i < baseSet.length; i++) {
        if (time_less < baseSet[i].refund_ticket_time) {
          rate = baseSet[i].refund_ticket_ratio
          break
        }
      }
      return 100 - rate > 0 ? 100 - rate : 0
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    handleSubChange (val) {
      this.subSelection = val
    },
    cancel () {
      this.$emit('close')
    },
    async handleRefund () {
      // multipleSelection: [],
      // subSelection: [],
      if (this.multipleSelection.length === 0 && this.subSelection.length === 0) {
        this.$message({
          message: '请选择需要退款的车票或订单',
          type: 'warning'
        })
        return
      }

      if (this.multipleSelection.length > 0 && this.multipleSelection.some((o) => Number(o.fee) === 0) && this.order.is_pay === 1) {
        this.$message({
          message: '退票比例需大于0',
          type: 'warning'
        })
        return
      }

      await this.$confirm('此操作不可恢复，确认为当前订单退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      if (this.subSelection && this.subSelection.length) {
        this.loading = true
        const res = await orderRefunds({
          rate: 100,
          reason: '后台取消订单',
          order: this.subSelection.map((o) => o.order_id).join(',')
        }).finally(() => {
          this.loading = false
        })
      }

      if (this.multipleSelection && this.multipleSelection.length) {
        this.loading = true
        const res_ = await ticketRefunds({
          ticket: this.multipleSelection.map((o) => o.id).join(',') || 0,
          fee: this.multipleSelection.map((o) => {
            const item_ = {}
            item_[o.id] = o.fee
            return item_
          }),
          reason: this.reason
        }).finally(() => {
          this.loading = false
        })
      }

      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.$emit('close')
      this.$emit('fresh')
    }
  }
}
</script>
<style lang="scss" scoped>
  .refund-gray {
    color: rgba(0, 0, 0, 0.65);
    font-size: 12px;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .table-p {
    margin: 0;
    padding: 0;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .badge-status {
    position: relative;
    top: -1px;
    display: inline-block;
    width: 5px;
    height: 5px;
    vertical-align: middle;
    border-radius: 50%;
    margin-right: 5px;
  }
  .reason-txt {
    font-size: 14px;
    line-height: 30px;
    margin-top: 10px;
  }

  .final-word {
    font-size: 14px;
    line-height: 22px;
    margin-top: 14px;

    .err {
      color: #ff4d4f;
    }
  }
  .temp_order {
    position: absolute;
    width: 16px;
    height: 16px;
    top: 3px;
    left: 3px;
    background-color: #0067e1;
    border-radius: 10px;
    color: #fff;
    display: block;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .item-description-title {
    position: relative;
    font-size: 15px;
    margin: 16px 0;

    &:before {
      width: 4px;
      border-radius: 5px;
      height: 60%;
      background: #0067e1;
      position: absolute;
      left: -10px;
      top: 0;
      bottom: 0;
      margin: auto;
      content: "";
      display: block;
    }
  }
</style>
<style lang="scss">
  .el-table .dis-row {
    background: #f4f4f5;
    cursor: not-allowed;
  }

  .refund-table th.el-table__cell {
    background-color: #f5f7fa;
  }

  .refund-table {
    border-radius: 8px;
  }

  // .el-table .success-row {
  //   background: #f0f9eb;
  // }
</style>

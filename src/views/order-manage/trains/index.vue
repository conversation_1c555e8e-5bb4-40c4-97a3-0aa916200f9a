<template>
  <div class="app-container">
    <el-radio-group v-model="listQuery.status" size="mini" @change="tabsChange">
      <el-radio-button v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }} {{ item.num
      }}
      </el-radio-button>
    </el-radio-group>

    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.order_no" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">订单编号</template>
        </el-input>

        <el-input
          v-model="listQuery.passenger_phone" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">乘客手机</template>
        </el-input>

        <BranchSelect
          v-model="listQuery.branch_id" placeholder="所属分台" class="filter-item"
          @change="handleFilter"
        />

        <el-select v-model="listQuery.sub_business_type" placeholder="全部子业态" clearable size="small" class="filter-item">
          <el-option v-for="item in subBusinessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-input
          v-model="listQuery.driver_search_value"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item driver-search-input"
          @keyup.enter.native="handleFilter"
        >
          <el-select
            slot="prepend"
            v-model="listQuery.driver_search_type"
            placeholder="选择类型"
            style="width: 120px"
          >
            <el-option label="司机ID" value="driver_id" />
            <el-option label="司机工号" value="job_number" />
            <el-option label="司机车牌号" value="car_tail_number" />
            <el-option label="司机手机号" value="cellphone" />
            <el-option label="司机昵称" value="name" />
          </el-select>
        </el-input>

        <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button
          :loading="downloadLoading" class="filter-item" type="primary" size="small"
          plain
          icon="el-icon-download" @click="handleDownload"
        >
          全部导出
        </el-button>
        <el-button
          class="filter-item" type="warning" size="small"
          plain
          icon="el-icon-refresh" @click="handleReset"
        >
          重置
        </el-button>
        <el-button
          :loading="selectedDownloadLoading" class="filter-item" type="primary" size="small"
          plain
          @click="exportSelectedOrders"
        >
          导出所选
        </el-button>
        <el-button class="filter-item" type="text" size="small" @click.stop="handleCollapse">
          {{ collapse === 'more' ? '收起' : '展开' }}<i
            :class="collapse === 'more' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          />
        </el-button>
      </div>

      <div v-show="collapse === 'more'" class="filter-more">
        <div class="filter-container">
          <el-select v-model="listQuery.is_paid" placeholder="是否支付" clearable size="small" class="filter-item">
            <el-option v-for="item in isPaidOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

          <el-input
            v-model="listQuery.ft_number" placeholder="请输入" clearable size="small"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          >
            <template slot="prepend">航班号</template>
          </el-input>

          <el-input
            v-model="listQuery.start_address_remark" placeholder="请输入" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">出发地址</template>
          </el-input>

          <el-input
            v-model="listQuery.end_address_remark" placeholder="请输入" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">目的地址</template>
          </el-input>

          <el-select v-model="listQuery.payway" placeholder="请选择付款方式" clearable size="small" class="filter-item">
            <template slot="prepend">付款方式</template>
            <el-option v-for="item in payWayOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

          <el-select
            v-model="listQuery.order_method" placeholder="请选择下单方式" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">下单方式</template>
            <el-option label="平台代约下单" value="proxy" />
            <el-option label="乘客自主下单" value="self-serving" />
          </el-select>

          <branch-select v-model="listQuery.temp_apply_branchid" placeholder="请选择代约分台" size="mini" class="filter-item" />

          <el-input
            v-model="listQuery.temp_apply_driver_name" placeholder="请输入" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">代约司机</template>
          </el-input>

          <el-input
            v-model="listQuery.temp_apply_account_name" placeholder="请输入" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">代约人</template>
          </el-input>

          <el-date-picker
            v-model="listQuery.create_time" type="datetimerange" size="small"
            class="filter-item date-condition" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
            start-placeholder="下单开始日期" end-placeholder="下单结束日期" unlink-panels :picker-options="pickerOptions"
          />

          <el-date-picker
            v-model="listQuery.start_time" type="datetimerange" size="small"
            class="filter-item date-condition" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
            start-placeholder="出发开始日期" end-placeholder="出发结束日期" :picker-options="pickerOptions"
          />

          <el-date-picker
            v-model="listQuery.time_complete" type="datetimerange" size="small"
            class="filter-item date-condition" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
            start-placeholder="完成开始日期" end-placeholder="完成结束日期" :picker-options="pickerOptions"
          />
        </div>
      </div>
    </div>

    <el-table
      ref="orderMultiple" v-loading="isLoading" class="order-table-view" stripe
      :data="list" fit
      highlight-current-row :height="`calc(100vh - ${leftheight})`" size="mini" @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单类型/编号" size="medium" align="left" width="120" fixed>
        <template slot-scope="scope">
          <sup v-if="scope.row.is_temp === 1" class="temp_order">代</sup>
          <div class="col-content">
            <el-tag v-if="scope.row.ch_id" size="mini" effect="plain" class="type-tag">
              摆渡车
            </el-tag>
            <el-tag v-if="scope.row.point_redeemable_amount > 0" size="mini" effect="plain" class="type-tag">
              积分抵扣
            </el-tag>
            {{ scope.row.order_no }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="出发时间" size="mini" align="left" show-overflow-tooltip
        width="160" prop="start_time"
        sortable="custom"
      >
        <template slot-scope="scope">
          <i class="el-icon-time" />
          {{ scope.row.start_time.slice(0, 11) || "/" }} {{ scope.row.start_time.slice(11, 16) || "/" }}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" size="mini" align="left" prop="reseverd_phone" width="120">
        <template slot-scope="scope">
          {{ (scope.row && scope.row.reseverd_phone) || "/" }}
          <el-popover
            v-if="scope.row.reseverd_info" class="item" trigger="click" placement="top-start"
            :content="(scope.row && scope.row.reseverd_info) || ''"
          >
            <p slot="reference">
              <el-button size="mini" type="text" class="el-icon-message" />
            </p>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="left" label="路线/上下车点" size="mini" width="280">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 4" class="table-p">
            {{ (scope.row && scope.row.agency && scope.row.agency.name) || '' }}
          </span>
          <div v-else class="route-column-content">
            <div class="route-preview" @click="openRouteDialog(scope.row)">
              <div class="route-preview-content">
                <span>{{ scope.row | filterStartCity }}</span>
                <span class="line-show-name">{{ scope.row | filterStartName }}</span>
                <i class="el-icon-right" />
                <span>{{ scope.row | filterEndCity }}</span>
                <span class="line-show-name">{{ scope.row | filterEndName }}</span>
              </div>
              <el-tooltip content="查看详细线路" placement="top">
                <i class="el-icon-map-location route-icon" />
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实票/总票/实付" align="left" size="mini" width="120">
        <template slot-scope="scope">
          <div>
            {{
              [3, 4].includes(scope.row.type)
                ? "-"
                : scope.row.book_seating
                  ? scope.row.book_seating + ""
                  : "0"
            }}
            /
            {{
              [3, 4].includes(scope.row.type)
                ? "-"
                : scope.row.total_booking_seats
                  ? scope.row.total_booking_seats + ""
                  : "0"
            }}
            /
            <span
              :class="scope.row.is_pay > 0 || scope.row.is_pre_pay > 0
                ? 'payed'
                : 'not-pay'
              "
            >￥{{ scope.row.real_price }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" size="mini" align="left" width="150">
        <template slot-scope="scope">
          <Tablestatus v-if="scope.row.state === 1 && scope.row.appoint == 1" mode="warning" word="待接单" />
          <Tablestatus v-else-if="scope.row.state === 2" mode="processing" word="已接单" />
          <Tablestatus v-else-if="scope.row.state === 3" mode="processing" word="在路上" />
          <Tablestatus v-else-if="scope.row.state === 4" mode="processing" word="在路上" />
          <Tablestatus v-else-if="scope.row.state === 5" mode="processing" word="已送达" />
          <Tablestatus v-else-if="scope.row.state === 6" mode="default" word="已完成" />
          <Tablestatus
            v-else-if="scope.row.state === 7" mode="default"
            :word="'已取消' + (scope.row.refund_status ? scope.row.refund_amount === scope.row.real_price ? '(全额退款)' : scope.row.refund_amount === '0.00' ? '(未退款)' : '(部分退款)' : '')"
          />
          <Tablestatus v-else mode="default" :word="scope.row.cn_state" />
        </template>
      </el-table-column>
      <el-table-column label="支付模式" size="mini" align="left" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.cn_pay_mode" :type="getPayModeTagType(scope.row.cn_pay_mode)" size="mini">
            {{ scope.row.cn_pay_mode }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="是否支付" size="mini" align="left" width="80">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.is_pay === 1 || scope.row.is_pre_pay === 1" size="mini" hit
            type="success"
          >已支付</el-tag>
          <el-tag v-else size="mini" hit type="info">未支付</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="付款方式" size="mini" align="left" width="140">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.cn_payway" :type="getPaywayTagType(scope.row.cn_payway)" size="mini">
            {{ scope.row.cn_payway }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="车票状态" size="mini" align="left" width="80">
        <template slot-scope="scope">
          <template v-if="scope.row.is_pay == 1">
            <el-popover
              v-if="scope.row.is_draw == 3" placement="top-start" title="退票原因" width="200"
              trigger="click"
              effect="dark" :content="scope.row.cn_refund_reason"
            >
              <span slot="reference" class="refund-gray">{{ scope.row.cn_ticket_status }} <i
                class="el-icon-question refund-gray cursor"
              /></span>
            </el-popover>
            <span v-else :class="[0, 1].includes(scope.row.is_draw) ? 'refund-gray' : 'payed'">{{
              scope.row.cn_ticket_status
            }}</span>
          </template>
          <span v-else>{{ scope.row.cn_ticket_status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="线下车票" size="mini" align="left" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.offline_ticket_issued" size="mini" type="success">已开具</el-tag>
          <el-tag v-else size="mini" type="info">未开具</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" size="mini" align="left" width="100">
        <template slot-scope="scope">
          <div
            v-if="scope.row.is_pay == 1 && scope.row.refund_status
            " class="column-flex"
          >
            <div class="status-line-top">
              <span class="refund-text">-￥{{ scope.row.refund_amount }}</span>
            </div>
            <div class="refund-gray">
              {{ scope.row.refund_status === 'success' ? '退款成功' : scope.row.refund_status === 'processing' ? '退款中' :
                '退款失败' }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- 航班号列 -->
      <el-table-column label="航班信息" size="mini" align="left" width="120">
        <template slot-scope="scope">
          <template v-if="scope.row.ft_number">
            <el-button
              type="text" size="mini" class="flight-number-btn"
              @click.stop="viewFlightInfo(scope.row.ft_number)"
            >
              <i class="el-icon-airplane" /> {{ scope.row.ft_number }}
            </el-button>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" size="mini" align="left" prop="create_time" sortable="custom" min-width="160" />
      <el-table-column label="派单司机" size="mini" align="left" show-overflow-tooltip min-width="150">
        <template slot-scope="scope">
          <template v-if="scope.row && scope.row.driver">
            <p class="table-p cursor-pointer" @click="showDriverPopover(scope.row)">
              <i class="el-icon-user driver-icon" />
              {{ (scope.row.driver && scope.row.driver.name) || "/" }} -
              {{ (scope.row.driver && scope.row.driver.cellphone) || "/" }}
            </p>
            <el-popover
              :visible.sync="scope.row.showDriverInfo" placement="top" width="250" trigger="manual"
              popper-class="driver-popover"
            >
              <div class="driver-info-container">
                <div class="driver-info-header">
                  <i class="el-icon-user-solid" />
                  <span>{{ (scope.row.driver && scope.row.driver.name) || "/" }}</span>
                </div>
                <div class="driver-info-content">
                  <div class="driver-info-item">
                    <i class="el-icon-mobile-phone" />
                    <span>{{ (scope.row.driver && scope.row.driver.cellphone) || "-" }}</span>
                  </div>
                  <div class="driver-info-item">
                    <i class="el-icon-truck" />
                    <span>{{ (scope.row.driver && scope.row.driver.car_tail_number) || "-" }}</span>
                  </div>
                  <div class="driver-info-actions">
                    <el-button
                      type="primary" size="mini" icon="el-icon-phone"
                      @click="callDriver(scope.row.driver)"
                    >联系</el-button>
                    <el-button
                      type="info" size="mini" icon="el-icon-message"
                      @click="messageDriver(scope.row.driver)"
                    >短信</el-button>
                  </div>
                </div>
              </div>
            </el-popover>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="所属分台" size="mini" align="left" show-overflow-tooltip width="160">
        <template slot-scope="scope">
          <div class="branch-info">
            <span>{{ (scope.row && scope.row.branch && scope.row.branch.mchname) || '' }} {{ (scope.row &&
              scope.row.branch && scope.row.branch.cellphone) || "-" }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="代约名称/渠道" size="mini" align="left" show-overflow-tooltip width="160">
        <template slot-scope="scope">
          <p class="table-p">
            <!-- {{ (scope.row && scope.row.temp_apply_branch && scope.row.temp_apply_branch.mchname) || "-" }}

            {{ (scope.row && scope.row.temp_apply_branch && scope.row.temp_apply_branch.cellphone) || "-" }} -->

            {{ scope.row.channel_name }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="支付通道" size="mini" align="left" prop="pay_config.mchnumber" min-width="100" />
      <el-table-column label="下单方式" size="mini" align="left" width="100">
        <template slot-scope="scope">
          {{ (scope.row && scope.row.is_temp === 1) ? "平台代约" : "自主下单" }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="left" label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="preView(scope.row.order_id)">详情</el-button>
          <el-divider v-if="scope.row.state === 1" direction="vertical" />
          <el-button
            v-if="scope.row.state === 1" type="text" size="mini"
            @click="handleDispatch(scope.row)"
          >指派</el-button>
          <template
            v-if="[1, 2, 6].includes(scope.row.state) || (scope.row.is_pay && [7, 8].includes(scope.row.state))"
          >
            <el-divider direction="vertical" />
            <el-dropdown size="mini" trigger="hover" @command="(command) => actionClose(command, scope.row)">
              <span class="el-dropdown-link">
                更多<i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">

                <el-dropdown-item v-if="[1, 2].includes(scope.row.state)" command="cancel">取消订单</el-dropdown-item>

                <el-dropdown-item command="check">{{ '电子客票检票' }}</el-dropdown-item>

                <el-dropdown-item
                  v-if="scope.row.is_pay && [1, 2, 7, 8, 6].includes(scope.row.state)"
                  command="refund"
                >{{
                  scope.row.refund_amount === scope.row.real_price ? '退款明细' : '手动退款' }}</el-dropdown-item>

                <el-dropdown-item v-if="!scope.row.offline_ticket_issued" command="offline_ticket">
                  开具纸质车票
                </el-dropdown-item>

                <!-- <el-dropdown-item disabled command="close">关闭订单</el-dropdown-item> -->
                <!-- <el-dropdown-item disabled command="finish">完成订单</el-dropdown-item> -->

              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0" layout="sizes, total, prev, pager, next" background
        :page-size="listQuery.per_page" :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        style="text-align: right" @current-change="handleCurrentChanges" @size-change="handleSizeChange"
      />
    </div>

    <el-dialog :visible.sync="dialogDes" title="订单详情" append-to-body width="950px">
      <Description :des-status="dialogDes" :order="orderDetailId" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="mini" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogRefound" :title="isReadOnly ? '退款明细' : '退票/退款，支持部分部分退款（输入退款比例）'" append-to-body
      width="1050px" class="refound-dialog"
    >
      <Refund
        :orderid="currentOrder" :readonly="isReadOnly" :dialog-status="dialogRefound" @fresh="fetchData(true)"
        @close="dialogRefound = false"
      />
    </el-dialog>

    <el-dialog :visible.sync="dialogTicket" title="电子客票检票" append-to-body width="1050px" class="refound-dialog">
      <Check
        :orderid="currentOrder" :readonly="isReadOnly" :dialog-status="dialogTicket" @fresh="fetchData(true)"
        @close="dialogTicket = false"
      />
    </el-dialog>

    <RouteDialog
      :visible.sync="routeDialogVisible" :start-city="routeDialogData.startCity"
      :start-name="routeDialogData.startName" :end-city="routeDialogData.endCity" :end-name="routeDialogData.endName"
      :start-address-remark="routeDialogData.startAddressRemark" :end-address-remark="routeDialogData.endAddressRemark"
      :order-no="routeDialogData.orderNo" :start-time="routeDialogData.startTime"
      :passenger-phone="routeDialogData.passengerPhone" :order-id="routeDialogData.orderId" @view-order="preView"
      @close="routeDialogVisible = false"
    />

    <!-- 航班信息弹窗 -->
    <FlightInfoDialog
      :visible.sync="flightDialogVisible" :flight-number="currentFlightNumber"
      @close="handleFlightDialogClose"
    />

  </div>
</template>

<script>
import {
  dispatchBanXianLines
} from '@/api/business'
import { getAdmindOrder, ticketRefunds, orderRefunds, exportExcels, markOfflineTicketIssued } from '@/api/order'

import Description from './components/Description'
import Refund from './components/Refund'
import Check from './components/Check'
import RouteDialog from './components/RouteDialog'
import FlightInfoDialog from './components/FlightInfoDialog'
import { formatStartName, formatEndName } from '@/utils/filters'
import moment from 'moment'
import { mapGetters } from 'vuex'
import BranchSelect from '@/components/BranchSelect'

export default {
  components: {
    Description,
    Refund,
    Check,
    RouteDialog,
    FlightInfoDialog,
    BranchSelect
  },
  filters: {
    filterStartCity(val) {
      return formatStartName(val).start_city
    },
    filterStartName(val) {
      return formatStartName(val).start_name
    },
    filterEndCity(val) {
      return formatEndName(val).end_city
    },
    filterEndName(val) {
      return formatEndName(val).end_name
    },
    filSname(val) {
      return formatStartName(val).start_name
    },
    filEname(val) {
      return formatEndName(val).end_name
    }
  },
  data() {
    return {
      typeIndeterminate: true,
      typeAll: false,
      isIndeterminate: false,
      checkedAll: false,
      selectedOrders: [], // 初始化为空数组
      pickerOptions: {
        shortcuts: [
          {
            text: '昨天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().subtract(1, 'days').startOf('day').valueOf()),
                new Date(moment().subtract(1, 'days').endOf('day').valueOf())
              ])
            }
          },
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().startOf('day').valueOf()),
                new Date(moment().endOf('day').valueOf())
              ])
            }
          },
          {
            text: '明天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().add(1, 'days').startOf('day').valueOf()),
                new Date(moment().add(1, 'days').endOf('day').valueOf())
              ])
            }
          }
        ]
      },
      lineClasses: [],
      collapse: '',
      dialogDes: false,
      dialogRefound: false,
      dialogTicket: false,
      currentOrder: '',
      orderDetailId: '',
      payWayOptions: [
        { value: 'wechat_jsapi', label: '线上支付-微信公众号' },
        { value: 'wechat_mini', label: '线上支付-微信小程序' },
        { value: 'offline', label: '线下支付' }
      ],
      typeOptions: [
        { value: 5, label: '定制客运', checked: true },
        { value: 1, label: '拼车' },
        { value: 2, label: '包车' },
        { value: 3, label: '带货' },
        { value: 4, label: '代办' },
        { value: 6, label: '顺风车' },
        { value: 7, label: '快车' },
        { value: 11, label: '出租车' },
        { value: 20, label: '摆渡车' },
        { value: 30, label: '代驾', disabled: true },
        { value: 40, label: '租车', disabled: true }
      ],
      statusOptions: [
        { value: 'total', label: '全部', num: 0 },
        { value: 'pending', label: '待指派', num: 0 },
        { value: 'dispatched', label: '已指派', num: 0 },
        { value: 'accepted', label: '已接单', num: 0 },
        { value: 'checked', label: '在路上', num: 0 },
        { value: 'arrived', label: '已送达', num: 0 },
        { value: 'completed', label: '已完成', num: 0 },
        { value: 'canceled', label: '已取消', num: 0 },
        { value: 'closed', label: '已关闭', num: 0 }
      ],
      list: null,
      isLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [5],
        status: 'total',
        sub_business_type: '',
        time_complete: '',
        temp_apply_branchid: '',
        temp_apply_driver_name: '',
        temp_apply_account_name: '',
        driver_search_type: 'cellphone',
        driver_search_value: ''
      },
      isPaidOptions: [
        { value: 'true', label: '已支付' },
        { value: 'false', label: '未支付' }
      ],
      isReadOnly: false, // 退款明细是否已读
      routeDialogVisible: false,
      routeDialogData: {},
      flightDialogVisible: false,
      currentFlightNumber: '',
      subBusinessTypeOptions: [
        { value: 1, label: '班线客运 [班]' },
        { value: 2, label: '定制客运 [班-定制]' },
        { value: 3, label: '城市公共交通运输 [公交]' },
        { value: 4, label: '旅游班线客运 [班-旅游]' },
        { value: 5, label: '包车客运[包]' },
        { value: 6, label: '城乡/农村客运[班-农村]' }
      ],
      downloadLoading: false,
      selectedDownloadLoading: false // 为"导出所选"添加独立的加载状态
    }
  },
  computed: {
    ...mapGetters(['info']),
    leftheight() {
      return this.collapse === 'more' ? '375px' : '325px'
    }
  },
  created() {
    console.log(this.$route)
    const { query } = this.$route
    if (query.orderNo) {
      this.listQuery.order_no = query.orderNo
      // 自动执行查询
      this.$nextTick(() => {
        this.handleFilter()
      })
    }
    this.fetchData(true)
    this.fetchBanXianLines()
  },
  methods: {
    getPaywayTagType(payway) {
      switch (payway) {
        case '线上支付-微信公众号':
          return 'success'
        case '线上支付-微信小程序':
          return 'warning'
        default:
          return 'default'
      }
    },
    getPayModeTagType(payMode) {
      switch (payMode) {
        case '先坐车后支付':
          return 'success' // 绿色标签
        case '先支付后坐车':
          return 'warning' // 黄色标签
        default:
          return 'default' // 默认灰色标签
      }
    },
    // 更新选择的订单
    handleSelectionChange(selection) {
      this.selectedOrders = selection.map(item => item.order_id)
    },

    // 导出所有符合条件的订单
    async handleDownload() {
      try {
        await this.$confirm('确认导出当前筛选条件下的所有订单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        // 用户取消操作
        return
      }

      // 设置 loading 状态
      this.downloadLoading = true
      try {
        // 调用API发起异步导出任务
        await exportExcels({
          ...this.listQuery,
          biz: 'normal'
        })

        // 使用 $msgbox 提示并提供跳转按钮
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往"导出任务管理"页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage() // Navigate on confirm
          }
        }).catch(() => {
          // Action on cancel or close, usually do nothing
        })
      } catch (error) {
        console.error('导出任务提交失败:', error)
        // 可以添加更详细的错误提示
        this.$message.error('导出任务提交失败，请稍后重试。')
      } finally {
        // 无论成功或失败，都要取消 loading 状态
        this.downloadLoading = false
      }
    },

    // 导出所选订单
    async exportSelectedOrders() {
      if (!this.selectedOrders.length) { // 仅检查是否有选中的订单
        this.$message.warning('请选择需要导出的订单。')
        return
      }

      const confirmationMessage = `确认导出已选中的 ${this.selectedOrders.length} 条订单吗？`

      try {
        await this.$confirm(confirmationMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        // 用户取消操作
        return
      }

      this.selectedDownloadLoading = true // 开始加载状态
      try {
        // 调用API发起异步导出任务
        await exportExcels({
          order_ids: this.selectedOrders,
          biz: 'normal'
        })

        // 使用 $msgbox 提示并提供跳转按钮
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往"导出任务管理"页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage() // Navigate on confirm
          }
        }).catch(() => {
          // Action on cancel or close, usually do nothing
        })

        this.$refs.orderMultiple.clearSelection() // 导出成功后清空选择
      } catch (error) {
        console.error('导出任务提交失败:', error)
        this.$message.error('导出任务提交失败，请稍后重试。')
      } finally {
        this.selectedDownloadLoading = false // 结束加载状态
      }
    },

    handleFilTable(e) {
      this.listQuery.is_paid = e
      this.handleFilter()
    },
    bussessChange(val, type) {
      if (val) {
        this.listQuery.type.push(type)
      } else {
        this.listQuery.type.splice(this.listQuery.type.indexOf(type), 1)
      }
      if (this.listQuery.type.length === 9) {
        this.typeAll = true
        this.typeIndeterminate = false
        this.listQuery.type = [3, 2, 1, 4, 5, 6, 7, 11, 20]
      } else if (this.listQuery.type.length === 0) {
        this.typeAll = false
        this.typeIndeterminate = false
      } else {
        this.typeAll = false
        this.typeIndeterminate = true
      }
      this.handleFilter()
    },

    typeChange(e) {
      if (e === 5) {
        this.collapse = 'more'
      }
    },
    tabsChange(e) {
      this.listQuery = {
        ...this.listQuery,
        page: 1,
        status: e,
        biz: 'normal'
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    async sortChange(e) {
      const { prop, order } = e
      this.listQuery['sort_by'] = order ? prop : null
      this.listQuery['sort'] =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.listQuery.page = 1
      this.fetchData(true)
    },
    async fetchBanXianLines() {
      const { data } = await dispatchBanXianLines({
        branch_id: this.listQuery.branch_id
      })
      if (data && data.length > 0) {
        this.lineClasses = data.map((o) => {
          return {
            label: `线路${o.id}：${o.start_name} → ${o.end_name}`,
            value: o.id
          }
        })
      } else {
        this.lineClasses = []
      }
    },
    async fetchData(loading = true) {
      if (loading) this.isLoading = true
      const query = Object.assign({}, this.listQuery)

      // 处理司机搜索参数
      if (query.driver_search_value && query.driver_search_type) {
        switch (query.driver_search_type) {
          case 'driver_id':
            query.driver_id = query.driver_search_value
            break
          case 'job_number':
            query.driver_job_number = query.driver_search_value
            break
          case 'car_tail_number':
            query.driver_car_tail_number = query.driver_search_value
            break
          case 'cellphone':
            query.driver_cellphone = query.driver_search_value
            break
          case 'name':
            query.driver_name = query.driver_search_value
            break
        }
      }

      // 删除自定义搜索字段，避免发送给后端
      delete query.driver_search_type
      delete query.driver_search_value

      // 检查 time_complete 是否为有效数组
      if (Array.isArray(query.time_complete) && query.time_complete.length === 2) {
        query.time_complete_start = query.time_complete[0]
        query.time_complete_end = query.time_complete[1]
      } else {
        // 如果不是有效数组，则不传递或传递空值，根据后端接口要求调整
        query.time_complete_start = undefined
        query.time_complete_end = undefined
      }
      // 删除原始的 time_complete 属性，避免发送给后端
      delete query.time_complete

      const { data } = await getAdmindOrder(query)
      if (loading) this.isLoading = false
      this.list = data.data || []
      this.total = data.meta.pagination.total
      // { value: 'total', label: '全部' ,num:0 }
      const meta = data.meta
      this.statusOptions = this.statusOptions.map((item) => {
        for (const o in meta) {
          if (item.value === o) {
            item.num = meta[o]
          }
        }
        return item
      })
    },
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [5],
        status: 'total',
        sub_business_type: '',
        driver_search_type: 'cellphone',
        driver_search_value: ''
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    openRouteDialog(order) {
      const startInfo = formatStartName(order)
      const endInfo = formatEndName(order)

      this.routeDialogData = {
        startCity: startInfo.start_city || order.start_city,
        startName: startInfo.start_name || order.start_name,
        endCity: endInfo.end_city || order.end_city,
        endName: endInfo.end_name || order.end_name,
        startAddressRemark: order.start_address_remark,
        endAddressRemark: order.end_address_remark,
        orderNo: order.order_no,
        startTime: order.start_time,
        passengerPhone: order.reseverd_phone,
        orderId: order.order_id
      }
      this.routeDialogVisible = true
    },
    showDriverPopover(row) {
      this.$set(row, 'showDriverInfo', !row.showDriverInfo)
    },
    callDriver(driver) {
      // 实现呼叫司机的逻辑
      console.log('呼叫司机', driver)
    },
    messageDriver(driver) {
      // 实现发送消息给司机的逻辑
      console.log('发送消息给司机', driver)
    },
    viewFlightInfo(flightNumber) {
      this.currentFlightNumber = flightNumber
      this.flightDialogVisible = true
    },
    handleCollapse() {
      this.collapse = this.collapse === 'more' ? '' : 'more'
    },
    handleFlightDialogClose() {
      this.flightDialogVisible = false
    },
    preView(id) {
      this.orderDetailId = id.toString()
      this.dialogDes = true
    },
    actionClose(_, order) {
      switch (_) {
        case 'refund':
          this.currentOrder = order.order_id
          this.$nextTick(() => {
            // row.refund_amount=== scope.row.real_price
            this.isReadOnly = order.refund_amount === order.real_price && order.refund_amount !== '0.00'
            this.dialogRefound = true
          })
          break
        case 'check':
          this.currentOrder = order.order_id
          this.$nextTick(() => {
            this.isReadOnly = false
            this.dialogTicket = true
          })
          break
        case 'offline_ticket':
          if (order.offline_ticket_issued) {
            this.$message({
              message: '该订单已标记为已开具纸质车票',
              type: 'info'
            })
            return
          }
          this.$confirm(
            '确认已开具纸质车票？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(async () => {
            try {
              this.loading = true
              await markOfflineTicketIssued(order.order_id)
              this.loading = false
              this.$notify({
                message: '标记成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            } catch (error) {
              this.loading = false
              this.$notify({
                message: '标记失败',
                type: 'error',
                duration: 2000
              })
            }
          })
          break
        default:
          this.$confirm(
            `确认取消当前订单？${order.is_pay ? '订单已支付金额将会全部原路退回，' : ''}取消后无法恢复，请谨慎操作`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(async () => {
            this.loading = true
            await ticketRefunds({
              ticket: order.dingzhikeyun_tickets.map((o) => o.id).join(',') || 0,
              fee: order.dingzhikeyun_tickets.map((o) => {
                const item_ = {}
                item_[o.id] = 100
                return item_
              }),
              reason: '后台取消订单'
            })
            if (order.ferry_order && order.ferry_order.length) {
              await orderRefunds({
                rate: 100,
                reason: '后台取消订单',
                order: order.ferry_order.map((o) => o.order_id).join(',')
              })
            }
            this.loading = false
            this.$notify({
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
            this.fetchData()
          })
      }
    },
    // 处理订单指派跳转
    handleDispatch(row) {
      // 将订单号存入localStorage而不是query参数
      localStorage.setItem('dispatchOrderNo', row.order_no)
      // 将订单出发时间存入localStorage
      localStorage.setItem('dispatchOrderStartTime', row.start_time)
      // 不带orderNo参数跳转
      this.$router.push({ path: '/operationCenter/dispatching' })
    },
    goToExportTasksPage() {
      this.$router.push('/resourceManage/exportTask') // 请确认此路径是否正确
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  margin: 0 12px;
  background: #fff;
  border-radius: 4px;
}

.el-radio-group {
  margin-bottom: 16px;
  background: #fff;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .el-radio-button__inner {
    padding: 8px 15px;
    font-size: 13px;
  }

  .el-radio-button:first-child .el-radio-button__inner {
    background-color: #409EFF;
    border-color: #409EFF;
    color: #fff;
    box-shadow: none;
  }
}

.handle-container {
  background: #fff;
  padding: 5px 15px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    margin: 0;
    flex-shrink: 0;

    &.mini-condition {
      width: 180px;
    }

    &.date-condition {
      width: 320px;
    }

    &.el-button--text {
      padding: 7px 0;
      margin-left: 8px;
    }

    &.el-button--primary {
      padding: 7px 15px;
    }
  }
}

.filter-more {
  margin-top: 5px;
  border-top: 1px solid #ebeef5;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;

    .filter-item {
      margin: 0;

      &.el-input,
      &.el-select {
        width: 220px;
      }

      &.date-condition {
        width: 320px;
      }
    }
  }
}

.el-input-group__prepend {
  padding: 0 12px;
  background-color: #f5f7fa;
  color: #606266;
  min-width: 70px;
  text-align: center;
  font-size: 13px;
}

.el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-button--small {
  padding: 8px 15px;
}

.el-select {
  width: 180px;
}

.el-input {
  width: 220px;
}

.driver-search-input {
  width: 280px !important;
}

.el-date-editor.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 320px;
}

.temp_order {
  position: absolute;
  width: 12px;
  height: 12px;
  top: 10px;
  left: 3px;
  background-color: #0067e1;
  border-radius: 10px;
  color: #fff;
  display: block;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-header {
  cursor: pointer;
}

.custom-link {
  font-weight: normal;
  font-size: 13px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}

.payed {
  color: #67c23a;
  font-size: 12px;
}

.not-pay {
  color: #e6a23c;
}

.refund-gray {
  color: rgba(0, 0, 0, 0.65);
}

.line-show-name {
  background-color: #409EFF;
  color: #fff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 12px;
  transform: scale(0.9);
  margin-left: 2px;
}

.badge-status {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}

.line-show-warp {
  display: flex;
  align-items: center;
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-left {
  text-align: left;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fff;
  border-radius: 4px;
  margin-top: 16px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.route-column-content {
  width: 100%;
}

.route-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #ecf5ff;
  }
}

.route-preview-content {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.route-icon {
  color: #409EFF;
  font-size: 16px;
  margin-left: 8px;
}

.driver-icon {
  color: #409EFF;
  margin-right: 5px;
}

.driver-more-btn {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.driver-more-btn i {
  margin-right: 4px;
}

.driver-info-container {
  padding: 8px;
}

.driver-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
  font-weight: 500;
}

.driver-info-header i {
  color: #409EFF;
  margin-right: 8px;
  font-size: 16px;
}

.driver-info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.driver-info-item {
  display: flex;
  align-items: center;
}

.driver-info-item i {
  color: #909399;
  margin-right: 8px;
  width: 14px;
}

.driver-info-label {
  color: #606266;
  margin-right: 5px;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;

  &:hover {
    color: #409EFF;
  }
}

.branch-info {
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 20px;
  }
}
</style>

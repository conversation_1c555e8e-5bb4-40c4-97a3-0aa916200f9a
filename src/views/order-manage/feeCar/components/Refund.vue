<template>
  <div v-loading="loading" class="refound-ticket">
    <el-table
      :data="tickets"
      element-loading-text=""
      fit
      highlight-current-row
      size="small"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column label="票号" width="155" prop="ticket_no" />
      <el-table-column label="乘客" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <p class="table-p">{{ scope.row?.passenger_name || "-" }}</p>
          <p class="table-p">
            {{ scope.row?.passenger_id_no || "-" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="座位" width="50" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row?.seat_label || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="车票状态" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <Tablestatus
            v-if="scope.row.status === 'success'"
            mode="success"
            :word="scope.row.cn_status"
          />
          <Tablestatus
            v-else-if="scope.row.status === 'checked'"
            mode="warning"
            :word="scope.row.cn_status"
          />
          <Tablestatus
            v-else-if="scope.row.status === 'refunded'"
            mode="default"
            :word="scope.row.cn_status"
          />
          <Tablestatus v-else mode="processing" word="出票中" />
        </template>
      </el-table-column>
      <el-table-column label="车票金额" width="80" prop="actual_amount" />
      <el-table-column label="已退金额" width="135">
        <template slot-scope="scope">
          <template v-if="order.is_pay===1">
            {{ scope.row?.refunded_amount || "0" }}/
            <el-tag type="info" size="mini" effect="plain" style="transform: scale(0.9);"> {{
            scope.row?.refunded_amount_rate || "0"
            }}%</el-tag>

          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="可退金额" width="135" show-overflow-tooltip>
        <template slot-scope="scope">
          <template v-if="order.is_pay===1">
            {{ scope.row?.refundable_amount || "0" }}/
            <el-tag :type="scope.row?.refundable_amount_rate===0?'info':'success'" size="mini" effect="plain" style="transform: scale(0.9);"> {{
            scope.row?.refundable_amount_rate || "0"
            }}%</el-tag>
          </template>
          <span v-else>-</span>

        </template>
      </el-table-column>
      <el-table-column label="修改退款金额/比例">
        <template slot-scope="scope">

          <template v-if="order.is_pay===1">
            {{ scope.row?.fee_amount || "0" }} /
            <template v-if="Number(scope.row.refundable_amount) > 0">
              <el-input
                v-model="tickets[scope.$index].fee"
                style="width: 60px"
                size="mini"
                placeholder="比例"
                @input="(_) => feeChange(_, scope.$index)"
              />
              %
            </template>
            <span v-else>-</span>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <p
      v-if="multipleSelection && multipleSelection.length > 0"
      class="final-word"
    >
      共计退票 <span>{{ multipleSelection.length }}</span> 张，
      <span v-if="order.is_pay===1">
        退款金额合计<span class="err">{{ totalAmount }}</span>元
      </span>
      <span v-else class="err">订单未支付，无退款</span>
    </p>
    <p class="reason-txt">退票原因</p>
    <el-input
      v-model="reason"
      type="textarea"
      maxlength="40"
      style="width: 50%"
      :rows="2"
      placeholder="请输入"
    />
    <div class="dialog-footer">
      <el-button size="small" @click="cancel"> 取消 </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleRefund"
      >确认</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { ticketRefunds } from '@/api/order'
import moment from 'moment'
export default {
  props: {
    order: {
      type: Object,
      default: () => { }
    }
  },

  data() {
    return {
      multipleSelection: [],
      tickets: null,
      reason: '',
      totalAmount: 0,
      loading: false
    }
  },
  computed: {
    ...mapGetters(['setting'])
    // totalAmount() {
    //   let final = 0
    //   if (this.multipleSelection.length > 0) {
    //     final = this.multipleSelection.map((o) => o.fee_amount).reduce(
    //       (a, b) => Number(a) + Number(b)
    //     )
    //   }
    //   return final
    // },
  },
  watch: {
    multipleSelection: {
      handler(n) {
        if (n.length > 0) {
          this.totalAmount = n.map((o) => o.fee_amount).reduce(
            (a, b) => Number(a) + Number(b)
          )
        } else {
          this.totalAmount = 0
        }
      },
      immediate: true,
      deep: true
    },
    order: {
      handler(n) {
        if (n && n.dingzhikeyun_tickets) {
          this.tickets = Object.assign([], n).dingzhikeyun_tickets.map((o, i) => {
            this.$set(o, 'fee', this.checkStartTime(this.order.start_time))
            // o['fee'] = this.checkStartTime(this.order.start_time)
            o['fee_amount'] = (Number(o.refundable_amount) * this.checkStartTime(this.order.start_time) * 0.01).toFixed(2)
            return o
          })
          this.reason = ''
        }
      },
      immediate: true
    }
  },
  methods: {
    selectable(row, index) {
      return row.refundable_amount > 0
    },
    feeChange(_, index) {
      console.log(_)
      console.log(index)
      this.$set(this.tickets[index], 'fee_amount', (Number(this.tickets[index].refundable_amount) * Number(_) * 0.01).toFixed(2))

      this.$forceUpdate()
    },
    checkStartTime(time) {
      const baseSet = this.setting.REFUND_CONF.sort((a, b) => a.refund_ticket_time - b.refund_ticket_time)
      const time_less = moment(time).diff(
        moment(),
        'minutes'
      )
      let rate = 0
      for (let i = 0; i < baseSet.length; i++) {
        if (time_less < baseSet[i].refund_ticket_time) {
          rate = baseSet[i].refund_ticket_ratio
          break
        }
      }
      return 100 - rate > 0 ? 100 - rate : 0
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    cancel() {
      this.$emit('close')
    },
    async handleRefund() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: '请至少选择一张车票',
          type: 'warning'
        })
        return
      }
      if (this.multipleSelection.some((o) => Number(o.fee) === 0) && this.order.is_pay === 1) {
        this.$message({
          message: '退票比例需大于0',
          type: 'warning'
        })
        return
      }

      await this.$confirm('此操作不可恢复，确认为当前选择车票退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.loading = true
      await ticketRefunds({
        ticket: this.multipleSelection.map((o) => o.id).join(','),
        fee: this.multipleSelection.map((o) => {
          const item_ = {}
          item_[o.id] = o.fee
          return item_
        }),
        reason: this.reason
      }).finally(() => {
        this.loading = false
      })
      this.$notify({
        message: '操作成功',
        type: 'success',
        duration: 2000
      })
      this.$emit('close')
      this.$emit('fresh')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.reason-txt {
  font-size: 14px;
  line-height: 30px;
  margin-top: 10px;
}
.final-word {
  font-size: 14px;
  line-height: 30px;
  margin-top: 20px;
  .err {
    color: #ff4d4f;
  }
}
</style>

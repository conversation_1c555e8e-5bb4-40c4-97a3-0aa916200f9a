<template>
  <div v-loading="loading" class="description-warp">
    <el-skeleton v-if="!orderDetail" style="height: 400px" />
    <h6 class="item-description-title">基本信息</h6>
    <el-descriptions v-if="orderDetail" :column="2" size="small" border>
      <el-descriptions-item label="订单编号">{{ orderDetail.order_no }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        <el-tag size="mini" effect="plain">
          {{
            orderDetail.type === 7
              ? [1, 2].includes(orderDetail.ferry_type)
                ? "摆渡车"
                : "快车"
              : orderDetail.order_type_string
          }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="所属城市">
        {{ orderDetail | filSname }}
      </el-descriptions-item>
      <el-descriptions-item label="出发时间">{{ orderDetail.start_time }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        <Tablestatus mode="default" :word="orderDetail.cn_state" />
      </el-descriptions-item>
      <el-descriptions-item label="支付状态">
        <Tablestatus v-if="orderDetail.is_pay === 1 || orderDetail.is_pre_pay === 1" mode="success" word="已支付" />
        <Tablestatus v-else mode="warning" word="待支付" />
      </el-descriptions-item>
      <el-descriptions-item label="乘车人">{{ (orderDetail.passenger && orderDetail.passenger.name) || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人">{{ orderDetail.reseverd_phone }}
      </el-descriptions-item>
      <el-descriptions-item label="上车点">{{ orderDetail.start_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item label="下车点">{{ orderDetail.end_address_remark }}
      </el-descriptions-item>
      <el-descriptions-item label="订单总价">¥ {{ orderDetail.price }}元
      </el-descriptions-item>
      <el-descriptions-item label="优惠金额">¥ {{ orderDetail.coupon_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="实付金额">¥ {{ orderDetail.real_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="渠道所得">¥ {{ orderDetail.offer_channel_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="司机所得">¥ {{ orderDetail.offer_price }}元
      </el-descriptions-item>
      <el-descriptions-item label="平台对司机提成">{{ orderDetail.split*100 }} %
      </el-descriptions-item>
      <el-descriptions-item label="退款比例">{{ orderDetail.refund_ticket_ratio * 100 }} %
      </el-descriptions-item>
      <el-descriptions-item label="已退款金额">¥ {{ orderDetail.refund_amount }}元
      </el-descriptions-item>
      <el-descriptions-item label="退款手续费">¥ {{ orderDetail.refund_fee }}元
      </el-descriptions-item>
      <el-descriptions-item label="下单时间">{{ orderDetail.create_time }}
      </el-descriptions-item>
      <el-descriptions-item label="所属分台">{{ orderDetail.branch.mchname || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="渠道名称">{{ orderDetail.channel_branch_name || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="乘客留言">{{ orderDetail.reseverd_info || "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <br>
    <el-descriptions v-if="orderDetail && orderDetail.driver" title="司机信息" :column="2" size="small" border>
      <el-descriptions-item label="司机名称">{{ orderDetail.driver.name }}
      </el-descriptions-item>
      <el-descriptions-item label="司机电话">{{ orderDetail.driver.cellphone }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆品牌">{{ orderDetail.driver.car_brand }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌号码">
        <el-tag size="mini" type="info" effect="dark">
          {{ orderDetail.driver.car_tail_number }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { orderInfo } from '@/api/business'
import { formatStartName, formatEndName } from '@/utils/filters'

export default {
  filters: {
    filterStart (val) {
      return (
        formatStartName(val).start_city +
        ' | ' +
        formatStartName(val).start_name
      )
    },
    filterEnd (val) {
      return formatEndName(val).end_city + ' | ' + formatEndName(val).end_name
    },
    filSname (val) {
      return formatStartName(val).start_name
    },
    filEname (val) {
      return formatEndName(val).end_name
    }
  },
  props: {
    order: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      orderDetail: null,
      loading: false
    }
  },
  watch: {
    order: {
      handler (n) {
        if (n) {
          this.fetchDetail()
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchDetail () {
      this.loading = true
      const { data } = await orderInfo({ order: this.order })
      this.loading = false
      this.orderDetail = data
    }
  }
}
</script>
<style lang="scss">
  .description-warp {
    .el-descriptions--small.is-bordered .el-descriptions-item__cell {
      min-width: 80px;
    }
    .el-descriptions-item__content {
      min-width: 280px;
    }
  }
  .el-descriptions__header {
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .item-description-title {
    position: relative;
    font-size: 15px;
    margin: 16px 0;
    &:before {
      width: 4px;
      border-radius: 5px;
      height: 60%;
      background: #0067e1;
      position: absolute;
      left: -10px;
      top: 0;
      bottom: 0;
      margin: auto;
      content: "";
      display: block;
    }
  }
</style>

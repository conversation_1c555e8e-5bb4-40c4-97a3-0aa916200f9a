<template>
  <div>
    <Intercity :order-type="2" />
  </div>
</template>

<script>
import Intercity from '../components/Intercity'
export default {
  components: {
    Intercity
  }
}
</script>
<style lang="scss" scoped>
.temp_order {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 3px;
  left: 3px;
  background-color: #0067e1;
  border-radius: 10px;
  color: #fff;
  display: block;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-header {
  cursor: pointer;
}
.custom-link {
  font-weight: normal;
  font-size: 13px;
}
.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}
.surplus {
  color: #67c23a;
}

.nosurplus {
  color: #f56c6c;
}
.refund-gray {
  color: gray;
}
.line-show-name {
  background-color: #3399ff;
  color: #fff;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 12px;
  transform: scale(0.9);
}
.badge-status {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}
.line-show-warp {
  display: flex;
  align-items: center;
}
.item-filter {
  display: flex;
  align-items: center;
  line-height: 44px;
  .label-key {
    min-width: 66px;
    text-align: right;
    margin-right: 6px;
    font-weight: 500;
  }
}
.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table-left {
  text-align: left;
}
</style>
<style lang="scss">
.menu_check {
  background-color: rgb(230, 240, 252);
  color: rgb(51, 133, 231);
}
.collapse-filrter {
  margin-top: 15px;
  .filter-container {
    padding: 0;
  }
  .el-collapse {
    border: none;
  }
  .el-collapse-item__header {
    height: auto;
  }
}
.refound-dialog {
  .el-dialog__body {
    padding: 30px 0;
  }
}
.checkbox-type {
  margin-bottom: 15px;
  .el-checkbox {
    margin-right: 16px;
    .el-checkbox__label {
      padding-left: 5px;
    }
  }
}
.daterange-condition {
  width: 290px !important;
}
.el-icon-right {
  margin: 0 3px;
}
</style>

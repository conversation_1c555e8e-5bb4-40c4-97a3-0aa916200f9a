<template>
  <div class="app-container">
    <el-radio-group v-model="listQuery.status" size="mini" @change="tabsChange">
      <el-radio-button v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }}{{ item.num
      }}</el-radio-button>
    </el-radio-group>

    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.order_no" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">订单编号</template>
        </el-input>

        <el-input
          v-model="listQuery.passenger_phone" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">下单手机</template>
        </el-input>

        <BranchSelect
          v-model="listQuery.branch_id" size="small" placeholder="请选择" width="180px"
          class="filter-item"
          @change="handleFilter"
        />

        <el-select v-model="listQuery.is_paid" size="small" placeholder="请选择" clearable class="filter-item">
          <el-option v-for="item in isPaidOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click.stop="handleFilter">
          查询
        </el-button>
        <el-button :loading="downloadLoading" class="filter-item" type="primary" size="small" plain icon="el-icon-download" @click.stop="handleDownload">
          全部导出
        </el-button>
        <el-button class="filter-item" type="warning" size="small" plain icon="el-icon-refresh" @click.stop="handleReset">
          重置
        </el-button>
        <el-button :loading="selectedDownloadLoading" class="filter-item" type="primary" size="small" plain @click.stop="exportSelectedOrders">
          导出所选
        </el-button>
        <el-button class="filter-item" type="text" size="small" @click.stop="handleCollapse">
          {{ collapse === 'more' ? '收起' : '展开' }}<i
            :class="collapse === 'more' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          />
        </el-button>
      </div>

      <div v-show="collapse === 'more'" class="filter-more">
        <div class="filter-container">
          <el-input
            v-model="listQuery.driver_card_no" placeholder="请输入" clearable size="small"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          >
            <template slot="prepend">车牌号码</template>
          </el-input>

          <el-input
            v-model="listQuery.driver_phone" placeholder="请输入" clearable size="small"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          >
            <template slot="prepend">司机手机</template>
          </el-input>

          <el-date-picker
            v-model="listQuery.create_time" size="small" type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss" class="filter-item date-condition" range-separator="至"
            start-placeholder="下单开始日期" end-placeholder="下单结束日期" unlink-panels :picker-options="pickerOptions"
          />
        </div>
      </div>
    </div>

    <el-table
      ref="orderMultiple" v-loading="isLoading" stripe class="order-table"
      :data="list" fit
      highlight-current-row :height="`calc(100vh - ${leftheight})`" :cell-style="{ padding: '6px 0' }" size="small"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" fixed />
      <el-table-column label="订单编号" size="small" align="left" width="140" fixed>
        <template slot-scope="scope">
          <sup v-if="scope.row.temp_apply_branchid > 0" class="temp_order">代</sup>
          {{ scope.row.order_no }}
        </template>
      </el-table-column>
      <el-table-column
        label="取件时间" size="small" align="left" show-overflow-tooltip
        width="120" prop="start_time"
        sortable="custom"
      >
        <template slot-scope="scope">
          <p class="table-p fb">
            {{ scope.row.start_time.slice(11, 16) || "/" }}
          </p>
          <p class="table-p fb">
            {{ scope.row.start_time.slice(0, 11) || "/" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="收发件信息" align="left" width="230">
        <template slot-scope="scope">
          <p class="table-p">
            <span class="badge-status" style="background-color: #52c41a" />发件：{{ scope.row && scope.row.reseverd_person || "-" }}
            /
            {{ scope.row && scope.row.reseverd_phone || "-" }}
          </p>
          <p class="table-p cutmore">
            <i class="el-icon-location-information" />
            {{ scope.row.start_address_remark || "-" }}
          </p>
          <p class="table-p">
            <span class="badge-status" style="background-color: #ff4d4f" />收件：{{ scope.row && scope.row.delivery_person || "-" }}
            /
            {{ scope.row && scope.row.delivery_phone || "-" }}
          </p>
          <p class="table-p cutmore">
            <i class="el-icon-location-information" />
            {{ scope.row.end_address_remark || "-" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" size="small" align="left" prop="reseverd_phone" width="145">
        <template slot-scope="scope">
          <p class="table-p">
            {{ scope.row && scope.row.reseverd_phone || "/" }}
          </p>
          <el-popover
            v-if="scope.row && scope.row.reseverd_info" class="item" trigger="click" placement="top-start"
            :content="scope.row && scope.row.reseverd_info || ''"
          >
            <p slot="reference">
              <el-button size="mini" type="text">订单备注</el-button>
            </p>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="left" label="所属路线" size="small" width="280">
        <template slot-scope="scope">
          <div>
            {{ scope.row | filterStartCity
            }}<span class="line-show-name">{{
              scope.row | filterStartName
            }}</span>
            <i class="el-icon-right" />
            {{ scope.row | filterEndCity }}
            <span class="line-show-name">{{
              scope.row | filterEndName
            }}</span>
            <p class="table-p">线路ID：{{ scope.row.line_id || "/" }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="重量(KG)" align="left" size="small" width="90" prop="weight" />
      <el-table-column label="订单金额" align="left" size="small" width="120" prop="real_price" />
      <el-table-column label="订单状态" size="small" align="left" width="170">
        <template slot-scope="scope">
          <div class="column-flex">
            <div class="status-line-top">
              <el-tag
                v-if="scope.row.is_pay === 1 || scope.row.is_pre_pay === 1" size="mini" hit
                type="success"
              >已支付</el-tag>
              <el-tag v-else size="mini" hit type="warning">未支付</el-tag>
            </div>
            <Tablestatus v-if="scope.row.state === 1" mode="warning" word="待派单" />
            <Tablestatus v-else-if="scope.row.state === 2" mode="processing" word="已接单" />
            <Tablestatus v-else-if="scope.row.state === 3" mode="processing" word="已上车" />
            <Tablestatus v-else-if="scope.row.state === 4" mode="processing" word="在路上" />
            <Tablestatus v-else-if="scope.row.state === 5" mode="processing" word="已送达" />
            <Tablestatus v-else-if="scope.row.state === 6" mode="default" word="已完成" />
            <Tablestatus
              v-else-if="scope.row.state === 7" mode="default"
              :word="'已取消' + (scope.row.refund_status ? scope.row.refund_amount === scope.row.real_price ? '（全额退款）' : scope.row.refund_amount === '0.00' ? '（未退款）' : '（部分退款）' : '')"
            />
            <Tablestatus v-else mode="default" :word="scope.row.cn_state" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" size="small" align="left" width="130">
        <template slot-scope="scope">
          <div
            v-if="scope.row.is_pay == 1 && scope.row.refund_status
            " class="column-flex"
          >
            <div class="status-line-top">
              <span class="refund-text">-￥{{ scope.row.refund_amount }}</span>
            </div>
            <div class="refund-gray">
              {{ scope.row.refund_status === 'success' ? '退款成功' : scope.row.refund_status === 'processing' ? '退款中' :
                '退款失败' }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="线下车票" size="small" align="left" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.offline_ticket_issued" size="mini" type="success">已开具</el-tag>
          <el-tag v-else size="mini" type="info">未开具</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="派单司机" size="small" align="left" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <template v-if="scope.row && scope.row.driver">
            <p class="table-p">
              {{ scope.row.driver && scope.row.driver.name || "/" }}
              {{ scope.row.driver && scope.row.driver.cellphone || "/" }}
            </p>
            <p class="table-p">
              <el-tag type="info" size="mini" style="transform: scale(0.9); margin-left: 0" effect="plain">{{
                scope.row.driver && scope.row.driver.car_tail_number || "/" }}</el-tag>
            </p>
            <p class="table-p">
              {{
                scope.row.dispatched_at
                  ? "派单时间：" + scope.row.dispatched_at
                  : ""
              }}
            </p>
            <p class="table-p">
              {{
                scope.row.driver && scope.row.driver.driver_group_name
                  ? "司机归属：" + (scope.row.driver && scope.row.driver.driver_group_name)
                  : ""
              }}
            </p>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" size="small" align="left" prop="create_time" sortable="custom" min-width="160" />
      <el-table-column label="支付通道" size="small" align="left" prop="pay_config.mchnumber" min-width="160" />
      <el-table-column label="所属分台" size="small" align="left" width="140">
        <template slot-scope="scope">
          <p class="table-p">{{ scope.row && scope.row.branch && scope.row.branch.mchname || '' }}</p>
          <p class="table-p">{{ scope.row && scope.row.branch && scope.row.branch.cellphone || "-" }}</p>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="left" label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="preView(scope.row.order_id)">详情</el-button>
          <el-divider direction="vertical" />
          <el-button
            v-if="scope.row.state === 1" type="text" size="small"
            @click="handleDispatch(scope.row)"
          >指派</el-button>
          <el-divider v-if="scope.row.state === 1" direction="vertical" />
          <el-dropdown size="mini" trigger="hover" @command="(command) => actionClose(command, scope.row)">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item disabled command="cancel">取消订单</el-dropdown-item>
              <el-dropdown-item disabled command="close">关闭订单</el-dropdown-item>
              <el-dropdown-item disabled command="finish">完成订单</el-dropdown-item>
              <el-dropdown-item command="logs">派单记录</el-dropdown-item>
              <el-dropdown-item v-if="!scope.row.offline_ticket_issued" command="offline_ticket">
                开具纸质车票
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0" layout="sizes, total, prev, pager, next" background
        :page-size="listQuery.per_page" :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        style="text-align: right" @current-change="handleCurrentChanges" @size-change="handleSizeChange"
      />
    </div>
    <el-dialog :visible.sync="dialogDes" title="订单详情" append-to-body width="850px">
      <Description :order="orderDetailId" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="small" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog :visible.sync="dialogLogs" title="派单记录" append-to-body width="800px" class="refound-dialog">
      <Sendlogs :logs="currentLogs" @close="dialogLogs = false" />
    </el-dialog>
  </div>
</template>

<script>
import { closeOrders, dispatchBanXianLines } from '@/api/business'
import { getAdmindOrder, markOfflineTicketIssued, exportExcels } from '@/api/order'
import Description from './components/Description'
import Sendlogs from '@/components/Sendlogs'
import { formatStartName, formatEndName } from '@/utils/filters'
import moment from 'moment'
import { mapGetters } from 'vuex'
import BranchSelect from '@/components/BranchSelect'

export default {
  components: {
    Description,
    Sendlogs,
    BranchSelect
  },
  filters: {
    filterStartCity(val) {
      return formatStartName(val).start_city + ' | '
    },
    filterStartName(val) {
      return formatStartName(val).start_name
    },
    filterEndCity(val) {
      return formatEndName(val).end_city + ' | '
    },
    filterEndName(val) {
      return formatEndName(val).end_name
    },
    filSname(val) {
      return formatStartName(val).start_name
    },
    filEname(val) {
      return formatEndName(val).end_name
    }
  },
  data() {
    return {
      dialogLogs: false,
      currentLogs: [], // 派单记录
      typeIndeterminate: true,
      typeAll: false,
      downloadLoading: false,
      selectedDownloadLoading: false,
      multipleSelection: [],
      isIndeterminate: false,
      checkedAll: false,

      pickerOptions: {
        shortcuts: [
          {
            text: '昨天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().subtract(1, 'days').startOf('day').valueOf()),
                new Date(moment().subtract(1, 'days').endOf('day').valueOf())
              ])
            }
          },
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().startOf('day').valueOf()),
                new Date(moment().endOf('day').valueOf())
              ])
            }
          },
          {
            text: '明天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().add(1, 'days').startOf('day').valueOf()),
                new Date(moment().add(1, 'days').endOf('day').valueOf())
              ])
            }
          }
        ]
      },
      lineClasses: [],
      collapse: '',
      dialogDes: false,
      dialogRefound: false,
      currentOrder: {},
      orderDetailId: '',
      statusOptions: [
        { value: 'total', label: '全部', num: 0 },
        { value: 'pending', label: '待指派', num: 0 },
        { value: 'dispatched', label: '已指派', num: 0 },
        { value: 'accepted', label: '已接单', num: 0 },
        { value: 'checked', label: '已取货', num: 0 },
        { value: 'completed', label: '已完成', num: 0 },
        { value: 'canceled', label: '已取消', num: 0 },
        { value: 'closed', label: '已关闭', num: 0 }
      ],
      list: null,
      isLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [3],
        status: 'total'
      },
      isPaidOptions: [
        { value: 'true', label: '已支付' },
        { value: 'false', label: '未支付' }
      ]
    }
  },
  computed: {
    ...mapGetters(['info']),
    leftheight() {
      return this.collapse === 'more' ? '355px' : '305px'
    }
  },
  created() {
    this.fetchData(true)
    this.fetchBanXianLines()
  },
  methods: {
    handleFilTable(e) {
      this.listQuery.is_paid = e
      this.handleFilter()
    },
    typeChange(e) {
      if (e === 5) {
        this.collapse = 'more'
      }
    },
    tabsChange(e) {
      this.listQuery = {
        ...this.listQuery,
        page: 1,
        status: e,
        biz: 'normal'
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    async fetchBanXianLines() {
      const { data } = await dispatchBanXianLines({
        branch_id: this.listQuery.branch_id
      })
      if (data && data.length > 0) {
        this.lineClasses = data.map((o) => {
          return {
            label: `线路${o.id}：${o.start_name} → ${o.end_name}`,
            value: o.id
          }
        })
      } else {
        this.lineClasses = []
      }
      this.fetchData()
    },
    async sortChange(e) {
      const { prop, order } = e
      this.listQuery['sort_by'] = order ? prop : null
      this.listQuery['sort'] =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.listQuery.page = 1
      this.fetchData(true)
    },
    preView(id) {
      this.orderDetailId = id.toString()
      this.dialogDes = true
    },
    actionClose(_, order) {
      switch (_) {
        case 'refund':
          this.currentOrder = order
          console.log(order)
          this.$nextTick(() => {
            this.dialogRefound = true
          })
          break
        case 'logs':
          this.currentLogs = order.virtual_driver || []
          this.$nextTick(() => {
            this.dialogLogs = true
          })
          break
        case 'offline_ticket':
          if (order.offline_ticket_issued) {
            this.$message({
              message: '该订单已标记为已开具纸质车票',
              type: 'info'
            })
            return
          }
          this.$confirm(
            '确认已开具纸质车票？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(async () => {
            try {
              this.loading = true
              await markOfflineTicketIssued(order.order_id)
              this.loading = false
              this.$notify({
                message: '标记成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            } catch (error) {
              this.loading = false
              this.$notify({
                message: '标记失败',
                type: 'error',
                duration: 2000
              })
            }
          })
          break
        default:
          this.$confirm(
            `确认关闭当前订单？关闭后无法恢复，请谨慎操作`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            closeOrders({
              order: order.order_id
            }).then(() => {
              this.$notify({
                message: '操作成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            })
          })
      }
    },

    async fetchData(loading = true) {
      if (loading) this.isLoading = true
      const query = Object.assign({}, this.listQuery)
      const { data } = await getAdmindOrder(query)
      if (loading) this.isLoading = false
      this.list = data.data || []
      this.total = data.meta.pagination.total
      // { value: 'total', label: '全部' ,num:0 }
      const meta = data.meta
      this.statusOptions = this.statusOptions.map((item) => {
        for (const o in meta) {
          if (item.value === o) {
            item.num = meta[o]
          }
        }
        return item
      })
    },

    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [3],
        status: 'total'
      }
      if (this.$refs.orderMultiple) { // 确保表格已渲染
        this.$refs.orderMultiple.clearSort()
      }
      this.fetchData()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    handleDispatch(row) {
      localStorage.setItem('dispatchOrderNo', row.order_no)
      localStorage.setItem('dispatchOrderStartTime', row.start_time)
      this.$router.push({ path: '/operationCenter/dispatching' })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async handleDownload() {
      try {
        await this.$confirm('确认导出当前筛选条件下的所有订单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return // 用户取消操作
      }

      this.downloadLoading = true
      try {
        const params = { ...this.listQuery }
        delete params.page
        delete params.per_page
        params.export = 'all'
        params.biz = 'normal' // 确认 biz 类型

        await exportExcels(params)
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往 \"导出任务管理\" 页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage()
          }
        }).catch(() => {}) // 用户点击遮罩层或按下 ESC 关闭 msgbox
      } catch (error) {
        console.error('导出任务提交失败:', error)
        let errorMsg = '导出任务提交失败，请稍后重试。'
        if (error && error.response && error.response.data && error.response.data.message) {
          errorMsg = error.response.data.message
        }
        this.$message.error(errorMsg)
      } finally {
        this.downloadLoading = false
      }
    },

    // 导出所选订单
    async exportSelectedOrders() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择需要导出的订单')
        return
      }
      const orderIds = this.multipleSelection.map(item => item.order_id)
      const confirmationMessage = `确认导出已选中的 ${orderIds.length} 条订单吗？`

      try {
        await this.$confirm(confirmationMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return // 用户取消操作
      }

      this.selectedDownloadLoading = true
      try {
        const params = { export: 'selected', order_ids: orderIds, biz: 'normal' } // 确认 biz 类型

        await exportExcels(params)
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往 \"导出任务管理\" 页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage()
          }
        }).catch(() => {}) // 用户点击遮罩层或按下 ESC 关闭 msgbox
        if (this.$refs.orderMultiple) { // 确保表格已渲染
          this.$refs.orderMultiple.clearSelection() // 清空选择
        }
      } catch (error) {
        console.error('导出所选订单任务提交失败:', error)
        let errorMsg = '导出所选订单任务提交失败，请稍后重试。'
        if (error && error.response && error.response.data && error.response.data.message) {
          errorMsg = error.response.data.message
        }
        this.$message.error(errorMsg)
      } finally {
        this.selectedDownloadLoading = false
      }
    },
    // 跳转到导出任务管理页面
    goToExportTasksPage() {
      // 确保路径与 Intercity.vue 中的一致或根据项目实际情况调整
      this.$router.push('/resourceManage/exportTask')
    },
    handleCollapse() {
      this.collapse = this.collapse === 'more' ? '' : 'more'
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-header {
  cursor: pointer;
}

.custom-link {
  font-weight: normal;
  font-size: 13px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}

.surplus {
  color: #67c23a;
}

.nosurplus {
  color: #f56c6c;
}

.refund-gray {
  color: gray;
}

.line-show-name {
  background-color: #0067e1;
  color: #fff;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 12px;
  transform: scale(0.9);
}

.badge-status {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}

.line-show-warp {
  display: flex;
  align-items: center;
}

.item-filter {
  display: flex;
  align-items: center;
  line-height: 44px;

  .label-key {
    min-width: 66px;
    text-align: right;
    margin-right: 6px;
    font-weight: 500;
  }
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-left {
  text-align: left;
}
</style>
<style lang="scss">
.menu_check {
  background-color: rgb(230, 240, 252);
  color: rgb(51, 133, 231);
}

.app-container {
  margin: 0 12px;
  background: #fff;
  border-radius: 4px;
  min-height: 100vh;
}

.el-radio-group {
  margin-bottom: 16px;
  background: #fff;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .el-radio-button__inner {
    padding: 8px 15px;
    font-size: 13px;
  }

  .el-radio-button:first-child .el-radio-button__inner {
    background-color: #409EFF;
    border-color: #409EFF;
    color: #fff;
    box-shadow: none;
  }
}

.handle-container {
  background: #fff;
  padding: 5px 15px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    margin: 0;
    flex-shrink: 0;

    &.mini-condition {
      width: 180px;
    }

    &.date-condition {
      width: 320px;
    }

    &.el-button--text {
      padding: 7px 0;
      margin-left: 8px;
    }

    &.el-button--primary {
      padding: 7px 15px;
    }
  }
}

.filter-more {
  margin-top: 5px;
  border-top: 1px solid #ebeef5;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;

    .filter-item {
      margin: 0;

      &.el-input,
      &.el-select {
        width: 220px;
      }

      &.date-condition {
        width: 320px;
      }
    }
  }
}

.refound-dialog {
  .el-dialog__body {
    padding: 30px 0;
  }
}

.checkbox-type {
  margin-bottom: 15px;

  .el-checkbox {
    margin-right: 16px;

    .el-checkbox__label {
      padding-left: 5px;
    }
  }
}

.el-input-group__prepend {
  padding: 0 12px;
  background-color: #f5f7fa;
  color: #606266;
  min-width: 70px;
  text-align: center;
  font-size: 13px;
}

.el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-button--small {
  padding: 8px 15px;
}

.el-select {
  width: 180px;
}

.el-input {
  width: 220px;
}

.el-date-editor.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 320px;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fff;
  border-radius: 4px;
  margin-top: 16px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.daterange-condition {
  width: 290px !important;
}

.el-icon-right {
  margin: 0 3px;
}
.temp_order {
  position: absolute;
  width: 12px;
  height: 12px;
  top: 10px;
  left: 3px;
  background-color: #0067e1;
  border-radius: 10px;
  color: #fff;
  display: block;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>

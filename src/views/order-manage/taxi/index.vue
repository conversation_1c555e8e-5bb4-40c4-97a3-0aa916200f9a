<template>
  <div class="app-container">
    <el-radio-group v-model="listQuery.status" size="mini" @change="tabsChange">
      <el-radio-button v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }} {{ item.num
      }}</el-radio-button>
    </el-radio-group>

    <div class="handle-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.order_no" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">订单编号</template>
        </el-input>

        <el-input
          v-model="listQuery.passenger_phone" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">乘客手机</template>
        </el-input>

        <BranchSelect
          v-model="listQuery.branch_id" placeholder="请选择" width="180px" class="filter-item"
          @change="handleFilter"
        />

        <el-select v-model="listQuery.is_paid" placeholder="请选择" clearable size="small" class="filter-item">
          <template slot="prepend">支付状态</template>
          <el-option v-for="item in isPaidOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-input
          v-model="listQuery.passenger_name" placeholder="请输入" clearable size="small"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        >
          <template slot="prepend">乘客昵称</template>
        </el-input>

        <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button
          :loading="downloadLoading" class="filter-item" type="primary" size="small"
          plain
          icon="el-icon-download" @click="handleDownload"
        >
          全部导出
        </el-button>
        <el-button class="filter-item" type="warning" size="small" plain icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button :loading="selectedDownloadLoading" class="filter-item" type="primary" size="small" plain @click="exportSelectedOrders">
          导出所选
        </el-button>
        <el-button class="filter-item" type="text" size="small" @click.stop="handleCollapse">
          {{ collapse === 'more' ? '收起' : '展开' }}<i
            :class="collapse === 'more' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          />
        </el-button>
      </div>

      <div v-show="collapse === 'more'" class="filter-more">
        <div class="filter-container">
          <el-input
            v-model="listQuery.start_address_remark" placeholder="请输入" clearable size="small"
            class="filter-item"
          >
            <template slot="prepend">出发地址</template>
          </el-input>

          <el-input v-model="listQuery.end_address_remark" placeholder="请输入" clearable size="small" class="filter-item">
            <template slot="prepend">目的地址</template>
          </el-input>

          <el-input v-model="listQuery.driver_card_no" placeholder="请输入" clearable size="small" class="filter-item">
            <template slot="prepend">车牌号码</template>
          </el-input>

          <el-input v-model="listQuery.driver_name" placeholder="请输入" clearable size="small" class="filter-item">
            <template slot="prepend">司机昵称</template>
          </el-input>

          <el-input v-model="listQuery.driver_phone" placeholder="请输入" clearable size="small" class="filter-item">
            <template slot="prepend">司机手机</template>
          </el-input>
        </div>

        <div class="filter-container">
          <el-date-picker
            v-model="listQuery.create_time" type="datetimerange" size="small"
            class="filter-item date-condition" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
            start-placeholder="下单开始日期" end-placeholder="下单结束日期" unlink-panels :picker-options="pickerOptions"
          />

          <el-date-picker
            v-model="listQuery.start_time" type="datetimerange" size="small"
            class="filter-item date-condition" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
            start-placeholder="出发开始日期" end-placeholder="出发结束日期" :picker-options="pickerOptions"
          />
        </div>
      </div>
    </div>
    <el-table
      ref="orderMultiple" v-loading="isLoading" stripe class="order-table-view"
      :data="list" fit
      highlight-current-row :height="`calc(100vh - ${leftheight})`" :cell-style="{ padding: '6px 0' }" size="small"
      @sort-change="sortChange" @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" size="small" align="left" width="140" fixed>
        <template slot-scope="scope">
          {{ scope.row.order_no }}
        </template>
      </el-table-column>
      <el-table-column
        label="出发时间" size="small" align="left" show-overflow-tooltip
        width="140" prop="start_time"
        sortable="custom"
      >
        <template slot-scope="scope">
          <p class="table-p fb">
            {{ scope.row.start_time.slice(11, 16) || "/" }}
          </p>
          <p class="table-p fb">
            {{ scope.row.start_time.slice(0, 11) || "/" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" size="small" align="left" prop="reseverd_phone" width="145">
        <template slot-scope="scope">
          <p class="table-p">
            {{ (scope.row && scope.row.reseverd_phone) || "/" }}
          </p>
          <el-popover
            v-if="scope.row.reseverd_info" class="item" trigger="click" placement="top-start"
            :content="scope.row.reseverd_info"
          >
            <p slot="reference">
              <el-button size="mini" type="text">乘客留言</el-button>
            </p>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="left" label="出发城市" size="small" width="100">
        <template slot-scope="scope">
          <span class="line-show-name">{{ scope.row | filterStartCity }}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="上下车位置" size="small" width="280">
        <template slot-scope="scope">
          <p class="table-p">
            <span class="badge-status" style="background-color: #52c41a" />上：{{ scope.row.start_address_remark || "-"
            }}
          </p>
          <p class="table-p">
            <span class="badge-status" style="background-color: #ff4d4f" />下：{{ scope.row.end_address_remark || "-" }}
          </p>
        </template>
      </el-table-column>
      <!-- <el-table-column label="人数" align="left" size="small" width="70" prop="book_seating" /> -->
      <el-table-column label="订单金额" align="left" size="small" width="120" prop="real_price" />
      <el-table-column label="订单状态" size="small" align="left" width="170">
        <template slot-scope="scope">
          <div class="column-flex">
            <div class="status-line-top">
              <el-tag
                v-if="scope.row.is_pay === 1 || scope.row.is_pre_pay === 1" size="mini" hit
                type="success"
              >已支付</el-tag>
              <el-tag v-else size="mini" hit type="warning">未支付</el-tag>
            </div>
            <Tablestatus v-if="scope.row.state === 1" mode="warning" word="待派单" />
            <Tablestatus v-else-if="scope.row.state === 2" mode="processing" word="已接单" />
            <Tablestatus v-else-if="scope.row.state === 3" mode="processing" word="已上车" />
            <Tablestatus v-else-if="scope.row.state === 4" mode="processing" word="在路上" />
            <Tablestatus v-else-if="scope.row.state === 5" mode="processing" word="已送达" />
            <Tablestatus v-else-if="scope.row.state === 6" mode="default" word="已完成" />
            <Tablestatus
              v-else-if="scope.row.state === 7" mode="default"
              :word="'已取消' + (scope.row.refund_status ? scope.row.refund_amount === scope.row.real_price ? '（全额退款）' : scope.row.refund_amount === '0.00' ? '（未退款）' : '（部分退款）' : '')"
            />
            <Tablestatus v-else mode="default" :word="scope.row.cn_state" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" size="small" align="left" width="130">
        <template slot-scope="scope">
          <div
            v-if="scope.row.is_pay == 1 && scope.row.refund_status
            " class="column-flex"
          >
            <div class="status-line-top">
              <span class="refund-text">-￥{{ scope.row.refund_amount }}</span>
            </div>
            <div class="refund-gray">
              {{ scope.row.refund_status === 'success' ? '退款成功' : scope.row.refund_status === 'processing' ? '退款中' :
                '退款失败' }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="派单司机" size="small" align="left" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <template v-if="scope.row && scope.row.driver">
            <p class="table-p">
              {{ (scope.row.driver && scope.row.driver.name) || "/" }}-{{ (scope.row.driver &&
                scope.row.driver.cellphone) || "/" }}
            </p>
            <p class="table-p">
              <el-tag type="info" size="mini" style="transform: scale(0.9);margin-left:0" effect="plain">
                {{ (scope.row.driver && scope.row.driver.car_tail_number) || "/" }}
              </el-tag>
            </p>
            <p class="table-p">
              {{ scope.row.dispatched_at ? '派单时间：' + scope.row.dispatched_at : '' }}
            </p>
            <p class="table-p">
              {{ (scope.row.driver && scope.row.driver.driver_group_name) ? '司机归属：' + scope.row.driver.driver_group_name
                : "" }}
            </p>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" size="small" align="left" prop="create_time" sortable="custom" min-width="180" />
      <el-table-column label="支付通道" size="small" align="left" prop="pay_config.mchnumber" min-width="160" />
      <el-table-column label="所属分台" size="small" align="left" width="140">
        <template slot-scope="scope">
          <p class="table-p">{{ (scope.row && scope.row.branch && scope.row.branch.mchname) || '' }}</p>
          <p class="table-p">{{ (scope.row && scope.row.branch && scope.row.branch.cellphone) || "-" }}</p>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="left" label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="preView(scope.row.order_id)">详情</el-button>
          <el-divider v-if="scope.row.state === 1" direction="vertical" />
          <el-button v-if="scope.row.state === 1" type="text" @click="handleDispatch(scope.row)">指派</el-button>
          <el-divider direction="vertical" />
          <el-dropdown size="mini" trigger="hover" @command="(command) => actionClose(command, scope.row)">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="scope.row.is_pay == 1 && [1, 2].includes(scope.row.state)"
                command="cancel"
              >取消订单</el-dropdown-item>
              <el-dropdown-item command="logs">派单记录</el-dropdown-item>
              <el-dropdown-item v-if="!scope.row.offline_ticket_issued" command="offline_ticket">
                开具纸质车票
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="bottom-container">
      <div />
      <el-pagination
        v-if="total > 0" layout="sizes, total, prev, pager, next" background
        :page-size="listQuery.per_page" :total="total" :page-sizes="[15, 50, 100, 200]" :current-page="listQuery.page"
        style="text-align: right" @current-change="handleCurrentChanges" @size-change="handleSizeChange"
      />
    </div>
    <el-dialog :visible.sync="dialogDes" title="订单详情" append-to-body width="850px">
      <Description :order="orderDetailId" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="small" @click="dialogDes = false">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog :visible.sync="dialogLogs" title="派单记录" append-to-body width="850px" class="refound-dialog">
      <Sendlogs :logs="currentLogs" @close="dialogLogs = false" />
    </el-dialog>
  </div>
</template>

<script>
import {
  dispatchBanXianLines
} from '@/api/business'
import { getAdmindOrder, orderRefunds, exportExcels, markOfflineTicketIssued } from '@/api/order'
import Description from './components/Description'
import Sendlogs from '@/components/Sendlogs'
import { formatStartName, formatEndName } from '@/utils/filters'
import moment from 'moment'
import { mapGetters } from 'vuex'
import BranchSelect from '@/components/BranchSelect'

export default {
  components: {
    Description,
    Sendlogs,
    BranchSelect
  },
  filters: {
    filterStartCity(val) {
      return (
        formatStartName(val).start_city
      )
    },
    filterStartName(val) {
      return (
        formatStartName(val).start_name
      )
    },
    filterEndCity(val) {
      return formatEndName(val).end_city
    },
    filterEndName(val) {
      return formatEndName(val).end_name
    },
    filSname(val) {
      return formatStartName(val).start_name
    },
    filEname(val) {
      return formatEndName(val).end_name
    }
  },
  data() {
    return {
      dialogLogs: false,
      currentLogs: [], // 派单记录
      typeIndeterminate: true,
      typeAll: false,
      isIndeterminate: false,
      checkedAll: false,
      selectedOrders: [], // 初始化为空数组
      pickerOptions: {
        shortcuts: [
          {
            text: '昨天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().subtract(1, 'days').startOf('day').valueOf()),
                new Date(moment().subtract(1, 'days').endOf('day').valueOf())
              ])
            }
          },
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().startOf('day').valueOf()),
                new Date(moment().endOf('day').valueOf())
              ])
            }
          },
          {
            text: '明天',
            onClick(picker) {
              picker.$emit('pick', [
                new Date(moment().add(1, 'days').startOf('day').valueOf()),
                new Date(moment().add(1, 'days').endOf('day').valueOf())
              ])
            }
          }
        ]
      },
      lineClasses: [],
      collapse: '',
      dialogDes: false,
      dialogRefound: false,
      currentOrder: '',
      orderDetailId: '',
      payWayOptions: [
        { value: 'wechat_jsapi', label: '线上支付-微信公众号' },
        { value: 'wechat_mini', label: '线上支付-微信小程序' },
        { value: 'offline', label: '线下支付' }
      ],
      statusOptions: [
        { value: 'total', label: '全部', num: 0 },
        { value: 'pending', label: '待指派', num: 0 },
        { value: 'dispatched', label: '已指派', num: 0 },
        { value: 'accepted', label: '已接单', num: 0 },
        { value: 'checked', label: '已上车', num: 0 },
        { value: 'completed', label: '已完成', num: 0 },
        { value: 'canceled', label: '已取消', num: 0 },
        { value: 'closed', label: '已关闭', num: 0 }
      ],
      list: null,
      isLoading: false,
      total: 0,
      downloadLoading: false, // 添加导出加载状态
      selectedDownloadLoading: false, // 添加导出所选加载状态
      listQuery: {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [11],
        status: 'total'
      },
      isPaidOptions: [
        { value: 'true', label: '已支付' },
        { value: 'false', label: '未支付' }
      ]
    }
  },
  computed: {
    ...mapGetters(['info']),
    leftheight() {
      return this.collapse === 'more' ? '375px' : '325px'
    }
  },
  created() {
    const { query } = this.$route
    if (query.orderNo) {
      this.listQuery.order_no = query.orderNo
      this.$nextTick(() => {
        this.handleFilter()
      })
    }
    this.fetchData(true)
    this.fetchBanXianLines()
  },
  methods: {
    handleFilTable(e) {
      this.listQuery.is_paid = e
      this.handleFilter()
    },
    typeChange(e) {
      if (e === 5) {
        this.collapse = 'more'
      }
    },
    tabsChange(e) {
      this.listQuery = {
        ...this.listQuery,
        page: 1,
        status: e,
        biz: 'normal'
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    async sortChange(e) {
      const { prop, order } = e
      this.listQuery['sort_by'] = order ? prop : null
      this.listQuery['sort'] = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.listQuery.page = 1
      this.fetchData(true)
    },
    async fetchBanXianLines() {
      const { data } = await dispatchBanXianLines({
        branch_id: this.listQuery.branch_id
      })
      if (data && data.length > 0) {
        this.lineClasses = data.map((o) => {
          return {
            label: `线路${o.id}：${o.start_name} → ${o.end_name}`,
            value: o.id
          }
        })
      } else {
        this.lineClasses = []
      }
    },
    preView(id) {
      this.orderDetailId = id.toString()
      this.dialogDes = true
    },
    actionClose(_, order) {
      switch (_) {
        case 'refund':
          this.currentOrder = order
          console.log(order)
          this.$nextTick(() => {
            this.dialogRefound = true
          })
          break
        case 'logs':
          this.currentLogs = order.virtual_driver || []
          this.$nextTick(() => {
            this.dialogLogs = true
          })
          break
        case 'offline_ticket':
          if (order.offline_ticket_issued) {
            this.$message({
              message: '该订单已标记为已开具纸质车票',
              type: 'info'
            })
            return
          }
          this.$confirm(
            '确认已开具纸质车票？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(async () => {
            try {
              this.loading = true
              await markOfflineTicketIssued(order.order_id)
              this.loading = false
              this.$notify({
                message: '标记成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            } catch (error) {
              this.loading = false
              this.$notify({
                message: '标记失败',
                type: 'error',
                duration: 2000
              })
            }
          })
          break
        default:
          this.$confirm(`确认取消当前订单？${order.is_pay ? '订单已支付金额将会全部原路退回，' : ''}取消后无法恢复，请谨慎操作`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            orderRefunds({
              rate: 100,
              reason: '后台取消订单',
              order: order.order_id
            }).then(() => {
              this.$notify({
                message: '操作成功',
                type: 'success',
                duration: 2000
              })
              this.fetchData()
            })
          })
      }
    },

    async fetchData(loading = true) {
      if (loading) this.isLoading = true
      const query = Object.assign({}, this.listQuery)
      const { data } = await getAdmindOrder(query)
      if (loading) this.isLoading = false
      this.list = data.data || []
      this.total = data.meta.pagination.total
      // { value: 'total', label: '全部' ,num:0 }
      const meta = data.meta
      this.statusOptions = this.statusOptions.map((item) => {
        for (const o in meta) {
          if (item.value === o) {
            item.num = meta[o]
          }
        }
        return item
      })
    },

    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        per_page: 15,
        biz: 'normal',
        type: [11],
        status: 'total'
      }
      this.$refs.orderMultiple.clearSort()
      this.fetchData()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    // 处理订单指派跳转
    handleDispatch(row) {
      // 将订单号存入localStorage而不是query参数
      localStorage.setItem('dispatchOrderNo', row.order_no)
      // 不带orderNo参数跳转
      this.$router.push({ path: '/operationCenter/dispatching' })
    },
    // 更新选择的订单
    handleSelectionChange(selection) {
      this.selectedOrders = selection.map(item => item.order_id)
    },

    // 导出所有符合条件的订单
    async handleDownload() {
      try {
        await this.$confirm('确认导出当前筛选条件下的所有订单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }

      this.downloadLoading = true
      try {
        await exportExcels({
          ...this.listQuery,
          biz: 'normal' // 确认业务类型 biz 改为 normal
        })
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往"导出任务管理"页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage()
          }
        }).catch(() => {})
      } catch (error) {
        console.error('导出任务提交失败:', error)
        this.$message.error('导出任务提交失败，请稍后重试。')
      } finally {
        this.downloadLoading = false
      }
    },

    // 导出所选订单
    async exportSelectedOrders() {
      if (!this.selectedOrders.length) {
        this.$message.warning('请选择需要导出的订单。')
        return
      }

      const confirmationMessage = `确认导出已选中的 ${this.selectedOrders.length} 条订单吗？`

      try {
        await this.$confirm(confirmationMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }

      this.selectedDownloadLoading = true
      try {
        await exportExcels({
          order_ids: this.selectedOrders,
          biz: 'normal' // 确认业务类型 biz 改为 normal
        })
        this.$msgbox({
          title: '导出任务已提交',
          message: '请稍后前往"导出任务管理"页面查看或下载文件。',
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '前往查看',
          cancelButtonText: '知道了',
          center: true
        }).then(action => {
          if (action === 'confirm') {
            this.goToExportTasksPage()
          }
        }).catch(() => {})
        this.$refs.orderMultiple.clearSelection() // 清空选择
      } catch (error) {
        console.error('导出任务提交失败:', error)
        this.$message.error('导出任务提交失败，请稍后重试。')
      } finally {
        this.selectedDownloadLoading = false
      }
    },

    goToExportTasksPage() {
      this.$router.push('/resourceManage/exportTask') // 确认路径
    },
    handleCollapse() {
      this.collapse = this.collapse === 'more' ? '' : 'more'
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  margin: 0 12px;
  background: #fff;
  border-radius: 4px;
  min-height: 100vh;
}

.el-radio-group {
  margin-bottom: 16px;
  background: #fff;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .el-radio-button__inner {
    padding: 8px 15px;
    font-size: 13px;
  }

  .el-radio-button:first-child .el-radio-button__inner {
    background-color: #409EFF;
    border-color: #409EFF;
    color: #fff;
    box-shadow: none;
  }
}

.handle-container {
  background: #fff;
  padding: 8px 15px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    margin: 0;
    flex-shrink: 0;

    &.mini-condition {
      width: 180px;
    }

    &.date-condition {
      width: 320px;
    }

    &.el-button--text {
      padding: 7px 0;
      margin-left: 4px;
    }

    &.el-button--primary {
      padding: 7px 15px;
    }
  }

}

.filter-more {
  margin-top: 8px;
  border-top: 1px solid #ebeef5;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;

    .filter-item {
      margin: 0;

      &.el-input,
      &.el-select {
        width: 200px;
      }

      &.date-condition {
        width: 300px;
      }
    }
  }
}

.el-input-group__prepend {
  padding: 0 12px;
  background-color: #f5f7fa;
  color: #606266;
  min-width: 70px;
  text-align: center;
  font-size: 13px;
}

.el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-button--small {
  padding: 8px 15px;
}

.el-select {
  width: 160px;
}

.el-input {
  width: 200px;
}

.el-date-editor.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 300px;
}

.temp_order {
  position: absolute;
  width: 12px;
  height: 12px;
  top: 10px;
  left: 3px;
  background-color: #0067e1;
  border-radius: 10px;
  color: #fff;
  display: block;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #0067e1;
  font-size: 12px;
}

.payed {
  color: #67c23a;
  font-size: 12px;
}

.not-pay {
  color: #e6a23c;
}

.refund-gray {
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
}

.line-show-name {
  background-color: #409EFF;
  color: #fff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 12px;
  transform: scale(0.9);
  margin-left: 2px;
}

.badge-status {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 5px;
  height: 5px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fff;
  border-radius: 4px;
  margin-top: 16px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
</style>
<style lang="scss">
.el-radio-button__orig-radio:checked+.el-radio-button__inner {
  background-color: #409EFF;
  border-color: #409EFF;
  box-shadow: -1px 0 0 0 #409EFF;
}

.el-table {
  margin-top: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
    font-size: 13px;
    height: 45px;
    padding: 8px 0;
  }

  td {
    padding: 8px 0;
    font-size: 13px;
    color: #606266;
  }

  .el-table__row:hover td {
    background-color: #f5f7fa !important;
  }

  .el-button--text {
    padding: 0 8px;
    font-size: 13px;

    &:hover {
      color: #66b1ff;
    }
  }
}

.table-p {
  margin: 0;
  padding: 0;
  line-height: 20px;
  color: #606266;
  font-size: 13px;

  &:not(:last-child) {
    margin-bottom: 4px;
  }

  .el-tag {
    margin-right: 4px;
  }
}

.line-show-name {
  background-color: #409EFF;
  color: #fff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  margin: 0 4px;
  display: inline-block;
}

.badge-status {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 6px;
  height: 6px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 6px;
}

.payed {
  color: #67c23a;
  font-weight: 500;
}

.not-pay {
  color: #e6a23c;
  font-weight: 500;
}

.refund-gray {
  color: rgba(0, 0, 0, 0.65);
}

.el-divider--vertical {
  margin: 0 8px;
  height: 0.9em;
}

.column-flex {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .status-line-top {
    margin-bottom: 2px;
  }

  .refund-text {
    color: #f56c6c;
    font-weight: 500;
  }
}
</style>

<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="handle-container">
      <div class="filter-container">
        <span class="label-key">广告标题</span>
        <el-input
          v-model="listQuery.keyword"
          placeholder="请输入"
          clearable
          size="small"
          class="filter-item mini-condition"
          @keyup.enter.native="handleFilter"
        />
        <span class="label-key">状态</span>
        <el-select
          v-model="listQuery.state"
          placeholder="请选择"
          clearable
          size="small"
          class="filter-item mini-condition"
        >
          <el-option label="全部" value="" />
          <el-option label="已上架" value="1" />
          <el-option label="已下架" value="0" />
        </el-select>
        <span class="label-key">展示时间</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          value-format="yyyy-MM-dd"
          style="width: 260px"
          class="filter-item"
        />
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          plain
          icon="el-icon-refresh"
          @click="handleReset"
        >
          重置
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          新建广告
        </el-button>
        <el-button
          :disabled="!selectedIds.length"
          class="filter-item"
          type="success"
          size="small"
          icon="el-icon-top"
          @click="handleBatchUp"
        >
          批量上架
        </el-button>
        <el-button
          :disabled="!selectedIds.length"
          class="filter-item"
          type="warning"
          size="small"
          icon="el-icon-bottom"
          @click="handleBatchDown"
        >
          批量下架
        </el-button>
        <el-button
          :disabled="!selectedIds.length"
          class="filter-item"
          type="danger"
          size="small"
          icon="el-icon-delete"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      style="width: 100%"
      size="mini"
      stripe
      highlight-current-row
      :header-cell-style="{background:'#f5f7fa'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" prop="id" align="center" width="80" />
      <el-table-column label="广告标题" prop="title" min-width="200" show-overflow-tooltip />
      <el-table-column label="广告简介" prop="description" min-width="200" show-overflow-tooltip />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status ? 'success' : 'info'" effect="dark">
            {{ row.status ? '已上架' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="广告类型" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="{row}">
          <el-tooltip placement="top" :content="formatAdTypes(row.ad_types)">
            <span>{{ formatAdTypes(row.ad_types) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="广告位" align="center" width="250" show-overflow-tooltip>
        <template slot-scope="{row}">
          <el-tooltip placement="top" :content="row.position">
            <span>{{ row.position }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="图片" width="120" align="center">
        <template slot-scope="{row}">
          <el-image
            style="width: 100px; height: 40px; border-radius: 4px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);"
            :src="row.image_url"
            :preview-src-list="[row.image_url]"
            fit="cover"
            lazy
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
            <div slot="placeholder" class="image-slot">
              <i class="el-icon-loading" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="start_time" width="160" align="center" />
      <el-table-column label="结束时间" prop="end_time" width="160" align="center" />

      <el-table-column label="创建时间" prop="created_at" width="160" align="center" />
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="{row}">
          <el-tooltip content="编辑" placement="top" :hide-after="1000">
            <el-button type="primary" size="mini" icon="el-icon-edit" circle @click="handleUpdate(row)" />
          </el-tooltip>
          <el-tooltip :content="row.status ? '下架' : '上架'" placement="top" :hide-after="1000">
            <el-button
              :type="row.status ? 'warning' : 'success'"
              size="mini"
              :icon="row.status ? 'el-icon-bottom' : 'el-icon-top'"
              circle
              @click="handleStatusChange(row)"
            />
          </el-tooltip>
          <el-tooltip content="删除" placement="top" :hide-after="1000">
            <el-button type="danger" size="mini" icon="el-icon-delete" circle @click="handleDelete(row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="bottom-container">
      <div class="table-tips">
        已选择 <span class="highlight">{{ selectedIds.length }}</span> 项
      </div>
      <el-pagination
        v-if="total > 0"
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-size="listQuery.limit"
        :total="total"
        :page-sizes="[15, 50, 100, 200]"
        :current-page="listQuery.page"
        style="padding: 10px 0;"
        @current-change="handleCurrentChanges"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新建广告' : '编辑广告'"
      :visible.sync="dialogVisible"
      width="700px"
      :close-on-click-modal="false"
      @close="$refs['dataForm'].clearValidate()"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        status-icon
      >
        <el-form-item label="广告标题" prop="title">
          <el-input
            v-model="temp.title"
            placeholder="请输入1-20个字符"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="链接" prop="link">
          <el-input
            v-model="temp.link"
            placeholder="请填写完整url（例如:http://www.cczhaoce.com）"
          />
        </el-form-item>
        <el-form-item label="广告简介" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入1-200个字符"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="广告类型" prop="ad_types">
          <el-select v-model="temp.ad_types" multiple placeholder="请选择广告类型" style="width: 100%">
            <el-option label="拼车" :value="1" />
            <el-option label="包车" :value="2" />
            <el-option label="带货" :value="3" />
            <el-option label="代办" :value="4" />
            <el-option label="班线" :value="5" />
            <el-option label="顺风车" :value="6" />
            <el-option label="快车" :value="7" />
            <el-option label="出租车" :value="11" />
          </el-select>
        </el-form-item>
        <el-form-item label="广告位" prop="positions">
          <div class="positions-container">
            <div class="positions-group">
              <div class="group-title">乘客端</div>
              <div class="select-all">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
              </div>
              <el-checkbox-group v-model="temp.positions" class="checkbox-list" @change="handlePositionsChange">
                <el-checkbox :label="2" border>拼车页</el-checkbox>
                <el-checkbox :label="3" border>包车页</el-checkbox>
                <el-checkbox :label="4" border>班线车页</el-checkbox>
                <el-checkbox :label="5" border>顺风车</el-checkbox>
                <el-checkbox :label="6" border>带货页</el-checkbox>
                <el-checkbox :label="7" border>代办页</el-checkbox>
                <el-checkbox :label="8" border>个人中心页</el-checkbox>
                <el-checkbox :label="9" border>注册页</el-checkbox>
                <el-checkbox :label="10" border>出租车页</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="广告图片" prop="image_url">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :headers="uploadHeaders"
            name="file"
            list-type="picture-card"
            @preview="handlePictureCardPreview"
          >
            <img v-if="temp.image_url" :src="temp.image_url" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <div class="upload-tip">建议尺寸：720x200px（长宽比3.6:1），格式：jpg/png，大小：不超过2M</div>
        </el-form-item>
        <el-form-item label="展示时间" prop="dateRange">
          <el-date-picker
            v-model="temp.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="dialogStatus === 'create' ? createData() : updateData()">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 预览图片对话框 -->
    <el-dialog
      :title="`预览图片`"
      :visible.sync="dialogImageVisible"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-image
        :src="dialogImageUrl"
        :preview-src-list="[dialogImageUrl]"
        fit="contain"
      >
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline" />
        </div>
        <div slot="placeholder" class="image-slot">
          <i class="el-icon-loading" />
        </div>
      </el-image>
    </el-dialog>
  </div>
</template>

<script>
import { getAdList, createAd, updateAd, deleteAd, upAd, downAd, batchUpAds, batchDownAds, batchDeleteAds } from '@/api/ad'
import { getToken } from '@/utils/auth'

export default {
  name: 'AdManage',
  components: {},
  data() {
    const validateImageUrl = (rule, value, callback) => {
      if (value) {
        callback()
      } else {
        callback(new Error(rule.message))
      }
    }
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 15,
        keyword: '',
        status: '',
        start_time: '',
        end_time: ''
      },
      dateRange: [],
      selectedIds: [],
      dialogVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        title: '',
        link: '',
        description: '',
        ad_type: '',
        ad_types: [],
        positions: [],
        position: '',
        image_url: '',
        dateRange: [],
        start_time: '',
        end_time: '',
        status: 0
      },
      rules: {
        title: [
          { required: true, message: '请输入广告标题', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        link: [
          { required: false, message: '请输入链接地址', trigger: 'blur' },
          { pattern: /^https?:\/\//, message: '请输入正确的URL格式', trigger: 'blur', required: false }
        ],
        description: [
          { required: true, message: '请输入广告简介', trigger: 'blur' },
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ],
        ad_types: [{ type: 'array', required: true, message: '请至少选择一个广告类型', trigger: 'change' }],
        positions: [{ type: 'array', required: true, message: '请至少选择一个广告位', trigger: 'change' }],
        image_url: [{ required: true, message: '请上传广告图片', validator: validateImageUrl, trigger: 'change' }],
        dateRange: [{ required: true, message: '请选择展示时间', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      submitLoading: false,
      selectAll: false,
      dialogImageUrl: '',
      dialogImageVisible: false
    }
  },
  computed: {
    uploadHeaders() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    },
    uploadUrl() {
      return process.env.VUE_APP_BASE_UPLOAD_API + '/admin/fapiao/attachments'
    }
  },
  watch: {
    dateRange(val) {
      if (val) {
        this.listQuery.start_time = val[0]
        this.listQuery.end_time = val[1]
      } else {
        this.listQuery.start_time = ''
        this.listQuery.end_time = ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.listLoading = true
      try {
        const response = await getAdList(this.listQuery)
        if (response.status === 'success' && response.data) {
          // 适配返回的数据结构
          const items = response.data.data || []
          this.list = items.map(item => {
            // 处理广告位数据
            const positions = item.categories ? item.categories.map(cat => cat.id) : []
            // 处理广告类型数据
            const adTypes = item.limit_biz
              ? (Array.isArray(item.limit_biz)
                ? item.limit_biz.map(Number)
                : (typeof item.limit_biz === 'string' && item.limit_biz.includes(',')
                  ? item.limit_biz.split(',').map(Number)
                  : [parseInt(item.limit_biz)]))
              : []

            return {
              id: item.id,
              title: item.title,
              link: item.link || '',
              description: item.describe,
              position: this.formatPositions(positions),
              positions: positions,
              ad_types: adTypes,
              image_url: item.imgsrc_url,
              start_time: item.start_time,
              end_time: item.end_time,
              display_count: item.display_times,
              click_count: item.clicks,
              status: parseInt(item.state) === 1,
              created_at: item.create_time
            }
          })
          this.total = response.data.meta.pagination ? response.data.meta.pagination.total : 0
        } else {
          this.list = []
          this.total = 0
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取广告列表失败:', error)
        this.$message.error('获取广告列表失败')
        this.list = []
        this.total = 0
      }
      this.listLoading = false
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.selectedIds = val.map(item => item.id)
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        title: '',
        link: '',
        description: '',
        ad_type: '',
        ad_types: [],
        positions: [],
        position: '',
        image_url: '',
        dateRange: [],
        start_time: '',
        end_time: '',
        status: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      console.log('原始日期数据:', row.start_time, row.end_time)

      // 处理时间戳转换为日期格式
      let startTime = row.start_time
      let endTime = row.end_time

      // 如果日期包含时间部分，只保留日期部分
      if (startTime && startTime.includes(' ')) {
        startTime = startTime.split(' ')[0]
      }

      if (endTime && endTime.includes(' ')) {
        endTime = endTime.split(' ')[0]
      }

      console.log('处理后的日期:', startTime, endTime)

      this.temp = {
        id: row.id,
        title: row.title,
        link: row.link || '',
        description: row.description,
        ad_types: Array.isArray(row.ad_types) ? row.ad_types : [row.ad_types],
        positions: Array.isArray(row.positions) ? row.positions : [],
        position: row.position,
        image_url: row.image_url,
        dateRange: [startTime, endTime],
        start_time: startTime,
        end_time: endTime,
        status: row.status ? 1 : 0
      }
      // 检查并设置全选状态
      const allPositions = [2, 3, 4, 5, 6, 7, 8, 9, 10]
      this.selectAll = this.temp.positions.length === allPositions.length
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          // 处理图片路径
          const imageUrl = this.temp.image_url

          const data = {
            title: this.temp.title,
            link: this.temp.link,
            describe: this.temp.description,
            limit_biz: this.temp.ad_types,
            imgsrc: imageUrl,
            start_time: this.temp.start_time,
            end_time: this.temp.end_time,
            state: this.temp.status ? 1 : 0,
            cate_id: this.temp.positions
          }

          try {
            const response = await createAd(data)
            if (response.status === 'success') {
              this.dialogVisible = false
              this.$message({
                type: 'success',
                message: '创建成功'
              })
              this.getList()
            } else {
              this.$message.error(response.message || '创建失败')
            }
          } catch (error) {
            console.error('创建广告失败:', error)
            this.$message.error('创建广告失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    async updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          // 处理图片路径
          const imageUrl = this.temp.image_url

          const data = {
            id: this.temp.id,
            title: this.temp.title,
            link: this.temp.link,
            describe: this.temp.description,
            limit_biz: this.temp.ad_types,
            imgsrc: imageUrl,
            start_time: this.temp.start_time,
            end_time: this.temp.end_time,
            state: this.temp.status,
            cate_id: this.temp.positions
          }

          try {
            const response = await updateAd(data.id, data)
            if (response.status === 'success') {
              this.dialogVisible = false
              this.$message({
                type: 'success',
                message: '更新成功'
              })
              this.getList()
            } else {
              this.$message.error(response.message || '更新失败')
            }
          } catch (error) {
            console.error('更新广告失败:', error)
            this.$message.error('更新广告失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该广告吗？', '提示', {
          type: 'warning'
        })
        await deleteAd(row.id)
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.getList()
      } catch (error) {
        console.error('删除广告失败:', error)
      }
    },
    async handleBatchDelete() {
      try {
        await this.$confirm(`确认删除选中的 ${this.selectedIds.length} 条广告吗？`, '提示', {
          type: 'warning'
        })
        await batchDeleteAds(this.selectedIds)
        this.$message({
          type: 'success',
          message: '批量删除成功'
        })
        this.getList()
      } catch (error) {
        console.error('批量删除广告失败:', error)
      }
    },
    async handleStatusChange(row) {
      try {
        const isUp = !row.status
        const response = isUp ? await upAd(row.id) : await downAd(row.id)
        if (response.status === 'success') {
          this.$message({
            type: 'success',
            message: `${isUp ? '上架' : '下架'}成功`
          })
          this.getList()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('更新广告状态失败:', error)
        this.$message.error('更新广告状态失败')
      }
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('上传文件只能是图片格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    handleUploadSuccess(response) {
      if (response.status === 'success' && response.data) {
        this.temp.image_url = response.data.url || response.data.path
        this.$message.success('图片上传成功')
      } else {
        this.$message.error(response.message || '图片上传失败')
      }
    },
    handleUploadError(error) {
      console.error('图片上传错误:', error)
      this.$message.error('图片上传失败')
    },
    handleCurrentChanges(val) {
      this.listQuery.page = val
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        limit: 15,
        keyword: '',
        status: '',
        start_time: '',
        end_time: ''
      }
      this.dateRange = []
      this.getList()
    },
    async handleBatchUp() {
      try {
        await this.$confirm(`确认上架选中的 ${this.selectedIds.length} 条广告吗？`, '提示', {
          type: 'warning'
        })
        const response = await batchUpAds(this.selectedIds)
        if (response.status === 'success') {
          this.$message({
            type: 'success',
            message: '批量上架成功'
          })
          this.getList()
        } else {
          this.$message.error(response.message || '批量上架失败')
        }
      } catch (error) {
        console.error('批量上架广告失败:', error)
        this.$message.error('批量上架失败')
      }
    },
    async handleBatchDown() {
      try {
        await this.$confirm(`确认下架选中的 ${this.selectedIds.length} 条广告吗？`, '提示', {
          type: 'warning'
        })
        const response = await batchDownAds(this.selectedIds)
        if (response.status === 'success') {
          this.$message({
            type: 'success',
            message: '批量下架成功'
          })
          this.getList()
        } else {
          this.$message.error(response.message || '批量下架失败')
        }
      } catch (error) {
        console.error('批量下架广告失败:', error)
        this.$message.error('批量下架失败')
      }
    },
    handleSelectAll(val) {
      const allPositions = [2, 3, 4, 5, 6, 7, 8, 9, 10]
      this.temp.positions = val ? allPositions : []
    },
    handlePositionsChange(val) {
      const allPositions = [2, 3, 4, 5, 6, 7, 8, 9, 10]
      this.selectAll = val.length === allPositions.length
    },
    handleDateRangeChange(val) {
      this.$nextTick(() => {
        if (val) {
          this.temp.start_time = val[0]
          this.temp.end_time = val[1]
        } else {
          this.temp.start_time = ''
          this.temp.end_time = ''
        }
        this.$refs['dataForm'].validateField('dateRange')
      })
    },
    // 格式化广告位显示
    formatPositions(positions) {
      if (!Array.isArray(positions)) {
        return ''
      }
      const positionMap = {
        2: '拼车页',
        3: '包车页',
        4: '班线车页',
        5: '顺风车',
        6: '带货页',
        7: '代办页',
        8: '个人中心页',
        9: '注册页',
        10: '出租车页'
      }
      return positions.map(id => positionMap[id] || id).join('、')
    },
    formatAdTypes(adTypes) {
      if (!Array.isArray(adTypes)) {
        return ''
      }
      const typeMap = {
        1: '拼车',
        2: '包车',
        3: '带货',
        4: '代办',
        5: '班线',
        6: '顺风车',
        7: '快车',
        11: '出租车'
      }
      return adTypes.map(type => typeMap[type] || type).join('、')
    },
    // 格式化时间戳为日期时间格式
    formatDateTime(timestamp) {
      if (!timestamp) return ''

      // 如果是字符串日期格式
      if (typeof timestamp === 'string') {
        // 如果只有日期部分，添加时间部分
        if (timestamp.length === 10 && timestamp.includes('-')) {
          return timestamp + ' 00:00:00'
        }
        // 如果已经是完整的日期时间格式，直接返回
        if (timestamp.includes('-') && timestamp.includes(':')) {
          return timestamp
        }
      }

      // 检查是否为数字类型的时间戳
      if (!isNaN(timestamp)) {
        // 转换为数字
        const numTimestamp = Number(timestamp)
        // 10位时间戳，需要乘以1000转换为毫秒
        if (String(numTimestamp).length === 10) {
          timestamp = numTimestamp * 1000
        } else {
          timestamp = numTimestamp
        }

        const date = new Date(timestamp)
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      // 如果无法处理，返回原始值
      return timestamp
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogImageVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.handle-container {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .filter-item {
    margin-right: 10px;
    margin-bottom: 8px;
  }
}

.avatar-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.avatar {
  width: 148px;
  height: 148px;
  display: block;
  object-fit: cover;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.table-tips {
  color: #606266;
  font-size: 13px;

  .highlight {
    color: #409EFF;
    font-weight: bold;
    margin: 0 4px;
  }
}

::v-deep {
  .el-table {
    border-radius: 4px;
    overflow: hidden;

    th {
      background-color: #f5f7fa !important;
    }
  }

  .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
    }

    .el-dialog__body {
      padding: 30px 20px;
    }

    .el-dialog__footer {
      padding: 20px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .el-form-item {
    margin-bottom: 22px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-button {
    padding: 9px 15px;

    &.is-circle {
      padding: 9px;
    }
  }
}

.positions-container {
  .positions-group {
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
      padding-left: 5px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -5px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background-color: #409EFF;
        border-radius: 2px;
      }
    }

    .checkbox-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .el-checkbox {
        margin-right: 0;
        margin-left: 0;
      }
    }
  }
}

::v-deep {
  .el-checkbox {
    &.is-bordered {
      padding: 7px 15px 7px 10px;
      border-radius: 4px;
      border: 1px solid #DCDFE6;

      &:hover {
        border-color: #409EFF;
      }

      &.is-checked {
        border-color: #409EFF;
        background-color: #ecf5ff;
      }
    }

    .el-checkbox__label {
      padding-left: 6px;
      font-size: 13px;
    }
  }
}
</style>

<style lang="scss">
.label-key {
  margin-left: 4px;
  margin-right: 8px;
  font-weight: 500;
}

.mini-condition {
  width: 180px;
  margin-right: 10px;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}
</style>

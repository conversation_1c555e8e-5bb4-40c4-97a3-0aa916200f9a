import Mock from 'mockjs'
import { builder, getQueryParameters } from '../util'

// 商户状态：0-正常，1-冻结
const STATUS_NORMAL = 0
// eslint-disable-next-line
const STATUS_FROZEN = 1

// 商户列表数据
const merchantList = []
for (let i = 0; i < 50; i++) {
  merchantList.push({
    admin_id: Mock.mock('@integer(10000, 99999)'),
    account: Mock.mock('@word(5, 10)'),
    mchname: Mock.mock('@cname') + '商户',
    cellphone: Mock.mock('@string("number", 11)'),
    email: Mock.mock('@email'),
    total_amount: Mock.mock('@float(1000, 1000000, 2, 2)'),
    balance: Mock.mock('@float(0, 100000, 2, 2)'),
    is_freeze: Mock.mock('@integer(0, 1)'),
    create_time: Mock.mock('@datetime'),
    update_time: Mock.mock('@datetime'),
    group_id: 2, // 2-商户超管账号
    branch_tree: function () {
      const id = this.admin_id
      return `${id}`
    },
    branch_type: 0
  })
}

// 商户权限定义
const permissionDefinitions = {
  menu_permissions_definition: [
    {
      permission: 'dashboard',
      meta: { title: '仪表盘' },
      enabled: true,
      sub_menu: [
        {
          permission: 'analysis',
          meta: { title: '分析页' },
          enabled: true
        },
        {
          permission: 'monitor',
          meta: { title: '监控页' },
          enabled: false
        },
        {
          permission: 'workplace',
          meta: { title: '工作台' },
          enabled: true
        }
      ]
    },
    {
      permission: 'order',
      meta: { title: '订单管理' },
      enabled: true,
      sub_menu: [
        {
          permission: 'order-list',
          meta: { title: '订单列表' },
          enabled: true
        },
        {
          permission: 'order-detail',
          meta: { title: '订单详情' },
          enabled: true
        }
      ]
    },
    {
      permission: 'driver',
      meta: { title: '司机管理' },
      enabled: true,
      sub_menu: [
        {
          permission: 'driver-list',
          meta: { title: '司机列表' },
          enabled: true
        },
        {
          permission: 'driver-detail',
          meta: { title: '司机详情' },
          enabled: true
        }
      ]
    },
    {
      permission: 'branch',
      meta: { title: '分台管理' },
      enabled: true,
      sub_menu: [
        {
          permission: 'branch-line',
          meta: { title: '线路分台' },
          enabled: true
        },
        {
          permission: 'branch-channel',
          meta: { title: '渠道分台' },
          enabled: false
        }
      ]
    },
    {
      permission: 'finance',
      meta: { title: '财务管理' },
      enabled: false,
      sub_menu: [
        {
          permission: 'finance-account',
          meta: { title: '账户管理' },
          enabled: false
        },
        {
          permission: 'finance-settlement',
          meta: { title: '结算管理' },
          enabled: false
        }
      ]
    }
  ],
  function_permission_definition: {
    freshOrderNotice: true,
    provincialReportConfig: false,
    exportData: true,
    batchOperations: true
  }
}

// 业务类型列表
const businessTypes = [
  { key: 'chartered', label: '包车' },
  { key: 'shuttle', label: '班车' },
  { key: 'airport', label: '接送机' },
  { key: 'free_ride', label: '顺风车' }
]

// 获取商户列表
Mock.mock(/\/merchant\/list/, 'get', options => {
  const params = getQueryParameters(options)

  let result = merchantList

  // 按商户名称筛选
  if (params.mchname) {
    result = result.filter(item => item.mchname.includes(params.mchname))
  }

  // 按手机号筛选
  if (params.cellphone) {
    result = result.filter(item => item.cellphone.includes(params.cellphone))
  }

  // 按状态筛选
  if (params.status !== undefined && params.status !== null) {
    result = result.filter(item => item.is_freeze === parseInt(params.status))
  }

  // 分页处理
  const pageSize = parseInt(params.pageSize) || 10
  const pageNo = parseInt(params.pageNo) || 1
  const start = (pageNo - 1) * pageSize
  const end = pageNo * pageSize
  const list = result.slice(start, end)

  return builder({
    data: list,
    pageSize: pageSize,
    pageNo: pageNo,
    totalCount: result.length,
    totalPage: Math.ceil(result.length / pageSize)
  })
})

// 获取商户详情
Mock.mock(/\/merchant\/detail\/\d+/, 'get', options => {
  const url = options.url
  const id = url.match(/\/merchant\/detail\/(\d+)/)[1]
  const merchant = merchantList.find(item => item.admin_id === parseInt(id)) || merchantList[0]

  return builder(merchant)
})

// 创建商户
Mock.mock(/\/merchant\/create/, 'post', options => {
  const newMerchant = JSON.parse(options.body)
  newMerchant.admin_id = Mock.mock('@integer(10000, 99999)')
  newMerchant.create_time = Mock.mock('@datetime')
  newMerchant.update_time = Mock.mock('@datetime')
  newMerchant.group_id = 2 // 商户超管账号
  newMerchant.branch_tree = `${newMerchant.admin_id}`
  newMerchant.is_freeze = STATUS_NORMAL

  merchantList.unshift(newMerchant)

  return builder({ result: true, message: '创建成功' })
})

// 更新商户
Mock.mock(/\/merchant\/update\/\d+/, 'put', options => {
  const url = options.url
  const id = url.match(/\/merchant\/update\/(\d+)/)[1]
  const updatedMerchant = JSON.parse(options.body)

  const index = merchantList.findIndex(item => item.admin_id === parseInt(id))
  if (index >= 0) {
    merchantList[index] = { ...merchantList[index], ...updatedMerchant, update_time: Mock.mock('@datetime') }
  }

  return builder({ result: true, message: '更新成功' })
})

// 删除商户
Mock.mock(/\/merchant\/delete\/\d+/, 'delete', options => {
  const url = options.url
  const id = url.match(/\/merchant\/delete\/(\d+)/)[1]

  const index = merchantList.findIndex(item => item.admin_id === parseInt(id))
  if (index >= 0) {
    merchantList.splice(index, 1)
  }

  return builder({ result: true, message: '删除成功' })
})

// 修改商户状态
Mock.mock(/\/merchant\/change-status/, 'post', options => {
  const { id, status } = JSON.parse(options.body)

  const index = merchantList.findIndex(item => item.admin_id === parseInt(id))
  if (index >= 0) {
    merchantList[index].is_freeze = status
    merchantList[index].update_time = Mock.mock('@datetime')
  }

  return builder({ result: true, message: status === STATUS_NORMAL ? '启用成功' : '禁用成功' })
})

// 修改商户密码
Mock.mock(/\/merchant\/change-password\/\d+/, 'post', options => {
  return builder({ result: true, message: '密码修改成功' })
})

// 获取商户权限
Mock.mock(/\/merchant\/permissions\/\d+/, 'get', () => {
  return builder(permissionDefinitions)
})

// 更新商户权限
Mock.mock(/\/merchant\/update-permissions\/\d+/, 'post', () => {
  return builder({ result: true, message: '权限更新成功' })
})

// 获取商户分台树
Mock.mock(/\/merchant\/branch-tree\/\d+/, 'get', options => {
  const url = options.url
  const id = url.match(/\/merchant\/branch-tree\/(\d+)/)[1]
  const merchant = merchantList.find(item => item.admin_id === parseInt(id))

  if (!merchant) {
    return builder([])
  }

  // 生成随机分台树
  const branchTree = [
    {
      admin_id: merchant.admin_id,
      mchname: merchant.mchname,
      branch_tree: `${merchant.admin_id}`,
      children: []
    }
  ]

  // 添加一级分台
  for (let i = 0; i < Mock.Random.integer(2, 5); i++) {
    const firstLevelBranch = {
      admin_id: Mock.mock('@integer(100000, 999999)'),
      mchname: Mock.mock('@city') + '分台',
      branch_tree: `${merchant.admin_id},${Mock.mock('@integer(100000, 999999)')}`,
      children: []
    }

    // 添加二级分台
    for (let j = 0; j < Mock.Random.integer(0, 3); j++) {
      const secondLevelBranch = {
        admin_id: Mock.mock('@integer(1000000, 9999999)'),
        mchname: Mock.mock('@county') + '分台',
        branch_tree: `${merchant.admin_id},${firstLevelBranch.admin_id},${Mock.mock('@integer(1000000, 9999999)')}`,
        children: []
      }

      firstLevelBranch.children.push(secondLevelBranch)
    }

    branchTree[0].children.push(firstLevelBranch)
  }

  return builder(branchTree)
})

// 获取业务类型列表
Mock.mock(/\/merchant\/business-types/, 'get', () => {
  return builder(businessTypes)
})

import Mock from 'mockjs2'
import { builder, getBody } from '../util'

// 模拟SaaS API登录接口
const login = (options) => {
  const body = getBody(options)
  console.log('mock saas login: body', body)

  // 简单验证登录信息
  if (!body.account || !body.password) {
    return builder({ message: '账户或密码不能为空' }, '账户或密码错误', 401)
  }

  // 返回成功的登录响应，包含access_token
  return builder({
    access_token: 'saas_mock_token_' + Mock.mock('@guid'),
    expires_in: 86400, // token有效期24小时
    token_type: 'Bearer'
  }, '登录成功', 200, { 'Custom-Header': Mock.mock('@guid') })
}

// 模拟退出登录接口
const logout = () => {
  return builder({}, '退出成功', 200)
}

// 模拟获取当前用户信息接口
const me = () => {
  return builder({
    user: {
      id: Mock.mock('@integer(10000, 99999)'),
      account: 'admin',
      name: '管理员',
      mchname: 'Serati Ma公司',
      cellphone: Mock.mock('@string("number", 11)'),
      email: Mock.mock('@email'),
      is_freeze: 0,
      create_time: Mock.mock('@datetime'),
      update_time: Mock.mock('@datetime'),
      group_id: 1 // 1-平台超管账号
    },
    permissions: [
      {
        id: 'admin',
        name: '管理员权限',
        actions: ['add', 'edit', 'delete', 'query']
      },
      {
        id: 'merchant',
        name: '商户管理',
        actions: ['add', 'edit', 'delete', 'query']
      }
    ]
  }, '获取用户信息成功', 200)
}

// 模拟刷新token接口
const refreshToken = () => {
  return builder({
    access_token: 'saas_refresh_token_' + Mock.mock('@guid'),
    expires_in: 86400, // token有效期24小时
    token_type: 'Bearer'
  }, '刷新token成功', 200)
}

// 模拟验证token接口
const verifyToken = () => {
  return builder({ valid: true }, 'token验证成功', 200)
}

// 模拟获取商户列表接口
const merchants = (options) => {
  const merchants = []
  for (let i = 0; i < 10; i++) {
    merchants.push({
      id: Mock.mock('@integer(10000, 99999)'),
      mchname: Mock.mock('@cname') + '商户',
      account: Mock.mock('@word(5, 10)'),
      cellphone: Mock.mock('@string("number", 11)'),
      email: Mock.mock('@email'),
      total_amount: Mock.mock('@float(1000, 1000000, 2, 2)'),
      balance: Mock.mock('@float(0, 100000, 2, 2)'),
      is_freeze: Mock.mock('@integer(0, 1)'),
      create_time: Mock.mock('@datetime'),
      update_time: Mock.mock('@datetime')
    })
  }

  return builder({
    list: merchants,
    pagination: {
      total: 100,
      page: 1,
      pageSize: 10
    }
  }, '获取商户列表成功', 200)
}

// 注册mock接口
Mock.mock(/\/api\/admin\/saas\/login/, 'post', login)
Mock.mock(/\/api\/admin\/saas\/logout/, 'post', logout)
Mock.mock(/\/api\/admin\/saas\/me/, 'get', me)
Mock.mock(/\/api\/admin\/saas\/refresh/, 'post', refreshToken)
Mock.mock(/\/api\/admin\/saas\/verify/, 'get', verifyToken)
Mock.mock(/\/api\/admin\/saas\/merchants/, 'get', merchants)

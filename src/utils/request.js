import axios from 'axios'
import store from '@/store'
import storage from 'store'
import notification from 'ant-design-vue/es/notification'
import { VueAxios } from './axios'
import { ACCESS_TOKEN } from '@/store/mutation-types'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: process.env.VUE_APP_API_BASE_URL || '', // API base_url, 如果 VUE_APP_API_BASE_URL 未定义则默认为空字符串（相对路径）
  timeout: 60000, // request timeout
  withCredentials: false, // 不使用credentials模式，解决CORS问题
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    'Accept': 'application/json'
  }
})

// 输出API配置信息
console.log('axios配置baseURL:', request.defaults.baseURL)

// 异常拦截处理器
const errorHandler = (error) => {
  console.error('API请求错误:', error)

  if (error.response) {
    const data = error.response.data
    // 从 localstorage 获取 token
    const token = storage.get(ACCESS_TOKEN)

    // 处理404错误
    if (error.response.status === 404) {
      notification.error({
        message: 'API不存在',
        description: `请求的API接口不存在: ${error.config.url}`
      })
    } else if (error.response.status === 403) { // 处理403错误
      notification.error({
        message: '无权限',
        description: data.message || '您没有权限访问该资源'
      })
    } else if (error.response.status === 401 && !(data.result && data.result.isLogin)) { // 处理401错误
      notification.error({
        message: '未授权',
        description: '授权验证失败，请重新登录'
      })
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
    } else { // 处理其他错误
      notification.error({
        message: '请求错误',
        description: data.message || `请求失败 (${error.response.status})`
      })
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    notification.error({
      message: '网络错误',
      description: '服务器无响应，请检查网络连接'
    })
  } else {
    // 请求配置错误
    notification.error({
      message: '请求错误',
      description: error.message
    })
  }

  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  // 尝试直接从localStorage获取token
  const token = localStorage.getItem(ACCESS_TOKEN)

  // 如果 token 存在，使用Bearer认证方式
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }

  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use((response) => {
  // 如果响应是登录成功，确保存储token
  if (response.data && response.data.status === 'success' && response.data.code === 200 && response.config.url.includes('login')) {
    const token = response.data.data.access_token
    if (token) {
      storage.set(ACCESS_TOKEN, token)
      localStorage.setItem(ACCESS_TOKEN, token)
    }
  }
  return response.data
}, errorHandler)

const installer = {
  vm: {},
  install (Vue) {
    Vue.use(VueAxios, request)
  }
}

export default request

export {
  installer as VueAxios,
  request as axios
}

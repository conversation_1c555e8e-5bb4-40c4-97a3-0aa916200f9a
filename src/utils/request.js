import axios from 'axios'
import router from '@/router'
import { Token } from '@/utils/token'
import { Notification } from 'element-ui'
import store from '@/store'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 10000 // request timeout
})

// 创建不同服务的请求实例
export const createServiceRequest = (servicePath) => {
  const serviceInstance = axios.create({
    baseURL: process.env.VUE_APP_BASE_HOST + '/' + servicePath, // 基础URL + 服务路径
    timeout: 10000
  })

  // 复用相同的请求拦截器
  serviceInstance.interceptors.request.use(
    (config) => {
      config.headers['Authorization'] = 'Bearer ' + Token.get()
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 复用相同的响应拦截器
  serviceInstance.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      console.error('请求错误:', error)
      // 复用相同的错误处理逻辑
      if (error.code === 'ECONNABORTED') {
        Notification.error({
          title: '请求失败',
          message: '请求超时,请检查网络'
        })
        return Promise.reject(error)
      }
      
      // 处理401未授权错误，无论响应数据格式如何
      if (error.response && error.response.status === 401) {
        console.log('createServiceRequest interceptor: Detected 401 Error')
        // 清除Token
        Token.clear()
        // 关闭WebSocket连接
        // store.dispatch('websocket/setClose') // Temporarily comment out to avoid TypeError
        // 显示提示信息
        const errorMsg = error.response.data && error.response.data.message 
          ? error.response.data.message 
          : '登录状态已失效请重新登录！'
        Notification.error({
          title: '登录失效',
          message: errorMsg
        })
        // 重定向到登录页，添加延时确保跳转生效
        setTimeout(() => {
          console.log('createServiceRequest interceptor: Redirecting to /login')
          router.push('/login')
        }, 100)
        return Promise.reject(error)
      }
      
      if (error.response) {
        const errorData = error.response.data
        console.error('错误响应数据:', errorData)
        Notification.error({
          title: '请求失败',
          message: errorData.message || '请求失败，请重试'
        })
      } else {
        Notification.error({
          title: '请求失败',
          message: error.message || '请求失败，请重试'
        })
      }
      return Promise.reject(error)
    }
  )

  return serviceInstance
}

const errMessage = (message) => {
  Notification({
    title: '请求错误',
    message: message,
    type: 'error',
    duration: 2000
  })
}

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent
    config.headers['Authorization'] = 'Bearer ' + Token.get()
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

  /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     * {
     *   data:
     *   msg:
     *   code:
     * }
     */
  (response) => {
    return response.data
  },
  (error) => {
    console.log(error.response)
    if (error.code === 'ECONNABORTED') {
      Notification.error({
        title: '请求失败',
        message: '请求超时,请检查网络'
      })
      return Promise.reject(error)
    }
    
    // 先检查是否401错误，无论响应数据格式如何
    if (error.response && error.response.status === 401) {
      console.log('Default service interceptor: Detected 401 Error')
      // 清除Token
      Token.clear()
      // 关闭WebSocket连接
      // store.dispatch('websocket/setClose') // Temporarily comment out to avoid TypeError
      // 显示提示信息
      const errorMsg = error.response.data && error.response.data.message 
        ? error.response.data.message 
        : '登录状态已失效请重新登录！'
      errMessage(errorMsg)
      // 重定向到登录页
      setTimeout(() => {
        console.log('Default service interceptor: Redirecting to /login')
        router.push('/login')
      }, 100)
      return Promise.reject(error)
    }
    
    // 处理其他错误
    if (error.response && error.response.data && error.response.data.code) {
      switch (error.response.data.code) {
        case 400:
          errMessage(error.response.data.message)
          break
        case 422:
          errMessage(error.response.data.message)
          break
        case 403:
          errMessage('暂无权限访问，请联系运营人员开通。')
          break
        case 404:
          errMessage('请求路径不存在')
          break
        case 500:
          errMessage(error.response.data.message || '请求错误')
          break
      }
    } else if (error.response && error.response.data) {
      errMessage(error.response.data.message || '请求失败，请重试')
    } else {
      errMessage('网络错误，请稍后重试')
    }
    
    return Promise.reject(error)
  }
)

export default service

import routeViewMap from '@/router/routeViewMap'

export function generateTree_ (data, isChild = false) {
  // 递归检查节点及其子节点是否有enabled为true的项
  const hasEnabledChild = (node) => {
    // 如果节点本身是enabled的，直接返回true
    if (node.enabled) {
      return true
    }

    // 检查子菜单
    if (node.sub_menu && node.sub_menu.length > 0) {
      for (const subItem of node.sub_menu) {
        if (hasEnabledChild(subItem)) {
          return true
        }
      }
    }

    // 检查页面
    if (node.page && node.page.length > 0) {
      for (const pageItem of node.page) {
        if (hasEnabledChild(pageItem)) {
          return true
        }
      }
    }

    return false
  }

  // 预处理数据，修改父级菜单的enabled状态
  const processData = (items) => {
    return items.map(item => {
      // 创建一个新对象避免修改原始数据
      const newItem = { ...item }
      
      // 如果该项已经enabled=true，则不需要检查子项
      if (!newItem.enabled) {
        // 检查是否有enabled的子项
        newItem.enabled = hasEnabledChild(newItem)
      }
      
      // 递归处理子菜单
      if (newItem.sub_menu) {
        newItem.sub_menu = processData(newItem.sub_menu)
      }
      
      // 递归处理子页面
      if (newItem.page) {
        newItem.page = processData(newItem.page)
      }
      
      return newItem
    })
  }

  // 处理数据
  const processedData = processData(data)
  
  const authResourceNodes = []
  processedData.map((item, index) => {
    if (item.enabled) {
      const itemRoute = {
        // alwaysShow: !isChild,
        path: index === 0 && !isChild ? '/' : `${isChild ? '' : '/'}${item.permission}`,
        name: item.permission,
        component: isChild ? routeViewMap[item.permission] : routeViewMap.layout,
        meta: { ...item.meta, page: item.page },
        alwaysShow: item.alwaysShow,
        sidebarHidden: item.sidebarHidden
      }
      if (index === 0 && !isChild) {
        itemRoute['redirect'] = `/${item.permission}/index`
        itemRoute['sidebarHidden'] = item.sidebarHidden
      }
      if (!isChild && !item.sub_menu) {
        itemRoute['children'] = [{
          path: 'index',
          alwaysShow: item.alwaysShow,
          component: routeViewMap[item.permission],
          name: item.permission,
          sidebarHidden: item.sidebarHidden,
          meta: { ...item.meta, page: item.page }
        }]
      }
      if (item.sub_menu) {
        itemRoute['children'] = generateTree_(item.sub_menu, true)
      }
      authResourceNodes.push(itemRoute)
    }
  })
  return authResourceNodes
}

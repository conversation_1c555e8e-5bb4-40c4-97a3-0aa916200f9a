function formatAdcode (code) {
  if (code === 110100) {
    return '北京市'
  }
  if (code === 500100) {
    return '重庆市'
  }
  if (code === 120100) {
    return '天津市'
  }
  if (code === 310100) {
    return '上海市'
  }
  return '-'
}

export const formatStartName = (params) => {
  let start_city = ''; let start_name = ''
  switch (params.type) {
    // 拼车
    case 1:
      start_city = !['市辖区', '县'].includes(params?.line?.start_city?.name) ? params?.line?.start_city?.name : params?.line?.start_city?.city?.name || '-'
      start_name = params?.line?.start_name || '-'
      break
    case 2:
    case 7:
      start_city = params?.line_chartered ? (!['市辖区', '县'].includes(params?.line_chartered?.start_city?.name) ? params?.line_chartered?.start_city?.name : params?.line_chartered?.start_city?.city?.name || '-') : [110100, 500100, 120100, 310100].includes(params?.start_gb_region?.city?.address_id) ? formatAdcode(params?.start_gb_region?.city?.address_id) : params?.start_gb_region?.city?.name || '-'
      start_name = params?.line_chartered ? (params?.line_chartered?.start_name || '-') : params?.start_gb_region?.name || '-'
      break
    case 3:
    case 6:
      start_name = params?.start_gb_region?.name
      start_city = [110100, 500100, 120100, 310100].includes(params?.start_gb_region?.city?.address_id) ? formatAdcode(params?.start_gb_region?.city?.address_id) : params?.start_gb_region?.city?.name
      break
    case 5:
      start_city = !['市辖区', '县'].includes(params?.line_class_train?.line_class?.start_city?.name) ? params?.line_class_train?.line_class?.start_city?.name : params?.line_class_train?.line_class?.start_city?.city?.name || '-'
      start_name = params?.line_class_train?.line_class?.start_name || '-'
      break
    case 11:
      start_city = !['市辖区', '县'].includes(params?.line_taxi?.start_city?.name) ? params?.line_taxi?.start_city?.name : params?.line_taxi?.start_city?.city?.name || '-'
      start_name = params?.line_taxi?.start_name || '-'
      break
  }
  return {
    start_city: start_city,
    start_name: start_name
  }
}

export const formatEndName = (params) => {
  let end_city = ''; let end_name = ''
  switch (params.type) {
    // 拼车
    case 1:
      end_city = !['市辖区', '县'].includes(params?.line?.end_city?.name) ? params?.line?.end_city?.name : params?.line?.end_city?.city?.name || '-'
      end_name = params?.line?.end_name || '-'
      break
    case 2:
    case 7:

      end_city = params?.line_chartered ? (!['市辖区', '县'].includes(params?.line_chartered?.end_city?.name) ? params?.line_chartered?.end_city?.name : params?.line_chartered?.end_city?.city?.name || '-') : [110100, 500100, 120100, 310100].includes(params?.end_gb_region?.city?.address_id) ? formatAdcode(params?.end_gb_region?.city?.address_id) : params?.end_gb_region?.city?.name || '-'
      end_name = params?.line_chartered ? (params?.line_chartered?.end_name || '-') : params?.end_gb_region?.name || '-'
      break
    case 3:
    case 6:
      end_city = [110100, 500100, 120100, 310100].includes(params?.end_gb_region?.city?.address_id) ? formatAdcode(params?.end_gb_region?.city?.address_id) : params?.end_gb_region?.city?.name
      end_name = params?.end_gb_region?.name
      break
    case 5:
      end_city = !['市辖区', '县'].includes(params?.line_class_train?.line_class?.end_city?.name) ? params?.line_class_train?.line_class?.end_city?.name : params?.line_class_train?.line_class?.end_city?.city?.name || '-'
      end_name = params?.line_class_train?.line_class?.end_name || '-'
      break
    case 11:
      end_city = !['市辖区', '县'].includes(params?.line_taxi?.end_city?.name) ? params?.line_taxi?.end_city?.name : params?.line_taxi?.end_city?.city?.name || '-'
      end_name = params?.line_taxi?.end_name || '-'
      break
  }
  return {
    end_city: end_city,
    end_name: end_name
  }
}

/**
 * Created by PanJ<PERSON><PERSON>hen on 16/11/18.
 */
import moment from 'moment'
import Notify from '@wcjiang/notify'
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
            1 +
            '月' +
            d.getDate() +
            '日' +
            d.getHours() +
            '时' +
            d.getMinutes() +
            '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

export function calculaDate(date = null) {
  // 2022-12-10 00
  moment.locale('zh-cn', {
    weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  })
  const array = []
  var oneDay = 24 * 3600 * 1000
  // 生成date日期往后15天数据
  for (let i = 0; i < 15; i++) {
    // moment(date).format('dddd M月DD日')
    // const day = moment(date).format('D')
    const date_ = date ? new Date(date).getTime() : new Date().getTime()
    const day = moment(date_ + oneDay * i).format('MM.DD')
    const week = moment(date_ + oneDay * i).format('dddd')
    const value = moment(date_ + oneDay * i).format('YYYY-MM-DD')
    array.push({ day: day, week: week, value: value })
  }
  return array
}

export function distanceGetTime (time) {
  // 转换为式分秒
  const h = parseInt(time / 60 / 60 % 24)
  const m = parseInt(time / 60 % 60)
  // 作为返回值返回
  return h > 0 ? (h + '小时') : '' + m > 0 ? (m + '分钟') : ''
}

export function shortcutsTime () {
  return [
    {
      text: '今天',
      time: [moment(moment().startOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().endOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    {
      text: '10分钟',
      time: [moment(moment().subtract(10, 'minutes').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    // {
    //   text: '20分钟',
    //   time: [moment(moment().subtract(20, 'minutes').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    // },
    {
      text: '30分钟',
      time: [moment(moment().subtract(30, 'minutes').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    {
      text: '1小时',
      time: [moment(moment().subtract(1, 'hours').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    // {
    //   text: '2小时',
    //   time: [moment(moment().subtract(2, 'hours').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    // },
    {
      text: '5小时',
      time: [moment(moment().subtract(5, 'hours').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    {
      text: '12小时',
      time: [moment(moment().subtract(12, 'hours').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    {
      text: '明天',
      time: [moment(moment().add(1, 'days').startOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().add(1, 'days').endOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    },
    {
      text: '后天',
      time: [moment(moment().add(2, 'days').startOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss'), moment(moment().add(2, 'days').endOf('day').valueOf()).format('YYYY/MM/DD HH:mm:ss')]
    }
  ]
}

// export function playSound () {
//   var borswer = window.navigator.userAgent.toLowerCase()
//   if (borswer.indexOf('ie') >= 0) {
//     // IE内核浏览器
//     // var strEmbed = '<embed name="embedPlay" src="/admin/images/30.wav" autostart="true" hidden="true" loop="false"></embed>'
//     // if ($('body').find('embed').length <= 0) { $('body').append(strEmbed) }
//     // var embed = document.embedPlay
//     // // 浏览器不支持 audion，则使用 embed 播放
//     // embed.volume = 100
//     // embed.play();这个不需要
//   } else {
//     // 非IE内核浏览器
//     var audio = new Audio(require('../assets/new_order_voice.mp3'))
//     audio.play()
//   }
// }

export function noticeview() {
  if (!window.notify) {
    const notify = new Notify({
      message: '您收到了新订单消息', // page title.
      effect: 'flash', // flash | scroll, Flashing or scrolling
      onclick: () => {
        // Click on the pop-up window trip event
        // Programmatically closes a notification.
        notify.close()
      },
      // Optional playback sound
      audio: {
        // You can use arrays to pass sound files in multiple formats.
        file: require('../assets/new_order_voice.mp3')
        // The following is also work.
        // file: 'msg.mp4'
      },
      // Title flashing, or scrolling speed
      interval: 1000,
      disableFavicon: false, // Optional, default false, if true, No longer overwrites the original favicon
      // Optional, default green background white text. Favicon
      updateFavicon: {
        // favicon font color
        textColor: '#fff',
        // Background color, set the background color to be transparent, set the value to "transparent"
        backgroundColor: '#2F9A00'
      },
      // Optional chrome browser notifications，
      // The default is not to fill in the following content
      notification: {
        title: '订单通知', // Set notification title
        icon: '', // Set notification icon, The default is Favicon
        body: '您收到了新订单消息!' // Set message content
      }
    })
    window.notify = notify
  }

  return window.notify
}
// var iNotify = new iNotify().init()
// // 推荐下面写法
// var iNotify = new iNotify({
//   message: '有消息了。', // 标题
//   effect: 'flash', // flash | scroll 闪烁还是滚动
//   openurl: 'http://www.bing.com', // 点击弹窗打开连接地址
//   onclick: function() { // 点击弹出的窗之行事件
//     console.log('---')
//   },
//   // 可选播放声音
//   audio: {
//     // 可以使用数组传多种格式的声音文件
//     file: ['msg.mp4', 'msg.mp3', 'msg.wav']
//     // 下面也是可以的哦
//     // file: 'msg.mp4'
//   },
//   // 标题闪烁，或者滚动速度
//   interval: 1000,
//   // 可选，默认绿底白字的  Favicon
//   updateFavicon: {
//     // favicon 字体颜色
//     textColor: '#fff',
//     // 背景颜色，设置背景颜色透明，将值设置为“transparent”
//     backgroundColor: '#2F9A00'
//   },
//   // 可选chrome浏览器通知，默认不填写就是下面的内容
//   notification: {
//     title: '通知！', // 设置标题
//     icon: '', // 设置图标 icon 默认为 Favicon
//     body: '您来了一条新消息'// 设置消息内容
//   }
// })

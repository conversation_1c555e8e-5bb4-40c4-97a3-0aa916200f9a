import { getToken, removeToken, setToken } from '@/utils/auth'
import store from '@/store'

export const Token = {
  token: null,
  set: function(value) {
    setToken(value)
    this.token = value
    store.commit('user/SET_TOKEN', value)
  },
  get: function() {
    if (this.token == null) {
      this.token = getToken()
    }
    return this.token
  },
  clear: function() {
    removeToken()
    this.token = null
    store.commit('user/SET_TOKEN', null)
  }
}

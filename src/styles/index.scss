@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:  -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
  font-weight: 400;
  background: #eef3fc;
  min-width: 1440px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  margin:0 12px;
  background: #fff;
  padding: 12px;
  border-radius: 4px;
}
.el-menu-item i{
  color: #fff !important;
}
#app .sidebar-container .el-menu-item.is-active:hover{
 background-color: #0067e1  !important
}
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  .left-content {
    display: flex;
    align-items: center;
  }
}

.hanldle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.view-detail {
  color: #5F5F5F;
  line-height: 22px;
  font-size: 15px;
  img {
    max-width: 100%;
    display: block;
    object-fit: contain;
    margin: 10px 0;
  }
  p {
    color: #5F5F5F;
    line-height: 22px;
    font-size: 14px;
  }

}
.component-container {
  margin-top: 24px;
}
.bottom-container {
  .bottom-container-left {
    display: flex;
    justify-content: flex-start;
    font-size: 14px;
    color: #666;
    p {
      margin-right: 24px;
    }
  }
}
// 设置顶部红条
.top-prompt-red {
  background-color: #F56C6C;
  color: #fff;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  padding: 0px 24px;
  margin-bottom: 24px;
}

  .el-table__body {
    width: 100% !important;
    table-layout: fixed !important;
  }

  .el-table th.el-table__cell{
    background-color: #f0f2f5;
  }
  * {
    margin: 0;
    padding: 0;
  }
  // .el-table .el-table__cell {
  //   padding: 0;
  // }
  .fb {
    .cell {
      font-weight: bold !important;
    }
  }

  .block{
    display: block;
  }

  .scale{
    transform: scale(0.85);
    margin-left:-5px !important;
  }

  .cover_url_content {
    .el-upload-dragger {
      width: auto;
      height: auto;
    }
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
  }


  .avatar {
    width: 108px;
    height: 108px;
    object-fit: contain;
    display: block;
  }

  .el-tag {
    margin: 3px;
  }

  .el-descriptions{
    margin-bottom: 20px;
  }
  .fb {
    font-weight: bold;
  }

  .c2e {
    color: #2e2e2e;
  }

  .fz16 {
    font-size: 16px;
  }

  .fz18 {
    font-size: 18px;
  }

  .fz17 {
    font-size: 17px;
  }

  .fz14 {
    font-size: 14px;
  }

  .fz15 {
    font-size: 15px;
  }

  .fz13 {
    font-size: 13px;
  }

  .fz12 {
    font-size: 12px;
  }

  .c0c {
    color: #0c0c0c;
  }

  .c2e {
    color: #2e2e2e;
  }

  .c4b {
    color: #4B4B4B;
  }

  .c74 {
    color: #747474;
  }

  .c83 {
    color: #838383;
  }

  .cma {
    color: #0067E1;
  }

  .cursor {
    cursor: pointer;

    i {
      margin-left: 5px;
    }
  }
  .cff {
    color: #FF8033;
  }

  .c12 {
    color: #122F54;
  }


.tc{
  text-align: center;
}
  .fb {
    font-weight: bold;
  }
.ml{
  margin-left: 5px;
}
.mr{
  margin-right: 5px;
}


  //菜单徽标
  .menu-wrapper {
    .el-badge__content.is-fixed {
      position: relative;
      right: 23px;
    }

    .el-badge__content {
      line-height: 16px;
    }
  }

  //重置按钮样式
  .el-button--primary {
    span {
      color: #fff;
    }
  }

  .el-button--primary.is-plain {
    span {
      color: #0067E1;
    }

    &:hover {
      background-color: rgba(0, 103, 225, 0.2);
      color: #0067E1;
      border-color: rgba(0, 103, 225, 0.2);
    }

    &:active {
      background-color: rgba(0, 103, 225, 0.2);
      color: #0067E1;
      border-color: rgba(0, 103, 225, 0.2);
    }

    &:focus {
      background-color: rgba(0, 103, 225, 0.2);
      color: #0067E1;
      border-color: rgba(0, 103, 225, 0.2);
    }
  }



  .el-tooltip__popper {
    max-width: 500px;
    font-size: 13px;
    line-height: 18px;
  }

  .avatar-uploader {
    .el-upload {
      //   border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #409EFF;
      }
    }
  }

  .avatar-uploader {
    .el-upload:hover {
      border-color: #409eff;
    }
  }


  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 108px;
    height: 108px;
    line-height: 108px;
    text-align: center;
  }



  .el-upload-list__item-thumbnail {
    object-fit: contain;
  }

  .cutmore {
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .two-line {
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .filter-container {
    padding: 0 0 12px 0;
    display: flex;
    align-items: center;

    button {
      margin: 0 10px;
      margin-right: 0;
    }
  }

  .condition {
    width: 220px;
    margin-left: 10px;

  }

  .query-select {
    width: 100px;
    margin-left: 10px;
  }

  .query-select-long {
    width: 100px;
    margin-left: 10px;
  }

  .daterange-condition {
    width: 260px !important;
    margin-left: 10px;
  }

  .mini-condition {
    margin-left: 10px;
    width: 165px;
  }
  .el-textarea__inner {
    padding: 5px 15px 20px 15px;
  }

  .el-textarea .el-input__count {
    background: inherit;
    bottom: -7px;
  }


  .el-table thead tr th {
    background-color: #f5f7fa;
    color: #464646;
  }

  .el-button--text {
    font-size: 14px;
  }

  //table form 
  .el-table__body .el-button--text span {
    margin-left: 0;
    font-size: 12px;
  }
  .el-table {
    // font-size: 13px;
    thead {
      tr {
        th {
          background-color: #f5f7fa;
          color: #464646;
          padding: 5px 0;
          height: 50px;

          .cell {
            font-weight: bold;
            font-size: 13px;

          }
        }
      }
    }

    .cell {
      line-height: 18px;
      font-weight: 400;
      font-size: 13px;
    }

    td {
      padding: 0;
      overflow-y: hidden;
      height: 45px;
      padding: 3px 0;
    }
  }

  .isFirst>.cell {
    padding-left: 24px !important;
  }

  .el-table__empty-block {
    width: inherit !important;
  }

  .el-button--primary.is-plain {
    background: #fff;
  }

  .addInputLength {
    width: 350px;
  }
  .formItemWarp{
    width: 230px;

  }

  .baseflex {
    display: flex;
    align-items: center;
    padding: 8px 0;
  }

  .plan-rules {
    line-height: 20px;
  }

  .numberInputLength {
    width: 250px;
  }

  .persentInput {
    width: 120px;
  }

  .rechargeInput {
    margin-left: 20px !important;
  }

  .el-tooltip__popper {
    max-width: 500px;
  }

  .el-page-header__title {
    color: #606266;
  }

  .el-icon-back {
    color: #606266;
  }

  .el-button [class*=el-icon-]+span {
    margin-left: 3px;
  }

  //日期组件样式修改
  .el-date-table td.available:hover span {
    background: #50a8fa;
    color: #fff;
    border-radius: 3px;
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    border-radius: 3px;
  }

  //dialog样式
  .el-dialog__title {
    font-weight: 400;
    color: #8b96a6;
    font-size: 17px;
  }

  .el-dialog__header {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }

  .el-dialog {
    padding: 0 20px;
  }

  .el-dialog__headerbtn {
    top: 15px;
  }

  .el-dialog .el-form-item__label {
    color: #8b96a6;
  }

  .el-dialog label {
    font-weight: 500;
  }

  .el-dialog .el-button--default {
    background: #eef3fc;
    color: #8b96a6;
  }

  //确认弹窗样式
  .el-message-box__header {
    padding: 10px 0;
    margin: 0 10px;
    border-bottom: 1px solid #eee;
  }

  .el-message-box__title {
    font-weight: 400;
    color: #8b96a6;
    font-size: 17px;
  }

  .el-message-box__headerbtn {
    top: 10px;
  }

  //优化搜索按钮间距
  .rightPancel {
    margin-left: 10px;
    margin-right: 0;
  }

  .el-cascader-panel .el-radio {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .el-cascader-panel .el-radio__input {
    visibility: hidden;
  }

  /* 这个样式针对IE有用，不考虑IE的可以不用管*/
  .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
  }

  .bottom-container {
    .el-button--text {
      margin-right: 10px;
    }
  }

  .el-table-column--selection>.cell {
    padding-left: 24px !important;
  }

  .isFirst>.cell {
    padding-left: 24px !important;
    font-weight: bold;
    color: #2e2e2e;

  }


  .pic_mind {
    font-size: 12px;
    line-height: 15px;
    margin: 0;
    color: #999;
  }

  .error-mind {
    font-size: 13px;
    line-height: 15px;
    margin: 0;
    margin-top: 10px;
    color: rgba(247, 82, 70, 1);
  }


  .official-dot {
    margin-left: 10px;
    border-radius: 50%;
    text-align: center;
    font-size: 12px;
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    transform-origin: 100% 50%;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    line-height: 16px;
  }

  .official-dot:hover {
    background-color: #c0c4cc;
    color: #fff;
  }

  .tab-slot {
    padding: 5px 0;
    display: inline-block;
    vertical-align: middle;

    p {
      padding: 0;
      margin: 0;
    }
  }

  .list-mind {
    font-size: 13px;
    line-height: 15px;
    margin: 0;
    // margin-top: 10px;
    color: #838383;
    text-align: center;
    padding: 15px 0;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 997;
    width: 100%;
    overflow: hidden;
    left: 216px;
    width: calc(100% - 216px);
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
    transition: all .2s;
  }

  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .table-inline {
    thead tr th {
      color: #464646;
      padding: 3px 0;
    }
  }

.label-key{
  font-size: 13px;
  color: #8B96A6;
  font-weight: 400;
}


  /*css主要部分的样式*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    // width: 8px;
    /*对垂直流动条有效*/
    // height: 10px;
    /*对水平流动条有效*/
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  ::-webkit-scrollbar-track {
    // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .05);
    // background-color: #fff;
    // border-radius: 3px;
  }

  /*定义滑块颜色、内阴影及圆角*/
  //   ::-webkit-scrollbar-thumb {
  //     border-radius: 7px;
  //     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  //     background-color: #E8E8E8;
  //   }

  .switchStyle{

  .el-switch__label * {
    line-height: 1;
    font-size: 12px;
    display: inline-block;
  }
 .el-switch__label {
    position: absolute;
    display: none;
    color: #fff !important;
    font-size: 12px !important;
  }
  /*打开时文字位置设置*/
.el-switch__label--right {
    z-index: 1;
  }
  /*关闭时文字位置设置*/
.el-switch__label--left {
    z-index: 1;
    left: 20px;
  }
  /*显示文字*/
.el-switch__label.is-active {
    display: block;
  }
   /*开关宽度*/
.el-switch .el-switch__core,
  .el-switch .el-switch__label {
    width: 60px !important;
  }
}

.from-container{
    .el-form{
      .item-warp-form-content{
        display: flex;
        flex-wrap: wrap;
      }
        .el-form-item{
            width: 390px;
            line-height: 36px;
            margin-bottom: 25px;
            .el-form-item__label{
              line-height: 20px;
            }
        }
    }
}

.el-collapse-item__arrow{
    display: none;
}
.el-collapse-item__wrap{
    border-bottom: none;
}
.el-descriptions{
    margin-bottom: 5px;
}
.el-collapse-item__content{
    padding-bottom: 0;
}

//自动移滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 滑块部分
::-webkit-scrollbar-thumb {
  border-radius: 2em;
  background-color: rgba(50, 50, 50, 0.2);
  cursor: pointer;
}
// 辊道部分
::-webkit-scrollbar-track {
  border-radius: 2em;
  background-color: #fff;
}

.el-menu-item{
  span{
    margin-left: 10px;
  }
}
.el-menu-item [class^="el-icon-"]{
  margin-right: 0;
}

.line-table{
  .el-tag--mini{
    // transform: scale(0.9);
    margin: 0;
  }
}
.noPadding{
  .el-dialog__body{
    padding: 0;
  }
  .el-dialog__footer{
    padding: 20px 0;
  }
}


.customcss {
  .cell {
    line-height: 21px;
  }
}

.points-views{
  .amap-marker-label{
    border:none;
  }
}

.order-table-view{
  margin-top: 20px;
}
.flex-end{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.column-flex{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.status-line-top{
  display: flex;
  flex-direction: column;
  align-items: center;
  &::after{
    width: 40px;
    height: 1px;
    content: '';
    display: block;
    background-color: #dcdfe6;
    margin: 4px 0;

  }

}

.refund-text{
  color: #ff5600;
  font-weight: bold;
}

.multiline-table {
  .el-table__cell{
    padding: 0;
  }
}
.multiline-action-table{
  .el-button + .el-button{
    margin-left: 0;
  }
  .el-button {
    margin-right: 10px;
  }
} 

.noLeftPading>.cell{
  padding-left: 0;
}
.customDialog{
  .el-dialog__body{
    padding: 14px 0 0 0;
  }
  .el-dialog__footer{
    margin-top: 20px;
    padding: 14px 0;
  }
}


.no_error_form{
  .el-form-item{
    margin-bottom: 5px;
  }
 .el-form-item__error{
  display: none;
}}

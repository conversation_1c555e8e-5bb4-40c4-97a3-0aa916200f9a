// sidebar
$menuText:#f4f4f5;
$menuActiveText:#f4f4f5;
$subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951
$menuBg:rgb(36, 37, 37);
$menuHover:#113545;

$subMenuBg:#fff;
$subMenuHover:#113545;
$menuAcitiveBg:#0067e1;
$sideBarWidth: 216px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuAcitiveBg: $menuAcitiveBg;
}

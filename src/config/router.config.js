// eslint-disable-next-line
import { UserLayout, BasicLayout, BlankLayout } from '@/layouts'
// import { bxAnaalyse } from '@/core/icons'

const RouteView = {
  name: 'RouteView',
  render: h => h('router-view')
}

export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    meta: { title: 'menu.home' },
    redirect: '/merchant/list',
    children: [
      // 商户管理
      {
        path: '/merchant',
        name: 'merchant',
        component: RouteView,
        redirect: '/merchant/list',
        meta: { title: '商户管理', icon: 'shop', permission: ['merchant'] },
        children: [
          {
            path: '/merchant/list',
            name: 'MerchantList',
            component: () => import('@/views/merchant/List'),
            meta: { title: '商户列表', keepAlive: true, permission: ['merchant'] }
          },
          {
            path: '/merchant/detail/:id',
            name: 'MerchantDetail',
            component: () => import('@/views/merchant/Detail'),
            meta: { title: '商户详情', keepAlive: false, permission: ['merchant'] },
            hidden: true
          }
        ]
      }
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      },
      {
        path: 'recover',
        name: 'recover',
        component: undefined
      }
    ]
  },

  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
]

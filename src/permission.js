import router from './router'
import store from './store'
// import { Message } from 'element-ui'
import { resetRouter, publicRoutes } from './router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, setToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()

  // 判断路由地址是否有token参数
  const urlToken = to.query.token
  const queryOrder = to.query.orderNo
  if (urlToken) {
    setToken(urlToken)
    store.commit('user/SET_TOKEN', urlToken)
    store.dispatch('user/getProfiles')
    let item = { path: to.path }
    if (queryOrder) {
      item = { path: to.path, query: { orderNo: queryOrder }}
    }
    next(item)
  } else {
    if (hasToken) {
      if (to.path === '/login') {
        // if is logged in, redirect to the home page
        next({ path: '/' })
        NProgress.done()
      } else {
        // 安全检查，确保 roles 存在
        const roles = store.getters.roles || []
        // console.log(store.getters.roles)
        if (roles.length === 0) {
          // 恢复loginType，确保登录类型正确
          const savedLoginType = localStorage.getItem('loginType')
          if (savedLoginType) {
            store.commit('user/SET_LOGIN_TYPE', savedLoginType)
          }

          // 判断当前用户是否已拉取完权限信息
          store.dispatch('user/getAuthority').then((res) => {
            // 拉取info
            let asyncRoutes = store.getters.roles || []
            console.log(asyncRoutes)
            asyncRoutes = asyncRoutes.concat(publicRoutes) // 手动加入404页面路由防止刷新自动进入404
            resetRouter()
            router.addRoutes(asyncRoutes) // 动态添加可访问路由表
            console.log(to)

            // 安全检查，确保 roles[0] 和 roles[0].children 存在
            const hasDefaultRoute = store.getters.roles &&
                                   store.getters.roles[0] &&
                                   store.getters.roles[0].children &&
                                   store.getters.roles[0].children[0]

            const toRoute = to.path === '/' && hasDefaultRoute
              ? { name: store.getters.roles[0].children[0].name }
              : { ...to }

            // 获取用户信息
            store.dispatch('user/getProfiles').then(() => {
              // next()
              next({ ...toRoute, replace: true }) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
            }).catch(err => {
              console.log('获取用户信息失败:', err)
              next({ ...toRoute, replace: true })
            })
          })
            .catch((err) => {
              console.log(err)
            })
        } else {
          // console.log(to.path)
          next() // 当有用户权限的时候，说明所有可访问路由已生成 如访问没权限的全面会自动进入404页面
          NProgress.done()
          // }
        }
      }
    } else {
      /* has no token*/

      if (whiteList.indexOf(to.path) !== -1) {
        // in the free login whitelist, go directly
        next()
      } else {
        // other pages that do not have permission to access are redirected to the login page.
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})

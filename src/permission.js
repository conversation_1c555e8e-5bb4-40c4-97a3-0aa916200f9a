import router from './router'
import store from './store'
import storage from 'store'

// import NProgress from 'nprogress' // progress bar
// import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { i18nRender } from '@/locales'

// NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = ['login', 'register', 'registerResult'] // no redirect allowList
const loginRoutePath = '/user/login'
const defaultRoutePath = '/merchant/list'

// 添加全局请求计数器防止无限循环
let getUserInfoAttempts = 0
const MAX_GET_USER_INFO_ATTEMPTS = 1 // 限制只尝试一次

router.beforeEach((to, from, next) => {
  // NProgress.start() // 已移除进度条启动
  to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${i18nRender(to.meta.title)} - ${domTitle}`))

  // 如果目标路径是login且带有redirect参数，记录下来用于调试
  if (to.path === loginRoutePath && to.query.redirect) {
    console.log('重定向到登录页，重定向目标:', to.query.redirect)
  }

  // 优先使用saas模块的token
  const token = storage.get(ACCESS_TOKEN) || store.getters.saasToken || store.getters.token

  console.log('路由守卫检查 - 路径:', to.path, '- token状态:', !!token)

  if (token) {
    if (to.path === loginRoutePath) {
      console.log('已有token，直接跳转到默认页面:', defaultRoutePath)
      next({ path: defaultRoutePath })
      // NProgress.done() // 已移除进度条完成
    } else {
      // 检查用户是否已获取其权限角色
      if (store.getters.roles.length === 0 && getUserInfoAttempts < MAX_GET_USER_INFO_ATTEMPTS) {
        console.log('需要获取用户角色权限，尝试次数:', getUserInfoAttempts + 1)
        getUserInfoAttempts++ // 增加计数器

        // 获取用户信息
        // 注意：在多模块环境下，根据登录类型调用不同的GetInfo
        const getInfoAction = store.getters.saasToken ? 'saas/GetUserInfo' : 'user/GetInfo'
        console.log('使用的GetInfo action:', getInfoAction)

        store.dispatch(getInfoAction).then(res => {
          // 获取用户路由权限
          console.log('获取用户信息成功:', res)
          // 确保roles存在，即使没有角色信息也能正常显示页面
          const roles = res && res.result && res.result.role ? res.result.role : { id: 'admin', name: 'admin', permissionList: ['admin', 'merchant'] }
          console.log('用户角色信息:', roles)

          store.dispatch('GenerateRoutes', { roles }).then(() => {
            // 根据roles权限生成可访问的路由表
            console.log('生成路由表完成:', store.getters.addRouters)

            // 动态添加可访问路由表，使用router.addRoute代替router.addRoutes
            const addRouters = store.getters.addRouters
            addRouters.forEach(route => {
              router.addRoute(route)
            })

            // 请求带有 redirect 重定向时，登录成功后，使用 replace 跳转到该路由
            const redirect = decodeURIComponent(from.query.redirect || to.path)
            console.log('跳转目标:', redirect, '当前路径:', to.path)
            if (to.path === redirect) {
              // 设置replace: true，这样导航就不会留下历史记录
              next({ ...to, replace: true })
            } else {
              // 跳转到目的路由
              next({ path: redirect })
            }
          })
        }).catch((error) => {
          console.error('获取用户信息失败:', error)
          notification.warning({
            message: '警告',
            description: '获取用户信息失败，将使用默认权限继续'
          })

          // 使用默认角色信息继续
          const roles = {
            id: 'admin',
            name: 'admin',
            permissionList: ['admin', 'merchant']
          }

          console.log('使用默认角色信息:', roles)

          // 使用默认角色生成路由
          store.dispatch('GenerateRoutes', { roles }).then(() => {
            console.log('使用默认角色生成路由表完成:', store.getters.addRouters)
            // 动态添加可访问路由表，使用router.addRoute代替router.addRoutes
            const addRouters = store.getters.addRouters
            addRouters.forEach(route => {
              router.addRoute(route)
            })

            // 跳转到商户列表页
            next({ path: defaultRoutePath, replace: true })
          })
        })
      } else {
        // 超过最大尝试次数或已有角色权限
        if (store.getters.roles.length === 0) {
          console.log('已达到最大尝试次数，使用默认权限继续')
          // 使用默认角色信息继续
          const roles = {
            id: 'admin',
            name: 'admin',
            permissionList: ['admin', 'merchant']
          }

          // 设置角色避免再次进入这个检查
          store.commit('user/SET_ROLES', roles)
        }

        console.log('已有角色权限或使用默认角色，直接通过')
        next()
      }
    }
  } else {
    console.log('无token，检查是否在免登录列表:', to.name, allowList.includes(to.name))
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next()
    } else {
      console.log('重定向到登录页')
      next({ path: loginRoutePath, query: { redirect: to.fullPath } })
      // NProgress.done() // 已移除 - if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  // NProgress.done() // 已移除进度条完成
})

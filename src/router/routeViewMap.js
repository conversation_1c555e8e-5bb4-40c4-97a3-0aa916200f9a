/**
 * import时会先把模块编译，使用的时候再异步导入，也就是说路由是支持同步编译，异步导入的
 * webpack4中的 import不支持异步编译
 */

const routeViewMap = {
  layout: () => import('@/layout'), // 基本框架
  pointsMall: () => import('@/views/points'), // 积分商城
  marketingTool: () => import('@/views/marketing-tool'), // 营销工具
  login: () => import('@/views/login'), // 登录
  dataOverview: () => import('@/views/dashboard'), // 数据分析
  appointment: () => import('@/views/business-manange/appointment'), // 代约下单/来电
  dispatching: () => import('@/views/business-manange/dispatching'), // 智能调度中心

  invoiceManage: () => import('@/views/invoice'), // 发票管理
  invoiceLog: () => import('@/views/invoice/invoice-log'), // 发票记录
  invoiceSetting: () => import('@/views/invoice/invoice-setting'), // 发票配置

  vehicleManage: () => import('@/views/car-manange'), // 车辆管理
  facheManage: () => import('@/views/ticket-print'), // 票务打印
  printPreview: () => import('@/views/print-preview'), // 打印预览

  financialManagement: () => import('@/views/financial-management'), // 结算中心
  driverSettlement: () => import('@/views/financial-management/withdrawal/driver-settlement'), // 司机结算管理
  branchUnSettlement: () => import('@/views/financial-management/settlement'), // 分台未结算订单
  branchSettlement: () => import('@/views/financial-management/withdrawal-apply'), // 分台已结算记录
  merchantApproval: () => import('@/views/financial-management/withdrawal/withdrawal-review'), // 总台结算审核

  tarinsManage: () => import('@/views/order-manage/trains'), // 定制客运订单管理
  taxiManage: () => import('@/views/order-manage/taxi'), // 出租车订单管理
  baocheManage: () => import('@/views/order-manage/charter'), // 包车订单管理
  kuiacheManage: () => import('@/views/order-manage/fastCar'), // 快车订单管理
  baiducheManage: () => import('@/views/order-manage/feeCar'), // 摆渡订单管理
  pincheManage: () => import('@/views/order-manage/carpooling'), // 拼车订单管理
  daihuoManage: () => import('@/views/order-manage/delivery'), // 代货订单管理
  daibanManage: () => import('@/views/order-manage/agent'), // 代办订单管理
  coupon: () => import('@/views/marketing/coupon'), // 优惠券管理
  activities: () => import('@/views/marketing/activities'), // 营销活动
  commodity: () => import('@/views/points/commodity'), // 积分商品
  exchange: () => import('@/views/points/exchange'), // 积分兑换
  referral: () => import('@/views/marketing-tool/referral'), // 邀请有礼
  commoditySetting: () => import('@/views/points/commodity-setting'), // 积分配置
  spusTypes: () => import('@/views/points/spus-types'), // 积分配置
  overview: () => import('@/views/points/overview'), // 积分配置
  adminManage: () => import('@/views/admin-manage'), // 账户管理
  accountManagement: () => import('@/views/account-management'), // 账号管理
  dingzhikeyunLineManage: () => import('@/views/lines-manage/trains'), // 定制客运管理
  suggestionManage: () => import('@/views/suggestion-manage/suggestion'), // 投诉建议
  driverPermission: () => import('@/views/driver-management'), // 司机管理
  driver: () => import('@/views/driver-management/driver'), // 司机管理
  driverGroup: () => import('@/views/driver-management/group-management'), // 司机分组管理
  driverLineManage: () => import('@/views/driver-management/lineInfo'), // 司机线路管理
  passengerManage: () => import('@/views/passenger-management'), // 乘客管理
  passenger: () => import('@/views/passenger-management/passenger'), // 乘客管理
  passengerWalletProfile: () =>
    import('@/views/passenger-management/passenger-wallet-profile'), // 乘客钱包
  passengerWalletApply: () =>
    import('@/views/passenger-management/passenger-wallet-apply'), // 乘客钱包申请

  smsManage: () => import('@/views/sms'), // 短信管理
  smsSendRecord: () => import('@/views/sms/sms-send-record'), // 短信记录
  smsConfig: () => import('@/views/sms/sms-config'), // 短信配置

  adManage: () => import('@/views/ad/ad-manage'), // 广告管理
  branchManage: () => import('@/views/branch-management'), // 分台管理
  channelBranch: () => import('@/views/branch-management/channel'), // 渠道分台列表
  lineBranch: () => import('@/views/branch-management/line'), // 线路分台列表

  exportTask: () => import('@/views/export-task') // 导出任务管理
}

export default routeViewMap

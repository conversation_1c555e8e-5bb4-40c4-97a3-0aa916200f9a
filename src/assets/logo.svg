<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A90E2" />
      <stop offset="100%" stop-color="#1976D2" />
    </linearGradient>
  </defs>
  <!-- 外层圆环 -->
  <circle cx="100" cy="100" r="95" fill="url(#gradient)" opacity="0.3" />
  
  <!-- 中间圆环 -->
  <circle cx="100" cy="100" r="80" fill="url(#gradient)" opacity="0.6" />
  
  <!-- 内部主体 C 形 -->
  <path d="M100,30 C148,30 185,67 185,115 C185,163 148,200 100,200 C52,200 15,163 15,115 C15,67 52,30 100,30 Z" fill="url(#gradient)" />
  
  <!-- 内部白色部分 -->
  <path d="M100,55 C134,55 160,81 160,115 C160,149 134,175 100,175 C66,175 40,149 40,115 C40,81 66,55 100,55 Z M100,80 C80,80 65,95 65,115 C65,135 80,150 100,150 C120,150 135,135 135,115 L100,115 L100,80 Z" fill="white" />
</svg>

import store from '@/store'
import storage from 'store'
import {
  ACCESS_TOKEN,
  APP_LANGUAGE,
  TOGGLE_CONTENT_WIDTH,
  TOGGLE_FIXED_HEADER,
  TOGGLE_FIXED_SIDEBAR, TOGGLE_HIDE_HEADER,
  TOG<PERSON><PERSON>_LAYOUT, TOGGLE_NAV_THEME, TOGGLE_WEAK,
  TOGGLE_COLOR, TOGGLE_MULTI_TAB
} from '@/store/mutation-types'
import { printANSI } from '@/utils/screenLog'
import defaultSettings from '@/config/defaultSettings'
import { loadLanguageAsync } from '@/locales'

export default function Initializer () {
  printANSI() // 请自行移除该行.  please remove this line

  store.commit(TOGGLE_LAYOUT, storage.get(TOGGLE_LAYOUT, defaultSettings.layout))
  store.commit(TOGGLE_FIXED_HEADER, storage.get(TOGGLE_FIXED_HEADER, defaultSettings.fixedHeader))
  store.commit(TOG<PERSON><PERSON>_FIXED_SIDEBAR, storage.get(TOG<PERSON><PERSON>_FIXED_SIDEBAR, defaultSettings.fixSiderbar))
  store.commit(TOGGLE_CONTENT_WIDTH, storage.get(TOGGLE_CONTENT_WIDTH, defaultSettings.contentWidth))
  store.commit(TOGGLE_HIDE_HEADER, storage.get(TOGGLE_HIDE_HEADER, defaultSettings.autoHideHeader))
  store.commit(TOGGLE_NAV_THEME, storage.get(TOGGLE_NAV_THEME, defaultSettings.navTheme))
  store.commit(TOGGLE_WEAK, storage.get(TOGGLE_WEAK, defaultSettings.colorWeak))
  store.commit(TOGGLE_COLOR, storage.get(TOGGLE_COLOR, defaultSettings.primaryColor))
  store.commit(TOGGLE_MULTI_TAB, storage.get(TOGGLE_MULTI_TAB, defaultSettings.multiTab))

  // 通过token获取器判断使用哪个模块
  const tokenValue = storage.get(ACCESS_TOKEN)
  if (tokenValue) {
    // 默认使用user模块存储token，也兼容saas模块
    store.commit('user/SET_TOKEN', tokenValue)
    // 同时更新saas模块token，以保持一致性
    store.commit('saas/SET_TOKEN', tokenValue)

    // 主动获取用户信息，解决刷新页面后用户名显示loading的问题
    console.log('检测到token存在，主动获取用户信息')
    store.dispatch('saas/GetUserInfo').catch(err => {
      console.error('初始化时获取用户信息失败:', err)
      // 失败时设置默认用户信息，避免界面一直loading
      store.commit('saas/SET_USER', {
        name: '管理员',
        account: 'admin',
        mchname: '管理员'
      })
    })
  }

  // 先保存中文语言设置到storage
  storage.set(APP_LANGUAGE, 'zh-CN')
  // 然后使用loadLanguageAsync强制加载中文语言包
  loadLanguageAsync('zh-CN').then(() => {
    // 最后通过store分发setLang事件
    store.dispatch('setLang', 'zh-CN')
  })

  // last step
}

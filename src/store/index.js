import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import user from './modules/user'
import saas from './modules/saas'

// default router permission control
// 默认路由模式为静态路由 (router.config.js)
import permission from './modules/static-router'

// dynamic router permission control (Experimental)
// 动态路由模式（api请求后端生成）
// import permission from './modules/async-router'

import getters from './getters'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    saas,
    permission
  },
  state: {},
  mutations: {},
  actions: {
    // 全局统一的登出处理
    Logout ({ dispatch }) {
      console.log('全局登出操作开始')

      // 依次调用各模块的登出处理
      return dispatch('user/Logout').then(() => {
        console.log('用户模块登出完成')
        return dispatch('saas/Logout')
      }).then(() => {
        console.log('SaaS模块登出完成')
        return Promise.resolve()
      }).catch(err => {
        console.error('登出过程中发生错误:', err)
        // 出错也视为登出成功，让页面能正常跳转
        return Promise.resolve()
      })
    }
  },
  getters
})

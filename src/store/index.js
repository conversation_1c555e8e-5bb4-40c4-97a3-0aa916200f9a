import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import websocket from './modules/websocket'
import tagsView from './modules/tagsView'
import user from './modules/user'
import router from './modules/router'
import unread from '@/store/modules/unread'
import line from '@/store/modules/line'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    websocket,
    tagsView,
    user,
    router,
    unread,
    line
  },
  getters
})

export default store

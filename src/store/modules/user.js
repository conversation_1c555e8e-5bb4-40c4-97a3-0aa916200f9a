import storage from 'store'
import expirePlugin from 'store/plugins/expire'
import { login as saasLogin, getCurrentUser } from '@/api/saas'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { welcome } from '@/utils/util'
import router from '@/router'

storage.addPlugin(expirePlugin)
const user = {
  namespaced: true,
  state: {
    token: '',
    name: '',
    welcome: '',
    avatar: '',
    roles: [],
    info: {}
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, { name, welcome }) => {
      state.name = name
      state.welcome = welcome
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_INFO: (state, info) => {
      state.info = info
    }
  },

  actions: {
    // 登录
    Login ({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        saasLogin(userInfo).then(response => {
          if (response.status === 'success' && response.code === 200) {
            const data = response.data
            // 使用storage库存储token
            storage.set(ACCESS_TOKEN, data.access_token, new Date().getTime() + data.expires_in * 60 * 1000)
            // 同时直接存入localStorage作为备份
            localStorage.setItem(ACCESS_TOKEN, data.access_token)

            console.log('登录成功，保存token:', data.access_token)
            console.log('存储库中的token:', storage.get(ACCESS_TOKEN))
            console.log('localStorage中的token:', localStorage.getItem(ACCESS_TOKEN))

            commit('SET_TOKEN', data.access_token)
            resolve(data)
          } else {
            reject(new Error(response.message || '登录失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo ({ commit }) {
      return new Promise((resolve, reject) => {
        // 使用saas.js中的getCurrentUser方法获取用户信息
        getCurrentUser().then(response => {
          console.log('GetInfo响应:', response)

          try {
            // 尝试从响应中提取用户数据
            const userData = response.data || {}

            // 创建角色结构
            const role = {
              id: 'admin',
              name: '管理员',
              permissions: [
                {
                  permissionId: 'admin',
                  permissionName: '管理员权限',
                  actionList: ['add', 'edit', 'delete', 'query']
                },
                {
                  permissionId: 'merchant',
                  permissionName: '商户管理',
                  actionList: ['add', 'edit', 'delete', 'query']
                }
              ],
              permissionList: ['admin', 'merchant']
            }

            // 尝试获取用户名，使用多种可能的字段
            let userName = '管理员'
            if (userData.user) {
              userName = userData.user.account || userData.user.mchname || userData.user.name || '管理员'
            } else {
              userName = userData.account || userData.mchname || userData.name || '管理员'
            }

            const result = {
              role: role,
              name: userName,
              avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
              userData
            }

            commit('SET_ROLES', role)
            commit('SET_INFO', result)
            commit('SET_NAME', { name: result.name, welcome: welcome() })
            commit('SET_AVATAR', result.avatar)
            resolve(result)
          } catch (err) {
            console.error('处理用户信息时出错:', err)
            // 使用默认值
            const role = {
              id: 'admin',
              name: '管理员',
              permissions: [
                {
                  permissionId: 'admin',
                  permissionName: '管理员权限',
                  actionList: ['add', 'edit', 'delete', 'query']
                },
                {
                  permissionId: 'merchant',
                  permissionName: '商户管理',
                  actionList: ['add', 'edit', 'delete', 'query']
                }
              ],
              permissionList: ['admin', 'merchant']
            }

            const result = {
              role: role,
              name: '管理员',
              avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
            }

            commit('SET_ROLES', role)
            commit('SET_INFO', result)
            commit('SET_NAME', { name: result.name, welcome: welcome() })
            commit('SET_AVATAR', result.avatar)
            resolve(result)
          }
        }).catch(error => {
          console.error('获取用户信息API请求失败:', error)
          // 即使API请求失败，也提供默认用户信息
          const role = {
            id: 'admin',
            name: '管理员',
            permissions: [
              {
                permissionId: 'admin',
                permissionName: '管理员权限',
                actionList: ['add', 'edit', 'delete', 'query']
              },
              {
                permissionId: 'merchant',
                permissionName: '商户管理',
                actionList: ['add', 'edit', 'delete', 'query']
              }
            ],
            permissionList: ['admin', 'merchant']
          }

          const result = {
            role: role,
            name: '管理员',
            avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
          }

          commit('SET_ROLES', role)
          commit('SET_INFO', result)
          commit('SET_NAME', { name: result.name, welcome: welcome() })
          commit('SET_AVATAR', result.avatar)
          resolve(result)
        })
      })
    },

    // 登出
    Logout ({ commit, state }) {
      console.log('USER模块登出处理开始')

      // 获取当前token，用于登出请求
      const token = localStorage.getItem(ACCESS_TOKEN)
      console.log('USER模块登出前检查token:', token ? '存在' : '不存在')

      if (!token) {
        console.log('没有token，直接返回无需调用API')
        // 如果没有token，直接清理并返回
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_INFO', {})
        storage.remove(ACCESS_TOKEN)
        localStorage.removeItem(ACCESS_TOKEN)
        return Promise.resolve()
      }

      return new Promise((resolve) => {
        console.log('准备登出，调用API:', process.env.VUE_APP_API_HOST + process.env.VUE_APP_API_BASE_URL + '/api/admin/saas/logout')

        // 先记录一个临时变量保存token状态
        let hadLoggedOut = false

        // 配置登出请求头
        const logoutHeaders = {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }

        // 使用fetch直接发送请求
        fetch('/account-bin/api/admin/saas/logout', {
          method: 'POST',
          headers: logoutHeaders
        })
        .then(response => {
          console.log('USER模块登出请求响应状态:', response.status)

          // 无论响应如何，都清除本地状态
          if (!hadLoggedOut) {
            hadLoggedOut = true
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            commit('SET_INFO', {})
            storage.remove(ACCESS_TOKEN)
            localStorage.removeItem(ACCESS_TOKEN)

            // 尝试重置路由
            try {
              if (router && router.resetRouter) {
                router.resetRouter()
                console.log('USER模块重置路由成功')
              }
            } catch (e) {
              console.error('USER模块重置路由失败:', e)
            }
          }

          resolve()
        })
        .catch(error => {
          console.error('USER模块登出API调用失败:', error)

          // 确保只清除一次
          if (!hadLoggedOut) {
            hadLoggedOut = true
            // 即使登出失败，也清除本地token
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            commit('SET_INFO', {})
            storage.remove(ACCESS_TOKEN)
            localStorage.removeItem(ACCESS_TOKEN)
          }

          resolve()
        })
      })
    }

  }
}

export default user

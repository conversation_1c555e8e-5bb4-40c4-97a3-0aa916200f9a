import { login, logout, getProfiles as fetchMerchantProfiles, getAccountPermissionRules } from '@/api/user'
import { branchLogin, branchLogout, getBranchInfo as fetchBranchProfiles } from '@/api/branch'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { generateTree_ } from '@/utils/authResources'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    setting: null,
    token: getToken(),
    name: '',
    avatar: '',
    info: null,
    permissions: [],
    roles: [],
    function_permission_definition: null,
    loginType: localStorage.getItem('loginType') || 'merchant' // 登录类型：merchant 或 branch
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_ROLE: (state, role) => {
    state.roles = role
  },
  SET_Info: (state, info) => {
    state.info = info
  },
  set_definition: (state, permission) => {
    state.function_permission_definition = permission
  },
  set_setting: (state, setting) => {
    state.setting = setting
  },
  SET_LOGIN_TYPE: (state, loginType) => {
    state.loginType = loginType
    localStorage.setItem('loginType', loginType)
  }
}

const actions = {
  // user login
  login({ dispatch, commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then((response) => {
          const { data } = response
          commit('SET_TOKEN', data.token)
          commit('SET_LOGIN_TYPE', 'merchant')
          setToken(data.token)
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // 分台账号登录
  branchLogin({ dispatch, commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      branchLogin({ username: username.trim(), password: password })
        .then((response) => {
          const { data } = response
          commit('SET_TOKEN', data.token)
          commit('SET_LOGIN_TYPE', 'branch')
          setToken(data.token)
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  getAuthority({ dispatch, commit }, state) {
    return new Promise((resolve, reject) => {
      // 先检查并确保loginType正确
      const savedLoginType = localStorage.getItem('loginType')
      if (savedLoginType) {
        commit('SET_LOGIN_TYPE', savedLoginType)
      }

      getAccountPermissionRules()
        .then((response) => {
          const { data } = response
          if (!data) {
            return reject('Verification failed, please Login again.')
          }
          commit('set_definition', data?.PERMISSION?.function_permission_definition)
          commit('set_setting', data)
          const asyncroutes = generateTree_(data?.PERMISSION?.menu_permissions_definition)
          commit('SET_ROLE', asyncroutes)
          resolve(asyncroutes)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getProfiles({ commit, dispatch, state }) {
    return new Promise((resolve, reject) => {
      // 根据登录类型选择正确的获取用户信息API函数
      const fetchInfoApi = state.loginType === 'branch' ? fetchBranchProfiles : fetchMerchantProfiles;

      // 调用选定的API函数获取用户信息
      // API函数通常通过拦截器自动处理token，无需显式传递
      fetchInfoApi()
        .then((response) => {
          const { data } = response
          if (!data) {
            return reject('Verification failed, please Login again.')
          }
          // 提交获取到的用户信息（确保包含 branch_id 给 websocket 使用）
          commit('SET_Info', data)

          // 用户信息已更新，现在可以触发 WebSocket 连接
          // connectSocket 内部会使用 getToken() 获取 token
          dispatch('websocket/connectSocket', null, { root: true })

          // 假设总台和分台接口返回的数据中都有 admin_id 用于获取未读数
          // 如果分台返回的ID字段不同（例如 branch_admin_id），请相应调整
          const userIdForUnread = data.admin_id;
          dispatch('unread/getOrderCount', userIdForUnread, { root: true })
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 根据登录类型选择不同的登出API
      const logoutApi = state.loginType === 'branch' ? branchLogout : logout

      logoutApi(state.token)
        .then(() => {
          removeToken() // must remove token first
          localStorage.removeItem('loginType') // 清除登录类型
          resetRouter()
          commit('RESET_STATE')
          commit('SET_ROLE', [])
          resolve()
        })
        .catch((error) => {
          console.error('登出API调用失败:', error)
          // 即使API调用失败，也要清除本地token并重置状态
          removeToken()
          localStorage.removeItem('loginType')
          resetRouter()
          commit('RESET_STATE')
          commit('SET_ROLE', [])
          resolve() // 这里改为resolve而不是reject，确保前端可以正常退出
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove token first
      localStorage.removeItem('loginType') // 清除登录类型
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

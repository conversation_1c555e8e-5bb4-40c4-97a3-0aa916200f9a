import { getToken } from '@/utils/auth'
import { noticeview } from '@/utils'
// 未使用的引入已注释掉
// import { Notification } from 'element-ui'
import store from '@/store'

// import Notify from 'title-notify'
const state = {
  isMtClose: false,
  local_num: null,
  ws_heart: '', // ws心跳定时器
  lockReconnect: false, // 是否真正建立连接
  timeoutnum: null, // 断开 重连倒计时
  reconnectAttempts: 0, // 重连次数
  isConnected: false // WebSocket连接状态
}
const mutations = {
  unReadSysMsg(state, options) {
    state.readSysMsgList = options
  },
  unReadSysMsglenght(state, options) {
    state.readSysMsgListLength = options
  },
  setLocalNum(state, obj) {
    state.local_num = obj
  },
  setConnectionStatus(state, status) {
    state.isConnected = status
  }
}
const actions = {
  connectSocket({ state, commit, rootState, dispatch }) {
    console.log('state', state)
    const admin_id = rootState.user.info.admin_id
    const local_state = localStorage.getItem(admin_id)
    const init_ = local_state ? JSON.parse(local_state) : null
    commit('setLocalNum', init_)

    // 先判断浏览器是否支持WebSocket
    if (typeof WebSocket === 'undefined') {
      console.log('您的浏览器不支持socket')
      return
    }

    // 检查token是否存在且有效
    const token = getToken()
    if (!token) {
      console.warn('WebSocket连接失败: 缺少有效token')
      return
    }

    // 提前判断 WebSocket是否已经建立，避免重复连接问题
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接，跳过重复连接')
      return
    }

    if (this.socket) {
      this.socket.close()
      this.socket = null
    }

    try {
      // 实例化socket
      // 根据用户类型确定 user_type 参数
      const userInfo = rootState.user.info || {}
      console.log('WebSocket Debug - User Info:', userInfo)
      const userType = userInfo.group_id === 3 ? 'branch' : 'mch'
      console.log('WebSocket Debug - Determined User Type:', userType)
      console.log('WebSocket Debug - Token:', token)

      const wsUrl = `${process.env.VUE_APP_WS_HOST}/ws?token=${token}&user_type=${userType}`
      console.log('WebSocket Debug - Connecting to URL:', wsUrl)

      this.socket = new WebSocket(wsUrl)

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
          console.warn('WebSocket连接超时，关闭连接')
          this.socket.close()
        }
      }, 10000) // 10秒超时

      this.socket.onopen = () => {
        clearTimeout(connectionTimeout)
        console.log('websocket已连接')
        // 重置重连次数
        state.reconnectAttempts = 0
        // 更新连接状态
        commit('setConnectionStatus', true)
        dispatch('reset')
      }

      this.socket.onmessage = (msg) => {
        console.log('WebSocket Raw Message Received:', msg.data)

        // 尝试解析 JSON
        let parsedData
        try {
          parsedData = JSON.parse(msg.data)
        } catch (e) {
          console.error('WebSocket Error: Failed to parse message JSON:', e, msg.data)
          return
        }

        console.log('WebSocket Parsed Data:', parsedData)

        // 后续处理逻辑
        const { isNotification } = rootState.settings
        if (!isNotification) {
          console.log('WebSocket Info: Notification setting is off.')
          return
        }

        if (parsedData.data && parsedData.data.event === 'fresh-order') {
          console.log('WebSocket Info: Processing fresh-order event.')
          store.dispatch('unread/getOrderCount', admin_id || '')

          if (window.getOrderList) {
            window.getOrderList(false)
          }

          let unread = local_state ? Number(JSON.parse(local_state)?.num) : 0
          unread += 1
          localStorage.setItem(admin_id, JSON.stringify({ num: unread, isread: false }))
          commit('setLocalNum', { num: unread, isread: false })

          noticeview().player()
          noticeview().setTitle(true)
          noticeview().notify({
            title: 'cc招车管理系统',
            body: '您收到了新订单消息,请立即处理',
            icon: require('../../assets/favicon.png'),
            onclick: function () {
              console.log('on click')
              window.focus()
            },
            onshow: function () {
              console.log('on show')
            }
          })
        }

        dispatch('reset')
      }

      this.socket.onclose = function (e) {
        clearTimeout(connectionTimeout)
        console.log('WebSocket连接关闭，代码:', e.code, '原因:', e.reason)

        // 更新连接状态
        commit('setConnectionStatus', false)

        // 检查关闭原因，避免无效token导致的无限重连
        if (e.code === 1006 || e.code === 1011 || e.code === 1008) {
          console.warn('WebSocket连接因认证或服务器错误关闭，不进行重连')
          return
        }

        if (!state.isMtClose) {
          dispatch('reconnect')
        }
      }

      this.socket.onerror = function (e) {
        clearTimeout(connectionTimeout)
        console.error('WebSocket发生错误:', e)
        // 更新连接状态
        commit('setConnectionStatus', false)
        dispatch('reconnect')
      }
    } catch (error) {
      console.error('WebSocket初始化失败:', error)
    }
  },
  setClose({ state }) {
    var that = this
    state.isMtClose = true

    // 清理所有定时器
    if (that.ws_heart) {
      clearInterval(that.ws_heart)
      that.ws_heart = null
    }

    if (that.timeoutnum) {
      clearTimeout(that.timeoutnum)
      that.timeoutnum = null
    }

    // 关闭WebSocket连接
    if (that.socket) {
      that.socket.close()
      that.socket = null
    }

    console.log('WebSocket已关闭')
  },
  // 重新连接（断开跟错误后都需要进行重连操作）
  reconnect({ dispatch, state }) {
    var that = this

    // 检查重连次数限制
    if (state.reconnectAttempts >= 5) {
      console.warn('WebSocket重连次数达到上限，停止重连')
      return
    }

    if (that.lockReconnect) {
      return
    }

    that.lockReconnect = true
    state.reconnectAttempts += 1

    // 清理旧的重连定时器
    if (that.timeoutnum) {
      clearTimeout(that.timeoutnum)
    }

    // 计算重连延迟（递增延迟）
    const delay = Math.min(5000 * state.reconnectAttempts, 60000) // 最大60秒

    console.log(`WebSocket重连中... (第${state.reconnectAttempts}次，${delay / 1000}秒后重试)`)

    that.timeoutnum = setTimeout(function () {
      dispatch('connectSocket')
      that.lockReconnect = false
    }, delay)
  },
  // 建立连接及有新消息接收后进行心跳重置
  reset({ dispatch, state }) {
    var that = this
    // 清除旧的心跳定时器
    if (that.ws_heart) {
      clearInterval(that.ws_heart)
      that.ws_heart = null
    }
    // 重启心跳
    dispatch('start')
  },
  // 心跳检测
  start({ dispatch }) {
    // 检查WebSocket连接状态
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，跳过心跳检测')
      return
    }

    // 实时推送ping消息，查看连接是否断开
    this.ws_heart = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        try {
          const actions = JSON.stringify({ event: 'heartbeat' })
          this.socket.send(actions)
        } catch (error) {
          console.error('心跳检测发送失败:', error)
          clearInterval(this.ws_heart)
          this.ws_heart = null
        }
      } else {
        console.warn('WebSocket连接已断开，停止心跳检测')
        clearInterval(this.ws_heart)
        this.ws_heart = null
      }
    }, 30000)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

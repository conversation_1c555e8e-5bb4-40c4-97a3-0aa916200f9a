import { getInvoiceTotal } from '@/api/systemSetup'
import {
  numbers
} from '@/api/business'

const state = {
  unread: {
    INVOICE_PENDING: 0,
    ORDER_PENDING: 0,
    timeoutCount: 0
  }
}

const mutations = {
  update: (state, value) => {
    console.log(value)
    state.unread.INVOICE_PENDING = value.fapiao_no_handle
    // console.log('status[unread.pending]', 'update', value)
  },
  SET_ORDERS: (state, count) => {
    state.unread.ORDER_PENDING = count
  },
  SET_TIMEOUT: (state, count) => {
    state.unread.timeoutCount = count
  }

}

const actions = {
  refresh(context) {
    // getInvoiceTotal().then((response) => {
    //   console.log('response', response)
    //   context.commit('update', response.data)
    // })
  },
  async getOrderCount(context, query) {
    const { data } = await numbers({
      branch_id: query.branch_id || '',
      order_type: query.order_type || [],
      sub_business_type: query.sub_business_type || '',
      start_time: query.start_time || '',
      is_timeout: query.is_timeout || false,
      search_key: query.search_key || '',
      search_value: query.search_value || '',
      biz: query.biz || 'undispatched'
    })
    context.commit('SET_ORDERS', data.total_orders)
    context.commit('SET_TIMEOUT', data.timeout_orders)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

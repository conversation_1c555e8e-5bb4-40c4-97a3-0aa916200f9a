import { schedulesCityCode, timetables } from '@/api'

// 初始状态
const state = {
  // 出发地选项
  startCode: [],
  // 目的地选项
  endCode: [],
  // 时刻表列表
  timetablesList: [],
  // 分台ID
  branchId: '',
  // 加载状态
  loading: {
    startCode: false,
    endCode: false,
    timetables: false
  }
}

// 同步修改状态的方法
const mutations = {
  SET_START_CODE: (state, data) => {
    state.startCode = data
  },
  SET_END_CODE: (state, data) => {
    state.endCode = data
  },
  SET_TIMETABLES_LIST: (state, data) => {
    state.timetablesList = data
  },
  SET_BRANCH_ID: (state, id) => {
    state.branchId = id
  },
  SET_LOADING: (state, { type, value }) => {
    state.loading[type] = value
  }
}

// 异步操作方法
const actions = {
  // 设置分台ID
  setBranchId({ commit, dispatch }, branchId) {
    commit('SET_BRANCH_ID', branchId)
    dispatch('resetAll') // 重置所有数据
    dispatch('fetchStartCode')
    dispatch('fetchEndCode')
  },

  // 重置状态
  resetAll({ commit }) {
    commit('SET_START_CODE', [])
    commit('SET_END_CODE', [])
    commit('SET_TIMETABLES_LIST', [])
    // 根据需要可以添加其他需要重置的状态
  },

  // 获取出发地选项
  async fetchStartCode({ commit, state }) {
    try {
      commit('SET_LOADING', { type: 'startCode', value: true })
      // 即使branchId为空也发起请求
      const { data } = await schedulesCityCode({
        filter: 'origin',
        branch_id: state.branchId || ''
      })

      if (data && data.length > 0) {
        const formattedData = formatCityCode(data)
        commit('SET_START_CODE', formattedData)
        return formattedData
      } else {
        commit('SET_START_CODE', [])
        return []
      }
    } catch (error) {
      console.error('获取出发地选项失败:', error)
      commit('SET_START_CODE', [])
      return []
    } finally {
      commit('SET_LOADING', { type: 'startCode', value: false })
    }
  },

  // 获取目的地选项
  async fetchEndCode({ commit, state }, startAddressCode) {
    try {
      commit('SET_LOADING', { type: 'endCode', value: true })
      // 即使branchId为空也发起请求
      const { data } = await schedulesCityCode({
        filter: 'destination',
        branch_id: state.branchId || ''
      })

      if (data && data.length > 0) {
        const formattedData = formatCityCode(data)
        commit('SET_END_CODE', formattedData)
        return formattedData
      } else {
        commit('SET_END_CODE', [])
        return []
      }
    } catch (error) {
      console.error('获取目的地选项失败:', error)
      commit('SET_END_CODE', [])
      return []
    } finally {
      commit('SET_LOADING', { type: 'endCode', value: false })
    }
  },

  // 获取时刻表
  async fetchTimetables({ commit, state }, { startAddressCode, endAddressCode, subBusinessType, lineKeyword }) {
    try {
      commit('SET_LOADING', { type: 'timetables', value: true })

      // 构建请求参数，无论筛选条件如何都发起请求
      const params = {
        branch_id: state.branchId || ''
      }

      // 只有在有值时才添加筛选条件
      if (startAddressCode) {
        params.start_address_code = startAddressCode
      }

      if (endAddressCode) {
        params.end_address_code = endAddressCode
      }

      // 添加子业态筛选条件
      if (subBusinessType) {
        params.sub_business_type = subBusinessType
      }

      // 添加线路搜索关键字
      if (lineKeyword) {
        params.line_keyword = lineKeyword
      }

      const res = await timetables(params)

      let timetablesData = []

      console.log('ressssssssss', res)

      // 处理各种可能的数据结构情况
      if (!res || !res.data) {
        timetablesData = []
      } else if (Array.isArray(res.data)) {
        timetablesData = res.data
      } else if (typeof res.data === 'object') {
        // 如果是对象，尝试将对象的值转换为数组
        timetablesData = Object.values(res.data)
      }

      commit('SET_TIMETABLES_LIST', timetablesData)
      return timetablesData
    } catch (error) {
      console.error('获取时刻表失败:', error)
      commit('SET_TIMETABLES_LIST', [])
      return []
    } finally {
      commit('SET_LOADING', { type: 'timetables', value: false })
    }
  }
}

// 格式化城市代码数据
function formatCityCode(cityData) {
  return cityData.map(city => ({
    value: city.city_id,
    label: city.city_name,
    children: city.city_attributes ? city.city_attributes.map(area => ({
      value: area.area_id,
      label: area.area_name
    })) : []
  }))
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

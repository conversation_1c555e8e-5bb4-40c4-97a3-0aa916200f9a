import { asyncRouterMap, constantRouterMap } from '@/config/router.config'
import cloneDeep from 'lodash.clonedeep'

/**
 * 过滤账户是否拥有某一个权限，并将菜单从加载列表移除
 *
 * @param permission
 * @param route
 * @returns {boolean}
 */
function hasPermission (permission, route) {
  if (route.meta && route.meta.permission) {
    console.log('检查权限:', route.meta.permission, '用户权限:', permission)
    if (permission === undefined) {
      console.log('用户权限未定义，返回false')
      return false
    }

    // 如果没有权限限制，直接通过
    if (!route.meta.permission || route.meta.permission.length === 0) {
      console.log('路由无权限限制，返回true')
      return true
    }

    // 管理员权限，允许访问所有
    if (permission.includes('admin')) {
      console.log('用户拥有admin权限，返回true')
      return true
    }

    let flag = false
    for (let i = 0, len = permission.length; i < len; i++) {
      flag = route.meta.permission.includes(permission[i])
      if (flag) {
        console.log('找到匹配权限:', permission[i], '返回true')
        return true
      }
    }
    console.log('未找到匹配权限，返回false')
    return false
  }
  console.log('路由无meta或permission信息，返回true')
  return true
}

/**
 * 单账户多角色时，使用该方法可过滤角色不存在的菜单
 *
 * @param roles
 * @param route
 * @returns {*}
 */
// eslint-disable-next-line
function hasRole(roles, route) {
  if (route.meta && route.meta.roles) {
    return route.meta.roles.includes(roles.id)
  } else {
    return true
  }
}

function filterAsyncRouter (routerMap, role) {
  const accessedRouters = routerMap.filter(route => {
    if (hasPermission(role.permissionList, route)) {
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, role)
      }
      return true
    }
    return false
  })
  return accessedRouters
}

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes ({ commit }, data) {
      return new Promise(resolve => {
        const { roles } = data
        console.log('GenerateRoutes接收到的角色数据:', roles)

        // 增强兼容性
        let role = roles
        if (!role) {
          console.warn('未提供有效的角色数据，使用默认管理员角色')
          role = {
            id: 'admin',
            name: 'admin',
            permissionList: ['admin', 'merchant']
          }
        } else if (!role.permissionList) {
          console.warn('角色数据中缺少permissionList，补充默认权限')
          role.permissionList = ['admin', 'merchant']
        }

        console.log('处理后的角色数据:', role)
        const routerMap = cloneDeep(asyncRouterMap)
        const accessedRouters = filterAsyncRouter(routerMap, role)
        console.log('过滤后的路由:', accessedRouters)
        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    }
  }
}

export default permission

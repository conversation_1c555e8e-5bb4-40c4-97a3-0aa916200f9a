const getters = {
  sidebar: (state) => state.app.sidebar,
  device: (state) => state.app.device,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  roles: (state) => state.user.roles,
  info: (state) => state.user.info,
  setting: (state) => state.user.setting,
  function_permission_definition: (state) => state.user.function_permission_definition,
  isNotification: (state) => state.settings.isNotification,
  timeoutCount: (state) => state.unread.unread.timeoutCount,
  local_num: (state) => state.websocket.local_num,
  isWsConnected: (state) => state.websocket.isConnected,
  orderCount: (state) => state.unread.unread.ORDER_PENDING,
  loginType: (state) => state.user.loginType,

  // 新增的line模块相关getters
  startCode: (state) => state.line.startCode,
  endCode: (state) => state.line.endCode,
  timetablesList: (state) => state.line.timetablesList,
  lineLoading: (state) => state.line.loading
}
export default getters

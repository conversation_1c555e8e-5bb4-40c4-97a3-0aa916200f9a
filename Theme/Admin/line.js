/*线路管理 ->类型列表  */
var line_categorypage = {
    getLineCateogyrList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var dom = $("#line_category");
        var fields = "";
        var str = "page=" + i + "&size=" + pageCount;
        if ($('#line_category_id').val() != 0) {
            str += '&line_category_id=' + $('#line_category_id').val()
        }
        var s = {
            url: Path.web_path + "/Admin/LineManager/doLineCateogyrList",
            params: str,
            sucrender: function (data) {
                //console.log(data);
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_categorypage.getLineCateogyrList);
                }
                var tmp = '';
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    } else {
                        o.opp = "";
                    }

                    tmp += '<tr class="' + o.opp + '">'
                        + '<td>' + o.id + '</a></td>'
                        + '<td>' + o.category_name + '</a></td>'
                        + '<td>' + o.title + '</a></td>'
                        + '<td class="lst-child">'
                        + '<a href="' + Path.web_path + '/edit_line_category?id=' + o.id + '">修改</a>'
                        + '<a href="javascript:;" class="adelete" rel="' + o.id + '">删除</a>';
                    tmp += '</td>'
                        + '</tr>';
                });
                var dom = $("#line_category");
                if (dom[0].localName == "table" || dom[0].nodeName == "TABLE") {
                    dom.find("td").parents("tr").remove();
                    dom.append(tmp);
                } else {
                    dom.html(tmp);
                }

                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#line_category");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="10" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    deleTargetItem: function () {
        $("#line_category a.adelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineCateogry",
                    params: "id=" + id,
                    sucrender: function () {
                        window.location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_categorypage.getLineCateogyrList(0);
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.getLineCateogyrList();
        /*获取列表数据*/
        this.iniSearch();//搜索
        this.deleTargetItem();
    }
};

//线路管理--添加类型
var add_line_categorypage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_add_agencypage;
        adm.iniInputcheck("#title", "名称不能为空");
    },
    iniupPage: function () {
        $('#add_line_category').on('click', function () {
            $("#title").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var title = $("#title").val();
                var str = "title=" + title + '&category_id=' + $('#line_category').val();
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doAddLineCategory",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_category";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};


// 计价规则管理
var line_pricingpage = {
    getLineCateogyrList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var dom = $("#line_pricing");
        var fields = "";
        var str = "page=" + i + "&size=" + pageCount;
        var s = {
            url: Path.web_path + "/Admin/LineManager/doLinePricingList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_pricingpage.getLineCateogyrList);
                }
                var tmp = '';
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    } else {
                        o.opp = "";

                    }

                    if (o.kuaiche_type == 2) {
                        cn_type = '快车-摆渡车'
                    } else if (o.kuaiche_type == 1) {
                        cn_type = '快车'
                    } else {
                        cn_type = ''
                    }

                    tmp += '<tr class="' + o.opp + '">'
                        + '<td>' + o.id + '</td>'
                        + '<td>' + cn_type + '</td>'
                        + '<td>' + o.title + '</td>'
                        + '<td class="lst-child">'
                        + '<a href="' + Path.web_path + '/edit_line_pricing?id=' + o.id + '">修改</a>'
                        + '<a href="javascript:;" class="adelete" rel="' + o.id + '">删除</a>';
                    tmp += '</td>'
                        + '</tr>';
                });
                var dom = $("#line_pricing");
                if (dom[0].localName == "table" || dom[0].nodeName == "TABLE") {
                    dom.find("td").parents("tr").remove();
                    dom.append(tmp);
                } else {
                    dom.html(tmp);
                }

                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#line_pricing");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="10" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    deleTargetItem: function () {
        $("#line_pricing a.adelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLinePricing",
                    params: "id=" + id,
                    sucrender: function () {
                        window.location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_categorypage.getLineCateogyrList(0);
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.getLineCateogyrList();
        /*获取列表数据*/
        this.iniSearch();//搜索
        this.deleTargetItem();
    }
};
// 计价规则管理
var add_line_pricingpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_add_agencypage;
        adm.iniInputcheck("#title", "名称不能为空");
    },
    iniupPage: function () {
        $('#add_line_pricing').on('click', function () {
            $("#title").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var title = $("#title").val();
                var str = "title=" + title;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doAddLinePricing",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_pricing";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};
// 计价规则管理
var edit_line_pricingpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_edit_agencypage;
        adm.iniInputcheck("#title", "名称不能为空");
    },
    iniupPage: function () {
        $('#edit_line_pricing').on('click', function () {
            $("#title").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var id = $("#id").val();
                var title = $("#title").val();
                var str = "id=" + id + "&title=" + title;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doEditLinePricing",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_pricing";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};


function setIsShow(obj, id) {
    var is_show = $(obj).attr('is_show');
    var line_type = $(obj).attr('line_type');
    var str = "id=" + id + "&type=" + line_type + "&status=" + is_show;
    var s = {
        url: Path.web_path + "/Admin/LineManager/doSetIsShow",
        params: str,
        sucrender: function () {
            alert("操作成功");
            if (is_show == 1) {
                $(obj).attr({ 'is_show': 2 })
                $(obj).text('下架')
            } else {
                $(obj).attr({ 'is_show': 1 })
                $(obj).text('上架')
            }
        },
        failrender: function (data) {
            alert(data.data);
        }
    };
    HGAjax.HAjax(s);
}
//线路管理--编辑类型
var edit_line_categorypage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_edit_agencypage;
        adm.iniInputcheck("#title", "名称不能为空");
    },
    iniupPage: function () {
        $('#edit_line_category').on('click', function () {
            $("#title").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var id = $("#id").val();
                var title = $("#title").val();
                var str = "id=" + id + "&title=" + title + '&category_id=' + $('#line_category').val();
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doEditLineCategory",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_category";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

// 路线start
var line_listpage = {
    getDriversList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_start_name = $('#search_start_name').val();
        var search_end_name = $('#search_end_name').val();
        var search_line_id = $('#search_line_id').val();
        var search_branchid = $('#search_branchid').val();
        var fields = "id,branchid,start_name,end_name,channel_price,start_lat";
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + '&search_branchid=' + search_branchid + '&search_line_id=' + search_line_id + '&search_start_name=' + search_start_name + '&search_end_name=' + search_end_name;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_listpage.getDriversList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    var sta = o.state;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }

                    if (sta == 0 || sta == 3) {
                        o.state = "隐身中";
                    }

                    if (sta == 1) {
                        o.state = "等候";
                    }
                    if (sta == 2) {
                        o.state = "在路上";
                    }
                    var virtual = o.virtual;
                    if (virtual == 1) {
                        o.virtual = "是";
                    } else {
                        o.virtual = "不是";
                    }
                    if (o.start_lat) {
                        o.is_set_pos = '';
                    } else {
                        o.is_set_pos = '未设置上下车点';
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                });
                var varr = ['id', 'businessTimeStr', 'route', 'set_order_time', 'line_type', 'price', 'channel_price', 'branchname', 'create_time', 'opp', 'is_set_pos', 'is_show', 'is_show_message'];
                var tmp = '<tr class="{opp}">'
                    + '<td>{id}</td>'
                    + '<td>{route}</td>'
                    + '<td>{line_type}</td>'
                    + '<td>{price}</td>'
                    + '<td>{channel_price}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{businessTimeStr}</td>'
                    + '<td>{create_time}</td>'
                    + '<td>{set_order_time}分钟</td>'
                    + '<td>'
                    /*  + '<a href="javascript:;" class="freeze" type="{is_freeze_type}" rel="{driver_id}">{is_freeze}</a>'*/
                    + '<a href="' + Path.web_path + '/lineEdit?line_id={id}"  class="update">编辑</a>'
                    + '<a href="javascript:;" class="mdelete" rel="{id}">删除</a>'
                    + '<a href="javascript:;" onclick="setIsShow($(this),\'{id}\')"  line_type="1" is_show="{is_show}" >{is_show_message}</a>'
                    + '<span style="color: red">&nbsp;&nbsp;{is_set_pos}</span>'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("Line", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#Line");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="13" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_listpage.getDriversList(0)
        });
    },
    /*删除单条乘客*/
    deleSingleDriver: function () {
        $("#Line a.mdelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLine",
                    params: "id=" + id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },

    /*删除恢复*/
    freezeDriver: function () {
        $("#Line td a.freeze").live("click", function () {
            var cur = $(this);
            var isfr = cur.attr("type");
            var id = $(this).attr("rel");
            if (isfr == "0") {
                var s = {
                    url: Path.web_path + "/Admin/MemberManager/doFreezeDriver",
                    params: "is_freeze=1" + "&driver_id=" + id,
                    sucrender: function () {
                        cur.attr("type", "1");
                        cur.text("恢复");
                        cur.parents("tr").find("span.freeze").text("已被删除");
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                }
            }
            if (isfr == "1") {
                s = {
                    url: Path.web_path + "/Admin/MemberManager/doFreezeDriver",
                    params: "is_freeze=0" + "&driver_id=" + id,
                    sucrender: function () {
                        cur.attr("type", "0");
                        cur.text("删除");
                        cur.parents("tr").find("span.freeze").text("正常");
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                }

            }
            HGAjax.HAjax(s);
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.iniSearch();
        this.getDriversList();
        this.deleSingleDriver();
        this.freezeDriver();
        /*删除乘客*/
    }
};

var linepage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = linepage;
        adm.iniInputcheck("#start_name", "出发地不能为空");
        adm.iniInputcheck("#end_name", "目的地不能为空");
        adm.iniInputcheck("#price", "价格不能为空");
    },
    iniaddState: function () {
        $('#add_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                var start_lng = $('#st_lng').val()
                var start_lat = $('#st_lat').val()
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();
                var end_lng = $('#end_lng').val()
                var end_lat = $('#end_lat').val()
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                if (!start_lng) {
                    alert("请选择出发地的区域坐标"); return;
                }
                if (!$('#start_name').val()) {
                    alert("请输入出发地显示名称"); return;
                }
                if (!$('#end_name').val()) {
                    alert("请输入目的地显示名称"); return;
                }
                if (!end_lng) {
                    alert("请选择目的地的区域坐标"); return;
                }
                if (start_area_code == 0) {
                    //alert("请选择出发地的区域");return;
                }

                if (end_area_code == 0) {
                    // alert("请选择目的地的区域");return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }
                var LineSummary = $("#lineSummary").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var price = $('#price').val();
                var channel_price = $('#channel_price').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });

                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }
                if (!Check.Money(price)) {
                    alert("请输入正确的价格"); return;
                }
                if (!Check.MoneyLenght(price)) {
                    alert("价格范围0.00-99999.00"); return;
                }
                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&price=' + price + '&channel_price=' + channel_price + "&summary=" + LineSummary + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLine",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
var lineEditpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineEditpage;
        adm.iniInputcheck("#start", "出发点不能为空");
        adm.iniInputcheck("#end", "目的地不能为空");
        adm.iniInputcheck("#lineName", "路线名不能为空");
        adm.iniInputcheck("#price", "价格不能为空");
    },
    iniupPage: function () {
        $('#edit_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var start_lng = $('#st_lng').val()
                var start_lat = $('#st_lat').val()
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                var end_lng = $('#end_lng').val()
                var end_lat = $('#end_lat').val()
                if (!$('#start_name').val()) {
                    alert("请输入出发地显示名称"); return;
                }
                if (!$('#end_name').val()) {
                    alert("请输入目的地显示名称"); return;
                }
                if (start_area_code == 0) {
                    //alert("请选择出发地的区域");return;
                }

                if (end_area_code == 0) {
                    // alert("请选择目的地的区域");return;
                }
                if (!start_lng) {
                    alert("请选择出发地的区域坐标"); return;
                }
                if (!end_lng) {
                    alert("请选择目的地的区域坐标"); return;
                }
                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var line_id = $('#line_id').val();
                var LineSummary = $("#lineSummary").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var price = $('#price').val();
                var channel_price = $('#channel_price').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&price=' + price + '&channel_price=' + channel_price + "&summary=" + LineSummary + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();

                var adm = linepage;

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }

                if (!Check.Money(price)) {
                    alert("请输入正确的价格"); return;
                }
                if (!Check.MoneyLenght(price)) {
                    alert("价格范围0.00-99999.00"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLine",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

// 路线end

// 快车start
var line_fast_listpage = {
    getLineFastList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_start_name = $('#search_start_name').val();
        var search_end_name = $('#search_end_name').val();
        var search_line_id = $('#search_line_id').val();
        var search_branchid = $('#search_branchid').val();
        var search_type = $("#search_type").val() //快车类型搜索
        var fields = "id,branchid,start_name,end_name,channel_price,start_lat,type"
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + '&search_branchid=' + search_branchid + '&search_line_id=' + search_line_id + '&search_start_name=' + search_start_name + '&search_end_name=' + search_end_name + '&search_type=' + search_type
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineFastList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_fast_listpage.getLineFastList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    var sta = o.state;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }

                    if (sta == 0 || sta == 3) {
                        o.state = "隐身中";
                    }

                    if (sta == 1) {
                        o.state = "等候";
                    }
                    if (sta == 2) {
                        o.state = "在路上";
                    }
                    var virtual = o.virtual;
                    if (virtual == 1) {
                        o.virtual = "是";
                    } else {
                        o.virtual = "不是";
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                });
                var varr = ['id', 'businessTimeStr', 'route', 'cn_type', 'title', 'branchname', 'create_time', 'opp', 'is_show', 'is_show_message'];
                var tmp = '<tr class="{opp}">'
                    + '<td>{id}</td>'
                    + '<td>{route}</td>'
                    + '<td>{title}</td>'
                    + '<td>{cn_type}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{businessTimeStr}</td>'
                    + '<td>{create_time}</td>'
                    + '<td>'
                    + '<a href="' + Path.web_path + '/lineFastFee?line_id={id}">设置快车计价</a>'
                    + '<a href="' + Path.web_path + '/lineFastEdit?line_id={id}"  class="update">编辑</a>'
                    + '<a href="javascript:;" class="mdelete" rel="{id}">删除</a>'
                    + '<a href="javascript:;" onclick="setIsShow($(this),\'{id}\')"  line_type="7" is_show="{is_show}" >{is_show_message}</a>'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("Line", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#Line");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="13" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_fast_listpage.getLineFastList(0)
        });
    },
    /*删除单条线路*/
    deleSingleLineFast: function () {
        $("#Line a.mdelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineFast",
                    params: "id=" + id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },

    inipage: function () {
        Base.iniLeftNav(1);
        this.iniSearch();
        this.getLineFastList();
        this.deleSingleLineFast();
    }
};

var lineFastpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineFastpage;
    },
    iniaddState: function () {
        $('#add_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                var start_lng = $('#st_lng').val()
                var start_lat = $('#st_lat').val()
                //目的地
                var epcode = $("div.start_place").find("select.prov").val();
                var eccode = $("div.start_place").find("select.city").val();
                var eqcode = $("div.start_place").find("select.area").val();
                var end_lng = $('#st_lng').val()
                var end_lat = $('#st_lat').val()
                var kuaiche_title = $('#kuaiche_title').val();
                var kuaiche_type = $('#kuaiche_type').val();
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                if (start_city_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_city_code == 0) {
                    alert("请选择目的地的区域"); return;
                }
                var base_price = $('#base_price').val();
                var base_kilometre = $('#base_kilometre').val();
                var plus_kilometre_price = $('#plus_kilometre_price').val();
                var start_address_code = start_city_code;
                var end_address_code = end_city_code;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var driver = {};
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }
                var start_polygon = $('#start_polygon').val();

                var str = "area_radius_end=&area_lng_end=&area_lat_end=&area_lat_start=&area_lng_start=&area_radius_start=&start_lng=&start_lat=&end_lng=&end_lat=&start_polygon=" + start_polygon + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();
                str += "&kuaiche_title=" + kuaiche_title + "&kuaiche_type=" + kuaiche_type
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLineFast",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_fast_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
var lineFastEditpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineFastEditpage;
    },
    iniupPage: function () {
        $('#edit_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.start_place").find("select.prov").val();
                var eccode = $("div.start_place").find("select.city").val();
                var eqcode = $("div.start_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                // var start_lng = $('#st_lng').val();
                // var start_lat = $('#st_lat').val();
                var start_appointment_time = $('#start_appointment_time').val();
                var end_appointment_time = $('#end_appointment_time').val();
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val();
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                // var end_lng = $('#st_lng').val();
                // var end_lat = $('#st_lat').val();
                var kuaiche_title = $('#kuaiche_title').val();

                var kuaiche_type = $('#kuaiche_type').val();
                var plus_kilometre_price = $('#plus_kilometre_price').val();
                var line_id = $('#line_id').val();
                var start_address_code = start_city_code;
                var end_address_code = end_city_code;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var driver = {};

                var start_polygon = $('#start_polygon').val();
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var str = "area_radius_end=&area_lng_end=&area_lat_end=&area_lat_start=&area_lng_start=&area_radius_start=&start_lng=&start_lat=&end_lng=&end_lat=&start_polygon=" + start_polygon + "&id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();
                str += "&kuaiche_title=" + kuaiche_title + "&kuaiche_type=" + kuaiche_type
                if (start_city_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_city_code == 0) {
                    alert("请选择目的地的区域"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLineFast",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_fast_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

var lineFastFeepage = {
    inipage: function () {
        Base.iniLeftNav(1);
    }
}

// 快车end

// 出租车start
var line_taxi_listpage = {
    getLineTaxiList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_start_name = $('#search_start_name').val();
        var search_end_name = $('#search_end_name').val();
        var search_line_id = $('#search_line_id').val();
        var search_branchid = $('#search_branchid').val();
        var fields = "id,branchid,start_name,end_name,channel_price,start_lat";
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + '&search_branchid=' + search_branchid + '&search_line_id=' + search_line_id + '&search_start_name=' + search_start_name + '&search_end_name=' + search_end_name;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineTaxiList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_taxi_listpage.getLineTaxiList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    var sta = o.state;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }

                    if (sta == 0 || sta == 3) {
                        o.state = "隐身中";
                    }

                    if (sta == 1) {
                        o.state = "等候";
                    }
                    if (sta == 2) {
                        o.state = "在路上";
                    }
                    var virtual = o.virtual;
                    if (virtual == 1) {
                        o.virtual = "是";
                    } else {
                        o.virtual = "不是";
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                });
                var varr = ['id', 'businessTimeStr', 'route', 'branchname', 'create_time', 'opp', 'is_show', 'is_show_message'];
                var tmp = '<tr class="{opp}">'
                    + '<td>{id}</td>'
                    + '<td>{route}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{businessTimeStr}</td>'
                    + '<td>{create_time}</td>'
                    + '<td>'
                    + '<a href="' + Path.web_path + '/lineTaxiFee?line_id={id}">设置出租车计价</a>'
                    + '<a href="' + Path.web_path + '/lineTaxiEdit?line_id={id}"  class="update">编辑</a>'
                    + '<a href="javascript:;" class="mdelete" rel="{id}">删除</a>'
                    + '<a href="javascript:;" onclick="setIsShow($(this),\'{id}\')"  line_type="7" is_show="{is_show}" >{is_show_message}</a>'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("Line", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#Line");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="13" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_taxi_listpage.getLineTaxiList(0)
        });
    },
    /*删除单条线路*/
    deleSingleLineTaxi: function () {
        $("#Line a.mdelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineTaxi",
                    params: "id=" + id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },

    inipage: function () {
        Base.iniLeftNav(1);
        this.iniSearch();
        this.getLineTaxiList();
        this.deleSingleLineTaxi();
    }
};

var lineTaxipage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineTaxipage;
    },
    iniaddState: function () {
        $('#add_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                var start_lng = $('#st_lng').val()
                var start_lat = $('#st_lat').val()
                //目的地
                var epcode = $("div.start_place").find("select.prov").val();
                var eccode = $("div.start_place").find("select.city").val();
                var eqcode = $("div.start_place").find("select.area").val();
                var end_lng = $('#st_lng').val()
                var end_lat = $('#st_lat').val()
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                if (start_city_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_city_code == 0) {
                    alert("请选择目的地的区域"); return;
                }
                var base_price = $('#base_price').val();
                var base_kilometre = $('#base_kilometre').val();
                var plus_kilometre_price = $('#plus_kilometre_price').val();
                var start_address_code = start_city_code;
                var end_address_code = end_city_code;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var driver = {};
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }

                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLineTaxi",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_taxi_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
var lineTaxiEditpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineTaxiEditpage;
    },
    iniupPage: function () {
        $('#edit_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.start_place").find("select.prov").val();
                var eccode = $("div.start_place").find("select.city").val();
                var eqcode = $("div.start_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var start_lng = $('#st_lng').val();
                var start_lat = $('#st_lat').val();
                var start_appointment_time = $('#start_appointment_time').val();
                var end_appointment_time = $('#end_appointment_time').val();
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val();
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                var end_lng = $('#st_lng').val();
                var end_lat = $('#st_lat').val();
                var base_price = $('#base_price').val();
                var base_kilometre = $('#base_kilometre').val();
                var plus_kilometre_price = $('#plus_kilometre_price').val();
                var line_id = $('#line_id').val();
                var start_address_code = start_city_code;
                var end_address_code = end_city_code;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var driver = {};
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + '&set_order_time=' + $('#set_order_time').val();
                if (start_city_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_city_code == 0) {
                    alert("请选择目的地的区域"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLineTaxi",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_taxi_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

var lineTaxiFeepage = {
    inipage: function () {
        Base.iniLeftNav(1);
    }
}

// 出租车end

// 路线start
var line_phone_listpage = {
    getLinePhoneList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_line_id = $('#search_line_id').val();
        var search_start_name = $('#search_start_name').val();
        var search_end_name = $('#search_end_name').val();
        var search_branchid = $('#search_branchid').val();
        var is_phone_line_verify = $('#is_phone_line_verify').val();
        var fields = "id,branchid,start_name,end_name,channel_price,start_lat";
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + '&search_branchid=' + search_branchid + '&search_line_id=' + search_line_id + '&search_start_name=' + search_start_name + '&search_end_name=' + search_end_name + '&is_phone_line_verify=' + is_phone_line_verify;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLinePhoneList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_phone_listpage.getLinePhoneList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    var sta = o.state;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }

                    if (sta == 0 || sta == 3) {
                        o.state = "隐身中";
                    }

                    if (sta == 1) {
                        o.state = "等候";
                    }
                    if (sta == 2) {
                        o.state = "在路上";
                    }
                    var virtual = o.virtual;
                    if (virtual == 1) {
                        o.virtual = "是";
                    } else {
                        o.virtual = "不是";
                    }
                    if (o.start_lat) {
                        o.is_set_pos = '';
                    } else {
                        o.is_set_pos = '未设置上下车点';
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.is_phone_line_verify == 0) {
                        o.isPhoneLineVerifyStr = '-'
                        o.isPhoneLineVerifyStrRegoin = '-';
                    } else {
                        o.isPhoneLineVerifyStr = '已购买'
                        o.isPhoneLineVerifyStrRegoin = o.phone_line_verify_start_time + " 至 " + o.phone_line_verify_end_time;
                    }
                });
                var varr = ['id', 'isPhoneLineVerifyStr', 'isPhoneLineVerifyStrRegoin', 'driver_info', 'route', 'line_type', 'price', 'branchname', 'create_time', 'phone_line_remarks', 'opp', 'is_set_pos', 'is_show', 'is_show_message'];
                var tmp = '<tr class="{opp}">'
                    + '<td>{id}</td>'
                    + '<td>{route}</td>'
                    + '<td>{line_type}</td>'
                    + '<td>{price}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{isPhoneLineVerifyStr}</td>'
                    + '<td>{isPhoneLineVerifyStrRegoin}</td>'
                    + '<td>{driver_info}</td>'
                    + '<td>{phone_line_remarks}</td>'
                    + '<td>{create_time}</td>'
                    + '<td>'
                    /*  + '<a href="javascript:;" class="freeze" type="{is_freeze_type}" rel="{driver_id}">{is_freeze}</a>'*/
                    + '<a href="' + Path.web_path + '/line_phone_edit?line_id={id}"  class="update">编辑</a>'
                    + '<a href="javascript:;" class="mdelete" rel="{id}">删除</a>'
                    + '<a href="javascript:;" onclick="setIsShow($(this),\'{id}\')"  line_type="1" is_show="{is_show}" >{is_show_message}</a>'
                    + '<span style="color: red">&nbsp;&nbsp;{is_set_pos}</span>'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("Line", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#Line");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="13" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    /*删除单条乘客*/
    deleSingleLinePhone: function () {
        $("#Line a.mdelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLinePhone",
                    params: "id=" + id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_phone_listpage.getLinePhoneList(0)
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.iniSearch();
        this.deleSingleLinePhone();
        this.getLinePhoneList();
    }
};

var line_phonepage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = linepage;
        adm.iniInputcheck("#start_name", "出发地不能为空");
        adm.iniInputcheck("#end_name", "目的地不能为空");
        adm.iniInputcheck("#price", "价格不能为空");
    },
    iniaddState: function () {
        $('#add_line').bind('click', function () {
            $("#lineName,#price,#channel_price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                var start_lng = $('#st_lng').val();
                var start_lat = $('#st_lat').val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();
                var end_lng = $('#end_lng').val();
                var end_lat = $('#end_lat').val();
                var phone_line_verify_start_time = $('#phone_line_verify_start_time').val();
                var phone_line_verify_end_time = $('#phone_line_verify_end_time').val();
                var is_phone_line_verify = $('#is_phone_line_verify input[name=is_phone_line_verify]:checked').val();
                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                if (!start_lng) {
                    alert("请选择出发地的区域坐标"); return;
                }
                if (!$('#start_name').val()) {
                    alert("请输入出发地显示名称"); return;
                }
                if (!$('#end_name').val()) {
                    alert("请输入目的地显示名称"); return;
                }
                if (!end_lng) {
                    alert("请选择目的地的区域坐标"); return;
                }
                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }
                var phone_line_remarks = $("#phone_line_remarks").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var price = $('#price').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });

                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }
                if (!Check.Money(price)) {
                    alert("请输入正确的价格"); return;
                }
                if (!Check.MoneyLenght(price)) {
                    alert("价格范围0.00-99999.00"); return;
                }
                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&price=' + price + "&phone_line_remarks=" + phone_line_remarks + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&phone_line_verify_start_time=" + phone_line_verify_start_time + "&phone_line_verify_end_time=" + phone_line_verify_end_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&is_phone_line_verify=' + is_phone_line_verify;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLinePhone",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_phone_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
var line_phone_editpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = lineEditpage;
        adm.iniInputcheck("#start", "出发点不能为空");
        adm.iniInputcheck("#end", "目的地不能为空");
        adm.iniInputcheck("#lineName", "路线名不能为空");
        adm.iniInputcheck("#price", "价格不能为空");
    },
    iniupPage: function () {
        $('#edit_line').bind('click', function () {
            $("#lineName,#price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var start_lng = $('#st_lng').val()
                var start_lat = $('#st_lat').val()
                var phone_line_verify_start_time = $('#phone_line_verify_start_time').val();
                var phone_line_verify_end_time = $('#phone_line_verify_end_time').val();
                var is_phone_line_verify = $('#is_phone_line_verify input[name=is_phone_line_verify]:checked').val();
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;
                var end_lng = $('#end_lng').val()
                var end_lat = $('#end_lat').val()
                if (!$('#start_name').val()) {
                    alert("请输入出发地显示名称"); return;
                }
                if (!$('#end_name').val()) {
                    alert("请输入目的地显示名称"); return;
                }
                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }
                if (!start_lng) {
                    alert("请选择出发地的区域坐标"); return;
                }
                if (!end_lng) {
                    alert("请选择目的地的区域坐标"); return;
                }
                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var line_id = $('#line_id').val();
                var phone_line_remarks = $("#phone_line_remarks").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var price = $('#price').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var str = "area_radius_end=" + $('#area_radius_end').val() + "&area_lng_end=" + $('#area_lng_end').val() + "&area_lat_end=" + $('#area_lat_end').val() + "&area_lat_start=" + $('#area_lat_start').val() + "&area_lng_start=" + $('#area_lng_start').val() + "&area_radius_start=" + $('#area_radius_start').val() + "&start_lng=" + start_lng + "&start_lat=" + start_lat + "&end_lng=" + end_lng + "&end_lat=" + end_lat + "&id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&price=' + price + "&phone_line_remarks=" + phone_line_remarks + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&phone_line_verify_start_time=" + phone_line_verify_start_time + "&phone_line_verify_end_time=" + phone_line_verify_end_time + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&is_phone_line_verify=' + is_phone_line_verify;

                var adm = linepage;

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }

                if (!Check.Money(price)) {
                    alert("请输入正确的价格"); return;
                }
                if (!Check.MoneyLenght(price)) {
                    alert("价格范围0.00-99999.00"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLinePhone",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_phone_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

// 路线end

//添加包车线路
var line_charteredpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = linepage;
        adm.iniInputcheck("#start_name", "出发地不能为空");
        adm.iniInputcheck("#end_name", "目的地不能为空");
    },
    iniaddState: function () {
        $('#add_line_chartered').bind('click', function () {
            $("#lineName,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;

                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var list = {};
                var clist = {};
                var LineSummary = $("#lineSummary").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var price = $('input[name="price"]').val();
                var channel_price = $('input[name="channel_price"]').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $('input[name="price"]').each(function (k, v) {
                    var temp = {};
                    if ($(this).val()) {
                        if (!Check.Money($(this).val())) {
                            alert("请输入正确的价格"); return false;
                        }
                        if (!Check.MoneyLenght($(this).val())) {
                            alert("价格范围0.00-99999.00"); return false;
                        }
                        var car_type_id = $(this).attr("data-toggle");
                        temp['price'] = $(this).val();
                        temp['channel_price'] = $('input[name="channel_price_' + car_type_id + '"]').val();
                        temp['id'] = car_type_id;
                        list[k] = temp;
                    }
                });

                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var prices = JSON.stringify(list);
                var channel_prices = JSON.stringify(clist);

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }

                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }


                var str = "mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&prices=' + prices + "&summary=" + LineSummary + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + '&set_order_time=' + $('#set_order_time').val();
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLineChartered",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_chartered_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
// 包车线路
var line_chartered_listpage = {
    getDriversList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_line_id = $('#search_line_id').val();
        var search_branchid = $('#search_branchid').val();
        var fields = "id,branchid,start_name,end_name,business_time_type";
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + '&search_branchid=' + search_branchid + '&search_line_id=' + search_line_id;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineChareteredList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_chartered_listpage.getDriversList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    var sta = o.state;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }

                    if (sta == 0 || sta == 3) {
                        o.state = "隐身中";
                    }

                    if (sta == 1) {
                        o.state = "等候";
                    }
                    if (sta == 2) {
                        o.state = "在路上";
                    }
                    var virtual = o.virtual;
                    if (virtual == 1) {
                        o.virtual = "是";
                    } else {
                        o.virtual = "不是";
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                });
                var varr = ['id', 'businessTimeStr', 'set_order_time', 'route', 'line_type', 'price', 'channel_price', 'branchname', 'create_time', 'opp', 'is_show', 'is_show_message'];
                var tmp = '<tr class="{opp}">'
                    + '<td>{id}</td>'
                    + '<td>{route}</td>'
                    + '<td>{line_type}</td>'
                    + '<td>{price}</td>'
                    + '<td>{channel_price}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{businessTimeStr}</td>'
                    + '<td>{create_time}</td>'
                    + '<td>{set_order_time}分钟</td>'
                    + '<td>'
                    /*  + '<a href="javascript:;" class="freeze" type="{is_freeze_type}" rel="{driver_id}">{is_freeze}</a>'*/
                    + '<a href="' + Path.web_path + '/line_chartered_edit?line_charetered_id={id}"  class="update">编辑</a>'
                    + '<a href="javascript:;" class="mdelete" rel="{id}">删除</a>'
                    + '<a href="javascript:;" onclick="setIsShow($(this),\'{id}\')"  line_type="2" is_show="{is_show}" >{is_show_message}</a>'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("LineChartered", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#LineChartered");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="11" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_chartered_listpage.getDriversList(0)
        });
    },
    /*删除单条乘客*/
    deleSingleDriver: function () {
        $("#LineChartered a.mdelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineChartered",
                    params: "id=" + id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },

    inipage: function () {
        Base.iniLeftNav(1);
        this.iniSearch();
        this.getDriversList();
        this.deleSingleDriver();
        /*删除乘客*/
    }
};
//编辑包车
var line_chartered_editpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = linepage;
        adm.iniInputcheck("#start_name", "出发点不能为空");
        adm.iniInputcheck("#end_name", "目的地不能为空");
        adm.iniInputcheck("#lineName", "路线名不能为空");
    },
    iniupPage: function () {
        $('#edit_line_chartered').bind('click', function () {

            $("#lineName,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;

                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var list = {};
                var line_id = $('#line_id').val();
                var LineSummary = $("#lineSummary").val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var branchid = $("div.branch").find("select.branch-select").val();
                var lc = {};
                var driver = {};
                $('input[name="price"]').each(function (k, v) {
                    var temp = {};
                    if ($(this).val()) {
                        if (!Check.Money($(this).val())) {
                            alert("请输入正确的价格"); return false;
                        }
                        if (!Check.MoneyLenght($(this).val())) {
                            alert("价格范围0.00-99999.00"); return false;
                        }
                        var car_type_id = $(this).attr("data-toggle");
                        temp['price'] = $(this).val();
                        temp['channel_price'] = $('input[name="channel_price_' + car_type_id + '"]').val();
                        temp['id'] = car_type_id;
                        list[k] = temp;
                    }
                })

                $("#lc input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        lc[k] = $(this).val();
                    }
                });
                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver[k] = $(this).val();
                    }
                });
                var prices = JSON.stringify(list);

                var str = "id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&prices=' + prices + "&summary=" + LineSummary + '&start_name=' + start_name + "&end_name=" + end_name + '&lc=' + JSON.stringify(lc);
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code + "&driver=" + JSON.stringify(driver) + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time + '&set_order_time=' + $('#set_order_time').val();
                var adm = linepage;

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLineChartered",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_chartered_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};


// 路线start
var line_class_listpage = {
    getLineClassList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        // //从本地存储读取缓存
        // var page_location = localStorage.getItem("page_location");
        // //本地无缓存
        // if (page_location == undefined) {
        //     //读取当前选择的页数
        //     var current_location = $('span[class="current"]').text()
        //     if (current_location != '') {
        //         //设置本地缓存
        //         localStorage.setItem("page_location", current_location);
        //         i = current_location    
        //     }
        // } else {
        //     //读取当前选择的页数
        //     var current_location = $('span[class="current"]').text()
        //     if (current_location != page_location) {
        //         if (current_location != '') {
        //             //设置本地缓存
        //             localStorage.setItem("page_location", current_location);
        //             i = current_location;
        //         } else {
        //             //本地有缓存
        //             i = page_location
        //         }
        //     } else {
        //         //本地有缓存
        //         i = page_location
        //     }
        // }
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();
        var search_branchid = $('#search_branchid').val();
        var start_time_type = $('#start_time_type').val();
        var support_points_deduction = $('#support_points_deduction').val();
        var fields = "id,branchid,start_name,end_name";
        var status = $('#status').val();
        var line_class_train_no = $('#line_class_train_no').val();
        var start_name = $('#start_name').val();
        var end_name = $('#end_name').val();
        var line_category = $('#line_category').val();
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid + "&status=" + status + "&line_class_train_no=" + line_class_train_no + "&start_name=" + start_name + "&end_name=" + end_name + '&start_time_type=' + start_time_type + '&search_branchid=' + search_branchid + '&support_points_deduction=' + support_points_deduction;
        str += '&line_category=' + line_category;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineClassList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_class_listpage.getLineClassList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var freeze = o.is_freeze;
                    if (freeze == 0) {
                        o.is_freeze = "正常";
                        o.is_freeze_type = "0";
                    }
                    else {
                        o.is_freeze = "已被删除";
                        o.is_freeze_type = "1"
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    var strting_time_arr=[]
                    if (o.start_time_type == 2) {
                        /**
                         * 滚动发车时间段
                         * 2023.12.08
                         */
                        var strting_time_string = ''
                        if (o.starting_interval_time) {
                            var starting_intervel_times = o.starting_interval_time;
                            for (let index = 0; index < starting_intervel_times.length; index++) {
                                for (let key in starting_intervel_times[index]) {
                                    strting_time_string += '<p class="starting_time_fixed_block"><span class="line_show2">' + key.substr(0, 5) + ' → ' + starting_intervel_times[index][key].substr(0,5) + '</span></p>';
                                   strting_time_arr.push( key.substr(0, 5) + ' → ' + starting_intervel_times[index][key].substr(0,5))
                                }   
                            }
                        } else {
                            // 滚动发车时间格式
                            o.start_time = o.start_earliest_time.substr(0, 5) + ' → ' + o.end_latest_time.substr(0, 5);
                            strting_time_string = '<p class="starting_time_fixed_block"><span class="line_show2">' + o.start_time + '</span></p>';
                            strting_time_arr=[o.start_time]
                        }
                        o.startTimeStr = '<p>滚动发车</p>' + strting_time_string;
                        
                    } else {
                        var strting_time_string = ''
                        if (o.starting_time) {
                            var starting_times = o.starting_time
                            for (let index = 0; index < starting_times.length; index++) {
                                strting_time_string += '<p class="starting_time_fixed_block"><span class="line_show2 line_show2_fixed"> ' + starting_times[index].substr(0, 5) + '</span></p>'
                                strting_time_arr.push(starting_times[index].substr(0, 5))
                            }
                        } else {
                            strting_time_string = '<p class="starting_time_fixed_block"><span class="line_show2 line_show2_fixed"> ' + o.start_time + '</span></p>'
                            strting_time_arr=[o.start_time]
                        }
                        o.startTimeStr = '<p>固定发车</p>' + strting_time_string
                    }
                    o['strting_time_arr']=strting_time_arr

                    if (o.status == 1) {
                        o.status_message = '<select style="width: 80px" rel="' + o.id + '" class="ty_action"><option selected value="1">运行中</option><option value="0" style="color:red">已停运</option></select>';
                    } else {
                        o.status_message = '<select style="width: 80px; color:red" rel="' + o.id + '" class="ty_action"><option value="1">运行中</option><option selected value="0" style="color:red">已停运</option></select>';
                    }
                    if (o.scheduling_type == 2) {
                        o.scheduling_type_str = '自动排班【预售' + o.pre_sale_time + '天】';
                    } else {
                        o.scheduling_type_str = '手动排班';
                    }
                    
                    // 处理线路方向标签
                    if (o.direction == 1 || o.direction == '上行') {
                        o.direction_class = 'up';
                        o.direction_label = '上行';
                    } else if (o.direction == 2 || o.direction == '下行') {
                        o.direction_class = 'down';
                        o.direction_label = '下行';
                    } else {
                        o.direction_class = '';
                        o.direction_label = '';
                    }
                });
                var varr = [
                    'id',
                    'direction_class',
                    'direction_label',
                    'start_point',
                    'startTimeStr',
                    'end_point',
                    'start',
                    'start_name',
                    'last_scheduling_time',
                    'stop_sell_number',
                    'stop_sell_time',
                    'start_time',
                    'is_seat_selection',
                    'is_seat_selection_info',
                    'price_info',
                    'end',
                    'end_name',
                    'aboard_address',
                    'over_train_count',
                    'on_way_train_count',
                    'branchname',
                    'line_type',
                    'create_time',
                    'opp',
                    'is_show',
                    'is_show_message',
                    'status_message',
                    'scheduling_type_str',
                    'branch_drivers',
                    'strting_time_arr',
                    'starting_time',
                    'is_start_ferry',
                    'is_start_ferry_pricing',
                    'is_end_ferry',
                    'is_end_ferry_pricing',
                    'schedule_plan',
                    'stop_description'
                ];

                var tmp = '<tr class="{opp}">'
                    + '<td width="50"><input ids="{id}" class="ckeckebox_e" type="checkbox"></td>'
                    + '<td>{id}<span class="line_direction_{direction_class}">{direction_label}</span></td>'
                    + '<td class="td_better" style="font-size:11px;"><p>出发城市:<span style="color: blue">{start}</span></p><p>出发站：<span class="line_show">{start_name}</span></p><p>上车点：</p><div>{start_point}</div></td>'
                    + '<td class="td_better" style="font-size:11px;"><p>目的城市:<span class="red" style="color: blue">{end}</span></p><p>目的站：<span class="line_show">{end_name}</span></p><p>下车点：</p><div>{end_point}</div></td>'
                    + '<td class="td_better" style="font-size:11px;"><p>{startTimeStr}</p>'
                    + '</td>'
                    + '<td style="font-size:11px;" id="{id}_scheduling_type_str" class="td_better">{scheduling_type_str}'
                    + '<p>最新排班至：{last_scheduling_time}</p>'
                    + '<p style="color:red;">停售时间：{stop_description}</p>'
                    + '<p>{schedule_plan}</p>'
                    + '</td>'
                    + '<td style="font-size:11px;" >{is_seat_selection_info}</td>'
                    + '<td style="font-size:11px;" >{price_info}</td>'
                    + '<td style="font-size:11px;" >{line_type}</td>'
                    + '<td >{branchname}</td>'
                    + '<td width="80">{status_message}</td>'
                    + '<td>'
                    + '<div class="action-dropdown">'
                    + '<div class="action-dropdown-menu">'
                    + '<a rel="{id}" class="action-dropdown-item primary line_copy" href="javascript:">复制班线</a>'
                    + '<a rel="{id}" class="action-dropdown-item primary line_copy_return" href="javascript:">复制返程</a>'
                    + '<a href="' + Path.web_path + '/line_class_edit?line_class_id={id}" class="action-dropdown-item success update">修改班线</a>'
                    + '<a href="' + Path.web_path + '/line_class_train_list?line_class_id={id}" class="action-dropdown-item">班次列表</a>'
                    + '<a href="javascript:" ids="{id}" tp_is_start_ferry="{is_start_ferry}" tp_is_start_ferry_pricing=\'{is_start_ferry_pricing}\' tp_is_end_ferry="{is_end_ferry}" tp_is_end_ferry_pricing=\'{is_end_ferry_pricing}\' onclick="showPbBox(2,$(this),{is_seat_selection},\'{branchname}\')" branch_drivers = \'{branch_drivers}\' starting_time= \'{strting_time_arr}\' class="action-dropdown-item success">排班管理</a>'
                    + '<div style="border-top: 1px solid #eee; margin: 4px 0;"></div>'
                    + '<a href="javascript:;" class="action-dropdown-item danger lineClassDelete" rel="{id}">删除班线</a>'
                    + '</div>'
                    + '</div>'
                    + '{schedule_plan}'
                    + '</td>'
                    + '</tr>';

                Base.GenTemp("LineClass", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#LineClass");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="12" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_class_listpage.getLineClassList(0);
        });
    },
    /*删除单条线路*/
    deleSingleLineClass: function () {
        $("#LineClass a.lineClassDelete").live("click", function () {
            var msg = confirm("删除班线将同时删除该班线班次，确认删除吗?");
            var line_class_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineClass",
                    params: "line_class_id=" + line_class_id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    /*复制单条路线*/
    copyLine: function () {
        $("#LineClass a.line_copy").live("click", function () {
            var msg = confirm("确定要复制当前路线吗?复制后需自行排班！");
            var line_class_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doCopyLineClass",
                    params: "line_class_id=" + line_class_id,
                    sucrender: function () {
                        alert('复制成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    /*复制返程线路*/
    copyLineReturn: function () {
        $("#LineClass a.line_copy_return").live("click", function () {
            var msg = confirm("确定要复制当前路线的返程线路吗?复制后需自行排班！");
            var line_class_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doCopyLineClass",
                    params: "line_class_id=" + line_class_id + '&is_return=1',
                    sucrender: function () {
                        alert('复制成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doOperation: function () {
        $("#LineClass select.ty_action").live("change", function () {
            var str = 'id=' + $(this).attr('rel') + '&status=' + $(this).val() + '&is_batch=0&is_train=0';
            var s = {
                async: true,
                url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                params: str,
                sucrender: function () {
                    alert('操作成功！')
                    location.reload();
                },
                failrender: function (data) {
                    alert(data.data);
                }
            };
            HGAjax.HAjax(s);
        });
    },
    doBatchOperation: function () {
        $("#doBatchOperation").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            var str = 'id=' + str1.substring(0, str1.length - 1) + '&status=1&is_batch=1';
            var msg = confirm("确认要批量运行吗?");
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doBatchShutdown: function () {
        $("#doBatchShutdown").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            var str = 'id=' + str1.substring(0, str1.length - 1) + '&status=0&is_batch=1';
            var msg = confirm("确认要批量停运吗?");
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doBatchDel: function () {
        $("#doBatchDel").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            var str = 'id=' + str1.substring(0, str1.length - 1);
            var msg = confirm("删除班线将同时删除该班线班次，确认批量删除吗?");
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchDelLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.doBatchOperation()
        this.doBatchShutdown()
        this.iniSearch();
        this.copyLine();
        this.copyLineReturn();
        this.doBatchDel();
        this.doOperation()
        this.getLineClassList();
        this.deleSingleLineClass();
    }
};
var line_classpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = line_classpage;
        adm.iniInputcheck("#start_name", "出发地不能为空");
        adm.iniInputcheck("#end_name", "目的地不能为空");

        //adm.iniInputcheck("#price", "价格不能为空");
    },
    iniaddState: function () {
        $('#add_line_class').bind('click', function () {
            $("#lineName,#price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;
                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;

                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var aboard_longitude = $("#p_position").attr("aboard_longitude");
                var aboard_latitude = $("#p_position").attr("aboard_latitude");
                var aboard_address = $("#p_position").html();

                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }
                if ((!Check.notEmpty(aboard_longitude) || aboard_longitude == 0)
                    || (!Check.notEmpty(aboard_latitude) || aboard_latitude == 0)) {
                    alert("请选择上车地点"); return;
                }

                var str = "mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&start_name=' + start_name + "&end_name=" + end_name;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code;
                str += "&aboard_longitude=" + aboard_longitude + "&aboard_latitude=" + aboard_latitude + "&aboard_address=" + aboard_address;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLineClass",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_class_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inirouteOps: function () {
        $("div.driver input[name='rtype']").bind("click", function () {
            var vv = $(this).val();
            if (vv == 1) {
                $(this).parents("table").find("tr.mod2").addClass("hidden");
                $(this).parents("table").find("tr.mod1").removeClass("hidden");
            } else {
                $(this).parents("table").find("tr.mod2").removeClass("hidden");
                $(this).parents("table").find("tr.mod1").addClass("hidden");
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
        this.inirouteOps();

    }
}
var line_class_editpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = line_class_editpage;
        adm.iniInputcheck("#start", "出发点不能为空");
        adm.iniInputcheck("#end", "目的地不能为空");
    },
    iniupPage: function () {
        $('#edit_line_class').bind('click', function () {
            $("#lineName,#price,#start,#end").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var code = "";
                var ecode = "";
                //出发地
                var pcode = $("div.start_place").find("select.prov").val();
                var ccode = $("div.start_place").find("select.city").val();
                var qcode = $("div.start_place").find("select.area").val();
                //目的地
                var epcode = $("div.end_place").find("select.prov").val();
                var eccode = $("div.end_place").find("select.city").val();
                var eqcode = $("div.end_place").find("select.area").val();

                var start_province_code = pcode;
                var start_city_code = ccode;
                var start_area_code = qcode;

                var end_area_code = eqcode;
                var end_city_code = eccode;
                var end_province_code = epcode;

                if (start_area_code == 0) {
                    alert("请选择出发地的区域"); return;
                }

                if (end_area_code == 0) {
                    alert("请选择目的地的区域"); return;
                }

                if (pcode == 0) {
                    code = "";
                } else if (ccode == 0) {
                    code = pcode;
                } else if (qcode == 0) {
                    code = ccode;
                } else {
                    code = qcode;
                }

                if (epcode == 0) {
                    ecode = "";
                } else if (eccode == 0) {
                    ecode = epcode;
                } else if (eqcode == 0) {
                    ecode = eccode;
                } else {
                    ecode = eqcode;
                }

                var aboard_longitude = $("#p_position").attr("aboard_longitude");
                var aboard_latitude = $("#p_position").attr("aboard_latitude");
                var aboard_address = $("#p_position").html();

                var line_id = $('#line_id').val();
                var start_name = $('#start_name').val();
                var end_name = $('#end_name').val();
                var start_address_code = code;
                var end_address_code = ecode;
                var mchid = $('#mchid').val();
                var branchid = $("div.branch").find("select.branch-select").val();

                var str = "id=" + line_id + "&mchid=" + mchid + "&branchid=" + branchid + "&start_address_code=" + start_address_code + "&end_address_code=" + end_address_code + '&start_name=' + start_name + "&end_name=" + end_name;
                str += "&start_province_code=" + start_province_code + "&start_city_code=" + start_city_code + "&start_area_code=" + start_area_code + "&end_area_code=" + end_area_code + "&end_city_code=" + end_city_code + "&end_province_code=" + end_province_code;
                str += "&aboard_longitude=" + aboard_longitude + "&aboard_latitude=" + aboard_latitude + "&aboard_address=" + aboard_address;

                var adm = linepage;

                if (!Check.AreaCode(ccode) || ccode == 0) {
                    alert("请选择出发地"); return;
                }
                if (!Check.AreaCode(eccode) || eccode == 0) {
                    alert("请选择目的地"); return;
                }
                if (!Check.notEmpty(start_name)) {
                    alert("请填写出发地"); return;
                }
                if (!Check.notEmpty(end_name)) {
                    alert("请填写目的地"); return;
                }
                if (!Check.notEmpty(branchid) || branchid == 0) {
                    alert("请选择所属分台"); return;
                }
                if ((!Check.notEmpty(aboard_longitude) || aboard_longitude == 0)
                    || (!Check.notEmpty(aboard_latitude) || aboard_latitude == 0)) {
                    alert("请选择上车地点"); return;
                }

                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLineClass",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_class_list";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

// 路线end
var line_class_schedulepage = {
    getLineClassScheduleList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var branchid = $('#search_branchid').val();
        var line_class_id = $('#line_class_id').val();
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        var start_name = $('#start_name').val();
        var end_name = $('#end_name').val();
        var str = "line_class_id=" + line_class_id + "&page=" + i + "&size=" + pageCount + "&branchid=" + branchid + "&start_time=" + start_time + "&end_time=" + end_time;
        str += "&start_date=" + start_date + "&end_date=" + end_date;
        str += "&start_name=" + start_name + "&end_name=" + end_name;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineClassScheduleList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_class_schedulepage.getLineClassScheduleList);
                }
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    o.currentDriver='<p class="number-order">李师傅<span>[10]</span></p>'
                    if (o.start_time_type == 2) {
                        o.start_time = o.start_earliest_time + '--' + o.end_latest_time;
                        o.startTimeStr = '<p><span>滚动发车</span></p>';
                        o.startTimeStr += ('<span class="startTimeStr">' + o.start_time.substr(0, 5) + '</span>');
                    } else {
                        o.startTimeStr = '<span class="startTimeStr">' + o.start_time.substr(0, 5) + '</span>'
                    }

                    if (o.scheduling_type == 2) {
                        o.scheduling_type_str = '自动排班<br/>(预售' + o.pre_sale_time + '天)';
                    } else {
                        o.scheduling_type_str = '手动排班';
                    }

                    $.each(o.date_list, function (dateI, dateO) {
                        var var_date_name = "date" + dateI;
                        if (dateO.is_schedule == 0) {
                            o[var_date_name] = "";
                        } else if (dateO.is_schedule == 1) {//已排班
                            o[var_date_name] = '<span style="color: darkgreen">√</span>';
                        } else if (dateO.is_schedule == 2) {//排班手动删除，恢复班次
                            o[var_date_name] = '<p><span style="color: red">×</span></p>';
                            o[var_date_name] += '<p>已手动删除，<br><a href="javascript:;" class="restoreLineClassTrain" line_class_train_id="' + dateO.line_class_train_id + '" start_date="' + dateO.date + '">恢复</a></p>'
                        }
                        console.log(dateO.date + " " + dateO.is_schedule);
                        console.log(var_date_name);
                        console.log(o);
                    });
                });
                var varr = [
                    'id',
                    'branchname',
                    'start_name',
                    'end_name',
                    'startTimeStr',
                    'currentDriver',
                    'start_time',
                    'scheduling_type_str',
                    'date0',
                    'date1',
                    'date2',
                    'date3',
                    'date4',
                    'date5',
                    'date6',
                    'date7',
                    'date8',
                    'date9',
                    'date10',
                    'date11',
                    'date12',
                    'date13',
                    'date14',
                    'date15',
                    'date16',
                    'date17',
                    'date18',
                    'date19',
                    'date20',
                    'date21',
                    'date22',
                    'date23',
                    'date24',
                    'date25',
                    'date26',
                    'date27',
                    'date28',
                    'date29'
                ];
                var tmp = '<tr class="{opp}">'
                    + '<td class="jumpId">{id}</td>'
                    + '<td>{branchname}</td>'
                    + '<td>{start_name}</td>'
                    + '<td>{end_name}</td>'
                    + '<td >{startTimeStr}</td>'
                    + '<td >{currentDriver}</td>'
                    + '<td>{scheduling_type_str}</td>'
                    + '<td>{date0}</td>'
                    + '<td>{date1}</td>'
                    + '<td>{date2}</td>'
                    + '<td>{date3}</td>'
                    + '<td>{date4}</td>'
                    + '<td>{date5}</td>'
                    + '<td>{date6}</td>'
                    + '<td>{date7}</td>'
                    + '<td>{date8}</td>'
                    + '<td>{date9}</td>'
                    + '<td>{date10}</td>'
                    + '<td>{date11}</td>'
                    + '<td>{date12}</td>'
                    + '<td>{date13}</td>'
                    + '<td>{date14}</td>'
                    + '<td>{date15}</td>'
                    + '<td>{date16}</td>'
                    + '<td>{date17}</td>'
                    + '<td>{date18}</td>'
                    + '<td>{date19}</td>'
                    + '<td>{date20}</td>'
                    + '<td>{date21}</td>'
                    + '<td>{date22}</td>'
                    + '<td>{date23}</td>'
                    + '<td>{date24}</td>'
                    + '<td>{date25}</td>'
                    + '<td>{date26}</td>'
                    + '<td>{date27}</td>'
                    + '<td>{date28}</td>'
                    + '<td>{date29}</td>'
                    + '</tr>';

                Base.GenTemp("lineClassScheduleList", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#lineClassScheduleList");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="36" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_class_schedulepage.getLineClassScheduleList(0);
        });
    },
    /*增加班次*/
    restoreLineClassTrain: function () {
        $("#lineClassScheduleList a.restoreLineClassTrain").live("click", function () {
            var msg = confirm("确认恢复该班次?");
            var line_class_train_id = $(this).attr("line_class_train_id");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doRestoreLineClassTrain",
                    params: "line_class_train_id=" + line_class_train_id,
                    sucrender: function (data) {
                        alert(data.data);
                        cur.parent().parent().html('<span style="color: darkgreen">√</span>');
                        //cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    /*删除单条线路*/
    deleSingleLineClassTrain: function () {
        $("#lineClassScheduleList a.lineClassDelete").live("click", function () {
            var msg = confirm("确认删除?");
            var line_class_train_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineClassTrain",
                    params: "line_class_train_id=" + line_class_train_id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);//11
        this.iniSearch();
        this.getLineClassScheduleList();
        this.deleSingleLineClassTrain();
        this.restoreLineClassTrain();
    }
};

// 班次时刻start
var line_class_train_listpage = {
    getLineClassTrainList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var branchid = $('#search_branchid').val();
        var line_class_id = $('#line_class_id').val();
        var status = $('#status').val();
        var line_class_train_no = $('#line_class_train_no').val();
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        var start_name = $('#start_name').val();
        var end_name = $('#end_name').val();
        var fields = "line_class_train_id,start_time,line_class_train_no,cn_scheduling_type,cn_dispatch_mode,cn_ferry,total_tickets,remain_tickets,market_price,price,channel_price,travel_time,create_time,branchid,driver_id,car_seats,seat_layout,seat_price,driver_info";
        var str = "fields=" + fields + "&line_class_id=" + line_class_id + "&page=" + i + "&size=" + pageCount + "&branchid=" + branchid + "&status=" + status + "&line_class_train_no=" + line_class_train_no + "&start_time=" + start_time + "&end_time=" + end_time;
        str += "&start_date=" + start_date + "&end_date=" + end_date;
        str += "&start_name=" + start_name + "&end_name=" + end_name;
        var s = {
            url: Path.web_path + "/Admin/LineManager/getLineClassTrainList",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_class_train_listpage.getLineClassTrainList);
                }
                $.each(data.data, function (i, o) {
                    console.log(o);
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    }
                    else {
                        o.opp = "";
                    }
                    var line_class_status = o.line_class_status;
                    if (line_class_status == 0) {
                        o.line_class_status_class = "color:green;";
                    } else {
                        o.line_class_status_class = "color:red;";
                    }
                    if (o.status == 1) {
                        o.status_message = '<select style="width: 80px" rel="' + o.line_class_train_id + '" class="ty_action"><option selected value="1">运行中</option><option value="0" style="color:red">已停运</option></select>';
                    } else {
                        o.status_message = '<select style="width: 80px; color:red" rel="' + o.line_class_train_id + '" class="ty_action"><option value="1">运行中</option><option selected value="0" style="color:red">已停运</option></select>';
                    }
                });
                var varr = [
                    'start_date',
                    'start_time',
                    'line_class_id',
                    'cn_scheduling_type',
                    'cn_dispatch_mode',
                    'route',
                    'is_seat_selection',
                    'is_seat_selection_info',
                    'price_info',
                    'branchname',
                    'driver_info',
                    'total_tickets',
                    'remain_tickets',
                    'sell_tickets',
                    'status_message',
                    'create_time',
                    'opp',
                    'line_class_train_id',
                    'line_class_train_no',
                    'price',
                    'channel_price',
                    'line_class_status_class',
                    //'driver_id',
                    //'branch_drivers',
                    //'car_seats',
                    //'seat_layout',
                    //'seat_price',
                    'cn_ferry',
                    'cn_ferry_show'
                ];

                var tmp = '<tr class="{opp}">'
                    + '<td width="50"><input ids="{line_class_train_id}" class="ckeckebox_e" type="checkbox"></td>'
                    + '<td>{start_date} <br/> {start_time}</td>'
                    + '<td>{line_class_id}/{line_class_train_id}</td>'
                    + '<td style="font-size:11px;">{route}</td>'
                    + '<td onclick="showPbBox(2,$(this))" style="cursor:pointer; color:blue;font-size:11px;" rel="{line_class_train_id}" id="{line_class_train_id}_driver_info">{driver_info}</td>'
                    + '<td style="font-size:11px;">{cn_scheduling_type}</td>'
                    + '<td style="font-size:11px;" id="{line_class_train_id}_is_seat_selection_info">{is_seat_selection_info}</td>'
                    + '<td style="font-size:11px;" id="{line_class_train_id}_cn_price">{price_info}</td>'
                    + '<td>{branchname}</td>'
                    + '<td id="{line_class_train_id}_cn_tickets"><b>{sell_tickets}</b> / {remain_tickets} / {total_tickets}</td>'
                    + '<td class="{cn_ferry_show}">{cn_ferry}</td>'
                    + '<td width="80">{status_message}</td>'
                    + '<td>'
                    + '<a href="javascript:" id="{line_class_train_id}_aid" rel="{line_class_train_id}" onclick="showPbBox(2,$(this))" class="update">编辑</a>'
                    + '<a href="' + Path.web_path + '/line_class_train_sell_order_list?line_class_train_no={line_class_train_no}" target="_blank">售票列表</a>'
                    + '<a href="javascript:;" class="lineClassDelete" rel="{line_class_train_id}">删除</a>'
                    + '</td>'
                    + '</tr>';

            

                Base.GenTemp("LineClassTrain", varr, data.data, tmp);
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#LineClassTrain");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="15" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_class_train_listpage.getLineClassTrainList(0);
        });
    },
    /*删除单条线路*/
    deleSingleLineClassTrain: function () {
        $("#LineClassTrain a.lineClassDelete").live("click", function () {
            var msg = confirm("确认删除?");
            var line_class_train_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteLineClassTrain",
                    params: "line_class_train_id=" + line_class_train_id,
                    sucrender: function () {
                        cur.parents("tr").remove();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doBatchOperation: function () {
        $("#doBatchOperation").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            console.log(str1)
            var str = 'id=' + str1.substring(0, str1.length - 1) + '&status=1&is_batch=1&is_train=1';
            var msg = confirm("确认要批量运行吗?");
            var line_class_id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doOperation: function () {
        $("#LineClassTrain select.ty_action").live("change", function () {
            var str = 'id=' + $(this).attr('rel') + '&status=' + $(this).val() + '&is_batch=0&is_train=1';
            var s = {
                async: true,
                url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                params: str,
                sucrender: function () {
                    alert('操作成功！')
                    location.reload();
                },
                failrender: function (data) {
                    alert(data.data);
                }
            };
            HGAjax.HAjax(s);
        });
    },
    doBatchShutdown: function () {
        $("#doBatchShutdown").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            var str = 'id=' + str1.substring(0, str1.length - 1) + '&status=0&is_batch=1&is_train=1';
            var msg = confirm("确认要批量停运吗?");
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchOperationLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    doBatchDel: function () {
        $("#doBatchDel").live("click", function () {
            if (LineClass_ids.length < 1) {
                alert('请先选择班线！');
                return false;
            }
            str1 = '';
            for (var index in LineClass_ids) {
                str1 += LineClass_ids[index] + '|';
            }
            var str = 'id=' + str1.substring(0, str1.length - 1) + '&is_train=1';
            var msg = confirm("确认批量删除吗?");
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doBatchDelLineClass",
                    params: str,
                    sucrender: function () {
                        alert('操作成功！')
                        location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);//11
        this.iniSearch();
        this.doOperation()
        this.doBatchDel()
        this.doBatchOperation();
        this.doBatchShutdown();
        this.getLineClassTrainList();
        this.deleSingleLineClassTrain();
    }
};
var line_class_trainpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = line_class_trainpage;
        adm.iniInputcheck("#start_time", "发车时间不能为空");
        adm.iniInputcheck("#total_tickets", "票数不能为空");
        adm.iniInputcheck("#market_price", "市场价格不能为空");
        adm.iniInputcheck("#price", "销售价格不能为空");
    },
    iniaddState: function () {
        $('#add_line_class_train').bind('click', function () {
            var trlen = $("#lr_table").find('tr').length;
            var mchid = $('#mchid').val();
            var line_class_id = $('#line_class_id').val();
            var class_train_date = $("#dt").val();
            if (class_train_date === undefined || class_train_date === null || class_train_date === "") {
                alert("请选择出班次日期");
                return false;
            }

            if (trlen > 20) {
                alert("一次性批量最多添加20条");
                return false;
            }
            if (trlen > 1) {
                var class_train_data = {};
                var class_train_len = $("#lr_table tr:last").attr('rel');
                for (var i = 1, j = 0; i <= class_train_len; i++) {
                    var class_train = {};
                    var start_time_element_name = 'input[name="class_train[' + i + '][start_time]"]';
                    console.log(start_time_element_name);
                    var start_time_element = $(start_time_element_name);
                    if (start_time_element.val() === undefined || start_time_element.val() === null || start_time_element.val() === "") {
                        alert("发车时间不能为空");
                        return false;
                    }
                    class_train["start_time"] = start_time_element.val();

                    var market_price_element_name = 'input[name="class_train[' + i + '][market_price]"';
                    var market_price_element = $(market_price_element_name);
                    if (market_price_element.val() === undefined || market_price_element.val() === null || market_price_element.val() === "") {
                        alert("市场价格不能为空");
                        return false;
                    }
                    class_train["market_price"] = market_price_element.val();

                    var price_element_name = 'input[name="class_train[' + i + '][price]"';
                    var price_element = $(price_element_name);
                    if (price_element.val() === undefined || price_element.val() === null || price_element.val() === "") {
                        alert("销售价格不能为空");
                        return false;
                    }
                    class_train["price"] = price_element.val();

                    var channel_price_element_name = 'input[name="class_train[' + i + '][channel_price]"';
                    var channel_price_element = $(channel_price_element_name);
                    if (channel_price_element.val() === undefined || channel_price_element.val() === null || channel_price_element.val() === "") {
                        alert("渠道价格不能为空");
                        return false;
                    }
                    class_train["channel_price"] = channel_price_element.val();

                    var travel_time_element_name = 'input[name="class_train[' + i + '][travel_time]"';
                    var travel_time_element = $(travel_time_element_name);
                    if (travel_time_element.val() === undefined || travel_time_element.val() === null) {
                        class_train["travel_time"] = "";
                    } else {
                        class_train["travel_time"] = travel_time_element.val();
                    }

                    var driver_element_name = 'select[name="class_train[' + i + '][driver]"';
                    var driver_id = $(driver_element_name).find("option:selected").val();
                    if (driver_id === undefined || driver_id === null || driver_id === "") {
                        alert("请选择司机");
                        return false;
                    }
                    class_train["driver_id"] = driver_id;

                    class_train_data[j] = class_train;
                    j++;
                }

                var str = "mchid=" + mchid + "&line_class_id=" + line_class_id + "&class_train_date=" + class_train_date + "&line_class_trains=" + JSON.stringify(class_train_data);
                str += "&driver_id=" + driver_id;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doCreateLineClassTrains",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/line_class_train_list?line_class_id=" + line_class_id;
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniaddState();
    }
}
var line_class_train_editpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = linepage;
        adm.iniInputcheck("#start_time", "出发时间不能为空");
        adm.iniInputcheck("#market_price", "市场价格不能为空");
        adm.iniInputcheck("#price", "销售价格不能为空");
    },
    iniupPage: function () {
        $('#edit_line_class_train').bind('click', function () {
            $("#start_time,#price").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var start_time = $("#start_time").val();
                //var total_tickets = $("#total_tickets").val();
                var market_price = $('#market_price').val();
                var price = $('#price').val();
                var channel_price = $('#channel_price').val();
                var travel_time = $('#travel_time').val();
                var mchid = $('#mchid').val();
                var line_class_id = $('#line_class_id').val();
                var line_class_train_id = $('#line_class_train_id').val();
                var driver_id = {};

                $("#driver input").each(function (k, v) {
                    if ($(this).is(":checked")) {
                        driver_id = $(this).val();
                    }
                });

                var str = "line_class_train_id=" + line_class_train_id + "&mchid=" + mchid + "&line_class_id=" + line_class_id + "&start_time=" + start_time + "&market_price=" + market_price + "&price=" + price + "&channel_price=" + channel_price + "&travel_time=" + travel_time;
                str += "&driver_id=" + driver_id;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doUpdateLineClassTrain",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/line_class_train_list?line_class_id=" + line_class_id;
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};
var line_class_train_sell_order_listpage = {
    getLineClassTrainSellOrderList: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var ono = $("#order_no").val();
        var pphone = $("#pphone").val();
        var dphone = $("#dphone").val();
        var type = $("#order_type").val();
        var pay_type = $("#pay_type").val();
        var order_state = $("#order_state").val();
        var search_start_time = $("#search_start_time").val();
        var search_end_time = $("#search_end_time").val();
        var line_id = $("#line_class_train_id").val();
        var car_tail_number = $("#car_tail_number").val();
        var driver_id = $("#line_driver_id option:selected").val();

        var isBranch = 1;
        if (!$("#branch_type").val()) {
            isBranch = 0;
        }
        var fields = "type,ID_number,ID_real_name,virtual_driver,price,real_price,offer_channel_price,start_address,end_address,order_id,order_no,reseverd_phone,is_temp,passenger.passenger_id,passenger.name,passenger.cellphone,driver.name,driver.cellphone,driver.driver_id,type_ride,create_time,book_seating,state,start_time,is_buy_insurance,offer_boss_price,insurance_price,merge_price,coupon_price,real_price,total_booking_seats,refund_total_amount";
        var str = "fields=" + fields + "&page=" + i + "&size=" + pageCount + "&type=" + type + "&pay_type=" + pay_type + "&order_state=" + order_state + "&order_no=" + ono + "&passenger_cellphone=" + pphone;
        str += "&driver_cellphone=" + dphone + "&search_start_time=" + search_start_time + "&search_end_time=" + search_end_time;
        str += "&line_id=" + line_id + "&car_tail_number=" + car_tail_number + "&driver_id=" + driver_id;
        var s = {
            url: Path.web_path + "/Admin/OrderManager/getSellOrders",
            params: str,
            sucrender: function (data) {
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", line_class_train_sell_order_listpage.getLineClassTrainSellOrderList);
                }
                var tmp = '';
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    } else {
                        o.opp = "";
                    }
                    o.passenger_id = o.passenger.passenger_id;
                    if (!o.passenger.name) {
                        o.pinfo = "临时乘客" + "<br/>";
                    } else {
                        o.pinfo = o.passenger.name;
                    }

                    if (!o.passenger.cellphone) {
                        o.pphone = '无';
                    } else {
                        o.pphone = o.passenger.cellphone;
                    }
                    if (!o.driver || !o.driver.name || !o.driver.cellphone) {
                        o.dinfo = "";
                    } else {
                        o.dinfo = o.driver.name + "<br/>" + (typeof o.driver.cellphone == "undefined" ? "" : o.driver.cellphone) + "<br/>";
                        o.dinfo += o.driver.car_tail_number;
                    }
                    if (!o.branchName) {
                        o.branch_name = "";
                    } else {
                        o.branch_name = o.branchName;
                    }
                    if (!o.route) {
                        o.route = "";
                    } else {
                        o.route = o.route;
                    }
                    tmp += '<tr>';
                    //订单编号
                    tmp += '<td><a href="' + Path.web_path + '/admin_orderdetail?order_no=' + o.order_no + '">' + o.order_no + '</a></td>';
                    //出发时间
                    tmp += '<td>' + o.start_time + '</a></td>';
                    //所属分台
                    if (isBranch == 0) {
                        tmp += '<td>' + o.branch_name + '</a></td>';
                    }
                    //购车人手机号
                    tmp += '<td>' + o.pphone + '</a></td>';
                    //联系人手机号
                    tmp += '<td>' + o.reseverd_phone + '</a></td>';

                    tmp += '<td>' + o.generation + '</td>'
                        /* + '<td>' + o.pphone + '</td>'*/
                        + '<td style="text-align: left">' + o.route + '</td>'
                        + '<td>' + o.dinfo + '</td>'
                        + '<td>' + o.book_seating + ' / ' + o.total_booking_seats + ' / ' + o.real_price + '</td>'
                        + '<td style="text-align: center">' + o.pay_message + '<hr style="color: #f1f1f1">' + o.state_message + '</td>';
                    //下单时间
                    tmp += '<td>' + o.create_time + '</td>';
                    tmp += '<td class="lst-child">';
                    if (o.state == 1 && o.appoint == 0) {
                        tmp += '<a href="javascript:void();" onclick="appointOrder(this,' + o.order_id + ');">派单 </a>';
                    }
                    if (o.order_refund_onoff == 1) {
                        //小于2023-05-18 00:00:00时间，支持退票
                        var defaultTime = new Date('2023-05-18 00:00:00');
                        var diffTime = new Date(o.create_time);
                        if (diffTime <= defaultTime) {
                            tmp += '<a href="javascript:;" class="refund_order" rel="' + o.order_id + '">退款 </a>';
                        }
                    }
                    if (o.order_over_onoff == 1) {
                        tmp += '<a href="javascript:;" class="over_order" rel="' + o.order_id + '">完成订单 </a>';
                    }
                    if (o.order_close_onoff == 1) {
                        tmp += '<a href="javascript:;" class="close_order" rel="' + o.order_id + '">关闭订单 </a>';
                    }
                    if (o.state <= 3) {
                        tmp += '<a href="' + Path.web_path + '/admin_modifydetail?order_no=' + o.order_no + '">修改订单 </a>';
                    }
                    tmp += '<a href="' + Path.web_path + '/admin_orderdetail?order_no=' + o.order_no + '">查看 </a>';
                    tmp += '</td>'
                        + '</tr>';
                });
                var dom = $("#orderList");
                if (dom[0].localName == "table" || dom[0].nodeName == "TABLE") {
                    dom.find("td").parents("tr").remove();
                    dom.append(tmp);
                } else {
                    dom.html(tmp);
                }
                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#orderList");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="15" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    iniSearch: function () {
        $("#doSearchact").bind("click", function () {
            line_class_train_sell_order_listpage.getLineClassTrainSellOrderList(0);
        });
    },
    /*完成订单操作*/
    doOverOrder: function () {
        $("#orderList a.over_order").live("click", function () {
            if (confirm("确认完成订单吗")) {
                var order_id = $(this).attr('rel');
                var str = 'order_id=' + order_id;
                var S = {
                    url: Path.web_path + "/Admin/OrderManager/doOverOrder",
                    params: str,
                    sucrender: function (data) {
                        if (data.ret) {
                            alert('完成成功');
                            location.reload();
                        } else {
                            alert(data.data);
                        }
                    }
                };
                HGAjax.HAjax(S);
            }
        });
    },
    /*关闭订单操作*/
    doCloseOrder: function () {
        $("#orderList a.close_order").live("click", function () {
            if (confirm("确认关闭吗")) {
                var order_id = $(this).attr('rel');
                var str = 'order_id=' + order_id;
                var S = {
                    url: Path.web_path + "/Admin/OrderManager/doCloseOrder",
                    params: str,
                    sucrender: function (data) {
                        if (data.ret) {
                            alert('关闭成功');
                            location.reload();
                        } else {
                            alert(data.data);
                        }
                    },
                    failrender: function (res) {
                        alert(res.data);
                    }
                };
                HGAjax.HAjax(S);
            }
        });
    },
    /*订单退款操作*/
    doRefundOrder: function () {
        $("#orderList a.refund_order").live("click", function () {
            if (confirm("确认退款吗？一旦确认退款将无法撤销")) {
                var order_id = $(this).attr('rel');
                var str = 'order_id=' + order_id;
                var S = {
                    url: Path.web_path + "/Admin/OrderManager/doRefundOrder",
                    params: str,
                    async: true,
                    sucrender: function (data) {
                        if (data.ret) {
                            alert('退款成功');
                            location.reload();
                        } else {
                            alert(data.data);
                        }
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(S);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);//11
        this.iniSearch();
        this.doOverOrder();
        this.doCloseOrder();
        this.doRefundOrder();
        this.getLineClassTrainSellOrderList();
    }
};
// 路线end


/*线路管理 ->带货管理  */
var admin_take_goods_pricepage = {
    getOrders: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var dom = $("#agencylist");
        // dom.find("td").parents("tr").remove();
        // dom.append(COMMONTEMP.TP(9));

        var fields = "";
        var str = "page=" + i + "&size=" + pageCount;
        var s = {
            url: Path.web_path + "/Admin/LineManager/doTakeGoodsPriceList",
            params: str,
            sucrender: function (data) {
                console.log(data);
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", admin_take_goods_pricepage.getOrders);
                }
                var tmp = '';
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    } else {
                        o.opp = "";
                    }
                    if (o.is_default == 1) {
                        o.status = "是";
                        o.default = '';
                    } else {
                        o.status = "不是";
                        o.default = '<a href="javascript:;" class="default" rel="' + o.take_goods_price_id + '">设为默认</a>';
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }

                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                    //<th class="th1">编号</th>
                    //    <th class="th2">商户名</th>
                    //    <th class="th3">基础价格（元）</th>
                    //<th class="th3">基础重量(KG)</th>
                    //    <th class="th3">基础距离（KM）</th>
                    //<th class="th3">是否默认</th>
                    tmp += '<tr class="' + o.opp + '">'
                        + '<td>' + o.take_goods_price_id + '</a></td>';
                    tmp += '<td>' + o.mchname + '</td>'
                        + '<td>' + o.name + '</td>'
                        + '<td>' + o.base_price + '</td>'
                        + '<td>' + o.base_weight + '</td>'
                        + '<td>' + o.base_kilometre + '</td>'
                        + '<td>' + o.channel_rofit_price + '</td>'
                        + '<td>' + o.status + '</td>'
                        + '<td>' + o.businessTimeStr + '</td>'
                        + '<td>' + o.set_order_time + '分钟</td>'
                        + '<td class="lst-child">'
                        + o.default
                        + '<a href="' + Path.web_path + '/admin_edit_take_goods_price?tgp_id=' + o.take_goods_price_id + '">修改</a>'
                        + '<a href="javascript:;" class="adelete" rel="' + o.take_goods_price_id + '">删除</a>'
                        + '<a href="javascript:;" onclick="setIsShow($(this),' + o.take_goods_price_id + ')"  line_type="4" is_show="' + o.is_show + '" >' + o.is_show_message + '</a>'
                    tmp += '</td>'
                        + '</tr>';
                });
                var dom = $("#agencylist");
                if (dom[0].localName == "table" || dom[0].nodeName == "TABLE") {
                    dom.find("td").parents("tr").remove();
                    dom.append(tmp);
                } else {
                    dom.html(tmp);
                }

                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#agencylist");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="10" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    deleTargetItem: function () {
        $("#agencylist a.adelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteTakeGoodsPrice",
                    params: "tgp_id=" + id,
                    sucrender: function () {
                        window.location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    defaultTargetItem: function () {
        $("#agencylist a.default").live("click", function () {
            var msg = confirm("确认设为默认?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDefaultTakeGoodsPrice",
                    params: "tgp_id=" + id,
                    sucrender: function () {
                        window.location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    iniSearch: function () {
        $("#order_search").bind("click", function () {
            admin_orderpage.getOrders(0);
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.getOrders();
        /*获取列表数据*/
        this.iniSearch();//搜索
        this.deleTargetItem();
        this.defaultTargetItem();//设置默认数据
    }
};

//线路管理--添加带货
var admin_add_take_goods_pricepage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_add_take_goods_pricepage;
        adm.iniInputcheck("#name", "计费标准名称不能为空");
        adm.iniInputcheck("#base_price", "基础价格不能为空");
        adm.iniInputcheck("#base_weight", "基础重量不能为空");
        adm.iniInputcheck("#base_kilometre", "基础距离不能为空");
    },
    iniupPage: function () {
        $('#add_tgp').on('click', function () {
            $("#name").trigger("blur");
            $("#base_price").trigger("blur");
            $("#base_weight").trigger("blur");
            $("#pribase_weightce").trigger("blur");
            $("#base_kilometre").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var name = $("#name").val();
                var base_price = $("#base_price").val();
                var base_weight = $("#base_weight").val();
                var base_kilometre = $("#base_kilometre").val();
                var pribase_weightce = $("#pribase_weightce").val();
                var plus_weight_price = $("#plus_weight_price").val();
                var plus_kilometre_price = $("#plus_kilometre_price").val();
                var channel_rofit_price = $("#channel_rofit_price").val();
                var start_appointment_time = $('#start_appointment_time').val()
                var end_appointment_time = $('#end_appointment_time').val()
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val()
                var summary = $("#summary").val();
                var str = "name=" + name + "&base_price=" + base_price + "&base_weight=" + base_weight + "&pribase_weightce=" + pribase_weightce + "&plus_weight_price=" + plus_weight_price + "&plus_kilometre_price=" + plus_kilometre_price + "&channel_rofit_price=" + channel_rofit_price + "&summary=" + summary + "&base_kilometre=" + base_kilometre + '&set_order_time=' + $('#set_order_time').val() + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doAddTakeGoodsPrice",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/admin_take_goods_price";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

//线路管理--编辑带货
var admin_edit_take_goods_pricepage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_edit_take_goods_pricepage;
        adm.iniInputcheck("#name", "计费标准名称不能为空");
        adm.iniInputcheck("#base_price", "基础价格不能为空");
        adm.iniInputcheck("#base_weight", "基础重量不能为空");
        adm.iniInputcheck("#base_kilometre", "基础距离不能为空");
    },
    iniupPage: function () {
        $('#edit_tgp').on('click', function () {
            $("#name").trigger("blur");
            $("#base_price").trigger("blur");
            $("#base_weight").trigger("blur");
            $("#pribase_weightce").trigger("blur");
            $("#base_kilometre").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var id = $("#id").val();
                var name = $("#name").val();
                var base_price = $("#base_price").val();
                var base_weight = $("#base_weight").val();
                var base_kilometre = $("#base_kilometre").val();
                var pribase_weightce = $("#pribase_weightce").val();
                var plus_weight_price = $("#plus_weight_price").val();
                var plus_kilometre_price = $("#plus_kilometre_price").val();
                var channel_rofit_price = $("#channel_rofit_price").val();
                var start_appointment_time = $('#start_appointment_time').val();
                var end_appointment_time = $('#end_appointment_time').val();
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val();
                var summary = $("#summary").val();
                var str = "id=" + id + "&name=" + name + "&base_price=" + base_price + "&base_weight=" + base_weight + "&pribase_weightce=" + pribase_weightce + "&plus_weight_price=" + plus_weight_price + "&plus_kilometre_price=" + plus_kilometre_price + "&channel_rofit_price=" + channel_rofit_price + "&summary=" + summary + "&base_kilometre=" + base_kilometre + '&set_order_time=' + $('#set_order_time').val() + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doEditTakeGoodsPrice",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/admin_take_goods_price";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};


/*线路管理 ->代办管理  */
var admin_agencypage = {
    getOrders: function (i) {
        if (!!!i) {
            i = 0;
        }
        i += 1;
        var dom = $("#agencylist");
        // dom.find("td").parents("tr").remove();
        // dom.append(COMMONTEMP.TP(9));
        var mchid = $('#mchid').val();
        var branchid = $('#branchid').val();

        var fields = "";
        var str = "page=" + i + "&size=" + pageCount + "&mchid=" + mchid + "&branchid=" + branchid;
        var s = {
            url: Path.web_path + "/Admin/LineManager/doAgencyList",
            params: str,
            sucrender: function (data) {
                console.log(data);
                var len = data.count;
                if (i == 1) {
                    Base.iniPagination(len, "#pagination", admin_agencypage.getOrders);
                }
                var tmp = '';
                $.each(data.data, function (i, o) {
                    if (i % 2 != 0) {
                        o.opp = "trg";
                    } else {
                        o.opp = "";
                    }
                    if (o.is_show == 0) {
                        o.is_show = 1;
                        o.is_show_message = '上架';
                    } else {
                        o.is_show = 2;
                        o.is_show_message = '下架';
                    }
                    if (o.business_time_type == 0) {
                        o.businessTimeStr = '全天24小时营业'
                    } else {
                        o.businessTimeStr = o.start_appointment_time + '--' + o.end_appointment_time
                    }
                    tmp += '<tr class="' + o.opp + '">'
                        + '<td>' + o.agency_id + '</a></td>';
                    tmp += '<td>' + o.mchname + '</td>'
                        + '<td>' + o.branch_name + '</td>'
                        + '<td>' + o.name + '</td>'
                        + '<td>' + o.price + '</td>'
                        + '<td>' + o.channel_price + '</td>'
                        + '<td>' + o.businessTimeStr + '</td>'
                        + '<td>' + o.set_order_time + '分钟</td>'
                        + '<td class="lst-child">'
                        + '<a href="' + Path.web_path + '/admin_edit_agency?agency_id=' + o.agency_id + '">修改</a>'
                        + '<a href="javascript:;" class="adelete" rel="' + o.agency_id + '">删除</a>'
                        + '<a href="javascript:;" onclick="setIsShow($(this),' + o.agency_id + ')"  line_type="5" is_show="' + o.is_show + '" >' + o.is_show_message + '</a>'
                    tmp += '</td>'
                        + '</tr>';
                });
                var dom = $("#agencylist");
                if (dom[0].localName == "table" || dom[0].nodeName == "TABLE") {
                    dom.find("td").parents("tr").remove();
                    dom.append(tmp);
                } else {
                    dom.html(tmp);
                }

                if (document.all && document.querySelector && !document.addEventListener) {
                    $("table.list_tb tr:nth-child(odd)").css("background-color", "#f2f9fd");
                }
            },
            failrender: function () {
                Base.iniPagination(0, "#pagination", null);
                var dom = $("#agencylist");
                dom.find("td").parents("tr").remove();
                dom.append('<tr class="nodata"><td colspan="10" class="red"><p>暂时没有相关数据</p></td></tr>');
            }
        };
        HGAjax.HAjax(s);
    },
    deleTargetItem: function () {
        $("#agencylist a.adelete").live("click", function () {
            var msg = confirm("确认删除?");
            var id = $(this).attr("rel");
            var cur = $(this);
            if (msg == true) {
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doDeleteAgency",
                    params: "agency_id=" + id,
                    sucrender: function () {
                        window.location.reload();
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    iniSearch: function () {
        $("#order_search").bind("click", function () {
            admin_orderpage.getOrders(0);
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.getOrders();
        /*获取列表数据*/
        this.iniSearch();//搜索
        this.deleTargetItem();
    }
};

//线路管理--添加代办
var admin_add_agencypage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_add_agencypage;
        adm.iniInputcheck("#name", "名称不能为空");
        adm.iniInputcheck("#price", "请输入价格");
    },
    iniupPage: function () {
        $('#add_agency').on('click', function () {
            $("#price").trigger("blur");
            $("#name").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var name = $("#name").val();
                var summary = $("#summary").val();
                var price = $("#price").val();
                var channel_price = $("#channel_price").val();
                var branch = $("#branch").val();
                if (branch == 0) {
                    alert("请选择分台"); return;
                }
                var start_appointment_time = $('#start_appointment_time').val();
                var end_appointment_time = $('#end_appointment_time').val();
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val();
                var str = "name=" + name + "&summary=" + summary + "&price=" + price + "&channel_price=" + channel_price + "&branch=" + branch + '&set_order_time=' + $('#set_order_time').val() + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doAddAgency",
                    params: str,
                    sucrender: function () {
                        alert("添加成功!");
                        location.href = Path.web_path + "/admin_agency";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

//线路管理--编辑代办
var admin_edit_agencypage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_edit_agencypage;
        adm.iniInputcheck("#name", "名称不能为空");
        adm.iniInputcheck("#price", "请输入价格");
    },
    iniupPage: function () {
        $('#edit_agency').on('click', function () {
            $("#name").trigger("blur");
            $("#price").trigger("blur");
            var elen = $("div.driver").find("span.errorTip").length;
            if (elen == 0) {
                var id = $("#id").val();
                var name = $("#name").val();
                var summary = $("#summary").val();
                var price = $("#price").val();
                var channel_price = $("#channel_price").val();
                var branch = $("#branch").val();
                if (branch == 0) {
                    alert("请选择分台"); return;
                }
                var start_appointment_time = $('#start_appointment_time').val();
                var end_appointment_time = $('#end_appointment_time').val();
                var start_time_type = $('#start_time_type input[name=start_time_type]:checked').val();
                var str = "id=" + id + "&name=" + name + "&summary=" + summary + "&price=" + price + "&channel_price=" + channel_price + "&branch=" + branch + '&set_order_time=' + $('#set_order_time').val() + '&business_time_type=' + start_time_type + "&start_appointment_time=" + start_appointment_time + "&end_appointment_time=" + end_appointment_time;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doEditAgency",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/admin_agency";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

//线路管理--编辑顺风车计价
var mileage_price_editpage = {
    iniInputcheck: function (obj, msg) {
        $(obj).focus(function () {
            $(this).removeClass("red_border");
            $(this).addClass("green_border");
            $(this).parent().find("span.errorTip").remove();
        }).blur(function () {
            var cur = $(this);
            var str = cur.val().replace(new RegExp(" ", "g"), "");
            if (str == "") {
                cur.addClass("red_border");
                cur.parent().find("span.errorTip").remove();
                var tmp = COMMONTEMP.T0002;
                tmp = tmp.replace("{emsg}", msg);
                cur.parent().append(tmp);
            } else {
                cur.removeClass("green_border");
            }
        });
    },
    inirequireCheck: function () {
        var adm = admin_edit_take_goods_pricepage;
        adm.iniInputcheck("#name", "计费标准名称不能为空");
        adm.iniInputcheck("#base_price", "基础价格不能为空");
        adm.iniInputcheck("#base_kilometre", "基础公里不能为空");
    },
    iniupPage: function () {
        $('#edit_mileage_price').on('click', function () {
            $("#name").trigger("blur");
            $("#base_price").trigger("blur");
            $("#base_kilometre").trigger("blur");
            var elen = $("div.mileage_price").find("span.errorTip").length;
            if (elen == 0) {
                var name = $("#name").val();
                var base_price = $("#base_price").val();
                var base_kilometre = $("#base_kilometre").val();
                var plus_kilometre_price = $("#plus_kilometre_price").val();
                var summary = $("#summary").val();
                var str = "&name=" + name + "&base_price=" + base_price + "&plus_kilometre_price=" + plus_kilometre_price + "&summary=" + summary + "&base_kilometre=" + base_kilometre;
                var s = {
                    url: Path.web_path + "/Admin/LineManager/doEditMileagePrice",
                    params: str,
                    sucrender: function () {
                        alert("修改成功!");
                        location.href = Path.web_path + "/mileage_price_edit";
                    },
                    failrender: function (data) {
                        alert(data.data);
                    }
                };
                HGAjax.HAjax(s);
            }
        });
    },
    inipage: function () {
        Base.iniLeftNav(1);
        this.inirequireCheck();
        this.iniupPage();
    }
};

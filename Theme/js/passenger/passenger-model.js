/**
 * 2023.10.19
 *
 */

var map,
  new_map,
  d_map,
  timer,
  picker,
  positionPicker,
  driving,
  polyPositionPicker,
  pointDriving;
//全局变量保存座位与保险信息
var seat_lists = [];
var markers = [];
var service_list = [];
var charter_list = [];
var city_area_list = []; //每次展开城市筛选获取的城市列表
var startMarker = null;
var pointCheckMarker = null;
var district = null;
var customVector;
var priceVector = [];

// var start_city_list = [], end_city_list = [];
//获取已开通的城市
var list = [];

var mapCity;
var mapCitypoly = [];
var mapServiceArea = []; //快车可用区域范围点集合，用于比较当前位置是否在区域内部

function isInMiniProgramWebView() {
  var userAgent = navigator.userAgent;
  return /miniProgram/.test(userAgent); // 注意：这里的 miniProgram 是示例，实际根据用户代理字符串中是否有特定标识来判断
}

function flatten(arr) {
  // 合并多个数组
  const result = [];
  for (let i = 0; i < arr.length; i++) {
    for (let j = 0; j < arr[i].length; j++) {
      result.push(arr[i][j]);
    }
  }
  return result;
}

async function defaultWordsSearch(city, keywords, type = "start") {
  return new Promise((resolve, reject) => {
    const citySearchOpt = {
      citylimit: true,
      city: city,
    };
    const citySearch_ = new AMap.AutoComplete(citySearchOpt);
    citySearch_.search(keywords, (status, result) => {
      if (status === "complete") {
        let list_ipo_Html = "";
        for (let i = 0; i < result.tips.length; i++) {
          if (result.tips[i].location.lat) {
            list_ipo_Html += `<div class="${
              type === "start" ? "address_item" : "address_item_end"
            }"  adcode="${result.tips[i].adcode}" name="${
              result.tips[i].name
            }" lat="${result.tips[i].location.lat}" lng="${
              result.tips[i].location.lng
            }">
              <i class="address-dot"></i>
              <div class="title">${result.tips[i].name}</div>
              <div class="description">${result.tips[i].district}</div>
            </div>`;
          }
        }
        resolve(list_ipo_Html);
      } else {
        resolve("");
      }
    });
  });
}

function jumpCharter() {
  togglePopupExpress(true, "#j-popup-half-carpool");
  document.getElementById("chart_car").click();
}

async function fetchCates() {
  return new Promise((resolve, reject) => {
    $.ajax({
      url:
        "/Home/Category/getCategories?type=5&callback=" +
        sessionStorage.getItem("callback"),
      type: "get",
      success: function (json) {
        const data = JSON.parse(json);
        resolve(data.ret ? data.data : []);
      },
      error: function () {
        reject("error");
      },
      timeout: 300000,
    });
  });
}

function polyLocation() {
  getWxLocation()
    .then(async (res) => {
      const [lng, lat] = res;
      poly_map.setZoomAndCenter(15, new AMap.LngLat(lng, lat), false, 300);
      console.log("手动定位成功");
    })
    .catch(() => {
      console.log("手动定位失败");
      lui.toast({
        position: "middle",
        text: "定位失败",
      });
    });
}
//留言
function openMessage(el) {
  togglePopupExpress(false, "#j-popup-full-message");
  $(".message-sure-btn").attr("el", el);
  // openMessage
}
//乘车人
function openexchangeFare(el) {
  togglePopupExpress(false, "#j-popup-full-passenger");
  $(".passenger-sure-btn").attr("el", el);
  // openMessage
}
$(document).on("click", ".passenger-sure-btn", function () {
  const el = $(this).attr("el");
  const val = $(".passenger-info-input-cell").val();

  if (!isValidPhoneNumber(val)) {
    lui.toast({
      position: "middle",
      text: "请输入正确的联系方式",
    });
    return;
  }
  $(`.${el}`).html(
    val ? `尾号 ${val.slice(7, 11)}` : `<span class="default-info">代叫</span>`
  );
  $(`.${el}`).attr("reseverd_tel", val);
  togglePopupExpress(false, "#j-popup-full-passenger");
});

$(document).on("click", "#j-popup-half-charter .info-container", function () {
  //打开包车详情
  $(".agreement-full-cover").show();
});

$(document).on("click", ".message-sure-btn", function () {
  const el = $(this).attr("el");
  // <span class="default-info">行程备注</span>
  const val = $(".message-info-input-area").val();
  $(`.${el}`).html(val ? val : `<span class="default-info">行程备注</span>`);
  $(`.${el}`).attr("reseverd_info", val);
  togglePopupExpress(false, "#j-popup-full-message");
});
$(document).on("click", ".ui_actionSheet_item", function () {
  const number = $(this).attr("number");
  $(".passenger-number-warp").attr("number", number);
  $(".passenger-number-warp").html($(this).html());
  const price = $("#j-popup-half-carpool .view-label-price").attr("price");
  const showcarpool = toDecimal(Number(number) * Number(price));
  $("#j-popup-half-carpool .view-label-price").html(showcarpool);
  toggleActionSheet("#js_actionSheet");
});
$(document).on("click", ".full-menus .item-menu", function () {
  const pageid = $(this).attr("bid");
  document.getElementById(pageid).click();
  togglePopupExpress(true, "#j-popup-half-menu");
  indexPage.pageControl(pageid);
});

$(document).on("click", ".quick-bussess .item-quick", function () {
  const pageid = $(this).attr("bid");
  
  // 掌上巴士商户自定义跳转页面逻辑
  const callback = sessionStorage.getItem("callback");
  const consultPages = $("#consult_pages").val();
  const consultCodes = $("#consult_codes").val();
  if(consultCodes && consultPages && callback && 
     consultCodes.split(',').includes(callback) && 
     consultPages.split(',').includes(pageid)) {
    window.location.href = `/passenger_consult?callback=${callback}`;
    return;
  }
  
  if (pageid === "phone") {
    const { lng_ = "", lat_ = "" } = sessionStorage.getItem("sessionLocation")
      ? JSON.parse(sessionStorage.getItem("sessionLocation"))
      : {};
    window.location.href = `/passenger_call?lng=${lng_}&lat=${lat_}&callback=${sessionStorage.getItem(
      "callback"
    )}`;
    return;
  }
  if (pageid === "ai_chat") {
    window.location.href = "http://c.higgses.com/micro-apps/chat/";
    return;
  }
  sessionStorage.setItem("flag_", pageid);
  window.location.href = `/passenger_index/callback/${sessionStorage.getItem(
    "callback"
  )}`;
});
$(document).on("click", ".recharge-text", function () {
  window.location.href =
    "/passenger_recharge?callback=" + sessionStorage.getItem("callback");
});

//选择车型
$(document).on("click", ".item-car-seat", function () {
  $(this).addClass("ui_checkbox_checked");
  $(this).siblings().removeClass("ui_checkbox_checked");
  $(this).find("input").attr("checked", true);
  $(this).siblings().find("input").attr("checked", false);
  $(".charterOrderAction").attr("pid", $(this).attr("line_chartered_price_id"));
});

$(document).on("click", ".thanos-tabbar li", function () {
  if (!$(this).hasClass("is-tranform")) {
    lui.toast({
      position: "middle",
      text: `即将开放`,
    });
  }
});
$(document).on("click", ".clear-driver", function () {
  var newurl = removeDepAndAfter(window.location.href);
  window.location.href = newurl;

  $(".header-info-container").html('<div class="info-text"></div>');
});

//切换日期
$(document).on("click", ".date-scroll .date-item", function () {
  $(this).addClass("selected-date-line");
  $(this).siblings().removeClass("selected-date-line");
  var date = $(this).attr("date");
  const index_ = Number($(this).attr("index")); //当前下标
  if (index_ > 2) {
    //构造横向滚动过渡效果
    $(".date-scroll")
      .stop()
      .animate({ scrollLeft: 55 * (index_ - 2) }, 300);
  } else {
    $(".date-scroll").stop().animate({ scrollLeft: 0 }, 300);
  }
  indexPage.getregular(date);
});
function charterBack() {
  togglePopupExpress(false, "#popup-charter-list", "right");
}
function trainsBack() {
  var newurl = updateQueryStringParameter(window.location.href, "cid", "");
  window.history.replaceState(
    {
      path: newurl,
    },
    "",
    newurl
  );
  togglePopupExpress(false, "#j-popup-right", "right");
}

async function checkCityUsed(city_name, bussess_) {
  // 业态 1:拼车 2:包车 5:班线 7:快车 11:出租车
  const options = {
    taxi: 11,
    fast_car: 7,
    regular: 5,
    carpool: 1,
    chart_car: 2,
  };
  return new Promise((resolve, reject) => {
    if (["charge", "goods", "free_car"].includes(bussess_)) {
      resolve(true);
      return;
    }
    $.ajax({
      url: `/Passenger/Order/queryEndpointCityEnabled`,
      type: "get",
      dataType: "json",
      data: {
        city_name: city_name,
        type: options[bussess_],
        callback: sessionStorage.getItem("callback"),
      },
      success: function (data) {
        if (data.ret) {
          resolve(data.data.service_enabled);
        } else {
          resolve(false);
        }
      },
      fail: function () {
        reject("出错了");
      },
    });
  });
}

function openTrainPage() {
  $(".full-trains-container").html(`<div class="new-trainList"></div>
  <p class="unable-title">----以下为不可预定班次----</p>
  <div class="unabled-list"></div>`);
  $(".unable-title").css("display", "none");
  togglePopupExpress(false, "#j-popup-right", "right");
}

function renderHistory(data) {
  let historyDom = "";
  data.map((item) => {
    historyDom += `<div class='item-history-cell' 
    end_code="${item.end.end_code}"
    end_level="${item.end.end_level}"
    end_name="${item.end.end_name}"
    start_code="${item.start.start_code}"
    start_level="${item.start.start_level}"
    start_name="${item.start.start_name}"
    >${item.start.start_name}-${item.end.end_name}</div>`;
  });
  return historyDom;
}

function toDecimal(x) {
  var f = parseFloat(x);
  if (isNaN(f)) {
    return;
  }
  f = Math.round(x * 100) / 100;
  return f;
}

function checkSearch(clickType, codelevel, adcode, linename) {
  let ad_code = "";
  let city_name = "";
  let line_name = "";
  let area_name = "";
  const codeType = $("#j-popup-full-citys").attr("codeType");
  const bussess_ = $("#j-popup-full-citys").attr("bussess_");
  switch (codelevel) {
    case "city":
      city_name = linename;
      if (bussess_ === "train") {
        $(codeType === "start" ? "#strart_city" : "#end_city").html(
          `<span class="station-selected">${city_name}</span>`
        );
      } else {
        $(
          codeType === "start" ? ".start-pinche-place" : ".end-pinche-place"
        ).html(`<b class="hadSelcetedCode">${city_name}</b>`);
      }

      break;
    case "area":
      ad_code = adcode;
      area_name = linename;
      if (bussess_ === "train") {
        $(codeType === "start" ? "#strart_city" : "#end_city").html(
          `<span class="station-selected">${area_name}</span>`
        );
      } else {
        $(
          codeType === "start" ? ".start-pinche-place" : ".end-pinche-place"
        ).html(`<b class="hadSelcetedCode">${area_name}</b>`);
      }
      break;
    default:
      line_name = linename;
      ad_code = adcode;
      if (bussess_ === "train") {
        $(codeType === "start" ? "#strart_city" : "#end_city").html(
          `<span class="station-selected">${line_name}</span>`
        );
      } else {
        $(
          codeType === "start" ? ".start-pinche-place" : ".end-pinche-place"
        ).html(`<b class="hadSelcetedCode">${line_name}</b>`);
      }
  }
  if (codeType === "start") {
    $(".citys-unabled-view-tip").html("");
    bussess_ === "train"
      ? $("#strart_city").attr({
          codelevel: codelevel,
          ad_code: ad_code,
          city_name: city_name,
          line_name: line_name,
          area_name: area_name,
        })
      : $(".start-pinche").attr({
          codelevel: codelevel,
          ad_code: ad_code,
          city_name: city_name,
          line_name: line_name,
          area_name: area_name,
        });
    let toName = $("#end_city .station-selected").html();
    if (bussess_ === "train") {
      if (!!!toName) {
        setTimeout(() => {
          document.getElementById("end_city").click();
        }, 700);
      } else {
        setTimeout(() => {
          document.getElementById("train-search").click();
        }, 700);
      }
    }
  } else {
    bussess_ === "train"
      ? $("#end_city").attr({
          codelevel: codelevel,
          ad_code: ad_code,
          city_name: city_name,
          line_name: line_name,
          area_name: area_name,
        })
      : $(".end-pinche").attr({
          codelevel: codelevel,
          ad_code: ad_code,
          city_name: city_name,
          line_name: line_name,
          area_name: area_name,
        });
    let fromName = $("#strart_city .station-selected").html();
    if (bussess_ === "train") {
      if (fromName) {
        setTimeout(() => {
          document.getElementById("train-search").click();
        }, 700);
      }
    }
  }
  if (bussess_ === "pinche") loadData("fresh");
  togglePopupExpress(false, "#j-popup-full-citys");
}

async function openValidCity(codeType, bussess_) {
  $("#j-popup-full-citys").attr({ codeType: codeType, bussess_: bussess_ });
  if (codeType === "start") {
    $("#j-popup-full-citys .ui_popup_title").html("选择出发城市");
  } else {
    const isCheckVal = $("#strart_city .station-selected").html();
    if (!!!isCheckVal) {
      lui.toast({
        position: "middle",
        text: "请选择出发城市",
      });
      return;
    }
    $("#j-popup-full-citys .ui_popup_title").html("选择目的城市");
  }
  $("#j-popup-full-citys .city-warp-selected").html("");
  $(".city-name-input-search").val("");
  togglePopupExpress(false, "#j-popup-full-citys");

  $(".loading-warp-content").toggle();
  // city_name = '', start_name = '', start_address_code = ''
  const city_name =
    $("#strart_city").attr("codelevel") === "city"
      ? $("#strart_city").attr("city_name")
      : "";
  const start_name =
    $("#strart_city").attr("codelevel") === "name"
      ? $("#strart_city").attr("line_name")
      : "";
  const start_address_code =
    $("#strart_city").attr("codelevel") !== "city"
      ? $("#strart_city").attr("ad_code")
      : "";
  const result = await fetchCityAreaList(
    codeType,
    city_name,
    start_name,
    start_address_code,
    bussess_
  );
  city_area_list = result;
  renderCityDom(result);
  $(".loading-warp-content").toggle();
}
function renderCityDom(list) {
  const codeType = $("#j-popup-full-citys").attr("codetype");
  const citylist = list;
  let cityDom = "";
  for (let a = 0; a < citylist.length; a++) {
    let itemSub = "";
    for (let b = 0; b < citylist[a].city_attributes.length; b++) {
      const child = citylist[a].city_attributes[b];
      let third = "";
      for (
        let c = 0;
        c < citylist[a].city_attributes[b].area_attributes.length;
        c++
      ) {
        const thirdChild = citylist[a].city_attributes[b].area_attributes[c];
        third += `<div class="point-name" onclick="checkSearch('${codeType}','name',${
          child.area_id
        },'${
          codeType === "start" ? thirdChild.start_name : thirdChild.end_name
        }')" >${
          codeType === "start" ? thirdChild.start_name : thirdChild.end_name
        }</div>`;
      }
      itemSub += `<div class="area-warp-wapper"><div class="area-name" onclick="checkSearch('${codeType}','area',${
        child.area_id
      },'${
        child.area_name === "市辖区"
          ? `${citylist[a].city_name}·${child.area_name}`
          : child.area_name
      }')" >${
        child.area_name === "市辖区"
          ? `${citylist[a].city_name}·${child.area_name}`
          : child.area_name
      }</div>
         ${third}</div>`;
    }
    cityDom += `<div class="item-mian-city">
    <div class="city-name" >
       <div class="city-btn" onclick="checkSearch('${codeType}','city','','${citylist[a].city_name}')">${citylist[a].city_name} <i class="cell-right-dark"></i></div>
    </div>${itemSub}
    </div>`;
  }
  if (!cityDom) {
    $("#j-popup-full-citys .city-warp-selected")
      .html(`<div class="city-empty-warp">
     <img src="/Theme/images/passenger/tip.svg"   />
     <span>暂无可匹配数据</span>
    </div>`);
    return;
  }
  $("#j-popup-full-citys .city-warp-selected").html(cityDom);
}

function fetchCityAreaList(
  codeType,
  city_name = "",
  start_name = "",
  start_address_code = "",
  bussess_ = ""
) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url: `${
        bussess_ === "train"
          ? "/Home/AddressCode/filterDingzhikeyunAddresses"
          : "/Home/AddressCode/filterPincheAddresses"
      }?callback=${sessionStorage.getItem("callback")}`,
      type: "get",
      dataType: "json",
      data: {
        filter: codeType === "start" ? "origin" : "destination",
        city_name: codeType === "end" ? city_name : "",
        start_name: codeType === "end" ? start_name : "",
        start_address_code: codeType === "end" ? start_address_code : "",
      },
      success: function (data) {
        if (data.ret) {
          resolve(data.data);
        } else {
          resolve([]);
        }
      },
      fail: function () {
        reject("出错了");
      },
    });
  });
}

//生成日期
function proDate(date, start_params, end_params) {
  const fname =
    Object.prototype.toString.call(start_params) === "[object Object]"
      ? start_params.start_name
      : start_params;
  const tname =
    Object.prototype.toString.call(end_params) === "[object Object]"
      ? end_params.end_name
      : end_params;

  lui.loading();
  //获取时间
  $.ajax({
    url:
      "/Passenger/Line/getLineClassDate?date=" +
      date +
      "&callback=" +
      sessionStorage.getItem("callback"),
    type: "get",
    success: function (json) {
      var stl = "";
      var json = JSON.parse(json);
      var selectIndex = 0;
      if (json.ret) {
        let attr_object = { fname: fname, tname: tname };
        if (
          Object.prototype.toString.call(start_params) === "[object Object]"
        ) {
          attr_object = { ...attr_object, ...start_params };
        } else {
          attr_object = {
            ...attr_object,
            ...{
              start_name: "",
              start_level: "",
              start_code: "",
            },
          };
        }
        if (Object.prototype.toString.call(end_params) === "[object Object]") {
          attr_object = { ...attr_object, ...end_params };
        } else {
          attr_object = {
            ...attr_object,
            ...{
              end_name: "",
              end_level: "",
              end_code: "",
            },
          };
        }
        $("#j-popup-right").attr(attr_object);
        indexPage.getregular(date);
        for (var i = 0; i < json.data.length; i++) {
          const item = json.data[i];
          if (item.selectd) selectIndex = i;

          stl += `<div class="date-item ${
            item.selectd ? "selected-date-line" : ""
          }" index="${i}" date="${item.date}">
                  <div>${item.show.month_number.replace(".", "/")}</div>
                  <div>${item.show.class}</div>
                </div>
                <div class="line"></div>`;
        }
        $(".date-scroll").html(stl);
        $(".date-scroll").scrollLeft(
          selectIndex > 2 ? 55 * (selectIndex - 2) : 0
        );
      } else {
        lui.loading("close");
        lui.showModal({
          content: "无法获取班次日期，请重新选择日期",
          showCancel: false,
          confirm: function () {
            trainsBack();
          },
        });
      }
    },
    error: function (json) {},
    timeout: 300000,
  });
}

//坐标转换
async function geocoderDetail(lnglat) {
  return new Promise((resolve, reject) => {
    AMap.plugin("AMap.Geocoder", function () {
      var geocoder = new AMap.Geocoder({
        city: "全国",
        extensions: "all",
      });
      geocoder.getAddress(lnglat, function (status, result) {
        if (status === "complete" && result.info === "OK") {
          // result为对应的地理位置详细信息
          const regeocode = result?.regeocode;
          resolve(regeocode);
        } else {
          reject("出错了");
        }
      });
    });
  });
}

//导航路径，时间预估
function checkDriving(slng, slat, elng, elat) {
  if (driving) {
    driving.clear();
  }
  driving = new AMap.Driving({
    map: map,
    autoFitView: false,
    isOutline: false,
  });
  return new Promise((resolve, reject) => {
    driving.search(
      new AMap.LngLat(slng, slat),
      new AMap.LngLat(elng, elat),
      function (status, result) {
        const { routes = [] } = result;

        console.log("resultresultresult", result);
        if (!routes || routes.length == 0) {
          reject("error");
          return;
        }

        let distanceView = "";
        let distance = routes[0].distance;
        let timeView = distanceGetTime(routes[0].time);

        if (distance >= 1000) {
          distanceView = (Math.round(distance / 100) / 10).toFixed(1) + "公里";
        } else {
          distanceView = distance + "米";
        }
        resolve({ distanceView, timeView });
      }
    );
  });
}

//批量逆地理编码
async function geocoderAddress(points) {
  return new Promise((resolve, reject) => {
    AMap.plugin("AMap.Geocoder", function () {
      var geocoder = new AMap.Geocoder({
        city: "全国",
        extensions: "all",
      });
      const lnglats = points
        .filter(o => o && o.longitude && o.latitude && !isNaN(o.longitude) && !isNaN(o.latitude))
        .map((o) => {
          return new AMap.LngLat(o.longitude, o.latitude);
        });
      geocoder.getAddress(lnglats, function (status, result) {
        if (status === "complete" && result.info === "OK") {
          console.log(
            "points_fee_startpoints_fee_startpoints_fee_start",
            result
          );
          // result为对应的地理位置详细信息
          const regeocodes = result?.regeocodes.map((o) => o.formattedAddress);
          if (points.length === regeocodes.length) {
            points.map((o, i) => {
              o["address"] = regeocodes[i];
              return o;
            });
          }
        }
        resolve(points);
      });
    });
  });
}
function openSearch() {
  const clicktype = $(".confirm-map-selected-point").attr("clicktype");
  const start_area_code = $(".confirm-map-selected-point").attr(
    "start_area_code"
  );
  const end_area_code = $(".confirm-map-selected-point").attr("end_area_code");

  if (clicktype === "start") {
    $("#j-popup-full-search .ui_popup_title").html("选择上车点");
    console.log("start_area_code", start_area_code);
    expressSearch && expressSearch.setCity(start_area_code);
    $(".address-list-points").html("");
  } else {
    expressSearch && expressSearch.setCity(end_area_code);
    $("#j-popup-full-search .ui_popup_title").html("选择下车点");
    $(".address-list-points").html("");
  }

  $(".full-search-input").val("");
  $(".full-search-input").focus();
  togglePopupExpress(false, "#j-popup-full-search");
}

function adcodeName() {
  return new Promise((resolve, reject) => {
    $.ajax({
      type: "get",
      url: `https://restapi.amap.com/v3/config/district?keywords=370000&subdistrict=2&extensions=base&key=a6ceea2f9dc1b9ae091653d69b11072d&output=json`,
      success: function (data) {
        console.log("district", data);
      },
    });
  });
}
//渲染广告dom
function renderSwiper(data, type, biz) {
  //biz  4-定制客运，2-拼车  3-包车
  let stl = "";
  if (type === "bgImg") {
    for (let i = 0; i < data.length; i++) {
      stl += ` <div class="swiper-slide">
               <a href="${data[i].link}" class="bg-action-busses"><img width="100%"  src="${data[i].imgsrc_url}" /></a>
             </div>`;
    }
    return `<div class="swiper-container">
              <div class="swiper-wrapper">
               ${stl}
              </div>
            </div>`;
  } else {
    for (let i = 0; i < data.length; i++) {
      stl +=
        '<div class="swiper-slide new-swiper-slide"><a href="' +
        data[i].link +
        '"><img style="width:100%;height:100%;display:block;" alt="" src=' +
        data[i].imgsrc_url +
        "></img></a></div>";
    }

    return `<div class="swiper-container">
              <div class="swiper-wrapper">${stl}</div>
              <div class="swiper-pagination"></div>
            </div>`;
  }
}
//获取广告图
function fetchswiper(cate_id) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url: `/Passenger/Ad/getAds?cate_id=${cate_id}&callback=${sessionStorage.getItem(
        "callback"
      )}`,
      type: "get",
      success: function (json) {
        var stl = "";
        var json = JSON.parse(json);
        if (json.ret) {
          resolve(json.data);
        } else {
          resolve([]);
        }
      },
      error: function (json) {
        reject();
      },
      timeout: 300000,
    });
  });
}
//生成定制客运分类
function renderClassification(data) {
  let stl = "";
  for (var i = 0; i < data.length; i++) {
    if (data[i].show_type !== '1') {
      continue;
    }
    stl += `<span class="train-classify"  cid=${data[i].id}  ctitle="${data[i].title}">${data[i].title}</span>`;
  }
  return stl;
}
function renderClassificationImage(data) {
  let stl = "";
  for (var i = 0; i < data.length; i++) {
    if (data[i].show_type !== '2') {
      continue;
    }
    stl += `<span class="item-quick"  cid=${data[i].id}  ctitle="${data[i].title}">
                <img class="bussess_1" src=${data[i].image} alt="">
                <span>${data[i].title}</span>
            </span>`;
  }
  return stl;
}
//包车分类
function rendercharteredListy(data) {
  let stl = "";
  for (var i = 0; i < data.length; i++) {
    stl += `<span class="chartered-classify"  cid=${data[i].id}  ctitle="${data[i].title}">${data[i].title}</span>`;
  }
  return stl;
}
function flattenArray(arr) {
  let flattened = [];
  for (let i = 0; i < arr.length; i++) {
    if (Array.isArray(arr[i])) {
      flattened = flattened.concat(flattenArray(arr[i]));
    } else {
      flattened.push(arr[i]);
    }
  }
  return flattened;
}
function fetBounds(params) {
  return new Promise((resolve, reject) => {
    if (!district) {
      //实例化DistrictSearch
      var opts = {
        subdistrict: 0, //获取边界不需要返回下级行政区
        extensions: "all", //返回行政区边界坐标组等具体信息
        level: "city", //查询行政级别为 市
      };
      district = new AMap.DistrictSearch(opts);
    }
    district.search(params.toString(), function (status, result) {
      var bounds = result.districtList?.[0]?.boundaries;

      console.log("boundsboundsbounds", bounds);

      let latlngArr = [];
      for (var i = 0; i < bounds.length; i += 1) {
        let arr = JSON.parse(JSON.stringify(bounds[i]));
        latlngArr.push(arr);
      }
      if (bounds) {
        resolve(latlngArr);
      } else {
        resolve([]);
      }
    });
  });
}

function convertSecondsToHoursMinutes(seconds) {
  var hours = Math.floor(seconds / 3600);
  var minutes = Math.floor((seconds % 3600) / 60);

  return hours + "小时 " + minutes + "分钟";
}
function distanceGetTime(time) {
  // 转换为式分秒
  let h = parseInt((time / 60 / 60) % 24);
  let m = parseInt((time / 60) % 60);
  // 作为返回值返回
  return (h > 0 ? h + "小时" : "") + (m > 0 ? m + "分钟" : "");
}
function setOverLines() {
  var line_lng = $(".origin_place").attr("lng");
  var line_lat = $(".origin_place").attr("lat");
  const startObj = {
    position: [line_lng, line_lat],
    address: $(".origin_clicked .alias-name").html(),
  };

  var end_lng = $(".finish_place").attr("lng");
  var end_lat = $(".finish_place").attr("lat");
  var address__ = $(".finish_place").html();
  const endObj = {
    position: [end_lng, end_lat],
    address: address__,
  };

  if (positionPicker) {
    positionPicker.stop();
  }
  $(".infowindow-map").css("display", "none");
  driving = new AMap.Driving({ autoFitView: false, isOutline: false });
  // var styleName = 'amap://styles/whitesmoke'
  // var styleName = 'amap://styles/normal'
  map.setMapStyle("amap://styles/whitesmoke"); //路劲规划显示精简地图
  map.clearMap();
  driving.clear();
  //设置起点位置
  beginMarker = new AMap.Marker({
    map: map,
    position: startObj.position,
    offset: new AMap.Pixel(-12, -40), //相对于基点的偏移位置
    icon: new AMap.Icon({
      size: new AMap.Size(24, 39), //图标大小
      image: "/Theme/images/passenger/frommap.png",
      // imageOffset: new AMap.Pixel(8, 2), //图标相对于marker的偏移量
      imageSize: new AMap.Size(24, 39),
    }),
  });

  const start_info_content = `<div class="endpoint-info-window startpoint-info-window origin_clicked">
  <img class="info-window-light"  src="/Theme/images/passenger/right-light.png" />
  <div class="point-over-view">${startObj.address}</div>
  </div>`;

  var infoMarker = new AMap.Marker({
    map: map,
    position: startObj.position,
    // 将 html 传给 content
    content: start_info_content,
    // 以 icon 的 [center bottom] 为原点
    offset: new AMap.Pixel(-83, -80),
  });
  driving.search(
    new AMap.LngLat(startObj.position[0], startObj.position[1]),
    new AMap.LngLat(endObj.position[0], endObj.position[1]),
    function (status, result) {
      const { routes = [] } = result;
      if (!routes || routes.length == 0) {
        return;
      }

      console.log("resultresultresultresultresult", result);
      const { steps = [] } = routes[0];
      const pathArr = [];
      steps.map((i) => {
        pathArr.push(i.path);
        return pathArr;
      });
      const path = flatten(pathArr);
      // 绘制轨迹new AMap
      const linePolyline = new AMap.Polyline({
        map: map,
        path,
        showDir: true,
        strokeColor: "#47c184", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 8, // 线宽
        strokeStyle: "solid", // 线样式
        lineJoin: "round", // 折线拐点的绘制样式
        zIndex: 999,
      });
      linePolyline.setMap(map);

      let distanceView = "";
      let info_content = "";
      let distance = routes[0].distance;
      let timeView = distanceGetTime(routes[0].time);

      if (distance >= 1000) {
        distanceView = (Math.round(distance / 100) / 10).toFixed(1) + "公里";
      } else {
        distanceView = distance + "米";
      }
      info_content = `<div class="endpoint-info-window finish_place_btn">
    <img class="info-window-light"  src="/Theme/images/passenger/right-light.png" />
    <div class="point-over-view">${endObj.address}</div>
    <div class="info-over-view">全程<span  class="info-over-view-num">${distanceView}</span>，约<span class="info-over-view-num">${timeView}</span></div></div>`;
      var infoWindow = new AMap.InfoWindow({
        anchor: "bottom-center",
        content: info_content,
        offset: new AMap.Pixel(0, -50),
      });
      infoWindow.open(map, endObj.position);
    }
  );

  //设置终点位置
  finishMarker = new AMap.Marker({
    map: map,
    position: endObj.position,
    offset: new AMap.Pixel(-12, -40), //相对于基点的偏移位置
    icon: new AMap.Icon({
      size: new AMap.Size(24, 39), //图标大小
      image: "/Theme/images/passenger/tomap.png",
      imageSize: new AMap.Size(24, 39),
    }),
  });

  map.setFitView(
    [beginMarker, finishMarker], // 覆盖物数组
    true, // 动画过渡到制定位置
    [100, 360, 150, 50] // 周围边距，上、下、左、右
  );
}
//围栏选点地图  新增加价区域显示加价区域显示  尝试单独增加逻辑后冗余代码太多，所以在任意地图区域做兼容相对更符合逻辑
async function polyMapRender(
  maptype,
  polyData = [],
  current,
  polyType = "polygon",
  circleData = {},
  price_polygons = "",
  palceData = [], //添加固定点逻辑，结合区域任意点

) {
  //maptype=地图类型(起点，终点)  polyData 围栏数据  polyType //围栏类型  polygon-多边形 circle-圆形  area-行政区域
if (poly_map) poly_map.destroy();
if(palceData.length) {
  const pois_ = await geocoderAddress(palceData);
  console.log("pois_pois_pois", pois_);
  let dom_point = "";
  for (let i = 0; i < pois_.length; i++) {
    dom_point += `  
      <div class="item-address-point-label-station custom-label-point" place_type="${maptype}" or_time="${
      pois_[i].in_time_pra || ""
    }"   name="${pois_[i].alias}" sta_time="${
      pois_[i].in_time || ""
    }" price-point-id="${pois_[i].id}"   price-point-fee="${
      pois_[i].additional_fee
    }"  viewaddress="${pois_[i].address}" address="${pois_[i].address}" lng="${
      pois_[i].longitude
    }" lat="${pois_[i].latitude}" price-point-use-time="${pois_[i].use_time}">
         <img class="address-point-icon" src="/Theme/images/passenger/icon-start.png" alt="" />
         <div class="right-point-label">
          <p class="address-complete-name">${
            pois_[i].alias
          } <span class="viewpoint-time">${pois_[i].in_time || ""}</span> ${
      pois_[i].additional_fee && Number(pois_[i].additional_fee) !== 0
        ? `<span class="viewpoint-price">${
            Number(pois_[i].additional_fee) > 0 ? "+" : ""
          } ${pois_[i].additional_fee}</span>元`
        : ""
    }</p> 

          <p class="address-complete-tip">${
            pois_[i].distance ? `<span>${pois_[i].distance} |</span>` : ""
          } ${pois_[i].address}</p>
           </div>
    </div>`;
  }
  $(".area-list-point-quick").html(`<div class="area-dom"><div class="top-title">常用${maptype=='start'?'上':'下'}车站点</div><div class="scroll-point">${dom_point}</div></div>`);


}else{
  $(".area-list-point-quick").html("");
}





  if (pointCheckMarker) {
    pointCheckMarker.setMap(null);
    pointCheckMarker = null;
  }
  if (customVector) {
    customVector = null;
  }
  if (priceVector.length) {
    priceVector = [];
  }
  let price_polygons_area = [];




  AMapUI.loadUI(["misc/PositionPicker"],  function (PositionPicker) {
    //加载地图
    poly_map = new AMap.Map("polyMap", {
      resizeEnable: true,
      showBuildingBlock: true,
      touchZoomCenter: 1,
      zoom: 16,
      scrollWheel: true,
    });
    if(maptype === "start") polyLocation();
  // 监听地图拖动结束事件
  poly_map.on("dragend", function () {
    if(polyPositionPicker) {
      $('.map-marker-center').hide();
      polyPositionPicker.start();
    }
  });


  let isManualZoom = false;
  // 监听鼠标滚轮事件（手动缩放）
  poly_map.on("mousewheel", function () {
    isManualZoom = true;
  });
  // 监听触摸事件（手动缩放）
  poly_map.on("touchstart", function () {
    isManualZoom = true;
  });
  poly_map.on("zoomstart", function () {
    console.log("缩放开始");
  });
  poly_map.on("zoomend", function () {
    const zoomLevel = poly_map.getZoom();
    console.log("当前缩放级别:", zoomLevel);
    if (isManualZoom) {
      console.log("这是手动缩放");
      if(polyPositionPicker) {
        $('.map-marker-center').hide();
        polyPositionPicker.start();
      };
    } else {
      console.log("这是程序触发的缩放");
    }
    isManualZoom = false;
  });



    polyPositionPicker = new PositionPicker({
      mode: "dragMap",
      map: poly_map,
      iconStyle: {
        //自定义外观
        url: "/Theme/images/passenger/map-point.png", //图片地址
        size: [26, 37], //要显示的点大小，将缩放图片
        ancher: [12, 35], //锚点的位置，即被size缩放之后，图片的什么位置作为选中的位置
      },
    });
    setTimeout(() => {
      $('.map-marker-center').hide();
      polyPositionPicker.start();
    }, 400);
    if (price_polygons) {
      //渲染加价区域
      const price_polygons_ = JSON.parse(price_polygons).filter((o) => o.polygons != "");
      price_polygons_.map((price_a) => {
        let item_area = new AMap.Polygon({
          path: price_a.polygons.split("-").map((o) => o.split(",")),
          fillColor: "rgb(246,137,38)",
          strokeOpacity: 1,
          fillOpacity: 0.5,
          strokeColor: "rgb(246,137,38)",
          strokeWeight: 1,
          extData: price_a,
          zIndex: 500,
          // strokeStyle: 'dashed',
          // strokeDasharray: [5, 5],
        });
        priceVector.push(item_area);
        // 获取多边形的边界框，即为矩形
        let bounds = item_area.getBounds();
        // 获取矩形的中心点
        let center_area = bounds.getCenter();
        let item_text = new AMap.Text({
          text: `${price_a.title ? price_a.title : "调度费用"}：${
            price_a.price > 0 ? "+" : ""
          }${price_a.price}元/人`,
          offset: new AMap.Pixel(-60, 0),
          position: center_area, // 多边形的中心点
          style: {
            fontSize: 11,
            fontWeight: "normal",
            fillColor: "#fff",
            // fillColor: 'rgb(255, 215, 0)',
            // strokeColor: '#fff',
            // strokeWidth: 2,
            // fold: true,
            padding: "2, 5",
            backgroundColor: "rgb(246,137,38,0.8)",
            borderColor: "#fff",
            borderWidth: 1,
          },
        });
        item_text.setMap(poly_map);
      });
      poly_map.add(priceVector);

      price_polygons_area = JSON.parse(price_polygons).map((ii) => {
        let inv = ii.polygons.split("-").map((o) => o.split(","));
        return { path: inv, ...ii };
      });
    }
    window.checkPointAvailable = checkPointAvailable;
    function checkPointAvailable(point_params) {

     const {
      address_,
      name_,
      lng,
      lat,
    }=point_params
      
      // 添加经纬度有效性检查
      if (!lng || !lat || isNaN(lng) || isNaN(lat)) {
        console.error("Invalid coordinates:", lng, lat);
        $(".map-bubble span").html("无效的位置坐标");
        $(".error-tip").html("请选择有效位置");
        $(".confirm-map-selected-point").addClass("disabled-check");
        $(".area-price-notice").html(``);
        return;
      }
      
      //后台设置了加价区域
      let lngLatObj;
      try {
        // 确保转换为数字并检查是否有效
        const numLng = Number(lng);
        const numLat = Number(lat);
        if (isNaN(numLng) || isNaN(numLat)) {
          throw new Error(`无效的经纬度值: lng=${lng}, lat=${lat}`);
        }
        lngLatObj = new AMap.LngLat(numLng, numLat);
      } catch (error) {
        console.error("创建LngLat对象失败:", error);
        $(".map-bubble span").html("无效的位置坐标");
        $(".error-tip").html("请选择有效位置");
        $(".confirm-map-selected-point").addClass("disabled-check");
        $(".area-price-notice").html(``);
        return;
      }
      
      let isPointInPriceAreaAble = null;
      if (price_polygons_area && price_polygons_area.length > 0) {
        isPointInPriceAreaAble = isPointInPriceArea(
          lngLatObj,
          price_polygons_area
        );
      }
      console.log( "是否在允许区域内：",
        isPointInPriceAreaAble
      );
      if (isPointInPriceAreaAble) {
        //有加价区域，且在加价区域内

        $(".map-bubble span").html(
          maptype === "start" ? "从这里上车" : "在这里下车"
        );
        $(".error-tip").html("");
        $(".confirm-map-selected-point").removeClass("disabled-check");
        $(".area-price-notice").html(`<span class="ba">当前位置${
          maptype === "start" ? "上车" : "下车"
        }${isPointInPriceAreaAble.price > 0 ? "需" : "可"}</span>
            <span class="custom-area-price-view">${
              isPointInPriceAreaAble.price > 0 ? "+" : ""
            }${isPointInPriceAreaAble.price}元/人</span>车辆调度费用`);
        // 橙色范围内由于车辆调度原因，会在原有票价上产生价格上下浮动
        point_params = { ...point_params, ...isPointInPriceAreaAble };
      } else if (
        isPointInArea(
          polyType === "circle" ? [Number(lng), Number(lat)] : lngLatObj,
          polyType,
          polyData,
          customVector
        )
      ) {
        $(".map-bubble span").html(
          maptype === "start" ? "从这里上车" : "在这里下车"
        );
        $(".error-tip").html("");
        $(".confirm-map-selected-point").removeClass("disabled-check");
        $(".area-price-notice").html(``);
      } else {
        $(".map-bubble span").html("请选择服务范围内的地址");
        $(".error-tip").html("超出服务范围");
        $(".confirm-map-selected-point").addClass("disabled-check");
        $(".area-price-notice").html(``);
      }

      $(".confirm-map-selected-point").attr(
        "params",
        JSON.stringify(point_params)
      );
      $(".select-point-warp-view").html(`<div class="left-point">
        <div class="point-title">${name_}</div>
        <div class="point-desc">${address_}</div>
      </div>`);


    }


    polyPositionPicker.on("success",  function (polyResult) {
      const aois_name =
        polyResult?.regeocode?.aois?.[0]?.name || polyResult.address;
      const address = polyResult.address;
      const lng_ =
        polyResult?.regeocode?.aois?.[0]?.location?.lng ||
        polyResult.position?.lng;
      const lat_ =
        polyResult?.regeocode?.aois?.[0]?.location?.lat ||
        polyResult.position?.lat;
      let point_params = {
        address_: address,
        name_: aois_name,
        lng: lng_,
        lat: lat_,
      };
      checkPointAvailable(point_params)

    });

    poly_map.panBy(0, 1);
    if (polyType === "circle") {
      //围栏为圆形自定义
      customVector = new AMap.Circle({
        center: circleData.center,
        radius: circleData.radius, //半径
        strokeColor: maptype === "start" ? "#2b8cbe" : "#e90000",
        strokeOpacity: 1,
        strokeWeight: 1,
        strokeOpacity: 0.2,
        fillOpacity: 0.4,
        strokeStyle: "dashed",
        strokeDasharray: [10, 10],
        // 线样式还支持 'dashed'
        fillColor: maptype === "start" ? "#1c73e2" : "#e90000",
        zIndex: 50,
      });
      poly_map.add(customVector);
    }

    if (polyData && polyData.length) {
      //围栏为默认多边形

      console.log("polyDatapolyDatapolyDatapolyData", polyData);
      let polyData_ = [...polyData];
      if (polyType === "area") {
        //生成行政区划polygon
        for (var i = 0; i < polyData_.length; i += 1) {
          //构造MultiPolygon的path
          polyData_[i] = [polyData_[i]];
        }
      }
      customVector = new AMap.Polygon({
        path: polyData_,
        fillColor: maptype === "start" ? "#1c73e2" : "#e90000",
        strokeOpacity: 1,
        fillOpacity: 0.3,
        strokeColor: maptype === "start" ? "#2b8cbe" : "#e90000",
        strokeWeight: 1,
        strokeStyle: "dashed",
        strokeDasharray: [5, 5],
      });
      poly_map.add(customVector);
    }

    if (current) {
      poly_map.setZoomAndCenter(
        16,
        new AMap.LngLat(current.lng, current.lat),
        false,
        300
      );
    } else {
      let fitViews = [customVector];
      poly_map.setFitView(fitViews);
    }
    if(palceData.length) {
      //渲染固定点
      let pointMarkers = [];
      palceData.map((o) => {
        pointMarkers.push(
          new AMap.Marker({
            map: poly_map,
            icon: new AMap.Icon({
              size: new AMap.Size(48, 48), //图标大小
              image:"/Theme/images/passenger/custom-point.png",
              imageOffset: new AMap.Pixel(0, 0), //图标相对于marker的偏移量
              imageSize: new AMap.Size(48, 48),
            }),
            position: [o.longitude, o.latitude],
            anchor: "bottom-center",
            offset: new AMap.Pixel(0, 0),
            label: {
              content: `<div class="point-label-container" id="${o.id}">${o.alias} <img src="/Theme/images/passenger/color-right.svg" /></div>`, // 名称内容
              direction: "top",           // 文字方向（顶部）
              offset: new AMap.Pixel(0, -5) // 向上偏移 25px（根据图标高度调整）
            }
          })
        );
      });


    }



    
  });
}

function polyinclde() {
  if (customVector) poly_map.setFitView([customVector]);
}

//固定点地图选择
async function pointMapRender(
  palceData = [],
  fullPoints = [],
  maptype = "start"
) {
  //maptype=地图类型(起点，终点)   palceData 固定上下车点数据 fullPoints 全量上下车点

  if (point_map) point_map.destroy();

  AMapUI.loadUI(["misc/PositionPicker"], function (PositionPicker) {
    //加载地图
    point_map = new AMap.Map("pointMap", {
      resizeEnable: true,
      showBuildingBlock: true,
      touchZoomCenter: 1,
      zoom: 16,
      scrollWheel: true,
    });

    pointDriving = new AMap.Driving({
      autoFitView: false,
      isOutline: false,
      hideMarkers: true,
    });
    // point_map.setMapStyle('amap://styles/whitesmoke') //路劲规划显示精简地图
    point_map.clearMap();
    pointDriving.clear();

    let pointMarkers = [];
    fullPoints.map((o) => {
      pointMarkers.push(
        new AMap.Marker({
          map: point_map,
          icon: new AMap.Icon({
            size: new AMap.Size(20, 30), //图标大小
            image:
              "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            imageOffset: new AMap.Pixel(0, 0), //图标相对于marker的偏移量
            imageSize: new AMap.Size(20, 30),
          }),
          position: [o.longitude, o.latitude],
          anchor: "bottom-center",
          offset: new AMap.Pixel(0, 0),
        })
      );
    });
    if (fullPoints.length > 1) {
      const startPoi = fullPoints[0];
      const endPoi = fullPoints[fullPoints.length - 1];
      const waypoints = fullPoints.slice(1, fullPoints.length - 1).map((o) => {
        return new AMap.LngLat(o.longitude, o.latitude);
      });
      pointDriving.search(
        new AMap.LngLat(startPoi.longitude, startPoi.latitude),
        new AMap.LngLat(endPoi.longitude, endPoi.latitude),
        {
          waypoints: waypoints,
        },
        function (status, result) {
          const { routes = [] } = result;
          if (!routes || routes.length == 0) {
            return;
          }

          const { steps = [] } = routes[0];
          const pathArr = [];
          steps.map((i) => {
            pathArr.push(i.path);
            return pathArr;
          });
          const path = flatten(pathArr);
          // 绘制轨迹new AMap
          const linePolyline = new AMap.Polyline({
            map: point_map,
            path,
            showDir: true,
            strokeColor: "#999", // 线颜色
            strokeOpacity: 1, // 线透明度
            strokeWeight: 8, // 线宽
            strokeStyle: "solid", // 线样式
            lineJoin: "round", // 折线拐点的绘制样式
            zIndex: 999,
          });
          linePolyline.setMap(point_map);
        }
      );
    }
    point_map.setFitView(
      pointMarkers, // 覆盖物数组
      true, // 动画过渡到制定位置
      [150, 260, 50, 50] // 周围边距，上、下、左、右
    );
  });
  console.log("palceData", palceData);
  const pois_ = await geocoderAddress(palceData);
  let dom_point = "";
  for (let i = 0; i < pois_.length; i++) {
    dom_point += `
    <label class="ui_radio station-checkbox"  place_type="${maptype}" or_time="${
      pois_[i].in_time_pra || ""
    }"   name="${pois_[i].alias}" sta_time="${
      pois_[i].in_time || ""
    }" price-point-id="${pois_[i].id}"   price-point-fee="${
      pois_[i].additional_fee
    }"  viewaddress="${pois_[i].address}" address="${pois_[i].address}" lng="${
      pois_[i].longitude
    }" lat="${pois_[i].latitude}" price-point-use-time="${pois_[i].use_time}">
      <input type="radio"  class="station-agree-check" value="no" name="point" or_time="${
        pois_[i].in_time_pra || ""
      }"  sta_time="${pois_[i].in_time || ""}"  alias="${
      pois_[i].alias
    }" viewaddress="${pois_[i].address}" address="${pois_[i].address}" lng="${
      pois_[i].longitude
    }" lat="${pois_[i].latitude}" price-point-id="${
      pois_[i].id
    }" price-point-fee="${pois_[i].additional_fee}" price-point-use-time="${
      pois_[i].use_time
    }" >  
      <div class="item-address-point-label-station">

          <p class="address-complete-name">${
            pois_[i].alias
          } <span class="viewpoint-time">${pois_[i].in_time || ""}</span> ${
      pois_[i].additional_fee && Number(pois_[i].additional_fee) !== 0
        ? `<span class="viewpoint-price">${
            Number(pois_[i].additional_fee) > 0 ? "+" : ""
          } ${pois_[i].additional_fee}</span>元`
        : ""
    }</p> 

          <p class="address-complete-tip">${
            pois_[i].distance ? `<span>${pois_[i].distance} |</span>` : ""
          } ${pois_[i].address}</p>
    </div>
  </label>`;
  }
  $(".points-station-list").html(dom_point);
}




async function checkPinche(id) {
  $(".pincheOrderAction").attr("id", id);
  let lines_pinche_arr = JSON.parse(
    sessionStorage.getItem("lines_pinche_arr")
  ).find((o) => o.id == id); //当前打开路线
  const business_time_type = lines_pinche_arr.business_time_type;
  const set_order_time = lines_pinche_arr.set_order_time;
  if (business_time_type == 1) {
    set_time_space(
      lines_pinche_arr.start_appointment_time,
      lines_pinche_arr.end_appointment_time,
      set_order_time
    );
  } else {
    set_time_space("00:00", "24:00", set_order_time);
  }
  $("#j-popup-half-carpool .view-label-price").html(lines_pinche_arr.price);
  $("#j-popup-half-carpool .view-label-price").attr(
    "price",
    lines_pinche_arr.price
  );

  $("#j-popup-half-carpool #picker5").html(
    `<span class="default-info">现在出发</span>`
  );
  $("#j-popup-half-carpool #picker5").attr(`current_date`, "");
  $(".passenger-number-warp").html("1人");
  $(".passenger-number-warp").attr("number", "1");
  $(".has-message").attr("reseverd_info", "");
  $(".has-message").html('<span class="default-info">行程备注</span>');
  if (lines_pinche_arr.summary) {
    $("#j-popup-half-carpool .desc-row-warp")
      .html(`   <div class="info-container" data-role="popover-btn">
    <i class="checked-right-icon"></i>
    <span>须知</span>
  </div>
  <div class="ui_popover" data-role="popover">
    <i class="ui_popover_arrow" style="left:18px"></i>
    <div class="ui_popover_content">
    <div class="tag-title">路线须知</div>
    ${lines_pinche_arr.summary}</div>
  </div>`);
  } else {
    $("#j-popup-half-carpool .desc-row-warp").html("");
  }

  $(".pinche-name-start").html(
    (lines_pinche_arr?.start_city?.name || "") +
      "·" +
      lines_pinche_arr.start_name
  );
  $(".pinche-name-end").html(
    (lines_pinche_arr?.end_city?.name || "") + "·" + lines_pinche_arr.end_name
  );
  const sessionLocation = sessionStorage.getItem("sessionLocation");

  togglePopupExpress(true, "#j-popup-half-carpool");
  let start_default_dom = "";
  if (sessionLocation) {
    const { aois_name, address, lng_, lat_ } = JSON.parse(sessionLocation);

    const current_point = [lng_, lat_];
    let polyType = "",
      circleData = {},
      polygon_ = [];
    const {
      area_lng_start,
      area_lat_start,
      area_radius_start,
      start_city_code,
      start_area_code,
      start_area,
    } = lines_pinche_arr;
    if (!!area_lng_start) {
      //圆形围栏
      polyType = "circle";
      circleData = {
        center: [area_lng_start, area_lat_start],
        radius: area_radius_start,
      };
    } else {
      //行政区域围栏
      polyType = "area";
      adcode = start_area.name === "市辖区" ? start_city_code : start_area_code;
      polygon_ = await fetBounds(adcode);
    }
    // console.log(circleData)
    console.log(
      "是否在区域内",
      isPointRange(current_point, polyType, polygon_, circleData)
    );
    const isdefaultRange = isPointRange(
      current_point,
      polyType,
      polygon_,
      circleData
    );

    start_default_dom = `<div class="check-start-point ${
      !!!isdefaultRange ? "item-feedback-error" : ""
    }" id="check-start-point" onclick="openCarpoolMap('start')">
    <div class="tarin-point-label" name="${aois_name}"  address="${address}"  lng="${lng_}"  lat="${lat_}">${aois_name}</div>
    <span class="selcet-label-tip-start">修改</span>
    <span class="error-point-txt">${
      !!!isdefaultRange ? "当前位置不在服务范围内" : ""
    }</span>
  </div>`;
  } else {
    start_default_dom = `<div class="check-start-point"   id="check-start-point"  onclick="openCarpoolMap('start')">
    <div class="tarin-point-label">请选择您的上车位置</div>
    <span class="selcet-label-tip-start">选择</span>
  </div>`;
  }

  $(".check-place-carpool")
    .html(`${start_default_dom}<div class="check-end-point"   id="check-end-point"  onclick="openCarpoolMap('end')">
  <div class="tarin-point-label">请选择您的下车位置</div>
  <span class="selcet-label-tip-start">选择</span>
</div>`);
}

function checkCharter(id) {
  $(".charterOrderAction").attr("id", id);
  let lines_charter = charter_list.find((o) => o.id == id); //当前打开路线
  const business_time_type = lines_charter.business_time_type;
  const set_order_time = lines_charter.set_order_time;
  if (business_time_type == 1) {
    set_time_space(
      lines_charter.start_appointment_time,
      lines_charter.end_appointment_time,
      set_order_time
    );
  } else {
    set_time_space("00:00", "24:00", set_order_time);
  }
  $("#j-popup-half-charter #picker5").html(
    `<span class="default-info">用车时间</span>`
  );
  $("#j-popup-half-charter #picker5").attr(`current_date`, "");
  $(`.charter-fare`).html(`<span class="default-info">代叫</span>`);
  $(`.charter-fare`).attr("reseverd_tel", $("#cellphone-data").val());
  $(".charter-message").attr("reseverd_info", "");
  $(".charter-message").html('<span class="default-info">行程备注</span>');

  if (lines_charter.summary) {
    $("#j-popup-half-charter .desc-row-warp")
      .html(`<div class="info-container" data-role="popover-btn">
    <i class="checked-right-icon"></i>
    <span>包车详情</span>
  </div>`);
    $(".agreement-words").html(lines_charter.summary);
    $(".agreement-title-top").html("包车详情");
  } else {
    $("#j-popup-half-charter .desc-row-warp").html("");
  }

  let cars = "";
  // $('.line-car-list-price')
  for (let i = 0; i < lines_charter.car_type_data.length; i++) {
    const itemCar = lines_charter.car_type_data[i];
    cars += `<div class="ui_checkbox item-car-seat ${
      i === 0 ? "ui_checkbox_checked" : ""
    }" line_chartered_price_id="${itemCar.line_chartered_price_id}">
    <div class="left-seat">
    ${
      itemCar.car_type_num == 4
        ? '<img class="car-seat-icon" src="/Theme/images/passenger/5seats.png" alt="" srcset="">'
        : itemCar.car_type_num == 6
        ? '<img class="car-seat-icon" src="/Theme/images/passenger/7seats.png" alt="" srcset="">'
        : '<img class="car-seat-icon" src="/Theme/images/passenger/9seats.png" alt="" srcset="">'
    }
      <div class="desc-label">
         <b>${
           itemCar.car_type_num == 4
             ? "经济5座"
             : itemCar.car_type_num == 6
             ? "商务7座"
             : "商务9座"
         }</b> ${
      itemCar.car_type_num == 4 ? "4" : itemCar.car_type_num == 6 ? "6" : "8"
    }人·${itemCar.car_type_num == 4 ? "2" : "4"}行李
      </div>
     </div>
    <div class="right-checkbox">
      <span class="price-label">
        <label >${itemCar.price}</label>元
      </span>
      <input type="checkbox"    value="no" name="checkbox">
    </div>
   </div>`;
  }
  $(".line-car-list-price").html(cars);
  $(".charterOrderAction").attr(
    "pid",
    lines_charter.car_type_data[0].line_chartered_price_id
  );
  $(".ui_checkbox_checked").find("input").attr("checked", true);
  togglePopupExpress(true, "#j-popup-half-charter");
}

// function flattenArray(arr) {
//   var flattenedArray = [];

//   for (var i = 0; i < arr.length; i++) {
//     if (Array.isArray(arr[i])) {
//       flattenedArray = flattenedArray.concat(flattenArray(arr[i])); // 递归展开子数组
//     } else {
//       flattenedArray.push(arr[i]); // 将非数组元素添加到展开数组中
//     }
//   }

//   return flattenedArray;
// }

function isPointRange(point__, areatype, polygon_, circleData = {}) {
  if (areatype === "area") {
    const isPointInRing = polygon_.some((o) => {
      return AMap.GeometryUtil.isPointInRing(point__, o);
    });
    return isPointInRing;
  } else if (areatype === "polygon") {
    return AMap.GeometryUtil.isPointInRing(point__, polygon_);
  } else {
    //圆形围栏直接判断圆心到坐标距离是否大于半径
    // 返回 p1 到 p2 间的地面距离，单位：米
    var dis = AMap.GeometryUtil.distance(point__, circleData.center);
    console.log(dis);
    return dis < circleData.radius;
  }
}

function isPointInPriceArea(point__, priceVector) {
  console.log("point__point__point__point__point__", point__);
  console.log("priceVectorpriceVectorpriceVector", priceVector);
  
  // 检查参数有效性
  if (!point__ || !priceVector || !Array.isArray(priceVector) || priceVector.length === 0) {
    console.error("无效的参数:", point__, priceVector);
    return null;
  }
  
  // 检查point__是否为有效的AMap.LngLat对象
  if (!(point__ instanceof AMap.LngLat) || isNaN(point__.getLng()) || isNaN(point__.getLat())) {
    console.error("无效的坐标点对象:", point__);
    return null;
  }
  
  let itemPriceView = priceVector.find((o) => {
    if (!o || !o.path || !Array.isArray(o.path) || o.path.length === 0) {
      console.error("无效的价格区域路径:", o);
      return false;
    }
    
    // 检查路径中是否有无效坐标
    if (o.path.some(p => !Array.isArray(p) || p.length < 2 || isNaN(p[0]) || isNaN(p[1]))) {
      console.error("价格区域路径包含无效坐标:", o.path);
      return false;
    }
    
    try {
      return AMap.GeometryUtil.isPointInRing(point__, o.path);
    } catch (error) {
      console.error("检查点是否在价格区域内时出错:", error);
      return false;
    }
  });
  return itemPriceView;
}

function isPointInArea(point__, areatype, polygon_, circleData = null) {
  // 检查参数有效性
  if (!point__) {
    console.error("无效的坐标点:", point__);
    return false;
  }
  
  try {
    // 如果point__是数组（[lng, lat]格式），转换为AMap.LngLat对象
    if (Array.isArray(point__)) {
      if (point__.length < 2 || isNaN(point__[0]) || isNaN(point__[1])) {
        console.error("无效的坐标点数组:", point__);
        return false;
      }
      // 创建新的LngLat对象前确保值有效
      point__ = new AMap.LngLat(Number(point__[0]), Number(point__[1]));
    } else if (point__ instanceof AMap.LngLat) {
      // 确保LngLat对象的值有效
      if (isNaN(point__.getLng()) || isNaN(point__.getLat())) {
        console.error("LngLat对象包含无效值:", point__);
        return false;
      }
    } else {
      console.error("坐标点类型无效，应为数组或AMap.LngLat对象:", point__);
      return false;
    }
    
    if (areatype === "area") {
      if (!polygon_ || !Array.isArray(polygon_) || polygon_.length === 0) {
        console.error("无效的多边形数组:", polygon_);
        return false;
      }
      const isPointInRing = polygon_.some((o) => {
        if (!o || !Array.isArray(o)) {
          console.error("无效的多边形路径:", o);
          return false;
        }
        // 检查多边形路径中是否有无效坐标
        if (o.some(p => !Array.isArray(p) || p.length < 2 || isNaN(p[0]) || isNaN(p[1]))) {
          console.error("多边形路径包含无效坐标:", o);
          return false;
        }
        try {
          return AMap.GeometryUtil.isPointInRing(point__, o);
        } catch (error) {
          console.error("检查点是否在多边形内时出错:", error);
          return false;
        }
      });
      return isPointInRing;
    } else if (areatype === "polygon") {
      if (!polygon_ || !Array.isArray(polygon_) || polygon_.length === 0) {
        console.error("无效的多边形路径:", polygon_);
        return false;
      }
      // 检查多边形路径中是否有无效坐标
      if (polygon_.some(p => !Array.isArray(p) || p.length < 2 || isNaN(p[0]) || isNaN(p[1]))) {
        console.error("多边形路径包含无效坐标:", polygon_);
        return false;
      }
      try {
        return AMap.GeometryUtil.isPointInRing(point__, polygon_);
      } catch (error) {
        console.error("检查点是否在多边形内时出错:", error);
        return false;
      }
    } else {
      if (circleData && typeof circleData.contains === 'function') {
        try {
          return circleData.contains(point__);
        } catch (error) {
          console.error("检查点是否在圆形区域内时出错:", error);
          return false;
        }
      } else {
        console.error("无效的圆形区域数据:", circleData);
        return false;
      }
    }
  } catch (error) {
    console.error("isPointInArea函数执行出错:", error);
    return false;
  }
}

async function openCarpoolMap(type) {
  const data_id = Number($(".pincheOrderAction").attr("id"));
  let lines_pinche_arr = JSON.parse(
    sessionStorage.getItem("lines_pinche_arr")
  ).find((o) => o.id == data_id); //当前拼车路线对象
  let polygon_ = [];
  let current = "";
  let polyType = "",
    circleData = {},
    adcode = "";
  $(".confirm-map-selected-point").attr("clicktype", type);
  $(".confirm-map-selected-point").attr("bussness_type", "pinche");

  const {
    area_lng_start,
    start_area,
    end_area,
    area_lat_start,
    area_lng_end,
    area_lat_end,
    area_radius_start,
    area_radius_end,
    start_area_code,
    end_area_code,
    end_city_code,
    start_city_code,
  } = lines_pinche_arr;

  if (type === "start") {
    //判断是否设置精准限制围栏区域
    console.log("ssssssssssssssss", area_lng_start);
    if (!!area_lng_start) {
      //圆形围栏
      polyType = "circle";
      circleData = {
        center: [area_lng_start, area_lat_start],
        radius: area_radius_start,
      };
    } else {
      //行政区域围栏
      polyType = "area";
      adcode = start_area.name === "市辖区" ? start_city_code : start_area_code;
    }

    const isCheckName = $(
      ".check-place-carpool .check-start-point .tarin-point-label"
    ).attr("name");
    const isCheckAddress = $(
      ".check-place-carpool .check-start-point .tarin-point-label"
    ).attr("address");
    const lng = $(
      ".check-place-carpool .check-start-point .tarin-point-label"
    ).attr("lng");
    const lat = $(
      ".check-place-carpool .check-start-point .tarin-point-label"
    ).attr("lat");
    current = isCheckName
      ? {
          name: isCheckName,
          address: isCheckAddress,
          lng: lng,
          lat: lat,
        }
      : "";
    $("#j-popup-full .ui_popup_title").html("选择上车地点");
    $(".notice-view span").html(
      "蓝色围栏服务范围内可免费接送，请您选择方便的上车点"
    );
  } else {
    if (!!area_lng_end) {
      //圆形围栏
      polyType = "circle";
      circleData = {
        center: [Number(area_lng_end), Number(area_lat_end)],
        radius: Number(area_radius_end),
      };
    } else {
      //行政区域围栏
      polyType = "area";
      adcode = end_area.name === "市辖区" ? end_city_code : end_area_code;
    }

    const isCheckName = $(
      ".check-place-carpool .check-end-point .tarin-point-label"
    ).attr("name");
    const isCheckAddress = $(
      ".check-place-carpool .check-end-point .tarin-point-label"
    ).attr("address");
    const lng = $(
      ".check-place-carpool .check-end-point .tarin-point-label"
    ).attr("lng");
    const lat = $(
      ".check-place-carpool .check-end-point .tarin-point-label"
    ).attr("lat");
    current = isCheckName
      ? {
          name: isCheckName,
          address: isCheckAddress,
          lng: lng,
          lat: lat,
        }
      : "";

    $("#j-popup-full .ui_popup_title").html("选择下车地点");
    $(".notice-view span").html(
      "红色围栏服务范围内可免费接送，请您选择方便的下车点"
    );
  }

  if (polyType === "area") {
    const data_poly = await fetBounds(adcode);
    polygon_ = data_poly;
  }

  polyMapRender(type, polygon_, current, polyType, circleData);
  togglePopupExpress(false, "#j-popup-full");
}

function formatuseTime(arr_, start_) {
  const is_default_via_axis = $("#is_default_via_axis").val();

  arr_.map((o) => {
    o["in_time"] =
      o.use_time != 0 && is_default_via_axis == 1
        ? "约" + addMinutesToTime(start_, o.use_time)
        : "";
    o["in_time_pra"] =
      o.use_time != 0 && is_default_via_axis == 1
        ? addMinutesToTime(start_, o.use_time)
        : "";
    return o;
  });

  return arr_;
}

function openSelectMap(type) {
  const data_index = Number($("#j-popup-half-train-order").attr("index"));
  let ticketObj = JSON.parse(sessionStorage.getItem("ticketObj"))[data_index]; //当前打开班次
  let polygon_ = [];
  let palceData_ = [];
  let current = "";
  $(".confirm-map-selected-point").attr("clicktype", type);
  $(".confirm-map-station-check").attr("clicktype", type);
  $(".confirm-map-selected-point").attr("bussness_type", "train");
  const end_area_code =
    ticketObj.line_class.end_area &&
    ticketObj.line_class.end_area.region_name === "市辖区"
      ? ticketObj.line_class.end_city_code
      : ticketObj.line_class.end_area_code;
  const start_area_code =
    ticketObj.line_class.start_area &&
    ticketObj.line_class.start_area.region_name === "市辖区"
      ? ticketObj.line_class.start_city_code
      : ticketObj.line_class.start_area_code;

  $(".confirm-map-selected-point").attr("start_area_code", start_area_code);
  $(".confirm-map-selected-point").attr("end_area_code", end_area_code);

  const start_points =
    ticketObj.start_address_type == 2 ? [] : ticketObj.start_point;
  const end_points = ticketObj.end_address_type == 2 ? [] : ticketObj.end_point;
  const fullPoints = [...start_points, ...end_points];

  const line_class_ferry = $("#line_class_ferry").val(); //商务摆渡车设置

  if (type === "start") {
    polygon_ =
      ticketObj.start_address_type == 2
        ? JSON.parse(ticketObj.start_polygon).map((o) => o.split(","))
        : [];
    palceData_ =
      ticketObj.start_address_type == 2
        ? []
        : formatuseTime(ticketObj.start_point, ticketObj.start_time);


    

    const isCheckName = $(".check-start-point .tarin-point-label").attr("name");
    const isCheckAddress = $(".check-start-point .tarin-point-label").attr(
      "address"
    );
    const lng = $(".check-start-point .tarin-point-label").attr("lng");
    const lat = $(".check-start-point .tarin-point-label").attr("lat");
    current = isCheckName
      ? {
          name: isCheckName,
          address: isCheckAddress,
          lng: lng,
          lat: lat,
        }
      : "";
    if (ticketObj.start_address_type == 2) {
      if (ticketObj.start_point && ticketObj.start_point.length > 0) {
        //有摆渡车

        const end__ = ticketObj.is_end_ferry == 1 ? ticketObj.end_point : [];
        const fee_full_points = [...ticketObj.start_point, ...end__];
        const sessionLocation = sessionStorage.getItem("sessionLocation");
        const points_fee_start = ticketObj.start_point;
        points_fee_start.map((o) => {
          if (sessionLocation) {
            try {
              const sessionData = JSON.parse(sessionLocation);
              if (sessionData && sessionData.lng_ && sessionData.lat_ && o.longitude && o.latitude) {
                const user_position = new AMap.LngLat(sessionData.lng_, sessionData.lat_);
                const point_position = new AMap.LngLat(o.longitude, o.latitude);
                var distance = Math.round(user_position.distance(point_position));
                if (distance >= 1000) {
                  distance = (Math.round(distance / 100) / 10).toFixed(1) + "km";
                } else {
                  distance = distance + "m";
                }
                o["distance"] = distance;
              } else {
                o["distance"] = null;
              }
            } catch (e) {
              o["distance"] = null;
            }
          } else {
            o["distance"] = null;
          }
          return o;
        });

        console.log("confirm");
        $("#j-popup-full .ui_popup_title").html("选择上车位置");
        polyMapRender(type, polygon_, current,"polygon",
      {},
      ticketObj.line_class.pickup_polygons,points_fee_start);
        $("#j-popup-full").attr("polyType", "fee");
        togglePopupExpress(false, "#j-popup-full");
        $(".notice-view span").html(
          "蓝色围栏服务范围内支持接送，请您选择方便的上车点"
        );


        // lui.showModal({
        //   title: "选择接您的方式",
        //   cancelText: "自行去固定点上车",
        //   confirmText: "上门接",
        //   showCancel: true,
        //   content: `<p class="fee-txt">本条路线班次出发地已开启电子围栏范围内上门接送，或固定上车点车场接送服务，请选择您的具体接送方式：</p>
        // <p class="fee-txt">1. 若选择【上门接】，你可以在围栏范围内选择任意点(乘车人方便上车的位置)作为上车点，司机去接您，请提前到达。</p>
        //   <p class="fee-txt"> 2. 若选择【自行去固定点上车】，您需要自行提前到达你选择的固定上车点或车场。</p>`,
        //   cancel: function () {
        //     //自行去
        //     console.log("cancel");
        //     $("#j-popup-full-point .ui_popup_title").html(
        //       "选择出发地车场或固定上车点"
        //     );
        //     pointMapRender(points_fee_start, fee_full_points, "start");
        //     $("#j-popup-full-point").attr("pointType", "fee");
        //     togglePopupExpress(false, "#j-popup-full-point");
        //   },
        //   confirm: function () {
        //     //需要接送
        //     console.log("confirm");
        //     $("#j-popup-full .ui_popup_title").html("选择上车位置");
        //     polyMapRender(type, polygon_, current,"polygon",
        //   {},
        //   ticketObj.line_class.pickup_polygons,points_fee_start);
        //     $("#j-popup-full").attr("polyType", "fee");
        //     togglePopupExpress(false, "#j-popup-full");
        //     $(".notice-view span").html(
        //       "蓝色围栏服务范围内支持接送，请您选择方便的上车点"
        //     );
        //   },
        // });
      } else {
        $("#j-popup-full .ui_popup_title").html("选择出发地点");
        polyMapRender(
          type,
          polygon_,
          current,
          "polygon",
          {},
          ticketObj.line_class.pickup_polygons
        );
        $("#j-popup-full").attr("polyType", "free");
        togglePopupExpress(false, "#j-popup-full");
        $(".notice-view span").html(
          "蓝色围栏服务范围内支持免费接送，请您选择方便的上车点"
        );
      }
    } else {
      $("#j-popup-full-point .ui_popup_title").html("选择出发地点");
      pointMapRender(palceData_, fullPoints, "start");
      $("#j-popup-full-point").attr("pointType", "free");

      togglePopupExpress(false, "#j-popup-full-point");
    }
  } else {
    polygon_ =
      ticketObj.end_address_type == 2
        ? JSON.parse(ticketObj.end_polygon).map((o) => o.split(","))
        : [];
    palceData_ =
      ticketObj.end_address_type == 2
        ? []
        : formatuseTime(ticketObj.end_point, ticketObj.start_time);
    const isCheckName = $(".check-end-point .tarin-point-label").attr("name");
    const isCheckAddress = $(".check-end-point .tarin-point-label").attr(
      "address"
    );
    const lng = $(".check-end-point .tarin-point-label").attr("lng");
    const lat = $(".check-end-point .tarin-point-label").attr("lat");
    current = isCheckName
      ? {
          name: isCheckName,
          address: isCheckAddress,
          lng: lng,
          lat: lat,
        }
      : "";

    if (ticketObj.end_address_type == 2) {
      if (ticketObj.end_point && ticketObj.end_point.length > 0) {
        //终点有摆渡车

        const start__ =
          ticketObj.is_start_ferry == 1 ? ticketObj.start_point : [];
        const fee_full_points = [...start__, ...ticketObj.end_point];
          
        // 处理下车点的距离信息
        const points_fee_end = ticketObj.end_point;
        points_fee_end.map((o) => {
          if (sessionStorage.getItem("sessionLocation")) {
            try {
              const sessionData = JSON.parse(sessionStorage.getItem("sessionLocation"));
              if (sessionData && sessionData.lng_ && sessionData.lat_ && o.longitude && o.latitude) {
                const user_position = new AMap.LngLat(sessionData.lng_, sessionData.lat_);
                const point_position = new AMap.LngLat(o.longitude, o.latitude);
                var distance = Math.round(user_position.distance(point_position));
                if (distance >= 1000) {
                  distance = (Math.round(distance / 100) / 10).toFixed(1) + "km";
                } else {
                  distance = distance + "m";
                }
                o["distance"] = distance;
              } else {
                o["distance"] = null;
              }
            } catch (e) {
              o["distance"] = null;
            }
          } else {
            o["distance"] = null;
          }
          return o;
        });

          //需要接送
          console.log("confirm");
          $("#j-popup-full .ui_popup_title").html("选择下车位置");
          polyMapRender(
            type, 
            polygon_, 
            current,
            "polygon",
            {},
            ticketObj.line_class.dropoff_polygons,
            points_fee_end
          );
          $("#j-popup-full").attr("polyType", "fee");
          togglePopupExpress(false, "#j-popup-full");
          $(".notice-view span").html(
            "红色围栏服务范围内支持接送，请您选择方便的下车点"
          );

        // lui.showModal({
        //   title: "选择送您的方式",
        //   cancelText: "固定点下车",
        //   confirmText: "送上门",
        //   showCancel: true,
        //   content: `<p class="fee-txt">本条路线班次目的地已开启电子围栏范围内上门接送，或固定下车点车场接送服务，请选择您的具体接送方式：</p>
        // <p class="fee-txt">1. 若选择【送上门】，你可以在围栏范围内选择任意点(乘车人方便下车的位置)作为下车点，司机把你送到家。</p>
        //   <p class="fee-txt">2. 若选择【固定点下车】，司机将送你到你选择的固定下车点或车场。</p>`,
        //   cancel: function () {
        //     //自行去
        //     console.log("cancel");
        //     $("#j-popup-full-point .ui_popup_title").html(
        //       "选择目的地车场或固定下车点"
        //     );
        //     // 处理下车点距离信息
        //     const points_fee_end = ticketObj.end_point.map((o) => {
        //       if (sessionStorage.getItem("sessionLocation")) {
        //         const { lng_, lat_ } = JSON.parse(sessionStorage.getItem("sessionLocation"));
        //         const user_position = new AMap.LngLat(lng_, lat_);
        //         const point_position = new AMap.LngLat(o.longitude, o.latitude);
        //         var distance = Math.round(user_position.distance(point_position));
        //         if (distance >= 1000) {
        //           distance = (Math.round(distance / 100) / 10).toFixed(1) + "km";
        //         } else {
        //           distance = distance + "m";
        //         }
        //         return {...o, distance};
        //       } else {
        //         return {...o, distance: null};
        //       }
        //     });
        //     pointMapRender(points_fee_end, fee_full_points, "end");
        //     $("#j-popup-full-point").attr("pointType", "fee");
        //     togglePopupExpress(false, "#j-popup-full-point");
        //   },
        //   confirm: function () {
        //     //需要接送
        //     console.log("confirm");
        //     $("#j-popup-full .ui_popup_title").html("选择下车位置");
        //     polyMapRender(
        //       type, 
        //       polygon_, 
        //       current,
        //       "polygon",
        //       {},
        //       ticketObj.line_class.dropoff_polygons,
        //       points_fee_end
        //     );
        //     $("#j-popup-full").attr("polyType", "fee");
        //     togglePopupExpress(false, "#j-popup-full");
        //     $(".notice-view span").html(
        //       "红色围栏服务范围内支持接送，请您选择方便的下车点"
        //     );
        //   },
        // });
      } else {
        $("#j-popup-full .ui_popup_title").html("选择到达地点");
        polyMapRender(
          type,
          polygon_,
          current,
          "polygon",
          {},
          ticketObj.line_class.dropoff_polygons,
          ticketObj.end_point
        );
        $("#j-popup-full").attr("polyType", "free");
        togglePopupExpress(false, "#j-popup-full");
        $(".notice-view span").html(
          "红色围栏服务范围内支持免费接送，请您选择方便的下车点"
        );
      }
    } else {
      $("#j-popup-full-point .ui_popup_title").html("选择到达地点");
      
      // 处理下车点距离信息
      const formatted_end_points = formatuseTime(ticketObj.end_point, ticketObj.start_time).map((o) => {
        if (sessionStorage.getItem("sessionLocation")) {
          try {
            const sessionData = JSON.parse(sessionStorage.getItem("sessionLocation"));
            if (sessionData && sessionData.lng_ && sessionData.lat_ && o.longitude && o.latitude) {
              const user_position = new AMap.LngLat(sessionData.lng_, sessionData.lat_);
              const point_position = new AMap.LngLat(o.longitude, o.latitude);
              var distance = Math.round(user_position.distance(point_position));
              if (distance >= 1000) {
                distance = (Math.round(distance / 100) / 10).toFixed(1) + "km";
              } else {
                distance = distance + "m";
              }
              return {...o, distance};
            }
          } catch (e) {}
        }
        return {...o, distance: null};
      });
      
      pointMapRender(formatted_end_points, fullPoints, "end");
      $("#j-popup-full-point").attr("pointType", "free");

      togglePopupExpress(false, "#j-popup-full-point");
    }
  }
}
const newSeatView = (seat_list, iscontrol = false) => {
  let seatDom = "";
  for (let a = 0; a < seat_list.length; a++) {
    let itemSeat = "";
    for (let b = 0; b < seat_list[a].length; b++) {
      const child = seat_list[a][b];
      itemSeat += `<div  class="item-seat ${
        child.seat_id === 1 || child.optional === 1 ? "disabled" : ""
      } ${child.checked ? "checked" : ""}" 
      name="${child.name}" price="${child.price}" seat_id="${child.seat_id}">
               ${
                 child.seat_id === 1 || child.optional === 1
                   ? `<img class="seat-icon" src="/Theme/images/passenger/seat-disable.svg" />`
                   : child.checked
                   ? `<img class="seat-icon"  src="/Theme/images/passenger/seat-checked.svg" />`
                   : `<img class="seat-icon"  src="/Theme/images/passenger/seat-select.svg" />`
               }
                ${child.seat_id === 1 ? `<span>${child.name}</span>` : ""}
               ${
                 child.seat_id != 1
                   ? `<span  class="price-seat">
                 <label class="price-label">¥</label>${child.price}
               </span>`
                   : ""
               }
               </div>`;
    }
    seatDom += `<div class="seat-line new-seat-line"><span class="line-index">第${
      a + 1
    }排</span> ${itemSeat}</div>`;
  }
  return `
      <div class="seat-container">${seatDom}</div>
      <div class="showCheck"></div>
     `;
};
const seatView = (seat_list, iscontrol = false) => {
  let seatDom = "";
  for (let a = 0; a < seat_list.length; a++) {
    let itemSeat = "";
    for (let b = 0; b < seat_list[a].length; b++) {
      const child = seat_list[a][b];
      itemSeat += `<div  class="item-seat ${
        child.seat_id === 1 || child.optional === 1 ? "disabled" : ""
      } ${child.checked ? "checked" : ""}" 
      name="${child.name}" price="${child.price}" seat_id="${child.seat_id}">
               ${
                 child.seat_id === 1 || child.optional === 1
                   ? `<img class="seat-icon" src="/Theme/images/passenger/seat-disable.svg" />`
                   : child.checked
                   ? `<img class="seat-icon"  src="/Theme/images/passenger/seat-checked.svg" />`
                   : `<img class="seat-icon"  src="/Theme/images/passenger/seat-select.svg" />`
               }
                <span>${child.name}</span>
               ${
                 child.seat_id != 1
                   ? `<span  class="price-seat">
                 <label class="price-label">¥</label>${child.price}
               </span>`
                   : ""
               }
               </div>`;
    }
    seatDom += `<div class="seat-line">${itemSeat}</div>`;
  }
  return `
      <div class="seat-container">${seatDom}</div>
      <div class="showCheck"></div>
     `;
};

function fetchCharterList(start_code = "", end_code = "", cid = "") {
  lui.loading();
  return new Promise((resolve, reject) => {
    $.ajax({
      url:
        "/Passenger/Line/queryBaocheLine?callback=" +
        sessionStorage.getItem("callback"),
      // url: "/Passenger/Line/getAutoCharteredLines?callback=" + sessionStorage.getItem("callback"),
      type: "get",
      dataType: "json",
      data: {
        start_address_code: start_code,
        end_address_code: end_code,
        cid: cid,
      },
      success: function (data) {
        lui.loading("close");
        charter_list = data.ret ? data.data : [];
        resolve(data.ret ? data.data : []);
      },
      error: function () {
        lui.loading("close");
        reject("error");
      },
    });
  });
}

function renderCharter(data) {
  let dom = "";
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    // let tag = ''
    // let tags = item.summary.split(',')
    // tags.map((o) => {
    //   tag += `<div class="item-light"><span class="">${o}</span><div class="line"></div></div>`
    // })

    let cates = "";
    item.line_categories.map((o_) => {
      cates += `<span class="pinche-tag">${o_.title}</span>`;
    });
    dom += `<li class="item-charter-line" onclick="checkCharter(${item.id})">
    <div class="label-title-warp">
     <div class="base-flex">
      <div class="label-title">${item.start_name}</div>
      <i class="pinche_palce_right"></i>
      <div class="label-title">${item.end_name}</div>
     </div> 
    </div>
    <div class="touch-light">${item.summary}</div>
     <div class="bottom-pinche-price">
      <div class="tagss">${cates}</div> 
     <div class="right-o">
      <div class="price"><b>${item.car_type_data[0].price}</b><label>元起</label></div>
     </div>
     </div>
  </li>`;
  }
  if (!dom) {
    $(".charter-list-container").html(`<div class="bus-no-result">
    <img class="empty-content-icon" src="/Theme/images/passenger/empty-content.svg" alt="">
    <div class="title">抱歉，您所选择的地址暂无法提供服务，请重新选择上下车地址</div>
    <div class="content">您可尝试切换线路</div>
    <button type="button" size="sm" class="ui_btn research" onclick="charterBack()">重新查询</button>
    </div>`);
    return;
  }
  $(".charter-list-container").html(dom);
  // 抱歉，您所选择的地址暂无法提供服务，请重新选择上下车地址
}

function showRules() {
  const sub_business_type = sessionStorage.getItem("sub_business_type");
  $.ajax({
    url: `${$(
      "#api_host"
    ).val()}/api/types/4/agreements?sub_business_type=${sub_business_type}`,
    type: "get",
    headers: {
      Authorization: `Bearer ${$("#passenger_token").val()}`,
    },
    success: function (json) {
      if (json.data) {
        console.log(json.data.agreement_content);
        $(".agreement-words").html(json.data.agreement_content);
        $(".agreement-title-top").html(json.data.agreement_name_alias);
        $(".agreement-full-cover").show();
      } else {
        $(".agreement-full-cover").hide();
      }
    },
    error: function (json) {},
    timeout: 300000,
  });
}
function showDesc() {
  const clickIndex = Number($("#j-popup-half-train-order").attr("index"));
  const currentClass = JSON.parse(sessionStorage.getItem("ticketObj"))[
    clickIndex
  ];
  $(".agreement-words").html(
    currentClass.summary ? currentClass.summary : "暂无说明"
  );
  $(".agreement-title-top").html("路线说明");
  $(".agreement-full-cover").show();
}

function renderBussess_(biz_support) {
  const bussness_type = sessionStorage.getItem("flag_");
  let bussess_ = "";
  for (let i = 0; i < biz_support.length; i++) {
    if (
      (bussness_type && biz_support[i].id != bussness_type) ||
      (!bussness_type && biz_support[i].id != "carpool")
    ) {
      bussess_ += `<div class="item-quick" bid="${biz_support[i].id}">
      ${
        biz_support[i].id != "phone"
          ? `<img class="bussess_${biz_support[i].type}" src="/Theme/images/passenger/bussess_${biz_support[i].type}.png" alt="">`
          : `<img class="bussess_" src="/Theme/images/passenger/bussess_.png" alt="">`
      }
      <span>${biz_support[i].name}</span>
    </div>`;
    }
  }
  $(".quick-bussess").html(bussess_);

  const doms_ = $(".custom-bussess").children().length;
  console.log("!doms_", doms_);
  // console.log('!doms_', !doms_)
  // console.log('!doms_', doms_ == '')
  if (!bussess_ && !doms_) {
    $(".margin-box").hide();
  }
}

var indexPage = {
  //加载地图
  loadMap: function() {
    if ($("#container").hasClass("loadservice")) {
      return false;
    } else {
      $("#container").addClass("loadservice");
      //加载地图
      map = new AMap.Map('container', {
        resizeEnable: true,
        showBuildingBlock: true,
        zoom: 16
      });
      getWxLocation()
        .then((res) => {
          map.clearMap();
          //定位成功
          const [lngX, latY] = res;
          const img = "/Theme/images/passenger/init.png";
          const userMarker = new AMap.Marker({
            //添加自定义点标记
            map: map,
            position: res, //基点位置
            offset: new AMap.Pixel(-10, -10), //相对于基点的偏移位置
            draggable: false, //是否可拖动
            content: `<img class="user_icon" src=${img} style="width:24px;height:24px;" />`, //自定义点标记覆盖物内容
          });
          $(".map").attr({
            p_lng: lngX,
            p_lat: latY,
          });
          //设定当前位置为地图中心点
          map.setCenter(new AMap.LngLat(lngX, latY));
          $(".origin_place").attr({
            lng: lngX,
            lat: latY,
          });
          //设置地图默认图标
          startMarker = new AMap.Marker({
            map: map,
            position: res,
            offset: new AMap.Pixel(-12, -24), //相对于基点的偏移位置，减小偏移
            icon: new AMap.Icon({
              size: new AMap.Size(24, 36), //图标大小减小
              image: "/Theme/images/passenger/map_control.png",
              imageOffset: new AMap.Pixel(8, 2), //图标相对于marker的偏移量
              imageSize: new AMap.Size(16, 24), //图片显示大小减小
            }),
          });
          // 尝试设置动画，如果支持的话
          try {
            if (startMarker.setAnimation && typeof startMarker.setAnimation === 'function') {
              startMarker.setAnimation("AMAP_ANIMATION_DROP");
            }
            
            // 更新定位状态和位置信息到界面
            $(".current-view-city-label").text("定位成功");
            $(".left-adress").text("定位成功");
          } catch(e) {
            console.log("地图标记动画设置失败", e);
          }
        })
        .catch((err) => {
          console.log("地图加载错误", err);
        });
    }
  },
  
  //初始化
  initpage: async function () {
    sessionStorage.setItem("callback", $("#call_url").val());
    sessionStorage.setItem("current_home_page", $("#current_home_page").val());
    var that = this;

    // 获取URL中的参数
    const cid = $("#cid").val() || "";
    const driver_id = $("#driver_id").val() || "";

    // 主动处理URL中的cid或driver_id参数，自动执行班次查询
    if (cid || driver_id) {
      // 切换到regular页签
      document.getElementById("regular").click();

      if (cid) {
        const cates = await fetchCates();
        const customized = cates.find((o) => o.id == cid);
        if (customized) {
          $("#j-popup-right .info-text").html(customized.title);
          const search_date = moment().locale("zh-cn").format("YYYY-MM-DD");
          // 打开班次页面
          openTrainPage();
          // 自动查询班次
          that.getregular(search_date);
        }
      } else if (driver_id) {
        // 获取司机信息
        renderDriverInfo(driver_id);
        const search_date = moment().locale("zh-cn").format("YYYY-MM-DD");
        // 打开班次页面
        openTrainPage();
        proDate(search_date, "", "");
        // 自动查询班次
        that.getregular(search_date);
      }
    }

    // 以下是原有代码
    //默认调用微信sdk定位
    that.locationService();
    //绑定用户基本操作dom
    that.baseControl();
    //初始化日期
    that.setTime();
    //that.getOrders();
    //获取系统配置
    $.ajax({
      url:
        "/Home/Common/getUsePlatformInfo?callback=" +
        sessionStorage.getItem("callback"),
      type: "get",
      success: function (json) {
        var json = JSON.parse(json);
        if (json.ret) {
          const { biz_support } = json?.data || [];
          localStorage.setItem("biz_support", JSON.stringify(biz_support));
          const local_biz = localStorage.getItem("biz_support");
          let data__ =
            local_biz && local_biz != "undefined"
              ? JSON.parse(local_biz)
              : biz_support;
          renderBussess_(data__);
        }
      },
      timeout: 300000,
    });
  },
  //功能控制
  pageControl: async function (roootId) {
    if (roootId === "carpool") {
      if ($("#tpl_home").hasClass("loaded")) {
        return;
      }
      $("#tpl_home").addClass("loaded");

      setTimeout(() => {
        loaded();
        if ($(".start-pinche-place .hadSelcetedCode").html()) {
          pullDownAction();
        }
      }, 800);
      // //获取拼车广告
      const trainAd = await fetchswiper(2);
      if (trainAd.length) {
        $(".top-bg-pinche-content").html(renderSwiper(trainAd, "bgImg", 2));
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
      //获取拼车场景
      $.ajax({
        url:
          "/Home/Category/getCategories?type=1&callback=" +
          sessionStorage.getItem("callback"),
        type: "get",
        dataType: "json",
        success: function (e) {
          let html1 = "";
          if (e.ret) {
            for (var i = 0; i < e.data.length; i++) {
              html1 += `<div class="item-classes-filter" id=${e.data[i].id}>${e.data[i].title}</div>`;
            }
            $(".classes-group").html(`<div class="group-list">
            <div class="item-classes-filter checked">全部</div>
            ${html1}
          </div>`);
          } else {
            $(".classes-group").html("");
          }
        },
        error: function () {
          console.log("出错了");
        },
      });
    } else if (roootId === "taxi") {
      indexPage.palceCheckControl(11);
      if ($("#tpl_taxi").hasClass("loaded")) {
        return;
      }
      $("#tpl_taxi").addClass("loaded");
      if (!!!map) indexPage.loadMap();
      $(".taxi_click").on("click", function () {
        $(".main_content").attr("place_type", "taxi");
      });
      const taxiAd = await fetchswiper(10);
      if (taxiAd.length) {
        $("#tpl_taxi .point-info-end").after(renderSwiper(taxiAd, "", 11));
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
      //出租车下单
      $(document).on("click", ".taxiOrderAction", function () {
        if (
          $("#cellphone-data").val() == "" ||
          $("#cellphone-data").val().length == 0
        ) {
          $(".register_page_one").show("normal");
          $(".register_page_one").css("left", 0);
        } else {
          const checked = $(".taxi-agree-check").prop("checked");
          if (!checked) {
            lui.toast({
              position: "middle",
              text: "下单前需要阅读并同意《用车服务协议》",
            });
            return;
          }
          var start_address_code = $(this).attr("scode");
          var end_address_code = $(this).attr("ecode");
          var line_taxi_id = $(this).attr("taxiId");
          var person_num = 1;
          var str_time = $("#j-popup-half-comfirm #picker5").attr(
            "current_date"
          );
          var start_lng = $(".origin_place").attr("lng");
          var start_lat = $(".origin_place").attr("lat");
          var end_lng = $(".finish_place").attr("lng");
          var end_lat = $(".finish_place").attr("lat");
          var distance = $(this).attr("distance");
          var duration = $(this).attr("duration");
          var start_address = $(".origin_place").html();
          var end_address = $(".finish_place").html();
          var reseverd_info = $(".fast-message").attr("reseverd_info");
          var reseverd_phone = $(".fast-fare").attr("reseverd_tel");

          lui.loading();
          $.ajax({
            url:
              "/Passenger/Order/doMobileBookLineTaxi?callback=" +
              sessionStorage.getItem("callback"),
            type: "post",
            data: {
              distance: distance,
              duration: duration,
              end_address_code: end_address_code,
              start_address_code: start_address_code,
              drive_mode: 1,
              line_taxi_id: line_taxi_id,
              book_seating: person_num,
              start_time: str_time
                ? str_time
                : moment().locale("zh-cn").format("YYYY-MM-DD HH:mm"),
              start_longitude: start_lng,
              start_latitude: start_lat,
              start_address_remark: start_address,
              end_address_remark: end_address,
              end_longitude: end_lng,
              end_latitude: end_lat,
              reseverd_info: reseverd_info,
              reseverd_phone: reseverd_phone,
              // coupon_record_id: $(".sureTaxiOrder").attr("coupon_record_id"),
            },
            dataType: "json",
            success: function (data) {
              lui.loading("close");
              //下单成功
              if (data.ret) {
                lui.toast({
                  type: "ok",
                  text: "预约成功",
                });
                setTimeout(() => {
                  var order_id = data.data.order_id;
                  window.location.href =
                    "/passenger_order_detail?order_id=" +
                    order_id +
                    "&callback=" +
                    sessionStorage.getItem("callback") +
                    "&show=1";
                }, 1500);
                // sessionStorage.setItem("showcontrol", 1)
              }
              //下单失败
              else {
                lui.toast({
                  type: "warning",
                  text: data?.data || "操作失败",
                });
              }
            },
            error: function () {
              lui.loading("close");
            },
          });
        }
      });
    } else if (roootId === "fast_car") {
      indexPage.palceCheckControl(7);
      if ($("#tpl_fast").hasClass("loaded")) {
        return;
      }
      $("#tpl_fast").addClass("loaded");
      if (!!!map) indexPage.loadMap();
      $(".fast_click").on("click", function () {
        $(".main_content").attr("place_type", "fast");
      });
      //快车下单
      $(document).on("click", ".fastOrderAction", function () {
        if (
          $("#cellphone-data").val() == "" ||
          $("#cellphone-data").val().length == 0
        ) {
          $(".register_page_one").show("normal");
          $(".register_page_one").css("left", 0);
        } else {
          const checked = $(".taxi-agree-check").prop("checked");
          if (!checked) {
            lui.toast({
              position: "middle",
              text: "下单前需要阅读并同意《用车服务协议》",
            });
            return;
          }

          var start_address_code = $(this).attr("scode");
          var end_address_code = $(this).attr("ecode");
          var line_fast_id = $(this).attr("fastId");
          var str_time = $("#j-popup-half-comfirm #picker5").attr(
            "current_date"
          );
          var start_lng = $(".origin_place").attr("lng");
          var start_lat = $(".origin_place").attr("lat");
          var end_lng = $(".finish_place").attr("lng");
          var end_lat = $(".finish_place").attr("lat");
          var distance = $(this).attr("distance");
          var duration = $(this).attr("duration");
          var start_address = $(".origin_place").html();
          var end_address = $(".finish_place").html();
          var reseverd_info = $(".fast-message").attr("reseverd_info");
          var reseverd_phone = $(".fast-fare").attr("reseverd_tel");
          lui.loading();
          $.ajax({
            url:
              "/Passenger/Order/doMobileBookLineFast?callback=" +
              sessionStorage.getItem("callback"),
            type: "post",
            data: {
              distance: distance,
              duration: duration,
              end_address_code: end_address_code,
              start_address_code: start_address_code,
              drive_mode: 1,
              line_fast_id: line_fast_id,
              book_seating: 1,
              start_time: str_time
                ? str_time
                : moment().locale("zh-cn").format("YYYY-MM-DD HH:mm"),
              start_longitude: start_lng,
              start_latitude: start_lat,
              start_address_remark: start_address,
              end_address_remark: end_address,
              end_longitude: end_lng,
              end_latitude: end_lat,
              reseverd_info: reseverd_info,
              reseverd_phone: reseverd_phone,
              // coupon_record_id: $(".sureFastOrder").attr("coupon_record_id"),
            },
            dataType: "json",
            success: function (data) {
              lui.loading("close");
              //下单成功
              if (data.ret) {
                lui.toast({
                  type: "ok",
                  text: "预约成功",
                });

                setTimeout(() => {
                  var order_id = data.data.order_id;
                  window.location.href =
                    "/passenger_order_detail?order_id=" +
                    order_id +
                    "&callback=" +
                    sessionStorage.getItem("callback") +
                    "&show=1";
                }, 1500);
              }
              //下单失败
              else {
                lui.toast({
                  type: "warning",
                  text: data?.data || "操作失败",
                });
              }
            },
            error: function () {
              lui.loading("close");
            },
          });
        }
      });
    } else if (roootId === "chart_car") {
      indexPage.palceCheckControl(2);
      if ($("#tpl_chart").hasClass("loaded")) {
        return;
      }
      $("#tpl_chart").addClass("loaded");
      //包车广告
      const charteredAd = await fetchswiper(3);
      if (charteredAd.length) {
        $(".top-bg-chartered-content").html(
          renderSwiper(charteredAd, "bgImg", 3)
        );
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
      //包车场景
      $.ajax({
        url:
          "/Home/Category/getCategories?type=2&callback=" +
          sessionStorage.getItem("callback"),
        type: "get",
        dataType: "json",
        success: function (res) {
          if (res.ret) {
            $(".chartered-classes").html(rendercharteredListy(res.data));
          } else {
            $(".chartered-classes").html("");
          }
        },
      });
      set_time_space("00:00", "24:00", "60");
      //包车分类点击直接进入
      $(document).on("click", ".chartered-classify", async function () {
        const cid = $(this).attr("cid");
        const cname = $(this).attr("ctitle");
        $(".thanos-header-info .center-title").html(`${cname}`);
        $(".line-mile").css("display", "none");
        $(".charter-list-container").html("");
        togglePopupExpress(false, "#popup-charter-list", "right");
        const res = await fetchCharterList("", "", cid);
        renderCharter(res);
      });

      //包车路线筛选
      $(".chartered-search").on("click", async function () {
        const start_code = $(".start-chartered-place").attr(
          "start_address_code"
        );
        const end_code = $(".end-chartered-place").attr("end_address_code");
        let fromName = $(".start-chartered-place .chartered-checked").html();
        let toName = $(".end-chartered-place .chartered-checked").html();
        var start_lng = $(".start-chartered-place").attr("start_longitude");
        var start_lat = $(".start-chartered-place").attr("start_latitude");
        var end_lng = $(".end-chartered-place").attr("end_longitude");
        var end_lat = $(".end-chartered-place").attr("end_latitude");

        // const timeReady = $('.end-chartered #picker5').attr('current_date')
        if (!!!start_code) {
          lui.toast({
            position: "middle",
            text: `请选择起点`,
          });
          return;
        }
        // if (!!!timeReady) {
        //   lui.toast({
        //     position: 'middle',
        //     text: `请选择用车时间`
        //   });
        //   return
        // }
        if (!!!end_code) {
          lui.toast({
            position: "middle",
            text: `请选择终点`,
          });
          return;
        }
        $(".thanos-header-info .center-title").html(`${fromName} - ${toName}`);
        $(".line-mile").css("display", "none");
        $(".charter-list-container").html("");
        togglePopupExpress(false, "#popup-charter-list", "right");
        const res = await fetchCharterList(start_code, end_code);
        renderCharter(res);
        const result = await checkDriving(
          start_lng,
          start_lat,
          end_lng,
          end_lat
        );
        $(".line-mile").css("display", "flex");
        $(".line-mile span").html(
          `起终点约${result.distanceView}/约行驶${result.timeView}`
        );
      });

      //包车下单
      $(".charterOrderAction").on("click", function () {
        if (
          $("#cellphone-data").val() == "" ||
          $("#cellphone-data").val().length == 0
        ) {
          $(".register_page_one").show("normal");
          $(".register_page_one").css("left", 0);
        } else {
          var start_lng = $(".start-chartered-place").attr("start_longitude");
          var start_lat = $(".start-chartered-place").attr("start_latitude");
          var start_address = $(".start-chartered-place").attr("start_name");
          var end_lng = $(".end-chartered-place").attr("end_longitude");
          var end_lat = $(".end-chartered-place").attr("end_latitude");
          var end_address = $(".end-chartered-place").attr("end_name");
          var line_id = $(this).attr("id");
          var price_id = $(this).attr("pid");

          if (!start_lng) {
            lui.toast({
              position: "middle",
              text: "请选择包车起点",
            });
            return;
          }
          if (!end_lng) {
            lui.toast({
              position: "middle",
              text: "请选择包车终点",
            });
            return;
          }

          const reseverd_phone = $(".charter-fare").attr("reseverd_tel");
          const reseverd_info = $(".charter-message").attr("reseverd_info");
          const start_time = $("#j-popup-half-charter #picker5").attr(
            "current_date"
          );
          if (!start_time) {
            document.getElementById("picker5").click();
            return;
          }
          const checked = $(".charter-agree-check").prop("checked");
          if (!checked) {
            lui.toast({
              position: "middle",
              text: "下单前需要阅读并同意《用车服务协议》",
            });
            return;
          }

          lui.loading();
          $.ajax({
            url:
              "/Passenger/Order/doMobileBookLineChartered?callback=" +
              sessionStorage.getItem("callback"),
            type: "post",
            data: {
              line_id: line_id,
              start_time: start_time,
              start_longitude: start_lng,
              start_latitude: start_lat,
              end_longitude: end_lng,
              end_latitude: end_lat,
              start_address_remark: start_address,
              end_address_remark: end_address,
              line_chartered_price_id: price_id,
              reseverd_phone: reseverd_phone,
              reseverd_info: reseverd_info,
              coupon_record_id: $(".tpl_chart").attr("coupon_record_id"),
            },
            dataType: "json",
            success: function (data) {
              lui.loading("close");
              //下单成功
              if (data.ret) {
                var order_id = data.data.order_id;
                const pay_mode = $("#pay_mode").val();
                if (pay_mode == 0) {
                  lui.toast({
                    type: "ok",
                    text: "预约成功",
                  });
                  setTimeout(() => {
                    window.location.href =
                      "/passenger_order_detail?order_id=" +
                      order_id +
                      "&callback=" +
                      sessionStorage.getItem("callback") +
                      "&show=1";
                  }, 2000);
                } else {
                  indexPage.getPay(
                    data?.data?.order_no,
                    data?.data?.price,
                    data?.data?.order_id
                  );
                }
              } else {
                lui.toast({
                  type: "warning",
                  text: data?.data || "操作失败",
                });
              }
            },
            error: function () {
              lui.loading("close");
            },
          });
        }
      });
    } else if (roootId === "regular") {
      if ($("#tpl_regular").hasClass("loaded")) {
        return;
      }
      $("#tpl_regular").addClass("loaded");
      //获取热门路线
      const strart_city = $("#strart_city .station-selected").html() || "";
      $.ajax({
        url:
          `/Passenger/Line/getRecommendLineClassTrain?city_name=${strart_city}&page=1&size=10&callback=` +
          sessionStorage.getItem("callback"),
        type: "get",
        success: function (json) {
          var stl = "";
          var json = JSON.parse(json);

          if (json.ret) {
            sessionStorage.removeItem("ticketHotObj");
            sessionStorage.setItem("ticketHotObj", JSON.stringify(json.data));
            for (var i = 0; i < json.data.length; i++) {
              const o = json.data[i];
              stl += `<div class="item-lines" start_address_name="${o.start_address_name}" start_address_code="${o.start_address_code}" end_address_code="${o.end_address_code}" end_address_name="${o.end_address_name}">
                <div class="line-label">${o.start_address_name} ⇀ ${o.end_address_name}</div>
                            <div class="right-info-warp">
                                <div class="price">
                                    ¥<span>${o.min_price}</span>起
                                </div>
                                <div class="line-action">预定</div>
                            </div>
                        </div>`;
            }
            if (stl) {
              const hotsHtml = `<div class="hot-lines-container">
                            <h6><img class="hot" src="/Theme/images/passenger/hot.svg"> 热门推荐</h6>
                            <div class="lines-list">
                            ${stl}
                            </div>
                        </div>`;
              $(".hot-lines-warp").html(hotsHtml);
            } else {
              $(".hot-lines-warp").html("");
            }
          }
        },
        error: function (json) {},
        timeout: 300000,
      });
      const trainAd = await fetchswiper(4);
      if (trainAd.length) {
        $(".top-bg-warp-content").html(renderSwiper(trainAd, "bgImg", 4));
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
      //获取班线车分类
      $.ajax({
        url:
          "/Home/Category/getCategories?type=5&callback=" +
          sessionStorage.getItem("callback"),
        type: "get",
        success: function (json) {
          var json = JSON.parse(json);
          if (json.ret) {
            if (json.count <= 30) {
              var append = `<div class="concat-line">
            <div class="line-concat"><img src="/Theme/images/passenger/lianjie1.png" alt=""></div>
            <div class="line-concat"><img src="/Theme/images/passenger/lianjie1.png" alt=""></div>
            </div>`;
              var classificationImage = renderClassificationImage(json.data);
              if (classificationImage) {
                append = append + `<div class="train-classes quick-bussess">` + classificationImage + `</div>`;
              }
              var classification = renderClassification(json.data);
              if (classification) {
                append = append + `<div class="train-classes">` + classification + `</div>`;
              }
              $(".typeList-container").html(append);
            } else {
              const moreDom = `<span class="more-dom">${renderClassification(
                json.data.slice(30, json.data.length)
              )}</span>`;
              $(".typeList-container").html(
                `<div class="train-classes">${renderClassification(
                  json.data.slice(0, 30)
                )} ${moreDom} <span class="more-sence">更多 <i class="arrow-down"></i><span/></div>`
              );
            }
          }
        },
        error: function (json) {},
        timeout: 300000,
      });
      //显示历史搜索
      const historyList = JSON.parse(localStorage.getItem("trainLogs"));
      if (historyList) {
        $(".list-history-warp").html(renderHistory(historyList));
        $(".searchHistory").show();
      }
      // 交换地点
      $(".exchange_icon").on("click", function () {
        let fromName = $("#strart_city .station-selected").html();
        let toName = $("#end_city .station-selected").html();
        if (!!!fromName) {
          lui.toast({
            position: "middle",
            text: `请选择出发地`,
          });
          return;
        }
        if (!!!toName) {
          lui.toast({
            position: "middle",
            text: `请选择目的地`,
          });
          return;
        }

        var exchange = "";
        exchange = fromName;
        fromName = toName;
        toName = exchange;
        $("#strart_city .station-selected").html(fromName);
        $("#end_city .station-selected").html(toName);
      });
      //定制客运分类点击直接进入
      $(document).on("click", ".train-classify", function () {
        const cid = $(this).attr("cid");
        const cname = $(this).attr("ctitle");
        const today_ = moment().locale("zh-cn").format("YYYY-MM-DD");
        var newurl = updateQueryStringParameter(
          window.location.href,
          "cid",
          cid
        );
        //向当前url添加参数，没有历史记录
        window.history.replaceState(
          {
            path: newurl,
          },
          "",
          newurl
        );
        $("#cid").val(cid);
        $("#j-popup-right .info-text").html(cname);
        openTrainPage();
        proDate(today_, "", "");
      });
      //筛选查询
      $(".train-search").on("click", function () {
        let fromName = $("#strart_city .station-selected").html();
        let toName = $("#end_city .station-selected").html();
        if (!!!fromName) {
          // lui.toast({
          //   position: 'middle',
          //   text: `请选择出发地`
          // });
          document.getElementById("strart_city").click();
          return;
        }
        if (!!!toName) {
          // lui.toast({
          //   position: 'middle',
          //   text: `请选择目的地`
          // });
          document.getElementById("end_city").click();
          return;
        }
        const start_level = $("#strart_city").attr("codelevel");
        const end_level = $("#end_city").attr("codelevel");
        const start_code = $("#strart_city").attr("ad_code") || "";
        const end_code = $("#end_city").attr("ad_code") || "";
        const start_params = {
          start_name: fromName,
          start_level: start_level,
          start_code: start_code,
        };
        const end_params = {
          end_name: toName,
          end_level: end_level,
          end_code: end_code,
        };

        let currentLog = JSON.parse(localStorage.getItem("trainLogs")) || [];
        const itemLog = {
          start: start_params,
          end: end_params,
        };
        for (var i = 0; i < currentLog.length; i++) {
          if (JSON.stringify(itemLog) == JSON.stringify(currentLog[i])) {
            currentLog.splice(i, 1);
          }
        }
        currentLog.unshift(itemLog);
        localStorage.setItem("trainLogs", JSON.stringify(currentLog));
        $(".list-history-warp").html(renderHistory(currentLog));
        $(".searchHistory").show();
        const search_date = $(".date-row").attr("date");
        $("#j-popup-right .info-text").html(fromName + " ⇀ " + toName);
        openTrainPage();
        proDate(search_date, start_params, end_params);
      });
      //点击历史
      $(".searchHistory").on("click", ".item-history-cell", function () {
        const start_level = $(this).attr("start_level");
        const end_level = $(this).attr("end_level");
        const start_code = $(this).attr("start_code");
        const end_code = $(this).attr("end_code");
        const start_name = $(this).attr("start_name");
        const end_name = $(this).attr("end_name");
        const start_params = {
          start_name: start_name,
          start_level: start_level,
          start_code: start_code,
        };
        const end_params = {
          end_name: end_name,
          end_level: end_level,
          end_code: end_code,
        };
        const search_date = moment().locale("zh-cn").format("YYYY-MM-DD");
        $("#j-popup-right .info-text").html(start_name + " ⇀ " + end_name);
        openTrainPage();
        proDate(search_date, start_params, end_params);
      });
      //清除历史
      $(".searchHistory").on("click", ".handleClear", function () {
        localStorage.removeItem("trainLogs");
        $(".list-history-warp").empty();
        $(".searchHistory").hide();
      });
      //推荐路线
      $(".hot-lines-warp").on("click", ".item-lines", function () {
        var start_address_name = $(this).attr("start_address_name");
        var end_address_name = $(this).attr("end_address_name");
        var start_address_code = $(this).attr("start_address_code");
        var end_address_code = $(this).attr("end_address_code");
        const search_date = moment().locale("zh-cn").format("YYYY-MM-DD");
        $("#j-popup-right .info-text").html(
          start_address_name + " ⇀ " + end_address_name
        );
        openTrainPage();
        const start_params = {
          start_name: start_address_name,
          start_level: "name",
          start_code: start_address_code,
        };
        const end_params = {
          end_name: end_address_name,
          end_level: "name",
          end_code: end_address_code,
        };
        proDate(search_date, start_params, end_params);
      });
      $(document).on(
        "click",
        ".unabled-list .item-new-trainticket",
        function () {
          if ($(this).hasClass("hadover")) {
            lui.toast({
              position: "middle",
              text: "该班次已无票或已过售票时间，请选择其他班次",
            });
            return;
          }
        }
      );
      //点击班次列表
      $(document).on(
        "click",
        ".new-trainList .item-new-trainticket",
        function () {
          $(this).find(".item-time-in").addClass("translation");
          setTimeout(() => {
            $(this).find(".item-time-in").removeClass("translation");
          }, 1000);

          lui.toast({
            position: "middle",
            text: "请直接选择出发时间，即可快速订票",
          });
          return;
        }
      );
      //点击班次列表
      $(document).on(
        "click",
        ".new-trainList .item-new-trainticket .item-time-in, .new-trainList .item-coustom-trainticket .act-reserve ",
        function (e) {
          e.stopPropagation();
          if ($(this).hasClass("hadover")) {
            lui.toast({
              position: "middle",
              text: "该售票时间已无法购票，请选择其他班次",
            });
            return;
          }

          seat_lists = []; //重置全局变量
          const clickIndex = Number($(this).attr("index")); //获取当前点击班次下标，去除$this副作用
          const currentClass = JSON.parse(sessionStorage.getItem("ticketObj"))[
            clickIndex
          ]; //currentClass代替$this，方便去除dom元素附带属性过多

          const sub_business_type = currentClass.sub_business_type;
          sessionStorage.setItem("sub_business_type", sub_business_type);

          const is_remain_tickets_control =
            currentClass.is_remain_tickets_control;
          if (is_remain_tickets_control == 0) {
            $("#ticket_nums").attr("style", "display: flex;");
          }
          var centere = currentClass.center_end_latlng;
          let start_center = $(".origin_place").attr("lng")
            ? `${$(".origin_place").attr("lng")},${$(".origin_place").attr(
                "lat"
              )}`
            : "";
          $("#checkstart").attr("center_lo", start_center);
          $("#checkend").attr("center_lo", centere);
          if (currentClass.start_time_type == 2) {
            $("#oneWayShow .date-view-line-warp").html(
              currentClass.start_date.slice(5, 10) +
                " " +
                `<span class='ssstime'>${
                  currentClass.start_earliest_time +
                  "-" +
                  currentClass.end_latest_time
                }</span>`
            );
          } else {
            $("#oneWayShow .date-view-line-warp").html(
              currentClass.start_date.slice(5, 10) +
                " " +
                currentClass.start_time
            );
          }

          console.log("currentClass.summary", currentClass.summary);
          if (currentClass.summary == "") {
            $(".summaryInfo").html("");
          } else {
            $(".summaryInfo").html(currentClass.summary);
          }
          $(".carrier").html(currentClass?.branch?.mchname || "");

          $(".toPay").attr(
            "line_class_train_id",
            currentClass.line_class_train_id
          );
          $(".toPay").attr("start_date", currentClass.start_date);
          $(".confirm-set-train").attr(
            "start_time_type",
            currentClass.start_time_type
          );
          $(".confirm-set-train").attr("start_time", currentClass.start_time);
          $(".confirm-set-train").attr(
            "start_earliest_time",
            currentClass.start_earliest_time
          );
          $(".confirm-set-train").attr(
            "end_latest_time",
            currentClass.end_latest_time
          );
          $(".confirm-set-train").attr("clickIndex", clickIndex);
          $(".timemores").html(
            currentClass.start_earliest_time +
              "-" +
              currentClass.end_latest_time
          );
          // $(".ticket_car").show();
          togglePopupExpress(true, "#j-popup-half-train");

          var train_id = currentClass.line_class_train_id;
          $(".confirm-set-train").attr("line_class_train_id", train_id);
          $("#ticket_s").html(currentClass.start_name);
          $(".confirm-set-train").attr("sname", currentClass.start_name);
          $(".confirm-set-train").attr("fname", currentClass.end_name);
          $(".confirm-set-train").attr("stime", currentClass.start_time);
          $(".confirm-set-train").attr("sdate", currentClass.start_date);
          // set_time_space("00:00", "24:00", 0, 1, '2023-09-13');

          $("#ticket_e").html(currentClass.end_name);
          $("#ticket_type").html(currentClass.car_type);
          if (currentClass.refund_time_set >= 60) {
            if (Number(currentClass.refund_time_set) % 60 != 0) {
              var newtimeless =
                Math.floor(Number(currentClass.refund_time_set) / 60) +
                "小时" +
                (Number(currentClass.refund_time_set) % 60);
              $(".return_view").html("发车前" + newtimeless + "分钟可在线退票");
              $(".return-words").html(
                "发车前" + newtimeless + "分钟可在线退票"
              );
            } else {
              var newtimeless =
                Math.floor(Number(currentClass.refund_time_set) / 60) + "小时";
              $(".return_view").html("发车前" + newtimeless + "可在线退票");
              $(".return-words").html("发车前" + newtimeless + "可在线退票");
            }
          } else if (
            Number(currentClass.refund_time_set) > 0 &&
            Number(currentClass.refund_time_set) < 60
          ) {
            $(".return_view").html(
              "发车前" + currentClass.refund_time_set + "分钟可在线退票"
            );
            $(".return-words").html(
              "发车前" + currentClass.refund_time_set + "分钟可在线退票"
            );
          } else {
            $(".return_view").html("发车前可在线退票");
            $(".return-words").html("发车前可在线退票");
          }
          const via_name = JSON.parse(sessionStorage.getItem("ticketObj"))[
            clickIndex
          ].via_name;
          $(".confirm-set-train").attr("via_name", via_name);
          $("#oneWayShow .show_via_name").html(
            via_name ? "途经：" + via_name : ""
          );
          $(".residue_num").html(currentClass.remain_tickets);
          $(".ticket_number_view").attr(
            "data-max",
            currentClass.remain_tickets
          );
          renderInsurance();
        }
      );

      //确认购票信息
      $(".confirm-set-train").on("click", async function () {
        // 调用航班号显示逻辑
        try {
          handleFlightNumberVisibility();
        } catch (error) {
          console.error('调用handleFlightNumberVisibility出错:', error);
        }
        
        $("#j-popup-half-train-order .check-start-point").html(
          `<div class="tarin-point-label">请输入您的上车位置</div>
          <label class="fee-desc-mind">请在该时间到达上车点候车</label>
            <label class="fee-desc"></label>
              <span class="selcet-label-tip-start"  id="selcet-label-tip-start"  onclick="openSelectMap('start')">选择</span>`
        );
        $("#j-popup-half-train-order .check-end-point").html(
          `<div class="tarin-point-label">请输入您的下车位置</div>
          <label class="fee-desc"></label>
             <span class="selcet-label-tip-end"  id="selcet-label-tip-end"  onclick="openSelectMap('end')">选择</span>`
        );
        $(".fee-money-cell").hide();
        $(".fee-money-cell .fee-money").html("0.00");
        $(".fee-money-cell").attr("fee-start", 0);
        $(".fee-money-cell").attr("fee-end", 0);

        $(".area-money-cell").attr("area-start", 0);
        $(".area-money-cell").attr("area-end", 0);
        $(".area-money-cell").hide();
        $(".insurance-form").html("");
        $(".checked-insuranced").hide();
        //重置一下html
        $(".area-money-cell").html(`<div class="price-detail-cell-view">
          <span>调度费：</span>
          <span class="money-detail-txt"></span>
          <span class="money-view">￥
            <i class="area-money">0.00</i></span>
        </div>`);

        const payMode = $("#pay_mode").val();
        if (payMode == 0 || $("#wallet_service_enabled").val() != 1) {
          //后支付不需要显示支付方式
          $(".paymode-container").hide();
        } else {
          $(".paymode-container").show();
        }

        //默认选中需要
        $($(".item-radio")[1]).addClass("checked");
        $($(".item-radio")[0]).removeClass("checked");
        //调试班次~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        var clickIndex = Number($(this).attr("clickIndex"));
        // var clickIndex = 0
        let ticketObj = JSON.parse(sessionStorage.getItem("ticketObj"))[
          clickIndex
        ];
        var checkBox = document.getElementById("point-used");
        //设置为选中状态
        checkBox.checked = false;

        $(".train-price-view b").html("--");
        $(".toPay").attr("is_seat_selection", ticketObj.is_seat_selection);
        let is_seat_selection = ticketObj.is_seat_selection;
        if (is_seat_selection == 1) {
          seat_lists = Object.assign([], ticketObj.seat_price).map((i) => {
            i["passenger"] = {
              real_name: "",
              ID_number: "",
            };
            return i;
          });

          let seatlayout = "";
          if (ticketObj.seat_layout) {
            seatlayout = ticketObj.seat_layout;
          } else {
            seatlayout =
              ticketObj.seat_price.length === 5
                ? "2-3"
                : ticketObj.seat_price.length === 7
                ? "2-2-3"
                : ticketObj.seat_price.length === 9
                ? "2-2-2-3"
                : "";
          }
          const length = seatlayout.split("-");
          const seats_ = new Array();
          let index = 0;
          for (let i = 0; i < length.length; i++) {
            if (i > 0) {
              index += Number(length[i - 1]);
            }
            seats_[i] = new Array();
            for (let o = 0; o < Number(length[i]); o++) {
              seats_[i][o] = ticketObj.seat_price[o + index];
            }
          }

          let setViewHtml =
            ticketObj.seat_price.length > 9
              ? newSeatView(seats_)
              : seatView(seats_);
          $(".component-seat").html(`
                      <div class="warp-title">
                          <span class="m-title">在线选座</span>
                      </div>${setViewHtml}
                      <if condition="$childbuyinfo eq 1">
                        <div style="color: #ff3141;font-size: 12px;">
                          注：儿童也需要购票!!!
                        </div>
                      </if>`);

          $(".right-price-view .inner-price-view").html("");
        } else {
          seat_lists = [
            {
              checked: true,
              passenger: {
                real_name: "",
                ID_number: "",
              },
            },
          ];
          //  2023/09/21调整
          // seat_lists = []//清空
          const ht_ = `<div class="warp-title">
                <span class="m-title">票数/人数</span>
                <div class="ticket_number_action">
                  <a class="action-btn-cell action-reduce disabled needsclick">-</a>
                  <span  class="ticket_number_view" data-max="${
                    ticketObj.remain_tickets === -1
                      ? ""
                      : ticketObj.remain_tickets
                  }" data-min="1" >1</span>
                  <a class="action-btn-cell action-increase  needsclick">+</a>
                </div>
            </div>
            <if condition="$childbuyinfo eq 1">
              <div style="color: #ff3141;font-size: 12px;">
                注：儿童也需要购票!!!
              </div>
            </if>`;
          $(".component-seat").html(ht_);
          $(".right-price-view .inner-price-view").html(
            `￥<b>${ticketObj.price}</b>`
          );

          // 创建一个观察器实例并传入回调函数
          const observer = new MutationObserver((mutationsList, observer) => {
            handleTicketNumberChange(mutationsList[0]);
          });
          // 配置观察选项
          const config = {
            attributes: true, // 不观察属性变化
            childList: true, // 不观察子节点的变化
            subtree: false,
            characterData: true,
          };

          // 选择需要观察变动的节点
          const targetNode = document.querySelector(".ticket_number_view");
          console.log("targetNode", targetNode);
          // 开始观察
          observer.observe(targetNode, config);
        }

        // 定义处理票数变化的函数
        function handleTicketNumberChange(node) {
          const currentTicketCount = parseInt(node.target.textContent, 10); // 当前票数
          const previousTicketCount = seat_lists.length; // 变化前票数

          if (previousTicketCount > currentTicketCount) {
            // 减票数
            seat_lists.pop();
          } else if (previousTicketCount < currentTicketCount) {
            // 增加票数
            const newItem = {
              checked: true,
              passenger: {
                real_name: "",
                ID_number: "",
              },
            };
            seat_lists.push(newItem);
          }

          console.log("seat_lists:", seat_lists);
          renderPssengerInfo();
          console.log("票数变化了");
          priceCalculation();
        }

        renderPssengerInfo();
        priceCalculation();
        closePopup();

        $("#j-popup-half-train-order .train-order-title").html(
          `${ticketObj.start_name} ⇀ ${ticketObj.end_name}`
        );
        setTimeout(() => {
          togglePopupExpress(false, "#j-popup-half-train-order");
          //弹出购票须知
          showRules();
        }, 300);
        $("#j-popup-half-train-order").attr("index", clickIndex);

        // 班次切换后立即调用航班号显示逻辑
        try {
          handleFlightNumberVisibility();
        } catch (error) {
          console.error('班次切换时调用handleFlightNumberVisibility出错:', error);
        }

        //固定上下车点
        var startIn = formatuseTime(
          ticketObj.start_point,
          ticketObj.start_time
        );
        var endIn = formatuseTime(ticketObj.end_point, ticketObj.start_time);
        if (ticketObj.start_address_type == 1) {
          const checkPointStart = await geocoderAddress(startIn);
          const defult_start_point = checkPointStart[0];
          console.log(
            "defult_start_pointdefult_start_pointdefult_start_point",
            defult_start_point
          );
          $(".check-start-point .tarin-point-label").attr(
            "name",
            defult_start_point.alias
          );
          $(".check-start-point .tarin-point-label").attr(
            "address",
            defult_start_point.address
          );
          $(".check-start-point .tarin-point-label").attr(
            "lng",
            defult_start_point.longitude
          );
          $(".check-start-point .tarin-point-label").attr(
            "lat",
            defult_start_point.latitude
          );
          $(".check-start-point .tarin-point-label").attr(
            "or_time",
            defult_start_point.in_time_pra
          );
          $(".check-start-point .tarin-point-label").html(
            defult_start_point.alias +
              '<div class="u-time-in">' +
              defult_start_point.in_time +
              "</div>"
          );

          if (defult_start_point.in_time) {
            $(".fee-desc-mind").show();
          } else {
            $(".fee-desc-mind").hide();
          }
          $(".selcet-label-tip-start").html("修改");
          if (checkPointStart.length === 1) {
            $(".selcet-label-tip-start").hide();
          }
          let area_end = Number($(".area-money-cell").attr("area-end")) || 0;
          if (
            (defult_start_point.additional_fee &&
              Number(defult_start_point.additional_fee) !== 0 &&
              defult_start_point.id) ||
            (defult_start_point.use_time && defult_start_point.id)
          ) {
            let feeDesc = "调度点上车";
            if (
              defult_start_point.additional_fee &&
              Number(defult_start_point.additional_fee) !== 0
            ) {
              feeDesc += `，需<span class="fee-custom-price-view">+<b class="end-custom-price-fee">${Money(
                defult_start_point.additional_fee,
                2,
                true
              )}</b></span>元/人`;
            }
            // if (defult_start_point.use_time && defult_start_point.use_time > 0) {
            //   feeDesc += `，耗时<span class="fee-custom-price-view">+<b class="end-custom-price-fee">${defult_start_point.use_time}</b></span>分钟`;
            // }
            $(".fee-content-start .fee-desc").html(feeDesc);
            $(".area-money").html(
              Money(defult_start_point.additional_fee + area_end, 2)
            );
            $(".area-money-cell").attr(
              "area-start",
              defult_start_point.additional_fee || 0
            );
            $(".toPay").attr("point_seq_start", defult_start_point.id);
            $(".area-money-cell").show();
          } else {
            if (area_end === 0) {
              $(".area-money-cell").hide();
            }
            $(".area-money-cell").attr("area-start", 0);
            $(".toPay").attr("point_seq_start", "");
          }
        }
        if (ticketObj.end_address_type == 1) {
          const checkPointEnd = await geocoderAddress(endIn);
          const defult_end_point = checkPointEnd[0];
          $(".check-end-point .tarin-point-label").attr(
            "name",
            defult_end_point.alias
          );
          $(".check-end-point .tarin-point-label").attr(
            "address",
            defult_end_point.address
          );
          $(".check-end-point .tarin-point-label").attr(
            "lng",
            defult_end_point.longitude
          );
          $(".check-end-point .tarin-point-label").attr(
            "lat",
            defult_end_point.latitude
          );
          $(".check-end-point .tarin-point-label").html(
            defult_end_point.alias +
              '<div class="u-time-in">' +
              defult_end_point.in_time +
              "</div>"
          );
          $(".selcet-label-tip-end").html("修改");
          if (checkPointEnd.length === 1) {
            $(".selcet-label-tip-end").hide();
          }

          let area_start =
            Number($(".area-money-cell").attr("area-start")) || 0;
          if (
            (defult_end_point.additional_fee &&
              Number(defult_end_point.additional_fee) !== 0 &&
              defult_end_point.id) ||
            (defult_end_point.use_time && defult_end_point.id)
          ) {
            let feeDesc = "调度点下车";
            if (
              defult_end_point.additional_fee &&
              Number(defult_end_point.additional_fee) !== 0
            ) {
              feeDesc += `，需<span class="fee-custom-price-view">+<b class="end-custom-price-fee">${Money(
                defult_end_point.additional_fee,
                2,
                true
              )}</b></span>元/人`;
            }
            // if (defult_end_point.use_time && defult_end_point.use_time > 0) {
            //   feeDesc += `，耗时<span class="fee-custom-price-view">+<b class="end-custom-price-fee">${defult_end_point.use_time}</b></span>分钟`;
            // }
            $(".fee-content-end .fee-desc").html(feeDesc);
            $(".area-money").html(
              Money(defult_end_point.additional_fee + area_start, 2)
            );
            $(".area-money-cell").attr(
              "area-end",
              defult_end_point.additional_fee || 0
            );
            $(".toPay").attr("point_seq_end", defult_end_point.id);
            $(".area-money-cell").show();
          } else {
            if (area_start === 0) {
              $(".area-money-cell").hide();
            }
            $(".area-money-cell").attr("area-end", 0);
            $(".toPay").attr("point_seq_end", "");
          }
        }
        priceCalculation();
        $(".start-over-view-date").html(
          moment(ticketObj.start_date).locale("zh-cn").format("MM月DD日")
        );
        //滚动发车时间
        if (ticketObj.start_time_type == 1) {
          //固定
          $(".check-width-time").html(
            `<label class="check-start-time">${ticketObj.start_time}</label>`
          );
          $(".check-start-way").html(`发车`);
          $(".time-select-warp").html("");
        } else {
          //构造时间选择函数
          $(".time-select-warp").html(`
          <div class="time-check-content">
            <img class="i-time-revert"  src="/Theme/images/passenger/time-circle-fill.svg" />
            <div id="picker5">选择出发时间</div>
            <img class="right-light"  src="/Theme/images/passenger/right-light.png" />
          </div>`);
          set_time_space(
            ticketObj.start_earliest_time,
            ticketObj.end_latest_time,
            0,
            1,
            ticketObj.start_date,
            ticketObj.line_class.interval_minutes,
            ticketObj.line_class.is_supplementary_time
          );
          $(".check-width-time").html(
            `<label class="check-on-time">${
              ticketObj.start_earliest_time + "-" + ticketObj.end_latest_time
            }</label>`
          );
          $(".check-start-way").html(` 滚动发车`);
        }
      });

      //购票支付
      $(".toPay").on("click", function () {
        const dom_ = $(this).html();
        if (dom_ !== "提交订单") {
          return;
        }

        if (
          $("#cellphone-data").val() == "" ||
          $("#cellphone-data").val().length == 0
        ) {
          $(".register_page_one").show("normal");
          $(".register_page_one").css("left", 0);
          return;
        }
        const data_index = Number($("#j-popup-half-train-order").attr("index"));
        let ticketObj = JSON.parse(sessionStorage.getItem("ticketObj"))[
          data_index
        ]; //当前打开班次
        if (
          !$(
            ".pop-train-contariner .check-start-point .tarin-point-label"
          ).attr("lng")
        ) {
          // lui.toast({
          //   position: 'middle',
          //   text: '请选择上车点'
          // });
          document.getElementById("selcet-label-tip-start").click();
          return;
        }
        if (
          !$(".pop-train-contariner .check-end-point .tarin-point-label").attr(
            "lng"
          )
        ) {
          // lui.toast({
          //   position: 'middle',
          //   text: '请选择下车点'
          // });
          document.getElementById("selcet-label-tip-end").click();
          return;
        }

        if (
          ticketObj.is_seat_selection == 1 &&
          (!$(this).attr("seat_ids") || $(this).attr("seat_ids") == "")
        ) {
          lui.toast({
            position: "middle",
            text: "请选择座位",
          });
          return;
        }
        const checked = $(".train-order-agree-check").prop("checked");
        if (!checked) {
          let scroll_to_bottom = document.getElementById("scroll-to-bottom");
          scroll_to_bottom.scrollTop = scroll_to_bottom.scrollHeight;
          lui.toast({
            position: "middle",
            text: "下单前需要阅读并同意《定制班线车协议》",
          });
          return;
        }

        var that = this;
        //保险公司信息
        const insurance = sessionStorage.getItem("insurance");
        //获取保险输入信息
        const isNeed = $(".item-radio.checked").attr("need");
        //座位选择信息
        const members =
          seat_lists
            .map((o) => {
              return { ...o.passenger };
            })
            .filter((i) => {
              // 如果是儿童票，则只需要检查姓名字段
              const isChildTicket = i.passenger_type === '2' || i.passenger_type === 'child';
              return i.real_name && (isChildTicket || i.ID_number);
            }) || [];

        //后台设置乘车是否需要实名制
        const id_upload_config = $("#id_upload_config").val();
        let seat_occupied = [];
        if (id_upload_config == 1) {
          //后台开启实名乘车，不管是否开启保险功能，是否支持选座，均需要选乘车人
          var seat_ids = $(that).attr("seat_ids");
          //构造选座跟不选座实名数据结构
          console.log("seat_lists", seat_lists);
          seat_occupied = Object.assign([], seat_lists)
            .filter((o) => o.checked)
            .map((item) => {
              return {
                passenger_name: item?.passenger?.real_name,
                passenger_id_no: item?.passenger?.ID_number,
                passenger_id: item?.passenger?.passenger_member_id,
                passenger_type: item?.passenger?.passenger_type || "adult",
                seat_id: item.seat_id || "",
              };
            });
          const seats =
            ticketObj.is_seat_selection == 1
              ? seat_ids.split(",").length
              : $(".ticket_number_view").html();
          if (Number(seats) !== members.length) {
            lui.toast({
              position: "middle",
              text: "请输入完整乘车人信息",
            });
            return;
          }
        } else {
          seat_occupied = Object.assign([], seat_lists)
            .filter((o) => (ticketObj.is_seat_selection == 1 ? o.checked : o))
            .map((item) => {
              //不实名制无需乘客信息，只需要座位id
              return {
                passenger_name: "",
                passenger_id_no: "",
                passenger_id: "",
                passenger_type: item?.passenger?.passenger_type || "adult",
                seat_id: item.seat_id,
              };
            });
        }

        console.log("seat_occupied", seat_occupied);

        const isExta = $("#point-used").prop("checked");
        const point_redeemable_amount = isExta
          ? Number($(".point-left label").html() || 0)
          : 0; //是否勾选使用积分兑换

        $(that).attr("order_type", 5);
        var trains_id = $(that).attr("line_class_train_id");
        var slng = $(".check-start-point .tarin-point-label").attr("lng");
        var slat = $(".check-start-point .tarin-point-label").attr("lat");
        var splace = $(".check-start-point .tarin-point-label").attr("name");
        var elng = $(".check-end-point .tarin-point-label").attr("lng");
        var elat = $(".check-end-point .tarin-point-label").attr("lat");
        var eplace = $(".check-end-point .tarin-point-label").attr("name");
        var reseverd_phone = $("#user_phone").val();
        let start_time =
          $(".check-start-point .tarin-point-label").attr("or_time") || "";
        if (ticketObj.start_time_type == 2) {
          start_time = $("#picker5").attr("current_date");
          if (!start_time) {
            lui.toast({
              position: "middle",
              text: "请选择出发时间",
            });

            return;
          }
        }

        var leaveword = $(".train-info-input-area").val();
        const is_start_ferry_check =
          $(".toPay").attr("is_start_ferry_check") || 0;
        const is_end_ferry_check = $(".toPay").attr("is_end_ferry_check") || 0;
        let url_, data_;

        if (
          (is_start_ferry_check && is_start_ferry_check == 1) ||
          (is_end_ferry_check && is_end_ferry_check == 1)
        ) {
          //接乘客起始位置信息
          var ferry_start_longitude = slng;
          var ferry_start_latitude = slat;
          var ferry_start_address_remark = splace;

          //送乘客终点位置信息
          var ferry_end_longitude = elng;
          var ferry_end_latitude = elat;
          var ferry_end_address_remark = eplace;

          //车场位置信息
          var fe_poin_start_longitude = $(".toPay").attr("fee_slng");
          var fe_poin_start_latitude = $(".toPay").attr("fee_slat");
          var fe_poin_start_address = $(".toPay").attr("fee_splace");
          var fe_poin_fee_sdistance = $(".toPay").attr("fee_sdistance");
          var fe_poin_fee_sduration = $(".toPay").attr("fee_sduration");
          var fe_poin_end_longitude = $(".toPay").attr("fee_elng");
          var fe_poin_end_latitude = $(".toPay").attr("fee_elat");
          var fe_poin_end_address = $(".toPay").attr("fee_eplace");
          var fe_poin_fee_edistance = $(".toPay").attr("fee_edistance");
          var fe_poin_fee_edistance = $(".toPay").attr("fee_edistance");
          var fe_poin_fee_eduration = $(".toPay").attr("fee_eduration");

          url_ = "/Passenger/Order/doMobileBookLineClassTrainFerry?callback=";
          data_ = {
            seat_ids: $(this).attr("seat_ids"),
            reseverd_phone: reseverd_phone,
            seat_occupied: seat_occupied,
            driver_id: $("#driver_id").val() || "",
            line_class_train_id: trains_id,
            number: seat_occupied.length,
            point_redeemable_amount: point_redeemable_amount,

            ferry_start_longitude: ferry_start_longitude || "",
            ferry_start_latitude: ferry_start_latitude || "",
            ferry_end_longitude: ferry_end_longitude || "",
            ferry_end_latitude: ferry_end_latitude || "",
            ferry_start_address_remark: ferry_start_address_remark || "",
            ferry_end_address_remark: ferry_end_address_remark || "",

            start_ferry_duration:
              is_start_ferry_check == 1 ? fe_poin_fee_sduration : "",
            end_ferry_duration:
              is_end_ferry_check == 1 ? fe_poin_fee_eduration : "",
            start_longitude:
              is_start_ferry_check == 1 ? fe_poin_start_longitude || "" : slng,
            start_latitude:
              is_start_ferry_check == 1 ? fe_poin_start_latitude || "" : slat,
            start_address_remark:
              is_start_ferry_check == 1 ? fe_poin_start_address || "" : splace,
            end_longitude:
              is_end_ferry_check == 1 ? fe_poin_end_longitude || "" : elng,
            end_latitude:
              is_end_ferry_check == 1 ? fe_poin_end_latitude || "" : elat,
            end_address_remark:
              is_end_ferry_check == 1 ? fe_poin_end_address || "" : eplace,
            start_ferry_distance:
              is_start_ferry_check == 1 ? fe_poin_fee_sdistance || "" : 0,
            end_ferry_distance:
              is_end_ferry_check == 1 ? fe_poin_fee_edistance || "" : 0,

            is_start_ferry_check: is_start_ferry_check,
            is_end_ferry_check: is_end_ferry_check,
            start_time: start_time || "",
            coupon_record_id:
              $(".used-coupon-params").attr("coupon_record_id") || "",
            reseverd_info: leaveword,
            members: JSON.stringify(members),
            insurance: insurance,
            is_buy_insurance: isNeed,
          };
        } else {
          const channel_branch_id = $("#channel_branch_id").val() || "";

          url_ = "/Passenger/Order/doMobileBookLineClassTrain?callback=";
          data_ = {
            //新增加价区域跟固定加价上车点
            pickup_polygon_seq: $(this).attr("area_seq_start") || "",
            dropoff_polygon_seq: $(this).attr("area_seq_end") || "",
            fixed_boarding_point_seq: $(this).attr("point_seq_start") || "",
            fixed_dropoff_point_seq: $(this).attr("point_seq_end") || "",

            seat_ids: $(this).attr("seat_ids"),
            point_redeemable_amount: point_redeemable_amount,
            seat_occupied: seat_occupied,
            reseverd_phone: reseverd_phone,
            line_class_train_id: trains_id,
            number: seat_occupied.length,
            start_longitude: slng || "",
            start_latitude: slat || "",
            end_longitude: elng || "",
            end_latitude: elat || "",
            start_address_remark: splace || "",
            end_address_remark: eplace || "",
            start_time: start_time || "",
            driver_id: $("#driver_id").val() || "",
            coupon_record_id:
              $(".used-coupon-params").attr("coupon_record_id") || "",
            reseverd_info: leaveword,
            insurance: insurance,
            members: JSON.stringify(members),
            is_buy_insurance: isNeed,
            channel_branch_id: channel_branch_id,
          };
        }

        // 获取航班号字段
        const flight_number = $("#flight_number").val() || "";
        data_.ft_number = flight_number;
        
        // 验证航班号是否必填
        const ticketObjStr = sessionStorage.getItem("ticketObj");
        
        // 尝试获取当前选择的班次索引
        let orderDataIndex;
        const popupElement = document.getElementById('j-popup-half-train-order');
        if (popupElement) {
          orderDataIndex = Number($(popupElement).attr("index") || 0);
        } else {
          orderDataIndex = 0;
        }
        
        if (ticketObjStr) {
          try {
            const ticketObjArray = JSON.parse(ticketObjStr);
            if (Array.isArray(ticketObjArray) && orderDataIndex < ticketObjArray.length) {
              const ticketObj = ticketObjArray[orderDataIndex];
              if (ticketObj && ticketObj.line_class && ticketObj.line_class.is_required_flight_number == 1) {
                // 如果航班号是必填的，检查是否已填写
                if (!flight_number.trim()) {
                  lui.toast({
                    type: "warning",
                    text: "请填写航班号",
                  });
                  return false;
                }
              }
            }
          } catch (error) {
            console.error("解析ticketObj时出错:", error);
          }
        }

        const subOrder = (params, requestUrl) => {
          lui.loading();
          $.ajax({
            url: `${requestUrl}${sessionStorage.getItem("callback")}`,
            type: "post",
            data: params,
            dataType: "json",
            success: function (cn) {
              lui.loading("close");
              window.clearInterval(timer);
              if (cn.ret) {
                seat_lists = [];
                var order_id = cn.data.order_id;
                const pay_mode = $("#pay_mode").val();
                if (pay_mode == 0 || Number(cn.data.price) == 0) {
                  lui.toast({
                    type: "ok",
                    text: "下单成功",
                  });
                  setTimeout(() => {
                    window.location.href =
                      "/passenger_order_detail?order_id=" +
                      order_id +
                      "&callback=" +
                      sessionStorage.getItem("callback") +
                      "&show=1";
                  }, 2000);
                } else {
                  //判断是否选择微信支付

                  // wallet_service_enabled
                  var balanceRadio = document.getElementById("balance-radio");
                  if (balanceRadio.checked) {
                    console.log("用户选择了余额支付");
                    // Passenger/Account/doPassengerWalletPay
                    lui.showModal({
                      title: "确认支付",
                      content: "确认使用余额支付" + cn.data.price + "元吗?",
                      cancelText: "取消",
                      confirmText: "确认",
                      showCancel: true,
                      cancel: function () {
                        window.location.href =
                          "/passenger_order_detail?order_id=" +
                          order_id +
                          "&callback=" +
                          sessionStorage.getItem("callback") +
                          "&show=1";
                      },
                      confirm: function () {
                        lui.loading();
                        $.ajax({
                          url:
                            "/Passenger/Account/doPassengerWalletPay?callback=" +
                            sessionStorage.getItem("callback"),
                          type: "post",
                          dataType: "json",
                          data: {
                            order_no: cn?.data?.order_no,
                            amount: cn?.data?.price,
                          },
                          success: function (n) {
                            lui.loading("close");
                            if (n.ret) {
                              lui.toast({
                                type: "success",
                                text: "支付成功",
                              });
                            } else {
                              lui.toast({
                                position: "middle",
                                text: n.data,
                              });
                            }

                            setTimeout(() => {
                              window.location.href =
                                "/passenger_order_detail?order_id=" +
                                cn?.data?.order_id +
                                "&callback=" +
                                sessionStorage.getItem("callback") +
                                "&show=1";
                            }, 1500);
                          },
                          error: function () {
                            lui.loading("close");
                          },
                        });
                      },
                    });
                  } else {
                    indexPage.getPay(
                      cn?.data?.order_no,
                      cn?.data?.price,
                      cn?.data?.order_id
                    );
                  }
                }
              } else {
                lui.toast({
                  type: "warning",
                  text: cn?.data || "操作失败",
                });
              }
            },
            error: function () {
              lui.loading("close");
            },
          });
        };
        const isEnble = $(".insurance-container-warp").attr("insurance"); //是否开启保险功能
        if (isEnble == 1 && isNeed == 0) {
          lui.showModal({
            title: "购买保险",
            content: "买份保险，让出行更安心",
            cancelText: "不需要",
            confirmText: "需要",
            showCancel: true,
            cancel: function () {
              console.log("cancel");
              subOrder(data_, url_);
            },
            confirm: function () {
              console.log("confirm");
              $($(".item-radio")[1]).trigger("click"); //切换到需要
              data_.is_buy_insurance = 1;
              subOrder(data_, url_);
            },
          });
        } else {
          subOrder(data_, url_);
        }
      });
      $(".ticket_sure").on("click", function () {
        window.location.reload();
      });
      //不在微信小程序的web-view中
      if (!isInMiniProgramWebView()) {
        //定制客运业态腾讯广告
        $.ajax({
          url: `/Passenger/Ad/getCustomizedPassengerAd/callback/${sessionStorage.getItem(
            "callback"
          )}`,
          type: "get",
          success: function (json) {
            const res_ = JSON.parse(json);
            if (res_.ret) {
              if (
                res_.data &&
                res_.data.length > 0 &&
                res_.data[0] &&
                res_.data[0].ad_code
              ) {
                const ad_code = JSON.parse(res_.data[0].ad_code);
                //申明全局命名空间
                window.TencentGDT = window.TencentGDT || [];
                //广告位申明
                TencentGDT.push({
                  app_id: ad_code.app_id,
                  placement_id: ad_code.placement_id,
                  type: ad_code.type,
                  display_type: ad_code.display_type,
                  carousel: ad_code.carousel,
                  containerid: "tencent_ad_container",
                  onComplete: function (res) {
                    if (res.ret == 0) {
                      $("#tencent_ad_container")
                        .parent()
                        .css("display", "block");
                      console.log("广告播放成功");
                    } else {
                      console.log("广告播放失败");
                    }
                  },
                });
                //加载H5 SDK
                (function () {
                  var doc = document,
                    h = doc.getElementsByTagName("head")[0],
                    s = doc.createElement("script");
                  s.async = true;
                  s.src = "//qzs.qq.com/qzone/biz/res/i.js";
                  h && h.insertBefore(s, h.firstChild);
                })();
              }
            }
          },
          error: function (json) {
            console.debug(json);
          },
          timeout: 300000,
        });
      }
    } else if (roootId === "goods") {
      indexPage.palceCheckControl(1);
      if ($("#tpl_goods").hasClass("loaded")) {
        return;
      }
      $("#tpl_goods").addClass("loaded");
      $.ajax({
        url:
          "/Passenger/Line/getTakeGoodsAppointmentTime&callback=" +
          sessionStorage.getItem("callback"),
        type: "get",
        success: function (json) {
          var stl = "";
          var json = JSON.parse(json);
          if (json.ret) {
            var dotype = json.data.business_time_type;
            var set_order_time = json.data.set_order_time;
            if (dotype == 1) {
              var oDatemin = json.data.start_appointment_time;
              var oDatemax = json.data.end_appointment_time;
              set_time_space(oDatemin, oDatemax, set_order_time);
            } else {
              set_time_space("00:00", "24:00", set_order_time);
            }
          } else {
          }
        },
        error: function (json) {},
        timeout: 300000,
      });

      const goodsAd = await fetchswiper(6);
      if (goodsAd.length) {
        $(".goods-ad-container").html(renderSwiper(goodsAd, "", 6));
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
    } else if (roootId === "charge") {
      if ($("#tpl_charge").hasClass("loaded")) {
        return;
      }
      lui.loading();
      $("#tpl_charge").addClass("loaded");
      $(document).on("click", ".item-service", function () {
        $("#textarea_z").val("");
        const key_ = $(this).attr("index");
        const current_ = service_list[key_];
        $(".confirm-service-name").html(current_.name);
        $(".confirm-service-desc").html(current_.summary);
        $(".service-confirm-warp .service-tag").html(
          `${
            current_.business_time_type == 0
              ? "全天二十四小时"
              : `${current_.start_appointment_time}至${current_.end_appointment_time}`
          }`
        );
        $(".service-price-view b").html(current_.price);
        $(".confirm-service-order").attr("agency_id", current_.agency_id);
        let dotype = current_.business_time_type;
        var set_order_time = current_.set_order_time;
        if (dotype == 1) {
          var oDatemin = current_.start_appointment_time;
          var oDatemax = current_.end_appointment_time;
          set_time_space(oDatemin, oDatemax, set_order_time);
        } else {
          set_time_space("00:00", "24:00", set_order_time);
        }
        togglePopupExpress(true, "#j-popup-half-agent");
      });

      const serviceAd = await fetchswiper(7);
      if (serviceAd.length) {
        $(".serviceAd-ad-container").html(renderSwiper(serviceAd, "", 7));
        var swiper = new Swiper(".swiper-container", {
          spaceBetween: 10,
          loop: true,
          autoplay: 3000,
        });
      }
      //获取代办事件
      $.ajax({
        url:
          "/Passenger/Order/getMobileAgencyList?callback=" +
          sessionStorage.getItem("callback"),
        type: "GET",
        data: {
          page: 1,
          size: 10,
        },
        dataType: "json",
        success: function (z) {
          lui.loading("close");
          z = eval(z);
          if (z.ret) {
            var con = "";
            service_list = z.data;
            for (var i = 0; i < z.data.length; i++) {
              con += `<div class="item-service" index="${i}">
                  <div class="item-service-name">${z.data[i].name}</div>
                  <div class="item-service-desc">${z.data[i].summary}</div>
                  <div class="bottom-warp">
                     <span class="service-tag">${
                       z.data[i].business_time_type == 0
                         ? "全天二十四小时"
                         : `${z.data[i].start_appointment_time}至${z.data[i].end_appointment_time}`
                     }</span>
                     <span class="service-price">${
                       z.data[i].price
                     }<b>元</b></span>
                  </div>
               </div>`;
            }
            $(".service-warp").html(con);
          } else {
            $(".service-warp").html(`<div class="empty-service-view">
              <img src="{$FILE_ROOT}Theme/images/passenger/empty-c.png" alt="" srcset="" />
              <p>暂无可用服务</p>
            </div>`);
          }
        },
        error: function () {
          lui.loading("close");
          console.log("出错了");
        },
      });
      //代办下单
      $(".confirm-service-order").on("click", function () {
        if (
          $("#cellphone-data").val() == "" ||
          $("#cellphone-data").val().length == 0
        ) {
          $(".register_page_one").show("normal");
          $(".register_page_one").css("left", 0);
        } else {
          const checked = $(".service-agree-check").prop("checked");
          var str_time = $("#picker5").attr("current_date");
          var agency_id = $(this).attr("agency_id");
          var reseverd_phone = $("#p_phone").val();
          var reseverd_info = $("#textarea_z").val();
          if (!str_time) {
            lui.toast({
              position: "middle",
              text: "请选择服务时间",
            });
            return;
          }
          if (!reseverd_phone) {
            lui.toast({
              position: "middle",
              text: "请输入联系电话",
            });
            return;
          }

          if (!checked) {
            lui.toast({
              position: "middle",
              text: "下单前需要阅读并同意《代办服务协议》",
            });
            return;
          }

          lui.loading();
          $.ajax({
            url:
              "/Passenger/Order/doMobileBookAgency?callback=" +
              sessionStorage.getItem("callback"),
            data: {
              start_time: str_time,
              agency_id: agency_id,
              reseverd_phone: reseverd_phone,
              reseverd_info: reseverd_info,
              coupon_record_id: $("#tpl_charge").attr("coupon_record_id"),
            },
            dataType: "json",
            type: "post",
            success: function (data) {
              if (data.ret) {
                var order_id = data.data.order_id;
                const pay_mode = $("#pay_mode").val();
                if (pay_mode == 0) {
                  lui.loading("close");
                  lui.toast({
                    type: "ok",
                    text: "预约成功",
                  });
                  setTimeout(() => {
                    window.location.href =
                      "/passenger_order_detail?order_id=" +
                      order_id +
                      "&callback=" +
                      sessionStorage.getItem("callback") +
                      "&show=1";
                  }, 2000);
                } else {
                  indexPage.getPay(
                    data?.data?.order_no,
                    data?.data?.price,
                    data?.data?.order_id
                  );
                }
              } else {
                lui.loading("close");
                lui.toast({
                  type: "warning",
                  text: data?.data || "操作失败",
                });
              }
            },
          });
        }
      });
    }
  },
  //加减票数
  upDownOperation: function (element) {
    var _input = element.parent().find("span"),
      _value = _input.html(),
      _step = _input.attr("data-step") || 1;
    if (element.hasClass("disabled")) {
      return;
    }
    element.hasClass("disabled") && element.removeClass("disabled");
    if (element.hasClass("action-increase")) {
      var _new_value = parseInt(parseFloat(_value) + parseFloat(_step)),
        _max = _input.attr("data-max") || false,
        _down = element.parent().find(".action-reduce");
      _down.hasClass("disabled") && _down.removeClass("disabled");
      if (_max && _new_value >= _max) {
        _new_value = _max;
        element.addClass("disabled");
      }
    } else {
      var _new_value = parseInt(parseFloat(_value) - parseFloat(_step)),
        _min = _input.attr("data-min") || false,
        _up = element.parent().find(".action-increase");
      _up.hasClass("disabled") && _up.removeClass("disabled");
      if (_min && _new_value <= _min) {
        _new_value = _min;
        element.addClass("disabled");
      }
    }
    _input.html(_new_value);
  },
  //选点控制
  palceCheckControl: function (biz = "", start_filter = 1) {
    list = [];
    $.ajax({
      url:
        "/Passenger/AddressCode/getAddressCodes?callback=" +
        sessionStorage.getItem("callback"),
      type: "post",
      dataType: "json",
      data: {
        biz: biz,
        start_filter: start_filter,
      },
      success: function (data) {
        if (data.ret) {
          var str = "";

          for (var i = 0; i < data.data.length; i++) {
            str += '<li class="list-group-item">' + data.data[i].name + "</li>";
          }
          list = data.data.map((o) => o.name);
          // if (start_filter === 0) {
          //   end_city_list = data.data
          // } else {
          //   start_city_list = data.data
          //   end_city_list = data.data
          // }

          $(".list_city_wrapper-end").html(str);
          $(".list_city_wrapper").html(str);
        }
      },
    });

    if ($(".main_content").hasClass("loadedcontrol")) {
      return false;
    }
    $(".main_content").addClass("loadedcontrol");

    $("#city_search").bind("input porpertychange", function () {
      console.log("listlist", list);
      // console.log()
      var z = $(this).val();
      //正则表达式
      var len = list.length;
      var arr = [];
      //
      for (var i = 0; i < len; i++) {
        //如果字符串中不包含目标字符会返回
        //
        console.log("list[i]", list[i]);
        console.log(list[i].indexOf(z));
        console.log("zzzz", z);
        if (list[i].indexOf(z) > -1) {
          $(".list_city_wrapper").empty();
          arr.push(list[i]);
        }
      }
      var strnew = "";
      for (var t = 0; t < arr.length; t++) {
        strnew += '<li class="list-group-item">' + arr[t] + "</li>";
      }
      if (strnew) {
        $(".list_city_wrapper").html(strnew);
      } else {
        $(".list_city_wrapper").html(`<div class="city-empty-warp">
          <img src="/Theme/images/passenger/tip.svg"   />
          <span>暂无可匹配数据</span>
         </div>`);
      }
    });
    $("#city_search_end").bind("input porpertychange", function () {
      var q = $(this).val();

      var len1 = list.length;
      var arr1 = [];
      for (var i = 0; i < len1; i++) {
        //如果字符串中不包含目标字符会返回
        //
        if (list[i].indexOf(q) >= 0) {
          $(".list_city_wrapper-end").empty();
          arr1.push(list[i]);
          var strnew1 = "";
          for (var t = 0; t < arr1.length; t++) {
            strnew1 += '<li class="list-group-item">' + arr1[t] + "</li>";
          }
          $(".list_city_wrapper-end").append(strnew1);
        } else {
          $(".list_city_wrapper-end").html(`<div class="city-empty-warp">
          <img src="/Theme/images/passenger/tip.svg"   />
          <span>暂无可匹配数据</span>
         </div>`);
        }
      }
    });

    $(document).on("click", ".current-view-city-warp", function () {
      $(".city-wrapper").hide();
      $(".city-input-wrapper").show();
      $(".list-wrapper").show();
      $(".list-wrapper-write").hide();
      $("#searchInput").html("");
      $(".origin_select").show();
    });
    //起点点设置
    $(document).on("click", ".origin_clicked", function () {
      const bussess_ = sessionStorage.getItem("flag_");
      if (bussess_ === "fast_car") {
        indexPage.palceCheckControl(7);
      } else if (bussess_ === "taxi") {
        indexPage.palceCheckControl(11);
      }

      $(".city-wrapper").show();
      $(".city-input-wrapper").hide();
      $(".list-wrapper").hide();
      $(".list-wrapper-write").show();
      $("#searchInput").html("");
      $(".origin_select").show();
    });
    var content;
    //起点城市
    $(".city-wrapper").on("click", function () {
      content = $(".list_city_wrapper").html();
      $(".list-wrapper").show();
      $(this).hide();
      $(".city-input-wrapper").show();
      $(".list-wrapper-write").hide();
    });
    $(".list_city_wrapper").on("click", ".list-group-item", async function () {
      $(".pulldown").css("transform", "");
      $(this).parent().parent().hide();
      $("#city_search").val("");
      $(".city-wrapper").show();
      $(".list_city_wrapper").html(content);
      $(".city-wrapper .text").html($(this).html());
      $("#searchInput").val("");
      $("#searchResult").html("");
      $(".city-input-wrapper").hide();
      $(this).hide();
      $(this).siblings().show();
      $(".list-wrapper").hide();
      //切换城市默认搜索城市关键字
      const res_ = await defaultWordsSearch($(this).html(), $(this).html());
      if (res_) {
        $("#searchResult").html(res_);
        $(".list-wrapper-write").show();
      }
    });
    $("#city_search").bind("input porpertychange", function () {
      if ($(this).val() == "") {
        $(".list_city_wrapper").html(content);
      }
    });
    $(document).on("click", ".address_item", function () {
      const name = $(this).attr("name");
      const lng = $(this).attr("lng");
      const lat = $(this).attr("lat");
      const adcode = $(this).attr("adcode");
      $("body").unbind("touchmove");
      const bussess_ = sessionStorage.getItem("flag_");
      if (bussess_ == "fast_car" || bussess_ == "taxi") {
        //待完善，修改起点重新计算快车，出租车????
        const end_lng = $(".finish_place").attr("lng");
        const end_lat = $(".finish_place").attr("lat");
        const end_code = $(".finish_place").attr("adcode");

        if (end_lng) {
          const lineObj = {
            start_longitude: lng,
            start_latitude: lat,
            book_seating: 1,
            end_longitude: end_lng,
            end_latitude: end_lat,
            start_address_code: adcode,
            end_address_code: end_code,
          };
          $(".cover-load").show();
          openFastPop();
          bussess_ == "fast_car"
            ? getFastPrice(lineObj)
            : getTaxiPrice(lineObj);
          $(".confirm-order-on-time").addClass(
            bussess_ == "taxi" ? "taxiOrderAction" : "fastOrderAction"
          );
          $(".confirm-order-on-time").removeClass(
            bussess_ == "taxi" ? "fastOrderAction" : "taxiOrderAction"
          );
          if (driving) {
            driving.clear();
                  }
        //显示模拟地图路线
        setOverLines();
      }
      if (map) {
        map.setCenter(new AMap.LngLat(lng, lat));
      }
      $(".point-info-start .alias-name").html(name);
      }
      $(".origin_place").attr({ lng: lng, lat: lat, adcode: adcode });
      if (bussess_ == "chart_car") {
        $(".start-chartered-place").html(
          `<div class="chartered-checked">${name}<i class="checked-right"></i></div>`
        );
        $(".start-chartered-place").attr({
          start_address_code: adcode,
          start_name: name,
          start_longitude: lng,
          start_latitude: lat,
        });
        $(".check-place-charter .check-start-point .tarin-point-label").html(
          name
        );
      }
      if (bussess_ == "goods") {
        $(".input-express-address").val(name);
        $(".confirm-set-express").attr("adcode", adcode);
        $(".confirm-set-express").attr("lng", lng);
        $(".confirm-set-express").attr("lat", lat);
        $(".confirm-set-express").attr("viewaddress", name);
      }

      $(".origin_select").hide();
    });
    //起点搜索输入框获取焦点
    $("#searchInput").bind("input porpertychange", async function (e) {
      var keyword = $("#searchInput").val();
      var city_f = $(".city-wrapper .text").html();
      const res_ = await defaultWordsSearch(city_f, keyword ? keyword : city_f);
      if (res_) {
        $("#searchResult").html(res_);
      } else {
        $("#searchResult").html(`<div class="city-empty-warp">
        <img src="/Theme/images/passenger/tip.svg"   />
        <span>暂无匹配结果，请重新输入</span>
       </div>`);
      }
      $(".list-wrapper-write").show();
      $(".list-wrapper").hide();
    });
    $("#searchInput").focus(function () {
      $(".city-input-wrapper").hide();
      $(".city-wrapper").show();
      $(".list-wrapper").hide();
      $(".list-wrapper-write").show();
    });
    $(".list-title").on("click", async function () {
      const current_ = $(this).find("i").html();
      if (current_ === "定位失败") return;
      $(".city-wrapper .text").html(current_);
      $(".city-wrapper").show();
      $(".city-input-wrapper").hide();
      $(".list-wrapper").hide();
      const res_ = await defaultWordsSearch(current_, current_);
      if (res_) {
        $("#searchResult").html(res_);
        $(".list-wrapper-write").show();
      }
    });
    $(".cancel-wrapper").on("click", function () {
      $(".origin_select").hide();
      $("body").unbind("touchmove");
    });
    var content_end;
    //打开终点地址
    $(document).on("click", ".finish_place_btn", async function () {
      const start_info = $(".origin_place").attr("lng");
      if (!!!start_info) {
        lui.toast({
          position: "middle",
          text: "请先选择起点位置",
        });
        return;
      }
      const bussess_ = sessionStorage.getItem("flag_");
      if (bussess_ === "fast_car") {
        indexPage.palceCheckControl(7, 0);
      } else if (bussess_ === "taxi") {
        indexPage.palceCheckControl(11, 0);
      }

      const default_end_city = $(".city-wrapper-end").find("span").html();
      if (default_end_city && default_end_city != "定位中..") {
        const res_ = await defaultWordsSearch(
          default_end_city,
          default_end_city,
          "end"
        );
        if (res_) {
          $("#searchResult_end").html(res_);
          $(".list-wrapper-write").show();
        }
      }

      // city-wrapper-end
      content_end = $(".list_city_wrapper-end").html();
      $(".list-wrapper-end").hide();
      $(".city-wrapper-end").show();
      $(".city-input-wrapper_end").hide();
      $(".list-wrapper-write").show();
      $("#searchInput_end").html("");
      $(".finish_select").show();
    });
    $(".city-wrapper-end").on("click", function () {
      content_end = $(".list_city_wrapper-end").html();
      $(".list-wrapper-end").show();
      $(this).hide();
      $(".city-input-wrapper_end").show();
      $(".list-wrapper-write").hide();
    });
    $(".list-title-end").on("click", async function () {
      const current_ = $(this).find("i").html();
      if (current_ === "定位失败") return;
      $(".city-wrapper-end .text").html(current_);
      $(".city-wrapper-end").show();
      $(".city-input-wrapper_end").hide();
      $(".list-wrapper-end").hide();
      const res_ = await defaultWordsSearch(current_, current_, "end");
      if (res_) {
        $("#searchResult_end").html(res_);
        $(".list-wrapper-write").show();
      }
    });
    $(document).on("click", ".list_city_wrapper-end li", async function () {
      $(".pulldown").css("transform", "");
      $(".list-wrapper-end").hide();
      $("#city_search_end").val("");
      $(".city-wrapper-end").show();
      $(".city-input-wrapper_end").hide();
      $(".list_city_wrapper-end").html(content_end);
      $(".city-wrapper-end .text").html($(this).html());
      $("#searchInput_end").val("");
      $("#searchResult_end").html("");
      $(this).hide();
      $(this).siblings().show();
      const res_ = await defaultWordsSearch(
        $(this).html(),
        $(this).html(),
        "end"
      );
      if (res_) {
        $("#searchResult_end").html(res_);
        $(".list-wrapper-write").show();
      }
    });
    $(".cancel-wrapper-end").on("click", function () {
      $(".finish_select").hide();
      $("body").unbind("touchmove");
    });

    $("#searchInput_end").focus(function () {
      $(".list-wrapper-end").hide();
      $(".city-wrapper-end").show();
      $(".city-input-wrapper_end").hide();
      $(".list-wrapper-write").show();
    });
    //终点获取焦点
    $("#searchInput_end").bind("input porpertychange", async function (e) {
      var keyword = $("#searchInput_end").val();
      var city_f = $(".city-wrapper-end .text").html();

      const res_ = await defaultWordsSearch(
        city_f,
        keyword ? keyword : city_f,
        "end"
      );
      if (res_) {
        $("#searchResult_end").html(res_);
      } else {
        $("#searchResult_end").html(`<div class="city-empty-warp">
        <img src="/Theme/images/passenger/tip.svg"   />
        <span>暂无匹配结果，请重新输入</span>
       </div>`);
      }
      $(".list-wrapper-end").hide();
      $(".list-wrapper-write").show();
    });

    $(document).on("click", ".address_item_end", function () {
      const name = $(this).attr("name");
      const lng = $(this).attr("lng");
      const lat = $(this).attr("lat");
      const adcode = $(this).attr("adcode");
      const e = {
        data: {
          location: {
            lng: lng,
            lat: lat,
          },
          name: name,
          adcode: adcode,
        },
      };
      $("body").unbind("touchmove");
      $(".finish_select").hide();
      $(".finish_place").attr({
        lng: lng,
        lat: lat,
      });
      var line_lng = $(".origin_place").attr("lng");
      var line_lat = $(".origin_place").attr("lat");

      $(".finish_place").html(name);
      $(".finish_place").attr("adcode", adcode);
      var start_code = $(".origin_place").attr("adcode");
      const business_type = sessionStorage.getItem("flag_");
      if (business_type == "fast_car" || business_type == "taxi") {
        const lineObj = {
          start_longitude: line_lng,
          start_latitude: line_lat,
          book_seating: 1,
          end_longitude: e.data.location.lng,
          end_latitude: e.data.location.lat,
          start_address_code: start_code,
          end_address_code: adcode,
        };
        $(".cover-load").show();
        openFastPop();
        business_type == "fast_car"
          ? getFastPrice(lineObj)
          : getTaxiPrice(lineObj);
        $(".confirm-order-on-time").addClass(
          business_type == "taxi" ? "taxiOrderAction" : "fastOrderAction"
        );
        $(".confirm-order-on-time").removeClass(
          business_type == "taxi" ? "fastOrderAction" : "taxiOrderAction"
        );
        if (driving) {
          driving.clear();
        }
        //显示模拟地图路线
        setOverLines();
      }
      if (business_type == "free") {
        //如果是顺风车获取价格
        var person_number = $(".create_free_nums").html();
        lui.loading();
        $.ajax({
          url:
            "/Passenger/Order/doMobileComputeLineFreeRidePrice?callback=" +
            sessionStorage.getItem("callback"),
          type: "post",
          dataType: "json",
          data: {
            start_longitude: line_lng,
            start_latitude: line_lat,
            book_seating: person_number,
            end_longitude: e.data.location.lng,
            end_latitude: e.data.location.lat,
          },
          success: function (n) {
            lui.loading("close");
            if (n.ret) {
              $(".write_price").show();
              //获取优惠券价格
              $(".free_p_price i").html(n.data.price);
            } else {
              lui.toast({
                position: "middle",
                text: n.data,
              });
            }
          },
          error: function () {
            lui.loading("close");
          },
        });
        return;
      }
      if (business_type == "chart_car") {
        $(".end-chartered-place").html(
          `<div class="chartered-checked">${name}<i class="checked-right"></i></div>`
        );
        $(".end-chartered-place").attr({
          end_address_code: adcode,
          end_name: name,
          end_longitude: lng,
          end_latitude: lat,
        });
        $(".check-place-charter .check-end-point .tarin-point-label").html(
          name
        );
      }

      if (business_type == "goods") {
        $(".input-express-address").val(name);
        $(".confirm-set-express").attr("adcode", adcode);
        $(".confirm-set-express").attr("lng", lng);
        $(".confirm-set-express").attr("lat", lat);
        $(".confirm-set-express").attr("viewaddress", name);
      }
    });
  },

  getAbledCity: function () {},

  getOrders: function () {
    var that = this;
    $.ajax({
      url:
        "/Passenger/Order/getOrdersOnWay?callback=" +
        sessionStorage.getItem("callback"),
      type: "get",
      success: function (json) {
        if (json.length > 20) {
          json = JSON.parse(json);
          if (json.ret && json.data.length > 0 && json.data[0].state < 6) {
            $(".message_view").show();
            $("#sureIngorder").show();
            $("#sureIngorder_btn").attr("order_id", json.data[0].order_id);
          } else {
            $(".message_view").hide();
          }
          // that.genListTpl(json);
        }
      },
      error: function (json) {
        $(".message_view").hide();
      },
      timeout: 300000,
    });
  },
  locationService: function () {
    getWxLocation()
      .then(async (res) => {
        const [lng, lat] = res;
        const address_result = await geocoderDetail(res);
        console.log(address_result);
        const address_ = address_result?.addressComponent;
        // address_.city = '代县'
        // address_.adcode = '140923'
        //当前定位信息缓存
        const aois_name =
          address_result?.aois?.[0]?.name || address_result.formattedAddress;
        const address = address_result.formattedAddress;
        const aois_city_name = address_result.addressComponent.city;
        const aois_adcode = address_result.addressComponent.adcode;
        const aois_district = address_result?.addressComponent?.district || "";
        const lng_ = address_result?.aois?.[0]?.location?.lng || lng;
        const lat_ = address_result?.aois?.[0]?.location?.lat || lat;
        // debug:调为代县
        // const lng_ = 112.96
        // const lat_ = 39.07
        const sessionLocation = {
          aois_name,
          address,
          aois_city_name,
          aois_adcode,
          lng_,
          lat_,
        };
        sessionStorage.setItem(
          "sessionLocation",
          JSON.stringify(sessionLocation)
        );

        if (
          sessionStorage.getItem("callback") ==
          "M05311655A15B75FAB86956663E1819CDH3943"
        ) {
          //单独为雁门出行显示名称为区县级
          $("#strart_city").html(
            `<span class="station-selected">${
              address_?.district || address_.city
            }</span>`
          );
          $('#strart_city').attr({
            codelevel: 'area',
            city_name: address_?.city || address_.province,
            ad_code: address_?.adcode || ''
          })
        } else {
          $("#strart_city").html(
            `<span class="station-selected">${
              address_?.city || address_.province
            }</span>`
          );
          $("#strart_city").attr({
            codelevel: "city",
            city_name: address_?.city || address_.province,
            ad_code: address_?.adcode || ''
          });
        }
        const result = await fetchCityAreaList(
          "end",
          address_?.city || address_.province,
          "",
          "",
          "train"
        );
        //M05311655A15B75FAB86956663E1819CDH3943 雁门出行  district

        if (result && result.length === 1) {
          //默认只有一个终点城市，默认选中
          $("#end_city").html(
            `<span class="station-selected">${result[0].city_name}</span>`
          );
          $("#end_city").attr({
            codelevel: "city",
            city_name: result[0].city_name,
          });
        }
        $("#j-popup-full-citys .current-warp").html(
          `<div class="hadCity" onclick="checkSearch('','city','','${
            address_?.city || address_.province
          }')">${address_?.city || address_.province}</div>`
        );
        $(".startAddressCode").html(
          `<b class="hadSelcetedCode">${address_?.city || address_.province}${
            aois_district ? `·${aois_district}` : ""
          }</b>`
        );
        $(".start-pinche").attr({
          codelevel: "city",
          city_name: address_?.city || address_.province,
          ad_code: aois_adcode,
        });
        $(".start-chartered-place").html(
          `<div class="chartered-checked">${aois_name}<i class="checked-right"></i></div>`
        );
        $(".start-chartered-place").attr({
          start_address_code: aois_adcode,
          start_name: aois_name,
          start_longitude: lng_,
          start_latitude: lat_,
        });
        $(".check-place-charter .check-start-point .tarin-point-label").html(
          aois_name
        );
        $(".origin_place").attr({ lng: lng_, lat: lat_, adcode: aois_adcode });
        $(".city-wrapper .text").html(aois_city_name);
        $(".city-wrapper-end .text").html(aois_city_name);
        const res_ = await defaultWordsSearch(aois_city_name, aois_city_name);
        if (res_) {
          $("#searchResult").html(res_);
        }

        const bussess_ = sessionStorage.getItem("flag_");
        const service_enabled = await checkCityUsed(aois_city_name, bussess_);
        $(".citys-unabled-view-tip").html(
          !service_enabled ? "暂未开通服务" : ""
        );
        //  起点终点显示当前定位城市
        $(".list-title i").html(aois_city_name);
        $(".list-title-end i").html(aois_city_name);
        bussess_ == "carpool" && loadData("fresh");
      })
      .catch((err) => {
        console.log("errerrerrerr", err);
        $(".pincheLoading").hide();
        $("#pullUp").hide();
        $(".pinche-empty-nocity").show();
        //定位失败
        $("#j-popup-full-citys .current-warp").html(`
        <div class="current-fail">定位失败</div>
        <div class="btn-reget-loaction" onclick="indexPage.resetLocation()">
          <img src="/Theme/images/passenger/location.svg" alt="">
        </div>`);

        $(".city-wrapper-end").find("span").html("请选择");
        $(".city-wrapper").find("span").html("请选择");
        $(".list-title-end").find("i").html("定位失败");
        $(".list-title").find("i").html("定位失败");
        // $(".origin_place ").html("您要从哪里出发");
        var offsetp = sessionStorage.getItem("flag_");
        if (offsetp !== "charge" || typeof offsetp == "undefind") {
          lui.showModal({
            confirmText: "我知道了",
            title: "获取当前位置失败",
            content: "请确认已开启定位权限，以获取准确的订单起点",
            confirm: function () {
              console.log("confirm");
            },
          });
        }

        // positionPicker.start();
      });
  },
  resetLocation: async function () {
    $(".map-reset .locate-icon").addClass("map-loading");
    try {
      // 确保地图容器样式正确
      const container = document.getElementById('container');
      if (container) {
        container.style.display = 'block';
        container.style.minHeight = '350px';
        // 使用尽可能低的z-index值，避免覆盖导航栏
        container.style.zIndex = '1';
        container.style.position = 'relative';
        
        // 确保导航栏显示在地图上方
        const navElement = document.getElementById('tagnav');
        if (navElement) {
          navElement.style.zIndex = '100';
          navElement.style.position = 'relative';
        }
        
        // 同时确保地图容器的父元素正确设置
        const mapParent = container.parentElement;
        if (mapParent && mapParent.classList.contains('map')) {
          mapParent.style.zIndex = '1';
          mapParent.style.position = 'relative';
        }
      }
      
      // 获取位置信息
      const res = await getWxLocation();
      
      if (!res || !Array.isArray(res) || res.length < 2) {
        throw new Error("获取到的位置数据无效");
      }
      
      // 清除加载状态
      $(".map-reset .locate-icon").removeClass("map-loading");
      
      // 获取或设置用户头像
      let userAvatar = $("#head_img").attr("src");
      if (!userAvatar) {
        userAvatar = "/Theme/images/passenger/init.png";
      }
      
      const [lng, lat] = res;
      
      // 检查地图实例是否可用
      if (!map) {
        console.warn("地图实例不可用");
        // 尝试创建新地图实例
        if (window.AMap && container) {
          try {
            console.log("尝试创建新地图实例");
            map = new window.AMap.Map(container, {
              resizeEnable: true,
              zoom: 15,
              viewMode: '2D'
            });
          } catch(e) {
            console.error("创建地图实例失败:", e);
            throw new Error("创建地图实例失败");
          }
        } else if (!window.AMap) {
          console.error("高德地图API未加载");
          $(".left-adress").text("地图未加载，请稍后再试");
          throw new Error("高德地图API未加载");
        }
      }
      
      // 清除地图上的标记
      if (map && typeof map.clearMap === 'function') {
        map.clearMap();
      }
      
      // 创建用户位置标记（显示用户头像或默认图标）
      if (map && window.AMap) {
        try {
          // 创建用户位置标记
          console.log("创建用户位置标记");
          const userMarker = new AMap.Marker({
            map: map,
            position: [lng, lat], // 使用定位的坐标
            offset: new AMap.Pixel(-12, -12), // 相对于基点的偏移位置
            draggable: false, // 不可拖动
            content: `<img class="user_icon" src="${userAvatar}" style="width:24px;height:24px;border-radius:50%;border:2px solid #3BBBA3;" />`, // 显示用户头像
            zIndex: 100 // 确保在其他标记之上
          });
          
          // 设置地图标记（出发地标记）
          startMarker = new AMap.Marker({
            map: map,
            position: [lng, lat],
            offset: new AMap.Pixel(-12, -24), // 调整偏移
            icon: new AMap.Icon({
              size: new AMap.Size(24, 36), // 图标大小
              image: "/Theme/images/passenger/map_control.png",
              imageOffset: new AMap.Pixel(8, 2), // 图标相对于marker的偏移量
              imageSize: new AMap.Size(16, 24) // 图片显示大小
            }),
            zIndex: 99
          });
        } catch(e) {
          console.error("创建地图标记失败:", e);
          // 创建失败不中断流程
        }
        
        // 添加动画效果（添加判断防止方法不存在）
        if (startMarker && typeof startMarker.setAnimation === 'function') {
          try {
            startMarker.setAnimation("AMAP_ANIMATION_DROP");
          } catch(e) {
            console.log("设置标记动画失败:", e);
            // 动画设置失败不影响主要功能
          }
        }
      }
      
      // 设置地图中心
      if (map && typeof map.setCenter === 'function') {
        map.setCenter(new AMap.LngLat(lng, lat));
        
        // 强制地图显示
        if (typeof map.resize === 'function') {
          console.log("调整地图大小");
          setTimeout(() => map.resize(), 100);
        }
      } else {
        console.warn("地图实例不完整，setCenter方法不可用");
      }
      
      // 获取地址详细信息
      const address_result = await geocoderDetail(res);
      const address_ = address_result?.addressComponent;
      
      // 当前定位信息缓存
      const aois_name = address_result?.aois?.[0]?.name || address_result.formattedAddress;
      const address = address_result.formattedAddress;
      const aois_city_name = address_result.addressComponent.city;
      const aois_adcode = address_result.addressComponent.adcode;
      const lng_ = address_result?.aois?.[0]?.location?.lng || lng;
      const lat_ = address_result?.aois?.[0]?.location?.lat || lat;
      
      const sessionLocation = {
        aois_name,
        address,
        aois_city_name,
        aois_adcode,
        lng_,
        lat_,
      };
      sessionStorage.setItem("sessionLocation", JSON.stringify(sessionLocation));
      
      // 更新UI显示
      // 更新当前位置显示
      $(".left-adress").text(aois_name || address);
      $(".origin_place").text(aois_name || address);
      
      // 更新城市名称
      $(".current-view-city-label").text(address_?.city || address_.province);
      $(".city-wrapper .text").text(address_?.city || address_.province);
      $(".city-wrapper-end .text").text(address_?.city || address_.province);
      
      // 更新列表标题
      $(".list-title i").text(address_?.city || address_.province);
      $(".list-title-end i").text(address_?.city || address_.province);
      
      // 更新起始点的当前位置信息
      if (aois_name) {
        $(".infowindow-map").removeClass("origin_clicked");
      }

      $("#strart_city").html(
        `<span class="station-selected">${address_?.city || address_.province}</span>`
      );
      $("#j-popup-full-citys .current-warp").html(
        `<div class="hadCity" onclick="checkSearch('','city','','${address_?.city || address_.province}')">${address_?.city || address_.province}</div>`
      );
      const bussess_ = sessionStorage.getItem("flag_");
      const service_enabled = await checkCityUsed(aois_city_name, bussess_);
      $(".citys-unabled-view-tip").html(
        !service_enabled ? "暂未开通服务" : ""
      );
      console.log("定位成功");
      
      // 检查地图是否正确显示
      if (map && typeof map.getContainer === 'function') {
        const mapContainer = map.getContainer();
        if (mapContainer && !mapContainer.querySelector('.amap-layers')) {
          console.warn("地图图层未正确加载，尝试重建");
          
          // 尝试重建地图
          if (window.forceResetMap && typeof window.forceResetMap === 'function') {
            window.forceResetMap();
          } else if (typeof map.destroy === 'function') {
            map.destroy();
            
            // 重新创建地图
            setTimeout(() => {
              if (window.AMap && container) {
                try {
                  console.log("开始重建地图实例");
                  map = new window.AMap.Map(container, {
                    resizeEnable: true,
                    zoom: 15,
                    viewMode: '2D',
                    center: [lng, lat]
                  });
                  console.log("已重建地图");
                  
                  // 重建后再次创建用户位置标记
                  if (map) {
                    try {
                      // 创建用户位置标记
                      const userMarker = new AMap.Marker({
                        map: map,
                        position: [lng, lat],
                        offset: new AMap.Pixel(-12, -12),
                        draggable: false,
                        content: `<img class="user_icon" src="${userAvatar}" style="width:24px;height:24px;border-radius:50%;border:2px solid #3BBBA3;" />`,
                        zIndex: 100
                      });
                    } catch(e) {
                      console.error("重建后创建用户标记失败:", e);
                    }
                  }
                } catch(e) {
                  console.error("重建地图实例失败:", e);
                }
              }
            }, 500); // 延长等待时间，确保API加载完成
          }
        }
      }
    } catch (error) {
      // 统一的错误处理
      console.error("地图定位过程中发生错误:", error);
      $(".map-reset .locate-icon").removeClass("map-loading");
      $(".left-adress").text("定位失败，请稍后再试");
      
      // 显示重试按钮
      $("#j-popup-full-citys .current-warp").html(`
        <div class="current-fail">定位失败</div>
        <div class="btn-reget-loaction" onclick="indexPage.resetLocation()">
          <img src="/Theme/images/passenger/location.svg" alt="">
        </div>
      `);
    }
  },
  getPay: function (orderNo, orderPrice, order_id) {
    var openid = $("#openid-pay").val();

    if (isInMiniProgramWebView()) {
      console.log("当前在小程序的web-view中");
      let url =
        "/pages/payPage?payParam=" +
        encodeURIComponent(
          JSON.stringify({ orderNo, orderPrice, order_id, openid })
        );
      console.log(wx);
      console.log(WeixinJSBridge);
      wx.miniProgram.navigateTo({
        url: url,
      });

      // setTimeout(() => {
      //   window.location.href =
      //     "/passenger_order_detail?order_id=" +
      //     order_id +
      //     "&callback=" +
      //     sessionStorage.getItem("callback") +
      //     "&show=1";
      // }, 1500)

      return;
    }

    $.ajax({
      url:
        "/Passenger/Account/doWxOrderPay?callback=" +
        sessionStorage.getItem("callback"),
      data: {
        order_no: orderNo,
        openid: openid,
        amount: orderPrice,
      },
      type: "post",
      success: function (json) {
        json = JSON.parse(json);
        lui.loading("close");
        if (json.ret) {
          if (typeof WeixinJSBridge == "undefined") {
            if (document.addEventListener) {
              document.addEventListener(
                "WeixinJSBridgeReady",
                onBridgeReady,
                false
              );
            } else if (document.attachEvent) {
              document.attachEvent("WeixinJSBridgeReady", onBridgeReady);
              document.attachEvent("onWeixinJSBridgeReady", onBridgeReady);
            }
          } else {
            onBridgeReady(
              json.data.appId,
              json.data.timeStamp,
              json.data.nonceStr,
              json.data.package,
              json.data.paySign,
              order_id
            );
          }
        } else {
          lui.toast({
            position: "middle",
            text: json.data || "获取支付参数出错，请稍后再试",
          });
          setTimeout(() => {
            window.location.href =
              "/passenger_order_detail?order_id=" +
              order_id +
              "&callback=" +
              sessionStorage.getItem("callback") +
              "&show=1";
          }, 1500);
        }
      },
      error: function (json) {
        lui.toast({
          position: "middle",
          text: "操作失败，请稍后再试",
        });
      },
      timeout: 300000,
    });
  },
  baseControl: function () {
    /* 导航栏点击事件-出行 */
    $(".nav-home").on("click", function () {
      window.location.href = "/passenger_index?callback=" + $("#call_url").val() + "&clickFrom=homepage";
    });
    /* 导航栏点击事件-兑购 */
    /* href跳转 */
    /* 导航栏点击事件-订单 */
    $(".nav-order").on("click", function () {
      window.location.href = "/passenger_order_history?callback=" + $("#call_url").val() + "&clickFrom=homepage";
    });
    /* 导航栏点击事件-我的 */
    $(".nav-mine").on("click", function () {
      window.location.href = "/passenger_mine?callback=" + $("#call_url").val() + "&clickFrom=homepage";
    });
    $(".mine-in-warp").on("click", function () {
      window.location.href =
          "/passenger_mine?callback=" +
          $("#call_url").val() +
          "&clickFrom=homepage";
    });
    $(".responsive").on("click", function () {
      window.location.href =
        "/passenger_mine?callback=" +
        $("#call_url").val() +
        "&clickFrom=homepage";
    });
    indexPage.bindphone();
    //取消绑定手机
    $(".bind-nav .navbar-content-left").on("click", function () {
      $(".register_page_one").css("left", "-100%", "normal");
    });
    $("#unbind_and_next").on("click", function () {
      $(".register_page_one").css("left", "-100%", "normal");
    });

    //刷新显示
    var _offset = sessionStorage.getItem("flag_");
    if (_offset) {
      $("#" + _offset).addClass("menu-active");
      $("#" + _offset)
        .siblings()
        .removeClass("menu-active");
      indexPage.baseShow(_offset);
      indexPage.pageControl(_offset);

      const index_ = Number($("#" + _offset).attr("index")) - 1; //当前下标
      var children = $("#nav_all").children();
      let totalWidth = 0;
      children.each(function (i, _) {
        if (i <= index_) {
          totalWidth += $(this).width();
        }
      });
      if (index_ > 2) {
        //构造横向滚动过渡效果
        $("#nav_all")
          .stop()
          .animate({ scrollLeft: totalWidth - 180 }, 300);
      } else {
        $("#nav_all").stop().animate({ scrollLeft: 0 }, 300);
      }
    } else {
      $(".nav_all li").eq(0).addClass("menu-active");
      $(".nav_all li").eq(0).siblings().removeClass("menu-active");
      var currut_id = $(".nav_all li").eq(0).attr("id");
      indexPage.baseShow(currut_id);
      indexPage.pageControl(currut_id);
    }
    //顶部导航切换
    $(".nav_all li").on("click", function () {
      let canClick = $(this).attr("canclick");
      if (canClick == 0) {
        return;
      }
      var pageid = $(this).attr("id");
      if (pageid === "phone") {
        const { lng_ = "", lat_ = "" } = sessionStorage.getItem(
          "sessionLocation"
        )
          ? JSON.parse(sessionStorage.getItem("sessionLocation"))
          : {};
        window.location.href = `/passenger_call?lng=${lng_}&lat=${lat_}&callback=${sessionStorage.getItem(
          "callback"
        )}`;
        return;
      }

      const index_ = Number($(this).attr("index")) - 1; //当前下标
      var children = $("#nav_all").children();
      let totalWidth = 0;
      children.each(function (i, _) {
        if (i <= index_) {
          totalWidth += $(this).width();
        }
      });
      if (index_ > 2) {
        //构造横向滚动过渡效果
        $("#nav_all")
          .stop()
          .animate({ scrollLeft: totalWidth - 180 }, 300);
      } else {
        $("#nav_all").stop().animate({ scrollLeft: 0 }, 300);
      }

      indexPage.resetLocation();
      const el = document.querySelector("#j-popup-half-comfirm");
      var style = window.getComputedStyle(el); //el即DOM元素
      if (style.display === "block") {
        //
        closeConfirmOrder();
      }

      const callback = sessionStorage.getItem("callback");
      const consultPages = $("#consult_pages").val();
      const consultCodes = $("#consult_codes").val();
      if(consultCodes.split(',').includes(callback) && consultPages.split(',').includes(pageid)) {
        //掌上巴士商户自定义跳转页面
        window.location.href = `/passenger_consult?callback=${callback}`;
        return;
      }

      if (pageid == "goods") {
        //获取营业时间
        $.ajax({
          url:
            "/Passenger/Line/getTakeGoodsAppointmentTime&callback=" +
            sessionStorage.getItem("callback"),
          type: "get",
          success: function (json) {
            var stl = "";
            var json = JSON.parse(json);
            if (json.ret) {
              var dotype = json.data.business_time_type;
              var set_order_time = json.data.set_order_time;
              if (dotype == 1) {
                var oDatemin = json.data.start_appointment_time;
                var oDatemax = json.data.end_appointment_time;
                set_time_space(oDatemin, oDatemax, set_order_time);
              } else {
                set_time_space("00:00", "24:00", set_order_time);
              }
            } else {
            }
          },
          error: function (json) {},
          timeout: 300000,
        });
      }
      //出租车、快车地图自动刷新

      console.log("刷新机制=====");
      if (pageid == "taxi" || pageid == "fast_car") {
        $(".infowindow-map").css("display", "flex");
        if (map) {
          mapCity = "";
          map.clearMap();
          //订单确认界面返回逻辑：1.地图回到起点位置,视野范围17 2.清除地图路劲规划 3.地图样式恢复为默认状态 4.开启滑动地图选点
          const current_start_point_lng = $(".origin_place").attr("lng");
          const current_start_point_lat = $(".origin_place").attr("lat");
          console.log("current_start_point_lng", current_start_point_lng);
          current_start_point_lng &&
            map.setZoomAndCenter(
              16,
              new AMap.LngLat(current_start_point_lng, current_start_point_lat),
              false,
              300
            );
          driving && driving.clear();
          map.setMapStyle("amap://styles/normal");
          setTimeout(() => {
            positionPicker && positionPicker.start();
          }, 800);
        }
        $(".point-info-view-warp").css("display", "block");
        $(".point-info-end").html(`<div class="haspoint">
             <div class="end-point finish_place">请输入您的下车位置</div>
           </div>`);
      }
      indexPage.pageControl(pageid);
      $(this).addClass("menu-active");
      $(this).siblings().removeClass("menu-active");
      indexPage.baseShow(pageid);
    });

    $("#cancel_sure").on("click", function () {
      $("#sureIngorder").hide();
      // sessionStorage.setItem("showcontrol", 3);
    });
    $("#sureIngorder_btn").on("click", function () {
      window.location.href =
        "/passenger_order_detail?order_id=" +
        $(this).attr("order_id") +
        "&callback=" +
        sessionStorage.getItem("callback");
      $("#sureIngorder").hide();
      // sessionStorage.setItem("showcontrol", 2);
    });
    $(document).on("click", ".action-increase", function () {
      indexPage.upDownOperation($(this));
    });
    $(document).on("click", ".action-reduce", function () {
      indexPage.upDownOperation($(this));
    });
  },
  bindphone: function () {
    //获取验证码
    var set;
    $(".get_phone_code").on("click", function () {
      const this_ = this;
      var cellphone = $("#tset_phone").val();
      if (cellphone == "" || !!!cellphone) {
        lui.toast({
          position: "middle",
          text: "请输入手机号码",
        });
        return;
      }

      if (!isValidPhoneNumber(cellphone)) {
        lui.toast({
          position: "middle",
          text: "请输入正确的手机号码",
        });
        return;
      }

      $.ajax({
        type: "post",
        url:
          "/Passenger/Account/doMobileSendCellphoneValidateCode?callback=" +
          sessionStorage.getItem("callback"),
        data: {
          cellphone: cellphone,
        },
        dataType: "json",
        success: function (json) {
          json = eval(json);
          if (json.ret) {
            indexPage.clickButton(this_);
            lui.toast({
              position: "middle",
              text: "获取验证码成功",
            });
          } else {
            $(".get_phone_code").attr("disabled", false).html("获取验证码");
            $(".get_phone_code").removeClass("cc-button-disabled");
            clearInterval(set);
            $(".get_phone_code").removeAttr("disabled");
            lui.toast({
              position: "middle",
              text: json.data,
            });
          }
        },
        error: function (json) {
          console.log("获取验证码失败了");
        },
      });
    });
    $(".phone_botton").on("click", function () {
      const checked = $(".bind-agree-check").prop("checked");
      var cellphone_validate_code = $("#test_word").val();
      var cellphone = $("#tset_phone").val();
      if (cellphone == "" || cellphone == null) {
        lui.toast({
          position: "middle",
          text: "请输入手机号码",
        });
        $("#tset_phone").focus();
        return;
      }

      if (cellphone_validate_code == "" || cellphone_validate_code == null) {
        lui.toast({
          position: "middle",
          text: "请输入验证码",
        });
        $("#test_word").focus();
        return;
      }
      if (!checked) {
        lui.toast({
          position: "middle",
          text: "请阅读并勾选底部协议",
        });
        return;
      }
      lui.loading();
      $.ajax({
        type: "post",
        url:
          "/Passenger/Account/doBindingCellphone?callback=" +
          sessionStorage.getItem("callback"),
        data: {
          cellphone: cellphone,
          cellphone_validate_code: cellphone_validate_code,
        },
        datatype: "json",
        success: function (json) {
          lui.loading("close");
          json = eval("(" + json + ")");
          if (json.ret) {
            lui.toast({
              position: "middle",
              text: "绑定成功",
            });
            $("#cellphone-data").val(cellphone);
            $(".user_phone").html(cellphone);
            setTimeout(function () {
              $(".register_page_one").css("left", "-100%", "normal");
            }, 1000);
          } else {
            // $(".get_phone_code").removeAttr("disabled");
            lui.toast({
              position: "middle",
              text: json.data,
            });
          }
        },
        error: function (res) {
          // 错误提示
          alert(res.data);
        },
      });
    });
  },
  clickButton: function (obj) {
    var obj = $(obj);
    obj.attr("disabled", "disabled");
    obj.addClass("cc-button-disabled");
    var time = 60;
    set = setInterval(function () {
      obj.html(--time + "(s)后重新获取");
    }, 1000);
    setTimeout(function () {
      obj.removeClass("cc-button-disabled");
      obj.html("获取验证码");
      obj.removeAttr("disabled");
      clearInterval(set);
    }, 60000);
  },
  setFreeCity: function () {
    $("#loadinganimate").show();
    //获取城市
    $.ajax({
      url:
        "/Passenger/AddressCode/getAddressCodes?callback=" +
        sessionStorage.getItem("callback"),
      type: "get",
      dataType: "json",
      success: function (data) {
        $("#loadinganimate").hide();
        if (data.ret) {
          var tem = template("tem-data", data);
          $("#cityCheckContent").html(tem);
          $("#cityCheckContent").find("div").eq(0).addClass("active");
        } else {
          $(".cityCheckContent").html(`<div class="ui_empty freecity-warp" >
          <i class="ui_empty_ico"></i>
          <p class="ui_empty_text">暂无开通城市</p>
        </div>`);
        }
      },
    });
    $(".o_search").on("click", function () {
      sessionStorage.setItem("getcity", "start");
      $(".city_select").show();
    });
    //获取终点城市
    $(".f_search").on("click", function () {
      $(".city_select").show();
      sessionStorage.setItem("getcity", "end");
    });
    $(document).on("click", ".areaList .item-area-label", function () {
      var cityset = sessionStorage.getItem("getcity");
      if (cityset == "start") {
        var city = $(this).html();
        var code = $(this).attr("city_code");
        $(".o_search").attr("address_id", code);
        $(".o_title").html(city);
      } else {
        var city = $(this).html();
        var code = $(this).attr("city_code");
        $(".f_search").attr("end_address_id", code);
        $(".f_title").html(city);
      }
      $(".city_select").hide();
    });
    $("#cityCheckContent").on("click", ".list-group-item", function () {
      $(this).addClass("active");
      $(this).siblings().removeClass("active");
    });

    //关闭选择城市
    $(".close_city").on("click", function () {
      $(".city_select").hide();
    });
  },
  getregular: function (search_date) {
    $(".new-trainList").html("");
    $(".unabled-list").html("");
    $(".unable-title").hide();
    lui.loading();
    const cid = $("#cid").val() || "";
    const driver_id = $("#driver_id").val() || "";
    const channel_branch_id = $("#channel_branch_id").val() || "";

    // 修复BUG：如果URL中有cid或driver_id参数，直接查询班次，不需要其他参数
    if (
      (cid && cid !== "") ||
      (driver_id && driver_id !== "") ||
      (channel_branch_id && channel_branch_id !== "")
    ) {
      $.ajax({
        type: "get",
        url:
          "/Passenger/Line/queryDingzhikeyunLine?callback=" +
          sessionStorage.getItem("callback"),
        dataType: "json",
        data: {
          cid: cid,
          day_time: search_date,
          driver_id: driver_id,
          channel_branch_id: channel_branch_id,
        },
        success: function (data) {
          lui.loading("close");
          if (data.ret) {
            let abled_ = [];
            let disabled_ = [];
            data.data.map((o, i) => {
              o["index"] = i;
              return o;
            });
            //根据不同的商户需求处理班次列表数据
            const is_default_via_axis = $("#is_default_via_axis").val();
            if (is_default_via_axis == 1) {
              //按默认时间轴显示数据
              data.data.map((o) => {
                if (
                  o.is_expire !== 1 &&
                  (Number(o.remain_tickets) > 0 ||
                    Number(o.remain_tickets) === -1)
                ) {
                  abled_.push(o);
                } else {
                  disabled_.push(o);
                }
              });
            } else {
              //按默认相近时间轴分组后重新显示数据
              const groupedData = data.data
                .reduce((acc, item) => {
                  const key = item.line_class.id;
                  // 如果当前 key 已经存在于累积器中，则添加到现有组；否则创建新组
                  if (acc[acc.length - 1]?.key === key) {
                    acc[acc.length - 1].items.push(item);
                  } else {
                    acc.push({ key, items: [item] });
                  }
                  return acc;
                }, [])
                .map((o) => {
                  return {
                    ...o.items[0],
                    tickets: o.items,
                  };
                });

              console.log("groupedData", groupedData);
              groupedData.forEach((item_t) => {
                if (
                  item_t.tickets.some((o) => {
                    return (
                      (o.is_expire !== 1 && Number(o.remain_tickets) > 0) ||
                      (o.is_expire !== 1 && Number(o.remain_tickets) === -1)
                    );
                  })
                ) {
                  abled_.push(item_t);
                } else {
                  disabled_.push(item_t);
                }
              });
            }
            console.log("abled", abled_);
            console.log("disabled_", disabled_);
            sessionStorage.removeItem("ticketObj");
            sessionStorage.setItem("ticketObj", JSON.stringify(data.data));
            $(".new-trainList").html(
              is_default_via_axis == 1
                ? renderCustomClasses(abled_)
                : renderClasses(abled_)
            );
            if (disabled_.length) {
              $(".unable-title").show();
              $(".unabled-list").html(
                is_default_via_axis == 1
                  ? renderCustomClasses(disabled_)
                  : renderClasses(disabled_)
              );
            } else {
              $(".unable-title").hide();
              $(".unabled-list").html("");
            }
            if (!!!abled_.length) {
              $(".new-trainList").html(`<div class="busConditionUnbook">
              <img class="empty-content-icon" src="/Theme/images/passenger/empty-content.svg" alt="">
              <div class="content">很抱歉，您选择的条件下暂无可预订班次~</div>
              </div>`);
            }
          } else {
            $(".new-trainList").html(
              `<div class="bus-no-result">
              <img class="empty-content-icon" src="/Theme/images/passenger/empty-content.svg" alt="">
              <div class="title">未查询到符合条件的班次</div>
              <div class="content">您可尝试切换线路</div>
              <button type="button" size="sm" class="ui_btn research" onclick="trainsBack()">重新查询</button>
              </div>`
            );
          }
        },
        error: function (xhr, type) {
          lui.loading("close");
          console.log("数据加载出错");
        },
      });
    } else {
      const start_name = $("#j-popup-right").attr("start_name") || "";
      const end_name = $("#j-popup-right").attr("end_name") || "";
      const start_address_code = $("#j-popup-right").attr("start_code") || "";
      const end_address_code = $("#j-popup-right").attr("end_code") || "";
      const start_level = $("#j-popup-right").attr("start_level") || "";
      const end_level = $("#j-popup-right").attr("end_level") || "";
      $.ajax({
        type: "get",
        url:
          "/Passenger/Line/queryDingzhikeyunLine?callback=" +
          sessionStorage.getItem("callback"),
        dataType: "json",
        data: {
          cid: cid,
          start_address_code:
            start_level && start_level !== "city" ? start_address_code : "",
          end_address_code:
            end_level && end_level !== "city" ? end_address_code : "",
          start_name: start_level && start_level === "name" ? start_name : "",
          end_name: end_level && end_level === "name" ? end_name : "",
          day_time: search_date,
          start_city_name:
            start_level && start_level === "city" ? start_name : "",
          end_city_name: end_level && end_level === "city" ? end_name : "",
          driver_id: $("#driver_id").val() || "",
          channel_branch_id: $("#channel_branch_id").val() || "",
        },
        success: function (data) {
          // 记录起始时间
          const startTime = performance.now();
          lui.loading("close");
          if (data.ret) {
            let abled_ = [];
            let disabled_ = [];
            data.data.map((o, i) => {
              o["index"] = i;
              return o;
            });
            //根据不同的商户需求处理班次列表数据
            const is_default_via_axis = $("#is_default_via_axis").val();
            if (is_default_via_axis == 1) {
              //按默认时间轴显示数据
              data.data.map((o) => {
                if (
                  o.is_expire !== 1 &&
                  (Number(o.remain_tickets) > 0 ||
                    Number(o.remain_tickets) === -1)
                ) {
                  abled_.push(o);
                } else {
                  disabled_.push(o);
                }
              });
            } else {
              //按默认相近时间轴分组后重新显示数据
              const groupedData = data.data
                .reduce((acc, item) => {
                  const key = item.line_class.id;
                  // 如果当前 key 已经存在于累积器中，则添加到现有组；否则创建新组
                  if (acc[acc.length - 1]?.key === key) {
                    acc[acc.length - 1].items.push(item);
                  } else {
                    acc.push({ key, items: [item] });
                  }
                  return acc;
                }, [])
                .map((o) => {
                  return {
                    ...o.items[0],
                    tickets: o.items,
                  };
                });

              console.log("groupedData", groupedData);
              groupedData.forEach((item_t) => {
                if (
                  item_t.tickets.some((o) => {
                    return (
                      (o.is_expire !== 1 && Number(o.remain_tickets) > 0) ||
                      (o.is_expire !== 1 && Number(o.remain_tickets) === -1)
                    );
                  })
                ) {
                  abled_.push(item_t);
                } else {
                  disabled_.push(item_t);
                }
              });
            }
            console.log("abled", abled_);
            console.log("disabled_", disabled_);
            sessionStorage.removeItem("ticketObj");
            sessionStorage.setItem("ticketObj", JSON.stringify(data.data));
            $(".new-trainList").html(
              is_default_via_axis == 1
                ? renderCustomClasses(abled_)
                : renderClasses(abled_)
            );
            if (disabled_.length) {
              $(".unable-title").show();
              $(".unabled-list").html(
                is_default_via_axis == 1
                  ? renderCustomClasses(disabled_)
                  : renderClasses(disabled_)
              );
            } else {
              $(".unable-title").hide();
              $(".unabled-list").html("");
            }
            if (!!!abled_.length) {
              $(".new-trainList").html(`<div class="busConditionUnbook">
              <img class="empty-content-icon" src="/Theme/images/passenger/empty-content.svg" alt="">
              <div class="content">很抱歉，您选择的条件下暂无可预订班次~</div>
              </div>`);
            }
          } else {
            $(".new-trainList").html(
              `<div class="bus-no-result">
              <img class="empty-content-icon" src="/Theme/images/passenger/empty-content.svg" alt="">
              <div class="title">未查询到符合条件的班次</div>
              <div class="content">您可尝试切换线路</div>
              <button type="button" size="sm" class="ui_btn research" onclick="trainsBack()">重新查询</button>
              </div>`
            );
          }
          // 记录结束时间
          const endTime = performance.now();
          // 计算执行时间
          const executionTime = endTime - startTime;
          // 输出执行时间
          console.log(`代码执行时间: ${executionTime} 毫秒`);
        },
        error: function (xhr, type) {
          lui.loading("close");
          console.log("数据加载出错");
        },
      });
    }
  },
  setTime: function () {
    const weeks_ = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    $(".open-calendar .date-row").html(
      `${moment().locale("zh-cn").format("MM月DD日")} ${weeks_[moment().day()]}`
    );
    $(".open-calendar .date-row").attr(
      "date",
      moment().locale("zh-cn").format("YYYY-MM-DD")
    );
  },
  baseShow: function (pageId) {
    sessionStorage.setItem("flag_", pageId);
    const local_biz = localStorage.getItem("biz_support");

    renderBussess_(
      local_biz && local_biz != "undefined" ? JSON.parse(local_biz) : []
    );

    if (pageId === "carpool") {
      $("#tpl_home").show();
      $(".map").hide();
      $("#tpl_home").siblings().hide();
    } else if (pageId === "fast_car") {
      $("#tpl_fast").show();
      $("#tpl_fast").siblings().hide();
      $(".map").show();
    } else if (pageId === "taxi") {
      $("#tpl_taxi").show();
      $("#tpl_taxi").siblings().hide();
      $(".map").show();
    } else if (pageId === "chart_car") {
      $("#tpl_chart").show();
      $("#tpl_chart").siblings().hide();
      $(".map").hide();
    } else if (pageId === "regular") {
      $("#tpl_regular").show();
      $("#tpl_regular").siblings().hide();
      $(".map").hide();
    } else if (pageId === "goods") {
      $("#tpl_goods").show();
      $("#tpl_goods").siblings().hide();
      $(".map").hide();
      set_time_space("00:00", "24:00");
    } else if (pageId === "charge") {
      $("#tpl_charge").show();
      $("#tpl_charge").siblings().hide();
      $(".map").hide();
      set_time_space("00:00", "24:00");
    } else if (pageId === "ai_chat") {
      window.location.href = "http://c.higgses.com/micro-apps/chat/";
      return;
    } else {
      $("#tpl_home").show();
      $("#tpl_home").siblings().hide();
      $(".map").hide();
    }
  },
};
indexPage.initpage();

function checkPlaceAvail(point) {
  console.log("mapServiceArea", mapServiceArea);
  const isPointInService = mapServiceArea.some((o, k) => {
    return AMap.GeometryUtil.isPointInRing(point, o);
  });
  return isPointInService;
}

async function fetchUsedCity(city_name) {
  console.log("city_namecity_namecity_name", city_name);
  return new Promise((resolve, reject) => {
    $.ajax({
      type: "get",
      url:
        "/Passenger/Line/queryKuaicheLine?callback=" +
        sessionStorage.getItem("callback"),
      dataType: "json",
      data: {
        start_city_name: city_name,
      },
      success: function (res) {
        if (res.ret) {
          const city_attributes = res?.data?.[0]?.city_attributes;
          resolve(city_attributes);
        } else {
          resolve([]);
        }
      },
    });
  });
}

async function drawpolys(city_attributes) {
  for (let i = 0; i < city_attributes.length; i++) {
    let item_ = city_attributes[i];
    let icode = "";
    if (item_.start_area) {
      //3级非市辖区
      if (item_.start_area.name != "市辖区") {
        icode = item_.start_area.address_id;
      } else {
        icode = item_.start_city_code;
      }
    } else {
      if (item_.start_city) {
        if (item_.start_city.name != "市辖区") {
          icode = item_.start_city.address_id;
        } else {
          icode = item_.start_city_code;
        }
      } else {
        icode = item_.start_province_code;
      }
    }
    const area_poly = await fetBounds(icode);
    const poly_ = item_.start_polygon.map((o) => o.split(","));

    area_poly.map((o) => {
      let range =
        item_.start_polygon.length > 1
          ? AMap.GeometryUtil.ringRingClip(poly_, o)
          : o;
      if (range.length) {
        let custompoly = new AMap.Polygon({
          path: range,
          fillColor: "#1c73e2",
          strokeOpacity: 1,
          fillOpacity: 0.3,
          strokeColor: "#2b8cbe",
          strokeWeight: 1,
          strokeStyle: "dashed",
          strokeDasharray: [5, 5],
        });
        mapServiceArea.push(range);
        map.add(custompoly);
        mapCitypoly.push(custompoly);
      }
    });
  }
}

function checkLength(obj, maxlength) {
  if (obj.value.length > maxlength) {
    obj.value = obj.value.substring(0, maxlength);
  }
}

$(document).on("click", ".coupon-popup .item-coupon", function () {
  if ($(this).hasClass("ui_coupon_checkbox_checked")) {
    $(this).find("input").attr("checked", false);
    $(this).removeClass("ui_coupon_checkbox_checked");
  } else {
    $(this).addClass("ui_coupon_checkbox_checked");
    $(this).siblings().removeClass("ui_coupon_checkbox_checked");
    $(this).siblings().find("input").attr("checked", false);
    $(this).find("input").attr("checked", true);
  }
});

$(document).on("click", ".comfirm-coupon-action", function () {
  const select_coupon_id = $(".coupon-popup .ui_coupon_checkbox_checked").attr(
    "coupon_record_id"
  );
  const select_coupon_val = $(".coupon-popup .ui_coupon_checkbox_checked").attr(
    "value"
  );
  const select_coupon_type = $(
    ".coupon-popup .ui_coupon_checkbox_checked"
  ).attr("type");
  const const_price = $(".used-coupon-params").attr("const_price") || 0;
  if (select_coupon_id) {
    const disc_money =
      select_coupon_type == 1
        ? select_coupon_val
        : Money((1 - Number(select_coupon_val) / 10) * Number(const_price), 2); //显示优惠金额
    $(".used-coupon-params").attr({
      coupon_record_id: select_coupon_id,
      type: select_coupon_type,
      value: select_coupon_val,
      disc_money,
      const_price,
    });
    $(".coupon-val-warp").html(
      `<label class="selected-coupon-view">-￥${disc_money}</label>`
    );
  } else {
    $(".used-coupon-params").attr({
      coupon_record_id: "",
      type: "",
      value: 0,
      disc_money: 0,
      const_price,
    });
    const length_ = $(".coupon-popup").children().length;
    $(".coupon-val-warp").html(
      length_ > 0 ? `${length_}张优惠券可用` : "暂无可用优惠"
    );
  }
  priceCalculation(true);
  togglePopupExpress(true, "#j-popup-half-coupon");
});
function isValidJSON(str) {
  const jsonRegex = /^\s*(\{[\w\W]*\}|\[[\w\W]*\])\s*$/;
  if (!jsonRegex.test(str)) {
    return false;
  }
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

function fetchAbledCoupons(const_price, biz) {
  $(".train-check-order-bt").html(`<div class="spinner loading-spinner">
  <div class="bounce1"></div>
  <div class="bounce2"></div>
  <div class="bounce3"></div>
  </div>`);

  return new Promise((resolve, reject) => {
    $.ajax({
      url:
        "/Passenger/Coupon/getPassengerAbleCoupons?callback=" +
        sessionStorage.getItem("callback"),
      type: "post",
      data: {
        price: const_price,
        type: biz,
      },
      success: function (json) {
        console.log("jsonjsonjson", isValidJSON(json));
        if (!isValidJSON(json)) {
          $(".coupon-popup").html(`<div class="empty-coupon-view">
            <img class="empty-coupon-img" src="/Theme/images/passenger/empty-coupon.svg" alt="">
            <span>暂无可用优惠券</span>
          </div>`);
          $(".coupon-val-warp").html("暂无可用优惠");
          $(".coupon-bottom").hide();
          $(".used-coupon-params").attr({
            coupon_record_id: "",
            type: "",
            value: "",
            disc_money: "",
          });
          resolve(true);
          $(".train-check-order-bt").html(`提交订单`);
          return;
        }
        var json = JSON.parse(json);
        let coupon_list_dom = "";
        if (json.ret) {
          //默认计算优惠了多少金额（折扣券）
          const { coupon_record_id, type, value } = json.data[0];
          const disc_money =
            type == 1
              ? value
              : Money((1 - Number(value) / 10) * Number(const_price), 2); //显示优惠金额
          $(".used-coupon-params").attr({
            coupon_record_id,
            type,
            value,
            disc_money,
            const_price,
          });
          $(".coupon-val-warp").html(
            `<label class="selected-coupon-view">-￥${disc_money}</label>`
          );

          for (let i = 0; i < json.data.length; i++) {
            const item = json.data[i];
            coupon_list_dom += `<div class="item-coupon ui_checkbox ${
              i === 0 ? "ui_coupon_checkbox_checked" : ""
            }" 
              value="${item.value}"
              type="${item.type}"
              coupon_record_id="${item.coupon_record_id}">
              <img class="item-coupon-bg" src="/Theme/images/passenger/coupon-bg.png" />
              <div class="coupon-warp">
                <div class="left">
                  <div class="val">
                    <span class="fz48 fb">${item.value}</span>
                    <span class="fz24">${item.type == 1 ? "元" : "折"}</span>
                  </div>
                  <span class="condition">满${item.rule}可用</span>
                </div>
                <div class="right">
                  <div class="umore">
                    <div class="m-info">
                      <span class="coupon-name">${item.name}</span>
                      <span class="a-time">有效期至 ${item.end_time.slice(
                        0,
                        10
                      )}</span>
                    </div>
                    <div class="u-action">
                      <input type="checkbox" value="no" name="checkbox">
                    </div>
                  </div>
                </div>
              </div>
             </div>`;
          }
          $(".coupon-popup").html(coupon_list_dom);
          $(".coupon-bottom").show();
          $(".ui_coupon_checkbox_checked").find("input").attr("checked", true);
          resolve(true);
        } else {
          $(".coupon-popup").html(`<div class="empty-coupon-view">
          <img class="empty-coupon-img" src="/Theme/images/passenger/empty-coupon.svg" alt="">
          <span>暂无可用优惠券</span>
        </div>`);
          $(".coupon-val-warp").html("暂无可用优惠");
          $(".coupon-bottom").hide();
          $(".used-coupon-params").attr({
            coupon_record_id: "",
            type: "",
            value: "",
            disc_money: "",
          });
          resolve(true);
        }
        $(".train-check-order-bt").html(`提交订单`);
      },
    });
  });
}

//计算价格变化显示
async function priceCalculation(isChangeCoupon = false) {
  const data_index = Number($(".confirm-set-train").attr("clickindex"));
  let ticketObj = JSON.parse(sessionStorage.getItem("ticketObj"))[data_index]; //当前打开班次
  //是否选座、是否有优惠券、是否需要保险、乘客人数、接送费、      （保险组合）

  let const_price = 0,
    total_price = 0,
    insurance_price = 0,
    transfer_price = 0,
    real_price = 0,
    area_price = 0; //车票原价、小计、接送费、保险、实付
  let price_item_view = "";
  let insurance_item_view = "";
  let numbers = 0;
  const { is_seat_selection, price } = ticketObj;
  const isNeed = $(".item-radio.checked").attr("need");
  const item_insurance = $(".insuranceCheck").attr("insurance_amount") || 0;
  //不选座
  if (is_seat_selection == 0) {
    console.log("不选座模式下，票数", seat_lists.length);
    numbers = seat_lists.length || 1; //乘客人数
    const_price = Number(price) * numbers;
    price_item_view = `￥${price} x ${numbers}张`;
    insurance_item_view = isNeed == 1 ? `${item_insurance} x ${numbers}份` : "";
    insurance_price = isNeed == 1 ? Money(Number(item_insurance) * numbers) : 0;
  } else {
    const seat_lists__ = Object.assign([], seat_lists).filter((o) => o.checked);
    let price_arr = [];
    numbers = seat_lists__.length || 0;
    seat_lists.map((item) => {
      if (item.checked) {
        const_price += Number(item.price);
        price_arr.push(`￥${item.price}`);
      }
    });
    price_item_view = "￥" + Money(const_price);
    insurance_item_view = isNeed == 1 ? `${item_insurance} x ${numbers}份` : "";
    insurance_price = isNeed == 1 ? Money(Number(item_insurance) * numbers) : 0;
  }
  console.log("触发价格计算", seat_lists);

  // Money
  !isChangeCoupon && (await fetchAbledCoupons(const_price, 5));

  const fee_start = Number($(".fee-money-cell").attr("fee-start")) || 0;
  const fee_end = Number($(".fee-money-cell").attr("fee-end")) || 0;
  transfer_price = fee_start + fee_end;

  const start_area_price =
    Number($(".area-money-cell").attr("area-start")) || 0;
  const end_area_price = Number($(".area-money-cell").attr("area-end")) || 0;
  const area_start = start_area_price * numbers;
  const area_end = end_area_price * numbers;
  area_price = area_start + area_end;

  const area_item_view =
    (start_area_price > 0
      ? `￥${Money(start_area_price, 2)} x ${numbers} `
      : "") +
    (area_start > 0 && area_end > 0 ? `+ ` : "") +
    (area_end > 0 ? `￥${Money(end_area_price, 2)} x ${numbers}` : "");
  $(".money-detail-txt").html(area_item_view);
  $(".const-money").html(price_item_view); //显示原价
  $(".fee-money-cell .fee-money").html(Money(transfer_price));
  $(".area-money-cell .area-money").html(Money(area_price, 2, true));
  const disc_money = $(".used-coupon-params").attr("disc_money") || 0;
  $(".disc-money").html(Money(disc_money)); //-优惠金额

  const pointabledPrice = (const_price - Number(disc_money)).toFixed(2); //可用于积分抵扣总金额
  //判断当前路线是否支持积分兑换
  if (ticketObj.support_points_deduction === 1 && Number(pointabledPrice) > 0) {
    await getUseAblePoints(pointabledPrice);
  } else {
    $(".used-points-params").hide();
  }
  // used-points-params

  const isExta = $("#point-used").prop("checked");
  const point_money = isExta ? Number($(".point-left label").html() || 0) : 0; //是否勾选使用积分兑换
  $(".point-money").html(Money(point_money)); //-积分金额
  $(".insurance-money").html(insurance_item_view); //保险金额
  disc_money > 0 ? $(".disc-money-cell").show() : $(".disc-money-cell").hide();
  point_money > 0
    ? $(".point-money-cell").show()
    : $(".point-money-cell").hide();
  isNeed == 1 && Number(insurance_price) > 0
    ? $(".insurance-money-cell").show()
    : $(".insurance-money-cell").hide();
  total_price = Number(const_price) - Number(disc_money) - Number(point_money);
  $(".total-money").html(Money(total_price >= 0 ? total_price : 0));
  real_price =
    Number(total_price) +
    Number(insurance_price) +
    Number(transfer_price) +
    Number(area_price);
  $(".train-price-view b").html(numbers > 0 ? Money(real_price, 2) : "--");

  if ($("#wallet_service_enabled").val() == 1) {
    getUseAbleBalance(real_price); //判断余额是否足够支付
  } else {
    $(".paymode-container").hide();
  }
}
function addMinutesToTime(startTime, minutesToAdd) {
  const [hours, minutes] = startTime.split(":").map(Number);
  const totalMinutes = hours * 60 + minutes + minutesToAdd;
  const newHours = Math.floor(totalMinutes / 60) % 24;
  const newMinutes = totalMinutes % 60;
  return `${newHours.toString().padStart(2, "0")}:${newMinutes
    .toString()
    .padStart(2, "0")}`;
}
function renderCustomClasses(data) {
  let result = "";
  for (var i = 0; i < data.length; i++) {
    var item = data[i];
    if (item.travel_time == "" || typeof item.travel_time == "undefined") {
      item.travel_time = "";
    }

    let el_dom = "";
    let tic = item;
    // 处理剩余票数逻辑
    tic.remain_tickets = tic.remain_tickets;

    let doms_ = "";

    // 判断是否过期
    if (tic.is_expire === 1) {
      doms_ = '<span class="fz26 c85" style="white-space:nowrap;">售罄</span>';
    } else {
      // 根据 `is_remain_tickets_control` 确定余票显示逻辑
      if (tic.is_remain_tickets_control === 0) {
        // 不隐藏余票信息
        if (tic.remain_tickets === -1) {
          doms_ = '<span class="fz26 c2e">有票</span>';
        } else if (tic.remain_tickets === 0) {
          doms_ = '<span class="fz26 c85">已售罄</span>';
        } else {
          doms_ = `
              <span class="fz26 ${tic.remain_tickets < 5 ? "cff" : "c2e"}">
                余<label class="fz26 ${
                  tic.remain_tickets < 5 ? "cff" : "c2e"
                }">${tic.remain_tickets}张</label>
              </span>`;
        }
      } else {
        // 隐藏余票信息
        if (tic.remain_tickets === -1) {
          doms_ = '<span class="fz26 c2e">有票</span>';
        } else if (tic.remain_tickets === 0) {
          doms_ = '<span class="fz26 c85">已售罄</span>';
        } else if (tic.remain_tickets <= 5) {
          doms_ = `
              <span class="fz26 cff">
                余<label class="fz26 cff">${tic.remain_tickets}张</label>
              </span>`;
        } else {
          doms_ = '<span class="fz26 c2e">有票</span>';
        }
      }
    }

    let pointHtml = "";
    let point_ = "";
    const { start_point = [], end_point = [] } = tic;
    if (tic.start_address_type == 1 || tic.end_address_type == 1) {
      let arrs = [...start_point, ...end_point];
      arrs.forEach((o) => {
        o["newTime"] = addMinutesToTime(tic.start_time, o.use_time);
        point_ += `<div class="route-step via">
                  <div class="step-item-time">约${o.newTime}</div>
                    <div class="step-icon"></div>
                    <div class="step-content">${o.alias}</div>
                </div>`;
      });

      pointHtml = `<div class="toggle-button">
                   <i class="icon-down-up"></i> 途径点
                </div><div class="list-p-via">${point_}</div>`;
    } else {
      pointHtml = `<div class="cutdown-view"></div>`;
    }

    // 拼接最终的 DOM 结构
    el_dom += `
        <div 
          index="${tic.index}" 
          class="item-time-txt ${
            tic.is_expire === 1 || tic.remain_tickets === 0 ? "hadover" : ""
          }">
         ${
           tic.start_time_type === 2
             ? `<span class="fz28 c2e block fb time-in">
           ${tic.start_earliest_time}
          </span><span class="fz28 c2e block fb time-art">
          </span><span class="fz28 c2e block fb time-in  time-out">
          ${tic.end_latest_time}
          </span>`
             : `<span class="fz28 c2e block fb time-out">${tic.start_time}</span>`
         }
          ${doms_}
        </div>`;

    const line_class_ferry = $("#line_class_ferry").val();
    result += `<div  class="item-coustom-trainticket" >
    <div class="main-ticket-info">
        <div class="left-dom">
         ${el_dom}
        </div>
        <div class="middle flexcon tl">
          <div class="lineCanct">
            <div class="dot1"></div>
            <div class="dot2"></div>
            <div class="dot3"></div>
          </div>
          <div class="middle-line-name">
          <span class="fz32 fb c2e overhidden">${item.start_name}</span>
            ${
              item.start_address_type == 2
                ? `<span class="point-tag">上门接</span>`
                : ""
            }
          ${
            line_class_ferry == 1 && item.is_start_ferry == 1
              ? `<span class="point-tag">摆渡车</span>`
              : ""
          }
          </div>
                ${pointHtml}
          <div  class="middle-line-name">
          <span class="fz32 fb c2e overhidden">${item.end_name}</span>
            ${
              item.end_address_type == 2 &&
              item.line_class.hide_songdaojia_keyword == false
                ? `<span class="point-tag">送到家</span>`
                : ""
            }
            ${
              line_class_ferry == 1 && item.is_end_ferry == 1
                ? `<span class="point-tag">摆渡车</span>`
                : ""
            }
            
          </div>

        </div>
        <div class="right flexcon tr">
          <span class="price-txt fz32">
            <span class="fz24">￥</span>
              <span class="fb price-txt-label">${
                item.is_seat_selection == 0 ? item.price : item.min_seat_price
              }</span>	
              ${
                item.is_seat_selection == 1
                  ? `<span  class="fz24">起</span>`
                  : ""
              }
          </span>
          <div class="ticket-more-txt">
          <div class="ex-info">
                ${
                  item.support_points_deduction == 1
                    ? `<div class="cds-tag">积分抵扣</div>`
                    : ""
                }
                ${
                  item.is_seat_selection == 1
                    ? `<div class="cds-tag">可选座</div>`
                    : ""
                }
          </div>
            <div class="ex-info">
                ${
                  !Array.isArray(item.driver)
                    ? `<div class="cds-tag">${item.driver.car_tail_number}</div>`
                    : ""
                }
            </div>
              <div class="ex-info">
              ${
                !Array.isArray(item.driver)
                  ? `<div class="un-seat">${item.driver.name}</div>`
                  : ""
              }
              </div>
               <div  index="${item.index}"  class="act-reserve ${
      tic.is_expire === 1 || tic.remain_tickets === 0 ? "hadover" : ""
    }">预定</div>
          </div>
        </div>
        </div>
        <div class="act-bt">
        <div class="more-info-mind-txt">
          <span class="depart-type">${
            item.start_time_type == 2 ? "滚动发车" : ""
          }</span>
          ${
            item.start_time_type == 2 && item.line_class.travel_time
              ? `<span class="depart-type"> | </span>`
              : ""
          }
          ${
            item.line_class.travel_time
              ? `<span  class="depart-type">${item.line_class.travel_time}</span>`
              : ""
          }
        </div>
      
        </div>
      </div>`;
  }
  return result;
}
// <div class="time-select-content  ${item.start_time_type == 1 ? 'flex-content' : ''}"  >
// ${el_dom}
// </div>

function renderClasses(data) {
  let result = "";
  for (var i = 0; i < data.length; i++) {
    var item = data[i];
    if (item.travel_time == "" || typeof item.travel_time == "undefined") {
      item.travel_time = "";
    }

    let el_dom = "";

    item.tickets.forEach((tic) => {
      // 处理剩余票数逻辑
      tic.remain_tickets = tic.remain_tickets;

      let doms_ = "";

      // 判断是否过期
      if (tic.is_expire === 1) {
        doms_ =
          '<span class="fz24 c85" style="white-space:nowrap;">售罄</span>';
      } else {
        // 根据 `is_remain_tickets_control` 确定余票显示逻辑
        if (tic.is_remain_tickets_control === 0) {
          // 不隐藏余票信息
          if (tic.remain_tickets === -1) {
            doms_ = '<span class="fz24 c2e">有票</span>';
          } else if (tic.remain_tickets === 0) {
            doms_ = '<span class="fz24 c85">已售罄</span>';
          } else {
            doms_ = `
              <span class="fz24 ${tic.remain_tickets < 5 ? "cff" : "c2e"}">
                余<label class="fz24 ${
                  tic.remain_tickets < 5 ? "cff" : "c2e"
                }">${tic.remain_tickets}张</label>
              </span>`;
          }
        } else {
          // 隐藏余票信息
          if (tic.remain_tickets === -1) {
            doms_ = '<span class="fz24 c2e">有票</span>';
          } else if (tic.remain_tickets === 0) {
            doms_ = '<span class="fz24 c85">已售罄</span>';
          } else if (tic.remain_tickets <= 5) {
            doms_ = `
              <span class="fz24 cff">
                余<label class="fz24 cff">${tic.remain_tickets}张</label>
              </span>`;
          } else {
            doms_ = '<span class="fz24 c2e">有票</span>';
          }
        }
      }

      // 拼接最终的 DOM 结构
      el_dom += `
        <div 
          index="${tic.index}" 
          class="item-time-in ${
            tic.start_time_type === 1 ? "item-time-in-custom" : ""
          } ${
        tic.is_expire === 1 || tic.remain_tickets === 0 ? "hadover" : ""
      }">
          <span class="fz28 c2e block fb time-in">
            ${
              tic.start_time_type === 2
                ? `${tic.start_earliest_time}-${tic.end_latest_time}`
                : tic.start_time
            }
          </span>
          ${doms_}
        </div>`;
    });

    const line_class_ferry = $("#line_class_ferry").val();
    result += `<div  class="item-new-trainticket" >
          <div class="main-ticket-info">
              <div class="middle flexcon tl">
                <div class="lineCanct">
                  <div class="dot1"></div>
                  <div class="dot2"></div>
                  <div class="dot3"></div>
                </div>
                <div class="middle-line-name">
                <span class="fz32 fb c2e overhidden">${item.start_name}</span>
                  ${
                    item.start_address_type == 2
                      ? `<span class="point-tag">上门接</span>`
                      : ""
                  }
                ${
                  line_class_ferry == 1 && item.is_start_ferry == 1
                    ? `<span class="point-tag">摆渡车</span>`
                    : ""
                }
                </div>
                  ${
                    item.via_name
                      ? `<span class="fz24 c5d middle-point" >途径：${item.via_name}</span>`
                      : ""
                  }
                <div  class="middle-line-name">
                <span class="fz32 fb c2e overhidden">${item.end_name}</span>
                  ${
                    item.end_address_type == 2 &&
                    item.line_class.hide_songdaojia_keyword == false
                      ? `<span class="point-tag">送到家</span>`
                      : ""
                  }
                  ${
                    line_class_ferry == 1 && item.is_end_ferry == 1
                      ? `<span class="point-tag">摆渡车</span>`
                      : ""
                  }
                  
                </div>

              </div>
              <div class="right flexcon tr">
                <span class="price-txt fz32">
                  <span class="fz24">￥</span>
                    <span class="fb price-txt-label">${
                      item.is_seat_selection == 0
                        ? item.price
                        : item.min_seat_price
                    }</span>	
                    ${
                      item.is_seat_selection == 1
                        ? `<span  class="fz24">起</span>`
                        : ""
                    }
                </span>
                <div>
                <div class="ex-info">
                      ${
                        item.support_points_deduction == 1
                          ? `<div class="cds-tag">积分抵扣</div>`
                          : ""
                      }
                      ${
                        item.is_seat_selection == 1
                          ? `<div class="cds-tag">可选座</div>`
                          : ""
                      }
                </div>
                  <div class="ex-info">
                      ${
                        !Array.isArray(item.driver)
                          ? `<div class="cds-tag">${item.driver.car_tail_number}</div>`
                          : ""
                      }
                </div>
                    <div class="ex-info">
                    ${
                      !Array.isArray(item.driver)
                        ? `<div class="un-seat">${item.driver.name}</div>`
                        : ""
                    }
                </div>
                </div>
              </div>
              </div>

              <div class="time-select-content  ${
                item.start_time_type == 1 ? "flex-content" : ""
              }"  >
              ${el_dom}
              </div>
              <div class="more-info-mind-txt">
                <span class="depart-type">${
                  item.start_time_type == 2 ? "滚动发车" : ""
                }</span>
                ${
                  item.start_time_type == 2 && item.line_class.travel_time
                    ? `<span class="depart-type"> | </span>`
                    : ""
                }
                ${
                  item.line_class.travel_time
                    ? `<span  class="depart-type">${item.line_class.travel_time}</span>`
                    : ""
                }
              </div>
            </div>`;
  }
  return result;
}

function GetRequest() {
  var url = location.search; //获取url中"?"符后的字串
  var theRequest = new Object();
  if (url.indexOf("?") != -1) {
    var str = url.substr(1);
    strs = str.split("&");
    for (var i = 0; i < strs.length; i++) {
      theRequest[strs[i].split("=")[0]] = decodeURIComponent(
        strs[i].split("=")[1]
      );
    }
  }
  return theRequest;
}

//预估快车价格
function getFastPrice(lineObj) {
  $.ajax({
    url:
      "/Passenger/Order/doMobileComputeLineFastPrice?callback=" +
      sessionStorage.getItem("callback"),
    type: "post",
    dataType: "json",
    data: lineObj,
    success: function (n) {
      if (n.ret) {
        $(".point-info-view-warp").css("display", "none");
        $(".confirm-set-car").attr("fastId", n.data.line_fast_id);
        $(".confirm-set-car").attr("scode", lineObj.start_address_code);
        $(".confirm-set-car").attr("ecode", lineObj.end_address_code);
        $(".confirm-set-car").attr("distance", n.data.rules.distance);
        $(".confirm-set-car").attr("duration", n.data.rules.duration);
        $(".item-fast-car-seat label").html(n.data.price);
        $(".base_price").html(n.data.rules.base_price + "元");
        $(".distance-label").html(`里程费(${n.data.rules.distance}公里)`);
        $(".mileage_price").html(n.data.rules.mileage_price + "元");
        $(".duration-label").html(
          `时长费(${distanceGetTime(n.data.rules.duration)})`
        );
        $(".duration_price").html(n.data.rules.duration_price + "元");
        $(".longdistance_price").html(n.data.rules.longdistance_price + "元");
        $(".price-price").html(n.data.price);

        $(".item-fast-car-seat .left-seat").html(`
        <img class="car-seat-icon" src="/Theme/images/passenger/carpool.png" alt="" srcset="">
        <div class="desc-label">
          <b>快车-经济型</b>
        </div>`);
        $(".cover-load").hide();
      } else {
        lui.showModal({
          content: n?.data || "出错了，请稍后再试",
          confirm: function () {
            closeConfirmOrder();
          },
        });
      }
    },
  });
}

function openFastPop() {
  const el = document.querySelector("#j-popup-half-comfirm");
  var style = window.getComputedStyle(el); //el即DOM元素
  if (style.display === "none") {
    $(".format-bg").attr("class", "format-bg");
    set_time_space("00:00", "24:00");
    $("#j-popup-half-comfirm #picker5").html(
      `<span class="default-info">现在出发</span>`
    );
    $("#j-popup-half-comfirm #picker5").attr(`current_date`, "");

    $(`.fast-fare`).html(`<span class="default-info">代叫</span>`);
    $(`.fast-fare`).attr("reseverd_tel", $("#cellphone-data").val());

    $(".has-message").attr("reseverd_info", "");
    $(".has-message").html('<span class="default-info">行程备注</span>');

    togglePopupExpress(false, "#j-popup-half-comfirm");
  }
}

//预估出租车价格
function getTaxiPrice(lineObj) {
  $.ajax({
    url:
      "/Passenger/Order/doMobileComputeLineTaxiPrice?callback=" +
      sessionStorage.getItem("callback"),
    type: "post",
    dataType: "json",
    data: lineObj,
    success: function (n) {
      if (n.ret) {
        $(".point-info-view-warp").css("display", "none");
        $(".confirm-set-car").attr("taxiId", n.data.line_taxi_id);
        $(".confirm-set-car").attr("scode", lineObj.start_address_code);
        $(".confirm-set-car").attr("ecode", lineObj.end_address_code);
        $(".confirm-set-car").attr("distance", n.data.rules.distance);
        $(".confirm-set-car").attr("duration", n.data.rules.duration);
        $(".item-fast-car-seat label").html(n.data.price);
        $(".base_price").html(n.data.rules.base_price + "元");
        $(".distance-label").html(`里程费(${n.data.rules.distance}公里)`);
        $(".mileage_price").html(n.data.rules.mileage_price + "元");
        $(".duration-label").html(
          `时长费(${distanceGetTime(n.data.rules.duration)})`
        );
        $(".duration_price").html(n.data.rules.duration_price + "元");
        $(".longdistance_price").html(n.data.rules.longdistance_price + "元");
        $(".price-price").html(n.data.price);
        $(".item-fast-car-seat .left-seat").html(`
        <img class="car-seat-icon" src="/Theme/images/passenger/taxi.png" alt="" srcset="">
        <div class="desc-label">
          <b>出租车</b>
        </div>`);
        $(".cover-load").hide();
      } else {
        lui.showModal({
          content: n?.data || "出错了，请稍后再试",
          confirm: function () {
            closeConfirmOrder();
          },
        });
      }
    },
  });
}

function getFujiaPrice(
  type,
  ferry_longitude,
  ferry_latitude,
  longitude,
  latitude
) {
  lui.loading();
  $.ajax({
    url:
      "/Passenger/Order/doMobileComputeLineClassPrice?callback=" +
      sessionStorage.getItem("callback"),
    type: "post",
    dataType: "json",
    data: {
      ferry_longitude: ferry_longitude,
      ferry_latitude: ferry_latitude,
      longitude: longitude,
      latitude: latitude,
    },
    success: function (n) {
      lui.loading("close");
      if (n.ret) {
        var sum_price = n.data.price + "元";
        if (type == "fstart") {
          $(".shangche .fujiamoney").attr("money", n.data.price);
          $(".shangche .fujiamoney").html(sum_price);
          $(".shangche").css("visibility", "visible");
          var tprice = $(".cost-price").attr("pay_price");
          var fprice = $(".xiache .fujiamoney").attr("money") || 0;
          $(".total-price").html(
            (n.data.price + Number(tprice) + Number(fprice)).toFixed(2)
          );
        } else {
          $(".xiache .fujiamoney").html(sum_price);
          $(".xiache .fujiamoney").attr("money", n.data.price);
          $(".xiache").css("visibility", "visible");
          var tprice = $(".cost-price").attr("pay_price");
          var fprice = $(".shangche .fujiamoney").attr("money") || 0;
          $(".total-price").html(
            (n.data.price + Number(tprice) + Number(fprice)).toFixed(2)
          );
        }
      } else {
        ///////需要重置地图，待完善
        lui.toast({
          position: "middle",
          text: n.data,
        });
      }
    },
    error: function () {
      lui.loading("close");
    },
  });
}

//格式化时间
function fix(num, length) {
  return ("" + num).length < length
    ? (new Array(length + 1).join("0") + num).slice(-length)
    : "" + num;
}
var obj = GetRequest("show");
if (obj.show == 1) {
  sessionStorage.setItem("showcontrol", 1);
}
//获取通用优惠券
function fetchCoupons() {
  $.ajax({
    url:
      "/Passenger/Coupon/getCoupons?page=1&size=20&callback=" +
      sessionStorage.getItem("callback"),
    type: "get",
    success: function (json) {
      var json = JSON.parse(json);
      if (json.ret) {
        let getlist = [];
        let res_ = "";
        for (var i = 0; i < json.data.length; i++) {
          getlist.push(json.data[i].coupon_record_id);
          const item = json.data[i];
          res_ += `<div class="item-red-pop">
          <div class="left-red">
          ${item.type == 1 ? `<span class="uni-label">￥</span>` : ""}
            <span class="red-value">${item.value}</span>
          ${item.type == 2 ? `<span class="uni-label">折</span>` : ""}
          </div>
          <div class="right-red">
            <p class="red-name">${item.name}</p>
            <p class="avi-time">${item.end_time
              .slice(0, 10)
              .replace("-", ".")
              .replace("-", ".")} 后过期</p>
          </div>
         </div>`;
        }
        $(".scroll-red-list").html(res_);
        $(".pop-red-content").attr("coupon_record_ids", getlist.join(","));
        togglePopupAd("#j-popup-ad");
      }
    },
    error: function (json) {},
    timeout: 300000,
  });
}
$(function () {
  $(document).on("click", ".action-agreement", function () {
    $(".agreement-full-cover").hide();
  });
  var user_phone = $("#cellphone-data").val();
  if (user_phone != "" || typeof user_phone != "undefined") {
    $("#user_phone").val(user_phone);
  }
  $(".close_coupon").click(function () {
    $(".coupon-cover").hide();
  });
  //领取
  $(".recvied-red-pack").click(function () {
    $.ajax({
      url:
        "/Passenger/Coupon/doReceiveCoupon?callback=" +
        sessionStorage.getItem("callback"),
      type: "post",
      data: {
        coupon_record_ids: $(".pop-red-content").attr("coupon_record_ids"),
      },
      success: function (json) {
        var json = JSON.parse(json);
        if (json.ret) {
          lui.toast({
            position: "middle",
            text: "领取成功",
          });
          closeAd();
        } else {
          lui.toast({
            position: "middle",
            text: "出错了，稍候再试",
          });
        }
      },
      error: function (json) {
        lui.toast({
          position: "middle",
          text: "出错了，稍候再试",
        });
      },
      timeout: 300000,
    });
  });
  //领取新人优惠
  $(document).on("click", ".onClosePopup", function () {
    $.ajax({
      url:
        "/Passenger/Coupon/doReceiveCoupon?callback=" +
        sessionStorage.getItem("callback"),
      type: "post",
      data: {
        coupon_record_ids: $(".new-user-coupons-popup").attr(
          "coupon_record_ids"
        ),
      },
      success: function (json) {
        var json = JSON.parse(json);
        if (json.ret) {
          $(".new-user-coupons-popup").hide();
          fetchCoupons(); //领取新人优惠,获取普通优惠券
        } else {
          lui.toast({
            position: "middle",
            text: json.data || "参数异常",
          });
        }
      },
      timeout: 300000,
    });
  });

  //获取优惠券
  //优先判断是否有新人优惠券
  $.ajax({
    url:
      "/Passenger/Coupon/getCoupons?page=1&size=20&type=5&callback=" +
      sessionStorage.getItem("callback"),
    type: "get",
    success: function (json) {
      var json = JSON.parse(json);
      if (json.ret) {
        const html = `<span>${json.data[0].value}</span>${
          json.data[0].type == 1 ? "元" : "折"
        }`;
        $(".use-value").html(html);
        $(".new-user-coupons-popup").attr(
          "coupon_record_ids",
          json.data[0].coupon_record_id
        );
        $(".new-user-coupons-popup").show();
      } else {
        fetchCoupons(); //没有新人优惠,获取普通优惠券
      }
    },
    error: function (json) {},
    timeout: 300000,
  });
});
//支付
function onBridgeReady(appId, timetamp, nonceStr, package, paySign, order_id) {
  lui.loading();

  WeixinJSBridge.invoke(
    "getBrandWCPayRequest",
    {
      appId: appId, //公众号名称，由商户传入
      timeStamp: timetamp, //时间戳，自1970年以来的秒数
      nonceStr: nonceStr, //随机串
      package: package,
      signType: "MD5", //微信签名方式：
      paySign: paySign, //
    },
    function (res) {
      lui.loading("close");
      if (res.err_msg == "get_brand_wcpay_request:ok") {
        //支付成功
        togglePopupAd("#j-popup-pay");
        getOrderDetail(order_id);
      } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
        lui.toast({
          type: "warning",
          text: "取消支付",
        });
        setTimeout(() => {
          window.location.href =
            "/passenger_order_detail?order_id=" +
            order_id +
            "&callback=" +
            sessionStorage.getItem("callback") +
            "&show=1";
        }, 1500);
        // 取消支付
      } else {
        lui.toast({
          type: "warning",
          text: "支付失败，请重试",
        });
        setTimeout(() => {
          window.location.href =
            "/passenger_order_detail?order_id=" +
            order_id +
            "&callback=" +
            sessionStorage.getItem("callback") +
            "&show=1";
        }, 1500);
      } // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回    ok，但并不保证它绝对可靠。
    }
  );
}
//查看航班信息
$(document).on(" ", ".showflight", function () {
  var num = $(this).attr("num");
  $.ajax({
    url:
      "/Home/Common/getFightDetail?ft_number=" +
      num +
      "&callback=" +
      sessionStorage.getItem("callback"),
    type: "get",
    success: function (json) {
      var stl = "";
      var json = JSON.parse(json);
      if (json.ret) {
        for (var i = 0; i < json.data.length; i++) {
          stl +=
            '<li style="display: block;padding: 10px;font-size: 16px;">\n' +
            '<p style="color: #1C73E2"><span style="width: 80px;display: inline-block; margin-right: 20px; color: #333;">航班:</span>' +
            json.data[i].flightNo +
            "</p>\n" +
            '<p style="color: #1C73E2"><span style="width: 80px;display: inline-block; margin-right: 20px; color: #333;">出发地:</span>' +
            json.data[i].from +
            "</p>\n" +
            '<p style="color: #1C73E2"><span style="width: 80px;display: inline-block; margin-right: 20px; color: #333;">到达地:</span>' +
            json.data[i].to +
            "</p>\n" +
            '<p style="color: #1C73E2"><span style="width: 80px;display: inline-block; margin-right: 20px;color: #333;">出发时间:</span>' +
            json.data[i].planTime +
            "</p>\n" +
            '<p style="color: #1C73E2"><span style="width: 80px;display: inline-block; margin-right: 20px;color: #333;">到达时间:</span>' +
            json.data[i].planArriveTime +
            "</p>\n" +
            "</li>";
        }
        $(".flight-all").html(stl);
        $(".flight-page").show();
      } else {
        lui.toast({
          position: "middle",
          text: "暂无航班信息",
        });
      }
    },
    error: function (json) {},
    timeout: 300000,
  });
});
$(".close-flight").on("click", function () {
  $(".flight-page").hide();
});
$(document).on("click", ".copy", function () {
  //复制
  var content = $(".showflight").attr("num");
  var clipboard = new Clipboard(".copy", {
    text: function () {
      return content;
    },
  });
  clipboard.on("success", function (e) {
    lui.toast({
      position: "middle",
      text: "复制成功",
    });
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });
  document.querySelector(".copy").click();
});

function infoChange(event, i, type) {
  seat_lists[i].passenger[type] = event.value;
}

//渲染选座保险表单信息
    function renderPssengerInfo() {
  // 确保seat_lists已初始化
  if (!window.seat_lists) {
    window.seat_lists = [];
  }
  
  const is_seat_selection = $(".train-check-order-bt").attr(
    "is_seat_selection"
  );
  if (is_seat_selection == 1) {
    const seatDoms = $(".seat-container").find(".item-seat");
    for (let i = 0; i < seatDoms.length; i++) {
      // 确保seat_lists[i]存在
      if (!seat_lists[i]) {
        seat_lists[i] = {};
      }
      
      if ($(seatDoms[i]).hasClass("checked")) {
        seat_lists[i]["checked"] = true;
      } else {
        seat_lists[i]["checked"] = false;
        seat_lists[i]["passenger"] = { real_name: "", ID_number: "", passenger_type: "" };
      }
    }
  }

  //后台设置乘车是否需要实名制
  const id_upload_config = $("#id_upload_config").val();
  if (id_upload_config == 1) {
    let finalHmtl = "";
    let formHmtl = "";
    seat_lists.map((o, index) => {
      if (o.checked) {
        const itemInsurance = `<div class="user-warp">
    <div class="btn-warp">
      ${
        is_seat_selection == 1
          ? `<div class="del-passenger-info" index="${index}">
        <img  src="/Theme/images/passenger/del-user.png" alt=""  />
      </div>`
          : ""
      }
       ${
         o.name
           ? `<div class="seat-name-warp">座位${o.name}</div>`
           : `<div class="seat-name-warp oneline-pice">乘车人${index + 1}</div>`
       }
    </div>
  <div class="center-warp">
  <div class="item-user-info name-input">
    <span class="label">姓名</span>
    <div style="position: relative; width: 100%; display: flex; align-items: center;">
      <input type="text" placeholder="请输入真实姓名" oninput="infoChange(this,${index},'real_name')" value="${
          o.passenger.real_name
        }"  class="flight_input insuranceName" style="font-size: 14px; width: calc(100% - 60px);" />
      ${o.passenger.passenger_type ? `<span style="margin-left: 5px; font-size: 12px; color: #0055BD; background-color: #EEF4FF; padding: 2px 6px; border-radius: 4px;">${
          o.passenger.passenger_type == '2' || o.passenger.passenger_type == 'child' ? '儿童票' :
          o.passenger.passenger_type == '1' || o.passenger.passenger_type == 'student' ? '学生票' :
          o.passenger.passenger_type == '3' || o.passenger.passenger_type == 'disabled-soldier' ? '残疾军人票' :
          '成人票' // Default to 成人票
      }</span>` : ''}
    </div>
  </div>
  <div class="item-user-info">
    <span class="label">身份证号</span>
    <input type="text" placeholder="请输入身份证号" oninput="infoChange(this,${index},'ID_number')"value="${
          o.passenger.ID_number
        }" class="flight_input insuranceIdcard" style="font-size: 14px;" />
  </div>
  </div>
  <div class="right-warp" >
     <div index="${index}" class="select-btn">选择</div>
  </div>
 </div>`;
        formHmtl += itemInsurance;
        console.log("[Debug] RenderPssengerInfo - Displaying:", o.passenger.real_name, "Seat Name:", o.name, "Stored Type:", o.passenger.passenger_type);
      }
    });
    const contentHmtl = `
  <div class="insurance-user isShow">
    <div class="user-info-content">
    ${formHmtl}
    </div>
  </div>`;
    finalHmtl = formHmtl ? contentHmtl : "";
    $(".insurance-form").html(finalHmtl);
    formHmtl ? $(".passenger-cards").show() : $(".passenger-cards").hide();
  } else {
    //未开启实名制，无需乘客信息
    $(".insurance-form").html("");
    $(".passenger-cards").hide();
  }
}
//价格显示转换
function Money(value, num = 2, isminus = false) {
  if (Number(value) <= 0 && !isminus) {
    return "0.00";
  }
  num = num > 0 && num <= 20 ? num : 2;
  value = parseFloat((value + "").replace(/[^\d\.-]/g, "")).toFixed(num) + ""; //将金额转成比如 123.45的字符串
  var valueArr = value.split(".")[0].split("").reverse(); //将字符串的数变成数组
  const valueFloat = value.split(".")[1]; // 取到 小数点后的值
  let valueString = "";
  for (let i = 0; i < valueArr.length; i++) {
    valueString +=
      valueArr[i] +
      ((i + 1) % 3 == 0 && i + 1 != valueArr.length && valueArr[0] !== "-"
        ? ","
        : ""); //循环 取数值并在每三位加个','
  }
  const money = valueString.split("").reverse().join("") + "." + valueFloat; //拼接上小数位
  return money;
}

$(function () {
  $(document).on("click", ".toggle-button", function () {
    $(this).toggleClass("active");
    if ($(this).hasClass("active")) {
      $(this).html(`<i class="icon-down-up"></i> 隐藏途径站点`);
      $(this).next().show();
    } else {
      $(this).html(`<i class="icon-down-up"></i> 途径站点`);
      $(this).next().hide();
    }
  });

  $(document).on("click", ".more-sence", function () {
    $(this).toggleClass("hasShow");
    if ($(this).hasClass("hasShow")) {
      $(this).html('收起 <i class="arrow-down"></i>');
      $(".more-dom").show();
    } else {
      $(this).html('更多 <i class="arrow-down"></i>');
      $(".more-dom").hide();
    }
  });
  $(".popup-confirm-btn").on("click", function () {
    $(".popup").hide();
  });
  $(document).on("click", ".insurance-desc", function () {
    $(".popup").attr("style", "display: flex;");
  });

  //是否需要保险点击事件
  $(document).on("click", ".item-radio", function () {
    $(this).addClass("checked");
    $(this).siblings().removeClass("checked");
    priceCalculation(true);
  });
  //选座点击事件
  $(document).on("click", ".item-seat", function () {
    if ($(this).hasClass("disabled")) {
      return;
    }
    if ($(this).hasClass("checked")) {
      $(this)
        .find(".seat-icon")
        .attr("src", "/Theme/images/passenger/seat-select.svg");
    } else {
      $(this)
        .find(".seat-icon")
        .attr("src", "/Theme/images/passenger/seat-checked.svg");
    }
    $(this).toggleClass("checked");
    renderPssengerInfo();
    priceCalculation();
    let result = "";
    let ids = [];
    $(".item-seat[name]").each(function () {
      if ($(this).hasClass("checked")) {
        result += ` ${$(this).attr("name")}`;
        ids.push($(this).attr("seat_id"));
      }
    });

    $(".showCheck").html(`<span>已选座位：</span>${result}`);
    $(".toPay").attr("seat_ids", ids.join(","));
  });

  //选择乘车人
  $(document).on("click", ".item-passanger .check-laebl-info", function () {
    if ($(this).parent().hasClass("disabled")) return;
    const index = $("#j-popup-half-passenger").attr("index");
    const passenger_ = {
      real_name: $(this).attr("real_name"),
      ID_number: $(this).attr("ID_number"),
      passenger_member_id: $(this).attr("uid"),
      passenger_type: $(this).attr("passenger_type") || "adult",
    };
    console.log("[Debug] Passenger Clicked - Attribute Type:", $(this).attr("passenger_type"), "Saved Passenger Obj:", passenger_);
    seat_lists[index].passenger = passenger_;
    togglePopupExpress(true, "#j-popup-half-passenger");
    renderPssengerInfo();
  });
  $(".pop-back").on("click", function () {
    $(".pop-edit-page").hide();
  });
  $(document).on("click", ".add-warp", function () {
    $(".confirm-edit").attr("editStatus", "add");
    $(".pop-edit-page").show();
  });

  // passenger-popup

  $(document).on("click", ".select-btn", function () {
    $("#j-popup-half-passenger").attr("index", $(this).attr("index"));
    const pHtml = ` <div class="passanger-info-warp">
                      <div class="add-warp">
                        <img class="close" src="/Theme/images/passenger/add-warp.png" alt="">
                        <span>添加乘车人信息</span>
                      </div>
                      <div class="passenger-full-warp">
                      </div>
                    </div>`;
    $(".passenger-popup").html(pHtml);
    renderPassenger();
    togglePopupExpress(true, "#j-popup-half-passenger");
  });

  $(document).on("change", "#card-type", function () {
    const cardType = $(this).val();
    const allMap = [
      "edit-xing",
      "edit-ming",
      "edit-validity",
      "edit-xing-en",
      "edit-ming-en",
    ];
    const typeMap = {
      "second-generation-identity-card": [],
      passport: ["edit-xing", "edit-ming", "edit-validity"],
      "taiwan-passport": ["edit-xing", "edit-ming", "edit-validity"],
      "hong-kong-passport": ["edit-xing", "edit-ming", "edit-validity"],
      "military-certificate": ["edit-xing", "edit-ming", "edit-validity"],
      "household-register": ["edit-xing", "edit-ming"],
      "birth-certificate": ["edit-xing", "edit-ming"],
      "hong-kong-taiwan-residence-permit": ["edit-validity"],
      "foreign-residence-permit": [
        "edit-xing-en",
        "edit-ming-en",
        "edit-validity",
      ],
    };
    // 获取allMap中的Dom, 如果没有在typeMap[cardType]中, 则隐藏
    allMap.forEach((dom) => {
      if (typeMap[cardType].includes(dom)) {
        console.log($(dom));
        $(`#${dom}`).show();
      } else {
        $(`#${dom}`).hide();
      }
    });
  });

  $(document).on("click", ".del-passenger", function () {
    if ($(this).parent().parent().hasClass("disabled")) return;
    const this_ = $(this);
    lui.showModal({
      content: "确认删除该乘车人信息？",
      showCancel: true,
      cancel: function () {
        console.log("cancel");
      },
      confirm: function () {
        lui.loading();
        $.ajax({
          url:
            `/Passenger/PassengerMember/doDelPassengerMember?callback=` +
            sessionStorage.getItem("callback"),
          type: "post",
          data: {
            passenger_member_id: this_.attr("passenger_member_id"),
          },
          success: function (json) {
            lui.loading("close");
            const res = JSON.parse(json);
            if (res.ret) {
              renderPassenger();
            } else {
              lui.toast({
                position: "middle",
                text: res.data,
              });
            }
          },
        });
      },
    });
  });
  //取消选择乘客信息
  $(document).on("click", ".del-passenger-info", function () {
    const is_seat_selection = $(".toPay").attr("is_seat_selection");
    const index_ = $(this).attr("index");

    if (is_seat_selection == 1) {
      //支持选座

      const seatDoms = $(".seat-container").find(".item-seat");
      $(seatDoms[index_]).removeClass("checked");
      const seat_id = seat_lists[index_].seat_id;
      let currents = $(".toPay").attr("seat_ids").split(",");
      currents.splice(
        currents.findIndex((o) => o == seat_id),
        1
      );
      $(".toPay").attr("seat_ids", currents.join(","));
      seat_lists[index_]["checked"] = false;
      seat_lists[index_].passenger = {
        real_name: "",
        ID_number: "",
      };
    } else {
      seat_lists.splice(index_, 1);
      $(".ticket_number_view").html(seat_lists.length);
    }
    renderPssengerInfo();
    priceCalculation();
  });

  $(document).on("click", ".edit-passenger", function () {
    if ($(this).parent().parent().hasClass("disabled")) return;
    $(".confirm-edit").attr({
      editStatus: "edit",
      passenger_member_id: $(this).attr("passenger_member_id"),
    });
    $("#edit-name").val($(this).attr("real_name"));
    $("#edit-idcard").val($(this).attr("ID_number"));
    $(".pop-edit-page").show();
  });

  $(document).on("click", ".confirm-edit", function () {
    // 0-成人票 1-学生票 2-儿童票 3-残疾军人票
    const enumsPassengerType = {
      adult: 0,
      student: 1,
      child: 2,
      "disabled-soldier": 3,
    };
    // 0-二代身份证 1-护照 2-台胞证 3-回乡证 4-军人证 5-户口簿 6-出生证 7-港澳台居民居住证 8-外国人永久居留身份证
    const enumsCardType = {
      "second-generation-identity-card": 0,
      passport: 1,
      "taiwan-passport": 2,
      "hong-kong-passport": 3,
      "military-certificate": 4,
      "household-register": 5,
      "birth-certificate": 6,
      "hong-kong-taiwan-residence-permit": 7,
      "foreign-residence-permit": 8,
    };
    //新增/编辑
    const editStatus = $(this).attr("editStatus");
    let surname = $("#edit-xing-input").val();
    let given_name = $("#edit-ming-input").val();
    if ($("#card-type").val() == "foreign-residence-permit") {
      surname = $("#edit-xing-en-input").val();
      given_name = $("#edit-ming-en-input").val();
    }
    let params = {
      passenger_type: enumsPassengerType[$("#passenger-type").val()],
      id_type: enumsCardType[$("#card-type").val()],
      id_context: {
        surname: surname,
        given_name: given_name,
        valid_until: $("#edit-validity").val(),
      },
      real_name: $("#edit-name").val(),
      ID_number: $("#edit-idcard").val(),
      cellphone: $("#edit-phone").val(),
      email: $("#edit-email").val(),
    };

    // 获取乘客类型
    const passengerType = $("#passenger-type").val() || "";
    const isChildTicket = passengerType === "child";
    // 修改验证逻辑：儿童票时允许证件号码为空
    if (!$("#edit-name").val() || (!isChildTicket && !$("#edit-idcard").val())) {
      lui.toast({
        position: "middle",
        text: "请输入完整信息",
      });
      return;
    }
    if (editStatus === "edit") {
      params["passenger_member_id"] = $(this).attr("passenger_member_id");
    }
    lui.loading();
    $.ajax({
      url:
        `${
          editStatus === "add"
            ? "/Passenger/PassengerMember/doAddPassengerMember"
            : "/Passenger/PassengerMember/doEditPassengerMember"
        }?callback=` + sessionStorage.getItem("callback"),
      type: "post",
      data: params,
      success: function (json) {
        lui.loading("close");

        const res = JSON.parse(json);
        if (res.ret) {
          lui.toast({
            position: "middle",
            text: "操作成功",
          });

          $(".pop-edit-page").hide();
          $("#edit-name").val("");
          $("#edit-idcard").val("");
          renderPassenger();
        } else {
          if (res.data) {
            lui.toast({
              position: "middle",
              text: res.data,
            });
          }
        }
      },
    });
  });

  $(document).on("change", "#point-used", function () {
    priceCalculation();
    if (this.checked) {
      console.log("Checkbox is checked");
      // 在这里执行选中时的处理逻辑
      $(".point-money-cell").show();
    } else {
      console.log("Checkbox is not checked");
      // 在这里执行未选中时的处理逻辑
      $(".point-money-cell").hide();
    }
  });
});

function renderPassenger() {
  lui.loading();
  const emtpyHtml = ` <div class="passanger-info-container">
  <div class="empty-view">
    <img class="empty-img" src="/Theme/images/passenger/empty-c.png" alt="">
    <span>暂无信息</span>
  </div>
  </div>`;

  $.ajax({
    url:
      `/Passenger/PassengerMember/getPassengerMembers?callback=` +
      sessionStorage.getItem("callback"),
    type: "get",
    data: {
      page: 1,
      size: 50,
    },
    success: function (json) {
      lui.loading("close");
      const res = JSON.parse(json);
      if (res.ret) {
        if (res.data.length > 0) {
          let list_Html = "";
          console.log("seat_listsseat_lists", seat_lists);
          for (let i = 0; i < res.data.length; i++) {
            console.log("[Debug] RenderPassenger - Passenger:", res.data[i].real_name, "Raw Type:", res.data[i].passenger_type, "Raw CN Type:", res.data[i].cn_passenger_type);
            // 获取乘客类型
            let passenger_type = res.data[i].passenger_type || "adult";
            let passenger_type_text = res.data[i].cn_passenger_type || (res.data[i].passenger_type === "child" ? "儿童票" : res.data[i].passenger_type === "student" ? "学生票" : res.data[i].passenger_type === "disabled-soldier" ? "残疾军人票" : "成人票");

            list_Html += `<div class="item-passanger ${
              seat_lists.findIndex(
                (o) =>
                  o.passenger.passenger_member_id ==
                  res.data[i].passenger_member_id
              ) > -1
                ? "disabled"
                : ""
            }">
          <div class="check-laebl-info"  real_name="${
            res.data[i].real_name
          }" ID_number="${res.data[i].ID_number}" uid="${
              res.data[i].passenger_member_id
            }" passenger_type="${passenger_type}">
            <p class="name">${res.data[i].real_name} 
            <span class="passenger-type" style="display:inline-block;margin-left:5px;font-size:12px;color:#0055BD;background-color:#EEF4FF;padding:2px 6px;border-radius:4px;">${passenger_type_text}</span>
            ${res.data[i].is_self ? '<span class="is-self">本人</span>' : ""}
            </p>
            <p class="idcard">${res.data[i].ID_number}</p>
          </div>
          <div class="action-img">
          <div class="edit-passenger"   passenger_member_id="${
            res.data[i].passenger_member_id
          }" ID_number="${res.data[i].ID_number}" real_name="${
              res.data[i].real_name
            }">
            <img src="/Theme/images/passenger/edit-p.png"  alt="">
            </div>
          <div  class="del-passenger"  passenger_member_id="${
            res.data[i].passenger_member_id
          }" ID_number="${res.data[i].ID_number}" real_name="${
              res.data[i].real_name
            }"  >
           <img src="/Theme/images/passenger/del-p.png" class="del-passenger"  alt="">
           </div>
           </div>
        </div>`;
          }
          const content_Html = ` <div class="passanger-info-warp-list">${list_Html}
          </div>`;
          $(".passenger-full-warp").html(content_Html);
        }
      } else {
        if (res.data) {
          lui.toast({
            position: "middle",
            text: res.data,
          });
        } else {
          $(".passenger-full-warp").html(emtpyHtml);
        }
      }
    },
  });
}

//保险配置
function renderInsurance() {
  $.ajax({
    url:
      `/Home/Common/getInsuranceConfig?callback=` +
      sessionStorage.getItem("callback"),
    type: "get",
    success: function (json) {
      const res = JSON.parse(json);
      if (res.ret) {
        const { insurance, config } = res.data;
        sessionStorage.setItem("insurance", JSON.stringify([config]));

        if (insurance) {
          const insurance_Html = `<div class="final-payinfo confirm-warp-cell">
              <div class="insurance-container-warp" insurance="${
                insurance ? 1 : 0
              }">
            <div class="insuranceCheck"  insurance_amount="${
              config.insurance_amount
            }">
                  <div class="check-warp">
                    <span class="warp-label">${config.insurance_name}</span>
                    <div class="radio-view">
                      <div class="item-radio" need="0">
                        <img class="dot-checked" src="/Theme/images/passenger/checked.png" alt="">
                        <img class="dot-check" src="/Theme/images/passenger/check.png" alt="">
                        <span class="label">不需要</span>
                      </div>
                      <div class="item-radio checked" need="1">
                        <img class="dot-checked" src="/Theme/images/passenger/checked.png" alt="">
                        <img class="dot-check" src="/Theme/images/passenger/check.png" alt="">
                        <span class="label">需要</span>
                      </div>
                    </div>
                  </div>
                  <div class="check-warp desc-warp-in">
                    <span class="insurance-desc">保险说明<img class="desc-check" src="/Theme/images/passenger/question.png" alt=""></span>
                    <div class="insurance_money_view">
                      ¥<i class="init-price">${config.insurance_amount} / 人</i>
                    </div>
                </div>
                </div>
                </div>`;
          $(".pop-insource").html(config.insurance_desc);
          $(".insurance-bussess").html(insurance_Html);
        } else {
          $(".insurance-bussess").html("");
        }
      } else {
        $(".insurance-bussess").html("");
      }
    },
  });
}

function getParamsWithUrl(url) {
  let params = {};
  // 是否是字符串
  if (Object.prototype.toString.call(url).slice(8, -1) != "String") {
    return params;
  }
  
  // 再处理查询参数
  // 是否存在参数字符串
  let paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return params;
  }
  // 将参数字符串以&分割成数组
  let paramsArr = paramsStr.split("&");
  // 循环处理，又以=分割， item.split('=')[0]作为key, item.split('=')[1]作为value
  paramsArr.forEach((item) => {
    if (item.split("=")[0] && item.split("=")[1])
      params[item.split("=")[0]] = item.split("=")[1];
  });
  return params;
}

function removeDepAndAfter(uri) {
  const url = new URL(uri);
  const path = url.pathname;
  const query = url.search;

  let newPath = path.includes("_dep")
    ? path.substring(0, path.indexOf("_dep"))
    : path;

  url.pathname = newPath;
  return url.toString();
}

function updateQueryStringParameter(uri, key, value) {
  // 如果不传值，则不修改URL
  if (!value) {
    // 如果是要删除路径中的参数，例如 /cid/123/_cbp/xxx 这样的格式
    // 对于路径型参数，我们需要特殊处理
    if (key === "cid") {
      const cidPathRegex = /\/cid\/(\d+)/i;
      uri = uri.replace(cidPathRegex, "");
    } else if (key === "_cbp") {
      const cbpPathRegex = /\/_cbp\/([^\/]+)/i;
      uri = uri.replace(cbpPathRegex, "");
    } else if (key === "driver_dep") {
      const depPathRegex = /\/_dep\/(\d+)/i;
      uri = uri.replace(depPathRegex, "");
    }

    // 对于查询参数，继续使用原来的方式删除
    var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
    if (uri.match(re)) {
      return uri.replace(re, "$1$2").replace(/&$/, "");
    }
    return uri;
  }

  // 首先处理查询参数（?key=value 或 &key=value 格式）
  var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
  var separator = uri.indexOf("?") !== -1 ? "&" : "?";

  // 检查URI中是否存在路径格式的同名参数
  const pathParamExists = (uri, k, v) => {
    if (k === "cid") {
      return uri.match(/\/cid\/(\d+)/i);
    } else if (k === "_cbp") {
      return uri.match(/\/_cbp\/([^\/]+)/i);
    } else if (k === "driver_dep") {
      return uri.match(/\/_dep\/(\d+)/i);
    }
    return null;
  };

  // 如果路径中已存在同名参数，则替换它
  const pathMatch = pathParamExists(uri, key, value);
  if (pathMatch) {
    if (key === "cid") {
      uri = uri.replace(/\/cid\/(\d+)/i, `/cid/${value}`);
    } else if (key === "_cbp") {
      uri = uri.replace(/\/_cbp\/([^\/]+)/i, `/_cbp/${value}`);
    } else if (key === "driver_dep") {
      uri = uri.replace(/\/_dep\/(\d+)/i, `/_dep/${value}`);
    }

    // 同时删除查询参数中的重复项
    if (uri.match(re)) {
      uri = uri.replace(re, "$1$2").replace(/&$/, "");
    }
    return uri;
  }

  // 如果查询参数中存在，则替换
  if (uri.match(re)) {
    return uri.replace(re, "$1" + key + "=" + value + "$2");
  } else {
    // 如果都不存在，则添加为查询参数
    return uri + separator + key + "=" + value;
  }
}

function fetchFeePrice(type, train_id, lng, lat) {
  lui.loading();
  return new Promise((resolve, reject) => {
    if (type == "start") {
      $.ajax({
        type: "post",
        url: `/Passenger/Order/doMobileComputeLineClassFerryPrice?callback=${sessionStorage.getItem(
          "callback"
        )}`,
        data: {
          line_class_train_id: train_id,
          ferry_start_longitude: lng,
          ferry_start_latitude: lat,
          ferry_end_longitude: "",
          ferry_type: 1,
          ferry_end_latitude: "",
        },
        success: function (data) {
          lui.loading("close");
          const data_ = JSON.parse(data);
          if (data_.ret) {
            $(".fee-content-start .fee-desc").html(
              `接至：<b>${data_.data.start_address_remark}</b>上车，<span class="fee-custom-price-view">+<b class="start-custom-price-fee">${data_.data.price}</b></span>元`
            );
            let fee_end = Number($(".fee-money-cell").attr("fee-start")) || 0;
            $(".fee-money").html(Money(data_.data.price + fee_end, 2));
            $(".fee-money-cell").attr("fee-start", data_?.data?.price || 0);
            $(".toPay").attr("fee_slng", data_.data.start_longitude);
            $(".toPay").attr("fee_slat", data_.data.start_latitude);
            $(".toPay").attr("fee_sdistance", data_.data.distance);
            $(".toPay").attr("fee_sduration", data_.data.duration);
            $(".toPay").attr("fee_splace", data_.data.start_address_remark);
            $(".toPay").attr("is_start_ferry_check", 1);
            $(".fee-money-cell").show();
            priceCalculation(true);
            resolve();
          } else {
            lui.toast({
              position: "middle",
              text: data_.data,
            });
            $(".fee-content-start .fee-desc").html("");
            $(".toPay").attr("fee_slng", "");
            $(".toPay").attr("fee_slat", "");
            $(".toPay").attr("fee_sdistance", "");
            $(".toPay").attr("fee_sduration", "");
            $(".toPay").attr("fee_splace", "");
            $(".toPay").attr("is_start_ferry_check", 0);
            priceCalculation(true);
            reject();
          }
        },
      });
    } else {
      $.ajax({
        type: "post",
        url: `/Passenger/Order/doMobileComputeLineClassFerryPrice?callback=${sessionStorage.getItem(
          "callback"
        )}`,
        data: {
          line_class_train_id: train_id,
          ferry_start_longitude: "",
          ferry_type: 2,
          ferry_start_latitude: "",
          ferry_end_longitude: lng,
          ferry_end_latitude: lat,
        },
        success: function (data) {
          lui.loading("close");
          const data_ = JSON.parse(data);
          if (data_.ret) {
            $(".fee-content-end .fee-desc").html(
              `从：<b>${data_.data.end_address_remark}</b>送至下车位置，<span class="fee-custom-price-view">+<b class="end-custom-price-fee">${data_.data.price}</b></span>元`
            );
            let fee_start = Number($(".fee-money-cell").attr("fee-start")) || 0;
            $(".fee-money-cell").attr("fee-end", data_?.data?.price || 0);
            $(".fee-money").html(Money(data_?.data?.price || 0 + fee_start, 2));
            $(".toPay").attr("fee_elng", data_.data.end_longitude);
            $(".toPay").attr("fee_elat", data_.data.en_latitude);
            $(".toPay").attr("fee_edistance", data_.data.distance);
            $(".toPay").attr("fee_eduration", data_.data.duration);
            $(".toPay").attr("fee_eplace", data_.data.end_address_remark);
            $(".toPay").attr("is_end_ferry_check", 1);
            $(".fee-money-cell").show();
            priceCalculation(true);
            resolve();
          } else {
            lui.toast({
              position: "middle",
              text: data_.data,
            });
            $(".fee-content-end .fee-desc").html("");
            $(".toPay").attr("fee_elng", "");
            $(".toPay").attr("fee_elat", "");
            $(".toPay").attr("fee_edistance", "");
            $(".toPay").attr("fee_eduration", "");
            $(".toPay").attr("fee_eplace", "");
            $(".toPay").attr("is_end_ferry_check", 0);
            priceCalculation(true);
            reject();
          }
        },
      });
    }
  });
}

function openDetail() {
  const real_money = $(".train-price-view b").html();

  if (real_money == "--") {
    return;
  }
  onShowPopup("#j-popup-half-train-detail");
}

/**
 * 初始化iScroll控件
 */
function loaded() {
  pullDownEl = document.getElementById("pullDown");
  pullDownOffset = pullDownEl.offsetHeight;
  pullUpEl = document.getElementById("pullUp");
  pullUpOffset = pullUpEl.offsetHeight;
  myScroll = new iScroll("scroller_wrap", {
    scrollbarClass: "myScrollbar",
    useTransition: false,
    mouseWheel: true,
    scrollX: true,
    checkDOMChanges: true, //dom改变的时候refresh
    topOffset: pullDownOffset,
    onRefresh: function () {
      if (pullDownEl.className.match("loading")) {
        pullDownEl.className = "";
        pullDownEl.querySelector(".pullDownLabel").innerHTML = "下拉刷新...";
      } else if (pullUpEl.className.match("loading")) {
        pullUpEl.className = "";
        pullUpEl.querySelector(".pullUpLabel").innerHTML = "上拉加载更多...";
      }
    },
    onScrollMove: function () {
      if (this.y > 5 && !pullDownEl.className.match("flip")) {
        pullDownEl.className = "flip";
        pullDownEl.querySelector(".pullDownLabel").innerHTML =
          "松手开始更新...";
        this.minScrollY = 0;
      } else if (this.y < 5 && pullDownEl.className.match("flip")) {
        pullDownEl.className = "";
        pullDownEl.querySelector(".pullDownLabel").innerHTML = "下拉刷新...";
        this.minScrollY = -pullDownOffset;
      } else if (
        this.y < this.maxScrollY - 5 &&
        !pullUpEl.className.match("flip")
      ) {
        pullUpEl.className = "flip";
        pullUpEl.querySelector(".pullUpLabel").innerHTML = "松手开始更新...";
        this.maxScrollY = this.maxScrollY;
      } else if (
        this.y > this.maxScrollY + 5 &&
        pullUpEl.className.match("flip")
      ) {
        pullUpEl.className = "";
        pullUpEl.querySelector(".pullUpLabel").innerHTML = "上拉加载更多...";
        this.maxScrollY = pullUpOffset;
      }
    },
    onScrollEnd: function () {
      if (pullDownEl.className.match("flip")) {
        pullDownEl.className = "loading";
        pullDownEl.querySelector(".pullDownLabel").innerHTML = "加载中...";
        pullDownAction(); // Execute custom function (ajax call?)
      } else if (pullUpEl.className.match("flip")) {
        pullUpEl.className = "loading";
        pullUpEl.querySelector(".pullUpLabel").innerHTML = "加载中...";
        pullUpAction(); // Execute custom function (ajax call?)
      }
    },
  });
}

async function getOrderDetail(order_id) {
  const { data, ret } = await fetchOrder(order_id);
  if (ret) {
    if (data.is_pay == 1) {
      togglePopupAd("#j-popup-pay");
      setTimeout(() => {
        window.location.href =
          "/passenger_order_detail?order_id=" +
          order_id +
          "&callback=" +
          sessionStorage.getItem("callback") +
          "&show=1";
      }, 1000);
    } else {
      setTimeout(() => {
        getOrderDetail(order_id);
      }, 1000);
    }
  } else {
    setTimeout(() => {
      getOrderDetail(order_id);
    }, 1000);
  }
}

function fetchOrder(id) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url:
        "/Passenger/Order/getOrderDetail?order_id=" +
        id +
        "&callback=" +
        sessionStorage.getItem("callback"),
      type: "get",
      success: function (json) {
        console.log("json", JSON.parse(json));
        resolve(JSON.parse(json));
      },
      error: function (json) {
        reject();
      },
      timeout: 100000,
    });
  });
}
// fetchOrder(388)
// debounce(keywordsSearch, 500, true)
function getUseAblePoints(price = 0) {
  return new Promise((resolve, reject) => {
    //下单前获取可用积分
    $.ajax({
      url: `/Passenger/PassengerPage/getPassengerProfile/callback/${sessionStorage.getItem(
        "callback"
      )}?price=${price}`,
      type: "get",
      success: function (json) {
        const res_ = JSON.parse(json);
        if (res_.ret) {
          $(".point-left .point-val").html(res_?.data?.user_extra?.points);
          $(".point-left label").html(
            res_?.data?.user_extra?.redeemable_amount
          );
          $(".point-left .total-points").html(
            res_?.data?.user_extra?.total_points || 0
          );
          if (
            Number(res_?.data?.user_extra?.points > 0) &&
            Number(res_?.data?.user_extra?.redeemable_amount)
          ) {
            $(".used-points-params").show();
          }
        } else {
          $(".used-points-params").hide();
        }
        resolve();
      },
      error: function (json) {},
      timeout: 300000,
    });
  });
}

function getUseAbleBalance(price = 0) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url: `/Passenger/PassengerPage/getPassengerProfile/callback/${sessionStorage.getItem(
        "callback"
      )}?price=${price}`,
      type: "get",
      success: function (json) {
        const res_ = JSON.parse(json);
        if (res_.ret) {
          let balance = parseFloat(res_?.data?.cash_balances || 0).toFixed(2);
          if (res_?.data?.cash_balances < price) {
            document.getElementById("balance-radio").disabled = true;
            setPaymentMethod("wechat");
            $(".abled-left-balance").addClass("default-disabled");
            $("#balance-radio").addClass("default-disabled");
            $(".label-text-balance").html("余额不足，剩余：");
          } else {
            setPaymentMethod("balance");
            $(".abled-left-balance").removeClass("default-disabled");
            $("#balance-radio").removeClass("default-disabled");
            $(".label-text-balance").html("可用余额: ");
          }
          $(".available-balance").html(balance);
        } else {
          $(".available-balance").html("0.00");
          $(".abled-left-balance").addClass("default-disabled");
          $("#balance-radio").addClass("default-disabled");
          $(".label-text-balance").html("可用余额: ");
        }
        resolve();
      },
      error: function (json) {},
      timeout: 300000,
    });
  });
}

function setPaymentMethod(method) {
  var wechatRadio = document.getElementById("wechat-radio");
  var balanceRadio = document.getElementById("balance-radio");

  if (method === "wechat") {
    wechatRadio.checked = true;
    balanceRadio.checked = false;
  } else if (method === "balance") {
    wechatRadio.checked = false;
    balanceRadio.checked = true;
  }
}

function renderDriverInfo(id) {
  $.ajax({
    url: `/cgi-bin/api/inner/account_types/driver/account_ids/${id}/access_token`,
    type: "post",
    success: function (json) {
      const token = json?.data?.access_token;
      $.ajax({
        url: `/cgi-bin/api/auth/driver/profiles`,
        type: "get",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        success: function (res) {
          console.log("resresres", res);
          const driverInfo = res.data;
          $(".header-info-container")
            .html(` <div class="fliter-container"><span>当前司机：</span>
              <div class="fileter-driver">${driverInfo.name}-${driverInfo.car_tail_number}  <div class="clear-driver"><img src="/Theme/images/passenger/cancel1.svg" alt=""></div></div> </div>`);
        },
        timeout: 300000,
      });
    },
  });
}

// 处理航班号输入字段的显示逻辑
function handleFlightNumberVisibility() {
  try {
    // 检查必要的DOM元素是否存在
    const popupElement = document.getElementById('j-popup-half-train-order');
    if (!popupElement) {
      return;
    }
    
    // 获取当前选择的班次索引
    const data_index = Number($(popupElement).attr("index") || 0);
    
    // 检查ticketObj是否存在
    const ticketObjStr = sessionStorage.getItem("ticketObj");
    if (!ticketObjStr) {
      return;
    }
    
    // 尝试解析ticketObj
    let ticketObjArray;
    try {
      ticketObjArray = JSON.parse(ticketObjStr);
      if (!Array.isArray(ticketObjArray)) {
        return;
      }
    } catch (parseError) {
      return;
    }
    
    // 检查索引是否有效
    if (data_index >= ticketObjArray.length) {
      return;
    }
    
    // 获取当前选择的班次数据
    const ticketObj = ticketObjArray[data_index];
    
    if (ticketObj && ticketObj.line_class) {
      // 获取是否需要显示航班号
      const isRequiredFlightNumber = ticketObj.line_class.is_required_flight_number;
      
      // 查找现有的航班号容器
      const flightNumberContainer = document.querySelector('.flight-number-container');
      if (flightNumberContainer) {
        // 根据是否需要航班号来显示或隐藏容器
        flightNumberContainer.style.display = isRequiredFlightNumber == 1 ? 'block' : 'none';
        
        // 如果需要显示航班号，清空之前的值
        if (isRequiredFlightNumber == 1) {
          const flightNumberInput = flightNumberContainer.querySelector('#flight_number');
          if (flightNumberInput) {
            flightNumberInput.value = '';
          }
        }
      }
      
      console.log(`班次切换: 索引=${data_index}, 航班号必填=${isRequiredFlightNumber}, 容器显示=${isRequiredFlightNumber == 1 ? '是' : '否'}`);
    }
  } catch (error) {
    console.error('handleFlightNumberVisibility执行出错:', error);
  }
}

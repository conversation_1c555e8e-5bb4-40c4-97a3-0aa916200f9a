# 管理 WebSocket 协议的连接升级机制
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

# WebSocket 服务的独立上游
upstream websocket_backend {
    server **************:1215;  # 独立的 WebSocket 服务
    keepalive 64;
}

upstream swoole2 {
    # 通过 IP:Port 连接
    ip_hash;
    server **************:1215 weight=4 max_fails=2 fail_timeout=10s;
    keepalive 64;
}

upstream pay-swoole {
    # 你的 API 数据不一致 可能是 负载均衡、Token 解析、数据库同步、缓存未同步 等多个问题导致的。
    # 负载均衡绑定用户请求到同一实例：
    ip_hash;
    server **************:1216 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 64;
}

upstream pay-payout-swoole {
    # 通过 IP:Port 连接
    # 负载均衡绑定用户请求到同一实例：
    ip_hash;
    server **************:8000 weight=5 max_fails=3 fail_timeout=30s;
}

upstream reporting-swoole {
    ip_hash;
    # 通过 IP:Port 连接
    server **************:8010 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream account-swoole {
    # 通过 IP:Port 连接
    server **************:8020 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream finance-swoole {
    # 通过 IP:Port 连接
    server **************:8030 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

server {
    # 监听 HTTP 和 HTTPS 端口
    listen 80;
    listen 443 ssl; # 如果需要启用 HTTPS，则取消注释并配置证书

    server_name pre.cczhaoche.com;
    root /var/www/master/cczhaoche/thinkphp31-app; # 网站根目录
    index index.php; # 默认首页文件
    # 访问日志配置
    access_log /var/log/nginx/pre.cczhaoche.com.access.log;
    error_log /var/log/nginx/pre.cczhaoche.com.error.log;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cczhaoche.com/cczhaoche.com.pem; # SSL 证书路径
    ssl_certificate_key /etc/nginx/ssl/cczhaoche.com/cczhaoche.com.key; # SSL 证书密钥路径
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # 加密套件和协议配置
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4; # 加密算法选择
    ssl_protocols TLSv1.2 TLSv1.3; # 优先使用更安全的 TLSv1.2 和 TLSv1.3
    ssl_prefer_server_ciphers on; # 优先使用服务器端的加密套件

    client_max_body_size 100M; # 允许最大上传 100MB，可根据需要调整

    # GZIP压缩配置
    gzip on;
    gzip_comp_level 5; # 降低压缩级别以减少CPU使用
    gzip_buffers 16 8k;
    gzip_min_length 1024;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-icon;
    gzip_vary on;

    # 默认位置块，处理index.php重定向
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    # 子目录反向代理配置
    # 本地目录别名映射，直接读取服务器上的静态内容
    location /dingzhikeyun/ {
        alias /mnt/www/master/cczhaoche/thinkphp31-app/dingzhikeyun/; # 指定目录别名
        index index.html; # 默认首页文件
    }


    # 处理 finance-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /finance-bin/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/finance-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 finance-bin 下的 Swagger 文档 JSON 文件
    location = /finance-bin/docs/api-docs.json {
        proxy_pass http://finance-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务
    location ^~ /finance-bin/ {
        proxy_pass http://finance-swoole/;
        proxy_http_version 1.1;
    }


    # 处理 account-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /account-bin/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/account-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 account-bin 下的 Swagger 文档 JSON 文件
    location = /account-bin/docs/api-docs.json {
        proxy_pass http://account-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务
    location ^~ /account-bin/ {
        proxy_pass http://account-swoole/;
        proxy_http_version 1.1;
    }


    # 处理 pay-bin/payout 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/payout/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/payment-payout-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin/payout 下的 Swagger 文档 JSON 文件
    location = /pay-bin/payout/docs/api-docs.json {
        proxy_pass http://pay-payout-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关转账微服务
    location ^~ /pay-bin/payout/ {
        proxy_pass http://pay-payout-swoole/;
    }


    # 处理 pay-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/payment-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin 下的 Swagger 文档 JSON 文件
    location = /pay-bin/docs/api-docs.json {
        proxy_pass http://pay-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关微服务
    location ^~ /pay-bin/ {
        proxy_pass http://pay-swoole/;
    }


    # 处理 report-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /report-bin/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/reporting-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 report-bin 下的 Swagger 文档 JSON 文件
    location = /report-bin/docs/api-docs.json {
        proxy_pass http://reporting-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至数据上报微服务
    location ^~ /report-bin/ {
        proxy_pass http://reporting-swoole/;
    }


    # 处理 cgi-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /cgi-bin/docs/asset/ {
        alias /var/www/master/cczhaoche/micro-services/laravel-api-app/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 cgi-bin 下的 Swagger 文档 JSON 文件
    location = /cgi-bin/docs/api-docs.json {
        proxy_pass http://swoole2/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至主业微服务
    location ^~ /cgi-bin/ {
        proxy_pass http://swoole2/;
    }

    location ^~ /micro-apps/invoice/ {
        alias /var/www/master/cczhaoche/micro-apps/invoice/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invoice/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/invoice/index.html;
    }

    location ^~ /micro-apps/invitation/ {
        alias /var/www/master/cczhaoche/micro-apps/invitation/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/invitation/index.html;
    }

    location ^~ /micro-apps/point-mall/ {
        alias /var/www/master/cczhaoche/micro-apps/point-mall/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/point-mall/index.html;
    }

    location ^~ /micro-apps/uniapp-driver/ {
        alias /var/www/master/cczhaoche/micro-apps/uniapp-driver/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/uniapp-driver/index.html;
    }

    location ^~ /micro-apps/uniapp-passenger/ {
        alias /var/www/master/cczhaoche/micro-apps/uniapp-passenger/;
        index index.html index.htm;
        try_files $uri $uri/ /micro-apps/uniapp-passenger/index.html;
    }

     # 处理 WebSocket 请求，直接代理到独立的 WebSocket 服务
    # WebSocket 服务
    location = /ws {
        proxy_pass http://websocket_backend;  # 独立的 WebSocket 服务
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Scheme $scheme;
        proxy_set_header Server-Protocol $server_protocol;
        proxy_set_header Server-Name $server_name;
        proxy_set_header Server-Addr $server_addr;
        proxy_set_header Server-Port $server_port;
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass php-fpm-56:9000;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        include fastcgi_params;
    }

    # 图片、字体等静态资源的长期缓存
    location ~* \.(gif|jpg|jpeg|png|bmp|swf|woff|ttf|txt|pdf|apk)$ {
        expires 30d; # 缓存时间为 30 天
        add_header Cache-Control "public"; # 设置缓存控制头
    }

    # JS 和 CSS 文件的短期缓存
    location ~* \.(js|css)$ {
        expires 1h; # 缓存时间为 1 小时
        add_header Cache-Control "public"; # 设置缓存控制头
    }
    
}

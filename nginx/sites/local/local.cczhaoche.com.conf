# WebSocket 服务的独立上游
upstream websocket_backend {
    server php-worker:1215;  # 独立的 WebSocket 服务
    keepalive 64;
}


upstream swoole2 {
    # 通过 IP:Port 连接
    server php-worker:1215 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream pay-swoole {
    # 通过 IP:Port 连接
    server php-worker:1216 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream pay-payout-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8000 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream reporting-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8010 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream account-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8020 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream finance-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8030 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

server {
    listen 80;
    server_name local.cczhaoche.com;
    root /var/www/cczhaoche/thinkphp31-app/;
    index index.html index.htm index.php;
    # 优化GZIP压缩配置
    gzip on;
    gzip_comp_level 4;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/javascript
        application/json
        application/x-javascript
        application/xml
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

    # 静态资源缓存优化
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header X-Content-Type-Options nosniff;
        try_files $uri =404;
    }
    # index.php重定向
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }
    # 访问日志配置
    access_log /var/log/nginx/local.cczhaoche.com.access.log;
    error_log /var/log/nginx/local.cczhaoche.com.error.log;

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    location ~ \.php {
        fastcgi_pass php-fpm-56:9000;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        include fastcgi_params;
    }
    location ~ /.well-known {
        allow all;
    }

    # 处理 WebSocket 请求，直接代理到独立的 WebSocket 服务
    location = /ws {
        proxy_pass http://websocket_backend;  # 独立的 WebSocket 服务
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Scheme $scheme;
        proxy_set_header Server-Protocol $server_protocol;
        proxy_set_header Server-Name $server_name;
        proxy_set_header Server-Addr $server_addr;
        proxy_set_header Server-Port $server_port;
    }

    # 子目录反向代理配置
    location /dingzhikeyun/ {
        alias /var/www/master/cczhaoche/thinkphp31-app/dingzhikeyun/; # 指定目录别名
        index index.html; # 默认首页文件
    }

     # 处理 finance-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /finance-bin/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/finance-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 finance-bin 下的 Swagger 文档 JSON 文件
    location = /finance-bin/docs/api-docs.json {
        proxy_pass http://finance-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务 - 简化配置
    location /finance-bin/ {
        proxy_pass http://finance-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 account-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /account-bin/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/account-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 account-bin 下的 Swagger 文档 JSON 文件
    location = /account-bin/docs/api-docs.json {
        proxy_pass http://account-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务 - 简化配置
    location /account-bin/ {
        proxy_pass http://account-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 pay-bin/payout 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/payout/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/payment-payout-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin/payout 下的 Swagger 文档 JSON 文件
    location = /pay-bin/payout/docs/api-docs.json {
        proxy_pass http://pay-payout-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关转账微服务
    location ^~ /pay-bin/payout/ {
        proxy_pass http://pay-payout-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 pay-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/payment-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin 下的 Swagger 文档 JSON 文件
    location = /pay-bin/docs/api-docs.json {
        proxy_pass http://pay-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关微服务
    location ^~ /pay-bin/ {
        proxy_pass http://pay-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 report-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /report-bin/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/reporting-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 report-bin 下的 Swagger 文档 JSON 文件
    location = /report-bin/docs/api-docs.json {
        proxy_pass http://reporting-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至数据上报微服务
    location ^~ /report-bin/ {
        proxy_pass http://reporting-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 cgi-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /cgi-bin/docs/asset/ {
        alias /var/www/cczhaoche/micro-services/laravel-api-app/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 cgi-bin 下的 Swagger 文档 JSON 文件
    location = /cgi-bin/docs/api-docs.json {
        proxy_pass http://swoole2/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至主业微服务
    location ^~ /cgi-bin/ {
        proxy_pass http://swoole2/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 反向代理微应用（独立服务器）
    location ^~ /micro-apps/ {
        proxy_pass http://**************/; # 代理到 Vue.js 托管的服务器
    }

    # 反向代理新闻子网站
    location ^~ /news/ {
        proxy_pass http://news.cczhaoche.com/;
    }

    # 图片、字体等静态资源的长期缓存
    location ~* \.(gif|jpg|jpeg|png|bmp|swf|woff|ttf|txt|pdf|apk)$ {
        expires 30d; # 缓存时间为 30 天
        add_header Cache-Control "public"; # 设置缓存控制头
    }

    # JS 和 CSS 文件的短期缓存
    location ~* \.(js|css)$ {
        expires 1h; # 缓存时间为 1 小时
        add_header Cache-Control "public"; # 设置缓存控制头
    }

}

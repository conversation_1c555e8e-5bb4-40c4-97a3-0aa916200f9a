server {
    listen  80;

    server_name dev.hiyamedia.cn;
    root   /var/www/develop/hiyamedia/home-webapp/current/dist;
    index  index.html index.htm index.php;

    access_log  /var/log/nginx/dev.hiyamedia.cn.access.log  ;


    gzip on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;


    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.html?s=$1 last;
            break;
        }
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

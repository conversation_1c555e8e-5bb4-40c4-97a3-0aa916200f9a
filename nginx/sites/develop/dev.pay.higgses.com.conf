# /opt/docker/nginx/sites/dev.pay.higgses.com.conf
# 定义 Laravel Octane (Swoole) 反向代理
upstream larave-octane-swoole {
    server php-worker-83:8000 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

server {
    listen 80;
    server_name dev.pay.higgses.com;

    root /var/www/develop/cczhaoche/micro-services/payment-gateway-service/public;
    index index.html index.htm index.php;

    access_log /var/log/nginx/dev.pay.higgses.com.access.log;
    error_log /var/log/nginx/dev.pay.higgses.com.error.log warn;

    # Gzip 压缩优化
    gzip on;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_min_length 1000;
    gzip_vary on;
    gzip_types
        text/plain text/css text/javascript application/json application/javascript
        application/x-javascript application/xml application/xml+rss application/x-httpd-php
        image/svg+xml image/x-icon image/jpeg image/png image/gif font/woff2;

    # 处理主站请求
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 反向代理至 Laravel Octane（Swoole）
    location ^~ /payout/ {
        proxy_pass http://larave-octane-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header Server-Protocol $server_protocol;
        proxy_set_header Server-Name $server_name;
        proxy_set_header Server-Addr $server_addr;
        proxy_set_header Server-Port $server_port;

        # 优化代理请求处理
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_temp_file_write_size 256k;
    }

    # 处理主站 PHP 请求（排除 /payout/，走 PHP-FPM）
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 600;
        fastcgi_send_timeout 600;
        fastcgi_connect_timeout 60s;
    }

    # 防止访问隐藏文件（.git、.env等）
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 安全优化：防止 PHP 代码注入漏洞
    location ~* ^/(uploads|images|assets)/.*\.php$ {
        deny all;
    }
}

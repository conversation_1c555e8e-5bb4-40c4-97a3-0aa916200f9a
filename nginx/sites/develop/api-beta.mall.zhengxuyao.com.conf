server {
    listen  80;
    server_name api-beta.mall.zhengxuyao.com;
    rewrite ^(.*)$ https://$host$1 permanent;
}

server {
    # For https
    listen 443 ssl;
    #listen [::]:443 ssl ipv6only=on;
    ssl_certificate /etc/nginx/ssl/api-beta.mall.zhengxuyao.com/9404071_api-beta.mall.zhengxuyao.com.pem;
    ssl_certificate_key /etc/nginx/ssl/api-beta.mall.zhengxuyao.com/9404071_api-beta.mall.zhengxuyao.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    server_name api-beta.mall.zhengxuyao.com;
    root  /var/www/testing/higgses/zhengxuyao/api/current/public;
    index  index.html index.htm index.php;

    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass   php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

}

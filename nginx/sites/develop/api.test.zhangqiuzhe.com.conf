server {
    listen  80;
    server_name api.test.zhangqiuzhe.com;
    #把http的域名请求转成https
    rewrite ^(.*)$ https://$host$1;
}

server {
        listen 443 ssl;
    #填写绑定证书的域名
    #证书文件名称
    ssl_certificate /etc/nginx/ssl/api.test.zhangqiuzhe.com/7634764_api.test.zhangqiuzhe.com.pem; 
    #私钥文件名称
        ssl_certificate_key /etc/nginx/ssl/api.test.zhangqiuzhe.com/7634764_api.test.zhangqiuzhe.com.key; 
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    

    server_name api.test.zhangqiuzhe.com;
    root  /var/www/testing/zhangqiuzhe/api/current/public;
    index  index.html index.htm index.php;
    access_log  /var/log/nginx/api.test.zhangqiuzhe.com.access.log;

    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

}

server {
    listen  80;
    server_name api.haidao.higgses.com;
    rewrite ^(.*)$ https://$host$1 permanent;
}

server {
    # For https
    #SSL 访问端口号为 443
    listen 443 ssl; 
    #listen [::]:443 ssl ipv6only=on;
    #填写绑定证书的域名
    server_name api.haidao.higgses.com;
    #证书文件名称
    ssl_certificate /etc/nginx/ssl/api.haidao.higgses.com/api.haidao.higgses.com.pem; 
    #私钥文件名称
    ssl_certificate_key /etc/nginx/ssl/api.haidao.higgses.com/api.haidao.higgses.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    root  /var/www/develop/island-travel-app/laravel8-api-app/public;
    index  index.html index.htm index.php;
	access_log /var/log/nginx/api.haidao.higgses.com.log ;

    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

}

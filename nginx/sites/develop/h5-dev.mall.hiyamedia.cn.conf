
server {
    listen 80;
    server_name h5-dev.mall.hiyamedia.cn;
    rewrite ^(.*)$ https://${server_name}$1 permanent;
}

server {
    # For https
    listen 443 ssl;
    ssl_certificate /etc/nginx/ssl/5637825_h5-dev.mall.hiyamedia.cn.pem;
    ssl_certificate_key /etc/nginx/ssl/5637825_h5-dev.mall.hiyamedia.cn.key;
    
    server_name h5-dev.mall.hiyamedia.cn;
    root   /var/www/develop/hiyamedia/mall-webapp/current/build;
    index  index.html index.htm index.php;

    gzip on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;


    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location / {
        try_files $uri $uri/ /index.html;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

server {
    listen 80;
    server_name wlhy.higgses.com;
    return 301 https://$host$request_uri;  # 将HTTP请求永久重定向到HTTPS
}

server {
    listen 443 ssl;
    server_name wlhy.higgses.com;
    root /var/www/master/higgses/u2;
    index index.html index.htm;

    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/higgses.com/fullchain.cer;
    ssl_certificate_key /etc/nginx/ssl/higgses.com/higgses.com.key;

    # SSL协议和密码套件配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_prefer_server_ciphers on;

    # SSL会话配置
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;

    # HTTP严格传输安全策略（HSTS）
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # gzip压缩配置
    gzip on;
    gzip_static on;
    gzip_comp_level 6;  # 压缩级别调整为6，平衡压缩率和CPU使用率
    gzip_buffers 16 8k;  # 增加gzip缓冲区
    gzip_min_length 256;  # 调整最小压缩长度
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_vary on;

    # 错误页面配置
    error_page 404 /404.html;

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 静态文件缓存配置
    location ~* \.(gif|jpg|jpeg|png|bmp|swf)$ {
        expires 1d;
        add_header Cache-Control "public, no-transform";
    }

    location ~* \.(js|css)?$ {
        expires 2h;
        add_header Cache-Control "public, no-transform";
    }

    # 允许访问.well-known目录
    location ~ /\.well-known {
        allow all;
    }

    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}

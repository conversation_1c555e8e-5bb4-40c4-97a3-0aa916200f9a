# HTTP 服务器配置  
server {  
    listen 80;  
    server_name api-beta.wlqs.higgses.com;  
  
    # 将所有 HTTP 请求永久重定向到 HTTPS  
    rewrite ^(.*)$ https://$host$1 permanent;  
}  
  
# HTTPS 服务器配置  
server {  
    listen 443 ssl;  
    server_name api-beta.wlqs.higgses.com;  
  
    # SSL 证书和私钥配置  
    ssl_certificate /etc/nginx/ssl/api-beta.wlqs.higgses.com/api-beta.wlqs.higgses.com.pem;  
    ssl_certificate_key /etc/nginx/ssl/api-beta.wlqs.higgses.com/api-beta.wlqs.higgses.com.key;  
  
    # SSL 配置优化  
    ssl_session_timeout 5m;  
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;  
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;  
    ssl_prefer_server_ciphers on;  
  
    # 服务器基本配置  
    root /var/www/develop/wenlvqinshen/laravel-api/public;  
    index index.html index.htm index.php;  
    access_log /var/log/nginx/api-beta.wlqs.higgses.com.log;  
  
    # 启用 GZIP 压缩  
    gzip on;  
    gzip_static on;  
    gzip_comp_level 9;  
    gzip_buffers 4 32k;  
    gzip_min_length 1k;  
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;  
    gzip_vary on;  
  
    # 处理静态文件请求  
    location / {  
        try_files $uri $uri/ /index.php?$query_string;  
    }  
  
    # 处理 PHP 脚本请求  
    location ~ \.php$ {  
        try_files $uri /index.php =404;  
        fastcgi_pass php-upstream; # 假设已经定义了名为 php-upstream 的 upstream  
        fastcgi_index index.php;  
        fastcgi_buffers 16 16k;  
        fastcgi_buffer_size 32k;  
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;  
        fastcgi_read_timeout 600;  
        include fastcgi_params;  
    }  
}

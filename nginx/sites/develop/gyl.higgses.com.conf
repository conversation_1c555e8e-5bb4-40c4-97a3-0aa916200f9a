server {
    listen 80;
    server_name gyl.higgses.com;

    # 将所有 HTTP 请求重定向到 HTTPS
    return 301 https://$host$request_uri;
}


# HTTPS 服务器配置
server {
    listen 443 ssl;
    server_name gyl.higgses.com;

    # SSL 证书和私钥配置
    ssl_certificate /etc/nginx/ssl/gyl.higgses.com/gyl.higgses.com.cer;
    ssl_certificate_key /etc/nginx/ssl/gyl.higgses.com/gyl.higgses.com.key;

    # SSL 配置优化
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    # 服务器基本配置
    access_log /var/log/nginx/gyl.higgses.com.access.log;
    error_log  /var/log/nginx/gyl.higgses.com.error.log notice;

    # 启用 GZIP 压缩
    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length 1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    # 处理静态文件请求
    location / {
        root /var/www/develop/gyl.higgses.com/org-web;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    location /privacy-policy {
        alias /var/www/develop/gyl.higgses.com/privacy-policy/;
        index  index.html index.htm;
        try_files $uri $uri/ /privacy-policy/index.html;
    }
    location /scs {
        alias /var/www/develop/gyl.higgses.com/admin-web/;
        index  index.html index.htm;
        # 处理子路径下的前端路由（关键！）
        try_files $uri $uri/ /scs/index.html;
    }

    # 配置/api路径的转发规则，将以/api开头的请求转发到后端服务
    location ~^/cz-admin {
        proxy_pass http://*************:9123;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 配置/api路径的转发规则，将以/api开头的请求转发到后端服务
    location ~^/cz-api {
        proxy_pass http://*************:9124;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    
    listen 80;
    listen 443 ssl;
    ssl_certificate /etc/nginx/ssl/hiyamedia.cn/5207221_shoph5.hiya.higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/hiyamedia.cn/5207221_shoph5.hiya.higgses.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    server_name shoph5.hiya.higgses.com;
    root   /var/www/testing/hiyamedia/mall-webapp/current/build;
    index  index.html index.htm index.php;

    gzip on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;
	

    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
	
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.html?s=$1 last;
            break;
        }
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

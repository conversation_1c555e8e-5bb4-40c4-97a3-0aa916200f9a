server {

    #listen 80;
    #listen [::]:80;

    # For https
    listen 443 ssl;
    listen [::]:443 ssl ipv6only=on;
    ssl_certificate /etc/nginx/ssl/4891786_api-dev.campvan.higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/4891786_api-dev.campvan.higgses.com.key;

    server_name api-dev.campvan.higgses.com;
    root /var/www/develop/campvan/backend/admin-api/current/public;
    index index.php index.html index.htm;

    access_log  /var/log/nginx/api-dev.campvan.higgses.com.access.log  ;

    gzip on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html
    # laravel url rewirte
    location / {
         try_files $uri $uri/ /index.php$is_args$args;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

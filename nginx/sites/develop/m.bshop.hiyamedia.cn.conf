server {
    listen 80;
    server_name m.bshop.hiyamedia.cn;
    #把http的域名请求转成https
    rewrite ^(.*)$ https://$host$1;
}

server {
    listen 443 ssl;
    ssl_certificate /etc/nginx/ssl/hiyamedia.cn/fullchain.cer;
    ssl_certificate_key /etc/nginx/ssl/hiyamedia.cn/hiyamedia.cn.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    server_name m.bshop.hiyamedia.cn;
    root   /var/www/testing/hiyamedia/mall-webapp/current/build;
    index  index.html index.htm index.php;


    gzip on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;


    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }


    location / {
        try_files $uri $uri/ /index.html;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

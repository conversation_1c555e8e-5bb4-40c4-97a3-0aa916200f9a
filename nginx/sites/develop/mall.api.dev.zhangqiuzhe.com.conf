server {
    listen  80;

    server_name  mall.api.dev.zhangqiuzhe.com;
    #rewrite ^(.*)$  https://$host$1 permanent;
    root   /var/www/develop/zhangqiuzhe/mall/current/public;
    index  index.html index.htm index.php;

    access_log  /var/log/nginx/mall.api.dev.zhangqiuzhe.com.access.log  ;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

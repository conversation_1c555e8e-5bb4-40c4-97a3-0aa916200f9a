server {
    # For https
    listen 443 ssl;
    #listen [::]:443 ssl ipv6only=on;
    ssl_certificate /etc/nginx/ssl/zs-dev.saas.zhongjiacloud.com/zs-dev.saas.zhongjiacloud.com.pem;
    ssl_certificate_key /etc/nginx/ssl/zs-dev.saas.zhongjiacloud.com/zs-dev.saas.zhongjiacloud.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;
    

    server_name zs-dev.saas.zhongjiacloud.com;
    root /var/www/develop/zhongjiayun/frontend/zs-app/current/dist/dev/h5;
    index  index.html index.htm;
   
    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length  1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;


    error_page  404             /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;

    location / {
      try_files $uri $uri/ /index.html;
    }


    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      1d;
    }

    location ~ .*\.(js|css)?$
    {
        expires      2h;
    }

    location ~ /.well-known {
        allow all;
    }

}

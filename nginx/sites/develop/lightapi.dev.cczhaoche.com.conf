# 管理 WebSocket 的升级机制
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

# # WebSocket 服务的独立后端
# upstream websocket_backend {
#     server *************:1215;  # 独立的 WebSocket 服务
#     keepalive 16;
# }

# Laravel API 服务的负载均衡后端
upstream api_backend {
    server *************:1215 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

server {
    listen 80;
    listen 443 ssl;
    server_name lightapi.dev.cczhaoche.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/dev.cczhaoche.com/dev.cczhaoche.com.pem;
    ssl_certificate_key /etc/nginx/ssl/dev.cczhaoche.com/dev.cczhaoche.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    root /var/www/develop/cczhaoche/laravel-api-app/public;
    index index.html index.htm index.php;
    access_log /var/log/nginx/lightapi.dev.cczhaoche.com_access.log;

    gzip on;
    gzip_static on;
    gzip_comp_level 5;
    gzip_buffers 4 32k;
    gzip_min_length 1k;
    gzip_types text/plain application/json application/javascript application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    # 处理静态资源
    location / {
        try_files $uri @laravels;  # 静态文件直接返回，其他请求转发到 LaravelS
    }

    # # WebSocket 服务
    # location = /ws {
    #     proxy_pass http://websocket_backend;  # 独立的 WebSocket 服务
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection $connection_upgrade;
    #     proxy_set_header Host $http_host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Real-PORT $remote_port;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header Scheme $scheme;
    #     proxy_set_header Server-Protocol $server_protocol;
    #     proxy_set_header Server-Name $server_name;
    #     proxy_set_header Server-Addr $server_addr;
    #     proxy_set_header Server-Port $server_port;
    #     proxy_connect_timeout 60s;
    #     proxy_read_timeout 60s;
    # }

    # 动态资源由 LaravelS 处理
    location @laravels {
        proxy_pass http://api_backend;  # 转发到 LaravelS
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Scheme $scheme;
        proxy_set_header Server-Protocol $server_protocol;
        proxy_set_header Server-Name $server_name;
        proxy_set_header Server-Addr $server_addr;
        proxy_set_header Server-Port $server_port;
        proxy_connect_timeout 60s;
        proxy_read_timeout 120s;
    }
}

server {
    listen  80;

    server_name mp.xy.higgses.com;
    root  /var/www/master/higgses/xy/mp/public;
    index  index.html index.htm index.php;

    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }
    
    location ~ \.php$ {
        fastcgi_pass   php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}

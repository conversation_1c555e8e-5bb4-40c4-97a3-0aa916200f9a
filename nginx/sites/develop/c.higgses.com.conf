# WebSocket 服务的独立上游
upstream websocket_backend {
    server php-worker:1215;  # 独立的 WebSocket 服务
    keepalive 64;
}

upstream swoole2 {
    # 通过 IP:Port 连接
    server php-worker:1215 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream pay-swoole {
    # 通过 IP:Port 连接
    server php-worker:1216 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream pay-payout-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8000 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream reporting-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8010 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream account-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8020 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream finance-swoole {
    # 通过 IP:Port 连接
    server php-worker-83:8030 weight=5 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

# upstream flask-chat-service {
#     # 通过 IP:Port 连接
#     server flask-chat-service:5001 weight=5 max_fails=3 fail_timeout=30s;
#     keepalive 16;
# }

server {
    listen 80;
    listen 443 ssl;

    server_name c.higgses.com;
    root /var/www/develop/cczhaoche/thinkphp31-app;
    index index.html index.htm index.php;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/higgses.com/higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/higgses.com/higgses.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4';
    ssl_protocols TLSv1.2 TLSv1.3; # 更新协议以提高安全性
    ssl_prefer_server_ciphers on;

    # 访问日志配置
    access_log /var/log/nginx/c.higgses.com.access.log;
    error_log /var/log/nginx/c.higgses.com.error.log;

    # 性能优化配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    client_max_body_size 20M;
    
    # 优化GZIP压缩配置
    gzip on;
    gzip_comp_level 6;  # 提高压缩级别获得更好的压缩比
    gzip_min_length 1024;  # 增加最小压缩长度
    gzip_proxied any;
    gzip_vary on;
    gzip_buffers 16 8k;  # 优化缓冲区配置
    gzip_types
        application/javascript
        application/json
        application/x-javascript
        application/xml
        application/xml+rss
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml
        application/vnd.ms-fontobject
        font/ttf
        font/opentype
        font/x-woff
        font/woff2;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

    # 静态资源缓存优化 - 图片文件
    location ~* \.(jpg|jpeg|png|gif|webp|ico|bmp|svg)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000, immutable";
        add_header X-Content-Type-Options nosniff;
        access_log off;  # 关闭访问日志以提高性能
        try_files $uri =404;
        
        # 开启sendfile和异步IO以提高文件传输性能
        sendfile on;
        aio on;
        directio 8m;  # 对于大于8MB的文件使用直接IO
    }
    
    # CSS和JavaScript文件缓存优化
    location ~* \.(css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header X-Content-Type-Options nosniff;
        access_log off;
        try_files $uri =404;
        
        # 启用gzip_static如果存在预压缩文件
        gzip_static on;
        sendfile on;
    }
    
    # 字体文件缓存优化
    location ~* \.(eot|ttf|woff|woff2|otf)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Access-Control-Allow-Origin "*";
        access_log off;
        try_files $uri =404;
        sendfile on;
    }

    # 屏蔽常见扫描器/恶意 User-Agent
    if ($http_user_agent ~* (curl|python|scrapy|wget|scan|bot|spider|crawler) ) {
        return 444;
    }

    # 处理 finance-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /finance-bin/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/finance-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 account-bin 下的 Swagger 文档 JSON 文件
    location = /finance-bin/docs/api-docs.json {
        proxy_pass http://finance-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务
    location ^~ /finance-bin/ {
        proxy_pass http://finance-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 account-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /account-bin/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/account-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 account-bin 下的 Swagger 文档 JSON 文件
    location = /account-bin/docs/api-docs.json {
        proxy_pass http://account-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至账户微服务
    location ^~ /account-bin/ {
        proxy_pass http://account-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 pay-bin/payout 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/payout/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/payment-payout-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin/payout 下的 Swagger 文档 JSON 文件
    location = /pay-bin/payout/docs/api-docs.json {
        proxy_pass http://pay-payout-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关转账微服务
    location ^~ /pay-bin/payout/ {
        proxy_pass http://pay-payout-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 pay-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /pay-bin/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/payment-gateway-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 pay-bin 下的 Swagger 文档 JSON 文件
    location = /pay-bin/docs/api-docs.json {
        proxy_pass http://pay-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至支付网关微服务
    location ^~ /pay-bin/ {
        proxy_pass http://pay-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 report-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /report-bin/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/reporting-service/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 report-bin 下的 Swagger 文档 JSON 文件
    location = /report-bin/docs/api-docs.json {
        proxy_pass http://reporting-swoole/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至数据上报微服务
    location ^~ /report-bin/ {
        proxy_pass http://reporting-swoole/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 处理 cgi-bin 下的 Swagger UI 资源请求 - 使用更精确的匹配
    location ^~ /cgi-bin/docs/asset/ {
        alias /var/www/develop/cczhaoche/micro-services/laravel-api-app/vendor/swagger-api/swagger-ui/dist/;
        expires 1h;
        add_header Cache-Control "public";
    }
    # 处理 cgi-bin 下的 Swagger 文档 JSON 文件
    location = /cgi-bin/docs/api-docs.json {
        proxy_pass http://swoole2/docs/api-docs.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    # 反向代理至主业微服务
    location ^~ /cgi-bin/ {
        proxy_pass http://swoole2/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 反向代理至Flask Chat Service
    # location ^~ /chat-bin/ {
    #     proxy_pass http://flask-chat-service/;
    #     proxy_http_version 1.1;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    # }


    location ^~ /micro-apps/invoice/ {
        alias /var/www/develop/cczhaoche/micro-apps/invoice/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invoice/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/invoice/index.html;
    }

    location ^~ /micro-apps/invitation/ {
        alias /var/www/develop/cczhaoche/micro-apps/invitation/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/invitation/index.html;
    }

    location ^~ /micro-apps/point-mall/ {
        alias /var/www/develop/cczhaoche/micro-apps/point-mall/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/point-mall/index.html;
    }

    location ^~ /micro-apps/saas/ {
        alias /var/www/develop/cczhaoche/micro-apps/saas-platform-admin/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/point-mall/index.html;
    }

    location ^~ /micro-apps/uniapp-driver/ {
        alias /var/www/develop/cczhaoche/micro-apps/uniapp-driver/;
        index index.html index.htm;

        # 总是返回 /micro-apps/invitation/index.html 让 Vue.js 处理路由
        try_files $uri $uri/ /micro-apps/uniapp-driver/index.html;
    }

    location ^~ /micro-apps/uniapp-passenger/ {
        alias /var/www/develop/cczhaoche/micro-apps/uniapp-passenger/;
        index index.html index.htm;
        try_files $uri $uri/ /micro-apps/uniapp-passenger/index.html;
    }

    location ^~ /micro-apps/chat/ {
        alias /var/www/develop/cczhaoche/micro-apps/vue-chat/;
        index index.html index.htm;
        try_files $uri $uri/ /micro-apps/vue-chat/index.html;
    }

    location ^~ /micro-apps/voice/ {
        alias /var/www/develop/cczhaoche/micro-apps/vue3-voice-ride/;
        index index.html index.htm;
        try_files $uri $uri/ /micro-apps/voice/index.html;
    }

     # 处理 WebSocket 请求，直接代理到独立的 WebSocket 服务
    # WebSocket 服务
    location = /ws {
        proxy_pass http://websocket_backend;  # 独立的 WebSocket 服务
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Scheme $scheme;
        proxy_set_header Server-Protocol $server_protocol;
        proxy_set_header Server-Name $server_name;
        proxy_set_header Server-Addr $server_addr;
        proxy_set_header Server-Port $server_port;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass php-fpm-56:9000;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        include fastcgi_params;
        
        # PHP-FPM 优化配置
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_read_timeout 300;
    }

    # index.php重定向
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    # 允许访问Let's Encrypt的.well-known目录
    location ~ /\.well-known {
        allow all;
    }
}


server {
	listen       80;
	server_name  h5.youmeng.higgses.com;
	default_type 'text/html';
	charset utf-8;	

# location ^/(.*)/qrcode {
	rewrite ^/qrcode/(.*) http://h5.youmeng.higgses.com/$1/index.html permanent;
# rewrite ^/taskCenter/(.*) http://h5.youmeng.higgses.com/user/index.html permanent;
# return;
# }

	location ^~ /video/ {
		root    /var/www/develop/have-dream/frontend/h5/;
	}
	location ^~ /user/ {
		root    /var/www/develop/have-dream/frontend/h5/;
		index  privacy.html privacy.htm;   
	}
	location ^~ /shop/ {
		root    /var/www/develop/have-dream/frontend/h5/;
		index  privacy.html privacy.htm;   
	}
	location ^~ /taskCenter/ {
		root    /var/www/develop/have-dream/frontend/h5/;
		index  privacy.html privacy.htm;   
	}
	location ^~ /task/ {
		root    /var/www/develop/have-dream/frontend/h5/;
		index  privacy.html privacy.htm;   
	}
	location ^~ /view/ {
		root   /var/www/develop/have-dream/frontend/h5/;
		index  index.html index.htm;
	}

	location ^~ /download {
		root   /var/www/develop/have-dream/frontend/h5/;
		index  index.html index.htm;
	}

	error_page   500 502 503 504  /50x.html;
	location = /50x.html {
		root   html;
	}

}


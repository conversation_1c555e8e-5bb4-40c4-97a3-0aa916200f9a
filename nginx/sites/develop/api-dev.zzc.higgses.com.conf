server {
    listen 80;
    server_name api-dev.zzc.higgses.com;
    return 301 https://$host$request_uri; # 更简洁的重定向方式
}

server {
    listen 443 ssl;
    #listen [::]:443 ssl ipv6only=on; # 若需启用IPv6，请取消注释

    ssl_certificate /etc/nginx/ssl/api-dev.zzc.higgses.com/api-dev.zzc.higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/api-dev.zzc.higgses.com/api-dev.zzc.higgses.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4';
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # 考虑仅保留TLSv1.2及更高版本以增强安全性
    ssl_prefer_server_ciphers on;

    server_name api-dev.zzc.higgses.com;
    root /var/www/develop/zhaozhengce/backend/laravel-app/public;
    index index.php index.html index.htm;

    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length 1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    location / {
        try_files $uri $uri/ /index.php$is_args$query_string;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_read_timeout 600s;
        include fastcgi_params;
    }
}

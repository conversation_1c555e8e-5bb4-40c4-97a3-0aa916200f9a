server {
    listen  80;

    server_name higgses.com www.higgses.com;
    root   /var/www/master/higgses/www/;
    index  index.html index.htm;

    # 添加字符集
    charset utf-8;

    # 优化gzip配置
    gzip on;
    gzip_comp_level 6;  # 降低到6，在压缩率和CPU使用之间取得平衡
    gzip_buffers 16 8k;
    gzip_min_length 1k;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/x-httpd-php image/svg+xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    gzip_proxied any;

    # 添加安全相关头信息
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Frame-Options "SAMEORIGIN";

    # 客户端缓存控制
    location ~* \.(html|htm)$ {
        expires 1h;
        add_header Cache-Control "public";
    }

    # 静态资源缓存优化
    location ~* \.(gif|jpg|jpeg|png|bmp|ico|webp|svg)$ {
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }

    location ~* \.(css|js)$ {
        expires 7d;
        add_header Cache-Control "public";
    }

    location ~* \.(eot|ttf|woff|woff2)$ {
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }

    # 错误页面配置
    error_page  404             /404.html;
    error_page  500 502 503 504 /50x.html;
    
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 允许Let's Encrypt验证
    location ~ /.well-known {
        allow all;
    }

    # 日志格式优化
    access_log /var/log/nginx/higgses.access.log;
    error_log /var/log/nginx/higgses.error.log;
}

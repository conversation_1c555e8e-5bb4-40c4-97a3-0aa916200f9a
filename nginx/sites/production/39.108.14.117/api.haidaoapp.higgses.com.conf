# HTTP重定向到HTTPS
server {
    listen 80;
    server_name api.haidaoapp.higgses.com;
    return 301 https://$host$request_uri; # 更简洁的重定向方式
}

# HTTPS服务器配置
server {
    listen 443 ssl;
    server_name api.haidaoapp.higgses.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/haidaoapp.higgses.com/haidaoapp.higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/haidaoapp.higgses.com/haidaoapp.higgses.com.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    # 静态文件配置
    root /var/www/master/island-travel-app/laravel8-api-app/public;
    index index.html index.htm index.php;
    access_log /var/log/nginx/api.haidaoapp.higgses.com.log params; # 确保日志路径正确且Nginx有写权限

    # 压缩配置
    gzip on;
    gzip_static on;
    gzip_comp_level 9;
    gzip_buffers 4 32k;
    gzip_min_length 1k;
    gzip_types text/plain application/json application/x-javascript application/css application/xml application/xml+rss text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    # 处理静态文件请求
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 处理PHP请求
    location ~ \.php$ {
        try_files $uri /index.php =404; # 尝试直接服务文件，如果不存在则重定向到index.php
        fastcgi_pass php-upstream; # 确保php-upstream已在nginx配置中定义
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_read_timeout 600; # 修复超时问题
        include fastcgi_params; # 包含FastCGI参数配置文件
    }
}

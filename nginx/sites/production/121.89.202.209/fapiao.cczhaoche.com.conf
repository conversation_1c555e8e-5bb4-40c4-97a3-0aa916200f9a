server {
    listen  80;
    listen 443 ssl; # 使用http2协议，同时明确ssl

    server_name fapiao.cczhaoche.com;
    root /var/www/master/cczhaoche/uniapp-fapiao-app;
    index  index.html index.htm;

    # SSL 证书配置，用于 HTTPS
    ssl_certificate /etc/nginx/ssl/fapiao.cczhaoche.com/fullchain.cer;
    ssl_certificate_key /etc/nginx/ssl/fapiao.cczhaoche.com/cczhaoche.com.key;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;  # 仅允许使用 TLS 1.2 和 TLS 1.3
    ssl_prefer_server_ciphers on;  # 优先选择服务器端的加密套件
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';  # 强制使用高强度加密算法
    ssl_session_timeout 1d;  # 设置 SSL 会话超时为 1 天
    ssl_session_cache shared:SSL:10m;  # 共享的 SSL 会话缓存，大小为 10MB
    ssl_stapling on;  # 启用 OCSP stapling 提升 SSL 证书的性能
    ssl_stapling_verify on;  # 验证 OCSP stapling 的响应

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 开启gzip
    gzip on;

    # 启用gzip压缩的最小文件，小于设置值的文件将不会压缩
    gzip_min_length 1k;

    # gzip 压缩级别，1-9，数字越大压缩的越好，也越占用CPU时间，后面会有详细说明
    gzip_comp_level 9;

    # 进行压缩的文件类型。javascript有多种形式。其中的值可以在 mime.types 文件中找到。
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/vnd.ms-fontobject font/ttf font/opentype font/x-woff image/svg+xml;

    # 是否在http header中添加Vary: Accept-Encoding，建议开启
    gzip_vary on;

    # 禁用IE 6 gzip
    gzip_disable "MSIE [1-6]\.";

    # 设置压缩所需要的缓冲区大小
    gzip_buffers 32 4k;

    # 设置gzip压缩针对的HTTP协议版本，没做负载的可以不用
    # gzip_http_version 1.0;
    # 反向代理微应用（独立服务器）
    location ^~ /micro-apps/ {
        proxy_pass http://**************/;  # 代理到 Vue.js 托管的服务器
    }

    # 开启缓存
    location ~* ^.+\.(ico|gif|jpg|jpeg|png)$ {
        access_log   off;
        expires      2d;
    }

    location ~* ^.+\.(css|js|txt|xml|swf|wav)$ {
        access_log   off;
        expires      24h;
    }

    location ~* ^.+\.(html|htm)$ {
        expires      1h;
    }

    location ~* ^.+\.(eot|ttf|otf|woff|svg)$ {
        access_log   off;
        expires max;
    }

}

server {

    listen 80;
    listen [::]:80;

    # For https
    # listen 443 ssl;
    # listen [::]:443 ssl ipv6only=on;
    # ssl_certificate /etc/nginx/ssl/default.crt;
    # ssl_certificate_key /etc/nginx/ssl/default.key;

    server_name hcms.higgses.com;
    root /var/www/master/higgses/hcms;
    index index.php index.html index.htm;

    
    #location  /zhangqiuzhe {
    #   proxy_pass http://hcms.higgses.com/zhangqiuzhe/;
    #}

    if (!-e $request_filename) {
        rewrite  ^(.*)$  /index.php?$1  last;
        break;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }

    access_log /var/log/nginx/hcms.higgses.com_access.log;
}

server {
    listen 80;
    server_name mall-beta.ly.higgses.com;
    rewrite ^(.*)$ https://${server_name}$1 permanent;
}

server {
    # For https
    listen 443 ssl;
    ssl_certificate /etc/nginx/ssl/ly.higgses.com/ly.higgses.com.pem;
    ssl_certificate_key /etc/nginx/ssl/ly.higgses.com/ly.higgses.com.key;

    server_name mall-beta.ly.higgses.com;
    root /var/www/master/lanyifood/mall;
    index index.php index.html index.htm;
    access_log /var/log/nginx/mall-beta.ly.higgses.com.access.log;

    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    location ~ \.php$ {
        fastcgi_pass php-fpm-72:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }
}


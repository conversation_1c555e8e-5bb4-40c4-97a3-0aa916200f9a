# 管理 WebSocket 协议的连接升级机制
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

# WebSocket 服务的独立上游
upstream websocket_backend {
    server *************:1215;  # 独立的 WebSocket 服务
    keepalive 64;
}

# Laravel API 服务的负载均衡
upstream api_backend {
    # 1️⃣ 负载均衡绑定用户请求到同一实例：
    ip_hash;
    server *************:1215 weight=4 max_fails=2 fail_timeout=10s;
    server *************:1215 weight=2 max_fails=2 fail_timeout=10s;
    server *************:1215 weight=5 max_fails=2 fail_timeout=10s;
    keepalive 64;
}

server {
    listen 80;
    listen 443 ssl;
    server_name lightapi.cczhaoche.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cczhaoche.com/www.cczhaoche.com.pem;
    ssl_certificate_key /etc/nginx/ssl/cczhaoche.com/www.cczhaoche.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # 网站根目录
    root /var/www/master/cczhaoche/micro-services/laravel-api-app/public;
    index index.html index.htm index.php;
    access_log /var/log/nginx/lightapi.cczhaoche.com_access.log;

    # gzip 压缩
    gzip on;
    gzip_static on;
    gzip_comp_level 5;
    gzip_buffers 4 32k;
    gzip_min_length 1k;
    gzip_types text/plain application/json application/x-javascript application/css
               application/xml application/xml+rss text/javascript
               application/x-httpd-php image/jpeg image/gif image/png image/x-ms-bmp;
    gzip_vary on;

    # 处理 WebSocket 请求，直接代理到独立的 WebSocket 服务
    location = /ws {
        proxy_pass http://websocket_backend;  # 独立 WebSocket 服务
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Laravel API 服务的负载均衡
    location / {
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        try_files $uri @laravels;
    }

    location @laravels {
        proxy_pass http://api_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Real-PORT $remote_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 60s;
        proxy_read_timeout 120s;
    }
}

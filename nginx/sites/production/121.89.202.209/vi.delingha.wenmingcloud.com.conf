server {
    listen  80;

    server_name vi.delingha.wenmingcloud.com;
    root /var/www/master/wenming<PERSON>jian/frontend/visualization-app/current/dist;
    index  index.html index.htm;
    
    location / {
      try_files $uri $uri/ /index.html;
    }
    
    # 开启gzip
    gzip on;
    
    # 启用gzip压缩的最小文件，小于设置值的文件将不会压缩
    gzip_min_length 1k;
    
    # gzip 压缩级别，1-9，数字越大压缩的越好，也越占用CPU时间，后面会有详细说明
    gzip_comp_level 9;
    
    # 进行压缩的文件类型。javascript有多种形式。其中的值可以在 mime.types 文件中找到。
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/vnd.ms-fontobject font/ttf font/opentype font/x-woff image/svg+xml;
    
    # 是否在http header中添加Vary: Accept-Encoding，建议开启
    gzip_vary on;
    
    # 禁用IE 6 gzip
    gzip_disable "MSIE [1-6]\.";
    
    # 设置压缩所需要的缓冲区大小     
    gzip_buffers 32 4k;
    
    # 设置gzip压缩针对的HTTP协议版本，没做负载的可以不用
    # gzip_http_version 1.0;
    
    
    # 开启缓存
    location ~* ^.+\.(ico|gif|jpg|jpeg|png)$ { 
        access_log   off; 
        expires      2d;
    }
    
    location ~* ^.+\.(css|js|txt|xml|swf|wav)$ {
        access_log   off;
        expires      24h;
    }
    
    location ~* ^.+\.(html|htm)$ {
        expires      1h;
    }
    
    location ~* ^.+\.(eot|ttf|otf|woff|svg)$ {
        access_log   off;
        expires max;
    }

}

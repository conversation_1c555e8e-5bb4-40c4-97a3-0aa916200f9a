server {

    listen 80;
    listen [::]:80;

    server_name demo.crm.higgses.com;
    root /var/www/master/higgses/demo-crm;
    index index.php index.html index.htm;

    location ~ \.php {
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        include       fastcgi_params;
    }

}

server {
    listen  80;
    server_name **************;

    # Gzip 压缩配置，减少传输数据量，提高加载速度
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_comp_level 5;
    gzip_buffers 16 8k;

    # SPA 应用的反向代理和优化
    location /invitation/ {
        alias /var/www/master/cczhaoche/micro-apps/invitation/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /invitation/index.html;  # 当找不到路径时，返回 SPA 的 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }

    location /invoice/ {
        alias /var/www/master/cczhaoche/micro-apps/invoice/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /invoice/index.html;  # Vue.js 前端路由，确保返回 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }
   
    location /point-mall/ {
        alias /var/www/master/cczhaoche/micro-apps/point-mall/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /point-mall/index.html;  # Vue.js 前端路由，确保返回 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }

    location /saas/ {
        alias /var/www/master/cczhaoche/micro-apps/posaas-platform-admin/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /saas-platform-admin/index.html;  # Vue.js 前端路由，确保返回 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }
   
    location /uniapp-driver/ {
        alias /var/www/master/cczhaoche/micro-apps/uniapp-driver/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /uniapp-driver/index.html;  # Vue.js 前端路由，确保返回 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }

    location /voice/ {
        alias /var/www/master/cczhaoche/micro-apps/voice/;  # 使用 alias 映射目录
        index index.html index.htm;
        try_files $uri $uri/ /voice/index.html;  # Vue.js 前端路由，确保返回 index.html
        # 缓存静态资源 24 小时
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|ttf|svg|eot)$ {
            expires 24h;  # 设置静态文件的缓存时间为2小时
            add_header Cache-Control "public";
        }
    }

    # 禁止访问敏感文件或目录
    location ~ /\.ht {
        deny all;
    }
}

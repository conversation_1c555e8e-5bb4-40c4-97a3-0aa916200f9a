server {
    listen  80;
    listen 443 ssl;

    server_name mch.cczhaoche.com;
    root /var/www/master/cczhaoche/micro-apps/element-ui-cms-app;
    index  index.html index.htm;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cczhaoche.com/www.cczhaoche.com.pem;
    ssl_certificate_key /etc/nginx/ssl/cczhaoche.com/www.cczhaoche.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # 优化GZIP压缩配置
    gzip on;
    gzip_comp_level 4;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/javascript
        application/json
        application/x-javascript
        application/xml
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

    # 图片优化处理 - 为图片添加专门的缓存和处理规则
    location ~* \.(jpg|jpeg|png|gif)$ {
        expires 30d;  # 延长图片缓存时间到30天
        add_header Cache-Control "public, max-age=2592000";
        add_header X-Content-Type-Options nosniff;
        
        # 启用proxy_cache如果有配置缓存区域
        # proxy_cache images_cache;
        # proxy_cache_valid 200 304 30d;
        
        # 设置较大的buffer以处理大图片
        proxy_buffers 16 32k;
        proxy_buffer_size 64k;
        
        try_files $uri =404;
    }
    
    # 其他静态资源缓存优化
    location ~* \.(css|js|ico)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header X-Content-Type-Options nosniff;
        try_files $uri =404;
    }

     # 针对大型JS库文件的特殊处理
    location ~* \.(chunk-libs\.js|app\.js|chunk-elementUI\.js)$ {
        expires 30d;  # 延长缓存时间到30天
        add_header Cache-Control "public, max-age=2592000, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # 增加buffer大小以处理大型JS文件
        proxy_buffers 16 64k;
        proxy_buffer_size 128k;
        
        # 启用压缩
        gzip_static on;  # 如果有预压缩的.gz文件
        
        try_files $uri =404;
    }

}

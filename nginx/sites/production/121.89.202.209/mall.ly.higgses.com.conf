server {
    listen 80;

    server_name mall.ly.higgses.com;
    root /var/www/master/lanyifood/mall;
    index index.php index.html index.htm;
 
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    location ~ \.php$ {
        #try_files $uri /index.php =404;
        fastcgi_pass php-fpm-72:9000;
        fastcgi_index index.php;
        #fastcgi_buffers 16 16k;
        #fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        #fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }

    access_log /var/log/nginx/mall-beta.ly.higgses.com.access.log;
}

FROM nginx:alpine

LABEL maintainer="<PERSON><PERSON><PERSON> <<EMAIL>>"

COPY nginx.conf /etc/nginx/

# If you're in China, or you need to change sources, will be set CHANGE_SOURCE to true in .env.

ARG CHANGE_SOURCE=false
RUN if [ ${CHANGE_SOURCE} = true ]; then \
    # Change application source from dl-cdn.alpinelinux.org to aliyun source
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/' /etc/apk/repositories \
;fi

# 安装构建 Nginx 和模块的依赖
RUN apk update \
    && apk upgrade \
    && apk --update add logrotate \
    && apk add --no-cache openssl \
    && apk add --no-cache bash \
    && apk --no-cache add \
        gcc \
        libc-dev \
        make \
        pcre-dev \
        zlib-dev \
        openssl-dev \
        bash \
        curl \
        git

RUN apk add --no-cache curl

# 下载并解压 Nginx 源代码
RUN curl -LO http://nginx.org/download/nginx-1.27.1.tar.gz && \
    tar -zxvf nginx-1.27.1.tar.gz

# 下载 nginx-upstream-check-module 模块
RUN curl -LO https://github.com/yaoweibin/nginx_upstream_check_module/archive/master.tar.gz && \
    tar -zxvf master.tar.gz

# 编译 Nginx 并集成 nginx-upstream-check-module
WORKDIR nginx-1.27.1
RUN ./configure --prefix=/etc/nginx \
    --add-dynamic-module=../nginx_upstream_check_module-master \
    --with-cc-opt="-O2" \
    --with-http_ssl_module \
    --with-http_v2_module && \
    make && \
    make install

# 清理不需要的构建文件
RUN rm -rf /nginx-1.27.1 /master.tar.gz

RUN set -x ; \
    addgroup -g 82 -S www-data ; \
    adduser -u 82 -D -S -G www-data www-data && exit 0 ; exit 1

ARG PHP_UPSTREAM_CONTAINER=php-fpm
ARG PHP_UPSTREAM_PORT=9000

# Create 'messages' file used from 'logrotate'
RUN touch /var/log/messages

# Copy 'logrotate' config file
COPY logrotate/nginx /etc/logrotate.d/

# Set upstream conf and remove the default conf
RUN echo "upstream php-upstream { server ${PHP_UPSTREAM_CONTAINER}:${PHP_UPSTREAM_PORT}; }" > /etc/nginx/conf.d/upstream.conf \
    && rm /etc/nginx/conf.d/default.conf

ADD ./startup.sh /opt/startup.sh
RUN sed -i 's/\r//g' /opt/startup.sh
CMD ["/bin/bash", "/opt/startup.sh"]

EXPOSE 80 81 443

#!/bin/bash

# 定义证书文件路径
KEY_FILE="/root/.acme.sh/hiyamedia.cn_ecc/hiyamedia.cn.key"
FULLCHAIN_FILE="/root/.acme.sh/hiyamedia.cn_ecc/fullchain.cer"

# 远程服务器信息
REMOTE_SERVER="39.98.190.164"
REMOTE_KEY_FILE="/opt/docker/nginx/ssl/hiyamedia.cn/hiyamedia.cn.key"  # 私钥应为 .key 文件
REMOTE_FULLCHAIN_FILE="/opt/docker/nginx/ssl/hiyamedia.cn/fullchain.cer"  # fullchain 应为 .pem 文件
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# 1. 将证书文件传输到远程服务器
scp $KEY_FILE root@$REMOTE_SERVER:$REMOTE_KEY_FILE
scp $FULLCHAIN_FILE root@$REMOTE_SERVER:$REMOTE_FULLCHAIN_FILE

# 2. 在远程服务器上重新加载Nginx
ssh root@$REMOTE_SERVER "$NGINX_RELOAD_CMD"

echo "证书已传输并且远程Nginx已重新加载"

#!/bin/bash
# /root/.acme.sh/ly.higgses.com_ecc/mall-beta.ly.higgses.com-deploy-cert.sh

# 定义证书文件路径
CERT_DIR="/root/.acme.sh/ly.higgses.com_ecc"
KEY_FILE="${CERT_DIR}/ly.higgses.com.key"
FULLCHAIN_FILE="${CERT_DIR}/fullchain.cer"

# 远程服务器信息
REMOTE_SERVER="121.89.202.209"
REMOTE_DIR="/opt/docker/nginx/ssl/ly.higgses.com"
REMOTE_KEY_FILE="${REMOTE_DIR}/ly.higgses.com.key"
REMOTE_FULLCHAIN_FILE="${REMOTE_DIR}/ly.higgses.com.pem"
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# 检查证书文件是否存在
if [ ! -f "$KEY_FILE" ] || [ ! -f "$FULLCHAIN_FILE" ]; then
    echo "错误：证书文件不存在，请检查路径"
    exit 1
fi

# 1. 将证书文件传输到远程服务器
echo "正在传输证书文件到远程服务器..."
scp -q "$KEY_FILE" "root@${REMOTE_SERVER}:${REMOTE_KEY_FILE}"
if [ $? -ne 0 ]; then
    echo "错误：私钥文件传输失败"
    exit 1
fi

scp -q "$FULLCHAIN_FILE" "root@${REMOTE_SERVER}:${REMOTE_FULLCHAIN_FILE}"
if [ $? -ne 0 ]; then
    echo "错误：证书文件传输失败"
    exit 1
fi

# 2. 在远程服务器上重新加载Nginx
echo "正在重启远程Nginx服务..."
ssh "root@${REMOTE_SERVER}" "$NGINX_RELOAD_CMD"
if [ $? -ne 0 ]; then
    echo "错误：Nginx重启失败"
    exit 1
fi

echo "✅ 证书已成功部署，远程Nginx已重新加载"

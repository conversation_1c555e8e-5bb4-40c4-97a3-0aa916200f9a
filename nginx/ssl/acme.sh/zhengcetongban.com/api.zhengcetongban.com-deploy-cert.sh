#!/bin/bash
# /root/.acme.sh/zhengcetongban.com_ecc/api.zhengcetongban.com-deploy-cert.sh
#
# 描述: 该脚本用于将SSL证书文件部署到远程Nginx服务器并重启服务
# 作者: 系统管理员
# 最后更新: $(date +%Y-%m-%d)

# 启用错误追踪
set -e

# 记录日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 错误处理函数
handle_error() {
    log "错误: $1"
    exit 1
}

# 定义证书文件路径
CERT_DIR="/root/.acme.sh/zhengcetongban.com_ecc"
KEY_FILE="${CERT_DIR}/zhengcetongban.com.key"
FULLCHAIN_FILE="${CERT_DIR}/fullchain.cer"

# 远程服务器信息
REMOTE_SERVER="**************"
REMOTE_USER="root"
REMOTE_DIR="/opt/docker/nginx/ssl/api.zhengcetongban.com" 
REMOTE_KEY_FILE="${REMOTE_DIR}/zhengcetongban.com.key"
REMOTE_FULLCHAIN_FILE="${REMOTE_DIR}/zhengcetongban.com.cer"
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# SSH/SCP 连接参数
SSH_OPTS="-o ConnectTimeout=10 -o BatchMode=yes -o StrictHostKeyChecking=accept-new"
SCP_OPTS="-q ${SSH_OPTS}"

# 检查证书文件是否存在
log "正在检查本地证书文件..."
[ -f "$KEY_FILE" ] || handle_error "私钥文件不存在: $KEY_FILE"
[ -f "$FULLCHAIN_FILE" ] || handle_error "证书文件不存在: $FULLCHAIN_FILE"

# 确保远程目录存在
log "确保远程目录存在..."
ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "mkdir -p ${REMOTE_DIR}" || handle_error "创建远程目录失败"

# 备份远程证书文件
log "备份远程证书文件..."
BACKUP_DATE=$(date +%Y%m%d%H%M%S)
ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "if [ -f ${REMOTE_KEY_FILE} ]; then cp ${REMOTE_KEY_FILE} ${REMOTE_KEY_FILE}.${BACKUP_DATE}.bak; fi" || log "警告: 备份私钥文件失败，继续执行..."
ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "if [ -f ${REMOTE_FULLCHAIN_FILE} ]; then cp ${REMOTE_FULLCHAIN_FILE} ${REMOTE_FULLCHAIN_FILE}.${BACKUP_DATE}.bak; fi" || log "警告: 备份证书文件失败，继续执行..."

# 1. 将证书文件传输到远程服务器
log "正在传输证书文件到远程服务器..."
scp ${SCP_OPTS} "$KEY_FILE" "${REMOTE_USER}@${REMOTE_SERVER}:${REMOTE_KEY_FILE}" || handle_error "私钥文件传输失败"
scp ${SCP_OPTS} "$FULLCHAIN_FILE" "${REMOTE_USER}@${REMOTE_SERVER}:${REMOTE_FULLCHAIN_FILE}" || handle_error "证书文件传输失败"

# 设置适当的权限
log "设置证书文件权限..."
ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "chmod 600 ${REMOTE_KEY_FILE} ${REMOTE_FULLCHAIN_FILE}" || handle_error "设置证书文件权限失败"

# 2. 在远程服务器上重新加载Nginx
log "正在重启远程Nginx服务..."
ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "$NGINX_RELOAD_CMD" || handle_error "Nginx重启失败"

# 验证Nginx服务状态
log "验证Nginx服务状态..."
NGINX_STATUS=$(ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_SERVER}" "docker ps | grep higgses-nginx | grep -c Up" || echo "0")
if [ "$NGINX_STATUS" -eq "0" ]; then
    handle_error "Nginx服务未正常运行，请手动检查"
fi

log "✅ 证书已成功部署，远程Nginx已重新加载并正常运行"

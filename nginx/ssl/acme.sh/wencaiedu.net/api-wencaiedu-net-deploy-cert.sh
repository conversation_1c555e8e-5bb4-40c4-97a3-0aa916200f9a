#!/bin/bash

# 定义证书文件路径
KEY_FILE="/root/.acme.sh/wencaiedu.net_ecc/wencaiedu.net.key"
FULLCHAIN_FILE="/root/.acme.sh/wencaiedu.net_ecc/fullchain.cer"
# 远程服务器信息
REMOTE_SERVER="8.130.8.43"
REMOTE_KEY_FILE="/mnt/docker/docker/nginx/ssl/api.wencaiedu.net/wencaiedu.net.key"  # 私钥应为 .key 文件
REMOTE_FULLCHAIN_FILE="/mnt/docker/docker/nginx/ssl/api.wencaiedu.net/fullchain.cer"  # fullchain 应为 .pem 文件
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# 1. 将证书文件传输到远程服务器
scp $KEY_FILE root@$REMOTE_SERVER:$REMOTE_KEY_FILE
scp $FULLCHAIN_FILE root@$REMOTE_SERVER:$REMOTE_FULLCHAIN_FILE

# 2. 在远程服务器上重新加载Nginx
ssh root@$REMOTE_SERVER "$NGINX_RELOAD_CMD"

echo "证书已传输并且远程Nginx已重新加载"

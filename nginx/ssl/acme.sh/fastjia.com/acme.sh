# 使用 acme.sh 为 fastjia.com 及其子域名申请通配符 SSL 证书
# 使用 DNS 验证方式（DNSpod 提供商），使用自定义账户配置并在调试模式下强制更新
~/.acme.sh/acme.sh --issue --dns dns_dp -d fastjia.com -d "*.fastjia.com" --accountconf ~/.acme.sh/account.conf --force --debug

# 证书获取成功后，部署到远程服务器
if [ $? -eq 0 ]; then
    echo "证书获取成功，开始部署..."
    bash $(dirname "$0")/xiaoji.fastjia.com-deploy-cert.sh
else
    echo "证书获取失败，跳过部署步骤。"
    exit 1
fi

# 证书文件位置参考：
# [Wed 21 May 2025 10:37:14 AM CST] 证书文件位置：/root/.acme.sh/fastjia.com_ecc/fastjia.com.cer
# [Wed 21 May 2025 10:37:14 AM CST] 证书密钥位置：/root/.acme.sh/fastjia.com_ecc/fastjia.com.key
# [Wed 21 May 2025 10:37:14 AM CST] 中间证书位置：/root/.acme.sh/fastjia.com_ecc/ca.cer
# [Wed 21 May 2025 10:37:14 AM CST] 完整证书链位置：/root/.acme.sh/fastjia.com_ecc/fullchain.cer

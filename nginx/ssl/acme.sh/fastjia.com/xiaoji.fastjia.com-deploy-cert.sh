#!/bin/bash

# 定义证书文件路径
KEY_FILE="/root/.acme.sh/fastjia.com_ecc/fastjia.com.key"
FULLCHAIN_FILE="/root/.acme.sh/fastjia.com_ecc/fullchain.cer"

# 远程服务器信息    
REMOTE_SERVER="39.98.190.164"
REMOTE_KEY_FILE="/opt/docker/nginx/ssl/fastjia.com/fastjia.com.key"      # 私钥文件（.key格式）
REMOTE_FULLCHAIN_FILE="/opt/docker/nginx/ssl/fastjia.com/fastjia.com.cer"  # 完整证书链文件（.cer格式）
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# 1. 将证书文件传输到远程服务器
scp $KEY_FILE root@$REMOTE_SERVER:$REMOTE_KEY_FILE
scp $FULLCHAIN_FILE root@$REMOTE_SERVER:$REMOTE_FULLCHAIN_FILE

# 2. 重启远程服务器上的Nginx容器
ssh root@$REMOTE_SERVER "$NGINX_RELOAD_CMD"

echo "证书已成功部署到远程服务器，并已重启Nginx容器"

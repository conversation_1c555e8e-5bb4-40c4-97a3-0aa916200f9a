#!/bin/bash


# -----<PERSON>ND CERTIFICATE-----
# [Sat 01 Mar 2025 10:25:38 PM CST] Your cert is in: /root/.acme.sh/babyhost.higgses.com_ecc/babyhost.higgses.com.cer
# [Sat 01 Mar 2025 10:25:38 PM CST] Your cert key is in: /root/.acme.sh/babyhost.higgses.com_ecc/babyhost.higgses.com.key
# [Sat 01 Mar 2025 10:25:38 PM CST] The intermediate CA cert is in: /root/.acme.sh/babyhost.higgses.com_ecc/ca.cer
# [Sat 01 Mar 2025 10:25:38 PM CST] And the full-chain cert is in: /root/.acme.sh/babyhost.higgses.com_ecc/fullchain.cer

# 定义证书文件路径
KEY_FILE="/root/.acme.sh/babyhost.higgses.com_ecc/babyhost.higgses.com.key" #ssl_certificate_key 必须是 私钥（.key）
FULLCHAIN_FILE="/root/.acme.sh/babyhost.higgses.com_ecc/fullchain.cer" # ssl_certificate 必须是 完整证书链（fullchain.cer）

# 远程服务器信息
REMOTE_SERVER="*************"
# 远程证书目录
REMOTE_SSL_DIR="/opt/docker/nginx/ssl/babyhost.higgses.com"
REMOTE_KEY_FILE="$REMOTE_SSL_DIR/babyhost.higgses.com.key"  # 私钥应为 .key 文件
REMOTE_FULLCHAIN_FILE="$REMOTE_SSL_DIR/babyhost.higgses.com.pem"  # fullchain 应为 .pem 文件

# 确保远程目录存在
ssh root@$REMOTE_SERVER "mkdir -p $REMOTE_SSL_DIR"
NGINX_RELOAD_CMD="docker restart higgses-nginx"

# 1. 将证书文件传输到远程服务器
scp $KEY_FILE root@$REMOTE_SERVER:$REMOTE_KEY_FILE
scp $FULLCHAIN_FILE root@$REMOTE_SERVER:$REMOTE_FULLCHAIN_FILE

# 2. 在远程服务器上重新加载Nginx
ssh root@$REMOTE_SERVER "$NGINX_RELOAD_CMD"

echo "证书已传输并且远程Nginx已重新加载"

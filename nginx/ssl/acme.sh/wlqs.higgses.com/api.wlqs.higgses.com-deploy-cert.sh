#!/bin/bash
# /root/.acme.sh/wlqs.higgses.com_ecc/api-wlqs-higgses-com-deploy-cert.sh

# Docker 容器名称
CONTAINER_NAME="higgses-nginx"

cp -rf /root/.acme.sh/wlqs.higgses.com_ecc/wlqs.higgses.com.key /mnt/docker/nginx/ssl/api.wlqs.higgses.com/wlqs.higgses.com.key
cp -rf /root/.acme.sh/wlqs.higgses.com_ecc/fullchain.cer /mnt/docker/nginx/ssl/api.wlqs.higgses.com/fullchain.cer

# 2. 在容器内重新加载 Nginx
docker exec $CONTAINER_NAME nginx -s reload

echo "证书已传输并且 Nginx 已重新加载"

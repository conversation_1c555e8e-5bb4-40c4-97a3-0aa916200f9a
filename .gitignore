.idea
/logs
/data
.env
/.project
.docker-sync
/jenkins/jenkins_home

/logstash/pipeline/*.conf
/logstash/config/pipelines.yml

/nginx/ssl/*.crt
/nginx/ssl/*.key
/nginx/ssl/*.csr

/.devcontainer/*
!/.devcontainer/devcontainer.example.json
!/.devcontainer/docker-compose.extend-example.yml

docker-compose.custom.yml

.DS_Store
*~
docker-compose.yml
./keepalived/keepalived.conf
./keepalived/check_keepalived.sh
elk-docker/elasticsearch/data/
java-web-containner/
!redis/production/docker-compose.yml
!redis/pre-release/docker-compose.yml
!redis/dev/docker-compose.yml

!rabbitmq/production/docker-compose.yml
!rabbitmq/pre-release/docker-compose.yml
rabbitmq/pre-release/data/

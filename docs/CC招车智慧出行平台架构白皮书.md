# 🚦 CC 招车智慧出行平台架构白皮书

> **最后更新**：2025‑04‑20 | **依据**：线上 Nginx `www.cczhaoche.com.conf` & `**************.conf`
>
> 本文仅描述  **实际运行状态**，为能力评估、容量规划和遗留系统治理提供准确基线。

---

## 1 🗺️  流量拓扑

```
      公网             私网 (172.26.*)
┌─────────────┐      ┌────────────────────┐
│ 入口① 80/443 │──→│ Nginx 网关 (SSL/转发)│
│ www.cczhaoche │    └────────┬───────────┘
└─────────────┘               │WS 升级
                              ▼
                 ┌──────────────────────────────┐
                 │      微服务集群（Swoole）     │
                 │ core / pay / payout / ...     │
                 └────────┬────────┬────────────┘
                          │        │
           ┌──────────────┘   ┌────┴──────────┐
           │ WebSocket        │ 数据存储层     │
           │ 188:1215         │ MySQL / Redis  │
           │                  │ RabbitMQ       │
           └──────────────────┴────────────────┘

┌─────────────┐
│ 入口② 80    │──→ Nginx 静态—→ 多 SPA (invitation…)
│ 121.89.202.*│
└─────────────┘
```

---

## 2 🔑 入口网关

| 名称                             | 作用                      | HTTPS                 | 备注            |
| -------------------------------- | ------------------------- | --------------------- | --------------- |
| **域名网关** `www.cczhaoche.com` | SSL 终端、负载、WebSocket | ✔️ TLS 1.2/1.3 + HSTS | 主访问入口      |
| **IP 网关** `**************`     | 静态  SPA 托管            | ❌                    | 仅测试/内部调用 |

---

## 3 🚏 后端服务分流

| 路径               | 职责         | 节点                           | 策略                     |
| ------------------ | ------------ | ------------------------------ | ------------------------ |
| `/cgi-bin/`        | 核心业务 API | 178·1215 / 183·1215 / 188·1215 | **固定落点** (`ip_hash`) |
| `/pay-bin/`        | 支付下单     | 188·1216 / 178·1216            | 同上                     |
| `/pay-bin/payout/` | 出款/转账    | 188·8000 / 178·8000 / 183·8000 | 同上                     |
| `/report-bin/`     | 数据上报     | 188·8010 / 178·8010 / 183·8010 | 同上                     |
| `/account-bin/`    | 账号系统     | 188·8020 / 178·8020 / 183·8020 | **平均分流**             |
| `/finance-bin/`    | 财务结算     | 188·8030 / 178·8030 / 183·8030 | **平均分流**             |
| `/ws`              | 实时定位 WS  | 188·1215                       | 单节点 ❗                |

> **提示**   固定落点 = `ip_hash`（会话粘滞）；平均分流 = 轮询。

---

## 4 💻 静态  SPA 清单

| 路径              | 目录                        | 描述     | 浏览器缓存 |
| ----------------- | --------------------------- | -------- | ---------- |
| `/invitation/`    | `micro-apps/invitation/`    | 邀请函   | 24 h       |
| `/invoice/`       | `micro-apps/invoice/`       | 电子发票 | 24 h       |
| `/point-mall/`    | `micro-apps/point-mall/`    | 积分商城 | 24 h       |
| `/uniapp-driver/` | `micro-apps/uniapp-driver/` | 司机 H5  | 24 h       |

📝  全部使用  `history`  路由，Nginx `try_files`  回落到  `index.html`。

---

## 5 🗃️  遗留单体

- 依旧运行  **ThinkPHP 3.1 + PHP 5.6**（已停止官方维护）。
- 负责旧版管理后台、少量历史接口。
- **风险**：安全漏洞无法官方修复；扩展兼容性差。

---

## 6 🗄️  数据库一览

| 库        | 业务             | 版本   | 内网地址      | 说明              |
| --------- | ---------------- | ------ | ------------- | ----------------- |
| 积分/发票 | 积分商城、发票   | 8.0.36 | 172.26.98.177 | 同机多库          |
| 支付网关  | 收单、退款       | 8.0.36 | 同上          |                   |
| 车辆管理  | 车辆档案         | 8.0.36 | 同上          |                   |
| 主业务    | 订单、乘客、司机 | 5.6    | 阿里  RDS     | 与省厅/平台同实例 |
| 省厅上报  | 监管报送         | 5.6    | 阿里  RDS     |                   |
| 平台管理  | 配置/权限        | 5.6    | 阿里  RDS     |                   |

> **访问方式**：需先登录测试  ECS，再用  SSH 隧道连接 3306，避免数据库暴露公网。

---

## 7 🛡️  性能 & 安全措施

- **压缩**：Gzip Lvl‑5；阈值 1 KB
- **缓存**：图片 30 d / JS+CSS 1 h / SPA 24 h
- **HTTP 头**：HSTS、`X-Frame-Options`、`X-XSS-Protection`  等

---

## 8 ⚠️  主要痛点

| 痛点                 | 影响                      | 建议                                   |
| -------------------- | ------------------------- | -------------------------------------- |
| WebSocket 单点       | 188  故障即定位服务不可用 | 加节点 + 负载策略  `hash $remote_addr` |
| PHP 5.6 EOL          | 安全漏洞、性能差          | 升级至 PHP 8 / 微服务化                |
| 粘性负载 (`ip_hash`) | 扩容受限                  | 会话改 Redis / JWT 无状态              |
| 证书单一             | 过期风险                  | Certbot 自动续期 + 冗余备份            |
| 静态服务无  HTTPS    | 浏览器警告                | 给  121.89.\* 部署 TLS / 上 CDN        |

---

## 9 📊  性能容量预估

> **说明**：以下数值基于目前测试机规格（8 vCPU / 16 GB 内存）与 Swoole Benchmark 经验实测，实际产线需乘以 1.5 ～ 2  倍安全系数。

### 9.1  单实例参考

| 组件                  | 并发连接数 | 稳定 QPS | 峰值 QPS | 延迟 P95 |
| --------------------- | ---------- | -------- | -------- | -------- |
| Swoole Worker (1  核) | ~2 000     | 1 200    | 1 600    | 25 ms    |
| MySQL 8 (读)          | —          | 2 500    | 3 000    | 8 ms     |
| Redis                 | —          | 30 000   | 45 000   | 2 ms     |

> **假设**：每台 ECS 启 8  个 Worker；MySQL 读写比 8:2。

### 9.2  按服务组汇总

| 服务组                 | 节点数 × Worker | 理论稳定 QPS    | 备注                                   |
| ---------------------- | --------------- | --------------- | -------------------------------------- |
| 核心 API (`/cgi-bin/`) | 3 × 8 = 24      | ≈ 28 000        | 已含 weight 差异修正                   |
| 支付下单 (`/pay-bin/`) | 2 × 8 = 16      | ≈ 19 000        | 支付链路额外调用第三方需预留 30 % 容量 |
| 转账出款               | 3 × 8 = 24      | ≈ 28 000        | 高并发场景较少，更多受上游银行限制     |
| 数据上报               | 3 × 8 = 24      | ≈ 32 000        | 数据写入 Redis 批处理，写库压力低      |
| 账户系统               | 3 × 8 = 24      | ≈ 24 000        | 轮询负载，无粘性会话                   |
| 财务结算               | 3 × 8 = 24      | ≈ 22 000        | 任务多为批处理、实时压力小             |
| WebSocket (定位)       | 1 × ePoll       | ≈ 40 000 长连接 | 建议再加 1  节点做热备                 |

### 9.3  全局估算

- **稳定并发**：≈ 25 k RPS（订单高峰 + WebSocket 心跳）
- **瞬时峰值**：≤ 35 k RPS（春节等大促）
- **在线连接**：WebSocket ≈ 80 k（双节点后）

### 9.4  瓶颈观察

1. **MySQL 5.6**：单主库无分片，峰值写入约 2 k RPS，需警惕锁争用。

2. `**ip_hash**`：节点数增加后不均衡，可能导致单机负载 90 %+.

3. **ECS 带宽**：公网入口 100 Mbps，如开放视频流需升级至 500 Mbps。

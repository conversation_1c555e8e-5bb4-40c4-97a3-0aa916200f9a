# 📝CC招车定制客运网络平台微服务架构说明

本说明文档旨在为开发人员提供更加清晰、可操作的指导，聚焦于**定制客运**等核心业务的微服务架构。结合平台现有需求和未来规划，以下从技术栈、服务拆分、代码结构、前端方案、开发部署流程等角度进行说明。

## 一、概述

![CC招车定制客运网络平台微服务架构设计](./images/CC招车定制客运网络平台微服务架构设计.png)

![CC招车定制客运网络平台入口](./images/CC招车定制客运网络平台入口.png)

CC招车定制客运网络平台面向多种出行场景，其中最主要的业务是**定制客运**，可提供灵活、高效、安全的运力调度方案。目前已经完成核心功能的上线，但在微服务治理、监控、服务注册发现等方面仍存在尚未实施或正在规划的功能（在架构图中以灰色标示）。

## 二、技术栈说明（当前与规划）

### 2.1 后端语言与框架
- **PHP**（多版本并行）：
  - ThinPHP 3.1（PHP 5.6）
  - Laravel 8（PHP 7.4 + Swoole）
  - Laravel 11（PHP 8.3 + Swoole）
- **Swoole** 提升 PHP 并发能力；
- **Docker** 容器化，使用 Laradock 或自定义 Dockerfile。

### 2.2 通信协议
- **RESTful API** 提供主要业务交互；
- **WebSocket** 用于实时定位、消息推送等。

### 2.3 前端技术
为了适配多种使用场景，本平台的前端技术栈主要是：
- **Web 应用（PC端/管理端）**：
  - **Vue 2** 为主要框架；
  - 搭配 **Element UI** 以及 **element-admin-template** 进行后台管理界面开发；
  - 项目打包构建可使用 **Webpack**；
- **移动端 H5**：
  - 主要使用原生 JavaScript 和 Vue 2，结合移动端的 UI 组件；
  - 依赖 Webpack 构建或直接内嵌在 App 内。
- **小程序 & 多端**：
  - **UniApp** 用于一次开发、多端适配（微信、支付宝、字节系小程序等），底层仍然是基于 Vue 2；
  - 与后端通过 RESTful API 或 WebSocket 实现实时互动；
  - 布局设计需注重移动端的交互体验。
- **原生 App（如需）**：
  - 基于 **UniApp** 打包至 iOS（Swift/Objective-C）或 Android（Kotlin/Java）；
  - 目前主要功能可通过 H5 或小程序实现，原生仅在必要时使用。

### 2.4 消息队列
- **RabbitMQ** 已经投入使用，用于异步消息处理；
  - 适合处理如订单通知、支付回调、消息推送等异步任务；
  - 提供可靠的消息投递机制，可避免部分高并发场景下的阻塞。
- 未来若有需要，可考虑在部分场景引入 **Kafka**，但目前 RabbitMQ 足以支撑主要需求。

### 2.5 服务注册与发现（规划中）
- 目前的微服务访问通过配置与 Nginx 转发实现，**尚未引入 Consul** 或其他服务注册中心。

### 2.6 监控与可观测性（规划中）
- 目前主要依赖简单的日志输出和第三方监控服务；
- 计划上线 **Prometheus + Grafana**，但暂未全面落地。

### 2.7 数据库与存储
- **MySQL** 作为核心关系型数据库；
- **Redis** 用于缓存与实时数据存储；
- **Elasticsearch** 用于全文检索与统计分析；
- **阿里云OSS** 保存图片、附件等大文件。

---

## 三、微服务拆分与模块概览

1. **网关服务（API Gateway）**
   - 基于 Nginx 进行负载均衡与路由；
   - 实现统一的 JWT 鉴权、HTTPS 加密、限流；
   - 未来可考虑自研或使用成熟网关框架（Kong、Traefik）。

2. **订单服务（核心模块）**
   - 处理订单创建、支付、退款、评价等全生命周期；
   - 聚焦于 **定制客运** 功能，结合时段、距离进行调度匹配；
   - 使用 Laravel + Swoole 提升并发与性能。

3. **支付与结算服务**
   - 对接第三方支付（如微信、支付宝）；
   - 处理账务、乘客与司机收入结算；
   - 提供订单对账、退款接口。

4. **用户与司机管理服务**
   - 维护用户信息、司机资料、车辆信息；
   - 规划中的用户权益体系（优惠券、积分等）尚未上线。

5. **调度服务**
   - 专注于 **定制客运**：基于时段、距离进行最优司机或车辆的匹配；
   - 后续可接入更高级的智能算法或大数据平台。

6. **基础服务层**
   - **RabbitMQ**：已用于异步消息处理；
   - **日志收集与分析**：目前仅采用简单日志和 ELK；
   - **监控系统**：Prometheus + Grafana 部署尚未完成，临时使用第三方监控。

---

## 四、代码结构与开发指引

### 4.1 代码仓库与管理
- 采用 **GitLab** 管理各个服务的代码仓库；
- **分支策略**：
  - `master`：保持稳定；
  - `develop`：日常开发分支；
  - `feature/*`：新功能开发分支；
  - `hotfix/*`：紧急 bug 修复分支；
- 建议在 CI 阶段使用自动化测试（PHPUnit、Codeception 等）。

### 4.2 环境配置
1. **本地开发环境**（Docker Compose 或 Laradock）：
   - 包含 PHP、MySQL、Redis、Nginx、Node.js 等容器；
   - 前端可使用本地 Node.js 或 Docker 内 Node.js 进行打包编译；
   - 可以对接本地 ES 和 OSS 测试环境，或使用 Mock 服务；
   - PC端或管理端前端项目常用 Vue2 + Element UI + element-admin-template。
2. **测试环境**
   - 部署在 c.higgses.com；
   - 用于功能联调、Bug 修复、性能测试；
   - 支持多版本 PHP（5.6、7.4、8.3），前端可在此环境下进行多端兼容性测试。
3. **生产环境**
   - 部署在 www.cczhaoche.com；
   - 负载均衡 + 容器化，保证高并发下的可用性；
   - 更严格的安全、审计策略（HTTPS、WAF、防刷机制等）；
   - 前端项目可通过 CDN 加速或 Nginx 统一分发。

### 4.3 服务间通信
- **HTTP/REST** 形式调用，使用统一的签名鉴权或 JWT；
- **WebSocket** 主要用于实时定位、消息推送等场景；
- 未来可加入 **gRPC** 等高效通信方式，但需配合服务注册中心完成服务发现。

### 4.4 数据库与数据管理
- **MySQL**（主从或读写分离），关键业务表：订单、支付、用户、车辆等；
- **Redis**，用于 Token 缓存、实时队列与临时数据存储；
- **Elasticsearch**（可选），提升模糊搜索、统计分析性能；
- **阿里云OSS**，存储图片、附件等大文件；
- 数据迁移/版本管理：建议使用 **Laravel Migrations** 或 **Flyway** 等工具。

---

## 五、部署与运维

### 5.1 CI/CD 流程
1. **GitLab CI/CD**：
   - 仓库中已经提供自动部署 Shell 脚本；
   - 在完成功能开发后，拥有相应权限的开发人员可在本地执行该脚本，一键触发自动构建与部署流程；
   - 建议将单元测试、集成测试、Lint 检查等环节纳入脚本与 CI 配置中。

2. **自动化构建与镜像管理**：
   - CI 通过 Dockerfile 构建镜像，推送至镜像仓库；
   - 测试或生产环境拉取最新镜像并进行容器更新。

3. **灰度发布或回滚**：
   - 当前主要通过手动脚本或 Docker Compose/Swarm；
   - 未来可考虑 Kubernetes 等编排工具实现更智能的发布策略。

### 5.2 Docker 化与容器编排
- 目前主要使用 Docker Compose 或脚本形式进行多容器部署；
- **Kubernetes** 等容器编排方案未来可行，但尚未实施。

### 5.3 监控与日志
- 目前暂未接入 **Prometheus + Grafana**；后续上线后可实现更细粒度的监控与告警；
- 日志方面：使用简单日志+ELK 方案进行采集与分析；
- **异常处理**：建议在代码中统一使用自定义异常，并在网关层或应用层捕获并记录日志。

---

## 六、灰色功能模块（未上线或部分实现）
在架构图中，**灰色部分**表示尚未上线或仅在测试环境中验证的功能，如：
- **服务注册与发现**（Consul / Eureka 等）；
- **全链路追踪**（Zipkin / Jaeger）；
- **Prometheus + Grafana** 与自定义告警系统。

> **RabbitMQ** 已经上线并在使用，ELK 已有初步部署，其余功能仍在预研或规划阶段。

这些功能均是完善微服务架构的重要一环，未来可逐步纳入开发计划，以显著提升系统可观测性、可维护性。

---

## 七、未来优化与扩展
1. **完善调度算法**：
   - 专注于定制客运场景，结合时段、距离、车辆空座率等多因素，提高效率；
   - 后续可扩展至更多业务逻辑、路线计算算法。

2. **大数据与智能分析**：
   - 建立数据仓库，沉淀订单与用户画像，以便精准运营；
   - 向机器学习或 AI 推荐系统升级，提供个性化出行方案。

3. **安全合规**：
   - 针对定制客运、网约车相关法律法规持续迭代，确保合规；
   - 强化司机资质审核、乘客信息安全保护等。

4. **模块化微服务治理**：
   - 引入 Kubernetes/Service Mesh，简化服务编排与运维；
   - 加强熔断、限流、降级策略，防止单点故障和雪崩效应。

---

## 八、总结
本说明主要面向开发团队，着重介绍当前平台已实现的服务与尚在规划的功能，尤其在前端技术、微服务拆分、容器部署与 CI/CD 流程等方面提出实践建议。

**当前状态：**
- 核心业务（订单、支付、调度、用户管理）已上线，前端以 Vue2 为主；
- 移动端主要使用原生 JavaScript、Vue2、UniApp，必要时原生打包；
- **RabbitMQ** 已投入使用，负责异步任务处理；
- ELK 用于基础日志采集与分析，Consul、Prometheus + Grafana 等微服务治理与监控工具尚未部署；
- CI/CD 提供自动部署脚本，开发者可在本地执行完成构建与上线；
- **分支策略**：`master`（稳定）、`develop`（日常开发）、`feature/*`（功能开发）、`hotfix/*`（紧急修复）。

通过持续迭代、统一的 DevOps 流程以及容器化手段，本平台可在后续快速扩展以满足更多出行需求。开发团队应密切关注灰色功能的演进，同时不断加强前端性能优化与多端协作，在保障核心业务稳定的同时，为用户提供更佳的使用体验。

# CC 生产配置

## 服务器

~~~
    公网IP: **************
~~~

数据盘位于`/mnt`下

## 1. Nginx

- 确保您的包索引是最新的：

~~~
    sudo apt-get update
~~~~

- 安装 Nginx：

~~~
    sudo apt-get install nginx
~~~

- 启动 Nginx：

~~~
    sudo systemctl start nginx
~~~

- 设置开机启动：

~~~
    sudo systemctl enable nginx
~~~

- 检查 Nginx 状态：

~~~
    sudo systemctl status nginx
~~~

## 2. PHP

- 添加 PPA

~~~
    sudo add-apt-repository ppa:ondrej/php
~~~
- 安装 PHP 5.6

~~~
    sudo apt-get install php5.6
~~~

- 检查安装：

~~~
    php -v
~~~

## 3. PHP-FPM 

- 安装 PHP-FPM 5.6

~~~
    sudo apt install php5.6-fpm
~~~

- 检查 PHP-FPM 状态

~~~
    sudo systemctl status php5.6-fpm
~~~

- 确保它正在运行。如果没有，可以使用以下命令启动：

~~~
    sudo systemctl start php5.6-fpm
~~~

## 4. 配置站点

- 在 /etc/nginx/sites-available/ 目录下。假设我们要创建一个名为 mp.cczhaoche.com 的站点：

~~~
    sudo vi /etc/nginx/sites-available/mp.cczhaoche.com
~~~

- 在文件中添加以下配置：

~~~nginx
    server {
    # 监听 HTTP 和 HTTPS 端口
    listen 80;
    listen 443 ssl;  # 如果需要启用 HTTPS，则取消注释并配置证书

    server_name mp.cczhaoche.com;
    root /mnt/www/master/cczhaoche/thinkphp31-app;  # 网站根目录
    index index.php;  # 默认首页文件

    # 日志文件配置
    access_log /mnt/nginx/log/nginx/access/www.cczhaoche.com.access.log;
    error_log /mnt/nginx/log/nginx/error/www.cczhaoche.com.error.log;

    # SSL 配置
    ssl_certificate /mnt/nginx/cert/cczhaoche.com/www.cczhaoche.com.pem;  # SSL 证书路径
    ssl_certificate_key /mnt/nginx/cert/cczhaoche.com/www.cczhaoche.com.key;  # SSL 证书密钥路径
    ssl_session_timeout 5m;  # SSL 会话超时时间

    # 加密套件和协议配置
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;  # 加密算法选择
    ssl_protocols TLSv1.2 TLSv1.3;  # 优先使用更安全的 TLSv1.2 和 TLSv1.3
    ssl_prefer_server_ciphers on;  # 优先使用服务器端的加密套件

    # 网站首页的重写规则，访问不存在的文件时重定向到 index.php
    location / {
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    # PHP 文件处理配置
    location ~ \.php$ {
        fastcgi_pass unix:/run/php/php5.6-fpm.sock;  # PHP-FPM 套接字路径
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        
        # PHP 相关参数配置
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        include fastcgi_params;
    }

    # 子目录反向代理配置
    # 本地目录别名映射，直接读取服务器上的静态内容
    location /dingzhikeyun/ {
        alias /mnt/www/master/thinkphp31-app/dingzhikeyun/;  # 指定目录别名
        index index.html;  # 默认首页文件
    }

    # 反向代理到其他服务器上的 Laravel API
    location ^~ /cgi-bin/ {
        proxy_pass http://lightapi.cczhaoche.com/;  # API 服务地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 微应用反向代理配置（独立 Vue.js 应用托管服务器）
    location ^~ /micro-apps/ {
        proxy_pass http://**************/;  # Vue.js 托管的服务器
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 反向代理新闻子网站
    location ^~ /news/ {
        proxy_pass http://news.cczhaoche.com/;  # 子网站地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 图片、字体等静态资源的长期缓存
    location ~* \.(gif|jpg|jpeg|png|bmp|swf|woff|ttf|txt|pdf|apk)$ {
        expires 30d;  # 缓存时间为 30 天
        add_header Cache-Control "public";  # 设置缓存控制头
    }

    # JS 和 CSS 文件的短期缓存
    location ~* \.(js|css)$ {
        expires 1h;  # 缓存时间为 1 小时
        add_header Cache-Control "public";  # 设置缓存控制头
    }
}
~~~

- 在 sites-available 目录下创建一个符号链接到 sites-enabled 目录：

~~~
    sudo ln -s /etc/nginx/sites-available/mp.cczhaoche.com /etc/nginx/sites-enabled/
~~~

## 5. 调优

### 1. 调整 pm（进程管理）设置

在 Linux 系统中配置 PHP-FPM 进程管理设置。

#### 配置文件路径

- **Ubuntu**: `/etc/php/5.6/fpm/pool.d/www.conf`

#### 配置： 

~~~
    pm = dynamic
    pm.max_children = 800
    pm.start_servers = 50
    pm.min_spare_servers = 50
    pm.max_spare_servers = 150
    pm.max_requests = 5000  
~~~

#### 配置解释
- **`pm = dynamic`**  
  使用动态进程管理模式。PHP-FPM 将根据请求量自动增加或减少子进程数量。
- **`pm.max_children = 800`**  
  最大子进程数。根据服务器的内存来决定这个值。假设每个 PHP-FPM 进程使用 50MB 内存，150 个进程将消耗大约 7.5GB 内存。此值可根据实际情况调整，确保不会超过系统总内存。
- **`pm.start_servers = 50`**  
  启动时的进程数。可以设置为 5，取决于你的流量预期。
- **`pm.min_spare_servers = 50`**  
  最小空闲进程数。确保有足够的空闲进程以应对突发流量。
- **`pm.max_spare_servers = 150`**  
  最大空闲进程数。合理的空闲进程数可以避免长时间等待 PHP 进程启动。
- **`pm.max_requests = 5000`**  
  每个 PHP-FPM 子进程最大处理请求数。设置为 500 后，每个进程处理完 500 个请求就会被重启，帮助避免内存泄漏。

### 2. 开启 PHP 的 OPcache 扩展

opcache：开启 PHP 的 OPcache 扩展，可以显著提高 PHP 性能，减少每次请求的编译时间。确保在 php.ini 中启用 opcache。

配置文件路径

- **Ubuntu**:`/etc/php/5.6/cli/php.ini`

配置

~~~ini
opcache.enable=1           ; 启用 OPcache
opcache.memory_consumption=128  ; 设置 OPcache 的内存限制（单位为MB）
opcache.interned_strings_buffer=8 ; 设置字符串缓存的内存（单位为MB）
opcache.max_accelerated_files=10000 ; 设置 OPcache 缓存的最大文件数
opcache.revalidate_freq=60 ; 每 60 秒检查一次缓存的 PHP 文件是否被修改
~~~

PHP-FPM 重启：

~~~ 
    sudo systemctl restart php5.6-fpm
~~~

Nginx 重启:

~~~
    sudo nginx -s reload
~~~

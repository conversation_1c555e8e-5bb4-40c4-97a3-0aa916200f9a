# 使用 SSH 隧道在本地和 Docker 中访问远程内网数据库

如果数据库位于内网且需要通过 ECS 的 SSH 隧道访问，可以使用以下步骤在本地设置隧道：

### 1、创建 SSH 隧道

在本地运行以下命令，将内网数据库通过 ECS 映射到本地：

```
ssh -C -f -L 3307:rm-8vb1s46wz51xxo79h.mysql.zhangbei.rds.aliyuncs.com:3306 root@************* -N
```

- **3307**- ：本地映射的端口。
- **rm-8vb1s46wz51xxo79h.mysql.zhangbei.rds.aliyuncs.com**- ：数据库的内网地址。
- **3306**- ：数据库服务器的端口。
- *****************- ：ECS 的公网 IP 地址。
- -N- ** 参数**- ：表示只建立隧道，不执行远程命令。
- -f- ** 参数**- ：将 SSH 会话放到后台运行。
- -C 参数开启压缩，适合低带宽或高延迟的网络。
<!-- -->

运行此命令后，本地的 127.0.0.1:3307 即可映射到远程 ECS 的内网数据库地址，您可以通过 127.0.0.1:3307 访问远程数据库

### 2、数据库配置

在本地运行项目时，配置 .env 文件或数据库连接设置：

**本地环境**：

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1      # 本地地址
DB_PORT=3307           # 映射的本地端口
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
```
    
**在 Docker 容器中：**

如果项目运行在 Docker 容器中，需要将数据库主机地址设置为 host.docker.internal（macOS 和 Windows 支持，Linux 需额外配置）：

```
DB_CONNECTION=mysql
DB_HOST=host.docker.internal # 容器中访问主机地址
DB_PORT=3307                 # 映射的本地端口
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#!/bin/bash

# 定义服务器信息
SERVER="higgses-dev-root"         # SSH 配置中定义的服务器别名（请确保 ~/.ssh/config 中有相应的 Host 配置）
REMOTE_SCRIPT_PATH="/var/www/develop/cczhaoche/micro-services/wx-speex2wav-service/deploy-sandbox.sh"  # 服务器上 deploy-sandbox.sh 的完整路径
REMOTE_SCRIPT_BACKUP_PATH="/var/www/develop/cczhaoche/micro-services/wx-speex2wav-service/deploy-sandbox.sh.example"  # 备份脚本路径

# Step 1: 检查并复制备用脚本到目标脚本位置
echo "Step 1: 检查并复制备用脚本到目标脚本位置..."
ssh $SERVER "[ -f \"$REMOTE_SCRIPT_BACKUP_PATH\" ] && cp \"$REMOTE_SCRIPT_BACKUP_PATH\" \"$REMOTE_SCRIPT_PATH\" && chmod +x \"$REMOTE_SCRIPT_PATH\""
if [ $? -eq 0 ]; then
    echo "已将 \"$REMOTE_SCRIPT_BACKUP_PATH\" 复制到 \"$REMOTE_SCRIPT_PATH\"，并设置了可执行权限。"
else
    echo "Error: 无法复制 \"$REMOTE_SCRIPT_BACKUP_PATH\" 或设置可执行权限，请检查路径和权限。"
    exit 1
fi

# Step 2: 检查目标脚本是否存在并具有可执行权限
echo "Step 2: 检查远程脚本是否存在并具有执行权限..."
ssh $SERVER "[ -f \"$REMOTE_SCRIPT_PATH\" ] && [ -x \"$REMOTE_SCRIPT_PATH\" ]"
if [ $? -ne 0 ]; then
    echo "Error: 远程脚本 \"$REMOTE_SCRIPT_PATH\" 不存在或没有可执行权限。"
    exit 1
else
    echo "远程脚本 \"$REMOTE_SCRIPT_PATH\" 存在并且具有可执行权限。"
fi

# Step 3: 执行远程部署脚本
echo "Step 3: 执行远程部署脚本 \"$REMOTE_SCRIPT_PATH\"..."
ssh $SERVER "bash \"$REMOTE_SCRIPT_PATH\""
if [ $? -eq 0 ]; then
    echo "远程部署脚本执行成功！"
else
    echo "Error: 远程部署脚本执行失败。"
    exit 1
fi

echo "部署完成。"

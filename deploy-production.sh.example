#!/bin/bash

# 定义变量
REMOTE_SERVER="higgses-prod-root"  # 需要替换为实际的服务器信息
REMOTE_PATH="/var/www/master/cczhaoche/micro-apps/saas-platform-admin"  # 远程服务器部署目录
LOCAL_DIST="./dist"  # 本地构建输出目录

# 检查必要的命令是否存在
command -v npm >/dev/null 2>&1 || { echo "错误: 需要安装 npm 但未找到" >&2; exit 1; }
command -v rsync >/dev/null 2>&1 || { echo "错误: 需要安装 rsync 但未找到" >&2; exit 1; }
command -v node >/dev/null 2>&1 || { echo "错误: 需要安装 node 但未找到" >&2; exit 1; }

# 1. 确保在项目根目录
echo "步骤 1: 检查工作目录"
if [ ! -f "package.json" ]; then
    echo "错误: 请在项目根目录运行此脚本" >&2
    exit 1
fi

# 清理旧的构建文件
if [ -d "${LOCAL_DIST}" ]; then
    echo "清理旧的构建文件..."
    rm -rf ${LOCAL_DIST}
fi

# 2. 安装依赖
echo "步骤 2: 安装依赖"
yarn install
if [ $? -ne 0 ]; then
    echo "错误: 安装依赖失败" >&2
    exit 1
fi

# 3. 构建项目
echo "步骤 3: 构建项目"
yarn build
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败" >&2
    exit 1
fi

# 4. 检查构建输出目录
echo "步骤 4: 检查构建输出"
if [ ! -d "${LOCAL_DIST}" ]; then
    echo "错误: 构建输出目录不存在" >&2
    exit 1
fi

# 5. 创建远程目录（如果不存在）
echo "步骤 5: 确保远程目录存在"
ssh ${REMOTE_SERVER} "mkdir -p ${REMOTE_PATH}"
if [ $? -ne 0 ]; then
    echo "错误: 创建远程目录失败" >&2
    exit 1
fi

# 6. 部署到远程服务器
echo "步骤 6: 部署到远程服务器"
rsync -avz --delete ${LOCAL_DIST}/ ${REMOTE_SERVER}:${REMOTE_PATH}/
if [ $? -ne 0 ]; then
    echo "错误: 部署到远程服务器失败" >&2
    exit 1
fi

# 7. 设置远程目录权限
echo "步骤 7: 设置远程目录权限"
ssh ${REMOTE_SERVER} "chmod -R 755 ${REMOTE_PATH}"
if [ $? -ne 0 ]; then
    echo "错误: 设置远程目录权限失败" >&2
    exit 1
fi

echo "前端项目部署完成！" 

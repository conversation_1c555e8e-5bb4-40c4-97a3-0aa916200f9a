# 希格斯科技容器套件 Higgses Docker Kit

基于Laradock构建私有镜像，构建应用基础环境。

## Build 20240914

```bash

1. registry.cn-hongkong.aliyuncs.com/higgses/php-worker-74:1.7.1 PHP独立进程容器升级，增加ZIP扩展

```


## Build 20240914

```bash

1. registry.cn-hongkong.aliyuncs.com/higgses/nginx:1.4.0 升级Nginx版本到1.27.0，增加日志自定义日志格式
2. registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-74:1.3.0 PHP-FPM 7.4版本升级，调整内存大小为2048MB
3. registry.cn-hongkong.aliyuncs.com/higgses/workspace-74:1.4.0 PHP-CLI 7.4版本升级 调整内存大小为2048MB
4. registry.cn-hongkong.aliyuncs.com/higgses/php-worker-74:1.7.0 PHP独立进程容器升级，调整内存大小为2048MB
5. registry.cn-hongkong.aliyuncs.com/higgses/rabbitmq:1.1.0 RabbitMQ升级到3.13.7版本，延迟消息队列插件升级到3.13.0版本
6. registry.cn-hongkong.aliyuncs.com/higgses/redis-cluster:1.0.0 新增Redis集群镜像
7. registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-56:1.1.0 优化PHP5.6版本，调整内存大小为2048MB

以上配置基于build-docker-compose.yml文件构建镜像。启动容器需要在不同的环境配置中补充相关配置参数，如: arm64-docker-compose.yml

```

### 验证 Redis 集群节点

#### 检查Redis集群节点状态

使用 redis-cli 连接集群的某个节点（比如 7001 端口）并查看节点信息：

```bash
docker exec -it higgses-redis-cluster redis-cli -c -p 7001  cluster nodes
```
这个命令会返回 Redis 集群中的所有节点信息，包括节点 ID、IP 地址、角色（主/从）、状态等。

#### 检查Redis 集群功能

##### 测试数据写入和读取

连接到 Redis 集群，向集群写入和读取数据，以确保集群正常工作：
```bash
docker exec -it higgses-redis-cluster redis-cli -c -p 7000
```
在连接的 Redis CLI 中执行以下命令：
```bash
SET foo "bar"
```
接着在另一个节点中读取相同的数据：
```bash
docker exec -it higgses-redis-cluster redis-cli -c -p 7001 GET foo
```
如果返回值是 "bar"，说明 Redis 集群正常工作。


### 查看Redis集群槽位分配
使用 cluster slots 命令可以查看 Redis 集群的槽位分配情况：
```bash
docker exec -it higgses-redis-cluster redis-cli -c -p 7000 cluster slots
```
该命令将显示 Redis 集群中每个节点管理的槽位范围，确保槽位分配正常。




## Build 20220411

### 构建PHP-FPM容器

1. 本地构建因为网络原因99%概率失败，建议在可用服务器环境中进行克隆与构建
```bash
$ git clone https://codeup.aliyun.com/higgses/higgses/laradock.git
```

2. 配置环境变量和重命名docker-compose.yml
```bash
$ cp .env.example .env
```


3. 构建容器镜像
```bash
$ docker-compose -f build-docker-compose.yml build --no-cache php-fpm
```

4. 上传容器镜像版本至远程仓库

登录阿里云Docker Registry
```bash
sudo docker login --username=huiyonghkw@higgses registry.cn-hongkong.aliyuncs.com
```
用于登录的用户名为阿里云账号全名，密码为开通服务时设置的密码。您可以.env.example凭证页面找到密码。

将镜像推送到Registry
```bash
sudo docker tag b7f0932c8a96 registry.cn-hongkong.aliyuncs.com/higgses/nginx:1.3.0
sudo docker push registry.cn-hongkong.aliyuncs.com/higgses/nginx:1.3.0
```

查看构建成功镜像
```bash
docker images
REPOSITORY                                                   TAG          IMAGE ID       CREATED          SIZE
registry.cn-hongkong.aliyuncs.com/higgses/nginx              1.0.2        b7f0932c8a96   32 seconds ago   30MB
...
```


### 配置Nginx多个PHP版本
Docker Compose 会启动PHP7.4与PHP7.2两个PHP—FPM容器，Nginx默认使用PHP7.4版本PHP-FPM容器，如遇一些老的项目需要低版本的PHP，可以使用下面的命令使用PHP7.2版本的PHP-FPM容器。
进入Nginx 虚拟站点配置目录`/opt/docker/nginx/sites`，找到一个站点，在其行首添加下面的配置。
```bash
upstream php-upstream72 { server php-fpm-72:9000; }
```
在站点的php解析入口，配置
```bash
location ~ \.php$ {
    fastcgi_pass php-upstream72;
}
```
重启容器生效。


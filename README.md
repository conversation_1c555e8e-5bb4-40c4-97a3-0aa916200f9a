# 镜像制作
Dockerfile构建基础镜像 docker build -t registry.cn-hongkong.aliyuncs.com/higgses/wx-speex2wav:1.0.0 .

# 容器运行
docker run -d --name wx-speex2wav-service \
-e GUNICORN_WORKERS=2 \
-e OSS_ACCESS_KEY=LTAI5t76RC5avYxzysqw4TSG \
-e OSS_SECRET_KEY=****************************** \
-e OSS_END_POINT=https://oss-cn-huhehaote.aliyuncs.com \
-e OSS_HOST=https://cczhaoche-mall-storage.oss-cn-huhehaote.aliyuncs.com \
-e OSS_REGION=cn-huhehaote \
-e OSS_BUCKET_NAME=cczhaoche-mall-storage \
-e OSS_OBJECT_PATH=cczhaoche/voice-call/ \
-p 8000:8000 -v ./:/app \
registry.cn-hongkong.aliyuncs.com/higgses/wx-speex2wav:1.0.0

# 环境变量说明:
    GUNICORN_WORKERS: gunicorn进程数
    OSS_ACCESS_KEY: oss access key
    OSS_SECRET_KEY: oss secret key
    OSS_END_POINT: oss 上传接口地址
    OSS_HOST: oss 外网访问地址
    OSS_REGION: oss 地域
    OSS_BUCKET_NAME: oss 存储桶名称
    OSS_OBJECT_PATH: oss 存储桶对象路径
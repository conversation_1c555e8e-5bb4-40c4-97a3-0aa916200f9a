variables:
  RELEASES_STORAGE_DIR: '/var/www/$CI_COMMIT_REF_NAME/$CI_PROJECT_PATH/storage'
  CREATE_RELEASES_STORAGE_DIR: '[ -d $RELEASES_STORAGE_DIR ] || sudo mkdir -p $RELEASES_STORAGE_DIR'
  RELEASES_DIR: '/var/www/$CI_COMMIT_REF_NAME/$CI_PROJECT_PATH/releases'
  CREATE_RELEASE_DIR: '[ -d $RELEASES_DIR ] || sudo mkdir -p $RELEASES_DIR'
  NEW_RELEASES_DIR: '$RELEASES_DIR/$CI_COMMIT_SHORT_SHA'
  CREATE_NEW_RELEASES_DIR: '[ -d $NEW_RELEASES_DIR ] || sudo mkdir -p $NEW_RELEASES_DIR'
  BEFORE_CHMOD: 'sudo chown -R deployer:deployer $NEW_RELEASES_DIR'
  AFTER_CHMOD: 'sudo chown -R apache:apache /var/www/$CI_COMMIT_REF_NAME && sudo chown -R apache:apache $RELEASES_STORAGE_DIR && sudo chmod -R 777 $RELEASES_STORAGE_DIR'
  CD_NEW_RELEASES_DIR: 'cd $NEW_RELEASES_DIR'
  CD_RELEASES_DIR: 'cd $RELEASES_DIR'
  #Linux删除除了某个文件之外的所有文件/目录
  CLEAN_RELEASES_DIR: 'ls |grep -v $CI_COMMIT_SHORT_SHA |xargs sudo rm -rf'
  LN_RELEASE_DIR: 'sudo ln -nfs $NEW_RELEASES_DIR /var/www/$CI_COMMIT_REF_NAME/$CI_PROJECT_PATH/current'
  MV_REPO: 'sudo mv /home/<USER>/$CI_PROJECT_DIR/* $NEW_RELEASES_DIR'
  CP_DEV_ENV: 'cp -rf /home/<USER>/$CI_PROJECT_DIR/.env.dev $NEW_RELEASES_DIR/.env'
  CP_TEST_ENV: 'cp -rf /home/<USER>/$CI_PROJECT_DIR/.env.testing $NEW_RELEASES_DIR/.env'


before_script:
  - echo "Before script"
  - echo $CI_COMMIT_REF_NAME
  - echo $CI_PROJECT_PATH
  - echo $CI_COMMIT_SHORT_SHA
  - echo $CI_REPOSITORY_URL
  - echo $CI_PROJECT_DIR
  - 'eval $CREATE_RELEASES_STORAGE_DIR'  # will execute
  - 'eval $CREATE_RELEASE_DIR'  # will execute
  - 'eval $CREATE_NEW_RELEASES_DIR'  # will execute
  - 'eval $CD_NEW_RELEASES_DIR'

stages:
  - build
  - test
  - deploy-dev
  - deploy-test

building:
  stage: build
  script:
    - echo "Move repo..."
    - echo $NEW_RELEASES_DIR
    - 'eval $BEFORE_CHMOD'
    - 'eval $MV_REPO'

testing:
  stage: test
  script:
    - echo "testing..."
    # - php ./vendor/bin/phpunit

deploying_dev:
  stage: deploy-dev
  script:
    - echo "deploying dev..."
    - 'eval $CP_DEV_ENV'
    - cnpm install
    - cnpm run build
    - echo 'Linking current directory'
    - 'eval $AFTER_CHMOD'
    - 'eval $LN_RELEASE_DIR'
    - echo 'Removing earlier app'
    - 'eval $CD_RELEASES_DIR'
    - 'eval $CLEAN_RELEASES_DIR'
  only:
    - develop

deploying_test:
  stage: deploy-test
  script:
    - echo "deploying test..."
    - 'eval $CP_TEST_ENV'
    - cnpm install
    - cnpm run build
    - echo 'Linking current directory'
    - 'eval $AFTER_CHMOD'
    - 'eval $LN_RELEASE_DIR'
    - echo 'Removing earlier app'
    - 'eval $CD_RELEASES_DIR'
    - 'eval $CLEAN_RELEASES_DIR'
  only:
    - testing

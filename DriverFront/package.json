{"name": "vue-passenger", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^0.19.0", "core-js": "^3.3.2", "cube-ui": "~1.12.5", "js-cookie": "^2.2.1", "nprogress": "^0.2.0", "script-ext-html-webpack-plugin": "^2.1.4", "slider-verification-code": "^1.0.3", "svg-sprite-loader": "^4.1.6", "vue": "^2.6.10", "vue-amap": "^0.5.10", "vue-drag-verify": "^1.0.6", "vue-router": "^3.0.7", "vuex": "^3.0.1", "weixin-js-sdk": "^1.4.0-test"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-router": "^4.0.0", "@vue/cli-plugin-vuex": "^4.0.0", "@vue/cli-service": "^4.0.0", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^5.0.0", "fastclick": "^1.0.6", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss-px2rem": "^0.3.0", "prettier": "^1.18.2", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue-cli-plugin-cube-ui": "^0.2.5", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/prettier"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}, "postcss-px2rem": {"remUnit": 37.5}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11", "Android >= 4.0", "iOS >= 8"], "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}}
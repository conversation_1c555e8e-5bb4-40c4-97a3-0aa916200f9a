<template>
  <div class="nodata">
    <svg-icon slot="icon" class-name="nodata-dot" icon-class="nodata" />
    <p class="empty-description">{{ textword }}</p>
  </div>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    textword: {
      type: String,
      default: "暂无数据"
    }
  }
};
</script>
<style lang="less" scoped>
.nodata {
  text-align: center;
  padding: 30px 0;
  .nodata-dot {
    width: 184px;
    height: 100px;
    margin: 10px auto;
  }
  .empty-description {
    text-align: center;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>

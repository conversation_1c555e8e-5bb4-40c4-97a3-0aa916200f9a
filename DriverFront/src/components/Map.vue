<template>
  <div class="amap-page-container">
    <el-amap
      vid="amapDemo"
      :zoom="zoom"
      :zoomEnable="!lineStatus"
      :events="events"
      :doubleClickZoom="false"
      :center="center"
      :plugin="plugin"
      ref="map"
    >
      <!-- <el-amap-marker v-for="(marker,index) in markers"   :position="[marker.location.lng,marker.location.lat]"  :offset="offset"  :vid="index" :key="index" v-if="markers.length>0&&!showline">
                          <div class="marker-view">
                            <i class="marker-dot"></i>
                            <p>{{marker.name}}</p>
                          </div>
                      </el-amap-marker> -->
    </el-amap>
    <div class="center-dot">
      <transition name="fade" mode="out-in">
        <span v-if="mapstate && !lineStatus" class="map_state_icon"
          >上车位置</span
        >
      </transition>
      <img
        v-if="!mapstate && !lineStatus"
        src="../../assets/map-point.png"
        alt=""
      />
    </div>
    <div
      @click="resetLocation"
      v-if="!lineStatus && baseLocation != ''"
      class="rest-handle shandow"
      :style="'top:' + resetHeight + 'px'"
    >
      <img src="../../assets/reset.png" alt="" />
    </div>
    <div class="cover_map" v-if="showCover"></div>
  </div>
</template>
<script>
export default {
  data() {
    let that = this;
    return {
      mapstate: true,
      zoom: 16,
      offset: [-11, -35],
      center: [104.0797, 30.52087],
      mapobj: "",
      startMarker: "",
      plugin: [
        {
          showMarker: true, //是否显示定位点
          markerOptions: {
            //自定义定位点样式，同Marker的Options
            content: '<div class="cc-light"></div>'
          },
          showCircle: false, //是否显示定位精度圈
          showButton: false, //是否显示定位按钮
          pName: "Geolocation",
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                if (result && result.position) {
                  let loaction = [result.position.lng, result.position.lat];
                  that.center = loaction;
                  that.$store.commit("changefromlocation", loaction.join(","));
                  that.$store.commit("changebaselocation", loaction.join(","));
                  that.$nextTick();
                }
              });
            }
          }
        }
      ],
      events: {
        init: o => {
          that.mapobj = that.$refs.map.$$getInstance();
          that.mapobj.setMapStyle("amap://styles/whitesmoke");
          let startIcon = new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(24, 35),
            // 图标的取图地址
            image: require("../../assets/map-point.png"),
            // 图标所用图片大小
            imageSize: new AMap.Size(24, 35),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0)
          });
          let startMarker = new AMap.Marker({
            position: that.mapobj.getCenter(),
            icon: startIcon,
            offset: new AMap.Pixel(-11, -33),
            zIndex: 999,
            // 设置是否可拖拽
            draggable: false,
            cursor: "move"
          });
          that.startMarker = startMarker;
          startMarker.setMap(that.mapobj);
          startMarker.setAnimation("AMAP_ANIMATION_DROP");
        },
        movestart: () => {
          if (that.lineStatus) {
            return;
          }
          that.mapstate = false;
          that.startMarker.hide();
        },
        moveend: e => {
          if (that.lineStatus) {
            return;
          }
          that.mapstate = true;
          let mapzoom = that.$refs.map.$$getInstance().getZoom();
          let mapcenter = that.$refs.map.$$getInstance().getCenter();
          let lnglat = new AMap.LngLat(mapcenter.lng, mapcenter.lat);
          that.startMarker.setPosition(lnglat);
          that.startMarker.show();
          that.$store.commit(
            "changefromlocation",
            [mapcenter.lng, mapcenter.lat].join(",")
          );
        },
        zoomstart: () => {
          if (that.lineStatus) {
            return;
          }
          that.mapstate = false;
          that.startMarker.hide();
        },
        zoomend: o => {
          if (that.lineStatus) {
            return;
          }
          //地图层级变化
          let mapzoom = that.$refs.map.$$getInstance().getZoom();
          //获取地图当前中心坐标
          let mapcenter = that.$refs.map.$$getInstance().getCenter();
          let centerlng = mapcenter.lng;
          let centerlat = mapcenter.lat;
          that.$store.commit(
            "changefromlocation",
            [mapcenter.lng, mapcenter.lat].join(",")
          );
          let lnglat = new AMap.LngLat(mapcenter.lng, mapcenter.lat);
          that.startMarker.setPosition(lnglat);
          that.startMarker.show();
        },
        click: e => {
          //  self.show=false;
        }
      }
    };
  },
  props: ["showCover", "resetHeight", "lineStatus"],
  computed: {
    markers() {
      return this.$store.state.aois || [];
    },
    fromLocation() {
      let that = this;
      if (that.$store.state.fromLocation) {
        return that.$store.state.fromLocation;
      }
    },
    toLocation() {
      let that = this;
      if (that.$store.state.toLocation) {
        return that.$store.state.toLocation;
      }
    },
    baseLocation() {
      let that = this;
      if (that.$store.state.baseLocation) {
        return that.$store.state.baseLocation || "";
      }
    }
  },
  watch: {
    fromLocation(newValue, oldValue) {
      let that = this;
      if (newValue != "") {
        that.mapobj.setCenter(that.$store.state.fromLocation.split(","));
      }
    }
  },
  mounted: function() {
    let that = this;
  },
  methods: {
    resetLocation() {
      let that = this;
      if (that.baseLocation != "") {
        that.mapobj.setCenter(that.baseLocation.split(","));
      }
    },
    //清空marker
    hideline() {
      let that = this;
      //  that.mapobj.clearMap();
      that.mapobj.remove(that.beginMarker);
      that.mapobj.remove(that.finishMarker);
      that.mapobj.remove(that.startMarker);
      that.beginMarker = "";
      that.finishMarker = "";
      that.startMarker = "";
      this.$store.commit("changetolocation", "");
      this.$store.commit("changeto", "您要去哪儿");
      this.$store.commit("changecode", "");
      this.$store.commit("changeecity", "");
      that.mapstate = true;
      if (that.baseLocation && that.baseLocation != "") {
        that.mapobj.setZoomAndCenter(18, that.baseLocation.split(","));
      }

      let startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(24, 35),
        // 图标的取图地址
        image: require("../../assets/map-point.png"),
        // 图标所用图片大小
        imageSize: new AMap.Size(24, 35),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      let startMarker = new AMap.Marker({
        position: that.mapobj.getCenter(),
        icon: startIcon,
        offset: new AMap.Pixel(-11, -33),
        // 设置是否可拖拽
        draggable: false,
        cursor: "move"
      });
      that.startMarker = startMarker;
      that.startMarker.setMap(that.mapobj);
      that.startMarker.setAnimation("AMAP_ANIMATION_DROP");
    },
    showline() {
      let that = this;
      that.mapstate = false;
      //设置起终点marker
      that.beginMarker = new AMap.Marker({
        position: that.$store.state.fromLocation.split(","),
        title: that.$store.state.startPlace,
        icon: new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(24, 39),
          // 图标的取图地址
          image: require("../../assets/frommap.png"),
          // 图标所用图片大小
          imageSize: new AMap.Size(24, 39),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
      });
      that.finishMarker = new AMap.Marker({
        position: that.$store.state.toLocation.split(","),
        title: that.$store.state.endPlace,
        icon: new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(24, 39),
          // 图标的取图地址
          image: require("../../assets/tomap.png"),
          // 图标所用图片大小
          imageSize: new AMap.Size(24, 39),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
      });

      that.beginMarker.setLabel({
        offset: new AMap.Pixel(20, 20), //设置文本标注偏移量
        content: "<p class='cma fb'>" + that.$store.state.startPlace + "</div>", //设置文本标注内容
        direction: "right" //设置文本标注方位
      });
      that.finishMarker.setLabel({
        offset: new AMap.Pixel(20, 20), //设置文本标注偏移量
        content: "<p class='cff fb'>" + that.$store.state.endPlace + "</div>", //设置文本标注内容
        direction: "right" //设置文本标注方位
      });
      that.mapobj.add(that.beginMarker);
      that.mapobj.add(that.finishMarker);
      that.mapobj.remove(that.startMarker);
      that.mapobj.setFitView(null, true, [40, 290, 0, 0], 10);
    }
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
.amap-page-container {
  flex: 1;
  width: 100%;
  flex-direction: column;
  display: flex;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  .rest-handle {
    width: 35px;
    height: 35px;
    display: block;
    position: absolute;
    left: 10px;
    img {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  #amapDemo {
    width: 100%;
    position: relative;
    .startmarker,
    .endmarker {
      width: 24px;
    }
  }
  .center-dot {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: block;
    width: 24px;
    height: 24px;
    img {
      width: 100%;
      margin-top: -24px;
    }
    .map_state_icon {
      position: absolute;
      text-align: center;
      line-height: 2.5em;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      margin-top: -4.5em;
      width: 6em;
      height: 2.5em;
      background: #7e7e7e;
      border-radius: 15px;
      color: #fff;
      font-size: 1.3em;
    }
  }
  .cover_map {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.27);
    z-index: 1;
    overflow: hidden;
    transition: all 2s linear;
  }
}
</style>

<template>
  <div class="navigation-top">
    <div class="back_btn" @click="backRoute">
      <img src="@/assets/left.svg" class="backBtn" alt="" />
    </div>
    <h5>{{ title }}</h5>
    <div class="el-down"></div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {};
  },
  props: {
    title: {
      type: String,
      default: ""
    }
  },
  name: "Navigation",
  computed: {
    ...mapGetters(["config"])
  },
  mounted: function() {},
  methods: {
    backRoute() {
      this.$router.back(-1);
    }
  }
};
</script>
<style lang="less" scoped>
.navigation-top {
  background: #fff;
  padding: 15px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.12);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  h5 {
    font-size: 16px;
  }
  .back_btn {
    width: 20px;
    height: 20px;
    display: block;
    img {
      display: block;
      width: 100%;
      object-fit: contain;
    }
  }
}
</style>

<template>
  <!-- <div> -->
  <footer>
    <div class="nav-bar">
      <cube-tab-bar v-model="selected" :data="labels" @change="changeHandler">
        <cube-tab
          v-for="(item, index) in labels"
          :label="item.label"
          :key="index"
          :value="item.path"
        >
          <!-- name为icon的插槽 -->
          <svg-icon
            slot="icon"
            class-name="foot-class"
            :icon-class="item.icon"
          />
          <!-- 默认插槽 -->
          {{ item.label }}
        </cube-tab>
      </cube-tab-bar>
    </div>
  </footer>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      centerDialogVisible: false
    };
  },
  //   watch: {
  //     $route: {
  //       handler: function(route) {
  //         console.log(route);
  //       },
  //       immediate: true,
  //       deep: true
  //     }
  //   },
  methods: {
    //切换路由
    changeHandler(value) {
      this.$router.push(value);
      // if you clicked different tab, this methods can be emitted
    },
    toMIne() {
      this.$router.push("/mine");
    }
  },
  computed: {
    ...mapGetters(["asyncroutes"]),
    labels() {
      let labels = [];
      this.asyncroutes.map(item => {
        if (!item.hidden) {
          item.children.map(itemroute => {
            labels.push({
              label: itemroute.meta.title,
              icon: itemroute.meta.icon,
              path: itemroute.path,
              value: itemroute.path
            });
          });
        }
      });
      return labels;
    },
    selected: {
      get: function() {
        return this.$route.path;
      },
      set: function(newValue) {
        //  this.$route.path = newValue
      }
    }
  }
};
</script>
<style scoped lang="less">
footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: #fff;
  box-shadow: 0 0 3px rgba(155, 155, 155, 0.9);
  z-index: 11;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  font-size: 13px;
  .foot-class {
    width: 1.5em;
    height: 1.5em;
    display: block;
    margin: 0 auto;
    margin-bottom: 0.3em;
  }
}
//iphoneX、iphoneXs

@media only screen and (device-width: 375px) and (height: 724px) and (-webkit-device-pixel-ratio: 3) {
  footer {
    padding-bottom: 20px;
  }
}

//设备屏幕可见宽度为375px， 可见高度为812px及设备像素比为3

//iphone Xs Max
@media only screen and (device-width: 414px) and (device-height: 808px) and (-webkit-device-pixel-ratio: 3) {
  footer {
    padding-bottom: 20px;
  }
}

//iphone XR
@media only screen and (device-width: 414px) and (device-height: 808px) and (-webkit-device-pixel-ratio: 2) {
  footer {
    padding-bottom: 20px;
  }
}
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active 在低于 2.1.8 版本中 */ {
  transform: translateX(10px);
  opacity: 0;
}
</style>

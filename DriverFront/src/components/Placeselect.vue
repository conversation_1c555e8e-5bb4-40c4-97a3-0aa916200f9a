<template>
  <div class="origin_select">
    <!--顶部-->
    <div class="suggest-header">
      <div class="city-wrapper" @click="showCity = true">
        <span class="text">{{ selectCity ? selectCity : "请选择" }}</span>
        <i class="pulldown"></i>
      </div>
      <div class="address-wrapper">
        <span class="cutline"></span>
        <cube-input
          :type="type"
          v-model="value"
          clearable
          :placeholder="placeword"
          @input="handleSearch"
          @blur="onBlur"
          @focus="showCity = false"
          class="address-input"
        ></cube-input>
        <span class="cutline"></span>

        <!-- <cube-input class="weui_input address-input" :placeholder="placeholder" ref="searchText" id="searchText" @focus="showCity=false" @keyup="handleSearch" />   -->
      </div>
      <div class="cancel-wrapper" @click="cancle">取消</div>
    </div>
    <div class="split"></div>
    <!--已开通城市-->
    <div class="list-wrapper" v-if="showCity">
      <div @click="sureCity(currentCity)" v-if="currentCity">
        <h1 class="list-title">
          当前城市：<i class="fz15">{{ currentCity }}</i>
        </h1>
      </div>
      <div v-else>
        <h1 class="list-title">当前城市：<i>无法定位,请选择</i></h1>
      </div>
      <div>
        <h2 class="list-title_fn">已开通城市</h2>
      </div>
      <div class="list_city_wrapper" v-if="cityData.length > 0">
        <cube-scroll ref="scroll" :data="cityData">
          <div
            class="city_item"
            v-for="(item, index) in cityData"
            :key="index"
            @click="sureCity(item.name)"
          >
            <div class="fz15">{{ item.name }}</div>
          </div>
        </cube-scroll>
      </div>
      <div v-else class="no_data c74">暂无已开通城市</div>
      <cube-loading v-if="showLoading"></cube-loading>
    </div>
    <!--搜索提示显示-->
    <div class="list-wrapper-write" v-if="!showCity">
      <div class="address_items" v-if="searchData.length > 0">
        <cube-scroll ref="scroll" :data="searchData">
          <div
            class="address_item"
            v-for="(item, index) in searchData"
            :key="index"
            @click="sureAddress(item)"
          >
            <i class="address-dot"></i>
            <div class="title">{{ item.name }}</div>
            <div class="description">
              {{ item.pname }} {{ item.cityname }} {{ item.address }}
            </div>
          </div>
        </cube-scroll>
      </div>
    </div>
    <div id="temp" style="display:none"></div>
  </div>
</template>
<script>
import { getsAllCity } from "../api/api";
export default {
  data() {
    return {
      value: "",
      type: "text",
      searchData: "",
      showCity: true,
      cityData: "",
      showLoading: false,
      selectCity: "成都市",
      currentCity: undefined
    };
  },
  props: ["placeType", "placeword"], //from:拼包车起点，to:拼包车终点，begin:带货起点，finish:带货终点
  methods: {
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    },
    sureCity(name) {
      this.keyUpSearch(name);
      this.selectCity = name;
      this.showCity = false;
    },
    handleSearch(e) {
      this.keyUpSearch(e);
    },
    keyUpSearch(txt) {
      let that = this;
      AMap.service(["AMap.PlaceSearch"], function() {
        var placeSearch = new AMap.PlaceSearch({
          //构造地点查询类
          pageSize: 20,
          pageIndex: 1,
          city: that.selectCity || "",
          //城市
          cityLimit: "true",
          panel: "temp"
        });
        //关键字查询
        placeSearch.search(txt, function(status, result) {
          if (status == "complete" && result.info == "OK") {
            that.searchData = result.poiList.pois;
            if (txt == "") {
              that.searchData = "";
            }
          }
        });
      });
    },
    getcitys() {
      let that = this;
      that.showLoading = true;
      getsAllCity().then(res => {
        that.showLoading = false;
        if (res.ret) {
          that.cityData = res.data;
        }
      });
    },
    cancle() {
      this.$emit("cancel");
    },
    sureAddress(info) {
      if (this.placeType == "start") {
        let Info = {
          start_address_remark: info.name,
          start_longitude: info.location.lng,
          start_latitude: info.location.lat,
          start_address_code: info.adcode
        };
        this.$emit("setFormInfo", Info);
      } else {
        let Info = {
          end_address_remark: info.name,
          end_longitude: info.location.lng,
          end_latitude: info.location.lat,
          end_address_code: info.adcode
        };
        this.$emit("setFormInfo", Info);
      }
      this.cancle();
    }
  },
  mounted: function() {
    this.getcitys();
    this.keyUpSearch(this.selectCity);
  }
};
</script>
<style lang="less" scoped>
.address-dot {
  display: block;
  width: 2em;
  height: 1.5em;
  background: url("../assets/addot.svg") no-repeat;
  background-size: cover;
  position: absolute;
  top: 1.6em;
  left: 1em;
}
.address_item {
  position: relative;
  padding: 0.7em 1em 0.5em 4em;
  border-bottom: 1px solid #efefef;
  line-height: 20px;
}
.title {
  font-size: 15px;
  color: #333;
}
.description {
  margin-top: 8px;
  font-size: 13px;
  color: #888;
}
.origin_select {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  background: #f7f7f7;
}
.suggest-header {
  position: relative;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 50px;
  white-space: nowrap;
  background: #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.12);
}
.city-wrapper {
  padding: 5px 15px 5px 0;
  position: relative;
}
.city-wrapper .text {
  line-height: 40px;
  padding-left: 15px;
  font-size: 15px;
  color: #333;
  display: inline-block;
}
.pulldown {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-image: url("../assets/down.svg");
  background-size: cover;
  position: absolute;
  top: 18px;
  right: -4px;
  transition: all 0.3s linear;
}
.address-wrapper {
  padding: 5px 0 5px 10px;
  flex: 1;
  display: flex;
  align-items: center;
  .cutline {
    width: 1px;
    height: 18px;
    background: #efefef;
    display: block;
    min-width: 1px;
  }
  .address-input {
    flex: 1;
  }
  .address-input:after {
    display: none !important;
  }
}
.cancel-wrapper {
  margin: 0;
  border: 0;
  font-weight: 400;
  line-height: 50px;
  padding: 0 15px;
  color: #666;
}
.cancel-wrapper span {
  line-height: 18px;
  padding: 0 16px;
  font-size: 15px;
  color: #666;
  display: inline-block;
}
.split {
  position: relative;
  width: 100%;
  height: 10px;
  background: #f3f4f5;
}
.list-wrapper {
  width: 95%;
  height: 100%;
  margin: 0 auto;
  overflow: auto;
  border-radius: 2px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.list-title {
  height: 50px;
  line-height: 50px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  font-weight: 400;
  vertical-align: baseline;
}
.list-title_fn {
  padding: 16px 16px 10px;
  line-height: 1;
  font-size: 14px;
  color: #999;
  background: #f7f7f7;
  font-weight: 400;
}
.list_city_wrapper {
  display: block;
  width: 95%;
  margin: auto;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 160px;
  padding-bottom: 20px;
  .city_item {
    padding: 14px 30px;
    border-bottom: 1px solid rgba(229, 229, 229, 0.2);
  }
}
.list-wrapper-write {
  width: 95%;
  margin: auto;
  position: absolute;
  top: 60px;
  bottom: 10px;
  left: 0;
  right: 0;
  border-radius: 2px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: scroll;
}
.address_items {
  position: absolute;
  top: 0;
  width: 95%;
  margin: auto;
  bottom: 20px;
  left: 0;
  padding: 10px 0;
}
</style>

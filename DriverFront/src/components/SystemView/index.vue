<template>
  <div class="version-info">
    <div class="pancel" v-if="config && config.tech_support == 1">
      <p>由<a href="https://www.cczhaoche.com">CC招车系统</a> 提供技术支持</p>
    </div>
    <div class="pancel" v-else-if="config && config.tech_support == 3">
      <p>{{ config.tech_text }}</p>
    </div>
    <p>{{ config && config.version_info }}</p>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {};
  },
  name: "SystemView",
  computed: {
    ...mapGetters(["config"])
  },
  mounted: function() {},
  methods: {}
};
</script>
<style lang="less" scoped></style>

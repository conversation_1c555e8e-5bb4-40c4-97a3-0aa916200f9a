<template>
  <div
    class="drag_verify"
    :style="dragVerifyStyle"
    @mousemove="dragMoving"
    @mouseup="dragFinish"
    @touchmove="dragMoving"
    @touchend="dragFinish"
  >
    <div
      class="dv_progress_bar"
      ref="progressBar"
      :style="progressBarStyle"
    ></div>
    <div class="success_text" :style="textStyle" ref="message" v-if="isPassing">
      {{ message }}
    </div>
    <div class="dv_text" :style="textStyle" ref="message" v-else>
      {{ message }}
    </div>
    <div
      class="dv_handler dv_handler_bg"
      @mousedown="dragStart"
      ref="handler"
      :style="handlerStyle"
      @touchstart="dragStart"
    >
      <i :class="handlerIcon"></i>
    </div>
  </div>
</template>
<script>
import "font-awesome/css/font-awesome.min.css";
import { mapGetters } from "vuex";
import { doUpdateOrder } from "@/api/api";
export default {
  data() {
    return {
      isRequest: false,
      isMoving: false,
      x: 0,
      isPassing: false,
      handlerIcon: "fa fa-angle-double-right",
      successIcon: "fa fa-check",
      background: "linear-gradient(-90deg,#1C73E2,#8fb5e6)",
      progressBarBg: "#FF8033",
      completedBg: "#FF8033",
      handlerBg: "#fff",
      successText: "操作成功",
      circle: false,
      width: 320,
      height: 50,
      textSize: "16px"
    };
  },
  name: "dragVerify",
  props: {
    type: {
      type: String
    },
    state: {
      type: String
    },
    order_id: {
      type: String
    }
  },
  computed: {
    ...mapGetters(["driverInfo"]),
    text() {
      if (this.state == 2) {
        if (this.type == 3) {
          return "已取货";
        } else if (this.type == 4) {
          return "开始处理";
        } else {
          return "乘客已上车";
        }
      }
      if (this.state == 3 || this.state == 4) {
        if (this.type == 3) {
          return "已交货";
        } else if (this.type == 4) {
          return "处理完成";
        } else {
          return "送达";
        }
      }
      if (this.state == 5 && this.type != 6) {
        return "已线下现金付款";
      }
    },
    controlStatus() {
      if (this.state == 2) {
        return 3;
      }
      if (this.state == 3 || this.state == 4) {
        return 5;
      }
      if (this.state == 5) {
        return 6;
      }
    },
    handlerStyle() {
      return {
        left: "0px",
        width: this.height + "px",
        height: this.height + "px",
        borderRadius: this.circle ? "50%" : 0,
        background: this.handlerBg
      };
    },
    message() {
      return this.isPassing && this.isRequest ? this.successText : this.text;
    },
    dragVerifyStyle() {
      return {
        width: this.width + "px",
        height: this.height + "px",
        lineHeight: this.height + "px",
        background: this.background,
        borderRadius: this.circle ? this.height / 2 + "px" : 0
      };
    },
    progressBarStyle() {
      return {
        background: this.progressBarBg,
        height: this.height + "px",
        borderRadius: this.circle
          ? this.height / 2 + "px 0 0 " + this.height / 2 + "px"
          : 0
      };
    },
    textStyle() {
      return {
        height: this.height + "px",
        width: this.width + "px",
        fontSize: this.textSize
      };
    },
    handlerIconClass() {
      return this.isPassing ? this.handlerIcon : this.successIcon;
    }
  },
  mounted: function () {
    this.init();
  },
  watch: {
    // isPassing(newValue, oldValue) {
    //   if (newValue) {
    //     this.doUpdateStatus();
    //   }
    // },
  },
  methods: {
    init() { },
    //操作订单
    doUpdateStatus() {
      let spa = {
        driver_id: this.driverInfo.driver_id,
        status: this.controlStatus,
        order_id: this.order_id
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdateOrder(spa).then(response => {
        toast.hide();
        if (response.ret) {
          this.passVerify();

          setTimeout(() => {
            this.$emit("refrshList");
          }, 500);
        } else {
          this.$createToast({
            txt: response.data,
            type: "warn",
            time: 1000
          }).show();
          setTimeout(() => {
            this.$emit("setStatus");
          }, 500);
        }
      });
    },
    dragStart(e) {
      if (!this.isPassing) {
        this.isMoving = true;
        var handler = this.$refs.handler;
        this.x =
          (e.pageX || e.touches[0].pageX) -
          parseInt(handler.style.left.replace("px", ""), 10);
      }
    },
    dragMoving(e) {
      if (this.isMoving && !this.isPassing) {
        var _x = (e.pageX || e.touches[0].pageX) - this.x;
        var handler = this.$refs.handler;
        if (_x > 0 && _x <= this.width - this.height) {
          handler.style.left = _x + "px";
          this.$refs.progressBar.style.width = _x + this.height / 2 + "px";
        } else if (_x > this.width - this.height) {
          handler.style.left = this.width - this.height + "px";
          this.$refs.progressBar.style.width =
            this.width - this.height / 2 + "px";
        }
      }
    },
    dragFinish(e) {
      if (this.isMoving && !this.isPassing) {
        var _x = (e.pageX || e.changedTouches[0].pageX) - this.x;
        if (_x < this.width - this.height) {
          this.$refs.handler.style.left = "0";
          this.$refs.progressBar.style.width = "0";
          return;
        }
        this.isMoving = false;
        this.doUpdateStatus();
      }
    },
    passVerify() {
      this.isRequest = true;
      this.isPassing = true;
      this.isMoving = false;
      var handler = this.$refs.handler;
      handler.className += " dv_handler_ok_bg";
      handler.children[0].className = this.successIcon;
      this.$refs.progressBar.style.background = this.completedBg;
      this.$refs.message.style.color = "#fff";
      this.$emit("passcallback");
    }
  }
};
</script>
<style lang="less" scoped>
.drag_verify {
  position: relative;
  background-color: #e8e8e8;
  text-align: center;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
    0 1px 4px rgba(0, 0, 0, 0.117647);
  .dv_handler {
    position: absolute;
    top: 0px;
    left: 0px;
    /* border: 1px solid #ccc; */
    cursor: move;
    i {
      color: #666;
      font-size: 1.5em;
    }
  }
  .dv_progress_bar {
    position: absolute;
    height: 34px;
    width: 0px;
    transition: background 2s ease-in;
  }
  .dv_text {
    position: absolute;
    top: 0px;
    color: #fff;
    text-align: right;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    right: 20px;
  }
  .success_text {
    position: absolute;
    top: 0px;
    color: #fff;
    text-align: center;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
  }
}
</style>

// import router from "./router";
// import store from "./store";
// import commonRoutes from "@/utils/defaultroutes";
// //引入路由对应组件
// const asyncMap = {
//   receipt: () => import("@/views/nav/receipt/index"),
//   studentcustomized: () => import("@/views/nav/group/index"),
//   freeride: () => import("@/views/nav/freeride/index"),
//   help: () => import("@/views/nav/help/index"),
//   order: () => import("@/views/nav/order/index"),
//   mine: () => import("@/views/nav/mine/index"),
//   notice_assistant: () => import("@/views/nav/notice_assistant/index")
// };
// //404页面重定向
// const erroRoute = {
//   path: "*",
//   redirect: "/404",
//   hidden: true,
//   meta: {
//     index: 0
//   }
// };
// const GenerateRoutes = [...commonRoutes];
// //加载进度条用于app端
// // import NProgress from 'nprogress' // progress bar
// // import 'nprogress/nprogress.css' // progress bar style
// import getPageTitle from "@/utils/get-page-title";
// // NProgress.configure({ showSpinner: true }) // NProgress Configuration
// const whiteList = ["/login", "/register", "/reset", "/404"]; // no redirect whitelist
// //使用async await
// router.beforeEach(async (to, from, next) => {
//   // start progress bar
//   // NProgress.start()
//   // set page title
//   // determine whether the user has logged in

//   const hasCallback = UrlCallback();
//   if (hasCallback) {
//     //地址栏有callback,更新本地缓存，重定向到地址栏不带callback
//     localStorage.setItem("callback", hasCallback);
//     const redctPath =
//       location.origin + location.pathname.split("/callback/")[0] + "/";
//     window.location.href = redctPath;
//     return;
//   }

//   const localCallback = localStorage.getItem("callback");
//   if (localCallback) {
//     await store.dispatch("user/setCallback", localCallback); //注入callback
//     if (store.getters.config) {
//       document.title = getPageTitle(
//         to.meta.title,
//         store.getters.config.mchname
//       );
//     } else {
//       try {
//         await store.dispatch("user/getDriverSideConfig"); //获取司机端配置信息
//         next(to.path);
//       } catch (error) {
//         localStorage.removeItem("callback");
//         next({
//           path: "/404",
//           query: {
//             type: "merchant"
//           }
//         });
//         return;
//       }
//     }
//     // determine if there has toke
//     const hasGetUserInfo = store.getters.driverInfo;
//     if (hasGetUserInfo) {
//       //登录后进入登录页
//       if (to.path === "/login") {
//         next();
//         // NProgress.done() // if current page is dashboard will not trigger afterEach hook, so manually handle it
//       } else {
//         // 当进入非登录页时，需要动态加载路由菜单
//         // 判断当前用户是否已加载动态菜单
//         if (
//           !store.getters.asyncroutes ||
//           store.getters.asyncroutes[3].children.length === 0
//         ) {
//           // 拉取roles
//           const roles = loadRouter(store.getters.config.navigation); //  需要动态为路由加载组件  note: roles must be a array! such as: ['editor','develop']
//           // 根据roles权限生成可访问的路由表
//           GenerateRoutes[3].children = roles;
//           //避免刷新404，404页面需要手动加载
//           GenerateRoutes.push(erroRoute);
//           await store.dispatch("user/setAsyncRoutes", GenerateRoutes);
//           router.addRoutes(GenerateRoutes); // 动态添加可访问路由表 为什么需要全部路由才能使用addRoutes方法?
//           const toRoute = to.path === "/" ? { name: "mine" } : { ...to };
//           next({ ...toRoute, replace: true }); // hack方法 确保addRoutes已完成 ,set the replace:
//         } else {
//           next();
//           await store.dispatch("user/getInfo"); //随时更新司机信息
//         }
//       }
//     } else {
//       // NProgress.done()
//       if (whiteList.indexOf(to.path) !== -1) {
//         // in the free login whitelist, go directly
//         next();
//       } else {
//         try {
//           // get user info
//           await store.dispatch("user/getInfo");
//           next(to.path);
//         } catch (error) {
//           // remove token and go to login page to re-login

//           next(`/login?redirect=${to.path}`);
//           // NProgress.done();
//         }
//       }
//     }
//     // }
//   } else {
//     if (whiteList.indexOf(to.path) !== -1) {
//       // in the free login whitelist, go directly
//       next();
//     } else {
//       next({
//         path: "/404",
//         query: {
//           type: "merchant"
//         }
//       });
//     }
//   }
// });
// router.afterEach(() => {
//   // finish progress bar
//   // NProgress.done()
// });

// function UrlCallback() {
//   var urlKey = location.pathname.match(/callback(\S*)/); //正则验证url中callback
//   if (urlKey) {
//     urlKey = urlKey[1].split("/");
//     return urlKey[1] || undefined;
//   } else {
//     return undefined;
//   }
// }

// function loadRouter(routerlist) {
//   routerlist.forEach(item => {
//     item["component"] = asyncMap[item.name];
//   });
//   return routerlist;
// }

import router from "./router";
import store from "./store";
import commonRoutes from "@/utils/defaultroutes";
//引入路由对应组件
const asyncMap = {
  receipt: () => import("@/views/nav/receipt/index"),
  studentcustomized: () => import("@/views/nav/group/index"),
  freeride: () => import("@/views/nav/freeride/index"),
  help: () => import("@/views/nav/help/index"),
  order: () => import("@/views/nav/order/index"),
  mine: () => import("@/views/nav/mine/index"),
  notice_assistant: () => import("@/views/nav/notice_assistant/index")
};
//404页面重定向
const erroRoute = {
  path: "*",
  redirect: "/404",
  hidden: true,
  meta: {
    index: 0
  }
};
const GenerateRoutes = [...commonRoutes];
//加载进度条用于app端
// import NProgress from 'nprogress' // progress bar
// import 'nprogress/nprogress.css' // progress bar style
import getPageTitle from "@/utils/get-page-title";
// NProgress.configure({ showSpinner: true }) // NProgress Configuration
const whiteList = ["/login", "/register", "/reset", "/404"]; // no redirect whitelist
//使用async await
router.beforeEach(async (to, from, next) => {
  // start progress bar
  // NProgress.start()
  // set page title
  // determine whether the user has logged in
  const hasCallback = UrlCallback();
  // console.log('hasCallback',hasCallback)
  //如果地址栏带有callback,更新cookie
  if (hasCallback) {
    await store.dispatch("user/setCallback", hasCallback); //注入callback
    if (store.getters.config) {
      document.title = getPageTitle(
        to.meta.title,
        store.getters.config.mchname
      );
    } else {
      try {
        await store.dispatch("user/getDriverSideConfig"); //获取司机端配置信息
        next(to.path);
      } catch (error) {
        next("/404");
      }
    }
    // determine if there has toke
    const hasGetUserInfo = store.getters.driverInfo;
    if (hasGetUserInfo) {
      //登录后进入登录页
      if (to.path === "/login") {
        next();
        // NProgress.done() // if current page is dashboard will not trigger afterEach hook, so manually handle it
      } else {
        // 当进入非登录页时，需要动态加载路由菜单
        // 判断当前用户是否已加载动态菜单
        if (
          !store.getters.asyncroutes ||
          store.getters.asyncroutes[3].children.length === 0
        ) {
          // 拉取roles
          const roles = loadRouter(store.getters.config.navigation); //  需要动态为路由加载组件  note: roles must be a array! such as: ['editor','develop']
          // 根据roles权限生成可访问的路由表
          GenerateRoutes[3].children = roles;
          //避免刷新404，404页面需要手动加载
          GenerateRoutes.push(erroRoute);
          await store.dispatch("user/setAsyncRoutes", GenerateRoutes);
          router.addRoutes(GenerateRoutes); // 动态添加可访问路由表 为什么需要全部路由才能使用addRoutes方法?
          const toRoute = to.path === "/" ? { name: "mine" } : { ...to };
          next({ ...toRoute, replace: true }); // hack方法 确保addRoutes已完成 ,set the replace:
        } else {
          next();
          await store.dispatch("user/getInfo"); //随时更新司机信息
        }
      }
    } else {
      // NProgress.done()
      if (whiteList.indexOf(to.path) !== -1) {
        // in the free login whitelist, go directly
        next();
      } else {
        try {
          // get user info
          await store.dispatch("user/getInfo");
          next(to.path);
        } catch (error) {
          // remove token and go to login page to re-login
          next(`/login?redirect=${to.path}`);
          // NProgress.done();
        }
      }
    }
    // }
  } else {
    next(`/404?redirect=${to.path}`);
  }
});
router.afterEach(() => {
  // finish progress bar
  // NProgress.done()
});

function UrlCallback() {
  var urlKey = location.pathname.match(/callback(\S*)/); //正则验证url中callback
  if (urlKey) {
    urlKey = urlKey[1].split("/");
    return urlKey[1] || undefined;
  } else {
    return undefined;
  }
}

function loadRouter(routerlist) {
  routerlist.forEach(item => {
    item["component"] = asyncMap[item.name];
  });
  return routerlist;
}

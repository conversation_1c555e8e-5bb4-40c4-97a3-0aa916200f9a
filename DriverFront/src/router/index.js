import Vue from "vue";
import Router from "vue-router";
import commonRoutes from "@/utils/defaultroutes";
Vue.use(Router);

const createRouter = routes => {
  return new Router({
    scrollBehavior: () => ({ y: 0 }),
    // mode: "history",
    routes: routes,
    linkActiveClass: "bottom-active"
  });
};

const router = createRouter(commonRoutes);
// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter(commonRoutes);
  router.matcher = newRouter.matcher; // reset router
}

export default router;

const commonRoutes = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/user/login/index"),
    hidden: true,
    meta: {
      index: 1
    }
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/views/user/register/index"),
    hidden: true,
    meta: {
      index: 2,
      title: "司机注册"
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/404"),
    hidden: true,
    meta: {
      index: 0
    }
  },
  {
    path: "/",
    redirect: "/mine",
    hidden: false,
    meta: {
      index: 0
    },
    component: () => import("@/views/Home.vue"),
    children: [] //底部菜单需要动态加载
  },
  {
    path: "/amount",
    name: "amount",
    component: () => import("@/views/user/amount/index"),
    hidden: true,
    meta: {
      index: 1
    }
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/views/user/register/index"),
    hidden: true,
    meta: {
      index: 2,
      title: "司机注册"
    }
  },
  {
    path: "/setting",
    name: "setting",
    component: () => import("@/views/user/setting/index"),
    hidden: true,
    meta: {
      index: 1
    }
  },
  {
    path: "/history",
    name: "history",
    component: () => import("@/views/user/orderhistory/index"),
    hidden: true,
    meta: {
      index: 1
    }
  },
  {
    path: "/create",
    name: "create",
    component: () => import("@/views/user/create/index"),
    hidden: true,
    meta: {
      index: 1,
      title: "发布顺风车"
    }
  },
  {
    path: "/createhelp",
    name: "createhelp",
    component: () => import("@/views/user/createhelp/index"),
    hidden: true,
    meta: {
      index: 1,
      title: "发布家长互助"
    }
  },
  {
    path: "/addmessage",
    name: "addmessage",
    component: () => import("@/views/user/addmessage/index"),
    hidden: true,
    meta: {
      index: 2,
      title: "备注信息"
    }
  },
  {
    path: "/withdrawal",
    name: "withdrawal",
    component: () => import("@/views/user/withdrawal/index"),
    hidden: true,
    meta: {
      index: 2
    }
  },
  {
    path: "/withdrawalhistory",
    name: "withdrawalhistory",
    component: () => import("@/views/user/amounthistory/index"),
    hidden: true,
    meta: {
      index: 2
    }
  },
  {
    path: "/reset",
    name: "reset",
    component: () => import("@/views/user/reset/index"),
    hidden: true,
    meta: {
      index: 2,
      title: "重置密码"
    }
  },
  {
    path: "/password",
    name: "password",
    component: () => import("@/views/user/password/index"),
    hidden: true,
    meta: {
      index: 2,
      title: "修改密码"
    }
  }
];

export default commonRoutes;

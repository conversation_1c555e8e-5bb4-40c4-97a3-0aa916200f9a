import axios from "axios";
import { getCallBack } from "@/utils/auth";
import store from "@/store";
// "/api"
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // baseURL: "/api", // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 5000 // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.callback) {
      //   // ['X-Token'] is a custom headers key
      //   // please modify it according to the actual situation
      config.headers["callback"] = getCallBack();
    }
    return config;
  },
  error => {
    // do something with request error
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    // if the custom code is not 20000, it is judged as an error.
    if (
      response.status == 200 ||
      response.status == 201 ||
      response.status == 204
    ) {
      //需要增加接口获取不到cookie的情况判断进入error
      if (typeof response.data === "string") {
        return Promise.reject(new Error("Error"));
      }

      return response.data;
    } else {
      //   Message({
      //     message: res.message || 'Error',
      //     type: 'error',
      //     duration: 5 * 1000
      //   })

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (
        response.status === 50008 ||
        response.status === 50012 ||
        response.status === 50014
      ) {
        // to re-login
      }
      return Promise.reject("Error");
    }
  },
  () => {
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    return Promise.reject(new Error("Error"));
  }
);

export default service;

export function fixedInput(e) {
  setTimeout(() => {
    const scrollHeight =
      document.documentElement.scrollTop || document.body.scrollTop || 0;
    window.scrollTo(0, Math.max(scrollHeight - 1, 0));
  }, 100);
}
//转化时间戳
export function timestampToTime(timestamp) {
  var date = new Date(timestamp * 1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + "-";
  let M =
    (date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1) + "-";
  let D = date.getDate() + " ";
  let h = date.getHours() + ":";
  let m = date.getMinutes() + ":";
  let s = date.getSeconds();
  return Y + M + D + h + m + s;
}

export function findIndex(ary, fn) {
  if (ary.findIndex) {
    return ary.findIndex(fn);
  }
  /* istanbul ignore next */
  let index = -1;
  /* istanbul ignore next */
  ary.some(function(item, i, ary) {
    const ret = fn.call(this, item, i, ary);
    if (ret) {
      index = i;
      return ret;
    }
  });
  /* istanbul ignore next */
  return index;
}
//转化年月日
export function exchange_time(time) {
  //2020-01-03 18:30:00
  return (
    time.slice(5, 7) + "月" + time.slice(8, 10) + "日 " + time.slice(11, 16)
  );
}
//转化日期
export function exchange_date(time) {
  //2020-01-03 18:30:00
  return time.slice(5, 7) + "月" + time.slice(8, 10) + "日 ";
}
export function exchange_year(time) {
  //2020-01-03 18:30:00
  return (
    time.slice(0, 4) +
    "年" +
    time.slice(5, 7) +
    "月" +
    time.slice(8, 10) +
    "日 "
  );
}
//转化时间
export function exchange_min(time) {
  //2020-01-03 18:30:00
  return time.slice(11, 16);
}

export function exchange_group(time) {
  //2020-01-03 18:30:00
  return time.slice(0, 5);
}

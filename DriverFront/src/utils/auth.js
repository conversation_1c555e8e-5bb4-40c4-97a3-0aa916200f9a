import Cookies from "js-cookie";

const CallBackKey = "merchant_callback";

export function getCallBack() {
  return Cookies.get(CallBackKey);
}

export function setCallBack(token) {
  return Cookies.set(CallB<PERSON><PERSON><PERSON>, token);
}

export function removeCallBack() {
  return Cookies.remove(CallBackKey);
}

export function setAsyncRoutes(routes) {
  return Cookies.set("setAsyncRoutes", JSON.stringify(routes));
}

export function getAsyncRoutes() {
  return Cookies.get("setAsyncRoutes");
}

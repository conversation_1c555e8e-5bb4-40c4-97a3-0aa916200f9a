import wx from "weixin-js-sdk";
import { getJsSdkConfig, doUpdateDriverPosition } from "@/api/api";
const getLocation = function() {
  wx.getLocation({
    type: "gcj02", // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
    success: function(res) {
      // 将微信定位拿到的经纬度赋值给高德地图
      // 经度，浮点数，范围为180 ~ -180。
      // 纬度，浮点数，范围为90 ~ -90
      //调用更新位置接口
      let query = {
        longitude: res.longitude,
        latitude: res.latitude,
      };
      doUpdateDriverPosition(query).then((res) => {
        console.log("更新位置成功");
      });
    },
    fail: function(error) {
      console.log(error);
    },
    cancel: function(res) {
      console.log("用户拒绝授权获取地理位置");
    },
  });
};
var heartUpdate = {
  timeout: 10000, //10ms
  timeoutObj: null, //timer
  reset: function() {
    clearTimeout(this.timeoutObj);
    this.start();
  },
  start: function() {
    getLocation(); //立即执行一次更新位置;
    this.timeoutObj = setInterval(function() {
      getLocation();
    }, this.timeout);
  },
};
export const updateLocation = () => {
  const query = {
    _url: window.location.href.split("#")[0],
  };
  getJsSdkConfig(query).then((res) => {
    if (res.ret) {
      // PS: 这里根据你接口的返回值来使用
      wx.config({
        debug: false, // 开启调试模式
        appId: res.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.noncestr, // 必填，生成签名的随机串
        signature: res.data.signature, // 必填，签名，见附录1
        jsApiList: [
          // 必填，需要使用的JS接口列表,写入自己用到的接口名称
          "checkJsApi",
          "openLocation",
          "getLocation",
          "onMenuShareTimeline",
          "onMenuShareAppMessage",
        ],
      });
      wx.ready((res) => {
        //自动执行的
        wx.checkJsApi({
          jsApiList: ["getLocation", "openLocation"],
          success: function(res) {
            if (res.checkResult.getLocation == false) {
              console.log(
                "你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！"
              );
              return;
            }
          },
        });
        heartUpdate.reset(); //重启定时任务
        wx.error(function(res) {
          console.log(res.errMsg);
        });
      });
    }
  });
};

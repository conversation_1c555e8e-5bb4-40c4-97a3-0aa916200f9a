body,
html,
div,
p,
li,
ul,
button,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

i,
b {
  font-style: normal !important;
}

a {
  text-decoration: none;
}

img {
  pointer-events: none;
}

li {
  list-style: none;
}

@media screen and (min-width: 420px) {
  html {
    font-size: 80px !important;
  }
}

html {
  font-size: 62.5%;
}

body,
#app {
  background-color: #fff;
  font-size: 12px !important;
  min-width: 320px;
  /* position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh; */
  padding-bottom: env(safe-area-inset-bottom);
}

.c66 {
  color: #666;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.listloading .cube-loading-spinners {
  margin: 1em auto;
}

.cube-textarea-wrapper::after {
  display: none !important;
}

.fz16 {
  font-size: 16px;
}
.fz18 {
  font-size: 18px;
}
.fz17 {
  font-size: 17px;
}

.fz14 {
  font-size: 14px;
}

.fz15 {
  font-size: 15px;
}

.fz13 {
  font-size: 13px;
}

.fz12 {
  font-size: 12px;
}

.c0c {
  color: #0c0c0c;
}

.c2e {
  color: #2e2e2e;
}

.c4b {
  color: #4b4b4b;
}
.c63 {
  color: #636363;
}
.c74 {
  color: #747474;
}

.c83 {
  color: #838383;
}
.c9f {
  color: #9f9f9f;
}
.cma {
  color: #1c73e2;
}

.cff {
  color: #ff8033;
}

.m0 {
  margin: 0;
}

.fb {
  font-weight: bold;
}

.tc {
  text-align: center;
}

.tl {
  text-align: left;
}

/* .cube-btn.cube-btn_active::after, .cube-btn:active::after{
    display: none !important;
} */
.my-content {
  display: flex;
  padding: 10px 15px;
  justify-content: space-between;
  align-items: center;
}
.my-content .view {
  font-size: 16px;
  color: #ff8033;
}
.cube-scroll-nav-bar-item {
  padding: 0 0.4rem !important;
}

.cube-scroll-nav-bar-item_active span {
  font-weight: 800;
}

.cube-slide-item > a > img {
  width: 100% !important;
}

.cube-btn-primary:active {
  background: #0c6ae2 !important;
}
.cube-scroll-nav-bar_horizontal .cube-scroll-wrapper {
  display: flex;
}
.amap-copyright {
  z-index: 1 !important;
}

.amap-logo {
  z-index: 1 !important;
}

.cube-loading-spinners {
  margin: 20px auto;
}

.amap-marker-label {
  background: none;
  border: none;
  width: 200px;
  white-space: normal;
  display: block;
  text-shadow: 0px 0px 5px #fff;
  font-weight: bold;
}

.marker-view {
  width: 200px;
  display: flex;
  align-items: center;
  color: #1c73e2;
  font-weight: bold;
}

.marker-view .marker-dot {
  width: 5px;
  height: 5px;
  background: #1c73e2;
  border-radius: 50%;
  min-width: 5px;
  margin-right: 5px;
}

.cubeic-round-border:before {
  content: "\E683";
}

.message_write .cube-textarea {
  background-color: #fff !important;
  min-height: 80px;
  font-size: 16px;
}

.cube-textarea-wrapper {
  min-height: 80px;
}

.cube-btn {
  padding: 14px 16px !important;
}

.cube-checkbox-wrap {
  justify-content: center;
}

.item-line {
  display: block;
  padding: 12px 15px !important;
}

.item-line .lineName {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lineInfo p {
  margin-top: 8px;
}
.amap-marker-content {
  z-index: 0 !important;
}
.amap-marker .amap-icon {
  z-index: 99999;
}

.cc-light {
  border: none;
  border-radius: 100%;
  cursor: pointer;
  position: absolute;
  width: 12px;
  height: 12px;
  margin-left: 4px;
  margin-top: 26px;
  background: #1c73e2;
}

.cc-light:after,
.cc-light:before {
  content: ".";
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 100%;
  overflow: hidden;
  display: inline-block;
  width: 10px;
  height: 10px;
  text-indent: 40px;
  margin-top: -5px;
  margin-left: -4px;
  animation-timing-function: ease;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: ease;
  -webkit-animation-iteration-count: infinite;
}
.cc-light:before {
  animation-name: lighting-small;
  animation-duration: 3s;
  -webkit-animation-name: lighting-small;
  -webkit-animation-duration: 3s;
}
.cc-light:after {
  width: 14px;
  height: 14px;
  margin-top: -7px;
  margin-left: -6px;
  animation-name: lighting-big;
  animation-duration: 2s;
  -webkit-animation-name: lighting-big;
  -webkit-animation-duration: 2s;
}
.pancel-content .cube-input::after,
.flexitem .cube-input::after {
  border: none;
}
.flexitem .cube-input {
  flex: 1;
}
.flexitem .cube-input-field {
  padding: 10px 0;
}
.oneline {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@keyframes lighting-small {
  0% {
    width: 10px;
    height: 10px;
    margin-left: -5px;
    margin-top: -5px;
    background-color: rgba(28, 115, 226, 0.5);
  }
  to {
    width: 28px;
    height: 28px;
    margin-left: -14px;
    margin-top: -14px;
    background-color: rgba(28, 115, 226, 0);
  }
}

@keyframes lighting-big {
  0% {
    width: 14px;
    height: 14px;
    margin-left: -7px;
    margin-top: -7px;
    background-color: rgba(28, 115, 226, 0.7);
  }
  to {
    width: 30px;
    height: 30px;
    margin-left: -15px;
    margin-top: -15px;
    background-color: rgba(28, 115, 226, 0);
  }
}

/* 公共样式 */
.cube-carType .cube-slide-group {
  display: flex;
}
.before-trigger,
.cube-pulldown-loaded {
  font-size: 14px;
}
.no_data {
  padding: 15px 0;
  font-size: 12px;
  color: #747474;
  text-align: center;
  display: block;
  margin: 0 auto;
}
.show_no_data {
  padding: 35px 0;
  font-size: 15px;
  color: #747474;
  text-align: center;
  display: block;
  margin: 0 auto;
}
.cube-pullup-wrapper .after-trigger {
  padding: 0 !important;
}
.page-top .cube-tab {
  padding: 12px 0;
  font-size: 16px;
}
.free-content .cube-tab-panels,
.order-content .cube-tab-panels {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.free-content .cube-tab-panels-group,
.order-content .cube-tab-panels-group {
  flex: 1;
}
.el-down {
  width: 20px;
  height: 20px;
  display: block;
}
.withdrawalPage .cube-input::after,
.login-form .cube-input::after,
.price-info .cube-input::after {
  border: none;
}
.money-write input {
  font-size: 24px;
  padding-left: 0;
  color: #2e2e2e;
}
.registerPage input {
  font-size: 17px;
  padding: 10px;
}
.resetPage input {
  font-size: 16px;
}
.login-form input {
  font-size: 18px;
}
.price-info input {
  text-align: right;
}
.version-info {
  display: block;
  text-align: center;
  margin-top: 20px;
  font-size: 12px;
  color: #cccccc;
}
.version-info p {
  line-height: 20px;
}
.settingPage .cube-btn {
  background: #fff;
  color: #1c73e2;
  font-size: 15px;
}
.cube-upload .cube-upload-file-def {
  width: 100%;
  height: 100%;
}
.el-vue-amap-container {
  display: none;
}
#amapDemo {
  display: none;
}

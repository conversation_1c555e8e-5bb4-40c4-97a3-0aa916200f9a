<template>
  <div id="app">
    <transition :name="transitionName">
      <keep-alive>
        <router-view class="app-view" />
      </keep-alive>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { updateLocation } from "@/utils/updateLocation";

export default {
  data() {
    return {
      transitionName: "",
      isWechat: false
    };
  },
  name: "App",
  components: {
    //   'app-foot':Foot
  },
  created() {},
  watch: {
    //使用watch 监听$router的变化
    $route: {
      handler: function(to, from) {
        //如果已登录，更新司机位置
        if (this.driverInfo) {
          updateLocation();
        }
        if (from && to) {
          //如果to索引大于from索引,判断为前进状态,反之则为后退状态
          if (to.meta.index > from.meta.index) {
            //设置动画名称
            this.transitionName = "slide-left";
          } else {
            this.transitionName = "slide-right";
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    ...mapGetters(["driverInfo"]),
    isWxBrowser() {
      //是否微信浏览器
      let ua = navigator.userAgent.toLowerCase();
      return ua.match(/MicroMessenger/i) == "micromessenger";
    }
  }
};
</script>

<style>
.app-view {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.slide-right-enter {
  opacity: 0;
  transform: translate(-100%, 0);
}
.slide-right-leave-active {
  opacity: 0;
  transform: translate(100%, 0);
}
.slide-left-enter {
  opacity: 0;
  transform: translate(100%, 0);
}
.slide-left-leave-active {
  opacity: 0;
  transform: translate(-100%, 0);
}

.slide-left-enter-active {
  animation-name: fold-left-in;
  animation-duration: 0.3s;
}
.slide-left-leave-active {
  animation-name: fold-left-out;
  animation-duration: 0.3s;
}
.slide-right-enter-active {
  animation-name: fold-right-in;
  animation-duration: 0.3s;
}
.slide-right-leave-active {
  animation-name: fold-right-out;
  animation-duration: 0.3s;
}
@keyframes fold-left-in {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fold-left-out {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  100% {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes fold-right-in {
  0% {
    width: 100%;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    width: 100%;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fold-right-out {
  0% {
    width: 100%;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  100% {
    width: 100%;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
body {
  margin: 0px;
  padding: 0px;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  width: 100%;
  height: 100%;
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
  transition: all 250ms;
}

#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>

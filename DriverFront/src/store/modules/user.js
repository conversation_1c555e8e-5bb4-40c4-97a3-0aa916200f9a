import { login, getInfo, getSideConfig, logout } from "@/api/user";
import { getCallBack, setCallBack, setAsyncRoutes } from "@/utils/auth";
import { resetRouter } from "@/router";

const state = {
  message: "",
  config: undefined,
  driverInfo: undefined,
  callback: getCallBack(),
  asyncroutes: undefined
};

const mutations = {
  //商户号
  SET_CALLBACK: (state, callback) => {
    state.callback = callback;
  },
  //配置信息
  SET_CONFIG: (state, config) => {
    state.config = config;
  },
  //留言
  SET_MESSAGE: (state, message) => {
    state.message = message;
  },
  //司机信息
  SET_INFO: (state, driverInfo) => {
    state.driverInfo = driverInfo;
  },
  SET_ROUTES: (stae, routes) => {
    state.asyncroutes = routes;
  }
};

const actions = {
  //注入callback
  setCallback({ commit }, callback) {
    return new Promise(resolve => {
      commit("SET_CALLBACK", callback);
      setCallBack(callback);
      resolve();
    });
  },
  setAsyncRoutes({ commit }, routes) {
    return new Promise(resolve => {
      commit("SET_ROUTES", routes);
      setAsyncRoutes(routes);
      resolve();
    });
  },
  // user login
  login({ commit }, userInfo) {
    const { cellphone, password } = userInfo;
    return new Promise((resolve, reject) => {
      login({
        cellphone: cellphone.trim(),
        password: password
      })
        .then(response => {
          if (response.ret) {
            const { data } = response;
          }

          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo()
        .then(response => {
          const { data } = response;
          if (data) {
            commit("SET_INFO", data);
            resolve(data);
          }
        })
        .catch(error => {
          console.log(6666666666);
          reject(error);
        });
    });
  },
  getDriverSideConfig({ commit, state }) {
    return new Promise((resolve, reject) => {
      getSideConfig()
        .then(response => {
          const { data } = response;
          commit("SET_CONFIG", data);
          resolve(data);
        })
        .catch(error => {
          reject("error");
        });
    });
  },
  // user logout
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      logout()
        .then(response => {
          if (response.ret) {
            commit("SET_INFO", undefined);
            resetRouter();
            resolve();
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      resolve();
    });
  },
  changeMessage({ commit }, message) {
    return new Promise(resolve => {
      commit("SET_MESSAGE", message);
      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

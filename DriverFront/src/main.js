import "amfe-flexible";
import App from "./App";
import Vue from "vue";
import router from "./router";
import store from "./store";
import Vuex from "vuex";
import AMap from "vue-amap";
import FastClick from "fastclick";
import "./style/base.css";
import "@/icons";
import "@/permission"; // permission control
import Empty from "@/components/Empty.vue";
import SystemView from "@/components/SystemView";
import Navigation from "@/components/Navigation";

Vue.component("Empty", Empty);
Vue.component("SystemView", SystemView);
Vue.component("Navigation", Navigation);
Vue.use(AMap);
Vue.use(Vuex);
import {
  /* eslint-disable no-unused-vars */
  Style,
  Button,
  ScrollNavBar,
  Checkbox,
  Input,
  Textarea,
  Select,
  Loading,
  Tip,
  Toast,
  Picker,
  TimePicker,
  DatePicker,
  Dialog,
  ActionSheet,
  Scroll,
  Slide,
  IndexList,
  Swipe,
  Popup,
  TabBar,
  Upload,
  TabPanels
} from "cube-ui";

import "amfe-flexible";

Vue.use(TabBar);
Vue.use(Upload);
Vue.use(Checkbox);
Vue.use(Style);
Vue.use(Button);
Vue.use(ScrollNavBar);
Vue.use(Input);
Vue.use(Textarea);
Vue.use(Select);
Vue.use(Loading);
Vue.use(Tip);
Vue.use(Toast);
Vue.use(Picker);
Vue.use(TimePicker);
Vue.use(Dialog);
Vue.use(ActionSheet);
Vue.use(Scroll);
Vue.use(Slide);
Vue.use(IndexList);
Vue.use(Swipe);
Vue.use(DatePicker);
Vue.use(Popup);
Vue.use(TabPanels);
FastClick.attach(document.body);
FastClick.prototype.focus = ele => {
  "use strict";
  ele.focus();
}; //修改focus()方法
AMap.initAMapApiLoader({
  key: "ae07d204562de06e95f7ed805770b18f",
  plugin: [
    "AMap.Autocomplete",
    "AMap.PlaceSearch",
    "AMap.Geolocation",
    "AMap.Scale",
    "AMap.OverView",
    "AMap.Driving",
    "AMap.ToolBar",
    "AMap.MapType",
    "AMap.PolyEditor",
    "AMap.CircleEditor",
    "Geocoder",
    "AMap.PositionPicker"
  ],
  // 默认高德 sdk 版本为 1.4.4
  v: "1.4.9"
});
// 全局指令
// const windowHeight = window.innerHeight

Vue.directive("fixedInput", function (el, binding) {
  el.addEventListener("blur", function () {
    setTimeout(() => {
      const scrollHeight =
        document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    }, 100);
  });
});
Vue.prototype.fixedInput = function (e) {
  setTimeout(() => {
    const scrollHeight =
      document.documentElement.scrollTop || document.body.scrollTop || 0;
    window.scrollTo(0, Math.max(scrollHeight - 1, 0));
  }, 100);
};

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");

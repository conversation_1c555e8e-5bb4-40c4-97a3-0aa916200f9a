import request from "@/utils/request";

//获取广告
export const getAds = params => {
  return request({
    url: "/Passenger/Ad/getAds",
    method: "get",
    params
  });
};
//获取微信jssdk授权
export const getJsSdkConfig = data => {
  return request({
    url: "/Passenger/Weixin/getJsSdkConfig",
    method: "post",
    data
  });
};

export const getUsePlatformInfo = data => {
  return request({
    url: "/Home/Common/getUsePlatformInfo",
    method: "post",
    data
  });
};


//获取已开通城市
export const getsAllCity = params => {
  return request({
    url: "/Passenger/AddressCode/getAddressCodes",
    method: "get",
    params
  });
};

//获取验证码
export const doGetCode = data => {
  return request({
    url: "/Passenger/Account/doMobileSendCellphoneValidateCode",
    method: "post",
    data
  });
};

//提现申请
export const doWithdrawal = data => {
  return request({
    url: "/Driver/Account/doMobileWithDrawDespoist",
    method: "post",
    data
  });
};

//提现记录
export const WithdrawalHistory = params => {
  return request({
    url: "/Driver/Account/doGetDriverWithDrawDespoists",
    method: "get",
    params
  });
};

//获取接单列表
export const doGetReceiptList = params => {
  return request({
    url: "/Driver/Order/doGetAppointOrderList",
    method: "get",
    params
  });
};
//接单、拒绝订单
export const doUpdateOrder = data => {
  return request({
    url: "/Driver/Order/doUpdateOrder",
    method: "post",
    data
  });
};
//学生号-组团包车（接单、拒单）
export const doUpdateStudentCustomizedLine = data => {
  return request({
    url: "/Driver/YyLine/doUpdateStudentCustomizedLine",
    method: "post",
    data
  });
};

//获取进行中订单
export const doGetOrderList = params => {
  return request({
    url: "/Driver/Order/doGetOrderOnList",
    method: "get",
    params
  });
};
//获取预约订单
export const doGetNextOrderList = params => {
  return request({
    url: "/Driver/Order/doGetOrderWaitList",
    method: "get",
    params
  });
};

//历史订单
export const doGetHistoryList = params => {
  return request({
    url: "/Driver/Order/doGetOrderOverList",
    method: "get",
    params
  });
};
//获取乘客发布顺风车
export const doGetFreeRideOrderOnList = params => {
  return request({
    url: "/Driver/Order/doGetFreeRideOrderOnList",
    method: "get",
    params
  });
};
//接受乘客顺风车订单
export const doUpdatePassengerReleaseFreeRideOrder = data => {
  return request({
    url: "/Driver/Order/doUpdatePassengerReleaseFreeRideOrder",
    method: "post",
    data
  });
};

//司机已发布顺风车
export const getOrderFreeRideLineList = params => {
  return request({
    url: "/Driver/Line/getOrderFreeRideLineList",
    method: "get",
    params
  });
};

//司机发布顺风车
export const doReleaseFreeRideLine = data => {
  return request({
    url: "/Driver/Line/doReleaseFreeRideLine",
    method: "post",
    data
  });
};

//司机取消顺风车行程
export const doDelFreeRideLine = data => {
  return request({
    url: "/Driver/Line/doDelFreeRideLine",
    method: "post",
    data
  });
};

//司机注册
export const doRegisterThree = data => {
  return request({
    url: "/Driver/Account/doRegisterThree",
    method: "post",
    data
  });
};

//司机重新提交
export const doSubmitDriverDataThree = data => {
  return request({
    url: "/Driver/Account/doSubmitDriverDataThree",
    method: "post",
    data
  });
};

//忘记密码
export const doForgetPassword = data => {
  return request({
    url: "/Driver/Account/doForgetPassword",
    method: "post",
    data
  });
};

//修改密码
export const doUpdatePassword = data => {
  return request({
    url: "/Driver/Account/doUpdatePassword",
    method: "post",
    data
  });
};

//获取司机已发布的家长互助线路
export const getLineParentHelpList = params => {
  return request({
    url: "/Driver/YyLine/getLineParentHelpList",
    method: "get",
    params
  });
};

//发布家长互助线路
export const doReleaseParentHelpLine = data => {
  return request({
    url: "/Driver/YyLine/doReleaseParentHelpLine",
    method: "post",
    data
  });
};

//司机删除已发布的家长互助线路
export const doDelParentHelpLine = data => {
  return request({
    url: "/Driver/YyLine/doDelParentHelpLine",
    method: "post",
    data
  });
};

//获取家长互助发布服务
export const getDriverParentHelpInfo = params => {
  return request({
    url: "/Driver/YyAccount/getDriverParentHelpInfo",
    method: "get",
    params
  });
};

//获取家长互助信息发布服务费
export const getParentHelpPrice = params => {
  return request({
    url: "/Driver/YyParentHelpPrice/getParentHelpPrice",
    method: "get",
    params
  });
};

//购买发布家长互助服务
export const doOrderParentHelpPrice = data => {
  return request({
    url: "/Driver/YyOrder/doOrderParentHelpPrice",
    method: "post",
    data
  });
};

//
export const doWxOrderParentHelpPricePay = data => {
  return request({
    url: "/Driver/YyPay/doWxOrderParentHelpPricePay",
    method: "post",
    data
  });
};

//定制包车-获取指派给我的线路
export const getStudentCustomizedLines = params => {
  return request({
    url: "/Driver/YyLine/getStudentCustomizedLines",
    method: "get",
    params
  });
};

//上传司机位置
export const doUpdateDriverPosition = data => {
  return request({
    url: "/Driver/Account/doUpdateDriverPosition",
    method: "post",
    data
  });
};

//司机更新附加费用
export const doUpdateExtraPrice = data => {
  return request({
    url: "/Driver/Order/doUpdateExtraPrice",
    method: "post",
    data
  });
};

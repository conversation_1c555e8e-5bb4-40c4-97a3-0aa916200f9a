import request from "@/utils/request";

export function login(data) {
  return request({
    url: "Driver/Account/doYyWebLogin",
    method: "post",
    data
  });
}
export function getInfo() {
  return request({
    url: "/Driver/Account/getDriverInfo",
    method: "get"
  });
}
export function getSideConfig() {
  return request({
    url: "/Driver/Account/getDriverSideConfig",
    method: "get"
  });
}

// export function updateUser(data) {
//   return request({
//     url: '/api/v1/user',
//     method: 'post',
//     data

//   })
// }

export function logout() {
  return request({
    url: "/Driver/Account/doWebLogout",
    method: "post"
  });
}

<template>
  <div class="freePage">
    <div class="page-top">
      <cube-tab-bar
        v-model="selectedLabel"
        showSlider
        :use-transition="true"
        ref="tabNav"
        :data="tabLabels"
      >
      </cube-tab-bar>
    </div>
    <div class="free-content">
      <cube-tab-panels v-model="selectedLabel">
        <cube-tab-panel style="position:relative" :value="0" :label="'人找车'">
          <free-list type="order" ref="listView"></free-list>
        </cube-tab-panel>
        <cube-tab-panel
          style="position:relative"
          :value="1"
          :label="'我发的车找人'"
        >
          <free-list type="mine" ref="listView"></free-list>
        </cube-tab-panel>
      </cube-tab-panels>
    </div>
    <div class="refresh_btn" @click="handleCreate">
      <svg-icon slot="icon" class-name="fresh-dot" icon-class="add" />
    </div>
  </div>
</template>

<script>
import Flist from "./components/freeList";

export default {
  data() {
    return {
      selectedLabel: 0,
      tabLabels: [
        {
          label: "人找车",
          value: 0,
          type: "order"
        },
        {
          label: "我发的车找人",
          value: 1,
          type: "mine"
        }
      ]
    };
  },
  methods: {
    handleCreate() {
      this.$router.push({
        path: "/create"
      });
    }
  },
  watch: {
    selectedLabel() {
      this.$refs.listView.onPullingDown();
    }
  },
  components: {
    "free-list": Flist
  },
  mounted: function() {}
};
</script>
<style lang="less" scoped>
.freePage {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
  width: 100vw;
  .page-top {
    z-index: 1;
    position: absolute;
    width: 100vw;
    top: 0;
    left: 0;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
      0 1px 4px rgba(0, 0, 0, 0.117647);
  }
  .refresh_btn {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 80px;
    right: 20px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.217647);
    z-index: 1;
    .fresh-dot {
      width: 30px;
      height: 30px;
      display: block;
    }
  }
  .free-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
  }
}
</style>

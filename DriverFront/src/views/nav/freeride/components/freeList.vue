<template>
  <div
    class="scroll-view-content"
    :style="{
      bottom: fullHeight == 808 || fullHeight == 724 ? '70px' : '50px'
    }"
  >
    <cube-scroll
      ref="scroll"
      :options="options"
      @pulling-down="onPullingDown"
      @pulling-up="onPullingUp"
      v-if="listdata.length > 0 || owndata.length > 0"
    >
      <div class="order-content">
        <div
          class="item-order"
          v-for="(item, index) in type === 'order' ? listdata : owndata"
          :key="index"
        >
          <div class="order-title">
            <div class="type-info">
              <p class="fz16 c0c fb">
                {{ item.start_name }}
                <svg-icon
                  slot="icon"
                  class-name="toright-dot"
                  icon-class="toright"
                />
                {{ item.end_name }}
              </p>
              <div class="free_driver_dot" v-if="type === 'order'">乘客</div>
              <div class="free_driver_dot" v-else>我</div>
              <div style="font-size: 12px;color: #999">
                {{ item.away_time }}
              </div>
            </div>
            <div class="order-price">
              <svg-icon slot="icon" class-name="price-dot" icon-class="price" />
              <p class="fz18 cff fb" v-if="type === 'order'">
                {{ item.price }}
                <span class="fz12 c83">元</span>
              </p>
              <p class="fz18 cff fb" v-else>
                {{ item.price }}
                <span class="fz12 c83">元/人</span>
              </p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz16 c2e">{{ show_time(item.start_time) }}</p>
            </div>
            <div class="order-pancel">
              <div class="s-dot"></div>
              <p class="fz16 c2e">{{ item.start_address_remark }}</p>
            </div>
            <div class="order-pancel">
              <div class="e-dot"></div>
              <p class="fz16 c2e">{{ item.end_address_remark }}</p>
            </div>
            <div class="order-pancel" v-if="type === 'order'">
              <svg-icon
                slot="icon"
                class-name="person-dot"
                icon-class="person"
              />
              <p class="fz16 c2e">{{ item.book_seating }} 人</p>
            </div>
            <div class="order-pancel" v-else>
              <svg-icon slot="icon" class-name="seat-dot" icon-class="seat" />
              <p class="fz16 c2e">剩余 {{ item.residual_seating }} 座</p>
            </div>
          </div>
          <div class="control-order">
            <!-- <cube-button :light="true"   :inline="true" style="flex:1" @click="detailOrder">详情</cube-button> -->
            <cube-button
              :primary="true"
              :inline="true"
              style="flex:2"
              @click="receiptOrder(item)"
              v-if="type === 'order'"
              >接单</cube-button
            >
            <cube-button
              :inline="true"
              style="flex:2"
              @click="cancelOrder(item)"
              v-else
              >取消</cube-button
            >
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty
      textword="暂无顺风车订单"
      v-if="type == 'order' && listtotal == 0"
    ></Empty>
    <Empty
      textword="还没有发布顺风车订单"
      v-if="type == 'own' && owntotal == 0"
    ></Empty>
  </div>
</template>
<script>
import {
  doGetFreeRideOrderOnList,
  getOrderFreeRideLineList,
  doUpdatePassengerReleaseFreeRideOrder,
  doDelFreeRideLine
} from "@/api/api";
import { exchange_time } from "@/utils/utils";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      value: false,
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      listdata: [], //  乘客发布
      owndata: [], // 司机发布
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      queryNextValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      listtotal: 0,
      owntotal: 0,
      fullHeight: document.body.offsetHeight,
      timer: undefined
    };
  },
  props: {
    type: {
      type: String
    }
  },
  computed: {
    ...mapGetters(["driverInfo"])
  },
  activated() {
    this.queryValue = {
      page: 1, // 开始页数
      size: 20 // 一次显示多少条
    };
    if (this.type == "order") {
      this.getNowList("down");
    } else {
      this.getNextList("down");
    }
    this.timer = setInterval(() => {
      if (this.type == "order") {
        this.getNowList("down");
      } else {
        this.getNextList("down");
      }
    }, 5000);
  },
  deactivated() {
    this.timer = clearInterval(this.timer);
  },
  methods: {
    show_time(time) {
      return exchange_time(time);
    },
    //接单
    receiptOrder(e) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-alert",
        title: "接受顺风车订单",
        content: "接单后不可取消订单",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          let obj = {
            status: 2,
            order_id: e.order_id
          };
          this.doUpdateStatus(obj);
        }
      }).show();
    },
    //接受顺风车订单
    doUpdateStatus(obj) {
      let spa = {
        driver_id: this.driverInfo.driver_id,
        ...obj
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdatePassengerReleaseFreeRideOrder(obj).then(response => {
        toast.hide();
        if (response.ret) {
          this.$createDialog({
            type: "alert",
            icon: "cubeic-alert",
            showClose: false,
            title: "接单成功",
            content: "可在“订单”中查看",
            confirmBtn: {
              text: "我知道了",
              active: true
            }
          }).show();
          setTimeout(() => {
            this.onPullingDown();
          }, 500);
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    },
    //取消顺风车
    cancelOrder(item) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-alert",
        title: "取消顺风车行程",
        content: "取消后乘客无法加入该行程",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "暂不",
          active: false
        },
        onConfirm: () => {
          console.log(item);
          let obj = {
            line_id: item.id
          };
          const toast = this.$createToast({
            txt: "操作中",
            time: 0
          });
          toast.show();
          doDelFreeRideLine(obj).then(response => {
            toast.hide();
            if (response.ret) {
              this.$createToast({
                txt: response.data,
                type: "Correct",
                time: 1000
              }).show();
              setTimeout(() => {
                this.onPullingDown();
              }, 500);
            } else {
              this.$createToast({
                txt: response.data,
                type: "error",
                time: 1000
              }).show();
            }
          });
        }
      }).show();
    },
    //乘客发布的订单
    getNowList(type) {
      let that = this;
      // this.toast.show();
      doGetFreeRideOrderOnList(that.queryValue).then(response => {
        // that.toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.listdata = that.listdata.concat(response.data);
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
            }
          } else {
            that.listdata = response.data;
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.listtotal = response.count;
        } else {
          if (type == "down") {
            this.listdata = [];
            this.listtotal = 0;
          }
          if (this.$refs.scroll && this.queryValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryValue.page--;
          }
        }
      });
    },
    //司机发布
    getNextList(type) {
      let that = this;
      // this.toast.show();
      getOrderFreeRideLineList(that.queryNextValue).then(response => {
        // that.toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.owndata = that.owndata.concat(response.data);
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
            }
          } else {
            that.owndata = response.data;
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.owntotal = response.count;
        } else {
          if (this.$refs.scroll && this.queryNextValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryNextValue.page--;
          }
        }
      });
    },
    //下拉刷新
    onPullingDown() {
      if (this.type === "order") {
        this.queryValue = {
          page: 1, // 开始页数
          size: 20 // 一次显示多少条
        };
        if (this.$refs.scroll) {
          this.$refs.scroll.refresh();
        }

        this.getNowList("down");
      } else {
        this.queryNextValue = {
          page: 1, // 开始页数
          size: 20 // 一次显示多少条
        };
        if (this.$refs.scroll) {
          this.$refs.scroll.refresh();
        }
        this.getNextList("down");
      }
    },
    //加载更多
    onPullingUp() {
      if (this.type === "order") {
        this.queryValue.page++;
        this.getNowList("up");
      } else {
        this.queryNextValue.page++;
        this.getNextList("up");
      }
    }
  }
};
</script>
<style scoped lang="less">
.scroll-view-content {
  display: block;
  position: absolute;
  top: 45px;
  left: 0;
  width: 100%;
  .order-content {
    background-color: #fff;
    padding: 0 10px;
    .item-order {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
        0 1px 4px rgba(0, 0, 0, 0.117647);
      display: block;
      padding: 0 10px 10px 10px;
      border-radius: 5px;
      margin: 15px auto;
      .order-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        .type-info {
          display: flex;
          align-items: center;
          .free_driver_dot {
            background-color: #1c73e2;
            font-size: 10px;
            color: #fff;
            border-radius: 4px;
            padding: 4px 5px;
            margin: 0 8px;
          }
        }
        .order-price {
          display: flex;
          align-items: center;
          .price-dot {
            margin-right: 5px;
          }
        }
      }
      .order-middle {
        padding: 10px 0;
        .order-pancel {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-bottom: 5px;
          .s-dot,
          .e-dot {
            width: 6px;
            height: 6px;
            min-width: 6px;
            border-radius: 50%;
            background: #1c73e2;
            margin-right: 16px;
            margin-left: 1px;
          }
          .e-dot {
            background: #fc9153;
          }
          p {
            line-height: 20px;
          }
          .time-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
          }
          .person-dot,
          .seat-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
            color: #999;
          }
        }
      }
      .control-order {
        display: flex;
        justify-content: space-between;
        align-items: center;
        button {
          margin: 0 5px;
        }
      }
    }
  }
}
</style>

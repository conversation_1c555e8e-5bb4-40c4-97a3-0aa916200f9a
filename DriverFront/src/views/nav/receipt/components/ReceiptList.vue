<template>
  <div class="scroll-view-content" :style="{
      bottom: fullHeight == 808 || fullHeight == 724 ? '70px' : '50px'
    }">
    <cube-scroll v-if="listdata.length > 0" ref="scroll" :data="listdata" :options="options" @pulling-down="refreshlist" @pulling-up="onPullingUp">
      <div class="order-content">
        <div class="item-order" v-for="(item, index) in listdata" :key="index">
          <div class="order-title">
            <div class="type-info">
              <p class="order-type" v-if="item.type == 1">拼车</p>
              <p class="order-type" v-else-if="item.type == 2">包车</p>
              <p class="order-type" v-else-if="item.type == 3">带货</p>
              <p class="order-type" v-else-if="item.type == 4">代办</p>
              <p class="order-type" v-else-if="item.type == 5">定制客运</p>
              <p class="order-type" v-else-if="item.type == 6">顺风车</p>
              <p class="order-type" v-else-if="item.type == 7">快车</p>
              <p class="order-type" v-else-if="item.type == 11">出租车</p>
              <p class="order-type" v-else>普通</p>
              <p class="paytype" v-if="item.is_pre_pay == 1">预付</p>
              <p class="paytype">{{ item.order_no }}</p>
              <span class="real-time-order-tag" v-if="item.type == 11||item.type == 7">实时</span>
            </div>
            <div class="order-price" v-if="item.type != 10&&item.type != 7&&item.type != 11">
              <svg-icon slot="icon" class-name="price-dot" icon-class="price" />
              <p class="fz18 cff fb">
                {{ item.price }}
                <span class="fz12 c83">元</span>
              </p>
            </div>
          </div>
          <div class="order-middle">
            <div class="new-order-warp" v-if="item.type == 7||item.type == 11">
              <div class="item-view-cell">
                <div class="label">价格</div>
                <div class="val-cell">
                  <b>{{ item.price }}</b>
                  <span>元</span>
                </div>
              </div>
              <div class="item-view-cell">
                <div class="label">距你</div>
                <div class="val-cell">
                  <b>{{item.fromDis}}</b>
                  <span>公里</span>
                </div>
              </div>
              <div class="item-view-cell sed">
                <div class="label">全程</div>
                <div class="val-cell">
                  <b>{{item.distance}}</b>
                  <span>公里</span>
                </div>
              </div>
            </div>
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz16 c2e">{{ show_time(item.start_time) }}</p>
            </div>
            <div class="line-content" v-if="item.type != 4">
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="user-dot" icon-class="user" />
                <p class="fz16 c2e">{{ item.book_seating }} 人</p>
              </div>
              <div class="order-pancel">
                <div class="s-dot"></div>
                <p class="fz16 c2e">{{ item.start_address_remark }}</p>
              </div>
              <div class="order-pancel">
                <div class="e-dot"></div>
                <p class="fz16 c2e">{{ item.end_address_remark }}</p>
              </div>
            </div>
            <div class="things-content" v-else>
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="type-dot" icon-class="things" />
                <p class="fz16 c2e">{{ item.name }}</p>
              </div>
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="type-dot" icon-class="about" />
                <p class="fz17 c2e">{{ item.summary }}</p>
              </div>
            </div>
            <div class="things-content" v-if="item.type == 3">
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="type-dot" icon-class="weight" />
                <p class="fz15 c2e">{{ item.weight }} kg</p>
              </div>
            </div>
          </div>
          <div class="control-order">
            <cube-button :light="true" :inline="true" style="flex:1" @click="refuseOrder(item)">拒绝</cube-button>
            <cube-button :primary="true" :inline="true" style="flex:2" @click="receiptOrder(item)">接单</cube-button>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="暂无订单" v-if="total == 0"></Empty>
  </div>
</template>
<script>
import { doGetReceiptList, doUpdateOrder } from "@/api/api";
import { exchange_time } from "@/utils/utils";
import { mapGetters } from "vuex";
export default {
  data () {
    return {
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      fullHeight: document.body.offsetHeight,
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      total: 0,
      listdata: [], // 数据存放数组,
      timer: undefined
    };
  },
  computed: {
    ...mapGetters(["driverInfo"])
  },
  activated () {
    this.queryValue = {
      page: 1, // 开始页数
      size: 20 // 一次显示多少条
    };
    this.getList("down", true);
    this.timer = setInterval(() => {
      this.getList("down");
    }, 15000);
  },
  deactivated () {
    this.timer = clearInterval(this.timer);
  },
  methods: {
    calcDistance (slng, slat, elng, elat) {
      if (!!!this.driving) {
        this.driving = new AMap.Driving({
          // 驾车路线规划策略，AMap.DrivingPolicy.LEAST_TIME是最快捷模式
          policy: AMap.DrivingPolicy.LEAST_TIME
        })

      }
      return new Promise((resolve, reject) => {
        this.driving.search(new AMap.LngLat(slng, slat),
          new AMap.LngLat(elng, elat), function (status, result) {

            console.log('result', result)
            const { routes = [] } = result
            if (!routes || routes.length == 0) {
              resolve('-')

              return
            }
            let distance = routes[0].distance
            let distanceView = (Math.round(distance / 100) / 10).toFixed(1)
            console.log(distanceView)
            resolve(distanceView)
          });



      })

    },


    show_time (time) {
      return exchange_time(time);
    },
    getList (type, isLoading = false) {
      let that = this;
      const toast = this.$createToast({
        txt: "努力加载中..",
        time: 0
      });
      if (isLoading) {
        toast.show();
      }
      doGetReceiptList(that.queryValue).then(response => {
        if (isLoading) {
          toast.hide();
        }
        if (response.ret) {
          let data_ = response.data
          data_.map(async (item_) => {
            if (['7', '11'].includes(item_.type)) {
              const distance = await this.calcDistance(item_.start_longitude, item_.start_latitude, item_.end_longitude, item_.end_latitude)
              this.$set(item_, 'distance', distance)
              let fromDis = '-'
              if (item_.longitude) {
                fromDis = await this.calcDistance(item_.longitude, item_.latitude, item_.start_longitude, item_.start_latitude)
              }
              this.$set(item_, 'fromDis', fromDis)
            }
            return item_
          })



          if (type == "up") {
            that.listdata = [...that.listdata, ...data_];
          } else {
            that.listdata = [...data_];
            if (this.$refs.scroll) {
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.total = response.count;
        } else {
          if (type == "down") {
            this.listdata = [];
            this.total = 0;
          }
          if (this.$refs.scroll && this.queryValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryValue.page--;
          }
        }
      });
    },
    refuseOrder (e) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-warn",
        title: "确认拒绝该订单？",
        content: "拒绝后将不会再次收到该订单",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          let obj = {
            status: 1,
            order_id: e.order_id
          };
          this.doUpdateStatus(obj);
        }
      }).show();
    },
    //接单
    receiptOrder (e) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-alert",
        title: "确认接单？",
        content: "接单后不可取消订单",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          let obj = {
            status: 2,
            order_id: e.order_id
          };
          this.doUpdateStatus(obj);
        }
      }).show();
    },
    //下拉刷新
    refreshlist () {
      this.queryValue = {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      };
      if (this.$refs.scroll) {
        this.$refs.scroll.refresh();
      }
      this.getList("down");
    },
    //加载更多
    onPullingUp () {
      this.queryValue.page++;
      this.getList("up");
    },
    //接单，拒绝订单
    doUpdateStatus (obj) {
      let spa = {
        driver_id: this.driverInfo.driver_id,
        ...obj
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdateOrder(obj).then(response => {
        toast.hide();
        if (response.ret) {
          this.$createToast({
            txt: response.data,
            type: "Correct",
            time: 1000
          }).show();
          setTimeout(() => {
            this.refreshlist();
          }, 500);
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
  .real-time-order-tag {
    height: 20px;
    padding: 0 5px;
    line-height: 20px;
    font-size: 12px;
    color: #909399;
    border: 1px solid #e9e9eb;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
    background-color: #f4f4f5;
    // position: absolute;
    // left: 0;
    // top: 0;
    // bottom: 0;
    display: inline-block;
    margin-left: 5px;
    // margin: auto;
  }
  .new-order-warp {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 15px 0;
    .item-view-cell {
      flex: 0 0 35%;
      display: flex;
      flex-direction: column;
      .label {
        color: #939496;
        font-size: 13px;
      }
      .val-cell {
        display: flex;
        align-items: flex-end;
        line-height: 1;
        margin-top: 10px;
        b {
          font-size: 22px;
          font-weight: 700;
          color: #333;
          line-height: 16px;
          margin-right: 3px;
        }
        span {
          font-size: 12px;
          color: #333;
        }
      }
    }
    .sed {
      flex: 0 0 30%;
    }
  }
  .scroll-view-content {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    .order-content {
      background-color: #fff;
      padding: 0 10px;
      .item-order {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
          0 1px 4px rgba(0, 0, 0, 0.117647);
        display: block;
        padding: 0 10px 10px 10px;
        border-radius: 5px;
        margin: 15px auto;
        .order-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
          .type-info {
            display: flex;
            align-items: center;
            .order-type {
              font-size: 15px;
              font-weight: bold;
              color: #474747;
              margin-right: 10px;
            }
            .paytype {
              padding: 3px 5px;
              font-size: 13px;
              border: 1px solid #eee;
            }
          }
          .order-price {
            display: flex;
            align-items: center;
            .price-dot {
              margin-right: 5px;
            }
          }
        }
        .order-middle {
          padding: 10px 0;
          .order-pancel {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-bottom: 5px;
            .s-dot,
            .e-dot {
              width: 6px;
              height: 6px;
              min-width: 6px;
              border-radius: 50%;
              background: #1c73e2;
              margin-right: 16px;
              margin-left: 1px;
            }
            .e-dot {
              background: #fc9153;
            }
            p {
              line-height: 22px;
            }
            .time-dot {
              width: 8px;
              height: 8px;
              margin-right: 15px;
            }
            .type-dot {
              width: 10px;
              height: 10px;
              margin-right: 13px;
            }
            .user-dot {
              width: 10px;
              height: 10px;
              margin-right: 13px;
            }
          }
        }
        .control-order {
          display: flex;
          justify-content: space-between;
          align-items: center;
          button {
            margin: 0 5px;
          }
        }
      }
    }
  }
</style>

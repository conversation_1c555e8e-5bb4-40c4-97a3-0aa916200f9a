<template>
  <div class="main-container">
    <div class="top-logout">
      <div class="out-handle" @click="handleLoginOut()">
        <img src="@/assets/exit.svg" alt="" />
      </div>
    </div>
    <div class="center-view">
      <img src="@/assets/info.png" alt="" />
      <p>消息助手</p>
    </div>
    <cube-button
      :primary="true"
      style="margin-bottom:20px ;"
      @click="handleOfficial()"
      >去公众号接单</cube-button
    >
  </div>
</template>
<script>
export default {
  data() {
    return {};
  },
  methods: {
    handleOfficial() {
      window.cc_js.handleOfficial();
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      await this.$store.dispatch("user/setAsyncRoutes", undefined);
      this.$router.push({ path: "/login" });
      let ua = navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        //ios的ua中无miniProgram，但都有MicroMessenger（表示是微信浏览器）
      } else {
        //其他浏览器
        var u = navigator.userAgent;
        var isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
        if (isAndroid) {
          //安卓
          window.cc_js.logout();
        }
      }
    },
    handleLoginOut() {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-warn",
        content: "你确定要退出登录？",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          this.logout();
        }
      }).show();
    }
  }
};
</script>
<style lang="less" scoped>
.top-logout {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding: 20px 0;
  .out-handle {
    width: 18px;
    height: 18px;
    display: block;
  }
  img {
    width: 18px;
    height: 18px;
    display: block;
  }
}
.main-container {
  padding: 0 20px;
  display: flex;
  height: 100vh;
  background: #f6f7f8;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .center-view {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 100px;
      height: 100px;
      object-fit: contain;
    }
    p {
      font-size: 16px;
      margin-top: 10px;
    }
  }
}
</style>

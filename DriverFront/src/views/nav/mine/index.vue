<template>
  <div class="minePage">
    <div class="mine-top">
      <img src="@/assets/login-bg.png" alt="" />
      <div class="top-content">
        <div class="view-logo" v-if="driverInfo">
          <img src="@/assets/ava.png" alt="" />
          <div class="tologin">
            <div class="user-name">
              <p>{{ driverInfo.name }}</p>
              <div class="config-status" v-if="driverInfo.status == 0">
                <img src="@/assets/renzhen.png" alt="" />
                <p style="color:#FF8A00;">已认证</p>
              </div>
            </div>
          </div>
          <div class="view-logo-label">
            <div>
              <p>月流水</p>
              <p class="dot" v-if="driverInfo.total_month_flows != ''">
                <span>￥</span>{{ driverInfo.total_month_flows }}
              </p>
              <p v-else class="dot"><span>￥</span>0.0</p>
            </div>
            <div>
              <p>月订单数</p>
              <p class="dot" v-if="driverInfo.total_month_orders != 0">
                {{ driverInfo.total_month_orders }}
              </p>
              <p v-else class="dot">0</p>
            </div>
            <div>
              <p>信用分</p>
              <p class="dot" v-if="driverInfo.credit_score != ''">
                {{ driverInfo.credit_score }}
              </p>
              <p v-else class="dot">0</p>
            </div>
          </div>
        </div>
        <div class="view-logo" v-else @click="tologin">
          <img src="@/assets/ava.png" alt="" />
          <p class="tologin">登录/注册</p>
        </div>
      </div>
    </div>
    <div class="mine-pancel-content">
      <div class="item-pancel money-item" @click="toRouter('/amount')">
        <div class="left-money">
          <div class="label">
            <img src="@/assets/money.png" alt="" />
            账户余额(元)
          </div>
          <h5 v-if="driverInfo.balance == ''">
            <span class="spanft">￥</span>0.0
          </h5>
          <h5 v-else><span class="spanft">￥</span>{{ driverInfo.balance }}</h5>
        </div>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </div>
      <div class="item-pancel" @click="toRouter('/history')">
        <div class="label">
          <img src="@/assets/order.png" alt="" />
          历史订单
        </div>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </div>
      <div class="item-pancel">
        <div class="label">
          <img src="@/assets/branch.png" alt="" />
          所属分台
        </div>
        <p class="fz15 c9f">{{ config.branch_name }}</p>
      </div>
      <a class="item-pancel" :href="'tel:' + config.mchtel">
        <div class="label">
          <img src="@/assets/service.png" alt="" />
          总台服务
        </div>
        <p class="fz15 c9f">{{ config.mchtel }}</p>
      </a>
      <div class="item-pancel" v-if="driverInfo" @click="toRouter('/setting')">
        <div class="label">
          <img src="@/assets/setting.png" alt="" />
          设置
        </div>
        <div
          class="remind-dot"
          v-if="driverInfo.status == 2 || driverInfo.status == 3"
        ></div>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </div>
    </div>
    <div class="remind-word" v-if="driverInfo">
      <p>1. 余额：实时交易计算，乘客支付成功后自动转入余额中。</p>
      <p>
        2.
        提现：司机需通过司机微信端/线下发起提现申请即可，预计1-5个工作日到账。
      </p>
      <p>
        3.
        服务条款：登录使用本系统，代表您已全面了解该系统，并严格遵守该系统规则。
        <a
          v-if="driverInfo.mchname != ''"
          :href="
            'https://www.cczhaoche.com/agreement.html?' +
              encodeURI('copyright=' + driverInfo.mchname)
          "
          >《服务条款》</a
        >
        <a v-else href="https://www.cczhaoche.com/agreement.html">《服务条款》</a>
      </p>
    </div>
    <div class="word-content" v-else>
      <div class="word-title">
        <img src="@/assets/line.png" alt="" />
        <h5>我是司机，想加盟接单?请联系开速账号</h5>
        <img src="@/assets/line.png" alt="" />
      </div>
      <div class="word-view">
        <div class="item-word">
          <img src="@/assets/true.png" alt="" />
          <p>
            仅业务联盟，
            <span>您的乘客不共享！</span>(永远仅自己可见)
          </p>
        </div>
        <div class="item-word">
          <img src="@/assets/true.png" alt="" />
          <p><span>年费或按单分成，</span>合作模式任你挑</p>
        </div>
        <div class="item-word">
          <img src="@/assets/true.png" alt="" />
          <p><span>随时随地</span>接派单，提现更方便</p>
        </div>
        <div class="item-word">
          <img src="@/assets/true.png" alt="" />
          <p>
            随时能退盟，
            <span>不约束！</span>
          </p>
        </div>
        <div class="item-word">
          <img src="@/assets/true.png" alt="" />
          <p><span>数据独立，</span>安全有保障</p>
        </div>
      </div>
    </div>
    <SystemView />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["config", "driverInfo"]),
  },
  components: {},
  mounted() {},
  methods: {
    tologin() {
      this.$router.push({ path: "/login" });
    },
    toRouter(route) {
      if (!this.driverInfo) {
        this.tologin();
      } else {
        this.$router.push({ path: route });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.minePage {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  display: block;
  padding-bottom: 70px;
  .remind-word {
    padding: 30px 15px;
    p {
      font-size: 14px;
      color: #b1b1b1;
      line-height: 23px;
      a {
        color: #1c73e2;
        font-size: 14px;
      }
    }
  }
  .word-content {
    display: block;
    padding: 0 15px;
    .word-title {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30px 0;
      img {
        display: block;
        width: 15px;
        object-fit: contain;
        margin: 0 8px;
      }
      h5 {
        font-size: 15px;
        color: #060606;
        font-weight: bold;
      }
    }
    .word-view {
      background: url("../../../assets/word-bg.png") no-repeat;
      background-size: 100% auto;
      padding: 50px 15px;
      .item-word {
        display: flex;
        padding: 5px 0;
        align-items: center;
        img {
          width: 14px;
          object-fit: contain;
          min-width: 14px;
          margin-right: 15px;
        }
        p {
          font-size: 14px;
          color: #808080;
          display: block;
          line-height: 20px;
          span {
            color: #1c73e2;
          }
        }
      }
    }
  }
  .mine-pancel-content {
    padding: 0 15px;
    display: block;
    .item-pancel {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(228, 227, 227, 1);
      padding: 20px 0;
      position: relative;
      .remind-dot {
        width: 7px;
        height: 7px;
        display: block;
        position: absolute;
        right: 25px;
        top: 10px;
        border-radius: 50%;
        background-color: #ea1924;
      }
      .label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #4a4a4a;
        img {
          width: 17px;
          display: block;
          object-fit: contain;
          margin-right: 15px;
        }
      }
      .right-dot {
        display: block;
      }
    }
    .money-item {
      h5 {
        font-size: 30px;
        color: #000000;
        text-align: left;
        display: block;
        margin-top: 15px;
      }
    }
  }
  .mine-top {
    img {
      width: 100%;
      object-fit: contain;
    }
    .top-content {
      margin: 0 15px;
      height: 140px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 8px 24px 1px rgba(28, 115, 226, 0.16);
      border-radius: 4px;
      margin-top: -80px;
      display: block;
      position: relative;
      z-index: 1;
      .view-logo {
        position: absolute;
        left: 0;
        right: 0;
        top: -40px;
        margin: auto;
        img {
          width: 75px;
          object-fit: contain;
          display: block;
          margin: 0 auto;
        }
        .view-logo-label {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          margin: 24px 0;
          font-size: 15px;
          font-weight: 600;
          color: #6c6868;
          p {
            margin: 0;
            padding: 4px 0;
            text-align: center;
          }
          span {
            font-size: 10px;
          }
        }
        .spanft {
          font-size: 10px;
        }
        .dot {
          color: #e85c0a;
        }
        .tologin {
          text-align: center;
          color: #4a4a4a;
          font-size: 17px;
          margin-top: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          .user-name {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .config-status {
              display: flex;
              align-items: center;
              margin-left: 5px;
              p {
                font-size: 11px;
              }
              img {
                width: 13.5px;
                height: 13.5px;
                display: block;
                margin-right: 3px;
                object-fit: contain;
              }
            }
          }
        }
      }
    }
  }
}
</style>

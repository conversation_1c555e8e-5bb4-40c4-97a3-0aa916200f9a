<template>
  <div class="helpPage">
    <div class="top-view" v-if="!helpInfo">
      <div class="tobuy">
        <p>你还未购买家长互助权益</p>
        <div class="buy-btn" @click="showConfirm">立即购买</div>
      </div>
    </div>
    <div class="hasbuy" v-else>
      <p
        class="tc fz14 cfff"
      >有效期:{{show_time(helpInfo.start_time)}} - {{show_time(helpInfo.end_time)}}</p>
    </div>
    <help-list type="mine" v-if="helpInfo" ref="listView"></help-list>
    <Empty textword="请先购买家长互助权益" v-if=" !helpInfo"></Empty>
    <div class="create_btn" @click="handleCreate">
      <svg-icon slot="icon" class-name="create-dot" icon-class="add"/>
    </div>
  </div>
</template>

<script>
import Hlist from "./components/helpList";
import {
  getDriverParentHelpInfo,
  getParentHelpPrice,
  doOrderParentHelpPrice,
  doWxOrderParentHelpPricePay,
} from "@/api/api";
import { wexinPay } from "@/utils/wxPay";
import { exchange_year } from "@/utils/utils";

export default {
  data() {
    return {
      helpInfo: undefined,
      priceInfo: undefined,
    };
  },
  methods: {
    show_time(time) {
      return exchange_year(time);
    },
    handleCreate() {
      if (!this.helpInfo) {
        this.showConfirm();
        return;
      }
      this.$router.push({
        path: "/createhelp",
      });
    },
    getHelpInfo() {
      getDriverParentHelpInfo().then((res) => {
        if (res.ret) {
          this.helpInfo = res.data;
        }
      });
    },
    getHelpPrice() {
      getParentHelpPrice().then((res) => {
        if (res.ret) {
          this.priceInfo = res.data;
        }
      });
    },
    showConfirm() {
      this.$createDialog(
        {
          type: "confirm",
          icon: "cubeic-alert",
          title: this.priceInfo.name,
          content:
            "购买权益后您才可以发布家长互助行程。" +
            this.priceInfo.name +
            "--" +
            this.priceInfo.price +
            "元",
          confirmBtn: {
            text: "立即购买",
            active: true,
          },
          cancelBtn: {
            text: "暂不",
            active: false,
          },
          onConfirm: () => {
            this.handleOrder();
          },
        },
        (createElement) => {
          return [
            // createElement(
            //   "div",
            //   {
            //     class: {
            //       "my-title": true,
            //     },
            //     slot: "title",
            //   },
            //   [
            //     createElement("div", {
            //       class: {
            //         "my-title-img": true,
            //       },
            //     }),
            //     createElement("p", "附近车少,优选出租车将来接您"),
            //   ]
            // ),
            createElement(
              "div",
              {
                class: {
                  "my-content": true,
                },
                slot: "content",
              },
              [
                createElement(
                  "div",
                  {
                    class: {
                      label: true,
                    },
                  },
                  "￥"
                ),
                createElement(
                  "div",
                  {
                    class: {
                      view: true,
                    },
                  },
                  this.priceInfo.price + "元"
                ),
              ]
            ),
          ];
        }
      ).show();
    },
    handleOrder() {
      const query = {
        parent_help_price_id: this.priceInfo.parent_help_price_id,
      };
      const toast = this.$createToast({
        txt: "努力加载中..",
        time: 0,
      });
      toast.show();
      doOrderParentHelpPrice(query).then((res) => {
        toast.hide();

        if (res.ret) {
          this.payOrder(res.data.price, res.data.order_parent_help_price_no);
        } else {
          const toast = this.$createToast({
            txt: res.data,
            time: 2000,
            type: "warn",
          });
          toast.show();
        }
      });
    },
    //支付
    payOrder(price, order_parent_help_price_no) {
      let that = this;
      const query = {
        order_parent_help_price_no: order_parent_help_price_no,
        amount: price,
      };
      const toast = this.$createToast({
        txt: "加载中..",
        time: 0,
      });
      toast.show();
      doWxOrderParentHelpPricePay(query).then((res) => {
        toast.hide();
        if (res.ret) {
          wexinPay(
            res.data,
            //成功回调函数
            (res) => {
              const toast = this.$createToast({
                txt: "购买成功",
                time: 1500,
                type: "correct",
              });
              toast.show();
              this.getHelpInfo();
            },
            (error) => {
              if (error.errMsg === "chooseWXPay:cancel") {
                const toast = this.$createToast({
                  txt: "取消支付",
                  type: "warn",
                  time: 1500,
                });
                toast.show();
              }
            }
          );
        } else {
          const toast = this.$createToast({
            txt: res.data,
            type: "warn",
            time: 1500,
          });
          toast.show();
        }
      });
    },
  },
  components: {
    "help-list": Hlist,
  },
  activated() {
    this.getHelpInfo();
    this.getHelpPrice();
    if (this.$refs.listView) {
      this.$refs.listView.onPullingDown();
    }
  },
  mounted: function() {},
};
</script>
<style lang="less" scoped>
.helpPage {
  display: block;
  width: 100vw;
  bottom: 100vh;
  .top-view {
    padding: 10px 20px;
    border-bottom: 1px solid #eee;
    .tobuy {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 15px;
      .buy-btn {
        width: 60px;
        text-align: center;
        height: 25px;
        line-height: 25px;
        font-size: 12px;
        border-radius: 4px;
        color: #fff;
        background: #1c73e2;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.217647);
      }
    }
  }
  .hasbuy {
    padding: 10px 20px;
    background-color: #ff8033;
    color: #fff;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.217647);
  }
  .create_btn {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 80px;
    right: 20px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.217647);
    z-index: 1;
    .create-dot {
      width: 30px;
      height: 30px;
      display: block;
    }
  }
}
</style>

<template>
  <div
    class="scroll-view-content"
    :style="{
      bottom: fullHeight == 808 || fullHeight == 724 ? '70px' : '50px'
    }"
  >
    <cube-scroll
      ref="scroll"
      :options="options"
      @pulling-down="onPullingDown"
      @pulling-up="onPullingUp"
      v-if="owndata.length > 0"
    >
      <div class="order-content">
        <div class="item-order" v-for="(item, index) in owndata" :key="index">
          <div class="order-title">
            <div class="type-info">
              <p class="fz16 c0c fb">
                {{ item.start_name }}
                <svg-icon slot="icon" class-name="toright-dot" icon-class="toright"/>
                {{ item.end_name }}
              </p>
              <div class="free_driver_dot">我</div>
              <div style="font-size: 12px;color: #999">{{ item.away_time }}</div>
            </div>
            <div class="order-price">
              <svg-icon slot="icon" class-name="price-dot" icon-class="price"/>
              <p class="fz18 cff fb">
                {{ item.price }}
                <span class="fz12 c83">元/人</span>
              </p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time"/>
              <p class="fz16 c2e">{{ show_time(item.start_time) }}</p>
            </div>
            <div class="order-pancel">
              <div class="s-dot"></div>
              <p class="fz16 c2e">{{ item.start_address_remark }}</p>
            </div>
            <div class="order-pancel">
              <div class="e-dot"></div>
              <p class="fz16 c2e">{{ item.end_address_remark }}</p>
            </div>
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="seat-dot" icon-class="seat"/>
              <p class="fz16 c2e">剩余 {{ item.residual_seating }} 座</p>
            </div>
          </div>
          <div class="control-order">
            <!-- <cube-button :light="true"   :inline="true" style="flex:1" @click="detailOrder">详情</cube-button> -->
            <cube-button :inline="true" :light="true" style="flex:2" @click="cancelOrder(item)">取消</cube-button>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="还没有发布过家长互助" v-if=" owntotal == 0"></Empty>
  </div>
</template>
<script>
import { getLineParentHelpList, doDelParentHelpLine } from "@/api/api";
import { exchange_time } from "@/utils/utils";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      value: false,
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功",
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成",
          },
        },
      },
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0,
      }),
      owndata: [], // 司机发布
      queryNextValue: {
        page: 1, // 开始页数
        size: 20, // 一次显示多少条
      },
      owntotal: 0,
      fullHeight: document.body.offsetHeight,
    };
  },
  props: {
    type: {
      type: String,
    },
  },
  computed: {
    ...mapGetters(["driverInfo"]),
  },
  mounted() {
    this.queryNextValue = {
      page: 1, // 开始页数
      size: 20, // 一次显示多少条
    };
    this.getNextList("down");
  },
  methods: {
    show_time(time) {
      return exchange_time(time);
    },
    //取消顺风车
    cancelOrder(item) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-alert",
        title: "取消家长互助",
        content: "取消后乘客无法加入该行程",
        confirmBtn: {
          text: "确定",
          active: true,
        },
        cancelBtn: {
          text: "暂不",
          active: false,
        },
        onConfirm: () => {
          let obj = {
            line_parent_help_id: item.line_parent_help_id,
          };
          const toast = this.$createToast({
            txt: "操作中",
            time: 0,
          });
          toast.show();
          doDelParentHelpLine(obj).then((response) => {
            toast.hide();
            if (response.ret) {
              this.$createToast({
                txt: response.data,
                type: "Correct",
                time: 1000,
              }).show();
              setTimeout(() => {
                this.onPullingDown();
              }, 500);
            } else {
              this.$createToast({
                txt: response.data,
                type: "error",
                time: 1000,
              }).show();
            }
          });
        },
      }).show();
    },
    //司机发布
    getNextList(type) {
      let that = this;
      // this.toast.show();
      getLineParentHelpList(that.queryNextValue).then((response) => {
        // that.toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.owndata = that.owndata.concat(response.data);
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
            }
          } else {
            that.owndata = response.data;
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.owntotal = response.count;
        } else {
          if (this.$refs.scroll && this.queryNextValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryNextValue.page--;
          }
        }
      });
    },
    //下拉刷新
    onPullingDown() {
      this.queryNextValue = {
        page: 1, // 开始页数
        size: 20, // 一次显示多少条
      };
      if (this.$refs.scroll) {
        this.$refs.scroll.refresh();
      }
      this.getNextList("down");
    },
    //加载更多
    onPullingUp() {
      this.queryNextValue.page++;
      this.getNextList("up");
    },
  },
};
</script>
<style scoped lang="less">
.scroll-view-content {
  display: block;
  position: absolute;
  top: 40px;
  left: 0;
  width: 100%;
  .order-content {
    background-color: #fff;
    padding: 0 10px;
    .item-order {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
        0 1px 4px rgba(0, 0, 0, 0.117647);
      display: block;
      padding: 0 10px 10px 10px;
      border-radius: 5px;
      margin: 15px auto;
      .order-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        .type-info {
          display: flex;
          align-items: center;
          .free_driver_dot {
            background-color: #1c73e2;
            font-size: 10px;
            color: #fff;
            border-radius: 4px;
            padding: 4px 5px;
            margin: 0 8px;
          }
        }
        .order-price {
          display: flex;
          align-items: center;
          .price-dot {
            margin-right: 5px;
          }
        }
      }
      .order-middle {
        padding: 10px 0;
        .order-pancel {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-bottom: 5px;
          .s-dot,
          .e-dot {
            width: 6px;
            height: 6px;
            min-width: 6px;
            border-radius: 50%;
            background: #1c73e2;
            margin-right: 16px;
            margin-left: 1px;
          }
          .e-dot {
            background: #fc9153;
          }
          p {
            line-height: 20px;
          }
          .time-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
          }
          .person-dot,
          .seat-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
            color: #999;
          }
        }
      }
      .control-order {
        display: flex;
        justify-content: space-between;
        align-items: center;
        button {
          margin: 0 5px;
        }
      }
    }
  }
}
</style>

<template>
  <div class="order-box">
    <div class="page-top">
      <cube-tab-bar v-model="selectedLabel" showSlider :use-transition="true" ref="tabNav" :data="tabLabels"></cube-tab-bar>
    </div>
    <div class="order-content">
      <cube-tab-panels v-model="selectedLabel">
        <cube-tab-panel style="position:relative" :value="0" :label="'实时订单'">
          <div class="tag-warp-filter">
            <div class="bussess-list-tag">
              <div class="bussess-scroll">
                <div class="item-tag" @click="checkStatus('')" :class="query.status==''?'active-tag':''">全部</div>
                <div class="item-tag" @click="checkStatus('not_boarding')" :class="query.status=='not_boarding'?'active-tag':''">待上车</div>
                <div class="item-tag" @click="checkStatus('not_delivered')" :class="query.status=='not_delivered'?'active-tag':''">待送达</div>
                <div class="item-tag" @click="checkStatus('not_paid')" :class="query.status=='not_paid'?'active-tag':''">待支付</div>
              </div>
            </div>
          </div>
          <order-list type="now" :query="query"></order-list>
        </cube-tab-panel>
        <cube-tab-panel style="position:relative" :value="1" :label="'预约订单'">
          <!-- <div class="tag-warp-filter">
            <div class="bussess-list-tag">
              <div class="bussess-scroll">
                <div class="item-tag" @click="checkStatus('')" :class="query.status==''?'active-tag':''">全部</div>
                <div class="item-tag" @click="checkStatus('not_boarding')" :class="query.status=='not_boarding'?'active-tag':''">待上车</div>
                <div class="item-tag" @click="checkStatus('not_delivered')" :class="query.status=='not_delivered'?'active-tag':''">待送达</div>
                <div class="item-tag" @click="checkStatus('not_paid')" :class="query.status=='not_paid'?'active-tag':''">待支付</div>
              </div>
            </div>
          </div> -->
          <order-list type="next" :query="query"></order-list>
        </cube-tab-panel>
      </cube-tab-panels>
      <div class="fix-warp-filter">
        <div class="item-tag-fill type-fliter-warp" @click="showTypePop"><span class="type-show-name">{{typename}}</span><i :class="query.type==''?'arrow-down':'check-down'"></i></div>
        <div class="item-tag-fill sort-fliter-warp" @click="showSortPop"><span class="sort-show-name">{{sortname}}</span><i :class="query.sort_by==''?'arrow-down':'check-down'"></i></div>
      </div>
    </div>
  </div>
</template>

<script>
import Olist from "./components/OrderList";
import {
  getUsePlatformInfo
} from "@/api/api";
export default {
  data () {
    return {
      selectedLabel: 0,
      busses: [],
      typename: '全部业态',
      sortname: '默认排序',
      query: {
        type: '',
        status: '',
        sort_by: '',
      },
      active: 0,
      sactive: 0,
      tabLabels: [
        {
          label: "实时订单",
          value: 0,
          type: "now"
        },
        {
          label: "预约订单",
          value: 1,
          type: "next"
        }
      ]
    };
  },
  mounted () {
    this.getBussess()
  },
  methods: {
    checkStatus (status) {
      this.query.status = status
    },
    getBussess () {
      getUsePlatformInfo().then(res => {
        if (res.ret) {
          this.busses = res.data.biz_support.filter((i) => i.type != 21).map((o) => {
            o['content'] = o.name
            return o
          })
        }
      });
    },
    showTypePop () {
      this.$createActionSheet({
        title: '业态筛选',
        active: this.active,
        data: [{ content: '全部业态', type: '' }, ...this.busses],
        onSelect: (item, index) => {
          this.typename = item.content
          this.query.type = item.type
          this.active = index
        },
      }).show()
    },
    showSortPop () {
      this.$createActionSheet({
        title: '排序方式',
        active: this.sactive,
        data: [
          {
            content: '默认排序',
            val: ''
          },
          {
            content: '按出发时间',
            val: 'start_time'
          },
        ],
        onSelect: (item, index) => {
          this.query.sort_by = item.val
          this.sactive = index
          this.sortname = item.content
        },
      }).show()
    }
  },
  components: {
    "order-list": Olist
  },
};
</script>

<style lang="less" scoped>
  .order-box {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100vh;
    width: 100vw;
    .page-top {
      z-index: 1;
      position: absolute;
      width: 100vw;
      top: 0;
      left: 0;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
        0 1px 4px rgba(0, 0, 0, 0.117647);
    }
    .order-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
    }
  }

  .bussess-list-tag {
    margin-top: 50px;
    padding-bottom: 12px;
    flex: 1;
    padding: 6px 0 6px 8px;
    overflow: hidden;

    .bussess-scroll {
      display: flex;
      align-items: center;
      white-space: nowrap;
      width: 100%;
      transition: all linear 0.5s;
      justify-content: space-between;
    }

    .item-tag {
      height: 26px;
      line-height: 26px;
      padding: 0 10px;
      background: #eee;
      color: #333;
      border-radius: 100px;
      display: flex;
      font-size: 13px;
      align-items: center;
      margin-right: 12px;
    }

    .active-tag {
      background: #e7f1ff;
      color: #1c73e2;
    }
  }
  .fix-warp-filter {
    z-index: 2;
    display: flex;
    justify-content: center;
    padding: 4px 0;
    box-sizing: border-box;
    box-shadow: 0px 0px 10px 7px rgba(0, 0, 0, 0.04);
    width: 240px;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 70px;
    margin: auto;
    border-radius: 100px;
    background-color: #fff;

    .item-tag-fill {
      display: flex;
      align-items: center;
      white-space: nowrap;
      justify-content: center;
      width: 50%;
      padding: 8px 5px;
      position: relative;

      &::after {
        display: block;
        content: "";
        width: 1px;
        height: 60%;
        background: #999;
        transform: scaleX(0.5);
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
      }

      span {
        font-size: 14px;
        color: #333;
      }

      i {
        width: 10px;
        height: 10px;
        display: block;
        background: url("../../../assets/down.svg") no-repeat;
        background-size: contain;
        margin-left: 5px;
      }

      .check-down {
        width: 10px;
        height: 10px;
        display: block;
        background: url("../../../assets/check-down.svg") no-repeat;
        background-size: contain;
        margin-left: 5px;
      }

      &:last-child {
        &::after {
          display: none;
        }
      }
    }
  }
</style>

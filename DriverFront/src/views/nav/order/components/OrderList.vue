<template>
  <div class="scroll-view-content" :style="{
      bottom: fullHeight == 808 || fullHeight == 724 ? '70px' : '50px'
    }">
    <cube-scroll ref="scroll" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp" v-if="nextdata.length > 0 || nowdata.length > 0">
      <div class="order-content">
        <div class="item-order" v-for="item in type === 'now' ? nowdata : nextdata" :key="item.order_id + Math.random()">
          <div class="order-title">
            <div class="type-info">
              <p class="order-type" v-if="item.type == 1">拼车</p>
              <p class="order-type" v-else-if="item.type == 2">包车</p>
              <p class="order-type" v-else-if="item.type == 3">带货</p>
              <p class="order-type" v-else-if="item.type == 4">代办</p>
              <p class="order-type" v-else-if="item.type == 5">定制客运</p>
              <p class="order-type" v-else-if="item.type == 6">顺风车</p>
              <p class="order-type" v-else-if="item.type == 7">快车</p>
              <p class="order-type" v-else-if="item.type == 11">出租车</p>
              <p class="order-type" v-else>普通</p>
              <p class="paytype" v-if="item.is_pre_pay == 1">预付</p>
              <p class="paytype">{{ item.order_no }}</p>
              <span class="real-time-order-tag" v-if="item.type == 11||item.type == 7">实时</span>
            </div>
            <div class="order-price">
              <svg-icon slot="icon" class-name="price-dot" icon-class="price" />
              <p class="fz18 cff fb">
                {{ item.price }}
                <span class="fz12 c83">元</span>
              </p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz17 c2e">{{ show_time(item.start_time) }}</p>
            </div>
            <div class="order-pancel" v-if="item.type != 4 && item.type != 10">
              <svg-icon slot="icon" class-name="people-dot" icon-class="people" />
              <p class="fz17 c2e">{{ item.book_seating }} 人</p>
            </div>
            <div class="order-pancel" v-if="item.type == 10">
              <svg-icon slot="icon" class-name="people-dot" icon-class="people" />
              <p class="fz17 c2e">{{ item.passenger_name }}</p>
            </div>
            <div class="line-content" v-if="
                item.type == 1 ||
                  item.type == 2 ||
                  item.type == 5 ||
                  item.type == 6 ||
                  item.type == 7 ||
                  item.type == 11
              ">
              <div class="order-pancel address-pancel">
                <div class="s-dot"></div>
                <p class="fz17 c2e">{{ item.start_address_remark }}</p>
                <div class="navigation-btn" v-if="isWxBrowser" @click="toNavigation(item, 'start')">
                  <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                  <!-- <p>8.5km</p> -->
                </div>
                <div v-else>
                  <span v-if="isAndroid" @click="
                      handleNav(item.start_latitude, item.start_longitude)
                    " class="navigation-btn">
                    <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                    <!-- <p>1.2km</p> -->
                  </span>
                  <a v-else :href="
                      'iosamap://navi?sourceApplication=appname&poiname=fangheng&lat=' +
                        item.start_latitude +
                        '&lon=' +
                        item.start_longitude +
                        '&dev=1&style=2'
                    " target="_blank" class="navigation-btn">
                    <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                    <!-- <p>1.2km</p> -->
                  </a>
                </div>
              </div>
              <div class="order-pancel address-pancel" style=" border-top: 1px solid #eee;">
                <div class="e-dot"></div>
                <p class="fz17 c2e">{{ item.end_address_remark }}</p>
                <div class="navigation-btn" v-if="isWxBrowser" @click="toNavigation(item, 'end')">
                  <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                  <!-- <p>8.5km</p> -->
                </div>
                <div v-else>
                  <span v-if="isAndroid" @click="handleNav(item.end_latitude, item.end_longitude)" class="navigation-btn">
                    <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                    <!-- <p>1.2km</p> -->
                  </span>
                  <a v-else :href="
                      'iosamap://navi?sourceApplication=appname&poiname=fangheng&lat=' +
                        item.end_latitude +
                        '&lon=' +
                        item.end_longitude +
                        '&dev=1&style=2'
                    " target="_blank" class="navigation-btn">
                    <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                    <!-- <p>1.2km</p> -->
                  </a>
                </div>
              </div>
            </div>
            <div class="line-content" v-if="item.type == 3">
              <div class="order-pancel address-pancel">
                <div class="e-dot"></div>
                <div class="goods-view">
                  <p class="fz17 c2e oneline">{{ item.end_address_remark }}</p>
                  <p class="fz14 c83 oneline">
                    {{ item.reseverd_person }} {{ item.reseverd_phone }}
                  </p>
                </div>
                <div class="navigation-btn" @click="toNavigation(item, 'start')">
                  <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                  <!-- <p>8.5km</p> -->
                </div>
              </div>
              <div class="order-pancel address-pancel" style=" border-top: 1px solid #eee;">
                <div class="s-dot"></div>
                <div class="goods-view">
                  <p class="fz17 c2e oneline">
                    {{ item.start_address_remark }}
                  </p>
                  <p class="fz14 c83 oneline">
                    {{ item.delivery_person }} {{ item.delivery_phone }}
                  </p>
                </div>
                <div class="navigation-btn" @click="toNavigation(item, 'end')">
                  <svg-icon slot="icon" class-name="navigation-dot" icon-class="navigation" />
                  <!-- <p>1.2km</p> -->
                </div>
              </div>
            </div>
            <div class="things-content" v-if="item.type == 4">
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="type-dot" icon-class="things" />
                <p class="fz17 c2e">{{ item.name }}</p>
              </div>
              <div class="order-pancel">
                <svg-icon slot="icon" class-name="type-dot" icon-class="about" />
                <p class="fz17 c2e">{{ item.summary }}</p>
              </div>
            </div>
            <a class="order-pancel address-pancel" v-if="item.type != 3" style=" border-top: 1px solid #eee;" :href="'tel:' + item.reseverd_phone">
              <svg-icon slot="icon" class-name="user-dot" icon-class="user" />
              <p class="fz14 c63" style="line-height:34px;">
                尾号 ****{{ item.reseverd_phone.slice(7, 11) }}
              </p>
              <div class="navigation-btn">
                <svg-icon slot="icon" class-name="call-dot" icon-class="call" />
              </div>
            </a>
          </div>
          <div class="control-order" v-if="type == 'now'">
            <Drag :order_id="item.order_id" :type="item.type" :state="item.state" :key="item.order_id" @setStatus="resetComponent" v-if="hackReset" @refrshList="onPullingDown"></Drag>
            <div v-if="item.type == 7 && (item.state == 3 || item.state == 4)" class="setting-price" @click="showEdit(item)">
              附加费
            </div>
            <!-- 出租车上车前可以取消订单 -->
            <div v-if="item.type == 11 && item.state == 2" class="setting-cancel" @click="showCancel(item)">
              取消订单
            </div>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="暂无实时订单" v-if="type == 'now' && nowtotal == 0 && !isLoading"></Empty>
    <Empty textword="暂无预约订单" v-if="type == 'next' && nexttotal == 0 && !isLoading"></Empty>
  </div>
</template>
<script>
import Drag from "@/components/Drag";
import {
  doGetOrderList,
  doGetNextOrderList,
  doUpdateExtraPrice,
  doUpdateOrder,
} from "@/api/api";
import { exchange_time } from "@/utils/utils";
import { mapGetters } from "vuex";
import wx from "weixin-js-sdk";
export default {
  data () {
    return {
      value: false,
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      nextdata: [], //  预约订单
      nowdata: [], // 实时订单
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      queryNextValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      nowtotal: 0,
      nexttotal: 0,
      fullHeight: document.body.offsetHeight,
      timer: undefined,
      hackReset: true,
      isLoading: false,
      driving: null
    };
  },
  components: {
    Drag
  },
  computed: {
    ...mapGetters(["config"]),
    isAndroid () {
      //是否安卓手机
      let u = navigator.userAgent;
      return u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
    },
    isWxBrowser () {
      //是否微信浏览器
      let ua = navigator.userAgent.toLowerCase();
      return ua.match(/MicroMessenger/i) == "micromessenger";
    }
  },
  props: {
    query: {
      type: Object
    },
    type: {
      type: String
    }
  },
  watch: {
    query: {
      handler (n) {
        console.log(n)
        if (this.type == "now") {
          this.queryValue = {
            page: 1, // 开始页数
            size: 20, // 一次显示多少条
            ...n
          };

          this.getNowList("down");
        } else {

          this.queryNextValue = {
            page: 1, // 开始页数
            size: 20, // 一次显示多少条
            ...n
          },
            this.getNextList("down");
        }
      },
      deep: true
    }
  },
  mounted () {
    this.isLoading = true;
    const toast = this.$createToast({
      txt: "获取订单中..",
      time: 0
    });
    toast.show();
    setTimeout(() => {
      toast.hide();
      this.isLoading = false;
    }, 1500);
  },
  activated () {
    this.queryValue = {
      page: 1, // 开始页数
      size: 20 // 一次显示多少条
    };
    if (this.type == "now") {
      this.getNowList("down");
    } else {
      this.getNextList("down");
    }
    this.timer = setInterval(() => {
      if (this.type == "now") {
        this.getNowList("down");
      } else {
        this.getNextList("down");
      }
    }, 15000);
  },
  deactivated () {
    this.timer = clearInterval(this.timer);
  },

  methods: {
    calcDistance (slng, slat, elng, elat) {
      if (!!!this.driving) {
        this.driving = new AMap.Driving({
          // 驾车路线规划策略，AMap.DrivingPolicy.LEAST_TIME是最快捷模式
          policy: AMap.DrivingPolicy.LEAST_TIME
        })

      }
      return new Promise((resolve, reject) => {
        this.driving.search(new AMap.LngLat(slng, slat),
          new AMap.LngLat(elng, elat), function (status, result) {

            console.log('result', result)
            const { routes = [] } = result
            if (!routes || routes.length == 0) {
              resolve('-')

              return
            }
            let distance = routes[0].distance
            let distanceView = (Math.round(distance / 100) / 10).toFixed(1)
            console.log(distanceView)
            resolve(distanceView)
          });



      })

    },
    handleNav (lat, lng) {
      window.cc_js.navi(Number(lat), Number(lng));
    },
    resetComponent () {
      this.hackReset = false;
      this.$nextTick(() => {
        this.hackReset = true;
      });
    },
    showCancel (item) {
      const this_ = this;
      this.dialog = this.$createDialog({
        type: "prompt",
        title: "取消订单",
        content: "请输入原因",
        prompt: {
          value: "",
          placeholder: "请输入"
        },
        onConfirm: (e, promptValue) => {
          if (!promptValue) {
            const toast = this_.$createToast({
              time: 1000,
              txt: "请输入原因",
              type: "error"
            });
            toast.show();
            this_.showCancel(item);
            return;
          }

          this.checkCancel(item, promptValue);
        }
      }).show();
    },
    showEdit (item) {
      this.dialog = this.$createDialog({
        type: "prompt",
        title: "订单附加金额",
        content: "请与乘客沟通确认附加金额及原因",
        prompt: {
          value: "",
          type: "tel",
          placeholder: "请输入附加金额(元)"
        },
        onConfirm: (e, promptValue) => {
          this.handleEdit(item, promptValue);
        }
      }).show();
    },
    checkCancel (params, reason) {
      //取消订单
      let query = {
        order_id: params.order_id,
        status: 7,
        reason: reason
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdateOrder(query).then(response => {
        toast.hide();
        if (response.ret) {
          this.$createToast({
            txt: response.data || "操作成功",
            type: "Correct",
            time: 1000
          }).show();
          this.queryValue = {
            page: 1, // 开始页数
            size: 20 // 一次显示多少条
          };
          if (this.$refs.scroll) {
            this.$refs.scroll.refresh();
          }
          this.getNowList("down");
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    },
    handleEdit (parms, amount) {
      let query = {
        order_id: parms.order_id,
        extra_price: amount
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdateExtraPrice(query).then(response => {
        toast.hide();
        if (response.ret) {
          this.$createToast({
            txt: response.data,
            type: "Correct",
            time: 1000
          }).show();
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    },
    toNavigation (item, type) {
      //  href="https://uri.amap.com/marker?position=104.043253,30.665739&name=中医大省医院"  高德网页版地图
      // OpenLocation(item, type);
      wx.openLocation({
        latitude:
          type === "start"
            ? parseFloat(item.start_latitude)
            : parseFloat(item.end_latitude),
        longitude:
          type === "start"
            ? parseFloat(item.start_longitude)
            : parseFloat(item.end_longitude),
        name: type === "start" ? "上车点" : "下车点",
        address:
          type === "start"
            ? item.start_address_remark
            : item.end_address_remark,
        scale: 14
      });
    },
    show_time (time) {
      return exchange_time(time);
    },

    //实时订单
    getNowList (type) {
      let that = this;
      // this.toast.show();
      doGetOrderList(that.queryValue).then(response => {
        // that.toast.hide();
        if (response.ret) {
          let data_ = response.data
          data_.map(async (item_) => {
            // if (['7', '11'].includes(item_.type)) {
            //   const distance = await this.calcDistance(item_.start_longitude, item_.start_latitude, item_.end_longitude, item_.end_latitude)
            //   this.$set(item_, 'distance', distance)
            //   let fromDis = '-'
            //   if (item_.longitude) {
            //     fromDis = await this.calcDistance(item_.longitude, item_.latitude, item_.start_longitude, item_.start_latitude)
            //   }
            //   this.$set(item_, 'fromDis', fromDis)
            // }
            return item_
          })


          if (type == "up") {
            that.nowdata = [...that.nowdata, ...data_];
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
            }
          } else {
            that.nowdata = [...data_];
            if (this.$refs.scroll) {
              this.$refs.scroll.forceUpdate();
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.nowtotal = response.count;
        } else {
          if (type == "down") {
            this.nowdata = [];
            this.nowtotal = 0;
          }
          if (this.$refs.scroll && this.queryValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryValue.page--;
          }
        }
      });
    },
    //预约单
    getNextList (type) {
      let that = this;
      // this.toast.show();
      doGetNextOrderList(that.queryNextValue).then(response => {
        // that.toast.hide();
        if (response.ret) {
          let data_ = response.data
          data_.map(async (item_) => {
            if (['7', '11'].includes(item_.type)) {
              const distance = await this.calcDistance(item_.start_longitude, item_.start_latitude, item_.end_longitude, item_.end_latitude)
              this.$set(item_, 'distance', distance)
              let fromDis = '-'
              if (item_.longitude) {
                fromDis = await this.calcDistance(item_.longitude, item_.latitude, item_.start_longitude, item_.start_latitude)
              }
              this.$set(item_, 'fromDis', fromDis)
            }
            return item_
          })



          if (type == "up") {
            that.nextdata = that.nextdata.concat(data_);
            this.$refs.scroll.forceUpdate();
          } else {
            that.nextdata = data_;
            this.$refs.scroll.forceUpdate();
            this.$refs.scroll.resetPullUpTxt();
          }
          that.nexttotal = response.count;
        } else {
          if (type == "down") {
            this.nextdata = [];
            this.nexttotal = 0;
          }
          if (this.$refs.scroll && this.queryNextValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryNextValue.page--;
          }
        }
      });
    },
    //下拉刷新
    onPullingDown () {
      if (this.type === "now") {
        this.queryValue = {
          page: 1, // 开始页数
          size: 20 // 一次显示多少条
        };
        if (this.$refs.scroll) {
          this.$refs.scroll.refresh();
        }
        this.getNowList("down");
      } else {
        this.queryNextValue = {
          page: 1, // 开始页数
          size: 20 // 一次显示多少条
        };
        if (this.$refs.scroll) {
          this.$refs.scroll.refresh();
        }
        this.getNextList("down");
      }
    },
    //加载更多
    onPullingUp () {
      if (this.type === "now") {
        this.queryValue.page++;
        this.getNowList("up");
      } else {
        this.queryNextValue.page++;
        this.getNextList("up");
      }
    }
  }
};
</script>
<style scoped lang="less">
  .new-order-warp {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 10px 0;
    .item-view-cell {
      flex: 0 0 35%;
      display: flex;
      flex-direction: column;
      .label {
        color: #939496;
        font-size: 13px;
      }
      .val-cell {
        display: flex;
        align-items: flex-end;
        line-height: 1;
        margin-top: 10px;
        b {
          font-size: 22px;
          font-weight: 700;
          color: #333;
          line-height: 16px;
          margin-right: 3px;
        }
        span {
          font-size: 12px;
          color: #333;
        }
      }
    }
    .sed {
      flex: 0 0 30%;
    }
  }
  .real-time-order-tag {
    height: 20px;
    padding: 0 5px;
    line-height: 20px;
    font-size: 12px;
    color: #909399;
    border: 1px solid #e9e9eb;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
    background-color: #f4f4f5;
    // position: absolute;
    // left: 0;
    // top: 0;
    // bottom: 0;
    display: inline-block;
    margin-left: 5px;
    // margin: auto;
  }
  .scroll-view-content {
    display: block;
    position: absolute;
    top: 90px;
    left: 0;
    width: 100%;
    .order-content {
      background-color: #fff;
      padding: 0 10px;
      .item-order {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
          0 1px 4px rgba(0, 0, 0, 0.117647);
        display: block;
        padding: 0 15px 20px 15px;
        border-radius: 5px;
        margin: 5px 0 15px 0;
        .order-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
          .type-info {
            display: flex;
            align-items: center;
            .order-type {
              font-size: 15px;
              font-weight: bold;
              color: #474747;
              margin-right: 10px;
            }
            .paytype {
              padding: 3px 5px;
              font-size: 13px;
              border: 1px solid #eee;
            }
          }
          .order-price {
            display: flex;
            align-items: center;
            .price-dot {
              margin-right: 5px;
            }
          }
        }
        .order-middle {
          padding: 10px 0;
          .order-pancel {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 5px 0;
            .s-dot,
            .e-dot {
              width: 7px;
              height: 7px;
              min-width: 7px;
              border-radius: 50%;
              background: #1c73e2;
              margin-right: 16px;
              margin-left: 1px;
            }
            .user-dot {
              margin-right: 13px;
            }
            .people-dot {
              margin-right: 13px;
            }
            .e-dot {
              background: #fc9153;
            }
            p {
              line-height: 22px;
            }
            .time-dot {
              width: 8px;
              height: 8px;
              margin-right: 15px;
            }
            .type-dot {
              width: 10px;
              height: 10px;
              margin-right: 13px;
            }
          }
          .address-pancel {
            justify-content: space-between;
            p {
              flex: 1;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
            }
            .goods-view {
              flex: 1;
              max-width: 260px;
              p {
                display: block;
              }
            }
            .navigation-btn {
              width: 40px;
              min-width: 40px;
              margin-left: 15px;
              display: block;
              .navigation-dot {
                width: 15px;
                height: 15px;
                color: #8a8a8a;
                display: block;
                margin: 0 auto;
              }
              .call-dot {
                width: 20px;
                height: 20px;
                display: block;
                margin: 0 auto;
              }
              p {
                font-size: 10px;
                color: #8a8a8a;
                text-align: center;
              }
            }
          }
        }
        .control-order {
          display: flex;
          justify-content: center;
          align-items: center;
          button {
            margin: 0 5px;
          }
          .setting-price {
            background: #ff8033;
            text-align: center;
            line-height: 50px;
            color: #fff;
            width: 50px;
            height: 50px;
            display: block;
            min-width: 50px;
            margin-left: 10px;
            box-shadow: 0 0.026667rem 0.16rem rgba(0, 0, 0, 0.117647),
              0 0.026667rem 0.106667rem rgba(0, 0, 0, 0.117647);
          }
          .setting-cancel {
            background: #fff;
            text-align: center;
            line-height: 50px;
            color: #888;
            width: 80px;
            height: 50px;
            display: block;
            min-width: 50px;
            margin-left: 10px;
            box-shadow: 0 0.026667rem 0.16rem rgba(0, 0, 0, 0.117647),
              0 0.026667rem 0.106667rem rgba(0, 0, 0, 0.117647);
          }
        }
      }
    }
  }
</style>

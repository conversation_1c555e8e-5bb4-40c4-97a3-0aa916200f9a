<template>
  <div
    class="scroll-view-content"
    :style="{
      bottom: fullHeight == 808 || fullHeight == 724 ? '70px' : '50px'
    }"
  >
    <cube-scroll
      v-if="listdata.length > 0"
      ref="scroll"
      :data="listdata"
      :options="options"
      @pulling-down="refreshlist"
      @pulling-up="onPullingUp"
    >
      <div class="order-content">
        <div class="item-order" v-for="(item, index) in listdata" :key="index">
          <div class="order-title">
            <div class="type-info">
              <p class="fz14 c4b fb">
                {{ item.start_address_remark }}
                <svg-icon
                  slot="icon"
                  class-name="toright-dot"
                  icon-class="wangfan"
                />
                {{ item.end_address_remark }}
              </p>
              <div style="font-size: 12px;color: #999">
                {{ item.away_time }}
              </div>
            </div>
            <div class="order-price">
              <p class="fz12 c83">
                往返
                <span class="fz15 cff">{{ item.everyday_times }}</span>
                <span class="fz12 c83">次/天</span>
              </p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c2e">
                起止日期：{{ show_date(item.start_time) }}-{{
                  show_date(item.end_time)
                }}
              </p>
            </div>
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c4b">
                早晨上学时间：{{ exchange_group(item.go_school_time_am) }}
              </p>
            </div>
            <div class="order-pancel" v-if="item.everyday_times == 4">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c4b">
                中午放学时间：{{ exchange_group(item.leave_school_time_am) }}
              </p>
            </div>
            <div class="order-pancel" v-if="item.everyday_times == 4">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c4b">
                下午上学时间：{{ exchange_group(item.go_school_time_pm) }}
              </p>
            </div>
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c4b">
                下午放学时间：{{ exchange_group(item.leave_school_time_pm) }}
              </p>
            </div>
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="person-dot" icon-class="user" />
              <p class="fz15 c4b withline">
                <span class="fz13 cff">{{ item.total_count }}人团</span>
              </p>
            </div>
          </div>
          <div class="control-order" v-if="item.status == 3">
            <cube-button
              :light="true"
              :inline="true"
              style="flex:1"
              @click="refuseOrder(item)"
              >拒绝</cube-button
            >
            <cube-button
              :primary="true"
              :inline="true"
              style="flex:2"
              @click="receiptOrder(item)"
              >接单</cube-button
            >
          </div>
          <div class="status-content" v-if="item.status == 4">
            <p class="cma fz14 tc">订单进行中</p>
          </div>
          <div class="status-content" v-if="item.status == 5">
            <p class="c83 fz14 tc">订单已完成</p>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="暂无定制接送订单" v-if="total == 0"></Empty>
  </div>
</template>
<script>
import {
  getStudentCustomizedLines,
  doUpdateStudentCustomizedLine
} from "@/api/api";
import { exchange_time, exchange_group, exchange_date } from "@/utils/utils";

import { mapGetters } from "vuex";
export default {
  data() {
    return {
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      fullHeight: document.body.offsetHeight,
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      total: undefined,
      listdata: [], // 数据存放数组,
      timer: undefined
    };
  },
  computed: {
    ...mapGetters(["driverInfo"])
  },
  activated() {
    this.queryValue = {
      page: 1, // 开始页数
      size: 20 // 一次显示多少条
    };
    this.getList("down");
    this.timer = setInterval(() => {
      this.getList("down");
    }, 10000);
  },
  deactivated() {
    this.timer = clearInterval(this.timer);
  },
  methods: {
    show_time(time) {
      return exchange_time(time);
    },
    show_date(time) {
      return exchange_date(time);
    },
    exchange_group(time) {
      return exchange_group(time);
    },
    getList(type) {
      let that = this;
      // const toast = this.$createToast({
      //   txt: "努力加载中..",
      //   time: 0
      // });
      // toast.show();
      getStudentCustomizedLines(that.queryValue).then(response => {
        // toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.listdata = that.listdata.concat(response.data);
          } else {
            that.listdata = response.data;
            if (this.$refs.scroll) {
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.total = response.count;
        } else {
          if (type == "down") {
            this.listdata = [];
            this.total = 0;
          }
          if (this.$refs.scroll && this.queryValue.page > 1) {
            this.$refs.scroll.forceUpdate();
            this.queryValue.page--;
          }
        }
      });
    },
    refuseOrder(e) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-warn",
        title: "确认拒绝该订单？",
        content: "拒绝后将不会再次收到该订单",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          let obj = {
            status: 1,
            student_customized_line_id: e.student_customized_line_id
          };
          this.doUpdateStatus(obj);
        }
      }).show();
    },
    //接单
    receiptOrder(e) {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-alert",
        title: "确认接单？",
        content: "接单后不可取消订单",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          let obj = {
            status: 2,
            student_customized_line_id: e.student_customized_line_id
          };
          this.doUpdateStatus(obj);
        }
      }).show();
    },
    //下拉刷新
    refreshlist() {
      this.queryValue = {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      };
      if (this.$refs.scroll) {
        this.$refs.scroll.refresh();
      }
      this.getList("down");
    },
    //加载更多
    onPullingUp() {
      this.queryValue.page++;
      this.getList("up");
    },
    //接单，拒绝订单
    doUpdateStatus(obj) {
      let spa = {
        driver_id: this.driverInfo.driver_id,
        ...obj
      };
      const toast = this.$createToast({
        txt: "操作中",
        time: 0
      });
      toast.show();
      doUpdateStudentCustomizedLine(obj).then(response => {
        toast.hide();
        if (response.ret) {
          this.$createToast({
            txt: response.data,
            type: "Correct",
            time: 1000
          }).show();
          setTimeout(() => {
            this.refreshlist();
          }, 500);
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
.scroll-view-content {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  .order-content {
    background-color: #fff;
    padding: 0 10px;
    .item-order {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
        0 1px 4px rgba(0, 0, 0, 0.117647);
      display: block;
      padding: 0 10px 10px 10px;
      border-radius: 5px;
      margin: 15px auto;
      .order-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        .type-info {
          display: flex;
          align-items: center;
          .free_driver_dot {
            background-color: #1c73e2;
            font-size: 10px;
            color: #fff;
            border-radius: 4px;
            padding: 4px 5px;
            margin: 0 8px;
          }
        }
        .order-price {
          display: flex;
          align-items: center;
          .price-dot {
            margin-right: 5px;
          }
        }
      }
      .order-middle {
        padding: 10px 0;
        .order-pancel {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-bottom: 5px;
          .withline {
            display: block;
            border: 1px solid #ff8033;
            padding: 0 5px;
          }
          p {
            line-height: 20px;
          }
          .time-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
          }
          .person-dot,
          .seat-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
            color: #999;
          }
        }
      }
      .control-order {
        display: flex;
        justify-content: space-between;
        align-items: center;
        button {
          margin: 0 5px;
        }
      }
      .status-content {
        padding: 10px 0;
      }
    }
  }
}
</style>

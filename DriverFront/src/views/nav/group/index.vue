<template>
  <div class="receipt-box">
    <group-list ref="rlist"></group-list>
    <div class="refresh_btn" ref="refresh" @click="handlerefresh">
      <svg-icon slot="icon" class-name="fresh-dot" icon-class="fresh"/>
    </div>
  </div>
</template>

<script>
import Glist from "./components/GroupList";
export default {
  data() {
    return {
      init: 1,
    };
  },
  methods: {
    handlerefresh() {
      this.$refs.refresh.style.transform = "rotate(" + this.init * 360 + "deg)";
      this.init++;
      this.$refs.rlist.getList("down");
    },
  },
  components: {
    "group-list": Glist,
  },
  // activated() {
  //   this.init = 1;
  // }
};
</script>

<style scoped lang="less">
.receipt-box {
  display: block;
  width: 100vw;
  bottom: 100vh;
  .refresh_btn {
    transition: all 0.5s linear;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 80px;
    right: 20px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.217647);
    z-index: 1;
    .fresh-dot {
      width: 30px;
      height: 30px;
      display: block;
    }
  }
}
</style>

<template>
  <div class="main-content">
    <foot-view v-if="showHead && labels && labels.length > 1"></foot-view>
    <keep-alive>
      <router-view
        :showHead="showHead"
        @hidehead="hidehead"
        @showhead="showhead"
      ></router-view>
    </keep-alive>
  </div>
</template>

<script>
import Foot from "@/components/Foot.vue";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      showHead: true
    };
  },
  computed: {
    ...mapGetters(["asyncroutes"]),
    labels() {
      let labels = [];
      this.asyncroutes.map(item => {
        if (!item.hidden) {
          item.children.map(itemroute => {
            labels.push({
              label: itemroute.meta.title,
              icon: itemroute.meta.icon,
              path: itemroute.path,
              value: itemroute.path
            });
          });
        }
      });
      return labels;
    }
  },
  methods: {
    showhead() {
      this.showHead = true;
    },
    hidehead() {
      this.showHead = false;
    }
  },
  watch: {},
  mounted() {
    //调用高德组件
    // setTimeout(()=>{
    //     that.$store.commit('changefromlocation',[104.056178,30.537898].join(','));
    //     that.$store.commit('changebaselocation',[104.056178,30.537898].join(','));
    // },1000)
  },
  components: {
    "foot-view": Foot
  },
  created: function() {}
};
</script>
<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
</style>

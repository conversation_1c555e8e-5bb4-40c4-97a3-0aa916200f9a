<template>
  <div class="registerPage">
    <div class="register-top">
      <img src="@/assets/login-bg.png" alt="">
      <div class="back_btn" @click="backRoute">
        <svg-icon slot="icon" class-name="left-dot" icon-class="bgleft"/>
      </div>
      <div class="top-content">
        <!-- <h5>注册</h5> -->
        <div class="register-form" v-if="!driverInfo">
          <div class="item-form">
            <cube-input
              v-model="registerForm.cellphone"
              style="flex:1"
              type="tel"
              pattern="\d*"
              @blur="onBlur"
              placeholder="请输入手机号"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form">
            <cube-input
              v-model="registerForm.cellphone_validate_code"
              type="tel"
              pattern="\d*"
              @blur="onBlur"
              placeholder="请输入验证码"
            ></cube-input>
            <cube-button
              :primary="true"
              :inline="true"
              :disabled="sendMsgDisabled"
              @click="getCode"
            >
              {{
              sendMsgDisabled ? time + "秒后获取" : "获取验证码"
              }}
            </cube-button>
          </div>
          <div class="item-form">
            <cube-input
              v-model="registerForm.password"
              style="flex:1"
              type="text"
              @blur="onBlur"
              placeholder="请设置登录密码"
              :clearable="clearable"
            ></cube-input>
          </div>
        </div>
        <div class="register-form">
          <div class="item-form">
            <div class="item-label">真实姓名</div>
            <cube-input
              v-model="registerForm.real_name"
              style="flex:1"
              type="text"
              @blur="onBlur"
              placeholder="请输入你的真实姓名"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form">
            <div class="item-label">昵称</div>
            <cube-input
              v-model="registerForm.name"
              style="flex:1"
              type="text"
              @blur="onBlur"
              placeholder="请输入你的昵称(如张师傅)"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form">
            <div class="item-label">车型</div>
            <cube-input
              v-model="registerForm.car_brand"
              style="flex:1"
              type="text"
              @blur="onBlur"
              placeholder="请输入车型(如朗逸)"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form">
            <div class="item-label">车牌号</div>
            <cube-input
              v-model="registerForm.car_tail_number"
              style="flex:1"
              type="text"
              @blur="onBlur"
              placeholder="请输入你的车牌号"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form">
            <div class="item-label">座位数</div>
            <cube-input
              v-model="registerForm.total_seating"
              style="flex:1"
              type="tel"
              pattern="\d*"
              @blur="onBlur"
              placeholder="请输入座位数(含司机)"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="upload-form border">
            <div class="upload-title">驾驶证</div>
            <cube-upload
              ref="driver_license"
              v-model="driver_list"
              :action="action"
              :max="1"
              @file-removed="fileremoved('driver_license')"
              @files-added="driverHandler"
              @file-success="checkSuccess"
              @file-error="errHandler"
            >
              <div class="clear-fix">
                <cube-upload-file v-for="(file, i) in driver_list" :file="file" :key="i"></cube-upload-file>
                <cube-upload-btn :multiple="false">
                  <div>
                    <i>＋</i>
                    <p>点击上传驾驶证正面</p>
                  </div>
                </cube-upload-btn>
              </div>
            </cube-upload>
          </div>
          <div class="upload-form">
            <div class="upload-title">行驶证</div>
            <cube-upload
              ref="driving_license"
              :max="1"
              v-model="driving_list"
              :action="action"
              @files-added="drivingHandler"
              @file-removed="fileremoved('driving_license')"
              @file-success="checkSuccess"
              @file-error="errHandler"
            >
              <div class="clear-fix">
                <cube-upload-file v-for="(file, i) in driving_list" :file="file" :key="i"></cube-upload-file>
                <cube-upload-btn :multiple="false">
                  <div>
                    <i>＋</i>
                    <p>点击上传行驶证正面</p>
                  </div>
                </cube-upload-btn>
              </div>
            </cube-upload>
          </div>
        </div>
      </div>
    </div>
    <SystemView/>
    <div class="control-register">
      <div class="view-control">
        <cube-button
          :primary="true"
          :disabled="canSubmit"
          @click="doRegister"
        >{{driverInfo?'重新提交':'提交'}}</cube-button>
      </div>
    </div>
  </div>
</template>
<script>
import { getAds } from "@/api/api";
import { doGetCode, doRegisterThree, doSubmitDriverDataThree } from "@/api/api";
import { getCallBack } from "@/utils/auth";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      registerForm: {
        cellphone: undefined,
        password: undefined,
        cellphone_validate_code: undefined,
        real_name: undefined,
        name: undefined,
        car_tail_number: undefined,
        car_brand: undefined,
        total_seating: undefined,
        driver_license: undefined,
        driving_license: undefined
      },
      driver_list: [],
      driving_list: [],
      uploadkey: "driver_license",
      clearable: {
        visible: true,
        blurHidden: true
      },
      action: {
        target: process.env.VUE_APP_BASE_API + "/Home/Common/doCommonUpload",
        headers: {
          callback: getCallBack()
        },
        data: {
          img: undefined
        }
      }, //上传地址
      autofocus: true,
      eye: {
        open: false,
        reverse: false
      },
      redirect: undefined,
      sendMsgDisabled: false,
      time: 60
    };
  },
  components: {},
  mounted() {
    // this.getAdview();
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
      deep: true
    },
    "registerForm.cellphone_validate_code": {
      handler(n, o) {
        n = n.slice(0, 6);
        this.$nextTick(() => {
          this.registerForm.cellphone_validate_code = n;
        });
      }
    }
  },
  computed: {
    ...mapGetters(["config", "driverInfo"]),
    canSubmit() {
      if (this.driverInfo) {
        if (
          this.registerForm.real_name &&
          this.registerForm.car_tail_number &&
          this.registerForm.car_brand &&
          this.registerForm.total_seating &&
          this.registerForm.driver_license &&
          this.registerForm.driving_license
        ) {
          return false;
        } else {
          return true;
        }
      } else {
        if (
          this.registerForm.cellphone &&
          this.registerForm.password &&
          this.registerForm.cellphone_validate_code &&
          this.registerForm.real_name &&
          this.registerForm.car_tail_number &&
          this.registerForm.car_brand &&
          this.registerForm.total_seating &&
          this.registerForm.driver_license &&
          this.registerForm.driving_license
        ) {
          return false;
        } else {
          return true;
        }
      }
    }
  },
  methods: {
    driverHandler(files) {
      this.action.data.img = files[0];
      this.uploadkey = "driver_license";
      const file = this.driver_list[0];
      file && this.$refs.driver_license.removeFile(file);
    },
    drivingHandler(files) {
      this.action.data.img = files[0];
      this.uploadkey = "driving_license";
      const file = this.driving_list[0];
      file && this.$refs.driving_license.removeFile(file);
    },
    checkSuccess(file) {
      if (file.response.ret) {
        this.registerForm[this.uploadkey] = file.response.data.img;
      }
    },
    errHandler(file) {
      this.$createToast({
        type: "warn",
        txt: "上传失败",
        time: 1000
      }).show();
    },
    fileremoved(key) {
      this.registerForm[key] = undefined;
    },
    backRoute() {
      this.$router.go(-1);
    },
    addedHandler(key) {
      const file = this.registerForm[key][0];
      file && this.$refs[key].removeFile(file);
    },
    errHandler(file) {
      // const msg = file.response.message
      this.$createToast({
        type: "warn",
        txt: "Upload fail",
        time: 1000
      }).show();
    },
    sendVerifyCode() {
      let that = this;
      let query = {
        cellphone: this.registerForm.cellphone
      };
      doGetCode(query).then(res => {
        if (res.ret) {
          this.toast = this.$createToast({
            txt: "发送成功",
            type: "txt"
          });
          this.toast.show();
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    },
    getCode() {
      let that = this;
      if (!that.registerForm.cellphone) {
        that.toast = that.$createToast({
          txt: "请先输入手机号码",
          type: "txt"
        });
        that.toast.show();
        return;
      }
      that.sendVerifyCode();
      that.sendMsgDisabled = true;
      let interval = setInterval(() => {
        if (that.time-- <= 0) {
          that.time = 60;
          that.sendMsgDisabled = false;
          clearInterval(interval);
        }
      }, 1000);
    },
    doRegister() {
      const toast = this.$createToast({
        txt: "提交中..",
        time: 0
      });
      toast.show();
      if (this.driverInfo) {
        //有司机信息，重新提交

        doSubmitDriverDataThree(this.registerForm).then(res => {
          toast.hide();
          if (res.ret) {
            this.$createDialog({
              type: "alert",
              icon: "cubeic-alert",
              showClose: false,
              title: "提交成功",
              content: "我们会在3个工作日已短信告知审核结果，请耐心等候",
              confirmBtn: {
                text: "我知道了",
                active: true
              },
              onConfirm: () => {
                this.$router.go(-1);
              }
            }).show();
          } else {
            this.toast = this.$createToast({
              txt: res.data,
              type: "txt"
            });
            this.toast.show();
          }
        });
      } else {
        doRegisterThree(this.registerForm).then(res => {
          toast.hide();
          if (res.ret) {
            this.$createDialog({
              type: "alert",
              icon: "cubeic-alert",
              showClose: false,
              title: "提交成功",
              content: "我们会在3个工作日已短信告知审核结果，请耐心等候",
              confirmBtn: {
                text: "我知道了",
                active: true
              },
              onConfirm: () => {
                this.$router.push({ path: "/login" });
              }
            }).show();
          } else {
            this.toast = this.$createToast({
              txt: res.data,
              type: "txt"
            });
            this.toast.show();
          }
        });
      }
    },
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    }
  }
};
</script>
<style lang="less" scoped>
.registerPage {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: auto;
  display: block;
  padding-bottom: 85px;
  .control-register {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1;
    background: #fff;
    box-shadow: 0 0 6px 1px rgba(28, 115, 226, 0.16);
    .view-control {
      padding: 15px;
    }
  }
  .click-btn {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    .cut-line {
      width: 1px;
      height: 15px;
      background: rgba(159, 159, 159, 1);
      display: block;
      margin: 0 20px;
    }
    .register {
      display: flex;
      align-items: center;
      color: #9f9f9f;
      .right-dot {
        margin-left: 5px;
      }
    }
  }
  .register-top {
    .back_btn {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: fixed;
      top: 15px;
      left: 0;
      z-index: 1;
      .left-dot {
        width: 20px;
        height: 20px;
        display: block;
      }
    }
    img {
      width: 100%;
      object-fit: contain;
    }
    .top-content {
      margin: 0 10px;
      margin-top: -100px;
      display: block;
      position: relative;
      z-index: 1;
      h5 {
        font-size: 19px;
        color: #fff;
        text-align: center;
        margin-bottom: 40px;
      }
      .register-form {
        padding: 15px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 12px 1px rgba(28, 115, 226, 0.16);
        border-radius: 4px;
        margin-bottom: 20px;
        .item-form {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 5px 0;
          .item-label {
            width: 70px;
            min-width: 70px;
            font-size: 15px;
            color: #636363;
          }
        }
        .upload-form {
          display: block;
          .upload-title {
            display: flex;
            padding: 10px 0;
            color: #2e2e2e;
            font-weight: bold;
            font-size: 14px;
          }
          .upload-title::before {
            width: 2px;
            display: block;
            content: "";
            height: 15px;
            background: #1c73e2;
            margin-right: 10px;
          }
        }
        .border {
          border-bottom: 1px solid #eee;
        }
      }
    }
  }
}
.cube-upload {
  .cube-upload-file,
  .cube-upload-btn {
    margin: 0;
    height: 200px;
  }
  .cube-upload-file {
    margin: 0;
    + .cube-upload-btn {
      margin-top: -200px;
      opacity: 0;
    }
  }
  .cubeic-wrong {
    display: none;
  }
  .cube-upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      text-align: center;
    }
    i {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin-bottom: 20px;
      font-size: 32px;
      line-height: 1;
      font-style: normal;
      color: #fff;
      background-color: rgba(28, 115, 226);
      border-radius: 50%;
    }
  }
}
</style>

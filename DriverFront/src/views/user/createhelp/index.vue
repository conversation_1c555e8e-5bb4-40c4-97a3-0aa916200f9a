<template>
  <div class="createPage">
    <Navigation title="发布"/>
    <section class="infoView">
      <div class="info-content">
        <div class="item-info border" @click="setorigin">
          <div class="label-dot"></div>
          <div class="info-view">
            {{
            lineObj.start_address_remark
            ? lineObj.start_address_remark
            : originp
            }}
          </div>
        </div>
        <div class="item-info border" @click="setfinish">
          <div class="label-dot end"></div>
          <div class="info-view">
            {{
            lineObj.end_address_remark ? lineObj.end_address_remark : finishp
            }}
          </div>
        </div>
        <div class="item-info border">
          <div class="item-harf line">
            <svg-icon slot="icon" class-name="info-dot" icon-class="time"/>
            <div class="harf-view time-view" @click="showTimePicker">{{ lineObj.timeshow }}</div>
          </div>
          <div class="item-harf" @click="showPassengerPicker">
            <div class="harf-view">剩余 {{ lineObj.residual_seating }} 座</div>
            <svg-icon slot="icon" class-name="toright-dot" icon-class="click-right"/>
          </div>
        </div>
        <div class="item-info price-info border">
          <div class="label" style="min-width:120px;">
            <svg-icon slot="icon" class-name="info-dot" icon-class="price"/>
            <p>价格(元/人)</p>
          </div>
          <cube-input
            v-model="lineObj.price"
            type="tel"
            pattern="\d*"
            @blur="onBlur"
            :autofocus="true"
            placeholder="请输入价格"
            :clearable="clearable"
          ></cube-input>
        </div>
        <div class="item-info" @click="tomessage">
          <svg-icon slot="icon" class-name="info-dot" icon-class="setmessage"/>
          <div class="info-view">{{ message!=""?message:"备注信息" }}</div>
        </div>
      </div>
      <cube-button :primary="true" :disabled="canCreate" @click="handelOrder">确认发布</cube-button>
    </section>
    <select-place
      v-if="setplace"
      @cancel="cancelplace"
      @setFormInfo="setFormInfo"
      :placeType="placeType"
      :placeword="placeword"
    ></select-place>
  </div>
</template>
<script>
import Placeset from "@/components/Placeselect";
import { doReleaseParentHelpLine } from "@/api/api";
import { mapGetters } from "vuex";
const passengers = [
  { text: "1座", value: 1 },
  { text: "2座", value: 2 },
  { text: "3座", value: 3 },
  { text: "4座", value: 4 },
  { text: "5座", value: 5 },
  { text: "6座", value: 6 }
];
export default {
  data() {
    return {
      setplace: false, //显示选点界面

      originp: "您从哪儿出发",
      finishp: "您要去哪儿",

      placeType: "start",
      placeword: "",
      clearable: {
        visible: true,
        blurHidden: true
      },
      lineObj: {
        timeshow: "最晚出发时间", //出发时间
        start_time: undefined,
        residual_seating: 1, //剩余座位
        price: undefined
      }
    };
  },
  computed: {
    ...mapGetters(["message"]),
    canCreate() {
      if (
        this.lineObj.start_longitude &&
        this.lineObj.end_longitude &&
        this.lineObj.price &&
        this.lineObj.start_time
      ) {
        return false;
      } else {
        return true;
      }
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    setFormInfo(info) {
      this.lineObj = { ...info, ...this.lineObj };
    },
    init() {
      this.lineObj = {
        timeshow: "最晚出发时间", //出发时间
        start_time: undefined,
        residual_seating: 1, //剩余座位
        price: undefined
      };
    },
    handelOrder() {
      const toast = this.$createToast({
        txt: "发布中",
        time: 0
      });
      toast.show();
      let query = {
        ...this.lineObj,
        summary: this.message ? this.message : undefined
      };
      doReleaseParentHelpLine(query).then(res => {
        toast.hide();
        if (res.ret) {
          this.$store.dispatch("user/changeMessage", "");
          this.$createDialog({
            type: "alert",
            icon: "cubeic-alert",
            showClose: false,
            title: "发布成功",
            content: "可在列表中查看我发布的家长互助",
            confirmBtn: {
              text: "我知道了",
              active: true
            },
            onConfirm: () => {
              this.init();
              this.$router.push({
                path: "/help"
              });
            }
          }).show();
        } else {
          this.$createToast({
            txt: response.data,
            type: "error",
            time: 1000
          }).show();
        }
      });
    },
    showTimePicker() {
      let vm = this;
      const timePicker = this.$createTimePicker({
        showNow: false,
        minuteStep: 15,
        delay: 20,
        day: {
          len: 7,
          filter: ["今天", "明天", "后天"],
          format: "M月d日"
        },
        onSelect: (selectedTime, selectedText, formatedTime) => {
          this.lineObj.timeshow = selectedText;
          this.lineObj.start_time = formatedTime;
        }
      });
      // console.log(vm.nowtime)
      timePicker.show();
    },
    showPassengerPicker() {
      if (!this.picker) {
        this.picker = this.$createPicker({
          title: "剩余座位",
          subtitle: "最多可选6座(包括婴儿、儿童)",
          data: [passengers],
          onSelect: this.selectHandle
        });
      }
      this.picker.show();
    },
    selectHandle(selectedVal, selectedIndex, selectedText) {
      this.lineObj.residual_seating = selectedVal.join(",");
    },
    tomessage() {
      this.$router.push("/addmessage");
    },
    setorigin() {
      this.placeword = "您从哪儿出发";
      this.placeType = "start";
      this.setplace = true;
    },
    setfinish() {
      this.placeType = "end";
      this.placeword = "您要去哪儿";
      this.setplace = true;
    },
    cancelplace() {
      this.setplace = false;
    },
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    }
  },
  components: {
    "select-place": Placeset
  }
};
</script>
<style lang="less" scoped>
.createPage {
  display: block;
  .infoView {
    display: block;
    padding: 0 10px;
    .info-content {
      margin: 20px 0;
      display: block;
      padding: 0 10px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.12);
      border-radius: 5px;
      .item-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0;
        .item-harf {
          flex: 1;
          display: flex;
          align-items: center;
          .harf-view {
            flex: 1;
            text-align: center;
            font-size: 16px;
            color: #4a4a4a;
          }
          .time-view {
            text-align: left;
            color: #1c73e2;
          }
        }
        .line {
          border-right: 1px solid #eee;
        }
        .label-dot {
          width: 6px;
          height: 6px;
          min-width: 6px;
          border-radius: 50%;
          display: block;
          background: #1c73e2;
          margin-right: 20px;
          margin-left: 1px;
        }
        .end {
          background: #ff8033;
        }
        .info-view {
          flex: 1;
          text-align: left;
          font-size: 15px;
          color: #747474;
        }
        .label {
          display: flex;
          align-items: center;
          margin-right: 10px;
          font-size: 15px;
          color: #747474;
        }
        .info-dot {
          margin-right: 15px;
        }
      }
      .border {
        border-bottom: 1px solid #eee;
      }
      .price-info {
        padding: 5px 0;
      }
    }
  }
}
</style>

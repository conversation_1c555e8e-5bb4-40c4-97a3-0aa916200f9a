<template>
  <div class="settingPage" v-if="driverInfo">
    <Navigation title="设置" />
    <div class="mine-pancel-content">
      <div class="item-pancel" @click="toRouter('/password')">
        <div class="label">修改密码</div>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </div>
      <div class="item-pancel">
        <div class="label">昵称</div>
        <p class="fz15 c9f">
          {{ driverInfo.nick_name != "" ? driverInfo.nick_name : "未设置" }}
        </p>
      </div>
      <div class="item-pancel">
        <div class="label">我的手机</div>
        <p class="fz15 c9f">{{ driverInfo.cellphone }}</p>
      </div>
      <div class="item-pancel" @click="toConfig">
        <div class="label">认证状态</div>
        <div class="label-pancel">
          <p style="color:#FF8A00" v-if="driverInfo.status == 0">已认证</p>
          <p style="color:#FFC100" v-if="driverInfo.status == 1">审核中</p>
          <p style="color:#ea1924" v-if="driverInfo.status == 2">
            审核未通过,请重新提交
          </p>
          <p style="color:#4A90E2" v-if="driverInfo.status == 3">需要认证</p>
          <svg-icon
            slot="icon"
            v-if="driverInfo.status == 2 || driverInfo.status == 3"
            class-name="right-dot"
            icon-class="click-right"
          />
        </div>
      </div>
      <a
        class="item-pancel"
        style="border-bottom:none;"
        href="https://c.higgses.com/agreement"
      >
        <div class="label">服务条款</div>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </a>
    </div>
    <cube-button :light="true" @click="handleLoginOut">退出登录</cube-button>
    <SystemView />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      redirect: undefined
    };
  },
  computed: {
    ...mapGetters(["driverInfo"])
  },
  components: {},
  mounted() {},
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toConfig() {
      if (this.driverInfo.status == 0 || this.driverInfo.status == 1) {
        return;
      }
      this.$router.push({ path: "/register" });
    },
    handleLoginOut() {
      this.$createDialog({
        type: "confirm",
        icon: "cubeic-warn",
        content: "你确定要退出登录？",
        confirmBtn: {
          text: "确定",
          active: true
        },
        cancelBtn: {
          text: "取消",
          active: false
        },
        onConfirm: () => {
          this.logout();
        }
      }).show();
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      await this.$store.dispatch("user/setAsyncRoutes", undefined);
      this.$router.push({ path: "/login" });

      let ua = navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        //ios的ua中无miniProgram，但都有MicroMessenger（表示是微信浏览器）
      } else {
        //其他浏览器
        var u = navigator.userAgent;
        var isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
        if (isAndroid) {
          //安卓
          window.cc_js.logout();
        }
        console.log("其他浏览器");
      }
    },
    tologin() {
      this.$router.push({ path: "/login" });
    },
    toRouter(route) {
      if (!this.driverInfo) {
        this.tologin();
      } else {
        this.$router.push({ path: route });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.settingPage {
  display: block;
  height: 100vh;
  background: #eeeeee;
  .remind-word {
    padding: 30px 15px;
    p {
      font-size: 14px;
      color: #b1b1b1;
      line-height: 23px;
      a {
        color: #1c73e2;
        font-size: 14px;
      }
    }
  }
  .mine-pancel-content {
    padding: 0 15px;
    display: block;
    margin: 10px 0;
    background: #fff;
    .item-pancel {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(228, 227, 227, 1);
      padding: 20px 0;
      .label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #4a4a4a;
        img {
          width: 17px;
          display: block;
          object-fit: contain;
          margin-right: 15px;
        }
      }
      .label-pancel {
        display: flex;
        align-items: center;
        .right-dot {
          margin-left: 5px;
        }
      }
      .right-dot {
        display: block;
      }
    }
    .money-item {
      h5 {
        font-size: 30px;
        color: #000000;
        text-align: left;
        display: block;
        margin-top: 15px;
      }
    }
  }
  .setting-top {
    img {
      width: 100%;
      object-fit: contain;
    }
    .view-logo {
      margin-top: -70px;
      img {
        width: 130px;
        object-fit: contain;
        display: block;
        margin: 0 auto;
      }
      h5 {
        font-size: 17px;
        font-weight: bold;
        color: #3b3b3b;
        text-align: center;
      }
    }
  }
}
</style>

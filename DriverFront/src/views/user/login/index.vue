<template>
  <div class="loginPage">
    <div class="login-top">
      <img src="@/assets/login-bg.png" alt="" />
      <div class="view-logo">
        <img src="@/assets/login.png" alt="" />
        <h5>欢迎登录</h5>
        <p v-if="config && config.mchname">商户名称：{{ config.mchname }}</p>
      </div>
    </div>
    <div class="login-form">
      <div class="item-form">
        <img class="login-dot" src="@/assets/phone.png" alt="" />
        <cube-input
          v-model="loginForm.cellphone"
          style="flex:1"
          type="tel"
          @blur="onBlur"
          pattern="\d*"
          :autofocus="autofocus"
          placeholder="请输入您的手机号"
          :clearable="clearable"
        ></cube-input>
      </div>
      <div class="item-form">
        <img class="login-dot" src="@/assets/password.png" alt="" />
        <cube-input
          v-model="loginForm.password"
          style="flex:1"
          type="password"
          :eye="eye"
          @blur="onBlur"
          placeholder="请输入您的密码"
          :clearable="clearable"
        ></cube-input>
      </div>
      <cube-button :primary="true" @click="doLogin">登录</cube-button>
    </div>
    <div class="click-btn">
      <div class="reset c9f fz14" @click="toReset">忘记密码？</div>
      <div class="cut-line" v-if="config && config.register_config == 1"></div>
      <div
        class="register"
        v-if="config && config.register_config == 1"
        @click="toRegister"
      >
        <p class="c9f fz14">现在注册</p>
        <svg-icon slot="icon" class-name="right-dot" icon-class="click-right" />
      </div>
    </div>
    <SystemView />
  </div>
</template>
<script>
import { getAds } from "@/api/api";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      loginForm: {
        cellphone: "",
        password: ""
      },
      clearable: {
        visible: true,
        blurHidden: true
      },
      autofocus: true,
      eye: {
        open: false,
        reverse: false
      },
      redirect: undefined
    };
  },
  components: {},
  computed: {
    ...mapGetters(["config", "driverInfo"])
  },
  mounted() {
    // this.getAdview();
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toRegister() {
      this.$router.push({ path: "/register" });
    },
    toReset() {
      this.$router.push({ path: "/reset" });
    },
    doLogin() {
      if (this.loginForm.cellphone === "") {
        this.toast = this.$createToast({
          txt: "请输入正确的手机号",
          type: "txt"
        });
        this.toast.show();
        return;
      }
      if (this.loginForm.password === "") {
        this.toast = this.$createToast({
          txt: "请输入密码",
          type: "txt"
        });
        this.toast.show();
        return;
      }
      const toast = this.$createToast({
        txt: "登录中..",
        time: 0
      });
      toast.show();
      this.$store.dispatch("user/login", this.loginForm).then(res => {
        toast.hide();
        if (res.ret) {
          let ua = navigator.userAgent.toLowerCase();
          if (ua.match(/MicroMessenger/i) == "micromessenger") {
            //ios的ua中无miniProgram，但都有MicroMessenger（表示是微信浏览器）
            window.location.href = res.data.wx_url;
          } else {
            //其他浏览器
            var u = navigator.userAgent;
            var isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
            if (isAndroid) {
              //安卓
              window.cc_js.login(res.data.driver_id);
            }
            this.$router.push({ path: "/" });
            console.log("其他浏览器");
          }
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    },
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    }
  }
};
</script>
<style lang="less" scoped>
.loginPage {
  .click-btn {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    .cut-line {
      width: 1px;
      height: 15px;
      background: rgba(159, 159, 159, 1);
      display: block;
      margin: 0 20px;
    }
    .register {
      display: flex;
      align-items: center;
      color: #9f9f9f;
      .right-dot {
        margin-left: 5px;
      }
    }
  }
  .login-form {
    padding: 20px 35px;
    .item-form {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e4e3e3;
      margin-bottom: 30px;
      .login-dot {
        width: 13px;
        display: block;
        object-fit: contain;
        min-width: 13px;
        margin-right: 15px;
      }
    }
  }
  .login-top {
    img {
      width: 100%;
      object-fit: contain;
    }
    .view-logo {
      margin-top: -70px;
      img {
        width: 130px;
        object-fit: contain;
        display: block;
        margin: 0 auto;
      }
      h5 {
        font-size: 17px;
        font-weight: bold;
        color: #3b3b3b;
        text-align: center;
      }
      p {
        text-align: center;
        color: #3b3b3b;
        font-size: 15px;
        margin-top: 10px;
      }
    }
  }
}
</style>

<template>
  <div class="amountPage">
    <Navigation  title="我的账户" />
    <div class="amount-content">
      <p>余额（元）</p>
      <p class="amount-view">{{driverInfo.balance}}</p>
    </div>
    <div class="amount-order">
        <cube-button :primary="true" @click="showFrom">提现</cube-button>
        <cube-button :light="true" @click="tolog" >提现记录</cube-button>
    </div>
    <div class="amount-step">
        <p class="fz16 fb">提现流程:</p>
        <p class="fz12 c4b">1.最低提现，大于10元。</p>
        <p class="fz12 c4b">2.填写提现金额并验证手机号码→申请成功,审核通过→耐心等待提现到账。</p>
        <p class="fz12 c4b">3.司机通过微信端发起的提现申请，预计1~5个工作日到账。</p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      toast:this.$createToast({
        txt: '努力加载中..',
        mask: false,
        time:0
      }),
      con:2
      }
  },
  computed:{
    ...mapGetters([
      'driverInfo'
    ]),
  },  
  methods: {
    showFrom(){
      this.$router.push({'path':'/withdrawal'})
    },
    tolog(){
       this.$router.push({'path':'/withdrawalhistory'})
    }
    
  },
  components: {
    
  },
  mounted: function() {

  }
};
</script>
<style lang="less" scoped>
.amountPage{
  .amount-step{
    padding: 20px;
    p{
      line-height: 25px;
    }
  }
  .amount-order{
    margin: 20px 0;
    padding: 0 20px;
      button{
        display: block;
        margin-bottom: 20px;
      }
  }
  .amount-content{
    padding: 20px;
    p{
      padding: 5px 0;
    }
    .amount-view{
      font-size: 32px;
      font-weight: bold;
      color: #000;
    }
  }
}
</style>

<template>
  <div class="message-box">
    <aside class="fix-top">
      <p class="fz14 c83" @click="backRoute">取消</p>
      <div class="el-down"></div>
      <p class="fz16 cma" @click="sureinfo">确认</p>
    </aside>
    <div class="message_write" style="padding-top:2.2em">
      <cube-textarea
        v-fixedInput
        :autofocus="autofocus"
        @input="changeword"
        v-model="message"
        placeholder="乘车备注，比如禁止带宠物"
        :maxlength="100"
      ></cube-textarea>
    </div>
    <div class="normal_word">
      <span @click="addme(item)" v-for="(item, index) in normalword" :key="index">{{ item }}</span>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      autofocus: true,
      normalword: [" 过路费平摊 ", " 禁带宠物 ", " 本地司机 ", " 走高速 "]
    };
  },
  computed: {
    // ...mapGetters(["message"]),
    message: {
      get() {
        return this.$store.getters.message;
      },
      set(val) {}
    }
  },
  methods: {
    backRoute() {
      this.$router.back(-1);
    },
    changeword(e) {
      this.$store.dispatch("user/changeMessage", e);
    },
    addme(e) {
      let newinfo = (this.message += e);
      this.$store.dispatch("user/changeMessage", newinfo);
    },
    sureinfo() {
      this.$router.back(-1);
    }
  }
};
</script>
<style lang="less" scoped>
.message-box {
  background: #f9f9f9;
  min-height: 100vh;
  .fix-top {
    width: 100%;
    height: 2.6em;
    font-size: 1.6em;
    text-align: center;
    line-height: 2.6em;
    background-color: #fff;
    color: #949494;
    box-shadow: 0 0 3px rgba(155, 155, 155, 0.9);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
      margin: 0 10px;
    }
  }
}
.message_write {
  padding: 20px;
  font-size: 30px;
}
.normal_word {
  padding: 0 20px 20px 20px;
  span {
    display: inline-block;
    margin: 5px 10px 5px 0;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 15px;
    color: #676767;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}
.btn_comfire {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  width: 100%;
  bottom: 30px;
  left: 0;
  .suremessage {
    background-color: #1c73e2;
    color: #fff;
    flex: 2;
    margin: 0 20px;
  }
  .cancel {
    flex: 1;
    margin: 0 20px;
  }
}
</style>

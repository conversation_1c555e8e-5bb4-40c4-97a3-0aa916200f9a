<template>
  <div class="resetPage">
    <div class="register-top">
      <img src="@/assets/login-bg.png" alt="" />
      <div class="back_btn" @click="backRoute">
        <svg-icon slot="icon" class-name="left-dot" icon-class="bgleft" />
      </div>
      <div class="top-content">
        <!-- <h5>注册</h5> -->
        <div class="register-form">
          <div class="item-form ">
            <cube-input
              v-model="registerForm.cellphone"
              style="flex:1"
              type="tel"
              pattern="\d*"
              @blur="onBlur"
              placeholder="请输入手机号"
              :clearable="clearable"
            ></cube-input>
          </div>
          <div class="item-form ">
            <cube-input
              v-model="registerForm.cellphone_validate_code"
              type="tel"
              pattern="\d*"
              @blur="onBlur"
              placeholder="请输入验证码"
            ></cube-input>
            <cube-button
              :primary="true"
              :inline="true"
              :disabled="sendMsgDisabled"
              @click="getCode"
              >{{
                sendMsgDisabled ? time + "秒后获取" : "获取验证码"
              }}</cube-button
            >
          </div>
          <div class="item-form">
            <cube-input
              v-model="registerForm.password"
              style="flex:1"
              type="password"
              @blur="onBlur"
              :eye="eye"
              placeholder="重新设置登录密码"
              :clearable="clearable"
            ></cube-input>
          </div>
          <cube-button
            :primary="true"
            style="margin-top:10px;"
            :disabled="canSubmit"
            @click="doRegister"
            >确认</cube-button
          >
        </div>
      </div>
    </div>
    <SystemView />
  </div>
</template>
<script>
import { getAds } from "@/api/api";
import { doGetCode, doForgetPassword } from "@/api/api";

export default {
  data() {
    return {
      registerForm: {
        cellphone: undefined,
        password: undefined,
        cellphone_validate_code: undefined
      },
      clearable: {
        visible: true,
        blurHidden: true
      },
      autofocus: true,
      eye: {
        open: false,
        reverse: false
      },
      redirect: undefined,
      sendMsgDisabled: false,
      time: 60
    };
  },
  components: {},
  mounted() {
    // this.getAdview();
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
      deep: true
    },
    registerForm(newV) {
      if (newV.cellphone_validate_code.length > 6) {
        newV.cellphone_validate_code = newV.cellphone_validate_code.slice(0, 6);
        this.$nextTick(() => {
          this.registerForm = newV;
        });
      }
    }
  },
  computed: {
    canSubmit() {
      if (
        this.registerForm.cellphone &&
        this.registerForm.password &&
        this.registerForm.cellphone_validate_code
      ) {
        return false;
      } else {
        return true;
      }
    }
  },
  methods: {
    backRoute() {
      this.$router.push({ path: "/login" });
    },
    sendVerifyCode() {
      let that = this;
      let query = {
        cellphone: this.registerForm.cellphone
      };
      doGetCode(query).then(res => {
        if (res.ret) {
          this.toast = this.$createToast({
            txt: "发送成功",
            type: "txt"
          });
          this.toast.show();
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    },
    getCode() {
      let that = this;
      if (!that.registerForm.cellphone) {
        that.toast = that.$createToast({
          txt: "请先输入手机号码",
          type: "txt"
        });
        that.toast.show();
        return;
      }
      that.sendVerifyCode();
      that.sendMsgDisabled = true;
      let interval = setInterval(() => {
        if (that.time-- <= 0) {
          that.time = 60;
          that.sendMsgDisabled = false;
          clearInterval(interval);
        }
      }, 1000);
    },
    doRegister() {
      const toast = this.$createToast({
        txt: "操作中..",
        time: 0
      });
      toast.show();
      doForgetPassword(this.registerForm).then(res => {
        toast.hide();
        if (res.ret) {
          this.$createDialog({
            type: "alert",
            icon: "cubeic-alert",
            showClose: false,
            content: "重置密码成功",
            confirmBtn: {
              text: "去登录",
              active: true
            },
            onConfirm: () => {
              this.$router.push({ path: "/login" });
            }
          }).show();
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    },
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    }
  }
};
</script>
<style lang="less" scoped>
.resetPage {
  display: block;
  padding-bottom: 85px;
  .click-btn {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    .cut-line {
      width: 1px;
      height: 15px;
      background: rgba(159, 159, 159, 1);
      display: block;
      margin: 0 20px;
    }
    .register {
      display: flex;
      align-items: center;
      color: #9f9f9f;
      .right-dot {
        margin-left: 5px;
      }
    }
  }
  .register-top {
    .back_btn {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: fixed;
      top: 15px;
      left: 0;
      z-index: 1;
      .left-dot {
        width: 20px;
        height: 20px;
        display: block;
      }
    }
    img {
      width: 100%;
      object-fit: contain;
    }
    .top-content {
      margin: 0 10px;
      margin-top: -100px;
      display: block;
      position: relative;
      z-index: 1;
      h5 {
        font-size: 19px;
        color: #fff;
        text-align: center;
        margin-bottom: 40px;
      }
      .register-form {
        padding: 15px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 12px 1px rgba(28, 115, 226, 0.16);
        border-radius: 4px;
        margin-bottom: 20px;
        .item-form {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 0;
          .item-label {
            width: 70px;
            min-width: 70px;
            font-size: 15px;
            color: #636363;
          }
        }
        .upload-form {
          display: block;
          .upload-title {
            display: flex;
            padding: 10px 0;
            color: #2e2e2e;
            font-weight: bold;
            font-size: 14px;
          }
          .upload-title::before {
            width: 2px;
            display: block;
            content: "";
            height: 15px;
            background: #1c73e2;
            margin-right: 10px;
          }
        }
        .border {
          border-bottom: 1px solid #eee;
        }
      }
    }
  }
}
.cube-upload {
  .cube-upload-file,
  .cube-upload-btn {
    margin: 0;
    height: 200px;
  }
  .cube-upload-file {
    margin: 0;
    + .cube-upload-btn {
      margin-top: -200px;
      opacity: 0;
    }
  }
  .cubeic-wrong {
    display: none;
  }
  .cube-upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      text-align: center;
    }
    i {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin-bottom: 20px;
      font-size: 32px;
      line-height: 1;
      font-style: normal;
      color: #fff;
      background-color: rgba(28, 115, 226);
      border-radius: 50%;
    }
  }
}
</style>

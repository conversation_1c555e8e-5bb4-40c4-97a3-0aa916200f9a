<template>
  <div class="withdrawalPage">
    <Navigation title="余额提现" />
    <div class="drawal-content">
      <div class="form-top border">
        <div class="item-drawal money-write">
          <div class="money-label">￥</div>
          <cube-input
            v-model="value"
            style="flex:1"
            type="tel"
            pattern="\d*"
            :autofocus="true"
            :clearable="clearable"
            @blur="onBlur"
          ></cube-input>
        </div>
        <div class="money c74 fz14">
          余额￥{{ driverInfo.balance }}，
          <p class="fz14 " @click="wirteAll">全部提现</p>
        </div>
      </div>
      <div class="item-drawal border" v-if="acciptvalue != ''">
        <div class="label">到账金额：</div>
        <p class="view-line">{{ acciptvalue }}元</p>
      </div>
      <div class="item-drawal border">
        <div class="label">预留手机号：</div>
        <p class="view-line">{{ driverInfo.cellphone }}</p>
      </div>
      <div class="item-drawal">
        <div class="label">验证码：</div>
        <cube-input
          v-model="Validatorvalue"
          type="tel"
          pattern="\d*"
          placeholder="请输入验证码"
          @blur="onBlur"
        ></cube-input>
        <cube-button
          :primary="true"
          :inline="true"
          :disabled="sendMsgDisabled"
          @click="getCode"
          >{{ sendMsgDisabled ? time + "秒后获取" : "获取验证码" }}</cube-button
        >
      </div>
    </div>
    <div class="withdrawal-order">
      <cube-button
        :primary="true"
        @click="handleWithdrawal"
        :disabled="value == ''"
        >提现</cube-button
      >
      <cube-button @click="backRoute" :light="true">取消</cube-button>
    </div>
    <div class="withdrawal-step">
      <p class="fz12 c4b">1.最低提现，大于10元。</p>
      <p class="fz12 c4b">
        2.提现微信平台会扣除{{
          config.split
        }}%的手续费，实际到账为扣除手续费之后金额。审核通过后，将转入本微信账号零钱中。
      </p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { doGetCode, doWithdrawal } from "@/api/api";
import store from "@/store";
export default {
  data() {
    return {
      toast: this.$createToast({
        txt: "努力加载中..",
        mask: false,
        time: 0
      }),
      value: "",
      Validatorvalue: "",
      clearable: {
        visible: true,
        blurHidden: true
      },
      sendMsgDisabled: false,
      time: 60
    };
  },
  computed: {
    ...mapGetters(["config", "driverInfo"]),
    acciptvalue() {
      if (this.value != "") {
        return (Number(this.value) * (1 - this.config.split / 100)).toFixed(2);
      } else {
        return "";
      }
    }
  },
  watch: {
    Validatorvalue(newV) {
      if (newV.length > 6) {
        newV = newV.slice(0, 6);
        this.$nextTick(() => {
          this.acciptvalue = newV;
        });
      }
    }
  },
  methods: {
    backRoute() {
      this.$router.go(-1);
    },
    onBlur() {
      setTimeout(() => {
        const scrollHeight =
          document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight - 1, 0));
      }, 100);
    },
    getCode() {
      this.sendVerifyCode();
      let that = this;
      that.sendMsgDisabled = true;
      let interval = setInterval(() => {
        if (that.time-- <= 0) {
          that.time = 60;
          that.sendMsgDisabled = false;
          clearInterval(interval);
        }
      }, 1000);
    },
    wirteAll() {
      if (this.driverInfo.balance == "0.00") {
        return;
      }
      this.value = this.driverInfo.balance;
    },
    sendVerifyCode() {
      let that = this;
      let query = {
        cellphone: this.driverInfo.cellphone
      };
      doGetCode(query).then(res => {
        if (res.ret) {
          this.toast = this.$createToast({
            txt: "发送成功",
            type: "txt"
          });
          this.toast.show();
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    },
    getNowFormatDate(d) {
      var date = d == undefined ? new Date() : new Date(d);
      var seperator1 = "-";
      var seperator2 = ":";
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      var currentdate =
        date.getFullYear() +
        seperator1 +
        month +
        seperator1 +
        strDate +
        " " +
        date.getHours() +
        seperator2 +
        date.getMinutes() +
        seperator2 +
        date.getSeconds();
      return currentdate;
    },
    handleWithdrawal() {
      if (this.Validatorvalue == "") {
        this.toast = this.$createToast({
          txt: "请输入验证码",
          type: "txt"
        });
        this.toast.show();
        return;
      }
      let query = {
        money: this.value,
        cellphone_validate_code: this.Validatorvalue,
        driver_cellphone: this.driverInfo.cellphone,
        time: this.getNowFormatDate()
      };
      doWithdrawal(query).then(res => {
        if (res.ret) {
          store.dispatch("user/getInfo");
          this.$createDialog({
            type: "alert",
            icon: "cubeic-alert",
            showClose: false,
            title: "申请成功",
            content: "预计1~5个工作日到账",
            confirmBtn: {
              text: "我知道了",
              active: true
            },
            onConfirm: () => {
              this.$router.back(-1);
            }
          }).show();
        } else {
          this.toast = this.$createToast({
            txt: res.data,
            type: "txt"
          });
          this.toast.show();
        }
      });
    }
  },
  components: {},
  mounted: function() {}
};
</script>
<style lang="less" scoped>
.withdrawalPage {
  display: block;
  .withdrawal-step {
    padding: 20px;
    p {
      line-height: 20px;
      color: #f56c6c;
    }
  }
  .withdrawal-order {
    padding: 0 10px;
    button {
      display: block;
      margin-bottom: 20px;
    }
  }
  .money {
    display: flex;
    align-content: center;
    margin-bottom: 15px;
    p {
      margin-left: 10px;
      color: rgb(121, 143, 173);
    }
  }
  .drawal-content {
    margin: 20px 10px;
    display: block;
    padding: 0 20px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    .item-drawal {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      .label {
        width: 100px;
        margin-right: 10px;
        font-size: 14px;
      }
      .money-label {
        font-size: 22px;
        color: #2e2e2e;
        font-weight: bold;
      }
      .view-line {
        padding: 10px 0;
        text-align: left;
        font-size: 17px;
        flex: 1;
      }
    }
    .form-top {
      padding: 0 0 15px 0;
    }
    .border {
      border-bottom: 1px solid #efefef;
    }
  }
}
</style>

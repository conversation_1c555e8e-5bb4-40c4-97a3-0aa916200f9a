<template>
  <div
    class="scroll-view-content" >
    <cube-scroll
      v-if="listdata.length > 0"
      ref="scroll"
      :data="listdata"
      :options="options"
      @pulling-down="refreshlist"
      @pulling-up="onPullingUp"
    >
      <div class="order-content">
        <div class="item-order" v-for="(item, index) in listdata" :key="index">
          <div class="order-title">
                <div class="type-info">
                  <p class="order-type" >{{ item.deposit_no}}</p>
                 </div>
            <div class="order-state">
              <p class="fz14" v-if="item.status==3" style="color: #e84e5d">{{item.status_msg}}</p>
              <p class="fz14" v-else-if="item.status==2" style="color: #5ab96f">{{item.status_msg}}</p>
              <p class="fz14" v-else style="color: #ffab68">{{item.status_msg}}</p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <p class="label">申请时间：</p>
              <p class="fz15 c74 oneline">{{ show_time(item.create_time) }}</p>
            </div>
            <div class="order-pancel">
              <p class="label">提现金额：</p>
              <p class="fz15 c74 oneline">{{ item.moneys }}</p>
            </div>
            <div class="order-pancel">
              <p class="label">实际到账：</p>
              <p class="fz15 c74 oneline">{{ item.real_amount }}</p>
            </div>
            <div class="order-pancel" v-if="item.error_msg">
              <p class="fz12" style="color: #e84e5d">{{ item.error_msg }}</p>
            </div>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="暂无记录" v-if="total == 0"></Empty>
  </div>
</template>
<script>
import { WithdrawalHistory } from "@/api/api";
import { exchange_time } from "@/utils/utils";
export default {
  data() {
    return {
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      fullHeight: document.body.offsetHeight,
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      total: 0,
      listdata: [] // 数据存放数组
    };
  },
  mounted() {
    this.getList("up");
  },
  methods: {
    show_time(time) {
      return exchange_time(time);
    },
    getList(type) {
      let that = this;
      const toast = this.$createToast({
             txt: "努力加载中..",
             time: 0
          })
          toast.show();
      WithdrawalHistory(that.queryValue).then(response => {
        toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.listdata = that.listdata.concat(response.data);
          } else {
            that.listdata = response.data;
            this.$refs.scroll.resetPullUpTxt();
          }
          that.total = response.count;
        } else {
          if (type == "down") {
             this.listdata=[];
             this.total=0;
          }
          this.$refs.scroll.forceUpdate();
          this.queryValue.page--;
        }
      });
    },
    //下拉刷新
    refreshlist() {
      this.queryValue = {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      };
      this.$refs.scroll.refresh();
      this.getList("down");
    },
    //加载更多
    onPullingUp() {
      this.queryValue.page++;
      this.getList("up");
    },
  }
};
</script>
<style scoped lang="less">
.scroll-view-content {
  display: block;
  position: absolute;
  top: 55px;
  left: 0;
  bottom: 0;
  width: 100%;
  .order-content {
    background-color: #fff;
    padding: 0 10px;
    .item-order {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05),
        0 1px 4px rgba(0, 0, 0, 0.05);
      display: block;
      padding: 0 10px 0 10px;
      border-radius: 5px;
      margin: 15px auto;
      .order-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        .type-info{
            display: flex;
            align-items: center;
            .order-type{
                font-size: 13px;
                color: #9F9F9F;
                margin-right: 10px;
            }
            .paytype{
                padding: 3px 5px;
                font-size: 13px;
                border: 1px solid #eee;
            }
        }        
      }
      .order-middle {
        padding: 10px 0;
        .order-pancel {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-bottom: 5px;
          .label{
            font-size: 14px;
            color: #838383;
          }
          p {
            line-height: 15px;
          }
        }
      }
      .control-order {
        display: flex;
        justify-content: space-between;
        align-items: center;
        button {
          margin: 0 5px;
        }
      }
    }
  }
}
</style>

<template>
  <div class="scroll-view-content">
    <cube-scroll
      v-if="listdata.length > 0"
      ref="scroll"
      :data="listdata"
      :options="options"
      @pulling-down="refreshlist"
      @pulling-up="onPullingUp"
    >
      <div class="order-content">
        <div class="item-order" v-for="(item, index) in listdata" :key="index">
          <div class="order-title">
            <div class="type-info">
              <p class="order-type" v-if="item.type == 1">拼车</p>
              <p class="order-type" v-else-if="item.type == 2">包车</p>
              <p class="order-type" v-else-if="item.type == 3">带货</p>
              <p class="order-type" v-else-if="item.type == 4">代办</p>
              <p class="order-type" v-else-if="item.type == 5">定制客运</p>
              <p class="order-type" v-else-if="item.type == 6">顺风车</p>
              <p class="order-type" v-else-if="item.type == 7">快车</p>
              <p class="order-type" v-else-if="item.type == 8">学生号</p>
              <p class="order-type" v-else-if="item.type == 10">定制接送</p>
              <p class="order-type" v-else-if="item.type == 11">出租车</p>
              <p class="order-type" v-else>其他</p>
              <p class="paytype" v-if="item.is_pre_pay == 1">预付</p>
            </div>
            <div class="order-state">
              <p class="fz15 c83" v-if="item.state == 6">已完成</p>
              <p class="fz15 c83" v-else-if="item.state == 7">已取消</p>
              <p class="fz15 c83" v-else>进行中</p>
            </div>
          </div>
          <div class="order-middle">
            <div class="order-pancel">
              <svg-icon slot="icon" class-name="time-dot" icon-class="time" />
              <p class="fz15 c63 oneline">{{ show_time(item.start_time) }}</p>
            </div>
            <div class="line-content" v-if="item.type != 4">
              <div
                class="order-pancel"
                v-if="item.type != 4 && item.type != 10"
              >
                <svg-icon
                  slot="icon"
                  class-name="people-dot"
                  icon-class="people"
                />
                <p class="fz15 c2e">{{ item.book_seating }} 人</p>
              </div>
              <div class="order-pancel" v-if="item.type == 10">
                <svg-icon
                  slot="icon"
                  class-name="people-dot"
                  icon-class="people"
                />
                <p class="fz15 c2e">{{ item.passenger_name }}</p>
              </div>
              <div class="order-pancel">
                <div class="s-dot"></div>
                <p class="fz15 c2e oneline">{{ item.start_address_remark }}</p>
              </div>
              <div class="order-pancel">
                <div class="e-dot"></div>
                <p class="fz15 c2e oneline">{{ item.end_address_remark }}</p>
              </div>
            </div>
            <div class="things-content" v-else>
              <div class="order-pancel">
                <svg-icon
                  slot="icon"
                  class-name="type-dot"
                  icon-class="things"
                />
                <p class="fz15 c2e oneline">{{ item.name }}</p>
              </div>
              <div class="order-pancel">
                <svg-icon
                  slot="icon"
                  class-name="type-dot"
                  icon-class="about"
                />
                <p class="fz15 c2e oneline">{{ item.summary }}</p>
              </div>
            </div>
            <div class="things-content" v-if="item.type == 3">
              <div class="order-pancel">
                <svg-icon
                  slot="icon"
                  class-name="type-dot"
                  icon-class="weight"
                />
                <p class="fz15 c2e">{{ item.weight }} kg</p>
              </div>
            </div>
            <div class="order-pancel" v-if="item.type != 10">
              <svg-icon slot="icon" class-name="time-dot" icon-class="price" />
              <p class="fz16 cff fb">
                {{ item.price }}
                <span class="fz12 c83">元</span>
              </p>
            </div>
            <div class="order-pancel" v-else>
              <svg-icon slot="icon" class-name="time-dot" icon-class="price" />
              <p class="fz16 cff fb">月卡支付</p>
            </div>
          </div>
        </div>
      </div>
    </cube-scroll>
    <Empty textword="暂无订单" v-if="total == 0"></Empty>
  </div>
</template>
<script>
import { doGetHistoryList } from "@/api/api";
import { exchange_time } from "@/utils/utils";
export default {
  data() {
    return {
      options: {
        pullDownRefresh: {
          threshold: 60,
          txt: "更新成功"
        },
        pullUpLoad: {
          threshold: 0,
          stop: 20,
          visible: true,
          txt: {
            more: "加载更多",
            noMore: "已全部加载完成"
          }
        }
      },
      fullHeight: document.body.offsetHeight,
      toast: this.$createToast({
        txt: "努力加载中..",
        time: 0
      }),
      queryValue: {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      },
      total: 0,
      listdata: [] // 数据存放数组
    };
  },
  methods: {
    changequery(parms) {
      this.queryValue = parms;
      this.getList("down");
    },
    show_time(time) {
      return exchange_time(time);
    },
    getList(type) {
      let that = this;
      const toast = this.$createToast({
        txt: "努力加载中..",
        time: 0
      });
      toast.show();
      doGetHistoryList(that.queryValue).then(response => {
        toast.hide();
        if (response.ret) {
          if (type == "up") {
            that.listdata = that.listdata.concat(response.data);
          } else {
            that.listdata = response.data;
            if (this.$refs.scroll) {
              this.$refs.scroll.resetPullUpTxt();
            }
          }
          that.total = response.count;
        } else {
          if (type == "down") {
            this.listdata = [];
            this.total = 0;
          }
          if (this.$refs.scroll) {
            this.$refs.scroll.forceUpdate();
          }
          this.queryValue.page--;
        }
      });
    },
    //下拉刷新
    refreshlist() {
      this.queryValue = {
        page: 1, // 开始页数
        size: 20 // 一次显示多少条
      };
      if (this.$refs.scroll) {
        this.$refs.scroll.refresh();
      }
      this.getList("down");
    },
    //加载更多
    onPullingUp() {
      this.queryValue.page++;
      this.getList("up");
    }
  }
};
</script>
<style scoped lang="less">
.scroll-view-content {
  display: block;
  position: absolute;
  top: 55px;
  left: 0;
  bottom: 0;
  width: 100%;
  .order-content {
    background-color: #fff;
    padding: 0 10px;
    .item-order {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
        0 1px 4px rgba(0, 0, 0, 0.117647);
      display: block;
      padding: 0 10px 0 10px;
      border-radius: 5px;
      margin: 15px auto;
      .order-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        .type-info {
          display: flex;
          align-items: center;
          .order-type {
            font-size: 14px;
            font-weight: bold;
            color: #474747;
            margin-right: 10px;
          }
          .paytype {
            padding: 3px 5px;
            font-size: 13px;
            border: 1px solid #eee;
          }
        }
        .order-price {
          display: flex;
          align-items: center;
          .price-dot {
            margin-right: 5px;
          }
        }
      }
      .order-middle {
        padding: 10px 0;
        .order-pancel {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-bottom: 5px;
          .s-dot,
          .e-dot {
            width: 6px;
            height: 6px;
            min-width: 6px;
            border-radius: 50%;
            background: #1c73e2;
            margin-right: 15px;
            margin-left: 2px;
          }
          .e-dot {
            background: #fc9153;
          }
          p {
            line-height: 18px;
          }
          .time-dot {
            width: 8px;
            height: 8px;
            margin-right: 15px;
          }
          .people-dot {
            width: 8px;
            height: 8px;
            margin-right: 15px;
          }
          .type-dot {
            width: 10px;
            height: 10px;
            margin-right: 13px;
          }
        }
      }
      .control-order {
        display: flex;
        justify-content: space-between;
        align-items: center;
        button {
          margin: 0 5px;
        }
      }
    }
  }
}
</style>

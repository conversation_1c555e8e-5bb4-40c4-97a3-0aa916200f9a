<template>
  <div class="history-box">
    <Navigation title="历史订单"/>
    <history-list ref="hlist"></history-list>
  </div>
</template>

<script>
import Hlist from "./components/HistoryList";
export default {
  data() {
    return {};
  },
  activated() {
    const queryValue = {
      page: 1, // 开始页数
      size: 20, // 一次显示多少条
    };
    console.log(11);
    this.$refs.hlist.changequery(queryValue);
  },
  methods: {},
  components: {
    "history-list": Hlist,
  },
  mounted: function() {},
};
</script>

<style scoped lang="less">
.history-box {
  display: block;
  width: 100vw;
  bottom: 100vh;
}
</style>

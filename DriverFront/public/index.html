<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="yes" name="apple-mobile-web-app-capable"> 
    <meta name="viewport" content="width=device-width,height=device-height,inital-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover;">
    <meta name="apple-mobile-web-app-title" content="CC招车系统官网—城际拼车、包车、顺风车（网约车）软件管理系统">
    <meta name="application-name" content="CC招车城际管理系统">
    <meta name="theme-color" content="#ffffff">
    <meta name="keywords" content="CC招车,CC用车,CC约车,网约车,cczhaoche,租车软件,租车APP开发,租车管理系统,专线调度软件,城际专线软件,货运APP软件开发,货运软件,跑腿软件开发,跑腿APP开发,速递软件开发,专车软件,专车软件制作,网约车软件,网约车软件开发,网约车APP,代驾管理系统,代驾APP开发,代驾系统开发,代驾系统定制,代驾险购买,平安代驾险,代驾管理软件,代驾软件开发,微信代驾软件,招车,拼车,管理系统,预定,微信,平台,司机端,乘客端,城际SAAS,拼车软件系统,微信叫车软件系统,微信打车软件系统,打车app开发,网约车平台开发,打车软件开发公司,出租车专车拼车顺风车班线车软件开发">
    <meta name="description" content="CC招车系统,为您快速搭建出行平台,接入只需3步!专注提供城际路线(拼车/包车/班线车/顺风车/带货/代办)的叫车、预约、接派单（网约车）软件管理系统,销售热线:028-61112106。企业级定制网约车全套解决方案,让城际出行更方便!">
    <meta itemprop="name" content="CC招车系统软件,拼车,包车,顺风车,班线车,官方网站"/>
    <link rel="shortcut icon" href=favicon.ico type=image/x-icon/ >
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,400italic">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <title>cc招车</title>
    <!-- <script>!function(e){function t(a){if(i[a])return i[a].exports;var n=i[a]={exports:{},id:a,loaded:!1};return e[a].call(n.exports,n,n.exports,t),n.loaded=!0,n.exports}var i={};return t.m=e,t.c=i,t.p="",t(0)}([function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=window;t["default"]=i.flex=function(normal,e,t){var a=e||100,n=t||1,r=i.document,o=navigator.userAgent,d=o.match(/Android[\S\s]+AppleWebkit\/(\d{3})/i),l=o.match(/U3\/((\d+|\.){5,})/i),c=l&&parseInt(l[1].split(".").join(""),10)>=80,p=navigator.appVersion.match(/(iphone|ipad|ipod)/gi),s=i.devicePixelRatio||1;p||d&&d[1]>534||c||(s=1);var u=normal?1:1/s,m=r.querySelector('meta[name="viewport"]');m||(m=r.createElement("meta"),m.setAttribute("name","viewport"),r.head.appendChild(m)),m.setAttribute("content","width=device-width,user-scalable=no,initial-scale="+u+",maximum-scale="+u+",minimum-scale="+u),r.documentElement.style.fontSize=normal?"50px": a/2*s*n+"px"},e.exports=t["default"]}]);  flex(false,100, 1);</script> -->

  </head>
  <body>
    <div id="app"></div>
    <script type="text/javascript">
      //如下代码主要是解决, 打包后的app点击返回键直接退出的bug.
      /* 自己写的 toast 提示框 */
      //下面用的是调的 Android 自身的 toast方法。 我把我自己写的toast 注释掉 
      /* 
      let flag = false;
      function toast(tip){
          if(flag) return;
          let oDiv  = document.createElement('div');
          let oBody = document.getElementsByTagName('body')[0];
          oDiv.innerText = tip;
          oDiv.style.background = 'rgba(50, 50, 51, 0.88)';
          oDiv.style.color = '#fff';
          oDiv.style.textAlign = 'center';
          oDiv.style.fontSize = '14px';
          oDiv.style.lineHeight = '30px';
          oDiv.style.width = '200px';
          oDiv.style.borderRadius = '4px';
          oDiv.style.position = 'fixed';
          oDiv.style.left = '50%';
          oDiv.style.transform = 'translateX(-50%)';
          oDiv.style.bottom = '100px';
          oDiv.style.zIndex = '1000';
          oBody.appendChild(oDiv);
          flag = true;
          setTimeout(function(){
              oBody.removeChild(oDiv);
              flag = false;
          },1000);
      }
       */
      /* 自己写的 toast 提示框---END--- */
      
      document.addEventListener('plusready', function(a) { //等待plus ready后再调用5+ API：
                  //// 在这里调用5+ API
                  var first = null;
                  plus.key.addEventListener('backbutton', function() { //监听返回键
                          //首次按键，提示‘再按一次退出应用’
                          if (!first) {
                              first = new Date().getTime(); //获取第一次点击的时间戳
                              // console.log('再按一次退出应用');//用自定义toast提示最好
                              // toast('双击返回键退出应用'); //调用自己写的吐丝提示 函数
                              plus.nativeUI.toast("双击退出", {duration:'short'}); //通过H5+ API 调用Android 上的toast 提示框
                              setTimeout(function() {
                                  first = null;
                              }, 1000);
                          } else {
                              if (new Date().getTime() - first < 1000) { //获取第二次点击的时间戳, 两次之差 小于 1000ms 说明1s点击了两次,
                                  plus.runtime.quit(); //退出应用
                              }
                          }
                      }, false);
              });
  </script>
    <!-- built files will be auto injected -->
  </body>
</html>


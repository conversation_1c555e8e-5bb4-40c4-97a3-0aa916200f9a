{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:pre-release": "vue-cli-service build --mode pre-release", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@wcjiang/notify": "^2.1.0", "axios": "0.18.1", "core-js": "^3.6.5", "downloadjs": "^1.4.7", "echarts": "^5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.1", "js-cookie": "2.2.0", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "regenerate": "^1.4.2", "vue": "2.7.10", "vue-pdf": "^4.3.0", "vue-router": "v3.5.4", "vuex": "3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.26.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^7.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "title-notify": "^1.1.1", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}
import logging
import os
import shutil
import subprocess
import uuid
from logging.handlers import RotatingFileHandler
from pydantic import BaseModel
import oss2
import requests
from fastapi import FastAPI, HTTPException, Body
from fastapi.responses import J<PERSON>NResponse
from oss2.credentials import EnvironmentVariableCredentialsProvider, Credentials

app = FastAPI()
file_handler = RotatingFileHandler(
    filename="app.log", maxBytes=5000000, backupCount=10  # 5MB轮转，保留10个备份
)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
file_handler.setFormatter(formatter)
logger = logging.getLogger()
logger.addHandler(file_handler)
logger.setLevel(logging.INFO)

# 临时文件存储目录
TEMP_DIR = "temp_files"
os.makedirs(TEMP_DIR, exist_ok=True)

OSS_ACCESS_KEY = os.getenv("OSS_ACCESS_KEY")
if not OSS_ACCESS_KEY:
    raise Exception("请设置环境变量 OSS_ACCESS_KEY")

OSS_SECRET_KEY = os.getenv("OSS_SECRET_KEY")
if not OSS_SECRET_KEY:
    raise Exception("请设置环境变量 OSS_SECRET_KEY")

# oss访问端点
OSS_END_POINT = os.getenv("OSS_END_POINT")
if not OSS_END_POINT:
    raise Exception("请设置环境变量 OSS_END_POINT")

# oss访问地址
OSS_HOST = os.getenv("OSS_HOST")
if not OSS_HOST:
    raise Exception("请设置环境变量 OSS_HOST")

OSS_REGION = os.getenv("OSS_REGION")
if not OSS_REGION:
    raise Exception("请设置环境变量 OSS_REGION")

OSS_BUCKET_NAME = os.getenv("OSS_BUCKET_NAME")
if not OSS_BUCKET_NAME:
    raise Exception("请设置环境变量 OSS_BUCKET_NAME")

OSS_OBJECT_PATH = os.getenv("OSS_OBJECT_PATH")
if not OSS_OBJECT_PATH:
    raise Exception("请设置环境变量 OSS_OBJECT_PATH")


class SpeexFile(BaseModel):
    speexFileUrl: str


@app.post("/api/speex2wav")
async def speex2wav(speexFile: SpeexFile = Body(...)):
    #根据 url 地址，下载speex文件到临时文件夹
    speexFileUrl = speexFile.speexFileUrl
    if not speexFileUrl.startswith("http"):
        speexFileUrl = f"{OSS_HOST}/{speexFileUrl}"
    logger.info(f"开始下载源文件: {speexFileUrl}")
    response = requests.get(speexFileUrl, stream=True)
    if response.status_code != 200:
        raise HTTPException(404, "URL资源不可用")
    # 生成唯一文件名,去掉-
    file_id = str(uuid.uuid4()).replace("-", "")
    speex_path = os.path.join(TEMP_DIR, f"{file_id}.speex")
    wav_path = os.path.join(TEMP_DIR, f"{file_id}.wav")
    # 保存上传文件
    with open(speex_path, "wb") as buffer:
        shutil.copyfileobj(response.raw, buffer)  # 流式复制
    # 转换操作
    if not convert_speex_to_wav(speex_path, wav_path):
        os.remove(speex_path)
        raise HTTPException(status_code=500, detail="音频转换失败")
    else:
        # 清理临时文件
        os.remove(speex_path)
    responses = upload_to_aliyun_oss(wav_path)
    return responses


"""上传文件到阿里云OSS"""


def upload_to_aliyun_oss(wav_path: str) -> JSONResponse:
    # 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
    auth = oss2.ProviderAuthV4(CredentialsProvider(OSS_ACCESS_KEY, OSS_SECRET_KEY))
    bucket = oss2.Bucket(auth, OSS_END_POINT, OSS_BUCKET_NAME, region=OSS_REGION)
    file_name = wav_path.split("/")[-1]
    logger.info(f"开始上传到oss: {file_name}")
    oss_path = f"{OSS_OBJECT_PATH}{file_name}"
    result = bucket.put_object_from_file(oss_path, wav_path)
    if result.status == 200:
        logger.info(f"成功上传到oss: {file_name}")
        # 删除临时文件
        os.remove(wav_path)
        return JSONResponse(status_code=result.status, content={"oss_path": f"{OSS_HOST}/{oss_path}"})
    else:
        logger.error(f"oss上传失败: {file_name},code:{result.status}")
        # 错误码说明:https://help.aliyun.com/zh/oss/developer-reference/putobject?spm=a2c4g.11186623.help-menu-31815.d_5_1_6_0_0.45d02f8dsY6O7k#section-dsv-grs-qgb
        raise HTTPException(status_code=result.status, detail="文件上传失败")


def convert_speex_to_wav(input_path: str, output_path: str) -> bool:
    """调用 speex_decode 工具进行格式转换"""
    try:
        logger.info(f"开始转换: {input_path}")
        result = subprocess.run(
            ["speex2wav", input_path, output_path],
            capture_output=True,
            text=True
        )
        if result.returncode != 0:
            logger.error(f"转换失败: {result.stderr}")
            return False
        logger.info(f"转换成功: {output_path}")
        return True
    except Exception as e:
        logger.error(f"系统错误: {str(e)}")
        return False


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(
        f"请求失败: {request.url} | 状态码: {exc.status_code} | 错误详情: {exc.detail}"
    )
    return JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})


class CredentialsProvider(EnvironmentVariableCredentialsProvider):

    def __init__(self, access_key_id, access_key_secret):
        super().__init__()
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret

    def get_credentials(self):
        return Credentials(self.access_key_id, self.access_key_secret)

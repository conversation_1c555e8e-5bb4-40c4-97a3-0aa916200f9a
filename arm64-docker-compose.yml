networks:
  frontend:
    driver: ${NETWORKS_DRIVER}
  backend:
    driver: ${NETWORKS_DRIVER}

volumes:
  docker-in-docker:
    driver: ${VOLUMES_DRIVER}

services:
  ### NGINX Server #########################################
  nginx:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/nginx:1.4.0
    container_name: higgses-nginx
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - ${NGINX_HOST_LOG_PATH}:/var/log/nginx
      - ${NGINX_SITES_PATH}:/etc/nginx/sites-available
      - ${NGINX_SSL_PATH}:/etc/nginx/ssl
    ports:
      - "${NGINX_HOST_HTTP_PORT}:80"
      - "${NGINX_HOST_HTTPS_PORT}:443"
    depends_on:
      - php-fpm
      - php-worker
    networks:
      - frontend
      - backend
    extra_hosts:
      - "host.docker.internal:host-gateway"
    platform: linux/amd64

  ### PHP-FPM ##############################################
  php-fpm:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-74:1.3.0
    container_name: higgses-php-fpm-74
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
    expose:
      - "9000"
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
      - FAKETIME=${PHP_FPM_FAKETIME}
      - TZ=${WORKSPACE_TIMEZONE}
    depends_on:
      - workspace
    networks:
      - backend
    links:
      - docker-in-docker
    platform: linux/amd64

  ### PHP-FPM-PHP72 ##############################################
  php-fpm-72:
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-72:1.0.0
    container_name: higgses-php-fpm-72
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
    expose:
      - 9000
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
      - FAKETIME=${PHP_FPM_FAKETIME}
    depends_on:
      - workspace
    networks:
      - backend
    links:
      - docker-in-docker
    platform: linux/amd64

  ### PHP-FPM ##############################################
  php-fpm-56:
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-56:1.1.0
    container_name: higgses-php-fpm-56
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
    expose:
      - 9000
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
      - FAKETIME=${PHP_FPM_FAKETIME}
    depends_on:
      - workspace
    networks:
      - backend
    links:
      - docker-in-docker
    platform: linux/amd64

  # ### PHP-FPM PHP8.3 版本 FPM容器可以正常使用，目前无应用场景，暂时未启用 ##############################################
  #   php-fpm-83:
  #     restart: always
  #     image: registry.cn-hongkong.aliyuncs.com/higgses/php-fpm-83:1.0.0
  #     container_name: higgses-php-fpm-83
  #     volumes:
  #       - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
  #       - docker-in-docker:/certs/client
  #     expose:
  #       - "9000"
  #     extra_hosts:
  #       - "dockerhost:${DOCKER_HOST_IP}"
  #     environment:
  #       - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
  #       - DOCKER_HOST=tcp://docker-in-docker:2376
  #       - DOCKER_TLS_VERIFY=1
  #       - DOCKER_TLS_CERTDIR=/certs
  #       - DOCKER_CERT_PATH=/certs/client
  #       - FAKETIME=${PHP_FPM_FAKETIME}
  #       - TZ=${WORKSPACE_TIMEZONE}
  #     depends_on:
  #       - workspace
  #     networks:
  #       - backend
  #     links:
  #       - docker-in-docker
  #     platform: linux/amd64

  ### Workspace Utilities ##################################
  workspace:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/workspace-74:1.4.0
    container_name: higgses-workspace-74
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
      - ./php-worker/supervisord.d:/etc/supervisord.d
      - ~/.ssh:/root/.ssh
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    expose:
      - 22
    tty: true
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
    networks:
      - frontend
      - backend
    links:
      - docker-in-docker
    platform: linux/amd64

  ### Workspace Utilities ##################################
  workspace-83:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/workspace-83:1.0.0
    container_name: higgses-workspace-83
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
      - ./php-worker-83/supervisord.d:/etc/supervisord.d
      - ~/.ssh:/root/.ssh
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    expose:
      - 22
    tty: true
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
    networks:
      - frontend
      - backend
    links:
      - docker-in-docker
    platform: linux/amd64

  ### PHP Worker ############################################
  php-worker:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-worker-74:1.7.1
    container_name: higgses-php-worker-74
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - ./php-worker/supervisord.d:/etc/supervisord.d
    expose:
      - 1215 # 让其他容器访问
      - 1216 # 让其他容器访问
    ports:
      - "1215:1215" # 让宿主机和外部访问
      - "1216:1216" # 让宿主机和外部访问
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - TZ=${WORKSPACE_TIMEZONE}
    networks:
      - backend
    platform: linux/amd64

  ### PHP Worker ############################################
  php-worker-83:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-worker-83:1.0.0
    container_name: higgses-php-worker-83
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - ./php-worker-83/supervisord.d:/etc/supervisord.d
    expose:
      - 8000 # 让其他容器访问
      - 8010 # 让其他容器访问
      - 8020 # 让其他容器访问
      - 8030 # 让其他容器访问
    ports:
      - "8000:8000" # 让宿主机和外部访问
      - "8010:8010" # 让宿主机和外部访问
      - "8020:8020" # 让宿主机和外部访问
      - "8030:8030" # 让宿主机和外部访问
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - TZ=${WORKSPACE_TIMEZONE}
    networks:
      - backend
    platform: linux/amd64

  ### Docker-in-Docker ################################################
  docker-in-docker:
    image: registry.cn-hongkong.aliyuncs.com/higgses/dokcer-in-docker:1.0.0
    container_name: higgses-docker-in-docker
    environment:
      DOCKER_TLS_SAN: DNS:docker-in-docker
    privileged: true
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
      - docker-in-docker:/certs/client
    expose:
      - 2375
    networks:
      - backend
    platform: linux/amd64

  ### wx-speex2wav Service ############################################
  wx-speex2wav-service:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/wx-speex2wav:1.0.0
    container_name: higgses-wx-speex2wav-service
    volumes:
      - ${APP_CODE_PATH_HOST}/cczhaoche/micro-services/wx-speex2wav-service:/app
    environment:
      - GUNICORN_WORKERS=2
      - OSS_ACCESS_KEY=LTAI5t76RC5avYxzysqw4TSG
      - OSS_SECRET_KEY=******************************
      - OSS_END_POINT=oss-cn-huhehaote.aliyuncs.com
      - OSS_HOST=https://cczhaoche-mall-storage.oss-cn-huhehaote.aliyuncs.com
      - OSS_REGION=cn-huhehaote
      - OSS_BUCKET_NAME=cczhaoche-mall-storage
      - OSS_OBJECT_PATH=cczhaoche/voice-call/
    ports:
      - 7900:8000 # 7900为宿主机端口，8000为容器端口
    networks:
      - backend
    depends_on:
      - workspace
    platform: linux/amd64
# # Consul
#   consul:
#     restart: always
#     image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/hashicorp/consul:1.20.1  # https://docker.aityp.com/image/docker.io/hashicorp/consul:1.20.1
#     container_name: higgses-consul
#     ports:
#       - 8500:8500
#     networks:
#       - backend
#     platform: linux/amd64

# ### RabbitMQ #############################################
#   rabbitmq:
#     restart: always
#     image: registry.cn-hongkong.aliyuncs.com/higgses/rabbitmq:1.1.0
#     container_name: higgses-rabbitmq
#     ports:
#       - "${RABBITMQ_NODE_HOST_PORT}:5672"
#       - "${RABBITMQ_MANAGEMENT_HTTP_HOST_PORT}:15672"
#       - "${RABBITMQ_MANAGEMENT_HTTPS_HOST_PORT}:15671"
#       - "${RABBITMQ_WEB_STOMP_HOST_PORT}:15674"
#     privileged: true
#     environment:
#       - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
#       - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
#     hostname: higgses-rabbitmq
#     volumes:
#       - ${DATA_PATH_HOST}/rabbitmq:/var/lib/rabbitmq
#       - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
#       - ./rabbitmq/management_agent.disable_metrics_collector.conf:/etc/rabbitmq/conf.d/management_agent.disable_metrics_collector.conf
#     depends_on:
#       - php-fpm
#     networks:
#       - backend
#     platform: linux/amd64

### Redis Cluster ##########################################
# redis-cluster:
#   restart: always
#   image: registry.cn-hongkong.aliyuncs.com/higgses/redis-cluster:1.0.0
#   container_name: higgses-redis-cluster
#   ports:
#     - "${REDIS_CLUSTER_PORT_RANGE}:7000-7005"
#   volumes:
#     - ${DATA_PATH_HOST}/redis:/data
#   environment:
#       - "IP=0.0.0.0"
#   networks:
#     - backend
#   platform: linux/amd64

# ### MySQL5.6 #############################################
#   mysql-56-container:
#     image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/mysql:5.6.48
#     container_name: higgses-mysql-56-container
#     restart: always
#     environment:
#       MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
#     ports:
#       - 6606:3306
#     volumes:
#       - ${DATA_PATH_HOST}/mysql/conf.d:/etc/mysql/conf.d
#       - ${DATA_PATH_HOST}/mysql/data:/var/lib/mysql
#       - ${DATA_PATH_HOST}/mysql/logs:/logs
#     networks:
#       - backend
#     platform: linux/amd64

version: "3"

networks:
  backend:
    driver: ${NETWORKS_DRIVER}

services:
  ### RabbitMQ #############################################
  rabbitmq:
    image: registry.cn-hongkong.aliyuncs.com/higgses/rabbitmq:1.0.4
    container_name: higgses-rabbitmq-pre-release
    ports:
      - "${RABBITMQ_NODE_HOST_PORT}:5672"
      - "${RABBITMQ_MANAGEMENT_HTTP_HOST_PORT}:15672"
      - "${RABBITMQ_MANAGEMENT_HTTPS_HOST_PORT}:15671"
      - "${RABBITMQ_WEB_STOMP_HOST_PORT}:15674"
      - "${RABBITMQ_STOMP_HOST_PORT}:61613"
    privileged: true
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
    hostname: higgses-rabbitmq-pre-release
    volumes:
      - ${DATA_PATH_HOST}/rabbitmq:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./rabbitmq/management_agent.disable_metrics_collector.conf:/etc/rabbitmq/conf.d/management_agent.disable_metrics_collector.conf
    networks:
      - backend

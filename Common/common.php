<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of String
 *
 * <AUTHOR>
 */
class ZString
{
    /**
     * 过滤HTML注释
     * @param  <string> $text 内容
     * @return <string> 过滤后的内容
     */
    public function filter_html_remark($text)
    {
        return preg_replace('/<!--?.*-->/', '', $text);
    }

    /**
     * 将数据封装为JSON格式
     * @param <mixed> $data 数据
     * @return string JSON字符串
     */
    public function json_encode($data)
    {
        if (is_object($data)) {
            //对象转换成数组
            $data = get_object_vars($data);
        } elseif (!is_array($data)) {
            if (is_bool($data)) {
                $data = $data ? 'true' : 'false';
            } elseif (is_int($data)) {
                $data = intval($data);
            } elseif (is_float($data)) {
                $data = floatval($data);
            } elseif (defined($data) && $data === null) {
                $data = strval(constant($data));
            } elseif (is_string($data)) {
                $data = strtr($data, array("\n" => '<br/>'));       //JSON解析换行符问题
                $data = strtr($data, array("\t" => ''));       //JSON解析换行符问题
                $data = strtr($data, array("\r\n" => ''));       //JSON解析换行符问题
                $data = strtr($data, array("\r" => ''));       //JSON解析换行符问题
                $data = '"' . addslashes($data) . '"';
                $data = strtr($data, array("\'" => "'"));       //JSON解析单引号问题
            }
            return $data;
            // 普通格式直接输出
            //return formatJsonValue($data);
        }
        // 判断是否关联数组
        if (empty($data) || is_numeric(implode('', array_keys($data)))) {
            $assoc = false;
        } else {
            $assoc = true;
        }
        // 组装 Json字符串
        $json = $assoc ? '{' : '[';
        foreach ($data as $key => $val) {
            if (!is_null($val)) {
                if ($assoc) {
                    $json .= "\"$key\":" . $this->json_encode($val) . ",";
                } else {
                    $json .= $this->json_encode($val) . ",";
                }
            } else {
                $json .= "\"$key\":" . "\"\",";
            }
        }
        if (strlen($json) > 1) { // 加上判断 防止空数组
            $json = substr($json, 0, -1);
        }
        $json .= $assoc ? '}' : ']';
        return $json;
    }

    /**
     * 字符串截取
     * @param  <string> $str     原字符串
     * @param  <int>    $length  截取长度
     * @param  <int>    $start   开始截取位置
     * @param  <string> $charset 编码
     * @param  <bool>   $suffix  是否显示省略号
     * @return <string> 截取后的字符串
     */
    public function substr($str, $length, $start = 0, $charset = "utf-8", $suffix = true)
    {
        if (function_exists("mb_substr")) {
            return mb_substr($str, $start, $length, $charset);
        } elseif (function_exists('iconv_substr')) {
            return iconv_substr($str, $start, $length, $charset);
        }
        $re['utf-8'] = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
        $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
        $re['gbk'] = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
        $re['big5'] = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
        preg_match_all($re[$charset], $str, $match);
        $slice = join("", array_slice($match[0], $start, $length));
        if ($suffix && (strlen($str) - $start > $length)) {
            return $slice . "...";
        }
        return $slice;
    }
}

/**
 * +----------------------------------------------------------
 * 生成图像验证码
 * +----------------------------------------------------------
 * @static
 * @access public
 * +----------------------------------------------------------
 * @param string $length 位数
 * @param string $mode 类型
 * @param string $type 图像格式
 * @param string $width 宽度
 * @param string $height 高度
 * +----------------------------------------------------------
 * @return string
+----------------------------------------------------------
 */
function buildImageVerify($length = 4, $mode = 1, $type = 'png', $width = 48, $height = 22, $verifyName = 'verify', $case_sensitive = false)
{
    $randval = rand_string($length, $mode);
    if ($case_sensitive) {
        $_SESSION[$verifyName] = md5($randval);
    } else {
        $_SESSION[$verifyName] = md5(strtolower($randval));
    }
    $width = ($length * 10 + 10) > $width ? $length * 10 + 10 : $width;
    if ($type != 'gif' && function_exists('imagecreatetruecolor')) {
        $im = @imagecreatetruecolor($width, $height);
    } else {
        $im = @imagecreate($width, $height);
    }
    $r = array(225, 255, 255, 223);
    $g = array(225, 236, 237, 255);
    $b = array(225, 236, 166, 125);
    $key = mt_rand(0, 3);

    $backColor = imagecolorallocate($im, $r[$key], $g[$key], $b[$key]);    //背景色（随机）
    $borderColor = imagecolorallocate($im, 100, 100, 100);                    //边框色
    $pointColor = imagecolorallocate($im, mt_rand(0, 255), mt_rand(0, 255), mt_rand(0, 255));                 //点颜色

    @imagefilledrectangle($im, 0, 0, $width - 1, $height - 1, $backColor);
    @imagerectangle($im, 0, 0, $width - 1, $height - 1, $borderColor);
    $stringColor = imagecolorallocate($im, mt_rand(0, 200), mt_rand(0, 120), mt_rand(0, 120));
    // 干扰
    for ($i = 0; $i < 10; $i++) {
        $fontcolor = imagecolorallocate($im, mt_rand(0, 255), mt_rand(0, 255), mt_rand(0, 255));
        imagearc($im, mt_rand(-10, $width), mt_rand(-10, $height), mt_rand(30, 300), mt_rand(20, 200), 55, 44, $fontcolor);
    }
    for ($i = 0; $i < 25; $i++) {
        $fontcolor = imagecolorallocate($im, mt_rand(0, 255), mt_rand(0, 255), mt_rand(0, 255));
        imagesetpixel($im, mt_rand(0, $width), mt_rand(0, $height), $pointColor);
    }
    for ($i = 0; $i < $length; $i++) {
        imagestring($im, 5, $i * 10 + 5, mt_rand(1, 8), $randval{
            $i}, $stringColor);
    }
    //        @imagestring($im, 5, 5, 3, $randval, $stringColor);
    ob_end_clean();
    Image::output($im, $type);
}

/**
 * 将数据封装成JSONP格式
 * @param  <bool>  $ret   TRUE OR FALSE
 * @param  <mixed> $data  数据
 * @param  <int>   $count 数据个数
 * @param  <mixed> $sup   额外数据
 * @return <string> JSONP格式字符串
 */
function jsonp_encode($ret, $data = null, $count = null, $sup = null)
{
    $str = new ZString();
    $jret = $ret ? 'true' : 'false';
    $jdata = isset($data) ? ',"data":' . $str->json_encode($data) : '';
    $jcount = isset($count) ? ',"count":' . $count : '';
    $jsup = isset($sup) ? ',"sup":' . $str->json_encode($sup) : '';
    if (empty($_REQUEST['jsoncallback'])) {
        $jsonp = '{"ret":' . $jret . $jdata . $jcount . $jsup . '}';
    } else {
        $jsonp = $_REQUEST['jsoncallback'] . '({"ret":' . $jret . $jdata . $jcount . $jsup . '});';
    }
    return $jsonp;
}

/**
 * 时分秒转化为 秒
 * @param $time
 * @return bool
 */
function convertToSecond($time)
{
    $arr = explode(":", $time);
    if (count($arr) != 3) {
        return false;
    }

    return $arr[0] * 3600 + $arr[1] * 60 + $arr[2];
}

/**
 * 秒转化为时间
 * @param $second
 * @return bool|string
 */
function converToTime($second)
{
    if ($second > 86400) {
        return false;
    }
    $hour = sprintf("%02d", intval($second / 3600));
    $min = sprintf("%02d", intval(($second - ($hour * 3600)) / 60));
    $second = sprintf("%02d", intval($second - ($hour * 3600) - ($min * 60)));
    return $hour + ":" + $min + ":" + $second;
}

/**
 * 获取当前时间
 */
function get_current_time()
{
    return date('Y-m-d H:i:s');
}

/**
 * 获取时间秒
 * @param $time
 */
function getTimeNumber($time)
{
    $temp = 0;
    if ($time) {
        $temp = intval(substr($time, 0, 2)) * 60 * 60;
        $temp += intval(substr($time, 3, 2)) * 60;
        $temp += intval(substr($time, 5, 2));
    }
    return $temp;
}

/**
 * 获取两个时间的差值
 */
function getDifftime($start_time, $end_time)
{
    $difftime = "已超时";
    $days = 0;
    $hours = 0;
    $minutes = 0;
    $timestamp = strtotime($start_time) - strtotime($end_time);
    if ($timestamp > 0) {
        $difftime = '仅剩 ';
        $days = intval($timestamp / 86400);
        $hours = intval(($timestamp - $days * 86400) / 3600);
        $minutes = intval(($timestamp - $days * 86400 - $hours * 3600) / 60);
        if ($days) {
            $difftime = $difftime . $days . '天';
        }
        if ($hours || $days > 0) {
            $difftime = $difftime . $hours . '小时';
        }
        if ($minutes) {
            $difftime = $difftime . $minutes . '分钟';
        }

        if (empty($days) && empty($hours)) {
            $difftime .= '，请立即派单!';
        }
    }
    return $difftime;
}

/**
 * 对象转换为数组
 * @param object $obj
 * @return array
 */
function object_to_array($obj)
{
    $_arr = is_object($obj) ? get_object_vars($obj) : $obj;
    if (!empty($_arr)) {
        foreach ($_arr as $key => $val) {
            $val = (is_array($val) || is_object($val)) ? object_to_array($val) : $val;
            $arr[$key] = $val;
        }
        return $arr;
    }
    return array();
}

/**
 * 数组转换为对象
 * @param array $params
 * @return object
 */
function array_to_object($params)
{
    if (is_array($params)) {
        $obj = new stdClass();
        foreach ($params as $key => $value) {
            $obj->$key = array_to_object($value);
        }
        return $obj;
    }
    return $params;
}

/**
 * 计算数量
 * @param string $model 视图模型名称
 * @param string $where 条件
 * @return int
 */
function tpl_count($model, $where)
{
    $model = $model . 'ViewModel';
    $model = new $model();
    return $model->where($where)->count();
}

/**
 * 日期过滤
 * @param array $item 数据
 */
function date_filter($key, &$item)
{
    //    $value = 'haha succeed!';
    //    $item[$key] = $value;
}

/**
 * 中文分词
 * @param string $text 中文
 * @return string 分词的unicode编码
 */
function ch_word_segment($text)
{
    $so = scws_new();
    $so->set_charset('utf8');
    $so->set_multi(SCWS_MULTI_SHORT | SCWS_MULTI_DUALITY);
    $text = str_replace(' ', '', $text);
    $text = preg_replace('/[a-z0-9A-Z_]/', '', $text);
    if (strlen($text) > 300) {
        $text = substr($text, 0, 300);
    }
    $so->send_text($text);
    $array = $so->get_result();
    $word = '';
    foreach ($array as $key => $value) {
        $word .= unicode_encode($value['word']) . ' ';
        //        $word.=str_replace('%','',urlencode($value['word'])).' ';
        // $word.=str2Unicode($value['word'], 'UTF-8') . ' ';
    }
    $so->close();
    return $word;
}

/**
 * 中文unicode编码
 * @param string $name 中文
 * @return string unicode编码
 */
function unicode_encode($name)
{
    $name = iconv('UTF-8', 'UCS-2LE', $name);
    $len = strlen($name);
    $str = '';
    for ($i = 0; $i < $len - 1; $i = $i + 2) {
        $c = $name[$i];
        $c2 = $name[$i + 1];
        if (ord($c) > 0) {    // 两个字节的文字
            //$str .= '\u' . base_convert(ord($c), 10, 16) . base_convert(ord($c2), 10, 16);
            $str .= base_convert(ord($c), 10, 16) . base_convert(ord($c2), 10, 16);
        } else {
            $str .= $c2;
        }
    }
    return $str;
}

/**
 * 填充数组
 * @param array $arr 数组引用
 * @param string $path 路径（key1.key2.key3)
 * @param mixed $v 值
 */
function fill_arr(&$arr, $path, $v)
{
    $err = explode(".", $path);
    if (count($err) > 1) {
        if (empty($arr[$err[0]])) {
            $arr[$err[0]] = array();
        } elseif (!is_array($arr[$err[0]])) {
            $arr[$err[0]] = array($arr[$err[0]]);
        }
        $key = $err[0];
        unset($err[0]);
        $first = true;
        $s = '';
        foreach ($err as $rr) {
            if ($first) {
                $s = $rr;
                $first = false;
            } else {
                $s .= '.' . $rr;
            }
        }
        fill_arr($arr[$key], $s, $v);
    } else {
        $arr[$err[0]] = $v;
    }
}

/**
 * 移除数组
 * @param array $arr 数组引用
 * @param string $path 路径（key1.key2.key3)
 */
function remove_arr(&$arr, $path)
{
    $err = explode(".", $path);
    if (count($err) > 1) {
        if (empty($arr[$err[0]])) {
            $arr[$err[0]] = array();
        } elseif (!is_array($arr[$err[0]])) {
            $arr[$err[0]] = array($arr[$err[0]]);
        }
        $key = $err[0];
        unset($err[0]);
        $first = true;
        $s = '';
        foreach ($err as $rr) {
            if ($first) {
                $s = $rr;
                $first = false;
            } else {
                $s .= '.' . $rr;
            }
        }
        remove_arr($arr[$key], $s);
    } else {
        unset($arr[$err[0]]);
    }
}

/**
 * 获取数组的值
 * @param array $arr 数组
 * @param string $path 路径（key1.key2.key3）
 * @return mixed
 */
function get_arr($arr, $path)
{
    $err = explode(".", $path);
    if (count($err) > 1) {
        $key = $err[0];
        unset($err[0]);
        $first = true;
        $s = '';
        foreach ($err as $rr) {
            if ($first) {
                $s = $rr;
                $first = false;
            } else {
                $s .= '.' . $rr;
            }
        }
        return c($arr[$key], $s);
    } else {
        return $arr[$err[0]];
    }
}

/**
 * 获取星期
 * @param string $time 时间字符串
 */
function get_week($time)
{
    $weekarray = array("日", "一", "二", "三", "四", "五", "六");
    return '星期' . $weekarray[date("w", strtotime($time))];
}

/**
 * 获取两个日期之间相差的天数
 * (针对1970年1月1日之后，求之前可以采用泰勒公式)
 * @param string $day1
 * @param string $day2
 * @return number
 */
function diffBetweenTwoDays($day1, $day2)
{
    $second1 = strtotime($day1);
    $second2 = strtotime($day2);

    if ($second1 < $second2) {
        $tmp = $second2;
        $second2 = $second1;
        $second1 = $tmp;
    }
    return ($second1 - $second2) / 86400;
}

/**
 * 获取时间区间数组
 */
function getTimeRegion($startDate, $startTime, $endTime, $period = 600)
{
    if (!$startTime || !$endTime) {
        return [];
    }

    $ret[] = $startDate . " " . substr($startTime, 0, 5);
    $startTimeStamp = strtotime(date($startDate . $startTime));
    $endTimeStamp = strtotime(date($startDate . $endTime));
    if ($period <= 0) {
        return $ret;
    }
    if ($endTimeStamp < $startTimeStamp) {
        $tempStamp = $startTimeStamp;
        $todayEndTimeStamp = strtotime(date($startDate . "23:59:59"));
        while (true) {
            $tempStamp += $period;
            if ($todayEndTimeStamp < $tempStamp) {
                break;
            }
            $ret[] = $startDate . " " . substr(date('Y-m-d H:i:s', $tempStamp), 11, 5);
        }

        $startNextDate = date("Y-m-d", strtotime($startDate) + 86400);
        $endNextDateStartTime =  strtotime(date($startNextDate . $endTime));
        while (true) {
            if ($endNextDateStartTime < $tempStamp) {
                break;
            }
            $ret[] = $startNextDate . " " . substr(date('Y-m-d H:i:s', $tempStamp), 11, 5);
            $tempStamp += $period;
        }

        return $ret;
    } else {
        $tempStamp = $startTimeStamp;
        while (true) {
            $tempStamp += $period;
            if ($endTimeStamp < $tempStamp) {
                break;
            }
            $ret[] = $startDate . " " . substr(date('Y-m-d H:i:s', $tempStamp), 11, 5);
        }
    }

    return $ret;
}
/**
 * 获取年龄
 */
function birthday($birthday)
{
    $age = strtotime($birthday);
    if ($age === false) {
        return false;
    }
    list($y1, $m1, $d1) = explode("-", date("Y-m-d", $age));
    $now = strtotime("now");
    list($y2, $m2, $d2) = explode("-", date("Y-m-d", $now));
    $age = $y2 - $y1;
    if ((int)($m2 . $d2) < (int)($m1 . $d1)) {
        $age -= 1;
    }
    return $age;
}

/*
 * author: zeus
 * time: 2017-12-21
 * 发博时间计算(年，月，日，时，分，秒)
 * $createtime 可以是当前时间
 * $gettime 你要传进来的时间
 */
class Mygettime
{
    public function __construct($createtime, $gettime)
    {
        $this->createtime = $createtime;
        $this->gettime = $gettime;
    }
    public function getSeconds()
    {
        return $this->createtime - $this->gettime;
    }
    public function getMinutes()
    {
        return ($this->createtime - $this->gettime) / (60);
    }
    public function getHours()
    {
        return ($this->createtime - $this->gettime) / (60 * 60);
    }
    public function getDay()
    {
        return ($this->createtime - $this->gettime) / (60 * 60 * 24);
    }
    public function getMonth()
    {
        return ($this->createtime - $this->gettime) / (60 * 60 * 24 * 30);
    }
    public function getYear()
    {
        return ($this->createtime - $this->gettime) / (60 * 60 * 24 * 30 * 12);
    }
    public function index()
    {
        if ($this->getYear() > 1) {
            if ($this->getYear() > 2) {
                return date("Y-m-d", $this->gettime);
            }
            return intval($this->getYear()) . "年前";
        }
        if ($this->getMonth() > 1) {
            return intval($this->getMonth()) . "月前";
        }
        if ($this->getDay() > 1) {
            return intval($this->getDay()) . "天前";
        }
        if ($this->getHours() > 1) {
            return intval($this->getHours()) . "小时前";
        }
        if ($this->getMinutes() > 1) {
            return intval($this->getMinutes()) . "分钟前";
        }
        if ($this->getSeconds() > 1) {
            return intval($this->getSeconds() - 1) . "秒前";
        }
    }
}

/**
 * 日期分类
 */
class DateClassify
{
    private $time;

    public function __construct($time)
    {
        $this->time = $time;
    }

    /**
     * 分类
     * @return string
     */
    public function classify()
    {
        if ($this->_isLast()) {
            $r = '先前';
        } elseif ($this->_isLastMonth()) {
            $r = '上个月';
        } elseif ($this->_isLastWeek()) {
            $r = '上周';
        } elseif ($this->_isYesterday()) {
            $r = '昨天';
        } elseif ($this->_isToday()) {
            $r = '今天';
        } elseif ($this->_isTomorrow()) {
            $r = '明天';
        } elseif ($this->_isThisWeek()) {
            $r = '本周';
        } elseif ($this->_isNextWeek()) {
            $r = '下周';
        } elseif ($this->_isThisMonth()) {
            $r = '本月';
        } elseif ($this->_isNextMonth()) {
            $r = '下个月';
        } elseif ($this->_isLater()) {
            $r = '以后';
        }
        return $r;
    }

    public function classifyshow()
    {
        $r = [];
        if ($this->_isToday()) {
            $r['class'] =  '今天';
        } elseif ($this->_isTomorrow()) {
            $r['class'] =  '明天';
        } elseif ($this->_isBackstage()) {
            $r['class'] =  '后天';
        } else {
            $r['class'] =  $this->_week();
        }
        $r['number'] = $this->_number();
        $r['month_number'] = $this->_monthNumber();

        return $r;
    }

    /**
     * 获取当前时间的秒数
     */
    public function classifyTimeNumber()
    {
        $hour = intval($this->_hour());
        $minute = intval($this->_minute());
        $second = intval($this->_second());
        $r = $hour * 60 * 60;
        $r += ($minute * 60);
        $r += $second;
        return $r;
    }

    /**
     * 判断是否是昨天
     * @return boolean
     */
    private function _isYesterday()
    {
        $s1 = date('Ymd', strtotime($this->time));
        $s2 = date('Ymd', strtotime(get_current_time()) - 1 * 24 * 60 * 60);
        if ($s1 === $s2) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是今天
     * @return boolean
     */
    private function _isToday()
    {
        $s1 = date('Ymd');
        $t = strtotime($this->time);
        $s2 = date('Ymd', $t);
        if ($s1 === $s2) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是明天
     * @return boolean
     */
    private function _isTomorrow()
    {
        $s1 = date('Ymd', strtotime($this->time));
        $s2 = date('Ymd', strtotime(get_current_time()) + 1 * 24 * 60 * 60);
        if ($s1 === $s2) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是后天
     * @return boolean
     */
    private function _isBackstage()
    {
        $s1 = date('Ymd', strtotime($this->time));
        $s2 = date('Ymd', strtotime(get_current_time()) + 2 * 24 * 60 * 60);
        if ($s1 === $s2) {
            return true;
        }
        return false;
    }

    private function _week()
    {
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        return '周' . $weekarray[date("w", strtotime($this->time))];
    }

    /**
     * 判断是否是上周
     */
    private function _isLastWeek()
    {
        $week_start = mktime(0, 0, 0, date("m"), date("d") - date("w") == 0 ? 7 : date("w"), date("Y"));
        $week_end = mktime(23, 59, 59, date("m"), date("d") - date("w") == 0 ? 7 : date("w"), date("Y"));
        $t = strtotime($this->time);
        if ($t >= $week_start && $t <= $week_end) {
            return true;
        }
        return false;
    }

    /**
     * 本周
     * @return boolean
     */
    private function _isThisWeek()
    {
        $week_start = mktime(0, 0, 0, date("m"), date("d") - (date("w") == 0 ? 7 : date("w")) + 1, date("Y"));
        $week_end = mktime(23, 59, 59, date("m"), date("d") - (date("w") == 0 ? 7 : date("w")) + 7, date("Y"));
        $t = strtotime($this->time);
        if ($t >= $week_start && $t <= $week_end) {
            return true;
        }
        return false;
    }

    /**
     * 下周
     * @return boolean
     */
    private function _isNextWeek()
    {
        $week_start = mktime(0, 0, 0, date("m"), date("d") - (date("w") == 0 ? 7 : date("w")) + 1 + 7, date("Y"));
        $week_end = mktime(23, 59, 59, date("m"), date("d") - (date("w") == 0 ? 7 : date("w")) + 7 + 7, date("Y"));
        $t = strtotime($this->time);
        if ($t >= $week_start && $t <= $week_end) {
            return true;
        }
        return false;
    }

    /**
     * 上个月
     * @return boolean
     */
    private function _isLastMonth()
    {
        $month_start = mktime(0, 0, 0, date("m") - 1, 1, date("Y"));
        $month_end = mktime(23, 59, 59, date("m"), 0, date("Y"));
        $t = strtotime($this->time);
        if ($t >= $month_start && $t <= $month_end) {
            return true;
        }
        return false;
    }

    /**
     * 本月
     * @return boolean
     */
    private function _isThisMonth()
    {
        $month_start = mktime(0, 0, 0, date("m"), 1, date("Y"));
        $month_end = mktime(23, 59, 59, date("m"), date("t"), date("Y"));
        $t = strtotime($this->time);
        if ($t >= $month_start && $t <= $month_end) {
            return true;
        }
        return false;
    }

    /**
     * 下个月
     * @return boolean
     */
    private function _isNextMonth()
    {
        $month_start = mktime(0, 0, 0, date("m") + 1, 1, date("Y"));
        $month_end = mktime(23, 59, 59, date("m") + 2, 0, date("Y"));
        $t = strtotime($this->time);
        if ($t >= $month_start && $t <= $month_end) {
            return true;
        }
        return false;
    }

    /**
     * 以后
     */
    private function _isLater()
    {
        $later = mktime(23, 59, 59, date("m") + 2, 0, date("Y"));
        $t = strtotime($this->time);
        if ($t > $later) {
            return true;
        }
        return false;
    }

    /**
     * 以前
     */
    private function _isLast()
    {
        $last = mktime(0, 0, 0, date("m") - 1, 1, date("Y"));
        $t = strtotime($this->time);
        if ($t < $last) {
            return true;
        }
        return false;
    }

    private function _second()
    {
        return substr($this->time, 17, 2);
    }

    private function _minute()
    {
        return substr($this->time, 14, 2);
    }

    private function _hour()
    {
        return substr($this->time, 11, 2);
    }

    /**
     * 几号例如： 01 02 03
     */
    private function _number()
    {
        return substr($this->time, 8, 2);
    }

    /*
     * 几月几号例如：01.01 01.23
     */
    private function _monthNumber()
    {
        return substr($this->time, 5, 2) . '.' . $this->_number();
    }
}

// 循环创建目录
function mk_dir($dir, $mode = 0777)
{
    if (is_dir($dir) || @mkdir($dir, $mode)) {
        return true;
    }
    if (!mk_dir(dirname($dir), $mode)) {
        return false;
    }
    return @mkdir($dir, $mode);
}

/**
 * 生成手机验证码
 */
function build_cellphone_validate_code()
{
    $time = strval(time());
    $pwd = substr($time, count($time) - 7, 6);
    return $pwd;
}

/**
 * 根据经纬度获取地址描述
 * @param float $longitude 经度
 * @param float $latitude 纬度
 */
function getAddressDescription($longitude, $latitude)
{
    $url = 'http://api.map.baidu.com/geocoder';
    $method = 'get';
    $params = array(
        'location' => "$latitude,$longitude",
        'key' => '854beda74147808fa0c20a5341e2c9ab',
        'output' => 'json'
    );
    $r = httpRequest($url, $method, $params);
    $r = json_decode($r);
    return $r;
}


/**
 * 发起 HTTP 请求
 * @param string $url 请求的 URL
 * @param string $method 请求方法
 * @param array $params 请求参数
 * @param array $headers 请求头
 * @param boolean $logCurl 是否记录日志
 * @return string|false 响应结果或 false
 */
function httpRequest($url, $method, $params = [], $headers = [], $logCurl = false)
{
//    $startTime = microtime(true);
//    $logData = [
//        'url' => $url,
//        'method' => $method,
//        'start_time' => date('Y-m-d H:i:s', $startTime)
//    ];
    if (trim($url) === '' || !in_array(strtolower($method), ['get', 'post', 'put', 'delete']) || !is_array($params)) {
        return false;
    }

    $curl = curl_init();

    // 基础设置
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    // 方法处理
    switch (strtolower($method)) {
        case 'get':
            // 参数编码并拼接
            if (!empty($params)) {
                $queryString = http_build_query($params);
                $url .= (strpos($url, '?') === false ? '?' : '&') . $queryString;
            }
            curl_setopt($curl, CURLOPT_URL, $url);
            break;

        case 'post':
        case 'put':
        case 'delete':
            $isJson = false;
            foreach ($headers as $header) {
                if (stripos($header, 'Content-Type: application/json') !== false) {
                    $isJson = true;
                    break;
                }
            }

            // 根据 Content-Type 编码参数
            $postData = $isJson ? json_encode($params) : http_build_query($params);

            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, strtoupper($method));
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
            break;

        default:
            curl_close($curl);
            return false;
    }

    // 执行 CURL 请求
//    $startTime = microtime(true);
    $result = curl_exec($curl);
//    $elapsed = round((microtime(true) - $startTime) * 1000);
//    \Log::write(sprintf("请求耗时: %dms\n请求方法: %s\n请求URL: %s", $elapsed, $method, $url), \Log::DEBUG, \Log::FILE, LOG_PATH . "curl_perf_" . date('Y-m-d') . ".log");
    if ($logCurl) {
        $logMessage = sprintf(
            "请求方法: %s\n请求URL: %s\n请求参数: %s\n请求头: %s\n响应结果: %s",
            $method,
            $url,
            json_encode($params, JSON_UNESCAPED_UNICODE),
            json_encode($headers, JSON_UNESCAPED_UNICODE),
            $result
        );
        \Log::write($logMessage, \Log::DEBUG, \Log::FILE, LOG_PATH . "curl_" . date('Y-m-d') . ".log");
    }

    // 错误处理
    if (curl_errno($curl)) {
        curl_close($curl);
        return false;
    }

//    $endTime = microtime(true);
//    $totalTime = $endTime - $startTime;
    
//    if (function_exists('curl_getinfo') && is_resource($curl)) {
//        $info = curl_getinfo($curl);
//        $logData += [
//            'dns_time' => $info['namelookup_time'],
//            'connect_time' => $info['connect_time'],
//            'pretransfer_time' => $info['pretransfer_time'],
//            'starttransfer_time' => $info['starttransfer_time'],
//            'total_time' => $info['total_time'],
//            'http_code' => $info['http_code']
//        ];
//    }
    
//    $logData['end_time'] = date('Y-m-d H:i:s', $endTime);
//    $logData['execution_time'] = $totalTime;
    
    // 记录到单独的性能日志文件
//    $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
    //file_put_contents('curl_perf.log', $logContent, FILE_APPEND);
//    \Log::write($logContent, \Log::DEBUG, \Log::FILE, LOG_PATH . "curl_perf_" . date('Y-m-d') . ".log");

    curl_close($curl);
    return $result;
}


function compareTimes($time1, $time2)
{
    return strtotime($time1) - strtotime($time2);
}


/**
 * Send XML POST DATA
 * <AUTHOR> <<EMAIL>>
 * @param string $url
 * @param string $xml
 * @param array $headers
 * @param boolean $post
 * @return void
 */
function sendXmlOverPost($url, $xml = array(), $headers = array(
    "Content-Type: application/xml",
    "Accept: application/xml",
), $post = true)
{
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_POST, $post);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

    curl_setopt($curl, CURLOPT_POSTFIELDS, $xml);

    //for debug only!
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

    $resp = curl_exec($curl);
    curl_close($curl);
    return $resp;
}

/**
 * 将xml转为array
 * @param string $xml
 * @throws WxPayException
 */
function xmlToArray($xml)
{
    if (!$xml) {
        throw new WxPayException("xml数据异常！");
    }
    //将XML转为array
    //禁止引用外部xml实体
    libxml_disable_entity_loader(true);
    return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
}

function get_client_browser_user_agent()
{
    return $_SERVER['HTTP_USER_AGENT'];
}


function validateIDCard($idcard)
{
    $idcard = strtoupper($idcard); // 转换为大写字母

    // 定义身份证类型、对应的长度和正则表达式
    $patterns = [
        '18位中国居民身份证' => [
            'length' => 18,
            'pattern' => '/^[1-9]\d{5}(18|19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\d{3}(\d|X)$/',
        ],
        '15位中国居民身份证' => [
            'length' => 15,
            'pattern' => '/^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\d{3}$/',
        ],
        '外国人永久居留身份证' => [
            'length' => 18,
            'pattern' => '/^9\d{5}(19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\d{3}(\d|X)$/',
        ],
        '港澳居民居住证' => [
            'length' => 11,
            'pattern' => '/^[A-Z][0-9]{6}(\([0-9A-Z]\))?$/',
        ],
        '台湾居民居住证' => [
            'length' => 10,
            'pattern' => '/^[A-Z][0-9]{9}$/',
        ],
        '港澳居民来往内地通行证' => [
            'length' => 11,
            'pattern' => '/^[A-Z][0-9]{7}$/',
        ],
        '台湾居民来往大陆通行证' => [
            'length' => 10,
            'pattern' => '/^[A-Z][0-9]{8}$/',
        ],
    ];

    $length = strlen($idcard);

    // 遍历正则表达式并匹配
    foreach ($patterns as $type => $info) {
        if ($length === $info['length'] && preg_match($info['pattern'], $idcard)) {
            // 特别处理校验码验证（适用于 18 位身份证和外国人永久居留身份证）
            if (in_array($type, ['18位中国居民身份证', '外国人永久居留身份证'])) {
                return checkChecksum($idcard);
            }
            return true;
        }
    }

    return true; // 不匹配任何规则返回 true
}

// 校验18位身份证号码的校验码
function checkChecksum($idcard)
{
    $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 权重因子
    $checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; // 校验码对照表

    $sum = 0;

    // 计算加权和
    for ($i = 0; $i < 17; $i++) {
        $sum += intval($idcard[$i]) * $weights[$i];
    }

    // 比较最后一位校验码
    return $checkCodes[$sum % 11] === substr($idcard, -1);
}

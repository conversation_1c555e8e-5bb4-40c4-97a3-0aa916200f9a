<?php

/**
 * Created by PhpStorm.
 * User: Liny
 * Date: 2015/10/22
 * Time: 18:07
 */
class SobeyPrintTrace
{
    public static function print_stack_trace()
    {
        $array = debug_backtrace();
        //print_r($array);//信息很齐全
        unset($array[0]);
        foreach ($array as $row) {
            $html .= $row['file'] . ':' . $row['line'] . '行,调用方法:' . $row['function'] . "<p>";
        }
        //return $html;
        echo $html;
    }

    public function print_include_info(){
        $included_files = get_included_files();
        var_dump($included_files);
    }
}
#!/bin/bash

# 定义本地工作目录和 Git 仓库信息
workspace="/var/www/develop/cczhaoche/micro-service/wx-speex2wav-service"
GIT_BRANCH=${GIT_BRANCH:-main}  # 如果没有指定 GIT_BRANCH 则默认使用 main 分支
SSH_GIT_REPO=${SSH_GIT_REPO:-"*********************:higgses/cczhaoche/micro-services/wx-speex2wav-service.git"}  # 默认仓库地址

# 1. 创建项目目录，如果目录不存在则创建
echo "步骤 1: 检查或创建工作目录：${workspace}"
if [ ! -d "${workspace}" ]; then
    mkdir -p ${workspace}
    if [ $? -eq 0 ]; then
        echo "目录 ${workspace} 创建成功。"
    else
        echo "错误：创建目录 ${workspace} 失败。" >&2
        exit 1
    fi
else
    echo "目录 ${workspace} 已存在。"
fi

# 2. 进入工作目录
echo "步骤 2: 进入工作目录：${workspace}"
cd ${workspace} || { echo "错误：切换到目录 ${workspace} 失败。" >&2; exit 1; }
echo "已切换到目录 ${workspace}。"

# 3. 如果目录为空则克隆仓库
echo "步骤 3: 检查目录内容，如果为空则克隆仓库：${SSH_GIT_REPO}"
if [ -z "$(ls -A ${workspace})" ]; then
    echo "目录为空。正在克隆仓库到 ${workspace}"
    git clone -b ${GIT_BRANCH} ${SSH_GIT_REPO} .
    if [ $? -eq 0 ]; then
        echo "仓库克隆成功。"
    else
        echo "错误：克隆仓库 ${SSH_GIT_REPO} 失败。" >&2
        exit 1
    fi
else
    echo "目录不为空，跳过克隆步骤。"
fi

# 4. 拉取最新代码
echo "步骤 4: 拉取最新代码：分支 ${GIT_BRANCH}"
git pull origin ${GIT_BRANCH}
if [ $? -eq 0 ]; then
    echo "从分支 ${GIT_BRANCH} 拉取代码成功。"
else
    echo "错误：从分支 ${GIT_BRANCH} 拉取代码失败。" >&2
    exit 1
fi

# 5. 重启 Wx Speex2Wav Service Worker
echo "步骤 5: 重启 Speex2Wav Service Worker"
docker restart higgses-wx-speex2wav-service
if [ $? -eq 0 ]; then
    echo "Speex2Wav Service Worker 重启成功。"
else
    echo "错误: 重启 Speex2Wav Service Worker 失败。" >&2
    exit 1
fi

# 6. 完成所有步骤
echo "脚本执行完成。"

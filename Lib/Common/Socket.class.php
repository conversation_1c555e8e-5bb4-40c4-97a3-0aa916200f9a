<?php
	include_once("Socket/SocketPoolSingle.class.php");
	include_once("Socket/Entity.class.php");
	set_time_limit(0);
	/**
	*Socket 类型
	*Date:2017-6-6 10:55
	*author: xhr
	*/
	class Socket{
				
		private $sps = null; //创建socket池子
		
		private $get_listen_socket_num = 900; //监听数量
		
		private $maxBufferSize = 2048; //最大缓冲数
		
		private $master = null; //默认一个主机
		
		private $port = 11111 ; //默认请求端口

		private $address = null; //服务器ip（改成自己的）
		
		protected $connections = array();

		/**
		 *消息类型过滤
		 *1001 、手机号
		 *1002 、微信号
		 */
		private $infoFilter = [1001,1002];

		private $drives = ['wechat','app','wap','pc']; //设备类型

		private $sockets = [];
		
		private $logLevel = 1;
		
		protected $headerOriginRequired = false;
		protected $headerSecWebSocketProtocolRequired = false;
		protected $headerSecWebSocketExtensionsRequired = false;
		
		
		function __construct(){

			$this->address = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '*************' ;
//			$this->address = '*************' ;
			$this->bindAndListen();
		}


		/**
		*绑定并且进行监听
		*/
		private function bindAndListen(){

			try{
				$this->master = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
				if($this->master){
					socket_set_option($this->master, SOL_SOCKET, SO_REUSEADDR, 1); //设置选项

					@socket_bind($this->master, $this->address , $this->port) or die("服务器已开启"); //绑定

					@socket_listen($this->master , $this->get_listen_socket_num) or die("服务器已开启"); //监听

					$this->sockets['m'] = $this->master;
				}else{
					//exit("asdasdasd");
					die("服务器已开启...");
				}
			}catch(Exception $e){
				die("服务器异常...");

			}
		}
		/**
		 *消息发送
		 *@param $fromNo    发送者账号或者编号（唯一）
		 *@param $toNo      接收者账号或者编号（唯一）
		 *@param $type      发送者的数据类型
		 *@param $data      转发的内容
		 *	内容格式
		 *	[
		 *		'type'      => "string",     //消息类型
		 *		'length'    => '18'         //消息长度
		 *		'content'   => '123456789' //消息内容
		 *
		]
		 */
		public function send($sender, $fromNo, $toNo , $type ,$data){
			$receiver = $this->sps->getPoolBySocketId($toNo);
			try{
				//判断发送者是否在池子里面
				if(empty($sender)){
					$this->setMessage($sender, array('type'=>'error','msg'=>'sender cannot be empty'));$this->disconnect($sender);
				}else{
					//判断转发者是否在池子里面
					if(empty($receiver)){
						$this->setMessage($sender, array('type'=>'error','msg'=>'The receiver has left'));$this->disconnect($sender);
					}else{
						//数据类型
						if(!in_array($type,$this->infoFilter)){
							$this->setMessage($sender, array('type'=>'error','msg'=>'The data type error'));$this->disconnect($sender);
						}else{
							//消息类型过滤
							if(!$this->_chekcDataType($data['type'] , $data['content'])){
								$this->setMessage($sender, array('type'=>'error','msg'=>'The wrong message type'));$this->disconnect($sender);
							}else{
								//判断内容长度
								if(strlen($data['content']) > $data['length']){
									$this->setMessage($sender, array('type'=>'error','msg'=>'The content length is greater than the specified length'));$this->disconnect($sender);
								}else{
									//将数据写入到接收者端
									$this->setMessage($receiver, array('type'=>'success','msg'=>[ 'type'=>$type , 'content'=> $data['content'] ]));
								}

							}
						}

					}
				}
			}catch(Exception $e){
				$this->setMessage($sender, array('type'=>'error','msg'=>'unknown exception'));$this->disconnect($sender);$this->sps->poPool($fromNo);$this->sps->poPool($toNo);
			}
		}
		
		/**
		*心跳检测
		*/
		public function heartbeat(){
			
		}
		

		/**
		*
		*运行socket
		*/
		public function run(){
			while(true){
				if(empty($this->sockets)){
					$this->sockets['m'] = $this->master;
				}
				
				$write=$exceot=null;
				$read = $this->sockets;
				
				$num_sockets = @socket_select($read,$write,$exceot,null);

				foreach($read as $key => $socket){
					if($socket == $this->master){
						$client = socket_accept($socket);
						if($client < 0){
							continue;
						}else{
							$this->connect($client);
						}
					}else{
						
						$conn = $this->getConnBySocket($socket);
						$numBytes = @socket_recv($socket, $buffer, $this->maxBufferSize, 0);
						
						if($numBytes === false){
							$this->getsocketErroHandling($socket);
							if(isset($conn->socket_NO) && !empty($conn->socket_NO)){
								$this->sps->poPool($conn->socket_NO);
							}
						}else if($numBytes == 0){
							$this->disconnect($socket);
						}else{
							if (!$conn->getHandshake()){
								$tmp = str_replace("\r", '', $buffer);
								if (strpos($tmp, "\n\n") === false)
								{
									continue; //等client发完消息头后再处理
								}
								$handshake = $this->doHandshake($conn, $buffer);
								
								if($handshake['flag']){
									@socket_write($conn->socket, $handshake['handshakeResponse'] ,strlen($handshake['handshakeResponse']) );
									$conn->setHandshake(true);
									$this->setMessage($socket, array('type'=>'handshake'));
								}else{

									@socket_write($conn->socket, $handshake['handshakeResponse'] ,strlen($handshake['handshakeResponse']) );$this->disconnect($conn->socket);
								}
							}else{
								//split packet into frame and send it to deframe
								$data = $this->split_packet($numBytes, $buffer, $conn);
					
								if(!empty($data) && isset($data['sendType'])){
									if(empty($this->sps)){
										$this->sps = SocketPoolSingle::getInstace(); //创建线程池单列
									}
									switch(strtolower($data['sendType'])){
										case 'master':

											//是否存入池子缓存,默认否
											if(isset($data['cache']) && $data['cache']){
												//删除以前存在池子的
												if($this->sps->getPoolBySocketId($data['sendNo']))
													$this->sps->poPool($data['sendNo']);
												//加入到链接池
												$this->sps->pushPool($data['sendNo'],$socket);
												$conn->socket_NO = $data['sendNo'];
											}

											//检测设备类型
											if(isset($data['drivesType']) && in_array(strtolower($data['drivesType']) , $this->drives) ){
												$dt = $data['drivesType'];
											}else{
												$this->setMessage($socket, array('type'=>'error','The device type cannot be empty'));$this->disconnect($conn);
											}

											//告诉客户端连接成功
											$this->setMessage($socket, array('type'=>'success'));break;
										case 'forwarding':
											if(isset($dt) && strtolower($dt) == "app"){
												$sender = $socket;
											}else{
												$sender = $this->sps->getPoolBySocketId($data['fromNo']);
											}
											//转发数据
											$this->send($sender, $data['fromNo'] ,$data['toNo'] , $data['type'] , $data['data']);break;
										default :$this->setMessage($socket, array('type'=>'error','Oh!Shit, Your request is too strange')); break;
									}
								}
								
								
							}
						}
					}
					
				}
				
			}
		}
		/**
		*关闭socket
		*/
		public function close(){
			try{
				$sockets = $this->sps->getAllPool();
				foreach($sockets as $socket){
					@socket_close($socket); //关闭池子里面的socket
					$conn = $this->getConnBySocket($socket);
					if(!empty($conn) && isset($conn->socket_NO)){
						$this->sps->poPool($conn->socket_NO); //移除所有的socket
					}

				}
				@socket_close($this->master);
			}catch (Exception $e){
				Throw new Exception("socket close exception");
				return false;
			}
			return true;
		}

		/**
		*新链接
		*@param $socket     socket对象
		*/
		private function connect($socket){
			try{
				$conn = new Entity(uniqid('u') , $socket); //创建实体
				$this->connections[$conn->socket_id] = $conn;
				$this->sockets[$conn->socket_id] = $conn->socket;
			}catch(Exception $e){
				$this->logInfo("connect:error".$e->getMessage());$this->disconnect($socket);
			}
			
		}
		
		/**
		 * 用公共握手算法握手
		 *
		 * @param $socket
		 * @param $buffer
		 *
		 * @return bool
		 */
		private function doHandShake($conn, $buffer) {
			// 获取到客户端的升级密匙
			$magicGUID = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11";
			
			$data = array(); 
			$lines = explode("\n", $buffer);
			$headers = array(); 
			foreach ($lines as $line)
			{
				if (strpos($line, ":") !== false)
				{
					$header                                = explode(":", $line, 2);
					$headers[strtolower(trim($header[0]))] = trim($header[1]);
				}elseif (stripos($line, "get") !== false){
					preg_match("/GET (.*) HTTP/i", $buffer, $reqResource);
					$headers['get'] = trim($reqResource[1]);
					$conn->path = $headers['get'];
				}
			}
			
			if (!isset($headers['get']) || !$this->checkUri($headers['get']))
			{          
				$handshakeResponse = "HTTP/1.1 405 Method Not Allowed\r\n\r\n";
			}
			if (!isset($headers['host']) || !$this->checkHost($headers['host']))
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}
			if (!isset($headers['upgrade']) || strtolower($headers['upgrade']) != 'websocket')
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}
			if (!isset($headers['connection']) || strpos(strtolower($headers['connection']), 'upgrade') === FALSE)
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}
			if (!isset($headers['sec-websocket-key']))
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}
			if (!isset($headers['sec-websocket-version']) || strtolower($headers['sec-websocket-version']) != 13)
			{
				$handshakeResponse = "HTTP/1.1 426 Upgrade Required\r\nSec-WebSocketVersion: 13";
			}
			if (($this->headerOriginRequired && !isset($headers['origin'])) || ($this->headerOriginRequired && !$this->checkOrigin($headers['origin'])))
			{
				$handshakeResponse = "HTTP/1.1 403 Forbidden";
			}
			if (($this->headerSecWebSocketProtocolRequired && !isset($headers['sec-websocket-protocol'])) || ($this->headerSecWebSocketProtocolRequired && !$this->checkWebsocProtocol($headers['sec-websocket-protocol'])))
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}
			if (($this->headerSecWebSocketExtensionsRequired && !isset($headers['sec-websocket-extensions'])) || ($this->headerSecWebSocketExtensionsRequired && !$this->checkWebsocExtensions($headers['sec-websocket-extensions'])))
			{
				$handshakeResponse = "HTTP/1.1 400 Bad Request";
			}

			// 如果上面设置了$handshakeResponse说明握手失败，主动关闭
			if (isset($handshakeResponse)){
				$data['flag'] = false;
				$data['handshakeResponse'] = $handshakeResponse;
			}else{
				
				$webSocketKeyHash = sha1($headers['sec-websocket-key'] . $magicGUID);
				$rawToken = "";
				for ($i = 0; $i < 20; $i++)
				{
					$rawToken .= chr(hexdec(substr($webSocketKeyHash, $i * 2, 2)));
				}
				$handshakeToken = base64_encode($rawToken) . "\r\n";
				
				$subProtocol = (isset($headers['sec-websocket-protocol'])) ? $this->processProtocol($headers['sec-websocket-protocol']) : "";
				$extensions  = (isset($headers['sec-websocket-extensions'])) ? $this->processExtensions($headers['sec-websocket-extensions']) : "";
				
				$handshakeResponse = "HTTP/1.1 101 Switching Protocols\r\nUpgrade: websocket\r\nConnection: Upgrade\r\nSec-WebSocket-Version: 13\r\nSec-WebSocket-Accept: $handshakeToken$subProtocol$extensions\r\n";
				
				
				$data['flag'] = true;
				$data['handshakeResponse'] = $handshakeResponse;
			}
			return $data ;
			
		}
		
		/**
		*信息输出
			case 'continuous':
			case 'text':
			case 'binary':
			case 'close':
			case 'ping':
			case 'pong':
					
		*/
		private function setMessage($conn,$data){
			$flag = false;
			try{
				$message = $this->frame(json_encode($data),'text');
				if(socket_write($conn, $message , strlen($message))){
					$flag = true;
				}
			}catch(Exception $e){
				$e->getMessage();$this->disconnect($conn);
			}
			return $flag;
		}
		
		/**
		*
		*断开连接并且清除与之相关的数据
		*
		*/
		private function disconnect($socket,  $sockErrNo = null , $triggerClosed = true){
				
			try{
				$conn = $this->getConnBySocket($socket);
				
				if($conn){
					unset($this->connections[$conn->socket_id]); //清除数组中的当前元素
					
					if(array_key_exists($conn->socket_id, $this->sockets)){
						unset($this->sockets[$conn->socket_id]);
					}
					
					if(!empty($conn->socket_NO)){
						$this->sps->poPool($conn->socket_NO);
					}
				}
				
				if (!is_null($sockErrNo)){
					socket_clear_error($socket);
				}
				
				if($triggerClosed){
					socket_close($socket);
				}
			}catch(Expection $e){
				$e->getMessage();;
			}
		}
		
		private function getsocketErroHandling($socket){
			$sockErrNo = socket_last_error($socket);
			switch ($sockErrNo)
			{
				case 102: // ENETRESET    -- Network dropped connection because of reset
				case 103: // ECONNABORTED -- Software caused connection abort
				case 104: // ECONNRESET   -- Connection reset by peer
				case 108: // ESHUTDOWN    -- Cannot send after transport endpoint shutdown -- probably more of an error on our part, if we're trying to write after the socket is closed.  Probably not a critical error, though.
				case 110: // ETIMEDOUT    -- Connection timed out
				case 111: // ECONNREFUSED -- Connection refused -- We shouldn't see this one, since we're listening... Still not a critical error.
				case 112: // EHOSTDOWN    -- Host is down -- Again, we shouldn't see this, and again, not critical because it's just one connection and we still want to listen to/for others.
				case 113: // EHOSTUNREACH -- No route to host
				case 121: // EREMOTEIO    -- Rempte I/O error -- Their hard drive just blew up.
				case 125: // ECANCELED    -- Operation canceled
					$this->disconnect($socket, $sockErrNo); // disconnect before clearing error, in case someone with their own implementation wants to check for error conditions on the socket.
					break;
				default:
					//$this->logError('Socket error: ' . socket_strerror($sockErrNo));
			}
			
		}
		
		
		//check packet if he have more than one frame and procss each frame individually
		private function split_packet($length, $packet, $conn , &$data = null){
			//add PartialPacket and calculate the new $length
			if ($conn->handlingPartialPacket)
			{
				$packet                      = $conn->partialBuffer . $packet;
				$conn->handlingPartialPacket = false;
				$length                      = strlen($packet);
			}
			$fullpacket = $packet;
			$frame_pos  = 0;
			$frame_id   = 1;
			
			while ($frame_pos < $length)
			{
				$headers      = $this->extractHeaders($packet);
				$headers_size = $this->calcoffset($headers);
				$framesize    = $headers['length'] + $headers_size;
				
				//split frame from packet and process it
				$frame = substr($fullpacket, $frame_pos, $framesize);
				
				if (($message = $this->deframe($frame, $conn, $headers)) !== FALSE)
				{
					if ($conn->hasSentClose)
					{
						$this->disconnect($conn->socket);
					}
					else
					{
						return json_decode($message,true);
					}
				}
				//get the new position also modify packet data
				$frame_pos += $framesize;
				$packet = substr($fullpacket, $frame_pos);
				$frame_id++;
			}
			return null;
		}
		
		private function deframe($message, &$conn)
		{
			//echo $this->strtohex($message);
			$headers   = $this->extractHeaders($message);
			$pongReply = false;
			$willClose = false;
			switch ($headers['opcode'])
			{
				case 0:
				case 1:
				case 2:
					break;
				case 8:
					// todo: close the connection
					$conn->hasSentClose = true;
					return "";
				case 9:
					$pongReply = true;
				case 10:
					break;
				default:
					//$this->disconnect($conn); // todo: fail connection
					$willClose = true;
					break;
			}
			
			/* Deal by split_packet() as now deframe() do only one frame at a time.
			if ($conn->handlingPartialPacket) {
			$message = $conn->partialBuffer . $message;
			$conn->handlingPartialPacket = false;
			return $this->deframe($message, $conn);
			}
			*/
			
			if ($this->checkRSVBits($headers, $conn))
			{
				return false;
			}
			
			if ($willClose)
			{
				// todo: fail the connection
				return false;
			}
			
			$payload = $conn->partialMessage . $this->extractPayload($message, $headers);
			
			if ($pongReply)
			{
				$reply = $this->frame($payload, 'pong');
				socket_write($conn->socket, $reply, strlen($reply));
				return false;
			}
			if (extension_loaded('mbstring'))
			{
				if ($headers['length'] > mb_strlen($this->applyMask($headers, $payload)))
				{
					$conn->handlingPartialPacket = true;
					$conn->partialBuffer         = $message;
					return false;
				}
			}
			else
			{
				if ($headers['length'] > strlen($this->applyMask($headers, $payload)))
				{
					$conn->handlingPartialPacket = true;
					$conn->partialBuffer         = $message;
					return false;
				}
			}
			
			$payload = $this->applyMask($headers, $payload);
			
			if ($headers['fin'])
			{
				$conn->partialMessage = "";
				return $payload;
			}
			$conn->partialMessage = $payload;
			return false;
		}
		
		private function calcoffset($headers)
		{
			$offset = 2;
			if ($headers['hasmask'])
			{
				$offset += 4;
			}
			if ($headers['length'] > 65535)
			{
				$offset += 8;
			}
			elseif ($headers['length'] > 125)
			{
				$offset += 2;
			}
			return $offset;
		}
		
		private function extractHeaders($message)
		{
			$header           = array(
				'fin' => $message[0] & chr(128),
				'rsv1' => $message[0] & chr(64),
				'rsv2' => $message[0] & chr(32),
				'rsv3' => $message[0] & chr(16),
				'opcode' => ord($message[0]) & 15,
				'hasmask' => $message[1] & chr(128),
				'length' => 0,
				'mask' => ""
			);
			$header['length'] = (ord($message[1]) >= 128) ? ord($message[1]) - 128 : ord($message[1]);
			
			if ($header['length'] == 126)
			{
				if ($header['hasmask'])
				{
					$header['mask'] = $message[4] . $message[5] . $message[6] . $message[7];
				}
				$header['length'] = ord($message[2]) * 256 + ord($message[3]);
			}
			elseif ($header['length'] == 127)
			{
				if ($header['hasmask'])
				{
					$header['mask'] = $message[10] . $message[11] . $message[12] . $message[13];
				}
				$header['length'] = ord($message[2]) * 65536 * 65536 * 65536 * 256 + ord($message[3]) * 65536 * 65536 * 65536 + ord($message[4]) * 65536 * 65536 * 256 + ord($message[5]) * 65536 * 65536 + ord($message[6]) * 65536 * 256 + ord($message[7]) * 65536 + ord($message[8]) * 256 + ord($message[9]);
			}
			elseif ($header['hasmask'])
			{
				$header['mask'] = $message[2] . $message[3] . $message[4] . $message[5];
			}
			//echo $this->strtohex($message);
			//$this->printHeaders($header);
			return $header;
		}
		
		private function extractPayload($message, $headers)
		{
			$offset = 2;
			if ($headers['hasmask'])
			{
				$offset += 4;
			}
			if ($headers['length'] > 65535)
			{
				$offset += 8;
			}
			elseif ($headers['length'] > 125)
			{
				$offset += 2;
			}
			return substr($message, $offset);
		}
		
		private function applyMask($headers, $payload)
		{
			$effectiveMask = "";
			if ($headers['hasmask'])
			{
				$mask = $headers['mask'];
			}
			else
			{
				return $payload;
			}
			
			while (strlen($effectiveMask) < strlen($payload))
			{
				$effectiveMask .= $mask;
			}
			while (strlen($effectiveMask) > strlen($payload))
			{
				$effectiveMask = substr($effectiveMask, 0, -1);
			}
			return $effectiveMask ^ $payload;
		}
		private function checkRSVBits($headers, $conn) // override this method if you are using an extension where the RSV bits are used.
		{
			if (ord($headers['rsv1']) + ord($headers['rsv2']) + ord($headers['rsv3']) > 0)
			{
				//$this->disconnect($conn); // todo: fail connection
				return true;
			}
			return false;
		}
		
		private function strtohex($str)
		{
			$strout = "";
			for ($i = 0; $i < strlen($str); $i++)
			{
				$strout .= (ord($str[$i]) < 16) ? "0" . dechex(ord($str[$i])) : dechex(ord($str[$i]));
				$strout .= " ";
				if ($i % 32 == 7)
				{
					$strout .= ": ";
				}
				if ($i % 32 == 15)
				{
					$strout .= ": ";
				}
				if ($i % 32 == 23)
				{
					$strout .= ": ";
				}
				if ($i % 32 == 31)
				{
					$strout .= "\n";
				}
			}
			return $strout . "\n";
		}
		
		private function getConnBySocket($socket)
		{
			foreach ($this->connections as $conn)
			{
				if ($conn->socket == $socket)
				{
					return $conn;
				}
			}
			return null;
		}
		
		/**
		 * 将普通信息组装成websocket数据帧
		 *
		 * @param $message
		 * @param $$message, $conn, $messageType = 'text'
		 *
		 * @return string
		 */
		private function frame($message, $messageType = 'text')
		{
			
			$b1 = 128;
			switch ($messageType){
				case 'continuous':
					$b1 += 0;
					break;
				case 'text':
					$b1 += 1;
					break;
				case 'binary':
					$b1 += 2;
					break;
				case 'close':
					$b1 += 8;
					break;
				case 'ping':
					$b1 += 9;
					break;
				case 'pong':
					$b1 += 10;
					break;
			}
			
			$length      = strlen($message);
			$lengthField = "";
			if ($length < 126){
				$b2 = $length;
			}elseif ($length <= 65536){
				$b2        = 126;
				$hexLength = dechex($length);
				if (strlen($hexLength) % 2 == 1){
					$hexLength = '0' . $hexLength;
				}
				$n = strlen($hexLength) - 2;
				
				for ($i = $n; $i >= 0; $i = $i - 2){
					$lengthField = chr(hexdec(substr($hexLength, $i, 2))) . $lengthField;
				}
				while (strlen($lengthField) < 2){
					$lengthField = chr(0) . $lengthField;
				}
			}else{
				$b2        = 127;
				$hexLength = dechex($length);
				if (strlen($hexLength) % 2 == 1){
					$hexLength = '0' . $hexLength;
				}
				$n = strlen($hexLength) - 2;
				
				for ($i = $n; $i >= 0; $i = $i - 2){
					$lengthField = chr(hexdec(substr($hexLength, $i, 2))) . $lengthField;
				}
				while (strlen($lengthField) < 8){
					$lengthField = chr(0) . $lengthField;
				}
			}
			
			return chr($b1) . chr($b2) . $lengthField . $message;
		}
		
		protected function checkUri($uri){
			return true; // Override and return false if the uri is not one that you would expect.
		}
		protected function checkHost($hostName){
			return true; // Override and return false if the host is not one that you would expect.
		}
		protected function checkOrigin($origin){
			return true; // Override and return false if the origin is not one that you would expect.
		}
		protected function processExtensions($extensions){
			return ""; // return either "Sec-WebSocket-Extensions: SelectedExtensions\r\n" or return an empty string.
		}

		/**
		*socket_id签名
		*@parma $master 主机编号
		*@parma $sendType 	发送类型
		*/
		
		private function signature($master,$sendType){
			
			return "{$sendType}_{$master}";
		}

		/**
		 *检验数据类型
		 *@param $type 数据类型
		 *@param $data 数据
		 */
		private function _chekcDataType($type ,$data){
			$msg = null;
			if(is_numeric($data)){
				$msg = "int";
			}else if(is_array($data)){
				$msg = "array";
			}else if(is_float($data)){
				$msg = "array";
			}else if(is_string($data)){
				$msg = "string";
			}else if(is_object($data)){
				$msg = "object";
			}else if(is_bool($data)){
				$msg = "boolean";
			}
			if(strtolower($type) == $msg){
				return true;
			}
			return false;
		}


		public function logInfo($message)
		{
			if ($this->logLevel != 0 && $this->logLevel <= 2)
			{
				$this->log('INFO ', $message);
			}
		}
		
		protected function log($level = 'INFO', $msg)
		{
			file_put_contents("./{$level}.txt",date('Y-m-d H:i:s') . " [{$level}] {$msg}\n",FILE_APPEND);
		}
		
		public function logError($message)
		{
			if ($this->logLevel != 0 && $this->logLevel <= 4)
			{
				$this->log('ERROR', $message);
			}
		}
		
		private function dump($array){
			$data = null;
			foreach($array as $key=>$val){
				$data .= "{$key}=>{$val}|";
			}
			return $data;
		}
	}
<?php
	
	/**
	*线程池单列模式
	*/
	
	class SocketPool{
		
		private $data = []; //定义一个数组类型的线程池
		
		private $maxSize = 100; //线程池初始大小
		
		/**
		*设置初始大小
		*@param $maxSize 线程池大小
		*/
		public function setMaxSize($maxSize){
			$this->maxSize = $maxSize;
		}
		
		
		/**
		*将加入到池里
		*@param $socket_id 签名后的用户编号
		*@param $socket    用户socket对象
		*/
		public function pushPool($socket_id = null , $socket = null){
			
			if(!$this->isFull()){
				$this->data[$socket_id] = $socket;
			}else{
				return null;
			}
			
		}
		
		/**
		*获取池里指定用户数据
		*@param $socket_id 签名后的用户编号
		*/
		public function getPoolBySocketId($socket_id){
			if($this->isExtis($socket_id)){
				return $this->data[$socket_id];
			}else{
				return null;
			}
		}
		/**
		*获取池里数据
		*@param $socket_id 签名后的用户编号
		*/
		public function getAllPool(){
				return $this->data;
		}
		/**
		*删除池里数据
		*@param $socket_id 签名后的用户编号
		*/
		public function poPool($socket_id = null){
			
			if($this->isExtis($socket_id)){
				unset($this->data[$socket_id]);
			}else{
				return null;
			}
		}
		/**
		*判断是否存在池子里面
		*@param $socket_id 签名后的用户编号
		*/
		public function isExtis($socket_id){
			$flag = false;
			foreach($this->data as $key => $val){
				if($key == $socket_id){
					$flag = true;break;
				}
			}
			
			return $flag;
		}
		
		/**
		*判断是否存已满
		*/
		public function isFull(){
			if(count($this->data) < $this->maxSize){
				return false;
			}else{
				return true;
			}
			
		}

		
	}
	
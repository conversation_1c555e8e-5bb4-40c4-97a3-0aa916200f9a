<?php
	/**
	*Entity 类型
	*Date:2017-6-6 10:55
	*author: xhr
	*/
	class Entity{
		
		public  $socket    = null; //当前socket
		public  $socket_id = null; //当前socket_id
		public $path       = ""; // websocket only use Http::GET
		public $headers   = [];    // 头信
		public $socket_NO = null; //存入池子的编号
		
		private $handshake = false;
		
		
		public $partialMessage = "";
		
		public $handlingPartialPacket = false;
		
		public $sendingContinuous = false;
	
		public $hasSentClose = false;
		
		function __construct($socket_id , $socket ){
			$this->socket_id = $socket_id;
			$this->socket = $socket;
		}
		

		//设置句柄
		public function setHandshake($handshake){
			$this->handshake = $handshake;
		}
		
		public function getHandshake(){
			return $this->handshake;
		}
		
		//设置句柄
		public function setPath($path){
			$this->$path = $path;
		}
		
		public function getPath(){
			return $this->$path;
		}
		

	}
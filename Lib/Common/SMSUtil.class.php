<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-11-30
 * Time: 14:49
 */
use Lib\Common\SMS\SMS;
include_once("SMS/SMS.class.php");

class SMSUtil{
    private $_platform = '';    //平台
    private $_smsObj = '';      //短信平台对象

    private $_templateConfigArray = '';     //模板配置
    private $_defaultTemplateConfigArray = '';  //默认配置模板
    private $_ignoreTemplateConfigArray = '';   //忽视模板
    private $_templateConfigContent = ''; //短信模板内容
    private $_templateConfigSign = ''; //短信签名

    //通用验证码
    const TEMP_ID_REGISTER = '1';//【CC招车】您的验证码为{1}，请于{2}内正确输入，如非本人操作，请忽略此短信。

    //司机接收
    const  TEMP_ID_DRIVER_NEW_ORDER = '2';//【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!

    //分台接收
    const  TEMP_ID_NEW_ORDER = '3';//【CC招车】您有新的订单了,订单编号为：{1}，乘客电话{2}，请及时派单！
    const  TEMP_ID_REFUSE_ORDER = '4';//【CC招车】司机{1}拒绝了您指派的编号为{2}的订单，请重新指派该订单!
    const  TEMP_ID_ACPET_ORDER = '5';//【CC招车】司机{1}接受了您指派的编号为{2}的订单!

    //乘客接收
    const  TEMP_ID_PASSENGER_ORDER_ACPET = '6';//【CC招车】司机{1}接受了您的订单，电话：{2},车牌号{3},投诉热线{4}

    //总台接收
    const  TEMP_ID_NEW_DEPOSIT_APPLY = '7';//【CC招车】您好,{1}(联系电话:{2})于{3}向您发起了新的提现申请(提现编号:{4}),提现金额{5}元,请及时处理!
    const  TEMP_ID_DEPOSIT_RESULT = '8';//【CC招车】您好,{1}(联系电话:{2})申请的提现(提现编号:{3}),提现金额{4}元,提现{5},请知悉!

    //通用通知
    const  TEMP_ID_APPLY_DEPOSIT_SUCCESS = '9';//【CC招车】尊敬的{1}你好，您的提现申请(提现编号:{2})已提交成功,预计{3}到账！
    const  TEMP_ID_APPLY_DEPOSIT_FAILED = '10';//【CC招车】尊敬的{1}你好，您的提现申请(提现编号:{2})审核未通过,如有疑问请及时联系:{3}！
    const  TEMP_ID_PSCANCEL = '11';//【CC招车】乘客已取消了本次行程，您的订单{1}已取消，如有疑问请拨打{2}
    const  TEMP_ID_DEPOSITRESULT = '12';// 【CC招车】您的账户{1}提现{2}审核。如有疑问，请拨打：028-********
    const  TEMP_ID_PAGE_NOT_ENOUGH = '13';// 【CC招车】您的{1}数量不足{2}，请及时增购！
    const  TEMP_ID_ACCOUNT_EXAMINE = '14';// 【CC招车】尊敬的{1}用户，您在{2}申请的{3}账号，审核{4}，如有疑问请及时联系:{5}！
    const  TEMP_ID_PASSENGER_ORDER_SUCCESS = '15';// 【CC招车】用户{1}成功订购{2},客服电话{3}

    const  TEMP_ID_PAFCHECK = 114416;//司机支付年费
    const  TEMP_ID_DRPAYFAIL = 114465;//商户未开通支付功能提示司机
    const  TEMP_ID_PAKELIMIT = 112244;//套餐限额通知
    const  TEMP_ID_PAKENOTICE = 112248;//套餐提醒通知

    const  TEMP_ID_COMMONNITICE = '113039';//通用通知模板
    const  TEMP_ID_DVCANCEL = '110969';//司机订单取消提示
    const  TEMP_ID_PSPSSUCC = '110966';//司机证件审核通过提示
    const  TEMP_ID_PSPSFAIL = '110964';//司机证件审核未通过

    const  TEMP_ID_PSPSORNEW = '110961';//提示司机有新订单

//    const TEMP_ID_REGISTER = 19924;//higgses注册验证
    const TEMP_ID_ORDER_ACT = 70241;//接受订单
    const TEMP_ID_ORDER_REF = 70240;//拒绝订单
    const TEMP_ID_ORDER_NEW = 70239;//新订单
    //const TEMP_ID_REGISTER = 19924;//测试


    //营销短信
    //RONGYUNLIAN
    const TEMP_ID_MARKETING_1001 = '1001';// 【CC招车系统】[{1}]恭喜您{2}，关注微信公众号“{3}”查看详情，电询{4}，退订回N

    //掌骏传媒
    //ZHANGJUNCHUANMEI
    const TEMP_ID_MARKETING_2101 = '2101';// 【CC招车】您的{1}套餐将于{2}到期，请及时续费！
    const TEMP_ID_MARKETING_2001 = '2001';// 【CC招车系统】[{1}]已开通{2}客服{3}退订回T


    public function __construct($platform){
        $this->setPlatform($platform);
        $this->setSmsObj(SMS::factory($platform));
        $this->setTemplateConfigArray(C($platform));
        $this->setDefaultTemplateConfigArray($this->getTemplateConfigArray()['DEFAULT']);
        $this->setIgnoreTemplateConfigArray($this->getTemplateConfigArray()['IGNORE']);
        $this->setTemplateConfigContent($this->getTemplateConfigArray()['TEMPLATECONTENT']);
        $this->setTemplateConfigSign($this->getTemplateConfigArray()['SIGN']);
    }

    /**
     * 发送模板短信
     * @param to 手机号码集合,用英文逗号分开
     * @param datas 内容数据 格式为数组 例如：array('Marry','Alon')，如不需替换请填 null
     * @param $tempCategoryId 模板类型Id
     * @param $mchId 商户id
     */
    public function CheckSmsNumber($count){
      $obj=$this->_smsObj->CheckSmsNumber();
        if($obj){
            if($count>=$obj->overage){
                return true;
            }else{
                return false;
            }
        }else{
            return true;
        }
    }
    public function sendTemplateSMS($to, $datas, $tempCategoryId, $mchId)
    {
        if(!$this->_checkMobilesParamter($to)){
            return false;
        }

        if(!$this->_ignore($tempCategoryId)){
            if(!$this->_checkSmsIsOnByMchid($mchId)){
                return false;
            }
        }

        $mobilesArr = array();
        if(is_string($to)){
            $mobilesArr = explode(',', $to);
            foreach($mobilesArr as $k => $v){
                $mobilesArr[$k] = trim($v);
            }
        }

        if(is_array($to)){
            foreach($to as $k => $v){
                $mobilesArr[$k] = trim($v);
            }
        }

        $mobiles = implode(',', $mobilesArr);
        $mobilesCount = count($mobilesArr);

        $tempId = $this->getDefaultTemplateConfigArray()[$tempCategoryId];
        $mchTempR = $this->_getMchShortMessageTemplate($mchId, $tempCategoryId);
        if($mchTempR->ret){
            $tempId = $mchTempR->data['template_id'];
            $sign = $mchTempR->data['sign'];
        }

        $this->_smsObj->setMobiles($mobiles);
        $this->_smsObj->setTempId($tempId);
        $this->_smsObj->setMchId($mchId);
        $this->_smsObj->setSign($sign);
        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($this->getTemplateConfigContent()[$tempCategoryId]);

        if($this->_smsObj->getContentLen() > 300){
            return false;
        }

        //检验用户短信数量
        $smsCount = $this->_getSmsCount($this->_smsObj->getContentLen(), $mobilesCount);

        if(!$this->checkMch($mchId, $smsCount)){
            return false;
        }

        if($tempId != $this->_smsObj->getTempId()){//发送了系统通知验证
            $this->_smsObj->setMobiles($mobiles);
            $this->_smsObj->setTempId($tempId);
            $this->_smsObj->setMchId($mchId);
            $this->_smsObj->setDataArray($datas);
            $this->_smsObj->setTemplateContent($this->getTemplateConfigContent()[$tempCategoryId]);
        }

        // 发送模板短信
        //echo "Sending TemplateSMS to $to <br/>";
        $ret = $this->_smsObj->send();
        if ($ret) {
            //扣除短信数量
            $this->_reduseMchMessage($mchId, $smsCount);
        }
        //记录短信发送日志
        $this->_smsLog($mobilesArr, $this->_smsObj->getContentLen(), $this->_getSmsSingleCount($this->_smsObj->getContentLen()));
        return $ret;
    }

    /**
     * 发送模板短信
     * @param to 手机号码集合,用英文逗号分开
     * @param datas 内容数据 格式为数组 例如：array('Marry','Alon')，如不需替换请填 null
     * @param $tempCategoryId 模板类型Id
     * @param $adminId 管理员id
     */
    public function sendSystemNoticeDefaultTemplateSMS($to, $datas, $tempCategoryId, $adminId)
    {
        if(!$this->_checkMobilesParamter($to)){
            return false;
        }

        $mobilesArr = array();
        if(is_string($to)){
            $mobilesArr = explode(',', $to);
            foreach($mobilesArr as $k => $v){
                $mobilesArr[$k] = trim($v);
            }
        }

        if(is_array($to)){
            foreach($to as $k => $v){
                $mobilesArr[$k] = trim($v);
            }
        }

        $mobiles = implode(',', $mobilesArr);
        $smsCount = count($mobilesArr);

        $tempId = $this->getDefaultTemplateConfigArray()[$tempCategoryId];
        $this->_smsObj->setMobiles($mobiles);
        $this->_smsObj->setTempId($tempId);
        $this->_smsObj->setMchId($adminId);
        $this->_smsObj->setSign($this->getTemplateConfigSign());
        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($this->getTemplateConfigContent()[$tempCategoryId]);

        // 发送模板短信
        //echo "Sending TemplateSMS to $to <br/>";
        $ret = $this->_smsObj->send();
        if ($ret) {
            //扣除短信数量
            $this->_reduseMchMessage($adminId, $smsCount);
        }
        //记录短信发送日志
        $this->_smsLog($mobilesArr, $this->_smsObj->getContentLen(), $this->_getSmsSingleCount($this->_smsObj->getContentLen()));
        return $ret;
    }

    /**
     * 发送营销短信
     * @param to 单个手机号码
     * @param datas 内容数据 格式为数组 例如：array('Marry','Alon')，如不需替换请填 null
     * @param $tempCategoryId 模板类型Id
     * @param $mchId 商户id
     */
    public function sendTemplateSMSMarketing($to, $account_type, $account_id, $datas, $tempCategoryId, $mchId)
    {
        if(!$this->_checkMobileParamter($to)){
            return false;
        }

        //检验用户短信数量
        if(!$this->checkMchMarketingSms($mchId, 1)){
            return false;
        }

        $tempId = $this->getDefaultTemplateConfigArray()[$tempCategoryId];
        $mchTempR = $this->_getMchShortMessageTemplate($mchId, $tempCategoryId);
        if($mchTempR->ret){
            $tempId = $mchTempR->data['template_id'];
            $sign = $mchTempR->data['sign'];
        }

        $this->_smsObj->setMobiles($to);
        $this->_smsObj->setTempId($tempId);
        $this->_smsObj->setMchId($mchId);
        $this->_smsObj->setSign($sign);
        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($this->getTemplateConfigContent()[$tempCategoryId]);

        // 发送模板短信
        //echo "Sending TemplateSMS to $to <br/>";
        $ret = $this->_smsObj->send();

        if ($ret) {
            //扣除短信数量
            $this->_reduseMchMarketingMessage($mchId, 1);
        }
        //记录短信发送日志
        $this->_smsMarketingLog($to, $account_type, $account_id);
        return $ret;
    }

    /**
     * 发送营销短信
     * @param to 单个手机号码
     * @param datas 内容数据 格式为数组 例如：array('Marry','Alon')，如不需替换请填 null
     * @param $tempcontent 模板内容
     * @param $mchId 商户id
     */
    public function sendTemplateSMSMarketingTwo($to, $account_type, $account_id, $datas, $tempcontent, $mchId,$num)
    {
        if(!$this->_checkMobileParamter($to)){
            return false;
        }

        //检验用户短信数量
        if(!$this->checkMchMarketingSms($mchId, $num)){
            return false;
        }

        $this->_smsObj->setMobiles($to);
        $this->_smsObj->setTempId(0);
        $this->_smsObj->setMchId($mchId);
        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($tempcontent);

        // 发送模板短信
        //echo "Sending TemplateSMS to $to <br/>";
        $ret = $this->_smsObj->send();

        if ($ret) {
            //扣除短信数量
            $this->_reduseMchMarketingMessage($mchId, $num);
        }
        //记录短信发送日志
        $this->_smsMarketingLog($to, $account_type, $account_id);
        return $ret;
    }


    public function checkSMSContentLenIsALlow($mchId, $tempCategoryId, $datas, $type = \CommonDefine::SMS_TYPE_1){
        $ret = false;
        if(empty($this->_smsObj)){
            return $ret;
        }

        $tempId = $this->getDefaultTemplateConfigArray()[$tempCategoryId];
        $mchTempR = $this->_getMchShortMessageTemplate($mchId, $tempCategoryId);
        if($mchTempR->ret){
            $tempId = $mchTempR->data['template_id'];
        }

        $this->_smsObj->setTempId($tempId);
        $this->_smsObj->setMchId($mchId);
        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($this->getTemplateConfigContent()[$tempCategoryId]);
        $contentLen = $this->_smsObj->getContentLen();

        switch ($type){
            case \CommonDefine::SMS_TYPE_1:{
                if(C('SMS_CONTENT_LENTH_LIMIT')['NOTICE'] >= $contentLen){
                    $ret = true;
                }
                break;
            }
            case \CommonDefine::SMS_TYPE_2:{
                if(C('SMS_CONTENT_LENTH_LIMIT')['MARKETING'] >= $contentLen){
                    $ret = true;
                }
                break;
            }
            default:
                break;
        }

        return $ret;
    }

    public function checkSMSContentLenIsALlowTwo($marketTemplateContent, $datas, $type = \CommonDefine::SMS_TYPE_1){
        $ret = false;
        if(empty($this->_smsObj)){
            return $ret;
        }

        $this->_smsObj->setDataArray($datas);
        $this->_smsObj->setTemplateContent($marketTemplateContent);
        $contentLen = $this->_smsObj->getContentLen();

        switch ($type){
            case \CommonDefine::SMS_TYPE_1:{
                if(C('SMS_CONTENT_LENTH_LIMIT')['NOTICE'] >= $contentLen){
                    $ret = true;
                }
                break;
            }
            case \CommonDefine::SMS_TYPE_2:{
                if(C('SMS_CONTENT_LENTH_LIMIT')['MARKETING'] >= $contentLen){
                    $ret = true;
                }
                break;
            }
            default:
                break;
        }

        return $ret;
    }


    /**
     * 手机号码参数检查
     */
    private function _checkMobilesParamter($mobiles){
        if(empty($mobiles)){
            return false;
        }

        if(is_string($mobiles)){
            if(!explode(',', $mobiles)){
                return false;
            }
        }

        if(is_array($mobiles)){
            return true;
        }

        return true;
    }

    /**
     * 单个手机号码参数检查
     */
    private function _checkMobileParamter($mobile){
        if(empty($mobile)){
            return false;
        }

        if(is_string($mobile)){
            if(!explode(',', $mobile)){
                return false;
            }
        }

        if(is_array($mobile)){
            return false;
        }

        return true;
    }

    /**
     * 忽视短信
     */
    private function _ignore($tempCategoryId)
    {
        foreach ($this->getIgnoreTemplateConfigArray() as $value) {
            if ($value  == $tempCategoryId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测商户短信开关是否开启
     * @return bool
     */
    private function _checkSmsIsOnByMchid($mchid){
        $smsConfig = \CommonDefine::SMS_CONFIG_1;//默认开启
        $systemConfigArr = M('SystemConfig')->where(array('account_id'=>$mchid, 'account_type'=>CommonDefine::SYSTEM_ROLE_1,'key'=>'sms_config'))->find();
        if($systemConfigArr){
            $smsConfig = $systemConfigArr['value'];
        }
        return $smsConfig==\CommonDefine::SMS_CONFIG_1?true:false;
    }
    /**
     * 检验商户剩余短信
     * @param $mchId 商户Id
     * @param $smsCount 发送短信数量
     * @param $notice 默认发送系统短信提示
     */
    public function checkMch($mchId, $smsCount, $notice = true){
        if($smsCount < 0){
            return false;
        }

        $admin = M('Admin')->where(array('admin_id'=>$mchId))->find();
        if(!$admin){
            return false;
        }

        if($admin['shortmessage'] <= 0 || $smsCount > $admin['shortmessage']){
            return false;
        }

        $restShortmessageNumber = $admin['shortmessage'] - $smsCount;
        if($this->_isReachShortmessageNoticeLimit($restShortmessageNumber, ($smsCount>1?false:true)) && $notice){
            try{
                $this->sendSystemNoticeDefaultTemplateSMS($admin['contact_tel'], array("短信剩余".$restShortmessageNumber."条,", ""), self::TEMP_ID_PAGE_NOT_ENOUGH,1);
            }catch (Exception $e){

            }
        }

        return true;
    }

    /**
     * 检验商户剩余营销短信
     * @param $mchId 商户Id
     * @param $smsCount 发送短信数量
     * @param $notice 默认发送系统短信提示
     */
    public function checkMchMarketingSms($mchId, $smsCount, $notice = true){
        if($smsCount < 0){
            return false;
        }

        $admin = M('Admin')->where(array('admin_id'=>$mchId))->find();
        if(!$admin){
            return false;
        }

        if($admin['shortmessage_marketing'] <= 0 || $smsCount > $admin['shortmessage_marketing']){
            return false;
        }

        $restShortmessageNumber = $admin['shortmessage_marketing'] - $smsCount;
        if($this->_isReachShortmessageNoticeLimit($restShortmessageNumber, ($smsCount>1?false:true)) && $notice){
            $this->sendSystemNoticeDefaultTemplateSMS($admin['tel'], array("营销短信剩余".$restShortmessageNumber."条,", ""), self::TEMP_ID_PAGE_NOT_ENOUGH,1);
        }

        return true;
    }

    /**
     * 短信余额不足通知
     * @param int $shortMessageNumber 当前短信数量
     * @param bool $eqNumber true-必须相等；false-非必须相等
     */
    private function _isReachShortmessageNoticeLimit($shortMessageNumber, $eqNumber = false)
    {
        foreach (C('ShortMessage_Notice_Limit') as $value) {
            if($eqNumber){
                if ($value  == $shortMessageNumber) {
                    return true;
                }
            }else{
                if ($value  >= $shortMessageNumber) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 扣除商户短信条数
     * @param $mchId 商户Id
     */
    private function _reduseMchMessage($mchId, $smsCount){
        $admin = M('Admin')->where(array('admin_id'=>$mchId))->find();
        if(!$admin){
            return false;
        }

        if($admin['shortmessage'] > 0 && (intval($admin['shortmessage']) - $smsCount) >=0 && $smsCount > 0){
            $admin = M('Admin')->where(array('admin_id'=>$mchId))->save(array('shortmessage' => ($admin['shortmessage'] - $smsCount)));
            if(!$admin){
                return false;
            }else{
                return true;
            }
        }
        return false;
    }

    /**
     * 扣除商户短信条数
     * @param $mchId 商户Id
     */
    private function _reduseMchMarketingMessage($mchId, $smsCount){
        $admin = M('Admin')->where(array('admin_id'=>$mchId))->find();
        if(!$admin){
            return false;
        }

        if($admin['shortmessage_marketing'] > 0 && (intval($admin['shortmessage_marketing']) - $smsCount) >=0 && $smsCount > 0){
            $admin = M('Admin')->where(array('admin_id'=>$mchId))->save(array('shortmessage_marketing' => ($admin['shortmessage_marketing'] - $smsCount)));
            if(!$admin){
                return false;
            }else{
                return true;
            }
        }
        return false;
    }

    /**
     * 获取商户的模板Id
     * @param $mchId 商户Id
     * @param $tempId 模板Id
     * @return 模板id
     */
    private function _getMchShortMessageTemplate($mchId, $tempCategoryId){
        $ret = new \ResultModel(false);
        if(empty($mchId)){
            return $ret;
        }

        $mchShortMessageR =  M('ShortMessageMerchant')->where(array('mchid'=>$mchId, 'short_message_caetgory_id' =>$tempCategoryId))->find();
        if($mchShortMessageR){
            if(!empty($mchShortMessageR['template_id'])){
                $ret->ret = true;
                $ret->data['template_id'] = $mchShortMessageR['template_id'];
                $ret->data['sign'] = $mchShortMessageR['tag_name'];
                $ret->count = 1;
            }
        }

        return $ret;
    }

    /**
     * 发送短信记录
     * @param $mobilesArr 手机号码数组集合
     * @param $contentLen 内容长度
     * @param $deductCount 扣除数量
     */
    private function _smsLog($mobilesArr, $contentLen, $deductCount){
        $db = M('sms');
        $data['mchid'] = $this->_smsObj->getMchId();
        $data['sms_platform_id'] = $this->getPlateformId();
        $data['accept_content'] = $this->_smsObj->getSign().$this->_smsObj->getContent();
        $data['tempid'] = $this->_smsObj->getTempId();
        $data['send_time'] = date('Y-m-d H:i:s',time());
        $data['content_lenth'] = $contentLen;
        if($this->_smsObj->getErrorCode() != 0){
            $data['accept_error'] = $this->_smsObj->getErrorCode().":".$this->_smsObj->getErrorMessage();
            $deductCount = 0;
        }
        $data['deduct_count'] = $deductCount;

        foreach($mobilesArr as $k => $v){
            if(!empty($v)){
                $data['accept_tel'] = $v;
                if(!$db->add($data)){
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 发送营销短信记录
     * @param $mobile 手机号码
     * @param $account_type 用户类型
     * @param $acount_id 用户id
     */
    private function _smsMarketingLog($mobile, $account_type = 0, $account_id = 0){
        $db = M('sms_marketing');
        $data['mchid'] = $this->_smsObj->getMchId();
        $data['sms_platform_id'] = $this->getPlateformId();
        $data['accept_content'] = $this->_smsObj->getContent();
        $data['tempid'] = $this->_smsObj->getTempId();;
        $data['send_time'] = date('Y-m-d H:i:s',time());
        $data['accept_tel'] = $mobile;
        $data['account_type'] = $account_type;
        $data['account_id'] = $account_id;
        $data['status'] = $this->_smsObj->getStatus();
        if($this->_smsObj->getStatus() != \CommonDefine::SMS_SEND_STATUS_0){
            $data['accept_error'] = $this->_smsObj->getErrorCode().":".$this->_smsObj->getErrorMessage();
        }

        if(!$db->add($data)){
            return false;
        }
        return true;
    }

    /**
     * @return string
     */
    public function getPlatform()
    {
        return $this->_platform;
    }

    /**
     * @param string $platform
     */
    public function setPlatform($platform)
    {
        $this->_platform = $platform;
    }

    /**
     * @return string
     */
    public function getPlateformId()
    {
        $modeDb = M('sms_platform');
        $platformArr = $modeDb->where("tag_name = '".$this->getPlatform()."'")->find();
        if($platformArr){
            return $platformArr['sms_platform_id'];
        }

        return '';
    }

    /**
     * @return string
     */
    public function getSmsObj()
    {
        return $this->_smsObj;
    }

    /**
     * @param string $smsObj
     */
    public function setSmsObj($smsObj)
    {
        $this->_smsObj = $smsObj;
    }

    /**
     * @return string
     */
    public function getTemplateConfigArray()
    {
        return $this->_templateConfigArray;
    }

    /**
     * @param string $templateConfigArray
     */
    public function setTemplateConfigArray($templateConfigArray)
    {
        $this->_templateConfigArray = $templateConfigArray;
    }

    /**
     * @return string
     */
    public function getDefaultTemplateConfigArray()
    {
        return $this->_defaultTemplateConfigArray;
    }

    /**
     * @param string $defaultTemplateConfigArray
     */
    public function setDefaultTemplateConfigArray($defaultTemplateConfigArray)
    {
        $this->_defaultTemplateConfigArray = $defaultTemplateConfigArray;
    }

    /**
     * @return string
     */
    public function getIgnoreTemplateConfigArray()
    {
        return $this->_ignoreTemplateConfigArray;
    }

    /**
     * @param string $ignoreTemplateConfigArray
     */
    public function setIgnoreTemplateConfigArray($ignoreTemplateConfigArray)
    {
        $this->_ignoreTemplateConfigArray = $ignoreTemplateConfigArray;
    }

    /**
     * @return string
     */
    public function getTemplateConfigContent()
    {
        return $this->_templateConfigContent;
    }

    /**
     * @param string $templateConfigContent
     */
    public function setTemplateConfigContent($templateConfigContent)
    {
        $this->_templateConfigContent = $templateConfigContent;
    }

    /**
     * @return string
     */
    public function getTemplateConfigSign()
    {
        return $this->_templateConfigSign;
    }

    /**
     * @param string $templateConfigSign
     */
    public function setTemplateConfigSign($templateConfigSign)
    {
        $this->_templateConfigSign = $templateConfigSign;
    }

    private function _getSmsCount($contentLen, $mobilesCount){
        if($contentLen <= 70){
            return $mobilesCount;
        }
        $count = intval($contentLen / 67);
        if($count == $contentLen / 67){
            return $mobilesCount * $count;
        }else{
            return $mobilesCount * ($count + 1);
        }
    }

    private function _getSmsSingleCount($contentLen){
        if($contentLen <= 70){
            return 1;
        }
        $count = intval($contentLen / 67);
        if($count == $contentLen / 67){
            return $count;
        }else{
            return ($count + 1);
        }
    }
}

<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-12-08
 * Time: 14:53
 */

class EncryptionUtil{
    /**
     * 加密解密类
     * 该算法仅支持加密数字。比较适用于数据库中id字段的加密解密，以及根据数字显示url的加密。
     * <AUTHOR>
     * @version alpha
     * @加密原则 标记长度 + 补位 + 数字替换
     * @加密步骤：
     * 将a-z,A-Z,0-9 62个字符打乱，取前M(数字最大的位数)位作为 标记长度字符串，取第M+1 到第M+10位为数字替换字符串，剩余的为补位字符串
     * 1.计算数字长度n,取乱码的第n位作为标记长度。
     * 2.计算补位的长度，加密串的长度N -1 - n 为补位的长度。根据指定的算法得到补位字符串。
     * 3.根据数字替换字符串替换数字，得到数字加密字符串。
     * 标记长度字符 + 补位字符串 + 数字加密字符串 = 加密串
     * Usage:
     *   $obj = new XDeode(9);
     *   $e_txt = $obj->encode(123);
     *   echo $e_txt.'<br/>';
     *   echo $key->decode($e_txt);
     */
    private $strbase,$key,$length,$codelen,$codenums,$codeext;
    private function init($length = 9,$key = 2543.5415412812){
        $this->strbase = $this->getParameter('encryption_key');
        $this->key = $key;
        $this->length = $length;
        $this->codelen = substr($this->strbase,0,$this->length);
        $this->codenums = substr($this->strbase,$this->length,10);
        $this->codeext = substr($this->strbase,$this->length + 10);
    }


    public function authcode($code, $operation = 'DECODE'){
        self::init();
        if($operation == 'DECODE'){
            $begin = substr($code,0,1);
            $rtn = '';
            $len = strpos($this->codelen,$begin);
            if($len!== false){
                $len++;
                $arrnums = str_split(substr($code,-$len));
                foreach ($arrnums as $v) {
                    $rtn .= strpos($this->codenums,$v);
                }
            }

            return $rtn;
        }else{
            $rtn = "";
            $numslen = strlen($code);
            //密文第一位标记数字的长度
            $begin = substr($this->codelen,$numslen - 1,1);

            //密文的扩展位
            $extlen = $this->length - $numslen - 1;
            $temp = str_replace('.', '', $code / $this->key);
            $temp = substr($temp,-$extlen);

            $arrextTemp = str_split($this->codeext);
            $arrext = str_split($temp);
            foreach ($arrext as $v) {
                $rtn .= $arrextTemp[$v];
            }

            $arrnumsTemp = str_split($this->codenums);
            $arrnums = str_split($code);
            foreach ($arrnums as $v) {
                $rtn .= $arrnumsTemp[$v];
            }
            return $begin.$rtn;
        }

    }


    /**
     * $string 明文或密文
     * $operation 加密ENCODE或解密DECODE
     * $key 密钥
     * $expiry 密钥有效期
     */
    /*    public function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0) {
            $ckey_length = 4;

            $key = md5($key ? $key : $this->getParameter("encryption_key"));
            $keya = md5(substr($key, 0, 16));
            $keyb = md5(substr($key, 16, 16));
            $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5(microtime()), -$ckey_length)) : '';

            $cryptkey = $keya.md5($keya.$keyc);
            $key_length = strlen($cryptkey);

            $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0).substr(md5($string.$keyb), 0, 16).$string;
            $string_length = strlen($string);

            $result = '';
            $box = range(0, 255);

            $rndkey = array();
            for($i = 0; $i <= 255; $i++) {
                $rndkey[$i] = ord($cryptkey[$i % $key_length]);
            }

            for($j = $i = 0; $i < 256; $i++) {
                $j = ($j + $box[$i] + $rndkey[$i]) % 256;
                $tmp = $box[$i];
                $box[$i] = $box[$j];
                $box[$j] = $tmp;
            }

            for($a = $j = $i = 0; $i < $string_length; $i++) {
                $a = ($a + 1) % 256;
                $j = ($j + $box[$a]) % 256;
                $tmp = $box[$a];
                $box[$a] = $box[$j];
                $box[$j] = $tmp;
                $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
            }

            if($operation == 'DECODE') {
                if((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) {
                    return substr($result, 26);
                } else {
                    return '';
                }
            } else {
                return $keyc.str_replace('=', '', base64_encode($result));
            }

        }*/
}
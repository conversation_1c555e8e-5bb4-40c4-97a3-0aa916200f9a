<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-11-30
 * Time: 14:51
 */
namespace Lib\Common\SMS;
include_once("YTSMS.class.php");
include_once("HXSMS.class.php");
include_once("ZJCMSMS.class.php");

abstract class SMS{
    private $_mchId = '';
    private $_mobiles = '';
    private $_sign = '';
    private $_dataArray = array();
    private $_tempId = '';
    private $_templateContent = '';
    private $_status = '';
    private $_errorCode = '';    //错误编码
    private $_errorMessage = '';

     static public function factory($platform){
         switch($platform){
             case \CommonDefine::RONGLIANYUN:{
                 return new YTSMS();
                 break;
             }
             case \CommonDefine::HUAXIN:{
                 return new HXSMS();
                 break;
             }
             case \CommonDefine::ZHANGJUNCHUANMEI:{
                 return new ZJCMSMS();
                 break;
             }
             default:
                 break;
         }
         return null;
     }

    /**
     * 发送短信
     * @return bool
     */
    abstract public function send();

    /**
     * 获取短信发送内容
     * @return string
     */
    abstract public function getContent();

    /**
     * 获取短信发送内容的长度
     * @return int
     */
    abstract public function getContentLen();

    /**
     * @return string
     */
    public function getMchId()
    {
        return $this->_mchId;
    }

    /**
     * @param string $mchId
     */
    public function setMchId($mchId)
    {
        $this->_mchId = $mchId;
    }

    /**
     * @return string
     */
    public function getMobiles()
    {
        return $this->_mobiles;
    }

    /**
     * @param string $mobiles
     */
    public function setMobiles($mobiles)
    {
        $this->_mobiles = $mobiles;
    }

    /**
     * @return str
     */
    public function getSign()
    {
        return $this->_sign;
    }

    /**
     * @param str $sign
     */
    public function setSign($sign)
    {
        $this->_sign = $sign;
    }

    /**
     * @return array
     */
    public function getDataArray()
    {
        return $this->_dataArray;
    }

    /**
     * @param array $dataArray
     */
    public function setDataArray($dataArray)
    {
        $this->_dataArray = $dataArray;
    }

    /**
     * @return string
     */
    public function getTempId()
    {
        return $this->_tempId;
    }

    /**
     * @param string $tempId
     */
    public function setTempId($tempId)
    {
        $this->_tempId = $tempId;
    }

    /**
     * @return string
     */
    public function getTemplateContent()
    {
        return $this->_templateContent;
    }

    /**
     * @param string $templateContent
     */
    public function setTemplateContent($templateContent)
    {
        $this->_templateContent = $templateContent;
    }

    /**
     * @return string
     */
    public function getErrorCode()
    {
        return $this->_errorCode;
    }

    /**
     * @param string $errorCode
     */
    public function setErrorCode($errorCode)
    {
        $this->_errorCode = $errorCode;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->_status;
    }

    /**
     * @param string $status
     */
    public function setStatus($status)
    {
        $this->_status = $status;
    }

    /**
     * @return string
     */
    public function getErrorMessage()
    {
        return $this->_errorMessage;
    }

    /**
     * @param string $errorMessage
     */
    public function setErrorMessage($errorMessage)
    {
        $this->_errorMessage = $errorMessage;
    }


}
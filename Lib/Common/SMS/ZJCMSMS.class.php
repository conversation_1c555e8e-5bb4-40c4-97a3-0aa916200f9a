<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-12-16
 * Time: 19:32
 */
namespace Lib\Common\SMS;
include_once('HttpClient.class.php');

class ZJCMSMS extends SMS{
    private $_userId = '1075'; //掌骏传媒分配的企业ID
    private $_account = 'cdxgs'; //掌骏传媒分配的企业账号
    private $_passwd = 'cdxgs789';//密码
    private $_url = 'http://120.77.14.55:8888/v2sms.aspx';//接口地址
    private $CheckSmsNumber_url='http://120.77.14.55:8888/v2sms.aspx';
    private $CheckingIllegalKeywords='http://120.77.14.55:8888/v2sms.aspx';
    /**
     * 发送短信
     * @return bool
     */
    public function send(){
        $time=date('YmdHis',time());
        $mobiles = $this->getMobiles();
        $content= $this->getContent();
        if(empty($mobiles) || empty($content)){
            return false;
        }
        $sign = md5( $this->_account.$this->_passwd.$time);//MD5加密，账号+密码+时间戳
        $postData = array(
            'action' => 'send',
            'userid' => $this->_userId,
            'timestamp'=>$time,
            'sign'=> $sign,
            'mobile' => $mobiles,
            'content' => $content,
            'sendtime'=>'',
            'extno'=>'',
        );

        $pageContents = \HttpClient::quickPost($this->_url, $postData);

        $xmlObj = new \SimpleXmlElement($pageContents);
        if ($xmlObj->returnstatus=='Success'){
            $this->setStatus(\CommonDefine::SMS_SEND_STATUS_0);
            return true;
        }
        else{
            $this->setStatus(\CommonDefine::SMS_SEND_STATUS_1);
            $this->setErrorCode($xmlObj->taskID);
            $this->setErrorMessage($xmlObj->message);
            return false;
        }
    }
    public function CheckSmsNumber(){
        $time=date('YmdHis',time());
        $sign = md5( $this->_account.$this->_passwd.$time);//MD5加密，账号+密码+时间戳
        $postData = array(
            'action' => 'overage',
            'userid' => $this->_userId,
            'timestamp'=>$time,
            'sign'=> $sign,
        );
        $pageContents = \HttpClient::quickPost($this->CheckSmsNumber_url, $postData);
        $xmlObj = new \SimpleXmlElement($pageContents);
        if ($xmlObj->returnstatus=='Sucess'){
            return $xmlObj;
        }
        else{
            $this->setStatus(\CommonDefine::SMS_SEND_STATUS_1);
            $this->setErrorCode($xmlObj->taskID);
            $this->setErrorMessage($xmlObj->message);
            return false;
        }
    }
    public function CheckingIllegalKeywords($string){
        $time=date('YmdHis',time());
        $sign = md5( $this->_account.$this->_passwd.$time);//MD5加密，账号+密码+时间戳
        $postData = array(
            'action' => 'checkkeyword',
            'userid' => $this->_userId,
            'timestamp'=>$time,
            'sign'=> $sign,
            'content'=>$string,
        );
        $pageContents = \HttpClient::quickPost($this->CheckingIllegalKeywords, $postData);
        $xmlObj = new \SimpleXmlElement($pageContents);
        return $xmlObj;
    }

    /**
     * 获取短信发送内容
     * @return string
     */
    public function getContent(){
        $content = "";
        if(is_array($this->getDataArray())){
            foreach($this->getDataArray() as $key=>$val){
                if($content){
                    $content = preg_replace('/\{'.($key+1).'\}/',$val,$content);
                }else{
                    $content = preg_replace('/\{'.($key+1).'\}/',$val,$this->getTemplateContent());
                }
            }
        }else{
            $content = $this->getTemplateContent();
        }
        return $content;
    }

    /**
     * 获取短信发送内容的长度
     * @return int
     */
    public function getContentLen(){
        preg_match_all("/./us",  $this->getContent(), $match);
        return count($match[0])?count($match[0]):0;
    }
}
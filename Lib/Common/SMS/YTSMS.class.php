<?php
namespace Lib\Common\SMS;
include_once("CCPRestSDK.php");

class YTSMS extends SMS
{
    //主帐号
    private $accountSid = 'aaf98f894c7d3aca014c9465fd890ce9';//正式

    //主帐号Token
    private $accountToken = 'e4b19cfd01604945aabf3cbe9a31e321';//正式

    //应用Id
    private $appId = '8a216da85697f5420156a0835a2b05ec';//正式 变域名

    //请求地址，格式如下，不需要写https://
    //private $serverIP='sandboxapp.cloopen.com';//沙盒地址
    private $serverIP = 'app.cloopen.com';//生产

    //请求端口
    private $serverPort = '8883';

    //REST版本号
    private $softVersion = '2013-12-26';

    /**
     * 发送短信
     * @return bool
     */
    public function send(){
        $rest = new \REST($this->serverIP, $this->serverPort, $this->softVersion);
        $rest->setAccount($this->accountSid, $this->accountToken);
        $rest->setAppId($this->appId);

        // 发送模板短信
        $result = $rest->sendTemplateSMS($this->getMobiles(), $this->getDataArray(), $this->getTempId());

        if ($result == null) {
//            echo "result error!";die;
            $this->setStatus(\CommonDefine::SMS_SEND_STATUS_1);
            return false;
        }

        if ($result->statusCode != 0) {
            //TODO 添加错误处理逻辑
            $this->setStatus(\CommonDefine::SMS_SEND_STATUS_1);
            $this->setErrorCode($result->statusCode);
            $this->setErrorMessage($result->statusMsg);
            return false;
        }
        $this->setStatus(\CommonDefine::SMS_SEND_STATUS_0);
        return true;
    }

    /**
     * 获取短信发送内容
     * @return string
     */
    public function getContent(){
        $content = "";
        foreach($this->getDataArray() as $key=>$val){
            if($content){
                $content = preg_replace('/\{'.($key+1).'\}/',$val,$content);
            }else{
                $content = preg_replace('/\{'.($key+1).'\}/',$val,$this->getTemplateContent());
            }
        }
        return $content;
    }

    /**
     * 获取短信发送内容的长度
     * @return int
     */
    public function getContentLen(){
        $completeSms = $this->getSign().$this->getContent();
        preg_match_all("/./us",  $completeSms, $match);
        return count($match[0])?count($match[0]):0;
    }
}

?>

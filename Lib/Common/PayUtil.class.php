<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/8/3
 * Time: 18:47
 */
vendor("init", VENDOR_PATH . '/Ping');

class PayUtil{
    protected $mTransferNo = "";    //付款使用的商户内部订单号。
    protected $mAppId = "";         //转账使用的  app 对象的  id ，
    protected $mChannel = "";       //付款使用的第三方支付渠道名称。目前支持  wx_pub (微信公众号)、 alipay (支付宝)、  unionpay （银联电子代付）、 allinpay （通联代付）和  jdpay （京东代付）。
    protected $mOrderNo = "";       //支付使用的商户内部订单号。
    protected $mAmount = "";        //付款金额，相关渠道的限额，请查看 帮助中心 。单位为对应币种的最小货币单位，例如：人民币为分。
    protected $mType = "";          //付款类型，转账到个人用户为 b2c，转账到企业用户为 b2b（微信公众号的企业付款，仅支持 b2c）。
    protected $mCurrency = "";      //三位 ISO 货币代码，目前仅支持人民币:  cny
    protected $mRecipient = "";
    protected $mDescription = "";
    protected $mMetadata = "";
    protected $mExtra = "";
    protected $mChid = "";

    /**
     * @return string
     */
    public function getMChid()
    {
        return $this->mChid;
    }

    /**
     * @param string $mChid
     */
    public function setMChid($mChid)
    {
        $this->mChid = $mChid;
    }          // Charge 对象的 id

    public function __construct(){

        //设置 API-Key
        //\Pingpp\Pingpp::setApiKey('sk_test_eDWXnDb5ybLS94aXDC1CSGmP');//测试
        \Pingpp\Pingpp::setApiKey('********************************');
        //SDK 验证签名设置
        \Pingpp\Pingpp::setPrivateKeyPath(APP_PATH . 'Conf/rsa_private_key.pem');

        $this->mCurrency = "cny";
//        $this->mAppId = "app_n5qfT8an5mj1Oy1u";//希格斯科技
        $this->mAppId = "app_HaPiTCOe9Su1qP4O";//CC招车
        $this->mChannel = "wx_pub";
        $this->mType = "b2c";
        $this->mDescription = "提现成功";
    }

    public function createTransfer(){
        $ret = new \ResultModel(false,null,null);
        try {
            $transfer = \Pingpp\Transfer::create(
                array(
                    'amount'    => $this->mAmount * 100,// 订单总金额, 人民币单位：分（如订单总金额为 1 元，此处请填 100,企业付款最小发送金额为 1 元）
                    'order_no'  => $this->mTransferNo,// 企业转账使用的商户内部订单号。wx(新渠道)、wx_pub 规定为 1 ~ 50 位不能重复的数字字母组合、unionpay 为不 1~16 位数字
                    'currency'  => $this->mCurrency,
                    'channel'   => $this->mChannel,// 目前支持 wx(新渠道)、 wx_pub、unionpay
                    'app'       => array('id' => $this->mAppId),
                    'type'      => $this->mType,// 付款类型，当前仅支持 b2c 企业付款。
                    'recipient' => $this->mRecipient,// 接收者 id， 为用户在 wx(新渠道)、wx_pub 下的 open_id
                    'description' => $this->mDescription,
                    'extra' => array(
                        //'user_name' => 'User Name', //收款人姓名。当该参数为空，则不校验收款人姓名，选填
                        //'force_check' => false// 是否强制校验收款人姓名。仅当 user_name 参数不为空时该参数生效，选填
                    )
                )
            );
            $ret->ret = true;
            $ret->data = $transfer;
            $ret->count = 1;
        } catch (\Pingpp\Error\Base $e) {
/*            header('Status: ' . $e->getHttpStatus());
            echo($e->getHttpBody());*/
            $ret->data = $e->getHttpBody();
//            $ret->data = $e->getHttpStatus().$e->getHttpBody().$e->getJsonBody();
        }
        return $ret;
    }

    public function createRefund(){
        $ret = new \ResultModel(false,null,null);
        if(!$this->mChid){
            return $ret;
        }
        $ch = \Pingpp\Charge::retrieve($this->mChid);//ch_id 是已付款的订单号
        $re = $ch->refunds->create(
            array(
                'amount' => $this->mAmount*100,
                'description' => '申请退款'
            )
        );
        $ret->ret = true;
        $ret->data = $re;
        $ret->count = 1;
        return $ret;
    }

    public function retrieveRefund($refund){
        $ret = new \ResultModel(false,null,null);
        if(!$this->mChid){
            return $ret;
        }
        $ch = \Pingpp\Charge::retrieve($this->mChid);//ch_id 是已付款的订单号
        $re = $ch->refunds->retrieve($refund);
        $ret->ret = true;
        $ret->data = $re;
        $ret->count = 1;
        return $ret;
    }



    /**
     * @return string
     */
    public function getMTransferNo()
    {
        return $this->mTransferNo;
    }

    /**
     * @param string $mTransferNo
     */
    public function setMTransferNo($mTransferNo)
    {
        $this->mTransferNo = $mTransferNo;
    }

    /**
     * @return string
     */
    public function getMAmount()
    {
        return $this->mAmount;
    }

    /**
     * @param string $mAmount
     */
    public function setMAmount($mAmount)
    {
        $this->mAmount = $mAmount;
    }

    /**
     * @return string
     */
    public function getMRecipient()
    {
        return $this->mRecipient;
    }

    /**
     * @param string $mRecipient
     */
    public function setMRecipient($mRecipient)
    {
        $this->mRecipient = $mRecipient;
    }

    /**
     * @return string
     */
    public function getMDescription()
    {
        return $this->mDescription;
    }

    /**
     * @param string $mDescription
     */
    public function setMDescription($mDescription)
    {
        $this->mDescription = $mDescription;
    }

    /**
     * @return string
     */
    public function getMAppId()
    {
        return $this->mAppId;
    }

    /**
     * @param string $mAppId
     */
    public function setMAppId($mAppId)
    {
        $this->mAppId = $mAppId;
    }

    /**
     * @return string
     */
    public function getMType()
    {
        return $this->mType;
    }

    /**
     * @param string $mType
     */
    public function setMType($mType)
    {
        $this->mType = $mType;
    }

    /**
     * @return string
     */
    public function getMChannel()
    {
        return $this->mChannel;
    }

    /**
     * @param string $mChannel
     */
    public function setMChannel($mChannel)
    {
        $this->mChannel = $mChannel;
    }




}

?>
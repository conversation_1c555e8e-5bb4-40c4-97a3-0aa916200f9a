<?php
/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/5/12
 * Time: 16:03
 */

class WecahtParam
{
//https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN
    private $access_token = null; //商户access_token
    private $send_url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="; //调用发送接口
    private $openid = null; //发送指定目标用户openid
    private $template_id = null; //使用的模板id
    private $data = array(); //发送数据
    private $redirectUrl = null; //跳转地址

    public function setAccessToken($access_tokey){
        $this->access_token = $access_tokey;
    }

    public function setOpenId($openid){
        $this->openid = $openid;
    }

    public function setTemplateId($template_id){
        $this->template_id = $template_id;
    }

    public function setData($data){
        $this->data = json_encode($data);
    }

    public function setRedircetUrl($redirectUrl){
        $this->redirectUrl = $redirectUrl;
    }

    //获取参数
    public function getAccessToken(){
        return $this->access_token;
    }

    public function getSendUrl(){
        return $this->send_url.$this->getAccessToken();
    }

    public function getOpenId(){
        return $this->openid;
    }

    public function getTemplateId(){
        return $this->template_id;
    }

    public function getData(){
        return $this->data;
    }

    public function getRedirectUrl(){
        return $this->redirectUrl;
    }
}


<?php
include_once("WecahtParam.class.php");

/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/5/12
 * Time: 16:27
 */
class WechatTpl
{
    private $wechat = null;
    //实例化微信对象
    function __construct(){
        if(empty($this->wechat)){
            $this->getInstaces();
        }
    }

    private function getInstaces(){
        if(empty($this->wechat)){
            $this->wechat = new WecahtParam();
        }
    }

    /**
     *微信消息发送状态
     */
    public function getSendWechatStataus(){
        $falg = 0;

        if(!$this->isEmpty($this->wechat->getSendUrl())){
            throw new Exception("send url not allow not empty");
        }

        $retData = $this->curl_post($this->wechat->getSendUrl(),  $this->paramReset());
        $result = json_decode($retData,true);

        if($result['errcode'] == 0 && strtolower($result['errmsg'])=="ok"){
            $falg = 1; //模板消息发送成功
        }else if($result['errcode'] == 41001){
            $falg = 2; //缺少access_token
        }else if($result['errcode'] == 42001){
            $falg = 3; //access_token超时，请检查access_token的有效期，请参考基础支持-获取access_token中，对access_token的详细机制说明
        }else if($result['errcode'] == 40008){
            $falg = 4; //不合法的消息类型
        }else if($result['errcode'] == 40003){
            $falg = 5; //不合法的OpenID，请开发者确认OpenID（该用户）是否已关注公众号，或是否是其他公众号的OpenID
        }else if($result['errcode'] == 41009){
            $falg = 6; //缺少openid
        }else if($result['errcode'] == 48001){
            $falg = 7; //api功能未授权，请确认公众号已获得该接口，可以在公众平台官网-开发者中心页中查看接口权限
        }else if($result['errcode'] == 48001){
            $falg = 8; //invalid template_id
        }else{
            $falg = 0; //服务器异常
        }
        return $falg;
    }
    /**errcode
     *参数重组
     * @return 返回一个组好的Json
     */

    private function paramReset(){
        $str = null;
        $array = json_decode($this->wechat->getData(),true);
        if(!$this->isEmpty($array)){
            throw new Exception("param not allow empty");
        }
        $temp1 = array(
            'first' => array('value'=>array_shift($array),'color'=>"#173177"),
            'remark' => array('value'=>array_pop($array),'color'=>"#FF4040")); //取数组中的第一个和最后一个
        $temp2 = array();
        if($array){
            foreach($array as $key=>$item){
                $temp2["keyword".($key+1)] = array('value'=>$item,'color'=>"#173177");
            }
        }
        $temp = array_merge($temp1,$temp2); //合并成一个数组
        ksort($temp);
        $this->wechat->setData(array(
            'touser' => $this->wechat->getOpenId(),
            'template_id' => $this->wechat->getTemplateId(),
            'url' => $this->wechat->getRedirectUrl(),
            'data' => $temp,
        ));
        unset($temp1,$temp2,$temp);
        $str = $this->wechat->getData();
        return $str;
    }

    /**
     *微信参数设置
     * @param $access_token 商户access_token
     * @param $openid 目标用户openid
     * @param $redirectUrl 跳转地址
     * @param $template_id 模板编号
     * @param $data 模板数据数组形式
    */
    public function setWechatConfig($access_token ,$openid ,$template_id ,$data = null ,$redirectUrl = null){
        if(!$this->isEmpty($access_token)){
            throw  new Exception("Access token not allow empty");
        }
        if(!$this->isEmpty($openid)){
            throw  new Exception("openid not allow empty");
        }
        if(!$this->isEmpty($template_id)){
            throw  new Exception("template_id not allow empty");
        }
        if(!$this->isEmpty($data)){
            throw  new Exception("data not allow empty");
        }
        if($this->isEmpty($redirectUrl)){
            $this->wechat->setRedircetUrl($redirectUrl);
        }

        $this->wechat->setAccessToken($access_token);
        $this->wechat->setOpenId($openid);
        $this->wechat->setTemplateId($template_id);
        $this->wechat->setData($data);
    }

    /**
     * 发起HTTPS请求
     */
    private function curl_post($url, $data, $header = null, $post = 1)
    {
        //初始化curl
        $ch = curl_init();
        //参数设置
        $res = curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, $post);
        if ($post)
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        //连接失败
        if ($result == FALSE) {
            if ($this-> v == 'json') {
                $result = "{\"statusCode\":\"172001\",\"statusMsg\":\"网络错误\"}";
            } else {
                $result = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><Response><statusCode>172001</statusCode><statusMsg>网络错误</statusMsg></Response>";
            }
        }
        curl_close($ch);
        return $result;
    }

    private function isEmpty($val){
        if(!isset($val)){
            return false;
        }
        if(empty($val)){
            return false;
        }
        if(is_null($val)){
            return false;
        }

        if($val == null){
            return false;
        }

        return true;
    }

}
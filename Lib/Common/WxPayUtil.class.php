<?php

/**
 * Created by PhpStorm.
 * User: zeus
 * Date: 2018-01-05
 * Time: 10:05
 * Wx payment
 */
require_once "Payment/WxPay/lib/WxPay.Api.php";
require_once 'Payment/WxPay/lib/WxPay.Notify.php';

class WxPayUtil extends WxPayNotify
{
    private $_mUrl = [];
    private $_mTradeType = array(
        'JSAPI' => 'JSAPI',
        'NATIVE' => 'NATIVE',
        'APP'   => 'APP'
    );
    //支付参数
    private $_mWxAppId;
    private $_mWxPayMchid;
    private $_mWxPayKey;
    private $_mWxPayAppSecret;

    //证书
    private $_mSslCertPath;
    private $_mSslKeyPath;

    public function __construct($payConfig = null)
    {
        $this->_mUrl = array(
            'notify_url' => C("WEB_ROOT").'/Passenger/Account/doWxPayNotify',
            'url' => 111,
        );
    }

    public function init($mchid, $channel = 'wx_pub')
    {
        $payService = M('pay_service')->where('mchid = '.$mchid)->find();
        if (!empty($payService)) {
            $where = "mchid = ".$mchid;
            $where .= " AND is_del = ".\CommonDefine::IS_DEL_0;
            $where .= " AND channel = '$channel'";
            $payConfig = M('pay_config')->where($where)->find();
            if (!empty($payConfig)) {
                $this->_mWxAppId = $payConfig['appid'];
                $this->_mWxPayMchid = $payConfig['mchnumber'];
                $this->_mWxPayKey = $payConfig['key'];
                $this->_mWxPayAppSecret = $payConfig['appsecret'];
                $this->_mSslCertPath = $payConfig['sslcret_path'];
                $this->_mSslKeyPath = $payConfig['sslkey_path'];
            } else {
                return false;
            }
        } else {
            $this->_mWxAppId = WxPayConfig::APPID;
            $this->_mWxPayMchid = WxPayConfig::MCHID;
            $this->_mWxPayKey = WxPayConfig::KEY;
            $this->_mWxPayAppSecret = WxPayConfig::APPSECRET;
            $this->_mSslCertPath =  WxPayConfig::SSLCERT_PATH;
            $this->_mSslKeyPath =  WxPayConfig::SSLKEY_PATH;
        }
        return true;
    }

    public function initPaltformDefault()
    {
        $this->_mWxAppId = WxPayConfig::APPID;
        $this->_mWxPayMchid = WxPayConfig::MCHID;
        $this->_mWxPayKey = WxPayConfig::KEY;
        $this->_mWxPayAppSecret = WxPayConfig::APPSECRET;
        $this->_mSslCertPath =  WxPayConfig::SSLCERT_PATH;
        $this->_mSslKeyPath =  WxPayConfig::SSLKEY_PATH;
        return true;
    }

    /**
     * 统一支付接口
     *
     * @param [type] $openId
     * @param [type] $orderNo
     * @param [type] $content
     * @param string $extra
     * @param [type] $price
     * @param [type] $cipmchid
     * @param [type] $createTime
     * @return void
     */
    public function createCharge($openId, $orderNo, $content, $extra = '', $price, $cipmchid, $createTime = null)
    {
        try {

            if (in_array($cipmchid, C('PAY_TIMEOUT_MCH'))) {
                # 朔州长运 超时2分钟
                $timeout = 120;
            } else {
                $timeout = 300;
            }
            $input = new WxPayUnifiedOrder();
            $input->SetBody($content);
            $input->SetAttach($extra);
            $input->SetOut_trade_no($orderNo);
            $input->SetTotal_fee($price * 100);
            # 设置支付开始时间
            $timeStart = $createTime ? strtotime($createTime) : time();
            $input->SetTime_start(date("YmdHis", $timeStart));
            # 设置支付过期时间
            $timeExpire = $createTime ? strtotime($createTime) + $timeout : time() + $timeout;
            $input->SetTime_expire(date("YmdHis", $timeExpire));
            $input->SetGoods_tag("");
            $input->SetNotify_url($this->_mUrl['notify_url'].'/callback/'.$cipmchid);
            $input->SetTrade_type($this->_mTradeType["JSAPI"]);
            $input->SetOpenid($openId);
            $order = WxPayApi::unifiedOrder($input, null, $this->_mWxAppId, $this->_mWxPayMchid, $this->_mWxPayKey);
            \Log::write("微信公众号支付统一下单接口响应：" . json_encode($order), \Log::DEBUG);
            if ($order['result_code'] !== 'SUCCESS') {
                throw new Exception($order['err_code_des']);
            }
            return $order;
        } catch (Exception $e) {
            throw $e;
            return false;
        }
    }

    public function createChargeWxmini($openId, $orderNo, $content, $extra = '', $price, $cipmchid, $appId, $createTime = null)
    {
        try {
            $input = new WxPayUnifiedOrder();
            $input->SetBody($content);
            $input->SetAttach($extra);
            $input->SetOut_trade_no($orderNo);
            $input->SetTotal_fee($price * 100);
            # 设置支付开始时间
            $timeStart = $createTime ? strtotime($createTime) : time();
            $input->SetTime_start(date("YmdHis", $timeStart));
            # 设置支付过期时间
            $timeExpire = $createTime ? strtotime($createTime) + 300 : time() + 300;
            $input->SetTime_expire(date("YmdHis", $timeExpire));
            $input->SetGoods_tag("");
            $input->SetNotify_url($this->_mUrl['notify_url'].'/callback/'.$cipmchid);
            $input->SetTrade_type($this->_mTradeType["JSAPI"]);
            $input->SetOpenid($openId);
            $order = WxPayApi::unifiedOrder($input, null, $appId, $this->_mWxPayMchid, $this->_mWxPayKey);
            \Log::write("微信小程序支付统一下单接口响应：" . json_encode($order), \Log::DEBUG);
            if ($order['result_code'] !== 'SUCCESS') {
                throw $order['err_code_des'];
            }
            return $order;
        } catch (Exception $e) {
            throw $e;
            return false;
        }
    }

    /**
     *
     * 获取jsapi支付的参数
     * @param array $UnifiedOrderResult 统一支付接口返回的数据
     * @throws WxPayException
     *
     * @return mixed
     */
    public function GetJsApiParameters($UnifiedOrderResult)
    {
        if (!array_key_exists("appid", $UnifiedOrderResult)
            || !array_key_exists("prepay_id", $UnifiedOrderResult)
            || $UnifiedOrderResult['prepay_id'] == "") {
            throw new WxPayException("参数错误");
        }
        $jsapi = new WxPayJsApiPay();
        $jsapi->SetAppid($UnifiedOrderResult["appid"]);
        $timeStamp = time();
        $jsapi->SetTimeStamp("$timeStamp");
        $jsapi->SetNonceStr(WxPayApi::getNonceStr());
        $jsapi->SetPackage("prepay_id=" . $UnifiedOrderResult['prepay_id']);
        $jsapi->SetSignType("MD5");
        $jsapi->setWxPayKey($this->_mWxPayKey);
        $jsapi->SetPaySign($jsapi->MakeSign());
        $parameters = json_encode($jsapi->GetValues());
        return $parameters;
    }

    //查询订单
    public function Queryorder($transaction_id)
    {
        $input = new WxPayOrderQuery();
        $input->SetTransaction_id($transaction_id);
        $result = WxPayApi::orderQuery($input, null, $this->_mWxAppId, $this->_mWxPayMchid, $this->_mWxPayKey);
        \Log::write("query:" . json_encode($result));
        if (array_key_exists("return_code", $result)
            && array_key_exists("result_code", $result)
            && $result["return_code"] == "SUCCESS"
            && $result["result_code"] == "SUCCESS") {
            return true;
        }
        return false;
    }

    //重写回调处理函数
    public function NotifyProcess($data, &$msg)
    {
        $notfiyOutput = array();

        if (!array_key_exists("transaction_id", $data)) {
            $msg = "输入参数不正确";
            return false;
        }
        //查询订单，判断订单真实性
        if (!$this->Queryorder($data["transaction_id"])) {
            $msg = "订单查询失败";
            return false;
        }

        return true;
    }

    //退款申请
    public function createRefund($out_trade_no, $total_fee, $refund_fee)
    {
        $ret = new \ResultModel(false, null, null);
        try {
            $input = new WxPayRefund();
            $input->SetOut_trade_no($out_trade_no);
            $input->SetTotal_fee($total_fee * 100);
            $input->SetRefund_fee($refund_fee * 100);
            $input->SetOut_refund_no($out_trade_no);
            $input->SetOp_user_id($this->_mWxPayMchid);
            $result = WxPayApi::refund($input, null, $this->_mWxAppId, $this->_mWxPayMchid, $this->_mWxPayKey, $this->_mSslCertPath, $this->_mSslKeyPath);

            $ret->ret = true;
            $ret->data = $result;
            $ret->count = 1;
            \Log::write("createRefund:".$out_trade_no." 数据：" . json_encode($ret));
        } catch (Exception $e) {
            $ret->data = $e->getCode().$e->getMessage();
            return $ret;
        }
        return $ret;
    }

    //退款查询
    public function refundQuery($out_trade_no)
    {
        $ret = new \ResultModel(false, null, null);
        try {
            $input = new WxPayRefundQuery();
            $input->SetOut_trade_no($out_trade_no);
            $result = WxPayApi::refundQuery($input, null, $this->_mWxAppId, $this->_mWxPayMchid, $this->_mWxPayKey);
            \Log::write("query:" . json_encode($result));
            if (array_key_exists("return_code", $result)
                && array_key_exists("result_code", $result)
                && $result["return_code"] == "SUCCESS"
                && $result["result_code"] == "SUCCESS") {
                if (array_key_exists("refund_status_0", $result)
                    && $result['refund_status_0'] == "SUCCESS") {
                    $ret->ret = true;
                    $ret->data['status'] =  \CommonDefine::REFUND_STATUS_3;
                    $ret->count = 1;
                } elseif (array_key_exists("refund_status_0", $result)
                    && $result['refund_status_0'] == "PROCESSING") {
                    $ret->ret = true;
                    $ret->data['status'] =  \CommonDefine::REFUND_STATUS_2;
                    $ret->count = 1;
                }
            }
            return $ret;
        } catch (Exception $e) {
            $ret->data = $e->getCode().$e->getMessage();
            return $ret;
        }

    }

    //企业付款到余额
    public function createTransfer($openId, $transferNo, $amount, $desc)
    {
        $ret = new \ResultModel(false, null, null);
        try {
            $input = new WxTransferOrder();
            $input->SetPartner_trade_no($transferNo);
            $input->SetAmount($amount * 100);
            $input->SetDesc($desc);
            $input->SetCheck_name("NO_CHECK");
            $input->SetOpenid($openId);
            $order = WxPayApi::transferOrder($input, null, $this->_mWxAppId, $this->_mWxPayMchid, $this->_mWxPayKey, $this->_mSslCertPath, $this->_mSslKeyPath);
            \Log::write("transferorder:" . json_encode($order));

            $ret->ret = true;
            $ret->data = $order;
            $ret->count = 1;
        } catch (Exception $e) {
            $ret->data = $e->getCode().$e->getMessage();
            return $ret;
        }
        return $ret;
    }

    public function getWxPayKey()
    {
        return $this->_mWxPayKey;
    }

    public function getWxAppId()
    {
        return $this->_mWxAppId;
    }
}

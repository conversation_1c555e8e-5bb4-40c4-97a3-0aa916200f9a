<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * App认证
 *
 * <AUTHOR>
 */
class AppAuthCheckBehavior extends Behavior
{
    /**
     * 行为参数定义
     * @var array
     */
    protected $options = array(
        'PASSENGER_AUTH_IGNORE' => array(''),
        'PASSENGER_AUTH_ENABLE' => true,
    );

    /**
     * 入口方法
     * @param CoreAction $params
     * @return boolean
     */
    public function run(&$params)
    {
        //身份认证
        if (C('APP_AUTH_ENABLE')) {
            if ($this->_ignore()) {
                return true;
            }

            if ($params->state) {
                if ($params->isDo || $params->isGet) {
                    $result = new ResultModel(false, '身份认证失败！');
                    $params->output($result);
                } else if ($params->isPage) {
                    /*                    redirect(C('WEB_ROOT') . 'branch_login');
                                        exit;*/
                }
            } else {
                //权限验证
                if (('BRANCH_ACCESS_ENABLE')) {
                    $r = $this->_getCurrentGroupCode($params->state->user_id);
                    if ($r->ret) {
                        if ($this->_access_control($r->data)) {
                            return true;
                        } else {
                            if ($params->isDo || $params->isGet) {
                                $result = new ResultModel(false, '分台权限认证失败！');
                                $params->output($result);
                            } else if ($params->isPage) {
                                echo '分台权限认证失败！';
                                exit;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 访问控制
     * @param string $group_code 组code
     */
    private function _access_control($code)
    {
        foreach (C('BRANCH_ACCESS_RULE') as $key => $value) {
            //是否定义规则
            if (!empty($value)) {
                //判断角色
                if ($key === $code) {
                    if ($value[0] === 'not in') {
                        foreach ($value[1] as $v) {
                            $arr = explode(".", $v);
                            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                                return false;
                            }
                        }
                        return true;
                    } else {
                        foreach ($value[1] as $v) {
                            $arr = explode(".", $v);
                            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                                return true;
                            }
                        }
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 忽视方法
     */
    private function _ignore()
    {
        foreach (C('APP_AUTH_IGNORE') as $value) {
            $arr = explode(".", $value);
            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前组code
     * @param int $user_id
     */
    private function _getCurrentGroupCode($user_id)
    {
        $am = new AdminModel();
        $data = $am->where(array('admin_id' => $user_id))->field('group_id')->find();
        if ($data) {
            $magm = new MetaAdminGroupModel();
            $data = $magm->where(array('group_id' => $data['group_id']))->field('code')->find();
            if ($data) {
                $r = new ResultModel(true, $data['code']);
            } else {
                $r = new ResultModel(false, '组ID不存在');
            }
        } else {
            $r = new ResultModel(false, '用户ID不存在');
        }
        return $r;
    }

}

?>

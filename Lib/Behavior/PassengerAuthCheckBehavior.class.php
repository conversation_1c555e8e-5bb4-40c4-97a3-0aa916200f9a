<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 乘客认证
 *
 * <AUTHOR>
 */
class PassengerAuthCheckBehavior extends Behavior
{
    /**
     * 行为参数定义
     * @var array
     */
    protected $options = array(
        'PASSENGER_AUTH_IGNORE' => array(''),
        'PASSENGER_AUTH_ENABLE' => true,
    );

    /**
     * 入口方法
     * @param CoreAction $params
     * @return boolean
     */
    public function run(&$params)
    {
        if (C('PASSENGER_AUTH_ENABLE')) {
            if ($this->_ignore()) {
                return true;
            }
            if (!$params->state) {
                if ($params->isDo || $params->isGet) {
                    $result = new ResultModel(false, '请求失败：您还未登录！', null, null, ['error_code' => 4000]);
                    $params->output($result);
                } elseif ($params->isPage) {
                    // echo '会员认证失败！';
                    /* redirect(C('WEB_ROOT'));
                     exit;*/
                }
            }
        }
        return true;
    }

    /**
     * 忽视方法
     */
    private function _ignore()
    {
        foreach (C('PASSENGER_AUTH_IGNORE') as $value) {
            $arr = explode(".", $value);
            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                return true;
            }
        }
        return $this->getAuthEnabled();
    }
}

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 会员认证
 *
 * <AUTHOR>
 */
class MemberAuthCheckBehavior extends Behavior
{

    /**
     * 行为参数定义
     * @var array
     */
    protected $options = array(
        'MEMBER_AUTH_IGNORE' => array('HomePage.indexPage','HomePage.newsPage'),
        'MEMBER_AUTH_ENABLE' => true,
    );

    /**
     * 入口方法
     * @param CoreAction $params
     * @return boolean
     */
    public function run(&$params)
    {
        if (C('MEMBER_AUTH_ENABLE')) {
            if ($this->_ignore()) {
                return true;
            }

            if (!$params->state) {
                if ($params->isDo || $params->isGet) {
                    $result = new ResultModel(false, '会员认证失败！');
                    $params->output($result);
                } else if ($params->isPage) {
                    // echo '会员认证失败！';
//                    redirect(C('WEB_ROOT'));
                    //  exit;
                }
            }
        }
        return true;
    }

    /**
     * 忽视方法
     */
    private function _ignore()
    {
        foreach (C('MEMBER_AUTH_IGNORE') as $value) {
            $arr = explode(".", $value);
            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                return true;
            }
        }
        return false;
    }

}

?>

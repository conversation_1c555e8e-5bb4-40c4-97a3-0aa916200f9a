<?php


/**
 * 车主认证
 *
 * <AUTHOR>
 */
class DriverAuthCheckBehavior extends Behavior
{
    /**
     * 行为参数定义
     * @var array
     */
    protected $options = array(
        'DRIVER_AUTH_IGNORE' => array(),
        'DRIVER_AUTH_ENABLE' => true,
    );

    /**
     * 入口方法
     * @param CoreAction $params
     * @return boolean
     */
    public function run(&$params)
    {
        if (C('DRIVER_AUTH_ENABLE')) {
            if ($this->_ignore()) {
                return true;
            }

            if (!$params->state) {
                if (($params->isDo || $params->isGet) && C('ENV') != 'local') {
                    $result = new ResultModel(false, '您还未登录！');
                    $params->output($result);
                } elseif ($params->isPage) {
                    // echo '会员认证失败！';
                    //redirect(C('WEB_ROOT'). 'driver_index');
                    //exit;
                }
            } else {
                if(!$params->isPage) {
                    $driverArr = M('driver')->where('driver_id ='.$params->state->user_id.' AND from_type ='.\CommonDefine::DRIVER_TYPE_1)->find();
                    if($driverArr) {
                        if($driverArr['status'] == \CommonDefine::EXAMINE_STATUS_1) {
                            $params->state->data['status'] = \CommonDefine::EXAMINE_STATUS_1;
                            $result = new ResultModel(false, '您的账号还在审核中！');
                            $params->output($result);
                        } elseif($driverArr['status'] ==  \CommonDefine::EXAMINE_STATUS_2) {
                            $result = new ResultModel(false, '您的账号审核未通过，请重新提交资料');
                            $params->output($result);
                        } elseif($driverArr['status'] == \CommonDefine::EXAMINE_STATUS_3) {
                            $result = new ResultModel(false, '请到我的里面完善您的资料');
                            $params->output($result);
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 忽视方法
     */
    private function _ignore()
    {
        foreach (C('DRIVER_AUTH_IGNORE') as $value) {
            $arr = explode(".", $value);
            if ($arr[0] == MODULE_NAME && $arr[1] == ACTION_NAME) {
                return true;
            }
        }
        return $this->getAuthEnabled();
    }

}

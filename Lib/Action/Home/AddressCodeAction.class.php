<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 前台地址公共模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class AddressCodeAction extends HomeCoreAction
{
    /**
     * 获取热门顺风车城市
     * @return null|resultModel
     */
    public function getHotFreeRideAddressCode()
    {
        $r = new \ResultModel(false);
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $r->ret = true;
        $r->data = [
            [
                'address_id' => '510100',
                'name' => '成都',
            ],
            [
                'address_id' => '441300',
                'name' => '惠州',
            ],
            [
                'address_id' => '530300',
                'name' => '曲靖',
            ]
        ];
        $r->count = count($r->data);
        return $this->output($r);
    }

    /**
     * 获取班线起点城市列表（一维）
     * @param null $end_address_code 如果已经选择了目的地，请传目的地code
     * @param int $page
     * @param int $size
     * @return null|\resultModel
     */
    public function getLineClassStartCodeList($end_address_code = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;

        if (!empty($end_address_code)) {
            $where .= ' AND ((end_province_code = '.$end_address_code.') OR (end_city_code = '.$end_address_code.') OR (end_area_code = '.$end_address_code.'))';
        }
        $r = $this->select($where, $page, $size, null, 'LineClass', 'start_province_code, start_city_code, start_area_code');

        if ($r->ret) {
            $temp = [];
            $tempcode = [];
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['start_province_code'], $this->directlyCityArr)) {
                    //去重
                    if (in_array($item['start_area_code'], $tempcode)) {
                        continue;
                    }

                    if ($item['start_area_code']) {
                        $areaR = $this->find(array('address_id' => $item['start_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['start_province_code'], $tempcode)) {
                                continue;
                            }

                            $areaPPR = $this->find(array('address_id' => $item['start_province_code']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[] = [
                                'address_id' => $areaPPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPPR->data['name']),
                            ];
                            $tempcode[] = $areaPPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    }
                } else {
                    //去重
                    if (in_array($item['start_area_code'], $tempcode)) {
                        continue;
                    }

                    if ($item['start_area_code']) {
                        $areaR = $this->find(array('address_id' => $item['start_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['start_city_code'], $tempcode)) {
                                continue;
                            }
                            $areaPR = $this->find(array('address_id' => $areaR->data['address_pid']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[] = [
                                'address_id' => $areaPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPR->data['name']),
                            ];
                            $tempcode[] = $areaPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    } elseif ($item['start_city_code']) {
                        //去重
                        if (in_array($item['start_city_code'], $tempcode)) {
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['start_city_code']), 'GdRegion', 'address_pid,address_id,name');
                        $temp[] = [
                            'address_id' => $areaR->data['address_id'],
                            'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                        ];
                        $tempcode[] = $areaR->data['address_id'];
                    }
                }
            }
            $r->data = $temp;
        }
        $r->count = count($r->data);
        return $this->output($r);
    }


    /**
     * 获取班线结束城市列表（一维）
     * @param null $start_address_code 如果已经选择了出发地，请传出发地code
     * @param int $page
     * @param int $size
     * @return null|\resultModel
     */
    public function getLineClassEndCodeList($start_address_code = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;
        if (!empty($start_address_code)) {
            $where .= ' AND ((start_province_code = '.$start_address_code.') OR (start_city_code = '.$start_address_code.') OR (start_area_code = '.$start_address_code.'))';
        }

        $r = $this->select($where, $page, $size, null, 'LineClass', 'end_province_code, end_city_code, end_area_code', false, 'end_area_code');
        if ($r->ret) {
            $temp = [];
            $tempcode = [];
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['end_province_code'], $this->directlyCityArr)) {
                    //去重
                    if (in_array($item['end_area_code'], $tempcode) || in_array($item['end_province_code'], $tempcode)) {
                        continue;
                    }

                    if ($item['end_area_code']) {
                        $areaR = $this->find(array('address_id' => $item['end_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['end_province_code'], $tempcode)) {
                                continue;
                            }
                            $areaPPR = $this->find(array('address_id' => $item['end_province_code']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[] = [
                                'address_id' => $areaPPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPPR->data['name']),
                            ];
                            $tempcode[] = $areaPPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    }
                } else {
                    //去重
                    if (in_array($item['end_area_code'], $tempcode)) {
                        continue;
                    }

                    if ($item['end_area_code']) {
                        $areaR = $this->find(array('address_id' => $item['end_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['end_city_code'], $tempcode)) {
                                continue;
                            }
                            $areaPR = $this->find(array('address_id' => $areaR->data['address_pid']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[] = [
                                'address_id' => $areaPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPR->data['name']),
                            ];
                            $tempcode[] = $areaPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    } elseif ($item['end_city_code']) {
                        //去重
                        if (in_array($item['end_city_code'], $tempcode)) {
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['end_city_code']), 'GdRegion', 'address_pid,address_id,name');
                        $temp[] = [
                            'address_id' => $areaR->data['address_id'],
                            'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                        ];
                        $tempcode[] = $areaR->data['address_id'];
                    }
                }
            }
            $r->data = $temp;
        }
        $r->count = count($r->data);
        return $this->output($r);
    }

    /**
     *  获取班线起点城市列表（二维）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLineClassStartCode($end_address_code = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;

        if (!empty($end_address_code)) {
            $where .= ' AND ((end_province_code = '.$end_address_code.') OR (end_city_code = '.$end_address_code.') OR (end_area_code = '.$end_address_code.'))';
        }

        $temp = [];
        $r = $this->select($where, $page, $size, null, 'LineClass', 'start_province_code, start_city_code, start_area_code');
        if ($r->ret) {
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['start_province_code'], $this->directlyCityArr)) {
                    if ($item['start_area_code']) {
                        if (array_key_exists($item['start_province_code'], $temp)) {
                            //去重
                            if (in_array($item['start_province_code'], $temp[$item['start_province_code']])) {
                                continue;
                            } else {
                                $temp[$item['start_province_code']][] = $item['start_area_code'];
                            }
                        } else {
                            $temp[$item['start_province_code']][] = $item['start_area_code'];
                        }
                    }
                } else {
                    if ($item['start_area_code']) {
                        if (array_key_exists($item['start_city_code'], $temp)) {
                            //去重
                            if (in_array($item['start_area_code'], $temp[$item['start_city_code']])) {
                                continue;
                            } else {
                                $temp[$item['start_city_code']][] = $item['start_area_code'];
                            }
                        } else {
                            $temp[$item['start_city_code']][] = $item['start_area_code'];
                        }
                    }
                }
            }
        }

        $data = $this->getAddressCodeArray($temp);

        $r->data = $data;
        $r->count = count($data);

        return $this->output($r);
    }

    /**
     *  获取班线结束城市列表（二维）
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLineClassEndCode($start_address_code = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;

        if (!empty($start_address_code)) {
            $where .= ' AND ((start_province_code = '.$start_address_code.') OR (start_city_code = '.$start_address_code.') OR (start_area_code = '.$start_address_code.'))';
        }

        $temp = [];
        $r = $this->select($where, $page, $size, null, 'LineClass', 'end_province_code, end_city_code, end_area_code');
        if ($r->ret) {
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['end_province_code'], $this->directlyCityArr)) {
                    if ($item['end_area_code']) {
                        if (array_key_exists($item['end_province_code'], $temp)) {
                            //去重
                            if (in_array($item['end_province_code'], $temp[$item['end_province_code']])) {
                                continue;
                            } else {
                                $temp[$item['end_province_code']][] = $item['end_area_code'];
                            }
                        } else {
                            $temp[$item['end_province_code']][] = $item['end_area_code'];
                        }
                    }
                } else {
                    if ($item['end_area_code']) {
                        if (array_key_exists($item['end_city_code'], $temp)) {
                            //去重
                            if (in_array($item['end_area_code'], $temp[$item['end_city_code']])) {
                                continue;
                            } else {
                                $temp[$item['end_city_code']][] = $item['end_area_code'];
                            }
                        } else {
                            $temp[$item['end_city_code']][] = $item['end_area_code'];
                        }
                    }
                }
            }
        }

        $data = $this->getAddressCodeArray($temp);

        $r->data = $data;
        $r->count = count($data);

        return $this->output($r);
    }

    /**
     * 转换字符集
     */
    public function convertCharsetToUTF8($str) {
        return mb_convert_encoding($str, 'UTF-8', mb_detect_encoding($str, ['UTF-8', 'GBK', 'GB2312', 'ISO-8859-1', 'BIG5'], true));
    }

    /**
     * 定制客运出发地与目的地筛选
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function filterDingzhikeyunAddresses($filter = 'origin', $start_address_code = '', $start_name = '', $end_address_code = '', $end_name = '', $city_name = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];
        $params = array(
            'start_address_code' => $start_address_code,
            'end_address_code'   => $end_address_code,
            'start_name'         => rawurlencode($this->convertCharsetToUTF8($_GET['start_name'])),
            'end_name'           => rawurlencode($this->convertCharsetToUTF8($_GET['end_name'])),
            'start_city_name'    => rawurlencode($this->convertCharsetToUTF8($_GET['start_city_name'])),
            'end_city_name'      => rawurlencode($this->convertCharsetToUTF8($_GET['end_city_name'])),
            'city_name'          => rawurlencode($this->convertCharsetToUTF8($_GET['city_name'])),
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/dingzhikeyun/filters/{$filter}/addresses", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success'
            && !empty($results['data'])) {
            $result = new \ResultModel(true, $results['data']);
        } else {
            $result = new \ResultModel(false, $results['message']);
        }
        echo jsonp_encode($result->ret, $result->data, 0, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
        die;
    }

    /**
     * 拼车出发地与目的地筛选
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function filterPincheAddresses($filter = 'origin', $start_address_code = '', $start_name = '', $end_address_code = '', $end_name = '', $city_name = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'start_name' => rawurlencode($start_name),
            'end_address_code' => $end_address_code,
            'end_name' => rawurlencode($end_name),
            'city_name' => rawurlencode($city_name),
            'start_city_name' => rawurlencode($start_city_name),
            'end_city_name' => rawurlencode($end_city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/pinche/filters/{$filter}/addresses", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success'
            && !empty($results['data'])) {
            $result = new \ResultModel(true, $results['data']);
        } else {
            $result = new \ResultModel(false, $results['message']);
        }
        echo jsonp_encode($result->ret, $result->data, 0, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
        die;
    }

    /**
     * 包车出发地与目的地筛选
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function filterBaocheAddresses($filter = 'origin', $start_address_code = '', $start_name = '', $end_address_code = '', $end_name = '', $city_name = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'start_name' => rawurlencode($start_name),
            'end_address_code' => $end_address_code,
            'end_name' => rawurlencode($end_name),
            'city_name' => rawurlencode($city_name),
            'start_city_name' => rawurlencode($start_city_name),
            'end_city_name' => rawurlencode($end_city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/baoche/filters/{$filter}/addresses", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success'
            && !empty($results['data'])) {
            $result = new \ResultModel(true, $results['data']);
        } else {
            $result = new \ResultModel(false, $results['message']);
        }
        echo jsonp_encode($result->ret, $result->data, 0, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
        die;
    }

    /**
     * 快车出发地与目的地筛选
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function filterKuaicheAddresses($filter = 'origin', $start_address_code = '', $start_name = '', $end_address_code = '', $end_name = '', $city_name = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'start_name' => rawurlencode($start_name),
            'end_address_code' => $end_address_code,
            'end_name' => rawurlencode($end_name),
            'city_name' => rawurlencode($city_name),
            'start_city_name' => rawurlencode($start_city_name),
            'end_city_name' => rawurlencode($end_city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/kuaiche/filters/{$filter}/addresses", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success'
            && !empty($results['data'])) {
            $result = new \ResultModel(true, $results['data']);
        } else {
            $result = new \ResultModel(false, $results['message']);
        }
        echo jsonp_encode($result->ret, $result->data, 0, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
        die;
    }


    /**
     * 出租车出发地与目的地筛选
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function filterTaxiAddresses($filter = 'origin', $start_address_code = '', $start_name = '', $end_address_code = '', $end_name = '', $city_name = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'start_name' => rawurlencode($start_name),
            'end_address_code' => $end_address_code,
            'end_name' => rawurlencode($end_name),
            'city_name' => rawurlencode($city_name),
            'start_city_name' => rawurlencode($start_city_name),
            'end_city_name' => rawurlencode($end_city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/taxi/filters/{$filter}/addresses", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success'
            && !empty($results['data'])) {
            $result = new \ResultModel(true, $results['data']);
        } else {
            $result = new \ResultModel(false, $results['message']);
        }
        echo jsonp_encode($result->ret, $result->data, 0, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
        die;
    }


    /**
     *  获取班线起点城市列表（二维含乡镇名称）
     * @param int $end_address_code 第几页（默认为1）
     * @param string $end_name 结束地名（默认为空）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLineClassStartCodeListTwo($end_address_code = null, $end_name = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;

        if (!empty($end_address_code)) {
            $where .= ' AND ((end_province_code = '.$end_address_code.') OR (end_city_code = '.$end_address_code.') OR (end_area_code = '.$end_address_code.'))';
        }
        if (!empty($end_name)) {
            $end_name = trim($end_name);
            $where .= " AND end_name = '$end_name'";
        }

        $r = $this->select($where, $page, $size, null, 'LineClass', 'start_province_code, start_city_code, start_area_code,start_name');
        if ($r->ret) {
            $temp = [];
            $tempcode = [];
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['start_province_code'], $this->directlyCityArr)) {
                    if ($item['start_area_code']) {
                        //去重
                        if (in_array($item['start_area_code'], $tempcode)) {
                            if (in_array($item['start_name'], $temp[$item['start_area_code']]['township_name'])) {
                                continue;
                            }
                            $temp[$item['start_area_code']]['township_name'][] = $item['start_name'];
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['start_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['start_province_code'], $tempcode)) {
                                if (in_array($item['start_name'], $temp[$item['start_province_code']]['township_name'])) {
                                    continue;
                                }
                                $temp[$item['start_province_code']]['township_name'][] = $item['start_name'];
                                continue;
                            }

                            $areaPPR = $this->find(array('address_id' => $item['start_province_code']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[$areaPPR->data['address_id']] = [
                                'address_id' => $areaPPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPPR->data['name']),
                            ];
                            $temp[$areaPPR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaPPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    }
                } else {
                    if ($item['start_area_code']) {
                        //去重
                        if (in_array($item['start_area_code'], $tempcode)) {
                            if (in_array($item['start_name'], $temp[$item['start_area_code']]['township_name'])) {
                                continue;
                            }
                            $temp[$item['start_area_code']]['township_name'][] = $item['start_name'];
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['start_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['start_city_code'], $tempcode)) {
                                if (in_array($item['start_name'], $temp[$item['start_city_code']]['township_name'])) {
                                    continue;
                                }
                                $temp[$item['start_city_code']]['township_name'][] = $item['start_name'];
                                continue;
                            }
                            $areaPR = $this->find(array('address_id' => $areaR->data['address_pid']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[$areaPR->data['address_id']] = [
                                'address_id' => $areaPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPR->data['name']),
                            ];
                            $temp[$areaPR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['start_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    } elseif ($item['start_city_code']) {
                        //去重
                        if (in_array($item['start_city_code'], $tempcode)) {
                            if (in_array($item['start_name'], $temp[$item['start_city_code']]['township_name'])) {
                                continue;
                            }
                            $temp[$item['start_city_code']]['township_name'][] = $item['start_name'];
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['start_city_code']), 'GdRegion', 'address_pid,address_id,name');
                        $temp[$areaR->data['address_id']] = [
                            'address_id' => $areaR->data['address_id'],
                            'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                        ];
                        $temp[$areaR->data['address_id']]['township_name'][] = $item['start_name'];
                        $tempcode[] = $areaR->data['address_id'];
                    }
                }
            }
            $r->data = $temp;
        }
        $r->count = count($r->data);
        return $this->output($r);
    }

    /**
     * 获取班线结束城市列表（二维含乡镇名称）
     * @param null $start_address_code 如果已经选择了出发地，请传出发地code
     * @param null $start_name 出发地名称
     * @param int $page
     * @param int $size
     * @return null|\resultModel
     */
    public function getLineClassEndCodeListTwo($start_address_code = null, $start_name = null, $page = null, $size = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }

        $where = ' is_del = '.\CommonDefine::IS_DEL_0;
        $where .= ' AND mchid = '.$this->mchid;
        if (!empty($start_address_code)) {
            $where .= ' AND ((start_province_code = '.$start_address_code.') OR (start_city_code = '.$start_address_code.') OR (start_area_code = '.$start_address_code.'))';
        }

        if (!empty($start_name)) {
            $start_name = trim($start_name);
            $where .= " AND start_name = '$start_name'";
        }

        $r = $this->select($where, $page, $size, null, 'LineClass', 'end_province_code, end_city_code, end_area_code, end_name');
        if ($r->ret) {
            $temp = [];
            $tempcode = [];
            foreach ($r->data as $key => $item) {
                //直辖市
                if (in_array($item['end_province_code'], $this->directlyCityArr)) {
                    //去重
                    if (in_array($item['end_area_code'], $tempcode) || in_array($item['end_province_code'], $tempcode)) {
                        if (in_array($item['end_name'], $temp[$item['end_province_code']]['township_name'])) {
                            continue;
                        }
                        $temp[$item['end_province_code']]['township_name'][] = $item['end_name'];
                        continue;
                    }

                    if ($item['end_area_code']) {
                        $areaR = $this->find(array('address_id' => $item['end_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['end_province_code'], $tempcode)) {
                                if (in_array($item['end_name'], $temp[$item['end_province_code']]['township_name'])) {
                                    continue;
                                }
                                $temp[$item['end_province_code']]['township_name'][] = $item['end_name'];
                                continue;
                            }
                            $areaPPR = $this->find(array('address_id' => $item['end_province_code']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[$areaPPR->data['address_id']] = [
                                'address_id' => $areaPPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPPR->data['name']),
                            ];
                            $temp[$areaPPR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaPPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    }
                } else {
                    if ($item['end_area_code']) {
                        //去重
                        if (in_array($item['end_area_code'], $tempcode)) {
                            if (in_array($item['end_name'], $temp[$item['end_area_code']]['township_name'])) {
                                continue;
                            }
                            $temp[$item['end_area_code']]['township_name'][] = $item['end_name'];
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['end_area_code']), 'GdRegion', 'address_pid,address_id,name');
                        $lastName = substr($areaR->data['name'], (strlen($areaR->data['name']) - 3), 3);
                        if ($lastName == '区') {
                            //去重
                            if (in_array($item['end_city_code'], $tempcode)) {
                                if (in_array($item['end_name'], $temp[$item['end_city_code']]['township_name'])) {
                                    continue;
                                }
                                $temp[$item['end_city_code']]['township_name'][] = $item['end_name'];
                                continue;
                            }
                            $areaPR = $this->find(array('address_id' => $areaR->data['address_pid']), 'GdRegion', 'address_pid,address_id,name');
                            $temp[$areaPR->data['address_id']] = [
                                'address_id' => $areaPR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaPR->data['name']),
                            ];
                            $temp[$areaPR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaPR->data['address_id'];
                        } elseif ($lastName == '县') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        } elseif ($lastName == '市') {
                            $temp[$areaR->data['address_id']] = [
                                'address_id' => $areaR->data['address_id'],
                                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                            ];
                            $temp[$areaR->data['address_id']]['township_name'][] = $item['end_name'];
                            $tempcode[] = $areaR->data['address_id'];
                        }
                    } elseif ($item['end_city_code']) {
                        //去重
                        if (in_array($item['end_city_code'], $tempcode)) {
                            if (in_array($item['end_name'], $temp[$item['end_city_code']]['township_name'])) {
                                continue;
                            }
                            $temp[$item['end_city_code']]['township_name'][] = $item['end_name'];
                            continue;
                        }
                        $areaR = $this->find(array('address_id' => $item['end_city_code']), 'GdRegion', 'address_pid,address_id,name');
                        $temp[$areaR->data['address_id']] = [
                            'address_id' => $areaR->data['address_id'],
                            'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                        ];
                        $temp[$areaR->data['address_id']]['township_name'][] = $item['end_name'];
                        $tempcode[] = $areaR->data['address_id'];
                    }
                }
            }
            $r->data = $temp;
        }
        $r->count = count($r->data);
        return $this->output($r);
    }

    /**
     * @param $address_pid
     */
    private function getAddressCodeArray($address)
    {
        $ret = [];
        foreach ($address as $k => $v) {
            $areaR = $this->find(array('address_id' => $k), 'GdRegion', 'address_pid,address_id,name');
            $sub_address[] = ['address_id' => -1, 'name' => $this->removeCityOrCountyLastName($areaR->data['name'])."·".'所有区县'];
            sort($v);
            foreach ($v as $kk => $vv) {
                $areaSR = $this->find(array('address_id' => $vv), 'GdRegion', 'address_pid,address_id,name');
                $sub_address[] = [
                    'address_id' => $vv,
                    'name' => $this->removeCityOrCountyLastName($areaR->data['name'])."·".$areaSR->data['name']
                ];
            }
            $ret[] = [
                'address_id' => $k,
                'name' => $this->removeCityOrCountyLastName($areaR->data['name']),
                'sub_address' => $sub_address
            ];
            unset($sub_address);
        }
        return $ret;
    }
}

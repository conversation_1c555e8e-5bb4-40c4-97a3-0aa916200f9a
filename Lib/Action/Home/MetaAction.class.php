<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 元数据模块
 *
 * <AUTHOR>
 */
class MetaAction extends HomeCoreAction
{

    /**
     * 解析地址编号
     * @param string $code 地址编号
     */
    public function doResolutionAddress($code)
    {
        return $this->output($this->resolutionAddress($code));
    }

    /**
     * 检测地址编号是否正确
     * @param string $code 地址编号
     */
    public function doCheckingAddressCode($code)
    {
        return $this->output($this->checkingAddressCode($code));
    }

    /**
     * 获取指定父地址ID的地址列表
     * @param int $address_pid 父地址ID（默认为0，获取所有的省份）
     */
    public function getAddressList($address_pid = 0)
    {
        $r = $this->select(array('address_pid' => $address_pid), null, null, 'address_id asc', 'MetaAddress', 'address_id,name,code');
        return $this->output($r);
    }

    /**
     * 高德
     * 获取指定父地址ID的地址列表
     * @param int $address_pid 父地址ID（默认为0，获取所有的省份）
     */
    public function getGdAddressList($address_pid = 0)
    {
        $r = $this->select(array('address_pid' => $address_pid), null, null, 'address_id asc', 'GdRegion', 'address_id,name');
        return $this->output($r);
    }

    /**
     * 支持的地址列表
     * 获取指定父地址ID的地址列表
     * @param int $address_pid 父地址ID（默认为0，获取所有的省份）
     */
    public function getSupportCityAddressList($address_pid = 0, $mchid = 0)
    {
        $adminInfo=M('admin')->where(['admin_id'=>$mchid])->find();
        if($adminInfo['group_id']==3){
            $mchid=$adminInfo['parent_admin_id'];
        }
        $r = $this->select(array('address_pid' => $address_pid, 'mchid' => $mchid), null, null, 'address_id asc', 'SupportCity', 'address_id,name');
        return $this->output($r);
    }

    /**
     * 获取当前商户下的所有分台
     * @param int $mchid 父ID（商户id）
     */
    public function getBranchList($mchid = -1)
    {
        $r = $this->select(array('parent_admin_id' => $mchid), null, null, 'address_id asc', 'Admin', 'admin_id, mchname');
        return $this->output($r);
    }

    /**
     * 初始化
     */
    private function doInit()
    {

        //添加管理员组
        $this->add('MetaAdminGroup', array('code' => 'super admin', 'name' => '超级管理员'));
        $this->add('MetaAdminGroup', array('code' => 'admin', 'name' => '管理员'));
        $this->add('MetaAdminGroup', array('code' => 'insurance company', 'name' => '保险公司'));

        //添加超级管理员
        $r = $this->find(array('code' => 'super admin'), 'MetaAdminGroup', 'group_id');
        if ($r->ret) {
            $this->add('Admin', array('group_id' => $r->data['group_id'], 'account' => 'admin', 'password' => 'admin'));
        }

        //添加乘客提醒操作
        $this->add('MetaPassengerRemindAction', array('name' => '车主邀请乘客乘车', 'code' => 'invite passenger', 'tpl_path' => 'Tpl/Remind/Passenger/invitePassenger.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主接受乘客预订', 'code' => 'accept book', 'tpl_path' => 'Tpl/Remind/Passenger/acceptBook.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主拒绝乘客预订', 'code' => 'refuse book', 'tpl_path' => 'Tpl/Remind/Passenger/refuseBook.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主拒载', 'code' => 'refuse take', 'tpl_path' => 'Tpl/Remind/Passenger/refuseTake.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主确认乘客上车', 'code' => 'confirm get on', 'tpl_path' => 'Tpl/Remind/Passenger/confirmGetOn.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主清空乘客座位', 'code' => 'remove seating', 'tpl_path' => 'Tpl/Remind/Passenger/removeSeating.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '车主取消邀请乘客', 'code' => 'cancel invite', 'tpl_path' => 'Tpl/Remind/Passenger/cancelInvite.html'));
        $this->add('MetaPassengerRemindAction', array('name' => '系统通知', 'code' => 'system notification', 'tpl_path' => ''));

        //添加车主提醒操作
        $this->add('MetaDriverRemindAction', array('name' => '乘客向车主预订座位', 'code' => 'book seating', 'tpl_path' => 'Tpl/Remind/Driver/bookSeating.html'));
        $this->add('MetaDriverRemindAction', array('name' => '乘客取消预订的座位', 'code' => 'cancel book', 'tpl_path' => 'Tpl/Remind/Driver/cancelBook.html'));
        $this->add('MetaDriverRemindAction', array('name' => '乘客下车', 'code' => 'get off', 'tpl_path' => 'Tpl/Remind/Driver/getOff.html'));
        $this->add('MetaDriverRemindAction', array('name' => '乘客接受车主的邀请', 'code' => 'accept invite', 'tpl_path' => 'Tpl/Remind/Driver/acceptInvite.html'));
        $this->add('MetaDriverRemindAction', array('name' => '乘客拒绝车主的邀请', 'code' => 'refuse invite', 'tpl_path' => 'Tpl/Remind/Driver/refuseInvite.html'));
        $this->add('MetaDriverRemindAction', array('name' => '乘客评价此次乘车', 'code' => 'evaluate', 'tpl_path' => 'Tpl/Remind/Driver/evaluate.html'));
        $this->add('MetaDriverRemindAction', array('name' => '系统通知', 'code' => 'system notification', 'tpl_path' => ''));

        //添加预订订单状态元数据
        $this->add('MetaBookOrderState', array('code' => 'book seating', 'name' => '乘客预订座位'));
        $this->add('MetaBookOrderState', array('code' => 'accepted', 'name' => '车主已接受乘客预订'));
        $this->add('MetaBookOrderState', array('code' => 'got on', 'name' => '乘客已上车'));
        $this->add('MetaBookOrderState', array('code' => 'remove seating', 'name' => '车主已清空座位，等待乘客下车'));
        $this->add('MetaBookOrderState', array('code' => 'got off', 'name' => '乘客已下车，等待车主清空位置'));
        $this->add('MetaBookOrderState', array('code' => 'success', 'name' => '成功到达'));
        $this->add('MetaBookOrderState', array('code' => 'cancel book', 'name' => '乘客已取消预订'));
        $this->add('MetaBookOrderState', array('code' => 'refuse book', 'name' => '车主不接受乘客预订'));
        $this->add('MetaBookOrderState', array('code' => 'refuse take', 'name' => '车主拒载'));

        //添加邀请订单状态元数据
        $this->add('MetaInviteOrderState', array('code' => 'invite passenger', 'name' => '车主向乘客发出邀请'));
        $this->add('MetaInviteOrderState', array('code' => 'accepted', 'name' => '乘客已接受车主邀请'));
        $this->add('MetaInviteOrderState', array('code' => 'got on', 'name' => '乘客已上车'));
        $this->add('MetaInviteOrderState', array('code' => 'remove seating', 'name' => '车主已清空座位，等待乘客下车'));
        $this->add('MetaInviteOrderState', array('code' => 'got off', 'name' => '乘客已下车，等待车主清空位置'));
        $this->add('MetaInviteOrderState', array('code' => 'success', 'name' => '成功到达'));
        $this->add('MetaInviteOrderState', array('code' => 'cancel invite', 'name' => '车主已取消邀请'));
        $this->add('MetaInviteOrderState', array('code' => 'refuse invite', 'name' => '乘客不接受车主邀请'));
        $this->add('MetaInviteOrderState', array('code' => 'refuse take', 'name' => '车主拒载'));

        //添加系统默认配置
        $this->add('SystemConfig', array('key' => 'insurance money', 'value' => '2.00', 'description' => '保险金'));
        $this->add('SystemConfig', array('key' => 'monthly rent', 'value' => '10.00', 'description' => '月租'));
        $this->add('SystemConfig', array('key' => 'monthly decute', 'value' => '5', 'description' => '每月扣费时间'));
        return $this->output();
    }

    /**
     * 更新地址信息
     */
    private function doUpdateAdress()
    {
        set_time_limit(0);
        $page = 1;
        $size = 100;
        $count = 1;
        while (true) {
            $r = $this->select(array('level' => 1), $page, $size, 'address_id asc', 'MetaAddress', 'address_id');
            if (!$r->ret) {
                break;
            } else {
                foreach ($r->data as $value) {
                    if (is_null($value['code'])) {
                        $this->save('MetaAddress', array('address_id' => $value['address_id'], 'code' => intval($count . '000000')));
                    }
                    /*             更新2级        * ** */
                    $page1 = 1;
                    $size1 = 100;
                    $count1 = 1;
                    while (true) {
                        $r1 = $this->select(array('address_pid' => $value['address_id'], 'level' => 2), $page1, $size1, 'address_id asc', 'MetaAddress', 'address_id');
                        if (!$r1->ret) {
                            break;
                        } else {
                            foreach ($r1->data as $value1) {
                                if ($count1 < 10) {
                                    $str1 = $count . '0' . $count1 . '0000';
                                    $pre1 = $count . '0' . $count1;
                                } else {
                                    $str1 = $count . $count1 . '0000';
                                    $pre1 = $count . $count1;
                                }
                                if (is_null($value1['code'])) {
                                    $this->save('MetaAddress', array('address_id' => $value1['address_id'], 'code' => intval($str1)));
                                }
                                /*                                 * 更新3级* */
                                $page2 = 1;
                                $size2 = 100;
                                $count2 = 1;
                                while (true) {
                                    $r2 = $this->select(array('address_pid' => $value1['address_id'], 'level' => 3), $page2, $size2, 'address_id asc', 'MetaAddress', 'address_id');
                                    if (!$r2->ret) {
                                        break;
                                    } else {
                                        foreach ($r2->data as $value2) {
                                            if ($count2 < 10) {
                                                $str2 = $pre1 . '0' . $count2 . '00';
                                                $pre2 = $pre1 . '0' . $count2;
                                            } else {
                                                $str2 = $pre1 . $count2 . '00';
                                                $pre2 = $pre1 . $count2;
                                            }
                                            if (is_null($value2['code'])) {
                                                $this->save('MetaAddress', array('address_id' => $value2['address_id'], 'code' => intval($str2)));
                                            }
                                            /*                                             * 更新4级* */
                                            $page3 = 1;
                                            $size3 = 100;
                                            $count3 = 1;
                                            while (true) {
                                                $r3 = $this->select(array('address_pid' => $value2['address_id'], 'level' => 4), $page3, $size3, 'address_id asc', 'MetaAddress', 'address_id');
                                                if (!$r3->ret) {
                                                    break;
                                                } else {
                                                    foreach ($r3->data as $value3) {
                                                        if ($count3 < 10) {
                                                            $str3 = $pre2 . '0' . $count3;
                                                        } else {
                                                            $str3 = $pre2 . $count3;
                                                        }
                                                        if (is_null($value3['code'])) {
                                                            $this->save('MetaAddress', array('address_id' => $value3['address_id'], 'code' => intval($str3)));
                                                        }
                                                        $count3++;
                                                    }
                                                    $page3++;
                                                }
                                            }
                                            /*                                             * 更新4级* */
                                            $count2++;
                                        }
                                        $page2++;
                                    }
                                }
                                /*                                 * 更新3级* */
                                $count1++;
                            }
                            $page1++;
                        }
                    }
                    /*                     * 更新2级 */
                    $count++;
                }
                $page++;
            }
        }
    }

}

?>

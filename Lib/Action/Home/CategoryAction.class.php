<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 获取场景（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class CategoryAction extends HomeCoreAction
{
    /**
     * 返回所有该商户的拼车|包车类型
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $type type:1-拼车；2-包车；3-带货；4-代办；5-班车；6-顺风车;（默认为拼车）
     */
    public function getCategories($page = 1, $size = 200, $type = \CommonDefine::ORDER_TYPE_1)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['line_category_business_id'] = $type;

        //具体的类型查询
        $r = $this->select($where, $page, $size, 'sort,create_time asc', 'LineCategory', 'id, title, image, show_type');

        if(!$r->ret) {
            $r->data =  '即将开通该线路，敬请期待';
        }
        return $this->output($r);
    }

}

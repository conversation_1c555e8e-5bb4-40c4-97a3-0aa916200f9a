<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');

/**
 * 前台页面模块
 *
 * <AUTHOR>
 */
class HomePageAction extends HomeCoreAction
{
    /**
     * 首页
     */
    public function indexPage()
    {
        if (C('ENV') == 'develop') {
            # 测试环境
            $this->display('Tpl/Home/Index/new_index.html');
        } else {
            $this->display('Tpl/Home/Index/index.html');
        }
    }
    /**
     * 司机-载客记录
     */
    public function driverrecordPage()
    {
        if ($this->user_type === 2) {
            $o = $this->sudo('Driver\\Account', $this->state->user_id, StateModel::$DRIVER);
            $driver = $o->getDriver($this->state->user_id)->data;
            $this->assign('driver', $driver);
        } else {
            return $this->output(new ResultModel(false, '只有车主身份可以使用该功能'));
        }
        $this->display('Tpl/Home/Records/drivers.html');
    }

    /**
     * 乘客-乘车记录
     */
    public function passagerrecordPage()
    {
        if ($this->user_type === 1) {
            $o = $this->sudo('Passenger\\Account', $this->state->user_id, StateModel::$PASSENGER);
            $passenger = $o->getPassenger($this->state->user_id)->data;
            $this->assign('passenger', $passenger);
        } else {
            return $this->output(new ResultModel(false, '只有乘客身份可以使用该功能'));
        }
        $this->display('Tpl/Home/Records/passagers.html');
    }

    /**
     * 详细页
     */
    public function detailPage()
    {
        if ($this->user_type === 1) {
            $o = $this->sudo('Passenger\\Account', $this->state->user_id, StateModel::$PASSENGER);
            $passenger = $o->getPassenger($this->state->user_id)->data;
            $this->assign('passenger', $passenger);
        } elseif ($this->user_type === 2) {
            $o = $this->sudo('Driver\\Account', $this->state->user_id, StateModel::$DRIVER);
            $driver = $o->getDriver($this->state->user_id)->data;
            $this->assign('driver', $driver);
        }
        $this->display('Tpl/Home/Detail/index.html');
    }

    /**
     * 关于页
     */
    public function aboutPage()
    {
        if ($this->user_type === 1) {
            $o = $this->sudo('Passenger\\Account', $this->state->user_id, StateModel::$PASSENGER);
            $passenger = $o->getPassenger($this->state->user_id)->data;
            $this->assign('passenger', $passenger);
        } elseif ($this->user_type === 2) {
            $o = $this->sudo('Driver\\Account', $this->state->user_id, StateModel::$DRIVER);
            $driver = $o->getDriver($this->state->user_id)->data;
            $this->assign('driver', $driver);
        }
        $this->display('Tpl/Home/Detail/about.html');
    }

    /**
     * 忘记密码
     */
    public function forgetpwdpage()
    {
        $this->display('Tpl/Home/Profile/forget.html');
    }

    /**
     * 修改密码
     */
    public function uppwdpage()
    {
        if ($this->user_type === 2) {
            $o = $this->sudo('Driver\\Account', $this->state->user_id, StateModel::$DRIVER);
            $driver = $o->getDriver($this->state->user_id)->data;
            $this->assign('driver', $driver);
        } else {
            return $this->output(new ResultModel(false, '只有车主身份可以使用该功能'));
        }
        $this->display('Tpl/Home/Profile/uppwd.html');
    }

    /**
     * 我的资料
     */
    public function profilepage()
    {
        if ($this->user_type === 1) {
            $o = $this->sudo('Passenger\\Account', $this->state->user_id, StateModel::$PASSENGER);
            $passenger = $o->getPassenger($this->state->user_id)->data;
            $this->assign('passenger', $passenger);
        } else {
            return $this->output(new ResultModel(false, '只有乘客身份可以使用该功能'));
        }
        $this->display('Tpl/Home/Profile/index.html');
    }

    public function agreementPage()
    {
        $this->assign('callback', $_REQUEST['callback']);
        $this->display('Tpl/Home/Index/agreement.html');
    }
    public function agreementallPage()
    {
        $this->assign('callback', $_REQUEST['callback']);
        $this->display('Tpl/Home/Index/agreementall.html');
    }


    public function safeagreementPage()
    {
        $mchid = $this->getMchid(true);
        $mchInfo = $this->getMchInfoByMchid($mchid);
        $this->assign('mchname', $mchInfo->data['mchname']);
        $this->display('Tpl/Home/Index/safe.html');
    }

    public function passengeragreementPage()
    {
        $mchid = $this->getMchid(true);
        $mchInfo = $this->getMchInfoByMchid($mchid);
        $this->assign('mchname', $mchInfo->data['mchname']);
        $this->display('Tpl/Home/Index/p_agreement.html');
    }

    public function aboutusPage()
    {
        $this->display('Tpl/Home/Index/about.html');
    }
    public function newsPage()
    {
        $this->display('Tpl/Home/Index/news.html');
    }
}

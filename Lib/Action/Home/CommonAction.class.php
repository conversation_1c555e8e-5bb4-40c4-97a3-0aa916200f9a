<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 前台公共模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class CommonAction extends CoreAction
{
    /**
     * 获取航班信息
     * @param $ft_number
     */
    public function getFightDetail($ft_number)
    {
        $ret = new \ResultModel(false);
        if ($ft_number) {
            $ft_number = strtoupper($this->trimall($ft_number));
            $url = "http://apicloud.mob.com/flight/no/query?key=237a64034f268&name=$ft_number";
            $sDistanceData = file_get_contents($url);
            $data = json_decode($sDistanceData, true);
            if ($data['retCode'] == 200) {
                $ret->ret = true;
                $ret->data = $data['result'];
                $ret->count = 1;
            }
            /*            $url = "http://www.variflight.com/flight/fnum/$ft_number.html?AE71649A58c77";
                        $data = $this->getHtmlInfo($url);
                        var_dump($data);die;
                        if($data){
                            $ret->ret = true;
                            $ret->data = $data;
                        }*/
        }

        return $this->output($ret);
    }

    /**
     * 获取航班信息
     * @param $ft_number
     */
    public function getFightDetailTwo($ft_number)
    {
        $ret = new \ResultModel(false);
        if ($ft_number) {
            /*            $url = "http://apicloud.mob.com/flight/no/query?key=237a64034f268&name=$ft_number";
                        $sDistanceData = file_get_contents($url);
                        $data = json_decode($sDistanceData, true);
                        if($data['retCode'] == 200){
                            $ret->ret = true;
                            $ret->data = $data['result'];
                            $ret->count = 1;
                        }*/
            $url = "http://www.variflight.com/flight/fnum/$ft_number.html?AE71649A58c77";
            $data = $this->getHtmlInfo($url);
            if ($data) {
                $ret->ret = true;
                $ret->data = $data;
            }
        }

        return $this->output($ret);
    }


    /**
     * 最近使用平台的用户
     * @return null|resultModel
     */
    public function getUsePlatformInfo()
    {
        if (empty($this->mchid)) {
            return $this->output(new ResultModel(true, [], 1));
        }

        $totalUseCount = 5401645;
        $lateUseUserData = [];
        //        $useUserR = $this->select(null, 1, 10, 'id desc', 'UseUser', 'third_account,third_avatar');
        $useUserAR = M()->table('cp_use_user')
            ->distinct(true)
            ->field('third_account,third_avatar')
            ->page(1, 10)
            ->order('id desc')
            ->select();
        if (!empty($useUserAR)) {
            $lateUseUserData = $useUserAR;
        }

        $useUserStatisticsR = $this->find('', 'UseUserStatistics');
        if ($useUserStatisticsR->ret) {
            $totalUseCount = $useUserStatisticsR->data['total_use_count'];
        }

        return $this->output(new \ResultModel(true, ['total_use' => $totalUseCount, 'late_use_user' => $lateUseUserData, 'biz_support' => $this->defineMerchantNavigationMenu($this->mchid)], 1));
    }

    /**
     * 公共上传图片
     * @param file $img (图片类型 "jpg", "gif", "bmp", "jpeg", "png")
     */
    public function doCommonUpload()
    {
        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');
        $url = C('FILE_ROOT');

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_COMMON'] . DIRECTORY_SEPARATOR .md5($this->mchid);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $imgExArr = C('IMG_TYPE_EX');
        $fileArr = explode('.', $_FILES['img']['name']);
        if (empty($fileArr)) {
            $this->output(new \ResultModel(false, '上传失败'));
        }
        $imgEx = end($fileArr);
        if (!in_array($imgEx, $imgExArr)) {
            return new \ResultModel(false, '不支持该文件类型');
        }
        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;

        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        if (!move_uploaded_file($_FILES['img']['tmp_name'], $fullPath . DIRECTORY_SEPARATOR . $realName)) {
            return new \ResultModel(false, '上传失败');
        }
        $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
        $urlPath = $url .$relateFullPath;

        $size = $_FILES['driving_img']['size'];
        $size += $_FILES['driver_img']['size'];
        $size += $_FILES['driver_ID_img']['size'];
        $bytes = round($size / 1048576 * 100) / 100;
        $mchR = $this->find(array('admin_id' => $this->mchid), 'Admin');
        if (!$mchR->ret) {
            return $this->output(new \ResultModel(false, '上传失败'));
        }

        $data['usestorage'] = $mchR->data['usestorage'] - $bytes;
        $data['admin_id'] = $this->mchid;
        $mchS = $this->save('Admin', $data);
        if (!$mchS->ret) {
            return $this->output(new \ResultModel(false, '上传失败'));
        }

        return $this->output(new \ResultModel(true, ['img' => $urlPath]));
    }

    /**
     * 获取平台信息
     */
    public function getPlatformInfo()
    {
        //公共信息
        $mchR = $this->find(array('admin_id' => $this->mchid), 'Admin', 'mchname, tel');
        if ($mchR->ret) {
            $platformInfoData['mchname'] = $mchR->data['mchname'];
            $platformInfoData['tel'] = $mchR->data['tel'];
        }
        //乘客信息
        $passenger['version'] = C('VERSION_FRONTEND_INFO');

        //司机信息
        $platformInfoData['passenger'] = $passenger;

        //分台信息
        $platformInfoData['driver'] = [];

        //渠道信息
        $platformInfoData['branch'] = [];

        return $this->output(new ResultModel(true, $platformInfoData));
    }

    /**
     * 获取系统配置信息
     * @return null|resultModel
     */
    public function getMiniAppInfo()
    {
        $appData = [
            'common' => [
                'version' => 'v1.0.0',
            ],
            'biz' => [
                ''
            ],
        ];
        if (empty($this->mchid)) {
            return $this->output(new ResultModel(true, $appData, 1));
        }
        $fapiao = false;
        /**
         * @Date 2023.06.21
         * <AUTHOR>
         */
        $enabled = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/fapiao/enabled", 'get');
        $response = json_decode($enabled, true);
        if ($response['status'] == 'success'
            && $response['data']['fapiao'] == true) {
            $fapiao = true;
        }

        $mchInfo = [
            '220' => [
                'mchlogo' => 'https://www.cczhaoche.com/upload/220/logo/20190417114830.jpg',
                'mchname' => '湖南城际拼车',
                'defualt_share_png' => 'https://www.cczhaoche.com/upload/220/share/defualt_share_png.png',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',
            ],
            '1063' => [
                'mchlogo' => 'https://www.cczhaoche.com/upload/1063/logo/yjtwlx_logo.png',
                'mchname' => '搭顺车城际约车',
                'defualt_share_png' => 'https://www.cczhaoche.com/upload/1063/logo/yjtwlx_logo.png',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',
            ],
            '181' => [
                'mchlogo' => 'https://www.cczhaoche.com/upload/merchant/cczhaoche/cczhaoche_logo.png',
                'mchname' => 'CC招车系统',
                'defualt_share_png' => 'https://www.cczhaoche.com/upload/merchant/cczhaoche/cczhaoche_logo.png',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',
                'biz' => [
                    [
                        'title' => '拼车',
                        'type' => 1,
                        'enabled' => true
                    ],
                    [
                        'title' => '快车',
                        'type' => 7,
                        'enabled' => true
                    ],
                    [
                        'title' => '出租车',
                        'type' => 11,
                        'enabled' => true
                    ],
                    [
                        'title' => '包车',
                        'type' => 2,
                        'enabled' => true
                    ],
                    [
                        'title' => '定制客运',
                        'type' => 5,
                        'enabled' => true
                    ],
                    [
                        'title' => '顺风车',
                        'type' => 6,
                        'enabled' => true
                    ],
                    [
                        'title' => '带货',
                        'type' => 3,
                        'enabled' => true
                    ],
                    [
                        'title' => '代办',
                        'type' => 4,
                        'enabled' => true
                    ],
                ],
            ],
            '1091' => [
                'mchlogo' => 'https://www.cczhaoche.com/upload/merchant/lzhyc/hhcx.png',
                'mchname' => '黄河出行',
                'defualt_share_png' => 'https://www.cczhaoche.com/upload/merchant/lzhyc/hhcx.png',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',
                'biz' => [
                    [
                        'title' => '拼车',
                        'type' => 1,
                        'enabled' => false
                    ],
                    [
                        'title' => '快车',
                        'type' => 7,
                        'enabled' => false
                    ],
                    [
                        'title' => '出租车',
                        'type' => 11,
                        'enabled' => true
                    ],
                    [
                        'title' => '包车',
                        'type' => 2,
                        'enabled' => false
                    ],
                    [
                        'title' => '定制客运',
                        'type' => 5,
                        'enabled' => true
                    ],
                    [
                        'title' => '顺风车',
                        'type' => 6,
                        'enabled' => false
                    ],
                    [
                        'title' => '带货',
                        'type' => 3,
                        'enabled' => false
                    ],
                    [
                        'title' => '代办',
                        'type' => 4,
                        'enabled' => false
                    ],
                ],
            ],
            '1280' => [
                'mchlogo' => 'https://cczhaoche.oss-cn-wulanchabu.aliyuncs.com/storage/glky_logo.jpg',
                'mchname' => '古浪客运',
                'defualt_share_png' => 'https://cczhaoche.oss-cn-wulanchabu.aliyuncs.com/storage/glky_logo.jpg',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',

            ],
            '1289' => [
                'mchlogo' => 'https://cczhaoche.oss-cn-wulanchabu.aliyuncs.com/storage/czkc.png',
                'mchname' => '陈州快车',
                'defualt_share_png' => 'https://cczhaoche.oss-cn-wulanchabu.aliyuncs.com/storage/czkc.png',
                'technical_support' => 'CC招车系统',
                'technical_url' => 'https://www.cczhaoche.com',

            ]
        ];

        $mchInfoData = $mchInfo[$this->mchid];
        $mchInfoData['tel'] = '';
        $mchR = $this->find(array('admin_id' => $this->mchid), 'Admin', 'mchname, tel');
        if ($mchR->ret) {
            $mchInfoData['tel'] = $mchR->data['tel'];
            $mchInfoData['fapiao_enabled'] = $fapiao;
        }

        $appData['mch_info'] = $mchInfoData;
        return $this->output(new ResultModel(true, $appData, 1));
    }

    /**
     * 获取保险配置
     * @return null|resultModel
     */
    public function getInsuranceConfig()
    {
        if (empty($this->mchid)) {
            return $this->output(new ResultModel(false, '参数错误或未开通保险功能'));
        }

        $insurance['insurance'] = false;
        $enabled = httpRequest(C('CC_INNER_API_HOST') ."/api/inner/merchants/{$this->mchid}/insurance/enabled", 'get');
        $response = json_decode($enabled, true);
        if ($response['status'] == 'success' && $response['data']['insurance'] == true) {
            $insurance =  $response['data'];
        }
        return $this->output(new ResultModel(true, $insurance, 1));
    }

}

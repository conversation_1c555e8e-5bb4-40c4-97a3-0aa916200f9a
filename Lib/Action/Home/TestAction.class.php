<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 前台地址公共模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class TestAction extends HomeCoreAction
{
    public function getWxminiCommit(){

    }

    public function jsonEncodeWxminiExt(){
        $extJsonData = array(
            'extEnable' => true,
            'extAppid' => 'wx012299708e7b119b',
            'directCommit' => true,
            'ext' => [
                'callback' => 'J2KFC221309746013AC554571FBD180E1C8L5I61236'
            ],
            'networkTimeout' => [
                'request' => 10000
            ],
        );

        $data = array(
            'template_id' => 1,
            'ext_json' => json_encode($extJsonData),
            'user_version' => "V1.0.0",
            'user_desc' => "小程序正式上线",
        );

        $commitUrlR = httpRequest('https://api.weixin.qq.com/wxa/commit?access_token=20_fN6WM6aAO7fcVi1aEtJnDdYqvhGBWmh-JSOZw-Wdo7EthqWrSiaedGDrf2qX6kgfMmcjtn8R24FhserVu1ukZxRXRW2LDiTWFOKpTnsVjSvCLVGTtCAHybW0til79fe7UTKVBOjrPoXj2FxkQYHeAIDJRA', 'POST',$data);
        var_dump($commitUrlR);die;
    }
    
    public function gettest($num = null){
        echo $num;
        //余票扣除
        M()->startTrans();
        $lineClassTrainArr = M('LineClassTrain')
            ->lock(true)
            ->where(array('line_class_train_id' => $num))
            ->find();
        if(empty($lineClassTrainArr)){

        }

        sleep(5);
        M()->commit();
        var_dump($lineClassTrainArr);
        die;
    }

    public function testSms($order_id){
        $startCityNameR = $this->getCityNameByAddressCode(510101);
        return $this->output($startCityNameR);
    }

    public function getTestRefundQuery($order_no){
        $where = " status =".\CommonDefine::REFUND_STATUS_1." OR status =".\CommonDefine::REFUND_STATUS_2;
        $where .= " AND create_time < '".date('Y-m-d H:i:s', strtotime("-5 minutes"))."'";
        $where .= " AND create_time > '".date("Y-m-d H:i:s", strtotime("-3 month"))."'";
        $refundsAR = M()->table('cp_refunds')->where($where)->order('id desc')->select();
        if(!empty($refundsAR)){
            foreach($refundsAR as $key=>$refundA){
                $orderR = $this->find(array('order_id' => $refundA['order_id']));
                if($orderR->ret){
                    $refundQuery = new \WxPayUtil();
                    if(!$refundQuery->init($orderR->data['mchid'])){
                        continue;
                    }
                    $refundQueryR = $refundQuery->refundQuery($refundA['refund_no']);
                    if($refundQueryR->ret){
                        $refundsS = $this->save('Refunds', array('id' => $refundA['id'], 'status' => $refundQueryR->data['status']));
                    }
                }
            }
        }
    }

    /**
     * 导入Excel
     */
    public function doImportExcelData(){
        $uploadFile = $_FILES['excel_file'];
        $imgExArr = array('xls','xlsx');

        if(empty($uploadFile)){
            return $this->output(new ResultModel(false, '上传失败'));
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');

        $fileArr = explode('.', $uploadFile['name']);
        $imgEx = strtolower(end($fileArr));
        if (!in_array($imgEx, $imgExArr)) {
            return $this->output(new \ResultModel(false, '不支持该文件类型'));
        }

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_TEMP'] . DIRECTORY_SEPARATOR .md5($this->state->user_id);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;
        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        $file = $fullPath . DIRECTORY_SEPARATOR . $realName;

        if (!move_uploaded_file($uploadFile['tmp_name'], $file)) {
            return $this->output(new \ResultModel(false, '上传证件失败'));
        }

        $phpExcelUtil = new \PhpExcelUtil();
        $excelData = $phpExcelUtil->importExecl($file);
        if($excelData['error'] == 0){
            unlink($file);
            return $this->output(new \ResultModel(false, $excelData['message']));
        }else{

        }

        //加入司机

        $mchid = 116;
        $branchid = 997;
        $mobiles = '';
        $retData = [];
        $count = 0;
        foreach($excelData['data'] as $sheet=>$sheetData){
            if(!empty($sheetData['Content'])){
                foreach($sheetData['Content'] as $k => $line){
/*                    $where['start_name'] = $line[1];
                    $where['end_name'] = $line[2];*/
                    $where = ' mchid = '.$mchid;
                    $where .=  ' AND branchid = '.$branchid;
                    if(is_float($line[5])){
                        if(empty($line[5])){
                            continue;
                        }
                        $where .=  ' AND cellphone = '.$line[5];

                        $driverR = $this->find($where,'Driver');
                        if(!$driverR->ret){
                            $data['total_seating'] = 5;
                            $data['cellphone'] = $line[5];
                            $data['cellphone_f'] = $line[5];
                            $data['password'] = 123456;
                            $data['repassword'] = 123456;
                            $data['car_type'] = '通用';
                            $data['car_tail_number'] = 'xxxxxxx';
                            $data['car_register_time'] = time();
                            $data['name'] = '师傅';
                            $data['balance'] = 0;
                            $data['branchid'] = $branchid;//重点
                            $data['mchid'] = $mchid;//重点
                            $data['sort'] = 99;
                            $data['status'] = 0;
                            $data['driver_type'] = 0;
                            $driverA = $this->add('Driver', $data);
                            if($driverA->ret){
                                $lineWhere['start_name'] =  $line[1];
                                $lineWhere['end_name'] =  $line[2];
                                $lineR = $this->find($lineWhere, 'Line');
                                if($lineR->ret){
                                    $driverLineData['type'] = \CommonDefine::ORDER_TYPE_1;
                                    $driverLineData['line_id'] = $lineR->data['id'];
                                    $driverLineData['driver_id'] = $driverA->data;
                                    $driverLineData['mchid'] = $lineR->data['mchid'];
                                    $driverLineA = $this->add('DriverLine', $driverLineData);
                                }

                                $retData[] = [
                                    'driver_id' => $driverA->data,
                                    'cellphone' => $data['cellphone'],
                                ];
                                $count++;
                            }
                        }else{
                            $lineWhere['start_name'] =  $line[1];
                            $lineWhere['end_name'] =  $line[2];
                            $lineR = $this->find($lineWhere, 'Line');
                            if($lineR->ret){
                                $driverLineR = $this->find(array('driver_id' => $driverR->data['driver_id'], 'mchid' => $mchid, 'line_id' => $lineR->data['line_id']), 'DriverLine');
                                if(!$driverLineR->ret){
                                    $driverLineData['type'] = \CommonDefine::ORDER_TYPE_1;
                                    $driverLineData['line_id'] = $lineR->data['id'];
                                    $driverLineData['driver_id'] = $driverR->data['driver_id'];
                                    $driverLineData['mchid'] = $lineR->data['mchid'];
                                    $driverLineA = $this->add('DriverLine', $driverLineData);
                                }
                            }
                        }
                    }else{
                        $cellphonesArr = explode(PHP_EOL, $line[5]);
                        foreach($cellphonesArr as $cellphone){
                            if(empty($cellphone)){
                                continue;
                            }
                            $where .=  ' AND cellphone = '.$cellphone;
                            $driverR = $this->find($where,'Driver');
                            if(!$driverR->ret){
                                $data['total_seating'] = 5;
                                $data['cellphone'] = $cellphone;
                                $data['cellphone_f'] = $cellphone;
                                $data['password'] = 123456;
                                $data['repassword'] = 123456;
                                $data['car_type'] = '通用';
                                $data['car_tail_number'] = 'xxxxxxx';
                                $data['car_register_time'] = time();
                                $data['name'] = '师傅';
                                $data['balance'] = 0;
                                $data['status'] = 0;
                                $data['branchid'] = $branchid;//重点
                                $data['mchid'] = $mchid;//重点
                                $data['sort'] = 99;
                                $data['driver_type'] = 0;
                                $driverA = $this->add('Driver', $data);
                                if($driverA->ret){
                                    $lineWhere['start_name'] =  $line[1];
                                    $lineWhere['end_name'] =  $line[2];
                                    $lineR = $this->find($lineWhere, 'Line');
                                    if($lineR->ret){
                                        $driverLineData['type'] = \CommonDefine::ORDER_TYPE_1;
                                        $driverLineData['line_id'] = $lineR->data['id'];
                                        $driverLineData['driver_id'] = $driverA->data;
                                        $driverLineData['mchid'] = $lineR->data['mchid'];
                                        $driverLineA = $this->add('DriverLine', $driverLineData);
                                    }
                                    $retData[] = [
                                        'driver_id' => $driverA->data,
                                        'cellphone' => $data['cellphone'],
                                    ];
                                    $count++;
                                }
                            }else{
                                $lineWhere['start_name'] =  $line[1];
                                $lineWhere['end_name'] =  $line[2];
                                $lineR = $this->find($lineWhere, 'Line');
                                if($lineR->ret){
                                    $driverLineR = $this->find(array('driver_id' => $driverR->data['driver_id'], 'mchid' => $mchid, 'line_id' => $lineR->data['line_id']),'DriverLine');
                                    if(!$driverLineR->ret){
                                        $driverLineData['type'] = \CommonDefine::ORDER_TYPE_1;
                                        $driverLineData['line_id'] = $lineR->data['id'];
                                        $driverLineData['driver_id'] = $driverR->data['driver_id'];
                                        $driverLineData['mchid'] = $lineR->data['mchid'];
                                        $driverLineA = $this->add('DriverLine', $driverLineData);
                                    }
                                }
                            }
                        }
                    }
                }

//                var_dump($sheetData['Content']);die;
//                var_dump(explode(PHP_EOL,$sheetData['Content'][14][5])) ;die;
/*                foreach($sheetData['Content'] as $k => $line){
                    $where['start_name'] = $line[1];
                    $where['end_name'] = $line[2];
                    $where['mchid'] = 116;
                    $where['branchid'] = 997;
                    $this->find('');
                }*/
            }
        }

        return $this->output(new ResultModel(true,$retData, $count));
    }
}

?>

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');

/**
 * 监控模块
 *
 * <AUTHOR>
 */
class MonitoringPageAction extends HomeCoreAction
{

    /**
     * 监控乘客
     * @param string $user_name 乘客用户名
     */
    public function passengerPage($user_name)
    {
        $r = $this->find(array('user_name' => $user_name), 'Passenger', 'passenger_id');
        $o = $this->sudo('Passenger\\Account', $r->data['passenger_id'], StateModel::$PASSENGER);
        $passenger = $o->getPassenger($r->data['passenger_id'])->data;
        echo "passenger id:$passenger[passenger_id]<br/>";
        echo "user name:$passenger[user_name]<br/>";
        echo "cellphone:$passenger[cellphone]<br/>";
        echo "name:$passenger[name]<br/>";
        if ($passenger['state'] == 1) {
            $state = "车上";
        } else if ($passenger['state'] == 2) {
            $state = "下车";
        } else {
            $state = "隐身";
        }
        echo "passenger state:$state<br/>";
        if ($passenger['drive_mode'] == 1) {
            $drive_mode = "A地到B地";
            $start_address = $passenger['start_address'];
            $end_address = $passenger['end_address'];
            $drive_route = "$start_address[province]$start_address[city]$start_address[country]$start_address[town]--$end_address[province]$end_address[city]$end_address[country]$end_address[town]";
        } else {
            $drive_mode = "出租车";
            $address = $passenger['address'];
            $drive_route = "$address[province]$address[city]$address[country]$address[town]";
        }
        echo "drive mode:$drive_mode<br/>";
        echo "drive route:$drive_route<br/>";
        echo "ride count:$passenger[ride_count]<br/>";
        echo "start time:$passenger[start_time]<br/>";
        $current_location = getAddressDescription($passenger['longitude'], $passenger['latitude']);
        $current_location = $current_location->result;
        echo "current location: $current_location->formatted_address<br/>";
        echo "orders:<br/>";
        $orders = $this->select(array('passenger_id' => $passenger['passenger_id']), 1, 10, 'create_time desc', 'Order', 'order_id,state,type,driver_id,update_time');
        if ($orders->ret) {
            foreach ($orders->data as $value) {
                $r = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'name');
                $driver_name = $r->data['name'];
                if ($value['type'] == 1) {
                    $type = '预订';
                    $t = $this->find(array('state_id' => $value['state']), 'MetaBookOrderState', 'name');
                    if ($t->ret) {
                        $order_state = $t->data['name'];
                    }
                } else {
                    $type = '邀请';
                    $t = $this->find(array('state_id' => $value['state']), 'MetaInviteOrderState', 'name');
                    if ($t->ret) {
                        $order_state = $t->data['name'];
                    }
                }
                echo '<a href="#">' . $value['order_id'] . '</a>    ';
                $debug_state = $this->convertState($value['state'], $value['type']);
                echo "$driver_name  $type   $value[state]   $debug_state   $order_state  $value[update_time]<br/>";
                $history_state = $this->select(array('order_id' => $value['order_id']), null, null, 'create_time desc', 'OrderHistoryState', 'id,state_id,type,create_time');
                if ($history_state->ret) {
                    foreach ($history_state->data as $value) {
                        if ($value['type'] == 1) {
                            $type = '预订';
                            $t = $this->find(array('state_id' => $value['state_id']), 'MetaBookOrderState', 'name');
                            if ($t->ret) {
                                $order_state = $t->data['name'];
                            }
                        } else {
                            $type = '邀请';
                            $t = $this->find(array('state_id' => $value['state_id']), 'MetaInviteOrderState', 'name');
                            if ($t->ret) {
                                $order_state = $t->data['name'];
                            }
                        }
                        echo "&nbsp;&nbsp;&nbsp;&nbsp;$value[id]    $order_state    $value[create_time]<br/>";
                    }
                }
            }
        }
        echo '<script language=javascript>
var int=self.setInterval("clock()",5000);
function clock()
  {
  window.location.reload();
  }
</script>';
    }

    /**
     * 监控司机
     * @param string $user_name 司机用户名
     */
    public function driverPage($user_name)
    {
        $r = $this->find(array('user_name' => $user_name), 'Driver', 'driver_id');
        $o = $this->sudo('Driver\\Account', $r->data['driver_id'], StateModel::$DRIVER);
        $driver = $o->getDriver($r->data['driver_id'])->data;
        echo "driver id:$driver[driver_id]<br/>";
        echo "user name:$driver[user_name]<br/>";
        echo "cellphone:$driver[cellphone]<br/>";
        echo "name:$driver[name]<br/>";
        if ($driver['state'] == 1) {
            $state = "等候";
        } else if ($driver['state'] == 2) {
            $state = "路上";
        } else {
            $state = "隐身";
        }
        echo "driver state:$state<br/>";
        if ($driver['drive_mode'] == 1) {
            $drive_mode = "A地到B地";
            $start_address = $driver['start_address'];
            $end_address = $driver['end_address'];
            $drive_route = "$start_address[province]$start_address[city]$start_address[country]$start_address[town]--$end_address[province]$end_address[city]$end_address[country]$end_address[town]";
        } else {
            $drive_mode = "出租车";
            $address = $driver['address'];
            $drive_route = "$address[province]$address[city]$address[country]$address[town]";
        }
        echo "drive mode:$drive_mode<br/>";
        echo "drive route:$drive_route<br/>";
        echo "start time:$driver[start_time]<br/>";
        echo "total seating:$driver[total_seating]<br/>";
        echo "residual seating:$driver[residual_seating]<br/>";
        $current_location = getAddressDescription($driver['longitude'], $driver['latitude']);
        $current_location = $current_location->result;
        echo "current location: $current_location->formatted_address<br/>";
        echo "orders:<br/>";
        $orders = $this->select(array('driver_id' => $driver['driver_id']), 1, 10, 'create_time desc', 'Order', 'order_id,state,type,passenger_id,update_time');
        if ($orders->ret) {
            foreach ($orders->data as $value) {
                $r = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger', 'user_name');
                $passenger_user_name = $r->data['user_name'];
                if ($value['type'] == 1) {
                    $type = '预订';
                    $t = $this->find(array('state_id' => $value['state']), 'MetaBookOrderState', 'name');
                    if ($t->ret) {
                        $order_state = $t->data['name'];
                    }
                } else {
                    $type = '邀请';
                    $t = $this->find(array('state_id' => $value['state']), 'MetaInviteOrderState', 'name');
                    if ($t->ret) {
                        $order_state = $t->data['name'];
                    }
                }
                echo '<a href="#">' . $value['order_id'] . '</a>    ';
                $debug_state = $this->convertState($value['state'], $value['type']);
                echo "$passenger_user_name  $type   $value[state]   $debug_state   $order_state  $value[update_time]<br/>";
                $history_state = $this->select(array('order_id' => $value['order_id']), null, null, 'create_time desc', 'OrderHistoryState', 'id,state_id,type,create_time');
                if ($history_state->ret) {
                    foreach ($history_state->data as $value) {
                        if ($value['type'] == 1) {
                            $type = '预订';
                            $t = $this->find(array('state_id' => $value['state_id']), 'MetaBookOrderState', 'name');
                            if ($t->ret) {
                                $order_state = $t->data['name'];
                            }
                        } else {
                            $type = '邀请';
                            $t = $this->find(array('state_id' => $value['state_id']), 'MetaInviteOrderState', 'name');
                            if ($t->ret) {
                                $order_state = $t->data['name'];
                            }
                        }
                        echo "&nbsp;&nbsp;&nbsp;&nbsp;$value[id]    $order_state    $value[create_time]<br/>";
                    }
                }
            }
        }
        echo '<script language=javascript>
var int=self.setInterval("clock()",5000);
function clock()
  {
  window.location.reload();
  }
</script>';
    }

    private function convertState($state, $type)
    {
        switch ($state) {
            case 1:
                if ($type == 1) {
                    return '乘客预订';
                } else {
                    return "车主邀请";
                }
            case 2:
                if ($type == 1) {
                    return '车主同意';
                } else {
                    return '乘客同意';
                }
            case 3:
                return '车主确认乘客上车';
            case 4:
                return '车主在乘客下车前清空位置';
            case 5:
                return '乘客在车主清空位置前下车';
            case 6:
                return '成功到达';
            case 7:
                if ($type == 1) {
                    return '乘客取消';
                } else {
                    return '车主取消';
                }
            case 8:
                if ($type == 1) {
                    return '车主拒绝';
                } else {
                    return '乘客拒绝';
                }
            case 9:
                return '车主在同意后，乘客上车前清空位置';
        }
    }

}

?>

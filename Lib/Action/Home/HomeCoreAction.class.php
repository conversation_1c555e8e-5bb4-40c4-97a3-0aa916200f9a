<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 前台核心模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class HomeCoreAction extends CoreAction
{
    /**
     * 用户类型（1-乘客，2-车主）
     * @var int
     */
    protected $user_type;

    protected $mchid_stop_arr = ['796'];


    //put your code here
    public function __construct()
    {
        $array = C('tags.auth_check');
        if (empty($array)) {
            add_tag_behavior('auth_check', 'MemberAuthCheck');
        }
        parent::__construct();
        $this->user_type = (isset($this->state) && isset($this->state->data['user_type'])) ? intval($this->state->data['user_type']) : null;
        if ($this->isPage) {
            $this->assign('user_type', $this->user_type);
        }
    }

    /**
     * 判断地址是否是同一个城市
     * @param string $code1 地址编号1
     * @param string $code2 地址编号2
     */
    protected function isSameCity($code1, $code2)
    {
        $r1 = $this->resolutionAddress($code1);
        $r2 = $this->resolutionAddress($code2);
        if (!$r1->ret) {
            return $r1;
        }
        if (!$r2->ret) {
            return $r2;
        }
        if ($r1->data['city_code'] == $r2->data['city_code']) {
            return new ResultModel(true);
        } else {
            return new ResultModel(false);
        }
    }

    /**
     * 解析地址编号
     * @param string $code 地址编号
     */
    protected function resolutionAddress($code)
    {
        if (intval($code) && ($l = strlen($code)) >= 7 && $l <= 8) {
            $addr = array();
            //获取省份
            if ($l == 7) {
                $province_code = intval($code[0]);
                $i = 0;
            } else {
                $province_code = intval($code[0] . $code[1]);
                $i = 1;
            }
            if ($province_code) {
                $r = $this->find(array('code' => $province_code . '000000'), 'MetaAddress', 'name,address_id,code');
                if ($r->ret) {
                    $addr['province'] = $r->data['name'];
                    $addr['province_id'] = $r->data['address_id'];
                    $addr['province_code'] = $r->data['code'];
                } else {
                    return new ResultModel(false, '找不到地址编号对应的省');
                }
            }
            //获取城市
            if (intval($code[$i + 1]) !== 0 || intval($code[$i + 2]) !== 0) {
                $city_code = intval($province_code . $code[$i + 1] . $code[$i + 2]);
                $r = $this->find(array('code' => $city_code . '0000'), 'MetaAddress', 'name,address_id,code');
                if ($r->ret) {
                    $addr['city'] = $r->data['name'];
                    $addr['city_id'] = $r->data['address_id'];
                    $addr['city_code'] = $r->data['code'];
                } else {
                    return new ResultModel(false, '找不到地址编号对应的城市');
                }
            }
            //获取县区
            if (intval($code[$i + 3]) !== 0 || intval($code[$i + 4]) !== 0) {
                $county_code = intval($city_code . $code[$i + 3] . $code[$i + 4]);
                $r = $this->find(array('code' => $county_code . '00'), 'MetaAddress', 'name,address_id,code');
                if ($r->ret) {
                    $addr['country'] = $r->data['name'];
                    $addr['country_id'] = $r->data['address_id'];
                    $addr['country_code'] = $r->data['code'];
                } else {
                    return new ResultModel(false, '找不到地址编号对应的县或区');
                }
            }
            //获取城镇
            if (intval($code[$i + 5]) !== 0 || intval($code[$i + 6]) !== 0) {
                $r = $this->find(array('code' => $code), 'MetaAddress', 'name,address_id,code');
                if ($r->ret) {
                    $addr['town'] = $r->data['name'];
                    $addr['town_id'] = $r->data['address_id'];
                    $addr['town_code'] = $r->data['code'];
                } else {
                    return new ResultModel(false, '找不到地址编号对应的城镇');
                }
            }
            return new ResultModel(true, $addr);
        } else {
            return new ResultModel(false, '地址编号格式不正确');
        }
    }

    /*
     * 判断是否在一个城市
     */
    protected function isSameCityTwo($code1, $code2)
    {
        $r1 = $this->resolutionAddressTwo($code1);
        $r2 = $this->resolutionAddressTwo($code2);
        if (!$r1->ret) {
            return $r1;
        }
        if (!$r2->ret) {
            return $r2;
        }
        if ($r1->data['city_code'] == $r2->data['city_code']) {
            return new ResultModel(true);
        } else {
            return new ResultModel(false);
        }
    }

    /**
     * 解析地址编号
     * @param string $code 地址编号
     */
    protected function resolutionAddressTwo($code)
    {
        try {
            if (empty($code)) {
                return new ResultModel(false, '地址编号格式不正确');
            }
            $code = substr($code, 0, 4)."00";
            return new ResultModel(true, ['city_code' => $code]);
        } catch (Exception $e) {
            return new ResultModel(false, '地址编号格式不正确');
        }
    }

    /**
     * 获取城市名称
     * @param $code
     */
    protected function getCityNameByAddressCode($code)
    {
        $ret = new \ResultModel(false);
        if (empty($code)) {
            return $ret;
        }
        $code .= "";//转化为字符串
        $provinceCode = substr($code, 0, 2)."0000";
        if (!in_array($provinceCode, $this->directlyCityArr)) {
            $cityCode = substr($code, 0, 4)."00";
            $cityR = $this->find(array('address_id' => $cityCode), 'GdRegion');
            if ($cityR->ret) {
                $ret->ret = true;
                $ret->data['city_name'] = $cityR->data['name'];
                $ret->count = 1;
            }
        } else {
            $cityR = $this->find(array('address_id' => $provinceCode), 'GdRegion');
            if ($cityR->ret) {
                $ret->ret = true;
                $ret->data['city_name'] = $cityR->data['name'];
                $ret->count = 1;
            }
        }

        return $ret;
    }

    /**
     * 检测地址编号是否正确
     * @param string $code 地址编号
     */
    protected function checkingAddressCode($code)
    {
        if (intval($code) && ($l = strlen($code)) >= 7 && $l <= 8) {
            $r = $this->find(array('code' => $code), 'MetaAddress', 'address_id,name');
            if ($r->ret) {
                return new ResultModel(true, array("address" => $r->data['name']));
            } else {
                return new ResultModel(false, '地址编号不存在');
            }
        } else {
            return new ResultModel(false, '地址编号格式不正确');
        }
    }

    /**
     * 高德检测地址编号是否正确
     * @param string $code 地址编号
     */
    protected function checkingGdAddressCode($code)
    {
        if (intval($code) && ($l = strlen($code)) == 6) {
            $r = $this->find(array('address_id' => $code), 'GdRegion', 'address_id,name');
            if ($r->ret) {
                return new ResultModel(true, array("address" => $r->data['name']));
            } else {
                return new ResultModel(false, '地址编号不存在');
            }
        } else {
            return new ResultModel(false, '地址编号格式不正确');
        }
    }

    /**
     * 高德检测父级地址编号是否正确
     * @param string $code 地址编号
     */
    protected function checkingGdParentAddressCode($code)
    {
        if (intval($code) && ($l = strlen($code)) == 6) {
            $r = $this->find(array('address_id' => $code), 'GdRegion', 'address_id,address_pid,name');
            if ($r->ret) {
                $pR = $this->find(array('address_id' => $r->data['address_pid']), 'GdRegion', 'address_id,name');
                if ($pR->ret) {
                    return new ResultModel(true, array("address" => $pR->data['name']));
                } else {
                    return new ResultModel(false, '地址编号不存在');
                }
            } else {
                return new ResultModel(false, '地址编号不存在');
            }
        } else {
            return new ResultModel(false, '地址编号格式不正确');
        }
    }

    /**
     * 添加预订订单历史状态
     * @param int $order_id 订单ID
     * @param string $state_code 状态标码
     */
    public function addBookOrderHistoryState($order_id, $state_code)
    {
        $r = $this->find(array('code' => $state_code), 'MetaBookOrderState', 'state_id');
        if ($r->ret) {
            $this->result = $this->add('OrderHistoryState', array('order_id' => $order_id, 'state_id' => $r->data['state_id'], 'type' => 1));
        } else {
            $this->result = new ResultModel(false, '预订订单状态标码不存在');
        }
        return $this->result;
    }

    /**
     * 添加邀请订单历史状态
     * @param int $order_id 订单ID
     * @param string $state_code 状态标码
     */
    public function addInviteOrderHistoryState($order_id, $state_code)
    {
        $r = $this->find(array('code' => $state_code), 'MetaInviteOrderState', 'state_id');
        if ($r->ret) {
            $this->result = $this->add('OrderHistoryState', array('order_id' => $order_id, 'state_id' => $r->data['state_id'], 'type' => 2));
        } else {
            $this->result = new ResultModel(false, '邀请订单状态标码不存在');
        }
        return $this->result;
    }

    /**
     * 扣除保险
     * @param int $order_id 订单ID
     */
    protected function deductInsurance($order_id)
    {
        $r = $this->find(array('order_id' => $order_id), 'Order', 'is_insure,driver_id,passenger_id');
        if ($r->ret) {
            $is_insure = intval($r->data['is_insure']);
            $driver_id = $r->data['driver_id'];
            $passenger_id = $r->data['passenger_id'];
            if ($is_insure === 1) {
                $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'balance');
                if ($r->ret) {
                    $balance = floatval($r->data['balance']);
                    $r = $this->find(array('key' => 'insurance money'), 'SystemConfig', 'value');
                    $insurance_money = floatval($r->data['value']);
                    if ($balance >= $insurance_money) {
                        $balance = $balance - $insurance_money;
                        //更新账户余额
                        $r = $this->save('Driver', array('balance' => $balance, 'driver_id' => $driver_id));
                        if ($r->ret) {
                            //添加车主费用记录
                            $r = $this->add('DriverCost', array('driver_id' => $driver_id, 'cost_type' => 3, 'money' => floatval($balance)));
                            if ($r->ret) {
                                //添加乘客购保记录
                                $r = $this->add('PassengerInsurance', array('passenger_id' => $passenger_id, 'money' => $insurance_money, 'order_id' => $order_id));
                            }
                        }
                        $this->result = $r;
                    } else {
                        $this->result = new ResultModel(false, '车主余额不够支付乘客保险费用');
                    }
                }
            }
        } else {
            $this->result = new ResultModel(false, '订单ID不存在');
        }
        return $this->result;
    }

    /**
     * 验证手机
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     */
    protected function ValidateCellphone($cellphone, $cellphone_validate_code, $mchid)
    {
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            $r = $this->find(array('cellphone' => $cellphone, 'validate_code' => $cellphone_validate_code,'mchid' => $mchid), 'CellphoneValidate', 'update_time,validate_code');
            if ($r->ret) {
                if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') < strtotime(get_current_time())) {
                    $this->result = new ResultModel(false, '验证码已经过期');
                }
                $this->result = new ResultModel(true);
            } else {
                $this->result = new ResultModel(false, '手机验证码错误');
            }
        } else {
            $this->result = new ResultModel(true);
        }
        return $this->result;
    }


    /**
     * 获取地址编码
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     */
    protected function getGdAddressCodeByGdApi($longitude = null, $latitude = null)
    {
        $apiUrl = C("GD_API_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("longitude", $longitude, $apiUrl);
        $apiUrl = str_replace("latitude", $latitude, $apiUrl);
        $sAddrCode = file_get_contents($apiUrl);
        $data = json_decode($sAddrCode, true);
        $ret = array();
        if ($data['status']) {
            $ret['status'] = $data['status'];
            $ret['code'] = $data['regeocode']['addressComponent']['adcode'];
            $ret['address'] = $data['regeocode']['formatted_address'];
        } else {
            $ret['status'] = $data['status'];
            $ret['code'] = 0;
        }
        return $ret;
    }

    /**
     * 根据地名和城市名称获取坐标点
     * @param null $city
     * @param null $address
     * @return \ResultModel
     */
    protected function getGdLocationByAddress($address = null, $city = null)
    {
        $ret = new \ResultModel(false);
        $apiUrl = C("GD_API_GEO_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("city_name", $city ? $city : "", $apiUrl);
        $apiUrl = str_replace("address_name", $address ? $address : "", $apiUrl);
        $sAddrCode = file_get_contents($apiUrl);
        $data = json_decode($sAddrCode, true);
        if ($data['status']) {
            if (!empty($data['geocodes'])) {
                if (!empty($data['geocodes'][0]['location'])) {
                    $ret->ret = true;
                    $ret->data['location'] = $data['geocodes'][0]['location'];
                    $ret->count = 1;
                }
            }
        }
        return $ret;
    }

    protected function getGdLocationByAdcode($adcode)
    {
        $ret = new \ResultModel(false);
        $apiUrl = C("GD_API_DISTRICT_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("keywords_value", $adcode ? $adcode : "", $apiUrl);
        $apiUrl = str_replace("subdistrict_value", 3, $apiUrl);
        $sAddrCode = file_get_contents($apiUrl);
        $data = json_decode($sAddrCode, true);
        if ($data['status']) {
            if (!empty($data['districts'])) {
                if (!empty($data['districts'][0]['center'])) {
                    $ret->ret = true;
                    $ret->data['location'] = $data['districts'][0]['center'];
                    $ret->count = 1;
                }
            }
        }
        return $ret;
    }

    /**
     * 获取驾车规划路径
     * @param double $start_longitude 开始经度
     * @param double $start_latitude 开始纬度
     * @param double $end_longitude 结束纬度
     * @param double $end_latitude 结束纬度
     */
    protected function getGdDirectionDrivingDistanceByGdApi($start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null)
    {
        $ret = new \ResultModel(false);
        $apiUrl = C("GD_API_DIRECTION_DRIVING_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("start_longitude", $start_longitude, $apiUrl);
        $apiUrl = str_replace("start_latitude", $start_latitude, $apiUrl);
        $apiUrl = str_replace("end_longitude", $end_longitude, $apiUrl);
        $apiUrl = str_replace("end_latitude", $end_latitude, $apiUrl);
        $sDistanceData = file_get_contents($apiUrl);
        $data = json_decode($sDistanceData, true);
        if ($data['status']) {
            if (is_array($data['route']['paths'])) {
                $ret->ret = true;
                $ret->data = array(
                    'distance' => $data['route']['paths']['0']['distance'], # 高德行驶距离，单位米
                    'duration' => $data['route']['paths']['0']['duration'], # 高德预计行驶时间，单位秒
                    'taxi_cost' => $data['route']['taxi_cost'] # 高德打车费用，单位：元，注意：extensions=all时才会返回
                );
            }
        }
        return $ret;
    }

    /**
     * id揭秘
     *
     **/

    public function checkauth($chart)
    {
        if (!$chart) {
            echo false;
            exit;
            //            return $this->output(new ResultModel(false, '地址参数错误'));
        } else {
            $model = M('cipher_control')->field('mchid')->where("ciphertext='" . $chart . "'");
            $data = $model->find();
            if ($data) {
                return new ResultModel(true, $data['mchid']);
            } else {
                echo false;
                exit;
                //                return $this->output(new ResultModel(false, '地址参数错误'));
            }
        }
    }

    public function incheck($uid = null)
    {
        return strtoupper('m' . md5($uid) . 'h'.rand(1000, 9999));
    }

    public function createOrderNo($start_time = null)
    {
        if (empty($start_time)) {
            return "NO" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
        } else {
            return "NO" . substr(strtotime($start_time), 6, 4) . rand(100000, 999999);
        }
    }

    public function createFerryOrderNo($start_time = null)
    {
        if (empty($start_time)) {
            return "FRYNO" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
        } else {
            return "FRYNO" . substr(strtotime($start_time), 6, 4) . rand(100000, 999999);
        }
    }

    public function createOrderNoNew()
    {
        return date('Ymd').substr(implode(null, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
    }


    public function createOrderCardNo()
    {
        return "CNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createOrderParentHelpPriceNo()
    {
        return "PHPNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createStudentCustomizedLineNo()
    {
        return "SCLNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createStudentCustomizedOrderNo()
    {
        return "SCONO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createLineFreeRideNo()
    {
        return "LFRN" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
    }

    public function createLineParentHelpNo()
    {
        return "LPHN" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
    }

    public function createTransferNo()
    {
        return "TSNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createDepositNo()
    {
        return "DNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createDestoonFinanceCashNo()
    {
        return "MNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createTrainNo()
    {
        return "TNO" . substr(time(), 6, 4) . rand(100000, 999999);
    }

    public function createLineClassTrainNo($start_time)
    {
        return date("Ymdhis", $start_time) . rand(1000, 9999);
    }

    /**
     * 获取订单类型
     */
    protected function getOrderClass($orderNo)
    {
        if (substr($orderNo, 0, 3) == 'CNO') {
            return \CommonDefine::ORDER_CLASS_2;
        } elseif (substr($orderNo, 0, 5) == 'PHPNO') {
            return \CommonDefine::ORDER_CLASS_3;
        } elseif (substr($orderNo, 0, 5) == 'SCONO') {
            return \CommonDefine::ORDER_CLASS_4;
        } elseif (substr($orderNo, 0, 5) == 'FRYNO') {
            return \CommonDefine::ORDER_CLASS_5;
        } else {
            return \CommonDefine::ORDER_CLASS_1;
        }
    }

    /**
     * 获取商户明文id
     * @param bool $isReload 商户id不一致时是否刷新页面
     * @return null|object|string
     */
    protected function getMchid($isReload = false)
    {
        $mchid = "";
        $cipmchid = self::getCipmchid($isReload);
        if ($cipmchid) {
            $mchid = $this->checkauth($cipmchid)->data;
        }
        return $mchid;
    }

    /**
     * 获取商户密文
     * @param bool $isReload 商户密文不一致时是否刷新页面
     * @return null|object|string
     */
    protected function getCipmchid($isReload = false)
    {
        //优先获取地址栏上的密文
        $cipmchid = isset($_GET['callback']) ? $_GET['callback'] : false;

        if (isset($_GET['callback'])) {
            $cipmchid = $_GET['callback'];
            $oldcipmchid = cookie('cipmchid');
            cookie('cipmchid', $cipmchid);

            //密文是否一致
            if ($oldcipmchid && $oldcipmchid != $cipmchid) {
                \StateModel::clear(\StateModel::$PASSENGER);
                \StateModel::clear(\StateModel::$BRANCH_USER);
                \StateModel::clear(\StateModel::$DRIVER);
                if ($isReload) {
                    echo '<script>window.location.href=window.location.href;</script>';
                    die;
                    /*                    $random = rand(1000,9999);
                                        echo '<script>window.location.href=window.location.href+"/refresh/'.$random.'";</script>';die;*/
                }
            }
        }

        if (!$cipmchid) {
            $cipmchid = cookie('cipmchid');
        }
        return $cipmchid;
    }

    /**
     * @param $mchid
     * @return ResultModel
     */
    protected function getChannelBranchCode($mchid)
    {
        $ret = new ResultModel(false);
        if (isset($_GET[\CommonDefine::CHANNEL_BRANCH_PARMTER])) {
            $branchCode = $_GET[\CommonDefine::CHANNEL_BRANCH_PARMTER];
            $branchR = $this->find(array('branch_code' => $branchCode), 'BranchQrAttachment');
            if ($branchR->ret && $branchR->data['mchid'] == $mchid) {
                $ret->ret = true;
                $ret->data['branch_code'] = $_GET[\CommonDefine::CHANNEL_BRANCH_PARMTER];
                $ret->data['branchid'] = $branchR->data['branchid'];
                $ret->data['mchid'] = $branchR->data['mchid'];
                $ret->count = 1;
            }
        }
        return $ret;
    }

    //获取渠道乘客URL地址
    protected function getChannelBranchPassengerUrl($mchid, $branchCode)
    {
        $cipherMchidR = $this->getCipmchidByMchid($mchid);
        if ($cipherMchidR->ret) {
            $cipherMchidR->data['url'] = C('WX_PASSENGER_URL') . '/callback/' . $cipherMchidR->data['ciphertext'].'/'.\CommonDefine::CHANNEL_BRANCH_PARMTER.'/'.$branchCode;
        }
        return $cipherMchidR;
    }

    /**
     * @param $mchid
     * @return ResultModel
     */
    protected function getDriverCode($mchid)
    {
        $ret = new ResultModel(false);
        if (isset($_GET[\CommonDefine::DRIVER_ENTRANCE_PARAMTER])) {
            $driverCode = $_GET[\CommonDefine::DRIVER_ENTRANCE_PARAMTER];
            $driverR = $this->find(array('driver_code' => $driverCode), 'DriverQrAttachment');
            if ($driverR->ret && $driverR->data['mchid'] == $mchid) {
                $ret->ret = true;
                $ret->data['driver_code'] = $_GET[\CommonDefine::DRIVER_ENTRANCE_PARAMTER];
                $ret->data['driver_id'] = $driverR->data['driver_id'];
                $ret->data['mchid'] = $driverR->data['mchid'];
                $ret->count = 1;
            }
        }
        return $ret;
    }

    //获取司机端的乘客URL地址
    protected function getDriverPassengerUrl($mchid, $driverCode)
    {
        $cipherMchidR = $this->getCipmchidByMchid($mchid);
        if ($cipherMchidR->ret) {
            $cipherMchidR->data['url'] = C('WX_PASSENGER_URL') . '/callback/' . $cipherMchidR->data['ciphertext'].'/'.\CommonDefine::DRIVER_ENTRANCE_PARAMTER.'/'.$driverCode;
        }
        return $cipherMchidR;
    }

    //获取司机端的乘客班线URL地址
    protected function getDriverPassengerTrainsUrl($mchid, $driverCode)
    {
        $cipherMchidR = $this->getCipmchidByMchid($mchid);
        if ($cipherMchidR->ret) {
            $cipherMchidR->data['url'] = C('WX_PASSENGER_TRAINS_URL') . '/callback/' . $cipherMchidR->data['ciphertext'].'/'.\CommonDefine::DRIVER_ENTRANCE_PARAMTER.'/'.$driverCode;
        }
        return $cipherMchidR;
    }

    //获取密文
    protected function getCipmchidByMchid($mchid)
    {
        return $this->find(array('mchid' => $mchid), 'CipherControl');
    }

    //获取商户名称
    protected function getMchInfoByMchid($mchid)
    {
        $mchArr = $this->find(array('admin_id' => $mchid), 'Admin', 'mchname, tel, package_id');
        return $mchArr;
    }

    //获取商户微信公众号二维码地址信息
    protected function getMchWechatQr($mchid)
    {
        $mchWechatQrR = $this->find(array('mchid' => $mchid), 'MchWechatQr');
        return $mchWechatQrR;
    }

    /**
     * 根据商户id及用户类型获取商户的回调地址
     * @param int $type 1-乘客端；2-司机端；3-分台端；
     */
    protected function getMerchatCallbackUrlByMchidAndType($mchid, $type)
    {
        $mchCallbackUrlR = new ResultModel(false);
        $mchCipherR = $this->find(array('mchid' => $mchid), 'CipherControl');
        if ($mchCipherR->ret) {
            switch ($type) {
                case \CommonDefine::USER_TYPE_1:{
                    $mchCallbackUrlR->data['url'] = C('WX_PASSENGER_URL'). '/callback/'.$mchCipherR->data['ciphertext'];
                    $mchCallbackUrlR->ret = true;
                    $mchCallbackUrlR->count = 1;
                    break;
                }
                case \CommonDefine::USER_TYPE_2:{
                    $mchCallbackUrlR->data['url'] = C('WX_DRIVER_URL'). '/callback/'.$mchCipherR->data['ciphertext'];
                    $mchCallbackUrlR->ret = true;
                    $mchCallbackUrlR->count = 1;
                    break;
                }
                case \CommonDefine::USER_TYPE_3:{
                    $mchCallbackUrlR->data['url'] = C('WX_BRANCH_URL'). '/callback/'.$mchCipherR->data['ciphertext'];
                    $mchCallbackUrlR->ret = true;
                    $mchCallbackUrlR->count = 1;
                    break;
                }
                default:
                    break;
            }
        }

        return $mchCallbackUrlR;
    }


    protected function getAddressCodeByName($provinceName = null, $cityName = null, $areaName = null)
    {
        $addressCode = null;
        if (!empty($areaName) && $areaName != "其他") {
            $where['name'] = array('like',$areaName.'%');
            $addressR = $this->select($where, null, null, null, 'GdRegion');
            if ($addressR->ret) {
                if (count($addressR->data) == 1) {
                    $addressCode = $addressR->data[0]['address_id'];
                    return $addressCode;
                } else {
                    foreach ($addressR->data as $v) {
                        //有相同的则查询父级元素
                        $addressPR = $this->find(array('address_id' => $v['address_pid']), 'GdRegion');
                        if ($addressPR->ret) {
                            if (strpos($cityName, $addressPR->data) !== false) {
                                $addressCode = $v['address_pid'];
                                return $addressCode;
                            }
                        }
                    }
                }
            }
        }
        if (!empty($cityName) && $cityName != "其他") {
            $where['name'] = array('like',$cityName.'%');
            $addressR = $this->select($where, null, null, null, 'GdRegion');
            if ($addressR->ret) {
                if (count($addressR->data) == 1) {
                    $addressCode = $addressR->data[0]['address_id'];
                    return $addressCode;
                } else {
                    foreach ($addressR->data as $v) {
                        //有相同的则查询父级元素
                        $addressPR = $this->find(array('address_id' => $v['address_pid']), 'GdRegion');
                        if ($addressPR->ret) {
                            if (strpos($provinceName, $addressPR->data) !== false) {
                                $addressCode = $v['address_pid'];
                                return $addressCode;
                            }
                        }
                    }
                }
            }
        }

        if (!empty($provinceName)) {
            $where['name'] = array('like',$provinceName.'%');
            $addressR = $this->select($where, null, null, null, 'GdRegion');
            if ($addressR->ret) {
                return $addressR->data['address_pid'];
            }
        }
        return $addressCode;
    }

    //判断当前位置是区还是县 0-失败；1-省；2-市；3-区;4-县域
    protected function getCodeProtity($code)
    {
        $ret = 0;
        if (!$code) {
            return $ret;
        }
        $where['address_id'] = $code;
        $addressR = $this->find($where, 'GdRegion');
        if ($addressR->ret) {
            //省
            if ($addressR->data['address_pid'] == 0) {
                $ret = 1;
            } else {
                $where['address_id'] = $addressR->data['address_pid'];
                $addressPR = $this->find($where, 'GdRegion');
                if ($addressPR->ret) {
                    if ($addressPR->data['address_pid'] == 0) {
                        $ret = 2;
                    } else {
                        $where['address_id'] = $addressPR->data['address_pid'];
                        $addressPPR = $this->find($where, 'GdRegion');
                        if ($addressPPR->ret) {
                            if ($addressPPR->data['address_pid'] == 0) {
                                //判断是区还是县
                                if (self::checkIsAreaOrCounty($code) == 1) {
                                    $ret = 3;
                                } elseif (self::checkIsAreaOrCounty($code) == 2) {
                                    $ret = 4;
                                }
                            }
                        }
                    }
                }
            }
        }
        return $ret;
    }

    /**
     * 获取热门市或县
     * @param int $address_code 地址编码
     */
    protected function getHotAddressByCode($address_code)
    {
        $addressCodeR = $this->find(array('address_id' => $address_code), 'GdRegion', 'address_pid,address_id,name');
        if ($addressCodeR->ret) {
            $lastName = substr($addressCodeR->data['name'], (mb_strlen($addressCodeR->data['name']) - 3), 3);
            if ($lastName == '区') {
                return $this->getHotAddressByCode($addressCodeR->data['address_pid']);
            } elseif ($lastName == '县' || $lastName == '市') {
                return  new \ResultModel(true, [
                    'address_id' => $addressCodeR->data['address_id'],
                    'name' => $this->removeCityOrCountyLastName($addressCodeR->data['name']),
                ]);
            }
        }
        return new \ResultModel(false);
    }

    /**
     * @param $areaCode
     * @return 0-失败;1-市区；2-县域；
     */
    protected function checkIsAreaOrCounty($code)
    {
        $ret = 0;
        if (!$code) {
            return $ret;
        }

        $where['address_id'] = $code;
        $addressR = $this->find($where, 'GdRegion');
        $areaName = "区";
        $coutryName = "区";
        if ($addressR->ret) {
            $realPos = strrpos($addressR->data['name'], $areaName);
            if ($realPos == strlen($addressR->data['name']) - 3) {
                return 1;
            }

            $realPos = strrpos($addressR->data['name'], $coutryName);
            if ($realPos == strlen($addressR->data['name']) - 3) {
                return 2;
            }
        }
        return $ret;
    }

    //通过详细的市区获取市辖区 code
    protected function getCityInnerCode($code)
    {
        $ret = 0;
        if (!$code) {
            return $ret;
        }
        $where['address_id'] = $code;
        $addressR = $this->find($where, 'GdRegion');
        if ($addressR->ret) {
            unset($where['address_id']);
            $where['address_pid'] = $addressR->data['address_pid'];
            $where['name'] = "市辖区";
            $addressFR = $this->find($where, 'GdRegion');
            if ($addressFR->ret) {
                $ret = $addressFR->data['address_id'];
            } else {
                $pwhere['address_id'] = $addressR->data['address_pid'];
                $pwhere['name'] = "市辖区";
                $addressFPR = $this->find($pwhere, 'GdRegion');
                if ($addressFPR->ret) {
                    $ret = $addressFPR->data['address_id'];
                }
            }
        }

        return $ret;
    }

    protected function getProvinceCityAreaCodeByCode($code)
    {
        $ret = array(
            'province_code' =>  '',
            'city_code' => '',
            'area_code' => '',
        );
        if (!$code) {
            return $ret;
        }

        $where['address_id'] = $code;
        $addressR = $this->find($where, 'GdRegion');
        if ($addressR->ret) {
            //省或直辖市
            if ($addressR->data['address_pid'] == 0) {
                $ret['province_code'] = $addressR->data['address_id'];
            } else {
                $where['address_id'] = $addressR->data['address_pid'];
                $addressPR = $this->find($where, 'GdRegion');
                if ($addressPR->ret) {
                    if ($addressPR->data['address_pid'] == 0) {
                        $ret['province_code'] = $addressPR->data['address_id'];
                        $ret['city_code'] = $addressR->data['address_id'];
                    } else {
                        $where['address_id'] = $addressPR->data['address_pid'];
                        $addressPPR = $this->find($where, 'GdRegion');
                        if ($addressPPR->ret) {
                            if ($addressPPR->data['address_pid'] == 0) {
                                //判断是区还是县
                                $ret['province_code'] = $addressPPR->data['address_id'];
                                $ret['city_code'] = $addressPR->data['address_id'];
                                $ret['area_code'] = $addressR->data['address_id'];
                            }
                        }
                    }
                }
            }
        }
        return $ret;
    }

    /**
     * @param int $areaCode 区域编码
     * @return ResultModel 县域名称或者市名称；
     */
    protected function getParentAdministrativeDivisionNameByCode($areaCode)
    {
        $ret = new \ResultModel(false);
        $addressR = $this->find(array('address_id' => $areaCode), 'GdRegion');
        if ($addressR->ret) {
            $str = $addressR->data['name'];
            $newstr = substr($str, strlen($str) - 3, 3);
            if ($newstr == "县") {
                $ret->ret = true;
                $ret->data = array(
                    'code' => $addressR->data['address_id'],
                    'name' => $addressR->data['name'],
                );
                $ret->count = 1;
            } elseif ($newstr == "市") {//县级市
                $ret->ret = true;
                $ret->data = array(
                    'code' => $addressR->data['address_id'],
                    'name' => $addressR->data['name'],
                );
                $ret->count = 1;
            } else {
                $addressPR = $this->find(array('address_id' => $addressR->data['address_pid']), 'GdRegion');
                if ($addressPR->ret) {
                    $str = $addressPR->data['name'];
                    $newstr = substr($str, strlen($str) - 3, 3);
                    if ($newstr == "市") {
                        $ret->ret = true;
                        $ret->data = array(
                            'code' => $addressPR->data['address_id'],
                            'name' => $addressPR->data['name'],
                        );
                        $ret->count = 1;
                    } elseif ($str == "市辖区") {
                        $addressPPR = $this->find(array('address_id' => $addressPR->data['address_pid']), 'GdRegion');
                        if ($addressPPR->ret) {
                            $str = $addressPPR->data['name'];
                            $newstr = substr($str, strlen($str) - 3, 3);
                            if ($newstr == "市") {
                                $ret->ret = true;
                                $ret->data = array(
                                    'code' => $addressPPR->data['address_id'],
                                    'name' => $addressPPR->data['name'],
                                );
                                $ret->count = 1;
                            }
                        }
                    }
                }
            }
        }
        return $ret;
    }

    //推送乘客消息
    protected function pushPassengerNewMessage($order_id, $passenger_id, $title, $content)
    {
        if (!$passenger_id) {
            return new \ResultModel(false, "参数错误");
        }
        $data['order_id']  = $order_id;
        $data['passenger_id']  = $passenger_id;
        $data['title']  = $title ? $title : "";
        $data['content']  = $content ? $content : "";
        $ret = $this->add('PassengerMessage', $data);
        return $ret;
    }

    //推送司机消息
    protected function pushDriverNewMessage($order_id, $driver_id, $title, $content)
    {
        if (!$driver_id) {
            return new \ResultModel(false, "参数错误");
        }
        $data['order_id']  = $order_id;
        $data['driver_id']  = $driver_id;
        $data['title']  = $title ? $title : "";
        $data['content']  = $content ? $content : "";
        $ret = $this->add('DriverMessage', $data);
        return $ret;
    }

    //推送分台消息
    protected function pushBranchNewMessage($order_id, $branch_id, $title, $content)
    {
        if (!$branch_id) {
            return new \ResultModel(false, "参数错误");
        }
        $data['order_id']  = $order_id;
        $data['branch_id']  = $branch_id;
        $data['title']  = $title ? $title : "";
        $data['content']  = $content ? $content : "";
        $ret = $this->add('BranchMessage', $data);
        return $ret;
    }

    //获取线路
    protected function getLineCharteredPriceByLineCharteredId($lineCharteredId)
    {
        $res = array();
        if (!$lineCharteredId) {
            return $res;
        }
        $where['line_chartered_id'] = $lineCharteredId;
        $where['is_del'] = 0;
        $r = $this->select($where, null, null, null, 'LineCharteredPrice');
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $carTypeR = $this->find(array('car_type_id' => $v['car_type_id']), 'CarType');
                if ($carTypeR->ret) {
                    $r->data['car_type_name'] = $carTypeR->data['name'];
                    $r->data['car_type_num'] = $carTypeR->data['num'];
                    $res[$v['car_type_id']] = array(
                        'line_chartered_price_id' => $v['line_chartered_price_id'],
                        'car_type_name' => $carTypeR->data['name'],
                        'car_type_num' => $carTypeR->data['num'],
                        'price' => $v['price'],
                    );
                }
            }
        }
        return $res;
    }

    //获取渠道线路
    protected function getLineCharteredChannelPriceByLineCharteredId($lineCharteredId)
    {
        $res = array();
        if (!$lineCharteredId) {
            return $res;
        }
        $where['line_chartered_id'] = $lineCharteredId;
        $where['is_del'] = 0;
        $r = $this->select($where, null, null, null, 'LineCharteredPrice');
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $carTypeR = $this->find(array('car_type_id' => $v['car_type_id']), 'CarType');
                if ($carTypeR->ret) {
                    $r->data['car_type_name'] = $carTypeR->data['name'];
                    $r->data['car_type_num'] = $carTypeR->data['num'];
                    $res[$v['car_type_id']] = array(
                        'line_chartered_price_id' => $v['line_chartered_price_id'],
                        'car_type_name' => $carTypeR->data['name'],
                        'car_type_num' => $carTypeR->data['num'],
                        'price' => $v['channel_price'],
                    );
                }
            }
        }
        return $res;
    }

    /**
    *过期
     */
    protected function remind()
    {
        $flag = false;
        $adminR = $this->find(array('admin_id' => $this->mchid,'group_id' => 2,'is_del' => 0), "Admin", 'endtime,admin_id');
        if (strtotime($adminR->data['endtime']) < time()) {
            $flag = true;
            $this->assign("endtime", $adminR->data['endtime']);
        }
        return $flag;
    }

    //根据订单获取分台id
    protected function getBranchIdByOrderId($order_id)
    {
        $branchId = 0;
        if (!$order_id) {
            return $branchId;
        }
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if ($orderR->ret) {
            switch ($orderR->data['type']) {
                case 1:
                    $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                    if ($lineR->ret) {
                        $branchId = $lineR->data['branchid'];
                    }
                    break;
                case 2:
                    $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                    if ($lineR->ret) {
                        $branchId = $lineR->data['branchid'];
                    }
                    break;
                case 3:
                    $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                    if ($lineR->ret) {
                        $branchId = $lineR->data['branchid'];
                    }
                    break;
                case 4:
                    $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                    if ($lineR->ret) {
                        $branchId = $lineR->data['branchid'];
                    }
                    break;
            }
        }
        return $branchId;
    }

    //比较两个浮点数是否相等
    protected static function floatcmp($f1, $f2, $precision = 10) // are 2 floats equal
    {
        $e = pow(10, $precision);
        $i1 = intval($f1 * $e);
        $i2 = intval($f2 * $e);
        return ($i1 == $i2);
    }

    protected static function floatgtr($big, $small, $precision = 10) // is one float bigger than another
    {
        $e = pow(10, $precision);
        $ibig = intval($big * $e);
        $ismall = intval($small * $e);
        return ($ibig > $ismall);
    }
    protected static function floatgtre($big, $small, $precision = 10) // is on float bigger or equal to another
    {
        $e = pow(10, $precision);
        $ibig = floatval($big * $e);
        $ismall = floatval($small * $e);
        return ($ibig >= $ismall);
    }

    /*
    *function：计算两个日期相隔多少年，多少月，多少天
    *param string $date1[格式如：2011-11-5]
    *param string $date2[格式如：2012-12-01]
    *return array array('年','月','日');
    */
    protected function diffDate($date1, $date2)
    {
        if (strtotime($date1) > strtotime($date2)) {
            $tmp = $date2;
            $date2 = $date1;
            $date1 = $tmp;
        }
        list($Y1, $m1, $d1) = explode('-', $date1);
        list($Y2, $m2, $d2) = explode('-', $date2);
        $Y = $Y2 - $Y1;
        $m = $m2 - $m1;
        $d = $d2 - $d1;
        if ($d < 0) {
            $d += (int)date('t', strtotime("-1 month $date2"));
            $m--;
        }
        if ($m < 0) {
            $m += 12;
            $Y--;
        }
        return array('year' => $Y,'month' => $m,'day' => $d);
    }

    protected function validation_filter_id_card($id_card)
    {
        if (strlen($id_card) == 18) {
            return self::idcard_checksum18($id_card);
        } elseif ((strlen($id_card) == 15)) {
            $id_card = self::idcard_15to18($id_card);
            return self::idcard_checksum18($id_card);
        } else {
            return false;
        }
    }
    // 计算身份证校验码，根据国家标准GB 11643-1999
    protected function idcard_verify_number($idcard_base)
    {
        if (strlen($idcard_base) != 17) {
            return false;
        }
        //加权因子
        $factor = array(7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2);
        //校验码对应值
        $verify_number_list = array('1','0','X','9','8','7','6','5','4','3','2');
        $checksum = 0;
        for ($i = 0;$i < strlen($idcard_base);$i++) {
            $checksum += substr($idcard_base, $i, 1) * $factor[$i];
        }
        $mod = $checksum % 11;
        $verify_number = $verify_number_list[$mod];
        return $verify_number;
    }
    // 将15位身份证升级到18位
    protected function idcard_15to18($idcard)
    {
        if (strlen($idcard) != 15) {
            return false;
        } else {
            // 如果身份证顺序码是996 997 998 999，这些是为百岁以上老人的特殊编码
            if (array_search(substr($idcard, 12, 3), array('996','997','998','999')) !== false) {
                $idcard = substr($idcard, 0, 6).'18'.substr($idcard, 6, 9);
            } else {
                $idcard = substr($idcard, 0, 6).'19'.substr($idcard, 6, 9);
            }
        }
        $idcard = $idcard.self::idcard_verify_number($idcard);
        return $idcard;
    }
    // 18位身份证校验码有效性检查
    protected function idcard_checksum18($idcard)
    {
        if (strlen($idcard) != 18) {
            return false;
        }
        $idcard_base = substr($idcard, 0, 17);
        if (self::idcard_verify_number($idcard_base) != strtoupper(substr($idcard, 17, 1))) {
            return false;
        } else {
            return true;
        }
    }


    //自动派单
    protected function autoAppointOrder($order_id)
    {
    }

    /**
     * 记录座位日志
     * @return bool
     */
    protected function recordSeatLog($order_id, $driver_id, $current_seat, $seat, $residual_seating, $type, $function, $message = null)
    {
        try {
            $seatLogData['order_id'] = $order_id;
            $seatLogData['driver_id'] = $driver_id;
            $seatLogData['current_seat'] = $current_seat;
            $seatLogData['residual_seating'] = $residual_seating;
            $seatLogData['seat'] = $seat;
            $seatLogData['type'] = $type;
            $seatLogData['function'] = $function;
            $seatLogData['message'] = $message;
            $seatLogS = $this->add('SeatLog', $seatLogData);
        } catch (Exception $e) {
        }
    }

    /**
     * 检测当前请求页面是否来自微信
     * @return bool
     */
    protected function is_weixin()
    {
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
            return true;
        }
        return false;
    }

    /**
     * 获取带货计费价格
     */
    protected function getDeliveryPrice($takeGoodsPriceId, $weight, $kilometer)
    {
        if (!$takeGoodsPriceId || !$weight || !$kilometer) {
            return 0;
        }
        $takeGoodsPriceR = $this->find(array('take_goods_price_id' => $takeGoodsPriceId, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$takeGoodsPriceR->ret) {
            return 0;
        }
        $price = $takeGoodsPriceR->data['base_price'];
        if ($weight > $takeGoodsPriceR->data['base_weight']) {
            $overWeight = (int)($weight - $takeGoodsPriceR->data['base_weight']) + 1;
            $price += ($overWeight * $takeGoodsPriceR->data['plus_weight_price']);
        }

        if ($kilometer > $takeGoodsPriceR->data['base_kilometre']) {
            $overPrice = (int)($kilometer - $takeGoodsPriceR->data['base_kilometre']) + 1;
            $price += ($overPrice * $takeGoodsPriceR->data['plus_kilometre_price']);
        }

        return $price;
    }

    /**
     * 获取带货计费价格
     */
    protected function getMileagePrice($mileagePriceId, $kilometer)
    {
        if (!$mileagePriceId || !$kilometer) {
            return 0;
        }
        $mileagePriceR = $this->find(array('mileage_price_id' => $mileagePriceId, 'is_del' => 0), 'MileagePrice');
        if (!$mileagePriceR->ret) {
            return 0;
        }
        $price = $mileagePriceR->data['base_price'];
        if ($this->floatgtr($kilometer, $mileagePriceR->data['base_kilometre'])) {
            $overPrice = (int)($kilometer - $mileagePriceR->data['base_kilometre']) + 1;
            $price += ($overPrice * $mileagePriceR->data['plus_kilometre_price']);
        }

        return $price;
    }

    /**
     * 获取快车里程计费价格
     * @param $id
     * @param $kilometer
     * @param $duration
     * @return ResultModel
     */
    protected function getLineFastPredictPrice($id, $kilometer, $duration)
    {
        $ret = new ResultModel(false);
        if (!$id) {
            return $ret;
        }
        $ret->ret = true;
        $currentTime = date('H:i:s', time());
        $computeTaxiBaseR = $this->computeBasePrice($id, $kilometer, $currentTime);
        if (!$computeTaxiBaseR->ret) {
            return new ResultModel(false);
        }
        $ret->data['base_price'] = $computeTaxiBaseR->data['base_price'];
        $ret->data['mileage_price'] = round($this->computeMileagePrice($id, $computeTaxiBaseR->data['remain_distance'], $currentTime), 2);
        $ret->data['duration_price'] = round($this->computeDurationPrice($id, $duration, $currentTime, 2));
        $ret->data['longdistance_price'] = round($this->computeLongdistancePrice($id, $kilometer, $currentTime), 2);

        $ret->data['price'] = $ret->data['base_price'];
        $ret->data['price'] += $ret->data['mileage_price'];
        $ret->data['price'] += $ret->data['duration_price'];
        $ret->data['price'] += $ret->data['longdistance_price'];
        return $ret;
    }

    /**
     * 根据高德获取快车预估费用
     * @param $kilometer
     * @return int
     */
    protected function getGdLineFastPredictPrice($start_longitude, $start_latitude, $end_longitude, $end_latitude)
    {
        $ret = new ResultModel(false);
        $apiUrl = C("GD_API_DIRECTION_DRIVING_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("start_longitude", $start_longitude, $apiUrl);
        $apiUrl = str_replace("start_latitude", $start_latitude, $apiUrl);
        $apiUrl = str_replace("end_longitude", $end_longitude, $apiUrl);
        $apiUrl = str_replace("end_latitude", $end_latitude, $apiUrl);
        $sDistanceData = file_get_contents($apiUrl);
        $data = json_decode($sDistanceData, true);
        if ($data['status']) {
            if (is_array($data['route']['paths']) && is_array($data['route'])) {
                $ret->ret = true;
                $ret->data = array(
                    'taxi_cost' => $data['route']['taxi_cost'],
                    'distance' => $data['route']['paths']['0']['distance'],
                    'duration' => $data['route']['paths']['0']['duration'],
                );
            }
        }
        return 0;
    }

    /**
     * 获取出租车里程计费价格
     * @param $id
     * @param $kilometer
     * @param $duration
     * @return ResultModel
     */
    protected function getLineTaxiPredictPrice($id, $kilometer, $duration)
    {
        $ret = new ResultModel(false);
        if (!$id) {
            return $ret;
        }
        $ret->ret = true;
        $currentTime = date('H:i:s', time());
        $computeTaxiBaseR = $this->computeTaxiBasePrice($id, $kilometer, $currentTime);
        if (!$computeTaxiBaseR->ret) {
            return new ResultModel(false);
        }
        $ret->data['base_price'] = $computeTaxiBaseR->data['base_price'];
        $ret->data['mileage_price'] = round($this->computeTaxiMileagePrice($id, $computeTaxiBaseR->data['remain_distance'], $currentTime), 2);
        $ret->data['duration_price'] = round($this->computeTaxiDurationPrice($id, $duration, $currentTime, 2));
        $ret->data['longdistance_price'] = round($this->computeTaxiLongdistancePrice($id, $kilometer, $currentTime), 2);

        $ret->data['price'] = $ret->data['base_price'];
        $ret->data['price'] += $ret->data['mileage_price'];
        $ret->data['price'] += $ret->data['duration_price'];
        $ret->data['price'] += $ret->data['longdistance_price'];
        return $ret;
    }

    /**
     * 根据高德获取出租车预估费用
     * @param $kilometer
     * @return int
     */
    protected function getGdLineTaxiPredictPrice($start_longitude, $start_latitude, $end_longitude, $end_latitude)
    {
        $ret = new ResultModel(false);
        $apiUrl = C("GD_API_DIRECTION_DRIVING_URL");
        $userKey = C("GD_API_KEY");
        $apiUrl = str_replace("userkey", $userKey, $apiUrl);
        $apiUrl = str_replace("start_longitude", $start_longitude, $apiUrl);
        $apiUrl = str_replace("start_latitude", $start_latitude, $apiUrl);
        $apiUrl = str_replace("end_longitude", $end_longitude, $apiUrl);
        $apiUrl = str_replace("end_latitude", $end_latitude, $apiUrl);
        $sDistanceData = file_get_contents($apiUrl);
        $data = json_decode($sDistanceData, true);
        if ($data['status']) {
            if (is_array($data['route']['paths']) && is_array($data['route'])) {
                $ret->ret = true;
                $ret->data = array(
                    'taxi_cost' => $data['route']['taxi_cost'],
                    'distance' => $data['route']['paths']['0']['distance'],
                    'duration' => $data['route']['paths']['0']['duration'],
                );
            }
        }
        return 0;
    }

    /**
     * 班线车接送费用计算
     * @param $kilometer
     * @return int
     */
    protected function getLineClassFerryPrice($kilometer)
    {
        return $kilometer * 0.2;
    }

    /**
     * 计算距离
     */
    protected function getDistance($lat1, $lng1, $lat2, $lng2, $miles = true)
    {
        $pi80 = M_PI / 180;
        $lat1 *= $pi80;
        $lng1 *= $pi80;
        $lat2 *= $pi80;
        $lng2 *= $pi80;
        $r = 6372.797; // mean radius of Earth in km
        $dlat = $lat2 - $lat1;
        $dlng = $lng2 - $lng1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlng / 2) * sin($dlng / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $km = $r * $c;
        return ($miles ? ($km * 0.621371192) : $km);
    }

    /**
     * 检测当前请求页面是否来自微信
     * @return bool
     */
    protected function checkIsInWeixinAndRedirectByConfig()
    {
        if (C('ENV') != 'local') {
            if ($this->ignoreClientBrowser()) {
                return;
            }
            //不是微信浏览器不能打开
            if (C("WX_OPEN_ON")) {
                if (!self::is_weixin()) {
                    redirect('https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf0263b790aec3378&redirect_uri&connect_redirect=1#wechat_redirect');
                }
            }
        }
    }

    /**
     * 系统维护中
     */
    protected function systemIsMaintenance()
    {
        if ($this->mchid == 251) {
            //echo "<div style='width: 100%;height: auto;text-align: center;font-size: 40px;padding-top: 300px;'>系统正在维护中...</div>";die;
        }
    }

    /**
     * 检测当前请求页面来自微信菜单
     * @return bool
     */
    protected function checkIsFromWeixinMenu()
    {
        if (isset($_GET[\CommonDefine::CHANNEL_BRANCH_PARMTER])) {
            return false;
        }
        return true;
    }

    /**
     * 单条推送短信
     * @return bool
     */
    protected function pushShortMessage($to, $datas, $tempCategoryId, $mchId)
    {
        if (C('SMS_ON')) {
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $smsUtil->sendTemplateSMS($to, $datas, $tempCategoryId, $mchId);
        }
    }

    /**
     * 批量推送短信
     * @return bool
     */
    protected function pushShortMessages($tos, $datas, $tempCategoryId, $mchId)
    {
        if (C('SMS_ON')) {
            if (!empty($tos)) {
                foreach ($tos as $k => $to) {
                    $this->pushShortMessage($to, $datas, $tempCategoryId, $mchId);
                }
            }
        }
    }

    /**
     *微信商户发送信息
     */
    protected function wechatPushInfo($mchid, $user_id, $userType, $open_id, $data, $msg_type)
    {
        $authorizer_access_token = $this->user_auth($mchid);
        \Log::write("微信模版消息通知进度，查询已授权公众号access_token: " . $authorizer_access_token, \Log::NOTICE);
        if (empty($authorizer_access_token)) {
            return -1;
        }
        if (empty($data)) {
            return -2;
        }
        try {
            $wtR = $this->find(array('c_id' => $msg_type,'mchid' => $mchid), "WechatTpl");
            $redirectUrlR = $this->getMerchatCallbackUrlByMchidAndType($mchid, $userType);
            $redirectUrl = $redirectUrlR->ret ? $redirectUrlR->data['url'] : "";
            $wt = new \WechatTpl();
            $wt->setWechatConfig($authorizer_access_token, $open_id, $wtR->data['wechat_tpl_code'], $data, $redirectUrl);
            //写入到数据库
            $param['mchid'] = $mchid;
            $param['user_id'] = $user_id;
            $param['type'] = $userType;
            $param['touser'] = $open_id;
            $param['template_id'] = $wtR->data['wechat_tpl_code'];
            $param['url'] = $redirectUrl;
            $param['status'] = $wt->getSendWechatStataus();
            $param['content'] = json_encode($data);
            \Log::write("微信模版消息通知进度，微信模版消息字典:" . json_encode($param), \Log::NOTICE);
            $retR = $this->add('WechatSendLog', $param);
            return 0;
        } catch (\Exception $e) {
            \Log::write("微信模版消息通知进度，发送模版消息失败:" . $e->getMessage(), \Log::ERR);
            return -3;
        }
    }

    protected function user_auth($mchid)
    {
        $userAuthR = $this->find(array('isauthorized' => 0,'uid' => $mchid), "UserAuth");

        if (!$userAuthR->ret || !$userAuthR->data['authorizer_access_token']) {
            return null;
        }

        if (time() - $userAuthR->data['timestamp'] > 7100) {
            $userAuthR->data['authorizer_access_token'] = \WechatAction::get_auth_user_access_token($mchid);
        }

        return $userAuthR->data['authorizer_access_token'];
    }

    /**
     * 批量微信推送
     */
    protected function pushWeixinMessage($to, $datas, $userType, $mchId, $msg_type)
    {
        if (C('WX_TEMPLATE_ON')) {
            if (!empty($to)) {
                return $this->wechatPushInfo($mchId, $to['account_id'], $userType, $to['openid'], $datas, $msg_type);
            }
        }
    }

    /**
     * 批量微信推送
     */
    protected function pushWeixinMessages($tos, $datas, $userType, $mchId, $msg_type)
    {
        if (C('WX_TEMPLATE_ON')) {
            if (!empty($tos)) {
                foreach ($tos as $k => $to) {
                    $this->pushWeixinMessage($to, $datas, $userType, $mchId, $msg_type);
                }
            }
        }
    }

    /**
     * 获取指定线路下的司机
     *
     */
    protected function getDriversByLineId($mchid, $line_type, $line_id)
    {
        $ret = new ResultModel(false);
        if (empty($mchid) || empty($line_type) ||  empty($line_id)) {
            return $ret;
        }
        $messageDriverField = 'd.*';
        $messageDriverOrder = 'dl.id ASC';
        $messageDriverWhere = "dl.mchid=".$mchid.' AND dl.type='.$line_type.' AND dl.line_id='.$line_id.' AND d.is_freeze ='.\CommonDefine::IS_FREEZE_0;
        $driverArr = M()->table('cp_driver_line dl')
            ->join('LEFT JOIN cp_driver d ON dl.driver_id = d.driver_id')
            ->where($messageDriverWhere)
            ->field($messageDriverField)
            ->order($messageDriverOrder)
            ->select();
        if ($driverArr) {
            $ret->ret = true;
            $ret->data = $driverArr;
            $ret->count = count($driverArr);
        }
        return $ret;
    }

    protected function getOrderType($num)
    {
        $msg = null;
        switch ($num) {
            case \CommonDefine::ORDER_TYPE_1: $msg = "拼车";
                break;
            case \CommonDefine::ORDER_TYPE_2: $msg = "包车";
                break;
            case \CommonDefine::ORDER_TYPE_3: $msg = "带货";
                break;
            case \CommonDefine::ORDER_TYPE_4: $msg = "代办";
                break;
            case \CommonDefine::ORDER_TYPE_5: $msg = "班线";
                break;
            case \CommonDefine::ORDER_TYPE_6: $msg = "顺风车";
                break;
            case \CommonDefine::ORDER_TYPE_7: $msg = "快车";
                break;
            case \CommonDefine::ORDER_TYPE_8: $msg = "学生号";
                break;
            case \CommonDefine::ORDER_TYPE_9: $msg = "家长互助";
                break;
            case \CommonDefine::ORDER_TYPE_11: $msg = "出租车";
                break;
            default: $msg = "其他";
                break;
        }
        if ($this->mchid == 969 && $num == \CommonDefine::ORDER_TYPE_5) {
            $msg = '城际网约车';
        }

        if ($this->mchid == 1116 && $num == \CommonDefine::ORDER_TYPE_5) {
            $msg = '预约订制出行';
        }

        if ($this->mchid == 1238) {
            if ($num == \CommonDefine::ORDER_TYPE_1) {
                $msg = '跨城拼车';
            } elseif ($num == \CommonDefine::ORDER_TYPE_5) {
                $msg = '定制客运';
            } elseif ($num == \CommonDefine::ORDER_TYPE_6) {
                $msg = '城际快车';
            }
        }

        if ($this->mchid == 950 && $num == \CommonDefine::ORDER_TYPE_3) {
            $msg = '小件速运';
        }

        if ($this->mchid == 950 && $num == \CommonDefine::ORDER_TYPE_4) {
            $msg = '跑腿';
        }

        return $msg;
    }

    /**
     * 获取订单状态描述
     * @param $state
     */
    protected function getOrderStateText($state)
    {
        $msg = null;
        switch ($state) {
            case \CommonDefine::ORDER_STATE_1: $msg = "预定中";
                break;
            case \CommonDefine::ORDER_STATE_2: $msg = "司机已接单";
                break;
            case \CommonDefine::ORDER_STATE_3: $msg = "乘客已上车";
                break;
            case \CommonDefine::ORDER_STATE_4: $msg = "在路上";
                break;
            case \CommonDefine::ORDER_STATE_5: $msg = "已送达";
                break;
            case \CommonDefine::ORDER_STATE_6: $msg = "已完成";
                break;
            case \CommonDefine::ORDER_STATE_7: $msg = "已取消";
                break;
            case \CommonDefine::ORDER_STATE_8: $msg = "已关闭";
                break;
            default: $msg = "订单异常";
                break;
        }
        return $msg;
    }

    /**
     * 检测分台是否已经有附件二维码
     * @param $branchid
     * @return ResultModel
     */
    protected function checkIsExistBranchQr($branchid)
    {
        $branchQrAttachmentR = $this->find(array('branchid' => $branchid), 'BranchQrAttachment');
        return $branchQrAttachmentR;
    }

    /**
     * 创建分台附件二维码
     * @param $mchid
     * @param $branchid
     * @return ResultModel
     */
    protected function createBranchQr($mchid, $branchid)
    {
        $ret = new ResultModel(false);
        try {
            if ($this->checkIsExistBranchQr($branchid)->ret) {
                return $ret;
            }
            $branchCode = md5($branchid);
            $channelBranchUrlR = $this->getChannelBranchPassengerUrl($mchid, $branchCode);
            if (!$channelBranchUrlR->ret) {
                return $ret;
            }

            $rootDir = C('ROOT_DIR');
            $uploadDir = C('UPLOAD_DIR');
            $url = C('FILE_ROOT');
            $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_BRANCH_QR_IMG'] . DIRECTORY_SEPARATOR .md5($mchid);
            $realName = 'qr_'.md5($branchid).'.png';
            $fullPath = $rootDir . $relatePath;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
            $urlPath = $url .$relateFullPath;
            $qrCodeContent = $channelBranchUrlR->data['url'];
            //生成二维码
            $qrCodeUtil = new \QrCodeUtil();
            $qrCodeUtil->createQrPngByText($qrCodeContent, $fullPath. DIRECTORY_SEPARATOR . $realName);

            //添加附件
            $data['branchid'] = $branchid;
            $data['mchid'] = $mchid;
            $data['branch_code'] = $branchCode;
            $data['qr_code'] = $qrCodeContent;
            $data['type'] = \CommonDefine::BRANCH_QR_TYPE_1;
            $data['qr_path'] = $relateFullPath;
            $data['qr_url'] = $urlPath;
            $ret = $this->add('BranchQrAttachment', $data);
        } catch (Exception $e) {
            return $ret;
        }
        return $ret;
    }

    /**
     * 检测司机是否已经有附件二维码
     * @param $driver_id
     * @return ResultModel
     */
    protected function checkIsExistDriverQr($driver_id)
    {
        $driverQrAttachmentR = $this->find(array('driver_id' => $driver_id), 'DriverQrAttachment');
        return $driverQrAttachmentR;
    }

    /**
     * 创建司机附件二维码
     * @param $mchid
     * @param $branchid
     * @return ResultModel
     */
    protected function createDriverQr($mchid, $driver_id)
    {
        $ret = new ResultModel(false);
        try {
            if ($this->checkIsExistDriverQr($driver_id)->ret) {
                return $ret;
            }
            $driverCode = md5($driver_id);
            $driverUrlR = $this->getDriverPassengerTrainsUrl($mchid, $driverCode);
            if (!$driverUrlR->ret) {
                return $ret;
            }

            $rootDir = C('ROOT_DIR');
            $uploadDir = C('UPLOAD_DIR');
            $url = C('FILE_ROOT');
            $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_DRIVER_QR_IMG'] . DIRECTORY_SEPARATOR .md5($mchid);
            $realName = 'qr_'.$driverCode.'.png';
            $fullPath = $rootDir . $relatePath;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
            $urlPath = $url .$relateFullPath;
            $qrCodeContent = $driverUrlR->data['url'];
            //生成二维码
            $qrCodeUtil = new \QrCodeUtil();
            $qrCodeUtil->createQrPngByText($qrCodeContent, $fullPath. DIRECTORY_SEPARATOR . $realName);

            //添加附件
            $data['driver_id'] = $driver_id;
            $data['mchid'] = $mchid;
            $data['driver_code'] = $driverCode;
            $data['qr_code'] = $qrCodeContent;
            $data['qr_path'] = $relateFullPath;
            $data['qr_url'] = $urlPath;
            $ret = $this->add('DriverQrAttachment', $data);
        } catch (Exception $e) {
            return $ret;
        }
        return $ret;
    }

    /**
     * @param $mchid 商户id
     * @param null $strore 文件大小(MB)
     * @param null $shortmess 短信
     * @param null $driv_num 司机人数
     * @return bool|null|\resultModel
     */
    public function updatePakeageInfo($mchid, $strore = null, $shortmess = null, $driv_num = null)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        if (!$mchR->ret) {
            return $this->output(new \ResultModel(false, '商户不存在'));
        }
        $strore = isset($strore) ? $strore : false;
        $shortmess = isset($shortmess) ? $shortmess : false;
        $driv_num = isset($driv_num) ? $driv_num : false;
        if ($strore) {
            $data['usestorage'] = $mchR->data['usestorage'] - $strore;
        }
        if ($shortmess) {
            $data['shortmessage'] = $mchR->data['shortmessage'] - $shortmess;
        }
        if ($driv_num) {
            $data['driver_num'] = $mchR->data['driver_num'] - $driv_num;
        }
        $data['admin_id'] = $mchid;
        $mchS = $this->save('Admin', $data);
        return $mchS;
    }

    protected function getCurrentVersionFrontendInfo()
    {
        return C('VERSION_FRONTEND_INFO');
    }

    protected function getCurrentVersionBackendInfo()
    {
        return C('VERSION_BACKEND_INFO');
    }

    protected function getPlatformUseUserStatistics()
    {
        $totalUseCount = 0;
        $useUserStatisticsR = $this->find('', 'UseUserStatistics');
        if ($useUserStatisticsR->ret) {
            $totalUseCount = $useUserStatisticsR->data['total_use_count'];
        }
        return $totalUseCount;
    }

    /**
     * 记账--线上支付&线上流水 先坐车后支付
     * @param $mchid
     * @param $branchid
     * @param $driver_id
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingOnAll($mchid, $branchid, $driver_id, $order_id)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$mchR->ret || !$branchR->ret || !$driverR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
            $driverR->data['balance'] += $orderR->data['offer_price'];//司机得到金额扣除平台分成
            $driverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'balance' => $driverR->data['balance']));
            if (!$driverS->ret) {
                return new \ResultModel(false);
            }
        }

        //分台账号余额,总台账户余额
        $channelPrice = 0;
        //渠道分台增加余额
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
            $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
            if ($channelBranchR->ret) {
                $channelBranchR->data['balance'] += $orderR->data['offer_channel_price'];
                $channelBranchR->data['total_amount'] += $orderR->data['price'];//交易总额
                $channelBranchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
                $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'balance' => $channelBranchR->data['balance'], 'total_amount' => $channelBranchR->data['total_amount'], 'total_turnover_on_amount' => $channelBranchR->data['total_turnover_on_amount']));
                if (!$channelBranchS->ret) {
                    return new \ResultModel(false);
                }

                $channelPrice = $orderR->data['offer_channel_price'];
            }
        } else {
            $channelPrice = 0;//无渠道，不扣渠道金额
        }

        //排除自己渠道用户给自己下单的二次统计
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_0 && $orderR->data['channel_account_id'] != $orderR->data['branchid']) {
            if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
                $branchR->data['balance'] += ($orderR->data['price'] - ($orderR->data['offer_price'] + $channelPrice));//余额
            } else {
                $branchR->data['balance'] += ($orderR->data['price'] - $channelPrice);//余额
            }
            $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
            $branchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_on_amount' => $branchR->data['total_turnover_on_amount']));
            if (!$branchS->ret) {
                return new \ResultModel(false);
            }
        }

        //总台
        if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
            $mchR->data['balance'] += ($orderR->data['price'] - ($orderR->data['offer_price'] + $channelPrice));//余额
        } else {
            $mchR->data['balance'] += ($orderR->data['price'] - $channelPrice);//余额
        }
        $mchR->data['total_amount'] += $orderR->data['price'];//交易总额
        $mchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
        $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $mchR->data['balance'], 'total_amount' => $mchR->data['total_amount'], 'total_turnover_on_amount' => $mchR->data['total_turnover_on_amount']));
        if (!$mchS->ret) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true);
    }

    /**
     * 记账--线下
     * @param $mchid
     * @param $branchid
     * @param $driver_id
     * @param $order_id
     */
    protected function bookkeepingDownAll($mchid, $branchid, $driver_id, $order_id)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$mchR->ret || !$branchR->ret || !$driverR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        //分台账号余额,总台账户余额
        if ($branchR->ret && $mchR->ret) {
            //渠道账号
            if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
                $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
                if ($channelBranchR->ret) {
                    $channelBranchR->data['total_amount'] += $orderR->data['price'];//交易总额
                    $channelBranchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
                    $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'balance' => $channelBranchR->data['balance'], 'total_amount' => $channelBranchR->data['total_amount'], 'total_turnover_down_amount' => $channelBranchR->data['total_turnover_down_amount']));
                    if (!$channelBranchS->ret) {
                        return new \ResultModel(false);
                    }
                }
            }

            //排除自己渠道用户给自己下单的二次统计
            if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
                if ($orderR->data['channel_account_id'] != $orderR->data['branchid']) {
                    //分台
                    // $branchR->data['balance'] += $orderR->data['offer_price'];
                    $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
                    $branchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
                }
            } else {
                //分台
                // $branchR->data['balance'] += $orderR->data['offer_price'];
                $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
                $branchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
            }

            //总台
            $mchR->data['total_amount'] += $orderR->data['price'];//交易总额
            $mchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_down_amount' => $branchR->data['total_turnover_down_amount']));
            $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $mchR->data['balance'], 'total_amount' => $mchR->data['total_amount'], 'total_turnover_down_amount' => $mchR->data['total_turnover_down_amount']));
            if (!$branchS->ret || !$mchS->ret) {
                return new \ResultModel(false);
            }
        }
        return new \ResultModel(true);
    }

    /**
     * 记账--线下，执行完成订单操作
     * @param $order_id
     */
    protected function bookkeepingDownAllByOver($order_id)
    {
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            return new \ResultModel(false);
        }

        $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
        $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
        if (!$mchR->ret || !$branchR->ret || !$orderR->ret) {
            return new \ResultModel(false, '订单数据异常');
        }

        //分台账号余额,总台账户余额
        if ($branchR->ret && $mchR->ret) {
            //渠道账号
            if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
                $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
                if ($channelBranchR->ret) {
                    $channelBranchR->data['total_amount'] += $orderR->data['price'];//交易总额
                    $channelBranchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
                    $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'balance' => $channelBranchR->data['balance'], 'total_amount' => $channelBranchR->data['total_amount'], 'total_turnover_down_amount' => $channelBranchR->data['total_turnover_down_amount']));
                    if (!$channelBranchS->ret) {
                        return new \ResultModel(false, '余额加入渠道分台失败');
                    }
                }
            }

            //排除自己渠道用户给自己下单的二次统计
            if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
                if ($orderR->data['channel_account_id'] != $orderR->data['branchid']) {
                    //分台
                    // $branchR->data['balance'] += $orderR->data['offer_price'];
                    $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
                    $branchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
                }
            } else {
                //分台
                // $branchR->data['balance'] += $orderR->data['offer_price'];
                $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
                $branchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
            }

            //总台
            $mchR->data['total_amount'] += $orderR->data['price'];//交易总额
            $mchR->data['total_turnover_down_amount'] += $orderR->data['price'];//线下交易总额
            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_down_amount' => $branchR->data['total_turnover_down_amount']));
            $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $mchR->data['balance'], 'total_amount' => $mchR->data['total_amount'], 'total_turnover_down_amount' => $mchR->data['total_turnover_down_amount']));
            if (!$branchS->ret || !$mchS->ret) {
                return new \ResultModel(false, '余额加入线路分台或Boss失败');
            }
        }
        return new \ResultModel(true);
    }

    /**
     * 记账（商户、渠道分台、调度分台）-线上支付记录流水 先支付后坐车
     * @param $mchid
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingMchOnTotalTurnoverOnAmount($mchid, $branchid, $order_id)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$mchR->ret || !$branchR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        //分台账号余额,总台账户余额
        $channelPrice = 0;
        //渠道分台增加余额
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
            $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
            if ($channelBranchR->ret) {
                $channelBranchR->data['total_amount'] += $orderR->data['price'];//交易总额
                $channelBranchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
                $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'total_amount' => $channelBranchR->data['total_amount'], 'total_turnover_on_amount' => $channelBranchR->data['total_turnover_on_amount']));
                if (!$channelBranchS->ret) {
                    return new \ResultModel(false);
                }

                $channelPrice = $orderR->data['offer_channel_price'];
            }
        } else {
            $channelPrice = 0;//无渠道，不扣渠道金额
        }

        //排除自己渠道用户给自己下单的二次统计
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_0 && $orderR->data['channel_account_id'] != $orderR->data['branchid']) {
            $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
            $branchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_on_amount' => $branchR->data['total_turnover_on_amount']));
            if (!$branchS->ret) {
                return new \ResultModel(false);
            }
        }

        $mchR->data['total_amount'] += $orderR->data['price'];//交易总额
        $mchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
        $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'total_amount' => $mchR->data['total_amount'], 'total_turnover_on_amount' => $mchR->data['total_turnover_on_amount']));
        if (!$mchS->ret) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true);
    }

    /**
     * 记账-线上支付加入（商户）
     * @param $mchid
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingMchOnlyOnTotalTurnoverOnAmount($mchid, $order_id)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$mchR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        //分台账号余额,总台账户余额
        $channelPrice = 0;
        //渠道分台增加余额
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
            $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
            if ($channelBranchR->ret) {
                $channelBranchR->data['total_amount'] += $orderR->data['price'];//交易总额
                $channelBranchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
                $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'total_amount' => $channelBranchR->data['total_amount'], 'total_turnover_on_amount' => $channelBranchR->data['total_turnover_on_amount']));
                if (!$channelBranchS->ret) {
                    return new \ResultModel(false);
                }

                $channelPrice = $orderR->data['offer_channel_price'];
            }
        } else {
            $channelPrice = 0;//无渠道，不扣渠道金额
        }

        $mchR->data['total_amount'] += $orderR->data['price'];//交易总额
        $mchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
        $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'total_amount' => $mchR->data['total_amount'], 'total_turnover_on_amount' => $mchR->data['total_turnover_on_amount']));
        if (!$mchS->ret) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true);
    }

    /**
     * 记账-线上支付加入(分台)
     * @param $mchid
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingBranchOnlyOnTotalTurnoverOnAmount($branchid, $order_id)
    {
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$branchR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        //排除自己渠道用户给自己下单的二次统计
        if ($orderR->data['channel_type'] != \CommonDefine::CHANNEL_TYPE_1 || $orderR->data['channel_account_id'] != $orderR->data['branchid']) {
            $branchR->data['total_amount'] += $orderR->data['price'];//交易总额
            $branchR->data['total_turnover_on_amount'] += $orderR->data['price'];//线上交易总额
            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_on_amount' => $branchR->data['total_turnover_on_amount']));
            if (!$branchS->ret) {
                return new \ResultModel(false);
            }
        }
        return new \ResultModel(true);
    }

    /**
     * 记账（商户）-交易完成加入余额
     * @param $mchid
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingMchOnBalance($mchid, $branchid, $driver_id, $order_id)
    {
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$mchR->ret || !$branchR->ret || !$driverR->ret || !$orderR->ret) {
            return new \ResultModel(false);
        }

        if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
            $newDriverBalance = $driverR->data['balance'] + $orderR->data['offer_price'];//司机得到金额扣除平台分成
            $driverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'balance' => $newDriverBalance));
            if (!$driverS->ret) {
                return new \ResultModel(false);
            }

            $log = "[正常执行:".__LINE__."]订单编号:".$orderR->data['order_no'];
            $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_3, $orderR->data['driver_id'], $driverR->data['balance'], $orderR->data['offer_price'], $newDriverBalance, $log);
            if (!$recordR->ret) {
                return new \ResultModel(false);
            }
        }

        //分台账号余额,总台账户余额
        $channelPrice = 0;
        //渠道分台增加余额
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
            $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
            if ($channelBranchR->ret) {
                $newChannelBranchBalance = $channelBranchR->data['balance'] + $orderR->data['offer_channel_price'];
                $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'balance' => $newChannelBranchBalance));
                if (!$channelBranchS->ret) {
                    return new \ResultModel(false);
                }

                $channelPrice = $orderR->data['offer_channel_price'];

                $log = "[正常执行:".__LINE__."]订单编号:".$orderR->data['order_no'];
                $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_2, $orderR->data['channel_account_id'], $channelBranchR->data['balance'], $orderR->data['offer_channel_price'], $newChannelBranchBalance, $log);
                if (!$recordR->ret) {
                    return new \ResultModel(false);
                }
            }
        } else {
            $channelPrice = 0;//无渠道，不扣渠道金额
        }

        //排除自己渠道用户给自己下单的二次统计
        /*        if ($orderR->data['channel_type'] != \CommonDefine::CHANNEL_TYPE_1 || $orderR->data['channel_account_id'] != $orderR->data['branchid']) {
                    //$branchR->data['balance'] += $orderR->data['offer_price'];
                    $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance'], 'total_amount' => $branchR->data['total_amount'], 'total_turnover_on_amount' => $branchR->data['total_turnover_on_amount']));
                    if (!$branchS->ret) {
                        return new \ResultModel(false);
                    }
                }*/

        //总台
        $updateMchBalance = 0;
        if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
            $updateMchBalance = $orderR->data['real_price'] - ($orderR->data['offer_price'] + $channelPrice);
        } else {
            $updateMchBalance = $orderR->data['real_price'] - $channelPrice;
        }
        $newMchBalance = $mchR->data['balance'] + $updateMchBalance;

        $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $newMchBalance));
        if (!$mchS->ret) {
            return new \ResultModel(false);
        }

        $log = "[正常执行:".__LINE__."]订单编号:".$orderR->data['order_no'];
        $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_1, $mchR->data['admin_id'], $mchR->data['balance'], $updateMchBalance, $newMchBalance, $log);
        if (!$recordR->ret) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true);
    }

    /**
     * 记账（商户）-手动完成订单计入余额
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingMchOnBalanceByOver($order_id)
    {
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            return new \ResultModel(false);
        }
        $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
        $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
        if (!$mchR->ret || !$branchR->ret) {
            return new \ResultModel(false);
        }

        //分台账号余额,总台账户余额
        $channelPrice = 0;
        //渠道分台增加余额
        if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) {
            $channelBranchR = $this->find(array('admin_id' => $orderR->data['channel_account_id']), 'Admin');
            if ($channelBranchR->ret) {
                $newChannelBranchBalance = $channelBranchR->data['balance'] + $orderR->data['offer_channel_price'];
                $channelBranchS = $this->save('Admin', array('admin_id' => $channelBranchR->data['admin_id'], 'balance' => $newChannelBranchBalance));
                if (!$channelBranchS->ret) {
                    return new \ResultModel(false);
                }

                $channelPrice = $orderR->data['offer_channel_price'];

                $log = "[完成订单]订单编号:".$orderR->data['order_no'];
                $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_2, $orderR->data['channel_account_id'], $channelBranchR->data['balance'], $orderR->data['offer_channel_price'], $newChannelBranchBalance, $log);
                if (!$recordR->ret) {
                    return new \ResultModel(false);
                }
            }
        }

        //判定如果已有司机接单则需要记账至司机余额
        if (!empty($orderR->data['driver_id'])) {
            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
            if (!$driverR->ret) {
                return new \ResultModel(false);
            }

            if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
                $newDriverBalance = $driverR->data['balance'] + $orderR->data['offer_price'];//司机得到金额扣除平台分成
                $driverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'balance' => $newDriverBalance));
                if (!$driverS->ret) {
                    return new \ResultModel(false);
                }

                $log = "[完成订单]订单编号:".$orderR->data['order_no'];
                $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_3, $orderR->data['driver_id'], $driverR->data['balance'], $orderR->data['offer_price'], $newDriverBalance, $log);
                if (!$recordR->ret) {
                    return new \ResultModel(false);
                }
            }

            //总台
            $updateMchBalance = 0;
            if ($driverR->data['driver_type'] == \CommonDefine::DRIVER_TYPE_0) {
                $updateMchBalance = $orderR->data['real_price'] - ($orderR->data['offer_price'] + $channelPrice);
            } else {
                $updateMchBalance = $orderR->data['real_price'] - $channelPrice;
            }
            $newMchBalance = $mchR->data['balance'] + $updateMchBalance;

            $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $newMchBalance));
            if (!$mchS->ret) {
                return new \ResultModel(false);
            }

            $log = "[完成订单]订单编号:".$orderR->data['order_no'];
            $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_1, $mchR->data['admin_id'], $mchR->data['balance'], $updateMchBalance, $newMchBalance, $log);
            if (!$recordR->ret) {
                return new \ResultModel(false);
            }
        } else {
            //全部计入总台
            $updateMchBalance = $orderR->data['real_price'] - $channelPrice;
            $newMchBalance = $mchR->data['balance'] + $updateMchBalance;

            $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $newMchBalance));
            if (!$mchS->ret) {
                return new \ResultModel(false);
            }
            $log = "[完成订单]订单编号:".$orderR->data['order_no'];
            $recordR = $this->recordAccountBalance($orderR->data['mchid'], \CommonDefine::SYSTEM_ROLE_1, $mchR->data['admin_id'], $mchR->data['balance'], $updateMchBalance, $newMchBalance, $log);
            if (!$recordR->ret) {
                return new \ResultModel(false);
            }
        }

        return new \ResultModel(true);
    }



    /**
     * 记账-取消订单费率加入商家收益
     * @param $mchid
     * @param $order_id
     * @return ResultModel
     */
    protected function bookkeepingMchOnBalanceByRefund($mchid, $order_id)
    {
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            return new \ResultModel(false);
        }
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        //总台
        $afterBalance = $mchR->data['balance'] + $orderR->data['refund_fee'];//计入余额
        $mchS = $this->save('Admin', array('admin_id' => $mchR->data['admin_id'], 'balance' => $afterBalance));
        if (!$mchS->ret) {
            return new \ResultModel(false);
        }
        $log = "乘客编号:".$orderR->data['passenger_id'].
               "，申请退款，订单编号：".$orderR->data['order_no'].
               "，申请退款总金额：".$orderR->data['real_price'].
               "，车票退款手续费：".$orderR->data['refund_fee'].
               "，车票实际退款金额：".$orderR->data['refund_amount'].
               "，保险实际退款金额：".$orderR->data['refund_insurance_amount'].
               "，退款实际到账总金额:".$orderR->data['refund_total_amount'];
        $recordR = $this->recordAccountBalance($mchid, \CommonDefine::SYSTEM_ROLE_1, $mchid, $mchR->data['balance'], $orderR->data['refund_fee'], $afterBalance, $log);
        if (!$recordR->ret) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true);
    }

    /**
     * 记录账户余额变更记录
     * @param $mchid
     * @param $accountType
     * @param $account_id
     * @param $amount
     * @param $log
     * @return ResultModel
     */
    protected function recordAccountBalance($mchid, $accountType, $account_id, $before, $amount, $after, $log)
    {
        if ($mchid < 0 || $accountType < 0 || $account_id < 0) {
            return new \ResultModel(false);
        }
        $accountBalanceRecordData['mchid'] = $mchid;
        $accountBalanceRecordData['account_type'] = $accountType;
        $accountBalanceRecordData['account_id'] = $account_id;
        $accountBalanceRecordData['before'] = $before;
        $accountBalanceRecordData['amount'] = $amount;
        $accountBalanceRecordData['after'] = $after;
        $accountBalanceRecordData['log'] = $log;
        $accountBalanceRecordA = $this->add('AccountBalanceRecord', $accountBalanceRecordData);
        if (!$accountBalanceRecordA->ret) {
            return new \ResultModel(false);
        }
        return new \ResultModel(true);
    }

    protected function getLineClassDetail($class_line_id)
    {
        $where = 'lct.line_class_train_id = '.$class_line_id;
        $field = "lct.line_class_id,
                            lc.start_time_type,lc.start_earliest_time,lc.end_latest_time,lc.start_time,
                            lc.refund_time_set,
                            lc.start_city_code,gdrs.name as start_city_name,
                            lc.end_city_code,gdre.name as end_city_name,
                            lc.start_address_type,
                            lc.end_address_type,
                            lc.is_return,
                            lc.return_start_time_type,
                            lc.return_start_time,
                            lc.return_start_address_type,
                            lc.return_start_alias,
                            lc.return_end_address_type,
                            lc.return_end_alias,
                            lct.line_class_train_id,
                            lct.line_class_train_no,
                            lct.remain_tickets,
                            lct.price,
                            lc.start_name,
                            lc.end_name,
                            lct.summary,
                            lct.travel_time,
                            d.car_brand,
                            via.province_code as via_province_code,
                            via.province_name as via_province_name,
                            via.city_code as via_city_code,
                            via.city_name as via_city_name,
                            via.area_code as via_area_code,
                            via.area_name as via_area_name,
                            via.name as via_name
                            ";
        $lineClassTrain = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_dingzhikeyun_line_via_points via ON lc.id = via.line_class_id')
            ->join('LEFT JOIN cp_driver d ON d.driver_id = lct.driver_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->where($where)
            ->field($field)
            ->find();

        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        //返程
        //固定上车点
        $returnStartPointField = 'alias,longitude,latitude,use_time';
        $returnStartPointOrder = 'id asc';
        //固定下车点
        $returnEndPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $returnEndPointOrder = 'id asc';

        if ($lineClassTrain['start_address_type'] == 1) {
            $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
            $lineClassTrain['start_point'] = M()->table('cp_line_class_point')
                ->where($startPointWhere)
                ->field($startPointField)
                ->order($startPointOrder)
                ->select();
        } else {
            $lineClassTrain['start_point'] = [];
        }

        if ($lineClassTrain['end_address_type'] == 1) {
            $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
            $lineClassTrain['end_point'] = M()->table('cp_line_class_point')
                ->where($endPointWhere)
                ->field($endPointField)
                ->order($endPointOrder)
                ->select();
        } else {
            $lineClassTrain['end_point'] = [];
        }

        //含返程
        if ($lineClassTrain['is_return'] == 1) {
            if ($lineClassTrain['return_start_address_type'] == 1) {
                $returnStartPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 1';
                $lineClassTrain['return_start_point'] = M()->table('cp_line_class_point')
                    ->where($returnStartPointWhere)
                    ->field($returnStartPointField)
                    ->order($returnStartPointOrder)
                    ->select();
            } else {
                $lineClassTrain['return_start_point'] = [];
            }

            if ($lineClassTrain['return_end_address_type'] == 1) {
                $returnEndPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 1';
                $lineClassTrain['return_end_point'] = M()->table('cp_line_class_point')
                    ->where($returnEndPointWhere)
                    ->field($returnEndPointField)
                    ->order($returnEndPointOrder)
                    ->select();
            } else {
                $lineClassTrain['return_end_point'] = [];
            }
        }

        if (empty($lineClassTrain)) {
            return new \ResultModel(false);
        }

        return new \ResultModel(true, $lineClassTrain, 1);
    }

    /**
     * 班线车出票
     * @param $line_class_train_id
     * @param $seat
     * @return ResultModel
     */
    protected function drawTicket(\ResultModel $orderR)
    {
        $ret = new \ResultModel(false);
        if (!$orderR->ret) {
            return $ret;
        }

        $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
        //        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
        if (!empty($lineClassTrainArr)) {
            $upLineClassTrainData = [];
            if (!empty($orderR->data['seat_optional'])) {
                $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                $seatIdsArr = [];
                foreach ($seatOptionalArr as $seatOptional) {
                    $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                }
                $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr, \CommonDefine::OPTIONAL_1);
                if (!$updateSeatPriceDataR->ret) {
                    return  $this->output(new \ResultModel(false, '操作异常，请联系管理员!'));
                }
                $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
            }
            $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
            $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] - $orderR->data['book_seating'];
            $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
            return $lineClassTrainS;
        }

        return $ret;
    }

    /**
     * 班线车退票
     * @param $orderR
     * @return ResultModel
     */
    protected function refundTicket(\ResultModel $orderR)
    {
        $ret = new \ResultModel(false);
        if (!$orderR->ret) {
            return $ret;
        }

        $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
        //        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
        if (!empty($lineClassTrainArr)) {
            $upLineClassTrainData = [];
            if (!empty($orderR->data['seat_optional'])) {
                $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                $seatIdsArr = [];
                foreach ($seatOptionalArr as $seatOptional) {
                    $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                }
                $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr, \CommonDefine::OPTIONAL_0);
                if (!$updateSeatPriceDataR->ret) {
                    return  $this->output(new \ResultModel(false, '退票失败，请联系管理员!'));
                }
                $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
            }
            //退票
            $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
            $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] + $orderR->data['book_seating'];
            $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
            return $lineClassTrainS;
        }

        return $ret;
    }

    /**
     * 学生号出票
     * @param $line_class_train_id
     * @param $seat
     * @return ResultModel
     */
    protected function yyDrawTicket($line_class_train_id, $seat)
    {
        $ret = new \ResultModel(false);
        if ($seat <= 0) {
            return $ret;
        }

        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'YyLineClassTrain');
        if ($yyLineClassTrainR->ret) {
            $upYyLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
            if ($yyLineClassTrainR->data['remain_tickets'] >= $seat) {
                $upYyLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] - $seat;
                $yyLineClassTrainS = $this->save('YyLineClassTrain', $upYyLineClassTrainData);
                return $yyLineClassTrainS;
            }
        }

        return $ret;
    }

    /**
     * 学生号退票
     * @param $line_class_train_id
     * @param $seat
     * @return ResultModel
     */
    protected function yyRefundTicket($line_class_train_id, $seat)
    {
        $ret = new \ResultModel(false);
        if ($seat <= 0) {
            return $ret;
        }

        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'YyLineClassTrain');
        if ($yyLineClassTrainR->ret) {
            //退票
            $upYyLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
            $upYyLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] + $seat;
            $yyLineClassTrainS = $this->save('YyLineClassTrain', $upYyLineClassTrainData);
            return $yyLineClassTrainS;
        }

        return $ret;
    }

    /**
     * 是否已过退票时间
     * @param $order_id
     * @return ResultModel
     */
    protected function checkeIsAllowRefundTicket($order_id)
    {
        $ret = new \ResultModel(false);
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            return $ret;
        }

        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
        if ($lineClassTrainR->ret) {
            $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
            if ($lineClassR->ret) {
                if ($lineClassR) {
                    $refund_time_set = strtotime($orderR->data['start_time']) - ($lineClassR->data['refund_time_set'] * 60);
                    if (time() < $refund_time_set) {
                        $ret->ret = true;
                        return $ret;
                    }
                }
            }
        }

        return $ret;
    }


    /**
     * 计算退款金额及退款费率
     * @param $startTime
     * @return ResultModel
     */
    protected function calculateRefundTicketFee($startTime, $realPrice)
    {
        $ret = new \ResultModel(false);
        $refundSecond = strtotime($startTime) - time();
        if ($refundSecond < 0) {
            return $ret;
        }
        $refundMin = ceil($refundSecond / 60);
        $ret->ret = true;
        $ret->data['refund_fee'] = 0;
        $ret->data['refund_amount'] = $realPrice;
        $ret->data['refund_ticket_ratio'] = 0;

        $where['mchid'] = $this->mchid;
        $where['refund_ticket_time'] = array('EGT', $refundMin);
        $refundTicketConfigsR = $this->select($where, 1, 1, 'refund_ticket_time ASC', 'RefundTicketConfig');
        if ($refundTicketConfigsR->ret) {
            $ret->data['refund_fee'] = round($realPrice * $refundTicketConfigsR->data[0]['refund_ticket_ratio'] / 100.00, 2);
            $ret->data['refund_amount'] = $realPrice - $ret->data['refund_fee'];
            $ret->data['refund_ticket_ratio'] = $refundTicketConfigsR->data[0]['refund_ticket_ratio'];
        }
        return $ret;
    }

    /**
     * 获取班线车司机
     */
    protected function getLineClassTrainDriver($lineClassTrainId, $number)
    {
        $ret = new \ResultModel(false);
        $where = ' lctd.line_class_train_id = '.$lineClassTrainId;
        $where .= ' AND d.state != 3 ';
        $fields = 'd.driver_id,d.residual_seating,lctd.appoint_number,lctd.id';
        $order = 'lctd.sort asc';
        $drivers = M()->table('cp_line_class_train_driver lctd')
            ->join("LEFT JOIN cp_driver d ON d.driver_id = lctd.driver_id")
            ->where($where)
            ->field($fields)
            ->order($order)
            ->select();
        if (empty($drivers)) {
            return $ret;
        }

        //依次获取司机
        foreach ($drivers as $k => $driver) {
            if (($driver['appoint_number'] + $number) < $driver['residual_seating']) {
                $lctdS = $this->save('LineClassTrainDriver', array('id' =>  $driver['id'], 'appoint_number' => ($driver['appoint_number'] + $number)));
                if ($lctdS->ret) {
                    $ret->ret = true;
                    $ret->data = ['driver_id' => $driver['driver_id']];
                    $ret->count = 1;
                    return $ret;
                }
            }
        }

        //如果都无法匹配默认就将订单指派给第一个司机
        $ret->ret = true;
        $ret->data = ['driver_id' => $drivers[0]['driver_id']];
        $ret->count = 1;
        return $ret;
    }

    /**
     * 是否是该班线车里的司机
     */
    protected function checkIsLineClassTrainDriver($lineClassTrainId, $driver_id)
    {
        $where['driver'] = $driver_id;
        $where['line_class_train_id'] = $lineClassTrainId;
        $lineClassTrainDriverR = $this->find($where, 'LineClassTrainDriver');
        return $lineClassTrainDriverR;
    }

    /**
     * 检测时间是否已过售票时间
     */
    protected function checkTimeIsPassSellTime($time, $lineClassTrainId)
    {
        $lineClassTrainR = $this->find(array('line_class_train_id' => $lineClassTrainId), 'LineClassTrain');
        if (!$lineClassTrainR->ret) {
            return false;
        }

        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
        if (!$lineClassR->ret) {
            return false;
        }

        if ($lineClassR->data['start_time_type'] == 1) {
            $startTime = $lineClassR->data['start_time'];

            $startDateTime = $lineClassTrainR->data['start_date'] ." ". $startTime;
            if (strtotime($startDateTime) < $time) {
                return false;
            }
        } else {
            if (\getTimeNumber($lineClassR->data['end_latest_time']) > \getTimeNumber($lineClassR->data['start_earliest_time'])) {//当天
                $startTime = $lineClassR->data['end_latest_time'];

                $startDateTime = $lineClassTrainR->data['start_date'] ." ". $startTime;
                if (strtotime($startDateTime) < $time) {
                    return false;
                }
            } else {//第二天
                $startTime = $lineClassR->data['start_earliest_time'];
                $startNextDate = date("Y-m-d", strtotime($lineClassTrainR->data['start_date']) + 86400);
                $startDateTime = $startNextDate ." ". $startTime;
                if (strtotime($startDateTime) < $time) {
                    return false;
                }
            }
        }

        if (\getTimeNumber($lineClassR->data['stop_sell_time']) != 0) {
            $stopSellDateTime = date('Y-m-d H:i:s', strtotime($lineClassTrainR->data['start_date'] ." ". $lineClassR->data['stop_sell_time']."-".$lineClassR->data['stop_sell_number']."day"));

            if (strtotime($stopSellDateTime) < $time) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检测时间是否在营业时间内
     */
    protected function checkIsInAppointmentTime($start_time, $start_appointment_time, $end_appointment_time)
    {
        $currentDate = date('Y-m-d');
        $start_time_stamp = strtotime($start_time);
        $start_appointment_time_stamp = strtotime($currentDate." ".$start_appointment_time);
        $end_appointment_time_stamp = strtotime($currentDate." ".$end_appointment_time);
        if ($start_time_stamp >= $start_appointment_time_stamp && $start_time_stamp <= $end_appointment_time_stamp) {
            return true;
        }

        return false;
    }

    /**
     * 获取学生号班线车司机
     */
    protected function getYyLineClassTrainDriver($lineClassTrainId, $number)
    {
        $ret = new \ResultModel(false);
        $where = ' yylctd.line_class_train_id = '.$lineClassTrainId;
        $fields = 'd.driver_id,d.residual_seating,yylctd.appoint_number,yylctd.id';
        $order = 'yylctd.sort asc';
        $drivers = M()->table('cp_yy_line_class_train_driver yylctd')
            ->join("LEFT JOIN cp_driver d ON d.driver_id = yylctd.driver_id")
            ->where($where)
            ->field($fields)
            ->order($order)
            ->select();
        if (empty($drivers)) {
            return $ret;
        }

        //依次获取司机
        foreach ($drivers as $k => $driver) {
            if (($driver['appoint_number'] + $number) < $driver['residual_seating']) {
                $lctdS = $this->save('YyLineClassTrainDriver', array('id' =>  $driver['id'], 'appoint_number' => ($driver['appoint_number'] + $number)));
                if ($lctdS->ret) {
                    $ret->ret = true;
                    $ret->data = ['driver_id' => $driver['driver_id']];
                    $ret->count = 1;
                    return $ret;
                }
            }
        }
        //如果都无法匹配默认就将订单指派给第一个司机
        $ret->ret = true;
        $ret->data = ['driver_id' => $drivers[0]['driver_id']];
        $ret->count = 1;
        return $ret;
    }

    /**
     * 删除名称后面市或县
     * @param $name
     */
    protected function removeCityOrCountyLastName($name)
    {
        $retStr = $name;
        $subStr = substr($name, (strlen($name) - 3), 3);
        if (($subStr == '市' || $subStr == '县') && (strlen($name) > 6)) {
            $retStr = substr($name, 0, (strlen($name) - 3));
        }
        return $retStr;
    }

    /**
     * 获取地址编码类型
     */
    protected function getAddressCodeType($addressCode)
    {
        if (substr($addressCode, 4, 2) != '00') {
            return 3;
        } elseif (substr($addressCode, 2, 2) != '00') {
            return 2;
        }
        return 1;
    }

    /**
     * 发送短信
     * @param $order_id
     * @param $to_user_type
     */
    protected function sendSms($order_id, $to_user_type, $message_type, $to_user_id = null)
    {
        try {
            if (C('SMS_ON')) {
                $orderR = $this->find(array('order_id' => $order_id), 'Order');
                if (!$orderR->ret) {
                    return;
                }
                $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
                if (!$mchR->ret) {
                    return;
                }

                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                switch ($to_user_type) {
                    case \CommonDefine::USER_TYPE_1:{//通知用户
                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0: {//下单成功
                                if (!in_array($orderR->data['mchid'], C('NOTIFY_PASSENGER_MERCHANT_IDS'))) {
                                    // 支持发送乘客短信的商户，才发送短信
                                    return;
                                }

                                $passengerName = "尾号".$this->getCellphoneSubTailFour($orderR->data['reseverd_phone']);
                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                if ($passengerR->ret) {
                                    $passengerName = $passengerR->data['name'];
                                }
                                $smsData = [];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:
                                    case \CommonDefine::ORDER_TYPE_2:
                                    case \CommonDefine::ORDER_TYPE_3:
                                    case \CommonDefine::ORDER_TYPE_4: {
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if (!$lineR->ret) {
                                            return ;
                                        }

                                        $lineInfo = ",订单编号：".$orderR->data['order_no'].",";
                                        $lineInfo .= "出发时间：".substr($orderR->data['start_time'], 5, 11).",";
                                        $lineInfo .= $lineR->data['start_name']."->".$lineR->data['end_name'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $lineInfo .= ",座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        if ($orderR->data['mchid'] == 1258) {
                                            $lineInfo .= ",上车点：".$orderR->data['start_address_remark'];
                                            $lineInfo .= ",下车点：".$orderR->data['end_address_remark'];
                                            $lineInfo .= ",保持电话通畅,司机会提前20分钟来联系您";
                                        } else {
                                            $lineInfo .= ",请佩戴好口罩,保持电话通畅,司机会提前与您约定上车时间";
                                        }
                                        $smsData = array($passengerName, $lineInfo ,$mchR->data['tel']);
                                        break;
                                    }
                                }
                                $smsUtil->sendTemplateSMS($orderR->data['reseverd_phone'], $smsData, \SMSUtil::TEMP_ID_PASSENGER_ORDER_SUCCESS, $orderR->data['mchid']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1: {//司机已经接单
                                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                //车牌号加密
                                $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);

                                $smsData = array($this->encryptionDriverName($driverR->data['name']), $driverR->data['cellphone'], $carTailNumber, $mchR->data['tel']);
                                $smsData[2] .= "，订单编号:".$orderR->data['order_no'];
                                $smsData[2] .= "，出发时间:".$orderR->data['start_time'];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[2] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_2:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_3:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[2] .= ("，重量:".$orderR->data['weight']."kg");
                                        $smsData[2] .= ("，距离:".$orderR->data['kilometre']."km");
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_4:{
                                        $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                                        $smsData[2] .= "，服务名称:".$agencyR->data['name'];
                                        $smsData[2] .= "，服务说明:".$agencyR->data['summary'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[2] .= "，人数:".$orderR->data['book_seating'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $smsData[2] .= "，座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_7:{
                                        $lineFastR = $this->find(array('id' => $orderR->data['line_id']), 'LineFast');
                                        if ($lineFastR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineFastR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineFastR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[2] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_11:{
                                        $lineTaxiR = $this->find(array('id' => $orderR->data['line_id']), 'LineTaxi');
                                        if ($lineTaxiR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[2] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[2] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[2] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    default:
                                        return;
                                }

                                //2025.2.17 新增乘客接收短信
                                $this->sendInnerNotification('passenger', 'sms', 'sms_passenger_new_order_dispatched', $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_2: {//提前定时通知
                                //乘客接收短信
                                if (empty($orderR->data['reseverd_phone'])) {
                                    return;
                                }
                                $smsData = [];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:
                                    case \CommonDefine::ORDER_TYPE_2:
                                    case \CommonDefine::ORDER_TYPE_3:
                                    case \CommonDefine::ORDER_TYPE_4: {
                                        return;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if (!$lineR->ret) {
                                            return ;
                                        }

                                        $lineInfo = "的订单编号：".$orderR->data['order_no']."即将出发，";
                                        $lineInfo .= "出发时间：".substr($orderR->data['start_time'], 5, 11).",";
                                        $lineInfo .= $lineR->data['start_name']."->".$lineR->data['end_name'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $lineInfo .= "，座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        $lineInfo .= "，保持电话通畅";
                                        $smsData = array($orderR->data['reseverd_phone'], $lineInfo , $mchR->data['tel']);
                                        break;
                                    }
                                }
                                $smsUtil->sendTemplateSMS($orderR->data['reseverd_phone'], $smsData, \SMSUtil::message_type, $orderR->data['mchid']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_3:{
                                $smsData = [];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:
                                    case \CommonDefine::ORDER_TYPE_2:
                                    case \CommonDefine::ORDER_TYPE_3:
                                    case \CommonDefine::ORDER_TYPE_4:
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $smsData = array($orderR->data['order_no'], $mchR->data['tel']);
                                        $smsUtil->sendTemplateSMS($orderR->data['reseverd_phone'], $smsData, \SMSUtil::TEMP_ID_PSCANCEL, $orderR->data['mchid']);
                                        break;
                                    }
                                }
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_2:{//通知司机
                        if (in_array($orderR->data['mchid'], C('NO_NOTIFY_DRIVER_MERCHANT_IDS'))) {
                            // 不支持发送司机短信的商户，不发送短信
                            return;
                        }

                        $driverR = $this->find(array('driver_id' => (empty($to_user_id) ? $orderR->data['driver_id'] : $to_user_id)), 'Driver');
                        if (!$driverR->ret) {
                            return;
                        }
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0:{//新订单

                                $smsData = array($orderR->data['order_no'], $orderR->data['reseverd_phone'].",[".$mchR->data['mchname']."]");
                                $shortMessageServiceR = $this->find(array('mchid' => $orderR->data['mchid']), 'ShortMessageService');
                                if ($shortMessageServiceR->ret) {
                                    $smsData = array($orderR->data['order_no'], $orderR->data['reseverd_phone']);
                                }
                                if ($orderR->data['is_temp'] == \CommonDefine::ORDER_IS_TEMP_1) {
                                    $smsData[0] .= "(代约)";
                                }
                                $smsData[0] .= ("，[". $this->getOrderType($orderR->data['type'])."]");
                                $smsData[0] .= "，出发时间:".$orderR->data['start_time'];

                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_2:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_3:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= ("，重量:".$orderR->data['weight']."kg");
                                        $smsData[0] .= ("，距离:".$orderR->data['kilometre']."km");
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_4:{
                                        $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                                        $smsData[0] .= "，服务名称:".$agencyR->data['name'];
                                        $smsData[0] .= "，服务说明:".$agencyR->data['summary'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $smsData[0] .= "，座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_7:{
                                        $lineFastR = $this->find(array('id' => $orderR->data['line_id']), 'LineFast');
                                        if ($lineFastR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineFastR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineFastR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_11:{
                                        $lineTaxiR = $this->find(array('id' => $orderR->data['line_id']), 'LineTaxi');
                                        if ($lineTaxiR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    default:
                                        return;
                                }
                                //2025.2.17 新增新订单司机短信通知
                                $this->sendInnerNotification('driver', 'sms', 'sms_driver_new_order', $orderR->data['order_id']);

                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1:{
                                $smsData = [];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:
                                    case \CommonDefine::ORDER_TYPE_2:
                                    case \CommonDefine::ORDER_TYPE_3:
                                    case \CommonDefine::ORDER_TYPE_4:
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $smsData = array($orderR->data['order_no'], $mchR->data['tel']);
                                        $smsUtil->sendTemplateSMS($driverR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_PSCANCEL, $orderR->data['mchid']);
                                        break;
                                    }
                                }
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2:{
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_3:{//通知分台
                        $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
                        if (!$branchR->ret) {
                            return;
                        }
                        if (in_array($branchR->data['parent_admin_id'], C('NO_NOTIFY_BRANCH_MERCHANT_IDS'))) {
                            // 不支持发送分台短信的商户，不发送短信
                            return;
                        }
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0:{//新订单
                                $smsData = array($orderR->data['order_no'], $orderR->data['reseverd_phone'].",[".$mchR->data['mchname']."]");
                                $smsData[0] .= ("，[". $this->getOrderType($orderR->data['type'])."]");
                                $smsData[0] .= "，出发时间:".$orderR->data['start_time'];
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_2:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_3:{
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= ("，重量:".$orderR->data['weight']."kg");
                                        $smsData[0] .= ("，距离:".$orderR->data['kilometre']."km");
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_4:{
                                        $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                                        $smsData[0] .= "，服务名称:".$agencyR->data['name'];
                                        $smsData[0] .= "，服务说明:".$agencyR->data['summary'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5:{
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $smsData[0] .= "，座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_7:{
                                        $lineFastR = $this->find(array('id' => $orderR->data['line_id']), 'LineFast');
                                        if ($lineFastR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineFastR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineFastR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_11:{
                                        $lineTaxiR = $this->find(array('id' => $orderR->data['line_id']), 'LineTaxi');
                                        if ($lineTaxiR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineTaxiR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $smsData[0] .= "，上车点:".$startCityName.$orderR->data['start_address_remark'];
                                        $smsData[0] .= "，下车点:".$endCityName.$orderR->data['end_address_remark'];
                                        $smsData[0] .= "，人数:".$orderR->data['book_seating'];
                                        break;
                                    }
                                    default:
                                        return;
                                }
                                //调用微服务短信发送
                                $this->sendInnerNotification('branch', 'sms', 'sms_branch_new_order', $orderR->data['order_id']);
                                // $smsUtil->sendTemplateSMS($branchR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_NEW_ORDER, $orderR->data['mchid']);
                                if ($mchR->data['admin_id'] == 950) {
                                    $smsData[1] .= "，服务商:".$branchR->data['mchname'];
                                    $smsUtil->sendTemplateSMS('13330496769', $smsData, \SMSUtil::TEMP_ID_NEW_ORDER, $orderR->data['mchid']);
                                    $smsUtil->sendTemplateSMS('13898009108', $smsData, \SMSUtil::TEMP_ID_NEW_ORDER, $orderR->data['mchid']);
                                }
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_1:{
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2:{
                                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                $smsData = array($driverR->data['name'], $orderR->data['order_no']);
                                $smsUtil->sendTemplateSMS($branchR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_REFUSE_ORDER, $orderR->data['mchid']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_3:{
                                $smsUtil->sendTemplateSMS($branchR->data['cellphone'], array($orderR->data['order_no'], $orderR->data['reseverd_phone']), \SMSUtil::TEMP_ID_PSCANCEL, $orderR->data['mchid']);
                                break;
                            }
                            default:
                                return;
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * 发送微信通知
     * @param $order_id
     * @param $to_user_type
     */
    protected function sendWxMessage($order_id, $to_user_type, $message_type, $to_user_id = null)
    {
        try {
            if (C('WX_TEMPLATE_ON')) {
                $orderR = $this->find(array('order_id' => $order_id), 'Order');
                if (!$orderR->ret) {
                    return;
                }
                $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
                if (!$mchR->ret) {
                    return;
                }

                switch ($to_user_type) {
                    case \CommonDefine::USER_TYPE_1:{//通知用户
                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0: {//下单成功
                                //即时通知
                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                if (!$passengerR->ret) {
                                    return;
                                }
                                $bookSeatingInfo = $orderR->data['book_seating'];
                                if (!empty($orderR->data['seat_optional'])) {
                                    $bookSeatingInfo .= ",座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                }

                                $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                if (!$lineClassTrainR->ret) {
                                    return ;
                                }

                                $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                if ($lineR->ret) {
                                    $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                    $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                    $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                    $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                }

                                $temp = array(
                                    '您已成功订购['.$this->getOrderType($orderR->data['type']).']订单,请提前规划好行程',
                                    '尾号'.$this->getCellphoneSubTailFour($orderR->data['reseverd_phone'])."的乘客",
                                    $bookSeatingInfo,
                                    $orderR->data['reseverd_phone'],
                                    '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                    $orderR->data['start_time'],
                                    '保持电话通畅。客服电话{'.$mchR->data['tel'].'}',
                                );
                                if ($this->mchid == 1231) {
                                    $temp[6] = '您已经成功获得积分，累计积分可以兑换车票。客服电话{'.$mchR->data['tel'].'}';
                                }

                                $passengerWeixinAccountInfoArr = array('openid' => $passengerR->data['openid'], 'account_id' => $passengerR->data['passenger_id']);
                                $this->pushWeixinMessage($passengerWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_1, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);

                                //定时推送
                                // $this->pushPassengerNoticeQueue(1, $orderR->data['order_id']);

                                # 2023.07.28下单成功，通知乘客与分台
                                $this->sendInnerNotification('passenger', 'wechat', 6, $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1: {//司机已经接单
                                //定时推送
                                // $this->pushPassengerNoticeQueue(1, $orderR->data['order_id']);
                                # 2023.07.28接单成功，通知乘客
                                $this->sendInnerNotification('passenger', 'wechat', 7, $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_2: {//提前定时通知
                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                if (!$passengerR->ret) {
                                    return;
                                }
                                $bookSeatingInfo = $orderR->data['book_seating'];
                                if (!empty($orderR->data['seat_optional'])) {
                                    $bookSeatingInfo .= ",座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                }

                                $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                if (!$lineClassTrainR->ret) {
                                    return ;
                                }

                                $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                if ($lineR->ret) {
                                    $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                    $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                    $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                    $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                }

                                $temp = array(
                                    '您的['.$this->getOrderType($orderR->data['type']).']订单即将出发,请提前规划好行程',
                                    '尾号'.$this->getCellphoneSubTailFour($orderR->data['reseverd_phone'])."的乘客",
                                    $bookSeatingInfo,
                                    $orderR->data['reseverd_phone'],
                                    '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                    $orderR->data['start_time'],
                                    '保持电话通畅。客服电话{'.$mchR->data['tel'].'}',
                                );
                                if ($this->mchid == 1231) {
                                    $temp[6] = '您已经成功获得积分，累计积分可以兑换车票。客服电话{'.$mchR->data['tel'].'}';
                                }
                                $passengerWeixinAccountInfoArr = array('openid' => $passengerR->data['openid'], 'account_id' => $passengerR->data['passenger_id']);
                                $this->pushWeixinMessage($passengerWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_1, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                # 2023.07.28提前乘客通知
                                $this->sendInnerNotification('passenger', 'wechat', 11, $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_3:{
                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                if (!$passengerR->ret) {
                                    return;
                                }
                                //【CC招车】乘客已取消了本次行程，您的订单{1}已取消，如有疑问请拨打{2}
                                //乘客已取消了本次行程
                                //订单编号：C1510170029132
                                //订单状态：乘客已取消
                                //如有疑问请拨打：************
                                $temp = array( '您成功取消了该订单', $orderR->data['order_no'], '已取消', '如有疑问请拨打:'.$mchR->data['tel']);
                                $passengerWeixinAccountInfoArr = array('openid' => $passengerR->data['openid'], 'account_id' => $passengerR->data['passenger_id']);
                                $this->pushWeixinMessage($passengerWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_1, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                // # 2023.07.28乘客取消，通知分台/乘客/司机
                                // $this->sendInnerNotification('passenger', 'wechat', 3, $orderR->data['order_id']);
                                break;
                            }
                            default:
                                break;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_2:{//通知司机
                        $driverR = $this->find(array('driver_id' => $to_user_id), 'Driver');
                        if (!$driverR->ret) {
                            return;
                        }
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0: {//新订单
                                $driverR = $this->find(array('driver_id' =>  (empty($to_user_id) ? $orderR->data['driver_id'] : $to_user_id)), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                $temp = [];
                                $passengerName = '';
                                if ($orderR->data['is_temp'] == 1) {//临时订单
                                    $passengerName = '代约临时订单';
                                } else {
                                    $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                    $passengerName = $passengerR->data['name'];
                                }

                                //您有新的[拼车]订单了
                                //乘客姓名：张三
                                //乘客人数：3人
                                //联系电话：1333333333
                                //预定线路：武汉-北京
                                //上车时间：2016/8/8 18:30
                                //请尽快与乘客取得联系
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }

                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $orderR->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_2: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $orderR->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_3: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $this->getOrderType($orderR->data['type']),
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            "重量:".$orderR->data['weight']."kg,距离:".$orderR->data['kilometre'].'km,请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_4: {
                                        $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $this->getOrderType($orderR->data['type']),
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            "-",
                                            $orderR->data['start_time'],
                                            "服务名称:".$agencyR->data['name'].",服务说明:".$agencyR->data['summary'].',请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5: {
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $bookSeatingInfo = $orderR->data['book_seating'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $bookSeatingInfo .= ",座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $bookSeatingInfo,
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_7: {
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $orderR->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$orderR->data['start_address_remark']."] - [下车点：". $orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_11: {
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $orderR->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$orderR->data['start_address_remark']."] - [下车点：". $orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时处理！'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    default:
                                        return;
                                }

                                $driverWeixinAccountInfoArr = array('openid' => $driverR->data['openid'], 'account_id' => $driverR->data['driver_id']);
                                $this->pushWeixinMessage($driverWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_2, $driverR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1:{
                                //乘客取消该订单
                                //订单编号：C1510170029132
                                //订单状态：乘客已取消
                                //给您带来的不便，敬请谅解！
                                $temp = array('乘客取消该订单', $orderR->data['order_no'], '已取消', '给您带来的不便，敬请谅解！');
                                $this->wechatPushInfo($mchR->data['mchid'], $orderR->data['driver_id'], \CommonDefine::USER_TYPE_2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                # 2023.07.28乘客取消，通知分台/乘客/司机
                                $this->sendInnerNotification('driver', 'wechat', 3, $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2:{
                                //乘客已取消了本次行程
                                //订单编号：C1510170029132
                                //订单状态：乘客已取消
                                //如有疑问请拨打：************
                                $temp = array( '分台取消了派单', $orderR->data['order_no'], '派单已撤回', '如有疑问请拨打:'.$mchR->data['tel']);
                                $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], \CommonDefine::USER_TYPE_2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                # 2023.07.28乘客取消，通知分台/乘客/司机
                                $this->sendInnerNotification('driver', 'wechat', 3, $orderR->data['order_id']);
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_3:{//通知分台
                        $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
                        if (!$branchR->ret) {
                            return;
                        }
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0: {//新订单
                                $temp = [];
                                $passengerName = '';
                                if ($orderR->data['is_temp'] == 1) {//临时订单
                                    $passengerName = '代约临时订单';
                                } else {
                                    $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                    $passengerName = $passengerR->data['name'];
                                }

                                //您有新的[拼车]订单了
                                //乘客姓名：张三
                                //乘客人数：3人
                                //联系电话：1333333333
                                //预定线路：武汉-北京
                                //上车时间：2016/8/8 18:30
                                //请尽快与乘客取得联系
                                switch ($orderR->data['type']) {
                                    case \CommonDefine::ORDER_TYPE_1: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }

                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $orderR->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时派单! 点击前往派单!'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_2: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'LineChartered');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $this->getOrderType($orderR->data['type']),
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            '请及时派单! 点击前往派单!'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_3: {
                                        $lineR = $this->find(array('id' => $orderR->data['line_id']), 'Line');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $this->getOrderType($orderR->data['type']),
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            "重量:".$orderR->data['weight']."kg,距离:".$orderR->data['kilometre'].'km,请及时派单! 点击前往派单!'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_4: {
                                        $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $this->getOrderType($orderR->data['type']),
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            "-",
                                            $orderR->data['start_time'],
                                            "服务名称:".$agencyR->data['name'].",服务说明:".$agencyR->data['summary'].',请及时派单! 点击前往派单!'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    case \CommonDefine::ORDER_TYPE_5: {
                                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                        if (!$lineClassTrainR->ret) {
                                            return ;
                                        }

                                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                                        if ($lineR->ret) {
                                            $startCityNameR = $this->getCityNameByAddressCode($lineR->data['start_area_code']);
                                            $startCityName = $startCityNameR->ret ? $startCityNameR->data['city_name'] : "";
                                            $endCityNameR = $this->getCityNameByAddressCode($lineR->data['end_area_code']);
                                            $endCityName = $endCityNameR->ret ? $endCityNameR->data['city_name'] : "";
                                        }
                                        $bookSeatingInfo = $orderR->data['book_seating'];
                                        if (!empty($orderR->data['seat_optional'])) {
                                            $bookSeatingInfo .= ",座位:".$this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                                        }
                                        $temp = array(
                                            '您有新的['.$this->getOrderType($orderR->data['type']).']订单了,订单编号：'.$orderR->data['order_no'],
                                            $passengerName,
                                            $bookSeatingInfo,
                                            $this->encryptionCellphoneByCellphone($orderR->data['reseverd_phone']),
                                            '[上车点：'.$startCityName.$orderR->data['start_address_remark']."] - [下车点：". $endCityName.$orderR->data['end_address_remark']."]",
                                            $orderR->data['start_time'],
                                            ',请及时派单! 点击前往派单!'."[".$mchR->data['mchname']."]"
                                        );
                                        break;
                                    }
                                    default:
                                        return;
                                }
                                $temp[0] = "[".$branchR->data['mchname']."]，".$temp[0];
                                $branchWeixinAccountInfoArr = array('openid' => $branchR->data['openid'], 'account_id' => $orderR->data['branchid']);
                                $this->pushWeixinMessage($branchWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_3, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                /**
                                 * 分台微信通知
                                 * 2024.07.03 切换到新系统发送
                                 * <AUTHOR> <<EMAIL>>
                                 */
                                $this->sendInnerNotification('branch', 'wechat', 'wechat_branch_new_order', $orderR->data['order_id']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_1:{
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2:{
                                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"拒绝了您的['.$this->getOrderType($orderR->data['type']).']派单！',
                                    $orderR->data['order_no'],
                                    '派单失败',
                                    '请及时重新派单！'
                                );
                                $branchWeixinAccountInfoArr = array('openid' => $branchR->data['openid'], 'account_id' => $orderR->data['branchid']);
                                $this->pushWeixinMessage($branchWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_3, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_3:{
                                $temp = array('乘客取消该订单', $orderR->data['order_no'], '已取消', '如有问题请及时联系乘客！');
                                $branchWeixinAccountInfoArr = array('openid' => $branchR->data['openid'], 'account_id' => $orderR->data['branchid']);
                                $this->pushWeixinMessage($branchWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_3, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                // # 2023.07.28乘客取消，通知分台/乘客/司机
                                // $this->sendInnerNotification('branch', 'wechat', 3, $orderR->data['order_id']);
                                break;
                            }
                            default:
                                return;
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }


    /**
     * 发送推送通知
     * @param $order_id
     * @param $to_user_type
     */
    protected function sendNoticeMessage($order_id, $to_user_type, $message_type, $to_user_id = null)
    {
        try {
            if (C('NOTICE_ON')) {
                $orderR = $this->find(array('order_id' => $order_id), 'Order');
                if (!$orderR->ret) {
                    return;
                }

                switch ($to_user_type) {
                    case \CommonDefine::USER_TYPE_1:{//通知用户
                        break;
                    }
                    case \CommonDefine::USER_TYPE_2:{//通知司机
                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0: {//新订单
                                //                                $driver_id = (empty($to_user_id)?$orderR->data['driver_id']:$to_user_id);
                                $responsed = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/voices", 'post');
                                \Log::write(__LINE__."消息助手,订单id：".$order_id.",订单编号：".$orderR->data['order_no'].",返回数据：".$responsed);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1:{
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2:{
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_3:{//通知分台
                        break;
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * @param $order_id
     * @param $to_user_type
     * @param $message_type
     * @param null $to_user_id
     * @param int $send_type
     */
    protected function sendMessage($order_id, $to_user_type, $message_type, $to_user_id = null, $send_type = 'all')
    {
        if ($send_type == 'all') {
            $this->sendSms($order_id, $to_user_type, $message_type, $to_user_id);
            $this->sendWxMessage($order_id, $to_user_type, $message_type, $to_user_id);
            $this->sendNoticeMessage($order_id, $to_user_type, $message_type, $to_user_id);
        } elseif ($send_type == 'sms') {
            $this->sendSms($order_id, $to_user_type, $message_type, $to_user_id);
        } elseif ($send_type == 'wx_message') {
            $this->sendWxMessage($order_id, $to_user_type, $message_type, $to_user_id);
        } elseif ($send_type == 'app_notice') {
            $this->sendNoticeMessage($order_id, $to_user_type, $message_type, $to_user_id);
        }
    }


    /**
     * 分台通知
     *
     * @param integer $mchid
     * @param integer $orderId
     * @return void
     */
    public function postBranchNewOrderMessage($mchid, $orderId)
    {
        if (is_array($mchid, [969, 1181])) {
            $this->sendWxMessage($orderId, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0);
            $this->sendNoticeMessage($orderId, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0);
        } else {
            $this->sendSms($orderId, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0);
            $this->sendWxMessage($orderId, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0);
            $this->sendNoticeMessage($orderId, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0);
        }
    }

    /**
     * 新版本发送通知
     *
     * @param string $userType
     * @param string $channel
     * @param integer $action
     * @param integer $orderId
     * @return void
     */
    public function sendInnerNotification($userType, $channel, $action, $orderId)
    {
        httpRequest(C('CC_INNER_API_HOST') . "/api/inner/notification/user_types/{$userType}/channels/{$channel}/actions/{$action}/orders/{$orderId}", 'post');
    }

    public function sendInnerWebSocket($order_id)
    {
        //新订单Websocket消息通知
        httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/websockets", 'post');
    }

    /**
     * 云裕众-定制组团发送短信消息
     * @param $student_customized_line_id
     * @param $to_user_type
     * @param $message_type
     * @param null $to_user_id
     * @return ResultModel|void
     */
    protected function yySendSms($student_customized_line_id, $to_user_type, $message_type, $to_user_id = null)
    {
        try {
            if (C('SMS_ON')) {
                $lineWhere['student_customized_line_id'] = $student_customized_line_id;
                $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
                if (!$studentCustomizedLineR->ret) {
                    return new \ResultModel(false, '订单异常');
                }
                $mchR = $this->find(array('admin_id' => $studentCustomizedLineR->data['mchid']), 'Admin');
                if (!$mchR->ret) {
                    return new \ResultModel(false, '订单异常');
                }

                $passengers = M()->table("cp_yy_student_customized_order sco")
                    ->join("LEFT JOIN cp_passenger p ON sco.passenger_id = p.passenger_id")
                    ->where("sco.status = ".\YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2. " AND sco.student_customized_line_id = $student_customized_line_id")
                    ->field('p.cellphone')
                    ->select();

                $passengerCellphoneArr = [];
                if ($passengers) {
                    foreach ($passengers as $passenger) {
                        $passengerCellphoneArr[] = $passenger['cellphone'];
                    }
                }

                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                switch ($to_user_type) {
                    case \CommonDefine::USER_TYPE_1:{//通知用户
                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0: {//下单成功
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1: {//司机已经接单
                                $driverR = $this->find(array('driver_id' => $studentCustomizedLineR->data['driver_id']), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                //车牌号加密
                                $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                                //乘客接收短信
                                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                $smsUtil->sendTemplateSMS($passengerCellphoneArr, array($this->encryptionDriverName($driverR->data['name']),$driverR->data['cellphone'],$carTailNumber,$mchR->data['tel'].",[".$mchR->data['mchname']."]"), \SMSUtil::TEMP_ID_PASSENGER_ORDER_ACPET, $studentCustomizedLineR->data['mchid']);
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_2:{//通知司机
                        $driverR = $this->find(array('driver_id' => (empty($to_user_id) ? $studentCustomizedLineR->data['driver_id'] : $to_user_id)), 'Driver');
                        if (!$driverR->ret) {
                            return;
                        }

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0:{//新订单
                                $smsData = array($studentCustomizedLineR->data['student_customized_line_no'], implode(',', $passengerCellphoneArr).",[".$mchR->data['mchname']."]");
                                $smsData[0] .= (",[定制包车]");
                                $smsData[0] .= ",出发时间:".$studentCustomizedLineR->data['start_time'];
                                $smsData[0] .= ",上车点:".$studentCustomizedLineR->data['start_address_remark'];
                                $smsData[0] .= ",下车点:".$studentCustomizedLineR->data['end_address_remark'];
                                $smsData[0] .= ",人数:".$studentCustomizedLineR->data['total_count'];
                                $smsUtil->sendTemplateSMS($driverR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_DRIVER_NEW_ORDER, $studentCustomizedLineR->data['mchid']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1:{
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2:{
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_3:{//通知分台
                        $branchR = $this->find(array('admin_id' => $studentCustomizedLineR->data['branchid']), 'Admin');
                        if (!$branchR->ret) {
                            return;
                        }

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0:{//新订单
                                $smsData = array($studentCustomizedLineR->data['student_customized_line_no'], implode(',', $passengerCellphoneArr).",[".$mchR->data['mchname']."]");
                                $smsData[0] .= (",[定制包车]");
                                $smsData[0] .= ",出发时间:".$studentCustomizedLineR->data['start_time'];
                                $smsData[0] .= ",上车点:".$studentCustomizedLineR->data['start_address_remark'];
                                $smsData[0] .= ",下车点:".$studentCustomizedLineR->data['end_address_remark'];
                                $smsData[0] .= ",人数:".$studentCustomizedLineR->data['total_count'];
                                $smsUtil->sendTemplateSMS($branchR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_NEW_ORDER, $studentCustomizedLineR->data['mchid']);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_1:{//接收订单
                                $smsData = array($studentCustomizedLineR->data['student_customized_line_no'], implode(',', $passengerCellphoneArr).",[".$mchR->data['mchname']."]");
                                $smsData[0] .= (",[定制包车]");
                                $smsData[0] .= ",出发时间:".$studentCustomizedLineR->data['start_time'];
                                $smsData[0] .= ",上车点:".$studentCustomizedLineR->data['start_address_remark'];
                                $smsData[0] .= ",下车点:".$studentCustomizedLineR->data['end_address_remark'];
                                $smsData[0] .= ",人数:".$studentCustomizedLineR->data['total_count'];
                                $smsUtil->sendTemplateSMS($branchR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_NEW_ORDER, $studentCustomizedLineR->data['mchid']);
                                break;
                            }
                            default:
                                return;
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * 云裕众-定制组团发送模板消息
     * @param $student_customized_line_id
     * @param $to_user_type
     * @param $message_type
     * @param null $to_user_id
     * @return ResultModel|void
     */
    protected function yySendWxMessage($student_customized_line_id, $to_user_type, $message_type, $to_user_id = null)
    {
        try {
            if (C('WX_TEMPLATE_ON')) {
                $lineWhere['student_customized_line_id'] = $student_customized_line_id;
                $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
                if (!$studentCustomizedLineR->ret) {
                    return new \ResultModel(false, '订单异常');
                }
                $mchR = $this->find(array('admin_id' => $studentCustomizedLineR->data['mchid']), 'Admin');
                if (!$mchR->ret) {
                    return;
                }

                $passengers = M()->table("cp_yy_student_customized_order sco")
                    ->join("LEFT JOIN cp_passenger p ON sco.passenger_id = p.passenger_id")
                    ->where("sco.status = ".\YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2. " AND sco.student_customized_line_id = $student_customized_line_id")
                    ->field('p.real_name,p.cellphone')
                    ->select();

                $passengerRealNameArr = [];
                $passengerCellphoneArr = [];
                if ($passengers) {
                    foreach ($passengers as $passenger) {
                        $passengerRealNameArr[] = $passenger['real_name'];
                        $passengerCellphoneArr[]  = $this->encryptionCellphoneByCellphone($passenger['cellphone']);
                    }
                }

                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                switch ($to_user_type) {
                    case \CommonDefine::USER_TYPE_1:{//通知用户
                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0: {//下单成功
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1: {//司机已经接单
                                break;
                            }
                            default:
                                break;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_2:{//通知司机
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0: {//新订单
                                $driverR = $this->find(array('driver_id' =>  (empty($to_user_id) ? $studentCustomizedLineR->data['driver_id'] : $to_user_id)), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }
                                $temp = array(
                                    '您有新的[定制包车]订单了,订单编号：'.$studentCustomizedLineR->data['student_customized_line_no'],
                                    implode(','.$passengerRealNameArr),
                                    $studentCustomizedLineR->data['total_count'],
                                    implode(',', $passengerCellphoneArr),
                                    '[上车点：'.$studentCustomizedLineR->data['start_address_remark']."] - [下车点：". $studentCustomizedLineR->data['end_address_remark']."]",
                                    $studentCustomizedLineR->data['start_time'],
                                    '请及时处理！'."[".$mchR->data['mchname']."]"
                                );

                                $driverWeixinAccountInfoArr = array('openid' => $driverR->data['openid'], 'account_id' => $driverR->data['driver_id']);
                                $this->pushWeixinMessage($driverWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_2, $driverR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1:{
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2:{
                                $driverR = $this->find(array('driver_id' => $to_user_id), 'Driver');
                                if (!$driverR->ret) {
                                    return;
                                }

                                //【CC招车】乘客已取消了本次行程，您的订单{1}已取消，如有疑问请拨打{2}
                                //乘客已取消了本次行程
                                //订单编号：C1510170029132
                                //订单状态：乘客已取消
                                //如有疑问请拨打：************
                                $temp = array( '分台取消了派单', $studentCustomizedLineR->data['student_customized_line_no'], '派单已撤回', '如有疑问请拨打:'.$mchR->data['tel']);
                                $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                                break;
                            }
                            default:
                                return;
                        }
                        break;
                    }
                    case \CommonDefine::USER_TYPE_3:{//通知分台
                        $branchR = $this->find(array('admin_id' => $studentCustomizedLineR->data['branchid']), 'Admin');
                        if (!$branchR->ret) {
                            return;
                        }
                        $startCityName = '';
                        $endCityName = '';

                        switch ($message_type) {
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_0: {//新订单
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_1: {//司机接收了指派的订单
                                $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
                                if (!$driverR->ret) {
                                    return $this->output(new \ResultModel(false, '司机账号异常'));
                                }
                                $temp = array(
                                    '司机"' . $driverR->data['name'] . '"接受了您的[定制包车]派单！',
                                    $studentCustomizedLineR->data['student_customized_line_no'],
                                    '进行中',
                                    '派单完成！'
                                );
                                $branchWeixinAccountInfoArr = array('openid' => $branchR->data['openid'], 'account_id' => $studentCustomizedLineR->data['branchid']);
                                $this->pushWeixinMessage($branchWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_3, $studentCustomizedLineR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                break;
                            }
                            case \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2: {//司机拒绝了指派的订单
                                $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
                                if (!$driverR->ret) {
                                    return $this->output(new \ResultModel(false, '司机账号异常'));
                                }
                                $temp = array(
                                    '司机"' . $driverR->data['name'] . '"拒绝了您的[定制包车]派单！',
                                    $studentCustomizedLineR->data['student_customized_line_no'],
                                    '派单失败',
                                    '请及时重新派单！'
                                );
                                $branchWeixinAccountInfoArr = array('openid' => $branchR->data['openid'], 'account_id' => $studentCustomizedLineR->data['branchid']);
                                $this->pushWeixinMessage($branchWeixinAccountInfoArr, $temp, \CommonDefine::USER_TYPE_3, $studentCustomizedLineR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                break;
                            }
                            default:
                                return;
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * 获取附近的车主
     * @param int $line_fast_id 经度城市线路编码
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param int $book_seating 座位数
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    protected function getLineFastAroundDrivers($line_fast_id, $longitude = null, $latitude = null, $book_seating = 0, $limit = 60000, $page = 1, $size = 10)
    {
        $distanceField = "GETDISTANCE(d.latitude,d.longitude,d.latitude,d.longitude) AS distance,";
        if ($latitude && $longitude) {
            $distanceField = 'GETDISTANCE(d.latitude,d.longitude,'.$latitude.','.$longitude.') AS distance,';
        }

        $db = M();
        $order = "distance ASC,d.driver_id ASC";//默认推荐
        $where = "dl.line_id = $line_fast_id and d.residual_seating >= $book_seating and d.state != 3 and dl.type = ".\CommonDefine::ORDER_TYPE_7;
        $data = $db->table("cp_driver_line dl")
            ->join("LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id")
            ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
            ->having("distance < $limit")
            ->where($where)
            ->order($order)
//            ->page($page, $size)
            ->group("d.driver_id")
            ->select();

        if ($data) {
            foreach ($data as $key => $item) {
                //计算司机快车已派单累计座位数
                $driverWhere = "o.driver_id = ".$item['driver_id'];
                $driverWhere .= " AND o.type = ".\CommonDefine::ORDER_TYPE_7;
                $driverWhere .= " AND o.state = ".\CommonDefine::ORDER_STATE_1;
                $driverWhere .= " AND o.appoint = ".\CommonDefine::APPOINT_TYPE_1;
                $orderFastTotal = $db->table("cp_order o")
                    ->where($driverWhere)
                    ->sum('o.book_seating');
                if (($item['residual_seating'] - $orderFastTotal) < $book_seating) {
                    unset($data[$key]);
                    continue;
                }
                $data[$key]['distance'] = number_format(($item['distance'] / 1000), 2);
            }
            $count = count($data);
            if ($count > 0) {
                return new ResultModel(true, array_slice($data, 0), $count);
            }
        }

        return new ResultModel(false, null, null);
    }


    /**
     * 获取附近的车主
     * @param int $line_taxi_id 经度城市线路编码
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param int $book_seating 座位数
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    protected function getLineTaxiAroundDrivers($line_taxi_id, $longitude = null, $latitude = null, $book_seating = 0, $limit = 60000, $page = 1, $size = 10)
    {
        $distanceField = "GETDISTANCE(d.latitude,d.longitude,d.latitude,d.longitude) AS distance,";
        if ($latitude && $longitude) {
            $distanceField = 'GETDISTANCE(d.latitude,d.longitude,'.$latitude.','.$longitude.') AS distance,';
        }

        $db = M();
        $order = "distance ASC,d.driver_id ASC";//默认推荐
        $where = "dl.line_id = $line_taxi_id and d.residual_seating > $book_seating and (d.residual_seating + 1) = d.total_seating and d.state != 3 and dl.type = ".\CommonDefine::ORDER_TYPE_11;
        $data = $db->table("cp_driver_line dl")
            ->join("LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id")
            ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
            ->having("distance < $limit")
            ->where($where)
            ->order($order)
//            ->page($page, $size)
            ->group("d.driver_id")
            ->select();

        if ($data) {
            foreach ($data as $key => $item) {
                //计算司机快车已派单累计座位数
                $driverWhere = "o.driver_id = ".$item['driver_id'];
                $driverWhere .= " AND o.type = ".\CommonDefine::ORDER_TYPE_11;
                $driverWhere .= " AND o.state = ".\CommonDefine::ORDER_STATE_1;
                $driverWhere .= " AND o.appoint = ".\CommonDefine::APPOINT_TYPE_1;
                $orderFastTotal = $db->table("cp_order o")
                    ->where($driverWhere)
                    ->sum('o.book_seating');
                if (($item['residual_seating'] - $orderFastTotal) < $book_seating) {
                    unset($data[$key]);
                    continue;
                }

                $data[$key]['distance'] = number_format(($item['distance'] / 1000), 2);
            }
            $count = count($data);
            if ($count > 0) {
                return new ResultModel(true, array_slice($data, 0), $count);
            }
        }
        return new ResultModel(false, null, null);
    }

    /**
     * 获取附近
     * @param int $line_class_id 线路id
     * @param int $type 车点类型：1-上车点；2-下车点；
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为600000，方圆600公里）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    protected function getLineClassAroundPoints($line_class_id, $type = 1, $longitude = null, $latitude = null, $limit = 600000, $page = 1, $size = 10)
    {
        $distanceField = "GETDISTANCE(lcp.latitude,lcp.longitude,lcp.latitude,lcp.longitude) AS distance,";
        if ($latitude && $longitude) {
            $distanceField = 'GETDISTANCE(lcp.latitude,lcp.longitude,'.$latitude.','.$longitude.') AS distance,';
        }

        $db = M();
        $order = "distance ASC";//默认推荐
        $where = "line_class_id = $line_class_id and type = $type";
        $data = $db->table("cp_line_class_point lcp")
            ->field($distanceField.'lcp.*')
            ->having("distance < $limit")
            ->where($where)
            ->order($order)
            ->select();

        if ($data) {
            return new ResultModel(true, $data, count($data));
        }
        return new ResultModel(false, null, null);
    }

    /**
     * 检测商户logo隐藏是否过期
     */
    protected function checkLogoIsTimeOut()
    {
        try {
            $currentDateTime = date('Y-m-d H:i:s', time());
            $where = " mchid = ". $this->mchid. " AND start_time <= '".$currentDateTime."' AND end_time >= '".$currentDateTime."'";
            $systemMerchantConfigR = $this->find($where, 'SystemMerchantConfig');
            if ($systemMerchantConfigR->ret) {
                return true;
            }
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 检测优惠券是否可用
     */
    protected function checkCouponIsAble($price = 0, $coupon_record_id = 0, $type = 0)
    {
        $couponRecordWhere = " is_del = 0";
        $couponRecordWhere .= " AND end_time >= '".date('Y-m-d H:i:s', time())."'";
        $couponRecordWhere .= " AND passenger_id = ".$this->state->user_id;
        $couponRecordWhere .= " AND mchid = ".$this->mchid;
        $couponRecordWhere .= " AND status = ".\CommonDefine::COUPON_STATUS_0;
        $couponRecordWhere .= " AND coupon_record_id = ".$coupon_record_id;
        $couponRecordR = $this->find($couponRecordWhere, 'CouponRecord');
        if (!$couponRecordR->ret) {
            return new \ResultModel(false, '优惠券不存在');
        }
        $couponR = $this->find(array('coupon_id' => $couponRecordR->data['coupon_id']), 'Coupon');
        if (!$couponR->ret) {
            return new \ResultModel(false, '优惠券不存在');
        }

        //2023.03.30 优惠券适用业务范围检查
        $scopes = explode(',', $couponR->data['scope']);
        if ($scopes && !in_array($type, $scopes)) {
            return new \ResultModel(false, '该优惠券不在业务适用范围');
        }

        if ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_1) {
            if ($this->floatgtre($price, $couponR->data['rule'], 2)) {
                return new \ResultModel(true, '优惠券可用');
            }
        } elseif ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_2) {
            if ($this->floatgtre($price, $couponR->data['rule'], 2)) {
                return new \ResultModel(true, '优惠券可用');
            }
        }

        return new \ResultModel(false, '优惠券不可用');
    }

    protected function updateCouponRecord($coupon_record_id, $status)
    {
        $couponRecordR = $this->find(array('coupon_record_id' => $coupon_record_id), 'CouponRecord');
        if ($couponRecordR->ret) {
            $marketActivityMode = new MarketActivityModel();
            if ($status != $couponRecordR->data['status']) {
                switch ($status) {
                    case \CommonDefine::COUPON_STATUS_0:{
                        //已过期后无法回滚
                        if ($couponRecordR->data['status'] == \CommonDefine::COUPON_STATUS_2) {
                            $marketActivityMode->where('market_activity_id = '.$couponRecordR->data['market_activity_id'])->setInc('use_count');
                            return new ResultModel(true);
                        }
                        $updateCouponRecordData['coupon_record_id'] = $coupon_record_id;
                        $updateCouponRecordData['status'] = $status;
                        $updateCouponRecordData['use_time'] = array('exp','null');
                        $couponRecordS = $this->save('CouponRecord', $updateCouponRecordData);
                        if ($couponRecordS->ret) {
                            $marketActivityMode->where('market_activity_id = '.$couponRecordR->data['market_activity_id'])->setDec('use_count');
                            return new ResultModel(true);
                        }
                        break;
                    }
                    case \CommonDefine::COUPON_STATUS_1:{
                        $updateCouponRecordData['coupon_record_id'] = $coupon_record_id;
                        $updateCouponRecordData['status'] = $status;
                        $updateCouponRecordData['use_time'] = date('Y-m-d H:i:s', time());
                        $couponRecordS = $this->save('CouponRecord', $updateCouponRecordData);
                        if ($couponRecordS->ret) {
                            $marketActivityMode->where('market_activity_id = '.$couponRecordR->data['market_activity_id'])->setInc('use_count');
                            return new ResultModel(true);
                        }
                        break;
                    }
                    default:{
                        $updateCouponRecordData['coupon_record_id'] = $coupon_record_id;
                        $updateCouponRecordData['status'] = $status;
                        $couponRecordS = $this->save('CouponRecord', $updateCouponRecordData);
                        return new ResultModel(true);
                        break;
                    }
                }
            }
        }
        return new ResultModel(false);
    }

    /**
     * 增加减少用户卡包中的次数
     */
    protected function updatePassengerCardInfo($passenger_id, $times = 0)
    {
        if (is_numeric($times)) {
            $updatePassengerCardInfoData['passenger_id'] = $passenger_id;
            $passengerCardInfoR = $this->find(array('passenger_id' => $passenger_id), 'PassengerCardInfo');
            if ($passengerCardInfoR->ret) {
                $updatePassengerCardInfoData['passenger_card_info_id'] = $passengerCardInfoR->data['passenger_card_info_id'];
                if ($times > 0) {
                    $updatePassengerCardInfoData['total_times'] = $passengerCardInfoR->data['total_times'] + $times;
                    $updatePassengerCardInfoData['residual_times'] = $passengerCardInfoR->data['residual_times'] + $times;
                    return $this->save('PassengerCardInfo', $updatePassengerCardInfoData);
                } elseif ($times < 0) {
                    if ($passengerCardInfoR->data['residual_times'] + $times > 0) {
                        $updatePassengerCardInfoData['residual_times'] = $passengerCardInfoR->data['residual_times'] + $times;
                        $updatePassengerCardInfoData['use_times'] = $passengerCardInfoR->data['use_times'] - $times;
                        return $this->save('PassengerCardInfo', $updatePassengerCardInfoData);
                    }
                }
            } else {
                $updatePassengerCardInfoData['total_times'] = $times;
                $updatePassengerCardInfoData['residual_times'] = $times;
                $updatePassengerCardInfoData['use_times'] = 0;
                return $this->add('PassengerCardInfo', $updatePassengerCardInfoData);
            }
        }
        return new ResultModel(false);
    }

    /**
     * 增加用户的有效期
     * @param $driver_id
     * @param $parent_help_price_id
     * @return ResultModel
     */
    protected function updateDriverParentHelpInfo($driver_id, $parent_help_price_id)
    {
        $parentHelpPriceR = $this->find(array('parent_help_price_id' => $parent_help_price_id), 'YyParentHelpPrice');
        if (!$parentHelpPriceR->ret) {
            return new ResultModel(false);
        }
        $type = $parentHelpPriceR->data['type'];
        $number = $parentHelpPriceR->data['number'];
        $updateDriverParentHelpInfoData['driver_id'] = $driver_id;
        switch ($type) {
            case 1:{//年
                $driverParentHelpInfoR = $this->find(array('driver_id' => $driver_id), 'YyDriverParentHelpInfo');
                if ($driverParentHelpInfoR->ret) {
                    $startTime = $driverParentHelpInfoR->data['start_time'];
                    $endTime = $driverParentHelpInfoR->data['end_time'];
                    $updateDriverParentHelpInfoData['start_time'] = date('Y-m-d 00:00:00', strtotime("$startTime +$number year"));
                    $updateDriverParentHelpInfoData['end_time'] = date('Y-m-d 00:00:00', strtotime("$endTime +$number year"));
                    return $this->save('YyDriverParentHelpInfo', $updateDriverParentHelpInfoData);
                } else {
                    $updateDriverParentHelpInfoData['start_time'] = date('Y-m-d 00:00:00', time());
                    $updateDriverParentHelpInfoData['end_time'] = date('Y-m-d 00:00:00', strtotime("+$number year"));
                    return $this->add('YyDriverParentHelpInfo', $updateDriverParentHelpInfoData);
                }
                break;
            }
            case 2:{//月
                break;
            }
            case 3:{//日
                break;
            }
            default:
                break;
        }

        return new ResultModel(false);
    }

    /**
     * 学生号更新组团余座
     */
    protected function updateStudentCustomizedLineRemainCount($student_customized_line_id, $count = 0)
    {
        $lineWhere['student_customized_line_id'] = $student_customized_line_id;
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if (!$studentCustomizedLineR->ret) {
            return $this->output(new \ResultModel(false, '更新失败'));
        }

        $studentCustomizedLineData['student_customized_line_id'] = $student_customized_line_id;
        $studentCustomizedLineData['remain_count'] = $studentCustomizedLineR->data['remain_count'] + $count;
        if ($count > 0) {
            if ($studentCustomizedLineData['remain_count'] > $studentCustomizedLineR->data['total_count']) {
                return $this->output(new \ResultModel(false, '更新失败'));
            }
        } else {
            if ($studentCustomizedLineData['remain_count'] < 0) {
                return $this->output(new \ResultModel(false, '更新失败'));
            }
        }
        return $this->save('YyStudentCustomizedLine', $studentCustomizedLineData);
    }

    /**
     * 更新司机是否已到达上车点附近
     * @param $order_id
     * @param $latitude
     * @param $longitude
     * @return ResultModel
     */
    protected function doUpdateOrderFastBoardingPoint($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = \CommonDefine::ORDER_STATE_2;
        $orderWhere['is_boarding_point'] = \CommonDefine::IS_BOARDING_POINT_0;
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }
        $distance = $this->getDistance($orderR->data['start_latitude'], $orderR->data['start_longitude'], $latitude, $longitude);
        $distance = round($distance, 3) * 1000;//保留两位
        if ($distance > C('BOARDING_POINT_DISTANCE')) {//是否到达上车点
            return new ResultModel(false, '还未到达上车点附近');
        }

        $orderData['order_id'] = $order_id;
        $orderData['is_boarding_point'] = \CommonDefine::IS_BOARDING_POINT_1;
        $orderData['arrive_boarding_time'] = date('Y-m-d H:i:s', time());
        return $this->save('Order', $orderData);
    }

    /**
     * 更新司机是否已到达下车点附近
     * @param $order_id
     * @param $latitude
     * @param $longitude
     * @return ResultModel
     */
    protected function doUpdateOrderFastAlightingPoint($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = \CommonDefine::ORDER_STATE_4;
        $orderWhere['is_alighting_point'] = \CommonDefine::IS_ALIGHTING_POINT_0;
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }
        $distance = $this->getDistance($orderR->data['end_latitude'], $orderR->data['end_longitude'], $latitude, $longitude);
        $distance = round($distance, 3) * 1000;//保留两位
        if ($distance > C('ALIGHTING_POINT_DISTANCE')) {//是否到达下车点
            return new ResultModel(false, '还未到达下车点附近');
        }

        $orderData['order_id'] = $order_id;
        $orderData['is_alighting_point'] = \CommonDefine::IS_ALIGHTING_POINT_1;
        return $this->save('Order', $orderData);
    }

    /**
     * 更新快车轨迹信息
     * @param $order_id
     * @param $latitude
     * @param $longitude
     */
    protected function doUpdateOrderFastLocus($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['type'] = \CommonDefine::ORDER_TYPE_7;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = ['in', [\CommonDefine::ORDER_STATE_2, \CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4, \CommonDefine::ORDER_STATE_5]];
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }

        $orderFastFeeR = $this->find(['order_id' => $order_id], 'OrderFastFee');
        if (!$orderFastFeeR->ret) {
            return new ResultModel(false, '更新失败');
        }

        if (time() - strtotime($orderFastFeeR->data['update_time']) < C('FAST_LINE_UPDATE_FREQUENCY')) {
            return new ResultModel(false, '更新失败');
        }

        $this->startTrans();
        $orderFastLocusData['latitude'] = $latitude;
        $orderFastLocusData['longitude'] = $longitude;
        $orderFastLocusData['order_id'] = $order_id;
        $orderFastLocusData['current_time'] = date('Y-m-d H:i:s', time());
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_2 && $orderR->data['is_boarding_point'] == \CommonDefine::IS_BOARDING_POINT_1) {//等待乘客
            $orderFastLocusData['is_wait'] = \CommonDefine::IS_WAIT_TYPE_1;
            $lastOrderFastLocusListR = $this->select(['order_id' => $order_id, 'is_wait' => \CommonDefine::IS_WAIT_TYPE_1], 1, 1, 'order_fast_locus_id desc', 'OrderFastLocus');
            if ($lastOrderFastLocusListR->ret) {
                $orderFastLocusData['time_from_last'] = strtotime($orderFastLocusData['current_time']) - strtotime($lastOrderFastLocusListR->data[0]['current_time']);

                //计算等待超时费用
                $orderFastFeeData['order_fast_fee_id'] = $orderFastFeeR->data['order_fast_fee_id'];
                $orderFastFeeData['total_wait_timeout'] = $orderFastFeeR->data['time_from_last'] + $orderFastLocusData['time_from_last'];
                if ($orderFastFeeData['total_wait_timeout'] > C('FAST_LINE_WAIT_TIMEOUT')) {
                    if ($orderFastFeeR->data['total_wait_timeout_price'] > 0) {
                        $orderFastFeeData['total_wait_timeout_price'] = $orderFastFeeR->data['total_wait_timeout_price'] + $this->_computeWaitTimeoutPrice($orderR->data['line_id'], $orderFastLocusData['time_from_last'], date('H:i:s', time()));
                    } else {
                        $orderFastFeeData['total_wait_timeout_price'] = $orderFastFeeR->data['total_wait_timeout_price'] + $this->_computeWaitTimeoutPrice($orderR->data['line_id'], $orderFastFeeData['total_wait_timeout'] - C('FAST_LINE_WAIT_TIMEOUT'), date('H:i:s', time()));
                    }
                }

                //更新价格
                $orderFastFeeS = $this->save('OrderFastFee', $orderFastFeeData);
                if (!$orderFastFeeS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }

                //更新订单总价
                $currentPrice = $orderFastFeeR->data['base_price'] + $orderFastFeeData['total_wait_timeout_price'];
                $updateOrderS = $this->updateOrderFastTotalPrice($order_id, $currentPrice);
                if (!$updateOrderS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }
            }
            $orderFastLocusA = $this->add('OrderFastLocus', $orderFastLocusData);
            if (!$orderFastLocusA->ret) {
                $this->transRollback();
                return new ResultModel(false, '更新失败');
            }
            $this->commitTrans();
            return new ResultModel(false, '更新成功');
        } elseif ($orderR->data['state'] == \CommonDefine::ORDER_STATE_2 && $orderR->data['is_boarding_point'] == \CommonDefine::IS_BOARDING_POINT_0) {//接乘客的路上
            $this->transRollback();
            return new ResultModel(false, '更新失败');
        } else {//在路上
            $orderFastLocusData['is_wait'] = \CommonDefine::IS_WAIT_TYPE_0;
            $lastOrderFastLocusListR = $this->select(['order_id' => $order_id, 'is_wait' => \CommonDefine::IS_WAIT_TYPE_0], 1, 1, 'order_fast_locus_id desc', 'OrderFastLocus');
            if ($lastOrderFastLocusListR->ret) {
                $orderFastLocusData['distance_from_last'] = round($this->getDistance($latitude, $longitude, $lastOrderFastLocusListR->data[0]['latitude'], $lastOrderFastLocusListR->data[0]['longitude']), 2);
                $orderFastLocusData['time_from_last'] = strtotime($orderFastLocusData['current_time']) - strtotime($lastOrderFastLocusListR->data[0]['current_time']);

                //计算正常费用
                $orderFastFeeData['order_fast_fee_id'] = $orderFastFeeR->data['order_fast_fee_id'];
                //里程费用
                $orderFastFeeData['total_mileage'] = $orderFastFeeR->data['total_mileage'] + $orderFastLocusData['distance_from_last'];
                $orderFastFeeData['total_mileage_price'] = $orderFastFeeR->data['total_mileage_price']  + $this->computeMileagePrice($orderR->data['line_id'], $orderFastLocusData['distance_from_last'], date('H:i:s', time()));
                //计算时长
                $orderFastFeeData['total_duration'] = $orderFastFeeR->data['total_duration'] + $orderFastLocusData['time_from_last'];
                $orderFastFeeData['total_duration_price'] = $orderFastFeeR->data['total_duration_price'] + $this->computeDurationPrice($orderR->data['line_id'], $orderFastLocusData['time_from_last'], date('H:i:s', time()));
                //计算远程费
                $orderFastFeeData['total_longdistance'] = 0;
                $orderFastFeeData['total_longdistance_price'] = 0;
                if ($orderFastFeeR->data['total_longdistance'] > 0) {
                    $orderFastFeeData['total_longdistance'] =  $orderFastFeeR->data['total_longdistance'] + $orderFastLocusData['distance_from_last'];
                    $orderFastFeeData['total_longdistance_price'] = $orderFastFeeR->data['total_longdistance_price'] + $this->computeLongdistancePrice($orderR->data['line_id'], $orderFastLocusData['distance_from_last'], date('H:i:s', time()));
                } else {
                    $time = date('H:i:s', time());
                    $feeWhere['line_fast_id'] = $orderR->data['line_id'];
                    $feeWhere['region_start_time'] = ['elt', $time];
                    $feeWhere['region_end_time'] = ['egt', $time];
                    $fastLineLongdistanceFeeR = $this->find($feeWhere, 'LineFastLongdistanceFee');
                    if ($fastLineLongdistanceFeeR->ret) {
                        $orderFastFeeData['total_longdistance'] =  $orderFastFeeData['total_mileage'] - $fastLineLongdistanceFeeR->data['base_distance'];
                        $orderFastFeeData['total_longdistance_price'] = $this->computeLongdistancePrice($orderR->data['line_id'], $orderFastFeeData['total_longdistance'], $time);
                    }
                }

                //更新价格
                $orderFastFeeS = $this->save('OrderFastFee', $orderFastFeeData);
                if (!$orderFastFeeS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }

                //更新订单总价
                $currentPrice = $orderFastFeeData['total_mileage_price'] + $orderFastFeeData['total_duration_price'] + $orderFastFeeData['total_longdistance_price'] + $orderFastFeeR->data['total_wait_timeout_price'];
                if ($currentPrice > ($orderFastFeeR->data['base_price'] + $orderFastFeeR->data['total_wait_timeout_price'])) {
                    $updateOrderS = $this->updateOrderFastTotalPrice($order_id, $currentPrice);
                    if (!$updateOrderS->ret) {
                        $this->transRollback();
                        return new ResultModel(false, '更新失败');
                    }
                }
            }

            $orderFastLocusA = $this->add('OrderFastLocus', $orderFastLocusData);
            if (!$orderFastLocusA->ret) {
                $this->transRollback();
                return new ResultModel(false, '更新失败');
            }
            $this->commitTrans();
            return new ResultModel(false, '更新成功');
        }
    }

    /**
     * 计算等待超时费用
     * @param $fast_line_id
     * @param $distance
     * @param $time
     * @return int
     */
    private function _computeWaitTimeoutPrice($fast_line_id, $timeout, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineTimeoutFeeR = $this->find($feeWhere, 'LineFastTimeoutFee');
        if ($fastLineTimeoutFeeR->ret) {
            return $timeout * $fastLineTimeoutFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 计算基础费用
     * @param $fast_line_id
     * @param $time
     * @return int
     */
    protected function computeBasePrice($fast_line_id, $distance, $time)
    {
        $ret = new ResultModel(true, ['remain_distance' => 0, 'base_price' => 0]);
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineBaseFeeR = $this->find($feeWhere, 'LineFastBaseFee');
        if ($fastLineBaseFeeR->ret) {
            $remainDistance = $distance - ($fastLineBaseFeeR->data['include_distance'] * 1000);
            if ($remainDistance > 0) {
                $ret->data['remain_distance'] = $remainDistance;
            }
            $ret->data['base_price'] = $fastLineBaseFeeR->data['price'];
        } else {
            return new \ResultModel(false);
        }
        return $ret;
    }

    /**
     * 计算里程费用
     * @param $fast_line_id
     * @param $distance 米
     * @param $time
     * @return int
     */
    protected function computeMileagePrice($fast_line_id, $distance, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineMileageFeeR = $this->find($feeWhere, 'LineFastMileageFee');
        if ($fastLineMileageFeeR->ret) {
            return ($distance / 1000) * $fastLineMileageFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 计算时长费用(有误差，秒和分钟)
     * @param $fast_line_id
     * @param $duration
     * @param $time
     * @return int
     */
    protected function computeDurationPrice($fast_line_id, $duration, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineDurationFeeR = $this->find($feeWhere, 'LineFastDurationFee');
        if ($fastLineDurationFeeR->ret) {
            return round($duration / 60, 0) * $fastLineDurationFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 根据总距离计算远程费
     * @param $fast_line_id
     * @param $distance 总行程
     * @param $time
     * @return int
     */
    protected function computeLongdistancePriceByDistance($fast_line_id, $distance, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineLongdistanceFeeR = $this->find($feeWhere, 'LineFastLongdistanceFee');
        if ($fastLineLongdistanceFeeR->ret) {
            if ($distance > $fastLineLongdistanceFeeR->data['base_distance']) {
                $longdistance = $distance - $fastLineLongdistanceFeeR->data['base_distance'];
                return ($longdistance / 1000) * $fastLineLongdistanceFeeR->data['price'];
            }
        }
        return 0;
    }


    /**
     * 计算远程距离
     * @param $fast_line_id
     * @param $distance 总行程
     * @param $time
     * @return int
     */
    protected function computeLongDistance($fast_line_id, $distance, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineLongdistanceFeeR = $this->find($feeWhere, 'LineFastLongdistanceFee');
        if ($fastLineLongdistanceFeeR->ret) {
            if ($distance > $fastLineLongdistanceFeeR->data['base_distance']) {
                return $distance - $fastLineLongdistanceFeeR->data['base_distance'];
            }
        }
        return 0;
    }

    /**
     * 计算远程费用
     * @param $fast_line_id
     * @param $distance 米
     * @param $time
     * @return int
     */
    protected function computeLongdistancePrice($fast_line_id, $distance, $time)
    {
        $feeWhere['line_fast_id'] = $fast_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $fastLineLongdistanceFeeR = $this->find($feeWhere, 'LineFastLongdistanceFee');
        if ($fastLineLongdistanceFeeR->ret) {
            if ($distance > $fastLineLongdistanceFeeR->data['base_distance']) {
                $longdistance = $distance - ($fastLineLongdistanceFeeR->data['base_distance'] * 1000);
                return ($longdistance / 1000) * $fastLineLongdistanceFeeR->data['price'];
            }
        }
        return 0;
    }

    /**
     * 更新订单总价
     * @param $order_id
     * @param $currentPrice
     * @param $mchid
     * @return ResultModel
     */
    protected function updateOrderFastTotalPrice($order_id, $currentPrice)
    {
        $orderR = $this->find(['order_id' => $order_id], 'Order');
        if ($orderR->ret) {
            if ($orderR->data['price'] < $currentPrice) {
                //附加费
                $currentPrice += $orderR->data['extra_price'];
                //扣除优惠券减免费用
                $orderData['real_price'] = $this->reduceCouponPrice($currentPrice, $orderR->data['coupon_record_id']);

                $mch = M('AnnualFee')->where(array('mchid' => $orderR->data['order_id']))->find();
                $orderData['price'] = $currentPrice;
                $orderData['offer_price'] = $orderData['price'] - $orderData['price'] * $mch['split'] / 100;
                $orderData['split'] = $mch['split'] / 100;
                $orderData['order_id'] = $order_id;
                return $this->save('Order', $orderData);
            }
            return new ResultModel(true, '更新成功');
        }
        return new ResultModel(false);
    }


    /**
     * 更新司机是否已到达上车点附近
     * @param $order_id
     * @param $latitude
     * @param $longitude
     * @return ResultModel
     */
    protected function doUpdateOrderTaxiBoardingPoint($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = \CommonDefine::ORDER_STATE_2;
        $orderWhere['is_boarding_point'] = \CommonDefine::IS_BOARDING_POINT_0;
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }
        $distance = $this->getDistance($orderR->data['start_latitude'], $orderR->data['start_longitude'], $latitude, $longitude);
        $distance = round($distance, 3) * 1000;//保留两位
        if ($distance > C('BOARDING_POINT_DISTANCE')) {//是否到达上车点
            return new ResultModel(false, '还未到达上车点附近');
        }

        $orderData['order_id'] = $order_id;
        $orderData['is_boarding_point'] = \CommonDefine::IS_BOARDING_POINT_1;
        $orderData['arrive_boarding_time'] = date('Y-m-d H:i:s', time());
        return $this->save('Order', $orderData);
    }

    /**
     * 更新司机是否已到达下车点附近
     * @param $order_id
     * @param $latitude
     * @param $longitude
     * @return ResultModel
     */
    protected function doUpdateOrderTaxiAlightingPoint($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = \CommonDefine::ORDER_STATE_4;
        $orderWhere['is_alighting_point'] = \CommonDefine::IS_ALIGHTING_POINT_0;
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }
        $distance = $this->getDistance($orderR->data['end_latitude'], $orderR->data['end_longitude'], $latitude, $longitude);
        $distance = round($distance, 3) * 1000;//保留两位
        if ($distance > C('ALIGHTING_POINT_DISTANCE')) {//是否到达下车点
            return new ResultModel(false, '还未到达下车点附近');
        }

        $orderData['order_id'] = $order_id;
        $orderData['is_alighting_point'] = \CommonDefine::IS_ALIGHTING_POINT_1;
        return $this->save('Order', $orderData);
    }

    /**
     * 更新快车轨迹信息
     * @param $order_id
     * @param $latitude
     * @param $longitude
     */
    protected function doUpdateOrderTaxiLocus($order_id, $latitude, $longitude)
    {
        $orderWhere['order_id'] = $order_id;
        $orderWhere['type'] = \CommonDefine::ORDER_TYPE_11;
        $orderWhere['mchid'] = $this->mchid;
        $orderWhere['state'] = ['in', [\CommonDefine::ORDER_STATE_2, \CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4, \CommonDefine::ORDER_STATE_5]];
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            return new ResultModel(false, '更新失败');
        }

        $orderTaxiFeeR = $this->find(['order_id' => $order_id], 'OrderTaxiFee');
        if (!$orderTaxiFeeR->ret) {
            return new ResultModel(false, '更新失败');
        }

        if (time() - strtotime($orderTaxiFeeR->data['update_time']) < C('TAXI_LINE_UPDATE_FREQUENCY')) {
            return new ResultModel(false, '更新失败');
        }

        $this->startTrans();
        $orderTaxiLocusData['latitude'] = $latitude;
        $orderTaxiLocusData['longitude'] = $longitude;
        $orderTaxiLocusData['order_id'] = $order_id;
        $orderTaxiLocusData['current_time'] = date('Y-m-d H:i:s', time());
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_2 && $orderR->data['is_boarding_point'] == \CommonDefine::IS_BOARDING_POINT_1) {//等待乘客
            $orderTaxiLocusData['is_wait'] = \CommonDefine::IS_WAIT_TYPE_1;
            $lastOrderTaxiLocusListR = $this->select(['order_id' => $order_id, 'is_wait' => \CommonDefine::IS_WAIT_TYPE_1], 1, 1, 'order_taxi_locus_id desc', 'OrderTaxiLocus');
            if ($lastOrderTaxiLocusListR->ret) {
                $orderTaxiLocusData['time_from_last'] = strtotime($orderTaxiLocusData['current_time']) - strtotime($lastOrderTaxiLocusListR->data[0]['current_time']);

                //计算等待超时费用
                $orderTaxiFeeData['order_taxi_fee_id'] = $orderTaxiFeeR->data['order_taxi_fee_id'];
                $orderTaxiFeeData['total_wait_timeout'] = $orderTaxiFeeR->data['time_from_last'] + $orderTaxiLocusData['time_from_last'];
                if ($orderTaxiFeeData['total_wait_timeout'] > C('TAXI_LINE_WAIT_TIMEOUT')) {
                    if ($orderTaxiFeeR->data['total_wait_timeout_price'] > 0) {
                        $orderTaxiFeeData['total_wait_timeout_price'] = $orderTaxiFeeR->data['total_wait_timeout_price'] + $this->_computeTaxiWaitTimeoutPrice($orderR->data['line_id'], $orderTaxiLocusData['time_from_last'], date('H:i:s', time()));
                    } else {
                        $orderTaxiFeeData['total_wait_timeout_price'] = $orderTaxiFeeR->data['total_wait_timeout_price'] + $this->_computeTaxiWaitTimeoutPrice($orderR->data['line_id'], $orderTaxiFeeData['total_wait_timeout'] - C('Taxi_LINE_WAIT_TIMEOUT'), date('H:i:s', time()));
                    }
                }

                //更新价格
                $orderTaxiFeeS = $this->save('OrderTaxiFee', $orderTaxiFeeData);
                if (!$orderTaxiFeeS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }

                //更新订单总价
                $currentPrice = $orderTaxiFeeR->data['base_price'] + $orderTaxiFeeData['total_wait_timeout_price'];
                $updateOrderS = $this->updateOrderTaxiTotalPrice($order_id, $currentPrice);
                if (!$updateOrderS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }
            }
            $orderTaxiLocusA = $this->add('OrderTaxiLocus', $orderTaxiLocusData);
            if (!$orderTaxiLocusA->ret) {
                $this->transRollback();
                return new ResultModel(false, '更新失败');
            }
            $this->commitTrans();
            return new ResultModel(false, '更新成功');
        } elseif ($orderR->data['state'] == \CommonDefine::ORDER_STATE_2 && $orderR->data['is_boarding_point'] == \CommonDefine::IS_BOARDING_POINT_0) {//接乘客的路上
            $this->transRollback();
            return new ResultModel(false, '更新失败');
        } else {//在路上
            $orderTaxiLocusData['is_wait'] = \CommonDefine::IS_WAIT_TYPE_0;
            $lastOrderTaxiLocusListR = $this->select(['order_id' => $order_id, 'is_wait' => \CommonDefine::IS_WAIT_TYPE_0], 1, 1, 'order_taxi_locus_id desc', 'OrderTaxiLocus');
            if ($lastOrderTaxiLocusListR->ret) {
                $orderTaxiLocusData['distance_from_last'] = round($this->getDistance($latitude, $longitude, $lastOrderTaxiLocusListR->data[0]['latitude'], $lastOrderTaxiLocusListR->data[0]['longitude']), 2);
                $orderTaxiLocusData['time_from_last'] = strtotime($orderTaxiLocusData['current_time']) - strtotime($lastOrderTaxiLocusListR->data[0]['current_time']);

                //计算正常费用
                $orderTaxiFeeData['order_taxi_fee_id'] = $orderTaxiFeeR->data['order_taxi_fee_id'];
                //里程费用
                $orderTaxiFeeData['total_mileage'] = $orderTaxiFeeR->data['total_mileage'] + $orderTaxiLocusData['distance_from_last'];
                $orderTaxiFeeData['total_mileage_price'] = $orderTaxiFeeR->data['total_mileage_price']  + $this->computeTaxiMileagePrice($orderR->data['line_id'], $orderTaxiLocusData['distance_from_last'], date('H:i:s', time()));
                //计算时长
                $orderTaxiFeeData['total_duration'] = $orderTaxiFeeR->data['total_duration'] + $orderTaxiLocusData['time_from_last'];
                $orderTaxiFeeData['total_duration_price'] = $orderTaxiFeeR->data['total_duration_price'] + $this->computeTaxiDurationPrice($orderR->data['line_id'], $orderTaxiLocusData['time_from_last'], date('H:i:s', time()));
                //计算远程费
                $orderTaxiFeeData['total_longdistance'] = 0;
                $orderTaxiFeeData['total_longdistance_price'] = 0;
                if ($orderTaxiFeeR->data['total_longdistance'] > 0) {
                    $orderTaxiFeeData['total_longdistance'] =  $orderTaxiFeeR->data['total_longdistance'] + $orderTaxiLocusData['distance_from_last'];
                    $orderTaxiFeeData['total_longdistance_price'] = $orderTaxiFeeR->data['total_longdistance_price'] + $this->computeTaxiLongdistancePrice($orderR->data['line_id'], $orderTaxiLocusData['distance_from_last'], date('H:i:s', time()));
                } else {
                    $time = date('H:i:s', time());
                    $feeWhere['line_taxi_id'] = $orderR->data['line_id'];
                    $feeWhere['region_start_time'] = ['elt', $time];
                    $feeWhere['region_end_time'] = ['egt', $time];
                    $taxiLineLongdistanceFeeR = $this->find($feeWhere, 'LineTaxiLongdistanceFee');
                    if ($taxiLineLongdistanceFeeR->ret) {
                        $orderTaxiFeeData['total_longdistance'] =  $orderTaxiFeeData['total_mileage'] - $taxiLineLongdistanceFeeR->data['base_distance'];
                        $orderTaxiFeeData['total_longdistance_price'] = $this->computeTaxiLongdistancePrice($orderR->data['line_id'], $orderTaxiFeeData['total_longdistance'], $time);
                    }
                }

                //更新价格
                $orderTaxiFeeS = $this->save('OrderTaxiFee', $orderTaxiFeeData);
                if (!$orderTaxiFeeS->ret) {
                    $this->transRollback();
                    return new ResultModel(false, '更新失败');
                }

                //更新订单总价
                $currentPrice = $orderTaxiFeeData['total_mileage_price'] + $orderTaxiFeeData['total_duration_price'] + $orderTaxiFeeData['total_longdistance_price'] + $orderTaxiFeeR->data['total_wait_timeout_price'];
                if ($currentPrice > ($orderTaxiFeeR->data['base_price'] + $orderTaxiFeeR->data['total_wait_timeout_price'])) {
                    $updateOrderS = $this->updateOrderTaxiTotalPrice($order_id, $currentPrice);
                    if (!$updateOrderS->ret) {
                        $this->transRollback();
                        return new ResultModel(false, '更新失败');
                    }
                }
            }

            $orderTaxiLocusA = $this->add('OrderTaxiLocus', $orderTaxiLocusData);
            if (!$orderTaxiLocusA->ret) {
                $this->transRollback();
                return new ResultModel(false, '更新失败');
            }
            $this->commitTrans();
            return new ResultModel(false, '更新成功');
        }
    }

    /**
     * 计算出租车等待超时费用
     * @param $taxi_line_id
     * @param $distance
     * @param $time
     * @return int
     */
    private function _computeTaxiWaitTimeoutPrice($taxi_line_id, $timeout, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineTimeoutFeeR = $this->find($feeWhere, 'LineTaxiTimeoutFee');
        if ($taxiLineTimeoutFeeR->ret) {
            return $timeout * $taxiLineTimeoutFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 计算出租车基础费用
     * @param $taxi_line_id
     * @param $time
     * @return ResultModel
     */
    protected function computeTaxiBasePrice($taxi_line_id, $distance, $time)
    {
        $ret = new ResultModel(true, ['remain_distance' => 0, 'base_price' => 0]);
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineBaseFeeR = $this->find($feeWhere, 'LineTaxiBaseFee');
        if ($taxiLineBaseFeeR->ret) {
            $remainDistance = $distance - ($taxiLineBaseFeeR->data['include_distance'] * 1000);
            if ($remainDistance > 0) {
                $ret->data['remain_distance'] = $remainDistance;
            }
            $ret->data['base_price'] = $taxiLineBaseFeeR->data['price'];
        } else {
            return new \ResultModel(false);
        }
        return $ret;
    }

    /**
     * 计算出租车里程费用
     * @param $taxi_line_id
     * @param $distance 米
     * @param $time
     * @return int
     */
    protected function computeTaxiMileagePrice($taxi_line_id, $distance, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineMileageFeeR = $this->find($feeWhere, 'LineTaxiMileageFee');
        if ($taxiLineMileageFeeR->ret) {
            return ($distance / 1000) * $taxiLineMileageFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 计算出租车时长费用(有误差，秒和分钟)
     * @param $taxi_line_id
     * @param $duration
     * @param $time
     * @return int
     */
    protected function computeTaxiDurationPrice($taxi_line_id, $duration, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineDurationFeeR = $this->find($feeWhere, 'LineTaxiDurationFee');
        if ($taxiLineDurationFeeR->ret) {
            return round($duration / 60, 0) * $taxiLineDurationFeeR->data['price'];
        }
        return 0;
    }

    /**
     * 根据总距离计算出租车远程费
     * @param $taxi_line_id
     * @param $distance 总行程
     * @param $time
     * @return int
     */
    protected function computeTaxiLongdistancePriceByDistance($taxi_line_id, $distance, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineLongdistanceFeeR = $this->find($feeWhere, 'LineTaxiLongdistanceFee');
        if ($taxiLineLongdistanceFeeR->ret) {
            if ($distance > $taxiLineLongdistanceFeeR->data['base_distance']) {
                $longdistance = $distance - ($taxiLineLongdistanceFeeR->data['base_distance'] * 1000);
                return ($longdistance / 1000) * $taxiLineLongdistanceFeeR->data['price'];
            }
        }
        return 0;
    }


    /**
     * 计算出租车远程距离
     * @param $taxi_line_id
     * @param $distance 总行程
     * @param $time
     * @return int
     */
    protected function computeTaxiLongDistance($taxi_line_id, $distance, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineLongdistanceFeeR = $this->find($feeWhere, 'LineTaxiLongdistanceFee');
        if ($taxiLineLongdistanceFeeR->ret) {
            if ($distance > $taxiLineLongdistanceFeeR->data['base_distance']) {
                return $distance - $taxiLineLongdistanceFeeR->data['base_distance'];
            }
        }
        return 0;
    }



    /**
     * 计算出租车远程费用
     * @param $taxi_line_id
     * @param $longdistance 米
     * @param $time
     * @return int
     */
    protected function computeTaxiLongdistancePrice($taxi_line_id, $longdistance, $time)
    {
        $feeWhere['line_taxi_id'] = $taxi_line_id;
        $feeWhere['region_start_time'] = ['elt', $time];
        $feeWhere['region_end_time'] = ['egt', $time];
        $taxiLineLongdistanceFeeR = $this->find($feeWhere, 'LineTaxiLongdistanceFee');
        if ($taxiLineLongdistanceFeeR->ret) {
            $extraDistance = $longdistance - ($taxiLineLongdistanceFeeR->data['base_distance'] * 1000);
            if ($extraDistance > 0) {
                return ($extraDistance / 1000) * $taxiLineLongdistanceFeeR->data['price'];
            }
        }
        return 0;
    }

    /**
     * 更新出租车订单总价
     * @param $order_id
     * @param $currentPrice
     * @param $mchid
     * @return ResultModel
     */
    protected function updateOrderTaxiTotalPrice($order_id, $currentPrice)
    {
        $orderR = $this->find(['order_id' => $order_id], 'Order');
        if ($orderR->ret) {
            if ($orderR->data['price'] < $currentPrice) {
                //附加费
                $currentPrice += $orderR->data['extra_price'];
                //扣除优惠券减免费用
                $orderData['real_price'] = $this->reduceCouponPrice($currentPrice, $orderR->data['coupon_record_id']);

                $mch = M('AnnualFee')->where(array('mchid' => $orderR->data['order_id']))->find();
                $orderData['price'] = $currentPrice;
                $orderData['offer_price'] = $orderData['price'] - $orderData['price'] * $mch['split'] / 100;
                $orderData['split'] = $mch['split'] / 100;
                $orderData['order_id'] = $order_id;
                return $this->save('Order', $orderData);
            }
            return new ResultModel(true, '更新成功');
        }
        return new ResultModel(false);
    }

    /**
     * 获取实际支付的价格
     * @param $price
     * @param $coupon_record_id
     * @return null|\resultModel
     */
    protected function getRealPrice($price, $coupon_record_id = null, $type = 0)
    {
        $realPriceR = new \ResultModel(true, ['real_price' => $price]);
        //读取优惠券信息
        if (!empty($coupon_record_id)) {
            $checkCouponRecordR = $this->checkCouponIsAble($price, $coupon_record_id, $type);
            if ($checkCouponRecordR->ret) {
                $couponRecordWhere = " coupon_record_id = ".$coupon_record_id;
                $couponRecordR = $this->find($couponRecordWhere, 'CouponRecord');


                if ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_1) {
                    if ($this->floatgtre($price, $couponRecordR->data['value'])) {
                        $realPriceR->data['real_price'] = $price - $couponRecordR->data['value'];
                    } else {
                        $realPriceR->data['real_price'] = 0;
                    }
                } elseif ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_2) {
                    $realPriceR->data['real_price'] = round($couponRecordR->data['value'] / 10, 2) * $price;
                } else {
                    return new \ResultModel(false, '下单失败，请稍后再试');
                }

                //更新优惠券的状态
                $updateCouponRecordR = $this->updateCouponRecord($coupon_record_id, \CommonDefine::COUPON_STATUS_1);
                if (!$updateCouponRecordR->ret) {
                    return new \ResultModel(false, '下单失败，请稍后再试');
                }
            } else {
                return new \ResultModel(false, $checkCouponRecordR->data);
            }
        }

        return $realPriceR;
    }

    /**
     * 扣除优惠券减免费用
     * @param $price
     * @param null $coupon_record_id
     * @return float|int
     */
    protected function reduceCouponPrice($price, $coupon_record_id = null)
    {
        if (!empty($coupon_record_id)) {
            $couponRecordWhere = " coupon_record_id = ".$coupon_record_id;
            $couponRecordR = $this->find($couponRecordWhere, 'CouponRecord');
            if ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_1) {
                if ($this->floatgtre($price, $couponRecordR->data['value'])) {
                    $price = $price - $couponRecordR->data['value'];
                } else {
                    $price = 0;
                }
            } elseif ($couponRecordR->data['type'] == \CommonDefine::COUPON_TYYE_2) {
                $price = $couponRecordR->data['value'] / 10 * $price;
            }
        }

        return $price;
    }

    protected function seatPrice($carSeats, $seatNameArr, $priceArr)
    {
        if (empty($carSeats) || empty($priceArr)) {
            return new \ResultModel(false, '座位价格不能为空');
        }
        if (count($priceArr) != $carSeats) {
            return new \ResultModel(false, '价格与座位数量不一致');
        }
        $seatPriceArr = [];
        foreach ($priceArr as $k => $price) {
            if ($k != 0) {
                if (empty($price)) {
                    return new \ResultModel(false, '座位价格不能为空');
                }
                # 价格最低为0.01
                if (floatval($price) < 0.01) {
                    return new \ResultModel(false, '座位价格最低为0.01');
                }
            }
            $seatPriceArr[] = [
                'seat_id' => $k + 1,
                'name' => $seatNameArr[$k],
                'price' => $price,
                'optional' => \CommonDefine::OPTIONAL_0,
            ];
        }
        $ret->ret = true;
        $ret->data = $seatPriceArr;
        $ret->count = count($seatPriceArr);
        return $ret;
    }

    //检测座位是否已经售出
    protected function getSeatOptional($originSeatsArr, $distSeatsArr)
    {
        $ret = new ResultModel(false);
        if (empty($originSeatsArr) || empty($distSeatsArr)) {
            return $ret;
        }
        $seatOptional = [];
        foreach ($originSeatsArr as $originSeat) {
            foreach ($distSeatsArr as $distSeat) {
                if ($originSeat['seat_id'] == $distSeat['seat_id']) {
                    if ($originSeat['optional'] == \CommonDefine::OPTIONAL_0) {
                        $seatOptional[] = $originSeat;
                    }
                }
            }
        }
        if (!empty($seatOptional) && count($seatOptional) == count($distSeatsArr)) {
            $ret->ret = true;
            $ret->data = $seatOptional;
            $ret->count = count($seatOptional);
        }
        return $ret;
    }

    //更新座位购买状态
    protected function updateSeatOptional($originSeatsArr, $distSeatsArr, $optional = \CommonDefine::OPTIONAL_1)
    {
        $ret = new ResultModel(false);
        if (empty($originSeatsArr) || empty($distSeatsArr)) {
            return $ret;
        }
        $updateCount = 0;

        foreach ($originSeatsArr as $k => $originSeat) {
            foreach ($distSeatsArr as $distSeat) {
                if ($originSeat['seat_id'] == $distSeat['seat_id']) {
                    if ($originSeat['optional'] == $optional) {//座位当前状态与要更新的状态相同，更新失败
                        return $ret;
                    } else {
                        $originSeatsArr[$k]['optional'] = $optional;
                        $updateCount++;
                    }
                }
            }
        }

        if ($updateCount != count($distSeatsArr)) {
            return  $ret;
        }

        if (!empty($originSeatsArr)) {
            $ret->ret = true;
            $ret->data = $originSeatsArr;
            $ret->count = count($originSeatsArr);
        }
        return $ret;
    }

    //更新座位价格
    protected function updateSeatprice($originSeatsArr, $distSeatpriceArr)
    {
        $ret = new ResultModel(false);
        if (empty($originSeatsArr) || empty($distSeatpriceArr)) {
            return $ret;
        }
        if (count($originSeatsArr) != count($distSeatpriceArr)) {
            $ret->data = "座位与价格数不匹配";
            return $ret;
        }
        foreach ($originSeatsArr as $k => &$originSeat) {
            if ($distSeatpriceArr[$k] <= 0 && $k > 0) {
                $ret->data = "座位价格不能小于等于0";
                return $ret;
            }
            $originSeat['price'] = $distSeatpriceArr[$k];
        }
        if (!empty($originSeatsArr)) {
            $ret->ret = true;
            $ret->data = $originSeatsArr;
            $ret->count = count($originSeatsArr);
        }
        return $ret;
    }

    //获得座位最低价格
    protected function getMinSeatPrice($originSeatsArr)
    {
        if (empty($originSeatsArr)) {
            return 0;
        }
        $seatPrice = [];
        foreach ($originSeatsArr as $k => $originSeat) {
            if ($k > 0) {
                $seatPrice[] = $originSeat['price'];
            }
        }
        $minSeatPrice = min($seatPrice);
        return $minSeatPrice == "" ? 0 : $minSeatPrice;
    }

    //获取座位信息
    protected function getOrderSeatOptionInfo($seatOptionalStr)
    {
        $orderSeatOptionInfo = "";
        if (empty($seatOptionalStr)) {
            return $orderSeatOptionInfo;
        }
        $seatOptionalArr = json_decode($seatOptionalStr, true);
        foreach ($seatOptionalArr as $seatOptional) {
            if (empty($orderSeatOptionInfo)) {
                $orderSeatOptionInfo = $seatOptional['name'];
            } else {
                $orderSeatOptionInfo .= "、". $seatOptional['name'];
            }
        }

        return $orderSeatOptionInfo;
    }

    protected function checkPayService($mchid)
    {
        return $this->find(['mchid' => $mchid], 'PayService');
    }

    protected function checkMarketService($mchid)
    {
        return $this->find(['mchid' => $mchid], 'MarketService');
    }

    protected function getPayConfig($mchid = 0, $channel = 'wx_pub')
    {
        $where['mchid'] = $mchid;
        $where['channel'] = $channel;
        return $this->find($where, 'PayConfig');
    }

    protected function ignoreClientBrowser()
    {
        if (strpos(get_client_browser_user_agent(), 'com.higgses.cczhaoche.yunyuzhong')) {
            return true;
        }

        return false;
    }

    /**
     *获取时间区间内的线下线上交易额
     *@param  管理员组 $group_id
     *@param  商户id $branchid
     *@param  类型 $type 1-线上线下全部；2-线上；3-线下；
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getGenerationRegionTurnover($group_id, $branchid, $type = 1, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);

        $turnoverConditionString = "";

        switch ($type) {
            case 1:
                $turnoverConditionString = "((is_pay=1 OR is_pre_pay=1) OR (is_pay=0 AND is_pre_pay=0  AND state=6))";//线上线下交易金额
                break;
            case 2:
                $turnoverConditionString = "(is_pay=1 OR is_pre_pay=1)";//线上交易金额
                break;
            case 3:
                $turnoverConditionString = "(is_pay=0 AND is_pre_pay=0  AND state=6)";//线下交易金额
                break;
            default:
                $turnoverConditionString = "((is_pay=1 OR is_pre_pay=1) OR (is_pay=0 AND is_pre_pay=0  AND state=6))";//线上线下交易金额
                break;
        }
        //        $todayPriceString = "";
        if (!empty($start_time) && !empty($end_time)) {
            $turnoverConditionString .= " AND create_time>='".$start_time."' AND create_time<='".$end_time."' ";
        }

        switch ($group_id) {
            case 1:
            case 2:
            case 3:
                $turnoverConditionString .= " AND temp_apply_branchid=".$branchid;
                break;
            default:
                return $ret;
        }

        $turnData = M()->table('cp_order o')
            ->where($turnoverConditionString)
            ->field('sum(price) as turnover')
            ->order('o.create_time desc')
            ->select();
        //        $ret = $this->select($turnoverConditionString,null,null,'create_time desc','Order','sum(price) as turnover');

        if ($turnData) {
            $ret->ret = true;
            $ret->data = $turnData[0];
            $ret->count = count($turnData);
        }

        return $ret;
    }

    /**
     * 订单完成计入积分
     * @Date 2022.07.13
     * <AUTHOR>
     * @version 3.3
     */
    protected function recordPassengerPoints($passengerId, $orderId)
    {
        try {
            if (!$passengerId || !$orderId) {
                return;
            }
            $pointsResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/users/{$passengerId}/orders/{$orderId}/points", 'post');
            $token = json_decode($pointsResponse, true);
            if ($token['status'] == 'success') {
                //            \Log::write("计入积分成功，用户ID：$passengerId,订单ID:$orderId");
            } else {
                //            \Log::write("计入积分失败，用户ID：$passengerId,订单ID:$orderId");
            }
        } catch (Exception $e) {
        }
    }


    /**
     * 乘客端推送通知入栈
     * @param $type
     * @param $relationId
     * @return 返回一个 ResultModel结果
     */
    protected function pushPassengerNoticeQueue($type = 1, $relationId)
    {
        if ($this->mchid == 181 || $this->mchid == 1231) {
        } else {
            return ;
        }
        try {
            switch ($type) {
                case 1:{
                    $where['type'] = $type;
                    $where['relation_id'] = $relationId;
                    $passengerNoticeQueueR = $this->find($where, 'PassengerNoticeQueue');
                    if ($passengerNoticeQueueR->ret) {
                        return;
                    }
                    $orderR = $this->find(['order_id' => $relationId], 'Order');
                    if ($orderR->ret) {
                        $noticeTime = date('Y-m-d H:i:s', strtotime("-".C('TIMER_PASSENGER_NOTICE')." second", strtotime($orderR->data['start_time'])));
                        if (strtotime($noticeTime) - strtotime(time()) < 0) {
                            return ;
                        }
                        if (empty($orderR->data['passenger_id'])) {
                            return ;
                        }

                        $noticeQueueData['notice_time'] = $noticeTime;
                        $noticeQueueData['passenger_id'] = $orderR->data['passenger_id'];
                        $noticeQueueData['type'] = $type;
                        $noticeQueueData['relation_id'] = $relationId;
                        $noticeData = [
                            'sms' => [
                                'order_id' => $relationId,
                                'to_user_type' => \CommonDefine::USER_TYPE_1,
                                'message_type' => \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_2,
                            ],
                            'wx_message' => [
                                'order_id' => $relationId,
                                'to_user_type' => \CommonDefine::USER_TYPE_1,
                                'message_type' => \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_2,
                            ]
                        ];
                        $noticeQueueData['notice_data'] = addslashes(json_encode($noticeData));
                        $this->add('PassengerNoticeQueue', $noticeQueueData);
                    }
                    break;
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * 乘客端删除推送消息
     * @param $type
     * @param $relationId
     * @return 返回一个 ResultModel结果
     */
    protected function delPassengerNoticeQueue($type, $relationId)
    {
        try {
            switch ($type) {
                case 1:{
                    M('PassengerNoticeQueue')->where(['type' => $type, 'relation_id' => $relationId])->delete();
                    break;
                }
            }
        } catch (Exception $e) {
        }
    }


    /**
     * 快速添加乘车人员
     * @param $members 购买保险成员[{"real_name":"001","ID_number":"510122200020170001"，"seat_id":""},{"real_name":"002","ID_number":"510122200020170002"，"seat_id":""}]
     */
    protected function doFastAddPassengerMember($members)
    {
        $ret = new \ResultModel(false);
        $membersArr = [];

        if (is_array($members)) {
            $membersArr = $members;
        } else {
            $membersArr = json_decode($members, true);
        }

        if (empty($membersArr)) {
            return $ret;
        }

        $retData['insurance_price'] = 0;
        $retData['count'] = 0;
        $tempIdNumberArr = [];
        foreach ($membersArr as $k => $member) {
            if (empty($member)) {
                return $ret;
            }

            if (!validateIDCard($member['ID_number'])) {
                return $this->output(new \ResultModel(false, '身份证号码错误'));
            }

            if (in_array($member['ID_number'], $tempIdNumberArr)) {
                return $this->output(new \ResultModel(false, '身份证号码重复！'));
            }
            array_push($tempIdNumberArr, $member['ID_number']);

            //同步添加至常用联系人列表中
            $passengerMemberWhere['passenger_id'] = $this->state->user_id;
            $passengerMemberWhere['ID_number'] =  $member['ID_number'];
            $passengerMemberWhere['is_del'] =  \CommonDefine::IS_DEL_0;
            $passengerMemberR = $this->find($passengerMemberWhere, 'PassengerMember');
            if (!$passengerMemberR->ret) {
                $passengerMemberData['passenger_id'] = $this->state->user_id;
                $passengerMemberData['real_name'] = $member['real_name'];
                $passengerMemberData['ID_number'] = $member['ID_number'];
                $passengerMemberA = $this->add('PassengerMember', $passengerMemberData);
                return $passengerMemberA;
            }
        }

        return new ResultModel(true, $retData);
    }

    /**
     * 购买乘客保险
     * @param $members 购买保险成员[{"real_name":"001","ID_number":"510122200020170001"},{"real_name":"002","ID_number":"510122200020170002"}]
     * @param $insurances 购买保险集合[{"insurance_name":"平安保险1","insurance_desc":"意外车险","insurance_amount":"1.00"},{"insurance_name":"平安保险2","insurance_desc":"意外车险","insurance_amount":"1.00"}]
     * @return null|\resultModel
     */
    protected function doOrderInsurances($members, $insurances)
    {
        $ret = new \ResultModel(false);
        $membersArr = [];
        $insurancesArr = [];

        if (is_array($members)) {
            $membersArr = $members;
        } else {
            $membersArr = json_decode($members, true);
        }

        if (is_array($insurances)) {
            $insurancesArr = $insurances;
        } else {
            $insurancesArr = json_decode($insurances, true);
        }

        if (empty($membersArr) || empty($insurancesArr)) {
            return $ret;
        }

        $retData['insurance_price'] = 0;
        $retData['count'] = 0;
        foreach ($membersArr as $k => $member) {
            if (empty($member)) {
                return $ret;
            }
            foreach ($insurancesArr as $kk => $insurance) {
                if (empty($insurance)) {
                    return $ret;
                }

                $orderInsuranceData['real_name'] = $member['real_name'];
                $orderInsuranceData['ID_number'] = $member['ID_number'];
                $orderInsuranceData['insurance_name'] = $insurance['insurance_name'];
                $orderInsuranceData['insurance_desc'] = $insurance['insurance_desc'];
                $orderInsuranceData['insurance_amount'] = $insurance['insurance_amount'];
                $orderInsuranceData['status'] = 1;
                $orderInsuranceA = $this->add('OrderInsurance', $orderInsuranceData);
                if (!$orderInsuranceA->ret) {
                    return $ret;
                }

                $retData['insurance_price'] += $insurance['insurance_amount'];
                $retData['insurance_count']++;
                $retData['order_insurance_ids'][] = $orderInsuranceA->data;
            }
            $retData['insurance_person']++;
        }

        return new ResultModel(true, $retData);
    }

    /**
     * 更新保险所属订单
     * @param $order_id 订单编号
     * @param $orderInsurancesArr 保险订单编号集合
     * @return null|\resultModel
     */
    protected function updateOrderInsurance($order_id, $orderInsurancesArr)
    {
        $ret = new \ResultModel(false);
        if (empty($order_id) || empty($orderInsurancesArr)) {
            return $ret;
        }

        foreach ($orderInsurancesArr as $k => $v) {
            if (empty($v)) {
                return $ret;
            }

            $updateOrderInsurance['order_insurance_id'] = $v;
            $updateOrderInsurance['order_id'] = $order_id;
            $updateOrderInsuranceS = $this->save('OrderInsurance', $updateOrderInsurance);
            if (!$updateOrderInsuranceS->ret) {
                return $ret;
            }
        }

        return new \ResultModel(true);
    }

    /**
     * 取消订单下所属的所有保险
     * @param $order_id 订单编号
     * @return null|\resultModel
     */
    protected function cancelOrderInsurances($order_id)
    {
        $ret = new \ResultModel(false);
        if (empty($order_id)) {
            return $ret;
        }
        $updateOrderInsurancesR = $this->select(['order_id' => $order_id], null, null, null, 'OrderInsurance');
        if (!$updateOrderInsurancesR->ret) {
            return $ret;
        }
        foreach ($updateOrderInsurancesR->data as $k => $orderInsurance) {
            $updateOrderInsurance['order_insurance_id'] = $orderInsurance['order_insurance_id'];
            $updateOrderInsurance['status'] = 2;
            $updateOrderInsuranceS = $this->save('OrderInsurance', $updateOrderInsurance);
            if (!$updateOrderInsuranceS->ret) {
                return $ret;
            }
        }

        return new \ResultModel(true);
    }

    /**
     * 预订摆渡车
     * @param int $line_fast_id 快车线路id
     * @param int $ferry_type 摆渡车类型（0-非接送单；1-接；2-送）
     * @param int $payMode 支付方式
     * @param int $book_seating 预定座位数
     * @param date $start_time 出发时间
     * @param string $reseverd_phone 预留电话号码
     * @param string $reseverd_info 预留信息
     * @param string $start_longitude 上车位置经度
     * @param string $start_latitude 上车位置维度
     * @param string $end_longitude 下车位置经度
     * @param string $end_latitude 下车位置维度
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param string $distance 距离（默认为0）
     * @param string $duration 时间（默认为0）
     */
    protected function doMobileBookLineFastInnerFunction($line_fast_id, $ferry_type = \CommonDefine::FERRY_TYPE_0, $payMode, $book_seating = 1, $start_time = null, $reseverd_phone = null, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $distance = 0, $duration = 0)
    {
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');

        $lineFastR = $this->find(['id' => $line_fast_id], 'LineFast');
        //附近司机
        $fastOrderData['driver_id'] = null;
        $aroundDriversR = $this->getLineFastAroundDrivers($line_fast_id, $start_longitude, $start_latitude, $book_seating);
        if (!$aroundDriversR->ret) {
            //          return $this->output(new \ResultModel(false, '附近暂无司机，请电话联系客服'));
        } else {
            $fastOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
            $fastOrderData['driver_id'] = $aroundDriversR->data[0]['driver_id'];
        }

        //计算费用
        $priceR = $this->getLineFastPredictPrice($lineFastR->data['id'], $distance * 1000, $duration);
        if (!$priceR->ret) {
            return new \ResultModel(false, '网络异常，请稍后再试');
        }
        $price = $priceR->data['price'];

        $fastOrderData['passenger_id'] = $this->state->user_id;
        $fastOrderData['type'] = \CommonDefine::ORDER_TYPE_7;
        $fastOrderData['mchid'] = $lineFastR->data['mchid'];
        $fastOrderData['line_id'] = $lineFastR->data['id'];
        $fastOrderData['branchid'] = $lineFastR->data['branchid'];
        $fastOrderData['start_address_code'] = $start_address_code;
        $fastOrderData['end_address_code'] = $end_address_code;
        $fastOrderData['start_address_remark'] = empty($start_address_remark) ? $lineFastR->data['start_name'] : $start_address_remark;
        $fastOrderData['end_address_remark'] = empty($end_address_remark) ? $lineFastR->data['end_name'] : $end_address_remark;
        $fastOrderData['drive_model'] = 1;
        $fastOrderData['start_time'] = $start_time;
        $fastOrderData['book_seating'] = $book_seating;
        $fastOrderData['order_no'] = $this->createOrderNo($start_time);
        $fastOrderData['rob_order'] = \CommonDefine::ROB_ORDER_0;//默认非抢单模式;
        $fastOrderData['pay_mode'] = $payMode;
        $fastOrderData['reseverd_phone'] = $reseverd_phone;
        $fastOrderData['reseverd_info'] = $reseverd_info;
        $fastOrderData['start_longitude'] = $start_longitude;
        $fastOrderData['start_latitude'] = $start_latitude;
        $fastOrderData['end_longitude'] = $end_longitude;
        $fastOrderData['end_latitude'] = $end_latitude;
        $fastOrderData['ferry_type'] = $ferry_type;

        $mch = M('AnnualFee')->where(array('mchid' => $lineFastR->data['mchid']))->find();
        $isChannel = false;
        if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
            //渠道价格
            $fastOrderData['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
            $fastOrderData['channel_account_id'] = $passengerR->data['invite_id'];
            $isChannel = true;
        }

        if ($isChannel) {
            //暂不支持渠道代约
            $fastOrderData['price'] = $price;
            $fastOrderData['offer_price'] = $fastOrderData['price'] - $fastOrderData['price'] * $mch['split'] / 100;
            $fastOrderData['split'] = $mch['split'] / 100;
        } else {
            $fastOrderData['price'] = $price;
            $fastOrderData['offer_price'] = $fastOrderData['price'] - $fastOrderData['price'] * $mch['split'] / 100;
            $fastOrderData['split'] = $mch['split'] / 100;
        }
        $fastOrderData['merge_price'] = $price;
        $fastOrderData['real_price'] = $price;
        $fastOrderData['offer_boss_price'] =  $fastOrderData['price'] * $this->data['split'];

        $orderS = $this->add('Order', $fastOrderData);
        if ($orderS->ret) {
            //添加计费信息
            $orderFastFeeData['order_id'] = $orderS->data;
            $orderFastFeeData['base_price'] = $priceR->data['base_price'];
            $orderFastFeeData['total_mileage'] = $distance * 1000;
            $orderFastFeeData['total_mileage_price'] = $priceR->data['mileage_price'];
            $orderFastFeeData['total_duration'] = $duration;
            $orderFastFeeData['total_duration_price'] = $priceR->data['duration_price'];
            $orderFastFeeData['total_longdistance'] = $this->computeLongDistance($line_fast_id, $distance, date('H:i:s', time()));
            $orderFastFeeData['total_longdistance_price'] = $priceR->data['longdistance_price'];
            $orderFastFeeData['total_wait_timeout'] = 0;
            $orderFastFeeData['total_wait_timeout_price'] = 0;
            $orderFastFeeS = $this->add('OrderFastFee', $orderFastFeeData);
            if (!$orderFastFeeS->ret) {
                return new \ResultModel(false, '下单失败');
            }

            return new \ResultModel(true, ['order_id' => $orderS->data, 'price' => $price, 'driver_id' => $fastOrderData['driver_id']]);
        }
        return $orderS;
    }

    /**
     * 取消班线-摆渡车（分流车）含接送  例如：a（摆渡车上车点）->A(班线车出发地)->B(班线车目的地)->b（摆渡车下车点）
     * @param int $order_id 班线车，订单id
     */
    protected function doCancelBookLineFastInnerFunction($order_id)
    {
        $orderSubWhere['order_id'] = $order_id;
        $orderSubRs = $this->select($orderSubWhere, null, null, null, 'OrderSub');
        if ($orderSubRs->ret) {
            foreach ($orderSubRs->data as $k => $v) {
                $orderR = $this->find(['order_id' => $v['relation_order_id']], 'Order');
                if (!$orderR->ret) {
                    return new \ResultModel(false);
                }

                if (!empty($orderR->data['driver_id'])) {
                    $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        if ($orderR->data['seat_is_add'] == 1) {
                            $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                            $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'],'residual_seating' => $residual_seating));
                            if ($driverS->ret) {
                                if (C('SEAT_LOG_ON')) {
                                    $this->recordSeatLog($orderR->data['order_id'], $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doCancelBook', '乘客取消订单');
                                }
                            } else {
                                return new \ResultModel(false, '摆渡车司机座位更新失败');
                            }
                        }
                    }
                }
                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_6) {
                    if ($orderR->data['ferry_type'] == \CommonDefine::FERRY_TYPE_1) {
                        return new \ResultModel(false, '取消失败，摆渡车(接)已完成');
                    } else {
                        return new \ResultModel(false, '取消失败，摆渡车(送)已完成');
                    }
                }

                $ferryOrderS = $this->save('Order', array('order_id' => $v['relation_order_id'],'state' => 7));
                //需要判定摆渡车订单已接单，司机座位数回滚
                if (!$ferryOrderS->ret) {
                    return new \ResultModel(false);
                }
            }
        }

        return new \ResultModel(true);
    }


    /**
     * 下单创建车票
     *
     * @param mixed $seat_occupied
     * @param integer $order_id
     * @param integer $number
     * @param mixed $seat_ids
     * @return void
     */
    protected function createDingzhikeyunTicket($seat_occupied, $order_id, $number, $seat_ids)
    {
        /**
         * 下单创建车票
         * <AUTHOR> <<EMAIL>>
         */
        $seatOccupied = $seat_occupied ? json_encode($seat_occupied) : '';
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/tickets", 'post', array('number' => $number, 'seat_ids' => $seat_ids, 'seat_occupied' => $seatOccupied));
        $result = json_decode($response, true);
        if (!isset($result['status'])
            || $result['status'] != 'success') {
            $this->orderTransRollback($order_id);
            # 前一个事务提交后，分布式系统再次提示错误则回滚数据
            return $this->output(new \ResultModel(false, $result['message']));
        } else {
            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($order_id);
        }
    }

    protected function orderTransRollback($order_id)
    {
        $where['order_id'] = $order_id;
        $data['deleted_at'] = date('Y-m-d H:i:s');
        $data['total_booking_seats'] = 0;
        $data['book_seating'] = 0;
        $data['state'] = \CommonDefine::ORDER_STATE_8;
        M('Order')->where($where)->save($data);
    }
}

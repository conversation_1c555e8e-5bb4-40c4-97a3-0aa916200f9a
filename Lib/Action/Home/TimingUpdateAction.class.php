<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 定时更新模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class TimingUpdateAction extends HomeCoreAction
{
    /**
     * @return void
     */
    public function doTimingOrderOrders()
    {
        try {
            if(C('REDIS_ON')) {
                $orderOrderExpireTime = strtotime("-2 hour"); //过了出发时间不再指派
                $redisInstance = Cache::getInstance('Redis', C('REDIS_ORDER_POOL'));
                $orderIdListLen = $redisInstance->len(\CommonDefine::ORDER_POOL_TAXI);
                $orderSuccessCount = 0;
                for ($i = 0; $i < $orderIdListLen; $i++) {
                    $orderId = $redisInstance->pop(\CommonDefine::ORDER_POOL_TAXI);
                    if (!empty($orderId)) {
                        $where['order_id'] = $orderId;
                        $where['state'] = \CommonDefine::ORDER_STATE_1;
                        $where['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                        $orderR = $this->find($where, 'Order');
                        if ($orderR->ret) {
                            if ($orderOrderExpireTime > strtotime($orderR->data['start_time'])) {
                                continue;
                            }
                            //附近司机
                            $aroundDriversR = $this->getLineTaxiAroundDrivers($orderR->data['line_id'], $orderR->data['start_longitude'], $orderR->data['start_latitude'], $orderR->data['book_seating']);
                            if (!$aroundDriversR->ret) {
                                $redisInstance->push(\CommonDefine::ORDER_POOL_TAXI, $orderId);
                                continue;
                            }
                            $driver_id = $aroundDriversR->data[0]['driver_id'];
                            $updateOrderData['order_id'] = $orderId;
                            $updateOrderData['driver_id'] = $driver_id;
                            $orderS = $this->save('Order', $updateOrderData);
                            if (!$orderS->ret) {
                                $redisInstance->push(\CommonDefine::ORDER_POOL_TAXI, $orderId);
                                continue;
                            }
                            $orderSuccessCount++;
                            //通知司机
                            $this->sendMessage($orderId, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
                        }
                    }
                }

                $ret = new ResultModel(true, [
                    'order_pool_len' => $orderIdListLen,
                    'order_success_count' => $orderSuccessCount,
                ], 1);

                return $this->output($ret);
            }
        } catch (Exception $e) {
            echo $e->getMessage();
            die;
        }
    }

    /**
     * @return void
     */
    public function doTimingOrderFastOrders()
    {
        try {
            if(C('REDIS_ON')) {
                $orderOrderExpireTime = strtotime("-2 hour"); //过了出发时间不再指派
                $redisInstance = Cache::getInstance('Redis', C('REDIS_ORDER_POOL'));
                $orderIdListLen = $redisInstance->len(\CommonDefine::ORDER_POOL_FAST);
                //                var_dump($redisInstance->range(\CommonDefine::ORDER_POOL_FAST, 0, -1));die;
                $orderSuccessCount = 0;
                for ($i = 0; $i < $orderIdListLen; $i++) {
                    $orderId = $redisInstance->pop(\CommonDefine::ORDER_POOL_FAST);
                    if (!empty($orderId)) {
                        $where['order_id'] = $orderId;
                        $where['state'] = \CommonDefine::ORDER_STATE_1;
                        $orderR = $this->find($where, 'Order');
                        if ($orderR->ret) {
                            if ($orderOrderExpireTime > strtotime($orderR->data['start_time'])) {
                                continue;
                            }
                            //附近司机
                            $aroundDriversR = $this->getLineFastAroundDrivers($orderR->data['line_id'], $orderR->data['start_longitude'], $orderR->data['start_latitude'], $orderR->data['book_seating']);
                            if (!$aroundDriversR->ret) {
                                $redisInstance->push(\CommonDefine::ORDER_POOL_FAST, $orderId);
                                continue;
                            }
                            $driver_id = $aroundDriversR->data[0]['driver_id'];
                            $updateOrderData['order_id'] = $orderId;
                            $updateOrderData['driver_id'] = $driver_id;
                            $orderS = $this->save('Order', $updateOrderData);
                            if (!$orderS->ret) {
                                $redisInstance->push(\CommonDefine::ORDER_POOL_FAST, $orderId);
                                continue;
                            }
                            $orderSuccessCount++;
                            //通知司机
                            $this->sendMessage($orderId, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
                        }
                    }
                }

                $ret = new ResultModel(true, [
                    'order_pool_len' => $orderIdListLen,
                    'order_success_count' => $orderSuccessCount,
                ], 1);

                return $this->output($ret);
            }
        } catch (Exception $e) {
            echo $e->getMessage();
            die;
        }
    }

    /**
     * 定时退款查询近三个月退款状态
     */
    public function doTimingUpdateRefunds()
    {
        $where = " status =" . \CommonDefine::REFUND_STATUS_1 . " OR status =" . \CommonDefine::REFUND_STATUS_2;
        $where .= " AND create_time < '" . date('Y-m-d H:i:s', strtotime("-5 minutes")) . "'";
        $where .= " AND create_time > '" . date("Y-m-d H:i:s", strtotime("-3 month")) . "'";
        $refundsAR = M()->table('cp_refunds')->where($where)->order('id desc')->select();
        if (!empty($refundsAR)) {
            foreach ($refundsAR as $key => $refundA) {
                $orderR = $this->find(array('order_id' => $refundA['order_id']));
                if ($orderR->ret) {
                    $refundQuery = new \WxPayUtil();
                    if (!$refundQuery->init($orderR->data['mchid'])) {
                        continue;
                    }
                    $refundQueryR = $refundQuery->refundQuery($refundA['refund_no']);
                    if ($refundQueryR->ret) {
                        $refundsS = $this->save('Refunds', array('id' => $refundA['id'], 'status' => $refundQueryR->data['status']));
                    }
                }
            }
        }
    }

    /**
     * 定时排班
     */
    public function doTimingAddLineClassTrain()
    {
        $currentDay = date('Y-m-d', time());

        $where = ' scheduling_type = 2';
        $where .= ' AND is_del = ' . \CommonDefine::IS_DEL_0;
        $where .= ' AND status = ' . \CommonDefine::LINE_CLASS_OPERATE_1;
        $lineClasssR = $this->select($where, null, null, null, 'LineClass');
        if ($lineClasssR->ret) {
            foreach ($lineClasssR->data as $k => $lineClass) {
                $lineClassTrainWhere = 'line_class_id = ' . $lineClass['id'];
                $lineClassTrainsR = $this->select($lineClassTrainWhere, 1, 1, 'start_date desc', 'LineClassTrain');
                if ($lineClassTrainsR->ret) {
                    foreach ($lineClassTrainsR->data as $tk => $lineClassTrain) {
                        $needSchedulingNum = $lineClass['pre_sale_time'];
                        $schedulingDate = $currentDay;
                        if (strtotime($currentDay) < strtotime($lineClassTrain['start_date'])) {
                            $needSchedulingNum = $lineClass['pre_sale_time'] - \diffBetweenTwoDays($currentDay, $lineClassTrain['start_date']);
                            $schedulingDate = $lineClassTrain['start_date'];
                        }

                        if ($needSchedulingNum > 0) {
                            for ($i = 1; $i <= $needSchedulingNum; $i++) {
                                $newStartDate = date('Y-m-d', strtotime($schedulingDate . " +{$i} day"));
                                if (!in_array(date('w', strtotime($newStartDate)), explode(',', $lineClass['exclude_scheduling']))) {//不在不排班的日期内
                                    $lineClassTrainDriverWhere = 'line_class_train_id = ' . $lineClassTrain['line_class_train_id'];
                                    $lineClassTrainDriversR = $this->select($lineClassTrainDriverWhere, null, null, null, 'LineClassTrainDriver');
                                    if (!$lineClassTrainDriversR->ret) {
                                        continue;
                                    }
                                    $data['line_class_id'] = $lineClass['id'];
                                    $data['mchid'] = $lineClass['mchid'];
                                    //计算座位数
                                    $data['remain_tickets'] = 0;
                                    $data['total_tickets'] = 0;
                                    $data['price'] = $lineClass['price'];
                                    $data['channel_price'] = $lineClass['channel_price'];
                                    $data['branchid'] = $lineClass['branchid'];
                                    $data['mchid'] = $lineClass['mchid'];
                                    $data['car_seats'] = $lineClass['car_seats'];
                                    $data['seat_price'] = $lineClass['seat_price'];
                                    $data['is_seat_selection'] = $lineClass['is_seat_selection'];
                                    $data['seat_layout'] = $lineClass['seat_layout'];
                                    $data['is_start_ferry'] = $lineClass['is_start_ferry'];
                                    $data['is_end_ferry'] = $lineClass['is_end_ferry'];
                                    $currentTime = date('Y-m-d H:i:s', time());
                                    $data['create_time'] = $currentTime;
                                    $data['update_time'] = $currentTime;

                                    if($lineClass['is_seat_selection']  == \CommonDefine::SEAT_SELECTION_0) {
                                        foreach ($lineClassTrainDriversR->data as $lineClassTrainDriverKey => $lineClassTrainDriverVal) {
                                            $driverR = $this->find(array('driver_id' => $lineClassTrainDriverVal['driver_id']), 'Driver');
                                            if ($driverR->ret) {
                                                $data['remain_tickets'] += $driverR->data['total_seating'] - 1;
                                                $data['total_tickets'] += $driverR->data['total_seating'] - 1;
                                            } else {
                                                continue;
                                            }
                                        }
                                    } else {
                                        $data['remain_tickets'] = $lineClass['car_seats'] - 1;
                                        $data['total_tickets'] = $lineClass['car_seats'] - 1;
                                    }

                                    $this->startTrans();
                                    $data['line_class_train_no'] = date('Ymd', strtotime($newStartDate)) . $lineClass['id'] . rand(10000, 99999);
                                    $data['start_date'] = $newStartDate;
                                    $LineClassTrainId = M('LineClassTrain')->add($data);
                                    if (!$LineClassTrainId) {
                                        $this->transRollback();
                                        continue;
                                    }

                                    //添加班次司机
                                    foreach ($lineClassTrainDriversR->data as $lineClassTrainDriverKey => $lineClassTrainDriverVal) {
                                        $lineClassTrainDriverData = [
                                            'line_class_train_id' => $LineClassTrainId,
                                            'driver_id' => $lineClassTrainDriverVal['driver_id'],
                                            'sort' => $lineClassTrainDriverVal['sort'],
                                            'mchid' => $data['mchid']
                                        ];
                                        $lineClassTrainDriverA = $this->add('LineClassTrainDriver', $lineClassTrainDriverData);
                                        if (!$lineClassTrainDriverA) {
                                            $this->transRollback();
                                            continue;
                                        }
                                    }
                                    $updateLineClassData['id'] = $lineClass['id'];
                                    $updateLineClassData['last_scheduling_time'] = $newStartDate;
                                    $lineClassS = $this->save('LineClass', $updateLineClassData);
                                    if (!$lineClassS->ret) {
                                        $this->transRollback();
                                        continue;
                                    }

                                    $this->commitTrans();
                                }
                            }
                        }
                    }
                } else {//没有排过班

                }
            }
        }
    }

    /**
     * 更新中心坐标（拼车）
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateLineCenterLocation($mchid = null)
    {
        $ret = new \ResultModel(false);
        $count = 0;
        if (!empty($mchid)) {
            $where['mchid'] = $mchid;
        }

        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $order = 'id desc';
        $linesR = $this->select($where, null, null, $order, 'Line');
        if ($linesR->ret) {
            foreach ($linesR->data as $k => $v) {
                $updateData['id'] = $v['id'];
                if ($v['start_province_code']) {
                    //直辖市
                    if (in_array($v['start_province_code'], $this->directlyCityArr)) {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        if ($startAddressPPR->ret) {
                            if ($v['start_area_code']) {
                                $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                                if ($startAddressR->ret) {
                                    if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        $startAddressPR = $this->find(array('address_id' => $v['start_city_code']), 'GdRegion');
                        if ($v['start_area_code']) {
                            $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                            if ($startAddressR->ret) {
                                if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }


                if ($v['end_province_code']) {
                    //直辖市
                    if (in_array($v['end_province_code'], $this->directlyCityArr)) {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        if ($endAddressPPR->ret) {
                            if ($v['end_area_code']) {
                                $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                                if ($endAddressR->ret) {
                                    if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        $endAddressPR = $this->find(array('address_id' => $v['end_city_code']), 'GdRegion');
                        if ($v['end_area_code']) {
                            $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                            if ($endAddressR->ret) {
                                if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $endAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }

                $lineS = $this->save('Line', $updateData);
                if ($lineS->ret) {
                    $count++;
                }
            }
        }

        if ($count > 0) {
            $ret->ret = true;
            $ret->data = $count;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 更新中心坐标（包车）
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateLineCharteredCenterLocation($mchid = null)
    {
        $ret = new \ResultModel(false);
        $count = 0;
        if (!empty($mchid)) {
            $where['mchid'] = $mchid;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $order = 'id desc';
        $linesR = $this->select($where, null, null, $order, 'LineChartered');
        if ($linesR->ret) {
            foreach ($linesR->data as $k => $v) {
                $updateData['id'] = $v['id'];
                if ($v['start_province_code']) {
                    //直辖市
                    if (in_array($v['start_province_code'], $this->directlyCityArr)) {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        if ($startAddressPPR->ret) {
                            if ($v['start_area_code']) {
                                $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                                if ($startAddressR->ret) {
                                    if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        $startAddressPR = $this->find(array('address_id' => $v['start_city_code']), 'GdRegion');
                        if ($v['start_area_code']) {
                            $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                            if ($startAddressR->ret) {
                                if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }


                if ($v['end_province_code']) {
                    //直辖市
                    if (in_array($v['end_province_code'], $this->directlyCityArr)) {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        if ($endAddressPPR->ret) {
                            if ($v['end_area_code']) {
                                $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                                if ($endAddressR->ret) {
                                    if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        $endAddressPR = $this->find(array('address_id' => $v['end_city_code']), 'GdRegion');
                        if ($v['end_area_code']) {
                            $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                            if ($endAddressR->ret) {
                                if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $endAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }

                $lineS = $this->save('LineChartered', $updateData);
                if ($lineS->ret) {
                    $count++;
                }
            }
        }

        if ($count > 0) {
            $ret->ret = true;
            $ret->data = $count;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 更新中心坐标（班线车）
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateLineClassCenterLocation($mchid = null)
    {
        $ret = new \ResultModel(false);
        $count = 0;
        if (!empty($mchid)) {
            $where['mchid'] = $mchid;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $order = 'id desc';
        $linesR = $this->select($where, null, null, $order, 'LineClass');
        if ($linesR->ret) {
            foreach ($linesR->data as $k => $v) {
                $updateData['id'] = $v['id'];
                if ($v['start_province_code']) {
                    //直辖市
                    if (in_array($v['start_province_code'], $this->directlyCityArr)) {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        if ($startAddressPPR->ret) {
                            if ($v['start_area_code']) {
                                $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                                if ($startAddressR->ret) {
                                    if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        $startAddressPR = $this->find(array('address_id' => $v['start_city_code']), 'GdRegion');
                        if ($v['start_area_code']) {
                            $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                            if ($startAddressR->ret) {
                                if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }


                if ($v['end_province_code']) {
                    //直辖市
                    if (in_array($v['end_province_code'], $this->directlyCityArr)) {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        if ($endAddressPPR->ret) {
                            if ($v['end_area_code']) {
                                $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                                if ($endAddressR->ret) {
                                    if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        $endAddressPR = $this->find(array('address_id' => $v['end_city_code']), 'GdRegion');
                        if ($v['end_area_code']) {
                            $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                            if ($endAddressR->ret) {
                                if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $endAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }
                $updateData['return_center_start_latlng'] = $updateData['center_end_latlng'];
                $updateData['return_center_end_latlng'] = $updateData['center_start_latlng'];

                $lineS = $this->save('LineClass', $updateData);
                if ($lineS->ret) {
                    $count++;
                }
            }
        }

        if ($count > 0) {
            $ret->ret = true;
            $ret->data = $count;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 更新中心坐标（班线车）
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateLineFreeRideCenterLocation($mchid = null)
    {
        $ret = new \ResultModel(false);
        $count = 0;
        if (!empty($mchid)) {
            $where['mchid'] = $mchid;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $order = 'id desc';
        $linesR = $this->select($where, null, null, $order, 'LineFreeRide');
        if ($linesR->ret) {
            foreach ($linesR->data as $k => $v) {
                $updateData['id'] = $v['id'];
                if ($v['start_province_code']) {
                    //直辖市
                    if (in_array($v['start_province_code'], $this->directlyCityArr)) {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        if ($startAddressPPR->ret) {
                            if ($v['start_area_code']) {
                                $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                                if ($startAddressR->ret) {
                                    if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $startAddressPPR = $this->find(array('address_id' => $v['start_province_code']), 'GdRegion');
                        $startAddressPR = $this->find(array('address_id' => $v['start_city_code']), 'GdRegion');
                        if ($v['start_area_code']) {
                            $startAddressR = $this->find(array('address_id' => $v['start_area_code']), 'GdRegion');
                            if ($startAddressR->ret) {
                                if ($startAddressR->data['name'] == "县" || $startAddressR->data['name'] == "市辖区" || $startAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($startAddressPR->data['name'] == "县" || $startAddressPR->data['name'] == "市辖区" || $startAddressPR->data['name'] == "省直辖县级行政区划" || $startAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPPR->data['name'], $startAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($startAddressPR->data['name'], $startAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_start_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }


                if ($v['end_province_code']) {
                    //直辖市
                    if (in_array($v['end_province_code'], $this->directlyCityArr)) {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        if ($endAddressPPR->ret) {
                            if ($v['end_area_code']) {
                                $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                                if ($endAddressR->ret) {
                                    if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $startAddressR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    } else {
                        $endAddressPPR = $this->find(array('address_id' => $v['end_province_code']), 'GdRegion');
                        $endAddressPR = $this->find(array('address_id' => $v['end_city_code']), 'GdRegion');
                        if ($v['end_area_code']) {
                            $endAddressR = $this->find(array('address_id' => $v['end_area_code']), 'GdRegion');
                            if ($endAddressR->ret) {
                                if ($endAddressR->data['name'] == "县" || $endAddressR->data['name'] == "市辖区" || $endAddressR->data['name'] == "省直辖县级行政区划" || $endAddressR->data['name'] == "省直辖县级行政区划") {
                                    if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    } else {
                                        $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                        if ($gdLocationR->ret) {
                                            $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                        }
                                    }
                                } else {
                                    $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressR->data['name']);
                                    if ($gdLocationR->ret) {
                                        $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                    }
                                }
                            }
                        } else {
                            if ($endAddressPR->data['name'] == "县" || $endAddressPR->data['name'] == "市辖区" || $endAddressPR->data['name'] == "省直辖县级行政区划" || $endAddressPR->data['name'] == "省直辖县级行政区划") {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPPR->data['name'], $endAddressPPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            } else {
                                $gdLocationR = $this->getGdLocationByAddress($endAddressPR->data['name'], $endAddressPR->data['name']);
                                if ($gdLocationR->ret) {
                                    $updateData['center_end_latlng'] = $gdLocationR->data['location'];
                                }
                            }
                        }
                    }
                }

                $lineS = $this->save('LineFreeRide', $updateData);
                if ($lineS->ret) {
                    $count++;
                }
            }
        }

        if ($count > 0) {
            $ret->ret = true;
            $ret->data = $count;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 更新优惠券
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateCouponRecordStatus($mchid = null)
    {
        $totalCount = 0;
        $where = " is_del = ".CommonDefine::IS_DEL_0;
        if(!empty($mchid)) {
            $where = " AND mchid = ".$mchid;
        }
        $where .= " AND status = ".\CommonDefine::COUPON_STATUS_0;
        $where .= " AND end_time <= '". date('Y-m-d H:i:s', time())."'";
        $couponRecordsR = $this->select($where, null, null, 'coupon_record_id ASC', 'CouponRecord');
        if($couponRecordsR->ret) {
            foreach($couponRecordsR->data as $k => $couponRecord) {
                if(!empty($couponRecord)) {
                    $updateData['status'] = \CommonDefine::COUPON_STATUS_2;
                    $updateData['coupon_record_id'] = $couponRecord['coupon_record_id'];
                    $couponRecordS = $this->save('CouponRecord', $updateData);
                    if($couponRecordS->ret) {
                        $totalCount += 1;
                    }
                }
            }
        }
        return $this->output(new ResultModel(true, ['total_count' => $totalCount]), 1);
    }

    /**
     * 更新分台代约流水统计数据 （0点准时更新）
     * @param $mchid
     * @return null|resultModel
     */
    public function doUpdateBranchTurnoverStatistics($mchid = null)
    {
        $where = " is_del = ".CommonDefine::IS_DEL_0;
        if(!empty($mchid)) {
            $where = " AND parent_admin_id = ".$mchid;
        }

        $where .= ' AND group_id = 3';
        $totalCount = 0;
        $branchsR = $this->select($where, null, null, 'admin_id ASC', 'Admin');
        if($branchsR->ret) {
            foreach($branchsR->data as $k => $branch) {
                //确定今天是否有代约
                $turnoverConditionString = "temp_apply_branchid=".$branch['admin_id'];
                $turnoverConditionString .= " AND create_time>='".date('Y-m-d 00:00:00', strtotime("-1 day"))."' AND create_time<='".date('Y-m-d 23:59:59', strtotime("-1 day"))."' ";
                $orderR = $this->find($turnoverConditionString, 'Order');
                if(!$orderR->ret) {
                    continue;
                }
                $total_generation_turnover_amount = $this->getGenerationRegionTurnover(2, $branch['admin_id'], 1, null, null);
                $total_generation_turnover_on_amount = $this->getGenerationRegionTurnover(2, $branch['admin_id'], 2, null, null);
                $total_generation_turnover_down_amount = $this->getGenerationRegionTurnover(2, $branch['admin_id'], 3, null, null);
                $branchTurnoverData['turnover_amount'] = ($total_generation_turnover_amount->ret ? ($total_generation_turnover_amount->data['turnover'] ? $total_generation_turnover_amount->data['turnover'] : 0) : 0);
                $branchTurnoverData['turnover_on_amount'] = ($total_generation_turnover_on_amount->ret ? ($total_generation_turnover_on_amount->data['turnover'] ? $total_generation_turnover_on_amount->data['turnover'] : 0) : 0);
                $branchTurnoverData['turnover_down_amount'] = ($total_generation_turnover_down_amount->ret ? ($total_generation_turnover_down_amount->data['turnover'] ? $total_generation_turnover_down_amount->data['turnover'] : 0) : 0);
                $branchTurnoverR = $this->find('branchid =>'.$branch['admin_id'], 'BranchTurnoverStatistics');
                if(!$branchTurnoverR->ret) {
                    $branchTurnoverData['branchid'] = $branch['admin_id'];
                    $branchTurnoverData['mchid'] = $branch['parent_admin_id'];
                    $branchTurnoverA = $this->add('BranchTurnoverStatistics', $branchTurnoverData);
                    if(!$branchTurnoverA->ret) {
                        continue;
                    }
                } else {
                    $branchTurnoverData['id'] = $branchTurnoverR->data['id'];
                    $branchTurnoverS = $this->save('BranchTurnoverStatistics', $branchTurnoverData);
                    if(!$branchTurnoverS->ret) {
                        continue;
                    }
                }
                $totalCount++;
            }
        }
        return $this->output(new ResultModel(true, ['total_count' => $totalCount]), 1);
    }


    /**
     * 车辆快出发时提前发送提醒通知
     * @param $mchid
     * @return null|resultModel
     */
    public function doSendPassengerNotices()
    {
        $where = " notice_time < '" . date("Y-m-d H:i:s") . "'";
        $passengerNoticesR = $this->select($where, null, null, 'passenger_notice_queue_id ASC', 'PassengerNoticeQueue');
        $totalCount = 0;
        if($passengerNoticesR->ret) {
            foreach($passengerNoticesR->data as $k => $v) {
                if(!empty($v['notice_data'])) {
                    $noticeDataArr = json_decode(stripslashes($v['notice_data']), true);
                    if(!empty($noticeDataArr['sms'])) {
                        $this->sendMessage($noticeDataArr['sms']['order_id'], $noticeDataArr['sms']['to_user_type'], $noticeDataArr['sms']['message_type'], null, 'sms');
                    }
                    if(!empty($noticeDataArr['wx_message'])) {
                        $this->sendMessage($noticeDataArr['wx_message']['order_id'], $noticeDataArr['wx_message']['to_user_type'], $noticeDataArr['wx_message']['message_type'], null, 'wx_message');
                    }

                    $this->delPassengerNoticeQueue($v['type'], $v['relation_id']);
                    $totalCount++;
                }
            }
        }
        return $this->output(new ResultModel(true, ['total_count' => $totalCount]), 1);
    }


    /**
     * 定时更新地址信息库
     * callback 商户秘文
     * @return null|resultModel
     */
    public function doTimingUpdateAddressCode()
    {
        $where = "group_id = 2";
        if(!empty($this->mchid)) {
            $where .= " AND admin_id = ".$this->mchid;
        }
        $updateCount = 0;
        $mchsR = $this->select($where, null, null, null, 'Admin');
        if($mchsR->ret) {
            foreach ($mchsR->data as $mch) {
                if(empty($mch)) {
                    continue;
                }
                $mchid = $mch['admin_id'];
                $provinceR = $this->select(array('mchid' => $mchid, 'address_pid' => array('eq',0)), null, null, 'support_id asc', 'SupportCity', 'address_id, name');
                $r = new \ResultModel(false, "查询失败");
                $cityArr = array();
                if($provinceR->ret) {
                    foreach($provinceR->data as $province) {
                        $cityR = $this->select(array('mchid' => $mchid, 'address_pid' => array('eq',$province['address_id'])), null, null, 'support_id asc', 'SupportCity', 'address_id, name');
                        if($cityR->ret) {
                            if(in_array($province['address_id'], $this->directlyCityArr)) {
                                $temp = [];
                                foreach($cityR->data as $key => $value) {
                                    $temp[] = $value['address_id'];
                                }
                                $address_ids = implode(',', $temp);
                                $areaR = $this->select(array('mchid' => $mchid, 'address_pid' => array('in', $address_ids)), null, null, 'support_id asc', 'SupportCity', 'address_id, name');
                                if ($areaR->ret) {
                                    $cityArr[] = array(
                                        'address_id' => $province['address_id'],
                                        'name' => $province['name'],
                                        'sub_address' => $areaR->data
                                    );
                                }
                            } else {
                                foreach($cityR->data as $key => $city) {
                                    $areaR = $this->select(array('mchid' => $mchid, 'address_pid' => array('eq', $city['address_id'])), null, null, 'support_id asc', 'SupportCity', 'address_id, name');
                                    if ($areaR->ret) {
                                        $cityArr[] = array(
                                            'address_id' => $city['address_id'],
                                            'name' => $city['name'],
                                            'sub_address' => $areaR->data
                                        );
                                    }
                                }
                            }
                            /*                        foreach($cityR->data as $k=>$v){
                                                    if($v['name'] == "县" || $v['name'] == "市辖区" || $v['name'] == "省直辖县级行政区划"){
                                                        unset($cityR->data[$k]);
                                                    }
                                                }*/
                            //                        $cityArr[] = array_merge($cityArr,$cityR->data);
                        }
                    }
                }
                if(!empty($cityArr)) {
                    $updateAddressCodeData['address_code'] = addslashes(json_encode($cityArr));
                    $updateAddressCodeData['mchid'] = $mchid;
                    $mchAddressCodeWhere = "mchid = ".$mchid;
                    $mchAddressCodeR = $this->find($mchAddressCodeWhere, 'AddressCode');
                    if($mchAddressCodeR->ret) {
                        $updateAddressCodeData['id'] = $mchAddressCodeR->data['id'];
                        $mchAddressCodeS = $this->save('AddressCode', $updateAddressCodeData);
                        if($mchAddressCodeS->ret) {
                            $updateCount++;
                        }
                    } else {
                        $mchAddressCodeA = $this->add('AddressCode', $updateAddressCodeData);
                        if($mchAddressCodeA->ret) {
                            $updateCount++;
                        }
                    }
                    unset($updateAddressCodeData);
                }
            }
        }
        return $this->output(new ResultModel(true, ['update_count' => $updateCount]), 1);
    }


    public function doTimingUpdateOverOrder()
    {
        $where = " mchid IN (1231,1280) ";//限定朔州长运与古浪客运
        $where .= " AND state < ".\CommonDefine::ORDER_STATE_6;
        $where .= " AND pay_mode = ".\CommonDefine::ORDER_PAYMENT_MODE_1;
        $where .= " AND is_pay =".\CommonDefine::PAY_STATUS_1;
        //        $where .= " AND (pay_mode = ".\CommonDefine::ORDER_PAYMENT_MODE_0." OR (pay_mode = ".\CommonDefine::ORDER_PAYMENT_MODE_1." AND is_pay =".\CommonDefine::PAY_STATUS_1."))";
        $where .= " AND start_time < '".date("Y-m-d H:i:s", time())."'";
        $where .= " AND type = ".\CommonDefine::ORDER_TYPE_5;//完成订单暂时只支持班线车
        $orderRs = $this->select($where, null, null, null, 'Order');
        if(!$orderRs->ret) {
            return $this->output(new ResultModel(true, ['over_count' => 0]), 1);
        }

        $total_over_count = $orderRs->count;
        $success_over_count = 0;
        $fail_over_count = 0;
        foreach ($orderRs->data as $k => $order) {
            switch ($order['type']) {
                case \CommonDefine::ORDER_TYPE_1:
                case \CommonDefine::ORDER_TYPE_2:
                case \CommonDefine::ORDER_TYPE_3:
                case \CommonDefine::ORDER_TYPE_4:{
                }
                case \CommonDefine::ORDER_TYPE_5:{
                    $this->startTrans();
                    $updateOrderData['order_id'] = $order['order_id'];
                    if ($order['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {//先坐车后支付
                        //只记录线下流水
                        $bookDownAllR = $this->bookkeepingDownAllByOver($order['order_id']);
                        if(!$bookDownAllR->ret) {
                            $this->transRollback();
                            $fail_over_count++;
                            continue;
                        }
                    } else {
                        if($order['is_pay'] == 1 || $order['is_pre_pay'] == 1) {
                            //直接分账
                            $bookR = $this->bookkeepingMchOnBalanceByOver($order['order_id']);
                            if(!$bookR->ret) {
                                $this->transRollback();
                                $fail_over_count++;
                                continue;
                            }
                        } else {
                            $this->transRollback();
                            $fail_over_count++;
                            continue;
                        }
                    }

                    $updateOrderData['state'] = \CommonDefine::ORDER_STATE_6;
                    $orderS = $this->save('Order', $updateOrderData);
                    if(!$orderS->ret) {
                        $this->transRollback();
                        $fail_over_count++;
                        continue;
                    }

                    $this->commitTrans();
                    $success_over_count++;
                    //计入用户积分
                    $this->recordPassengerPoints($order['passenger_id'], $order['order_id']);

                    $this->doAddLog("定时任务自动完成订单[".$order['order_id']."]", "原数据：".json_encode($order).",更新数据：".json_encode($updateOrderData));
                }
                case \CommonDefine::ORDER_TYPE_6:{
                }
                case \CommonDefine::ORDER_TYPE_7:{
                }
                case \CommonDefine::ORDER_TYPE_11:{
                }
            }
        }

        return $this->output(new \ResultModel(true, ['total_over_count' => $total_over_count, 'success_over_count' => $success_over_count, 'fail_over_count' => $fail_over_count], 1));
    }

}

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/OrderAction');

/**
 * 会员管理模块
 *
 * <AUTHOR>
 */
class MemberAction extends HomeCoreAction
{

    /**
     * 会员登录
     * @param string $password 密码
     * @param int $user_group 用户组（1-乘客，2-车主）
     * @param string $user_name 用户名 （用户名和手机号至少有一个不能为空）
     * @param string $cellphone 手机号 （用户名和手机号至少有一个不能为空）
     */
    public function doLogin($password, $user_group, $user_name = null, $cellphone = null)
    {
        $user_group = intval($user_group);
        if ($user_group === 1) {
            $pao = $this->sudo('Passenger\\Account', null, StateModel::$HOME_USER);
            $r = $pao->doLogin($user_name, $cellphone, $password);
        } else if ($user_group === 2) {
            $dao = $this->sudo('Driver\\Account', null, StateModel::$HOME_USER);
            $r = $dao->doLogin($user_name, $cellphone, $password);
        } else {
            return $this->output(new ResultModel(false, '用户组类型格式不正确'));
        }
        return $this->output($r);
    }

    /**
     * 忘记密码
     * @param string $user_name 用户名
     * @param int $user_group 用户组（1-乘客，2-车主）
     * @param string $cellphone_validate_code 验证码
     * @param string $password 新密码
     * @param string $repassword 确认新密码
     * @param string $cellphone 手机号
     */
    public function doForgetPassword($user_name, $user_group, $cellphone_validate_code, $password, $repassword, $cellphone = null)
    {
        //验证手机验证码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code);
        if ($r->ret) {
            $user_group = intval($user_group);
            //验证用户组
            if ($user_group === 1) {
                //验证乘客
                $r = $this->find(array('user_name' => $user_name), 'Passenger', 'passenger_id');
                if ($r->ret) {
                    $r = $this->save('Passenger', array('passenger_id' => $r->data['passenger_id'], 'password' => md5($password), 'repassword' => md5($repassword)));
                } else {
                    return $this->output(new ResultModel(false, '乘客用户名不正确'));
                }
            } else if ($user_group === 2) {
                //验证车主
                $r = $this->find(array('user_name' => $user_name), 'Driver', 'driver_id');
                if ($r->ret) {
                    $r = $this->save('Driver', array('driver_id' => $r->data['driver_id'], 'password' => md5($password), 'repassword' => md5($repassword)));
                } else {
                    return $this->output(new ResultModel(false, '车主用户名不正确'));
                }
            } else {
                return $this->output(new ResultModel(false, '用户组类型格式不正确'));
            }
        }
        return $this->output($r);
    }

    /**
     * 修改密码
     * @param string $original_password 原始密码
     * @param string $password 新密码
     * @param string $repassword 重复新密码
     */
    public function doUpdatePassword($original_password, $password, $repassword)
    {
        if ($this->user_type === 1) {
            $pao = $this->sudo('Passenger\\Account', $this->state->user_id, StateModel::$PASSENGER);
            $r = $pao->doUpdatePassword($original_password, $password, $repassword);
        } else if ($this->user_type === 2) {
            $dao = $this->sudo('Driver\\Account', $this->state->user_id, StateModel::$DRIVER);
            $r = $dao->doUpdatePassword($original_password, $password, $repassword);
        } else {
            $r = new ResultModel(false, '用户组类型格式不正确');
        }
        return $this->output($r);
    }

    /**
     * 退出登录
     */
    public function doLogout()
    {
        StateModel::clear(StateModel::$HOME_USER);
        return $this->output(new ResultModel(true));
    }

    /**
     * 修改乘客信息
     * @param string $cellphone 手机号
     * @param string $name 姓名
     * @param int $gender 性别（1-男，2-女）
     * @param string $email 邮箱
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdatePassengerInfo($cellphone, $name, $gender, $email, $drive_mode, $start_address_code, $end_address_code, $address_code, $start_address_remark = null, $end_address_remark = null)
    {
        if ($this->user_type === 1) {
            $this->data['passenger_id'] = $this->state->user_id;
            $this->data['email_f'] = $email;
            if (intval($drive_mode) === 1) {
                $r = $this->checkingAddressCode($start_address_code);
                if (!$r->ret) {
                    return $this->output(new ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
                }
                $r = $this->checkingAddressCode($end_address_code);
                if (!$r->ret) {
                    return $this->output(new ResultModel(false, '目的地格式不正确或地址不存在'));
                }
                if (intval($start_address_code) === intval($end_address_code)) {
                    return $this->output(new ResultModel(false, '出发地和目的地不能相同'));
                }
            } else {
                $r = $this->checkingAddressCode($address_code);
                if (!$r->ret) {
                    return $this->output(new ResultModel(false, '乘车地格式不正确或地址不存在'));
                }
            }
            $r = $this->save('Passenger');
        } else {
            return $this->output(new ResultModel(false, '只有乘客身份可以使用该功能'));
        }
        return $this->output($r);
    }

    /**
     * 获取乘车记录
     * @param string $fields 查询的字段列表
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getRidingRecords($fields = null, $page = 1, $size = 10)
    {
        if ($this->user_type === 1) {
            $o = $this->sudo('Passenger\\Order', $this->state->user_id, StateModel::$PASSENGER);
            $r = $o->getOrders(2, $page, $size, $fields);
        } else {
            return $this->output(new ResultModel(false, '只有乘客身份可以使用该功能'));
        }
        return $this->output($r);
    }

    /**
     * 获取载客记录
     * @param string $fields 查询的字段列表
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getCarryRecords($fields = null, $page = 1, $size = 10)
    {
        if ($this->user_type === 2) {
            $o = $this->sudo('Driver\\Order', $this->state->user_id, StateModel::$DRIVER);
            $r = $o->getOrders(2, null, $page, $size, $fields);
        } else {
            return $this->output(new ResultModel(false, '只有车主身份可以使用该功能'));
        }
        return $this->output($r);
    }

    /**
     * 发送手机验证码
     * @param string $cellphone 手机号码
     */
    public function doSendCellphoneValidateCode($cellphone, $mchid = null)
    {
        //验证验证码是否过期
        $r = $this->find(array('cellphone' => $cellphone, 'mchid' => $mchid), 'CellphoneValidate', 'update_time,id');
        if ($r->ret) {
            if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') >= strtotime(get_current_time())) {
                return $this->output(new ResultModel(false, '上次的验证码还未过期'));
            }
        }
        //生成验证码
        $validate_code = build_cellphone_validate_code();
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            if (C('SMS_ON')) {
                /**
                 * 向手机发送验证码
                 */
                $sms = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $b = $sms->sendTemplateSMS($cellphone, array($validate_code, "5分钟"), \SMSUtil::TEMP_ID_REGISTER, $mchid);
                if (!$b) {
                    return $this->output(new ResultModel(false, '验证码发送失败'));
                } else {
                    if ($r->ret) {
                        //更新验证码
                        $r = $this->save('CellphoneValidate', array('id' => $r->data['id'], 'validate_code' => $validate_code, 'mchid' => $mchid));
                        if (!$r->ret) {
                            return $this->output($r);
                        }
                    } else {
                        //添加验证码
                        $r = $this->add('CellphoneValidate', array('cellphone' => $cellphone, 'mchid' => $mchid, 'cellphone_f' => $cellphone, 'validate_code' => $validate_code));
                        if (!$r->ret) {
                            return $this->output($r);
                        }
                    }
                }
            } else {
                return $this->output(new ResultModel(false, '服务端未开启发送短信服务'));
            }
        }
        return $this->output(new ResultModel(true, $validate_code));
    }

    /**
     * 发送订单短信
     * @param string $cellphone 手机号码
     * @param string $tempId 模板id；TEMP_ID_ORDER_ACT-接受订单；TEMP_ID_ORDER_REF-拒绝订单; TEMP_ID_ORDER_NEW-新订单
     * @param string $mchid 商户id
     */
    public function doSendCellphoneOrderInfo($cellphone, $data, $tempId)
    {
        if (!$tempId) {
            return false;
        }

        $sms = new YTSMS();
        $b = $sms->sendTemplateSMS($cellphone, array($data), $tempId);
        if (!$b) {
            return false;
        } else {
            //添加验证码
            $r = $this->add('CellphoneSmsOrder', array('cellphone' => $cellphone, 'cellphone_f' => $cellphone, 'validate_code' => $data));
            if (!$r->ret) {
                return false;
            }
        }

        return true;
    }

}

?>

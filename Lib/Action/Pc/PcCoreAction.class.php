<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Home/HomeCoreAction');

/**
 * 后台核心模块
 *
 * <AUTHOR>
 */
class PcCoreAction extends HomeCoreAction
{

    /**
     * 管理组
     * @var string
     */

    //put your code here
    public function __construct()
    {
        add_tag_behavior('auth_check', 'PcAuthCheck');
        parent::__construct();
    }

    /**
     *检验手机号是否正确
     * @param $tel 手机号
     *返回一个布尔值
     */
    protected function isMobile($tel){
        return preg_match("/^1[34578]{1}\d{9}$/",$tel);
    }



}

?>

<?php

/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/6/29
 * Time: 16:45
 */
class PcManagerAction extends PcCoreAction
{
    /**
     *私人定制
     * @param $name 名称
     * @param $tel 电话
     * @param $company 公司
     * @param $scenario 场景
     * @return 返回字符串
     */
    public function doCustom($name = null ,$tel = null ,$company = null ,$scenario = null ){
        if(empty($name)){
            return $this->output(new \ResultModel(false, '联系人名称不能为空',null));
        }

        if(mb_strlen($name,"utf-8") > 20){
            return $this->output(new \ResultModel(false, '联系人名称不能超过20个字符',null));
        }

        if(empty($tel)){
            return $this->output(new \ResultModel(false, '联系电话不能为空'));
        }
        if(!$this->isMobile($tel)){
            return $this->output(new \ResultModel(false, '手机号格式不正确',null));
        }

        if(empty($company)){
            return $this->output(new \ResultModel(false, '公司不能为空'));
        }

        if(mb_strlen($company,"utf-8") > 50){
            return $this->output(new \ResultModel(false, '公司字符长度不能超过50个字符',null));
        }

        if(empty($scenario)){
            return $this->output(new \ResultModel(false, '场景不能为空'));
        }

        if(mb_strlen($scenario,"utf-8") > 200){
            return $this->output(new \ResultModel(false, '场景字符长度不能超过200个字符',null));
        }
        $data['name'] = $name;
        $data['tel'] = $tel;
        $data['company'] = $company;
        $data['scenario'] = $scenario;
        $data['is_del'] = 0;
        $r = $this->find($data, 'Custom');
        if (!$r->ret) {
            $this->startTrans();
            try{

                $dateTime = date();
                $data['create_time'] = $dateTime;
                $data['update_time'] = $dateTime;

                $r = $this->add('Custom',$data);
                if($r->ret){
                    $this->commitTrans();return $this->output(new \ResultModel(true, '定制成功！'));
                }else{
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '现在定制人过多，请稍后再试...！'));
                }
            }catch (Exception $e){
                $this->transRollback();
                return $this->output(new \ResultModel(false, '服务器繁忙,请稍后再试...！'));
            }
        }else{
            return $this->output(new \ResultModel(false, '该定制已存在！'));
        }
    }

    /**
     *新闻列表
     * @return 返回数组数据
     */
    public function doNewsList($page = 1 , $size = 10){
        $where = null;
        $where['is_del'] = 0;
        $r = $this->select($where,$page,$size,"create_time desc" , "News" , "id ,url ,memo");

        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据",null));
        }

        $this->output($r);
    }

    /**
     *获取新闻内容
     * @param $id 新闻id
     * @return 返回字符串
     */
    public function doNewsInfo($id = null){
        $where = null;
        if(!empty($id)){
            $where['id'] = $id;
        }
        $r = $this->find($where, "News" ,'id,title,url,content,create_time,update_time');
        if(!$r->ret){
            return $this->output(new ResultModel(false,"该数据异常",null));
        }
        $this->output($r);
    }
}
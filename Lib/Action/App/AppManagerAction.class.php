<?php

/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/6/29
 * Time: 16:45
 */
class AppManagerAction extends AppCoreAction
{
    /**
     *登录
     * @param $account 账户
     * @param $password 密码
     * @return 返回子商户的账户信息
     */
    public function doLogin($account = null ,$password = null){
//        var_dump($account);die;
        if(empty($account) && empty($password)){
            return $this->output(new \ResultModel(false, '账号或者密码不能为空'));
        }

        $r = $this->find(array('account' => $account ,'is_del'=>0), 'SubBranch');

        if ($r->ret) {
            if($r->data['password'] != md5($r->data['salt'].$password)){
                return $this->output(new \ResultModel(false, '密码错误'));
            }else{
                //判断是否被删除
                $adminR = $this->find(array('admin_id'=>$r->data['mchid'],'group_id'=>2,'is_del'=>0),'Admin','mchname,cellphone');
                if(!$adminR->ret){
                    return $this->output(new \ResultModel(false, '该商户已被注销！'));
                }else{
                    unset($r->data['password'],$r->data['salt'],$r->data['is_del']);
                    $r->data['mchname'] = $adminR->data['mchname'];
                    $r->data['mchphone'] = $adminR->data['cellphone'];
                    session("user",json_encode($r->data));
                }
            }
        }else{
            return $this->output(new \ResultModel(false, '账号不存在！'));
        }
        return $this->output($r);
    }
    //退出
    public function doLogout(){
        $user = json_decode(session("user"),true);
        if(!empty($user)){
            session_destroy(); //释放当前用户的session资源
        }
        return $this->output(new ResultModel(true));
    }
    /**
     * 获取商户下的所有未删除的分台
     * @param $mchid 商户id
     *  @return 商户下的所有分台
    **/
    public function doChoseBranch($mchid = null , $page = 1 ,$size = 10){
        try{
            $adminR = $this->find(array('admin_id'=>$mchid,'is_del'=>0) ,'Admin','admin_id');
            if(!$adminR->ret){
                $this->output(new ResultModel(false,'该商户不存在'));
            }

            $where['parent_admin_id'] = $adminR->data['admin_id'];
            $where['group_id'] = 3;
            $where['is_del'] = 0;
            $where['is_freeze'] = 0;

            $r = $this->select($where,$page,$size,'create_time desc','Admin','admin_id mchid, mchname, cellphone');
        }catch (Exception $e){
            $this->output(new ResultModel(false,'暂无数据'));
        }
//        $this->output($r);
        echo json_encode($r);die;
    }

//    public function doDemo(){
//        var_dump(session("user"));
//    }

}
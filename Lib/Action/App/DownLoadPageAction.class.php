<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 下载模块（核心模块的方法不保证事务安全）
 *
 * <AUTHOR>
 */
class DownLoadPageAction extends AppCoreAction
{
    /**
     * 司机端安卓App
     */
    public function downloadDriverAndroidPage(){
        $appDownloadR = $this->find(array('mchid' => $this->mchid), 'AppDownload');
        if(!$appDownloadR->ret){
           echo "商户异常";die;
        }
        $this->assign('driver_android_download_url',$appDownloadR->data['driver_android']);
        $this->display('Tpl/App/Download/driver_android_down.html');
    }
}
?>

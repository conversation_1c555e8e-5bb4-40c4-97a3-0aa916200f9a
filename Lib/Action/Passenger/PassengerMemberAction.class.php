<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 常用联系人模块
 *
 * <AUTHOR>
 */
class PassengerMemberAction extends \PassengerCoreAction
{
    /**
     * 获取乘客常用联系人
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为15）
     * @return null|\resultModel
     */
    public function getPassengerMembers($page = 1, $size = 15)
    {
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/members", 'get', array(
            'page' => $page,
            'per_page' => $size
        ));
        $result = json_decode($response, true);
        return $this->output(new \ResultModel(true, $result['data']['data']));
    }

    /**
     * 添加乘客常用联系人
     * @param string $real_name 真实姓名（必填）
     * @param string $ID_number 证件号码（必填）
     * @param string $cellphone 手机号（非必填）
     * @param int $passenger_type 乘客类型（非必填）：0-成人票，1-儿童票，2-学生票
     * @param int $id_type 证件类型（非必填）：0-身份证，1-护照，2-港澳通行证，3-台胞证
     * @param array $id_context 证件内容（非必填）：
     *                         - surname: 姓氏
     *                         - given_name: 名字
     *                         - valid_until: 有效期至，格式：YYYY-MM-DD
     * @param string $remark 备注（非必填）
     * @param string $email 邮箱（非必填）
     * @return null|\resultModel 成功返回乘客信息，失败返回错误信息
     *                          - passenger_member_id: 乘客ID
     *                          - is_verified: 是否已验证
     *                          - cn_passenger_type: 乘客类型中文
     *                          - cn_id_type: 证件类型中文
     */
    public function doAddPassengerMember($real_name, $ID_number, $cellphone = null, $passenger_type = null, $id_type = null, $id_context = null, $remark = null, $email = null)
    {
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/members", 'post', array(
            'real_name' => $real_name,
            'ID_number' => $ID_number,
            'cellphone' => $cellphone,
            'passenger_type' => $passenger_type,
            'id_type' => $id_type,
            'id_context' => $id_context,
            'remark' => $remark,
            'email' => $email,
        ));

        $result = json_decode($response, true);
        if ($result['status'] != 'success') {
            return $this->output(new \ResultModel(false, $result['message']));
        }

        return $this->output(new \ResultModel(true, $result['data']));
    }

    /**
     * 修改乘客常用联系人
     * @param int $passenger_member_id 常用联系人id
     * @param string $real_name 真实姓名（必填）
     * @param string $ID_number 证件号码（必填）
     * @param string $cellphone 手机号（非必填）
     * @param int $passenger_type 乘客类型（非必填）：0-成人票，1-儿童票，2-学生票
     * @param int $id_type 证件类型（非必填）：0-身份证，1-护照，2-港澳通行证，3-台胞证
     * @param array $id_context 证件内容（非必填）：
     *                         - surname: 姓氏
     *                         - given_name: 名字
     *                         - valid_until: 有效期至，格式：YYYY-MM-DD
     * @param string $remark 备注（非必填）
     * @param string $email 邮箱（非必填）
     * @return null|\resultModel 成功返回乘客信息，失败返回错误信息
     *                          - passenger_member_id: 乘客ID
     *                          - is_verified: 是否已验证
     *                          - cn_passenger_type: 乘客类型中文
     *                          - cn_id_type: 证件类型中文
     */
    public function doEditPassengerMember($passenger_member_id, $real_name, $ID_number, $cellphone = null, $passenger_type = null, $id_type = null, $id_context = null, $remark = null, $email = null)
    {
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/members/{$passenger_member_id}", 'put', array(
            'real_name' => $real_name,
            'ID_number' => $ID_number,
            'cellphone' => $cellphone,
            'passenger_type' => $passenger_type,
            'id_type' => $id_type,
            'id_context' => $id_context,
            'remark' => $remark,
            'email' => $email,
        ));

        $result = json_decode($response, true);
        if ($result['status'] != 'success') {
            return $this->output(new \ResultModel(false, $result['message']));
        }

        return $this->output(new \ResultModel(true, $result['data']));

    }

    /**
     * 删除乘客常用联系人
     * @param int $passenger_member_id 常用联系人ID
     * @return null|\ResultModel 成功返回true，失败返回错误信息
     */
    public function doDelPassengerMember($passenger_member_id)
    {
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/members/{$passenger_member_id}", 'delete', array());

        $result = json_decode($response, true);
        if ($result['status'] != 'success') {
            return $this->output(new \ResultModel(false, $result['message']));
        }

        return $this->output(new \ResultModel(true, $result['data']));
    }
}

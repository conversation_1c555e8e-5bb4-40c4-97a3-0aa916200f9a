<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-上传模块
 *
 * <AUTHOR>
 */
class YyUploadAction extends \PassengerCoreAction
{
    /**
     * 上传接口
     * @param integer $type 上传类型(0-头像；1-户口簿；2-学生证；3-身份证)
     * @param file $img 乘客身份证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     */
    public function doUpload($type = 0){
        if (!empty($_FILES['img']) && $_FILES['img']['name']) {
            switch($type){
                case \CommonDefine::IMG_TYPE_0:{
                    return $this->output($this->doUploadImg2($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_0));
                }
                case \CommonDefine::IMG_TYPE_1:{
                    return $this->output($this->doUploadImg2($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_1));
                }
                case \CommonDefine::IMG_TYPE_2:{
                    return $this->output($this->doUploadImg2($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_2));
                }
                case \CommonDefine::IMG_TYPE_3:{
                    return $this->output($this->doUploadImg2($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_3));
                }
                default:
                    break;
            }
        }

        return $this->output(new \ResultModel(false, '上传失败'));
    }
}

?>

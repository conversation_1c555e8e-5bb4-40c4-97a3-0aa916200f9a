<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-卡列表
 *
 * <AUTHOR>
 */
class YyCardAction extends \PassengerCoreAction
{
    /**
     * 获取学生号次卡套餐列表
     * @param integer $everyday_times 每天的乘车次数
     * @param integer $page 页码（默认为1）
     * @param integer $size 分页大小（默认为10）
     */
    public function getYyCards($everyday_times, $page = 1, $size = 10){
        $where = " mchid = ".$this->mchid;
        $where .= " AND is_del = ".\CommonDefine::IS_DEL_0;
        $where .= " AND is_shelf = ".\CommonDefine::IS_SHELF_1;
        $where .= " AND type = ".\CommonDefine::CARD_TYPE_2;
        $where .= " AND everyday_times = ".$everyday_times;
        $fields = "card_id,name, price, everyday_times, times, reseverd_info";
        $cardsList = $this->select($where, $page, $size, 'card_id asc', 'Card' , $fields);
        return $this->output($cardsList);
    }

}

?>

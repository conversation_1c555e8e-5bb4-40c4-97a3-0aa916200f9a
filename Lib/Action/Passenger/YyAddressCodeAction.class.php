<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-获取地址
 *
 * <AUTHOR>
 */
class YyAddressCodeAction extends \PassengerCoreAction
{
    /**
     * 获取附近的上车点/下车点,支持名称搜索
     * @param string $address_name 上车点名称（默认为空）
     * @param string $start_latitude 上车点纬度（默认为空）
     * @param string $start_longitude 上车点经度（默认为空）
     * @param string $end_latitude 下车点纬度（默认为空）
     * @param string $end_longitude 下车点（默认为空）
     */
    public function getAroundPoint($address_name = null, $start_latitude = null, $start_longitude = null, $end_latitude = null, $end_longitude = null){
        if(!empty($start_latitude) && !empty($start_longitude)){
            return $this->output($this->_getPoints($address_name, $start_latitude, $start_longitude, 1));
        }else if(!empty($end_latitude) && !empty($end_longitude)){
            return $this->output($this->_getPoints($address_name, $end_latitude, $end_longitude, 2));
        }else{
            return $this->output($this->_getPoints($address_name, $end_latitude, $end_longitude));
        }
    }

    //获取上下车
    private function _getPoints($address_name = null, $latitude = null, $longitude = null, $type = null){
        $pointsR = new \ResultModel(false);
//        if(empty($address_name) && empty($latitude) && empty($longitude)){
//            return $pointsR;
//        }
        $yyLineClassPointMode = new \YyLineClassPointModel();
        $yyLineClassMode = new \YyLineClassModel();
        $where['yylc.is_del'] = \CommonDefine::IS_DEL_0;
        $where['yylc.status'] = \CommonDefine::LINE_CLASS_OPERATE_1;
        $where['yylc.mchid'] = $this->mchid;
        if(!empty($address_name)){
            $where['yylcp.alias'] = [ 'like', '%'.$address_name.'%'];
        }
        if(!empty($type)){
            $where['yylcp.type'] = 1;
        }
        $fields = "yylcp.alias,yylcp.longitude,yylcp.latitude";
        $order = 'yylc.create_time desc';
        $group = "yylcp.alias";

        if(!empty($latitude) && !empty($longitude)){
            $fields.= (",GETDISTANCE(latitude,longitude,$latitude, $longitude) AS distance");
            $order = ('distance asc,'.$order);
            $pointsList = $yyLineClassPointMode->alias("yylcp")
                ->join("LEFT JOIN ".$yyLineClassMode->getTableName()." yylc ON yylc.id = yylcp.line_class_id")
                ->where($where)
                ->field($fields)
                ->group($group)
                ->having("distance != ''")
                ->order($order)
                ->select();

            $pointsCount = $yyLineClassPointMode->alias("yylcp")
                ->join("LEFT JOIN ".$yyLineClassMode->getTableName()." yylc ON yylc.id = yylcp.line_class_id")
                ->group($group)
                ->where($where)
                ->count();
            if($pointsCount > 0){
                $pointsR->ret = true;
                $pointsR->data = $pointsList;
                $pointsR->count = $pointsCount;
            }
        }else{
            $pointsList = $yyLineClassPointMode->alias("yylcp")
                ->join("LEFT JOIN ".$yyLineClassMode->getTableName()." yylc ON yylc.id = yylcp.line_class_id")
                ->where($where)
                ->field($fields)
                ->group($group)
                ->order($order)
                ->select();

            $pointsCount = $yyLineClassPointMode->alias("yylcp")
                ->join("LEFT JOIN ".$yyLineClassMode->getTableName()." yylc ON yylc.id = yylcp.line_class_id")
                ->group($group)
                ->where($where)
                ->count();
            if($pointsCount > 0){
                $pointsR->ret = true;
                $pointsR->data = $pointsList;
                $pointsR->count = $pointsCount;
            }
        }

        return $pointsR;
    }
}

?>

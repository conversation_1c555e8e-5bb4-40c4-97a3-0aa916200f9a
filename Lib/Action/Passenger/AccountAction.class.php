<?php

#
# CORS config for php
# Code by anrip[<EMAIL>]
#

namespace Passenger;

import('@/Action/Weixin/Wechat');
import('@/Action/Home/MemberAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 账户模块
 *
 * <AUTHOR>
 */
class AccountAction extends \PassengerCoreAction
{
    /**
     * 乘客注册
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     */
    public function doRegister($user_name, $cellphone, $mchid, $cellphone_validate_code, $password)
    {
        $mchid = $this->checkauth($mchid)->data;
        $bool = self::checkmchrights($mchid);
        if (!$bool) {
            return $this->output(new \ResultModel(false, '商户账号存在异常'));
        }
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['mchid'] = $mchid;

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $mchid);
        if ($r->ret) {
            $this->startTrans();
            //创建乘客用户
            $r = $this->add('Passenger');
            if ($r->ret) {
                $passenger_id = $r->data;
                $r = $this->select(null, null, null, null, 'MetaPassengerRemindAction', 'action_id');
                if ($r->ret) {
                    foreach ($r->data as $value) {
                        //添加提醒设置
                        $r = $this->add('PassengerRemindSet', array('action_id' => $value['action_id'], 'passenger_id' => $passenger_id));
                        if (!$r->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客提醒设置初始化失败'));
                        }
                    }
                    if ($r->ret) {
                        $this->commitTrans();
                        \StateModel::save($passenger_id, $password, \StateModel::$PASSENGER);
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '乘客提醒设置元数据不存在'));
                }
            } else {
                $this->transRollback();
            }
        }
        return $this->output($r);
    }

    /**
     * 乘客注册(手机端)
     * @param string $name 用户名
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     * @param string $repassword 重复密码
     * @param stringt $mchids 商户id
     */
    public function doMobileRegister($name, $cellphone, $mchid, $cellphone_validate_code, $password)
    {
        $mchid = $this->checkauth($mchid)->data;
        $data = self::checkmchrights($mchid);
        if (!$data) {
            return $this->output(new \ResultModel(false, '注册失败'));
        }
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $name;
        $this->data['password'] = md5($password);
        $this->data['mchid'] = $mchid;
        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $mchid);
        $r->ret = true;
        if ($r->ret) {
            $this->startTrans();
            //创建乘客用户
            $data = $this->select(array('cellphone' => $cellphone, 'mchid' => $mchid), null, null, null, 'Passenger', null)->count;
            if ($data) {
                return $this->output(new \ResultModel(false, '该用户已存在'));
            }
            $r = $this->add('Passenger');
            if ($r->ret) {
                $passenger_id = $r->data;
                $r = $this->select(null, null, null, null, 'MetaPassengerRemindAction', 'action_id');
                if ($r->ret) {
                    foreach ($r->data as $value) {
                        //添加提醒设置
                        $r = $this->add('PassengerRemindSet', array('action_id' => $value['action_id'], 'passenger_id' => $passenger_id));
                        if (!$r->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客提醒设置初始化失败'));
                        }
                    }
                    if ($r->ret) {
                        $this->commitTrans();
                        \StateModel::save($passenger_id, md5($password), \StateModel::$PASSENGER);
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '乘客提醒设置元数据不存在'));
                }
            } else {
                $this->transRollback();
            }
        }
        return $this->output($r);
    }


    /**
     * 发送手机验证码
     * @param string $cellphone 手机号码
     */
    public function doSendCellphoneValidateCode($cellphone)
    {
        $o = $this->sudo('Member', null);
        $r = $o->doSendCellphoneValidateCode($cellphone);
        return $this->output($r);
    }

    public function checkmchrights($mchid)
    {
        $data = M('admin')->where('admin_id=' . $mchid)->find();

        if (!$data || $data['status'] != 0) {
            return false;
        } else {
            return $data;
        }
    }


    /**
     * 发送手机验证码(手机端)
     * @param string $cellphone 手机号码
     */
    public function doMobileSendCellphoneValidateCode($cellphone, $mchid = null)
    {
        if (!$this->mchid) {
            return $this->output(new \ResultModel(false, "获取验证码失败"));
        }
        $o = $this->sudo('Member', null);
        $r = $o->doSendCellphoneValidateCode($cellphone, $this->mchid);
        return $this->output($r);
    }

    /**
     * 登录
     * @param string $user_name 用户名（可选）
     * @param string $cellphone 手机号（可选）
     * @param string $password 密码
     */
    public function doLogin($user_name, $cellphone, $password)
    {
        if (!empty($user_name)) {
            $r = $this->find(array('user_name' => $user_name, 'password' => md5($password)), 'Passenger', 'passenger_id,is_freeze');
        } else {
            $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password)), 'Passenger', 'passenger_id,is_freeze');
        }
        if ($r->ret) {
            //判断是否被删除
            if (intval($r->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
            \StateModel::save($r->data['passenger_id'], $password, \StateModel::$PASSENGER);
        }

        return $this->output($r);
    }

    /**
     * 登录(手机版)
     * @param string $cellphone 手机号（可选）
     * @param string $password 密码
     */
    public function doMobileLogin($cellphone, $password, $mchid)
    {
        $oldmchid = $mchid;
        if (!$mchid) {
            return $this->output('地址参数错误');
        }
        $mchid = $this->checkauth($mchid)->data;
        $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password), 'mchid' => $mchid), 'Passenger', 'name,user_name,cellphone,passenger_id,is_freeze');
        if (empty($r->data)) {
            return $this->output(new \ResultModel(false, '账号不存在或者密码错误'));
        }

        if ($r->ret) {
            //判断是否被删除
            if (intval($r->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
            $we = new \Wechat();
            $r->data['wx_isverify'] = 0;
            $state = $r->data['passenger_id'];
            $url = C('WEB_ROOT') . 'passlogcallback/callback/' . $oldmchid;
            $redirect_uri = urlencode($url);
            $r->data['wx_url'] = $we->getAuthorizeUrl($redirect_uri, $state, $oldmchid);
            \StateModel::save($r->data['passenger_id'], md5($password), \StateModel::$PASSENGER);
        }
        return $this->output($r);
    }

    /**
     * 微信登陆
     * @param int $cipmchid 商户密文id
     */
    public function doWechatLogin($cipmchid = null)
    {
        if (!$cipmchid) {
            $cipmchid = cookie('cipmchid');
        }

        if (isset($this->state->user_id)) {
            //解密商户id
            $mchid = $this->getMchidByCipmchid($cipmchid);
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $mchid), 'Passenger', 'passenger_id, cellphone');
            if ($passengerR->ret) {
                //获取用户信息
                $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id'],), 'ThirdParty', 'third_avatar,third_account');
                $userInfoArr = $thirdR->data;
                \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']));
                redirect(C('WX_PASSENGER_MYSET') . '/callback/' . $_GET['callback']);
            }
        }

        $r = new \ResultModel(true);
        $we = new \Wechat();
        $info = $we->getapsc($cipmchid);
        $url = C('WEB_ROOT') . 'passlogcallback/callback/' . $cipmchid;
        $redirect_uri = urlencode($url);
        //$r->data['wx_url']=$redirect_uri;
        $r->data['wx_url'] = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $info['appid'] . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_userinfo&state=" . $cipmchid . "#wechat_redirect";
        header("Location:" . $r->data['wx_url']);
        return;
        //        redirect("https://open.weixin.qq.com/connect/oauth2/authorize?appid=".$info['appid']."&redirect_uri=".$redirect_uri."&response_type=code&scope=snsapi_userinfo&state=".$cipmchid ."#wechat_redirect");
        return $this->output($r);
    }


    /**
     * 微信端回调处理,保存用户openid及相关信息
     */
    public function doGetWechatCode()
    {
        $code = $_GET['code'];
        $cipmchid = $_GET['state'];
        $wechat = new \Wechat();
        $url = $wechat->getCodeAccessTokenUrl($code, $cipmchid);
        //解密商户id
        $mchid = $this->getMchidByCipmchid($cipmchid);

        $data = file_get_contents($url);
        $arr = json_decode($data, true);
        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_pas_token_' . $mchid, $arr['access_token'], $expires_in);
        if (isset($arr['openid']) && $arr['openid']) {
            $r = $this->find(array('openid' => $arr['openid'], $mchid), 'Passenger', 'passenger_id,is_freeze,cellphone');
            if ($r->ret) {
                if (intval($r->data['is_freeze']) === 1) {
                    //                return $this->output(new \ResultModel(false, '账号被删除'));
                    redirect(C('WX_PASSENGER_URL') . '/callback/' . $cipmchid);
                }
                //获取用户信息
                $thirdR = $this->find(array('passenger_id' => $r->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
                $userInfoArr = $thirdR->data;
                if (!$thirdR->ret) {
                    //获取用户信息
                    $userInfoUrl = $wechat->getUserInfoUrl($arr['access_token'], $arr['openid']);
                    $userinfoData = file_get_contents($userInfoUrl);
                    $userInfoArr = json_decode($userinfoData, true);
                    $this->add('ThirdParty', array('passenger_id' => $r->data['passenger_id'], 'uuid' => $userInfoArr['openid'], 'third_avatar' => $userInfoArr['headimgurl'], 'third_account' => $userInfoArr['nickname'], 'type' => 2));
                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $r->data['cellphone'], 'avatar' => $userInfoArr['headimgurl'], 'nickname' => $userInfoArr['nickname']));
                } else {
                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $r->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']));
                }
            } else {
                //获取用户信息
                $userInfoUrl = $wechat->getUserInfoUrl($arr['access_token'], $arr['openid']);
                $userinfoData = file_get_contents($userInfoUrl);
                $userInfoArr = json_decode($userinfoData, true);
                if (isset($userInfoArr['openid']) && $userInfoArr['openid']) {
                    $passengerR = $this->find(array('openid' => $userInfoArr['openid']), 'Passenger', array('openid' => $arr['openid'], 'mchid' => $mchid), 'passenger_id, cellphone');
                    $this->startTrans();
                    $passengerId = "";
                    if (!$passengerR->ret) {
                        //创建新用户
                        $passengerR = $this->add('Passenger', array('openid' => $arr['openid'], 'mchid' => $mchid));
                        $passengerId = $passengerR->data;
                    } else {
                        $passengerId = $passengerR->data['passenger_id'];
                    }
                    //添加三方账号信息
                    if ($passengerR->ret) {
                        $thirdR = $this->add('ThirdParty', array('passenger_id' => $passengerId, 'uuid' => $userInfoArr['openid'], 'third_avatar' => $userInfoArr['headimgurl'], 'third_account' => $userInfoArr['nickname'], 'type' => 2));
                        if ($thirdR->ret) {
                            \StateModel::save($passengerId, "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['headimgurl'], 'nickname' => $userInfoArr['nickname']));
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            }
        } else {
            //授权异常处理
            echo "授权异常";
            die;
        }

        $this->display('Tpl/Passenger/Index/index.html');
        return;
        //        redirect(C('WX_PASSENGER_MYSET') . '/callback/' . $_GET['callback']);
        $mySeturl = C('WX_PASSENGER_URL') . '/callback/' . $cipmchid;
        echo "<script type='text/javascript'>location.href='" . $mySeturl . "';</script>";
        die;
    }

    /**
     * 乘客退出登录(手机端)
     */
    public function doWebLogout()
    {
        \StateModel::clear(\StateModel::$PASSENGER);
        return $this->output(new \ResultModel(true));
    }


    /**
     * 乘客绑定(手机端)
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     */
    public function doBindingCellphone($cellphone, $cellphone_validate_code)
    {
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        $passengerTempR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Passenger');
        if ($passengerTempR->ret) {
            if (empty($passengerTempR->data['openid'])) { //不是同一个账号
                if ($passengerTempR->data['passenger_id'] != $passenger_id) {
                    $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
                    if ($r->ret) {
                        $this->startTrans();
                        $updatePassengerData['passenger_id'] = $passengerTempR->data['passenger_id'];
                        $updatePassengerData['openid'] = $passengerR->data['openid'];
                        $updatePassengerData['openid_pay'] = $passengerR->data['openid_pay'];
                        $passengerS = $this->save('Passenger', $updatePassengerData);
                        if ($passengerS->ret) {
                            //删除新创建的账号
                            $thirdTempR = $this->find(array('passenger_id' => $passengerTempR->data['passenger_id']), 'ThirdParty');
                            $thirdR = $this->find(array('passenger_id' => $passenger_id), 'ThirdParty');
                            if ($thirdTempR->ret) {
                                if ($thirdTempR->data['uuid'] != $passengerR->data['openid']) {
                                    $updateThirdData['uuid'] = $passengerR->data['openid'];
                                    $updateThirdData['id'] = $thirdTempR->data['id'];
                                    $thirdS = $this->save('ThirdParty', $updateThirdData);
                                    if (!$thirdS->ret) {
                                        $this->transRollback();
                                        $this->add('Log', array('Operation_content' => "[$this->mchid]" . '绑定失败1', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => M()->getLastSql() . json_encode($updateThirdData)));
                                        return $this->output(new \ResultModel(false, '绑定失败1'));
                                    }
                                }
                                $thirdD = $this->delete($thirdR->data['id'], 'ThirdParty');
                                if (!$thirdD->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '绑定失败2'));
                                }
                            } else {
                                $updateThirdData['id'] = $thirdR->data['id'];
                                $updateThirdData['passenger_id'] = $passengerTempR->data['passenger_id'];
                                $thirdS = $this->save('ThirdParty', $updateThirdData);
                                if (!$thirdS->ret) {
                                    $this->transRollback();
                                    $this->add('Log', array('Operation_content' => "[$this->mchid]" . '绑定失败2', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => M()->getLastSql() . json_encode($updateThirdData)));
                                    return $this->output(new \ResultModel(false, '绑定失败3'));
                                }
                            }

                            $passengerD = $this->delete($passenger_id, 'Passenger');
                            if (!$passengerD->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '绑定失败3'));
                            }

                            $this->commitTrans();

                            $data = $this->state->data;
                            $data['mobile'] = $cellphone;
                            \StateModel::clear($this->state->user_id, $this->mchid);
                            \StateModel::save($passenger_id, "", \StateModel::$PASSENGER, $data, $this->mchid);
                            return $this->output(new \ResultModel(true, '绑定成功'));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '绑定失败4'));
                        }
                    }
                } else {
                    $data = $this->state->data;
                    $data['mobile'] = $cellphone;
                    \StateModel::save($passenger_id, "", \StateModel::$PASSENGER, $data, $this->mchid);
                    return $this->output(new \ResultModel(false, '已绑定,请勿重复绑定'));
                }
            } else {

                if ($passengerTempR->data['passenger_id'] != $passengerR->data['passenger_id']) {
                    $updatePassengerData['cellphone'] = $cellphone;
                    $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                    $this->save('Passenger', $updatePassengerData);
                    $data = $this->state->data;
                    $data['mobile'] = $cellphone;
                    \StateModel::clear($this->state->user_id, $this->mchid);
                    \StateModel::save($passenger_id, "", \StateModel::$PASSENGER, $data, $this->mchid);
                    return $this->output(new \ResultModel(true, '绑定成功'));
                } else {
                    return $this->output(new \ResultModel(false, '该手机号已经被绑定过'));
                }
            }
        } else {
            //验证手机号码
            $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
            if ($r->ret) {
                $passengerS = $this->save('Passenger', array('passenger_id' => $passenger_id, 'cellphone' => $cellphone));
                //同步祁县出行账号余额
                $this->syncQiXianAccountBalance($passenger_id, $this->mchid);
                $data = $this->state->data;
                $data['mobile'] = $cellphone;
                \StateModel::save($passenger_id, "", \StateModel::$PASSENGER, $data, $this->mchid);
                if ($passengerS->ret) {
                    return $this->output(new \ResultModel(true, '绑定成功'));
                }
            }
        }

        return $this->output(new \ResultModel(false, '绑定失败'));
    }


    /**
     * 同步祁县出行账号余额
     *
     * @param int $passengerId 乘客ID
     * @param int $merchantId 商户ID
     * @return void
     */
    protected function syncQiXianAccountBalance($passengerId, $merchantId)
    {
        $url = C('CC_INNER_API_HOST') . "/api/inner/merchants/{$merchantId}/passengers/{$passengerId}/balances/sync";
        try {
            // 发起 HTTP 请求
            $response = httpRequest($url, 'post');

            // 解析接口返回结果
            $result = json_decode($response, true);

            if (!isset($result['status']) || $result['status'] !== 'success') {
                throw new \Exception("祁县余额同步失败：{$response}");
            }
        } catch (\Exception $e) {
            // 捕获异常并记录日志
            \Log::write("祁县余额同步异常：{$e->getMessage()}", 'ERROR');
        }
    }

    /**
     * 获取账号类型
     * @return null|\resultModel
     */
    public function getPassengerType()
    {
        $passengerTypeR = $this->select(array('mchid' => $this->mchid, 'is_del' => \CommonDefine::IS_DEL_0), null, null, null, 'PassengerType', 'passenger_type_id,name');
        if ($passengerTypeR->ret) {
            return $this->output($passengerTypeR);
        }

        return $this->output(new \ResultModel(true, [['name' => '普通会员', 'passenger_type_id']], 1));
    }

    /**
     * 乘客认证提交资料
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $real_name 真实姓名
     * @param string $birthday 出生年月
     * @param string $ID_number 身份证号码
     * @param int $passenger_type_id 账号类型
     * @param file $id_img 乘客身份证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     * @param file $residencebooklet_img 乘客户口簿(图片 "jpg", "gif", "bmp", "jpeg", "png")
     */
    public function doMemberAuthSubmitData($cellphone, $cellphone_validate_code, $real_name, $birthday, $ID_number, $passenger_type_id)
    {
        if (empty($real_name)) {
            return $this->output(new \ResultModel(false, '请填写真实姓名'));
        }
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '账号异常'));
        }

        if ($passengerR->data['status'] == \CommonDefine::EXAMINE_STATUS_1) {
            return $this->output(new \ResultModel(false, '审核中不能重复提交资料'));
        }

        $passengerData['examine_status'] = \CommonDefine::EXAMINE_STATUS_1;
        $passengerData['birthday'] = $birthday;
        $passengerData['ID_number'] = $ID_number;
        $passengerData['real_name'] = $real_name;
        $passengerData['passenger_id'] = $this->state->user_id;
        $passengerData['passenger_type_id'] = $passenger_type_id;
        if (empty($passengerR->data['cellphone'])) {
            $passengerData['cellphone'] = $cellphone;
        } elseif ($passengerR->data['cellphone'] != $cellphone) {
            return $this->output(new \ResultModel(false, '手机号码不一致'));
        }

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
        if ($r->ret) {
            $this->startTrans();
            $passengerS = $this->save('Passenger', $passengerData);
            if ($passengerS->ret) {
                //修改乘客资料
                $size = $_FILES['driving_img']['size'];
                $size += $_FILES['driver_img']['size'];
                $bytes = round($size / 1048576 * 100) / 100;

                if (empty($_FILES['id_img']) && empty($_FILES['residencebooklet_img'])) {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '请上传身份证或者户口簿'));
                }

                //上传身份证
                if (!empty($_FILES['id_img']) && $_FILES['id_img']['name']) {
                    if (!$this->doUploadImg($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_3)) {
                        $this->transRollback();
                        $this->output(new \ResultModel(false, '上传身份证失败'));
                    }
                }
                //上传户口簿
                if (!empty($_FILES['residencebooklet_img']) && $_FILES['residencebooklet_img']['name']) {
                    if (!$this->doUploadImg($this->mchid, $this->state->user_id, \CommonDefine::IMG_TYPE_1)->ret) {
                        $this->transRollback();
                        $this->output(new \ResultModel(false, '上传户口簿失败'));
                    }
                }

                $updatePakeageR = self::updatePakeageInfo($this->mchid, $bytes, '', '');
                if (!$updatePakeageR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '提交失败'));
                }

                $this->commitTrans();
                if (empty($this->state->data['mobile'])) {
                    $data['mobile'] = $cellphone;
                    \StateModel::save($this->state->user_id, "", \StateModel::$PASSENGER, $data, $this->mchid);
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '提交失败'));
            }

            return $this->output(new \ResultModel(true, '提交成功'));
        }
        return $this->output($r);
    }


    /**
     * 刷新位置
     * @param double $longitude 经度
     * @param double $latitude 纬度
     */
    public function doRefreshPosition($longitude, $latitude)
    {
        $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        return $this->output($r);
    }

    /**
     * 刷新位置(手机端)
     * @param double $longitude 经度
     * @param double $latitude 纬度
     */
    public function doMobileRefreshPosition($longitude, $latitude)
    {
        $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        return $this->output($r);
    }


    /**
     * 完善信息(手机端)
     * @param string $name 姓名
     * @param int $gender 性别（1-男，2-女）
     * @param string $email 邮箱
     */
    public function doMobileCompleteInformation($name, $gender, $email)
    {
        $this->data['passenger_id'] = $this->state->user_id;
        $this->data['email_f'] = $email;
        $r = $this->save('Passenger');
        return $this->output($r);
    }

    /**
     * 完善信息
     * @param string $name 姓名
     * @param int $gender 性别（1-男，2-女）
     * @param string $email 邮箱
     */
    public function doCompleteInformation($name, $gender, $email)
    {
        $this->data['passenger_id'] = $this->state->user_id;
        $this->data['email_f'] = $email;
        $r = $this->save('Passenger');
        return $this->output($r);
    }

    /**
     * 修改乘车路线
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $start_time 出发时间
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param int $ride_count 乘车人数（默认为1）
     */
    public function doUpdateDriveMode($drive_mode, $start_address_code, $end_address_code, $address_code, $start_time, $start_address_remark = null, $end_address_remark = null, $ride_count = 1)
    {
        $this->data['passenger_id'] = $this->state->user_id;
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '乘车地格式不正确或地址不存在'));
            }
        }
        //验证出发时间
        if (strtotime($start_time) <= strtotime(get_current_time())) {
            return $this->output(new \ResultModel(false, '出发时间不能小于或等于当前时间'));
        }
        //验证乘车人数
        if (intval($ride_count) <= 0) {
            return $this->output(new \ResultModel(false, '乘车人数不能小于或等于0'));
        }
        $this->data['state'] = 2;
        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'state');
        if ($r->ret) {
            if (intval($r->data['state']) === 1) {
                $this->output(new \ResultModel(false, '在路上时不能发布乘车消息'));
            }
        }
        $r = $this->save('Passenger');
        return $this->output($r);
    }

    /**
     * 修改乘车路线(手机端)
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $start_time 出发时间
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param int $ride_count 乘车人数（默认为1）
     */
    public function doMobileUpdateDriveMode($drive_mode = 1, $start_address_code, $end_address_code, $address_code, $start_time, $start_address_remark = null, $end_address_remark = null, $ride_count = 1)
    {
        $this->data['passenger_id'] = $this->state->user_id;

        $r = $this->checkingGdAddressCode($start_address_code);
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
        }
        $r = $this->checkingAddressCode($end_address_code);
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
        }
        if (intval($start_address_code) === intval($end_address_code)) {
            return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
        }

        //验证出发时间
        if (strtotime($start_time) <= strtotime(get_current_time())) {
            return $this->output(new \ResultModel(false, '出发时间不能小于或等于当前时间'));
        }
        //验证乘车人数
        if (intval($ride_count) <= 0) {
            return $this->output(new \ResultModel(false, '乘车人数不能小于或等于0'));
        }
        $this->data['state'] = 2;
        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'state');
        if ($r->ret) {
            if (intval($r->data['state']) === 1) {
                $this->output(new \ResultModel(false, '在路上时不能发布乘车消息'));
            }
        }
        $r = $this->save('Passenger');
        return $this->output($r);
    }


    /**
     * 获取乘客信息
     * @param int $passenger_id 乘客ID（默认为空，为当前用户）
     * @param string $fields 查询字段列表（默认为空，查询所有）
     */
    public function getPassenger($passenger_id, $fields = null)
    {
        if (empty($passenger_id)) {
            return $this->output(new \ResultModel(false, '非微信用户'));
        }
        $r = $this->getEntityById($passenger_id, 'Passenger', $fields, 'password', 'name,drive_mode,start_address_code,end_address_code,address_code');
        if ($r->ret) {
            if ($r->data['birthday']) {
                $r->data['age'] = birthday($r->data['birthday']) . "岁";
            } else {
                $r->data['age'] = "未知";
            }
        }
        return $this->output($r);
    }

    /**
     * 获取乘客信息(手机端)
     * @param int $passenger_id 乘客ID（默认为空，为当前用户）
     * @param string $fields 查询字段列表（默认为空，查询所有）
     */
    public function getMobilePassenger($passenger_id = null, $fields = null)
    {
        if (empty($passenger_id)) {
            $passenger_id = $this->state->user_id;
        }
        $r = $this->getEntityById($passenger_id, 'Passenger', $fields, 'password', 'drive_mode,start_address_code,end_address_code,address_code');
        if ($r->ret) {
            if ($this->isIncludeRootField($fields, 'start_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['start_address_code']);
                if ($t->ret) {
                    $r->data['start_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'end_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['end_address_code']);
                if ($t->ret) {
                    $r->data['end_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'address_code') && intval($r->data['drive_mode']) === 2) {
                $t = $this->resolutionAddress($r->data['address_code']);
                if ($t->ret) {
                    $r->data['address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if (!$this->isIncludeRootField($fields, 'start_address_code')) {
                remove_arr($r->data, 'start_address_code');
            }
            if (!$this->isIncludeRootField($fields, 'end_address_code')) {
                remove_arr($r->data, 'end_address_code');
            }
            if (!$this->isIncludeRootField($fields, 'address_code')) {
                remove_arr($r->data, 'address_code');
            }
            if (!$this->isIncludeRootField($fields, 'drive_mode')) {
                remove_arr($r->data, 'drive_mode');
            }
        }
        return $this->output($r);
    }


    /**
     * 忘记密码
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 新密码
     * @param string $repassword 确认新密码
     */
    public function doForgetPassword($cellphone, $cellphone_validate_code, $mchid, $password, $repassword)
    {
        if (!$mchid) {
            return $this->output(new \ResultModel(false, '请稍后再试'));
        }
        //验证手机号码
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            $r = $this->find(array('cellphone' => $cellphone, 'validate_code' => $cellphone_validate_code), 'CellphoneValidate', 'update_time,validate_code');
            if ($r->ret) {
                if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') < strtotime(get_current_time())) {
                    return $this->output(new \ResultModel(false, '验证码已经过期'));
                }
            } else {
                return $this->output(new \ResultModel(false, '手机验证码错误'));
            }
        }
        $mchid = $this->checkauth($mchid);
        $r = $this->find(array('cellphone' => $cellphone, 'mchid' => $mchid), 'Passenger', 'passenger_id', 'mchid');
        if ($r->ret) {
            $r = $this->save('Passenger', array('passenger_id' => $r->data['passenger_id'], 'mchid' => $mchid, 'password' => md5($password)));
        }
        return $this->output($r);
    }

    /**
     * 忘记密码（手机版）
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 新密码
     * @param string $repassword 确认新密码
     */
    public function doMobileForgetPassword($cellphone, $cellphone_validate_code, $mchid, $password, $repassword)
    {
        $mchid = $this->checkauth($mchid)->data;
        //验证手机号码
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            $r = $this->find(array('cellphone' => $cellphone, 'validate_code' => $cellphone_validate_code, 'mchid' => $mchid), 'CellphoneValidate', 'update_time,validate_code');
            if ($r->ret) {
                if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') < strtotime(get_current_time())) {
                    return $this->output(new \ResultModel(false, '验证码已经过期'));
                }
            } else {
                return $this->output(new \ResultModel(false, '手机验证码错误'));
            }
        }
        $r = $this->find(array('cellphone' => $cellphone), 'Passenger', 'passenger_id');

        if ($r->ret) {
            $r = $this->save('Passenger', array('passenger_id' => $r->data['passenger_id'], 'mchid' => $mchid, 'password' => md5($password)));
            if ($r->ret) {
                return $this->output(new \ResultModel(true, '找回密码成功，请重新登录！'));
            } else {
                return $this->output(new \ResultModel(false, '找回密码失败，请稍后再试！'));
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '找回密码失败，请稍后再试！'));
        }
    }

    /**
     * 修改密码
     * @param string $prepassword 原始密码
     * @param string $password 新密码
     * @param string $repassword 确认新密码
     */
    public function doUpdatePassword($prepassword, $password, $repassword)
    {
        $r = $this->find(array('passenger_id' => $this->state->user_id, 'password' => md5($prepassword)), 'Passenger', 'passenger_id');
        if ($r->ret) {
            $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'password' => md5($password), 'repassword' => md5($repassword)));
            if ($r->ret) {
                \StateModel::save($r->data, $password, \StateModel::$PASSENGER);
            }
        } else {
            $this->output(new \ResultModel(false, '原始密码错误'));
        }
        return $this->output($r);
    }

    /**
     * 修改密码(手机版)
     * @param string $password 新密码
     * @param string $repassword 确认新密码
     */
    public function getMobileUpdatePassword($password, $repassword)
    {
        //        $r = $this->find(array('passenger_id' => $this->state->user_id, 'password' => md5($prepassword)), 'Passenger', 'passenger_id');
        //        if ($r->ret) {

        $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'password' => md5($password), 'repassword' => md5($repassword)));
        if ($r->ret) {
            \StateModel::save($r->data, $password, \StateModel::$PASSENGER);
        }
        //        } else {
        //            $this->output(new \ResultModel(false, '原始密码错误'));
        //        }
        return $this->output($r);
    }

    /**
     * 修改昵称(手机版)
     * @param string $name 昵称
     */
    public function doMobileUpdateName($passenger_id, $name)
    {
        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'passenger_id,name');
        if ($r->ret) {
            $this->startTrans();
            $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'name' => $name));
            if ($r) {
                $this->commitTrans();
            } else {

                $this->transRollback();
            }
        } else {
            $this->output(new \ResultModel(false, '未查找到该用户'));
        }
        return $this->output($r);
    }

    /**
     * 检测新版本
     * @param double $version_code 版本号
     */
    public function doTestingNewVersion($version_code)
    {
        $latest_version_code = C('PASSENGER_LATEST_VERSION_CODE');
        if ($version_code === $latest_version_code) {
            return $this->output(new \ResultModel(false, '已经是最新版本了'));
        } else {
            return $this->output(new \ResultModel(true, C('PASSENGER_LATEST_VERSION_PATH')));
        }
    }

    /**
     * 修改状态
     * @param int $state （1-车上，2-下车，3-隐身）
     */
    public function doUpdateState($passenger_id, $state)
    {

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state');
        if ($r->ret) {
            $current_state = intval($r->data['state']);
            $state = intval($state);

            $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('lt', 5)), 'Order');
            if ($r->ret) {
                if ($r->data > 0 && intval($state) !== 1) {
                    return $this->output(new \ResultModel(false, '你还有订单没有完成'));
                }
            }

            if ($state === 1) {
                if ($current_state !== 2) {
                    return $this->output(new \ResultModel(false, '你现在正在车上，不能停止'));
                }
            }

            $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'state' => intval($state)));
        } else {
            return $this->output(new \ResultModel(false, '乘客不存在'));
        }

        return $this->output($r);
    }

    /**
     * 修改状态(手机端)
     * @param int $state （1-车上，2-下车，3-隐身）
     */
    public function doMobileUpdateState($passenger_id, $state)
    {

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state');
        if ($r->ret) {
            $current_state = intval($r->data['state']);
            $state = intval($state);

            $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('lt', 5)), 'Order');
            if ($r->ret) {
                if ($r->data > 0 && intval($state) !== 1) {
                    return $this->output(new \ResultModel(false, '你还有订单没有完成'));
                }
            }

            if ($state === 1) {
                if ($current_state !== 2) {
                    return $this->output(new \ResultModel(false, '你现在正在车上，不能停止'));
                }
            }

            $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'state' => intval($state)));
        } else {
            return $this->output(new \ResultModel(false, '乘客不存在'));
        }

        return $this->output($r);
    }

    /**
     * 绑定银行卡(手机端)
     * * @param double $bank 银行名称
     * @param double $name 持卡人姓名
     * @param double $bank_phone 预留手机
     * @param double $card_num 银行卡号
     */
    public function doBindBank($passenger_id, $name, $bank_phone, $card_num)
    {

        $this->startTrans();
        $r = $this->add('Bank', array('account_id' => $passenger_id, 'name' => $name, 'bank_phone' => $bank_phone, 'card_num' => $card_num, 'type' => 1));
        if ($r) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'passenger_id,bank_name,bank_cellphone,bank_number');
        return $this->output($r);
    }

    /**
     * 提现管理(手机端)
     * @param double $passenger_id 银行卡id
     */
    public function getBankList($passenger_id)
    {
        $r = $this->select(array('account_id' => $passenger_id, 'type' => 1), 1, 10, null, 'Bank');
        $data = array();
        if (!$r->data) {
            $r->data = $data;
            $r->ret = true;
            return $this->output($r);
        } else {
            return $this->output($r);
        }
    }

    /**
     * 微信端页面
     * @param string $cellphone 手机号
     * @param string $password 密码
     */
    public function doGetPassengerStatus($passenger_id)
    {
        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'passenger_id,name,is_freeze,name,cellphone,openid');
        if ($r->ret) {
            //判断是否被删除
            if (intval($r->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
        }
        return $this->output($r);
    }

    /**
     * 余额转出(手机端)
     * @param double $cash_balances 转出金额
     * @param double $bank_number 银行卡号
     * @param double $bank_id 银行卡id
     */
    public function doMobileBalanceTransfer($passenger_id, $cash_balances, $bank_id)
    {
        $mchid = cookie('mchid');
        if (!$mchid) {
            $mchid = $_GET['callback'];
            $mchid = $this->checkauth($mchid)->data;
        }
        if (!$mchid) {
            return $this->output(new \ResultModel(false, '提现失败'));
        }
        if ($cash_balances > 0) {
            $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'cash_balances');
            if ($r->ret) {
                if ($r->data['cash_balances'] < $cash_balances) {
                    return $this->output(new \ResultModel(false, '当前余额不足'));
                }
                $money = $r->data['cash_balances'] - $cash_balances;
                $this->startTrans();
                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'cash_balances' => $money));
                if ($r->ret) {
                    $date = date('Y-m-d H:i:s', time());
                    //添加申请至提现表
                    $data = D('Deposit')->add(array('account_id' => $passenger_id, 'create_time' => $date, 'moneys' => $cash_balances, 'mchid' => $mchid, 'account_type' => 1, 'bank_id' => $bank_id));
                    if ($data) {
                        $this->commitTrans();
                        return $this->output(new \ResultModel(true, '提现申请成功'));
                    } else {
                        $this->transRollback();
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                return $this->output(new \ResultModel(false, '当前银行卡不存在,请先绑定银行卡'));
            }
        } else {
            return $this->output(new \ResultModel(false, '提现金额必须大于零'));
        }
        return $this->output(new \ResultModel(false, '提现申请失败'));
    }

    /**
     * 我的账户(手机端)
     */
    public function doMobileAccount($passenger_id)
    {
        if (!empty($passenger_id)) {
            $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'cash_balances');
            if (empty($r->data)) {
                return $this->output(new \ResultModel(false, '未查找到相关信息'));
            } else {
                if (!$r->data['cash_balances']) {
                    $r->data['cash_balances'] = '0';
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '请输入完整信息'));
        }

        return $this->output($r);
    }

    /**
     * 获取地址列表
     * @param int $status 1-省份；2-城市；3-区县；4-乡镇；
     */
    public function doGetAddressList($type = 0, $status = 1)
    {
        $metaModel = new \MetaAddressModel();
        $addressList = $metaModel->where('address_pid = ' . $type)->field('address_id, name,code')->select();
        $data = "";
        if ($status == 1) {
            $data .= '<option value="省份">省份</option>';
        } elseif ($status == 2) {
            $data .= '<option value="城市">城市</option>';
        } elseif ($status == 3) {
            $data .= '<option value="县区">县区</option>';
        } else {
            $data .= '<option value="乡镇">乡镇</option>';
        }
        if (is_array($addressList)) {
            foreach ($addressList as $k => $metaAddress) {
                $data .= '<option data-gcode=' . $metaAddress['code'] . ' value="' . $metaAddress['address_id'] . '">' . $metaAddress['name'] . '</option>';
            }
        } else {
            return $this->output(new \ResultModel(true, array(), 0));
        }
        $ret = array($data);
        return $this->output(new \ResultModel(true, $ret, count($ret)));
    }


    /**
     * 退出登录(手机端)
     */
    public function doMobileExit()
    {
        \StateModel::clear(\StateModel::$PASSENGER);
        return $this->output(new \ResultModel(true));
    }

    /**
     * 订单支付（小程序）
     * @param string $order_no 订单编号（必须）
     * @param double $amount 支付金额（必须）
     */
    public function doWxMiniOrderPay($order_no, $amount)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output(new \ResultModel(false, '统一下单失败'));
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo, 'is_pay' => 0), 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单不存在'));
        }

        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '用户信息错误'));
        }

        if (empty($passengerR->data['openid_mini'])) {
            return $this->output(new \ResultModel(false, '小程序用户openid不存在'));
        }

        $openid = $passengerR->data['openid_mini'];


        if (in_array($this->mchid, C('PAYMENT_UPGRADE_GRAY_MCH_IDS'))) {
            return $this->doUnionOrderPay($orderNo, $openid, $amount, 'wechat_mini');
        }

        $userAuthR = $this->find(array('uid' => $orderR->data['mchid'], 'auth_type' => \CommonDefine::AUTH_TYPE_2, 'isauthorized' => 0), 'UserAuth');
        if (!$userAuthR->ret) {
            return $this->output(new \ResultModel(false, '微信小程序授权失败，请重新授权'));
        }

        if ($orderR->data['real_price'] * 100 != $amount * 100) {
            return $this->output(new \ResultModel(false, '订单金额错误'));
        }

        $this->startTrans();

        //班线订单
        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            //            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
            if (empty($lineClassTrainArr)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '该班次不存在'));
            }
            if ($lineClassTrainArr['is_del'] == \CommonDefine::IS_DEL_1) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '该班次不存在'));
            }

            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
                $lineClassR = $this->find(array('id' => $lineClassTrainArr['line_class_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'LineClass');
                if (!$lineClassR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '线路已下架，请选择其他线路下单'));
                }
                if ($lineClassTrainArr['status'] == \CommonDefine::LINE_CLASS_OPERATE_O || $lineClassR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '线路已停运，请选择其他线路下单'));
                }
                // //余票检测
                // if (!empty($lineClassTrainArr)) {
                //     if ($lineClassTrainArr['remain_tickets'] < $orderR->data['book_seating']) {
                //         $this->transRollback();
                //         return $this->output(new \ResultModel(false, '余票不足，请选择其他线路下单'));
                //     }
                // }
            }
        }

        $this->data['account_id'] = $orderR->data['passenger_id'];
        $this->data['account_type'] = 0;

        $payHistoryR = $this->find(array('order_no' => $orderNo, 'openid' => $openid), 'PayOrderHistory');
        if (!$payHistoryR->ret) {
            $this->data['openid'] = $openid;
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单异常'));
            }
        }

        $extra = array('open_id' => $openid);
        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($orderR->data['mchid'])) {
            $this->transRollback();
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }
        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createChargeWxmini($openid, $orderNo, '代收车费', json_encode($extra), $orderR->data['real_price'], $this->getCipmchidByMchid($orderR->data['mchid'])->data['ciphertext'], $userAuthR->data['authorizer_appid'], $orderR->data['create_time']);
            if (!$charge) {
                \Log::write($charge);
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '支付失败，请线下付款!'));
            }

            if ($charge['return_code'] == 'SUCCESS') {
                # 更新订单支付方式
                $this->save('Order', array('order_id' => $orderR->data['order_id'], 'payway' => \CommonDefine::WECHAT_MINI));

                $this->commitTrans();
                return  $this->output(new \ResultModel(true, json_decode($wxPayUtil->GetJsApiParameters($charge), true)));
            }
        } catch (\Exception $e) {
            $this->transRollback();
            \Log::write($e->getCode() . $e->getMessage());
            return  $this->output(new \ResultModel(false, $e->getCode() . $e->getMessage()));
        }
        $this->transRollback();
        return  $this->output(new \ResultModel(false, '支付失败，请线下付款'));
    }


    /**
     * 支付网关接口
     * @param string $order_no
     * @param string $openid
     * @param float $amount
     * @param mixed $payway
     * @return mixed
     */
    public function doUnionOrderPay($order_no, $openid, $amount, $payway)
    {
        // 参数验证
        if (empty($this->mchid)) {
            return $this->createResult(false, '商户编码错误');
        }

        if (empty($order_no) || !is_numeric($amount) || $amount <= 0) {
            return $this->createResult(false, '订单号或金额无效');
        }

        if (empty($openid)) {
            return $this->createResult(false, '微信公众号OPENID无效');
        }

        // 请求头
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json',
            'Content-Type: application/json'
        ];

        // 请求参数
        $params = array(
            'order_no' => $order_no,
            'subject' => '代收车费',
            'amount' => $amount,
            'channel' => 'wechat',
            'payway' => $payway,
            'is_profit_sharing' => false,
            'buyer' => $openid,
            'profit_sharing_amount' => 0,
        );

        // 构建请求URL
        $url = C('CC_PROXY_API_HOST') . "/api/inner/union_orders";

        // 发起 HTTP 请求
        $response = httpRequest($url, 'post', $params, $header, true);

        // 处理响应结果
        $result = $this->processResponse($response, $url, $params);

        return $this->output($result);
    }


    /**
     * 支付网关接口
     * @param string $order_no
     * @param string $openid
     * @param float $amount
     * @return mixed
     */
    public function doCreatePassengerWallets($payway, $amount)
    {
        // 参数验证
        if (empty($this->state->user_id)) {
            return $this->createResult(false, '乘客ID错误');
        }

        if (!is_numeric($amount) || $amount <= 0) {
            return $this->createResult(false, '充值金额无效');
        }

        // 请求头
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json',
            'Content-Type: application/json'
        ];

        // 请求参数
        $params = array(
            'amount' => $amount,
            'payway' => $payway,
        );

        // 构建请求URL
        $url = C('CC_PROXY_API_HOST') . "/api/inner/wallets";

        // 发起 HTTP 请求
        $response = httpRequest($url, 'post', $params, $header, true);

        // 处理响应结果
        $result = $this->processResponse($response, $url, $params);

        return $this->output($result);
    }


    /**
     * 提现网关接口
     * @param float $amount 提现金额
     * @param string $payway 提现方式
     * @return mixed
     */
    public function doCreatePassengerWithdrawl($amount, $payway = 'wechat_jsapi')
    {
        // 参数验证
        if (empty($this->state->user_id)) {
            return $this->createResult(false, '乘客ID错误');
        }

        if (!is_numeric($amount) || $amount <= 0) {
            return $this->createResult(false, '提现金额无效');
        }

        // 请求头
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json',
            'Content-Type: application/json'
        ];

        // 请求参数
        $params = array(
            'amount' => $amount,
            'payway' => $payway,
        );

        // 构建请求URL
        $url = C('CC_PROXY_API_HOST') . "/api/inner/transfers";

        // 发起 HTTP 请求
        $response = httpRequest($url, 'post', $params, $header, true);

        // 处理响应结果
        $result = $this->processResponse($response, $url, $params);

        return $this->output($result);
    }

    /**
     * 处理 HTTP 响应结果
     *
     * @param string|false $response HTTP 响应
     * @param string $url 请求的 URL
     * @param array $params 请求参数
     * @return \ResultModel
     */
    protected function processResponse($response, $url, $params)
    {
        $ret = new \ResultModel(false);
        // 解码 JSON 响应
        $results = json_decode($response, true);

        // 判断响应状态
        if ($results['status'] == 'success' && !empty($results['data'])) {

            $payload = json_decode($results['data']['payload'], true);
            $ret->ret = true;
            $ret->data = $payload;
        } else {
            // 处理错误响应，并记录详细日志
            $ret->data = sprintf('请求失败：%s', $results['message'] ?: '未知错误');
        }

        return $ret;
    }

    /**
     * 创建统一的结果对象
     *
     * @param bool $success 执行成功或失败
     * @param string|null $message 错误或成功的消息
     * @return \ResultModel
     */
    protected function createResult($success, $message = null)
    {
        $ret = new \ResultModel($success);
        if ($message) {
            $ret->data = $message;
        }
        return $ret;
    }

    /**
     * 自定义日志记录方法
     *
     * @param string $message 日志消息
     * @param array $context 相关上下文信息
     * @param string $level 日志级别
     */
    protected function logPayRequest($message, $context = [], $level = 'ERR')
    {
        // 格式化日志信息
        $logMessage = $message . ' | ' . json_encode($context, JSON_UNESCAPED_UNICODE);

        // 使用 ThinkPHP 内置日志记录方法
        \Log::write($logMessage, $level, '', LOG_PATH . 'pay.log');
    }

    /**
     * 乘客钱包支付
     *
     * @param string $order_no 订单编号（必须）
     * @param float $amount 支付金额（必须）
     * @return void
     */
    public function doPassengerWalletPay($order_no, $amount)
    {
        // 参数验证
        if (empty($this->mchid)) {
            return $this->createResult(false, '商户编码错误');
        }

        if (empty($order_no) || !is_numeric($amount) || $amount <= 0) {
            return $this->createResult(false, '订单号或金额无效');
        }

        $params = [];

        // 请求头
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json',
            'Content-Type: application/json'
        ];

        // 构建请求URL
        $url = C('CC_PROXY_API_HOST') . "/api/inner/orders/{$order_no}/wallet_payments";

        // 发起 HTTP 请求
        $response = httpRequest($url, 'post', $params, $header, true);

        // 处理响应结果
        $result = $this->processResponse($response, $url, $params);

        return $this->output($result);
    }

    /**
     * 订单发起微信支付
     *
     * @param string $order_no 订单编号（必须）
     * @param string $openid 用户openid（必须）
     * @param float $amount 支付金额（必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     * @return void
     */
    public function doWxOrderPay($order_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        if (in_array($this->mchid, C('PAYMENT_UPGRADE_GRAY_MCH_IDS'))) {
            return $this->doUnionOrderPay($order_no, $openid, $amount, 'wechat_jsapi');
        }

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output(new \ResultModel(false, '统一下单失败'));
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo), 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, sprintf('订单号（%s）不存在', $orderNo)));
        }
        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
            if ($orderR->data['mchid'] == 1258) {
                return $this->output(new \ResultModel(false, '未开启线上支付功能，请联系司机线下收款'));
            }
        }

        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7) {
            $orderSubWhere['relation_order_id'] = $orderR->data['order_id'];
            $orderSubR = $this->find($orderSubWhere, 'OrderSub');
            if ($orderSubR->ret) {
                return $this->output(new \ResultModel(false, '摆渡接送单不支持单独支付'));
            }
        } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            # 实时检测班线订单座位是否被释放，如果座位被释放或者订单已被取消，无法继续发起支付
            if (
                $orderR->data['book_seating'] == 0
                || $orderR->data['state'] == \CommonDefine::ORDER_STATE_7
            ) {
                return $this->output(new \ResultModel(false, '订单已被取消或者班次车票不足，无法继续支付'));
            }
        }

        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '用户信息错误'));
        }

        $payServiceR = $this->checkPayService($orderR->data['mchid']);
        if ($payServiceR->ret) {
            if (empty($passengerR->data['openid'])) {
                return $this->output(new \ResultModel(false, '用户授权错误，退出后重新打开'));
            }
            $openid = $passengerR->data['openid'];
        } else {
            if (empty($passengerR->data['openid_pay'])) {
                return $this->output(new \ResultModel(false, '用户授权错误，退出后重新打开'));
            }
            $openid = $passengerR->data['openid_pay'];
        }

        if (
            $orderR->data['real_price'] > 0
            && $orderR->data['real_price'] * 100 != $amount * 100
        ) {
            return $this->output(new \ResultModel(false, '订单金额错误'));
        }

        $this->startTrans();

        //班线订单
        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            if (empty($lineClassTrainArr)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '线路已下架'));
            }
            if ($lineClassTrainArr['is_del'] == \CommonDefine::IS_DEL_1) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '该班次不存在'));
            }

            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
                $lineClassR = $this->find(array('id' => $lineClassTrainArr['line_class_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'LineClass');
                if (!$lineClassR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '线路已下架，请选择其他线路下单'));
                }
                if ($lineClassTrainArr['status'] == \CommonDefine::LINE_CLASS_OPERATE_O || $lineClassR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '线路已停运，请选择其他线路下单'));
                }
            }
        }

        $this->data['account_id'] = $orderR->data['passenger_id'];
        $this->data['account_type'] = 0;

        $payHistoryR = $this->find(array('order_no' => $orderNo, 'openid' => $openid), 'PayOrderHistory');
        if (!$payHistoryR->ret) {
            $this->data['openid'] = $openid;
            $this->data['order_no'] = $orderNo;
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单异常'));
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($orderR->data['mchid'], $channel)) {
            $this->transRollback();
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }
        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderNo, '代收车费', json_encode($extra), $amount, $this->getCipmchidByMchid($orderR->data['mchid'])->data['ciphertext']);
            if (!$charge) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '支付失败，请线下付款'));
            }

            if ($charge['return_code'] == 'SUCCESS') {
                # 更新订单支付方式
                $this->save('Order', array('order_id' => $orderR->data['order_id'], 'payway' => \CommonDefine::WECHAT_JSAPI));

                $this->commitTrans();
                return $this->output(new \ResultModel(true, json_decode($wxPayUtil->GetJsApiParameters($charge), true)));
            }
        } catch (\Exception $e) {
            $this->transRollback();
            \Log::write($e->getCode() . $e->getMessage());
            return $this->output(new \ResultModel(false, $e->getCode() . $e->getMessage()));
        }

        $this->transRollback();
        return $this->output(new \ResultModel(false, '支付失败，请线下付款'));
    }



    /**
     * 云裕众-学生号订单支付
     * @param string $order_no 订单编号（必须）
     * @param string $openid 用户openid（必须）
     * @param double $amount 支付金额（必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     * @param integer $student_order_pay_type 学生号拼车：不传；学生号组团包车：1-在线；2-扣卡；家长互助：不传
     */
    public function doYyWxStudentOrderPay($order_no, $openid = null, $amount, $channel = 'wx_pub', $student_order_pay_type = \YyCommonDefine::STUDENT_ORDER_PAY_TYPE_1)
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo, 'is_pay' => 0), 'Order');
        if (!$orderR->ret) {
            $ret->data = '订单不存在';
            return $this->output($ret);
        }

        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
        if (!$passengerR->ret) {
            $ret->data = '用户信息错误';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($orderR->data['mchid']);
        if ($payServiceR->ret) {
            if (empty($passengerR->data['openid'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid'];
        } else {
            if (empty($passengerR->data['openid_pay'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid_pay'];
        }

        if ($orderR->data['real_price'] * 100 != $amount * 100) {
            $ret->data = '订单金额错误';
            return $this->output($ret);
        }

        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_8) {
            $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'YyLineClassTrain');
            if (!$yyLineClassTrainR->ret) {
                $ret->data = '线路已下架';
                return $this->output($ret);
            }

            if ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_1) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
                    $yyLineClassR = $this->find(array('id' => $yyLineClassTrainR->data['line_class_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'YyLineClass');
                    if (!$yyLineClassR->ret) {
                        $ret->data = '线路已下架，请选择其他线路下单';
                        return $this->output($ret);
                    }
                    if ($yyLineClassTrainR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O || $yyLineClassR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O) {
                        $ret->data = '线路已停运，请选择其他线路下单';
                        return $this->output($ret);
                    }
                    //余票检测
                    if ($yyLineClassTrainR->ret) {
                        if ($yyLineClassTrainR->data['remain_tickets'] < $orderR->data['book_seating']) {
                            $ret->data = '余票不足，请选择其他线路下单';
                            return $this->output($ret);
                        }
                    }
                }
            } elseif ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_2) {
                if ($student_order_pay_type == \YyCommonDefine::STUDENT_ORDER_PAY_TYPE_1) {
                    $yyLineClassR = $this->find(array('id' => $yyLineClassTrainR->data['line_class_id'], 'is_del' => \CommonDefine::IS_DEL_0), 'YyLineClass');
                    if (!$yyLineClassR->ret) {
                        $ret->data = '线路已下架，请选择其他线路下单';
                        return $this->output($ret);
                    }
                    if ($yyLineClassTrainR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O || $yyLineClassR->data['status'] == \CommonDefine::LINE_CLASS_OPERATE_O) {
                        $ret->data = '线路已停运，请选择其他线路下单';
                        return $this->output($ret);
                    }
                    //余票检测
                    if ($yyLineClassTrainR->ret) {
                        if ($yyLineClassTrainR->data['remain_tickets'] < $orderR->data['book_seating']) {
                            $ret->data = '余票不足，请选择其他线路下单';
                            return $this->output($ret);
                        }
                    }
                } elseif ($student_order_pay_type == \YyCommonDefine::STUDENT_ORDER_PAY_TYPE_2) { //扣次数
                    $this->startTrans();
                    $updatePassengerCardInfoR = $this->updatePassengerCardInfo($orderR->data['passenger_id'], -$orderR->data['student_times']);
                    if (!$updatePassengerCardInfoR->ret) {
                        $this->transRollback();
                        $ret->data = '次数不足';
                        return $this->output($ret);
                    }

                    $updateTicketsR = $this->yyDrawTicket($orderR->data['line_id'], $orderR->data['book_seating']);
                    if (!$updateTicketsR->ret) {
                        $this->transRollback();
                        $ret->data = '出票失败';
                        return $this->output($ret);
                    }

                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //后支付模式
                        $orderUpdataData['order_id'] = $orderR->data['order_id'];
                        $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                            $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                        }

                        //支付成功,更新订单状态,更新账户余额
                        $orderS = $this->save('Order', $orderUpdataData);
                        if ($orderS->ret) {
                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                            if (!$driverR->ret) {
                                $this->transRollback();
                            }
                            $payOrderHistoryR = $this->find(array('order_no' => $orderNo), 'PayOrderHistory');
                            if ($payOrderHistoryR->ret) {
                                $payOrderHistoryS = $this->save('PayOrderHistory', array('id' => $payOrderHistoryR->data['id'], 'order_no' => $orderNo, 'status' => 1, 'pay_type' => 1));
                                if (!$payOrderHistoryS->ret) {

                                    $this->transRollback();
                                    $ret->data = '支付失败';
                                    return $this->output($ret);
                                }
                            } else {
                                $orderHistroyData['account_id'] = $orderR->data['passenger_id'];
                                $orderHistroyData['account_type'] = 0;
                                $orderHistroyData['status'] = 1;
                                $orderHistroyData['pay_type'] = 1;
                                $payOrderHistoryA = $this->add('PayOrderHistory', $orderHistroyData);
                                if (!$payOrderHistoryA->ret) {
                                    $this->transRollback();
                                    $ret->data = '订单异常';
                                    return $this->output($ret);
                                }
                            }

                            if ($orderR->data['state'] < 5) {
                                //记账
                                //todo 记账
                            } elseif ($orderR->data['state'] == 5) {
                                //完成订单
                            }

                            $this->commitTrans();
                            $ret->ret = true;
                            return $this->output($ret);
                        }
                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
                        //余票扣除
                        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'YyLineClassTrain');
                        if ($yyLineClassTrainR->ret) {
                            if ($yyLineClassTrainR->data['remain_tickets'] >= $orderR->data['book_seating']) {
                                //出票成功
                                $upYyLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
                                $upYyLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] - $orderR->data['book_seating'];
                                $yyLineClassTrainS = $this->save('YyLineClassTrain', $upYyLineClassTrainData);
                                if ($yyLineClassTrainS->ret) {
                                    $upOrderData['order_id'] = $orderR->data['order_id'];
                                    $upOrderData['is_pay'] = 1;
                                    $yyLineClassTrainDriverR = $this->getYyLineClassTrainDriver($orderR->data['line_id'], $orderR->data['book_seating']);
                                    if (!$yyLineClassTrainDriverR->ret) {
                                        $this->transRollback();
                                        $ret->data = '支付失败,线路司机指派失败';
                                        return $this->output($ret);
                                    }

                                    //班线车订单模式，默认自动派单
                                    $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
                                    //                                    $lineClassAppointConfigR = $this->find(array('key'=>\CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $orderR->data['mchid']),'SystemConfig');
                                    //                                    if($lineClassAppointConfigR->ret){
                                    //                                        $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
                                    //                                    }

                                    $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_1;
                                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                                        $upOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                                        $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                                        $upOrderData['driver_id'] = $yyLineClassTrainDriverR->data['driver_id'];
                                    } elseif ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1) {
                                        $upOrderData['state'] = \CommonDefine::ORDER_STATE_1;
                                        $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                                    }

                                    $orderS = $this->save('Order', $upOrderData);
                                    if ($orderS->ret) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1));
                                        if ($payOrderS->ret) {
                                            if (!empty($upOrderData['driver_id'])) {
                                                $driverR = $this->find(array('driver_id' => $yyLineClassTrainDriverR->data['driver_id']), 'Driver');
                                                if (!$driverR->ret) {
                                                    $this->transRollback();
                                                    $ret->data = '支付失败11';
                                                    return $this->output($ret);
                                                }
                                            }

                                            $this->commitTrans();
                                            \Log::write("支付成功处理成功！");
                                            //通知分台
                                            if ($upOrderData['appoint'] == \CommonDefine::APPOINT_TYPE_0) {
                                                $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                            } else {
                                                //通知司机
                                                $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                                                $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);

                                                if ($upOrderData['state'] == \CommonDefine::ORDER_STATE_2) {
                                                    //通知乘客
                                                    $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                    $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                }

                                                $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                            }
                                            $ret->ret = true;
                                            $ret->data = '支付成功';
                                            return $this->output($ret);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $this->transRollback();
                    $ret->data = '支付失败';
                    return $this->output($ret);
                } else {
                    $ret->data = '学生号组团订单异常，支付失败';
                    return $this->output($ret);
                }
            } else {
                $ret->data = '学生号订单异常，支付失败';
                return $this->output($ret);
            }
        } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_9) {
        } else {
            $ret->data = '订单类型错误，支付失败';
            return $this->output($ret);
        }

        $this->data['account_id'] = $orderR->data['passenger_id'];
        $this->data['account_type'] = 0;

        $payHistoryR = $this->find(array('order_no' => $orderNo, 'openid' => $openid), 'PayOrderHistory');
        if (!$payHistoryR->ret) {
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                $ret->data = '订单异常';
                return $this->output($ret);
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($orderR->data['mchid'], $channel)) {
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }
        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderNo, '代收车费', json_encode($extra), $amount, $this->getCipmchidByMchid($orderR->data['mchid'])->data['ciphertext'], $orderR->data['create_time']);
            if (!$charge) {
                $ret->data = '支付失败，请线下付款';
                return $this->output($ret);
            }

            if ($charge['return_code'] == 'SUCCESS') {
                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        } catch (\Exception $e) {
            \Log::write($e->getCode() . $e->getMessage());
            $ret->data = $e->getCode() . $e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请线下付款';
        return $this->output($ret);
    }

    /**
     * 微信支付异步通知
     *
     * @return void
     */
    public function doWxPayNotify()
    {
        $xml = file_get_contents('php://input');
        \Log::write("微信支付异步通知结果：" . $xml, \Log::DEBUG);

        $retData = xmlToArray($xml);
        if (empty($retData)) {
            \Log::write("微信支付异步通知结果错误：无法解析XML数据" . $xml);
            exit;
        }
        # 通过订单号获取订单业务类型
        if ($this->getOrderClass($retData['out_trade_no']) == \CommonDefine::ORDER_CLASS_2) {
            $orderCardR = $this->find(array('order_card_no' => $retData['out_trade_no'], 'is_pay' => \CommonDefine::PAY_STATUS_0), 'OrderCard');
            if (!$orderCardR->ret) {
                \Log::write("行号:" . __LINE__ . ",卡类订单-回调订单异常");
                exit;
            }

            $wxPayUtil = new \WxPayUtil();
            if (!$wxPayUtil->init($orderCardR->data['mchid'])) {
                \Log::write("行号:" . __LINE__ . ",卡类订单-商户支付配置异常:" . json_encode($orderCardR->data));
                exit;
            }
            $wxPayUtil->Handle(false, $wxPayUtil->getWxPayKey());
            $returnCode = $wxPayUtil->GetReturn_code();
            if ($returnCode == 'SUCCESS') {
                \Log::write("行号:" . __LINE__ . ",卡类订单:," . $retData['out_trade_no'] . "支付回调参数:" . json_encode($retData));
                $orderCardNo = $retData['out_trade_no'];
                $amount = $retData['cash_fee'];
                $payOrderCardR = $this->find(array('order_no' => $retData['out_trade_no'], 'status' => 0), 'PayOrderCardHistory', 'account_id,account_type');
                if (!$payOrderCardR->ret) {
                    \Log::write("行号:" . __LINE__ . ",卡类订单:" . $orderCardNo . "卡类订单");
                    exit;
                }

                $this->startTrans();
                $updateOrderCardData['order_card_id'] = $orderCardR->data['order_card_id'];
                $updateOrderCardData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                $orderCardS = $this->save('OrderCard', $updateOrderCardData);
                if ($orderCardS->ret) {
                    $this->updatePassengerCardInfo($orderCardR->data['passenger_id'], $orderCardR->data['times']);
                    $this->commitTrans();
                    \Log::write("行号:" . __LINE__ . ",卡类订单:," . $orderCardNo . "卡支付通知处理成功");
                    header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                    exit;
                } else {
                    $this->transRollback();
                    \Log::write("行号:" . __LINE__ . ",卡类订单:," . $orderCardNo . "卡支付通知处理失败");
                    exit;
                }
            } else {
                \Log::write("卡类订单-支付回调解析失败!");
                exit;
            }
        } elseif ($this->getOrderClass($retData['out_trade_no']) == \CommonDefine::ORDER_CLASS_3) {
            $dealR = $this->_dealYyWxParentHelpPriceWebHooks($retData);
            if ($dealR->ret) {
                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                exit;
            } else {
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                exit;
            }
        } elseif ($this->getOrderClass($retData['out_trade_no']) == \CommonDefine::ORDER_CLASS_4) {
            $dealR = $this->_dealYyWxStudentCustomizedWebHooks($retData);
            if ($dealR->ret) {
                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                exit;
            } else {
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                exit;
            }
        } elseif ($this->getOrderClass($retData['out_trade_no']) == \CommonDefine::ORDER_CLASS_5) {
            $orderR = $this->find(array('order_no' => $retData['out_trade_no'], 'is_pay' => 0), 'Order');
            if (!$orderR->ret) {
                \Log::write("行号:" . __LINE__ . ",订单:," . $retData['out_trade_no'] . "订单异常!" . M()->getLastSql());
                exit;
            }
            $dealR = $this->_dealMergeOrderWebHooks($retData);
            if ($dealR->ret) {
                //乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票
                httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$orderR->data['order_id']}/issued/tickets", 'post');
                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                exit;
            } else {
                //支付失败，车票退还
                httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$orderR->data['order_id']}/issued/tickets", 'delete');
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                exit;
            }
        } else {
            # 普通订单类型
            $tempOrderR = $this->find(array('order_no' => $retData['out_trade_no']), 'Order');
            if (!$tempOrderR->ret) {
                \Log::write("微信异步通知处理失败：行号:" . __LINE__ . "，系统未查询到订单:" . $retData['out_trade_no']);
                exit;
            }

            $wxPayUtil = new \WxPayUtil();
            if (!$wxPayUtil->init($tempOrderR->data['mchid'])) {
                \Log::write("微信异步通知处理失败，行号:" . __LINE__ . "，订单:" . $retData['out_trade_no'] . "商户支付配置异常:" . json_encode($tempOrderR->data));
                exit;
            }

            $wxPayUtil->Handle(false, $wxPayUtil->getWxPayKey());
            $returnCode = $wxPayUtil->GetReturn_code();
            $event_type = 'charge.succeeded';
            $result = [];
            if ($returnCode == 'SUCCESS') {
                $result = \WxPayResults::Init($xml, $wxPayUtil->getWxPayKey());
                $event_type = 'charge.succeeded';
            } else {
                \Log::write("订单号：" . $retData['out_trade_no'] . "微信支付异步通知参数解析失败!");
                exit;
            }

            switch ($event_type) {
                case "charge.succeeded":
                    $orderNo = $result['out_trade_no'];
                    $openId = $retData['openid'];
                    # 查询支付历史记录
                    $payR = $this->find(array('order_no' => $orderNo, 'status' => 0, 'openid' => $openId), 'PayOrderHistory', 'account_id,account_type');
                    if (!$payR->ret) {
                        \Log::write("微信异步通知处理失败，行号:" . __LINE__ . "，订单:" . $orderNo . " 已经异步通知已经处理成功，无需继续处理，查询SQL：" . M()->getLastSql());
                        exit;
                    }
                    # 查询订单支付状态
                    $orderR = $this->find(array('order_no' => $orderNo, 'is_pay' => 0), 'Order');
                    if (!$orderR->ret) {
                        \Log::write("微信异步通知处理失败行号:" . __LINE__ . "，订单:" . $orderNo . "已经异步通知处理成功，无需继续处理，查询SQL：" . M()->getLastSql());
                        exit;
                    }

                    $this->startTrans();
                    if ($payR->data['account_type'] == 0) { //乘客
                        switch ($orderR->data['type']) {
                            case \CommonDefine::ORDER_TYPE_1:
                            case \CommonDefine::ORDER_TYPE_2:
                            case \CommonDefine::ORDER_TYPE_3:
                            case \CommonDefine::ORDER_TYPE_4: {
                                $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                                    $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                }

                                //支付成功,更新订单状态,更新账户余额
                                $orderS = $this->save('Order', $orderUpdataData);
                                if ($orderS->ret) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                                        //添加到司机余额中去
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        if (!$driverR->ret) {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---3---");
                                            exit;
                                        }
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if ($bookR->ret) {
                                                $this->commitTrans();
                                                //计入用户积分
                                                if ($orderUpdataData['state'] == \CommonDefine::ORDER_STATE_6) {
                                                    $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                                }
                                                \Log::write("支付通知处理成功！");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---4---");
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---5---");
                                            exit;
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！");
                                                //通知服务
                                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                                if (!$passengerR->ret) {
                                                    \Log::write($orderNo . " ---6---");
                                                    exit;
                                                }
                                                if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                                                    $driversR = $this->getDriversByLineId($orderR->data['mchid'], \CommonDefine::ORDER_TYPE_1, $orderR->data['line_id']);
                                                    //分台下所有司机
                                                    //$driverR = $this->select(array('branchid' => $data->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone,openid');
                                                    if ($driversR) {
                                                        $driverCellphoneArr = array();
                                                        $driverWeixinArr = array();
                                                        foreach ($driversR->data as $driverData) {
                                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                                        }
                                                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $this->encryptionCellphoneByCellphone($this->data['reseverd_phone'])), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $data->data['mchid']);

                                                        //您有新的[拼车]订单了
                                                        //乘客姓名：张三
                                                        //乘客人数：3人
                                                        //联系电话：1333333333
                                                        //预定线路：武汉-北京
                                                        //上车时间：2016/8/8 18:30
                                                        //请尽快与乘客取得联系
                                                        $temp = array(
                                                            '有新的[' . $this->getOrderType($this->data['type']) . ']订单了',
                                                            $passengerR->data['name'],
                                                            $orderR->data['book_seating'],
                                                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                                            $orderR->data['start_name'] . " - " . $orderR->data['end_name'],
                                                            $orderR->data['start_time'],
                                                            '请及时抢单! 点击前往抢单'
                                                        );
                                                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                                    }
                                                } else {
                                                    $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                                }

                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---7---");
                                                exit;
                                            }
                                        }
                                    }
                                }
                                //通知司机支付成功

                                break;
                            }
                            case \CommonDefine::ORDER_TYPE_5: {
                                # 定制班线车
                                $payOverUpdateOrderR = $this->_payOverUpdateClassOrder($orderR, $xml);
                                \Log::write("行号:" . __LINE__ . ",订单:," . $orderNo . "，订单更新：" . $payOverUpdateOrderR->data);
                                $this->commitTrans();
                                //乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票
                                httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$orderR->data['order_id']}/issued/tickets", 'post');
                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                exit;
                            }
                            case \CommonDefine::ORDER_TYPE_6: {
                                $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5) {
                                    $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                }

                                //支付成功,更新订单状态,更新账户余额
                                $orderS = $this->save('Order', $orderUpdataData);
                                if ($orderS->ret) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                                        //添加到司机余额中去
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        if (!$driverR->ret) {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---22---");
                                            exit;
                                        }

                                        if (empty($orderR->data['branchid'])) {
                                            $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'branchid' => $driverR->data['branchid']));
                                            if (!$orderS->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---23---");
                                                exit;
                                            }
                                        }

                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {

                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if ($bookR->ret) {
                                                $this->commitTrans();
                                                //计入用户积分
                                                if ($orderUpdataData['state'] == \CommonDefine::ORDER_STATE_6) {
                                                    $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                                }
                                                \Log::write("支付通知处理成功！");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---24---");
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---25---");
                                            exit;
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookkeepingMchOnlyOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnlyOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['order_id']);
                                            if ($bookkeepingMchOnlyOnTotalTurnoverOnAmountR->ret) {
                                                if (!empty($orderR->data['line_id'])) {
                                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                                        $bookkeepingBranchOnlyOnTotalTurnoverOnAmount = $this->bookkeepingBranchOnlyOnTotalTurnoverOnAmount($orderR->data['branchid'], $orderR->data['order_id']);
                                                        if (!$bookkeepingBranchOnlyOnTotalTurnoverOnAmount->ret) {
                                                            $this->transRollback();
                                                            \Log::write($orderNo . " ---26---");
                                                            return $this->output(new \ResultModel(false, '订单异常'));
                                                        }
                                                    }
                                                }
                                                if (!empty($orderR->data['line_id'])) {
                                                    $lineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                                                    $residual_seating = $lineFreeRideR->data['residual_seating'] - $orderR->data['book_seating'];
                                                    $residual_seating = $residual_seating > 0 ? $residual_seating : 0;
                                                    $lineFreeRideS = $this->save('LineFreeRide', array('id' => $orderR->data['line_id'], 'residual_seating' => $residual_seating));
                                                    if (!$lineFreeRideS->ret) {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---27---");
                                                        return $this->output(new \ResultModel(false, '下单失败'));
                                                    }
                                                }

                                                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                                //通知服务,通知司机
                                                if (C('SMS_ON')) {
                                                    //2025.2.17 新增新订单司机短信通知
                                                    $this->sendInnerNotification('driver', 'sms', 'sms_driver_new_order', $orderR->data['order_id']);
                                                }

                                                if (C('WX_TEMPLATE_ON')) {
                                                    //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                                                    //您有新的订单了
                                                    //订单编号：C1510170029132
                                                    //时间：业务经理
                                                    //有问题请拨************
                                                    $temp = array(
                                                        '您有新的[' . $this->getOrderType($this->data['type']) . ']订单了',
                                                        $orderR->data['order_no'],
                                                        $orderR->data['start_time'],
                                                        '乘客电话:' . $passengerR->data['cellphone'] . '请及时处理!'
                                                    );
                                                    $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_5);
                                                    unset($temp);
                                                }

                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---28---");
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---29---");
                                            exit;
                                        }
                                    }
                                }
                                break;
                            }
                            case \CommonDefine::ORDER_TYPE_7: { //快车
                                $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                                    $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                }
                                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                    //                                    //附近司机
                                    //                                    $aroundDriversR = $this->getLineFastAroundDrivers($orderR->data['line_id'], $orderR->data['start_longitude'], $orderR->data['start_latitude'], $orderR->data['book_seating']);
                                    //                                    if($aroundDriversR->ret){
                                    //                                        $orderUpdataData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                                    //                                        $orderUpdataData['driver_id'] = $aroundDriversR->data[0]['driver_id'];
                                    //                                    }
                                }

                                //支付成功,更新订单状态,更新账户余额
                                $orderS = $this->save('Order', $orderUpdataData);
                                if ($orderS->ret) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                                        //添加到司机余额中去
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        if (!$driverR->ret) {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---3---");
                                            exit;
                                        }
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if ($bookR->ret) {
                                                $this->commitTrans();
                                                //计入用户积分
                                                if ($orderUpdataData['state'] == \CommonDefine::ORDER_STATE_6) {
                                                    $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                                }
                                                \Log::write("支付通知处理成功！");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---4---");
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---5---");
                                            exit;
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！");
                                                //通知服务
                                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                                if (!$passengerR->ret) {
                                                    \Log::write($orderNo . " ---6---");
                                                    exit;
                                                }
                                                if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                                                    $driversR = $this->getDriversByLineId($orderR->data['mchid'], \CommonDefine::ORDER_TYPE_1, $orderR->data['line_id']);
                                                    //分台下所有司机
                                                    //$driverR = $this->select(array('branchid' => $data->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone,openid');
                                                    if ($driversR) {
                                                        $driverCellphoneArr = array();
                                                        $driverWeixinArr = array();
                                                        foreach ($driversR->data as $driverData) {
                                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                                        }
                                                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $this->encryptionCellphoneByCellphone($this->data['reseverd_phone'])), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $data->data['mchid']);

                                                        //您有新的[拼车]订单了
                                                        //乘客姓名：张三
                                                        //乘客人数：3人
                                                        //联系电话：1333333333
                                                        //预定线路：武汉-北京
                                                        //上车时间：2016/8/8 18:30
                                                        //请尽快与乘客取得联系
                                                        $temp = array(
                                                            '有新的[' . $this->getOrderType($this->data['type']) . ']订单了',
                                                            $passengerR->data['name'],
                                                            $orderR->data['book_seating'],
                                                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                                            $orderR->data['start_name'] . " - " . $orderR->data['end_name'],
                                                            $orderR->data['start_time'],
                                                            '请及时抢单! 点击前往抢单'
                                                        );
                                                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                                    }
                                                } else {
                                                    if (empty($orderUpdataData['driver_id'])) { //附近没有司机直接通知分台
                                                        $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                                    } else {
                                                        $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $orderUpdataData['driver_id']);
                                                        $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $orderUpdataData['driver_id']);
                                                    }
                                                }

                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                break;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---7---");
                                                exit;
                                            }
                                        }
                                    }
                                }
                                //通知司机支付成功

                                break;
                            }
                            case \CommonDefine::ORDER_TYPE_8: {
                                if ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_1) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //后支付模式
                                        $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                        $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                                            $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                        }
                                        //支付成功,更新订单状态,更新账户余额
                                        $orderS = $this->save('Order', $orderUpdataData);
                                        if ($orderS->ret) {
                                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                            if (!$driverR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---8.1---");
                                                exit;
                                            }
                                            $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                            if ($payOrderS->ret) {
                                                if ($orderR->data['state'] < 5) {
                                                    $bookR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                                    if (!$bookR->ret) {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---9.1---");
                                                        exit;
                                                    }
                                                } elseif ($orderR->data['state'] == 5) {
                                                    $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                                    if (!$bookR->ret) {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---10.1---");
                                                        exit;
                                                    }
                                                }
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---11.1---");
                                                exit;
                                            }
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
                                        //余票扣除
                                        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'YyLineClassTrain');
                                        if ($yyLineClassTrainR->ret) {
                                            if ($yyLineClassTrainR->data['remain_tickets'] >= $orderR->data['book_seating']) {
                                                //出票成功
                                                $upYyLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
                                                $upYyLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] - $orderR->data['book_seating'];
                                                $yyLineClassTrainS = $this->save('YyLineClassTrain', $upYyLineClassTrainData);
                                                if ($yyLineClassTrainS->ret) {
                                                    $upOrderData['order_id'] = $orderR->data['order_id'];
                                                    $upOrderData['is_pay'] = 1;
                                                    $yyLineClassTrainDriverR = $this->getYyLineClassTrainDriver($orderR->data['line_id'], $orderR->data['book_seating']);
                                                    if (!$yyLineClassTrainDriverR->ret) {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---12.1---");
                                                        exit;
                                                    }

                                                    //班线车订单模式，默认自动派单
                                                    $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
                                                    $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $orderR->data['mchid']), 'SystemConfig');
                                                    if ($lineClassAppointConfigR->ret) {
                                                        $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
                                                    }

                                                    $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_1;
                                                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                                                        $upOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                                                        $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                                                        $upOrderData['driver_id'] = $yyLineClassTrainDriverR->data['driver_id'];
                                                    } elseif ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1) {
                                                        $upOrderData['state'] = \CommonDefine::ORDER_STATE_1;
                                                        $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                                                    }

                                                    $orderS = $this->save('Order', $upOrderData);
                                                    if ($orderS->ret) {
                                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                                        if ($payOrderS->ret) {
                                                            if (!empty($upOrderData['driver_id'])) {
                                                                $driverR = $this->find(array('driver_id' => $yyLineClassTrainDriverR->data['driver_id']), 'Driver');
                                                                if (!$driverR->ret) {
                                                                    $this->transRollback();
                                                                    \Log::write($orderNo . " ---13.1---");
                                                                    exit;
                                                                }
                                                            }

                                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                                            if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                                $this->commitTrans();
                                                                \Log::write("支付通知处理成功！");
                                                                //通知分台
                                                                if ($upOrderData['appoint'] == \CommonDefine::APPOINT_TYPE_0) {
                                                                    $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                                                } else {
                                                                    //通知司机
                                                                    $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                                                                    $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);

                                                                    if ($upOrderData['state'] == \CommonDefine::ORDER_STATE_2) {
                                                                        //通知乘客
                                                                        $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                                        $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                                    }

                                                                    $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                                                }
                                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                                exit;
                                                            } else {
                                                                $this->transRollback();
                                                                \Log::write($orderNo . " ---14.1---");
                                                                exit;
                                                            }
                                                        } else {
                                                            $this->transRollback();
                                                            \Log::write($orderNo . " ---15.1---");
                                                            exit;
                                                        }
                                                    } else {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---16.1---");
                                                        exit;
                                                    }
                                                } else {
                                                    $this->transRollback();
                                                    \Log::write($orderNo . " ---17.1---");
                                                    exit;
                                                }
                                            } else {
                                                //余票不足，退款
                                                $where['order_id'] = $orderR->data['order_id'];
                                                $where['account_id'] = $orderR->data['passenger_id'];
                                                $where['account_type'] = 1;
                                                $rfR = $this->find($where, 'Refunds');
                                                if ($rfR->ret) {
                                                    $this->transRollback();
                                                    //'操作失败，重复申请退款!'
                                                    \Log::write($orderNo . " ---18.1---");
                                                    exit;
                                                } else {
                                                    $wxPayUtil = new \WxPayUtil();
                                                    if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                                        $this->transRollback();
                                                        \Log::write("---18.5.1---商户支付配置异常:" . json_encode($orderR->data));
                                                        exit;
                                                    }
                                                    $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                                    if (!$refundR->ret) {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---19.1---");
                                                        exit;
                                                    }

                                                    if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---20.1---");
                                                        exit;
                                                    }

                                                    $refundData['amount'] = $orderR->data['price'];
                                                    $refundData['status'] = 3;
                                                    $refundData['order_id'] = $orderR->data['order_id'];
                                                    $refundData['account_type'] = 1; //乘客
                                                    $refundData['account_id'] = $orderR->data['passenger_id'];
                                                    $refundData['refund_id'] = $refundR->data['refund_id'];
                                                    //   $refundData['object'] = $refundR->data['object'];
                                                    $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                                    $refundData['created'] = time();
                                                    //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                                                    /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                                                    $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                                                    $refundData['description'] = "余票不足，系统已自动取消订单，并全额退款，请选择其他线路重新下单";
                                                    $refundData['charge'] = $refundR->data['charge'];
                                                    $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                                    $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                                    $reR = $this->add('Refunds', $refundData);
                                                    if ($reR->ret) {
                                                        $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'state' => 7, 'is_pay' => 1, 'is_draw' => \CommonDefine::DRAW_TICKET_3));
                                                        if ($orderS->ret) {
                                                            $this->commitTrans();
                                                            \Log::write("支付通知处理成功！");
                                                            //'退款中，退款将于3个工作日内到账!'
                                                            header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                            exit;
                                                        }
                                                    } else {
                                                        $this->transRollback();
                                                        \Log::write($orderNo . " ---21.1---");
                                                        exit;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                } elseif ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_2) { //组团拼车

                                }
                                break;
                            }
                            case \CommonDefine::ORDER_TYPE_9: {
                                $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5) {
                                    $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                }

                                //支付成功,更新订单状态,更新账户余额
                                $orderS = $this->save('Order', $orderUpdataData);
                                if ($orderS->ret) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                                        //添加到司机余额中去
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        if (!$driverR->ret) {
                                            $this->transRollback();
                                            exit;
                                        }

                                        if (empty($orderR->data['branchid'])) {
                                            $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'branchid' => $driverR->data['branchid']));
                                            if (!$orderS->ret) {
                                                $this->transRollback();
                                                exit;
                                            }
                                        }

                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {

                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if ($bookR->ret) {
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            exit;
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookkeepingMchOnlyOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnlyOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['order_id']);
                                            if ($bookkeepingMchOnlyOnTotalTurnoverOnAmountR->ret) {
                                                if (!empty($orderR->data['line_id'])) {
                                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                                        $bookkeepingBranchOnlyOnTotalTurnoverOnAmount = $this->bookkeepingBranchOnlyOnTotalTurnoverOnAmount($orderR->data['branchid'], $orderR->data['order_id']);
                                                        if (!$bookkeepingBranchOnlyOnTotalTurnoverOnAmount->ret) {
                                                            $this->transRollback();
                                                            exit;
                                                        }
                                                    }
                                                }
                                                if (!empty($orderR->data['line_id'])) {
                                                    $lineParentHelpsR = $this->find(array('id' => $orderR->data['line_id']), 'YyLineParentHelp');
                                                    $residual_seating = $lineParentHelpsR->data['residual_seating'] - $orderR->data['book_seating'];
                                                    $residual_seating = $residual_seating > 0 ? $residual_seating : 0;
                                                    $lineParentHelpsS = $this->save('YyLineParentHelp', array('id' => $orderR->data['line_id'], 'residual_seating' => $residual_seating));
                                                    if (!$lineParentHelpsS->ret) {
                                                        $this->transRollback();
                                                        exit;
                                                    }
                                                }

                                                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                                //通知服务,通知司机
                                                if (C('SMS_ON')) {
                                                    //2025.2.17 新增新订单司机短信通知
                                                    $this->sendInnerNotification('driver', 'sms', 'sms_driver_new_order', $orderR->data['order_id']);
                                                }

                                                if (C('WX_TEMPLATE_ON')) {
                                                    //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                                                    //您有新的订单了
                                                    //订单编号：C1510170029132
                                                    //时间：业务经理
                                                    //有问题请拨************
                                                    $temp = array(
                                                        '您有新的[' . $this->getOrderType($this->data['type']) . ']订单了',
                                                        $orderR->data['order_no'],
                                                        $orderR->data['start_time'],
                                                        '乘客电话:' . $passengerR->data['cellphone'] . '请及时处理!'
                                                    );
                                                    $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_5);
                                                    unset($temp);
                                                }

                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            exit;
                                        }
                                    }
                                }
                                break;
                            }
                            case \CommonDefine::ORDER_TYPE_11: { //出租车
                                $orderUpdataData['order_id'] = $orderR->data['order_id'];
                                $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
                                if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                                    $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
                                }
                                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                    //附近司机
                                    $aroundDriversR = $this->getLineFastAroundDrivers($orderR->data['line_id'], $orderR->data['start_longitude'], $orderR->data['start_latitude'], $orderR->data['book_seating']);
                                    if ($aroundDriversR->ret) {
                                        $orderUpdataData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                                        $orderUpdataData['driver_id'] = $aroundDriversR->data[0]['driver_id'];
                                    }
                                }

                                //支付成功,更新订单状态,更新账户余额
                                $orderS = $this->save('Order', $orderUpdataData);
                                if ($orderS->ret) {
                                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                                        //添加到司机余额中去
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        if (!$driverR->ret) {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---3---");
                                            exit;
                                        }
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if ($bookR->ret) {
                                                $this->commitTrans();
                                                //计入用户积分
                                                if ($orderUpdataData['state'] == \CommonDefine::ORDER_STATE_6) {
                                                    $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                                }
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---4---");
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---5---");
                                            exit;
                                        }
                                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                //通知服务
                                                $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                                if (!$passengerR->ret) {
                                                    \Log::write($orderNo . " ---6---");
                                                    exit;
                                                }
                                                if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                                                    $driversR = $this->getDriversByLineId($orderR->data['mchid'], \CommonDefine::ORDER_TYPE_1, $orderR->data['line_id']);
                                                    //分台下所有司机
                                                    //$driverR = $this->select(array('branchid' => $data->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone,openid');
                                                    if ($driversR) {
                                                        $driverCellphoneArr = array();
                                                        $driverWeixinArr = array();
                                                        foreach ($driversR->data as $driverData) {
                                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                                        }
                                                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $this->encryptionCellphoneByCellphone($this->data['reseverd_phone'])), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $data->data['mchid']);

                                                        //您有新的[拼车]订单了
                                                        //乘客姓名：张三
                                                        //乘客人数：3人
                                                        //联系电话：1333333333
                                                        //预定线路：武汉-北京
                                                        //上车时间：2016/8/8 18:30
                                                        //请尽快与乘客取得联系
                                                        $temp = array(
                                                            '有新的[' . $this->getOrderType($this->data['type']) . ']订单了',
                                                            $passengerR->data['name'],
                                                            $orderR->data['book_seating'],
                                                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                                            $orderR->data['start_name'] . " - " . $orderR->data['end_name'],
                                                            $orderR->data['start_time'],
                                                            '请及时抢单! 点击前往抢单'
                                                        );
                                                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                                    }
                                                } else {
                                                    if (empty($orderUpdataData['driver_id'])) { //附近没有司机直接通知分台
                                                        $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                                    } else {
                                                        $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $orderUpdataData['driver_id']);
                                                        $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $orderUpdataData['driver_id']);
                                                    }
                                                }

                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                break;
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---7---");
                                                exit;
                                            }
                                        }
                                    }
                                }
                                //通知司机支付成功

                                break;
                            }
                        }
                    } elseif ($payR->data['account_type'] == 1) { //司机
                    } elseif ($payR->data['account_type'] == 2) { //分台
                        switch ($orderR->data['type']) {
                            case \CommonDefine::ORDER_TYPE_1:
                            case \CommonDefine::ORDER_TYPE_2:
                            case \CommonDefine::ORDER_TYPE_3:
                            case \CommonDefine::ORDER_TYPE_4: {
                                if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) { //渠道代约
                                    //支付成功,更新订单状态,更新账户余额
                                    $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'is_pre_pay' => 1));
                                    if ($orderS->ret) {
                                        //添加到分台和总台余额中去
                                        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                        if ($payOrderS->ret) {
                                            //先计入流水，不计入余额
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if (!$bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---30---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                        }

                                        $this->commitTrans();
                                        \Log::write("支付通知处理成功！", \Log::DEBUG);
                                        $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                        header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                        exit;
                                    }
                                } else {
                                    //支付成功,更新订单状态,更新账户余额
                                    $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'is_pre_pay' => 1));
                                    if ($orderS->ret) {
                                        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5) {
                                            $upOrderData['state'] = \CommonDefine::ORDER_STATE_6;
                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['driver_id'], $orderR->data['order_id']);
                                            if (!$bookR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---31---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                        } else {
                                            //先计入流水，不计入余额
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if (!$bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---32---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                        }

                                        $this->commitTrans();
                                        \Log::write("支付通知处理成功！", \Log::DEBUG);
                                        $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                                        header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                        exit;
                                    }
                                }
                            }
                            case \CommonDefine::ORDER_TYPE_5: {
                                if ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_1) { //渠道代约
                                    //余票扣除
                                    $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
                                    //                                    $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                    if (!empty($lineClassTrainArr)) {
                                        if ($lineClassTrainArr['remain_tickets'] >= $orderR->data['book_seating']) {
                                            //出票成功
                                            $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
                                            $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] - $orderR->data['book_seating'];
                                            $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
                                            if ($lineClassTrainS->ret) {
                                                $lineClassTrainDriverR = $this->getLineClassTrainDriver($lineClassTrainArr['line_class_train_id'], $orderR->data['book_seating']);
                                                if (!$lineClassTrainDriverR->ret) {
                                                    $this->transRollback();
                                                    \Log::write($orderNo . " ---33---");
                                                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                    exit;
                                                }

                                                //班线车订单模式，默认自动派单
                                                $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
                                                $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $this->mchid), 'SystemConfig');
                                                if ($lineClassAppointConfigR->ret) {
                                                    $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
                                                }

                                                $upOrderData['order_id'] = $orderR->data['order_id'];
                                                $upOrderData['is_pay'] = 1;
                                                $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_2;

                                                if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                                                    $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                                                    $upOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                                                    $upOrderData['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                                                }

                                                $orderS = $this->save('Order', $upOrderData);
                                                if ($orderS->ret) {
                                                    $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                                    if ($payOrderS->ret) {
                                                        //先计入流水，不计入余额
                                                        $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                                        if (!$bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                            $this->transRollback();
                                                            \Log::write($orderNo . " ---34---");
                                                            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                            exit;
                                                        }

                                                        $this->commitTrans();
                                                        \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                        $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);

                                                        if ($upOrderData['appoint'] == \CommonDefine::APPOINT_TYPE_1) {
                                                            $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                                                            $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                                                            if ($this->data['state'] == \CommonDefine::ORDER_STATE_2) {
                                                                //通知乘客
                                                                $this->sendSms($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                                $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                                            }
                                                        }
                                                        header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                        exit;
                                                    }
                                                } else {
                                                    $this->transRollback();
                                                    \Log::write($orderNo . " ---35---");
                                                    exit;
                                                }
                                            } else {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---36---");
                                                exit;
                                            }
                                        } else {
                                            $wxPayUtil = new \WxPayUtil();
                                            if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                                $this->transRollback();
                                                \Log::write("---37.5---商户支付配置异常:" . json_encode($orderR->data));
                                                exit;
                                            }
                                            $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                            if (!$refundR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---37---");
                                                return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                                            }

                                            $refundData['amount'] = $orderR->data['price'];
                                            $refundData['status'] = 3;
                                            $refundData['order_id'] = $orderR->data['order_id'];
                                            $refundData['account_type'] = 2; //分台
                                            $refundData['account_id'] = $orderR->data['temp_apply_branchid'];
                                            $refundData['refund_id'] = $refundR->data['refund_id'];
                                            $refundData['object'] = $refundR->data['object'];
                                            $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                            $refundData['created'] = time();
                                            $refundData['description'] = "出票失败，全额退款";
                                            $refundData['charge'] = $refundR->data['charge'];
                                            $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                            $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                            $reR = $this->add('Refunds', $refundData);
                                            if ($reR->ret) {
                                                $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'state' => 7, 'is_pay' => 1, 'is_draw' => \CommonDefine::DRAW_TICKET_3));
                                                if ($orderS->ret) {
                                                    $this->commitTrans();
                                                    \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                    //'退款中，退款将于3个工作日内到账!'
                                                    header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    break;
                                } else { //普通预约
                                    //余票扣除
                                    $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                                    if ($lineClassTrainR->ret) {
                                        $upOrderData['order_id'] = $orderR->data['order_id'];
                                        $upOrderData['is_pay'] = 1;

                                        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5) {
                                            $upOrderData['state'] = \CommonDefine::ORDER_STATE_6;
                                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver', 'balance,branchid,mchid,cellphone,openid');
                                            if (!$driverR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---38---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                            $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                            if (!$bookR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---39---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                        } else {
                                            //先计入流水，不计入余额
                                            $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                                            if (!$bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                                                $this->transRollback();
                                                \Log::write($orderNo . " ---40---");
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                                exit;
                                            }
                                        }

                                        $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_2;
                                        $orderS = $this->save('Order', $upOrderData);
                                        if ($orderS->ret) {
                                            $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderNo, 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                                            if ($payOrderS->ret) {
                                                $this->commitTrans();
                                                \Log::write("支付通知处理成功！", \Log::DEBUG);
                                                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                                                exit;
                                            }
                                        } else {
                                            $this->transRollback();
                                            \Log::write($orderNo . " ---41---");
                                            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                                            exit;
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                    $this->transRollback();
                    \Log::write(" ---42---");
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    exit;
                default:
                    \Log::write("行号:" . __LINE__ . ",订单:," . $retData['out_trade_no'] . "支付回调解析失败!");
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    break;
            }
        }
    }
    public function doWxRefundNotify()
    {
        $xml = file_get_contents('php://input');
        \Log::write("退款通知：" . $xml);
        $retData = xmlToArray($xml);
        if (empty($retData)) {
            \Log::write("自定义解析失败!");
        }
        $tempOrderR = $this->find(array('order_no' => $retData['out_trade_no']), 'Order');
        if (!$tempOrderR->ret) {
            \Log::write("回调订单异常");
            exit;
        }
        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($tempOrderR->data['mchid'])) {
            \Log::write("doWxRefundNotify商户支付配置异常:" . json_encode($tempOrderR->data));
            exit;
        }
        $wxPayUtil->Handle(false, $wxPayUtil->getWxPayKey());
        $returnCode = $wxPayUtil->GetReturn_code();

        $event_type = 'refund.succeeded';
        $result = [];
        if ($returnCode == 'SUCCESS') {
            $result = \WxPayResults::Init($xml, $wxPayUtil->getWxPayKey());
            \Log::write("values:" . json_encode($result));
        }

        switch ($event_type) {
            default:
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                break;
        }
        exit;
    }

    /**
     * 学生号-套餐卡订单支付
     * @param string $order_card_no 订单编号（必须）
     * @param string $openid 用户openid（必须）
     * @param double $amount 支付金额（必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     */
    public function doWxOrderCardPay($order_card_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderCardNo = $this->data['order_card_no'];

        $orderCardR = $this->find(array('order_card_no' => $orderCardNo, 'is_pay' => 0), 'OrderCard');
        if (!$orderCardR->ret) {
            $ret->data = '订单不存在';
            return $this->output($ret);
        }

        $passengerR = $this->find(array('passenger_id' => $orderCardR->data['passenger_id']), 'Passenger');
        if (!$passengerR->ret) {
            $ret->data = '用户信息错误';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($orderCardR->data['mchid']);
        if ($payServiceR->ret) {
            if (empty($passengerR->data['openid'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid'];
        } else {
            if (empty($passengerR->data['openid_pay'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid_pay'];
        }

        if ($orderCardR->data['price'] * 100 != $amount * 100) {
            $ret->data = '订单金额错误';
            return $this->output($ret);
        }

        $this->data['account_id'] = $orderCardR->data['passenger_id'];
        $this->data['account_type'] = 0;
        $this->data['channel'] = $channel;

        $payHistoryR = $this->find(array('order_no' => $orderCardNo, 'openid' => $openid), 'PayOrderCardHistory');
        if (!$payHistoryR->ret) {
            $this->data['openid'] = $openid;
            $payHistoryS = $this->add('PayOrderCardHistory', $this->data);
            if (!$payHistoryS->ret) {
                $ret->data = '订单异常';
                return $this->output($ret);
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($orderCardR->data['mchid'], $channel)) {
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }
        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderCardNo, '代收次卡费用', json_encode($extra), $amount, $this->getCipmchidByMchid($orderCardR->data['mchid'])->data['ciphertext']);
            if (!$charge) {
                $ret->data = '支付失败，请稍后重试';
                return $this->output($ret);
            }

            if ($charge['return_code'] == 'SUCCESS') {
                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        } catch (\Exception $e) {
            \Log::write($e->getCode() . $e->getMessage());
            $ret->data = $e->getCode() . $e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请稍后重试';
        return $this->output($ret);
    }

    /**
     * 模拟登陆
     * @param string $cellphone 手机号码（非必须，默认***********）
     */
    public function doTestLogin($cellphone = '***********')
    {

        $passengerR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay,name');

        $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
        $userInfoArr = $thirdR->data;

        \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']), $this->mchid);
        return $this->output($passengerR);
    }

    /**
     * 处理云裕众-家长互助微信回调通知
     * @param $retData
     */
    private function _dealYyWxParentHelpPriceWebHooks($retData)
    {
        $yyOrderParentHelpPriceR = $this->find(array('order_parent_help_price_no' => $retData['out_trade_no'], 'is_pay' => \CommonDefine::PAY_STATUS_0), 'YyOrderParentHelpPrice');
        if (!$yyOrderParentHelpPriceR->ret) {
            \Log::write("家长互助订单-回调订单异常");
            return new \ResultModel(false);
        }

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($yyOrderParentHelpPriceR->data['mchid'])) {
            \Log::write("家长互助订单-商户支付配置异常:" . json_encode($yyOrderParentHelpPriceR->data));
            return new \ResultModel(false);
        }
        $wxPayUtil->Handle(false, $wxPayUtil->getWxPayKey());
        $returnCode = $wxPayUtil->GetReturn_code();
        if ($returnCode == 'SUCCESS') {
            \Log::write("家长互助订单-支付回调参数:" . json_encode($retData));
            $orderParentHelpPriceNo = $retData['out_trade_no'];
            $amount = $retData['cash_fee'];
            $payHistoryR = $this->find(array('order_parent_help_price_no' => $orderParentHelpPriceNo, 'status' => 0), 'YyPayOrderParentHelpPriceHistory', 'account_id,account_type');
            if (!$payHistoryR->ret) {
                \Log::write($orderParentHelpPriceNo . "家长互助订单 ---1---");
                return new \ResultModel(false);
            }

            $this->startTrans();
            $updateOrderData['order_parent_help_price_id'] = $yyOrderParentHelpPriceR->data['order_parent_help_price_id'];
            $updateOrderData['is_pay'] = \CommonDefine::PAY_STATUS_1;
            $orderS = $this->save('YyOrderParentHelpPrice', $updateOrderData);
            if ($orderS->ret) {
                $this->updateDriverParentHelpInfo($yyOrderParentHelpPriceR->data['driver_id'], $yyOrderParentHelpPriceR->data['parent_help_price_id']);
                $this->commitTrans();

                \Log::write("家长互助订单通知处理成功！");
                return new \ResultModel(true);
            } else {
                $this->transRollback();
                return new \ResultModel(false);
            }
        } else {
            \Log::write("家长互助订单-支付回调解析失败!");
            return new \ResultModel(false);
        }
    }

    /**
     * 处理云裕众-组团拼车回调通知
     * @param $retData
     */
    private function _dealYyWxStudentCustomizedWebHooks($retData)
    {
        $yyStudentCustomizedOrderR = $this->find(array('student_customized_order_no' => $retData['out_trade_no'], 'is_pay' => \CommonDefine::PAY_STATUS_0), 'YyStudentCustomizedOrder');
        if (!$yyStudentCustomizedOrderR->ret) {
            \Log::write("组团定制包车-回调订单异常");
            return new \ResultModel(false);
        }

        //判定线路状态
        $lineWhere['student_customized_line_id'] = $yyStudentCustomizedOrderR->data['student_customized_line_id'];
        $lineWhere['status'] = ['in', [\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_1, \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2]];
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if ($studentCustomizedLineR->ret) {
        } else { //退款
            \Log::write("组团定制包车-订单异常");
            return new \ResultModel(false);
        }

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($yyStudentCustomizedOrderR->data['mchid'])) {
            \Log::write("组团定制包车-商户支付配置异常:" . json_encode($yyStudentCustomizedOrderR->data));
            return new \ResultModel(false);
        }

        $wxPayUtil->Handle(false, $wxPayUtil->getWxPayKey());
        $returnCode = $wxPayUtil->GetReturn_code();
        if ($returnCode == 'SUCCESS') {
            \Log::write("组团定制包车-支付回调参数:" . json_encode($retData));
            $studentCustomizedOrderNo = $retData['out_trade_no'];
            $amount = $retData['cash_fee'];
            $payHistoryR = $this->find(array('order_no' => $studentCustomizedOrderNo, 'status' => 0), 'PayOrderHistory', 'account_id,account_type');
            if (!$payHistoryR->ret) {
                \Log::write($studentCustomizedOrderNo . "组团定制包车 ---1---");
                return new \ResultModel(false);
            }

            $this->startTrans();
            $updateOrderData['student_customized_order_id'] = $yyStudentCustomizedOrderR->data['student_customized_order_id'];
            $updateOrderData['is_pay'] = \CommonDefine::PAY_STATUS_1;
            $updateOrderData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2;
            $studentCustomizedOrderS = $this->save('YyStudentCustomizedOrder', $updateOrderData);
            if ($studentCustomizedOrderS->ret) {
                if ($studentCustomizedLineR->data['passenger_id'] == $yyStudentCustomizedOrderR->data['passenger_id']) { //发布人本人
                    if ($studentCustomizedLineR->data['remain_count'] == $yyStudentCustomizedOrderR->data['count']) {
                        $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3;
                    } else {
                        $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2;
                    }
                    $updateStudentCustomizedLineData['student_customized_line_id'] = $yyStudentCustomizedOrderR->data['student_customized_line_id'];
                    $updateStudentCustomizedLineS = $this->save('YyStudentCustomizedLine', $updateStudentCustomizedLineData);
                    if (!$updateStudentCustomizedLineS->ret) {
                        $this->transRollback();
                        return new \ResultModel(false);
                    }
                }

                $this->updateStudentCustomizedLineRemainCount($yyStudentCustomizedOrderR->data['student_customized_line_id'], -$yyStudentCustomizedOrderR->data['count']);
                $this->commitTrans();

                \Log::write("组团定制包车订单通知处理成功！");
                return new \ResultModel(true);
            } else {
                $this->transRollback();
                return new \ResultModel(false);
            }
        } else {
            \Log::write("组团定制包车-支付回调解析失败!");
            return new \ResultModel(false);
        }
    }

    /**
     * 处理班线车合成订单
     * @param $retData
     */
    private function _dealMergeOrderWebHooks($retData)
    {
        $this->startTrans();
        $payOrderS = $this->save('PayOrderHistory', array('order_no' => $retData['out_trade_no'], 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => json_encode($retData)));
        if (!$payOrderS->ret) {
            \Log::write("合成订单记录异常");
            return new \ResultModel(false);
        }

        $classOrderWhere['order_no'] = $retData['out_trade_no'];
        $classOrderWhere['is_pay'] = \CommonDefine::PAY_STATUS_0;
        $classOrderR = $this->find($classOrderWhere, 'Order');
        if (!$classOrderR->ret) {
            \Log::write("合成班线车订单异常" . M()->getLastSql());
            return new \ResultModel(false);
        }
        $updateMergeClassOrderR = $this->_updateMergeClassOrder($classOrderR->data['order_id']);
        if (!$updateMergeClassOrderR->ret) {
            \Log::write("[合成班线车]" . $updateMergeClassOrderR->data);
            $this->transRollback();
            return new \ResultModel(false);
        }

        $subOrderWhere['order_id'] = $classOrderR->data['order_id'];
        $subOrdersR = $this->select($subOrderWhere, null, null, null, 'OrderSub');
        if ($subOrdersR->ret) {
            foreach ($subOrdersR->data as $k => $v) {
                $updateFastOrderR = $this->_updateMergeFastOrder($v['relation_order_id']);
                if (!$updateFastOrderR->ret) {
                    \Log::write("合成快车更新失败" . $v['relation_order_id']);
                    $this->transRollback();
                    return new \ResultModel(false);
                }
            }
        }

        $this->commitTrans();
        \Log::write("班线车 含接送更新成功");
        return new \ResultModel(true);
    }

    private function _updateMergeClassOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return new \ResultModel(false);
        }

        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //后支付模式
            $orderUpdataData['order_id'] = $orderR->data['order_id'];
            $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
            }
            //支付成功,更新订单状态,更新账户余额
            $orderS = $this->save('Order', $orderUpdataData);
            if ($orderS->ret) {
                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                if (!$driverR->ret) {
                    return new \ResultModel(false);
                }
                if ($orderR->data['state'] < 5) {
                    $bookR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                    if (!$bookR->ret) {
                        return new \ResultModel(false);
                    }
                } elseif ($orderR->data['state'] == 5) {
                    $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                    if (!$bookR->ret) {
                        return new \ResultModel(false);
                    }
                }
                return new \ResultModel(true);
            }
        } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
            //余票扣除
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            //            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
            if (!empty($lineClassTrainArr)) {
                $upLineClassTrainData = [];
                // $refundTicket = false;
                // if(!empty($orderR->data['seat_optional'])) {
                //     $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                //     $seatIdsArr= [];
                //     foreach($seatOptionalArr as $seatOptional) {
                //         $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                //     }
                //     $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                //     $seatOptionalR = $this->getSeatOptional($seatsPriceArr, $seatIdsArr);
                //     if(!$seatOptionalR->ret) {
                //         $refundTicket = true;
                //         \Log::write(__LINE__.":".$orderR->data['order_no']."座位已售出，请点击屏幕上方刷新按钮以获取最新的数据");
                //     }
                //     $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr);
                //     if(!$updateSeatPriceDataR->ret) {
                //         $refundTicket = true;
                //         \Log::write(__LINE__.":".$orderR->data['order_no']."座位信息更新失败");
                //     }
                //     $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
                // }

                // if($lineClassTrainArr['remain_tickets'] >= $orderR->data['book_seating'] && $refundTicket == false) {
                //出票成功
                // $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
                // $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] - $orderR->data['book_seating'];
                // $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
                // if($lineClassTrainS->ret) {
                //日志线路更新
                $this->doAddLog("[行号:" . __LINE__ . "]订单支付成功[订单ID:" . $orderR->data['order_id'] . "]", "原数据：" . json_encode($lineClassTrainArr) . ";更新内容:" . json_encode($upLineClassTrainData), \StateModel::$PASSENGER);

                $upOrderData['order_id'] = $orderR->data['order_id'];
                $upOrderData['is_pay'] = 1;

                $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
                //班线车订单模式，默认自动派单
                $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $orderR->data['mchid']), 'SystemConfig');
                if ($lineClassAppointConfigR->ret) {
                    $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
                }

                $lineClassTrainDriverR = $this->getLineClassTrainDriver($orderR->data['line_id'], $orderR->data['book_seating']);
                if (!$lineClassTrainDriverR->ret) {
                    $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1;
                    \Log::write(__LINE__ . ":" . $orderR->data['order_no'] . "自动指派失败，分台重新指派");
                }

                $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_2;
                if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                    $upOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                    $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                    $upOrderData['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                } elseif ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1) {
                    $upOrderData['state'] = \CommonDefine::ORDER_STATE_1;
                    $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                }

                $orderS = $this->save('Order', $upOrderData);
                if ($orderS->ret) {
                    if (!empty($upOrderData['driver_id'])) {
                        $driverR = $this->find(array('driver_id' => $lineClassTrainDriverR->data['driver_id']), 'Driver');
                        if (!$driverR->ret) {
                            return new \ResultModel(false, __LINE__ . ":未找到对应的司机");
                        }
                    }

                    $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                    if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                        //通知分台
                        if ($upOrderData['appoint'] == \CommonDefine::APPOINT_TYPE_0) {
                            //通知分台
                            $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);

                            //通知乘客
                            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0);
                        } else {
                            //通知司机
                            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                            if ($upOrderData['state'] == \CommonDefine::ORDER_STATE_2) {
                                //通知乘客
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                            }
                            $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                        }
                        return new \ResultModel(true, __LINE__ . ":先支付模式，支付通知处理成功");
                    } else {
                        return new \ResultModel(false, __LINE__ . ":更新记账流水失败");
                    }
                } else {
                    return new \ResultModel(false, __LINE__ . ":更新支付状态记录失败");
                }
                // } else {
                //     return new \ResultModel(false, __LINE__.":未找到对应班次信息");
                // }
                // } else {
                //     //余票不足，退款
                //     $where['order_id'] = $orderR->data['order_id'];
                //     $where['account_id'] = $orderR->data['passenger_id'];
                //     $where['account_type'] = 1;
                //     $rfR = $this->find($where, 'Refunds');
                //     if($rfR->ret) {
                //         return new \ResultModel(false, __LINE__.":操作失败，重复申请退款!");
                //     } else {

                //         $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderR->data['order_no'], 'status' => 1));
                //         if (!$payOrderS->ret) {
                //             return new \ResultModel(false, __LINE__.":操作失败!");
                //         }

                //         $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                //         if(!$bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                //             return new \ResultModel(false, __LINE__.":操作失败!");
                //         }

                //         $wxPayUtil = new \WxPayUtil();
                //         if(!$wxPayUtil->init($orderR->data['mchid'])) {
                //             return new \ResultModel(false, __LINE__.":商户支付配置异常".json_encode($orderR->data));
                //         }
                //         $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['real_price'], $orderR->data['real_price']);
                //         if(!$refundR->ret) {
                //             return new \ResultModel(false, __LINE__.":创建退款单失败");
                //         }

                //         if($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                //             return new \ResultModel(false, __LINE__.":创建退款单失败");
                //         }

                //         $refundData['amount'] = $orderR->data['price'];
                //         $refundData['status'] = 3;
                //         $refundData['order_id'] = $orderR->data['order_id'];
                //         $refundData['account_type'] = 1;//乘客
                //         $refundData['account_id'] = $orderR->data['passenger_id'];
                //         $refundData['refund_id'] = $refundR->data['refund_id'];
                //         //   $refundData['object'] = $refundR->data['object'];
                //         $refundData['refund_no'] = $refundR->data['out_trade_no'];
                //         $refundData['created'] = time();
                //         //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                //         /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                //             $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                //         $refundData['description'] = "余票不足，系统已自动取消订单，并全额退款，请选择其他线路重新下单";
                //         $refundData['charge'] = $refundR->data['charge'];
                //         $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                //         $refundData['transaction_no'] = $refundR->data['transaction_id'];
                //         $reR = $this->add('Refunds', $refundData);
                //         if($reR->ret) {
                //             $orderS = $this->save('Order', array('order_id'=>$orderR->data['order_id'],'state'=>7,'is_pay' => 1,'is_draw' => \CommonDefine::DRAW_TICKET_3));
                //             if($orderS->ret) {
                //                 $this->doCancelBookLineFastInnerFunction($orderR->data['order_id']);
                //                 return new \ResultModel(true, __LINE__.":支付通知处理成功，余票不足并退款");
                //             }
                //         } else {
                //             return new \ResultModel(false, __LINE__.":退款失败");
                //         }
                //     }
                // }
            }
        }
    }

    private function _updateMergeFastOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $where['is_pay'] = \CommonDefine::PAY_STATUS_0;
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return new \ResultModel(false);
        }

        $orderUpdataData['order_id'] = $orderR->data['order_id'];
        $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
            $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
        }
        //支付成功,更新订单状态,更新账户余额
        $orderS = $this->save('Order', $orderUpdataData);
        if ($orderS->ret) {
            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //先坐车后支付
                //添加到司机余额中去
                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                if (!$driverR->ret) {
                    return new \ResultModel(false);
                }
                $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                if ($bookR->ret) {
                    return new \ResultModel(true);
                } else {
                    return new \ResultModel(false);
                }
            } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {
                    \Log::write("班线车合成[快车]通知处理成功！");
                    //通知服务
                    $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                    if (!$passengerR->ret) {
                        return new \ResultModel(false);
                    }
                    if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                        /*                        $driversR = $this->getDriversByLineId($orderR->data['mchid'], \CommonDefine::ORDER_TYPE_1, $orderR->data['line_id']);
                                                //分台下所有司机
                                                //$driverR = $this->select(array('branchid' => $data->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone,openid');
                                                if ($driversR) {
                                                    $driverCellphoneArr = array();
                                                    $driverWeixinArr = array();
                                                    foreach($driversR->data as $driverData){
                                                        $driverCellphoneArr[] = $driverData['cellphone'];
                                                        $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                                    }
                                                    //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $this->encryptionCellphoneByCellphone($this->data['reseverd_phone'])), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $data->data['mchid']);

                                                    //您有新的[拼车]订单了
                                                    //乘客姓名：张三
                                                    //乘客人数：3人
                                                    //联系电话：1333333333
                                                    //预定线路：武汉-北京
                                                    //上车时间：2016/8/8 18:30
                                                    //请尽快与乘客取得联系
                                                    $temp = array(
                                                        '有新的['.$this->getOrderType($this->data['type']).']订单了',
                                                        $passengerR->data['name'],
                                                        $orderR->data['book_seating'],
                                                        $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                                        $orderR->data['start_name']." - ". $orderR->data['end_name'],
                                                        $orderR->data['start_time'],
                                                        '请及时抢单! 点击前往抢单'
                                                    );
                                                    $this->pushWeixinMessages($driverWeixinArr,$temp,\CommonDefine::USER_TYPE_2,$orderR->data['mchid'],self::WECHAT_MSG_TYPE_9);
                                                }*/
                    } else {
                        if (empty($orderUpdataData['driver_id'])) { //附近没有司机直接通知分台
                            $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);
                        } else {
                            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $orderUpdataData['driver_id']);
                        }
                    }

                    return new \ResultModel(true);
                } else {
                    return new \ResultModel(false);
                }
            }
        }
    }

    /**
     * 更新定制班线车订单
     *
     * @param \ResultModel $orderR
     * @param string $xml
     * @return \ResultModel
     */
    private function _payOverUpdateClassOrder(\ResultModel $orderR, $xml = '')
    {
        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) { //后支付模式
            $orderUpdataData['order_id'] = $orderR->data['order_id'];
            $orderUpdataData['is_pay'] = \CommonDefine::PAY_STATUS_1;
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                $orderUpdataData['state'] = \CommonDefine::ORDER_STATE_6;
            }
            //支付成功,更新订单状态,更新账户余额
            $orderS = $this->save('Order', $orderUpdataData);
            if ($orderS->ret) {
                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                if (!$driverR->ret) {
                    return new \ResultModel(false, __LINE__ . ":未找到对应的司机");
                }
                $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderR->data['order_no'], 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                if ($payOrderS->ret) {
                    if ($orderR->data['state'] < 5) {
                        $bookR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);
                        if (!$bookR->ret) {
                            return new \ResultModel(false, __LINE__ . ":更新记账流水失败");
                        }
                    } elseif ($orderR->data['state'] == 5) {
                        $bookR = $this->bookkeepingOnAll($orderR->data['mchid'], $driverR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                        if (!$bookR->ret) {
                            return new \ResultModel(false, __LINE__ . ":更新账户余额失败");
                        }
                        //计入用户积分
                        $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                    }
                    return new \ResultModel(true, __LINE__ . ":后支付模式，支付通知处理成功");
                } else {
                    return new \ResultModel(false, __LINE__ . ":更新支付状态记录失败");
                }
            }
        } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) { //先支付模式
            //余票扣除
            //并发时保障行安全（行锁）
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            if (!empty($lineClassTrainArr)) {
                $upLineClassTrainData = [];
                $refundTicket = false;
                /**
                 * 支付完成后，仅仅是出票，占位操作已经在下单时通过新API实现
                 * <AUTHOR> <<EMAIL>>
                 */
                // if($lineClassTrainArr['remain_tickets'] >= $orderR->data['book_seating'] && $refundTicket == false) {
                //日志线路更新
                $this->doAddLog("[行号:" . __LINE__ . "]订单支付成功[订单ID:" . $orderR->data['order_id'] . "]", "原数据：" . json_encode($lineClassTrainArr) . ";更新内容:" . json_encode($upLineClassTrainData), \StateModel::$PASSENGER);

                $upOrderData['order_id'] = $orderR->data['order_id'];
                $upOrderData['is_pay'] = 1;

                $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
                //班线车订单模式，默认自动派单
                $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $orderR->data['mchid']), 'SystemConfig');
                if ($lineClassAppointConfigR->ret) {
                    $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
                }

                $lineClassTrainDriverR = $this->getLineClassTrainDriver($orderR->data['line_id'], $orderR->data['book_seating']);
                if (!$lineClassTrainDriverR->ret) {
                    $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1;
                    \Log::write(__LINE__ . ":" . $orderR->data['order_no'] . "自动指派失败，分台重新指派");
                }

                $upOrderData['is_draw'] = \CommonDefine::DRAW_TICKET_2;
                if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                    // $upOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                    $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                    if ($orderR->data['driver_id'] == 0) {
                        $upOrderData['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                    }
                } elseif ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_1) {

                    $upOrderData['state'] = \CommonDefine::ORDER_STATE_1;
                    if ($orderR->data['driver_id'] == 0) {
                        $upOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                    }
                }

                $orderS = $this->save('Order', $upOrderData);
                if ($orderS->ret) {
                    $payOrderS = $this->save('PayOrderHistory', array('order_no' => $orderR->data['order_no'], 'status' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'webhook' => $xml));
                    if ($payOrderS->ret) {
                        if (!empty($upOrderData['driver_id'])) {
                            $driverR = $this->find(array('driver_id' => $lineClassTrainDriverR->data['driver_id']), 'Driver');
                            if (!$driverR->ret) {
                                return new \ResultModel(false, __LINE__ . ":" . $orderR->data['order_no'] . ":未找到对应的司机");
                            }
                        }

                        $bookkeepingMchOnTotalTurnoverOnAmountR = $this->bookkeepingMchOnTotalTurnoverOnAmount($orderR->data['mchid'], $orderR->data['branchid'], $orderR->data['order_id']);

                        if ($bookkeepingMchOnTotalTurnoverOnAmountR->ret) {

                            if (in_array($orderR->data['mchid'], C('NOTIFY_NEW_WECHAT_TEMPLATE_MERCHANT_IDS'))) {
                                # 新升级微信商户独立通知
                                $this->sendInnerNotification('branch', 'wechat', 'wechat_branch_new_order', $orderR->data['order_id']);
                                $this->sendInnerNotification('driver', 'wechat', 'wechat_driver_new_order', $orderR->data['order_id']);
                                $this->sendInnerNotification('passenger', 'wechat', '6', $orderR->data['order_id']);
                            }
                            //广播司机
                            $this->sendInnerNotification('driver', 'websocket', 'websocket_driver_new_order', $orderR->data['order_id']);

                            $this->postBranchNewOrderMessage($orderR->data['mchid'], $orderR->data['order_id']);

                            return new \ResultModel(true, __LINE__ . ":" . $orderR->data['order_no'] . ":支付通知处理成功");
                        } else {
                            return new \ResultModel(false, __LINE__ . ":" . $orderR->data['order_no'] . ":更新记账流水失败");
                        }
                    } else {
                        return new \ResultModel(false, __LINE__ . ":" . $orderR->data['order_no'] . ":更新支付状态记录失败");
                    }
                } else {
                    return new \ResultModel(false, __LINE__ . ":" . $orderR->data['order_no'] . ":更新订单状态失败");
                }
            }
        }
    }


    public function sendNewOrderSmsBranch($orderR)
    {
        if (!in_array($orderR->data['mchid'], C('SMS_NOT_ALLOWED_TO_BRANCH'))) {
            # 2023.11.10 通知分台新订单短信
            $this->sendInnerNotification('branch', 'sms', 'sms_branch_new_order', $orderR->data['order_id']);
        }
    }
}

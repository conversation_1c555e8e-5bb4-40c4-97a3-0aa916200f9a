<?php

#
# CORS config for php
# Code by anrip[<EMAIL>]
#

namespace Passenger;

import('@/Action/Weixin/Wechat');
import('@/Action/Weixin/WechatMini');
import('@/Action/Home/MemberAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 微信小程序账户模块
 *
 * <AUTHOR>
 */
class AccountWxMiniAction extends \PassengerCoreAction
{
    /**
     * 开发平台
     * code换取session_key
     * @param string $code wx.login时code值
     */
    public function getWxSessionKey($code)
    {
        $ret = new \ResultModel(false, '获取失败');
        $weMini = new \WechatMini();
        $sessionData = $weMini->getSessionKey($code, $this->mchid);
        if($sessionData) {
            $ret->ret = true;
            $ret->data = $sessionData;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 单独小程序开发
     * code换取session_key
     */
    public function getWxSessionKeySingle($code)
    {
        $ret = new \ResultModel(false, '获取失败');
        $weMini = new \WechatMini();
        $sessionData = $weMini->getSessionKeySingle($code, $this->mchid);
        if($sessionData) {
            $ret->ret = true;
            $ret->data = $sessionData;
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 开放平台 -- 小程序登陆(直接使用手机号授权)，请求地址加上callback参数
     * @param String $encrypted_data	String	包括敏感数据在内的完整用户信息的加密数据
     * @param String $iv	加密算法的初始向量
     * @param String $openid_mini	小程序的openid，getWxSessionKey有返回
     * @param string $session_key getWxSessionKey返回的session_key值
     */
    public function doLoginAuthOne($encrypted_data, $iv, $openid_mini, $session_key, $endpoint = '', $endpoint_id = 0, $channel = 'mini_program', $inviter = 0, $share_url = '')
    {
        $ret = new \ResultModel(false, '登陆失败');
        $weMini = new \WechatMini();
        $operation_time = date('Y-m-d H:i:s', time());

        $this->data['session_key'] = $session_key;
        $this->data['encrypted_data'] = $encrypted_data;
        $this->data['iv'] = $iv;
        $this->add('Log', array('Operation_content' => 'doLoginAuthOne：原始密文', 'operation_time' => $operation_time, 'admin_id' => $this->mchid, 'data' => json_encode($this->data)));
        $decryptData = $weMini->decryptData($this->mchid, $encrypted_data, $iv, $session_key);
        if(!$decryptData) {
            return $this->output($ret);
        }

        $this->add('Log', array('Operation_content' => 'doLoginAuthOne：授权登陆', 'operation_time' => $operation_time, 'admin_id' => $this->mchid, 'data' => json_encode($decryptData)));

        $where['cellphone'] = $decryptData['purePhoneNumber'];
        $where['mchid'] = $this->mchid;
        $passengerR = $this->find($where, 'Passenger');
        if($passengerR->ret) {
            //判断是否被删除
            if (intval($passengerR->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }

            $userInfoArr['nickname'] = '';
            $userInfoArr['avatar'] = '';
            $userInfoArr['cellphone'] = $passengerR->data['cellphone'];

            //获取用户信息
            $thirdR = $this->find(array('passenger_id'=> $passengerR->data['passenger_id']), 'ThirdParty', 'passenger_id,third_avatar,third_account');
            if($thirdR->ret) {
                $userInfoArr['passenger_id'] = $thirdR->data['passenger_id'];
                $userInfoArr['nickname'] = $thirdR->data['third_account'];
                $userInfoArr['avatar'] = $thirdR->data['third_avatar'];
                $this->recordUseUser($passengerR->data['passenger_id'], $userInfoArr['nickname'], $userInfoArr['avatar']);
            }

            if(empty($passengerR->data['openid_mini'])) {
                $updatePassengerData['openid_mini'] = $openid_mini;
                $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                $passengerS = $this->save('Passenger', $updatePassengerData);
                if($passengerS->ret) {
                    $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty');
                    if ($thirdR->ret) {
                        $updateThirdData['id'] = $thirdR->data['id'];
                        $updateThirdData['openid_mini'] = $openid_mini;
                        $thirdS = $this->save('ThirdParty', $updateThirdData);
                        if($thirdS->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '登录失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '登录失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '登录失败'));
                }
            }

            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
            $ret->ret = true;
            $ret->data = $userInfoArr;
            $ret->count = 1;
            return $this->output($ret);
        } else {
            //创建新的账号
            $this->startTrans();
            $newPassengerData['mchid'] = $this->mchid;
            $newPassengerData['cellphone'] = $decryptData['purePhoneNumber'];
            $newPassengerData['openid_mini'] = $openid_mini;
            $passengerA = $this->add('Passenger', $newPassengerData);
            if($passengerA->ret) {
                $newThirddata['openid_mini'] = $openid_mini;
                $newThirddata['type'] = 2;
                $newThirddata['mchid'] = $this->mchid;
                $newThirddata['passenger_id'] = $passengerA->data;
                $thirdR = $this->add('ThirdParty', $newThirddata);
                if($thirdR->ret) {
                    $this->commitTrans();
                    $userInfoArr['passenger_id'] = $passengerA->data;
                    $userInfoArr['nickname'] = '';
                    $userInfoArr['avatar'] = '';
                    $userInfoArr['cellphone'] = $decryptData['purePhoneNumber'];
                    \StateModel::save($passengerA->data, "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                    $ret->ret = true;
                    $ret->data = $userInfoArr;
                    $ret->count = 1;
                    # 邀请有礼营销活动
                    httpRequest(C('CC_INNER_API_HOST') . "/api/inner/mini_program/referral_rewards", 'post', array(
                        'endpoint' => $endpoint,
                        'endpoint_id' => $endpoint_id,
                        'channel'=> $channel,
                        'inviter'=> $inviter,
                        'invitee' => $passengerA->data,
                        'share_url' => $share_url,
                    ));
                    return $this->output($ret);
                } else {
                    $this->transRollback();
                    return $this->output($ret);
                }
            } else {
                $this->transRollback();
                return $this->output($ret);
            }
        }

        return $this->output($ret);
    }

    /**
     * 自定义日志记录方法
     *
     * @param string $message 日志消息
     * @param array $context 相关上下文信息
     * @param string $level 日志级别
     */
    protected function logMiniProgramRequest($message, $context = [], $level = 'INFO')
    {
        // 格式化日志信息
        $logMessage = $message . ' | ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        
        // 使用 ThinkPHP 内置日志记录方法
        \Log::write($logMessage, $level, '', LOG_PATH . 'mini_program.log');
    }

    /**
     * 单个小程序 -- 小程序登陆(直接使用手机号授权)，请求地址加上callback参数
     * @param String $encrypted_data	String	包括敏感数据在内的完整用户信息的加密数据
     * @param String $iv	加密算法的初始向量
     * @param String $openid_mini	小程序的openid，getWxSessionKey有返回
     * @param string $session_key getWxSessionKey返回的session_key值
     */
    public function doLoginAuthOneSingle($encrypted_data = '', $iv = '', $openid_mini = '', $session_key = '', $endpoint = '', $endpoint_id = 0, $channel = 'mini_program', $inviter = 0, $share_url = '', $openid_gzh = '')
    {
        $context = [
            'encrypted_data' => $encrypted_data,
            'iv' => $iv,
            'openid_mini' => $openid_mini,
            'session_key' => $session_key,
            'endpoint' => $endpoint,
            'endpoint_id' => $endpoint_id,
            'channel' => 'mini_program',
            'inviter' => $inviter,
            'share_url' => $share_url,
            'openid_gzh' => $openid_gzh,
        ];
        $this->logMiniProgramRequest('小程序用户获取openid_mini', $context);
        //小程序用户获取openid_mini | {"encrypted_data":"","iv":"","openid_mini":"oHGmJ7ZjxqIVpeBmt4b-d0It5zZs","session_key":"NgZ1foyJ6R+HOHlFOyaKtw==","endpoint":"referral","endpoint_id":"","channel":"mini_program","inviter":"","share_url":"","openid_gzh":"oGLwmwZe7XdkVV6SqHok9imJB-0Y"}

        if ($encrypted_data && $iv) {
            //有encrypted_data，则去获取小程序手机号码
            $this->createUserByMiniProgramPhone($encrypted_data, $iv, $openid_mini, $session_key, $endpoint, $endpoint_id, $channel, $inviter, $share_url);
        } else {
            //没有encrypted_data，则不获取手机号    
            $where['openid'] = $openid_gzh;
            $where['mchid'] = $this->mchid;
            $where['is_freeze'] = 0;
            $passengerR = $this->find($where, 'Passenger');
            if($passengerR->ret) {
                
                $this->logMiniProgramRequest('查询用户', $passengerR->data);

                $userInfoArr['nickname'] = '';
                $userInfoArr['avatar'] = '';
                $userInfoArr['cellphone'] = $passengerR->data['cellphone'];

                //获取用户信息
                $thirdR = $this->find(array('passenger_id'=> $passengerR->data['passenger_id']), 'ThirdParty', 'passenger_id,third_avatar,third_account');
                if($thirdR->ret) {
                    $userInfoArr['passenger_id'] = $thirdR->data['passenger_id'];
                    $userInfoArr['nickname'] = $thirdR->data['third_account'];
                    $userInfoArr['avatar'] = $thirdR->data['third_avatar'];
                    $this->recordUseUser($passengerR->data['passenger_id'], $userInfoArr['nickname'], $userInfoArr['avatar']);
                }

                if(empty($passengerR->data['openid_mini'])) {
                    $updatePassengerData['openid_mini'] = $openid_mini;
                    $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                    $passengerS = $this->save('Passenger', $updatePassengerData);
                    if($passengerS->ret) {
                        $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty');
                        if ($thirdR->ret) {
                            $updateThirdData['id'] = $thirdR->data['id'];
                            $updateThirdData['openid_mini'] = $openid_mini;
                            $thirdS = $this->save('ThirdParty', $updateThirdData);
                            if($thirdS->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '登录失败'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '登录失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '登录失败'));
                    }
                } else {
                    if ($passengerR->data['openid_mini'] != $openid_mini) {
                        $updatePassengerData['openid_mini'] = $openid_mini;
                        $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                        $this->save('Passenger', $updatePassengerData);
                    }
                }

                $this->logMiniProgramRequest('查询用户', $passengerR->data);

                \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                $ret->ret = true;
                $ret->data = $userInfoArr;
                $ret->count = 1;
                return $this->output($ret);

            } else {
                //创建新的账号
                $this->startTrans();
                $newPassengerData['mchid'] = $this->mchid;
                $newPassengerData['openid_mini'] = $openid_mini;
                $passengerA = $this->add('Passenger', $newPassengerData);
                if($passengerA->ret) {
                    $newThirddata['openid_mini'] = $openid_mini;
                    $newThirddata['type'] = 2;
                    $newThirddata['passenger_id'] = $passengerA->data;
                    $thirdR = $this->add('ThirdParty', $newThirddata);
                    if($thirdR->ret) {
                        $this->commitTrans();
                        $userInfoArr['passenger_id'] = $passengerA->data;
                        $userInfoArr['nickname'] = '';
                        $userInfoArr['avatar'] = '';
                        $userInfoArr['cellphone'] = $decryptData['purePhoneNumber'];
                        \StateModel::save($passengerA->data, "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                        $ret->ret = true;
                        $ret->data = $userInfoArr;
                        $ret->count = 1;
                        # 邀请有礼营销活动
                        httpRequest(C('CC_INNER_API_HOST') . "/api/inner/mini_program/referral_rewards", 'post', array(
                           'endpoint' => $endpoint,
                           'endpoint_id' => $endpoint_id,
                           'channel'=> $channel,
                           'inviter'=> $inviter,
                           'invitee' => $passengerA->data,
                           'share_url' => $share_url,
                        ));
                        return $this->output($ret);
                    } else {
                        $this->transRollback();
                        return $this->output($ret);
                    }
                } else {
                    $this->transRollback();
                    return $this->output($ret);
                }
            }
        }
    }

    private function createUserByMiniProgramPhone($encrypted_data = '', $iv = '', $openid_mini = '', $session_key = '', $endpoint = '', $endpoint_id = 0, $channel = 'mini_program', $inviter = 0, $share_url = '')
    {
        $ret = new \ResultModel(false, '登陆失败');
        $weMini = new \WechatMini();
        $this->data['session_key'] = $session_key;
        $this->data['encrypted_data'] = $encrypted_data;
        $this->data['iv'] = $iv;
        $decryptData = $weMini->decryptDataSingle($this->mchid, $encrypted_data, $iv, $session_key);
        if(!$decryptData) {
            return $this->output($ret);
        }
        $where['cellphone'] = $decryptData['purePhoneNumber'];
        $where['mchid'] = $this->mchid;
        $passengerR = $this->find($where, 'Passenger');
        if($passengerR->ret) {
            //判断是否被删除
            if (intval($passengerR->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }

            $userInfoArr['nickname'] = '';
            $userInfoArr['avatar'] = '';
            $userInfoArr['cellphone'] = $passengerR->data['cellphone'];

            //获取用户信息
            $thirdR = $this->find(array('passenger_id'=> $passengerR->data['passenger_id']), 'ThirdParty', 'passenger_id,third_avatar,third_account');
            if($thirdR->ret) {
                $userInfoArr['passenger_id'] = $thirdR->data['passenger_id'];
                $userInfoArr['nickname'] = $thirdR->data['third_account'];
                $userInfoArr['avatar'] = $thirdR->data['third_avatar'];
                $this->recordUseUser($passengerR->data['passenger_id'], $userInfoArr['nickname'], $userInfoArr['avatar']);
            }

            if(empty($passengerR->data['openid_mini'])) {
                $updatePassengerData['openid_mini'] = $openid_mini;
                $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                $passengerS = $this->save('Passenger', $updatePassengerData);
                if($passengerS->ret) {
                    $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty');
                    if ($thirdR->ret) {
                        $updateThirdData['id'] = $thirdR->data['id'];
                        $updateThirdData['openid_mini'] = $openid_mini;
                        $thirdS = $this->save('ThirdParty', $updateThirdData);
                        if($thirdS->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '登录失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '登录失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '登录失败'));
                }
            }

            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
            $ret->ret = true;
            $ret->data = $userInfoArr;
            $ret->count = 1;
            return $this->output($ret);
        } else {
            //创建新的账号
            $this->startTrans();
            $newPassengerData['mchid'] = $this->mchid;
            $newPassengerData['cellphone'] = $decryptData['purePhoneNumber'];
            $newPassengerData['openid_mini'] = $openid_mini;
            $passengerA = $this->add('Passenger', $newPassengerData);
            if($passengerA->ret) {
                $newThirddata['openid_mini'] = $openid_mini;
                $newThirddata['type'] = 2;
                $newThirddata['passenger_id'] = $passengerA->data;
                $thirdR = $this->add('ThirdParty', $newThirddata);
                if($thirdR->ret) {
                    $this->commitTrans();
                    $userInfoArr['passenger_id'] = $passengerA->data;
                    $userInfoArr['nickname'] = '';
                    $userInfoArr['avatar'] = '';
                    $userInfoArr['cellphone'] = $decryptData['purePhoneNumber'];
                    \StateModel::save($passengerA->data, "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                    $ret->ret = true;
                    $ret->data = $userInfoArr;
                    $ret->count = 1;
                    # 邀请有礼营销活动
                    httpRequest(C('CC_INNER_API_HOST') . "/api/inner/mini_program/referral_rewards", 'post', array(
                       'endpoint' => $endpoint,
                       'endpoint_id' => $endpoint_id,
                       'channel'=> $channel,
                       'inviter'=> $inviter,
                       'invitee' => $passengerA->data,
                       'share_url' => $share_url,
                    ));
                    return $this->output($ret);
                } else {
                    $this->transRollback();
                    return $this->output($ret);
                }
            } else {
                $this->transRollback();
                return $this->output($ret);
            }
        }
    }
    

    // public function doLoginAuthOneSingle($encrypted_data = '', $iv = '', $openid_mini = '', $session_key = '', $endpoint = '', $endpoint_id = 0, $channel = 'mini_program', $inviter = 0, $share_url = '', $openid_gzh = '')
    // {
    //     if ($encrypted_data && $iv) {
    //         // 获取小程序手机号码
    //         return $this->createUserByMiniProgramPhone($encrypted_data, $iv, $openid_mini, $session_key, $endpoint, $endpoint_id, $channel, $inviter, $share_url);
    //     } 
        
    //     // 没有encrypted_data，直接使用公众号openid登录
    //     $where = [
    //         'openid' => $openid_gzh,
    //         'mchid' => $this->mchid,
    //         'is_freeze' => 0,
    //     ];
    //     $passengerR = $this->find($where, 'Passenger');
        
    //     if (!$passengerR->ret) {
    //         return $this->createNewPassenger($openid_mini, $endpoint, $endpoint_id, $channel, $inviter, $share_url);
    //     }

    //     // 返回用户信息
    //     $userInfoArr = [
    //         'nickname' => '',
    //         'avatar' => '',
    //         'cellphone' => $passengerR->data['cellphone'],
    //     ];

    //     $thirdR = $this->find(['passenger_id' => $passengerR->data['passenger_id']], 'ThirdParty', 'passenger_id,third_avatar,third_account');
    //     if ($thirdR->ret) {
    //         $userInfoArr = array_merge($userInfoArr, [
    //             'passenger_id' => $thirdR->data['passenger_id'],
    //             'nickname' => $thirdR->data['third_account'],
    //             'avatar' => $thirdR->data['third_avatar'],
    //         ]);
    //         $this->recordUseUser($passengerR->data['passenger_id'], $userInfoArr['nickname'], $userInfoArr['avatar']);
    //     }

    //     // 更新openid_mini
    //     if (empty($passengerR->data['openid_mini'])) {
    //         return $this->updatePassengerOpenidMini($passengerR->data['passenger_id'], $openid_mini, $userInfoArr);
    //     }

    //     \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
    //     return $this->outputSuccess($userInfoArr);
    // }

    // private function createNewPassenger($openid_mini, $endpoint, $endpoint_id, $channel, $inviter, $share_url)
    // {
    //     $this->startTrans();
    //     $newPassengerData = [
    //         'mchid' => $this->mchid,
    //         'openid_mini' => $openid_mini,
    //     ];
        
    //     $passengerA = $this->add('Passenger', $newPassengerData);
    //     if (!$passengerA->ret) {
    //         $this->transRollback();
    //         return $this->output(new \ResultModel(false, '创建乘客失败'));
    //     }

    //     $newThirdData = [
    //         'openid_mini' => $openid_mini,
    //         'type' => 2,
    //         'passenger_id' => $passengerA->data,
    //     ];

    //     $thirdR = $this->add('ThirdParty', $newThirdData);
    //     if (!$thirdR->ret) {
    //         $this->transRollback();
    //         return $this->output(new \ResultModel(false, '创建第三方信息失败'));
    //     }

    //     $this->commitTrans();
    //     $userInfoArr = [
    //         'passenger_id' => $passengerA->data,
    //         'nickname' => '',
    //         'avatar' => '',
    //         'cellphone' => '',  // 可根据业务逻辑设置
    //     ];

    //     \StateModel::save($passengerA->data, "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
    //     $this->triggerReferralRewards($endpoint, $endpoint_id, $channel, $inviter, $passengerA->data, $share_url);

    //     return $this->outputSuccess($userInfoArr);
    // }

    // private function updatePassengerOpenidMini($passenger_id, $openid_mini, $userInfoArr)
    // {
    //     $this->startTrans();

    //     $updatePassengerData = [
    //         'openid_mini' => $openid_mini,
    //         'passenger_id' => $passenger_id,
    //     ];
    //     $passengerS = $this->save('Passenger', $updatePassengerData);

    //     if (!$passengerS->ret) {
    //         $this->transRollback();
    //         return $this->output(new \ResultModel(false, '更新乘客信息失败'));
    //     }

    //     $thirdR = $this->find(['passenger_id' => $passenger_id], 'ThirdParty');
    //     if ($thirdR->ret) {
    //         $updateThirdData = [
    //             'id' => $thirdR->data['id'],
    //             'openid_mini' => $openid_mini,
    //         ];
    //         $thirdS = $this->save('ThirdParty', $updateThirdData);

    //         if ($thirdS->ret) {
    //             $this->commitTrans();
    //             return $this->outputSuccess($userInfoArr);
    //         }
    //     }

    //     $this->transRollback();
    //     return $this->output(new \ResultModel(false, '更新第三方信息失败'));
    // }

    // private function outputSuccess($userInfoArr)
    // {
    //     $ret = new \ResultModel(true, '');
    //     $ret->data = $userInfoArr;
    //     $ret->count = 1;
    //     return $this->output($ret);
    // }

    // private function triggerReferralRewards($endpoint, $endpoint_id, $channel, $inviter, $invitee, $share_url)
    // {
    //     httpRequest(C('CC_INNER_API_HOST') . "/api/inner/mini_program/referral_rewards", 'post', [
    //         'endpoint' => $endpoint,
    //         'endpoint_id' => $endpoint_id,
    //         'channel' => $channel,
    //         'inviter' => $inviter,
    //         'invitee' => $invitee,
    //         'share_url' => $share_url,
    //     ]);
    // }


    /**
     * 小程序登陆
     * @param string $cellphone 要绑定的电话号码
     * @param string $cellphone_validate_code 验证码
     * @param string $nickname 用户昵称
     * @param string $avatar 用户头像
     * @param string $openid_mini 小程序openid
     * @param string $callback 商户标识
     */
    public function doLoginAuthTwo($cellphone, $cellphone_validate_code, $nickname, $avatar, $openid_mini)
    {
        $this->add('Log', array('Operation_content' => '小程序登录', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => json_encode($this->data)));
        $passengerR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Passenger');
        if($passengerR->ret) {
            if(empty($passengerR->data['openid_mini'])) {//公众号已经注册的用户，还未绑定小程序
                //验证手机号码
                $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
                if($r->ret) {
                    $this->startTrans();
                    $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                    $updatePassengerData['name'] = $nickname;
                    $updatePassengerData['cellphone'] = $cellphone;
                    $updatePassengerData['openid_mini'] = $openid_mini;
                    $passengerS = $this->save('Passenger', $updatePassengerData);
                    if ($passengerS->ret) {
                        $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty');
                        if ($thirdR->ret) {
                            $updateThirdData['id'] = $thirdR->data['id'];
                            $updateThirdData['third_account'] = $nickname;
                            $updateThirdData['third_avatar'] = $avatar;
                            $updateThirdData['openid_mini'] = $openid_mini;
                            $thirdS = $this->save('ThirdParty', $updateThirdData);
                        }
                        $this->commitTrans();
                        $userInfoArr['passenger_id'] = $passengerR->data['passenger_id'];
                        $userInfoArr['nickname'] = $nickname;
                        $userInfoArr['avatar'] = $avatar;
                        $userInfoArr['cellphone'] = $cellphone;
                        \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);

                        return $this->output(new \ResultModel(true, $userInfoArr, 1));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '绑定失败1'));
                    }
                } else {
                    return $this->output(new \ResultModel(false, '绑定失败2'));
                }
            } elseif($passengerR->data['openid_mini'] && $passengerR->data['openid_mini'] != $openid_mini) {
                return $this->output(new \ResultModel(false, '该手机号已经被绑定过'));
            } else {
                $userInfoArr['passenger_id'] = $passengerR->data['passenger_id'];
                $userInfoArr['nickname'] = $nickname;
                $userInfoArr['avatar'] = $avatar;
                $userInfoArr['cellphone'] = $cellphone;
                \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                return $this->output(new \ResultModel(true, $userInfoArr, 1));
            }
        } else {
            //验证手机号码
            $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
            if($r->ret) {
                $where['openid_mini'] = $openid_mini;
                $where['mchid'] = $this->mchid;
                $passengerR = $this->find($where, 'Passenger');
                if ($passengerR->ret) {//已存在的账号
                    if(empty($passengerR->data['cellphone'])) {
                        $updatePassengerData['passenger_id'] = $passengerR->data['passenger_id'];
                        $updatePassengerData['name'] = $nickname;
                        $updatePassengerData['cellphone'] = $cellphone;
                        $passengerS = $this->save('Passenger', $updatePassengerData);
                        if ($passengerS->ret) {
                            $userInfoArr['passenger_id'] = $passengerR->data['passenger_id'];
                            $userInfoArr['nickname'] = $nickname;
                            $userInfoArr['avatar'] = $avatar;
                            $userInfoArr['cellphone'] = $cellphone;
                            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                            return $this->output(new \ResultModel(true, $userInfoArr, 1));
                        }
                    }
                    $this->add('Log', array('Operation_content' => '账号异常', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => json_encode($passengerR->data)));
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '账号异常'));
                } else {//新账户
                    //创建新的账号
                    $this->startTrans();
                    $newPassengerData['mchid'] = $this->mchid;
                    $newPassengerData['cellphone'] = $cellphone;
                    $newPassengerData['name'] = $nickname;
                    $newPassengerData['openid_mini'] = $openid_mini;
                    $passengerA = $this->add('Passenger', $newPassengerData);
                    if ($passengerA->ret) {
                        $newThirddata['openid_mini'] = $openid_mini;
                        $newThirddata['type'] = 2;
                        $newThirddata['third_account'] = $nickname;
                        $newThirddata['third_avatar'] = $avatar;
                        $newThirddata['passenger_id'] = $passengerA->data;
                        $thirdR = $this->add('ThirdParty', $newThirddata);
                        if ($thirdR->ret) {
                            $this->commitTrans();
                            $userInfoArr['passenger_id'] = $passengerA->data;
                            $userInfoArr['nickname'] = $nickname;
                            $userInfoArr['avatar'] = $avatar;
                            $userInfoArr['cellphone'] = $cellphone;
                            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                            return $this->output(new \ResultModel(true, $userInfoArr, 1));
                        } else {
                            $this->transRollback();
                            $this->add('Log', array('Operation_content' => '绑定失败', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => M()->getLastSql()));
                            return $this->output(new \ResultModel(false, '绑定失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '绑定失败4'));
                    }
                }
            }
        }

        return $this->output(new \ResultModel(false, '绑定失败5'));
    }

    /**
     * 微信授权后，用户信息更新接口
     * @param string $nickname 用户昵称
     * @param string $avatar 用户头像
     * @param string $callback 商户标识
     */
    public function doUpdateUserInfo($nickname, $avatar)
    {
        $ret = new \ResultModel(false, '用户信息更新失败');
        //判断该手机号账号是否已经存在
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if($passengerR->ret) {
            $this->startTrans();
            //更新已存在的账号信息
            $updateData['passenger_id'] = $passengerR->data['passenger_id'];
            $updateData['name'] = $nickname;
            $passengerS = $this->save('Passenger', $updateData);
            if($passengerS->ret) {
                $updateThirdData['passenger_id'] = $passengerR->data['passenger_id'];
                $updateThirdData['third_account'] = $nickname;
                $updateThirdData['third_avatar'] = $avatar;
                $thirdS = $this->save('ThirdParty', $updateThirdData);
                if($thirdS->ret) {
                    $this->commitTrans();
                    $userInfoArr['passenger_id'] = $passengerR->data['passenger_id'];
                    $userInfoArr['nickname'] = $nickname;
                    $userInfoArr['avatar'] = $avatar;
                    $userInfoArr['cellphone'] = $passengerR->data['cellphone'];

                    \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $userInfoArr, $this->mchid);
                    return $this->output(new \ResultModel(true, $userInfoArr, 1));
                } else {
                    $this->transRollback();
                }
            } else {
                $this->transRollback();
            }
        }

        return $ret;
    }
}

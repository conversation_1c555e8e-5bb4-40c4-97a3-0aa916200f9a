<?php

import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of PassengerPageAction
 *
 * <AUTHOR>
 */
class YyPassengerPageAction extends PassengerCoreAction
{

    public function __construct(){
        parent::__construct();
    }

    //乘客前端首页
    public function YyIndexPage()
    {
        //授权检测
        $this->_wxAuth($this->mchid, C('WEB_ROOT').'passenger_banma_index');
        $this->display('Tpl/YyPassenger/Yy_index/index.html');
        return;
    }

    /**
     * 希格斯科技授权，支付使用
     */
    public function yywechatauthforpayPage(){
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);
        $state = $dataArr[0];
        $tcall = $dataArr[1];
        $cipmchid = $dataArr[2];
        //解密商户id
        $mchid = $this->getMchidByCipmchid($cipmchid);

        $wechat = new \Wechat();
        $url = $wechat->getCodeAccessTokenUrlForPay($code);

        $data = file_get_contents($url);
        $arr = json_decode($data, true);
        if(isset($arr['errcode'])){
            //TODO 临时授权取消
            //$arr['openid']='oGLwmwah5UoYsNDxTMagR-ydeUwI';
            $this->error("授权失败：".$arr['errmsg']);
        }
        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_pas_token_'.$mchid, $arr['access_token'], $expires_in);
        $passengerR = $this->find(array( 'passenger_id' => $state, 'mchid' => $mchid),'Passenger', 'passenger_id, cellphone, openid_pay,name');
        if (isset($arr['openid']) && $arr['openid']) {
            if($passengerR->data['openid_pay'] != $arr['openid']){
                $passengerS = $this->save('Passenger',array('passenger_id'=>$state,'openid_pay'=>$arr['openid']));
                if(!$passengerS->ret){
                    $this->error("授权失败");
                }
            }
        }

        //获取用户信息
        $thirdR = $this->find(array('passenger_id'=> $passengerR->data['passenger_id']),'ThirdParty','third_avatar,third_account');
        $userInfoArr = $thirdR->data;
        $passengerInfo = array('mobile'=> $passengerR->data['cellphone'], 'avatar'=>$userInfoArr['third_avatar'], 'nickname'=>$userInfoArr['third_account'],'openid_pay' => $arr['openid']);

        \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);

        if(!empty($tcall)){
            redirect($tcall.'/callback/'.$this->getCipmchidByMchid($mchid)->data['ciphertext']);
        }else{
            redirect(C('WEB_ROOT') . 'passenger_index/callback/'.$this->getCipmchidByMchid($mchid)->data['ciphertext']);
        }
        return;
    }

    /**
     * 微信端回调处理,保存用户openid及相关信息,商户的公众号授权
     */
    public function yywechatauthPage()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);
        $cipmchid = $dataArr[0];
        $tcall = $dataArr[1];
        if(empty($cipmchid)){
            $this->error("授权失败！请稍后再试");
            //微信返回按钮
//            $this->display('Tpl/Passenger/Index/index.html');
            return;
        }
        $uid = '';

        $newPassengerData = [];
        //渠道来源
        if(count($dataArr) >= 3){
            if(substr($dataArr[2], 0, 3) == \CommonDefine::CHANNEL_BRANCH_PARMTER_CBP){
                //判定是不是渠道账号引入
                $newPassengerData['invite_id'] = substr($dataArr[2], 3, (strlen($dataArr[2]) -3));
                $newPassengerData['invite_type'] = \CommonDefine::INVITE_TYPE_1;
            }

            if(substr($dataArr[2], 0, 3) == 'uid'){
                $uid = substr($dataArr[2], 3, (strlen($dataArr[2]) -3));
            }
        }

        $wechat = new \Wechat();
        $url = $wechat->getCodeAccessTokenUrl($code, $cipmchid);
        //解密商户id
        $mchid = $this->getMchidByCipmchid($cipmchid);

        $data = file_get_contents($url);
        $arr = json_decode($data, true);

        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_pas_token_'.$mchid, $arr['access_token'], $expires_in);
        if (isset($arr['openid']) && $arr['openid']) {
            $r = $this->find(array('openid' => $arr['openid'], 'mchid' => $mchid),'Passenger', 'passenger_id,is_freeze,cellphone');
            if(!$r->ret && !empty($uid)){//公众号迁移后
                $r = $this->find(array('passenger_id' => $uid, 'mchid' => $mchid),'Passenger', 'passenger_id,is_freeze,cellphone,openid');
            }
            if($r->ret){
                if (intval($r->data['is_freeze']) === 1) {
//                return $this->output(new \ResultModel(false, '账号被删除'));
                    $this->error("该用户帐号已被删除，请联系管理员");
                    return;
                    //redirect(C('WX_PASSENGER_URL'));
                }
                //获取用户信息
                $thirdR = $this->find(array('passenger_id'=> $r->data['passenger_id'] ),'ThirdParty','id,third_avatar,third_account,uuid');
                $userInfoArr = $thirdR->data;
                if(!$thirdR->ret){
                    //获取用户信息
                    $userInfoUrl = $wechat->getUserInfoUrl($arr['access_token'], $arr['openid']);
                    $userinfoData = file_get_contents($userInfoUrl);
                    $userInfoArr = json_decode($userinfoData, true);
                    $this->add('ThirdParty', array('passenger_id'=> $r->data['passenger_id'],'uuid' => $userInfoArr['openid'], 'mchid' => $mchid, 'third_avatar' => $userInfoArr['headimgurl'],'third_account'=> $userInfoArr['nickname'],'type' => 2));
                    $this->save('Passenger',array('passenger_id' => $r->data['passenger_id'], 'name' => $userInfoArr['nickname'], 'openid' => $userInfoArr['openid']));

                    $passengerInfo = array('mobile'=> $r->data['cellphone'],'avatar'=>$userInfoArr['headimgurl'], 'nickname'=>$userInfoArr['nickname']);
                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER,$passengerInfo, $mchid);

                }else{
                    $passengerInfo = array('mobile'=> $r->data['cellphone'],'avatar'=>$userInfoArr['third_avatar'], 'nickname'=>$userInfoArr['third_account']);

                    if($r->data['openid'] != $arr['openid']){
                        $this->save('Passenger',array('passenger_id' => $r->data['passenger_id'], 'openid' => $arr['openid']));
                    }

                    if($thirdR->data['uuid'] != $arr['openid']){
                        $this->save('ThirdParty', array('id' => $thirdR->data['id'], 'uuid' => $arr['openid']));
                    }
                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                }
                //输出用户信息到页面
                $this->assign('cellphone', $r->data['cellphone']);
            }else{
                //获取用户信息
                $userInfoUrl = $wechat->getUserInfoUrl($arr['access_token'], $arr['openid']);
                $userinfoData = file_get_contents($userInfoUrl);
                $userInfoArr = json_decode($userinfoData, true);
                if(!empty($userInfoArr['nickname'])){
                    str_replace('\'','’',$userInfoArr['nickname']);
                }
                if(isset($userInfoArr['openid']) && $userInfoArr['openid']) {
                    $passengerR = $this->find(array('openid' => $userInfoArr['openid'], 'mchid' => $mchid),'Passenger', 'passenger_id, cellphone');
                    $this->startTrans();
                    $passengerId = "";
                    if(!$passengerR->ret){
                        //创建新用户
                        $newPassengerData['openid'] = $arr['openid'];
                        $newPassengerData['mchid'] = $mchid;
                        $newPassengerData['name'] = $userInfoArr['nickname'];
                        $passengerR = $this->add('Passenger', $newPassengerData);
                        $passengerId = $passengerR->data;
                    }else{
                        $passengerId = $passengerR->data['passenger_id'];
                    }
                    //添加三方账号信息
                    if($passengerR->ret){
                        $thirdR = $this->add('ThirdParty', array('passenger_id'=> $passengerId, 'uuid' => $userInfoArr['openid'],'mchid' => $mchid, 'third_avatar' => $userInfoArr['headimgurl'],'third_account'=> $userInfoArr['nickname'],'type' => 2));
                        if($thirdR->ret){
                            $passengerInfo = array('mobile' => $passengerR->data['cellphone'],'avatar'=>$userInfoArr['headimgurl'], 'nickname'=>$userInfoArr['nickname']);
                            \StateModel::save($passengerId, "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                            $this->commitTrans();
                        }else{
                            $this->transRollback();
                        }
                    }else{
                        $this->transRollback();
                    }
                }
            }
        }else{
            //授权异常处理
            $this->display('Tpl/Passenger/Index/index.html');
            return;
        }

        if(!empty($tcall)){
            redirect($tcall.'/callback/'.$this->getCipmchidByMchid($mchid)->data['ciphertext']);
        }else{
            redirect(C('WEB_ROOT') . 'passenger_banma_index/callback/'.$this->getCipmchidByMchid($mchid)->data['ciphertext']);
        }
        return;
    }

    /**
     * 微信授权
     * @param $mchid 商户id
     * @param $callbackUrl 授权回调地址不行太长，否则将被微信截断
     */
    private function _wxAuth($mchid, $callbackUrl){
        $cipmchid = $this->getCipmchidByMchid($mchid)->data['ciphertext'];

        $userId = '';
        //登陆过的用户直接跳转至首页
        if(isset($this->state->user_id) && $this->state->user_type == \StateModel::$PASSENGER){
            $passengerR = $this->find(array( 'passenger_id' => $this->state->user_id, 'mchid' => $mchid),'Passenger', 'passenger_id, cellphone, openid, openid_pay,name');
            if(!empty($passengerR->data['openid'])){
                if($passengerR->ret){
                    if(empty($passengerR->data['openid_pay'])|| !isset($passengerR->data['openid_pay'])
                        || empty($this->state->data['openid_pay']) || !isset($this->state->data['openid_pay']) || $this->state->data['openid_pay'] != $passengerR->data['openid_pay']
                    ){
                        //微信支付授权
                        $we = new \Wechat();

                        $url = C('WEB_ROOT') . 'passenger_banma_authpay';
                        $redirect_uri = urlencode($url);
                        $wxUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $we->appIdForPay . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_base&state=" . $passengerR->data['passenger_id'] .','.$callbackUrl.','.$cipmchid."#wechat_redirect";
                        header("Location:".$wxUrl);
                        return;
                    }else{
                        //获取用户信息
                        $thirdR = $this->find(array('passenger_id'=> $passengerR->data['passenger_id']),'ThirdParty','third_avatar,third_account');
                        $userInfoArr = $thirdR->data;

                        $passengerInfo = array('mobile'=> $passengerR->data['cellphone'], 'avatar'=>$userInfoArr['third_avatar'], 'nickname'=>$userInfoArr['third_account'],'openid_pay' => $passengerR->data['openid_pay']);

                        \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                        $this->recordUseUser($passengerR->data['passenger_id'], $userInfoArr['third_account'], $userInfoArr['third_avatar']);
                        return;
                    }
                }
            }else{
                $userId = $this->state->user_id;
            }
        }

        //微信授权页面
        $we = new \Wechat();
        $url = C('WEB_ROOT') . 'passenger_banma_auth';
        $redirect_uri = urlencode($url);
        $state = $cipmchid .','.$callbackUrl;
        if(!empty($userId)){
            $state .= ",uid".$userId;
        }

        $branchCodeR = $this->getChannelBranchCode($mchid);
        if($branchCodeR->ret){
            $state .=(",".\CommonDefine::CHANNEL_BRANCH_PARMTER_CBP.$branchCodeR->data['branchid']);
        }

        $wxUrl = $we->getAuthorizeUrl($redirect_uri, $state, $cipmchid);
        echo "<script language='javascript' type='text/javascript'>";
        echo "window.location.href='$wxUrl'";
        echo "</script>";
        die;
//        header("Location:".$wxUrl); 微信回调带参数时会有缓存问题
        return;
    }


    public function clearCachePage(){
        \StateModel::clear(\StateModel::$PASSENGER, $this->mchid);
        echo "清除缓存成功";die;
    }
}

?>

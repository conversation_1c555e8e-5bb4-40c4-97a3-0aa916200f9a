<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Home/HomeCoreAction');

/**
 * 乘客
 *
 * <AUTHOR>
 */
class PassengerCoreAction extends HomeCoreAction
{
    public function __construct()
    {
        //        header("Access-Control-Allow-Origin:*");
        add_tag_behavior('auth_check', 'PassengerAuthCheck');
        parent::__construct();

        # 本地环境调试
        if (C('ENV') == 'local') {
            $this->state->user_id = C('DEBUG_PASSENGER_ID');
            $this->mchid = C('DEBUG_MCH_ID');
        }
    }

    /**
     * 微信授权
     * @param $mchid 商户id
     * @param $callbackUrl 授权回调地址，不能太长，否则将被微信截断
     */
    public function _wxAuth($mchid, $callbackUrl = null)
    {
        // 添加错误处理
        $cipmchidR = $this->getCipmchidByMchid($mchid);
        if (!$cipmchidR->ret || empty($cipmchidR->data['ciphertext'])) {
            throw new Exception('获取商户密文失败');
        }
        $cipmchid = $cipmchidR->data['ciphertext'];

        // 检查用户是否已登录且为乘客类型
        if (isset($this->state->user_id) && $this->state->user_type == \StateModel::$PASSENGER) {
            // 查询乘客信息
            $passengerR = $this->find(
                ['passenger_id' => $this->state->user_id, 'mchid' => $mchid],
                'Passenger',
                'passenger_id, cellphone, openid, openid_pay, name'
            );

            // 如果乘客存在且有openid
            if ($passengerR->ret && !empty($passengerR->data['openid'])) {
                // 检查状态数据是否存在，避免未定义索引错误
                $stateOpenidPay = isset($this->state->data['openid_pay']) ? $this->state->data['openid_pay'] : '';

                // 如果openid_pay为空或与当前不一致，则更新并重定向
                if (
                    empty($passengerR->data['openid_pay']) ||
                    $stateOpenidPay != $passengerR->data['openid_pay']
                ) {
                    $updateR = $this->save('Passenger', [
                        'passenger_id' => $passengerR->data['passenger_id'],
                        'openid_pay' => $passengerR->data['openid']
                    ]);

                    // 确保更新成功
                    if (!$updateR->ret) {
                        throw new Exception('更新乘客openid_pay失败');
                    }

                    // 如果有回调URL，则重定向到回调URL
                    if (!empty($callbackUrl)) {
                        $separator = strpos($callbackUrl, '?') !== false ? '&' : '?';
                        $url = $callbackUrl . $separator . http_build_query($_REQUEST);
                        redirect($url);
                        exit();
                    }
                }
                // 如果不需要更新，直接返回（用户已认证完成）
                return;
            } else {
                $this->redirectToWechatAuth($cipmchid, $callbackUrl);
            }
        } else {
            $this->redirectToWechatAuth($cipmchid, $callbackUrl);
        }
    }

    /**
     * 重定向到微信授权
     * @param string $cipmchid 商户密文
     * @param string $callbackUrl 回调地址
     * @return void
     */
    private function redirectToWechatAuth($cipmchid, $callbackUrl = null)
    {
        // 准备微信授权
        $we = new \Wechat();

        // 构建基础URL
        $baseUrl = C('WEB_ROOT') . 'passenger_auth?callback=' . $cipmchid;

        // 获取请求参数
        $stateParams = [];
        if (isset($_REQUEST['cid'])) {
            $stateParams['cid'] = $_REQUEST['cid'];
        }  // 场景ID
        if (isset($_REQUEST['_cbp'])) {
            $stateParams['_cbp'] = $_REQUEST['_cbp'];
        }  // 渠道分台ID
        if (isset($_REQUEST['_qr'])) {
            $stateParams['_qr'] = $_REQUEST['_qr'];
        }  // 二维码类型
        if (isset($_REQUEST['_dep'])) {
            $stateParams['_dep'] = $_REQUEST['_dep'];
        }  // 司机ID

        // 构建重定向URI，避免重复编码
        $redirectUri = urlencode($baseUrl . '&' . http_build_query($stateParams));

        // 检查重定向URI长度，避免微信截断
        if (strlen($redirectUri) > 512) {
            throw new Exception('回调地址过长，可能被微信截断');
        }

        // 获取微信授权URL并直接重定向
        $wxUrl = $we->getAuthorizeUrl($redirectUri, '', $cipmchid);
        header("Location: " . $wxUrl);
        exit();
    }

    /**
     * 分配提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $driver_id 车主ID
     * @param string $action_code 操作标码
     * @param  int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignRemindTask($assigner_id, $driver_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaDriverRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'driver_id' => $driver_id), 'DriverRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('DriverRemindTask', array('assigner_id' => $assigner_id, 'driver_id' => $driver_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该车主没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '车主提醒操作标码不存在');
        }
        return $this->result;
    }

    /**
     * @param $mchid
     * @return ResultModel
     */
    protected function getChannelBranchCodeThrowPassenger($mchid)
    {
        $ret = new ResultModel(false);
        if (isset($this->state->data['branch_code'])) {
            $branchCode = $this->state->data['branch_code'];
            $branchR = $this->find(array('branch_code' => $branchCode), 'BranchQrAttachment');
            if ($branchR->ret && $branchR->data['mchid'] == $mchid) {
                $ret->ret = true;
                $ret->data['branch_code'] = $branchCode;
                $ret->data['branchid'] = $branchR->data['branchid'];
                $ret->data['mchid'] = $branchR->data['mchid'];
                $ret->count = 1;
            }
        }
        return $ret;
    }

    /**
     * 记录平台用户使用
     */
    protected function recordUseUser($passenger_id, $third_account, $third_avatar)
    {
        $data['passenger_id'] = $passenger_id;
        $data['third_account'] = $third_account;
        $data['third_avatar'] = $third_avatar;
        $data['mchid'] = $this->mchid;
        try {
            $this->add('UseUser', $data);
            $useUserStatisticsR = $this->find('', 'UseUserStatistics');
            if ($useUserStatisticsR->ret) {
                $updateData['total_use_count'] = $useUserStatisticsR->data['total_use_count'] + 1;
                $updateData['id'] = $useUserStatisticsR->data['id'];
                $this->save('UseUserStatistics', $updateData);
            }
        } catch (Exception $e) {
        }
    }

    //手机号是否已经绑定
    public function checkCellphoneIsBinding($cellphone, $mchid)
    {
        $r = $this->find(array('cellphone' => $cellphone, 'mchid' => $mchid), 'Passenger', 'passenger_id');
        return $r->ret;
    }

    protected function checkIsAllowOrder($mchid, $passenger_id)
    {
        if ($mchid == 1049 || $mchid == 181 || $mchid == 1075) {
            $multiOrderMode = \CommonDefine::MULTI_ORDER_MODE_1; //多订单；
            $systemConfigR = $this->find(array('key' => \CommonDefine::MULTI_ORDER_MODE, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $mchid), 'SystemConfig');
            if ($systemConfigR->ret) {
                $multiOrderMode = $systemConfigR->data['value'];
            }

            if ($multiOrderMode == \CommonDefine::MULTI_ORDER_MODE_0) {
                $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('elt', \CommonDefine::ORDER_STATE_5)), 'Order');

                if ($r->ret) {
                    if ($r->data > 0) {
                        return new \ResultModel(false, '请先取消之前的预订或者完成之前的订单');
                    }
                }
            }

            return new \ResultModel(true);
        }
        return new \ResultModel(true);
    }

    /**
     * 上传图片
     * @param string $mchid 商户id
     * @param string $passenger_id 乘客id
     * @param int $type 图片分类0-头像；1-行驶证；2-驾驶证；3-身份证
     */
    protected function doUploadImg($mchid, $passenger_id, $type = \CommonDefine::IMG_TYPE_0)
    {
        $saveKey = 'header_ico';
        $savePathKey = 'header_ico_path';
        $dirKey = 'UPLOAD_DIR_HEADER';
        $imgFile = 'header_ico';

        if ($type == \CommonDefine::IMG_TYPE_1) {
            $dirKey = 'UPLOAD_DIR_PASSENGER_RESIDENCE_BOOKLET_IMG';
            $saveKey = 'residence_booklet';
            $savePathKey = 'residence_booklet_path';
            $imgFile = 'residencebooklet_img';
        } elseif ($type == \CommonDefine::IMG_TYPE_2) {
            /*          $dirKey = 'UPLOAD_DIR_PASSENGER_STUDENT_ID_IMG';
                      $saveKey = 'driver_license';
                      $savePathKey = 'driver_license_path';
                      $imgFile = 'driver_img';*/
            return new \ResultModel(false, '上传失败');
        } elseif ($type == \CommonDefine::IMG_TYPE_3) {
            $dirKey = 'UPLOAD_DIR_PASSENGER_ID_IMG';
            $saveKey = 'ID_img';
            $savePathKey = 'ID_img_path';
            $imgFile = 'id_img';
        } else {
            $saveKey = 'header_ico';
            $savePathKey = 'header_ico_path';
            $dirKey = 'UPLOAD_DIR_HEADER';
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');
        $url = C('FILE_ROOT');

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir[$dirKey] . DIRECTORY_SEPARATOR . md5($mchid);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $imgExArr = C('IMG_TYPE_EX');
        $fileArr = explode('.', $_FILES[$imgFile]['name']);
        if (empty($fileArr)) {
            $this->output(new \ResultModel(false, '上传失败'));
        }
        $imgEx = end($fileArr);
        if (!in_array($imgEx, $imgExArr)) {
            return new \ResultModel(false, '不支持该文件类型');
        }
        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;

        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        if (!move_uploaded_file($_FILES[$imgFile]['tmp_name'], $fullPath . DIRECTORY_SEPARATOR . $realName)) {
            return new \ResultModel(false, '上传证件失败');
        }
        $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
        $urlPath = $url . $relateFullPath;
        $r = $this->save('Passenger', array('passenger_id' => $passenger_id, $saveKey => $urlPath, $savePathKey => $relateFullPath));
        if (!$r->ret) {
            return new \ResultModel(false, '上传失败');
        }
        return new \ResultModel(true, ['img' => $urlPath]);
    }

    /**
     * 上传图片
     * @param string $mchid 商户id
     * @param string $passenger_id 乘客id
     * @param int $type 图片分类0-头像；1-行驶证；2-驾驶证；3-身份证
     */
    protected function doUploadImg2($mchid, $passenger_id, $type = \CommonDefine::IMG_TYPE_0)
    {
        $saveKey = 'header_ico';
        $savePathKey = 'header_ico_path';
        $dirKey = 'UPLOAD_DIR_HEADER';
        $imgFile = 'img';
        if ($type == \CommonDefine::IMG_TYPE_1) {
            $dirKey = 'UPLOAD_DIR_PASSENGER_RESIDENCE_BOOKLET_IMG';
            $saveKey = 'residence_booklet';
            $savePathKey = 'residence_booklet_path';
        } elseif ($type == \CommonDefine::IMG_TYPE_2) {
            /*          $dirKey = 'UPLOAD_DIR_PASSENGER_STUDENT_ID_IMG';
                      $saveKey = 'driver_license';
                      $savePathKey = 'driver_license_path';
                      $imgFile = 'driver_img';*/
            return new \ResultModel(false, '上传失败');
        } elseif ($type == \CommonDefine::IMG_TYPE_3) {
            $dirKey = 'UPLOAD_DIR_PASSENGER_ID_IMG';
            $saveKey = 'ID_img';
            $savePathKey = 'ID_img_path';
        } else {
            $saveKey = 'header_ico';
            $savePathKey = 'header_ico_path';
            $dirKey = 'UPLOAD_DIR_HEADER';
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');
        $url = C('FILE_ROOT');

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir[$dirKey] . DIRECTORY_SEPARATOR . md5($mchid);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $imgExArr = C('IMG_TYPE_EX');
        $fileArr = explode('.', $_FILES[$imgFile]['name']);
        if (empty($fileArr)) {
            $this->output(new \ResultModel(false, '上传失败'));
        }
        $imgEx = end($fileArr);
        if (!in_array($imgEx, $imgExArr)) {
            return new \ResultModel(false, '不支持该文件类型');
        }
        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;

        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        if (!move_uploaded_file($_FILES[$imgFile]['tmp_name'], $fullPath . DIRECTORY_SEPARATOR . $realName)) {
            return new \ResultModel(false, '上传证件失败');
        }
        $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
        $urlPath = $url . $relateFullPath;
        $r = $this->save('Passenger', array('passenger_id' => $passenger_id, $saveKey => $urlPath, $savePathKey => $relateFullPath));
        if (!$r->ret) {
            return new \ResultModel(false, '上传失败');
        }
        return new \ResultModel(true, ['img' => $urlPath]);
    }
}

<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-订单模块
 *
 * <AUTHOR>
 */
class YyOrderAction extends \PassengerCoreAction
{
    /**
     * 下学生号订单
     * @param integer $line_class_train_id 线路班次编号
     * @param integer $number 座位数（默认为1）
     * @param string $reseverd_phone 电话（默认为空）
     * @param string $reseverd_info 备注（默认为空）
     * @param string $start_address_remark 出发地
     * @param string $start_latitude 开始纬度
     * @param string $start_longitude 开始经度
     * @param string $end_address_remark 目的地
     * @param string $end_latitude 结束纬度
     * @param string $end_longitude 结束经度
     */
    public function doStudentOrder($line_class_train_id, $number = 1, $reseverd_phone = null, $reseverd_info = null, $start_address_remark, $start_latitude, $start_longitude, $end_address_remark, $end_latitude, $end_longitude)
    {
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        //预留电话
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($number) || $number <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id, 'is_del' => \CommonDefine::IS_DEL_0), 'YyLineClassTrain');
        if (!$yyLineClassTrainR->ret) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }
        if ($yyLineClassTrainR->data['remain_tickets'] < $number) {
            return $this->output(new \ResultModel(false, '余票不足'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        //班线车订单模式，默认自动派单
        $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
        $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($lineClassAppointConfigR->ret) {
            $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
        }

        $yylineClassR = $this->find(array('id' => $yyLineClassTrainR->data['line_class_id']), 'YyLineClass');
        if ($yylineClassR->ret) {
            if (\intval($passengerR->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_8;
                $this->data['student_order_type'] = \YyCommonDefine::STUDENT_ORDER_TYPE_1;
                $this->data['mchid'] = $yyLineClassTrainR->data['mchid'];
                $this->data['line_id'] = $line_class_train_id;
                $this->data['branchid'] = $yyLineClassTrainR->data['branchid'];

                $this->data['start_address_code'] = $yylineClassR->data['start_address_code'];
                $this->data['end_address_code'] = $yylineClassR->data['end_address_code'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $yyLineClassTrainR->data['start_date']." ".$yylineClassR->data['start_time'];

                if (!$this->_checkTimeIsPassSellTime(time(), $line_class_train_id)) {
                    return $this->output(new \ResultModel(false, '已过售票时间'));
                }

                $this->data['book_seating'] = $number;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->data['is_draw'] = \CommonDefine::DRAW_TICKET_0;
                $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_0;
                $this->data['state'] = \CommonDefine::ORDER_STATE_1;
                $this->startTrans();
                //更新乘客信息
                $mch = M('AnnualFee')->where(array('mchid' => $yyLineClassTrainR->data['mchid']))->find();
                $this->data['price'] = $yyLineClassTrainR->data['price'] * $number;
                $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                $this->data['split'] = $mch['split'] / 100;
                $this->data['real_price'] = $this->data['price'];

                //添加订单
                $currentTime = date('Y-m-y H:i:s', time());
                $this->data['create_time'] = $currentTime;
                $this->data['update_time'] = $currentTime;

                if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                        $this->data['state'] = \CommonDefine::ORDER_STATE_2;
                        $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_1;

                        $lineClassTrainDriverR = $this->getYyLineClassTrainDriver($line_class_train_id, $number);
                        if (!$lineClassTrainDriverR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败'));
                        }

                        $this->data['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                    }
                }

                if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $r = $this->add('Order');
                    if ($r->ret) {
                        if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                            //出票成功
                            $upLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
                            $upLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] - $number;
                            $yyLineClassTrainS = $this->save('YyLineClassTrain', $upLineClassTrainData);
                            if (!$yyLineClassTrainS->ret) {
                                return $this->output(new \ResultModel(false, '余票不足!'));
                            }
                            $this->commitTrans();

                            //通知服务
                            $this->sendSms($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                            $this->sendWxMessage($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
                            if ($this->data['state'] == \CommonDefine::ORDER_STATE_2) {
                                //通知乘客
                                $this->sendSms($r->data, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                                $this->sendWxMessage($r->data, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                            }
                        } else {
                            $this->commitTrans();
                            $this->postBranchNewOrderMessage($passengerR->data['mchid'], $r->data);
                        }

                        return $this->output(new \ResultModel(true, array('order_id' => $r->data, 'order_no' => $this->data['order_no'],'price' => $this->data['price'])));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                } else {
                    $r = $this->add('Order');
                    if ($r->ret) {
                        $this->commitTrans();
                        //通知服务
                        if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                        } else {
                        }

                        return $this->output(new \ResultModel(true, array('order_id' => $r->data, 'order_no' => $this->data['order_no'],'price' => $this->data['price'])));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output(new \ResultModel(false, '下单失败'));
    }

    /**
     * 取消订单
     * @param integer $order_id 订单编号
     */
    public function doCancelStudentOrder($order_id)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2,3');
        $seOrderWhere['type'] = \CommonDefine::ORDER_TYPE_8;
        $this->startTrans();
        $orderR = $this->find($seOrderWhere, 'Order');
        if ($orderR->ret) {
            $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'YyLineClassTrain');
            if (!$yyLineClassTrainR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
            }
            $yyLineClassR = $this->find(array('line_class_id' =>  $yyLineClassTrainR->data['line_class_id']), 'YyLineClass');
            if (!$yyLineClassR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
            }

            //班线车订单模式，默认自动派单
            $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
            $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
            if ($lineClassAppointConfigR->ret) {
                $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
            }

            $order_state = intval($orderR->data['state']);

            /* if ($order_state == 2 ){
                 return $this->output(new \ResultModel(false, '司机已接单不能取消'));
             }*/ if ($order_state == 3) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '进行中的订单不能取消'));
            } else {
                if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    $isAllowRefundTicket = $this->_checkeIsAllowRefundTicket($orderR->data['order_id']);
                    if (!$isAllowRefundTicket->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '已过退票时间'));
                    }

                    //添加预订订单历史状态
                    $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                    if ($bookHistoryS->ret) {
                        //更新余票
                        $remain_tickets = $yyLineClassTrainR->data['remain_tickets'] + $orderR->data['book_seating'];
                        $yyLineClassTrainS = $this->save('YyLineClassTrain', array('line_class_train_id' => $yyLineClassTrainR->data['line_class_train_id'],'remain_tickets' => $remain_tickets));
                        if ($yyLineClassTrainS->ret) {
                            $wxPayUtil = new \WxPayUtil();
                            if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '商户支付异常，申请退款失败!'));
                            }
                            $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                            if (!$refundR->ret) {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                            }

                            if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                            }

                            $refundData['amount'] = $orderR->data['price'];
                            $refundData['status'] = 3;
                            $refundData['order_id'] = $order_id;
                            $refundData['account_type'] = 1;//乘客
                            $refundData['account_id'] = $orderR->data['passenger_id'];
                            $refundData['refund_id'] = $refundR->data['refund_id'];
                            //   $refundData['object'] = $refundR->data['object'];
                            $refundData['refund_no'] = $refundR->data['out_trade_no'];
                            $refundData['created'] = time();
                            //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                            /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                                $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                            $refundData['description'] = "取消订单，全额退款";
                            $refundData['charge'] = $refundR->data['charge'];
                            $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                            $refundData['transaction_no'] = $refundR->data['transaction_id'];

                            $reR = $this->add('Refunds', $refundData);
                            if ($reR->ret) {
                                $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'],'state' => 7));
                                if ($orderS->ret) {
                                    $this->commitTrans();
                                    //消息通知
                                    if (C('SMS_ON')) {
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                                        $this->pushShortMessage($driverR->data['cellphone'], array($orderR->data['order_no'], $passengerR->data['cellphone']), \SMSUtil::TEMP_ID_PSCANCEL, $orderR->data['mchid']);
                                    }

                                    //微信通知司机
                                    if (C('WX_TEMPLATE_ON')) {
                                        //{{first.DATA}}
                                        //订单编号：{{keyword1.DATA}}
                                        //订单状态：{{keyword2.DATA}}
                                        //{{remark.DATA}}
                                        $temp = array(
                                            '乘客取消了['.$this->getOrderType($orderR->data['type']).']订单',
                                            $orderR->data['order_no'],
                                            '已取消',
                                            '给您带来的不便，敬请谅解！'
                                        );
                                        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                        $this->pushWeixinMessage(array('openid' => $driverR->data['openid'], 'account_id' => $orderR->data['driver_id']), $temp, 1, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                        unset($temp);
                                    }
                                    return  $this->output(new \ResultModel(true, '退款中，退款将于3个工作日内到账!'));
                                }
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '取消失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                } elseif ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_0) {
                    if ($orderR->data['state'] == 2 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $refundTicketR = $this->yyRefundTicket($orderR->data['line_id'], $orderR->data['book_seating']);
                        if ($refundTicketR->ret) {
                            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                            if ($orderS->ret) {
                                //添加预订订单历史状态
                                $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                                if ($bookHistoryS->ret) {
                                    $this->commitTrans();
                                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {//班线车自动派单
                                        //消息通知
                                        if (C('SMS_ON')) {
                                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                            $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                                            $this->pushShortMessage($driverR->data['cellphone'], array($orderR->data['order_no'], $passengerR->data['cellphone']), \SMSUtil::TEMP_ID_PSCANCEL, $orderR->data['mchid']);
                                        }

                                        //微信通知司机
                                        if (C('WX_TEMPLATE_ON')) {
                                            //{{first.DATA}}
                                            //订单编号：{{keyword1.DATA}}
                                            //订单状态：{{keyword2.DATA}}
                                            //{{remark.DATA}}
                                            $temp = array(
                                                '乘客取消了['.$this->getOrderType($orderR->data['type']).']订单',
                                                $orderR->data['order_no'],
                                                '已取消',
                                                '给您带来的不便，敬请谅解！'
                                            );
                                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                                            $this->pushWeixinMessage(array('openid' => $driverR->data['openid'], 'account_id' => $orderR->data['driver_id']), $temp, 1, $orderR->data['mchid'], self::WECHAT_MSG_TYPE_1);
                                            unset($temp);
                                        }
                                    } else {
                                        //通知分台
                                    }

                                    return $this->output(new \ResultModel(true, '订单取消成功'));
                                } else {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                                }
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单取消失败'));
                        }
                    } else {
                        $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                        if ($orderS->ret) {
                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                $this->commitTrans();
                                return $this->output(new \ResultModel(true, '订单取消成功'));
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                    }
                }
            }
            unset($orderR->data['branchid']);
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
        }
        return $this->output($orderR);
    }

    /**
     * 学生号-参团定制包车下单
     * @param int $student_customized_line_id 定制包（必须）
     * @param int $number 人数（必须）
     */
    public function doOrderStudentCustomizedOrder($student_customized_line_id, $number)
    {
        $where['mchid'] = $this->mchid;
        $where['student_customized_line_id'] = $student_customized_line_id;
        $where['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2;
        $studentCustomizedLineR = $this->find($where, 'YyStudentCustomizedLine');
        if (!$studentCustomizedLineR->ret) {
            return $this->output(new \ResultModel(false, '组团信息异常'));
        }
        if ($studentCustomizedLineR->data['remain_count'] < $number) {
            return $this->output(new \ResultModel(false, '余座不足，请刷新后再试'));
        }
        if ($this->state->user_id == $studentCustomizedLineR->data['passenger_id']) {
            return $this->output(new \ResultModel(false, '请勿重复参与该团'));
        } else {
            $orderWhere['passenger_id'] = $this->state->user_id;
            $orderWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2;
            $orderWhere['student_customized_line_id'] = $student_customized_line_id;
            $studentCustomizedOrderR = $this->find($orderWhere, 'YyStudentCustomizedOrder');
            if ($studentCustomizedOrderR->ret) {
                return $this->output(new \ResultModel(false, '请勿重复参与该团'));
            }
        }

        $studentCustomizedOrderData['passenger_id'] = $this->state->user_id;
        $studentCustomizedOrderData['student_customized_order_no'] = $this->createStudentCustomizedOrderNo();
        $studentCustomizedOrderData['student_customized_line_id'] = $student_customized_line_id;
        $studentCustomizedOrderData['price'] = $studentCustomizedLineR->data['single_join_price'] * $number;
        $studentCustomizedOrderData['count'] = $number;
        $studentCustomizedOrderData['mchid'] = $this->mchid;
        $studentCustomizedOrderA = $this->add('YyStudentCustomizedOrder', $studentCustomizedOrderData);
        if (!$studentCustomizedOrderA->ret) {
            $this->output(new \ResultModel(false, '下单失败'));
        }

        return $this->output(new \ResultModel(true, array('student_customized_order_id' => $studentCustomizedOrderA->data, 'student_customized_order_no' => $studentCustomizedOrderData['student_customized_order_no'],'price' => $studentCustomizedOrderData['price'])));
    }

    /**
     * 学生号-取消参团定制包车
     * @param int $student_customized_order_id 参团订单id
     */
    public function doCancelStudentCustomizedOrder($student_customized_order_id)
    {
        $where['mchid'] = $this->mchid;
        $where['student_customized_order_id'] = $student_customized_order_id;
        $where['passenger_id'] = $this->state->user_id;
        $studentCustomizedOrderR = $this->find($where, 'YyStudentCustomizedOrder');
        if (!$studentCustomizedOrderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        $lineWhere['student_customized_line_id'] = $studentCustomizedOrderR->data['student_customized_line_id'];
        $lineWhere['status'] = ['in',[\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2, \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3]];
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if (!$studentCustomizedLineR->ret) {
            return $this->output(new \ResultModel(false, '取消失败'));
        }

        if ($studentCustomizedLineR->data['status'] == \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3) {//拼团结束前可以取消
            if ($studentCustomizedLineR->data['order_expire_time'] < date('Y-m-d H:i:s', time())) {
                return $this->output(new \ResultModel(false, '已过组团结束时间无法取消'));
            }
        }

        if ($studentCustomizedOrderR->data['status'] == \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_3) {
            return $this->output(new \ResultModel(false, '重复取消'));
        } elseif ($studentCustomizedOrderR->data['status'] == \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2) {//取消并退款
            if ($studentCustomizedOrderR->data['is_pay'] != \CommonDefine::PAY_STATUS_1) {
                return $this->output(new \ResultModel(false, '订单异常，取消失败'));
            }

            $this->startTrans();
            //更新余座
            $updateRemainCountR = $this->updateStudentCustomizedLineRemainCount($studentCustomizedOrderR->data['student_customized_line_id'], $studentCustomizedOrderR->data['count']);
            if (!$updateRemainCountR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单异常，取消失败'));
            }

            //创建微信退款
            $refundStudentCustomizedR = $this->refundStudentCustomizedOrder($student_customized_order_id);
            if (!$refundStudentCustomizedR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }

            if ($studentCustomizedLineR->data['passenger_id'] == $this->state->user_id) {
                $orderWhere['student_customized_line_id'] = $studentCustomizedLineR->data['student_customized_line_id'];
                $orderWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2;
                $studentCustomizedOrdersR = $this->select($orderWhere, null, null, 'student_customized_order_id ASC', 'YyStudentCustomizedOrder');
                if ($studentCustomizedOrdersR->count == 1) {//只有自己
                    $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_6;
                } else {
                    $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2;
                    $updateStudentCustomizedLineData['passenger_id'] = $studentCustomizedOrdersR->data[1]['passenger_id'];
                }

                $updateStudentCustomizedLineData['student_customized_line_id'] = $studentCustomizedOrderR->data['student_customized_line_id'];
                $updateStudentCustomizedLineS = $this->save('YyStudentCustomizedLine', $updateStudentCustomizedLineData);
                if (!$updateStudentCustomizedLineS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '取消失败'));
                }
            }

            $studentCustomizedOrderData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_3;
            $studentCustomizedOrderData['student_customized_order_id'] = $student_customized_order_id;
            $studentCustomizedOrderS = $this->save('YyStudentCustomizedOrder', $studentCustomizedOrderData);
            if (!$studentCustomizedOrderS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }

            $this->commitTrans();
            return  $this->output(new \ResultModel(true, '退款中，退款将于3个工作日内到账!'));
        } else {
            if ($studentCustomizedLineR->data['passenger_id'] == $this->state->user_id) {
                $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_6;
                $updateStudentCustomizedLineData['student_customized_line_id'] = $studentCustomizedOrderR->data['student_customized_line_id'];
                $updateStudentCustomizedLineS = $this->save('YyStudentCustomizedLine', $updateStudentCustomizedLineData);
                if (!$updateStudentCustomizedLineS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '取消失败'));
                }
            }

            $studentCustomizedOrderData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_3;
            $studentCustomizedOrderData['student_customized_order_id'] = $student_customized_order_id;
            $studentCustomizedOrderS = $this->save('YyStudentCustomizedOrder', $studentCustomizedOrderData);
            if (!$studentCustomizedOrderS->ret) {
                return $this->output(new \ResultModel(false, '取消失败'));
            }
            return $this->output(new \ResultModel(true, '取消成功'));
        }
        return $this->output(new \ResultModel(false, '订单异常，取消失败'));
    }

    /**
     * 学生号-定制接送续费
     * @param int $student_customized_line_id 当前定制接送线路id
     */
    public function doRenewStudentCustomizedOrder($student_customized_line_id)
    {
        $parent_student_customized_line_id = $student_customized_line_id;
        $parentLineWhere['student_customized_line_id'] = $parent_student_customized_line_id;
        $parentStudentCustomizedLineR = $this->find($parentLineWhere, 'YyStudentCustomizedLine');
        if (!$parentStudentCustomizedLineR->ret) {
            return $this->output(new \ResultModel(false, '定制接送线路异常'));
        }

        $parentOrderWhere['student_customized_line_id'] = $parentStudentCustomizedLineR->data['student_customized_line_id'];
        $parentOrderWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2;
        $parentOrderWhere['passenger_id'] = $this->state->user_id;
        $parentStudentCustomizedOrderR = $this->find($parentOrderWhere, 'YyStudentCustomizedOrder');
        if (!$parentStudentCustomizedOrderR->ret) {
            return $this->output(new \ResultModel(false, '定制接送订单异常'));
        }

        $where['parent_student_customized_line_id'] = $parent_student_customized_line_id;
        $studentCustomizedLineR = $this->find($where, 'YyStudentCustomizedLine');
        if ($studentCustomizedLineR->ret) {
            $orderWhere['student_customized_line_id'] = $studentCustomizedLineR->data['student_customized_line_id'];
            $orderWhere['passenger_id'] = $this->state->user_id;
            $studentCustomizedOrderR = $this->find($orderWhere, 'YyStudentCustomizedOrder');
            if ($studentCustomizedOrderR->ret) {
                //                if($studentCustomizedOrderR->data['status'] == \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_1){
                //                    return $this->output(new \ResultModel(true, '系统异常，重复生成续费订单'));
                //                }
                if ($studentCustomizedOrderR->data['status'] == \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2) {
                    return $this->output(new \ResultModel(true, '已经续费成功，请勿重复续费'));
                }
            }

            $studentCustomizedOrderData['passenger_id'] = $this->state->user_id;
            $studentCustomizedOrderData['student_customized_order_no'] = $this->createStudentCustomizedOrderNo();
            $studentCustomizedOrderData['student_customized_line_id'] = $studentCustomizedLineR->data['student_customized_line_id'];
            $studentCustomizedOrderData['price'] = $studentCustomizedLineR->data['single_join_price'] * $parentStudentCustomizedOrderR->data['count'];
            $studentCustomizedOrderData['count'] = $parentStudentCustomizedOrderR->data['count'];
            $studentCustomizedOrderData['mchid'] = $this->mchid;
            $studentCustomizedOrderA = $this->add('YyStudentCustomizedOrder', $studentCustomizedOrderData);
            if (!$studentCustomizedOrderA->ret) {
                $this->output(new \ResultModel(false, '续费失败'));
            }
        } else {//创建新的线路
            $cardR = $this->find(array('card_id' => $parentStudentCustomizedLineR->data['card_id']), 'Card');
            if (!$cardR->ret) {
                $this->output(new \ResultModel(false, '续费失败'));
            }

            $studentCustomizedLineData['parent_student_customized_line_id'] = $parent_student_customized_line_id;
            $studentCustomizedLineData['student_customized_line_no'] = $this->createStudentCustomizedLineNo();
            $studentCustomizedLineData['passenger_id'] = $this->state->user_id;
            $studentCustomizedLineData['start_time'] = date('Y-m-d 00:00:00', strtotime($parentStudentCustomizedLineR->data['end_time']." +1 day"));
            $studentCustomizedLineData['end_time'] = date('Y-m-d 23:59:59', strtotime($studentCustomizedLineData['start_time']." +".$cardR->data['times']." day"));
            $studentCustomizedLineData['single_join_price'] = $cardR->data['price'];
            $studentCustomizedLineData['total_count'] = $parentStudentCustomizedLineR->data['total_count'];
            $studentCustomizedLineData['remain_count'] = $parentStudentCustomizedLineR->data['total_count'];
            $studentCustomizedLineData['mchid'] = $this->mchid;
            $studentCustomizedLineData['branchid'] = $parentStudentCustomizedLineR->data['branchid'];
            $studentCustomizedLineData['order_expire_time'] = $studentCustomizedLineData['end_time'];
            $protectTimeConfig = C('YY.PROTECT_TIME') ;
            $studentCustomizedLineData['protect_time'] = $cardR->data['times'] > $protectTimeConfig ? date('Y-m-d 00:00:00', strtotime($parentStudentCustomizedLineR->data['end_time']." -$protectTimeConfig day")) : date('Y-m-d H:i:s', time());
            $studentCustomizedLineData['card_id'] = $parentStudentCustomizedLineR->data['card_id'];
            $studentCustomizedLineData['start_address_remark'] = $parentStudentCustomizedLineR->data['start_address_remark'];
            $studentCustomizedLineData['start_latitude'] = $parentStudentCustomizedLineR->data['start_latitude'];
            $studentCustomizedLineData['start_longitude'] = $parentStudentCustomizedLineR->data['start_longitude'];
            $studentCustomizedLineData['end_address_remark'] = $parentStudentCustomizedLineR->data['end_address_remark'];
            $studentCustomizedLineData['end_latitude'] = $parentStudentCustomizedLineR->data['end_latitude'];
            $studentCustomizedLineData['end_longitude'] = $parentStudentCustomizedLineR->data['end_longitude'];
            $studentCustomizedLineData['everyday_times'] = $parentStudentCustomizedLineR->data['everyday_times'];
            $studentCustomizedLineData['go_school_time_am'] = $parentStudentCustomizedLineR->data['go_school_time_am'];
            $studentCustomizedLineData['leave_school_time_am'] = $parentStudentCustomizedLineR->data['leave_school_time_am'];
            $studentCustomizedLineData['go_school_time_pm'] = $parentStudentCustomizedLineR->data['go_school_time_pm'];
            $studentCustomizedLineData['leave_school_time_pm'] = $parentStudentCustomizedLineR->data['leave_school_time_pm'];

            $this->startTrans();
            $studentCustomizedLineA = $this->add('YyStudentCustomizedLine', $studentCustomizedLineData);
            if (!$studentCustomizedLineA->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '续费失败'));
            }

            $studentCustomizedOrderData['passenger_id'] = $this->state->user_id;
            $studentCustomizedOrderData['student_customized_order_no'] = $this->createStudentCustomizedOrderNo();
            $studentCustomizedOrderData['student_customized_line_id'] = $studentCustomizedLineA->data;
            $studentCustomizedOrderData['price'] = $studentCustomizedLineData['single_join_price'] * $parentStudentCustomizedOrderR->data['count'];
            $studentCustomizedOrderData['count'] = $parentStudentCustomizedOrderR->data['count'];
            $studentCustomizedOrderData['mchid'] = $this->mchid;
            $studentCustomizedOrderA = $this->add('YyStudentCustomizedOrder', $studentCustomizedOrderData);
            if (!$studentCustomizedOrderA->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '续费失败'));
            }
            $this->commitTrans();
        }
        return $this->output(new \ResultModel(true, array('student_customized_order_id' => $studentCustomizedOrderA->data, 'student_customized_order_no' => $studentCustomizedOrderData['student_customized_order_no'],'price' => $studentCustomizedOrderData['price'])));
    }

    /**
     * 学生号-定制包车批量生成日订单
     * @param int $student_customized_line_id 组团线路编号
     * @param string $start_date 开始日期
     * @param int $start_type 1-早晨上学；2-早晨放学；3-下午上学；4-下午放学；
     * @return null|\resultModel
     */
    public function doBatchStudentCustomizedEverydayOrders($student_customized_line_id, $start_date, $everyday_type = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_EVERYDATY_1)
    {
        $where['student_customized_line_id'] = $student_customized_line_id;
        $where['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_4;
        $where['start_time'] = ['elt', $start_date];
        $where['end_time'] = ['egt', $start_date];
        $studentCustomizedLineR = $this->find($where, 'YyStudentCustomizedLine');
        if (!$studentCustomizedLineR->ret) {
            return $this->output(new \ResultModel(false, '批量生成失败'));
        }

        $orderWhere['student_customized_line_id'] = $student_customized_line_id;
        $orderWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_ORDER_STATUS_2;
        $studentCustomizedOrdersR = $this->select($orderWhere, null, null, null, 'YyStudentCustomizedOrder');
        if (!$studentCustomizedOrdersR->ret) {
            return $this->output(new \ResultModel(false, '批量生成失败'));
        }

        $start_time = '';
        switch ($everyday_type) {
            case \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_EVERYDATY_1:{
                $start_time = $start_date. " ".$studentCustomizedLineR->data['go_school_time_am'];
                break;
            }
            case \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_EVERYDATY_2:{
                $start_time = $start_date. " ".$studentCustomizedLineR->data['leave_school_time_am'];
                break;
            }
            case \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_EVERYDATY_3:{
                $start_time = $start_date. " ".$studentCustomizedLineR->data['go_school_time_pm'];
                break;
            }
            case \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_EVERYDATY_4:{
                $start_time = $start_date. " ".$studentCustomizedLineR->data['leave_school_time_pm'];
                break;
            }
            default:{
                return $this->output(new \ResultModel(false, '批量生成失败'));
            }
        }

        $orderData['driver_id'] = $studentCustomizedLineR->data['driver_id'];
        $orderData['type'] = \CommonDefine::ORDER_TYPE_10;
        $orderData['mchid'] = $studentCustomizedLineR->data['mchid'];
        $orderData['line_id'] = $student_customized_line_id;
        $orderData['branchid'] = $studentCustomizedLineR->data['branchid'];
        $orderData['drive_model'] = 1;
        $orderData['start_time'] = $start_time;
        $orderData['rob_order'] = \CommonDefine::ROB_ORDER_0;
        $orderData['pay_mode'] = \CommonDefine::ORDER_PAYMENT_MODE_1;
        $orderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $orderData['state'] = \CommonDefine::ORDER_STATE_2;
        $orderData['start_address_remark'] = $studentCustomizedLineR->data['start_address_remark'];
        ;
        $orderData['start_latitude'] = $studentCustomizedLineR->data['start_latitude'];
        $orderData['start_longitude'] = $studentCustomizedLineR->data['start_longitude'];
        $orderData['end_address_remark'] = $studentCustomizedLineR->data['end_address_remark'];
        $orderData['end_latitude'] = $studentCustomizedLineR->data['end_latitude'];
        $orderData['end_longitude'] = $studentCustomizedLineR->data['end_longitude'];
        $orderData['price'] = 0;
        $orderData['offer_price'] = 0;
        $orderData['split'] = 0;
        $orderData['real_price'] = 0;
        $currentTime = date('Y-m-y H:i:s', time());
        $orderData['create_time'] = $currentTime;
        $orderData['update_time'] = $currentTime;
        $this->startTrans();
        foreach ($studentCustomizedOrdersR->data as $k => $studentCustomizedOrder) {
            $orderData['passenger_id'] = $studentCustomizedOrder['passenger_id'];
            $orderData['order_no'] = $this->createOrderNoNew();
            $passengerR = $this->find(array('passenger_id' => $studentCustomizedOrder['passenger_id']), 'Passenger');
            if (!$passengerR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '批量生成失败'));
            }
            $orderData['reseverd_phone'] = $passengerR->data['cellphone'];
            $orderData['book_seating'] = $studentCustomizedOrder['count'];
            $orderA = $this->add('Order', $orderData);
            if (!$orderA->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '批量生成失败'));
            }
        }

        $this->commitTrans();
        return $this->output(new \ResultModel(true, '批量生成成功，共生成：'.$studentCustomizedOrdersR->count));
    }

    /**
     * 组团定制包车下单
     * @param integer $line_class_train_id 线路班次编号
     * @param integer $passenger_contact_ids 常用联系人列表
     * @param integer $student_order_pay_mode 学生号支付模式：1-统一支付；2-AA支付
     * @param string $reseverd_phone 电话（默认为空）
     * @param string $reseverd_info 备注（默认为空）
     * @param string $start_address_remark 出发地
     * @param string $start_latitude 开始纬度
     * @param string $start_longitude 开始经度
     * @param string $end_address_remark 目的地
     * @param string $end_latitude 结束纬度
     * @param string $end_longitude 结束经度
     */
    public function doStudentGroupOrder($line_class_train_id, $passenger_contact_ids, $student_order_pay_mode = 1, $reseverd_phone = null, $reseverd_info = null, $start_address_remark, $start_latitude, $start_longitude, $end_address_remark, $end_latitude, $end_longitude)
    {
        $passengerCount = 1;
        $passenger_contact_idsArr = [];
        if (!empty($passenger_contact_ids)) {
            $passenger_contact_idsArr = explode(',', $passenger_contact_ids);
            $passengerCount = count($passenger_contact_idsArr) + 1;
        }

        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'YyLineClassTrain');
        if (!$yyLineClassTrainR->ret) {
            return $this->output(new \ResultModel(false, '下单失败'));
        }
        //        $passengerContactsWhere = "passenger_id in ($passenger_contact_ids)";
        //        $passengerContactsCountR = $this->count($passengerContactsWhere, 'PassengerContacts');
        //        if($passengerContactsCountR->data != count($passenger_contact_idsArr)){
        //            return $this->output(new \ResultModel(false, '下单失败'));
        //        }

        $yylineClassR = $this->find(array('id' => $yyLineClassTrainR->data['line_class_id']), 'YyLineClass');
        if (!$yylineClassR->ret) {
            return $this->output(new \ResultModel(false, '下单失败'));
        }

        if ($yyLineClassTrainR->data['remain_tickets'] < $passengerCount) {
            return $this->output(new \ResultModel(false, '余座不足，请重新选择'));
        }
        $this->data['start_time'] = $yyLineClassTrainR->data['start_date']." ".$yylineClassR->data['start_time'];

        if (!$this->_checkTimeIsPassSellTime(time(), $line_class_train_id)) {
            return $this->output(new \ResultModel(false, '已过售票时间'));
        }

        $checkRet = $this->checkIsAllowOrder($this->mchid, $this->state->user_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        //班线车订单模式，默认自动派单
        $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
        //        $lineClassAppointConfigR = $this->find(array('key'=>\CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
        //        if($lineClassAppointConfigR->ret){
        //            $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
        //        }

        $this->startTrans();
        $group_order_ids_arr = array();
        $studentOrderGroupData['passenger_id'] = $this->state->user_id;
        $studentOrderGroupData['student_order_pay_mode'] = $student_order_pay_mode;
        $studentOrderGroupData['total_book_seating'] = $passengerCount;
        $studentOrderGroupA = $this->add('YyStudentOrderGroup', $studentOrderGroupData);
        if (!$studentOrderGroupA->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '下单失败'));
        }

        $orderData['passenger_id'] = $this->state->user_id;
        $orderData['is_launch'] = 1;
        $orderData['book_seating'] = 1;
        $orderData['reseverd_phone'] = $reseverd_phone;
        $orderData['student_order_group_id'] = $studentOrderGroupA->data;
        $orderData['type'] = \CommonDefine::ORDER_TYPE_8;
        $orderData['student_order_type'] = \YyCommonDefine::STUDENT_ORDER_TYPE_2;
        $orderData['mchid'] = $yyLineClassTrainR->data['mchid'];
        $orderData['line_id'] = $line_class_train_id;
        $orderData['branchid'] = $yyLineClassTrainR->data['branchid'];
        $orderData['start_address_code'] = $yylineClassR->data['start_address_code'];
        $orderData['end_address_code'] = $yylineClassR->data['end_address_code'];
        $orderData['drive_model'] = 1;
        $orderData['start_time'] = $yyLineClassTrainR->data['start_date']." ".$yylineClassR->data['start_time'];
        $orderData['order_no'] = $this->createOrderNo();
        $orderData['rob_order'] = $robOrder;
        $orderData['pay_mode'] = $payMode;
        $orderData['is_draw'] = \CommonDefine::DRAW_TICKET_0;
        $orderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
        $orderData['state'] = \CommonDefine::ORDER_STATE_1;
        $orderData['start_address_remark'] = $start_address_remark;
        $orderData['start_latitude'] = $start_latitude;
        $orderData['start_longitude'] = $start_longitude;
        $orderData['end_address_remark'] = $end_address_remark;
        $orderData['end_latitude'] = $end_latitude;
        $orderData['end_longitude'] = $end_longitude;
        $currentTime = date('Y-m-y H:i:s', time());
        $orderData['create_time'] = $currentTime;
        $orderData['update_time'] = $currentTime;

        switch ($student_order_pay_mode) {
            case \YyCommonDefine::STUDENT_ORDER_PAY_MODE_1:{
                $mch = M('AnnualFee')->where(array('mchid' => $yyLineClassTrainR->data['mchid']))->find();
                $orderData['price'] = $yyLineClassTrainR->data['price'] * $passengerCount;
                $orderData['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                $orderData['split'] = $mch['split'] / 100;
                $orderData['real_price'] = $orderData['price'];
                $orderData['student_times'] = $passengerCount;
                $orderData['book_seating'] = $passengerCount;

                if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                        $orderData['state'] = \CommonDefine::ORDER_STATE_2;
                        $orderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;

                        $lineClassTrainDriverR = $this->getYyLineClassTrainDriver($line_class_train_id, $passengerCount);
                        if (!$lineClassTrainDriverR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败'));
                        }

                        $orderData['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                        $mainOrderA = $this->add('Order', $orderData);
                        if (!$mainOrderA->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败'));
                        }
                        $group_order_ids_arr[] = $mainOrderA->data;

                        $this->commitTrans();
                        //通知服务
                        $this->_batchSendGroupOrderMessage($group_order_ids_arr, $orderData['state']);
                        return $this->output(new \ResultModel(true, array('order_id' => $mainOrderA->data, 'order_no' => $orderData['order_no'],'price' => $orderData['price'])));
                    } else {//手动派单暂不支持
                        return $this->output(new \ResultModel(false, '下单失败!'));
                    }
                } else {
                    $mainOrderA = $this->add('Order', $orderData);
                    if (!$mainOrderA->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                    //支付后再创建几个订单
                    $this->commitTrans();
                    return $this->output(new \ResultModel(true, array('order_id' => $mainOrderA->data, 'order_no' => $orderData['order_no'],'price' => $orderData['price'])));
                }
                break;
            }
            case \YyCommonDefine::STUDENT_ORDER_PAY_MODE_2:{
                $mch = M('AnnualFee')->where(array('mchid' => $yyLineClassTrainR->data['mchid']))->find();
                $orderData['price'] = $yyLineClassTrainR->data['price'] * 1;
                $orderData['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                $orderData['split'] = $mch['split'] / 100;
                $orderData['real_price'] = $orderData['price'];
                $orderData['student_times'] = 1;

                if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {
                    $orderData['state'] = \CommonDefine::ORDER_STATE_2;
                    $orderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;

                    $lineClassTrainDriverR = $this->getYyLineClassTrainDriver($line_class_train_id, 1);
                    if (!$lineClassTrainDriverR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }

                    $orderData['driver_id'] = $lineClassTrainDriverR->data['driver_id'];
                    $mainOrderA = $this->add('Order', $orderData);
                    if (!$mainOrderA->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                    $group_order_ids_arr[] = $mainOrderA->data;

                    $draw_ticket = true;
                    if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                        $draw_ticket = false;
                    }
                    $batchOrderGroupR = $this->_batchOrderGroup($line_class_train_id, $passenger_contact_idsArr, $student_order_pay_mode, $orderData, $group_order_ids_arr, $studentOrderGroupA->data, $draw_ticket);
                    if (!$batchOrderGroupR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }

                    $this->commitTrans();

                    $this->_batchSendGroupOrderMessage($group_order_ids_arr);
                    return $this->output(new \ResultModel(true, array('order_id' => $mainOrderA->data, 'order_no' => $orderData['order_no'],'price' => $orderData['price'])));
                } else {//手动派单暂不支持
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败!'));
                }
                break;
            }
            default:{
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        }
    }

    /**
     * 家长互助下单
     * @param integer $line_parent_help_id 家长互助线路id
     * @param integer $book_seating 下单座位数
     * @param string $start_longitude 出发经度
     * @param string $start_latitude 出发纬度
     */
    public function doParentHelpOrder($line_parent_help_id, $book_seating = 1, $start_longitude = null, $start_latitude = null)
    {
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }
        $this->data['reseverd_phone'] = $passengerR->data['cellphone'];

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        $lineParentHelpR = $this->find(array('line_parent_help_id' => $line_parent_help_id, 'is_del' => 0), 'YyLineParentHelp');
        if ($lineParentHelpR->data['status'] != 1) {
            return $this->output(new \ResultModel(false, '该家长互助已关闭'));
        }

        $orderWhere = " passenger_id = ".$passenger_id. " AND line_id = ".$lineParentHelpR->data['line_parent_help_id']. " AND state < 6 ";
        $orderR = $this->find($orderWhere, 'Order');
        if ($orderR->ret) {
            return $this->output(new \ResultModel(false, '您已经下过该线路的订单，前往个人中心查看'));
        }

        if ($lineParentHelpR->data['residual_seating'] < $book_seating) {
            return $this->output(new \ResultModel(false, '剩余座位数不足'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $currentTime = date('Y-m-d H:i:s', time());
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_9;
                $this->data['driver_id'] = $lineParentHelpR->data['driver_id'];
                $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                $this->data['mchid'] = $lineParentHelpR->data['mchid'];
                $this->data['line_id'] = $lineParentHelpR->data['line_parent_help_id'];
                $this->data['branchid'] = $lineParentHelpR->data['branchid'];

                $this->data['start_longitude'] = $start_longitude;
                $this->data['start_latitude'] = $start_latitude;
                $this->data['start_address_code'] = $lineParentHelpR->data['start_address_code'];
                $this->data['start_address_remark'] = $lineParentHelpR->data['start_address_remark'];
                $this->data['end_longitude'] = $lineParentHelpR->data['end_longitude'];
                $this->data['end_latitude'] = $lineParentHelpR->data['end_latitude'];
                $this->data['end_address_code'] = $lineParentHelpR->data['end_address_code'];
                $this->data['end_address_remark'] = $lineParentHelpR->data['end_address_remark'];
                $this->data['start_region_name'] = $lineParentHelpR->data['start_name'];
                $this->data['end_region_name'] = $lineParentHelpR->data['end_name'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $lineParentHelpR->data['start_time'];
                $this->data['book_seating'] = $book_seating;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->data['create_time'] = $currentTime;
                $this->data['update_time'] = $currentTime;
                $this->data['price'] = $lineParentHelpR->data['price'] * $book_seating;
                $this->data['real_price'] = $this->data['price'];

                $this->startTrans();
                //添加订单
                $r = $this->add('Order');
                if ($r->ret) {
                    if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $lineParentHelpS = $this->save('YyLineParentHelp', array('line_parent_help_id' => $line_parent_help_id, 'residual_seating' => ($lineParentHelpR->data['residual_seating'] - $book_seating)));
                        if (!$lineParentHelpS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败'));
                        }
                        $this->commitTrans();
                        $driverR = $this->find(array('driver_id' => $this->data['driver_id']), 'Driver');
                        $cellphone = $this->encryptionCellphoneByCellphone($passengerR->data['cellphone']);
                        //通知服务,通知司机
                        if (C('SMS_ON')) {
                            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                            $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array($this->data['order_no'], $cellphone), \SMSUtil::TEMP_ID_DRIVER_NEW_ORDER, $driverR->data['mchid']);
                        }

                        if (C('WX_TEMPLATE_ON')) {
                            //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                            //您有新的订单了
                            //订单编号：C1510170029132
                            //时间：业务经理
                            //有问题请拨************
                            $temp = array(
                                '您有新的['.$this->getOrderType($this->data['type']).']订单了',
                                $this->data['order_no'],
                                $currentTime,
                                '乘客电话:'.$cellphone.'请及时处理!'
                            );
                            $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_5);
                            unset($temp);
                        }
                    } else {
                        $this->commitTrans();
                    }

                    return $this->output(new \ResultModel(true, ['order_id' => $r->data]));
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败'));
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        }
        return $this->output($r);
    }

    /**
     * 学生号购买次卡
     * @param integer $card_id 订单编号
     */
    public function doOrderCard($card_id)
    {
        $where['mchid'] = $this->mchid;
        $where['card_id'] = $card_id;
        $cardR = $this->find($where, 'Card');
        if ($cardR->ret) {
            $orderData['card_id'] = $card_id;
            $orderData['passenger_id'] = $this->state->user_id;
            $orderData['order_card_no'] = $this->createOrderCardNo();
            $orderData['price'] = $cardR->data['price'];
            $orderData['mchid'] = $this->mchid;
            $orderData['times'] = $cardR->data['times'];
            $orderCardA = $this->add('OrderCard', $orderData);
            if ($orderCardA->ret) {
                return $this->output(new \ResultModel(true, ['order_card_no' => $orderData['order_card_no'], 'price' => $orderData['price']]));
            }
        }
        return $this->output(new \ResultModel(false, '下单失败'));
    }

    /**
     * 是否已过退票时间
     * @param $order_id
     * @return ResultModel
     */
    private function _checkeIsAllowRefundTicket($order_id)
    {
        $ret = new \ResultModel(false);
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            return $ret;
        }

        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'YyLineClassTrain');
        if ($yyLineClassTrainR->ret) {
            $yyLineClassR = $this->find(array('id' => $yyLineClassTrainR->data['line_class_id']), 'YyLineClass');
            if ($yyLineClassR->ret) {
                $refund_time_set = strtotime($orderR->data['start_time']) - ($yyLineClassR->data['refund_time_set'] * 60);
                if (time() < $refund_time_set) {
                    $ret->ret = true;
                    return $ret;
                }
            }
        }

        return $ret;
    }

    /**
     * 检测时间是否已过售票时间
     */
    private function _checkTimeIsPassSellTime($time, $lineClassTrainId)
    {
        $lineClassTrainR = $this->find(array('line_class_train_id' => $lineClassTrainId), 'YyLineClassTrain');
        if (!$lineClassTrainR->ret) {
            return false;
        }

        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'YyLineClass');
        if (!$lineClassR->ret) {
            return false;
        }

        if ($lineClassR->data['start_time_type'] == 1) {
            $startTime = $lineClassR->data['start_time'];

            $startDateTime = $lineClassTrainR->data['start_date'] ." ". $startTime;
            if (strtotime($startDateTime) < $time) {
                return false;
            }
        } else {
            if (\getTimeNumber($lineClassR->data['end_latest_time']) > \getTimeNumber($lineClassR->data['start_earliest_time'])) {//当天
                $startTime = $lineClassR->data['end_latest_time'];

                $startDateTime = $lineClassTrainR->data['start_date'] ." ". $startTime;
                if (strtotime($startDateTime) < $time) {
                    return false;
                }
            } else {//第二天
                $startTime = $lineClassR->data['start_earliest_time'];
                $startNextDate = date("Y-m-d", strtotime($lineClassTrainR->data['start_date']) + 86400);
                $startDateTime = $startNextDate ." ". $startTime;
                if (strtotime($startDateTime) < $time) {
                    return false;
                }
            }
        }

        if (\getTimeNumber($lineClassR->data['stop_sell_time']) != 0) {
            $stopSellDateTime = date('Y-m-d H:i:s', strtotime($lineClassTrainR->data['start_date'] ." ". $lineClassR->data['stop_sell_time']."-".$lineClassR->data['stop_sell_number']."day"));

            if (strtotime($stopSellDateTime) < $time) {
                return false;
            }
        }

        return true;
    }

    //批量生成订单
    private function _batchOrderGroup($line_class_train_id, $passenger_contact_idsArr, $student_order_pay_mode, $orderData, $group_order_ids_arr, $student_order_group_id, $draw_ticket = true)
    {
        $yyLineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'YyLineClassTrain');
        if (!$yyLineClassTrainR->ret) {
            return new \ResultModel(false, '下单失败');
        }

        $contactPassengersR = $this->_getContactsPassengerIds($passenger_contact_idsArr);
        if (!$contactPassengersR->ret) {
            return new \ResultModel(false, '下单失败');
        }

        foreach ($contactPassengersR->data as $k => $contacts_passenger_id) {
            $orderData['passenger_id'] = $contacts_passenger_id;
            $orderData['is_launch'] = 0;
            $orderData['order_no'] = $this->createOrderNo();
            $passengerR = $this->find(array('passenger_id' => $contacts_passenger_id), 'Passenger');
            $orderData['reseverd_phone'] = $passengerR->ret ? $passengerR->data['cellphone'] : "";
            $subOrderA = $this->add('Order', $orderData);
            if (!$subOrderA->ret) {
                return new \ResultModel(false, '下单失败');
            }
            $group_order_ids_arr[] = $subOrderA->data;
        }

        if ($draw_ticket) {
            //出票成功
            $upLineClassTrainData['line_class_train_id'] = $yyLineClassTrainR->data['line_class_train_id'];
            $upLineClassTrainData['remain_tickets'] = $yyLineClassTrainR->data['remain_tickets'] - (count($passenger_contact_idsArr) + 1);
            $yyLineClassTrainS = $this->save('YyLineClassTrain', $upLineClassTrainData);
            if (!$yyLineClassTrainS->ret) {
                return new \ResultModel(false, '余票不足!');
            }
        }

        $updateStudentOrderGroupData['student_order_group_id'] = $student_order_group_id;
        $updateStudentOrderGroupData['group_order_ids'] = implode(',', $group_order_ids_arr);
        if (!$this->save('YyStudentOrderGroup', $updateStudentOrderGroupData)->ret) {
            return new \ResultModel(false, '下单失败!');
        }

        return new \ResultModel(true, $group_order_ids_arr);
    }

    //批量发送消息
    private function _batchSendGroupOrderMessage($group_order_ids_arr, $orderState = \CommonDefine::ORDER_STATE_1)
    {
        foreach ($group_order_ids_arr as $k => $group_order_id) {
            $this->sendSms($group_order_id, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
            $this->sendWxMessage($group_order_id, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0);
            if ($orderState == \CommonDefine::ORDER_STATE_2) {
                //通知乘客
                $this->sendSms($group_order_id, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                $this->sendWxMessage($group_order_id, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            }
        }
    }

    //获取联系人id
    private function _getContactsPassengerIds($passenger_contact_idsArr)
    {
        $passenger_contact_ids = implode(',', $passenger_contact_idsArr);
        $where = "passenger_contacts_id in($passenger_contact_ids)";
        $passengerContactsR = $this->select($where, null, null, null, 'PassengerContacts');
        if (!$passengerContactsR->ret) {
            return new \ResultModel(false);
        }
        if ($passengerContactsR->count != count($passenger_contact_idsArr)) {
            return new \ResultModel(false);
        }
        $contact_passenger_ids_arr = array();
        foreach ($passengerContactsR->data as $k => $v) {
            if (empty($v['contacts_passenger_id'])) {
                return new \ResultModel(false);
            }
            $contact_passenger_ids_arr[] = $v['contacts_passenger_id'];
            //            if(empty($contact_passenger_ids)){
            //                $contact_passenger_ids = $v['contacts_passenger_id'];
            //            }else{
            //                $contact_passenger_ids .= $v['contacts_passenger_id'];
            //            }
        }

        return new \ResultModel(true, $contact_passenger_ids_arr, count($contact_passenger_ids_arr));
    }

    /*
     * 学生号组团创建微信退款
     */
    protected function refundStudentCustomizedOrder($student_customized_order_id)
    {
        $where['student_customized_order_id'] = $student_customized_order_id;
        $studentCustomizedOrderR = $this->find($where, 'YyStudentCustomizedOrder');
        if (!$studentCustomizedOrderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($studentCustomizedOrderR->data['mchid'])) {
            return  $this->output(new \ResultModel(false, '商户支付异常，申请退款失败!'));
        }

        $refundR = $wxPayUtil->createRefund($studentCustomizedOrderR->data['student_customized_order_no'], $studentCustomizedOrderR->data['price'], $studentCustomizedOrderR->data['price']);
        if (!$refundR->ret) {
            return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
        }

        if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
            $this->transRollback();
            return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
        }

        $refundData['amount'] = $studentCustomizedOrderR->data['price'];
        $refundData['status'] = 3;
        $refundData['order_id'] = $student_customized_order_id;
        $refundData['account_type'] = 1;//乘客
        $refundData['account_id'] = $studentCustomizedOrderR->data['passenger_id'];
        $refundData['refund_id'] = $refundR->data['refund_id'];
        $refundData['refund_no'] = $refundR->data['out_trade_no'];
        $refundData['created'] = time();
        $refundData['description'] = "取消订单，全额退款";
        $refundData['charge'] = $refundR->data['charge'];
        $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
        $refundData['transaction_no'] = $refundR->data['transaction_id'];
        return $this->add('YyRefunds', $refundData);
    }
}

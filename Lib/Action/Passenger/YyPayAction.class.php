<?php
#
# CORS config for php
# Code by anrip[<EMAIL>]
#
namespace Passenger;
import('@/Action/Weixin/Wechat');
import('@/Action/Home/MemberAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 云裕众-支付
 *
 * <AUTHOR>
 */
class YyPayAction extends \PassengerCoreAction
{
    /**
     * 学生号-组团订单支付
     * @param string $student_customized_order_no 订单编号（必须）
     * @param string $openid 用户openid（非必须）
     * @param double $amount 支付金额（非必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     */
    public function doWxStudentCustomizedOrderPay($student_customized_order_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $studentCustomizedOrderNo = $this->data['student_customized_order_no'];

        $studentCustomizedOrderR = $this->find(array('student_customized_order_no' => $studentCustomizedOrderNo, 'is_pay' => 0), 'YyStudentCustomizedOrder');
        if(!$studentCustomizedOrderR->ret){
            $ret->data = '订单不存在';
            return $this->output($ret);
        }

        $lineWhere['student_customized_line_id'] = $studentCustomizedOrderR->data['student_customized_line_id'];
        $lineWhere['status'] = ['in', [\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_1,\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_2]];
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if(!$studentCustomizedLineR->ret){
            $ret->data = '组团已取消或已组团成功';
            return $this->output($ret);
        }
        if($studentCustomizedLineR->data['remain_count'] < $studentCustomizedOrderR->data['count']){
            $ret->data = '余座不足，请另行组团';
            return $this->output($ret);
        }

        $passengerR = $this->find(array('passenger_id' => $studentCustomizedOrderR->data['passenger_id']), 'Passenger');
        if(!$passengerR->ret){
            $ret->data = '用户信息错误';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($studentCustomizedOrderR->data['mchid']);
        if($payServiceR->ret){
            if(empty($passengerR->data['openid'])){
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid'];
        }else{
            if(empty($passengerR->data['openid_pay'])){
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $passengerR->data['openid_pay'];
        }

        if($studentCustomizedOrderR->data['price'] * 100 != $amount * 100){
            $ret->data = '订单金额错误';
            return $this->output($ret);
        }

        $this->data['account_id'] = $studentCustomizedOrderR->data['passenger_id'];
        $this->data['account_type'] = 0;
        $this->data['channel'] = $channel;

        $payHistoryR = $this->find(array('order_no' => $studentCustomizedOrderNo, 'openid' => $openid),'PayOrderHistory');
        if(!$payHistoryR->ret){
            $this->data['openid'] = $openid;
            $this->data['order_no'] = $studentCustomizedOrderNo;
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                $ret->data = '订单异常';
                return $this->output($ret);
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if(!$wxPayUtil->init($studentCustomizedOrderR->data['mchid'], $channel)){
            return  $this->output(new \ResultModel(false,'商户支付配置异常!'));
        }
        try{
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $studentCustomizedOrderNo, '代收',json_encode($extra), $amount, $this->getCipmchidByMchid($studentCustomizedOrderR->data['mchid'])->data['ciphertext']);
            if(!$charge){
                $ret->data = '支付失败，请稍后重试';
                return $this->output($ret);
            }

            if($charge['return_code'] == 'SUCCESS'){
                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        }catch (\Exception $e){
            \Log::write($e->getCode().$e->getMessage());
            $ret->data = $e->getCode().$e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请稍后重试';
        return $this->output($ret);
    }
}

?>

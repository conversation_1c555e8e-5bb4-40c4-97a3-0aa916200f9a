<?php

namespace Passenger;

use CommonDefine;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');
import('@/Action/Weixin/WechatAction');
/**
 * 订单模块
 *
 * <AUTHOR>
 */
class OrderAction extends \PassengerCoreAction
{
    public static $businessValueTypes = [
        \CommonDefine::ORDER_TYPE_1 => '拼车',
        \CommonDefine::ORDER_TYPE_2 => '包车',
        \CommonDefine::ORDER_TYPE_3 => '带货',
        \CommonDefine::ORDER_TYPE_4 => '代办',
        \CommonDefine::ORDER_TYPE_5 => '定制班线车',
        \CommonDefine::ORDER_TYPE_6 => '顺风车',
        \CommonDefine::ORDER_TYPE_7 => '快车',
        \CommonDefine::ORDER_TYPE_8 => '学生号',
        \CommonDefine::ORDER_TYPE_9 => '家长互助',
        \CommonDefine::ORDER_TYPE_10 => '学生号定制包车',
        \CommonDefine::ORDER_TYPE_11 => '出租车',
    ];

    /**
     * 预定司机
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doBookDriver($driver_id, $drive_mode, $start_address_code, $end_address_code, $address_code, $start_time, $is_insure, $book_seating = 1, $start_address_remark = null, $end_address_remark = null)
    {
        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'state');
        if ($r->ret) {
            if (\intval($r->data['state']) != 1) {
                $this->data['passenger_id'] = $this->state->user_id;
                $this->data['type'] = 1;
                $this->data['is_insure'] = intval($is_insure);
                //验证地点
                if (intval($drive_mode) === 1) {
                    $r = $this->checkingAddressCode($start_address_code);
                    if (!$r->ret) {
                        return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
                    }
                    $r = $this->checkingAddressCode($end_address_code);
                    if (!$r->ret) {
                        return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
                    }
                    if (intval($start_address_code) === intval($end_address_code)) {
                        return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
                    }
                } else {
                    $r = $this->checkingAddressCode($address_code);
                    if (!$r->ret) {
                        return $this->output(new \ResultModel(false, '乘车地格式不正确或地址不存在'));
                    }
                }
                $this->startTrans();
                //更新乘客信息
                $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'drive_mode' => $drive_mode, 'start_address_code' => $start_address_code, 'end_address_code' => $end_address_code, 'address_code' => $address_code, 'start_time' => $start_time, 'start_address_remark' => $start_address_remark, 'end_address_remark' => $end_address_remark));
                if ($r->ret) {
                    $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'price');
                    if ($r->ret) {
                        $this->data['price'] = $r->data['price'];
                        //添加订单
                        $r = $this->add('Order');
                        if ($r->ret) {
                            $order_id = $r->data;
                            //添加提醒
                            $r = $this->assignRemindTask($this->state->user_id, $driver_id, 'book seating', $order_id);
                            if ($r->ret) {
                                //添加预订订单历史状态
                                $r = $this->addBookOrderHistoryState($order_id, 'book seating');
                                if ($r->ret) {
                                    $this->commitTrans();
                                } else {
                                    $this->transRollback();
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        return $this->output(new \ResultModel(false, '车主不存在'));
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }

    /**
     * 预订拼车线路
     * @param int $line_id 线路id
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doMobileBookLine($line_id, $number = 1, $start_time = '', $reseverd_phone = null, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        //预留电话
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($number) || $number <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        $data = $this->find(array('id' => $line_id, 'is_del' => 0), 'Line');
        if ($data->data['status'] != 1) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        if ($start_longitude && $start_latitude) {
            if ($data->data['area_lat_start'] && $data->data['area_lng_start']) {
                $pointCalculationObj = new \PointCalculation();
                $circle['center']['lat'] = $data->data['area_lat_start'];
                $circle['center']['lng'] = $data->data['area_lng_start'];
                $circle['radius'] = $data->data['area_radius_start'];
                if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                    return $this->output(new \ResultModel(false, '暂不支持该区域'));
                }
            }
        }

        if ($end_longitude && $end_latitude) {
            if ($data->data['area_lat_end'] && $data->data['area_lng_end']) {
                $pointCalculationObj = new \PointCalculation();
                $circle['center']['lat'] = $data->data['area_lat_end'];
                $circle['center']['lng'] = $data->data['area_lng_end'];
                $circle['radius'] = $data->data['area_radius_end'];
                if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                    return $this->output(new \ResultModel(false, '暂不支持该区域'));
                }
            }
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $data->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_1;
                $this->data['mchid'] = $data->data['mchid'];
                $this->data['line_id'] = $data->data['id'];
                $this->data['branchid'] = $data->data['branchid'];

                $this->data['start_address_code'] = $data->data['start_address_code'];
                $this->data['end_address_code'] = $data->data['end_address_code'];
                $this->data['start_address_remark'] = empty($start_address_remark) ? $data->data['start_name'] : $start_address_remark;
                $this->data['end_address_remark'] = empty($end_address_remark) ? $data->data['end_name'] : $end_address_remark;
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $start_time;
                $this->data['book_seating'] = $number;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->startTrans();
                //更新乘客信息
                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'start_address_code' => $data->data['start_address_code'], 'end_address_code' => $data->data['end_address_code'], 'start_time' => $data->data['start_time']));
                if ($r) {
                    $mch = M('AnnualFee')->where(array('mchid' => $data->data['mchid']))->find();
                    $isChannel = false;
                    if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
                        //渠道价格
                        $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
                        $this->data['channel_account_id'] = $passengerR->data['invite_id'];
                        $isChannel = true;
                    }

                    if ($isChannel) {
                        $this->data['price'] = $data->data['price'] * $number;
                        $this->data['offer_channel_price'] = ($data->data['price'] - $data->data['channel_price']) * $number;
                        $channelPrice = $data->data['channel_price'] * $number;
                        $this->data['offer_price'] = $channelPrice - $channelPrice * $mch['split'] / 100;
                        $this->data['split'] = $mch['split'] / 100;
                    } else {
                        $this->data['price'] = $data->data['price'] * $number;
                        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                        $this->data['split'] = $mch['split'] / 100;
                    }

                    $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::CHANNEL_TYPE_1);
                    if (!$realPriceR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, $realPriceR->data));
                    }
                    $this->data['real_price'] = $realPriceR->data['real_price'];

                    //添加订单
                    $this->data['create_time'] = $currentTime;
                    $this->data['update_time'] = $currentTime;
                    $r = $this->add('Order');
                    if ($r->ret) {
                        $this->commitTrans();
                        if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                            //通知服务
                            if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                                $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_1, $this->data['line_id']);
                                //分台下所有司机
                                //$driverR = $this->select(array('branchid' => $data->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone,openid');
                                if ($driversR) {
                                    $driverCellphoneArr = array();
                                    $driverWeixinArr = array();
                                    foreach ($driversR->data as $driverData) {
                                        $driverCellphoneArr[] = $driverData['cellphone'];
                                        $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                    }
                                    //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $this->encryptionCellphoneByCellphone($this->data['reseverd_phone'])), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $data->data['mchid']);
                                    //您有新的[拼车]订单了
                                    //乘客姓名：张三
                                    //乘客人数：3人
                                    //联系电话：1333333333
                                    //预定线路：武汉-北京
                                    //上车时间：2016/8/8 18:30
                                    //请尽快与乘客取得联系

                                    # 2023.11.29 随机派单一个司机
                                    $count = count($driverWeixinArr);
                                    $random = rand(0, $count);
                                    $randomDriver = $driverWeixinArr[$random];
                                    if ($randomDriver) {
                                        $r = $this->save('Order', array(
                                            'driver_id' => $randomDriver['driver_id'],
                                            'order_id' => $r->data,
                                        ));
                                        if (!$r->ret) {
                                            return $this->output(new \ResultModel(false, '抢单模式分派司机失败'));
                                        }

                                        $temp = array(
                                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                                            $passengerR->data['name'],
                                            $number,
                                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                            $data->data['start_name']." - ". $data->data['end_name'],
                                            $start_time,
                                            '请及时抢单! 点击前往抢单'
                                        );
                                        $this->pushWeixinMessages($randomDriver['openid'], $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                    }

                                }
                            } else {
                                $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                            }
                        }
                        // 新订单Websocket消息通知
                        $this->sendInnerWebSocket($r->data);

                        return $this->output(new \ResultModel(true, $this->queryOrderProfileById($r->data)));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败'));
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }


    /**
     * 乘客拨打电话时记录下单日志
     * @param int $line_id 线路id
     * @param int $number 座位数
     * @param int $driver_id 司机ID
     * @param string $reseverd_phone 输入的电话号码
     */
    public function doMobileBookPhoneLine($line_id = null, $number = 1, $driver_id = null, $reseverd_phone = null)
    {
        if (empty($line_id)) {
            return $this->output(new \ResultModel(false, '下单失败'));
        }
        if (!empty($reseverd_phone)) {
            if (strlen($reseverd_phone) > 20) {
                return $this->output(new \ResultModel(false, '下单失败，电话号码输入错误'));
            }
        }
        $this->data['mchid'] = $this->mchid;
        $this->data['start_time'] = date('Y-m-d H:i:s', time());
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['passenger_id'] = $this->state->user_id;
        $this->data['book_seating'] = $number;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_1;
        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if (empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = empty($r->data['cellphone']) ? '' : $r->data['cellphone'];
        }

        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $lineR = $this->find(array('id' => $line_id), 'Line');
                if ($lineR->ret) {
                    $this->data['branchid'] = $lineR->data['branchid'];
                    $this->data['price'] = $lineR->data['price'];
                }

                $orderA = $this->add('OrderPhone');
                return $this->output($orderA);
            } else {
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        }

        return $this->output($r);
    }


    /**
     * 预订包车线路
     * @param int $line_id 线路id
     * @param int $line_chartered_price_id 价格类型
     * @param date $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $start_longitude 结束经度
     * @param string $start_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $ft_number  航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doMobileBookLineChartered($line_id, $line_chartered_price_id, $start_time = '', $reseverd_phone = null, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        //预留电话
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        $data = $this->find(array('id' => $line_id, 'is_del' => 0), 'LineChartered');
        if ($data->data['status'] != 1) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        if ($data->data['business_time_type'] == 1) {
            if (!$this->checkIsInAppointmentTime($start_time, $data->data['start_appointment_time'], $data->data['end_appointment_time'])) {
                return $this->output(new \ResultModel(false, '出发时间不在营业时间范围内，请重新选择出发时间'));
            }
        }

        $lineCharteredPriceR = $this->find(array('line_chartered_price_id' => $line_chartered_price_id, 'is_del' => 0), 'LineCharteredPrice');
        if (!$lineCharteredPriceR->ret) {
            return $this->output(new \ResultModel(false, '暂不支持该车型'));
        }

        $carTypeR = $this->find(array('car_type_id' => $lineCharteredPriceR->data['car_type_id']), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $data->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_2;
                $this->data['mchid'] = $data->data['mchid'];
                $this->data['line_id'] = $data->data['id'];
                $this->data['branchid'] = $data->data['branchid'];
                $this->data['book_seating'] = $book_seating;
                $this->data['car_type_id'] = $carTypeR->data['car_type_id'];

                $this->data['start_address_code'] = $data->data['start_address_code'];
                $this->data['end_address_code'] = $data->data['end_address_code'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $start_time;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->startTrans();
                //更新乘客信息
                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'start_address_code' => $data->data['start_address_code'], 'end_address_code' => $data->data['end_address_code'], 'start_time' => $data->data['start_time']));
                if ($r) {
                    $mch = M('AnnualFee')->where(array('mchid' => $data->data['mchid']))->find();
                    $isChannel = false;
                    if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
                        //渠道价格
                        $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
                        $this->data['channel_account_id'] = $passengerR->data['invite_id'];
                        $isChannel = true;
                    }

                    if ($isChannel) {
                        $this->data['price'] = $lineCharteredPriceR->data['price'];
                        $this->data['offer_channel_price'] = $lineCharteredPriceR->data['price'] - $lineCharteredPriceR->data['channel_price'];
                        $channelPrice = $lineCharteredPriceR->data['channel_price'];
                        $this->data['offer_price'] = $channelPrice - $channelPrice * $mch['split'] / 100;
                        $this->data['split'] = $mch['split'] / 100;
                    } else {
                        $this->data['price'] = $lineCharteredPriceR->data['price'];
                        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                        $this->data['split'] = $mch['split'] / 100;
                    }

                    $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::ORDER_TYPE_2);
                    if (!$realPriceR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, $realPriceR->data));
                    }
                    $this->data['real_price'] = $realPriceR->data['real_price'];

                    //添加订单
                    $this->data['create_time'] = $currentTime;
                    $this->data['update_time'] = $currentTime;
                    $r = $this->add('Order');
                    if ($r->ret) {
                        $this->commitTrans();
                        if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                            //通知服务
                            if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                                $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_2, $this->data['line_id']);
                                if ($driversR->ret) {
                                    $driverCellphoneArr = array();
                                    $driverWeixinArr = array();
                                    foreach ($driversR->data as $driverData) {
                                        $driverCellphoneArr[] = $driverData['cellphone'];
                                        $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                    }

                                    # 2023.11.29 随机派单一个司机
                                    $count = count($driverWeixinArr);
                                    $random = rand(0, $count);
                                    $randomDriver = $driverWeixinArr[$random];
                                    if ($randomDriver) {
                                        $r = $this->save('Order', array(
                                            'driver_id' => $randomDriver['driver_id'],
                                            'order_id' => $r->data,
                                        ));
                                        if (!$r->ret) {
                                            return $this->output(new \ResultModel(false, '抢单模式分派司机失败'));
                                        }
                                        $temp = array(
                                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                                            $passengerR->data['name'],
                                            $this->data['book_seating'],
                                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                                            $data->data['start_name']." - ". $data->data['end_name'],
                                            $this->data['start_time'],
                                            '请及时抢单! 点击前往抢单'
                                        );
                                        $this->pushWeixinMessage($randomDriver['openid'], $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                                    }

                                }
                            } else {
                                $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                            }
                        }
                        // 新订单Websocket消息通知
                        $this->sendInnerWebSocket($r->data);

                        return $this->output(new \ResultModel(true, $this->queryOrderProfileById($r->data)));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败'));
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }


    /**
     * 更新订单司机
     * @param int $driverId
     * @param int $orderId
     * @return mixed
     */
    protected function updateOrderDriver($driverId, $orderId)
    {

    }

    /**
     * 查询订单
     *
     * @param integer $orderId
     * @return array
     */
    public function queryOrderProfileById($orderId)
    {
        return M('Order')->field('order_id,order_no,real_price as price')->where(array('order_id' => $orderId))->find();
    }


    /**
     * 乘客创建包车临时订单(手机端)---自定义
     * @param int $price 价格，默认为0
     * @param int $car_type_id 车型：1,2
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 开始地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 结束地址
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempOrderCharteredCustom($price = 0, $car_type_id, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ID_number = null, $ID_real_name = null)
    {
        $this->data['is_custom'] = 1;
        $this->data['is_face'] = 1;

        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'cellphone,mchid,openid_pay');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($price)) {
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }

        $carTypeR = $this->find(array('car_type_id' => $car_type_id), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        /*        $systemConfigR = $this->find(array('key'=>'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']),'SystemConfig');
                if($systemConfigR->ret){
                    $robOrder = $systemConfigR->data['value'];
                }*/

        /**
         * 自定义包车调度分台管理员
         */
        $lineCharterCustomBranchConfig = \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG_0;
        $lineCharterCustomBranchConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => \CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG), 'SystemConfig');
        if ($lineCharterCustomBranchConfigR->ret) {
            $lineCharterCustomBranchConfig = $lineCharterCustomBranchConfigR->data['value'];
        } else {
            return $this->output(new \ResultModel(false, '下单失败请联系管理员'));
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        //预留电话
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        } else {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }

        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        $mch = M('AnnualFee')->where(array('mchid' => $passengerR->data['mchid']))->find();
        $rate = $mch['split'] / 100;//提成比例

        $this->data['passenger_id'] = $passenger_id;
        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $passengerR->data['mchid'];
        $this->data['branchid'] = $lineCharterCustomBranchConfig;
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['offer_price'] = $price - $rate * $price;
        $this->data['price'] = $price;
        $this->data['split'] = $rate;
        $this->data['book_seating'] = $book_seating;
        $this->data['is_temp'] = 0;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_2;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $orderS = $this->add('Order');

        if ($orderS->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $passengerR->data['openid_pay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
            }

            return $this->output(new \ResultModel(true, ['order_id' => $orderS->data]));
        }

        return $this->output($orderS);
    }

    /**
     * 计算价格
     * @param double $weight 重量
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $start_longitude 结束经度
     * @param string $start_latitude 结束纬度
     */
    public function doMobileComputePrice($weight, $start_longitude, $start_latitude, $end_longitude, $end_latitude)
    {
        if (!is_numeric($weight) || $weight <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写重量'));
        }

        if (!$start_latitude || !$start_longitude || !$end_latitude || !$end_longitude) {
            return $this->output(new \ResultModel(false, '请重新选择位置'));
        }

        //计算距离
        $distance = $this->getDistance($start_latitude, $start_longitude, $end_latitude, $end_longitude);
        $distance = round($distance, 2);//保留两位
        $takeGoodsPriceR = $this->find(array('mchid' => $this->mchid, 'is_default' => 1), 'TakeGoodsPrice');
        if (!$takeGoodsPriceR->ret) {
            return $this->output(new \ResultModel(false, '暂未开通'));
        }
        //计费方式
        $price = $this->getDeliveryPrice($takeGoodsPriceR->data['take_goods_price_id'], $weight, $distance);
        //        $this->add('Log', array('Operation_content' => "doMobileComputePrice", 'operation_time' => time(), 'admin_id' => 181, 'data' => json_encode(array('price' => $price, 'distance' => $distance,'passenger_id' => $this->state->user_id))));
        return $this->output(new \ResultModel(true, array('price' => $price, 'distance' => $distance)));
    }

    /**
     * 预订带货
     * @param double $weight 重量
     * @param date $start_time 出发时间
     * @param string $reseverd_phone 发货人手机号
     * @param string $reseverd_person 发货人名称
     * @param strint $delivery_person 接货人
     * @param string $delivery_phone 接货人电话
     * @param string $reseverd_info 备注
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doMobileBookTakeGoods($weight, $start_time = '', $reseverd_phone = null, $reseverd_person = null, $delivery_person, $delivery_phone, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        if (!is_numeric($weight) || $weight <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写重量'));
        }

        if (empty($delivery_person) || empty($delivery_phone)) {
            return $this->output(new \ResultModel(false, '请补全收货人信息'));
        }

        if (!$start_latitude || !$start_longitude || !$end_latitude || !$end_longitude) {
            return $this->output(new \ResultModel(false, '请重新选择位置'));
        }

        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        //发货人手机号
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }
        //发货人姓名
        if (is_null($reseverd_person) || empty($reseverd_person)) {
            $this->data['reseverd_person'] = $passengerR->data['name'];
        }

        if ($start_address_code) {
            $where['start_address_code'] = $start_address_code;
        } else {
            return $this->output(new \ResultModel(false, '请选择取货地址'));
        }

        if ($end_address_code) {
            $where['end_address_code'] = $end_address_code;
        } else {
            return $this->output(new \ResultModel(false, '请选择收货地址'));
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        $takeGoodsPriceR = $this->find(array('mchid' => $passengerR->data['mchid'], 'is_default' => 1), 'TakeGoodsPrice');
        if (!$takeGoodsPriceR->ret) {
            return $this->output(new \ResultModel(false, '暂未开通'));
        }

        //自动获取分台
        $linesR = self::getAutoLines($start_address_code, $end_address_code, 1, 1);
        if (!$linesR->ret) {
            return $this->output(new \ResultModel(false, '暂未开通该区域的带货服务'));
        }

        $branchId = 0;
        $lineId = 0;
        foreach ($linesR->data as $k => $v) {
            if ($v['branchid']) {
                $branchId = $v['branchid'];
                $lineId = $v['id'];
                break;
            }
        }

        //计算距离
        $distance = $this->getDistance($start_latitude, $start_longitude, $end_latitude, $end_longitude);
        $distance = round($distance, 2);//保留两位

        //计费方式
        $price = $this->getDeliveryPrice($takeGoodsPriceR->data['take_goods_price_id'], $weight, $distance);

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $passengerR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone,create_time');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_3;
                $this->data['mchid'] = $passengerR->data['mchid'];
                $this->data['branchid'] = $branchId;
                $this->data['book_seating'] = 0;
                $this->data['line_id'] = $lineId;

                $this->data['start_address_code'] = $start_address_code;
                $this->data['end_address_code'] = $end_address_code;
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $start_time;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->data['take_goods_price_id'] = $takeGoodsPriceR->data['take_goods_price_id'];
                $this->data['kilometre'] = $distance;
                $this->data['weight'] = $weight;
                $this->startTrans();

                $mch = M('AnnualFee')->where(array('mchid' => $passengerR->data['mchid']))->find();
                $isChannel = false;
                if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
                    //渠道价格
                    $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
                    $this->data['channel_account_id'] = $passengerR->data['invite_id'];
                    $isChannel = true;
                }

                if ($isChannel) {
                    $offer_channel_price = $takeGoodsPriceR->data['channel_rofit_price'];
                    $channelPrice = $price - $offer_channel_price;
                    if ($channelPrice <= 0) {
                        $channelPrice = $price;
                        $offer_channel_price = 0;
                    }
                    $this->data['price'] = $price;
                    $this->data['offer_channel_price'] = $offer_channel_price;
                    $this->data['offer_price'] = $channelPrice - $channelPrice * $mch['split'] / 100;
                    $this->data['split'] = $mch['split'] / 100;
                } else {
                    $this->data['price'] = $price;
                    $this->data['offer_price'] = $price - $price * $mch['split'] / 100;
                    $this->data['split'] = $mch['split'] / 100;
                }

                $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::ORDER_TYPE_3);
                if (!$realPriceR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, $realPriceR->data));
                }
                $this->data['real_price'] = $realPriceR->data['real_price'];

                //添加订单
                $this->data['create_time'] = $currentTime;
                $this->data['update_time'] = $currentTime;
                $r = $this->add('Order');
                if ($r->ret) {
                    $this->commitTrans();
                    if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        //通知服务
                        if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                            $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_1/*\CommonDefine::ORDER_TYPE_3*/, $this->data['line_id']);
                            //$driverR = $this->select(array('branchid' => $branchId,'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                            if ($driversR->ret) {
                                $driverCellphoneArr = array();
                                $driverWeixinArr = array();
                                foreach ($driversR->data as $driverData) {
                                    $driverCellphoneArr[] = $driverData['cellphone'];
                                    $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                                }
                                //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $passengerR->data['cellphone']), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $passengerR->data['mchid']);

                                //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                                //您有新的订单了
                                //订单编号：C1510170029132
                                //时间：业务经理
                                //有问题请拨************


                                # 2023.11.29 随机派单一个司机
                                $count = count($driverWeixinArr);
                                $random = rand(0, $count);
                                $randomDriver = $driverWeixinArr[$random];
                                if ($randomDriver) {
                                    $r = $this->save('Order', array(
                                        'driver_id' => $randomDriver['driver_id'],
                                        'order_id' => $r->data,
                                    ));
                                    if (!$r->ret) {
                                        return $this->output(new \ResultModel(false, '抢单模式分派司机失败'));
                                    }
                                    $temp = array(
                                        '您有新的['.$this->getOrderType($this->data['type']).']订单了',
                                        $this->data['order_no'],
                                        $this->data['create_time'],
                                        '乘客电话:'.$this->encryptionCellphoneByCellphone($this->data['reseverd_phone']).',请及时抢单!'
                                    );
                                    $this->pushWeixinMessages($randomDriver['openid'], $temp, 2, $this->data['mchid'], self::WECHAT_MSG_TYPE_5);
                                }
                            }
                        } else {
                            $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                        }
                    }

                    // 新订单Websocket消息通知
                    $this->sendInnerWebSocket($r->data);

                    return $this->output(new \ResultModel(true, $this->queryOrderProfileById($r->data)));
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败'));
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        }
        return $this->output($r);
    }


    /**
     * 获取代办事件列表
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为20）
     */
    public function getMobileAgencyList($page = 1, $size = 20)
    {
        $size = 200;
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $order = ' create_time desc';
        $r = $this->select($where, $page, $size, $order, 'Agency', 'agency_id,name,summary,price,start_appointment_time,end_appointment_time,business_time_type,set_order_time');
        return $this->output($r);
    }

    /**
     * 预订代办
     * @param date $agency_id 代办事件
     * @param date $start_time 出发时间
     * @param string $reseverd_phone 代办人手机号
     * @param string $reseverd_info 备注
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doMobileBookAgency($agency_id, $start_time = '', $reseverd_phone = null, $reseverd_info = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'cellphone,name,mchid,state,start_address_code,end_address_code');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        $agencyR = $this->find(array('agency_id' => $agency_id, 'is_del = 0'), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new \ResultModel(false, '代办事件已下架,请重新选择'));
        }

        //代办人手机号
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $passengerR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        if (\intval($passengerR->data['state']) !== 1) {
            $this->data['passenger_id'] = $passenger_id;
            $this->data['type'] = \CommonDefine::ORDER_TYPE_4;
            $this->data['mchid'] = $passengerR->data['mchid'];
            $this->data['branchid'] = $agencyR->data['branchid'];
            $this->data['book_seating'] = 0;

            $this->data['drive_model'] = 1;
            $this->data['start_time'] = $start_time;
            $this->data['order_no'] = $this->createOrderNo();
            $this->data['rob_order'] = $robOrder;
            $this->data['pay_mode'] = $payMode;
            $this->data['agency_id'] = $agencyR->data['agency_id'];
            $this->startTrans();

            $mch = M('AnnualFee')->where(array('mchid' => $passengerR->data['mchid']))->find();
            $isChannel = false;
            if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
                //渠道价格
                $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
                $this->data['channel_account_id'] = $passengerR->data['invite_id'];
                $isChannel = true;
            }

            if ($isChannel) {
                $this->data['price'] = $agencyR->data['price'];
                $this->data['offer_channel_price'] = $agencyR->data['price'] - $agencyR->data['channel_price'];
                $channelPrice = $agencyR->data['channel_price'];
                $this->data['offer_price'] = $channelPrice - $channelPrice * $mch['split'] / 100;
                $this->data['split'] = $mch['split'] / 100;
            } else {
                $this->data['price'] = $agencyR->data['price'];
                $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                $this->data['split'] = $mch['split'] / 100;
            }

            $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::ORDER_TYPE_4);
            if (!$realPriceR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, $realPriceR->data));
            }
            $this->data['real_price'] = $realPriceR->data['real_price'];

            //添加订单
            $this->data['create_time'] = $currentTime;
            $this->data['update_time'] = $currentTime;
            $r = $this->add('Order');
            if ($r->ret) {
                $this->commitTrans();
                if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    //通知服务
                    if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                        //                    $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_4, $this->data['line_id']);
                        $driversR = $this->select(array('branchid' => $agencyR->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0), null, null, null, 'Driver', 'cellphone,openid,driver_id');
                        if ($driversR->ret) {
                            $driverCellphoneArr = array();
                            $driverWeixinArr = array();
                            foreach ($driversR->data as $driverData) {
                                $driverCellphoneArr[] = $driverData['cellphone'];
                                $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                            }
                            //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $passengerR->data['cellphone']), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $passengerR->data['mchid']);

                            //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                            //您有新的订单了
                            //订单编号：C1510170029132
                            //时间：业务经理
                            //有问题请拨************

                            # 2023.11.29 随机派单一个司机
                            $count = count($driverWeixinArr);
                            $random = rand(0, $count);
                            $randomDriver = $driverWeixinArr[$random];
                            if ($randomDriver) {
                                $r = $this->save('Order', array(
                                    'driver_id' => $randomDriver['driver_id'],
                                    'order_id' => $r->data,
                                ));
                                if (!$r->ret) {
                                    return $this->output(new \ResultModel(false, '抢单模式分派司机失败'));
                                }
                                $temp = array(
                                    '您有新的['.$this->getOrderType($this->data['type']).']订单了',
                                    $this->data['order_no'],
                                    $this->data['create_time'],
                                    '乘客电话:'.$this->encryptionCellphoneByCellphone($this->data['reseverd_phone']).',请及时抢单!'
                                );
                                $this->pushWeixinMessages($randomDriver['openid'], $temp, 2, $this->data['mchid'], self::WECHAT_MSG_TYPE_5);
                            }



                        }
                    } else {
                        $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                    }
                }
                // 新订单Websocket消息通知
                $this->sendInnerWebSocket($r->data);
                return $this->output(new \ResultModel(true, $this->queryOrderProfileById($r->data)));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '下单失败'));
        }
    }

    /**
     * 预订班线
     * @param int $line_class_train_id 班次id
     * @param string $seat_ids 座位id（默认为空）
     * @param int $number 预定数量
     * @param string $reseverd_phone 预留手机号
     * @param string $start_time 出发时间
     * @param string $start_longitude 出发经度（默认为空）
     * @param string $start_latitude 出发纬度（默认为空）
     * @param string $end_longitude 目的经度（默认为空）
     * @param string $end_latitude 目的纬度（默认为空）
     * @param string $start_address_remark 出发地名称（默认为空）
     * @param string $end_address_remark 目的地名称（默认为空）
     * @param string $return_start_time 出发时间（默认为空）
     * @param string $return_start_longitude 出发经度（默认为空）
     * @param string $return_start_latitude 出发纬度（默认为空）
     * @param string $return_end_longitude 目的经度（默认为空）
     * @param string $return_end_latitude 目的纬度（默认为空）
     * @param string $return_start_address_remark 出发地名称（默认为空）
     * @param string $return_end_address_remark 目的地名称（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     * @param int $driver_id 司机id（默认为空）
     * @param int $is_buy_insurance 是否已购买保险（默认为0）
     * @param json $members 乘车人员[{"real_name":"001","ID_number":"510122200020170001"，"seat_id":"2"},{"real_name":"002","ID_number":"510122200020170002"，"seat_id":"3"}]
     * @param json $insurance 购买保险集合[{"insurance_name":"平安保险1","insurance_desc":"意外车险","insurance_amount":"1.00"},{"insurance_name":"平安保险2","insurance_desc":"意外车险","insurance_amount":"1.00"}]
     */
    public function doMobileBookLineClassTrain($line_class_train_id, $seat_ids = null, $number = 1, $reseverd_phone = null, $start_time, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_remark = null, $end_address_remark = null, $return_start_time = null, $return_start_longitude = null, $return_start_latitude = null, $return_end_longitude = null, $return_end_latitude = null, $return_start_address_remark = null, $return_end_address_remark = null, $reseverd_info = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null, $driver_id = null, $is_buy_insurance = 0, $members = null, $insurance = null, $seat_occupied = '', $point_redeemable_amount = 0, $pickup_polygon_seq = 0, $dropoff_polygon_seq = 0, $fixed_boarding_point_seq = 0, $fixed_dropoff_point_seq = 0, $channel_branch_id = null, $ft_number = '')
    {
        if (empty($this->mchid)) {
            return $this->output(new \ResultModel(false, '商户参数错误'));
        }

        $systemConfigR = $this->find(
            array('key' => \CommonDefine::ID_UPLOAD_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $this->mchid),
            'SystemConfig'
        );

        if ($systemConfigR->ret
            && $systemConfigR->data['value'] == \CommonDefine::ID_UPLOAD_CONFIG_1) {
            // 循环校验 seat_occupied 中的身份证号码
            if (!empty($seat_occupied)) {
                foreach ($seat_occupied as $seat) {
                    if ((isset($seat['passenger_type']) && $seat['passenger_type'] != '2')) {
                        if (isset($seat['passenger_id_no']) && !$this->isValidIdNumber($seat['passenger_id_no'])
                        ) {
                            return $this->output(new \ResultModel(false, "乘客身份证号码:{$seat['passenger_id_no']} 错误"));
                        }
                    }
                }
            }

            // 循环校验 members 中的身份证号码
            if (!empty($members)) {
                foreach ($members as $member) {
                    if ((isset($member['passenger_type']) && $member['passenger_type'] != '2')) {
                        if (isset($member['ID_number']) && !$this->isValidIdNumber($member['ID_number'])
                        ) {
                            return $this->output(new \ResultModel(false, "无效的乘车人身份证号码:{$member['ID_number']}"));
                        }
                    }
                }
            }
        }

        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'line_class_train_id' => $line_class_train_id,
            'number' => $number,
            'is_buy_insurance' => $is_buy_insurance
        );

        if (!empty($seat_ids)) {
            $params['seat_ids'] = $seat_ids;
        }
        if (!empty($seat_occupied)) {
            $params['seat_occupied'] = json_encode($seat_occupied);
        }
        if (!empty($reseverd_phone)) {
            $params['reseverd_phone'] = $reseverd_phone;
        }
        if (!empty($start_longitude)) {
            $params['start_longitude'] = $start_longitude;
        }
        if (!empty($start_latitude)) {
            $params['start_latitude'] = $start_latitude;
        }
        if (!empty($end_longitude)) {
            $params['end_longitude'] = $end_longitude;
        }
        if (!empty($end_latitude)) {
            $params['end_latitude'] = $end_latitude;
        }
        if (!empty($start_address_remark)) {
            $params['start_address_remark'] = $start_address_remark;
        }
        if (!empty($end_address_remark)) {
            $params['end_address_remark'] = $end_address_remark;
        }
        if (!empty($start_time)) {
            $params['start_time'] = $start_time;
        }
        if (!empty($reseverd_info)) {
            $params['reseverd_info'] = $reseverd_info;
        }
        if (!empty($insurance)) {
            $params['insurance'] = $insurance;
        }
        if (!empty($members)) {
            $params['members'] = $members;
        }
        if (!empty($point_redeemable_amount)) {
            $params['point_redeemable_amount'] = $point_redeemable_amount;
        }
        if (!empty($pickup_polygon_seq)) {
            $params['pickup_polygon_seq'] = $pickup_polygon_seq;
        }
        if (!empty($dropoff_polygon_seq)) {
            $params['dropoff_polygon_seq'] = $dropoff_polygon_seq;
        }
        if (!empty($fixed_boarding_point_seq)) {
            $params['fixed_boarding_point_seq'] = $fixed_boarding_point_seq;
        }
        if (!empty($fixed_dropoff_point_seq)) {
            $params['fixed_dropoff_point_seq'] = $fixed_dropoff_point_seq;
        }
        if (!empty($channel_branch_id)) {
            $params['channel_branch_id'] = $channel_branch_id;
        }
        if (!empty($driver_id)) {
            $params['driver_id'] = $driver_id;
        }
        if (!empty($coupon_record_id)) {
            $params['coupon_record_id'] = $coupon_record_id;
        }
        if (!empty($ft_number)) {
            $params['ft_number'] = $ft_number;
        }


        $ret = new \ResultModel(false);
        $bizType = \CommonDefine::ORDER_TYPE_5;
        //passengers/{passenger}/types/{type}/orders
        $responsed = httpRequest(
            C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/types/{$bizType}/orders",
            'post',
            $params,
            $header
        );

        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('下单失败：%s', $results['message']);
        }

        return $this->output($ret);
        // 2023-10-18 调用异构系统服务
    }


    // 15位或18位身份证号码验证，18位最后一位可以是 X 或数字
    private function isValidIdNumber($idNumber)
    {
        // 校验 15 位或 18 位格式
        if (!preg_match('/(^\d{15}$)|(^\d{17}[\dXx]$)/', $idNumber)) {
            return false;
        }

        // 如果是 15 位身份证号码，先转换为 18 位
        if (strlen($idNumber) === 15) {
            $idNumber = $this->convertTo18Digit($idNumber);
        }

        // 加权因子
        $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkSumCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        // 计算校验码
        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($idNumber[$i]) * $weights[$i];
        }
        $mod = $sum % 11;

        // 比对校验码
        return strtoupper($idNumber[17]) === $checkSumCodes[$mod];
    }

    // 将 15 位身份证号码转换为 18 位
    private function convertTo18Digit($idNumber)
    {
        // 在第6位之后插入“19”，变成17位
        $idNumber17 = substr($idNumber, 0, 6) . '19' . substr($idNumber, 6);

        // 计算校验码
        $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkSumCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($idNumber17[$i]) * $weights[$i];
        }
        $mod = $sum % 11;

        // 添加校验码，形成18位号码
        return $idNumber17 . $checkSumCodes[$mod];
    }


    /**
     * 用户首单优惠券活动检测是否已经使用
     */
    protected function validateMarketActivityCoupon($couponId, $passengerId)
    {
        $activityIds = $couponRecordIds = array();
        $orders = M()
            ->table('cp_order')
            ->field('coupon_record_id')
            ->where('state = ' . \CommonDefine::ORDER_STATE_6 . ' AND passenger_id = ' . $passengerId)
            ->select();

        if (!empty($orders)) {
            foreach ($orders as $order) {
                $couponRecordIds[] = $order->coupon_record_id;
            }
            $records = M()
                ->table('cp_coupon_record')
                ->field('market_activity_id')
                ->where('coupon_record_id IN ('.implode(',', $couponRecordIds).') AND coupon_record_id =' . $couponId)
                ->select();
            if (!empty($records)) {
                foreach ($records as $record) {
                    $activityIds[] = $record->market_activity_id;
                }
            }

            $count = M()
                ->table('cp_market_activity')
                ->field('market_activity_id')
                ->where('coupon_record_id IN ('.implode(',', $activityIds).') AND type = 5')
                ->select();
            if (!empty($count)) {
                return new \ResultModel(false, '您已经参加过用户首单活动，请稍后再试！');
            }
        }
    }

    /**
     * 班线车-位置是否在电子围栏内
     * @param int $line_class_id 线路id
     * @param int $line_class_train_id 班次id
     * @param string $start_longitude 出发经度（默认为空）
     * @param string $start_latitude 出发纬度（默认为空）
     * @param string $end_longitude 目的经度（默认为空）
     * @param string $end_latitude 目的纬度（默认为空）
     */
    public function doCheckIsInArea($line_class_id, $line_class_train_id, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null)
    {
        $lineClassR = $this->find(['id' => $line_class_id, 'mchid' => $this->mchid], 'LineClass');
        if (!$lineClassR->ret) {
            return $this->output(new \ResultModel(false, '线路异常'));
        }

        if ($lineClassR->data['start_address_type'] == 2) {
            if ($start_longitude && $start_latitude && $lineClassR->data['start_polygon']) {
                $pointCalculationObj = new \PointCalculation();
                $pts = $pointCalculationObj->convertStrToArr(json_decode($lineClassR->data['start_polygon'], true));
                if (!$pointCalculationObj->is_point_in_polygon(['lat' => $start_latitude, 'lng' => $start_longitude], $pts)) {
                    return $this->output(new \ResultModel(false, '暂不支持该区域(接)'));
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '固定上车点暂不支持区域限制'));
        }

        if ($lineClassR->data['end_address_type'] == 2) {
            if ($end_longitude && $end_latitude && $lineClassR->data['end_polygon']) {
                $pointCalculationObj = new \PointCalculation();
                $pts = $pointCalculationObj->convertStrToArr(json_decode($lineClassR->data['end_polygon'], true));
                if (!$pointCalculationObj->is_point_in_polygon(['lat' => $end_latitude, 'lng' => $end_longitude], $pts)) {
                    return $this->output(new \ResultModel(false, '暂不支持该区域（送）'));
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '固定下车点暂不支持区域限制'));
        }

        return $this->output(new \ResultModel(true, '在服务的区域范围内'));
    }



    /**
     * 预订班线-摆渡车（分流车）含接送  例如：a（摆渡车上车点）->A(班线车出发地)->B(班线车目的地)->b（摆渡车下车点）
     * @param int $line_class_train_id 班次id
     * @param string $seat_ids 座位id（默认为空）
     * @param int $number 预定数量
     * @param string $reseverd_phone 预留手机号
     * @param string $reseverd_info 备注
     * @param string $start_time 出发时间
     * @param string $is_start_ferry_check 是否接乘客至班线车出发地（0：不接；1-接）
     * @param string $ferry_start_longitude 接送上车经度（默认为空）
     * @param string $ferry_start_latitude 接送上车纬度（默认为空）
     * @param string $start_longitude 班线车出发地经度（默认为空）
     * @param string $start_latitude 班线车出发地纬度（默认为空）
     * @param string $start_address_remark 班线车出发地名称（默认为空）
     * @param string $ferry_start_address_remark 摆渡车（接）起始位置名称（默认为空）
     * @param string $end_longitude 班线车目的经度（默认为空）
     * @param string $end_latitude 班线车目的纬度（默认为空）
     * @param string $end_address_remark 目的地名称（默认为空）
     * @param string $is_end_ferry_check 是否送至乘客指定下车点（0：不送；1-送）
     * @param string $ferry_end_longitude 接送下车经度（默认为空）
     * @param string $ferry_end_latitude 接送下车纬度（默认为空）
     * @param string $ferry_end_address_remark 摆渡车（送）终点位置名称（默认为空）
     * @param string $return_start_time 出发时间（默认为空）
     * @param string $return_start_longitude 出发经度（默认为空）
     * @param string $return_start_latitude 出发纬度（默认为空）
     * @param string $return_end_longitude 目的经度（默认为空）
     * @param string $return_end_latitude 目的纬度（默认为空）
     * @param string $return_start_address_remark 出发地名称（默认为空）
     * @param string $return_end_address_remark 目的地名称（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     * @param int $driver_id 司机id（默认为空）
     * @param int $is_buy_insurance 是否已购买保险（默认为0）
     * @param json $members 乘车人员[{"real_name":"001","ID_number":"510122200020170001"，"seat_id":"2"},{"real_name":"002","ID_number":"510122200020170002"，"seat_id":"3"}]
     * @param json $insurance 购买保险集合[{"insurance_name":"平安保险1","insurance_desc":"意外车险","insurance_amount":"1.00"},{"insurance_name":"平安保险2","insurance_desc":"意外车险","insurance_amount":"1.00"}]
     * @param int $start_ferry_distance 摆渡车（接）（距离默认为0）
     * @param int $start_ferry_duration 摆渡车（接）（耗时默认为0）
     * @param int $end_ferry_distance 摆渡车（送）（距离默认为0）
     * @param int $end_ferry_duration 摆渡车（送）（耗时默认为0）
     */
    public function doMobileBookLineClassTrainFerry($line_class_train_id, $seat_ids = null, $number = 1, $reseverd_phone = null, $reseverd_info = null, $start_time, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_remark = null, $end_address_remark = null, $is_start_ferry_check = 0, $ferry_start_longitude = null, $ferry_start_latitude = null, $is_end_ferry_check = 0, $ferry_end_longitude = null, $ferry_end_latitude = null, $ferry_start_address_remark = null, $ferry_end_address_remark = null, $return_start_time = null, $return_start_longitude = null, $return_start_latitude = null, $return_end_longitude = null, $return_end_latitude = null, $return_start_address_remark = null, $return_end_address_remark = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null, $driver_id = null, $is_buy_insurance = 0, $members = null, $insurance = null, $start_ferry_distance = 0, $start_ferry_duration = 0, $end_ferry_distance = 0, $end_ferry_duration = 0, $seat_occupied = null)
    {
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];
        $params = array(
            'seat_ids' => $seat_ids,
            'seat_occupied' => json_encode($seat_occupied),
            'reseverd_phone' => $reseverd_phone,
            'line_class_train_id' => $line_class_train_id,
            'start_longitude' => $start_longitude,
            'start_latitude' => $start_latitude,
            'end_longitude' => $end_longitude,
            'end_latitude' => $end_latitude,
            'start_address_remark' => ($start_address_remark),
            'end_address_remark' => ($end_address_remark),
            'start_time' => $start_time,
            'driver_id' => $driver_id,
            'reseverd_info' => ($reseverd_info),
            'insurance' => $insurance,
            'members' => $members,
            'is_buy_insurance' => $is_buy_insurance,
            'number' => $number,
            'coupon_record_id' => $coupon_record_id,
            'is_start_ferry_check' => $is_start_ferry_check,
            'is_end_ferry_check' => $is_end_ferry_check,
            'ferry_start_longitude' => $ferry_start_longitude,
            'ferry_start_latitude' => $ferry_start_latitude,
            'ferry_end_longitude' => $ferry_end_longitude,
            'ferry_end_latitude' => $ferry_end_latitude,
            'ferry_start_address_remark' => $ferry_start_address_remark,
            'ferry_end_address_remark' => $ferry_end_address_remark,
            'start_ferry_distance' => $start_ferry_distance,
            'start_ferry_duration' => $start_ferry_duration,
            'end_ferry_distance' => $end_ferry_distance,
            'end_ferry_duration' => $end_ferry_duration,
        );
        $ret = new \ResultModel(false);
        $bizType = \CommonDefine::ORDER_TYPE_20;
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/types/{$bizType}/orders", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('下单失败：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
     * 计算摆渡车（接、送）价格
     * @param int $line_class_train_id 班次id
     * @param int $ferry_type 接送上/下车点经度
     * @param string $ferry_start_longitude 接送上车经度（默认为空）
     * @param string $ferry_start_latitude 接送上车纬度（默认为空）
     * @param string $ferry_end_longitude 接送下车经度（默认为空）
     * @param string $ferry_end_latitude 接送下车纬度（默认为空）
     */
    public function doMobileComputeLineClassFerryPrice($line_class_train_id, $ferry_type = \CommonDefine::FERRY_TYPE_1, $ferry_start_longitude = null, $ferry_start_latitude = null, $ferry_end_longitude = null, $ferry_end_latitude = null)
    {
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $params = array(
            'line_class_train_id' => $line_class_train_id,
            'ferry_type' => $ferry_type,
            'ferry_start_longitude' => $ferry_start_longitude,
            'ferry_start_latitude' => $ferry_start_latitude,
            'ferry_end_longitude' => $ferry_end_longitude,
            'ferry_end_latitude' => $ferry_end_latitude,
        );
        $ret = new \ResultModel(false);
        $bizType = \CommonDefine::ORDER_TYPE_20;
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/ferry_fee", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('预估费用失败：%s', $results['message']);
        }
        return $this->output($ret);

        // $lineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id, 'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
        // if (!$lineClassTrainR->ret) {
        //     return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        // }
        // $start_longitude = null;
        // $start_latitude = null;
        // $end_longitude = null;
        // $end_latitude = null;
        // $start_point_longitude = null;
        // $start_point_latitude = null;
        // $start_point_address_remark = null;
        // $end_point_longitude = null;
        // $end_point_latitude = null;
        // $end_point_address_remark = null;
        // $fast_city_code = 0;
        // switch ($ferry_type) {
        //     case \CommonDefine::FERRY_TYPE_1:{
        //         if ($lineClassTrainR->data['is_start_ferry'] == \CommonDefine::IS_START_FERRY_0) {
        //             return $this->output(new \ResultModel(false, '该班次未开启摆渡车（接）服务'));
        //         }
        //         if (!$ferry_start_longitude || !$ferry_start_latitude) {
        //             return $this->output(new \ResultModel(false, '参数错误'));
        //         }
        //         $lineClassAroundPointsR = $this->getLineClassAroundPoints($lineClassTrainR->data['line_class_id'], $ferry_type, $ferry_start_longitude, $ferry_start_latitude);
        //         if (!$lineClassAroundPointsR->ret) {
        //             return $this->output(new \ResultModel(false, '附近没有车场点'));
        //         }
        //         $start_longitude = $ferry_start_longitude;
        //         $start_latitude = $ferry_start_latitude;
        //         $end_longitude = $start_point_longitude = $lineClassAroundPointsR->data[0]['longitude'];
        //         $end_latitude = $start_point_latitude = $lineClassAroundPointsR->data[0]['latitude'];
        //         $start_point_address_remark = $lineClassAroundPointsR->data[0]['alias'];
        //         $fast_city_code = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude)['code'];
        //         if (empty($fast_city_code)) {
        //             return $this->output(new \ResultModel(false, '区域编码错误(接)'));
        //         }
        //         break;
        //     }
        //     case \CommonDefine::FERRY_TYPE_2:{
        //         if ($lineClassTrainR->data['is_end_ferry'] == \CommonDefine::IS_END_FERRY_0) {
        //             return $this->output(new \ResultModel(false, '该班次未开启摆渡车（接）服务'));
        //         }

        //         if (!$ferry_end_longitude || !$ferry_end_latitude) {
        //             return $this->output(new \ResultModel(false, '参数错误'));
        //         }
        //         $lineClassAroundPointsR = $this->getLineClassAroundPoints($lineClassTrainR->data['line_class_id'], $ferry_type, $ferry_end_longitude, $ferry_end_latitude);
        //         if (!$lineClassAroundPointsR->ret) {
        //             return $this->output(new \ResultModel(false, '附近没有车场点'));
        //         }
        //         $start_longitude = $end_point_longitude = $lineClassAroundPointsR->data[0]['longitude'];
        //         $start_latitude = $end_point_latitude = $lineClassAroundPointsR->data[0]['latitude'];
        //         $end_longitude = $ferry_end_longitude;
        //         $end_latitude = $ferry_end_latitude;
        //         $end_point_address_remark = $lineClassAroundPointsR->data[0]['alias'];
        //         $fast_city_code = $this->getGdAddressCodeByGdApi($end_longitude, $end_latitude)['code'];
        //         if (empty($fast_city_code)) {
        //             return $this->output(new \ResultModel(false, '区域编码错误(送)'));
        //         }
        //         break;
        //     }
        //     default:{
        //         return $this->output(new \ResultModel(false, '参数异常'));
        //     }
        // }

        // $cityCodeR = $this->resolutionAddressTwo($fast_city_code);
        // if (!$cityCodeR->ret) {
        //     return $this->output(new \ResultModel(false, '城市编码错误'));
        // }

        // $lineFastR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $cityCodeR->data['city_code'], 'is_del' => 0, 'is_show' => 1), 'LineFast');
        // if (!$lineFastR->ret) {
        //     return $this->output(new \ResultModel(false, '暂未添加摆渡接单'));
        // }

        // //路径规划计算距离
        // $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($start_longitude, $start_latitude, $end_longitude, $end_latitude);
        // if (!$distanceR->ret) {
        //     return $this->output(new \ResultModel(false, '定位异常，请稍后再试'));
        // }

        // $distance = round($distanceR->data['distance']/ 1000, 2);//保留两位
        // //计费方式
        // $priceR = $this->getLineFastPredictPrice($lineFastR->data['id'], $distance * 1000, 0 /*$distanceR->data['duration']*/);
        // if (!$priceR->ret) {
        //     return $this->output(new \ResultModel(false, '网络异常，请稍后再试'));
        // }

        // $retData['price'] = $priceR->data['price'];
        // $retData['distance'] = $distance;
        // $retData['duration'] = 0;
        // $retData['start_address_remark'] = $start_point_address_remark;
        // $retData['start_longitude'] = $start_point_longitude;
        // $retData['start_latitude'] = $start_point_latitude;
        // $retData['end_address_remark'] = $end_point_address_remark;
        // $retData['end_longitude'] = $end_point_longitude;
        // $retData['en_latitude'] = $end_point_latitude;

        // return $this->output(new \ResultModel(true, $retData));
    }


    /**
     * 预订顺风车
     * @param int $line_id 顺风车线路id
     * @param int $book_seating 预定座位数
     * @param string $start_longitude 出发经度
     * @param string $start_latitude 出发纬度
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doMobileBookLineFreeRide($line_id, $book_seating = 1, $start_longitude = null, $start_latitude = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }
        $this->data['reseverd_phone'] = $passengerR->data['cellphone'];

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        $lineFreeRideR = $this->find(array('id' => $line_id, 'is_del' => 0), 'LineFreeRide');
        if ($lineFreeRideR->data['status'] != 1) {
            return $this->output(new \ResultModel(false, '该顺风车已关闭，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        $orderWhere = " passenger_id = ".$passenger_id. " AND line_id = ".$lineFreeRideR->data['id']. " AND state < 6 ";
        $orderR = $this->find($orderWhere, 'Order');
        if ($orderR->ret) {
            return $this->output(new \ResultModel(false, '您已经下过该行程的订单，前往个人中心查看'));
        }

        if ($lineFreeRideR->data['residual_seating'] < $book_seating) {
            return $this->output(new \ResultModel(false, '剩余座位数不足'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $currentTime = date('Y-m-d H:i:s', time());
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_6;
                $this->data['driver_id'] = $lineFreeRideR->data['driver_id'];
                $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                $this->data['mchid'] = $lineFreeRideR->data['mchid'];
                $this->data['line_id'] = $lineFreeRideR->data['id'];
                $this->data['branchid'] = $lineFreeRideR->data['branchid'];

                $this->data['start_longitude'] = $start_longitude;
                $this->data['start_latitude'] = $start_latitude;
                $this->data['start_address_code'] = $lineFreeRideR->data['start_address_code'];
                $this->data['start_address_remark'] = $lineFreeRideR->data['start_address_remark'];
                $this->data['end_longitude'] = $lineFreeRideR->data['end_longitude'];
                $this->data['end_latitude'] = $lineFreeRideR->data['end_latitude'];
                $this->data['end_address_code'] = $lineFreeRideR->data['end_address_code'];
                $this->data['end_address_remark'] = $lineFreeRideR->data['end_address_remark'];
                $this->data['start_region_name'] = $lineFreeRideR->data['start_name'];
                $this->data['end_region_name'] = $lineFreeRideR->data['end_name'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $lineFreeRideR->data['start_time'];
                $this->data['book_seating'] = $book_seating;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->data['create_time'] = $currentTime;
                $this->data['update_time'] = $currentTime;
                $this->startTrans();

                $mch = M('AnnualFee')->where(array('mchid' => $lineFreeRideR->data['mchid']))->find();
                $this->data['price'] = $lineFreeRideR->data['price'] * $book_seating;
                $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
                $this->data['split'] = $mch['split'] / 100;

                $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::ORDER_TYPE_6);
                if (!$realPriceR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, $realPriceR->data));
                }
                $this->data['real_price'] = $realPriceR->data['real_price'];

                //添加订单
                $r = $this->add('Order');
                if ($r->ret) {
                    if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $lineFreeRideS = $this->save('LineFreeRide', array('id' => $line_id, 'residual_seating' => ($lineFreeRideR->data['residual_seating'] - $book_seating)));
                        if (!$lineFreeRideS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败'));
                        }
                        $this->commitTrans();
                        $driverR = $this->find(array('driver_id' => $this->data['driver_id']), 'Driver');
                        $cellphone = $this->encryptionCellphoneByCellphone($passengerR->data['cellphone']);
                        //通知服务,通知司机
                        if (C('SMS_ON')) {
                            //2025.2.17 新增新订单司机短信通知
                            $this->sendInnerNotification('driver', 'sms', 'sms_driver_new_order', $orderR->data['order_id']);
                        }

                        if (C('WX_TEMPLATE_ON')) {
                            //【CC招车】您有新的订单了，订单编号：{1},乘客电话{2},请及时处理!
                            //您有新的订单了
                            //订单编号：C1510170029132
                            //时间：业务经理
                            //有问题请拨************
                            $temp = array(
                                '您有新的['.$this->getOrderType($this->data['type']).']订单了',
                                $this->data['order_no'],
                                $currentTime,
                                '乘客电话:'.$cellphone.'请及时处理!'
                            );
                            $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_5);
                            unset($temp);
                        }
                    } else {
                        $this->commitTrans();
                    }

                    // 新订单Websocket消息通知
                    $this->sendInnerWebSocket($r->data);

                    return $this->output(new \ResultModel(true, ['order_id' => $r->data]));
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败'));
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下单失败'));
            }
        }
        return $this->output($r);
    }


    /**
     * 计算顺风车价格
     * @param int $book_seating 座位数，小于6个
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     */
    public function doMobileComputeLineFreeRidePrice($book_seating, $start_longitude, $start_latitude, $end_longitude, $end_latitude, $ID_number = null, $ID_real_name = null)
    {
        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        if ($book_seating > 6) {
            return $this->output(new \ResultModel(false, '乘车人数最多6人'));
        }

        if (!$start_latitude || !$start_longitude || !$end_latitude || !$end_longitude) {
            return $this->output(new \ResultModel(false, '请重新选择位置'));
        }

        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'cellphone,mchid');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        //路径规划计算距离
        $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($start_longitude, $start_latitude, $end_longitude, $end_latitude);
        if (!$distanceR->ret) {
            return $this->output(new \ResultModel(false, '定位异常，请稍后再试'));
        }

        $distance = (int)($distanceR->data['distance'] / 1000);
        //        $distance = round($distanceR->data['distance']/ 1000,2);//保留两位
        $mileagePriceR = $this->find(array('mchid' => $passengerR->data['mchid'], 'is_default' => 1), 'MileagePrice');
        if (!$mileagePriceR->ret) {
            return $this->output(new \ResultModel(false, '暂未开通'));
        }
        //计费方式
        $price = $this->getMileagePrice($mileagePriceR->data['mileage_price_id'], $distance);
        return $this->output(new \ResultModel(true, array('price' => $price * $book_seating, 'distance' => $distance)));
    }

    /**
     * 发布顺风车
     * @param date $start_time 出发时间
     * @param int $book_seating 剩余座位数
     * @param string $price 总价（通过路径规划计算得来）
     * @param string $start_longitude 开始经度
     * @param string $start_latitude  开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $reseverd_info 备注信息
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doReleaseFreeRide($start_time = '', $book_seating = 1, $price, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code, $start_address_remark = null, $end_address_code, $end_address_remark = null, $reseverd_info = null, $ft_number = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null)
    {
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        if (strtotime($start_time) < time()) {
            return $this->output(new \ResultModel(false, '出发时间不能小于当前时间'));
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        $this->data['reseverd_phone'] = $passengerR->data['cellphone'];

        if (!is_numeric($price) &&  $price <= 0) {
            return $this->output(new \ResultModel(false, '价格计算异常'));
        }
        $realPriceR = $this->getRealPrice($this->data['price'], $coupon_record_id, \CommonDefine::ORDER_TYPE_6);
        if (!$realPriceR->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, $realPriceR->data));
        }
        $this->data['real_price'] = $realPriceR->data['real_price'];

        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }

        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        if ($book_seating > 6) {
            return $this->output(new \ResultModel(false, '乘车人数最多6人'));
        }

        $startCodeArr = $this->getProvinceCityAreaCodeByCode($start_address_code);
        $startNameR = $this->getParentAdministrativeDivisionNameByCode($start_address_code);
        $endCodeArr = $this->getProvinceCityAreaCodeByCode($end_address_code);
        $endNameR = $this->getParentAdministrativeDivisionNameByCode($end_address_code);
        if (!$startNameR->ret || !$endNameR->ret) {
            return $this->output(new \ResultModel(false, '定位异常，请稍后重试'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            $payMode = $payModeR->data['value'];
        }

        $this->data['passenger_id'] = $passenger_id;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_6;
        $this->data['mchid'] = $passengerR->data['mchid'];
        $this->data['start_region_name'] = $startNameR->data['name'];
        $this->data['end_region_name'] = $endNameR->data['name'];

        $this->data['drive_model'] = 1;
        $this->data['start_time'] = $start_time;
        $this->data['book_seating'] = $book_seating;
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->startTrans();

        $mch = M('AnnualFee')->where(array('mchid' => $passengerR->data['mchid']))->find();
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;

        $r = $this->add('Order');
        if ($r->ret) {
            $this->commitTrans();
            //通知服务
            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data);
            return $this->output(new \ResultModel(true, ['order_id' => $r->data]));
        }

        $this->transRollback();
        return $this->output(new \ResultModel(false, '下单失败'));
    }

    /**
     * 计算快车价格
     * @param int $book_seating 座位数，小于6个
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $end_longitude 结束经度
     * @param string $start_address_code 起始地行政区域编码
     * @param string $end_address_code 结束地行政区域编码
     */
    public function doMobileComputeLineFastPrice($book_seating, $start_longitude, $start_latitude, $end_longitude, $end_latitude, $start_address_code = null, $end_address_code = null)
    {
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $params = array(
            'start_longitude' => $start_longitude,
            'start_latitude' => $start_latitude,
            'book_seating' => $book_seating,
            'end_longitude' => $end_longitude,
            'end_latitude' => $end_latitude,
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
        );
        $ret = new \ResultModel(false);
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/kuaiche_fee", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('预估费用失败：%s', $results['message']);
        }
        return $this->output($ret);

        // if (!is_numeric($book_seating) || $book_seating <= 0) {
        //     return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        // }

        // if ($book_seating > 6) {
        //     return $this->output(new \ResultModel(false, '乘车人数最多6人'));
        // }

        // if (!$start_latitude || !$start_longitude || !$end_latitude|| !$end_longitude) {
        //     return $this->output(new \ResultModel(false, '请重新选择位置'));
        // }

        // if (empty($start_address_code) || empty($end_address_code)) {
        //     return $this->output(new \ResultModel(false, '区域编码错误'));
        // }

        // //        $isSameCityR = $this->isSameCityTwo($start_address_code, $end_address_code);
        // //        if(!$isSameCityR->ret){
        // //            return $this->output(new \ResultModel(false, '跨城请使用城际拼车服务'));
        // //        }
        // $cityCodeR = $this->resolutionAddressTwo($start_address_code);
        // if (!$cityCodeR->ret) {
        //     return $this->output(new \ResultModel(false, '城市编码错误'));
        // }

        // $passengerR = $this->find(array('passenger_id'=>$this->state->user_id), 'Passenger', 'cellphone,mchid');
        // if (!$passengerR->ret) {
        //     return $this->output(new \ResultModel(false, '该账户不存在'));
        // } elseif (empty($passengerR->data['cellphone'])) {
        //     return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        // }

        // $lineFastR = $this->find(array('mchid' => $passengerR->data['mchid'], 'start_city_code' => $cityCodeR->data['city_code'], 'is_del' => 0, 'is_show' => 1), 'LineFast');
        // if (!$lineFastR->ret) {
        //     return $this->output(new \ResultModel(false, '暂未开通'));
        // }

        // //路径规划计算距离
        // $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($start_longitude, $start_latitude, $end_longitude, $end_latitude);
        // if (!$distanceR->ret) {
        //     return $this->output(new \ResultModel(false, '定位异常，请稍后再试1'));
        // }

        // $distance = round($distanceR->data['distance']/ 1000, 2);//保留两位
        // //计费方式
        // $priceR = $this->getLineFastPredictPrice($lineFastR->data['id'], $distance * 1000, 0/*$distanceR->data['duration']*/);
        // if (!$priceR->ret) {
        //     return $this->output(new \ResultModel(false, '网络异常，请稍后再试'));
        // }
        // $retData['price'] = $priceR->data['price'];
        // $retData['line_fast_id'] = $lineFastR->data['id'];
        // $retData['rules'] = [
        //     'start_longitude' => $start_longitude,
        //     'start_latitude' => $start_latitude,
        //     'end_longitude' => $end_longitude,
        //     'end_latitude' => $end_latitude,
        //     'distance' => $distance,
        //     'duration' => $distanceR->data['duration'],
        //     'base_price' => $priceR->data['base_price'],
        //     'mileage_price' => $priceR->data['mileage_price'],
        //     'duration_price' => $priceR->data['duration_price'],
        //     'longdistance_price' => $priceR->data['longdistance_price'],
        // ];
        // return $this->output(new \ResultModel(true, $retData));


    }

    /**
     * 预订快车线路
     * @param int $line_fast_id 快车线路id
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     * @param string $coupon_record_id 优惠券（默认为空）
     */
    public function doMobileBookLineFast($line_fast_id, $book_seating = 1, $start_time = null, $reseverd_phone = null, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null, $distance = null, $duration = null, $is_buy_insurance = 0)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];
        $params = array(
            'line_fast_id' => $line_fast_id,
            'book_seating' => $book_seating,
            'reseverd_phone' => $reseverd_phone,
            'start_longitude' => $start_longitude,
            'start_latitude' => $start_latitude,
            'end_longitude' => $end_longitude,
            'end_latitude' => $end_latitude,
            'start_address_remark' => ($start_address_remark),
            'end_address_remark' => ($end_address_remark),
            'start_time' => $start_time,
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
            'reseverd_info' => ($reseverd_info),
            'distance' => $distance,
            'duration' => $duration,
            'is_buy_insurance' => $is_buy_insurance,
            'coupon_record_id' => $coupon_record_id,
        );
        $ret = new \ResultModel(false);
        $bizType = \CommonDefine::ORDER_TYPE_7;
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/types/{$bizType}/orders", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('下单失败：%s', $results['message']);
        }
        return $this->output($ret);

        // $currentTime = date('Y-m-d H:i:s', time());
        // if (is_null($start_time) || empty($start_time)) {
        //     $start_time = $currentTime;
        // } else {
        //     $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        // }
        // $passenger_id = $this->state->user_id;
        // $passengerR = $this->find(array('passenger_id'=>$passenger_id), 'Passenger');
        // if (!$passengerR->ret) {
        //     return $this->output(new \ResultModel(false, '该账户不存在'));
        // } elseif (empty($passengerR->data['cellphone'])) {
        //     return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        // }

        // //预留电话
        // if (is_null($reseverd_phone) || empty($reseverd_phone)) {
        //     $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        // }

        // $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        // if (!$checkRet->ret) {
        //     return $this->output(new \ResultModel(false, $checkRet->data));
        // }

        // if (!is_numeric($book_seating) || $book_seating <= 0) {
        //     return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        // }

        // $data = $this->find(array('id' => $line_fast_id, 'is_del' => 0, 'is_show' => 1), 'LineFast');
        // if ($data->data['status'] != 1) {
        //     return $this->output(new \ResultModel(false, '暂未开通'));
        // }

        // if ($start_longitude && $start_latitude) {
        //     if ($data->data['area_lat_start'] && $data->data['area_lng_start']) {
        //         $pointCalculationObj = new \PointCalculation();
        //         $circle['center']['lat'] = $data->data['area_lat_start'];
        //         $circle['center']['lng'] = $data->data['area_lng_start'];
        //         $circle['radius'] = $data->data['area_radius_start'];
        //         if (!$pointCalculationObj->is_point_in_circle(['lat'=> $start_latitude, 'lng' => $start_longitude], $circle)) {
        //             return $this->output(new \ResultModel(false, '暂不支持该区域'));
        //         }
        //     }
        // }

        // if ($end_longitude && $end_latitude) {
        //     if ($data->data['area_lat_end'] && $data->data['area_lng_end']) {
        //         $pointCalculationObj = new \PointCalculation();
        //         $circle['center']['lat'] = $data->data['area_lat_end'];
        //         $circle['center']['lng'] = $data->data['area_lng_end'];
        //         $circle['radius'] = $data->data['area_radius_end'];
        //         if (!$pointCalculationObj->is_point_in_circle(['lat'=> $end_latitude, 'lng' => $end_longitude], $circle)) {
        //             return $this->output(new \ResultModel(false, '暂不支持该区域'));
        //         }
        //     }
        // }

        // $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        // /*        $systemConfigR = $this->find(array('key'=>'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $data->data['mchid']),'SystemConfig');
        //         if($systemConfigR->ret){
        //             $robOrder = $systemConfigR->data['value'];
        //         }*/

        // //订单支付模式
        // $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;//动态计价只能是后支付模式
        // //        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
        // //        if($payModeR->ret){
        // //            $payMode = $payModeR->data['value'];
        // //        }

        // $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        // if ($r->ret) {
        //     if (\intval($r->data['state']) !== 1) {
        //         //附近司机
        //         $aroundDriversR = $this->getLineFastAroundDrivers($line_fast_id, $start_longitude, $start_latitude, $book_seating);
        //         if (!$aroundDriversR->ret) {
        //             return $this->output(new \ResultModel(false, '附近暂无司机请电话联系客服'));
        //         } else {
        //             $driver_id = $aroundDriversR->data[0]['driver_id'];
        //         }

        //         $this->data['passenger_id'] = $passenger_id;
        //         $this->data['type'] = \CommonDefine::ORDER_TYPE_7;
        //         $this->data['mchid'] = $data->data['mchid'];
        //         $this->data['line_id'] = $data->data['id'];
        //         $this->data['branchid'] = $data->data['branchid'];

        //         $this->data['start_address_code'] = $data->data['start_address_code'];
        //         $this->data['end_address_code'] = $data->data['end_address_code'];
        //         $this->data['start_address_remark'] = empty($start_address_remark) ? $data->data['start_name'] : $start_address_remark;
        //         $this->data['end_address_remark'] = empty($end_address_remark) ? $data->data['end_name'] : $end_address_remark;
        //         $this->data['drive_model'] = 1;
        //         $this->data['start_time'] = $start_time;
        //         $this->data['book_seating'] = $book_seating;
        //         $this->data['order_no'] = $this->createOrderNo();
        //         $this->data['rob_order'] = $robOrder;
        //         $this->data['pay_mode'] = $payMode;
        //         $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        //         $this->data['driver_id'] = $driver_id;

        //         //                //路径规划计算距离
        //         //                $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($start_longitude,$start_latitude,$end_longitude,$end_latitude);
        //         //                if(!$distanceR->ret){
        //         //                    return $this->output(new \ResultModel(false, '定位异常，请稍后再试'));
        //         //                }
        //         //                $distance = round($distanceR->data['distance']/ 1000, 2);

        //         //预估费用
        //         $priceR = $this->getLineFastPredictPrice($data->data['id'], $distance * 1000, $duration);
        //         if (!$priceR->ret) {
        //             return $this->output(new \ResultModel(false, '网络异常，请稍后再试'));
        //         }
        //         $price = $priceR->data['price'];

        //         $this->startTrans();

        //         //读取优惠券信息
        //         $realPriceR = $this->getRealPrice($price, $coupon_record_id);
        //         if (!$realPriceR->ret) {
        //             $this->transRollback();
        //             return $this->output(new \ResultModel(false, '下单失败，请稍后再试'));
        //         }
        //         $this->data['real_price'] = $realPriceR->data['real_price'];

        //         //更新乘客信息
        //         $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'start_address_code' => $data->data['start_address_code'], 'end_address_code' => $data->data['end_address_code'], 'start_time' => $data->data['start_time']));
        //         if ($r) {
        //             $mch = M('AnnualFee')->where(array('mchid' => $data->data['mchid']))->find();
        //             $isChannel = false;
        //             if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
        //                 //渠道价格
        //                 $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
        //                 $this->data['channel_account_id'] = $passengerR->data['invite_id'];
        //                 $isChannel = true;
        //             }

        //             if ($isChannel) {
        //                 //暂不支持渠道代约
        //                 $this->data['price'] = $price;
        //                 $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        //                 $this->data['split'] = $mch['split']/100;
        //             } else {
        //                 $this->data['price'] = $price;
        //                 $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        //                 $this->data['split'] = $mch['split']/100;
        //             }

        //             //添加订单
        //             $this->data['create_time'] = $currentTime;
        //             $this->data['update_time'] = $currentTime;
        //             $r = $this->add('Order');
        //             if ($r->ret) {
        //                 //添加计费信息
        //                 $orderFastFeeeData['order_id'] = $r->data;
        //                 $orderFastFeeeData['base_price'] = $priceR->data['base_price'];
        //                 $orderFastFeeeData['total_mileage'] = $distance * 1000;
        //                 $orderFastFeeeData['total_mileage_price'] = $priceR->data['mileage_price'];
        //                 $orderFastFeeeData['total_duration'] = $duration;
        //                 $orderFastFeeeData['total_duration_price'] = $priceR->data['duration_price'];
        //                 $orderFastFeeeData['total_longdistance'] = $this->computeLongDistance($line_fast_id, $distance, date('H:i:s', time()));
        //                 $orderFastFeeeData['total_longdistance_price'] = $priceR->data['longdistance_price'];
        //                 $orderFastFeeeData['total_wait_timeout'] = 0;
        //                 $orderFastFeeeData['total_wait_timeout_price'] = 0;
        //                 if (!$this->add('OrderFastFee', $orderFastFeeeData)->ret) {
        //                     $this->transRollback();
        //                     return $this->output(new \ResultModel(false, '下单失败'));
        //                 }
        //                 $this->commitTrans();
        //                 if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
        //                     //通知服务
        //                     if ($robOrder == \CommonDefine::ROB_ORDER_1) {
        //                         $this->sendSms($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
        //                         $this->sendWxMessage($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
        //                     } else {
        //                         $this->sendSms($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
        //                         $this->sendWxMessage($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
        //                     }
        //                 }

        //                 // 新订单Websocket消息通知
        //                 $this->sendInnerWebSocket($r->data);

        //                 return $this->output(new \ResultModel(true, ['order_id' => $r->data]));
        //             } else {
        //                 $this->transRollback();
        //                 return $this->output(new \ResultModel(false, '下单失败'));
        //             }
        //         } else {
        //             $this->transRollback();
        //             return $this->output(new \ResultModel(false, '下单失败'));
        //         }
        //     } else {
        //         return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
        //     }
        // }
        // return $this->output($r);
    }

    /**
     * 计算出租车价格
     * @param int $book_seating 座位数，小于6个
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $end_longitude 结束经度
     * @param string $start_address_code 起始地行政区域编码
     * @param string $end_address_code 结束地行政区域编码
     */
    public function doMobileComputeLineTaxiPrice($book_seating, $start_longitude, $start_latitude, $end_longitude, $end_latitude, $start_address_code = null, $end_address_code = null)
    {
        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }

        if ($book_seating > 6) {
            return $this->output(new \ResultModel(false, '乘车人数最多6人'));
        }

        if (!$start_latitude || !$start_longitude || !$end_latitude || !$end_longitude) {
            return $this->output(new \ResultModel(false, '请重新选择位置'));
        }

        if (empty($start_address_code) || empty($end_address_code)) {
            return $this->output(new \ResultModel(false, '区域编码错误'));
        }

        $cityCodeR = $this->resolutionAddressTwo($start_address_code);
        if (!$cityCodeR->ret) {
            return $this->output(new \ResultModel(false, '城市编码错误'));
        }

        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'cellphone,mchid');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '该账户不存在'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '请先到个人中心绑定手机号'));
        }

        $lineTaxiR = $this->find(array('mchid' => $passengerR->data['mchid'], 'start_city_code' => $cityCodeR->data['city_code'], 'is_del' => 0, 'is_show' => 1), 'LineTaxi');
        if (!$lineTaxiR->ret) {
            return $this->output(new \ResultModel(false, '该区域暂未开通此业务！'));
        }

        if ($start_longitude && $start_latitude) {
            if ($lineTaxiR->data['area_lat_start'] && $lineTaxiR->data['area_lng_start']) {
                # 起始限域-经度 VS 起始限域-维度
                $pointCalculationObj = new \PointCalculation();
                $circle['center']['lat'] = $lineTaxiR->data['area_lat_start'];
                $circle['center']['lng'] = $lineTaxiR->data['area_lng_start'];
                $circle['radius'] = $lineTaxiR->data['area_radius_start'];
                if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                    return $this->output(new \ResultModel(false, '该区域暂未开通此业务'));
                }
            }
        }

        # 路径规划计算距离，调用高德地图API
        $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($start_longitude, $start_latitude, $end_longitude, $end_latitude);
        if (!$distanceR->ret) {
            return $this->output(new \ResultModel(false, '定位异常，请稍后再试1'));
        }
        # 高德行驶距离，单位米
        $distance = round($distanceR->data['distance'] / 1000, 2);
        //计费方式
        $priceR = $this->getLineTaxiPredictPrice($lineTaxiR->data['id'], $distance * 1000, $distanceR->data['duration']);
        if (!$priceR->ret) {
            return $this->output(new \ResultModel(false, '网络异常，请稍后再试'));
        }
        $retData['price'] = $priceR->data['price'];
        $retData['line_taxi_id'] = $lineTaxiR->data['id'];
        $retData['rules'] = [
            'distance' => $distance,
            'duration' => $distanceR->data['duration'],
            'base_price' => $priceR->data['base_price'],
            'mileage_price' => $priceR->data['mileage_price'],
            'duration_price' => $priceR->data['duration_price'],
            'longdistance_price' => $priceR->data['longdistance_price'],
        ];
        return $this->output(new \ResultModel(true, $retData));
    }

    /**
     * 预订出租车线路
     * @param int $line_taxi_id 出租车线路id
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     * @param string $coupon_record_id 优惠券（默认为空）
     */
    public function doMobileBookLineTaxi($line_taxi_id, $book_seating = 1, $start_time = '', $reseverd_phone = null, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null, $coupon_record_id = null, $distance = null, $duration = null)
    {
        # 出发时间认定
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }
        # 乘客ID
        $passenger_id = $this->state->user_id;
        $passengerR = $this->find(array('passenger_id' => $passenger_id), 'Passenger');
        if (!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '下单乘客信息不存在，请核对信息再试'));
        } elseif (empty($passengerR->data['cellphone'])) {
            return $this->output(new \ResultModel(false, '乘客信息错误：请先到个人中心绑定手机号'));
        }
        # 预留电话为空，使用乘客自己电话
        if (is_null($reseverd_phone) || empty($reseverd_phone)) {
            $this->data['reseverd_phone'] = $passengerR->data['cellphone'];
        }
        # 商户是否允许乘客在有未完成的订单时再下单
        $checkRet = $this->checkIsAllowOrder($passengerR->data['mchid'], $passenger_id);
        if (!$checkRet->ret) {
            return $this->output(new \ResultModel(false, $checkRet->data));
        }
        # 预定数判断
        if (!is_numeric($book_seating) || $book_seating <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }
        # 出租车线路是否有效
        $data = $this->find(array('id' => $line_taxi_id, 'is_del' => 0, 'is_show' => 1), 'LineTaxi');
        if ($data->data['status'] != 1) {
            return $this->output(new \ResultModel(false, '该区域暂未开通此业务！'));
        }
        # 出发地经纬度校验
        if ($start_longitude && $start_latitude) {
            if ($data->data['area_lat_start'] && $data->data['area_lng_start']) {
                $pointCalculationObj = new \PointCalculation();
                $circle['center']['lat'] = $data->data['area_lat_start'];
                $circle['center']['lng'] = $data->data['area_lng_start'];
                $circle['radius'] = $data->data['area_radius_start'];
                if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                    return $this->output(new \ResultModel(false, '该区域暂未开通此业务'));
                }
            }
        }
        # 目的地经纬度校验
        if ($end_longitude && $end_latitude) {
            if ($data->data['area_lat_end'] && $data->data['area_lng_end']) {
                $pointCalculationObj = new \PointCalculation();
                $circle['center']['lat'] = $data->data['area_lat_end'];
                $circle['center']['lng'] = $data->data['area_lng_end'];
                $circle['radius'] = $data->data['area_radius_end'];
                if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                    return $this->output(new \ResultModel(false, '该区域暂未开通此业务'));
                }
            }
        }
        # 抢单模式
        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        # 订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;//动态计价只能是后支付模式
        # 再次查询乘客信息
        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time,cellphone');
        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {

                //附近司机
                $driver_id = null;
                /**
                 * 采用新系统消息队列实现司机派单
                $aroundDriversR = $this->getLineTaxiAroundDrivers($line_taxi_id, $start_longitude, $start_latitude, $book_seating);
                if(!$aroundDriversR->ret) {
                    //                    return $this->output(new \ResultModel(false, '附近暂无司机，请电话联系客服'));
                } else {
                    $driver_id = $aroundDriversR->data[0]['driver_id'];
                    $this->data['appoint'] = \CommonDefine::APPOINT_TYPE_1;
                }
                */

                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = \CommonDefine::ORDER_TYPE_11;
                $this->data['mchid'] = $data->data['mchid'];
                $this->data['line_id'] = $data->data['id'];
                $this->data['branchid'] = $data->data['branchid'];

                $this->data['start_address_code'] = $data->data['start_address_code'];
                $this->data['end_address_code'] = $data->data['end_address_code'];
                $this->data['start_address_remark'] = empty($start_address_remark) ? $data->data['start_name'] : $start_address_remark;
                $this->data['end_address_remark'] = empty($end_address_remark) ? $data->data['end_name'] : $end_address_remark;
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = $start_time;
                $this->data['book_seating'] = $book_seating;
                $this->data['order_no'] = $this->createOrderNo();
                $this->data['rob_order'] = $robOrder;
                $this->data['pay_mode'] = $payMode;
                $this->data['driver_id'] = $driver_id;
                $this->data['state'] = \CommonDefine::ORDER_STATE_1;
                # 预估费用
                $priceR = $this->getLineTaxiPredictPrice($data->data['id'], $distance * 1000, $duration);
                if (!$priceR->ret) {
                    return $this->output(new \ResultModel(false, '网络异常，请稍后再试'));
                }
                # 价格
                $price = $priceR->data['price'];
                # 开启事务
                $this->startTrans();
                # 读取优惠券信息，如果有优惠券ID
                $realPriceR = $this->getRealPrice($price, $coupon_record_id);
                if (!$realPriceR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败，请稍后再试'));
                }
                $this->data['real_price'] = $realPriceR->data['real_price'];
                # 更新乘客信息
                $r = $this->save('Passenger', array(
                    'passenger_id' => $passenger_id,
                    'start_address_code' => $data->data['start_address_code'],
                    'end_address_code' => $data->data['end_address_code'],
                    'start_time' => $data->data['start_time']
                ));
                if ($r) {
                    # 商户分成比例查询
                    $mch = M('AnnualFee')->where(array('mchid' => $data->data['mchid']))->find();
                    $isChannel = false;
                    if (!empty($passengerR->data['invite_id']) && $passengerR->data['invite_type'] == \CommonDefine::INVITE_TYPE_1) {
                        # 乘客被邀请，更新渠道价格
                        $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
                        $this->data['channel_account_id'] = $passengerR->data['invite_id'];
                        $isChannel = true;
                    }
                    $splitRate = floatval(bcdiv($mch['split'], 100, 2));
                    if ($isChannel) {
                        //暂不支持渠道代约
                        $this->data['price'] = $price;
                        $this->data['offer_price'] = floatval(bcsub($this->data['price'], bcmul($this->data['price'], $splitRate, 2), 2));
                        $this->data['split'] = $splitRate;
                    } else {
                        $this->data['price'] = $price;
                        $this->data['offer_price'] = floatval(bcsub($this->data['price'], bcmul($this->data['price'], $splitRate, 2), 2));
                        $this->data['split'] = $splitRate;
                    }
                    # 添加信息到订单
                    $this->data['create_time'] = $currentTime;
                    $this->data['update_time'] = $currentTime;

                    $r = $this->add('Order');
                    if ($r->ret) {
                        # 添加出租车计费信息
                        $orderFastTaxiData['order_id'] = $r->data;
                        $orderFastTaxiData['base_price'] = $priceR->data['base_price'];
                        $orderFastTaxiData['total_mileage'] = $distance;
                        $orderFastTaxiData['total_mileage_price'] = $priceR->data['mileage_price'];
                        $orderFastTaxiData['total_duration'] = $duration;
                        $orderFastTaxiData['total_duration_price'] = $priceR->data['duration_price'];
                        $orderFastTaxiData['total_longdistance'] = $this->computeTaxiLongDistance($line_taxi_id, $distance, date('H:i:s', time()));
                        $orderFastTaxiData['total_longdistance_price'] = $priceR->data['longdistance_price'];
                        $orderFastTaxiData['total_wait_timeout'] = 0;
                        $orderFastTaxiData['total_wait_timeout_price'] = 0;
                        if (!$this->add('OrderTaxiFee', $orderFastTaxiData)->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '下单失败：添加出租车计费信息失败'));
                        }
                        $this->commitTrans();
                        # 支付模式，后支付就发送通知
                        /**
                         * 采用新的消息队列架构派单
                        if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        //通知服务
                        if (!empty($driver_id)) {
                            $this->sendMessage($r->data, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
                        } else {
                            if (C('REDIS_ON')) {
                                //加入订单池
                                $redisInstance = \Cache::getInstance('Redis', C('REDIS_ORDER_POOL'));
                                $redisInstance->push(\CommonDefine::ORDER_POOL_TAXI, $r->data);
                            }
                        }
                        }
                        */
                        # 新订单Websocket消息通知
                        $this->sendInnerWebSocket($r->data);
                        /**
                         * 出租车订单派发司机入队列
                         * <AUTHOR> <<EMAIL>>
                         */
                        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/taxi/orders/{$r->data}/dispatch", 'post');
                        $result = json_decode($response, true);
                        if ($result['status'] == 'error') {
                            return $this->output(new \ResultModel(false, $result['message']));
                        }
                        return $this->output(new \ResultModel(true, ['order_id' => $r->data]));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '下单失败：添加订单信息失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '下单失败：更新乘客信息错误'));
                }
            } else {
                return $this->output(new \ResultModel(false, '下单失败：在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }

    /**
     * 预订司机(手机端)
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doMobileBookDriver($driver_id, $passenger_id, $number = 1)
    {
        $mchid = cookie('mchid');
        $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('elt', 3), 'mchid' => $mchid), 'Order');
        if ($r->ret) {
            if ($r->data > 0) {
                return $this->output(new \ResultModel(false, '请先取消之前的预订或者完成之前的订单'));
            }
        }
        if (!is_numeric($number) || $number <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }
        $data = $this->find(array('driver_id' => $driver_id), 'Driver', 'state,start_address_code,end_address_code,residual_seating,cellphone,name,price');
        if ($data->data['state'] != 1) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }
        if ($number > $data->data['residual_seating']) {
            return $this->output(new \ResultModel(false, '预定人数超额，该车辆剩余座位不足' . $number . '个'));
        }
        if ($data->data['residual_seating'] <= 0) {
            return $this->output(new \ResultModel(false, '该车辆已无座'));
        }
        if ($number > $data->data['residual_seating']) {
            return $this->output(new \ResultModel(false, '预定人数大于剩余座位数量'));
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time');

        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = 1;
                $this->data['mchid'] = $mchid;
                $this->data['virtual_driver'] = $driver_id;//虚拟司机记录

                $this->data['start_address_code'] = $data->data['start_address_code'];
                $this->data['end_address_code'] = $data->data['end_address_code'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = date('Y-m-d H:i:s', time());
                $this->data['book_seating'] = $number;
                $this->data['residual_seating'] = $data->data['residual_seating'] - $number;
                $this->data['order_no'] = "NO_" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
                $this->startTrans();
                //更新乘客信息
                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'start_address_code' => $data->data['start_address_code'], 'end_address_code' => $data->data['end_address_code'], 'start_time' => $data->data['start_time']));
                if ($r) {
                    $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,price,openid,cellphone,mchid');
                    if ($driverR) {
                        $mch = M('AnnualFee')->where(array('mchid' => $mchid))->find();
                        $this->data['price'] = $data->data['price'] * $number;
                        $this->data['offer_price'] = $this->data['price'] - $data->data['price'] * $mch['split'] / 100;
                        //添加订单
                        $r = $this->add('Order');
                        if ($r->ret) {
                            $o = $this->sudo('Member', null);
                            if ($driverR->data['openid'] && $driverR->data['openid'] != "") {
                                $order = $this->find(array('order_id' => $r->data), 'Order', 'order_no,create_time');
                                $this->commitTrans();
                                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array(date('Y-m-d H:i', time())), \SMSUtil::TEMP_ID_PSPSORNEW, $driverR->data['mchid']);
                                return $this->output(new \ResultModel(true, '操作成功'));
                            } else {
                                $this->commitTrans();
                                return $this->output(new \ResultModel(true, '操作成功'));
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        return $this->output(new \ResultModel(false, '车主不存在'));
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }

    /**
     * 预订司机(手机端)
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param date $start_time 出发时间
     * @param boolean $is_insure 是否带保险（0-不带，1-带）
     * @param int $book_seating 预定座位数
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doMobileBookVDriver($driver_id, $passenger_id, $number = 1)
    {
        $mchid = cookie('mchid');
        $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('elt', 3), 'mchid' => $mchid), 'Order');
        if ($r->ret) {
            if ($r->data > 0) {
                return $this->output(new \ResultModel(false, '请先取消之前的预订或者完成之前的订单'));
            }
        }
        if (!is_numeric($number) || $number <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写的预定人数'));
        }
        $data = $this->find(array('driver_id' => $driver_id, 'virtual' => 1), 'Driver', 'state,start_address_code,end_address_code,residual_seating,cellphone,name,price');
        if ($number > 4) {
            return $this->output(new \ResultModel(false, '单次最多预订4个'));
        }

        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state,start_address_code,end_address_code,start_time');

        if ($r->ret) {
            if (\intval($r->data['state']) !== 1) {
                $this->data['passenger_id'] = $passenger_id;
                $this->data['type'] = 1;
                $this->data['mchid'] = $mchid;
                $this->data['virtual_driver'] = $driver_id;//虚拟司机记录

                $this->data['start_address_code'] = $data->data['start_address_code'];
                $this->data['end_address_code'] = $data->data['end_address_code'];
                $this->data['drive_model'] = 1;
                $this->data['start_time'] = date('Y-m-d H:i:s', time());
                $this->data['book_seating'] = $number;
                $this->data['residual_seating'] = $data->data['residual_seating'] - $number;
                $this->data['order_no'] = "NO_" . substr(strtotime($this->data['start_time']), 6, 4) . rand(100000, 999999);
                $this->startTrans();
                //更新乘客信息
                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'start_address_code' => $data->data['start_address_code'], 'end_address_code' => $data->data['end_address_code'], 'start_time' => $data->data['start_time']));
                if ($r) {
                    $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,price,openid,cellphone');
                    if ($driverR) {
                        $mch = M('AnnualFee')->where(array('mchid' => $mchid))->find();
                        $this->data['price'] = $data->data['price'];
                        $this->data['offer_price'] = $this->data['price'] - $data->data['price'] * $mch['split'] / 100;
                        //添加订单
                        $r = $this->add('Order');
                        if ($r->ret) {
                            $o = $this->sudo('Member', null);
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, '操作成功'));
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        return $this->output(new \ResultModel(false, '车主不存在'));
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                return $this->output(new \ResultModel(false, '在车上时不能预订司机'));
            }
        }
        return $this->output($r);
    }

    /**
     * 获取正在进行的拼车订单
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrders($page = 1, $size = 10, $fields = null)
    {
        $where['state'] = array('in', '1,2,3,4,5');
        $where['type'] = 1;
        $order = 'create_time desc';

        $where['passenger_id'] = $this->state->user_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,order_id,type, state,type,price,book_seating,line_id,start_time,branchid,driver_id,driver_isover,create_time,reseverd_phone,start_longitude,start_latitude,start_address_code,start_address_remark,end_address_remark,is_temp,is_custom,car_type_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                $r->data[$key]['start_time_timestamp'] = strtotime($r->data[$key]['start_time']);
                $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];

                //获取线路信息
                if ($value['type'] == 1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                if (!$r->data[$key]['start_address_remark'] && !$r->data[$key]['end_address_remark']) {
                    $r->data[$key]['start_address_remark'] = $r->data[$key]['start_name'];
                    $r->data[$key]['end_address_remark'] = $r->data[$key]['end_name'];
                }

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                //司机位置
                $r->data[$key]['position'] = "-";
                //预约中（即司机接单中）
                if ($value['state'] == 1) {
                    $r->data[$key]['message'] = "派单中";
                } elseif ($value['state'] == 2) {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                    $r->data[$key]['message'] = "已接单,等待接我上车";
                } elseif ($value['state'] == 3) {
                    //已上车,等待出发
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'state');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 4) {
                    //在路上
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 5) {
                    //已送达，待支付
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } else {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['driver_longitude'] = "";
                        $r->data[$key]['driver_latitude'] = "";
                        $r->data[$key]['driver_address'] = "";
                    }
                }

                //                $r->data[$key] = $this->sudoGetEntityById("order_id", $value['order_id'], 'Passenger\\Order', 'getOrder', $fields)->data;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取正在进行的所有订单
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrdersOnWay($page = 1, $size = 10, $fields = null)
    {
        $where['state'] = array('in', '1,2,3,4,5');
        $order = 'create_time desc';

        $where['passenger_id'] = $this->state->user_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,order_id,type, state, price,book_seating,line_id,start_time,branchid,driver_id,driver_isover,create_time,reseverd_phone,reseverd_person,start_longitude,start_latitude,start_address_code,start_address_remark,end_address_remark,is_temp,is_custom,is_face,pay_mode,is_pay,car_type_id,delivery_person,delivery_phone,weight,agency_id,end_longitude,end_latitude,start_region_name,end_region_name,ft_number');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                $r->data[$key]['start_time_timestamp'] = strtotime($r->data[$key]['start_time']);
                $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];

                //获取线路信息
                if ($value['type'] == 1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        }
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $r->data[$key]['line_class_detail'] = [];
                    $lineClassDetailR =  $this->getLineClassDetail($r->data[$key]['line_id']);
                    if ($lineClassDetailR->ret) {
                        $r->data[$key]['line_class_detail'] = $lineClassDetailR->data;
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                        $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                        unset($r->data[$key]['start_region_name']);
                        unset($r->data[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                if (!$r->data[$key]['start_address_remark'] && !$r->data[$key]['end_address_remark']) {
                    $r->data[$key]['start_address_remark'] = $r->data[$key]['start_name'];
                    $r->data[$key]['end_address_remark'] = $r->data[$key]['end_name'];
                }

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                //司机位置
                $r->data[$key]['position'] = "-";
                //预约中（即司机接单中）
                if ($value['state'] == 1) {
                    $r->data[$key]['message'] = "派单中";
                } elseif ($value['state'] == 2) {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                    $r->data[$key]['message'] = "已接单,等待接我上车";
                } elseif ($value['state'] == 3) {
                    //已上车,等待出发
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'state');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 4) {
                    //在路上
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 5) {
                    //已送达，待支付
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } else {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = "";
                        $r->data[$key]['driver_latitude'] = "";
                        $r->data[$key]['driver_address'] = "";
                    }
                }

                if (in_array($value['state'], array(3,4,5))) {
                    if (isset($this->state->data['branch_code'])) {
                        unset($this->state->data['branch_code']);
                        $passengerInfo = $this->state->data;
                        \StateModel::save($this->state->user_id, "", \StateModel::$PASSENGER, $passengerInfo);
                    }
                }


                //                $r->data[$key] = $this->sudoGetEntityById("order_id", $value['order_id'], 'Passenger\\Order', 'getOrder', $fields)->data;
            }
        }
        return $this->output($r);
    }

    /**
    * 获取订单类型
    * @param int $type 订单类型
    */
    public function getOrderTypeStringAttribute($type)
    {
        return isset(self::$businessValueTypes[$type]) ? self::$businessValueTypes[$type] : '';
    }


    /**
     * 新版本订单详情
     *
     * @param integer $order_id
     * @return void
     */
    public function getOrderProfile($order_id)
    {
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/orders/{$order_id}", 'get', [], $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('订单错误：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
    * 定制客运订单支付页面联盟广告
    *
    * @return \ResultModel
    */
    public function getCustomizedTransportPayAd()
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/platforms/1/clients/1/placements/customized-transport-pay/ad_contents", 'get', array(), $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
        }
        return $this->output($ret);
    }


    /**
    * 新版本订单退款记录
    *
    * @param integer $order_id
    * @return void
    */
    public function getOrderRefunds($order_id)
    {
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/orders/{$order_id}/refunds", 'get', [], $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('订单错误：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
    * 新版本订单车票退款记录
    *
    * @param integer $ticket
    * @return void
    */
    public function getTicketRefunds($ticket)
    {
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/tickets/{$ticket}/refunds", 'get', [], $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('车票错误：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
     * 订单详情
     *
     * @param integer $order_id
     * @return void
     */
    public function getOrderDetail($order_id)
    {
        $where['order_id'] = $order_id;
        $where['passenger_id'] = $this->state->user_id;

        $r = $this->find($where, 'Order', 'order_no,order_id,type, state, price, real_price,merge_price,book_seating,line_id,start_time,branchid,driver_id,driver_isover,create_time,reseverd_phone,reseverd_person,start_longitude,start_latitude,start_address_code,start_address_remark,end_address_remark,is_temp,is_custom,is_face,pay_mode,is_pay,car_type_id,delivery_person,delivery_phone,weight,agency_id,end_longitude,end_latitude,start_region_name,end_region_name,ft_number,coupon_record_id,student_order_group_id,student_order_type,seat_optional,is_buy_insurance,insurance_price,ferry_type,line_class_train_id,seat_optional,ch_id,ferry_price,reseverd_info,refund_amount,refund_total_amount,refund_fee,refund_ticket_ratio,total_booking_seats,refund_status');
        if ($r->ret) {
            //获取乘客信息
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
            $r->data['start_time_timestamp'] = strtotime($r->data['start_time']);
            $r->data['cellphone'] = $passengerR->data['cellphone'];
            $r->data['reseverd_phone'] = is_null($r->data['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data['reseverd_phone'];
            $r->data['passenger_name'] = $passengerR->data['name'];
            $r->data['order_type_string'] = $this->getOrderTypeStringAttribute($r->data['type']);   //订单类型

            //获取线路信息
            if ($r->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                $lineR = $this->find(array('id' => $r->data['line_id']), 'Line');
                if ($lineR->ret) {
                    $r->data['start_name'] = $lineR->data['start_name'];
                    $r->data['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_2) {
                if ($r->data['is_custom'] == 0) {
                    $lineR = $this->find(array('id' => $r->data['line_id']), 'LineChartered');
                    if ($lineR->ret) {
                        $r->data['start_name'] = $lineR->data['start_name'];
                        $r->data['end_name'] = $lineR->data['end_name'];
                    }
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_3) {
                $lineR = $this->find(array('id' => $r->data['line_id']), 'Line');
                if ($lineR->ret) {
                    $r->data['start_name'] = $lineR->data['start_name'];
                    $r->data['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_4) {
                $agencyR = $this->find(array('agency_id' => $r->data['agency_id']), 'Agency');
                if ($agencyR->ret) {
                    $r->data['name'] = $agencyR->data['name'];
                    $r->data['summary'] = $agencyR->data['summary'];
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                $r->data['line_class_detail'] = [];
                $lineClassDetailR =  $this->getLineClassDetail($r->data['line_id']);
                if ($lineClassDetailR->ret) {
                    $r->data['line_class_detail'] = $lineClassDetailR->data;
                }

                $r->data['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data['seat_optional']);

                $r->data['ferry_order'] = [];
                if ($r->data['ch_id']) {
                    $r->data['ferry_order'] = $this->queryFerryOrder(explode(',', $r->data['ch_id']));
                }

            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                if (empty($r->data['line_id'])) {//乘客发布的
                    $r->data['start_name'] = $r->data['start_region_name'];
                    $r->data['end_name'] = $r->data['end_region_name'];
                    unset($r->data['start_region_name']);
                    unset($r->data['end_region_name']);
                } else {
                    $LineFreeRideR = $this->find(array('id' => $r->data['line_id']), 'LineFreeRide');
                    if ($LineFreeRideR->ret) {
                        $r->data['start_name'] = $LineFreeRideR->data['start_name'];
                        $r->data['end_name'] = $LineFreeRideR->data['end_name'];
                    }
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_7) {
                $r->data['start_name'] = $r->data['start_address_remark'];
                $r->data['end_name'] = $r->data['end_address_remark'];
                $r->data['rules'] = [];
                $orderFastFeeR = $this->find(['order_id' => $r->data['order_id']], 'OrderFastFee', 'order_fast_fee_id,order_id,create_time,update_time', true);
                if ($orderFastFeeR->ret) {
                    $r->data['rules'] = $orderFastFeeR->data;
                    $r->data['rules']['total_mileage'] = $orderFastFeeR->data['total_mileage'] / 1000;
                }
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_8) {
                $orderGroupR = $this->find('student_order_group_id = '.$r->data['student_order_group_id'], 'YyStudentOrderGroup');
                $r->data['student_order_pay_mode'] = $orderGroupR->ret ? $orderGroupR->data['student_order_pay_mode'] : 0;
            } elseif ($r->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                $r->data['start_name'] = $r->data['start_address_remark'];
                $r->data['end_name'] = $r->data['end_address_remark'];
                $r->data['rules'] = [];
                $orderTaxiFeeR = $this->find(['order_id' => $r->data['order_id']], 'OrderTaxiFee', 'order_taxi_fee_id,order_id,create_time,update_time', true);
                if ($orderTaxiFeeR->ret) {
                    $r->data['rules'] = $orderTaxiFeeR->data;
                    $r->data['rules']['total_mileage'] = $orderTaxiFeeR->data['total_mileage'] / 1000;
                }
            }

            unset($r->data['is_custom']);

            if (!$r->data['start_address_remark'] && !$r->data['end_address_remark']) {
                $r->data['start_address_remark'] = $r->data['start_name'];
                $r->data['end_address_remark'] = $r->data['end_name'];
            }

            $branchR = $this->find(array('admin_id' => $r->data['branchid']), 'Admin');
            if ($branchR->ret) {
                $r->data['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
            }

            //司机位置
            $r->data['position'] = "-";
            //预约中（即司机接单中）
            if ($r->data['state'] == 1) {
                $r->data['message'] = "派单中";
            } elseif ($r->data['state'] == 2) {
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data['driver_name'] = $driverR->data['name'];
                    $r->data['driver_header_ico'] = $driverR->data['header_ico'];
                    $r->data['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data['car_brand'] = $driverR->data['car_brand'];
                    $r->data['driver_longitude'] = $driverR->data['longitude'];
                    $r->data['driver_latitude'] = $driverR->data['latitude'];
                }
                $r->data['message'] = "已接单,等待接我上车";
            } elseif ($r->data['state'] == 3) {
                //已上车,等待出发
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver', 'state');
                if ($driverR->ret) {
                    $r->data['driver_name'] = $driverR->data['name'];
                    $r->data['driver_header_ico'] = $driverR->data['header_ico'];
                    $r->data['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data['car_brand'] = $driverR->data['car_brand'];
                    $r->data['driver_longitude'] = $driverR->data['longitude'];
                    $r->data['driver_latitude'] = $driverR->data['latitude'];
                    $r->data['driver_address'] = $driverR->data['address'];
                }
            } elseif ($r->data['state'] == 4) {
                //在路上
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data['driver_name'] = $driverR->data['name'];
                    $r->data['driver_header_ico'] = $driverR->data['header_ico'];
                    $r->data['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data['car_brand'] = $driverR->data['car_brand'];
                    $r->data['driver_longitude'] = $driverR->data['longitude'];
                    $r->data['driver_latitude'] = $driverR->data['latitude'];
                    $r->data['driver_address'] = $driverR->data['address'];
                }
            } elseif ($r->data['state'] == 5) {
                //已送达，待支付
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data['driver_name'] = $driverR->data['name'];
                    $r->data['driver_header_ico'] = $driverR->data['header_ico'];
                    $r->data['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data['car_brand'] = $driverR->data['car_brand'];
                    $r->data['driver_longitude'] = $driverR->data['longitude'];
                    $r->data['driver_latitude'] = $driverR->data['latitude'];
                    $r->data['driver_address'] = $driverR->data['address'];
                }
            } else {
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data['driver_name'] = $driverR->data['name'];
                    $r->data['driver_header_ico'] = $driverR->data['header_ico'];
                    $r->data['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data['car_brand'] = $driverR->data['car_brand'];
                    $r->data['driver_longitude'] = "";
                    $r->data['driver_latitude'] = "";
                    $r->data['driver_address'] = "";
                }
            }

            if (in_array($r->data['state'], array(3,4,5))) {
                if (isset($this->state->data['branch_code'])) {
                    unset($this->state->data['branch_code']);
                    $passengerInfo = $this->state->data;
                    \StateModel::save($this->state->user_id, "", \StateModel::$PASSENGER, $passengerInfo, $this->mchid);
                }
            }

            if (!empty($r->data['car_tail_number'])) {
                $r->data['car_tail_number'] = $this->encryptionCarNumByCarNum($r->data['car_tail_number']);
            }

            //优惠券信息
            if (!empty($r->data['coupon_record_id'])) {
                $couponRecordFields = "name,type,value";
                $couponRecordR = $this->find(array('coupon_record_id' => $r->data['coupon_record_id']), 'CouponRecord', $couponRecordFields);
                $r->data['coupon_info'] = $couponRecordR->ret ? $couponRecordR->data : "";
            } else {
                $r->data['coupon_record_id'] = null;
            }

            $r->data['refund_ticket_conf'] = $this->getRefundConf();
        }
        return $this->output($r);
    }


    /**
     * 查询摆渡车订单
     * @param array $orderNos 订单号列表
     * @return mixed
     */
    private function queryFerryOrder($orderNos)
    {
        $where['order_no'] = array('in', $orderNos);
        $r = $this->select($where, 1, 10, null, 'Order', 'order_no,ferry_type,start_address_remark,end_address_remark,start_longitude,start_latitude,end_longitude,end_latitude,order_id,price,real_price');
        return $r->data;
    }

    /**
     * 获取历史订单
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrdersHistory($page = 1, $size = 10, $fields = null)
    {
        $order = 'order_id desc';

        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " deleted_at IS NULL ";
        $where['passenger_id'] = $this->state->user_id;
        $where['ferry_type'] = \CommonDefine::FERRY_TYPE_0;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,order_id,state,type,price,book_seating,line_id,start_time,branchid,driver_id,driver_isover,create_time,reseverd_phone,reseverd_person,reseverd_info,start_longitude,start_latitude,start_address_code,start_address_remark,end_longitude,end_latitude,end_address_remark,is_temp,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,ft_number,pay_mode,real_price,is_buy_insurance,insurance_price,is_pay');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];

                //获取线路信息
                if ($r->data[$key]['type'] == 1) {
                    $line = $this->find(array('id' => $r->data[$key]['line_id']), 'Line', 'id,start_name,end_name');
                    $r->data[$key]['start_name'] = $line->data['start_name'];
                    $r->data[$key]['end_name'] = $line->data['end_name'];
                } elseif ($r->data[$key]['type'] == 2) {
                    if ($r->data[$key]['is_custom'] == 0) {
                        $lineChartered = $this->find(array('id' => $r->data[$key]['line_id']), 'LineChartered', 'id,start_name,end_name');

                        $r->data[$key]['start_name'] = $lineChartered->data['start_name'];
                        $r->data[$key]['end_name'] = $lineChartered->data['end_name'];
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                        $viaPointsR = $this->find(array('line_class_id' => $lineClassTrainR->data['line_class_id']), 'DingzhikeyunLineViaPoints');
                        if ($viaPointsR->ret) {
                            $r->data[$key]['via_province_code'] = $viaPointsR->data['province_code'];
                            $r->data[$key]['via_province_name'] = $viaPointsR->data['province_name'];
                            $r->data[$key]['via_city_code'] = $viaPointsR->data['city_code'];
                            $r->data[$key]['via_city_name'] = $viaPointsR->data['city_name'];
                            $r->data[$key]['via_area_code'] = $viaPointsR->data['area_code'];
                            $r->data[$key]['via_area_name'] = $viaPointsR->data['area_name'];
                            $r->data[$key]['via_name'] = $viaPointsR->data['name'];
                        }

                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                        $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                        unset($r->data[$key]['start_region_name']);
                        unset($r->data[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                } elseif ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_7) {
                    $line = $this->find(array('id' => $r->data[$key]['line_id']), 'LineFast', 'id,start_name,end_name');
                    $r->data[$key]['start_name'] = $line->data['start_name'];
                    $r->data[$key]['end_name'] = $line->data['end_name'];
                } elseif ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_11) {
                    $line = $this->find(array('id' => $r->data[$key]['line_id']), 'LineTaxi', 'id,start_name,end_name');
                    $r->data[$key]['start_name'] = $line->data['start_name'];
                    $r->data[$key]['end_name'] = $line->data['end_name'];
                } else {
                    $r->data[$key]['start_name'] = $r->data[$key]['start_address_remark'];
                    $r->data[$key]['end_name'] = $r->data[$key]['end_address_remark'];
                }

                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }
                //预约中（即司机接单中）
                if ($value['state'] == 6) {
                    $r->data[$key]['message'] = "已完成";
                } elseif ($value['state'] == 7) {
                    $r->data[$key]['message'] = "已取消";
                } elseif ($value['state'] == 8) {
                    $r->data[$key]['message'] = "已关闭";
                }
                //预约中（即司机接单中）
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                }
            }
        }
        return $this->output($r);
    }


    /**
     * 乘客订单列表查询
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function queryPassengerOrders($type = 0, $start_area_code = '', $end_area_code = '', $start_time = '', $create_time = '', $order_no = '', $passenger_phone = '', $passenger_name = '', $driver_card_no = '', $driver_phone = '', $driver_name = '', $order_method = '', $proxy_id = '', $sort_by = '', $sort = '', $per_page = '', $page = '', $status = '')
    {
        $ret = new \ResultModel(false);
        if (empty($this->state->user_id)) {
            $ret->data = '乘客信息错误，请求失败';
        }
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];

        $params = array(
            'start_area_code' => $start_area_code,
            'end_area_code' => $end_area_code,
            'start_time[]' => $start_time,
            'create_time[]' => $create_time,
            'order_no' => $order_no,
            'passenger_phone' => $passenger_phone,
            'passenger_name' => $passenger_name,
            'driver_card_no' => $driver_card_no,
            'driver_phone' => $driver_phone,
            'driver_name' => $driver_name,
            'order_method' => $order_method,
            'proxy_id' => $proxy_id,
            'sort_by' => $sort_by,
            'sort' => $sort,
            'per_page' => $per_page,
            'page' => $page,
            'status' => $status
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/types/{$type}/orders", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('查询失败：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
     * 商户入口城市是否开通
     *
     * @param string $filter
     * @param string $start_address_code
     * @param string $start_name
     * @param string $end_address_code
     * @param string $end_name
     * @return void
     */
    public function queryEndpointCityEnabled($type = 0, $city_name = '')
    {
        $ret = new \ResultModel(false);
        if (empty($this->mchid)) {
            $ret->data = '乘客信息错误，请求失败';
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'city_name' => urlencode($city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/types/{$type}/endpoint_city/enabled", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('开通失败：%s', $results['message']);
        }
        return $this->output($ret);
    }

    /**
     * 获取订单
     * @param int $order_id 订单ID
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrder($order_id, $fields = null)
    {
        $r = $this->getEntityById($order_id, 'Order', $fields, null, 'drive_mode,start_address_code,end_address_code,address_code,driver_id');
        if ($r->ret) {
            if ($this->isIncludeRootField($fields, 'start_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['start_address_code']);
                if ($t->ret) {
                    $r->data['start_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'end_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['end_address_code']);
                if ($t->ret) {
                    $r->data['end_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'address_code') && intval($r->data['drive_mode']) === 2) {
                $t = $this->resolutionAddress($r->data['address_code']);
                if ($t->ret) {
                    $r->data['address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            $this->sudoLoadSubEntity($r, 'driver_id', 'driver_id', 'Driver\\Account', 'getDriver', $fields, 'driver');
        }
        return $this->output($r);
    }

    /**
     * 取消预订
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    public function doCancelBook($order_id, $reason = null)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2,3,4');
        $seOrderWhere['type'] = array('in', \CommonDefine::ORDER_TYPE_1.','.\CommonDefine::ORDER_TYPE_2.','.\CommonDefine::ORDER_TYPE_3.','.\CommonDefine::ORDER_TYPE_4.','.\CommonDefine::ORDER_TYPE_7.','.\CommonDefine::ORDER_TYPE_11);
        $this->startTrans();
        $orderR = $this->find($seOrderWhere, 'Order');
        if ($orderR->ret) {
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_4 && $orderR->data['type'] != \CommonDefine::ORDER_TYPE_7) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '已经上车，无法取消！'));
            }
            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7 && $orderR->data['ferry_type'] != \CommonDefine::FERRY_TYPE_0) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败，摆渡车订单无法单独取消！'));
            }

            $order_state = intval($orderR->data['state']);
            /* if ($order_state == 2 ){
                 return $this->output(new \ResultModel(false, '司机已接单不能取消'));
             }*/ if ($order_state == 3) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '进行中的订单不能取消'));
            } else {
                if (!empty($orderR->data['coupon_record_id'])) {
                    //更新优惠券的状态
                    $updateCouponRecordR = $this->updateCouponRecord($orderR->data['coupon_record_id'], \CommonDefine::COUPON_STATUS_0);
                    if (!$updateCouponRecordR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '取消失败，请稍后再试'));
                    }
                }

                //已支付退款
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1 && $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    $wxPayUtil = new \WxPayUtil();
                    if (!$wxPayUtil->init($orderR->data['mchid'])) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户支付异常，申请退款失败!'));
                    }
                    $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                    if (!$refundR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }

                    if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                    }

                    $refundData['amount'] = $orderR->data['price'];
                    $refundData['status'] = 3;
                    $refundData['order_id'] = $order_id;
                    $refundData['account_type'] = 1;//乘客
                    $refundData['account_id'] = $orderR->data['passenger_id'];
                    $refundData['refund_id'] = $refundR->data['refund_id'];
                    //   $refundData['object'] = $refundR->data['object'];
                    $refundData['refund_no'] = $refundR->data['out_trade_no'];
                    $refundData['created'] = time();
                    //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                    /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                        $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                    $refundData['description'] = "取消订单，全额退款";
                    $refundData['charge'] = $refundR->data['charge'];
                    $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                    $refundData['transaction_no'] = $refundR->data['transaction_id'];
                    $reR = $this->add('Refunds', $refundData);
                    if (!$reR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }
                }

                $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                if ($orderS->ret) {
                    //添加预订订单历史状态
                    $orderHistoryStateR = $this->addBookOrderHistoryState($order_id, 'cancel book');
                    if ($orderHistoryStateR->ret) {
                        //更新司机座位数
                        if ($order_state == 2) {
                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                            if ($driverR->ret) {
                                if ($orderR->data['seat_is_add'] == 1) {
                                    $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                                    $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'],'residual_seating' => $residual_seating));
                                    if ($driverS->ret) {
                                        $this->commitTrans();
                                        if (C('SEAT_LOG_ON')) {
                                            $this->recordSeatLog($orderR->data['order_id'], $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doCancelBook', '乘客取消订单');
                                        }
                                    } else {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                                    }
                                } else {
                                    $this->commitTrans();
                                }

                                //通知乘客
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_3);

                                //通知司机
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                            }
                        } else {
                            $this->commitTrans();
                            //取消订单
                            if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                            } else {//派单
                                //通知乘客
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_3);

                                //通知分台
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_3);
                            }
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                }
            }
            unset($orderR->data['branchid']);
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
        }
        return $this->output($orderR);
    }

    /**
     * 取消班线订单
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    protected function doCancelBookLineClassTrainFerry($order_id, $reason = null)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2,3');
        $seOrderWhere['type'] = \CommonDefine::ORDER_TYPE_5;
        $this->startTrans();
        $orderR = $this->find($seOrderWhere, 'Order');
        if ($orderR->ret) {
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            //            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
            if (empty($lineClassTrainArr)) {
                $this->transRollback();
                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
            }

            $lineClassR = $this->find(array('id' =>  $lineClassTrainArr['line_class_id']), 'LineClass');
            if (!$lineClassR->ret) {
                $this->transRollback();
                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
            }

            //是否已过退票时间
            $refundTime = 0;
            if (!is_null($lineClassR->data['refund_time_set'])) {
                $refundTime = $lineClassR->data['refund_time_set'] >= 0 ? $lineClassR->data['refund_time_set'] : 0;
            }

            $refundDateTime = date('Y-m-d H:i:s', strtotime($orderR->data['start_time']." -$refundTime minute"));
            if (strtotime("now") > strtotime($refundDateTime)) {
                $this->transRollback();
                return new \ResultModel(false, '退票失败，已过允许退票时间');
            }

            //班线车订单模式，默认自动派单
            $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
            $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
            if ($lineClassAppointConfigR->ret) {
                $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
            }

            $order_state = intval($orderR->data['state']);
            if ($order_state == 3) {
                $this->transRollback();
                return new \ResultModel(false, '进行中的订单不能取消');
            } else {
                if (!empty($orderR->data['coupon_record_id'])) {
                    //更新优惠券的状态
                    $updateCouponRecordR = $this->updateCouponRecord($orderR->data['coupon_record_id'], \CommonDefine::COUPON_STATUS_0);
                    if (!$updateCouponRecordR->ret) {
                        $this->transRollback();
                        return new \ResultModel(false, '取消失败，请稍后再试');
                    }
                }

                //退保险
                if ($orderR->data['is_buy_insurance'] == \CommonDefine::IS_BUY_INSURANCE_1) {
                    $cancelOrderInsurancesR = $this->cancelOrderInsurances($orderR->data['order_id']);
                    if (!$cancelOrderInsurancesR->ret) {
                        $this->transRollback();
                        return  new \ResultModel(false, '退保险失败，请联系管理员!');
                    }
                }

                if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    $isAllowRefundTicket = $this->checkeIsAllowRefundTicket($orderR->data['order_id']);
                    if (!$isAllowRefundTicket->ret) {
                        $this->transRollback();
                        return new \ResultModel(false, '已过退票时间');
                    }

                    //添加预订订单历史状态
                    $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                    if ($bookHistoryS->ret) {
                        $upLineClassTrainData = [];
                        $wxPayUtil = new \WxPayUtil();
                        if (!$wxPayUtil->init($orderR->data['mchid'])) {
                            $this->transRollback();
                            return new \ResultModel(false, '商户支付异常，申请退款失败!');
                        }

                        //退票扣款计算
                        $ticketPrice = $orderR->data['real_price'];
                        //扣除优惠券减免的费用
                        if ($orderR->data['coupon_price'] > 0) {
                            $ticketPrice -= $orderR->data['coupon_price'];
                        }
                        $refundFeeR = $this->calculateRefundTicketFee($orderR->data['start_time'], $ticketPrice);
                        if (!$refundFeeR->ret) {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!');
                        }

                        $orderUpdateData['refund_ticket_ratio'] = $refundFeeR->data['refund_ticket_ratio'];
                        $orderUpdateData['refund_fee'] = $refundFeeR->data['refund_fee'];
                        $orderUpdateData['refund_amount'] = $refundFeeR->data['refund_amount'];
                        $orderUpdateData['refund_insurance_amount'] = $orderR->data['insurance_price'];
                        $orderUpdateData['refund_total_amount'] = $refundFeeR->data['refund_amount'] + $orderR->data['insurance_price'];
                        $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['real_price'], $orderUpdateData['refund_total_amount']);
                        if (!$refundR->ret) {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!');
                        }

                        if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!'.$orderR->data['order_no'].$refundR->data['err_code_des']);
                        }

                        $refundData['amount'] = $orderUpdateData['refund_total_amount'];
                        $refundData['status'] = 3;
                        $refundData['order_id'] = $order_id;
                        $refundData['account_type'] = 1;//乘客
                        $refundData['account_id'] = $orderR->data['passenger_id'];
                        $refundData['refund_id'] = $refundR->data['refund_id'];
                        //   $refundData['object'] = $refundR->data['object'];
                        $refundData['refund_no'] = $refundR->data['out_trade_no'];
                        $refundData['created'] = time();
                        //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                        /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                            $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                        $refundData['description'] = "取消订单，全额退款";
                        $refundData['charge'] = $refundR->data['charge'];
                        $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                        $refundData['transaction_no'] = $refundR->data['transaction_id'];

                        $reR = $this->add('Refunds', $refundData);
                        if ($reR->ret) {
                            $orderUpdateData['order_id'] = $orderR->data['order_id'];
                            $orderUpdateData['state'] = \CommonDefine::ORDER_STATE_7;
                            $orderS = $this->save('Order', $orderUpdateData);
                            if ($orderS->ret) {
                                //取消摆渡车（接、送）
                                $cancelFerryOrderSs = $this->doCancelBookLineFastInnerFunction($orderR->data['order_id']);
                                if (!$cancelFerryOrderSs->ret) {
                                    $this->transRollback();
                                    return new \ResultModel(false, '取消摆渡车订单失败');
                                }

                                //统计平台退费收益
                                if ($orderUpdateData['refund_fee'] > 0) {
                                    $bookkeepingR = $this->bookkeepingMchOnBalanceByRefund($this->mchid, $orderR->data['order_id']);
                                    if (!$bookkeepingR->ret) {
                                        $this->transRollback();
                                        return new \ResultModel(false, '申请退款记账失败，请联系管理员!');
                                    }
                                }

                                $this->commitTrans();

                                //日志
                                $this->doAddLog("[行号:".__LINE__."]取消班线订单[订单ID:".$order_id."]", "原数据：".json_encode($orderR->data).";更新内容:".json_encode($orderUpdateData), \StateModel::$PASSENGER);
                                $this->doAddLog("[行号:".__LINE__."]取消班线订单[订单ID:".$order_id."]", "原数据：".json_encode($lineClassTrainArr).";更新内容:".json_encode($upLineClassTrainData), \StateModel::$PASSENGER);

                                //通知司机
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                                //乘客端删除定时推送消息
                                $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);
                                return new \ResultModel(true, '退款中，退款将于3个工作日内到账!');
                            }
                        }
                    } else {
                        $this->transRollback();
                        return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                    }
                } elseif ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_0) {
                    if ($orderR->data['state'] == 2 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $refundTicketR = $this->refundTicket($orderR);
                        if ($refundTicketR->ret) {
                            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                            if ($orderS->ret) {
                                //添加预订订单历史状态
                                $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                                if ($bookHistoryS->ret) {
                                    $this->commitTrans();
                                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {//班线车自动派单
                                        //通知司机
                                        $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                                    } else {
                                        //通知分台
                                    }
                                    //乘客端删除定时推送消息
                                    $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);

                                    return new \ResultModel(true, '订单取消成功');
                                } else {
                                    $this->transRollback();
                                    return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                                }
                            } else {
                                $this->transRollback();
                                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                            }
                        } else {
                            $this->transRollback();
                            return new \ResultModel(false, '订单取消失败');
                        }
                    } else {
                        $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                        if ($orderS->ret) {
                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                //取消摆渡车（接、送）
                                $cancelFerryOrderSs = $this->doCancelBookLineFastInnerFunction($orderR->data['order_id']);
                                if (!$cancelFerryOrderSs->ret) {
                                    $this->transRollback();
                                    return new \ResultModel(false, '取消摆渡车订单失败');
                                }

                                $this->commitTrans();
                                //乘客端删除定时推送消息
                                $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);
                                return new \ResultModel(true, '订单取消成功');
                            } else {
                                $this->transRollback();
                                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                            }
                        } else {
                            $this->transRollback();
                            return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                        }
                    }
                }
            }
            unset($orderR->data['branchid']);
        } else {
            $this->transRollback();
            return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
        }
    }

    /**
     * 取消班线订单
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    protected function doCancelBookLineClassTrainNormal($order_id, $reason = null)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2,3');
        $seOrderWhere['type'] = \CommonDefine::ORDER_TYPE_5;
        $this->startTrans();
        $orderR = $this->find($seOrderWhere, 'Order');
        if ($orderR->ret) {
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            //            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
            if (empty($lineClassTrainArr)) {
                $this->transRollback();
                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
            }

            $lineClassR = $this->find(array('id' => $lineClassTrainArr['line_class_id']), 'LineClass');
            if (!$lineClassR->ret) {
                $this->transRollback();
                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
            }

            //是否已过退票时间
            $refundTime = 0;
            if (!is_null($lineClassR->data['refund_time_set'])) {
                $refundTime = $lineClassR->data['refund_time_set'] >= 0 ? $lineClassR->data['refund_time_set'] : 0;
            }

            $refundDateTime = date('Y-m-d H:i:s', strtotime($orderR->data['start_time']." -$refundTime minute"));
            if (strtotime("now") > strtotime($refundDateTime)) {
                $this->transRollback();
                return new \ResultModel(false, '退票失败，已过允许退票时间');
            }

            //班线车订单模式，默认自动派单
            $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;
            $lineClassAppointConfigR = $this->find(array('key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
            if ($lineClassAppointConfigR->ret) {
                $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
            }

            $order_state = intval($orderR->data['state']);
            if ($order_state == 3) {
                $this->transRollback();
                return new \ResultModel(false, '进行中的订单不能取消');
            } else {
                if (!empty($orderR->data['coupon_record_id'])) {
                    //更新优惠券的状态
                    $updateCouponRecordR = $this->updateCouponRecord($orderR->data['coupon_record_id'], \CommonDefine::COUPON_STATUS_0);
                    if (!$updateCouponRecordR->ret) {
                        $this->transRollback();
                        return new \ResultModel(false, '取消失败，请稍后再试');
                    }
                }

                //退保险
                if ($orderR->data['is_buy_insurance'] == \CommonDefine::IS_BUY_INSURANCE_1) {
                    $cancelOrderInsurancesR = $this->cancelOrderInsurances($orderR->data['order_id']);
                    if (!$cancelOrderInsurancesR->ret) {
                        $this->transRollback();
                        return  new \ResultModel(false, '退保险失败，请联系管理员!');
                    }
                }

                if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    $isAllowRefundTicket = $this->checkeIsAllowRefundTicket($orderR->data['order_id']);
                    if (!$isAllowRefundTicket->ret) {
                        $this->transRollback();
                        return new \ResultModel(false, '已过退票时间');
                    }

                    //添加预订订单历史状态
                    $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                    if ($bookHistoryS->ret) {
                        $upLineClassTrainData = [];
                        $wxPayUtil = new \WxPayUtil();
                        if (!$wxPayUtil->init($orderR->data['mchid'])) {
                            $this->transRollback();
                            return new \ResultModel(false, '商户支付异常，申请退款失败!');
                        }

                        //退票扣款计算
                        $ticketPrice = $orderR->data['real_price'];
                        if ($orderR->data['is_buy_insurance'] == \CommonDefine::IS_BUY_INSURANCE_1) {
                            $ticketPrice = $orderR->data['merge_price'];//兼容以前没买保险时的情况
                        }
                        //扣除优惠券减免的费用
                        if ($orderR->data['coupon_price'] > 0) {
                            $ticketPrice -= $orderR->data['coupon_price'];
                        }
                        $refundFeeR = $this->calculateRefundTicketFee($orderR->data['start_time'], $ticketPrice);
                        if (!$refundFeeR->ret) {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!');
                        }

                        $orderUpdateData['refund_ticket_ratio'] = $refundFeeR->data['refund_ticket_ratio'];
                        $orderUpdateData['refund_fee'] = $refundFeeR->data['refund_fee'];
                        $orderUpdateData['refund_amount'] = $refundFeeR->data['refund_amount'];
                        $orderUpdateData['refund_insurance_amount'] = $orderR->data['insurance_price'];
                        $orderUpdateData['refund_total_amount'] = $refundFeeR->data['refund_amount'] + $orderR->data['insurance_price'];
                        $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['real_price'], $orderUpdateData['refund_total_amount']);
                        if (!$refundR->ret) {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!');
                        }

                        if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                            $this->transRollback();
                            return new \ResultModel(false, '申请退款失败，请联系管理员!'.$orderR->data['order_no'].$refundR->data['err_code_des']);
                        }

                        $refundData['amount'] = $orderUpdateData['refund_total_amount'];
                        $refundData['status'] = 3;
                        $refundData['order_id'] = $order_id;
                        $refundData['account_type'] = 1;//乘客
                        $refundData['account_id'] = $orderR->data['passenger_id'];
                        $refundData['refund_id'] = $refundR->data['refund_id'];
                        //   $refundData['object'] = $refundR->data['object'];
                        $refundData['refund_no'] = $refundR->data['out_trade_no'];
                        $refundData['created'] = time();
                        //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                        /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                            $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                        $refundData['description'] = "取消订单，全额退款";
                        $refundData['charge'] = $refundR->data['charge'];
                        $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                        $refundData['transaction_no'] = $refundR->data['transaction_id'];

                        $reR = $this->add('Refunds', $refundData);
                        if ($reR->ret) {
                            $orderUpdateData['order_id'] = $orderR->data['order_id'];
                            $orderUpdateData['state'] = \CommonDefine::ORDER_STATE_7;
                            $orderS = $this->save('Order', $orderUpdateData);
                            if ($orderS->ret) {
                                //统计平台退费收益
                                if ($orderUpdateData['refund_fee'] > 0) {
                                    $bookkeepingR = $this->bookkeepingMchOnBalanceByRefund($this->mchid, $orderR->data['order_id']);
                                    if (!$bookkeepingR->ret) {
                                        $this->transRollback();
                                        return new \ResultModel(false, '申请退款记账失败，请联系管理员!');
                                    }
                                }

                                $this->commitTrans();

                                //日志
                                $this->doAddLog("[行号:".__LINE__."]取消班线订单[订单ID:".$order_id."]", "原数据：".json_encode($orderR->data).";更新内容:".json_encode($orderUpdateData), \StateModel::$PASSENGER);
                                $this->doAddLog("[行号:".__LINE__."]取消班线订单[订单ID:".$order_id."]", "原数据：".json_encode($lineClassTrainArr).";更新内容:".json_encode($upLineClassTrainData), \StateModel::$PASSENGER);

                                //通知司机
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                                //乘客端删除定时推送消息
                                $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);
                                return new \ResultModel(true, '退款中，退款将于3个工作日内到账!');
                            }
                        }
                    } else {
                        $this->transRollback();
                        return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                    }
                } elseif ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_0) {
                    if ($orderR->data['state'] == 2 && $orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $refundTicketR = $this->refundTicket($orderR);
                        if ($refundTicketR->ret) {
                            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                            if ($orderS->ret) {
                                //添加预订订单历史状态
                                $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                                if ($bookHistoryS->ret) {
                                    $this->commitTrans();
                                    if ($lineClassAppointConfig == \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0) {//班线车自动派单
                                        //通知司机
                                        $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                                    } else {
                                        //通知分台
                                    }
                                    //乘客端删除定时推送消息
                                    $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);

                                    return new \ResultModel(true, '订单取消成功');
                                } else {
                                    $this->transRollback();
                                    return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                                }
                            } else {
                                $this->transRollback();
                                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                            }
                        } else {
                            $this->transRollback();
                            return new \ResultModel(false, '订单取消失败');
                        }
                    } else {
                        $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                        if ($orderS->ret) {
                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                $this->commitTrans();
                                //乘客端删除定时推送消息
                                $this->delPassengerNoticeQueue(1, $orderR->data['order_id']);
                                return new \ResultModel(true, '订单取消成功');
                            } else {
                                $this->transRollback();
                                return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                            }
                        } else {
                            $this->transRollback();
                            return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
                        }
                    }
                }
            }
            unset($orderR->data['branchid']);
        } else {
            $this->transRollback();
            return new \ResultModel(false, '订单是ID无效或没有权限取消预订');
        }
    }

    /**
     * 取消班线订单
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    public function doCancelBookLineClassTrain($order_id, $reason = null)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['mchid'] = $this->mchid;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2');
        $seOrderWhere['type'] = \CommonDefine::ORDER_TYPE_5;
        $orderR = $this->find($seOrderWhere, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '抱歉，此订单无法取消，请联系平台管理员'));
        }
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/refunds", 'post', array('reason' => $reason));
        $result = json_decode($response, true);
        if ($result['code'] != 200) {
            return $this->output(new \ResultModel(false, $result['message']));
        }
        return $this->output(new \ResultModel(true, '订单取消成功'));

        // if ($this->getOrderClass($orderR->data['order_no']) == \CommonDefine::ORDER_CLASS_5) {
        //     $cancelOrderS = $this->doCancelBookLineClassTrainFerry($order_id, $reason);
        //     //→ 乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票（如果长时间不支持，5分钟自动释放车票），如果乘客取消订单，车票立即释放
        //     if ($cancelOrderS->ret) {
        //         httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/issued/tickets", 'delete');
        //     }
        //     return $this->output($cancelOrderS);
        // } else {
        //     $cancelOrderS = $this->doCancelBookLineClassTrainNormal($order_id, $reason);
        //     //→ 乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票（如果长时间不支持，5分钟自动释放车票），如果乘客取消订单，车票立即释放
        //     if ($cancelOrderS->ret) {
        //         httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$order_id}/issued/tickets", 'delete');
        //     }
        //     return $this->output($cancelOrderS);
        // }
        // return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
    }


    /**
     * 取消顺风车订单
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    public function doCancelBookLineFreeRide($order_id, $reason = null)
    {
        $seOrderWhere['order_id'] = $order_id;
        $seOrderWhere['passenger_id'] = $this->state->user_id;
        $seOrderWhere['state'] = array('in', '1,2,3');
        $seOrderWhere['type'] = \CommonDefine::ORDER_TYPE_6;
        $this->startTrans();
        $orderR = $this->find($seOrderWhere, 'Order');
        if ($orderR->ret) {
            $order_state = intval($orderR->data['state']);
            /* if ($order_state == 2 ){
                 return $this->output(new \ResultModel(false, '司机已接单不能取消'));
             }*/ if ($order_state == 3) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '进行中的订单不能取消'));
            } else {
                //已支付退款
                if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    //总台余额查询
                    $mchR = $this->find(array('admin_id' => $this->mchid, 'is_del' => 0,'group_id' => 2), 'Admin', 'admin_id,balance');
                    if (!$mchR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户异常，请联系管理员!'));
                    }

                    //更新顺风车剩余座位数
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6 && !empty($orderR->data['line_id'])) {
                        $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                        if (!$LineFreeRideR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                        }

                        $LineFreeRideUpdateData['id'] = $LineFreeRideR->data['id'];
                        $LineFreeRideUpdateData['residual_seating'] = $LineFreeRideR->data['residual_seating'] + $orderR->data['book_seating'];
                        $LineFreeRideS = $this->save('LineFreeRide', $LineFreeRideUpdateData);
                        if (!$LineFreeRideS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                        }
                    }

                    $wxPayUtil = new \WxPayUtil();
                    if (!$wxPayUtil->init($orderR->data['mchid'])) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户支付异常，申请退款失败!'));
                    }
                    $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                    if (!$refundR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }

                    if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                    }

                    $refundData['amount'] = $orderR->data['price'];
                    $refundData['status'] = 3;
                    $refundData['order_id'] = $order_id;
                    $refundData['account_type'] = 1;//乘客
                    $refundData['account_id'] = $orderR->data['passenger_id'];
                    $refundData['refund_id'] = $refundR->data['refund_id'];
                    //   $refundData['object'] = $refundR->data['object'];
                    $refundData['refund_no'] = $refundR->data['out_trade_no'];
                    $refundData['created'] = time();
                    //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                    /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                        $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                    $refundData['description'] = "取消订单，全额退款";
                    $refundData['charge'] = $refundR->data['charge'];
                    $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                    $refundData['transaction_no'] = $refundR->data['transaction_id'];
                    $reR = $this->add('Refunds', $refundData);
                    if (!$reR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }
                }
                if (!empty($orderR->data['coupon_record_id'])) {
                    //更新优惠券的状态
                    $updateCouponRecordR = $this->updateCouponRecord($orderR->data['coupon_record_id'], \CommonDefine::COUPON_STATUS_0);
                    if (!$updateCouponRecordR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '取消失败，请稍后再试'));
                    }
                }

                $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                if ($orderS->ret) {
                    //添加预订订单历史状态
                    $orderHistoryStateR = $this->addBookOrderHistoryState($order_id, 'cancel book');
                    if ($orderHistoryStateR->ret) {
                        //更新司机座位数
                        if ($order_state == 2) {
                            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                            if ($driverR->ret) {
                                //更新顺风车剩余座位数
                                if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6 && !empty($orderR->data['line_id']) && $orderR->data['is_pay'] != \CommonDefine::PAY_STATUS_1) {
                                    $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                                    if (!$LineFreeRideR->ret) {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                                    }
                                    $LineFreeRideUpdateData['id'] = $LineFreeRideR->data['id'];
                                    $LineFreeRideUpdateData['residual_seating'] = $LineFreeRideR->data['residual_seating'] + $orderR->data['book_seating'];
                                    $LineFreeRideS = $this->save('LineFreeRide', $LineFreeRideUpdateData);
                                    if (!$LineFreeRideS->ret) {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                                    }
                                }

                                if ($orderR->data['seat_is_add'] == 1) {
                                    $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                                    $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'],'residual_seating' => $residual_seating));
                                    if ($driverS->ret) {
                                        $this->commitTrans();
                                        if (C('SEAT_LOG_ON')) {
                                            $this->recordSeatLog($orderR->data['order_id'], $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doCancelBook', '乘客取消订单');
                                        }
                                    } else {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                                    }
                                } else {
                                    $this->commitTrans();
                                }

                                if (C('SMS_ON')) {
                                    $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
                                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                    $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array($orderR->data['order_no'], $passengerR->data['cellphone']), \SMSUtil::TEMP_ID_PSCANCEL, $driverR->data['mchid']);
                                }

                                //通知司机
                                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_1);
                            }
                        } else {
                            $this->commitTrans();
                            //取消订单

                            //通知
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                }
            }
            unset($orderR->data['branchid']);
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
        }
        return $this->output($orderR);
    }

    /**
     * 取消预订(手机端)
     * @param int $order_id 订单ID
     * @param int $reason 取消预订原因（默认为空，在车主接受前取消预订|非空表示在车主接受后取消预订，1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误）
     */
    public function doMobileCancelBook($order_id, $passenger_id, $reason = null)
    {
        $r = $this->find(array('order_id' => $order_id, 'passenger_id' => $passenger_id, 'type' => 1, 'state' => array('in', '1,2,3,8')), 'Order', 'order_id,driver_id,state,order_no,book_seating,create_time');
        $driverR = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver', 'driver_id,price,openid,cellphone,virtual,mchid');
        if ($r->ret) {
            $order_state = intval($r->data['state']);
            /*if ($order_state == 2) {
                return $this->output(new \ResultModel(false, '车主已接受您的订单'));
            } else */if ($order_state == 8) {
                return $this->output(new \ResultModel(false, '车主已拒绝您的订单'));
            } else {
                $r = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
                if ($r->ret) {
                    $this->commitTrans();
                    $order = $this->find(array('order_id' => $order_id), 'Order', 'order_no,create_time');
                    //已派单时推送短信和微信
                    if ($driverR->data['virtual'] == 0) {
                        //                        $wechat = new \Wechat();
                        //                        $wechatRet = $wechat->doPostToDriverOrderMessage($driverR->data['openid'], 2, $order->data['order_no'], "乘客取消预约");
                        $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                        $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array($r->data['order_no'], '028-62711128'), \SMSUtil::TEMP_ID_PSCANCEL, $driverR->data['mchid']);
                        //
                        //                        {{first.DATA}}
                        //                        订单编号：{{OrderSn.DATA}}
                        //                        订单状态：{{OrderStatus.DATA}}
                        //                        {{remark.DATA}}
                        //                        1-司机拒载，2-服务态度不好，3-司机没有来，4-有事耽误
                        ///test
                        if (C('WX_TEMPLATE_ON')) {
                            if ($reason == 1) {
                                $msg = "司机拒载";
                            } elseif ($reason == 2) {
                                $msg = "服务态度不好";
                            } elseif ($reason == 3) {
                                $msg = "司机没有来";
                            } elseif ($reason == 4) {
                                $msg = "有事耽误";
                            } else {
                                $msg = "乘客已取消";
                            }

                            $passengerR = $this->find(array('passenger_id' => $passenger_id,'Passenger','mchid'));
                            $temp = array( '订单取消', $this->data['order_no'],$msg);
                            $this->wechatPushInfo($passengerR->data['mchid'], $driverR->data['driver_id'], 1, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_5);
                            unset($temp);
                        }
                    }
                } else {
                    $this->transRollback();
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限取消预订'));
        }
        return $this->output($r);
    }

    /**
     * 下车
     * @param int $order_id 订单ID
     * @param boolean $is_in_start 是否在出发地（0-没有(默认），1-在）
     */
    public function doGetOff($order_id, $is_in_start = 0)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '3,4'), 'passenger_id' => $this->state->user_id), 'Order', 'driver_id,order_id,state,type,book_seating');
        if ($r->ret) {
            $driver_id = $r->data['driver_id'];
            $type = $r->data['type'];
            $book_seating = intval($r->data['book_seating']);
            if (intval($is_in_start) === 1) {
                //出发点下车
                $this->startTrans();
                if (intval($type) === 1) {
                    //取消预订
                    $o = $this->sudo('Passenger\\Order', $this->state->user_id, \StateModel::$PASSENGER);
                    $r = $o->doCancelBook($order_id, 4);
                } else {
                    //取消邀请
                    $o = $this->sudo('Driver\\Order', $driver_id, \StateModel::$DRIVER);
                    $r = $o->doCancelInvite($order_id);
                }
                if ($r->ret) {
                    //更新乘客状态，由“已上车”变为“下车”
                    $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'state');
                    if ($r->ret) {
                        $passenger_state = intval($r->data['state']);
                        if ($passenger_state === 2) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客已经下车'));
                        } else {
                            $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'state' => 2));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                }
            }
            if (intval($r->data['state']) === 3) {
                $state = 5; //乘客在车主清空位置前下车
                $is_allow_passenger_evaluate = 0;
            } else {
                $state = 6;
                $is_allow_passenger_evaluate = 1;
            }
            $this->startTrans();
            $r = $this->save('Order', array('order_id' => $order_id, 'state' => $state, 'is_allow_passenger_evaluate' => $is_allow_passenger_evaluate, 'is_allow_driver_evaluate' => 1));
            if ($r->ret) {
                //更新乘客状态，由“已上车”变为“下车”，信用度加1
                $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'state,credit');
                if ($r->ret) {
                    $passenger_state = intval($r->data['state']);
                    if ($passenger_state === 2) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '乘客已经下车'));
                    } else {
                        $r = $this->save('Passenger', array('passenger_id' => $this->state->user_id, 'state' => 2, 'credit' => (intval($r->data['credit']) + 1)));
                        if (!$r->ret) {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    }
                } else {
                    $this->transRollback();
                    return $this->output($r);
                }
                if ($state === 5) {
                    //提醒车主
                    $r = $this->assignRemindTask($this->state->user_id, $driver_id, 'get off', $order_id);
                    if ($r->ret) {
                        if (intval($type) === 1) {
                            //添加预订订单历史状态
                            $r = $this->addBookOrderHistoryState($order_id, 'got off');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            //添加邀请订单历史状态
                            $r = $this->addInviteOrderHistoryState($order_id, 'got off');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    if (intval($type) === 1) {
                        //添加预订订单历史状态
                        $r = $this->addBookOrderHistoryState($order_id, 'success');
                        if ($r->ret) {
                            $r = $this->deductInsurance($order_id);
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'success');
                        if ($r->ret) {
                            $r = $this->deductInsurance($order_id);
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        if ($r->ret) {
            return $this->output(new \ResultModel(true, array('is_allow_evaluate' => $is_allow_passenger_evaluate)));
        }
        return $this->output($r);
    }

    /**
     * 下车(手机端)
     * @param int $order_id 订单ID
     * @param boolean $is_in_start 是否在出发地（0-没有(默认），1-在）
     */
    public function doMobileGetOff($order_id)
    {
        $r = $this->find(array('order_id' => $order_id), 'Order', 'state,passenger_isover,driver_isover,driver_id');
        $this->startTrans();
        if ($r->ret) {
            $driverM = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver', 'state');
            if ($driverM->data['state'] != 2) {
                return $this->output(new \ResultModel(false, "司机尚未点击发车，请提示司机"));
            }
            if ($r->data['state'] == 2) {
                //进行中订单
                if ($r->data['driver_isover'] == 1) {
                    //司机已确认送达
                    $r = $this->save('Order', array('order_id' => $order_id, 'passenger_isover' => 1, 'state' => 6));
                    if ($r->ret) {
                        $this->commitTrans();
                        return $this->output(new \ResultModel(true, '确认成功'));
                    } else {
                        $this->transRollback();
                    }
                } else {
                    //司机还未确认送达
                    $r = $this->save('Order', array('order_id' => $order_id, 'passenger_isover' => 1));
                    if ($r->ret) {
                        $this->commitTrans();
                        return $this->output(new \ResultModel(true, '确认成功'));
                    } else {
                        $this->transRollback();
                    }
                }
            } elseif ($r->data['state'] == 6) {
                //已完成的订单，异常状态处理
                $r = $this->save('Order', array('order_id' => $order_id, 'passenger_isover' => 1));
                if ($r->ret) {
                    $this->commitTrans();
                    return $this->output(new \ResultModel(true, '确认成功'));
                } else {
                    $this->transRollback();
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }

        return $this->output($r);
    }

    /**
     * 处理车主邀请
     * @param int $order_id 订单ID
     * @param boolean $is_agree 是否同意（0-拒绝，1-接受）
     * @param int $reason 乘客拒绝邀请原因（默认为空，当拒绝时有效，1-时间仓促，2-做朋友的车，3-有事耽误，4-其他原因）
     */
    public function doAnswerInviteOrder($order_id, $is_agree, $reason = null)
    {
        if (intval($is_agree) === 1) {
            $r = $this->count(array('passenger_id' => $this->state->user_id, 'state' => array('lt', 5), 'order_id' => array('neq', $order_id)), 'Order');
            if ($r->ret) {
                if ($r->data > 0) {
                    return $this->output(new \ResultModel(false, '请先取消之前的预订或者拒绝之前的邀请'));
                }
            }
        }
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '1,2'), 'type' => 2, 'passenger_id' => $this->state->user_id), 'Order', 'driver_id,order_id,book_seating,state');
        if ($r->ret) {
            $driver_id = $r->data['driver_id'];
            $book_seating = intval($r->data['book_seating']);
            $pre_state = intval($r->data['state']);
            if (intval($is_agree) === 1) {
                $state = 2;
            } else {
                $state = 8;
            }
            $data = array('order_id' => $order_id, 'state' => $state);
            if ($reason) {
                $data['refuse_invite_reason'] = intval($reason);
            }
            $this->startTrans();
            $r = $this->save('Order', $data);
            if ($r->ret) {
                if ($state === 2) {
                    //车主剩余座位数减预订座位数
                    $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'residual_seating');
                    if ($r->ret) {
                        $residual_seating = intval($r->data['residual_seating']);
                        if ($residual_seating - $book_seating >= 0) {
                            $residual_seating = $residual_seating - $book_seating;
                            $r = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客太多了，坐不下了'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    $r = $this->assignRemindTask($this->state->user_id, $driver_id, 'accept invite', $order_id);
                    if ($r->ret) {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'accepted');
                        if ($r->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    if ($pre_state === 2) { //乘客同意后，再次拒绝邀请
                        //更新车主剩余座位数
                        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'residual_seating,total_seating');
                        if ($r->ret) {
                            $residual_seating = intval($r->data['residual_seating']);
                            $total_seating = intval($r->data['total_seating']);
                            if ($total_seating > $residual_seating) {
                                $residual_seating += $book_seating;
                                $data = array('driver_id' => $driver_id, 'residual_seating' => $residual_seating);
                                $r = $this->save('Driver', $data);
                                if (!$r->ret) {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    }
                    $r = $this->assignRemindTask($this->state->user_id, $driver_id, 'refuse invite', $order_id);
                    if ($r->ret) {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'refuse invite');
                        if ($r->ret) {
                            if ($reason) {
                                //添加评价
                                switch (intval($reason)) {
                                    case 1:
                                        $reason = '时间仓促';
                                        break;
                                    case 2:
                                        $reason = '做朋友的车';
                                        break;
                                    case 3:
                                        $reason = '有事耽误';
                                        break;
                                    case 4:
                                        $reason = '其他原因';
                                        break;
                                }
                                $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'user_name');
                                if ($r->ret) {
                                    $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 1, 'state' => 2, 'content' => $reason));
                                    if (!$r->ret) {
                                        $this->transRollback();
                                        return $this->output($r);
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true));
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        return $this->output($r);
    }

    /**
     * 处理车主邀请(手机端)
     * @param int $order_id 订单ID
     * @param boolean $is_agree 是否同意（0-拒绝，1-接受）
     * @param int $reason 乘客拒绝邀请原因（默认为空，当拒绝时有效，1-时间仓促，2-做朋友的车，3-有事耽误，4-其他原因）
     */
    public function doMobileAnswerInviteOrder($passenger_id, $order_id, $is_agree, $reason = null)
    {
        if (intval($is_agree) === 1) {
            $r = $this->count(array('passenger_id' => $passenger_id, 'state' => array('lt', 5), 'order_id' => array('neq', $order_id)), 'Order');
            if ($r->ret) {
                if ($r->data > 0) {
                    return $this->output(new \ResultModel(false, '请先取消之前的预订或者拒绝之前的邀请'));
                }
            }
        }
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '1,2'), 'type' => 2, 'passenger_id' => $passenger_id), 'Order', 'driver_id,order_id,book_seating,state');
        if ($r->ret) {
            $driver_id = $r->data['driver_id'];
            $book_seating = intval($r->data['book_seating']);
            $pre_state = intval($r->data['state']);
            if (intval($is_agree) === 1) {
                $state = 2;
            } else {
                $state = 8;
            }
            $data = array('order_id' => $order_id, 'state' => $state);
            if ($reason) {
                $data['refuse_invite_reason'] = intval($reason);
            }
            $this->startTrans();
            $r = $this->save('Order', $data);
            if ($r->ret) {
                if ($state === 2) {
                    //车主剩余座位数减预订座位数
                    $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'residual_seating');
                    if ($r->ret) {
                        $residual_seating = intval($r->data['residual_seating']);
                        if ($residual_seating - $book_seating >= 0) {
                            $residual_seating = $residual_seating - $book_seating;
                            $r = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客太多了，坐不下了'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    $r = $this->assignRemindTask($passenger_id, $driver_id, 'accept invite', $order_id);
                    if ($r->ret) {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'accepted');
                        if ($r->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    if ($pre_state === 2) { //乘客同意后，再次拒绝邀请
                        //更新车主剩余座位数
                        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'residual_seating,total_seating');
                        if ($r->ret) {
                            $residual_seating = intval($r->data['residual_seating']);
                            $total_seating = intval($r->data['total_seating']);
                            if ($total_seating > $residual_seating) {
                                $residual_seating += $book_seating;
                                $data = array('driver_id' => $driver_id, 'residual_seating' => $residual_seating);
                                $r = $this->save('Driver', $data);
                                if (!$r->ret) {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    }
                    $r = $this->assignRemindTask($passenger_id, $driver_id, 'refuse invite', $order_id);
                    if ($r->ret) {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'refuse invite');
                        if ($r->ret) {
                            if ($reason) {
                                //添加评价
                                switch (intval($reason)) {
                                    case 1:
                                        $reason = '时间仓促';
                                        break;
                                    case 2:
                                        $reason = '做朋友的车';
                                        break;
                                    case 3:
                                        $reason = '有事耽误';
                                        break;
                                    case 4:
                                        $reason = '其他原因';
                                        break;
                                }
                                $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'user_name');
                                if ($r->ret) {
                                    $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 1, 'state' => 2, 'content' => $reason));
                                    if (!$r->ret) {
                                        $this->transRollback();
                                        return $this->output($r);
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true));
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        return $this->output($r);
    }

    /**
     * 评价
     * @param int $order_id 订单ID
     * @param boolean $is_speeding 是否超速 （0-否，1-是）
     * @param boolean $is_overload 是否超载 （0-否，1-是）
     * @param int $service_attitude 服务态度（1-差评，2-中评，3-好评）
     */
    public function doEvaluate($order_id, $is_speeding, $is_overload, $service_attitude)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '5,6'), 'passenger_id' => $this->state->user_id), 'Order', 'order_id,driver_id');
        if ($r->ret) {
            $driver_id = intval($r->data['driver_id']);
            $this->startTrans();
            $r = $this->save('Order', array('order_id' => $order_id, 'is_speeding' => intval($is_speeding), 'is_overload' => intval($is_overload), 'service_attitude' => intval($service_attitude), 'is_allow_passenger_evaluate' => 0));
            if ($r->ret) {
                $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'credit,praise,bad,general');
                if ($r->ret) {
                    $credit = intval($r->data['credit']);
                    $praise = intval($r->data['praise']);
                    $bad = intval($r->data['bad']);
                    $general = intval($r->data['general']);
                    if (intval($is_speeding) === 0) {
                        $is_speeding = '超速 否';
                    } else {
                        $is_speeding = '超速 是';
                        $credit = $credit - 2;
                    }
                    if (intval($is_overload) === 0) {
                        $is_overload = '超载 否';
                    } else {
                        $is_overload = '超载 是';
                        $credit = $credit - 2;
                    }
                    if (intval($service_attitude) === 1) {
                        $service_attitude = '服务态度 差';
                        $credit--;
                        $bad++;
                    } elseif (intval($service_attitude) === 2) {
                        $service_attitude = '服务态度 一般';
                        $general++;
                    } else {
                        $service_attitude = '服务态度 好';
                        $credit++;
                        $praise++;
                    }
                    $r = $this->save('Driver', array('driver_id' => $driver_id, 'credit' => $credit, 'praise' => $praise, 'bad' => $bad, 'general' => $general));
                    if ($r->ret) {
                        $content = "$is_speeding,$is_overload,$service_attitude";
                        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'user_name');
                        if ($r->ret) {
                            $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 1, 'state' => 5, 'content' => $content));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限评价该订单'));
        }
        return $this->output($r);
    }

    /**
     * 评价(手机端)
     * @param int $order_id 订单ID
     * @param boolean $is_speeding 是否超速 （0-否，1-是）
     * @param boolean $is_overload 是否超载 （0-否，1-是）
     * @param int $service_attitude 服务态度（1-差评，2-中评，3-好评）
     */
    public function doMobileEvaluate($passenger_id, $order_id, $is_speeding, $is_overload, $service_attitude)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '5,6'), 'passenger_id' => $passenger_id), 'Order', 'order_id,driver_id');
        if ($r->ret) {
            $driver_id = intval($r->data['driver_id']);
            $this->startTrans();
            $r = $this->save('Order', array('order_id' => $order_id, 'is_speeding' => intval($is_speeding), 'is_overload' => intval($is_overload), 'service_attitude' => intval($service_attitude), 'is_allow_passenger_evaluate' => 0));
            if ($r->ret) {
                $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'credit,praise,bad,general');
                if ($r->ret) {
                    $credit = intval($r->data['credit']);
                    $praise = intval($r->data['praise']);
                    $bad = intval($r->data['bad']);
                    $general = intval($r->data['general']);
                    if (intval($is_speeding) === 0) {
                        $is_speeding = '超速 否';
                    } else {
                        $is_speeding = '超速 是';
                        $credit = $credit - 2;
                    }
                    if (intval($is_overload) === 0) {
                        $is_overload = '超载 否';
                    } else {
                        $is_overload = '超载 是';
                        $credit = $credit - 2;
                    }
                    if (intval($service_attitude) === 1) {
                        $service_attitude = '服务态度 差';
                        $credit--;
                        $bad++;
                    } elseif (intval($service_attitude) === 2) {
                        $service_attitude = '服务态度 一般';
                        $general++;
                    } else {
                        $service_attitude = '服务态度 好';
                        $credit++;
                        $praise++;
                    }
                    $r = $this->save('Driver', array('driver_id' => $driver_id, 'credit' => $credit, 'praise' => $praise, 'bad' => $bad, 'general' => $general));
                    if ($r->ret) {
                        $content = "$is_speeding,$is_overload,$service_attitude";
                        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'user_name');
                        if ($r->ret) {
                            $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 1, 'state' => 5, 'content' => $content));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限评价该订单'));
        }
        return $this->output($r);
    }

    /**
     * 获取正在进行的订单或历史订单(手机端)getMobileOrders
     * @param int $type 类型（1-进行中，2-历史记录,3-筛选历史订单，4-获取所有订单）
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getMobileOrders($passenger_id, $type, $page = 1, $size = 10, $begin_time = null, $fields = null)
    {
        $type = intval($type);
        if ($type === 1) {
            $where['state'] = array('in', '1,2');
            $order = 'create_time desc';
            $where['passenger_id'] = $passenger_id;
        } elseif ($type === 2) {
            //            $where['state'] = array('in', '6,8');//乘客获取被拒或者正常完成订单
            $where['state'] = array('in', '6');//乘客获取正常完成订单
            $where['passenger_id'] = $passenger_id;
            $order = 'update_time desc';
        } elseif ($type === 3) {
            $where['state'] = array('in', '6,8');
            $order = 'update_time desc';
            $where['passenger_id'] = $passenger_id;
            $start = date('Y-m-d 00:00:00', strtotime($begin_time));
            $end = date('Y-m-d 23:59:59', strtotime($begin_time));
            $where['start_time'] = array('between', array($start, $end));
        } elseif ($type === 4) {
            $order = 'update_time desc';
            $where['passenger_id'] = $passenger_id;
        } else {
            return $this->output(new \ResultModel(false, '类型格式不正确'));
        }
        $where['mchid'] = cookie('mchid');

        $r = $this->select($where, $page, $size, $order, 'Order', 'order_id,driver_id');
        $retArr = array();
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $data = $this->find(array('driver_id' => $r->data[$k]['driver_id'], 'mchid' => cookie('mchid')), 'Driver', 'driver_id,start_address_code,end_address_code,name,cellphone,start_time,price');
                $start = $this->checkingGdAddressCode($data->data['start_address_code']);
                $end = $this->checkingAddressCode($data->data['end_address_code']);
                $orderdata = $this->find(array('order_id' => $r->data[$k]['order_id'], 'mchid' => cookie('mchid')), 'Order', 'order_id,passenger_isover,state,book_seating,start_time');
                if ($orderdata->data['passenger_isover'] == 1) {
                    $orderdata->data['passenger_isover'] = false;
                } else {
                    $orderdata->data['passenger_isover'] = true;
                }

                if ($orderdata->data['state'] == 1) {
                    $orderdata->data['state'] = true;
                } else {
                    $orderdata->data['state'] = false;
                }

                $orderdata->data['cellphone'] = $data->data['cellphone'];
                $orderdata->data['route'] = $start->data['address'] . '-' . $end->data['address'];
                $orderdata->data['price'] = $data->data['price'];
                $orderdata->data['name'] = $data->data['name'];
                //                $orderdata->data['start_time'] = $data->data['start_time'];
                $retArr[] = $orderdata->data;
            }
        } else {
            return $this->output(new \ResultModel(false, '未查找到相关信息'));
        }

        return $this->output(new \ResultModel(true, $retArr));
    }

    /**
     * 获取订单(手机端)
     * @param int $order_id 订单ID
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getMobileOrder($order_id, $fields = null)
    {
        $r = $this->getEntityById($order_id, 'Order', $fields, null, 'drive_mode,start_address_code,end_address_code,address_code,driver_id');
        if ($r->ret) {
            if ($this->isIncludeRootField($fields, 'start_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['start_address_code']);
                if ($t->ret) {
                    $r->data['start_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'end_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['end_address_code']);
                if ($t->ret) {
                    $r->data['end_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'address_code') && intval($r->data['drive_mode']) === 2) {
                $t = $this->resolutionAddress($r->data['address_code']);
                if ($t->ret) {
                    $r->data['address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            $this->sudoLoadSubEntity($r, 'driver_id', 'driver_id', 'Driver\\Account', 'getDriver', $fields, 'driver');
        }
        return $this->output($r);
    }


    /**
     * 根据地址信息自动匹配拼车线路
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    private function getAutoLines($start_address_code = null, $end_address_code = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $this->mchid;
        $where['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_0;
        $where['is_del'] = 0;
        $where['status'] = 1;

        if ($start_address_code) {
            $where['start_address_code'] = $start_address_code;
        } else {
            return new \ResultModel(false, '定位失败，请手动选择位置');
        }

        if ($end_address_code) {
            $where['end_address_code'] = $end_address_code;
        } else {
            return new \ResultModel(false, '请选择目的地');
        }

        //具体的code查询
        $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary');
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
            }
        } else {
            //判断目的地是否是区
            if ($this->getCodeProtity($end_address_code) == 3) {//目的地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($end_address_code);
                if ($cityInnerCode) {
                    $where['end_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary');
                    if ($r->ret) {
                        foreach ($r->data as $k => $v) {
                            $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                            $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                        }
                    } else {
                        if ($this->getCodeProtity($start_address_code) == 3) {//开始地城市详细区域，匹配市辖区
                            $startCityInnerCode = $this->getCityInnerCode($start_address_code);
                            if ($startCityInnerCode) {
                                $where['start_address_code'] = $startCityInnerCode;
                                $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary');
                                if ($r->ret) {
                                    foreach ($r->data as $k => $v) {
                                        $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                        $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                        $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                    }
                                }
                            }
                        }
                    }
                }
            } elseif ($this->getCodeProtity($start_address_code) == 3) {//开始地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($start_address_code);
                if ($cityInnerCode) {
                    $where['start_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary');
                    if ($r->ret) {
                        foreach ($r->data as $k => $v) {
                            $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                            $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                        }
                    } else {
                        $endCityInnerCode = $this->getCityInnerCode($end_address_code);
                        if ($endCityInnerCode) {
                            $where['end_address_code'] = $endCityInnerCode;
                            $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary');
                            if ($r->ret) {
                                foreach ($r->data as $k => $v) {
                                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                    $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                    $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                }
                            }
                        }
                    }
                }
            } else {
                return new \ResultModel(false, '即将开通该线路，敬请期待');
            }
        }

        if (!$r->ret) {
            $r->data =  '即将开通该线路，敬请期待';
        }

        return $r;
    }

    /**
     * 获取退款配置
     *
     * @return mixed
     */
    public function getRefundConf()
    {
        $selWhere['mchid'] = $this->mchid;
        return M('RefundTicketConfig')->field('refund_ticket_time,refund_ticket_ratio')->where($selWhere)->order('id ASC')->select();
    }
}

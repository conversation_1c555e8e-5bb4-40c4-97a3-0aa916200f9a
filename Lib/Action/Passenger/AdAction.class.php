<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 广告模块
 *
 * <AUTHOR>
 */
class AdAction extends \PassengerCoreAction
{
    /**
     * 获取乘客端广告
     * @param int $cate_id 广告分类id（1-乘客端全部；2-乘客端拼车页；3-乘客端包车页；4-乘客端班车页；5-顺风车页；6-乘客端带货页；7-乘客端代办页；8-乘客端个人中心页；9-乘客端注册页）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为5）
     */
    public function getAds($cate_id = 1, $page = 1, $size = 5)
    {
        $ret = new \ResultModel(false, '暂无广告');
        $where['ad.mchid'] = $this->mchid;
        $where['adc.cate_id'] = $cate_id;
        $where['ad.start_time'] = array('elt', time());
        $where['ad.end_time'] = array('egt', time());
        $where['ad.state'] = \CommonDefine::AD_STATE_1;

        $fields = 'ad.id,adc.ad_id,ad.title,ad.describe,ad.imgsrc_url,ad.link,ad.limit_biz';
        $adList = M()->table('cp_mch_ad ad')
            ->join('LEFT JOIN cp_mch_ad_cate adc ON ad.id = adc.ad_id ')
            ->where($where)
            ->page($page, $size)
            ->field($fields)
            ->select();

        $adCount = M()->table('cp_mch_ad ad')
            ->join('LEFT JOIN cp_mch_ad_cate adc ON ad.id = adc.ad_id ')
            ->where($where)
            ->count();

        if ($adCount) {
            $ret->ret = true;
            $ret->data = $adList;
            $ret->count = $adCount;
        }

        return $this->output($ret);
    }


    /**
    * 定制客运主页联盟广告
    *
    * @return \ResultModel
    */
    public function getCustomizedPassengerAd()
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/platforms/1/clients/1/placements/customized-transport/ad_contents", 'get', array(), $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
        }
        return $this->output($ret);
    }
}

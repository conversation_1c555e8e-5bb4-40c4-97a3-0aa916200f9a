<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 优惠券模块
 *
 * <AUTHOR>
 */
class CouponAction extends \PassengerCoreAction
{
    /**
     * 可领取优惠券列表
     */
    public function getCoupons($page = 1, $size = 1, $type = -1)
    {
        $couponRecordsR = new \ResultModel(false);
        //获取可以参与的营销活动列表
        $marketActivityWhere = "give_out_type = ".\CommonDefine::GIVE_OUT_TYYE_1;
        $marketActivityWhere  .= " AND is_del = 0";
        $marketActivityWhere  .= " AND end_time >= '".date('Y-m-d H:i:s', time())."'";
        $marketActivityWhere  .= " AND total_count > recieve_count";
        $marketActivityWhere  .= " AND rule > 0";
        $marketActivityWhere  .= " AND mchid = ".$this->mchid;

        if ($type == -1) {
            $marketActivityWhere  .= " AND type IN (0, -1)";
        } else {
            $marketActivityWhere  .= " AND type = " . $type;
        }

        $marketActivityList = M()->table("cp_market_activity")
            ->where($marketActivityWhere)
            ->field("market_activity_id,rule,type")
            ->select();
        if (empty($marketActivityList)) {
            return $this->output($couponRecordsR);
        }

        $market_activity_Arr = [];
        $market_activity_ids_Arr = [];
        $market_activity_ids_where = '';
        foreach ($marketActivityList as $k => $v) {
            $market_activity_ids_Arr[] = $v['market_activity_id'];
            $market_activity_Arr[$v['market_activity_id']]['rule'] = $v['rule'];
            $market_activity_Arr[$v['market_activity_id']]['remain_receive'] = $v['rule'];
            $market_activity_Arr[$v['market_activity_id']]['type'] = $v['type'];
            if ($market_activity_ids_where) {
                $market_activity_ids_where .= ' OR cr.market_activity_id = '.$v['market_activity_id'];
            } else {
                $market_activity_ids_where = ' cr.market_activity_id = '.$v['market_activity_id'];
            }
        }

        if (!empty($this->state->user_id)) {
            //获取用户已经领取的营销活动统计数据
            $receiveStaticsWhere = " ma.is_del = 0";
            $receiveStaticsWhere .= " AND cr.passenger_id = ".$this->state->user_id;
            if ($market_activity_ids_where) {
                $receiveStaticsWhere .= (" AND (".$market_activity_ids_where.")");
            }
            $couponsReceiveStaticsList = M()->table("cp_coupon_record as cr")
                ->join("LEFT JOIN cp_market_activity as ma ON ma.market_activity_id = cr.market_activity_id")
                ->where($receiveStaticsWhere)
                ->field("cr.market_activity_id,count('cr.market_activity_id') as market_activity_count")
                ->group("cr.market_activity_id")
                ->select();

            if (!empty($couponsReceiveStaticsList)) {
                foreach ($couponsReceiveStaticsList as $k => $v) {

                    //用户首单已领取，不再继续弹框
                    if ($market_activity_Arr[$v['market_activity_id']]['type'] == 5) {
                        if ($v['market_activity_count'] > 0) {
                            $market_activity_Arr = [];
                            break;
                        }
                    } else {
                        if (intval($market_activity_Arr[$v['market_activity_id']]['rule']) <= $v['market_activity_count']) {
                            unset($market_activity_Arr[$v['market_activity_id']]);
                        } else {
                            $market_activity_Arr[$v['market_activity_id']]['remain_receive'] -= $v['market_activity_count'];
                        }
                    }
                }
            }
        }

        if (empty($market_activity_Arr)) {
            return $this->output($couponRecordsR);
        }

        $where = " cr.is_del = 0";
        $where .= " AND cr.end_time >= '".date('Y-m-d H:i:s', time())."'";
        $where .= " AND cr.passenger_id is null";
        $where .= " AND cr.mchid = ".$this->mchid;
        $where .= " AND cr.status = ".\CommonDefine::COUPON_STATUS_0;

        $fields = "cr.coupon_record_id";
        $fields .= ",cr.name";
        $fields .= ",cr.coupon_id";
        $fields .= ",cr.market_activity_id";
        $fields .= ",cr.status";
        $fields .= ",cr.type";
        $fields .= ",cr.value";
        $fields .= ",cr.start_time";
        $fields .= ",cr.end_time";
        $fields .= ",ma.claim_reward_way";

        $couponRecordsList = [];
        foreach ($market_activity_Arr as $kk => $vv) {
            $pageSize = $size > $vv['remain_receive'] ? $vv['remain_receive'] : $size;
            $tempwhere = $where.' AND cr.market_activity_id = '.$kk;
            $order = "cr.coupon_record_id asc";
            $tempCouponRecordsList = M()->table("cp_coupon_record as cr")
                ->join("LEFT JOIN cp_market_activity as ma ON ma.market_activity_id = cr.market_activity_id")
                ->where($tempwhere)
                ->page(1, $pageSize)
                ->field($fields)
                ->order($order)
                ->select();

            if (!empty($tempCouponRecordsList)) {
                $couponRecordsList = empty($couponRecordsList) ? $tempCouponRecordsList : array_merge_recursive($couponRecordsList, $tempCouponRecordsList);
            } else {
                continue;
            }

            if ($size == $pageSize) {
                break;
            }
        }

        if (!empty($couponRecordsList)) {
            $couponRecordsR->ret = true;
            $couponRecordsR->data = $couponRecordsList;
            $couponRecordsR->count = count($couponRecordsList);
        }
        return $this->output($couponRecordsR);
    }


    /**
     * 领取优惠券
     * @param string $coupon_record_ids  要领取的优惠券的集合
     * @return null|\resultModel
     */
    public function doReceiveCoupon($coupon_record_ids = null)
    {
        if (empty($coupon_record_ids)) {
            return $this->output(new \ResultModel(false, '参数异常'));
        }
        $coupon_record_ids = trim($coupon_record_ids);
        $where = "coupon_record_id in (".$coupon_record_ids.")";
        $where .= " AND is_del = ".\CommonDefine::IS_DEL_0;
        $where .= " AND passenger_id is null";
        $couponRecordsR = $this->select($where, null, null, null, 'CouponRecord');
        $marketActivityMode = new \MarketActivityModel();
        if ($couponRecordsR->ret) {
            $recieveCount = 0;
            $updateCouponRecordData['passenger_id'] = $this->state->user_id;
            $updateCouponRecordData['receive_time'] = date('Y-m-d H:i:s', time());
            foreach ($couponRecordsR->data as $k => $couponRecord) {
                $boolean = $this->isReceivedRepeat($couponRecord['market_activity_id']);
                if ($boolean) {
                    return $this->output(new \ResultModel(false, '您已领取过该优惠券！'));
                }
                $updateCouponRecordData['coupon_record_id'] = $couponRecord['coupon_record_id'];
                $couponRecordsS = $this->save('CouponRecord', $updateCouponRecordData);
                if ($couponRecordsS->ret) {
                    $recieveCount += 1;
                }
            }
            $marketActivityMode->where('market_activity_id = '.$couponRecord['market_activity_id'])->setInc('recieve_count', $recieveCount);
            return $this->output(new \ResultModel(true, '领取成功'));
        }
        return $this->output(new \ResultModel(false, '无可用优惠券领取'));
    }


    protected function isReceivedRepeat($marketActivityId)
    {
        $marketActivity = M("MarketActivity"); // 实例化MarketActivity对象
        $result = $marketActivity->where('market_activity_id=' . $marketActivityId)->find();

        $where['passenger_id'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['market_activity_id'] = $marketActivityId;
        $couponRecordsCount = $this->count($where, 'CouponRecord');
        return $couponRecordsCount->data >= $result['rule'];
    }

    /**
     * 我的优惠券列表
     * @param int $page
     * @param int $size
     */
    public function getPassengerCoupons($page = 1, $size = 5)
    {
        $where['passenger_id'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['end_time'] = ['egt', date("Y-m-d H:i:s", time())];
        $where['status'] = \CommonDefine::COUPON_STATUS_0;
        $fields = "coupon_record_id";
        $fields .= ",name";
        $fields .= ",coupon_id";
        $fields .= ",market_activity_id";
        $fields .= ",status";
        $fields .= ",type";
        $fields .= ",value";
        $fields .= ",start_time";
        $fields .= ",end_time";
        $fields .= ",receive_time";
        $couponRecordsR = $this->select($where, $page, $size, 'coupon_record_id desc', 'CouponRecord', $fields);

        //剩余天数
        if ($couponRecordsR->ret) {
            foreach ($couponRecordsR->data as $k => &$couponRecord) {
                if ($couponRecord['status'] == \CommonDefine::COUPON_STATUS_0) {
                    $couponRecord['start_time'] = !empty($couponRecord['start_time']) ? date('Y-m-d H:i', strtotime($couponRecord['start_time'])) : "";
                    $couponRecord['end_time'] = !empty($couponRecord['end_time']) ? date('Y-m-d H:i', strtotime($couponRecord['end_time'])) : "";
                }
            }
        }
        return $this->output($couponRecordsR);
    }

    /**
     * 用户获取可以使用优惠券的数量
     */
    public function getPassengerAbleCouponsTotalCount()
    {
        $where['passenger_id'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['end_time'] = ['egt', date("Y-m-d H:i:s", time())];
        $where['status'] = \CommonDefine::COUPON_STATUS_0;
        $couponRecordsCountR = $this->count($where, 'CouponRecord');
        return $this->output($couponRecordsCountR);
    }

    /**
     * 获取下单可使用优惠券列表
     * @param float $price 价格
     * @param int $page
     * @param int $size
     */
    public function getPassengerAbleCoupons($price, $type = 0, $page = 1, $size = 10)
    {
        $couponRecordsR = new \ResultModel(false);
        $where = " cr.is_del = 0";
        $where .= " AND c.is_del = 0";
        $where .= " AND cr.end_time >= '".date('Y-m-d H:i:s', time())."'";
        $where .= " AND cr.passenger_id = ".$this->state->user_id;
        $where .= " AND cr.mchid = ".$this->mchid;
        $where .= " AND cr.status = ".\CommonDefine::COUPON_STATUS_0;
        $where .= " AND c.rule <= ".$price;
        $where .= " AND ((cr.type = ".\CommonDefine::COUPON_TYYE_1." AND cr.value <= ".$price.") OR (cr.type = ".\CommonDefine::COUPON_TYYE_2."))";
        if ($type) {
            $where .= sprintf(" AND FIND_IN_SET(%s, c.scope)", $type);
        }
        $fields = "cr.coupon_record_id";
        $fields .= ",cr.name";
        $fields .= ",cr.coupon_id";
        $fields .= ",cr.market_activity_id";
        $fields .= ",cr.status";
        $fields .= ",cr.type";
        $fields .= ",cr.value";
        $fields .= ",cr.start_time";
        $fields .= ",cr.end_time";
        $fields .= ",ma.claim_reward_way";
        $fields .= ",c.rule";
        $order = "cr.coupon_record_id desc";

         $couponRecordsCount = M()->table("cp_coupon_record as cr")
             ->join("LEFT JOIN cp_market_activity as ma ON ma.market_activity_id = cr.market_activity_id")
             ->join("LEFT JOIN cp_coupon as c ON c.coupon_id = cr.coupon_id")
             ->where($where)
             ->field($fields)
             ->order($order)
             ->count();

        if (!empty($couponRecordsCount)) {
            $couponRecordsList = M()->table("cp_coupon_record as cr")
                ->join("LEFT JOIN cp_market_activity as ma ON ma.market_activity_id = cr.market_activity_id")
                ->join("LEFT JOIN cp_coupon as c ON c.coupon_id = cr.coupon_id")
                ->where($where)
                ->page($page, $size)
                ->field($fields)
                ->order($order)
                ->select();
            $couponRecordsR->ret = true;
            $couponRecordsR->data = $couponRecordsList;
            $couponRecordsR->count = $couponRecordsCount;
        }

        return $this->output($couponRecordsR);
    }

    /**
     * 获取优惠券使用说明
     * @param $coupon_id
     * @return null|\resultModel
     */
    public function getCouponInstructions($coupon_id)
    {
        $couponR = $this->find(['coupon_id' => $coupon_id], 'Coupon', 'summary');
        return $this->output($couponR);
    }
}

<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 支持的地址模块
 *
 * <AUTHOR>
 */
class AddressCodeAction extends \PassengerCoreAction
{
    /**
     * 获取商户支持的地址
     * @param int $biz 商户编号
     * @param int $startFilter 起始过滤
     * @return array
     */
    public function getAddressCodes($biz = 0, $start_filter = 1)
    {
        $filter = array();
        $mchAddressCodeWhere = "mchid = ".$this->mchid;
        $mchAddressCodeR = $this->find($mchAddressCodeWhere, 'AddressCode');
        if(!$mchAddressCodeR->ret) {
            return $this->output($mchAddressCodeR);
        } else {
            switch ($biz) {
                case \CommonDefine::ORDER_TYPE_1:
                    $filter = $this->filterPinche();
                    break;
                case \CommonDefine::ORDER_TYPE_2:
                    $filter = $this->filterBaoche();
                    break;
                case \CommonDefine::ORDER_TYPE_5:
                    $filter = $this->filterBanxian();
                    break;
                    // 快车
                case \CommonDefine::ORDER_TYPE_7:
                    if ($start_filter == 1) {
                        $filter = $this->filterKuaiche();
                    }
                    break;
                case \CommonDefine::ORDER_TYPE_11:
                    // 出租车
                    if ($start_filter == 1) {
                        $filter = $this->filterChuzuche();
                    }
                    break;
            }
            $cityArr = json_decode($mchAddressCodeR->data['address_code'], true);
            if ($filter) {
                foreach ($cityArr as $key => $element) {
                    if (!in_array(intval($element['address_id']), $filter)) {
                        unset($cityArr[$key]);
                    }
                }
                return $this->output(new \ResultModel('true', $cityArr, count($cityArr)));
            } else {
                return $this->output(new \ResultModel('true', $filter, count($filter)));
            }

        }
    }

    public function filterPinche()
    {
        $filters = [];
        $results = M('Line')->where([
            'mchid' => $this->mchid,
            'is_phone_line' => \CommonDefine::LINE_PHONE_TYYE_0,
        ])
        ->field('start_city_code,end_city_code')
        ->select();
        if ($results) {
            foreach ($results as $element) {
                $filters[] = $element['start_city_code'];
                $filters[] = $element['end_city_code'];
            }
        }
        return $filters;
    }

    public function filterBaoche()
    {
        $filters = [];
        $results = M('LineChartered')->where([
            'mchid' => $this->mchid,
            'is_del' => 0,
            'status' => 1,
        ])
        ->field('start_city_code,end_city_code')
        ->select();
        if ($results) {
            foreach ($results as $element) {
                $filters[] = $element['start_city_code'];
                $filters[] = $element['end_city_code'];
            }
        }
        return $filters;
    }

    public function filterBanxian()
    {
        $filters = [];
        $results = M('LineClass')->where([
            'mchid' => $this->mchid,
            'is_del' => 0,
            'status' => 1,
        ])
        ->field('start_city_code,end_city_code')
        ->select();
        if ($results) {
            foreach ($results as $element) {
                $filters[] = $element['start_city_code'];
                $filters[] = $element['end_city_code'];
            }
        }
        return $filters;
    }


    public function filterKuaiche()
    {
        $filters = [];
        $results = M('LineFast')->where([
            'mchid' => $this->mchid,
            'is_del' => 0,
            'status' => 1,
        ])
        ->field('start_city_code,end_city_code')
        ->select();
        if ($results) {
            foreach ($results as $element) {
                $filters[] = $element['start_city_code'];
                $filters[] = $element['end_city_code'];
            }
        }
        return $filters;
    }

    public function filterChuzuche()
    {
        $filters = [];
        $results = M('LineTaxi')->where([
            'mchid' => $this->mchid,
            'is_del' => 0,
            'status' => 1,
        ])
        ->field('start_city_code,end_city_code')
        ->select();
        if ($results) {
            foreach ($results as $element) {
                $filters[] = $element['start_city_code'];
                $filters[] = $element['end_city_code'];
            }
        }
        return $filters;
    }



}

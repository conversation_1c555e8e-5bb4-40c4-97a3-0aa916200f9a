<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');

/**
 * 附近模块
 *
 * <AUTHOR>
 */
class AroundAction extends PassengerCoreAction
{

    /**
     * 获取附件的司机数量
     * @param int $start_address_code 当前定位的位置的区域编码
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     */
    public function getAroundDrivers($start_address_code = null, $longitude = null, $latitude = null, $limit = 60000){
        try{
            if(empty($start_address_code)){
                return $this->output(new ResultModel(true, 0));
            }

            if(empty($longitude) || empty($latitude)){
                return $this->output(new ResultModel(true, 0));
            }
            return $this->output(new ResultModel(true, rand(0,4)));
        }catch (Exception $e){
            return $this->output(new ResultModel(true, 0));
        }
    }

    /**
     * 获取附近的车主
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     * @param string fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAroundDriver($longitude = null, $latitude = null, $limit = 10000, $fields = null, $page = 1, $size = 10)
    {

        $where = array();
        $r = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger', 'longitude,latitude,drive_mode,start_address_code,end_address_code,address_code,start_time');
        if ($r->ret) {
            if (empty($longitude) || empty($latitude)) {
                $latitude = $r->data['latitude'];
                $longitude = $r->data['longitude'];
                if (empty($longitude) || empty($latitude)) {
                    return $this->output(new ResultModel(false, '不能定位你的位置'));
                }
            }
            $drive_mode = intval($r->data['drive_mode']); //根据乘客的乘车路线过滤（市相同）
            if ($drive_mode === 1) {
                $start_address_code = $r->data['start_address_code'];
                $end_address_code = $r->data['end_address_code'];
                $where['drive_mode'] = $drive_mode;
                $r = $this->resolutionAddress($start_address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['start_address_code'] = $start_address_code;
                    } else {
                        $where['start_address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
                $r = $this->resolutionAddress($end_address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['end_address_code'] = $end_address_code;
                    } else {
                        $where['end_address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
            } else if ($drive_mode === 2) {
                $address_code = $r->data['address_code'];
                $where['drive_mode'] = $drive_mode;
                $r = $this->resolutionAddress($address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['address_code'] = $address_code;
                    } else {
                        $where['address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
            } else {
                return $this->output(new ResultModel(false, '没发布过乘车路线'));
            }
        } else {
            return $this->output(new ResultModel(false, '乘客不存在'));
        }
        $where['start_time'] = array('gt', get_current_time());   //出发时间大于当前时间
        $where['residual_seating'] = array('gt', 0);  //座位数大于零
        $where['state'] = 1; //车主状态为等候
        $driverModel = new DriverModel();
        $field = "driver_id,GETDISTANCE(latitude,longitude,$latitude,$longitude) AS distance";
        $data = $driverModel->where($where)->field($field)->having("distance < $limit")->order('distance asc')->page($page, $size)->select();
        if ($data) {
            $data1 = $driverModel->where($where)->field($field)->having("distance < $limit")->select();
            $count = count($data1);
            foreach ($data as $key => $value) {
                $o = $this->sudo('Driver\\Account', $value['driver_id'], StateModel::$DRIVER);
                $r = $o->getDriver($value['driver_id'], $fields);
                if ($r->ret) {
                    $data[$key] = $r->data;
                    $data[$key]['distance'] = $value['distance'];
                } else {
                    $data[$key] = $value;
                    $data[$key]['error'] = $r->data;
                }
            }
            return $this->output(new ResultModel(true, $data, $count));
        }
        return $this->output(new ResultModel(false));
    }


    /**
     * 附近司机(手机端)
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     * @param string fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    protected function getMobileAroundDriver($longitude, $latitude, $limit = 30000, $fields = null, $page = 1, $size = 10)
    {
        if (empty($longitude) || empty($latitude)) {
            return $this->output(new ResultModel(false, '不能定位你的位置'));
        }
        $mchid = cookie('mchid');
        if (!$mchid) {
            $mchid = $this->state->user_id;
        }
        if (!$mchid) {
            return $this->output(new ResultModel(false, '网络异常，请重新登陆！'));
        }
        $driverModel = new DriverModel();
        $field = "driver_id,GETDISTANCE(latitude,longitude,$latitude,$longitude) AS distance";
        $where = 'state = 1 and mchid=' . $mchid;
        $data = $driverModel->where($where)->field($field)->having("distance < $limit")->order('distance asc')->page($page, $size)->select();

        $retArr = array();
        if ($data) {
            $data1 = $driverModel->field($field)->having("distance < $limit")->select();
            $count = count($data1);
            foreach ($data as $key => $value) {
                $o = $this->sudo('Driver\\Account', $value['driver_id'], StateModel::$DRIVER);
                $r = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'driver_id,longitude,latitude');
                if ($r->ret) {
                    $retArr[] = array(array($r->data['longitude'], $r->data['latitude']), $r->data['driver_id']);
                }
            }
            return $this->output(new ResultModel(true, $retArr));
        }

        $data = $this->getGdAddressCodeByGdApi($longitude, $latitude);
        if ($data['status'] == 0) {
            return $this->output(new ResultModel(false, '获取数据失败，请重新加载！'));
        }

        //虚拟司机
        $driverModel = new DriverModel();
        $field = "driver_id";
        $where = 'virtual = 1 and mchid=' . $mchid;
//        $where = 'virtual = 1 and start_address_code = ' . $data['code'] . ' and mchid=' . $mchid;
        $data = $driverModel->where($where)->field($field)->page($page, $size)->select();
        if ($data) {
            foreach ($data as $key => $value) {
                $o = $this->sudo('Driver\\Account', $value['driver_id'], StateModel::$DRIVER);
                $rand = rand(0, 3);
                if ($rand == 0) {
                    $longitude -= rand(0, 50) / 30000;
                    $latitude -= rand(0, 50) / 30000;
                } else if ($rand == 1) {
                    $longitude -= rand(0, 50) / 30000;
                    $latitude += rand(0, 50) / 30000;
                } else if ($rand == 2) {
                    $longitude += rand(0, 50) / 30000;
                    $latitude -= rand(0, 50) / 30000;
                } else if ($rand == 3) {
                    $longitude += rand(0, 50) / 30000;
                    $latitude += rand(0, 50) / 30000;
                }

                $r = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'driver_id,longitude,latitude');
                if ($r->ret) {
                    $retArr[] = array(array($longitude, $latitude), $r->data['driver_id']);
                }
            }

            return $this->output(new ResultModel(true, $retArr));
        }

        return $this->output(new ResultModel(false, '周边暂无司机！'));
    }

    /**
     * 筛选司机(手机端)
     * @param double $longitude 经度（默认为空，为当前乘客上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前乘客上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为60000，方圆60公里）
     * @param string fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getMobileScreenDriver($longitude = null, $end_address_code = null, $latitude = null, $limit = 30000, $fields = null, $page = 1, $size = 10)
    {
        $where = array();

        if (empty($end_address_code)) {
            return $this->output(new ResultModel(false, '请输入目的地'));
        }

        $r = $this->checkingAddressCode($end_address_code);
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
        }

        $where['end_address_code'] = $end_address_code;
        //$where['start_time'] = array('gt', get_current_time());   //出发时间大于当前时间
        //$where['residual_seating'] = array('gt', 0);  //座位数大于零
        $where['state'] = 1; //车主状态为等候
        $driverModel = new DriverModel();
        $field = "driver_id,GETDISTANCE(latitude,longitude,$latitude,$longitude) AS distance";
        $data = $driverModel->where($where)->field($field)->having("distance < $limit")->order('distance asc')->page($page, $size)->select();
        $retArr = array();
        if ($data) {
            $data1 = $driverModel->where($where)->field($field)->having("distance < $limit")->select();
            $count = count($data1);
            foreach ($data as $key => $value) {
                $o = $this->sudo('Driver\\Account', $value['driver_id'], StateModel::$DRIVER);
                $r = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'driver_id,longitude,latitude');
                if ($r->ret) {
                    $retArr[] = array(array($r->data['longitude'], $r->data['latitude']), $r->data['driver_id']);
                }
            }

            return $this->output(new ResultModel(true, $retArr));
        }

        $data = $this->getGdAddressCodeByGdApi($longitude, $latitude);
        if ($data['status'] == 0) {
            return $this->output(new ResultModel(true, array()));
        }

        //虚拟司机
        $driverModel = new DriverModel();
        $field = "driver_id";
        $newwhere['virtual'] = 1;
        $newwhere['start_address_code'] = $data['code'];
        $newwhere['end_address_code'] = $end_address_code;
        $data = $driverModel->where($newwhere)->field($field)->page($page, $size)->select();
        if ($data) {
            foreach ($data as $key => $value) {
                $o = $this->sudo('Driver\\Account', $value['driver_id'], StateModel::$DRIVER);
                $rand = rand(0, 3);
                if ($rand == 0) {
                    $longitude -= rand(0, 50) / 30000;
                    $latitude -= rand(0, 50) / 30000;
                } else if ($rand == 1) {
                    $longitude -= rand(0, 50) / 30000;
                    $latitude += rand(0, 50) / 30000;
                } else if ($rand == 2) {
                    $longitude += rand(0, 50) / 30000;
                    $latitude -= rand(0, 50) / 30000;
                } else if ($rand == 3) {
                    $longitude += rand(0, 50) / 30000;
                    $latitude += rand(0, 50) / 30000;
                }
                $r = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'driver_id,longitude,latitude');
                if ($r->ret) {
                    $retArr[] = array(array($longitude, $latitude), $r->data['driver_id']);
                }
            }
            return $this->output(new ResultModel(true, $retArr));
        }
        return $this->output(new ResultModel(true, array()));
    }

    /**
     * 获取司机信息(手机端)
     * @param double $driver_id 司机id
     */
    public function getMobileDrivers($driver_id)
    {
        if (!empty($driver_id)) {
//            $r=$this->find(array('driver_id'=>$driver_id),'Driver','name,car_type,car_brand,car_register_time,start_time,start_address_code,end_address_code',null);
            $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'start_address_code,end_address_code,name,cellphone,price,car_type,car_brand,car_register_time,start_time,virtual');
            if (empty($r)) {
                return $this->output(new ResultModel(false, '未查找到该司机的相关信息'));
            }
        } else {
            return $this->output(new ResultModel(false, '请选择司机'));
        }
        $r->data['car_register_time'] = substr($r->data['car_register_time'], 0, 10);
        $r->data['start_time'] = substr($r->data['start_time'], 0, 16);
        $a = $this->checkingGdAddressCode($r->data['start_address_code']);
        $b = $this->checkingAddressCode($r->data['end_address_code']);
        $r->data['route'] = $a->data['address'] . '-' . $b->data['address'];
        if ($r->data['virtual'] == 1) {
            $r->data['virtual'] = true;
        } else {
            $r->data['virtual'] = false;
        }
        if (!$r->data['start_time']) {
            $r->data['start_time'] = "-";
        }
        return $this->output($r);
    }

    /**
     * 根据定位获取周边线路数量
     * @param string $latitude 纬度
     * @param string $longitude 经度
     * @return null|resultModel
     */
    public function getAroundLines($latitude = null, $longitude = null){
        $arroundLinesCount = 10;
        $totalLinesCount = 0;
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $totalLinesC = $this->count($where, 'Line');
        if($totalLinesC->ret){
            $totalLinesCount = $totalLinesC->data;
        }
        return $this->output(new \ResultModel(true, ['arround_lines_count' => $arroundLinesCount, 'total_lines_count' => $totalLinesCount]) ,1);
    }
}

?>

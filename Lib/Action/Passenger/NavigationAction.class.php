<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 导航接口
 *
 * <AUTHOR>
 */
class NavigationAction extends \PassengerCoreAction
{
    /**
     * 获取乘客端导航
     */
    public function getNavigation()
    {
        $ret = new \ResultModel(false, '暂无导航');
        if($this->mchid == 1018){
              $nav = [
/*                  [
                      'title' => '拼车',
                      'name' => 'carpool',
                      'is_show' => true,
                  ],*/
/*                  [
                      'title' => '包车',
                      'name' => 'chartcar',
                      'is_show' => true,
                  ],
                  [
                      'title' => '班线车',
                      'name' => 'train',
                      'is_show' => false,
                  ],
                  [
                      'title' => '顺风车',
                      'name' => 'freecar',
                      'is_show' => false,
                  ],
                  [
                      'title' => '带货',
                      'name' => 'goods',
                      'is_show' => false,
                  ],*/
                  [
                      'title' => '代办',
                      'name' => 'service',
                      'is_show' => true,
                  ],
              ];
            $ret->ret = true;
            $ret->data = $nav;
            $ret->count = count($nav);
        }else if($this->mchid == 1063){
            $nav = [
                  [
                      'title' => '拼车',
                      'name' => 'carpool',
                      'is_show' => true,
                  ],
                  [
                      'title' => '包车',
                      'name' => 'chartcar',
                      'is_show' => true,
                  ],
/*                  [
                      'title' => '班线车',
                      'name' => 'train',
                      'is_show' => true,
                  ],*/
                  [
                      'title' => '顺风车',
                      'name' => 'freecar',
                      'is_show' => true,
                  ],
                  [
                      'title' => '带货',
                      'name' => 'goods',
                      'is_show' => true,
                  ],
/*                [
                    'title' => '代办',
                    'name' => 'service',
                    'is_show' => true,
                ],*/
            ];
            $ret->ret = true;
            $ret->data = $nav;
            $ret->count = count($nav);
        }else if($this->mchid == 181){
            $nav = [
                [
                    'title' => '拼车',
                    'name' => 'carpool',
                    'is_show' => true,
                ],
                [
                    'title' => '出租车',
                    'name' => 'taxi',
                    'is_show' => true,
                ],
                [
                    'title' => '包车',
                    'name' => 'chartcar',
                    'is_show' => true,
                ],
                [
                    'title' => '班线车',
                    'name' => 'train',
                    'is_show' => true,
                ],
                [
                    'title' => '顺风车',
                    'name' => 'freecar',
                    'is_show' => true,
                ],
                [
                    'title' => '带货',
                    'name' => 'goods',
                    'is_show' => true,
                ],
                [
                    'title' => '代办',
                    'name' => 'service',
                    'is_show' => true,
                ],
            ];
            $ret->ret = true;
            $ret->data = $nav;
            $ret->count = count($nav);
        }else if($this->mchid == 1091){
            $nav = [
/*                [
                    'title' => '拼车',
                    'name' => 'carpool',
                    'is_show' => true,
                ],
                [
                    'title' => '快车',
                    'name' => 'fastcar',
                    'is_show' => true,
                ],*/
                [
                    'title' => '出租车',
                    'name' => 'taxi',
                    'is_show' => true,
                ],
/*                [
                    'title' => '包车',
                    'name' => 'chartcar',
                    'is_show' => true,
                ],*/
                [
                    'title' => '定制班线车',
                    'name' => 'train',
                    'is_show' => true,
                ],
/*                [
                    'title' => '顺风车',
                    'name' => 'freecar',
                    'is_show' => true,
                ],
                [
                    'title' => '带货',
                    'name' => 'goods',
                    'is_show' => true,
                ],
                [
                    'title' => '代办',
                    'name' => 'service',
                    'is_show' => true,
                ],*/
            ];
            $ret->ret = true;
            $ret->data = $nav;
            $ret->count = count($nav);
        }
        return $this->output($ret);
    }
}

?>

<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-常用联系人
 *
 * <AUTHOR>
 */
class YyPassengerContactsAction extends \PassengerCoreAction
{
    /**
     * 获取常用联系人
     * @param integer $page 页码（默认为1）
     * @param integer $size 分页大小（默认为10）
     * @param string $contacts_cellphone 联系人手机号（默认为空）
     */
    public function getContacts($page = 1, $size = 10, $contacts_cellphone = null){
        $where = " mchid = ".$this->mchid;
        $where .= " AND passenger_id = ".$this->state->user_id;
        if(!empty($contacts_cellphone)){
            $where .= " AND contacts_cellphone = ".$contacts_cellphone;
        }
        $contactsList = $this->select($where, $page, $size, 'passenger_contacts_id desc', 'PassengerContacts' ,"passenger_contacts_id,contacts_passenger_id,contacts_id_number,contacts_cellphone,contacts_name");
        return $this->output($contactsList);
    }

    /**
     * 添加联系人
     * @param integer $contacts_name 联系人姓名（必填）
     * @param integer $contacts_cellphone 联系人手机号（必填）
     * @param string $contacts_id_number 联系人身份证id（必填）
     */
    public function doAddContacts($contacts_name, $contacts_cellphone, $contacts_id_number){
        $this->data['passenger_id'] = $this->state->user_id;
        $this->data['mchid'] = $this->mchid;

        $passengerR = $this->find(array('mchid' => $this->mchid, 'cellphone' => $contacts_cellphone), 'Passenger');
        if($passengerR->ret){
            $this->data['contacts_passenger_id'] = $passengerR->data['passenger_id'];
            if($passengerR->data['passenger_id'] == $this->state->user_id){
                return $this->output(new \ResultModel(false, "添加失败"));
            }
        }

        $where = " contacts_cellphone = ".$contacts_cellphone;
        $where .= " OR contacts_id_number = $contacts_id_number";
        $where .= " OR contacts_name = '$contacts_name'";
        $passengerContactsR = $this->find($where, 'PassengerContacts');
        if($passengerContactsR->ret){
            return $this->output(new \ResultModel(false, "重复添加"));
        }
        $passengerContactsA = $this->add('PassengerContacts');
        if($passengerContactsA->ret){
            $this->data['passenger_contacts_id'] = $passengerContactsA->data;
            unset($this->data['passenger_id']);
            unset($this->data['contacts_passenger_id']);
            unset($this->data['mchid']);
            return $this->output(new \ResultModel(true, $this->data));
        }
        return $this->output(new \ResultModel(false));
    }

    /**
     * 修改联系人
     * @param integer $passenger_contacts_id  联系人关联id
     */
    public function doUpdateContacts($passenger_contacts_id, $contacts_name, $contacts_cellphone, $contacts_id_number){
        $where['mchid'] = $this->mchid;
        $where['passenger_contacts_id'] = $passenger_contacts_id;
        $passengerContactsR = $this->find($where, 'PassengerContacts');
        if($passengerContactsR->ret){
            $passengerR = $this->find(array('mchid' => $this->mchid, 'cellphone' => $contacts_cellphone), 'Passenger');
            if($passengerR->ret){
                $this->data['contacts_passenger_id'] = $passengerR->data['passenger_id'];
                if($passengerR->data['passenger_id'] == $this->state->user_id){
                    return $this->output(new \ResultModel(false, "添加失败"));
                }
            }

            $passengerContactsS = $this->save('PassengerContacts');
            if($passengerContactsS->ret){
                $this->data['passenger_contacts_id'] = $passengerContactsS->data;
                unset($this->data['passenger_id']);
                unset($this->data['contacts_passenger_id']);
                unset($this->data['mchid']);
                return $this->output(new \ResultModel(true, $this->data));
            }

        }

        return $this->output(new \ResultModel(false));
    }

    /**
     * 删除常用联系人
     * @param string $passenger_contacts_ids 支持批量删除（必填）
     */
    public function doDelContacts($passenger_contacts_ids){
        return $this->output($this->delete($passenger_contacts_ids, 'PassengerContacts'));
    }
}

?>

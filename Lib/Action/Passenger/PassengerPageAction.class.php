<?php

import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of PassengerPageAction
 *
 * <AUTHOR>
 */
class PassengerPageAction extends PassengerCoreAction
{
    public function __construct()
    {
        parent::__construct();
        //设置商户参数
        $this->_setMerchantConfig();
    }


    //每次进入微信页面存储一次商户id
    //cookie存储一次商户id
    //乘客前端首页
    public function indexPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();

        if (!$this->mchid) {
            return false;
        }

        $this->getMchInfoToPageByMchid($this->mchid);

        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->assign('total_use', $this->getPlatformUseUserStatistics());
        $this->assign('pay_mode', $this->queryPayMode());
        // 查询底部导航显隐配置
        $bottomNavigationEnabled = $this->checkBottomNavigationEnabled();
        $this->assign('bottom_navigation_enabled', $bottomNavigationEnabled);
        $hasOpened = false;
        foreach ($bottomNavigationEnabled as $value) {
            if ($value == 1) {
                $hasOpened = true;
                break;
            }
        }
        if (!$hasOpened) {
            $this->assign('bottom_navigation_closed_all', 1);
        } else {
            $this->assign('bottom_navigation_closed_all', 0);
        }

        $consults = $this->queryRedirectCustomPassengerConsult();
        $this->assign('consult_enabled', in_array($this->mchid, explode(',', $consults['merchant_ids'])));
        $this->assign('consult_pages', $consults['pages']);
        $this->assign('consult_codes', $consults['merchant_codes']);

        if (APP_DEBUG) {
            //获取用户信息
            $passengerR = $this->find(array('passenger_id' => C('DEBUG_PASSENGER_ID'), 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay,name');

            $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
            $userInfoArr = $thirdR->data;

            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']), $this->mchid);
            $this->assign('cellphone', $passengerR->data['cellphone']);
            $this->assign('openid', $passengerR->data['openid_pay']);
            $this->assign('uid', $passengerR->data['passenger_id']);
            $this->assign('name', $passengerR->data['name']);
            $lineR = $this->count(array('mchid' => $this->mchid, 'is_del' => 0), 'Line');
            $this->assign('line_count', $lineR->ret ? $lineR->data : 0);
            $this->assign('passenger_id', $this->state->user_id);
            $thirdR = $this->find(array('passenger_id' => $this->state->user_id), 'ThirdParty');
            $this->assign('avatar', $thirdR->ret ? $thirdR->data['third_avatar'] : "");
            //导航设置
            $navConfig = $this->defineMerchantNavigationMenu($this->mchid);
            $this->assign('nav_config_ids', $navConfig);
            $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
            $this->assign('total_use', $this->getPlatformUseUserStatistics());
            $this->assign('passenger_token', $this->getApiToken($this->state->user_id));

            //当前路由的主页
            $this->_setCurrentHomePage('passenger_index');
            $this->checkServiceEnable();
            $this->_userInfo($this->mchid);
            $this->display('Tpl/Passenger/Index/index.html');
            return;
        }
        if ($this->remind()) {
            $this->display('Tpl/Common/remind.html');
            return;
        }
        //钱包余额支付服务是否开启 0:未开启 1:开启
        $this->assign('wallet_service_enabled', $this->checkWalletService());
        $this->assign('is_default_via_axis', $this->checkMultiStationMerchant());

        $this->checkServiceEnable();

        // 判断用户是否已经登录，StateModel中的state->user_id不为空表示已登录
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_index');
        }

        $navConfig = $this->defineMerchantNavigationMenu($this->mchid);
        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        $lineR = $this->count(array('mchid' => $this->mchid, 'is_del' => 0), 'Line');
        $this->assign('line_count', $lineR->ret ? $lineR->data : 0);

        $this->assign('ai_banner_enabled', $this->checkAiBannerEnabled());

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_index');

        $this->assigiInnerApiAttribute();
        $this->display('Tpl/Passenger/Index/index.html');
    }

    private function checkAiBannerEnabled()
    {
        return in_array($this->mchid, C('AI_BANNER_MERCHANT_IDS')) ? 1 : 0;
    }

    /**
     * 包车/带货咨询页面独立Banner与页面
     *
     * @return mixed
     */
    private function queryRedirectCustomPassengerConsult()
    {
        // 从system_config表查询配置
        $config = $this->find([
            'key'          => 'REDIRECT_CUSTOM_PASSENGER_CONSULT',
        ], 'SystemConfig', 'key,value');
        if ($config->ret) {
            return json_decode($config->data['value'], true);
        }
        return array();
    }

    /**
     * 支持班次列表展示多个上下车点的商户列表
     *
     * @return bool
     */
    private function checkMultiStationMerchant()
    {
        // 从system_config表查询配置
        $config = $this->find([
            'account_type' => \CommonDefine::SYSTEM_ROLE_1,
            'account_id'   => $this->mchid,
            'key'          => 'passenger_multi_station_merchant_enabled',
        ], 'SystemConfig', 'key,value');
        if ($config->ret) {
            return intval($config->data['value']) == 1;
        }
        return false;
    }

    /**
     * 检查底部导航是否开启
     *
     * @return mixed
     */
    protected function checkBottomNavigationEnabled()
    {
        // 从system_config表查询配置
        $config = $this->find([
            'account_type' => \CommonDefine::SYSTEM_ROLE_1,
            'account_id'   => $this->mchid,
            'key'          => 'passenger_bottom_navigation_enabled',
        ], 'SystemConfig', 'key,value');
        if ($config->ret) {
            try {
                return json_decode($config->data['value'], true);
            } catch (Exception $e) {
                return array();
            }
        } else {
            return array();
        }
    }


    /**
     * 乘客积分抵扣信息
     *
     * @param integer $price
     * @return \ResultModel
     */
    public function getPassengerProfile($price = 0)
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $params = array(
            'price' => $price,
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
            $ret->count = count($results['data']);
        }
        return $this->output($ret);
    }

    /**
     * 乘客钱包充值记录
     *
     * @param integer $year
     * @param integer $month
     * @param integer $type
     * @return \ResultModel
     */
    public function getPassengerWallets($year, $month, $type)
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json',
            'Content-Type: application/json'
        ];
        $params = array(
            'year' => $year,
            'month' => $month,
            'type' => $type,
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/wallets", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
            $ret->count = count($results['data']);
        }
        return $this->output($ret);
    }


    /**
     * 乘客提现转账记录
     *
     * @param integer $year
     * @param integer $month
     * @param integer $type
     * @return \ResultModel
     */
    public function getPassengerTransfers($year = '', $month = '')
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json',
            'Content-Type: application/json'
        ];
        $params = array(
            'year' => $year,
            'month' => $month,
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/transfers", 'get', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
            $ret->count = count($results['data']);
        }
        return $this->output($ret);
    }


    /**
     * 获取乘客个人主页联盟广告
     *
     * @return \ResultModel
     */
    public function getPassengerProfileAd()
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'passenger: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/passengers/{$this->state->user_id}/platforms/1/clients/1/placements/passenger-profile/ad_contents", 'get', array(), $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
        }
        return $this->output($ret);
    }





    /**
     * 支付方式
     *
     * @return integer
     */
    public function queryPayMode()
    {
        $payModeR = $this->find(array('key' => \CommonDefine::ORDER_PAYMENT_MODE, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $this->mchid), 'SystemConfig');
        if ($payModeR->ret) {
            return $payModeR->data['value'];
        } else {
            return \CommonDefine::ORDER_PAYMENT_MODE_1;
        }
    }

    /**
     * 分配接口请求地址与TOKEN
     */
    protected function assigiInnerApiAttribute()
    {
        //分配接口请求地址与TOKEN
        $this->assign('api_host', sprintf('%scgi-bin/', C('WEB_ROOT')));
        $this->assign('passenger_token', $this->getApiToken($this->state->user_id));
    }


    /**
     * 拼车单独访问入口
     *
     * @return void
     */
    public function getPinchePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("微信授权失败！请稍后再试");
            return false;
        }
        $this->systemIsMaintenance();
        $this->getMchInfoToPageByMchid($this->mchid);
        if ($this->remind()) {
            $this->display('Tpl/Common/remind.html');
            return;
        }
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_pinche');
        }

        $navConfig = array(
            1 => array('id' => 'carpool',   'nav_id' => 1, 'name' => '拼车',   'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1, 'click' => \CommonDefine::IS_SHELF_1),
        );
        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        $lineR = $this->count(array('mchid' => $this->mchid, 'is_del' => 0), 'Line');
        $this->assign('line_count', $lineR->ret ? $lineR->data : 0);

        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->assign('total_use', $this->getPlatformUseUserStatistics());

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_pinche');

        //分配接口请求地址与TOKEN
        $this->assign('api_host', C('FILE_ROOT') . 'cgi-bin');
        $this->assign('passenger_token', $this->getApiToken($this->state->user_id));

        $this->display('Tpl/Passenger/Index/index.html');
    }

    public function getApiToken($accountId)
    {
        /**
         * @Date 2023.04.13
         * <AUTHOR>
         */
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/passenger/account_ids/{$accountId}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {
            return $token['data']['access_token'];
        }
    }

    //斑马首页
    public function banmaPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (APP_DEBUG) {
            //获取用户信息
            $passengerR = $this->find(array('passenger_id' => 859, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay,name');

            $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
            $userInfoArr = $thirdR->data;

            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']), $this->mchid);
            $this->assign('cellphone', $passengerR->data['cellphone']);
            $this->assign('openid', $passengerR->data['openid_pay']);
            $this->assign('uid', $passengerR->data['passenger_id']);
            $this->assign('name', $passengerR->data['name']);
            $lineR = $this->count(array('mchid' => $this->mchid, 'is_del' => 0), 'Line');
            $this->assign('line_count', $lineR->ret ? $lineR->data : 0);
            $this->assign('passenger_id', $this->state->user_id);
            $thirdR = $this->find(array('passenger_id' => $this->state->user_id), 'ThirdParty');
            $this->assign('avatar', $thirdR->ret ? $thirdR->data['third_avatar'] : "");
            $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
            $this->assign('total_use', $this->getPlatformUseUserStatistics());

            //当前路由的主页
            $this->_setCurrentHomePage('passenger_banma');

            $this->_userInfo($this->mchid);
            $this->display('Tpl/Passenger/Index/banmaindex.html');
            return;
        }


        $this->_userInfo($this->mchid);
        //授权检测
        $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_banma');
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_banma');

        $this->display('Tpl/Passenger/Index/banmaindex.html');
    }

    public function linesPage()
    {
        $this->getPinchePage();
    }

    public function taxiPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_taxi');
        }

        //导航设置
        $navConfig = array(
            8 => array('id' => 'taxi',  'nav_id' => 8, 'name' => '出租车',   'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
        );

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_taxi');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    //包车
    public function chartersPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_charters');
        }

        //导航设置
        $navConfig = array(
            1 => array('id' => 'carpool',   'nav_id' => 1, 'name' => \CommonDefine::ORDER_PINCHE,  'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            2 => array('id' => 'chart_car', 'nav_id' => 2, 'name' => \CommonDefine::ORDER_BAOCHE,   'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
            3 => array('id' => 'regular',   'nav_id' => 3, 'name' => \CommonDefine::ORDER_DINGZHIKEYUN, 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            4 => array('id' => 'free_car',  'nav_id' => 4, 'name' => \CommonDefine::ORDER_SHUNFENGCHE, 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            5 => array('id' => 'goods',     'nav_id' => 5, 'name' => \CommonDefine::ORDER_DAIHUO,   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            6 => array('id' => 'charge',    'nav_id' => 6, 'name' => \CommonDefine::ORDER_DAIBAN,   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
        );

        if ($this->mchid == 950 || $this->mchid == 969) {
            $navConfig[3]['name'] = '城际网约车';
        }
        if ($this->mchid == 950) {
            $navConfig[5]['name'] = '小件速运';
            $navConfig[6]['name'] = '跑腿';
        }
        if ($this->mchid == 967) {
            $navConfig[2]['name'] = '快车';
            $navConfig[5]['name'] = '跑腿';
        }
        if ($this->mchid == 1032) {
            $navConfig[3]['name'] = '巴士';
        }
        if ($this->mchid == 1135) {
            $navConfig[6]['name'] = '商旅';
        }

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_charters');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    //班线车
    public function trainsPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_trains');
        }

        //导航设置
        $navConfig = array(
            3 => array('id' => 'regular',   'nav_id' => 1, 'name' => '定制班线车', 'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
        );

        if ($this->mchid == 1116) {
            $navConfig[3]['name'] = '城际快线';
        }

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);
        $this->assigiInnerApiAttribute();

        $this->checkServiceEnable();

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_trains');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    //顺风车
    public function freeRidePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_free_ride');
        }

        //导航设置
        $navConfig = array(
            1 => array('id' => 'carpool',   'nav_id' => 1, 'name' => '拼车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            2 => array('id' => 'chart_car', 'nav_id' => 2, 'name' => '包车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            3 => array('id' => 'regular',   'nav_id' => 3, 'name' => '定制班线车', 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            4 => array('id' => 'free_car',  'nav_id' => 4, 'name' => '顺风车', 'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
            5 => array('id' => 'goods',     'nav_id' => 5, 'name' => '带货',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            6 => array('id' => 'charge',    'nav_id' => 6, 'name' => '代办',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
        );

        if ($this->mchid == 950 || $this->mchid == 969) {
            $navConfig[3]['name'] = '城际网约车';
        }
        if ($this->mchid == 950) {
            $navConfig[5]['name'] = '小件速运';
            $navConfig[6]['name'] = '跑腿';
        }
        if ($this->mchid == 967) {
            $navConfig[2]['name'] = '快车';
            $navConfig[5]['name'] = '跑腿';
        }
        if ($this->mchid == 1032) {
            $navConfig[3]['name'] = '巴士';
        }

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_free_ride');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    //带货
    public function takeGoodsPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_take_goods');
        }

        //导航设置
        $navConfig = array(
            1 => array('id' => 'carpool',   'nav_id' => 1, 'name' => '拼车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            2 => array('id' => 'chart_car', 'nav_id' => 2, 'name' => '包车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            3 => array('id' => 'regular',   'nav_id' => 3, 'name' => '定制班线车', 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            4 => array('id' => 'free_car',  'nav_id' => 4, 'name' => '顺风车', 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            5 => array('id' => 'goods',     'nav_id' => 5, 'name' => '带货',   'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
            6 => array('id' => 'charge',    'nav_id' => 6, 'name' => '代办',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
        );

        if ($this->mchid == 950 || $this->mchid == 969) {
            $navConfig[3]['name'] = '城际网约车';
        }
        if ($this->mchid == 950) {
            $navConfig[5]['name'] = '小件速运';
            $navConfig[6]['name'] = '跑腿';
        }
        if ($this->mchid == 967) {
            $navConfig[2]['name'] = '快车';
            $navConfig[5]['name'] = '跑腿';
        }
        if ($this->mchid == 1032) {
            $navConfig[3]['name'] = '巴士';
        }

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_take_goods');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    //代办
    public function agencyPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            $this->error("页面走丢了！请稍后再试");
            return false;
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_agency');
        }

        //导航设置
        $navConfig = array(
            1 => array('id' => 'carpool',   'nav_id' => 1, 'name' => '拼车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            2 => array('id' => 'chart_car', 'nav_id' => 2, 'name' => '包车',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            3 => array('id' => 'regular',   'nav_id' => 3, 'name' => '定制班线车', 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            4 => array('id' => 'free_car',  'nav_id' => 4, 'name' => '顺风车', 'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            5 => array('id' => 'goods',     'nav_id' => 5, 'name' => '带货',   'show' => \CommonDefine::IS_SHOW_0, 'active' => \CommonDefine::IS_SHELF_0),
            6 => array('id' => 'charge',    'nav_id' => 6, 'name' => '代办',   'show' => \CommonDefine::IS_SHOW_1, 'active' => \CommonDefine::IS_SHELF_1),
        );

        if ($this->mchid == 950 || $this->mchid == 969) {
            $navConfig[3]['name'] = '城际网约车';
        }
        if ($this->mchid == 950) {
            $navConfig[5]['name'] = '小件速运';
            $navConfig[6]['name'] = '跑腿';
        }
        if ($this->mchid == 967) {
            $navConfig[2]['name'] = '快车';
            $navConfig[5]['name'] = '跑腿';
        }
        if ($this->mchid == 1032) {
            $navConfig[3]['name'] = '巴士';
        }

        $this->assign('nav_config_ids', $navConfig);
        $this->_userInfo($this->mchid);

        //当前路由的主页
        $this->_setCurrentHomePage('passenger_agency');

        $this->display('Tpl/Passenger/Index/index.html');
    }

    public function orderPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_order');
        }
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        $this->assign('status', $status);

        $this->display('Tpl/Passenger/Index/passenger_order.html');
    }
    // 个人中心
    public function minePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        $showFollowRemind = 0;
        if (!isset($this->state->user_id)) {
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_mine');
        } else {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay', 'cash_balances', 'openid');
            if ($passengerR->ret) {
                if (is_null($passengerR->data['openid'])) {
                    $showFollowRemind = 1;
                }
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }

        //钱包余额支付服务是否开启 0:未开启 1:开启
        $this->assign('wallet_service_enabled', $this->checkWalletService());
        $this->assign('status', $status);
        $this->assign('show_follow_remind', $showFollowRemind);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);

        $this->checkServiceEnable();
        // 查询底部导航显隐配置
        $bottomNavigationEnabled = $this->checkBottomNavigationEnabled();
        $this->assign('bottom_navigation_enabled', $bottomNavigationEnabled);
        $hasOpened = false;
        foreach ($bottomNavigationEnabled as $value) {
            if ($value == 1) {
                $hasOpened = true;
                break;
            }
        }
        if (!$hasOpened) {
            $this->assign('bottom_navigation_closed_all', 1);
        } else {
            $this->assign('bottom_navigation_closed_all', 0);
        }

        $this->display('Tpl/Passenger/Index/mine.html');
    }

    public function followOfficialPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);
        $mchWechatQr = $this->find(array('mchid' => $this->mchid), 'MchWechatQr', 'mchid, mch_wechat_qr');
        if ($mchWechatQr->ret) {
            $this->assign('mch_wechat_qr', $mchWechatQr->data['mch_wechat_qr']);
        }
        $this->display('Tpl/Passenger/Index/follow_official.html');
    }

    public function queryMerchantProfile()
    {
        $default = 'https://www.cczhaoche.com/upload/merchant/cczhaoche/cczhaoche_logo.png';
        // 获取内部API的访问令牌
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$this->mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);

        // 检查令牌请求是否成功，并确保令牌存在
        if ($token['status'] == 'success' && isset($token['data']['access_token'])) {
            $accessToken = $token['data']['access_token'];

            // 设置请求头，包含Authorization和Accept
            $headers = array(
                "Authorization: Bearer {$accessToken}",
                "Accept: application/json",
            );

            // 请求商户的详细资料
            $response = sendXmlOverPost(C('CC_INNER_API_HOST') . "/api/auth/merchant/profiles", array(), $headers, false);

            // 检查资料请求是否成功，并确保响应中包含商户的 logo 属性
            if ($response['status'] == 'success' && isset($response['data']['logo_attr'])) {
                $attr = json_decode($response['data']['logo_attr']);

                // 返回小程序 logo URL，若未找到则返回默认值
                if (isset($attr->mini_program_logo_url)) {
                    $default = $attr->mini_program_logo_url;
                }
            }
        }

        // 返回默认的商户 logo URL
        return $default;
    }

    /**
     * 钱包余额支付服务是否开启
     *
     * @return int
     */
    public function checkWalletService()
    {
        $enabled = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/wallet/enabled", 'get');
        $response = json_decode($enabled, true);
        if (
            $response['status'] == 'success' && $response['data']['wallet'] == true
        ) {
            return 1;
        } else {
            return 0;
        }
    }


    public function checkServiceEnable()
    {
        /**
         * @Date 2022.07.13
         * <AUTHOR>
         * @version 3.3
         */
        $enabled = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/points/enabled", 'get');
        $response = json_decode($enabled, true);
        if (
            $response['status'] == 'success'
            && $response['data']['points'] == true
        ) {
            $this->assign('pmall', sprintf('%smicro-apps/point-mall/user?callback=%s', C('WEB_ROOT'), $this->getCipmchidByMchid($this->mchid)->data['ciphertext']));
            $this->assign('pmall_control', 'pmall_control_enable');
        } else {
            $this->assign('pmall_control', 'pmall_control_disable');
        }

        $this->assiginFapiaoUrl();

        # 邀请有礼活动配置
        $referralRes = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/referral_setups", 'get');
        $referralResonse = json_decode($referralRes, true);
        if (
            $referralResonse['status'] == 'success'
            && $referralResonse['data']['value']['enabled'] == true
        ) {
            $this->assign('referral_enabled', 1);
            $this->assign('referral_url', sprintf('%smicro-apps/invitation/?callback=%s&endpoint=referral&endpoint_id=%s&channel=gzh&inviter=%s', C('WEB_ROOT'), $this->getCipmchidByMchid($this->mchid)->data['ciphertext'], $referralResonse['data']['value']['setup']['id'], $this->state->user_id));

            $amount = isset($referralResonse['data']['value']['setup']['fresh_user_reward']['coupons'][0])
                ? $referralResonse['data']['value']['setup']['fresh_user_reward']['coupons'][0]['value']
                : $referralResonse['data']['value']['setup']['fresh_order_reward']['coupons'][0]['value'];
            $this->assign('referral_amount', $amount);
        } else {
            $this->assign('referral_enabled', 0);
        }
    }

    /**
     * 乘客端订单详情
     *
     * @return void
     */
    public function detailPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, sprintf('passenger_order_detail?order_id=%s', $_GET['order_id']));
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_order_detail');
        $this->assign('wallet_service_enabled', $this->checkWalletService());
        $this->assign('is_default_via_axis', $this->checkMultiStationMerchant());
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/orderDetail.html');
    }
    // withdrawalLog
    public function withdrawalLogPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_withdrawal_log');
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/withdrawalLog.html');
    }

    public function rechargePage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_recharge');
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/recharge.html');
    }

    public function balanceDetailPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_balance_detail');
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/balanceDetail.html');
    }
    public function resultPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_balance_result');
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/result.html');
    }

    public function widthdrawalPage()
    {
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }
        //当前路由的主页
        $this->_setCurrentHomePage('passenger_withdrawal');
        $this->assigiInnerApiAttribute();
        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->_userInfo($this->mchid);
        $this->checkServiceEnable();
        $this->display('Tpl/Passenger/Index/withdrawal.html');
    }





    //电话叫车
    public function callPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_call');
        } else {
            $status = 1;
            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay');
            if ($passengerR->ret) {
                $this->assign('openid', $passengerR->data['openid_pay']);
            }
        }

        $this->assign('status', $status);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->assign('total_use', $this->getPlatformUseUserStatistics());
        $this->_userInfo($this->mchid);

        $this->display('Tpl/Passenger/Index/callDriver.html');
    }

    public function sharePage($lfrn)
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $cipmchid = $this->getCipmchidByMchid($this->mchid)->data['ciphertext'];
        $this->getMchInfoToPageByMchid($this->mchid);
        //登陆状态
        if (!isset($this->state->user_id)) {
            //授权
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_share' . '/lfrn/' . $lfrn);
        } else {
            if ($this->state->user_type == \StateModel::$PASSENGER) {
                $this->assign('cellphone', $this->state->data['mobile']);
            }
        }

        $this->assign('lfrn', $lfrn);
        $this->assign('callback', $cipmchid);
        $this->display('Tpl/Passenger/Index/share_trip.html');
    }
    public function miniPage()
    {
        $miniProgramLogoUrl = $this->queryMerchantProfile();
        $this->assign('mini_program_logo_url', $miniProgramLogoUrl);
        $this->display('Tpl/Passenger/Index/openmini.html');
    }


    public function mysetPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        $this->state = \StateModel::get(\StateModel::$PASSENGER, $this->mchid);
        if ($this->state->user_id) {
            $this->assign('cellphone', $this->state->data['mobile']);
            $thirdR = $this->find(array('passenger_id' => $this->state->user_id), 'ThirdParty');
            $this->assign('nickname', $thirdR->ret ? $thirdR->data['third_account'] : "还没有设置昵称哦");
            $this->assign('avatar', $thirdR->ret ? $thirdR->data['third_avatar'] : "");
            $this->assign('passenger_id', $this->state->user_id);
        }

        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->display('Tpl/Passenger/Index/myset.html');
    }

    /**
     * 注册认证
     */
    public function registerauthPage()
    {
        // $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        if (APP_DEBUG) {
            //获取用户信息
            $passengerR = $this->find(array('passenger_id' => 859, 'mchid' => $this->mchid), 'Passenger', 'passenger_id, cellphone, openid_pay,name');

            $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
            $userInfoArr = $thirdR->data;
            \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account']), $this->mchid);
            $this->assign('cellphone', $passengerR->data['cellphone']);
            $this->assign('openid', $passengerR->data['openid_pay']);
            $this->assign('uid', $passengerR->data['passenger_id']);
            $this->assign('name', $passengerR->data['name']);
            $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
            $this->display('Tpl/Passenger/Index/registerauth.html');
            return;
        }
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_registerauth');
        }

        $this->display('Tpl/Passenger/Index/registerauth.html');
    }
    // 优惠券
    public function couponPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Passenger/Index/coupon.html');
    }
    //优惠券说明
    public function explainPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Passenger/Index/CouponInstructions.html');
    }

    /**
     * 包车/带货咨询页面
     */
    public function consultPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Passenger/Index/consult.html');
    }

    /**
     * 希格斯科技授权，支付使用
     */
    public function wechatauthforpayPage()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);
        $state = $dataArr[0];
        $tcall = $dataArr[1];
        $cipmchid = $dataArr[2];
        //解密商户id
        $mchid = $this->getMchidByCipmchid($cipmchid);
        //路上捡客司机id
        $driver_id = '';
        //渠道来源
        if (count($dataArr) >= 4) {
            if (substr($dataArr[3], 0, 3) == \CommonDefine::DRIVER_ENTRANCE_PARAMTER_DEP) {
                //判断是不是路上捡客
                $driver_id = substr($dataArr[3], 3, (strlen($dataArr[3]) - 3));
            }
        }

        $wechat = new \Wechat();
        $url = $wechat->getCodeAccessTokenUrlForPay($code);

        $data = file_get_contents($url);
        $arr = json_decode($data, true);
        if (isset($arr['errcode'])) {
            //TODO 临时授权取消
            //$arr['openid']='oGLwmwah5UoYsNDxTMagR-ydeUwI';
            $this->error("授权失败：" . $arr['errmsg']);
        }
        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_pas_token_' . $mchid, $arr['access_token'], $expires_in);
        $passengerR = $this->find(array('passenger_id' => $state, 'mchid' => $mchid), 'Passenger', 'passenger_id, cellphone, openid_pay,name,openid');

        //支付目前都是支付到自己的商户中，没有有默认的支付到CC招车系统了。

        // if (isset($arr['openid']) && $arr['openid']) {
        // if ($passengerR->data['openid_pay'] != $arr['openid']) {
        $passengerS = $this->save('Passenger', array('passenger_id' => $state, 'openid_pay' => $passengerR->data['openid']));
        // if (!$passengerS->ret) {
        //     $this->error("授权失败");
        // }
        // }
        // }

        //获取用户信息
        $thirdR = $this->find(array('passenger_id' => $passengerR->data['passenger_id']), 'ThirdParty', 'third_avatar,third_account');
        $userInfoArr = $thirdR->data;
        $passengerInfo = array('mobile' => $passengerR->data['cellphone'], 'avatar' => $userInfoArr['third_avatar'], 'nickname' => $userInfoArr['third_account'], 'openid_pay' => $passengerR->data['openid']);

        \StateModel::save($passengerR->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);

        if (!empty($tcall)) {
            # 如果URL中带有参数
            if (strpos($tcall, '?') === false) {
                $url = $tcall . '/callback/' . $this->getCipmchidByMchid($mchid)->data['ciphertext'];
            } else {
                $url = $tcall . '&callback=' . $this->getCipmchidByMchid($mchid)->data['ciphertext'];
            }
        } else {
            $url = C('WEB_ROOT') . 'passenger_index/callback/' . $this->getCipmchidByMchid($mchid)->data['ciphertext'];
        }

        if (!empty($driver_id)) {
            $driverQrAttachmentR = $this->find(array('driver_id' => $driver_id), 'DriverQrAttachment');
            if ($driverQrAttachmentR->ret) {
                $url .= ('/' . \CommonDefine::DRIVER_ENTRANCE_PARAMTER . '/' . $driverQrAttachmentR->data['driver_code']);
            }
        }
        # 重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节
        $url = $this->addWebRootIfMissing($url, C('WEB_ROOT'));
        \Log::write("微信支付授权后回调地址------------:" . $url, \Log::INFO);
        redirect($url);
        return;
    }

    /**
     * 微信授权后回调处理,保存用户openid及相关信息,商户的公众号授权
     */
    public function wechatauthPage()
    {
        if (!isset($_REQUEST['callback'])) {
            $this->error("微信用户授权商户参数无效，请稍后再试");
            return;
        }
        $cipmchid = $_REQUEST['callback'];
        $code = $_REQUEST['code'];
        $newPassengerData = [];
        $channelBranchId = '';
        $cid = isset($_REQUEST['cid']) ? $_REQUEST['cid'] : '';
        $driverId = '';
        $qrCode = isset($_REQUEST['_qr']) ? $_REQUEST['_qr'] : '';
        // 用户邀请渠道分台ID
        if (isset($_REQUEST['_cbp'])) {
            $branchCode = $_REQUEST['_cbp'];
            $branchR = $this->find(array('branch_code' => $branchCode), 'BranchQrAttachment');
            if ($branchR->ret) {
                $channelBranchId = $branchR->data['branchid'];
                $newPassengerData['invite_id'] = $branchR->data['branchid'];
                $newPassengerData['invite_type'] = \CommonDefine::INVITE_TYPE_1;
            }
        }
        if (isset($_REQUEST['_dep'])) {
            $driverCode = $_REQUEST['_dep'];
            $driverR = $this->find(array('driver_code' => $driverCode), 'DriverQrAttachment');
            if ($driverR->ret) {
                $driverId = $driverR->data['driver_id'];
            }
        }

        $wechat = new \Wechat();
        $url = $wechat->getCodeAccessTokenUrl($code, $cipmchid);
        // 获取商户ID
        $mchid = $this->getMchidByCipmchid($cipmchid);

        // 优化HTTP请求：使用cURL替代file_get_contents
        $data = $this->httpRequest($url);
        if (!$data) {
            $this->error("微信授权请求失败，请稍后再试");
            return;
        }

        /**
         * array (
            'access_token' => '90_ozbayGUpwh453XOfP_FQURnpubSTNVzgYF91B_Htl__uNgeUrl7WVEBNtwfQVwDEeQQd86vOdBJw4BV2k5aWL-37VXPuOKma8Y6mhvFmbeY',
            'expires_in' => 7200,
            'refresh_token' => '90_jn-bBXbwCky5LoSuYT7dE63QgEA7kH_qa7R9od7jWmpGnOwSxbiBL9AeRv_QXJk6HPTRR4FCp-HFTu8Gn_JfJdWHDquDXZIGizOq0tC_iN4',
            'openid' => 'oGLwmwZe7XdkVV6SqHok9imJB-0Y',
            'scope' => 'snsapi_userinfo',
            )
         */
        $arr = json_decode($data, true);

        // 添加JSON解码错误检查
        if (!$arr || json_last_error() !== JSON_ERROR_NONE) {
            $this->error("微信授权数据解析失败，请稍后再试");
            return;
        }

        // 设置token的cookie
        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_pas_token_' . $mchid, $arr['access_token'], time() + $expires_in);

        // 查询用户信息
        if (isset($arr['openid'])) {
            # 拉取用户信息(需scope为 snsapi_userinfo)
            $userInfoUrl = $wechat->getUserInfoUrl($arr['access_token'], $arr['openid']);

            // 优化HTTP请求：使用cURL替代file_get_contents
            $userinfoData = $this->httpRequest($userInfoUrl);
            if (!$userinfoData) {
                $this->error("获取用户信息失败，请稍后再试");
                return;
            }

            $userInfoArr = json_decode($userinfoData, true);

            // 添加JSON解码错误检查
            if (!$userInfoArr || json_last_error() !== JSON_ERROR_NONE) {
                $this->error("用户信息解析失败，请稍后再试");
                return;
            }

            // 查询用户信息
            $r = $this->find(array('openid' => $arr['openid'], 'mchid' => $mchid), 'Passenger', 'passenger_id,is_freeze,cellphone');
            if ($r->ret) {
                if (intval($r->data['is_freeze']) === 1) {
                    $this->error("该用户帐号已被删除，请联系管理员");
                    return;
                }
                $passengerId = $r->data['passenger_id'];
                //获取乘客三方账号信息
                $thirdR = $this->find(array('passenger_id' => $r->data['passenger_id']), 'ThirdParty', 'id,third_avatar,third_account,uuid');
                if (!$thirdR->ret) {
                    $this->add(
                        'ThirdParty',
                        array(
                            'passenger_id' => $r->data['passenger_id'],
                            'uuid' => $userInfoArr['openid'],
                            'mchid' => $mchid,
                            'third_avatar' => $userInfoArr['headimgurl'],
                            'third_account' => $userInfoArr['nickname'],
                            'type' => 2
                        )
                    );
                    $this->save(
                        'Passenger',
                        array(
                            'passenger_id' => $r->data['passenger_id'],
                            'name' => $userInfoArr['nickname'],
                            'openid' => $userInfoArr['openid']
                        )
                    );
                    $passengerInfo = array(
                        'openid' => $userInfoArr['openid'],
                        'passenger_id' => $r->data['passenger_id'],
                        'mobile' => $r->data['cellphone'],
                        'avatar' => $userInfoArr['headimgurl'],
                        'nickname' => $userInfoArr['nickname'],
                        'cid' => $cid,
                        'channel_branch_id' => $channelBranchId,
                        'qr_code' => $qrCode,
                        'driver_id' => $driverId,
                    );
                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                } else {
                    $passengerInfo = array(
                        'openid' => $arr['openid'],
                        'passenger_id' => $r->data['passenger_id'],
                        'mobile' => $r->data['cellphone'],
                        'avatar' => $userInfoArr['headimgurl'],
                        'nickname' => $userInfoArr['nickname'],
                        'cid' => $cid,
                        'channel_branch_id' => $channelBranchId,
                        'qr_code' => $qrCode,
                        'driver_id' => $driverId,
                    );
                    if ($r->data['openid'] != $arr['openid']) {
                        $this->save('Passenger', array('passenger_id' => $r->data['passenger_id'], 'openid' => $arr['openid']));
                    }
                    # 更新用户第三方授权信息
                    $thirdPartyUser = array(
                        'id' => $thirdR->data['id'],
                        'passenger_id' => $r->data['passenger_id'],
                        'uuid' => $arr['openid'],
                        'third_avatar' => $userInfoArr['headimgurl'],
                        'third_account' => $userInfoArr['nickname'],
                    );
                    $this->save('ThirdParty', $thirdPartyUser);

                    \StateModel::save($r->data['passenger_id'], "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                }
                //输出用户信息到页面
                $this->assign('cellphone', $r->data['cellphone']);
            } else {
                if (isset($userInfoArr['openid']) && $userInfoArr['openid']) {
                    $passengerR = $this->find(array('openid' => $userInfoArr['openid'], 'mchid' => $mchid), 'Passenger', 'passenger_id, cellphone');
                    $this->startTrans();
                    $passengerId = "";
                    if (!$passengerR->ret) {

                        //创建新用户
                        $newPassengerData['openid'] = $arr['openid'];
                        $newPassengerData['openid_pay'] = $arr['openid'];
                        $newPassengerData['mchid'] = $mchid;
                        $newPassengerData['name'] = $userInfoArr['nickname'];
                        $passengerR = $this->add('Passenger', $newPassengerData);
                        $passengerId = $passengerR->data;
                    } else {
                        $passengerId = $passengerR->data['passenger_id'];
                    }
                    //添加三方账号信息
                    if ($passengerR->ret) {
                        $thirdR = $this->add('ThirdParty', array('passenger_id' => $passengerId, 'uuid' => $userInfoArr['openid'], 'mchid' => $mchid, 'third_avatar' => $userInfoArr['headimgurl'], 'third_account' => $userInfoArr['nickname'], 'type' => 2));
                        if ($thirdR->ret) {
                            $passengerInfo = array(
                                'passenger_id' => $passengerId,
                                'mobile' => $passengerR->data['cellphone'],
                                'avatar' => $userInfoArr['headimgurl'],
                                'nickname' => $userInfoArr['nickname'],
                                'openid' => $userInfoArr['openid'],
                                'driver_id' => $driverId,
                                'cid' => $cid,
                                'channel_branch_id' => $channelBranchId,
                                'qr_code' => $qrCode,
                            );
                            \StateModel::save($passengerId, "", \StateModel::$PASSENGER, $passengerInfo, $mchid);
                            $this->commitTrans();
                        } else {
                            $this->rollbackTrans();
                        }
                    } else {
                        $this->rollbackTrans();
                    }
                }
            }
        } else {
            //授权异常处理
            $this->error("微信用户授权获取openid失败，请稍后再试");
            return;
        }

        // 从请求参数中移除code和state，避免重复授权
        $requestParams = $_REQUEST;
        unset($requestParams['code']);
        unset($requestParams['state']);
        unset($requestParams['callback']);
        if (isset($this->state->user_id)) {
            $separator = strpos($requestParams['redirect_uri'], '?') !== false ? '&' : '?';
            $url = $requestParams['redirect_uri'] . $separator . 'callback=' . $cipmchid . '&' . http_build_query($requestParams);
            // 使用HTTP 302重定向，更快
            header("Location: " . $url, true, 302);
            exit();
        } else {
            $url = C('WEB_ROOT') . 'passenger_index?callback=' . $cipmchid . '&' . http_build_query($requestParams);
            // 使用HTTP 302重定向，更快
            header("Location: " . $url, true, 302);
            exit();
        }
    }

    /**
     * HTTP请求优化方法（保守优化）
     */
    private function httpRequest($url, $timeout = 5)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; WechatAuth/1.0)'
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ($httpCode == 200 && $result !== false) ? $result : false;
    }

    public function addWebRootIfMissing($url, $webRoot)
    {
        // 检查URL是否包含webRoot
        if (strpos($url, $webRoot) === false) {
            // 如果没有，补充webRoot
            $url = rtrim($webRoot, '/') . '/' . ltrim($url, '/');
        }
        return $url;
    }

    public function orderhistoryPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!isset($this->state->user_id)) {
            //授权检测
            $this->_wxAuth($this->mchid, C('WEB_ROOT') . 'passenger_order_history');
        }

        $this->getMchInfoToPageByMchid($this->mchid);
        # 商户已开通服务
        $this->assign('nav_config_ids', $this->defineMerchantNavigationMenu($this->mchid));
        $this->assiginFapiaoUrl();
        $this->checkServiceEnable();
        // 查询底部导航显隐配置
        $bottomNavigationEnabled = $this->checkBottomNavigationEnabled();
        $this->assign('bottom_navigation_enabled', $bottomNavigationEnabled);
        $hasOpened = false;
        foreach ($bottomNavigationEnabled as $value) {
            if ($value == 1) {
                $hasOpened = true;
                break;
            }
        }
        if (!$hasOpened) {
            $this->assign('bottom_navigation_closed_all', 1);
        } else {
            $this->assign('bottom_navigation_closed_all', 0);
        }
        $this->display('Tpl/Passenger/Index/order_history.html');
    }

    public function assiginFapiaoUrl()
    {
        /**
         * @Date 2022.07.13
         * <AUTHOR>
         * @version 3.3
         */
        $enabled = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/fapiao/enabled", 'get');
        $response = json_decode($enabled, true);
        if (
            $response['status'] == 'success'
            && $response['data']['fapiao'] == true
        ) {
            $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/passenger/account_ids/{$this->state->user_id}/access_token", 'post');
            $token = json_decode($tokenResponse, true);
            if (
                $token['status'] == 'success'
                && $token['data']['access_token']
            ) {
                $this->assign('fapiao', 1);
                $this->assign('fapiao_url', sprintf('%smicro-apps/invoice/?callback=%s&token=%s', C('WEB_ROOT'), $this->getCipmchidByMchid($this->mchid)->data['ciphertext'], $token['data']['access_token']));
            }
        } else {
            $this->assign('fapiao_url', '');
            $this->assign('fapiao', 0);
        }
    }

    /**
     * 商户配置信息
     *
     * @param integer $mchid
     * @return void
     */
    private function getMchInfoToPageByMchid($mchid)
    {
        $mchInfo = $this->getMchInfoByMchid($mchid);
        $mchWechatQrR = $this->getMchWechatQr($mchid);
        $this->assign('mchname', $mchInfo->data['mchname']);
        $this->assign('mchqrurl', $mchWechatQrR->ret ? $mchWechatQrR->data['mch_wechat_qr'] : '');
        $this->assign('callback', $this->getCipmchidByMchid($mchid)->data['ciphertext']);
        $this->assign('servicecall', $mchInfo->ret ? $mchInfo->data['tel'] : "");
        $packageR = $this->find(array('package_id' => $mchInfo->data['package_id']), 'Package', 'tech_support');
        if ($packageR->ret) {
            if ($packageR->data['tech_support'] == 1) {
                $this->assign('tech_support', 1);
            } else {
                $this->assign('tech_support', 2);
            }
            $this->assign('tech_text');
            if ($mchid == 796) {
                $this->assign('tech_text', "穆青社服务中心");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 903) {
                $this->assign('tech_text', "长沙优通网络科技");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 942) {
                $this->assign('tech_text', "一步顺风车");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 969) {
                $this->assign('tech_text', "走起网约车");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1018) {
                $this->assign('tech_text', "搭顺车出行");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1032) {
                $this->assign('tech_text', "自由环球旅行");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 937) {
                $this->assign('tech_text', "安厦泉拼车平台");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1050) {
                $this->assign('tech_text', "成乐出行");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1075) {
                $this->assign('tech_text', "鑫约旅行");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1116) {
                $this->assign('tech_text', "斑马裕众");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1135) {
                $this->assign('tech_text', "石首商务车");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1170) {
                $this->assign('tech_text', "享约就约");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1174) {
                $this->assign('tech_text', "滴答城际出行");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1181) {
                $this->assign('tech_text', "帮邦行-奥通");
                $this->assign('tech_support', 3);
            }

            if ($mchid == 1258) {
                $this->assign('tech_text', "信泰出行");
                $this->assign('tech_support', 3);
            }
            if ($mchid == 1289) {
                $this->assign('tech_text', "陈州快车");
                $this->assign('tech_support', 3);
            }
        } else {
            $this->assign('tech_support', 0);
        }
    }

    private function _userInfo($mchid)
    {
        // 优先检查URL中是否有_dep参数
        $currentDriverId = $channelBranchId = $cid = $qrCode = '';
        if (isset($_REQUEST['_dep'])) {
            $driverCode = $_REQUEST['_dep'];
            $driverR = $this->find(array('driver_code' => $driverCode), 'DriverQrAttachment');
            if ($driverR->ret) {
                $currentDriverId = $driverR->data['driver_id'];
            }
        }

        // 用户邀请渠道分台ID
        if (isset($_REQUEST['_cbp'])) {
            $branchCode = $_REQUEST['_cbp'];
            $branchR = $this->find(array('branch_code' => $branchCode), 'BranchQrAttachment');
            if ($branchR->ret) {
                $channelBranchId = $branchR->data['branchid'];
            }
        }
        //cid
        if (isset($_REQUEST['cid'])) {
            $cid = $_REQUEST['cid'];
        }
        //_qr
        if (isset($_REQUEST['_qr'])) {
            $qrCode = $_REQUEST['_qr'];
        }

        if (isset($this->state->user_id)) {
            $this->assign('cellphone', $this->state->data['mobile']);
            $this->assign('openid', $this->state->data['openid']);
            $this->assign('openid-pay', $this->state->data['openid']);
            $this->assign('uid', $this->state->user_id);
            $this->assign('name', $this->state->data['nickname']);
            $this->assign('avatar', $this->state->data['avatar']);
            $this->assign('passenger_id', $this->state->user_id);

            $passengerR = $this->find(array('passenger_id' => $this->state->user_id, 'mchid' => $mchid), 'Passenger', 'passenger_id, cellphone, openid_pay', 'cash_balances', 'examine_status');
            if (!empty($passengerR->data['examine_status'])) {
                $passengerTypeR = $this->find(array('passenger_type_id' => $passengerR->data['']), 'PassengerType');
                $this->assign('passenger_type_name', $passengerTypeR->data['name']);
            }

            $this->assign('examine_status', $passengerR->data['examine_status']);
            // 使用当前URL中的driver_id（如果有），否则使用session中的值
            $this->assign('driver_id', $currentDriverId ?: $this->state->data['driver_id']);
            // 使用当前URL中的channel_branch_id（如果有），否则使用session中的值
            $this->assign('channel_branch_id', $channelBranchId ?: $this->state->data['channel_branch_id']);
            // 使用当前URL中的cid（如果有），否则使用session中的值
            $this->assign('cid', $cid ?: $this->state->data['cid']);
            // 使用当前URL中的qr_code（如果有），否则使用session中的值
            $this->assign('qr_code', $qrCode ?: $this->state->data['qr_code']);
        }
    }

    public function clearCachePage()
    {
        \StateModel::clear(\StateModel::$PASSENGER, $this->mchid);
        echo "清除缓存成功";
        die;
    }

    /**
     * @param $short_route 当前短路由
     */
    private function _setCurrentHomePage($short_route)
    {
        $this->assign('current_home_page', C('WEB_ROOT') . $short_route . '/callback/' . $this->getCipmchidByMchid($this->mchid)->data['ciphertext']);
    }

    /**
     * 配置商户信息
     */
    private function _setMerchantConfig()
    {
        $systemConfigR = $this->find(array('key' => \CommonDefine::ID_UPLOAD_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1, 'account_id' => $this->mchid), 'SystemConfig');
        $this->assign('id_upload_config', $systemConfigR->ret ? $systemConfigR->data['value'] : \CommonDefine::ID_UPLOAD_CONFIG_0);
        $lineClassFerryServiceR = $this->find(['mchid' => $this->mchid], 'LineClassFerryService');
        $this->assign('line_class_ferry', $lineClassFerryServiceR->ret ? 1 : 0);

        if ($this->mchid == 1174 || $this->mchid == 181) {
            $this->assign('hideCon', '');
            $this->assign('showCon', '');
            $this->assign('childbuyinfo', 1);
        } else {
            $this->assign('hideCon', 'hideCon');
            $this->assign('showCon', 'showCon');
        }
    }

    /**
     * 添加辅助方法，统一检查授权状态
     *
     * @param int $mchid 商户ID
     * @param string $redirectUrl 授权后重定向URL
     * @return bool 是否已授权
     */
}

<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 线路模块
 *
 * <AUTHOR>
 */
class LineAction extends \PassengerCoreAction
{
    /**
     * 获取拼车线路
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLines($start_province = null, $start_city = null, $start_area = null, $end_province = null, $end_city = null, $end_area = null, $start_address_code = null, $end_address_code = null, $page = 1, $size = 200)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_0;
        $where['status'] = 1;
        if ($start_address_code) {
            $where['start_address_code'] = $start_address_code;
        }
        if ($end_address_code) {
            $where['end_address_code'] = $end_address_code;
        }

        $start_address_code = $this->getAddressCodeByName($start_province, $start_city, $start_area);
        if ($start_address_code) {
            $where['start_address_code'] = $start_address_code;
        }

        $end_address_code = $this->getAddressCodeByName($end_province, $end_city, $end_area);
        if ($end_address_code) {
            $where['end_address_code'] = $end_address_code;
        }

        $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary,start_appointment_time,end_appointment_time,business_time_type,set_order_time,center_start_latlng,center_end_latlng');
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                if ($this->mchid == 1018) {
                    $r->data[$k]['tel'] = 96168;
                } elseif ($this->mchid == 1238) {
                    $r->data[$k]['tel'] = "0851-26220058";
                }
                unset($r->data[$k]['branchid']);
                if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }
            }
        } else {
            $start_address_code = $this->getAddressCodeByName($start_province, $start_city);
            if ($start_address_code) {
                $where['start_address_code'] = $start_address_code;
            }

            $end_address_code = $this->getAddressCodeByName($end_province, $end_city);
            if ($end_address_code) {
                $where['end_address_code'] = $end_address_code;
            }

            $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary,start_appointment_time,end_appointment_time,business_time_type,set_order_time,center_start_latlng,center_end_latlng');
            if ($r->ret) {
                foreach ($r->data as $k => $v) {
                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                    $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                    if ($this->mchid == 1018) {
                        $r->data[$k]['tel'] = 96168;
                    } elseif ($this->mchid == 1238) {
                        $r->data[$k]['tel'] = "0851-26220058";
                    }
                    unset($r->data[$k]['branchid']);
                    if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                        $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                        $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                    }
                }
            } else {
                return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
            }
        }
        return $this->output($r);
    }

    /**
     * 根据地址信息自动匹配拼车线路
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 出发地位置经度
     * @param string $start_latitude 出发地位置纬度
     * @param string $end_longitude 出发地位置经度
     * @param string $end_latitude 出发地位置纬度
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAutoLines($start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $page = 1, $size = 200)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_0;
        $where['status'] = 1;

        if ($start_address_code) {
            $where['start_address_code'] = $start_address_code;
        } else {
            return $this->output(new \ResultModel(false, '定位失败，请手动选择位置'));
        }

        if ($end_address_code) {
            $where['end_address_code'] = $end_address_code;
        } else {
            return $this->output(new \ResultModel(false, '请选择目的地'));
        }
        $fields = 'id, branchid, start_name, end_name,price,summary,start_appointment_time,end_appointment_time,business_time_type,set_order_time,center_start_latlng,center_end_latlng,area_radius_start,area_lng_start,area_lat_start,area_radius_end,area_lng_end,area_lat_end';

        //具体的code查询
        $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                if ($this->mchid == 1018) {
                    $r->data[$k]['tel'] = 96168;
                }
                unset($r->data[$k]['branchid']);
                if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }

                if (!empty($v['area_radius_start'])) {
                    if (!empty($start_longitude) && !empty($start_latitude)) {
                        $pointCalculationObj = new \PointCalculation();
                        $circle['center']['lat'] = $v['area_lat_start'];
                        $circle['center']['lng'] = $v['area_lng_start'];
                        $circle['radius'] = $v['area_radius_start'];
                        if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                            unset($r->data[$k]);
                            continue;
                        }
                    }
                }

                if (!empty($v['area_radius_end'])) {
                    if (!empty($end_longitude) && !empty($end_latitude)) {
                        $pointCalculationObj = new \PointCalculation();
                        $circle['center']['lat'] = $v['area_lat_end'];
                        $circle['center']['lng'] = $v['area_lng_end'];
                        $circle['radius'] = $v['area_radius_end'];
                        if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                            unset($r->data[$k]);
                            continue;
                        }
                    }
                }
            }
        } else {
            //判断目的地是否是区
            if ($this->getCodeProtity($end_address_code) == 3) { //目的地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($end_address_code);
                if ($cityInnerCode) {
                    $where['end_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                    if ($r->ret) {
                        foreach ($r->data as $k => $v) {
                            $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                            $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                            if ($this->mchid == 1018) {
                                $r->data[$k]['tel'] = 96168;
                            }
                            unset($r->data[$k]['branchid']);
                            if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                            }

                            if (!empty($v['area_radius_start'])) {
                                if (!empty($start_longitude) && !empty($start_latitude)) {
                                    $pointCalculationObj = new \PointCalculation();
                                    $circle['center']['lat'] = $v['area_lat_start'];
                                    $circle['center']['lng'] = $v['area_lng_start'];
                                    $circle['radius'] = $v['area_radius_start'];
                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                        unset($r->data[$k]);
                                        continue;
                                    }
                                }
                            }

                            if (!empty($v['area_radius_end'])) {
                                if (!empty($end_longitude) && !empty($end_latitude)) {
                                    $pointCalculationObj = new \PointCalculation();
                                    $circle['center']['lat'] = $v['area_lat_end'];
                                    $circle['center']['lng'] = $v['area_lng_end'];
                                    $circle['radius'] = $v['area_radius_end'];
                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                        unset($r->data[$k]);
                                        continue;
                                    }
                                }
                            }
                        }
                    } else {
                        if ($this->getCodeProtity($start_address_code) == 3) { //开始地城市详细区域，匹配市辖区
                            $startCityInnerCode = $this->getCityInnerCode($start_address_code);
                            if ($startCityInnerCode) {
                                $where['start_address_code'] = $startCityInnerCode;
                                $where['end_address_code'] = $end_address_code;
                                $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                                if ($r->ret) {
                                    foreach ($r->data as $k => $v) {
                                        $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                        $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                        $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                        unset($r->data[$k]['branchid']);
                                        if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                            $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                            $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                                        }

                                        if (!empty($v['area_radius_start'])) {
                                            if (!empty($start_longitude) && !empty($start_latitude)) {
                                                $pointCalculationObj = new \PointCalculation();
                                                $circle['center']['lat'] = $v['area_lat_start'];
                                                $circle['center']['lng'] = $v['area_lng_start'];
                                                $circle['radius'] = $v['area_radius_start'];
                                                if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                                    unset($r->data[$k]);
                                                    continue;
                                                }
                                            }
                                        }

                                        if (!empty($v['area_radius_end'])) {
                                            if (!empty($end_longitude) && !empty($end_latitude)) {
                                                $pointCalculationObj = new \PointCalculation();
                                                $circle['center']['lat'] = $v['area_lat_end'];
                                                $circle['center']['lng'] = $v['area_lng_end'];
                                                $circle['radius'] = $v['area_radius_end'];
                                                if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                                    unset($r->data[$k]);
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $where['end_address_code'] = $cityInnerCode;
                                    $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                                    if ($r->ret) {
                                        foreach ($r->data as $k => $v) {
                                            $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                            $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                            $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                            if ($this->mchid == 1018) {
                                                $r->data[$k]['tel'] = 96168;
                                            }
                                            unset($r->data[$k]['branchid']);
                                            if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                                $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                                $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                                            }

                                            if (!empty($v['area_radius_start'])) {
                                                if (!empty($start_longitude) && !empty($start_latitude)) {
                                                    $pointCalculationObj = new \PointCalculation();
                                                    $circle['center']['lat'] = $v['area_lat_start'];
                                                    $circle['center']['lng'] = $v['area_lng_start'];
                                                    $circle['radius'] = $v['area_radius_start'];
                                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                                        unset($r->data[$k]);
                                                        continue;
                                                    }
                                                }
                                            }

                                            if (!empty($v['area_radius_end'])) {
                                                if (!empty($end_longitude) && !empty($end_latitude)) {
                                                    $pointCalculationObj = new \PointCalculation();
                                                    $circle['center']['lat'] = $v['area_lat_end'];
                                                    $circle['center']['lng'] = $v['area_lng_end'];
                                                    $circle['radius'] = $v['area_radius_end'];
                                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                                        unset($r->data[$k]);
                                                        continue;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } elseif ($this->getCodeProtity($start_address_code) == 3) { //开始地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($start_address_code);
                if ($cityInnerCode) {
                    $where['start_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                    if ($r->ret) {
                        foreach ($r->data as $k => $v) {
                            $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                            $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                            if ($this->mchid == 1018) {
                                $r->data[$k]['tel'] = 96168;
                            }
                            unset($r->data[$k]['branchid']);
                            if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                            }

                            if (!empty($v['area_radius_start'])) {
                                if (!empty($start_longitude) && !empty($start_latitude)) {
                                    $pointCalculationObj = new \PointCalculation();
                                    $circle['center']['lat'] = $v['area_lat_start'];
                                    $circle['center']['lng'] = $v['area_lng_start'];
                                    $circle['radius'] = $v['area_radius_start'];
                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                        unset($r->data[$k]);
                                        continue;
                                    }
                                }
                            }

                            if (!empty($v['area_radius_end'])) {
                                if (!empty($end_longitude) && !empty($end_latitude)) {
                                    $pointCalculationObj = new \PointCalculation();
                                    $circle['center']['lat'] = $v['area_lat_end'];
                                    $circle['center']['lng'] = $v['area_lng_end'];
                                    $circle['radius'] = $v['area_radius_end'];
                                    if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                        unset($r->data[$k]);
                                        continue;
                                    }
                                }
                            }
                        }
                    } else {
                        $endCityInnerCode = $this->getCityInnerCode($end_address_code);
                        if ($endCityInnerCode) {
                            $where['start_address_code'] = $start_address_code;
                            $where['end_address_code'] = $endCityInnerCode;
                            $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                            if ($r->ret) {
                                foreach ($r->data as $k => $v) {
                                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                    $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                    $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                    if ($this->mchid == 1018) {
                                        $r->data[$k]['tel'] = 96168;
                                    }
                                    unset($r->data[$k]['branchid']);
                                    if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                        $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                        $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                                    }

                                    if (!empty($v['area_radius_start'])) {
                                        if (!empty($start_longitude) && !empty($start_latitude)) {
                                            $pointCalculationObj = new \PointCalculation();
                                            $circle['center']['lat'] = $v['area_lat_start'];
                                            $circle['center']['lng'] = $v['area_lng_start'];
                                            $circle['radius'] = $v['area_radius_start'];
                                            if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                                unset($r->data[$k]);
                                                continue;
                                            }
                                        }
                                    }

                                    if (!empty($v['area_radius_end'])) {
                                        if (!empty($end_longitude) && !empty($end_latitude)) {
                                            $pointCalculationObj = new \PointCalculation();
                                            $circle['center']['lat'] = $v['area_lat_end'];
                                            $circle['center']['lng'] = $v['area_lng_end'];
                                            $circle['radius'] = $v['area_radius_end'];
                                            if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                                unset($r->data[$k]);
                                                continue;
                                            }
                                        }
                                    }
                                }
                            } else {
                                $where['start_address_code'] = $cityInnerCode;
                                $r = $this->select($where, $page, $size, 'create_time desc', 'Line', $fields);
                                if ($r->ret) {
                                    foreach ($r->data as $k => $v) {
                                        $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                        $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                        $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                        if ($this->mchid == 1018) {
                                            $r->data[$k]['tel'] = 96168;
                                        }
                                        unset($r->data[$k]['branchid']);
                                        if ($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                                            $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                                            $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                                        }

                                        if (!empty($v['area_radius_start'])) {
                                            if (!empty($start_longitude) && !empty($start_latitude)) {
                                                $pointCalculationObj = new \PointCalculation();
                                                $circle['center']['lat'] = $v['area_lat_start'];
                                                $circle['center']['lng'] = $v['area_lng_start'];
                                                $circle['radius'] = $v['area_radius_start'];
                                                if (!$pointCalculationObj->is_point_in_circle(['lat' => $start_latitude, 'lng' => $start_longitude], $circle)) {
                                                    unset($r->data[$k]);
                                                    continue;
                                                }
                                            }
                                        }

                                        if (!empty($v['area_radius_end'])) {
                                            if (!empty($end_longitude) && !empty($end_latitude)) {
                                                $pointCalculationObj = new \PointCalculation();
                                                $circle['center']['lat'] = $v['area_lat_end'];
                                                $circle['center']['lng'] = $v['area_lng_end'];
                                                $circle['radius'] = $v['area_radius_end'];
                                                if (!$pointCalculationObj->is_point_in_circle(['lat' => $end_latitude, 'lng' => $end_longitude], $circle)) {
                                                    unset($r->data[$k]);
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
            }
        }

        if (!$r->ret) {
            $r->data =  '即将开通该线路，敬请期待';
        } else {
            $r->count = count($r->data);
        }

        return $this->output($r);
    }


    /**
     * 获取电话叫车线路
     * @param string $start_name 出发地
     * @param string $end_name 目的地
     * @param string $longitude 当前位置经度
     * @param string $latitude 当前位置纬度
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getPhoneLines($start_name = null, $end_name = null, $longitude = null, $latitude = null, $page = 1, $size = 10)
    {
        $ret = new \ResultModel(false);
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['is_phone_line'] = 1;
        $where['status'] = 1;
        if (!empty($start_name)) {
            $where['start_name'] = ['like', "%" . $start_name . "%"];
        }
        if (!empty($end_name)) {
            $where['end_name'] = ['like', "%" . $end_name . "%"];
        }

        //        $this->add('Log', array('Operation_content' => $longitude.",".$latitude, 'operation_time' => time(), 'admin_id' => $this->state->user_id, 'data' => []));
        /*        if(empty($longitude) || empty($latitude)){
                    return $this->output(new \ResultModel(false));
                }*/
        $lineData = [];
        $lineCount = 0;
        $lineMode = new \LineModel();
        $fields = "id, branchid, start_name, end_name,price,summary,is_phone_line_verify";
        $order = 'create_time desc';
        if (!empty($longitude) && !empty($latitude)) {
            $fields .= (",GETDISTANCE(center_start_lat,center_start_lng,$latitude,$longitude) AS distance");
            $order = ('distance asc,' . $order);
            $lineData = $lineMode->where($where)->field($fields)->having("distance != ''")->order($order)->page($page, $size)->select();
            $where['center_start_latlng'] = array('exp', '!= ""');
            $lineCount = $lineMode->where($where)->field($fields)->count();
        } else {
            $lineData = $lineMode->where($where)->field($fields)->order($order)->page($page, $size)->select();
            $lineCount = $lineMode->where($where)->field($fields)->order($order)->count();
        }

        if (!empty($lineData)) {
            foreach ($lineData as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $lineData[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                unset($lineData[$k]['branchid']);
                if ($lineData[$k]['start_appointment_time'] && $lineData[$k]['end_appointment_time']) {
                    $lineData[$k]['start_appointment_time'] = substr($lineData[$k]['start_appointment_time'], 0, 5);
                    $lineData[$k]['end_appointment_time'] = substr($lineData[$k]['end_appointment_time'], 0, 5);
                }

                $lineData[$k]['drivers'] = [];
                $driverWhere['dl.line_id'] = $lineData[$k]['id'];
                $driverWhere['dl.type'] = \CommonDefine::ORDER_TYPE_1;
                $driversR = M()->table('cp_driver_line dl')
                    ->join('LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id')
                    ->where($driverWhere)
                    ->field('d.driver_id,d.name,d.cellphone')
                    ->select();

                if (!empty($driversR)) {
                    $lineData[$k]['drivers'] = $driversR;
                }

                if ($this->mchid == 1018) {
                    $lineData[$k]['drivers'][]  = ['cellphone' => 96168, 'name' => '搭顺车出行', 'driver_id' => 0];
                }
            }
            $ret->ret = true;
            $ret->data = $lineData;
            $ret->count = $lineCount;
        }


        return $this->output($ret);
    }


    /**
     * 根据地址信息自动匹配包车线路
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAutoCharteredLines($start_address_code = null, $end_address_code = null, $page = 1, $size = 200)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['status'] = 1;

        if ($start_address_code) {
            if (in_array(substr($start_address_code, 0, 2) . "0000", $this->directlyCityArr)) {
                $where['start_province_code'] = substr($start_address_code, 0, 2) . "0000";
            } else {
                $where['start_city_code'] = substr($start_address_code, 0, 4) . "00";
            }
        } else {
            return $this->output(new \ResultModel(false, '定位失败，请手动选择位置'));
        }
        if ($end_address_code) {
            if (in_array(substr($end_address_code, 0, 2) . "0000", $this->directlyCityArr)) {
                $where['end_province_code'] = substr($end_address_code, 0, 2) . "0000";
            } else {
                $where['end_city_code'] = substr($end_address_code, 0, 4) . "00";
            }
        } else {
            return $this->output(new \ResultModel(false, '请选择目的地'));
        }

        $fields = 'id, branchid, start_name, end_name,summary,center_start_latlng,center_end_latlng,business_time_type,start_appointment_time,end_appointment_time,set_order_time';
        //具体的code查询
        $r = $this->select($where, $page, $size, null, 'LineChartered', $fields);

        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                unset($r->data[$k]['branchid']);
            }
        }
        /*自动匹配
        else {
            //判断目的地是否是区
            if($this->getCodeProtity($end_address_code) == 3){//目的地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($end_address_code);
                if($cityInnerCode){
                    $where['end_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered', $fields);
                    if($r->ret){
                        foreach($r->data as $k => $v){
                            $branchR = $this->find(array('admin_id' => $v['branchid']),'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret?$branchR->data['mchname']:"";
                            $r->data[$k]['tel'] = $branchR->ret?$branchR->data['cellphone']:"暂无";
                            $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                            unset($r->data[$k]['branchid']);
                        }
                    }else{
                        if($this->getCodeProtity($start_address_code) == 3){//开始地城市详细区域，匹配市辖区
                            $startCityInnerCode = $this->getCityInnerCode($start_address_code);
                            if($startCityInnerCode){
                                $where['end_address_code'] = $end_address_code;
                                $where['start_address_code'] = $startCityInnerCode;
                                $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered', $fields);
                                if($r->ret){
                                    foreach($r->data as $k => $v){
                                        $branchR = $this->find(array('admin_id' => $v['branchid']),'Admin');
                                        $r->data[$k]['branchname'] = $branchR->ret?$branchR->data['mchname']:"";
                                        $r->data[$k]['tel'] = $branchR->ret?$branchR->data['cellphone']:"暂无";
                                        $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                                        unset($r->data[$k]['branchid']);
                                    }
                                }else{
                                    $where['end_address_code'] = $cityInnerCode;
                                    $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered', $fields);
                                    if($r->ret){
                                        foreach($r->data as $k => $v){
                                            $branchR = $this->find(array('admin_id' => $v['branchid']),'Admin');
                                            $r->data[$k]['branchname'] = $branchR->ret?$branchR->data['mchname']:"";
                                            $r->data[$k]['tel'] = $branchR->ret?$branchR->data['cellphone']:"暂无";
                                            $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                                            unset($r->data[$k]['branchid']);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }else if($this->getCodeProtity($start_address_code) == 3){//开始地城市详细区域，匹配市辖区
                $cityInnerCode = $this->getCityInnerCode($start_address_code);
                if($cityInnerCode){
                    $where['start_address_code'] = $cityInnerCode;
                    $r = $this->select($where, $page, $size, 'create_time desc', $fields);
                    if($r->ret){
                        foreach($r->data as $k => $v){
                            $branchR = $this->find(array('admin_id' => $v['branchid']),'Admin');
                            $r->data[$k]['branchname'] = $branchR->ret?$branchR->data['mchname']:"";
                            $r->data[$k]['tel'] = $branchR->ret?$branchR->data['cellphone']:"暂无";
                            $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                            unset($r->data[$k]['branchid']);
                        }
                    }else{
                        $endCityInnerCode = $this->getCityInnerCode($end_address_code);
                        if($endCityInnerCode) {
                            $where['start_address_code'] = $start_address_code;
                            $where['end_address_code'] = $endCityInnerCode;
                            $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered', $fields);
                            if ($r->ret) {
                                foreach ($r->data as $k => $v) {
                                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                    $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                    $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                    $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                                    unset($r->data[$k]['branchid']);
                                }
                            }else{
                                $where['start_address_code'] = $cityInnerCode;
                                $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered', $fields);
                                if ($r->ret) {
                                    foreach ($r->data as $k => $v) {
                                        $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                                        $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                                        $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                                        $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                                        unset($r->data[$k]['branchid']);
                                    }
                                }
                            }
                        }
                    }
                }
            }else{
                return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
            }
        }*/

        if (!$r->ret) {
            $r->data =  '即将开通该线路，敬请期待';
        }

        return $this->output($r);
    }

    /**
     * 快车线路列表
     *
     * @param string $cid
     * @param string $start_address_code
     * @param string $end_address_code
     * @return void
     */
    public function queryKuaicheLine($cid = '', $start_address_code = '', $end_address_code = '', $start_city_name = '', $start_area_code = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
            'cid' => $cid,
            'start_city_name' => urlencode($start_city_name),
            'start_area_code' => $start_area_code
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/kuaiche/lines", 'get', $params, $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('快车线路列表查询失败：%s', $results['message']);
        }
        return $this->output($ret);
    }

    /**
     * 拼车线路列表
     *
     * @param string $cid
     * @param string $start_address_code
     * @param string $end_address_code
     * @return void
     */
    public function queryPincheLine($cid = '', $start_address_code = '', $end_address_code = '', $start_city_name = '', $end_city_name = '', $start_area_code = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
            'cid' => $cid,
            'start_city_name' => urlencode($start_city_name),
            'end_city_name' => urlencode($end_city_name),
            'start_area_code' => $start_area_code
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/pinche/lines", 'get', $params, $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('拼车线路列表查询失败：%s', $results['message']);
        }
        return $this->output($ret);
    }


    /**
     * 根据类型自动匹配拼车线路
     * @param int $cid 场景id（默认为空,为空时获取全部拼车线路）
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAutoLinesByCategory($cid = null, $start_address_code = null, $end_address_code = null, $page = 1, $size = 200)
    {
        $where = " l.mchid = " . $this->mchid;
        $lc = $this->find(array("id" => $cid), 'LineCategory');
        if ($lc->ret) {
            $where .= ' AND lflc.line_category_id = ' . $lc->data['id'];
            $where .= ' AND l.is_del = ' . \CommonDefine::IS_DEL_0;
            $where .= ' AND l.is_show = ' . \CommonDefine::IS_SHOW_1;
            $where .= ' AND l.is_phone_line = ' . \CommonDefine::LINE_PHONE_TYYE_0;
            $fields = 'l.id, l.branchid, l.start_name, l.end_name,l.price, l.summary, l.start_address_code, l.end_address_code,l.start_appointment_time,l.end_appointment_time,l.business_time_type,l.set_order_time,l.start_province_code,l.start_city_code,l.start_area_code,l.end_province_code,l.end_city_code,l.end_area_code,l.start_lng,l.start_lat,l.end_lng,l.end_lat,l.area_radius_start,l.area_lng_start,l.area_lat_start,l.area_radius_end,l.area_lng_end,l.area_lat_end,l.center_start_latlng,l.center_start_lat,l.center_start_lng,l.center_end_latlng';

            $lineArr = M()->table('cp_line_fk_line_category lflc')
                ->join('LEFT JOIN cp_line l ON lflc.line_id = l.id ')
                ->where($where)
                ->page($page, $size)
                ->field($fields)
                ->select();

            $lineCount = M()->table('cp_line_fk_line_category lflc')
                ->join('LEFT JOIN cp_line l ON lflc.line_id = l.id ')
                ->where($where)
                ->count();
            $ret = new \ResultModel(false, '即将开通该线路，敬请期待');
            if (!empty($lineArr)) {
                foreach ($lineArr as $key => $val) {
                    //具体的code查询
                    $branchR = $this->find(array('admin_id' => $val['branchid']), 'Admin');
                    $lineArr[$key]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $lineArr[$key]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                    if ($this->mchid == 1018) {
                        $lineArr[$key]['tel'] = 96168;
                    }
                    $lineArr[$key]['start_name'] = empty($val['start_name']) ? $this->getAddressTitleByCode($val['start_address_code']) : $val['start_name'];
                    $lineArr[$key]['en.d_name']   = empty($val['end_name']) ? $this->getAddressTitleByCode($val['end_address_code']) : $val['end_name'];
                    $lineArr[$key]['price']      = $val['price'];
                    $lineArr[$key]['summary']    = $val['summary'];
                    unset($lineArr[$key]['branchid']);
                    if ($lineArr[$key]['start_appointment_time'] && $lineArr[$key]['end_appointment_time']) {
                        $lineArr[$key]['start_appointment_time'] = substr($lineArr[$key]['start_appointment_time'], 0, 5);
                        $lineArr[$key]['end_appointment_time'] = substr($lineArr[$key]['end_appointment_time'], 0, 5);
                    }
                    $lineArr[$key]['start_city_name'] = $this->getAddressTitleByCode($val['start_address_code']);
                    $lineArr[$key]['end_city_name'] = $this->getAddressTitleByCode($val['end_address_code']);
                    $lineArr[$key]['start_area_name'] = $this->getAddressTitleByCode($val['start_area_code']);
                    $lineArr[$key]['end_area_name'] = $this->getAddressTitleByCode($val['end_area_code']);
                }
                $ret->ret = true;
                $ret->data = $lineArr;
                $ret->count = $lineCount;
            }
        } else {
            unset($where);
            $where['mchid'] = $this->mchid;
            $where['is_del'] = \CommonDefine::IS_DEL_0;
            $where['is_show'] = \CommonDefine::IS_SHOW_1;
            $where['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_0;
            $where['status'] = 1;
            $ret = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary,start_address_code, end_address_code,start_appointment_time,end_appointment_time,business_time_type,set_order_time,start_province_code,start_city_code,start_area_code,end_province_code,end_city_code,end_area_code,start_lng,start_lat,end_lng,end_lat,area_radius_start,area_lng_start,area_lat_start,area_radius_end,area_lng_end,area_lat_end,center_start_latlng,center_start_lat,center_start_lng,center_end_latlng');
            if ($ret->ret) {
                foreach ($ret->data as $k => $v) {
                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                    $ret->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $ret->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                    if ($this->mchid == 1018) {
                        $ret->data[$k]['tel'] = 96168;
                    }
                    unset($ret->data[$k]['branchid']);
                    if ($ret->data[$k]['start_appointment_time'] && $ret->data[$k]['end_appointment_time']) {
                        $ret->data[$k]['start_appointment_time'] = substr($ret->data[$k]['start_appointment_time'], 0, 5);
                        $ret->data[$k]['end_appointment_time'] = substr($ret->data[$k]['end_appointment_time'], 0, 5);
                    }
                    $ret->data[$k]['start_city_name'] = $this->getAddressTitleByCode($v['start_city_code']);
                    $ret->data[$k]['end_city_name'] = $this->getAddressTitleByCode($v['end_city_code']);
                    $ret->data[$k]['start_area_name'] = $this->getAddressTitleByCode($v['start_area_code']);
                    $ret->data[$k]['end_area_name'] = $this->getAddressTitleByCode($v['end_area_code']);
                }
            }
        }

        return $this->output($ret);
    }

    /**
     * 根据类型自动匹配包车线路
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAutoLinesCharteredByCategory($cid = null, $start_address_code = null, $end_address_code = null, $page = 1, $size = 200)
    {
        $lc = $this->find(array("id" => $cid), 'LineCategory');

        if (!$lc->ret) {
            return $this->output(new \ResultModel(false, '该路线类型不存在'));
        }

        $where = 'lcflc.line_category_id = ' . $lc->data['id'];
        $where .= ' AND lc.is_del = ' . \CommonDefine::IS_DEL_0;
        $where .= ' AND lc.is_show = ' . \CommonDefine::IS_SHOW_1;
        $fields = 'lc.id, lc.branchid, lc.start_name, lc.end_name, lc.summary, lc.end_address_code, lc.start_address_code,lc.start_appointment_time,lc.end_appointment_time,lc.business_time_type,lc.center_start_latlng,lc.center_end_latlng';
        $LineCharteredArr = M()->table('cp_line_chartered_fk_line_category lcflc')
            ->join('LEFT JOIN cp_line_chartered lc ON lcflc.line_id = lc.id ')
            ->where($where)
            ->page($page, $size)
            ->field($fields)
            ->select();
        $LineCharteredCount = M()->table('cp_line_chartered_fk_line_category lcflc')
            ->join('LEFT JOIN cp_line_chartered lc ON lcflc.line_id = lc.id ')
            ->where($where)
            ->count();

        $ret = new \ResultModel(false, '即将开通该线路，敬请期待');
        if (!empty($LineCharteredArr)) {
            foreach ($LineCharteredArr as $key => $val) {
                //具体的code查询
                $branchR = $this->find(array('admin_id' => $val['branchid']), 'Admin');
                $LineCharteredArr[$key]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $LineCharteredArr[$key]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                $LineCharteredArr[$key]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($val['id']);
                $LineCharteredArr[$key]['start_name'] = empty($val['start_name']) ? $this->getAddressTitleByCode($val['start_address_code']) : $val['start_name'];
                $LineCharteredArr[$key]['end_name'] = empty($val['end_name']) ? $this->getAddressTitleByCode($val['end_address_code']) : $val['end_name'];
                $LineCharteredArr[$key]['summary'] = $val['summary'];
                unset($LineCharteredArr[$key]['branchid']);
                if ($LineCharteredArr[$key]['start_appointment_time'] && $LineCharteredArr[$key]['end_appointment_time']) {
                    $LineCharteredArr[$key]['start_appointment_time'] = substr($LineCharteredArr[$key]['start_appointment_time'], 0, 5);
                    $LineCharteredArr[$key]['end_appointment_time'] = substr($LineCharteredArr[$key]['end_appointment_time'], 0, 5);
                }
            }
            $ret->ret = true;
            $ret->data = $LineCharteredArr;
            $ret->count = $LineCharteredCount;
        }
        return $this->output($ret);
    }

    /**
     * 获取班线车日期列表
     * @param string $date 日期。例如（2019-01-01）
     */
    public function getLineClassDate($date = null)
    {
        $ret = new \ResultModel(false, '获取失败');
        $currentTime = time();
        $sectionDate = 30;
        $offset = 0;

        if (empty($date)) {
            $date = date('Y-m-d');
        } else {
            $currentDay = date('Y-m-d', $currentTime);
            if (strtotime($currentDay) > strtotime($date)) {
                return $this->output($ret);
            }

            $dayNum = \diffBetweenTwoDays($currentDay, $date);
            if ($dayNum > $sectionDate / 2) {
                $offset = $dayNum - intval($sectionDate / 2);
            }
        }

        $dateArr = [];
        for ($i = 0; $i < $sectionDate; $i++, $offset++) {
            $temp = date('Y-m-d', strtotime("+" . $offset . " day"));
            $selectd = false;
            if (strtotime($temp) == strtotime($date)) {
                $selectd = true;
            }
            $dateClassify = new \DateClassify($temp);
            $dateArr[] = [
                'selectd' => $selectd,
                'index' => $i,
                'date' => $temp,
                'show' => $dateClassify->classifyshow()
            ];
        }

        $ret->ret = true;
        $ret->data = $dateArr;
        $ret->count = $sectionDate;
        return $this->output($ret);
    }

    protected function queryParentAddressCode($code)
    {
        $adcodeR = $this->find(['address_id' => $code], 'GdRegion');
        if ($adcodeR->ret) {
            return $adcodeR->data['address_pid'];
        }
    }


    /**
     * 定制客运线路列表
     *
     * @param string $cid
     * @param string $start_address_code
     * @param string $end_address_code
     * @param string $start_name
     * @param string $end_name
     * @param string $day_time
     * @param string $city_name
     * @param string $driver_id
     * @param string $start_city_name
     * @param string $end_city_name
     * @return void
     */
    public function queryDingzhikeyunLine($cid = '', $start_address_code = '', $end_address_code = '', $start_name = '', $end_name = '', $day_time = '', $city_name = '', $driver_id = '', $start_city_name = '', $end_city_name = '')
    {
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
            'cid' => $cid,
            'driver_id' => $driver_id,
            'start_name' => rawurlencode($_REQUEST['start_name']), // 移除多余的编码/解码
            'end_name' => rawurlencode($_REQUEST['end_name']), // 移除多余的编码/解码
            'day_time' => $day_time,
            'city_name' => $city_name,
            'start_city_name' => rawurlencode($_REQUEST['start_city_name']), // 移除多余的编码/解码
            'end_city_name' => rawurlencode($_REQUEST['end_city_name']), // 移除多余的编码/解码
        );
        $ts = microtime(true);
        $url = C('CC_PROXY_API_HOST') . "/api/inner/dingzhikeyun/lines";
        $responsed = httpRequest($url, 'get', $params, $header);
        \Log::write(
            sprintf(
                "\n请求URL: %s\n请求耗时: %dms\n请求参数: %s",
                $url,
                round((microtime(true) - $ts) * 1000),
                json_encode($params, JSON_UNESCAPED_UNICODE)
            ),
            \Log::DEBUG,
            \Log::FILE,
            LOG_PATH . "curl_perf_" . date('Y-m-d') . ".log"
        );
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('定制班线车查询失败：%s', $results['message']);
        }
        return $this->output($ret);
    }

    /**
     * 包车线路列表
     *
     * @param string $cid
     * @param string $start_address_code
     * @param string $end_address_code
     * @return void
     */
    public function queryBaocheLine($cid = '', $start_address_code = '', $end_address_code = '', $start_city_name = '', $end_city_name = '')
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];

        $params = array(
            'start_address_code' => $start_address_code,
            'end_address_code' => $end_address_code,
            'cid' => $cid,
            'start_city_name' => urlencode($start_city_name),
            'end_city_name' => urlencode($end_city_name),
        );

        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/baoche/lines", 'get', $params, $header);
        $results = json_decode($responsed, true);
        $ret = new \ResultModel(false);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('包车线路查询失败：%s', $results['message']);
        }
        return $this->output($ret);
    }

    /**
     *  小程序班次列表API
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param string $start_name 出发地名称（默认为空）
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param string $end_name 目的地名称（默认为空）
     * @param int $day_time 发车时间
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $cid 场景分类id（默认为空）
     * @param int $driver_id 司机id（默认为空）
     */
    public function getLineClassTrains($start_address_code = null, $start_name = null, $end_name = null, $end_address_code = null, $day_time = null, $page = 1, $size = 200, $cid = null, $driver_id = null)
    {
        $start_time = "";
        $end_time = "";
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s', $currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();
        $dayNum = \diffBetweenTwoDays(date('Y-m-d', $currentTime), $day_time);

        if (empty($day_time)) {
            $start_time = date('Y-m-d 00:00:00', $currentTime);
            $end_time = date('Y-m-d 23:59:59', $currentTime);
        } else {
            $start_time = date('Y-m-d 00:00:00', strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59', strtotime($day_time));
        }

        # 默认筛选条件
        $where = " lct.mchid = " . $this->mchid;
        $where .= " AND lct.start_date >= '" . $start_time . "'";
        $where .= " AND lct.start_date <= '" . $end_time . "'";
        $where .= " AND lct.status = '" . \CommonDefine::LINE_CLASS_OPERATE_1 . "'";
        $where .= " AND lc.status = '" . \CommonDefine::LINE_CLASS_OPERATE_1 . "'";
        # 按地址编码查询
        if (!empty($start_address_code) && $start_address_code != -1) {
            switch ($this->getAddressCodeType($start_address_code)) {
                case 3:
                    $where .= (" AND lc.start_area_code = " . $start_address_code);
                    break;
                case 2:
                    $where .= (" AND lc.start_city_code = " . $start_address_code);
                    break;
                case 1:
                    $where .= (" AND lc.start_province_code = " . $start_address_code);
                    break;
            }
        }
        if (!empty($end_address_code)  && $end_address_code != -1) {
            switch ($this->getAddressCodeType($end_address_code)) {
                case 3:
                    # 如果三级未找到，则通过end_address_code 查询上一级
                    $tempCode = $this->queryParentAddressCode($end_address_code);
                    if ($tempCode) {
                        $where .= (" AND (lc.end_area_code = '" . $end_address_code . "' OR lc.end_city_code = '" . $tempCode . "') ");
                    } else {
                        $where .= (" AND lc.end_area_code = " . $end_address_code);
                    }
                    break;
                case 2:
                    $where .= (" AND lc.end_city_code = " . $end_address_code);
                    break;
                case 1:
                    $where .= (" AND lc.end_province_code = " . $end_address_code);
                    break;
            }
        }

        if ($start_name && $start_address_code != -1) {
            $where .= (" AND ( lc.start_name LIKE '" . $start_name . "%' )");
        }
        if ($end_name && $end_address_code != -1) {
            $where .= (" AND ( lc.end_name LIKE '" . $end_name . "%' )");
        }

        if (!empty($cid) && $cid != 'undefined') {
            $where .= " AND lcfklc.line_category_id in (" . $cid . ")";
        }

        if (!empty($driver_id)) {
            $where .= " AND lctd.driver_id in (" . $driver_id . ")";
        }

        $where .= " AND lct.is_del =" . \CommonDefine::IS_DEL_0;

        # 组装查询条件
        $order = "lc.start_time asc, lct.start_time asc, lct.start_earliest_time asc, lct.end_latest_time asc";

        $field = "lct.line_class_id,
                lc.start_time_type,lc.start_earliest_time,lc.end_latest_time,lc.start_time, lct.start_time as starting_time,
                lc.refund_time_set,
                lc.start_city_code,gdrs.name as start_city_name,
                lc.end_city_code,gdre.name as end_city_name,
                lc.start_address_type,
                lc.end_address_type,
                lc.is_return,
                lc.center_start_latlng,lc.center_end_latlng,
                lc.return_start_time_type,lc.return_time_number,lc.return_start_time,lc.return_start_earliest_time,lc.return_end_latest_time,
                lc.return_start_address_type,
                lc.return_start_alias,
                lc.return_end_address_type,
                lc.return_end_alias,
                lc.start_polygon,
                lc.end_polygon,
                lct.line_class_train_id,
                lct.line_class_train_no,
                lct.remain_tickets,
                lct.price,
                lc.start_name,
                lc.end_name,
                lc.summary,
                lc.stop_sell_time,
                lc.stop_sell_number,
                lct.travel_time,
                lct.start_date,
                lct.is_seat_selection,
                lct.car_seats,
                lct.seat_layout,
                lct.seat_price,
                lct.is_start_ferry,
                lct.is_end_ferry,
                via.province_code as via_province_code,
                via.province_name as via_province_name,
                via.city_code as via_city_code,
                via.city_name as via_city_name,
                via.area_code as via_area_code,
                via.area_name as via_area_name,
                via.name as via_name
                ";

        $lineClassTrainsArr = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->join('LEFT JOIN cp_line_class_fk_line_category lcfklc ON lcfklc.line_class_id = lc.id')
            ->join('LEFT JOIN cp_line_class_train_driver lctd ON lctd.line_class_train_id = lct.line_class_train_id')
            ->join('LEFT JOIN cp_dingzhikeyun_line_via_points via ON lc.id = via.line_class_id')
            ->where($where)
            ->field($field)
            ->order($order)
            ->distinct('lc.id')
            ->select();

        //总数量
        $lineClassTrainsCount = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->join('LEFT JOIN cp_line_class_fk_line_category lcfklc ON lcfklc.line_class_id = lc.id')
            ->join('LEFT JOIN cp_line_class_train_driver lctd ON lctd.line_class_train_id = lct.line_class_train_id')
            ->where($where)
            ->field($field)
            ->order($order)
            ->count('distinct lc.id');

        $lineClassFerryServiceR = $this->find(['mchid' => $this->mchid], 'LineClassFerryService');
        $lineClassFerry = $lineClassFerryServiceR->ret ? 1 : 0;
        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        //返程
        //固定上车点
        $returnStartPointField = 'alias,longitude,latitude,use_time';
        $returnStartPointOrder = 'id asc';
        //固定下车点
        $returnEndPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $returnEndPointOrder = 'id asc';
        foreach ($lineClassTrainsArr as $k => $lineClassTrain) {
            $lineClassTrainsArr[$k]['is_remain_tickets_control'] = $this->mchid == 1231 ? 1 : 0; //是否控制余票显示方式
            $lineClassTrainsArr[$k]['is_expire'] = 0;
            $stop_sell_time = \getTimeNumber($lineClassTrain['stop_sell_time']);
            if ($dayNum == 0) {
                if ($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 0 && $stop_sell_time != 0) {
                    if (strtotime($day_time) == strtotime(date('Y-m-d', time()))) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }

                /**
                 * start_time_type 发车时间类型：1-固定发车时间；2-滚动发车时段
                 */
                if ($lineClassTrain['start_time_type'] == 2) {
                    if (\getTimeNumber($lineClassTrain->data['end_latest_time']) > \getTimeNumber($lineClassTrain->data['start_earliest_time'])) { //当天
                        if (\getTimeNumber($lineClassTrain['end_latest_time']) < $timeNumber) {
                            $lineClassTrainsArr[$k]['is_expire'] = 1;
                        }
                    }
                } else {
                    if (\getTimeNumber($lineClassTrain['starting_time']) < $timeNumber) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            } elseif ($dayNum == 1) {
                if ($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 1 && $stop_sell_time != 0) {
                    if (strtotime($day_time) == strtotime(date('Y-m-d', time()))) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }
            if ($this->mchid == 1258) {
                if ($lineClassTrainsArr[$k]['is_expire'] == 1) {
                    unset($lineClassTrainsArr[$k]);
                    continue;
                }
            }

            $lineClassTrainsArr[$k]['time_region'] = [];
            if ($lineClassTrainsArr[$k]['start_time_type']  == 2) {
                # 过滤掉最晚时刻小于当前时刻的班次
                if (
                    $lineClassTrain['start_date'] == date('Y-m-d')
                    && strtotime(date('Y-m-d') . ' ' . $lineClassTrain['end_latest_time']) < time()
                ) {
                    $lineClassTrainsArr[$k]['is_expire'] = 1;
                }
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time'] ? substr($lineClassTrainsArr[$k]['start_earliest_time'], 0, 5) : "";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time'] ? substr($lineClassTrainsArr[$k]['end_latest_time'], 0, 5) : "";
            } else {
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['starting_time']
                    ? substr($lineClassTrainsArr[$k]['starting_time'], 0, 5)
                    : substr($lineClassTrainsArr[$k]['start_time'], 0, 5);
            }

            if ($lineClassTrain['start_address_type'] == 1) {
                $startPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 1 AND is_return = 0';
                $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_line_class_point')
                    ->where($startPointWhere)
                    ->field($startPointField)
                    ->order($startPointOrder)
                    ->select();
                $lineClassTrainsArr[$k]['start_ferry_point'] = [];
            } else {
                if ($lineClassFerry) {
                    $lineClassTrainsArr[$k]['start_point'] = [];
                    $startPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 1 AND is_return = 0';
                    $lineClassTrainsArr[$k]['start_ferry_point'] = M()->table('cp_line_class_point')
                        ->where($startPointWhere)
                        ->field($startPointField)
                        ->order($startPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['is_start_ferry'] = \CommonDefine::IS_START_FERRY_0;
                }
            }

            if ($lineClassTrain['end_address_type'] == 1) {
                $endPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 2 AND is_return = 0';
                $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_line_class_point')
                    ->where($endPointWhere)
                    ->field($endPointField)
                    ->order($endPointOrder)
                    ->select();
            } else {
                if ($lineClassFerry) {
                    $lineClassTrainsArr[$k]['end_point'] = [];
                    $endPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 2 AND is_return = 0';
                    $lineClassTrainsArr[$k]['end_ferry_point'] = M()->table('cp_line_class_point')
                        ->where($endPointWhere)
                        ->field($endPointField)
                        ->order($endPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['is_end_ferry'] = \CommonDefine::IS_END_FERRY_0;
                }
            }

            //含返程
            if ($lineClassTrain['is_return'] == 1) {
                $lineClassTrainsArr[$k]['return_start_date'] = $lineClassTrainsArr[$k]['start_date'];
                if ($lineClassTrainsArr[$k]['return_time_number']) {
                    $lineClassTrainsArr[$k]['return_start_date'] = date('Y-m-d', strtotime($lineClassTrainsArr[$k]['start_date'] . " +" . $lineClassTrainsArr[$k]['return_time_number'] . "day"));
                }

                $lineClassTrainsArr[$k]['return_time_region'] = [];
                if ($lineClassTrainsArr[$k]['return_start_time_type']  == 2) {
                    $lineClassTrainsArr[$k]['return_time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['return_start_date'], $lineClassTrainsArr[$k]['return_start_earliest_time'], $lineClassTrainsArr[$k]['return_end_latest_time']);
                    $lineClassTrainsArr[$k]['return_start_earliest_time'] = $lineClassTrainsArr[$k]['return_start_earliest_time'] ? substr($lineClassTrainsArr[$k]['return_start_earliest_time'], 0, 5) : "";
                    $lineClassTrainsArr[$k]['return_end_latest_time'] = $lineClassTrainsArr[$k]['return_end_latest_time'] ? substr($lineClassTrainsArr[$k]['return_end_latest_time'], 0, 5) : "";
                } else {
                    $lineClassTrainsArr[$k]['return_start_time'] = $lineClassTrainsArr[$k]['return_start_time'] ? substr($lineClassTrainsArr[$k]['return_start_time'], 0, 5) : "";
                }

                if ($lineClassTrain['return_start_address_type'] == 1) {
                    $returnStartPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 1 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_start_point'] = M()->table('cp_line_class_point')
                        ->where($returnStartPointWhere)
                        ->field($returnStartPointField)
                        ->order($returnStartPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_start_point'] = [];
                }

                if ($lineClassTrain['return_end_address_type'] == 1) {
                    $returnEndPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 2 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_end_point'] = M()->table('cp_line_class_point')
                        ->where($returnEndPointWhere)
                        ->field($returnEndPointField)
                        ->order($returnEndPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_end_point'] = [];
                }
            }

            $lineClassTrainsArr[$k]['seat_price'] = json_decode($lineClassTrainsArr[$k]['seat_price'], true);
            $lineClassTrainsArr[$k]['min_seat_price'] = $this->getMinSeatPrice($lineClassTrainsArr[$k]['seat_price']);
        }

        $ret = new \ResultModel(false, '暂无数据');
        if (!empty($lineClassTrainsArr)) {
            # 把无票的放在后面显示前面显示有票的（仅api修改）
            // array_multisort(array_column($lineClassTrainsArr, 'is_expire'), SORT_DESC, $lineClassTrainsArr);
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr;
            $ret->count = $lineClassTrainsCount;
        }

        return $this->output($ret);
    }

    /**
     *  获取班次详情
     * @param int $line_class_train_id 线路编号：510107
     */
    public function getLineClassTrainDetail($line_class_train_id)
    {
        $where = "lct.mchid = " . $this->mchid;
        $where .= " AND lct.line_class_train_id = " . $line_class_train_id;
        $where .= " AND lct.status = '" . \CommonDefine::LINE_CLASS_OPERATE_1 . "'";
        $where .= " AND lct.is_del =" . \CommonDefine::IS_DEL_0;

        $order = "lc.sort asc";
        $field = "lct.*,lc.*,lct.seat_price as lct_seat_price, lct.start_time as show_start_time";

        $lineClassTrainsArr = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->join('LEFT JOIN cp_line_class_fk_line_category lcfklc ON lcfklc.line_class_id = lc.id')
            ->where($where)
            ->field($field)
            ->group('lc.id')
            ->order($order)
            ->select();

        //去程
        //固定上车点
        $startPointField = 'id,alias,longitude,latitude,use_time,additional_fee';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'id,line_class_id,alias,longitude,latitude,use_time,additional_fee';
        $endPointOrder = 'id asc';
        //返程
        //固定上车点
        $returnStartPointField = 'alias,longitude,latitude,use_time,additional_fee';
        $returnStartPointOrder = 'id asc';
        //固定下车点
        $returnEndPointField = 'line_class_id,alias,longitude,latitude,use_time,additional_fee';
        $returnEndPointOrder = 'id asc';
        foreach ($lineClassTrainsArr as $k => $lineClassTrain) {
            $lineClassTrainsArr[$k]['time_region'] = [];
            if ($lineClassTrainsArr[$k]['start_time_type']  == 2) {
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time'] ? substr($lineClassTrainsArr[$k]['start_earliest_time'], 0, 5) : "";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time'] ? substr($lineClassTrainsArr[$k]['end_latest_time'], 0, 5) : "";
            } else {
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['show_start_time'] ? substr($lineClassTrainsArr[$k]['show_start_time'], 0, 5) : "";
            }

            $branchR = $this->find(['admin_id' =>  $lineClassTrain['branchid']], 'Admin');
            $lineClassTrainsArr[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";

            if ($lineClassTrain['start_address_type'] == 1) {
                $startPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 1 AND is_return = 0';
                $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_line_class_point')
                    ->where($startPointWhere)
                    ->field($startPointField)
                    ->order($startPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['start_point'] = [];
            }

            if ($lineClassTrain['end_address_type'] == 1) {
                $endPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 2 AND is_return = 0';
                $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_line_class_point')
                    ->where($endPointWhere)
                    ->field($endPointField)
                    ->order($endPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['end_point'] = [];
            }

            //含返程
            if ($lineClassTrain['is_return'] == 1) {
                $lineClassTrainsArr[$k]['return_start_date'] = $lineClassTrainsArr[$k]['start_date'];
                if ($lineClassTrainsArr[$k]['return_time_number']) {
                    $lineClassTrainsArr[$k]['return_start_date'] = date('Y-m-d', strtotime($lineClassTrainsArr[$k]['start_date'] . " +" . $lineClassTrainsArr[$k]['return_time_number'] . "day"));
                }

                $lineClassTrainsArr[$k]['return_time_region'] = [];
                if ($lineClassTrainsArr[$k]['return_start_time_type']  == 2) {
                    $lineClassTrainsArr[$k]['return_time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['return_start_date'], $lineClassTrainsArr[$k]['return_start_earliest_time'], $lineClassTrainsArr[$k]['return_end_latest_time']);
                    $lineClassTrainsArr[$k]['return_start_earliest_time'] = $lineClassTrainsArr[$k]['return_start_earliest_time'] ? substr($lineClassTrainsArr[$k]['return_start_earliest_time'], 0, 5) : "";
                    $lineClassTrainsArr[$k]['return_end_latest_time'] = $lineClassTrainsArr[$k]['return_end_latest_time'] ? substr($lineClassTrainsArr[$k]['return_end_latest_time'], 0, 5) : "";
                } else {
                    $lineClassTrainsArr[$k]['return_start_time'] = $lineClassTrainsArr[$k]['return_start_time'] ? substr($lineClassTrainsArr[$k]['return_start_time'], 0, 5) : "";
                }

                if ($lineClassTrain['return_start_address_type'] == 1) {
                    $returnStartPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 1 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_start_point'] = M()->table('cp_line_class_point')
                        ->where($returnStartPointWhere)
                        ->field($returnStartPointField)
                        ->order($returnStartPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_start_point'] = [];
                }

                if ($lineClassTrain['return_end_address_type'] == 1) {
                    $returnEndPointWhere = 'line_class_id=' . $lineClassTrain['line_class_id'] . ' AND type = 2 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_end_point'] = M()->table('cp_line_class_point')
                        ->where($returnEndPointWhere)
                        ->field($returnEndPointField)
                        ->order($returnEndPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_end_point'] = [];
                }
            }
            $lineClassTrainsArr[$k]['lct_seat_price'] = json_decode($lineClassTrainsArr[$k]['lct_seat_price'], true);
            $lineClassTrainsArr[$k]['seat_price'] = json_decode($lineClassTrainsArr[$k]['seat_price'], true);
            $lineClassTrainsArr[$k]['min_seat_price'] = $this->getMinSeatPrice($lineClassTrainsArr[$k]['seat_price']);

        }

        $ret = new \ResultModel(false, '暂无数据');
        if (!empty($lineClassTrainsArr)) {
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr[0];
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 获取热门推荐线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     *
     */
    public function getRecommendLineClassTrain($page = 1, $size = 10)
    {
        $ret = new \ResultModel(false, '暂无数据');
        $header = [
            'merchant: ' . $this->mchid,
            'Accept: application/json'
        ];
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/dingzhikeyun/recommended_routes", 'get', array(), $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
            $ret->count = count($results['data']);
        }
        return $this->output($ret);
    }

    /**
     *  获取顺风车的列表
     * @param int $start_address_code 出发地城市编码
     * @param int $end_address_code 目的地城市编码
     * @param int $day_time 出发时间
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLineFreeRide($start_address_code = null, $end_address_code = null, $day_time = null, $page = 1, $size = 200)
    {
        $current_time_stamp = time();
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['mchid'] = $this->mchid;
        $where['residual_seating'] = array('gt', 0);
        if (!empty($day_time)) {
            $start_time = date('Y-m-d 00:00:00', strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59', strtotime($day_time));
            $where['start_time'] = array('between', array($start_time, $end_time));
        } else {
            $start_time = date('Y-m-d 00:00:00', $current_time_stamp);
            $where['start_time'] = array('egt', $start_time);
        }

        $orderBy['start_time'] = 'ASC ';
        if (!empty($start_address_code)) {
            $where['start_city_code'] = $start_address_code;
        }
        if (!empty($end_address_code)) {
            $where['end_city_code'] = $end_address_code;
        }

        $fields = 'id,line_free_ride_no as lfrn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,end_name,end_address_code,end_address_remark,create_time,center_start_latlng,center_end_latlng';
        $lineFreeRidesR = $this->select($where, $page, $size, $orderBy, 'LineFreeRide', $fields);
        if ($lineFreeRidesR->ret) {
            foreach ($lineFreeRidesR->data as $k => $v) {
                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $lineFreeRidesR->data[$k]['car_brand'] = $driverR->data['car_brand'];
                }
                $lineFreeRidesR->data[$k]['away_time'] = (new \Mygettime($current_time_stamp, strtotime($v['create_time'])))->index();
                unset($lineFreeRidesR->data[$k]['driver_id']);
            }
        }
        return $this->output($lineFreeRidesR);
    }

    /**
     * 获取顺风车线路详情
     * @param string $lfrn 线路编号，创建时接口返回的lfrn编号
     * @return null|\resultModel
     */
    public function getLineFreeRideDetail($lfrn)
    {
        $fields = 'id,line_free_ride_no as lfrn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,start_longitude,start_latitude,end_name,end_address_code,end_address_remark,end_longitude,end_latitude';
        $lineFreeRideR = $this->find(array('line_free_ride_no' => $lfrn), 'LineFreeRide', $fields);
        if ($lineFreeRideR->ret) {
            $driverR = $this->find(array('driver_id' => $lineFreeRideR->data['driver_id']), 'Driver');
            if ($driverR->ret) {
                $lineFreeRideR->data['driver_name'] = $driverR->data['name'];
                $lineFreeRideR->data['driver_cellphone'] = $driverR->data['cellphone'];
                $lineFreeRideR->data['car_tail_number'] = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                $lineFreeRideR->data['car_brand'] = $driverR->data['car_brand'];
            }
        }

        return $this->output($lineFreeRideR);
    }

    /**
     * 带货获取营业时间
     * @return null|\resultModel
     */
    public function getTakeGoodsAppointmentTime()
    {
        $takeGoodsR = $this->find(array('mchid' => $this->mchid, 'is_default' => 1), 'TakeGoodsPrice', 'business_time_type,start_appointment_time,end_appointment_time,set_order_time');
        return $this->output($takeGoodsR);
    }

    /**
     *  根据开始出发地址编码获取目的地址
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getEndLineByStartLineCode($start_address_code = null, $page = 1, $size = 200)
    {
        if (empty($start_address_code)) {
            return $this->output(new \ResultModel(false, '请选择出发地'));
        }
        $where['start_city_code'] = $start_address_code;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['mchid'] = $this->mchid;

        $r = $this->select($where, $page, $size, null, 'Line', 'end_city_code');
        if ($r->ret) {
            $temp = [];
            foreach ($r->data as $key => $item) {
                unset($r->data[$key]['end_city_code']);
                $gdRegionR = $this->find(array('address_id' => $item['end_city_code']), 'GdRegion', 'address_id,name');
                if ($gdRegionR->ret) {
                    if (!in_array($gdRegionR->data['name'], $temp)) {
                        $temp[$key] =  $gdRegionR->data['name'];
                        $r->data[$key]['end_address_code'] = $gdRegionR->data['address_id'];
                        $r->data[$key]['end_address_name'] = $gdRegionR->data['name'];
                    } else {
                        unset($r->data[$key]);
                    }
                }
            }
        } else {
            $r->data['end_address_code'] = null;
            $r->data['end_address_name'] = null;
        }
        $r->count = count($temp);
        return $this->output($r);
    }


    /**
     * 获取拼车线路通过城市编码
     * @param int $start_city_code 出发地
     * @param int $end_city_code 目的地
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLinesByCityCode($start_city_code = null, $end_city_code = null, $page = 1, $size = 200)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_show'] = \CommonDefine::IS_SHOW_1;
        $where['status'] = 1;
        if ($start_city_code) {
            $where['start_city_code'] = $start_city_code;
        }
        if ($end_city_code) {
            $where['end_city_code'] = $end_city_code;
        }

        $r = $this->select($where, $page, $size, 'create_time desc', 'Line', 'id, branchid, start_name, end_name,price,summary,set_order_time');
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$k]['tel'] = $branchR->ret ? $branchR->data['cellphone'] : "暂无";
                unset($r->data[$k]['branchid']);
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }


    private function getAddressTitleByCode($code)
    {
        return $this->find(array('address_id' => $code), 'GdRegion', 'name')->data['name'];
    }
}

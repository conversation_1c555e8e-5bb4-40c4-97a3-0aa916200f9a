<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-获取线路
 *
 * <AUTHOR>
 */
class YyLineAction extends \PassengerCoreAction
{

    /**
     * 获取日期列表
     * @param string $date 日期。例如（2019-01-01）
     */
    public function getLineClassDate($date = null){
        $ret = new \ResultModel(false, '获取失败');
        $currentTime = time();
        $sectionDate = 15;
        $offset = 0;

        if(empty($date)){
            $date = date('Y-m-d');
        }else{
            $currentDay = date('Y-m-d',$currentTime);
            if(strtotime($currentDay) > strtotime($date)){
                return $this->output($ret);
            }

            $dayNum = \diffBetweenTwoDays($currentDay, $date);
            if($dayNum > $sectionDate/2){
                $offset = $dayNum - intval($sectionDate/2);
            }
        }

        $dateArr = [];
        for($i = 0 ; $i < $sectionDate; $i++,$offset++){
            $temp = date('Y-m-d',strtotime("+".$offset." day"));
            $selectd = false;
            if(strtotime($temp) == strtotime($date)){
                $selectd = true;
            }
            $dateClassify = new \DateClassify($temp);
            $dateArr[] = [
                'selectd' => $selectd,
                'index' => $i,
                'date' => $temp,
                'show' => $dateClassify->classifyshow()
            ];
        }

        $ret->ret = true;
        $ret->data = $dateArr;
        $ret->count = $sectionDate;
        return $this->output($ret);
    }

    /**
     * 条件筛选学生号线路
     * @param string $start_address_name 出发地（默认为空）
     * @param string $end_address_name 目的地（默认为空）
     * @param string $day_time 出发日期（默认为空）
     * @param integer $page 页码（默认1）
     * @param integer $size 每页大小（默认10）
     */
    public function getYyLines($start_address_name = null, $end_address_name = null, $day_time = null, $page = 1, $size = 10){
        $start_time = "";
        $end_time = "";
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s',$currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();
        $dayNum = \diffBetweenTwoDays(date('Y-m-d',$currentTime), $day_time);

        if(empty($day_time)){
            $start_time = date('Y-m-d 00:00:00',$currentTime);
            $end_time = date('Y-m-d 23:59:59',$currentTime);
        }else{
            $start_time = date('Y-m-d 00:00:00',strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59',strtotime($day_time));
        }

        $where = "yylct.mchid = ".$this->mchid;
        $where .= " AND yylct.start_date >= '".$start_time."'";
        $where .= " AND yylct.start_date <= '".$end_time."'";
        $where .= " AND yylct.status = '".\CommonDefine::LINE_CLASS_OPERATE_1."'";

        if(!empty($start_address_name)){
            $where .= (" AND (yylcp1.alias = '".$start_address_name."' AND yylcp1.type = 1) ");
        }

        if(!empty($end_address_name)){
            $where .= (" AND (yylcp2.alias = '".$end_address_name."' AND yylcp2.type = 2) ");
        }

        $where .= " AND yylct.is_del =".\CommonDefine::IS_DEL_0 ;

        $order = "yylc.sort asc";
        $field = "yylct.line_class_id,
                yylc.start_time,
                yylc.refund_time_set,
                yylc.start_city_code,gdrs.name as start_city_name,
                yylc.end_city_code,gdre.name as end_city_name,
                yylct.line_class_train_id,
                yylct.line_class_train_no,
                yylct.remain_tickets,
                yylct.price,
                yylc.start_name,
                yylc.end_name,
                yylc.summary,
                yylc.stop_sell_time,
                yylc.stop_sell_number,
                yylct.travel_time,
                yylct.start_date,
                yylcp1.alias as start_point_alias,
                yylcp1.latitude as start_point_latitude,
                yylcp1.longitude as start_point_longitude,
                yylcp2.alias as end_point_alias,
                yylcp2.latitude as end_point_latitude,
                yylcp2.longitude as end_point_longitude";

        $lineClassTrainsArr = M()->table('cp_yy_line_class_train yylct')
            ->join('LEFT JOIN cp_yy_line_class yylc ON yylc.id = yylct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = yylc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = yylc.end_city_code')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp1 ON yylcp1.line_class_id = yylc.id')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp2 ON yylcp2.line_class_id = yylc.id')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->group('yylc.id')
            ->order($order)
            ->select();

        //总数量
        $lineClassTrainsCount = M()->table('cp_yy_line_class_train yylct')
            ->join('LEFT JOIN cp_yy_line_class yylc ON yylc.id = yylct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = yylc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = yylc.end_city_code')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp1 ON yylcp1.line_class_id = yylc.id')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp2 ON yylcp2.line_class_id = yylc.id')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->group('yylc.id')
            ->order($order)
            ->count();

        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        foreach($lineClassTrainsArr as $k=>$lineClassTrain){
            $lineClassTrainsArr[$k]['is_expire'] = 0;
            $stop_sell_time = \getTimeNumber($lineClassTrain['stop_sell_time']);
            if($dayNum == 0){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 0 && $stop_sell_time != 0){
                    if(strtotime($day_time) == strtotime(date('Y-m-d',time()))){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }

                if($lineClassTrain['start_time_type'] == 2){
                    if(\getTimeNumber($lineClassTrain->data['end_latest_time']) > \getTimeNumber($lineClassTrain->data['start_earliest_time'])){//当天
                        if(\getTimeNumber($lineClassTrain['end_latest_time']) < $timeNumber){
                            $lineClassTrainsArr[$k]['is_expire'] = 1;
                        }
                    }
                }else{
                    if(\getTimeNumber($lineClassTrain['start_time']) < $timeNumber){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }else if($dayNum == 1){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 1 && $stop_sell_time != 0){
                    if(strtotime($day_time) == strtotime(date('Y-m-d',time()))){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }

            $lineClassTrainsArr[$k]['time_region'] = [];
            if($lineClassTrainsArr[$k]['start_time_type']  == 2){
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time']?substr($lineClassTrainsArr[$k]['start_earliest_time'],0,5):"";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time']?substr($lineClassTrainsArr[$k]['end_latest_time'],0,5):"";
            }else{
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['start_time']?substr($lineClassTrainsArr[$k]['start_time'],0,5):"";
            }

            $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
            $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_yy_line_class_point')
                ->where($startPointWhere)
                ->field($startPointField)
                ->order($startPointOrder)
                ->select();

            $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
            $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_yy_line_class_point')
                ->where($endPointWhere)
                ->field($endPointField)
                ->order($endPointOrder)
                ->select();
        }

        $ret = new \ResultModel(false, '暂无数据');
        if(!empty($lineClassTrainsArr)){
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr;
            $ret->count = $lineClassTrainsCount;
        }

        return $this->output($ret);
    }

    /**
     * 条件筛选学生号组团定制线路
     * @param string $start_address_name 出发地（默认为空）
     * @param string $end_address_name 目的地（默认为空）
     * @param string $day_time 出发日期（默认为空）
     * @param integer $number 组团人数 (默认为1)
     * @param integer $page 页码（默认1）
     * @param integer $size 每页大小（默认10）
     */
    public function getYyGroupLines($start_address_name = null, $end_address_name = null, $number = 1, $day_time = null, $page = 1, $size = 10){
        $start_time = "";
        $end_time = "";
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s',$currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();
        $dayNum = \diffBetweenTwoDays(date('Y-m-d',$currentTime), $day_time);

        if(empty($day_time)){
            $start_time = date('Y-m-d 00:00:00',$currentTime);
            $end_time = date('Y-m-d 23:59:59',$currentTime);
        }else{
            $start_time = date('Y-m-d 00:00:00',strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59',strtotime($day_time));
        }

        $where = "yylct.mchid = ".$this->mchid;
        $where .= " AND yylct.start_date >= '".$start_time."'";
        $where .= " AND yylct.start_date <= '".$end_time."'";
        $where .= " AND yylct.status = '".\CommonDefine::LINE_CLASS_OPERATE_1."'";

        if(!empty($start_address_name)){
            $where .= (" AND (yylcp1.alias = '".$start_address_name."' AND yylcp1.type = 1) ");
        }

        if(!empty($end_address_name)){
            $where .= (" AND (yylcp2.alias = '".$end_address_name."' AND yylcp2.type = 2) ");
        }

        $where .= " AND yylct.is_del =".\CommonDefine::IS_DEL_0 ;
        $where .= " AND yylct.remain_tickets >=".$number ;

        $order = "yylc.sort asc";
        $field = "yylct.line_class_id,
                yylc.start_time,
                yylc.refund_time_set,
                yylc.start_city_code,gdrs.name as start_city_name,
                yylc.end_city_code,gdre.name as end_city_name,
                yylct.line_class_train_id,
                yylct.line_class_train_no,
                yylct.remain_tickets,
                yylct.price,
                yylc.start_name,
                yylc.end_name,
                yylc.summary,
                yylc.stop_sell_time,
                yylc.stop_sell_number,
                yylct.travel_time,
                yylct.start_date,
                yylcp1.alias as start_point_alias,
                yylcp1.latitude as start_point_latitude,
                yylcp1.longitude as start_point_longitude,
                yylcp2.alias as end_point_alias,
                yylcp2.latitude as end_point_latitude,
                yylcp2.longitude as end_point_longitude";

        $lineClassTrainsArr = M()->table('cp_yy_line_class_train yylct')
            ->join('LEFT JOIN cp_yy_line_class yylc ON yylc.id = yylct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = yylc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = yylc.end_city_code')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp1 ON yylcp1.line_class_id = yylc.id')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp2 ON yylcp2.line_class_id = yylc.id')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->group('yylc.id')
            ->order($order)
            ->select();

        //总数量
        $lineClassTrainsCount = M()->table('cp_yy_line_class_train yylct')
            ->join('LEFT JOIN cp_yy_line_class yylc ON yylc.id = yylct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = yylc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = yylc.end_city_code')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp1 ON yylcp1.line_class_id = yylc.id')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp2 ON yylcp2.line_class_id = yylc.id')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->group('yylc.id')
            ->order($order)
            ->count();

        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        foreach($lineClassTrainsArr as $k=>$lineClassTrain){
            $lineClassTrainsArr[$k]['is_expire'] = 0;
            $stop_sell_time = \getTimeNumber($lineClassTrain['stop_sell_time']);
            if($dayNum == 0){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 0 && $stop_sell_time != 0){
                    if(strtotime($day_time) == strtotime(date('Y-m-d',time()))){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }

                if($lineClassTrain['start_time_type'] == 2){
                    if(\getTimeNumber($lineClassTrain->data['end_latest_time']) > \getTimeNumber($lineClassTrain->data['start_earliest_time'])){//当天
                        if(\getTimeNumber($lineClassTrain['end_latest_time']) < $timeNumber){
                            $lineClassTrainsArr[$k]['is_expire'] = 1;
                        }
                    }
                }else{
                    if(\getTimeNumber($lineClassTrain['start_time']) < $timeNumber){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }else if($dayNum == 1){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 1 && $stop_sell_time != 0){
                    if(strtotime($day_time) == strtotime(date('Y-m-d',time()))){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }

            $lineClassTrainsArr[$k]['time_region'] = [];
            if($lineClassTrainsArr[$k]['start_time_type']  == 2){
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time']?substr($lineClassTrainsArr[$k]['start_earliest_time'],0,5):"";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time']?substr($lineClassTrainsArr[$k]['end_latest_time'],0,5):"";
            }else{
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['start_time']?substr($lineClassTrainsArr[$k]['start_time'],0,5):"";
            }

            $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
            $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_yy_line_class_point')
                ->where($startPointWhere)
                ->field($startPointField)
                ->order($startPointOrder)
                ->select();

            $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
            $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_yy_line_class_point')
                ->where($endPointWhere)
                ->field($endPointField)
                ->order($endPointOrder)
                ->select();
        }

        $ret = new \ResultModel(false, '暂无数据');
        if(!empty($lineClassTrainsArr)){
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr;
            $ret->count = $lineClassTrainsCount;
        }

        return $this->output($ret);
    }

    /**
     * 获取学生号线路班次详情
     * @param integer $line_class_train_id 班次id（必填）
     */
    public function getYyGroupLineDetail($line_class_train_id){
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s',$currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();

        $where = "yylct.mchid = ".$this->mchid;
        $where .= " AND yylct.line_class_train_id = ".$line_class_train_id;
        $where .= " AND yylct.status = '".\CommonDefine::LINE_CLASS_OPERATE_1."'";
        $where .= " AND yylct.is_del =".\CommonDefine::IS_DEL_0 ;
        $where .= " AND yylcp1.type = 1" ;
        $where .= " AND yylcp2.type = 2" ;

        $order = "yylc.sort asc";
        $field = "yylct.line_class_id,
                yylc.start_time,
                yylc.refund_time_set,
                yylc.start_city_code,gdrs.name as start_city_name,
                yylc.end_city_code,gdre.name as end_city_name,
                yylct.line_class_train_id,
                yylct.line_class_train_no,
                yylct.remain_tickets,
                yylct.price,
                yylc.start_name,
                yylc.end_name,
                yylc.summary,
                yylc.stop_sell_time,
                yylc.stop_sell_number,
                yylct.travel_time,
                yylct.start_date,
                yylcp1.alias as start_point_alias,
                yylcp1.latitude as start_point_latitude,
                yylcp1.longitude as start_point_longitude,
                yylcp2.alias as end_point_alias,
                yylcp2.latitude as end_point_latitude,
                yylcp2.longitude as end_point_longitude";

        $lineClassTrainsArr = M()->table('cp_yy_line_class_train yylct')
            ->join('LEFT JOIN cp_yy_line_class yylc ON yylc.id = yylct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = yylc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = yylc.end_city_code')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp1 ON yylcp1.line_class_id = yylc.id')
            ->join('LEFT JOIN cp_yy_line_class_point yylcp2 ON yylcp2.line_class_id = yylc.id')
            ->page(1, 1)
            ->where($where)
            ->field($field)
            ->group('yylc.id')
            ->order($order)
            ->select();

        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        foreach($lineClassTrainsArr as $k=>$lineClassTrain){
            $lineClassTrainsArr[$k]['is_expire'] = 0;
            $stop_sell_time = \getTimeNumber($lineClassTrain['stop_sell_time']);
            $dayNum = \diffBetweenTwoDays(date('Y-m-d',$currentTime), $lineClassTrain['start_date']);
            if($dayNum == 0){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 0 && $stop_sell_time != 0){
                    $lineClassTrainsArr[$k]['is_expire'] = 1;
                }

                if($lineClassTrain['start_time_type'] == 2){
                    if(\getTimeNumber($lineClassTrain->data['end_latest_time']) > \getTimeNumber($lineClassTrain->data['start_earliest_time'])){//当天
                        if(\getTimeNumber($lineClassTrain['end_latest_time']) < $timeNumber){
                            $lineClassTrainsArr[$k]['is_expire'] = 1;
                        }
                    }
                }else{
                    if(\getTimeNumber($lineClassTrain['start_time']) < $timeNumber){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }else if($dayNum == 1){
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 1 && $stop_sell_time != 0){
                    if(strtotime($lineClassTrain['start_date']) == strtotime(date('Y-m-d',time()))){
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }

            $lineClassTrainsArr[$k]['time_region'] = [];
            if($lineClassTrainsArr[$k]['start_time_type']  == 2){
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time']?substr($lineClassTrainsArr[$k]['start_earliest_time'],0,5):"";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time']?substr($lineClassTrainsArr[$k]['end_latest_time'],0,5):"";
            }else{
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['start_time']?substr($lineClassTrainsArr[$k]['start_time'],0,5):"";
            }

            $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
            $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_yy_line_class_point')
                ->where($startPointWhere)
                ->field($startPointField)
                ->order($startPointOrder)
                ->select();

            $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
            $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_yy_line_class_point')
                ->where($endPointWhere)
                ->field($endPointField)
                ->order($endPointOrder)
                ->select();
        }

        $ret = new \ResultModel(false, '暂无数据');
        if(!empty($lineClassTrainsArr)){
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr[0];
            $ret->count = 1;
        }

        return $this->output($ret);
    }

    /**
     * 获取所有家长互助线路
     * @param integer $page 页码（默认1）
     * @param integer $size 每页大小（默认10）
     */
    public function getParentsHelpLine($page = 1, $size = 200){
        $current_time_stamp = time();
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['mchid'] = $this->mchid;
        $where['residual_seating'] = array('gt', 0);
        $start_time = date('Y-m-d H:i:s',$current_time_stamp);
        $where['start_time'] = array('egt', $start_time);

        $orderBy['start_time'] = 'ASC ';
        if(!empty($start_address_code)){
            $where['start_city_code'] = $start_address_code;
        }
        if(!empty($end_address_code)){
            $where['end_city_code'] = $end_address_code;
        }

        $fields = 'line_parent_help_id,line_parent_help_no as lphn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,end_name,end_address_code,end_address_remark,create_time,center_start_latlng,center_end_latlng';
        $lineParentHelpsR = $this->select($where, $page, $size, $orderBy, 'YyLineParentHelp',$fields);
        if($lineParentHelpsR->ret){
            foreach($lineParentHelpsR->data as $k=>$v){
                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver');
                if($driverR->ret){
                    $lineParentHelpsR->data[$k]['car_brand'] = $driverR->data['car_brand'];
                    $lineParentHelpsR->data[$k]['cellphone'] = $driverR->data['cellphone'];
                    $lineParentHelpsR->data[$k]['name'] = $driverR->data['name'];
                    $lineParentHelpsR->data[$k]['header_ico'] = $driverR->data['header_ico'];
                }
                $lineParentHelpsR->data[$k]['away_time'] = (new \Mygettime($current_time_stamp, strtotime($v['create_time'])))->index();
                $passengerFavDriverR = $this->find(array('passenger_id' => $this->state->user_id, 'driver_id' => $v['driver_id']), 'PassengerFavDriver');
                if($passengerFavDriverR->ret){
                    $lineParentHelpsR->data[$k]['is_fav'] = true;
                }else{
                    $lineParentHelpsR->data[$k]['is_fav'] = false;
                }
            }
        }
        return $this->output($lineParentHelpsR);
    }


    /**
     * 获取关注的家长互助线路
     * @param integer $page 页码（默认1）
     * @param integer $size 每页大小（默认10）
     */
    public function getFavParentsHelpLine($page = 1, $size = 200){
        $current_time_stamp = time();
        $where = "pfd.passenger_id = " . $this->state->user_id;
        $where .= " AND lph.mchid = " . $this->mchid;
        $start_time = date('Y-m-d H:i:s',time());
        $where .= " AND lph.start_time >= '$start_time'";
        $where .= " AND lph.residual_seating > 0";
        $where .= " AND lph.is_del = ".\CommonDefine::IS_DEL_0;

        $fields = "lph.*";
        $lineParentHelpMode = new \YyLineParentHelpModel();
        $passengerFavDriverMode = new \PassengerFavDriverModel();
        $lineParentHelpLines = $passengerFavDriverMode->alias('pfd')->join("LEFT JOIN ".$lineParentHelpMode->getTableName()." lph ON lph.driver_id = pfd.driver_id")
            ->page($page, $size)
            ->where($where)
            ->field($fields)
            ->select();

        $lineParentHelpLinesCount = $passengerFavDriverMode->alias('pfd')->join("LEFT JOIN ".$lineParentHelpMode->getTableName()." lph ON lph.driver_id = pfd.driver_id")
            ->where($where)
            ->field($fields)
            ->count();

        $lineParentHelpLinesR = new \ResultModel(false);
        if($lineParentHelpLinesCount > 0){
            foreach($lineParentHelpLines as $k => $v){
                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver');
                if($driverR->ret){
                    $lineParentHelpLines[$k]['car_brand'] = $driverR->data['car_brand'];
                    $lineParentHelpLines[$k]['cellphone'] = $driverR->data['cellphone'];
                    $lineParentHelpLines[$k]['name'] = $driverR->data['name'];
                    $lineParentHelpLines[$k]['header_ico'] = $driverR->data['header_ico'];
                }
                $lineParentHelpLines[$k]['away_time'] = (new \Mygettime($current_time_stamp, strtotime($v['create_time'])))->index();
                $passengerFavDriverR = $this->find(array('passenger_id' => $this->state->user_id, 'driver_id' => $v['driver_id']), 'PassengerFavDriver');
                if($passengerFavDriverR->ret){
                    $lineParentHelpLines[$k]['is_fav'] = true;
                }else{
                    $lineParentHelpLines[$k]['is_fav'] = false;
                }
            }

            $lineParentHelpLinesR->ret = true;
            $lineParentHelpLinesR->data = $lineParentHelpLines;
            $lineParentHelpLinesR->count = $lineParentHelpLinesCount;
        }

        return $this->output($lineParentHelpLinesR);
    }

    /**
     * 获取家长互助线路详情
     * @param integer $line_parent_help_id 家长互助线路id（必填）
     */
    public function getParentHelpLineDetail($line_parent_help_id){
        $where['line_parent_help_id'] = $line_parent_help_id;
        $where['mchid'] = $this->mchid;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $fields = 'line_parent_help_id,line_parent_help_no as lphn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,end_name,end_address_code,end_address_remark,create_time,center_start_latlng,center_end_latlng';
        $lineParentHelpR = $this->find($where, 'YyLineParentHelp',$fields);
        if($lineParentHelpR->ret){
            $driverR = $this->find(array('driver_id' => $lineParentHelpR->data['driver_id']), 'Driver');
            if($driverR->ret){
                $lineParentHelpR->data['car_brand'] = $driverR->data['car_brand'];
                $lineParentHelpR->data['cellphone'] = $driverR->data['cellphone'];
                $lineParentHelpR->data['name'] = $driverR->data['name'];
                $lineParentHelpR->data['header_ico'] = $driverR->data['header_ico'];
            }
            $lineParentHelpR->data['away_time'] = (new \Mygettime(time(), strtotime($lineParentHelpR->data['create_time'])))->index();
            $passengerFavDriverR = $this->find(array('passenger_id' => $this->state->user_id, 'driver_id' => $lineParentHelpR->data['driver_id']), 'PassengerFavDriver');
            if($passengerFavDriverR->ret){
                $lineParentHelpR->data['is_fav'] = true;
            }else{
                $lineParentHelpR->data['is_fav'] = false;
            }
        }

        return $this->output($lineParentHelpR);
    }

    /**
     * 学生号-获取组团定制线路列表
     * @param string $start_address_remark 出发地（非必填）
     * @param string $end_address_remark 目地址（非必填）
     * @param int $number 乘车人数（非必填）
     * @param string $start_time 开始日期（非必填）
     */
    public function getStudentCustomizedLines($start_address_remark = null, $end_address_remark = null, $number = null, $start_time = null, $page = 1, $size = 10){
        $where = "mchid = ".$this->mchid;
        $where .= " AND status = 2";
        if(!empty($start_address_remark)){
            $where .= " AND start_address_remark like '%$start_address_remark%'";
        }
        if(!empty($end_address_remark)){
            $where .= " AND end_address_remark like '%$end_address_remark%'";
        }
        if(!empty($number)){
            $where .= " AND remain_count >= $number";
        }
        if(!empty($start_time)){
            $where .= " AND start_time >= '$start_time'";
        }
        $where .= " AND (protect_time IS NULL OR protect_time < '".date('Y-m-d H:i:s' ,time())."')";

        $exceptFields = "passenger_id,mchid,branchid,appoint,driver_id";
        $studentCustomizedLinesR =$this->select($where, $page, $size, ' start_time asc ', 'YyStudentCustomizedLine',$exceptFields, true);
        return $this->output($studentCustomizedLinesR);
    }

    /**
     * 学生号-发布组团定制拼车线路
     * @param string $start_address_remark 出发地
     * @param string $start_latitude 开始纬度
     * @param string $start_longitude 开始经度
     * @param string $end_address_remark 目的地
     * @param string $end_latitude 结束纬度
     * @param string $end_longitude 结束经度
     * @param int $everyday_times 每天乘车次数 2次或4次
     * @param string $go_school_time_am 早晨上学时间
     * @param string $leave_school_time_am 早晨放学时间
     * @param string $go_school_time_pm 下午上学时间
     * @param string $leave_school_time_pm 下午放学时间
     * @param int $group_type 组团类型：1- 4人团； 2 - 6人团
     * @param string $start_time 开始时间
     * @param string $order_expire_time 拼团结束时间(非必须)
     * @param int $card_id 卡id
     * @param int $current_number 当前参团人数
     */
    public function doReleaseStudentCustomizedLine($start_address_remark, $start_latitude, $start_longitude, $end_address_remark, $end_latitude, $end_longitude,$everyday_times,$go_school_time_am = null, $leave_school_time_am = null, $go_school_time_pm = null, $leave_school_time_pm = null, $group_type = 1, $start_time, $order_expire_time = null, $card_id, $current_number){
        if(strtotime($order_expire_time) > strtotime($start_time)){
            $this->output(new \ResultModel(false, '拼团结束时间不能大于组团生效开始时间'));
        }

        $cardR = $this->find(array('card_id' => $card_id), 'Card');
        if(!$cardR->ret){
            return $this->output(new \ResultModel(false, '发布失败'));
        }

        if($everyday_times == \YyCommonDefine::EVERYDAY_TIMES_2){
            if(empty($go_school_time_am) || empty($leave_school_time_pm)){
                return $this->output(new \ResultModel(false, '请选择上下学时间'));
            }
        }else if($everyday_times == \YyCommonDefine::EVERYDAY_TIMES_4){
            if(empty($go_school_time_am) || empty($leave_school_time_am) || empty($go_school_time_pm) || empty($leave_school_time_pm)){
                return $this->output(new \ResultModel(false, '请选择上下学时间'));
            }
        }else{
            return $this->output(new \ResultModel(false, '发布失败，请选择每天乘车次数'));
        }

        //学生号定制包车分台指定分台
        $yyStudentCustomizedLineBranchR = $this->find(array('key'=>\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_BRANCH_CONFIG,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
        if(!$yyStudentCustomizedLineBranchR->ret){
            return  $this->output(new \ResultModel(false, '发布失败,还未指定调度分台'));
        }

        $this->data['student_customized_line_no'] = $this->createStudentCustomizedLineNo();
        $this->data['passenger_id'] = $this->state->user_id;
        $this->data['end_time'] = date('Y-m-d 23:59:59', strtotime("$start_time +".$cardR->data['times']." day"));
        $this->data['single_join_price'] = $cardR->data['price'];
        $this->data['total_count'] = $group_type==\YyCommonDefine::GROUP_TYPE_1? 4 : 6;
        $this->data['remain_count'] = $this->data['total_count'];
        $this->data['mchid'] = $this->mchid;
        $this->data['branchid'] = $yyStudentCustomizedLineBranchR->data['value'];
        $this->data['order_expire_time'] = $start_time;

        $this->startTrans();
        $studentCustomizedLineA = $this->add('YyStudentCustomizedLine');
        if(!$studentCustomizedLineA->ret){
            $this->transRollback();
            return $this->output(new \ResultModel(false, '发布失败'));
        }

        $studentCustomizedOrderData['passenger_id'] = $this->state->user_id;
        $studentCustomizedOrderData['student_customized_order_no'] = $this->createStudentCustomizedOrderNo();
        $studentCustomizedOrderData['student_customized_line_id'] = $studentCustomizedLineA->data;
        $studentCustomizedOrderData['price'] = $this->data['single_join_price'] * $current_number;
        $studentCustomizedOrderData['count'] = $current_number;
        $studentCustomizedOrderData['mchid'] = $this->mchid;
        $studentCustomizedOrderA = $this->add('YyStudentCustomizedOrder', $studentCustomizedOrderData);
        if(!$studentCustomizedOrderA->ret){
            $this->transRollback();
            return $this->output(new \ResultModel(false, '发布失败'));
        }
        $this->commitTrans();

        return $this->output(new \ResultModel(true, array('student_customized_order_id' => $studentCustomizedOrderA->data, 'student_customized_order_no' =>$studentCustomizedOrderData['student_customized_order_no'],'price' => $studentCustomizedOrderData['price'])));
    }

    /*
     * 学生号-获取我的定制拼车
     * @param integer $page 页码（默认1）
     * @param integer $size 每页大小（默认10）
     */
    public function getMyStudentCustomizedLines($page = 1, $size = 10){
        $where = "sco.mchid = ".$this->mchid;
        $where .= " AND sco.status = 2";
        $where .= " AND sco.passenger_id = ".$this->state->user_id;
        $field = "scl.student_customized_line_id,
        scl.student_customized_line_no,
        scl.start_address_remark,
        scl.start_longitude,
        scl.start_latitude,
        scl.end_address_remark,
        scl.end_longitude,
        scl.end_latitude,
        scl.everyday_times,
        scl.go_school_time_am,
        scl.leave_school_time_am,
        scl.go_school_time_pm,
        scl.leave_school_time_pm,
        scl.group_type,
        scl.start_time,
        scl.end_time,
        scl.order_expire_time,
        scl.card_id,
        scl.total_count,
        scl.remain_count,
        scl.status,
        sco.student_customized_order_id,
        sco.student_customized_order_no
        ";
        $order = "sco.create_time desc";
        $studentCustomizedLines = M()->table("cp_yy_student_customized_order sco")
            ->join("LEFT JOIN cp_yy_student_customized_line scl ON scl.student_customized_line_id = sco.student_customized_line_id")
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->order($order)
            ->select();
        $studentCustomizedLinesCount = M()->table("cp_yy_student_customized_order sco")
            ->join("LEFT JOIN cp_yy_student_customized_line scl ON scl.student_customized_line_id = sco.student_customized_line_id")
            ->where($where)
            ->count();

        $renewTimeConfig = C('YY.RENEW_TIME');
        $studentCustomizedLinesR = new \ResultModel(false);
        if(!empty($studentCustomizedLines)){
            foreach($studentCustomizedLines as $k => $studentCustomizedLine){
                $studentCustomizedLines[$k]['is_able_renew'] = false;
                $renewWhere = "sco.mchid = ".$this->mchid;
                $renewWhere .= " AND sco.status = 2";
                $renewWhere .= " AND sco.passenger_id = ".$this->state->user_id;
                $renewWhere .= " AND scl.parent_student_customized_line_id = ".$studentCustomizedLine['student_customized_line_id'];

                $studentCustomizedOrder =  M()->table("cp_yy_student_customized_order sco")
                    ->join("LEFT JOIN cp_yy_student_customized_line scl ON scl.student_customized_line_id = sco.student_customized_line_id")
                    ->where($renewWhere)
                    ->select();
                if(empty($studentCustomizedOrder)){
                    if(time() > strtotime($studentCustomizedLine['end_time']." -$renewTimeConfig day")){//达到可续费的时间
                        $studentCustomizedLines[$k]['is_able_renew'] = true;
                    }
                }
            }

            $studentCustomizedLinesR->data = $studentCustomizedLines;
            $studentCustomizedLinesR->count = $studentCustomizedLinesCount;
            $studentCustomizedLinesR->ret = true;
        }

        return $this->output($studentCustomizedLinesR);
    }
}

?>

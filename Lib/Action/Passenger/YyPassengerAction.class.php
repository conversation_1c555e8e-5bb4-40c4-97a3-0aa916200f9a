<?php

namespace Passenger;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-获取用户信息
 *
 * <AUTHOR>
 */
class YyPassengerAction extends \PassengerCoreAction
{
    /**
     * 获取用户信息
     */
    public function getPassengerInfo(){
        $ret = new \ResultModel(false);
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if($passengerR->ret){
            $passengerCardInfoR = $this->find(array('passenger_id' => $this->state->user_id), 'PassengerCardInfo');
            $thirdR = $this->find(array('passenger_id' => $this->state->user_id),'ThirdParty');
            $couponWhere['passenger_id'] = $this->state->user_id;
            $couponWhere['is_del'] = \CommonDefine::IS_DEL_0;
            $couponWhere['status'] = ['in', [\CommonDefine::COUPON_STATUS_0, \CommonDefine::COUPON_STATUS_2]];
            $couponRecordR = $this->count($couponWhere, 'CouponRecord');
            $ret->ret = true;
            $ret->data['passenger_id'] = $this->state->user_id;
            $ret->data['avatar'] =  $thirdR->ret?$thirdR->data['third_avatar']:"";
            $ret->data['name'] = $passengerR->data['name'];
            $ret->data['residual_times'] = $passengerCardInfoR->ret?$passengerCardInfoR->data['residual_times']:0;
            $ret->data['coupon_record'] = $couponRecordR->ret?$couponRecordR->data:0;
            $ret->data['examine_status'] = $passengerR->data['examine_status'];
            $ret->data['passenger_type_id'] = $passengerR->data['passenger_type_id'];
            $ret->data['cellphone'] = $passengerR->data['cellphone'];
        }
        return $this->output($ret);
    }

    /**
     * 学生号认证信息
     * @param string $real_name 学生姓名
     * @param string $ID_number 学生省份证号
     * @param string $student_id_number 学生号
     * @param string $student_class 学生班级
     * @param string $school 学校
     */
    public function doSubmitStudent($real_name, $ID_number, $student_id_number, $student_class, $school){
        $updataPassengerData['passenger_id'] = $this->state->user_id;
        $updataPassengerData['real_name'] = $real_name;
        $updataPassengerData['ID_number'] = $ID_number;
        $updataPassengerData['student_id_number'] = $student_id_number;
        $updataPassengerData['student_class'] = $student_class;
        $updataPassengerData['school'] = $school;
        $updataPassengerData['passenger_type_id'] = 2;
        $updataPassengerData['examine_status'] = \CommonDefine::EXAMINE_STATUS_1;
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if($passengerR->data['examine_status'] === \CommonDefine::EXAMINE_STATUS_0){
            return $this->output(new \ResultModel(false, '已经认证通过'));
        }else if($passengerR->data['examine_status'] === \CommonDefine::EXAMINE_STATUS_1){
            return $this->output(new \ResultModel(false, '认证审核中'));
        }

        $passengerS = $this->save('Passenger', $updataPassengerData);
        return $this->output($passengerS);
    }


    /**
     * 老年号认证信息
     * @param string $real_name 姓名
     * @param string $ID_number 身份证号
     * @param string $birthday 出生日期
     */
    public function doSubmitOld($real_name, $ID_number, $birthday){
        $updataPassengerData['passenger_id'] = $this->state->user_id;
        $updataPassengerData['real_name'] = $real_name;
        $updataPassengerData['ID_number'] = $ID_number;
        $updataPassengerData['birthday'] = $birthday;
        $updataPassengerData['passenger_type_id'] = 1;
        $updataPassengerData['examine_status'] = \CommonDefine::EXAMINE_STATUS_1;
        $passengerR = $this->find(array('passenger_id' => $this->state->user_id), 'Passenger');
        if($passengerR->data['examine_status'] === \CommonDefine::EXAMINE_STATUS_0){
            return $this->output(new \ResultModel(false, '已经认证通过'));
        }else if($passengerR->data['examine_status'] === \CommonDefine::EXAMINE_STATUS_1){
            return $this->output(new \ResultModel(false, '认证审核中'));
        }

        $passengerS = $this->save('Passenger', $updataPassengerData);
        return $this->output($passengerS);
    }

    /**
     * 关注或取消关注 司机
     * @param integer $driver_id 司机id
     */
    public function doFavDriver($driver_id){
        $where['passenger_id'] = $this->state->user_id;
        $where['driver_id'] = $driver_id;

        $this->startTrans();
        $passengerFavDriverR = $this->find($where, 'PassengerFavDriver');
        if($passengerFavDriverR->ret){
            $passengerFavDriverD = $this->delete($passengerFavDriverR->data['passenger_fav_driver_id'], 'PassengerFavDriver');
            if($passengerFavDriverD->ret){
                $driverMode = new \DriverModel();
                if($driverMode->where('driver_id = '.$driver_id)->setDec('fav_count')){
                    $this->commitTrans();
                    return $this->output(new \ResultModel(true, '取消成功'));
                }else{
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '取消失败'));
                }
            }else{
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }
        }else{
            $passengerFavDriverData['passenger_id'] = $this->state->user_id;
            $passengerFavDriverData['driver_id'] = $driver_id;
            $passengerFavDriverA = $this->add('PassengerFavDriver', $passengerFavDriverData);
            if($passengerFavDriverA->ret){
                $driverMode = new \DriverModel();
                if($driverMode->where('driver_id = '.$driver_id)->setInc('fav_count')){
                    $this->commitTrans();
                    return $this->output(new \ResultModel(true, '关注成功'));
                }else{
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '关注失败'));
                }
            }else{
                $this->transRollback();
                return $this->output(new \ResultModel(false, '关注失败'));
            }
        }
    }

}

?>

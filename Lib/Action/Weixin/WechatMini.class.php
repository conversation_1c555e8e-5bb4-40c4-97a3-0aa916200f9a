<?php

/**
 * Description of Wechat
 *
 * <AUTHOR>
 */
include_once('WechatAction.class.php');
include_once("aes_mini/wxBizDataCrypt.php");
include_once("wechatoauth.lib.php");

class WechatMini
{
    # 2023.5.25 @huiyonghkw
    # 微信小程序授权登录配置信息，理论上应该从数据库读取，但是框架代码过低，不再继续维护，暂时配置
    private $singleAppidArr = array(
        220 => array(
            'appid' => 'wx279050ff7cfadc17',
            'secret' => '46141303b7c5cfc3329c7804c0540c02'
        ),
        1018 => array(
            'appid' => 'wxef5eb543a3c45867',
            'secret' => '42af875b3faefc6ddc7a1eef6d2c5e28'
        ),
        1063 => array(
            'appid' => 'wx97075aa435408f91',
            'secret' => 'ef8c3a5526c2b13c130f1a8deac47f70'
        ),
        181 => array(
            'appid' => 'wx012299708e7b119b',
            'secret' => '7dac700ae11a9c24e04ed537ed9995b8'
        ),
        1091 => array(
            'appid' => 'wxc35bcfa899dd6118',
            'secret' => '983828072aaae7dfedbcfbb12102059a'
        ),
        1280 => array(
            'appid' => 'wxbd6d700e6b7d92b8',
            'secret' => 'e35c665c36e6c90bd07b919d5e185292'
        ),
        1292 => array(
            'appid' => 'wx24b9deef999d5cc6',
            'secret' => 'f56cd2cd61d706b8c41a67b8fb5fd9f5',
        ),
        1289 => array(
            'appid' => 'wx93efc5594900ac73',
            'secret' => 'f08d46b687a734c7d0674c75ca78af89',
        ),
        1316 => array(
            'appid' => 'wx7195e37229c33580',
            'secret' => '953d3cb51e3fa6ba3ba55ab1320bab87',
        ),
        1330 => array(
            'appid' => 'wx6f9c88732e86617e',
            'secret' => '1ab45ec140ed570d4183bffde7c08b44',
        ),
        //融达客运
        1344 => array(
            'appid' => 'wxb53ddd0a52519fc0',
            'secret' => 'deaf6956a9ba6b2fadaeb3739bd9a0ca',
        ),
        //吴汇行（吴忠出行）
        1340 => array(
            'appid' => 'wx966791eaf9d33acb',
            'secret' => '3588491ea6b4ced4cc1fa61513c1ca7e',
        ),
        // 沅快车
        1341 => array(
            'appid' => 'wx77f3997b7a6820b4',
            'secret' => '12ca67ba1714d379f54a22a439374016',
        ),
        // 约车万顺
        1294 => array(
            'appid' => 'wx2a12090785530cb1',
            'secret' => 'a5e4e03b0aeec842f5879967d98a80f2',
        ),
        // T6出行
        1356 => array(
            'appid' => 'wx6e3b4372f7bea7e5',
            'secret' => '799d53665976cf4c28fe9b4bd02e6e9a',
        ),
        //领航出行
        1365 => array(
            'appid' => 'wx74cefea08f4650d5',
            'secret' => 'f2d8964c5af9eefc583f596fe35e1d5f',
        ),
        //临清出行
        1377 => array(
            'appid' => 'wxf4ddf0bbdd7c6d6c',
            'secret' => '********************************',
        ),
    );

    /**
     * 开放平台-解密数据
     * @param $cipmchid
     * @param $encryptedData
     * @param $iv
     * @param $sessionKey
     * @return bool
     */
    public function decryptData($mchid, $encryptedData, $iv, $sessionKey)
    {
        $info = self::getapsc($mchid, \CommonDefine::AUTH_TYPE_2);
        $wxBizDataCryptObj = new WXBizDataCrypt($info['appid'], $sessionKey);
        $errCode = $wxBizDataCryptObj->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            return json_decode($data, true);
        } else {
            return false;
        }
    }


    /**
     * 单个小程序-解密数据
     * @param $cipmchid
     * @param $encryptedData
     * @param $iv
     * @param $sessionKey
     * @return bool
     */
    public function decryptDataSingle($mchid, $encryptedData, $iv, $sessionKey)
    {
        $wxBizDataCryptObj = new WXBizDataCrypt($this->singleAppidArr[$mchid]['appid'], $sessionKey);
        $errCode = $wxBizDataCryptObj->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            return json_decode($data, true);
        } else {
            return $errCode;
        }
    }


    public function getapsc($mchid = null, $auth_type = \CommonDefine::AUTH_TYPE_1)
    {
        if (!$mchid) {
            return false;
        }

        $user = M('user_auth')->where('uid=' . $mchid . ' AND auth_type = ' . $auth_type)->find();
        if (time() - $user['timestamp'] > 7100) {
            $user['authorizer_access_token'] = WechatAction::get_auth_user_access_token($mchid, $auth_type);
            M('user_auth')->where('uid=' . $mchid)->save(['authorizer_access_token' => $user['authorizer_access_token']]);
        }

        return array(
            'appid' => $user['authorizer_appid'],
            'access_token' => $user['authorizer_access_token'],
            'component_access_token' => WechatAction::get_component_access_token(),
        );
    }

    public function getSessionKey($code, $mchid)
    {
        $info = self::getapsc($mchid, \CommonDefine::AUTH_TYPE_2);
        $url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" . $info['appid'] . "&js_code=" . $code . "&grant_type=authorization_code&component_appid=" . WECHAT_APPID . "&component_access_token=" . $info['component_access_token'];
        $data = file_get_contents($url);
        $arr = json_decode($data, true);
        if (isset($arr['errcode']) && $arr['errcode']) {
            return false;
        }
        return $arr;
    }

    public function getSessionKeySingle($code, $mchid)
    {
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid=" . $this->singleAppidArr[$mchid]['appid'] . "&secret=" . $this->singleAppidArr[$mchid]['secret'] . "&js_code=" . $code . "&grant_type=authorization_code";
        $data = file_get_contents($url);
        \Log::write($data, \Log::DEBUG, \Log::FILE, LOG_PATH . "wechat.log");
        $arr = json_decode($data, true);
        if (isset($arr['errcode']) && $arr['errcode']) {
            return false;
        }
        return $arr;
    }
}

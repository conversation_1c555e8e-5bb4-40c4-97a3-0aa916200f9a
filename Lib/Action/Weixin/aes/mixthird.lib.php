<?php
namespace LaneWeChat\Core\Aes;

use Home\Controller\MessageController;
use Think\Model;

/**
 * Created by PhpStorm.
 * User: Higgses006
 * Date: 2016/7/16
 * Time: 11:05
 */
class  mixThird
{


    public function get_third_Model()
    {
        $model = new Model('mixthird');
        return $model;
    }

    public function get_self_Model()
    {
        $model = new Model('keywords');
        return $model;
    }

    public function third_keyword_fullmacth($keyword, $tousername)
    {
        $uid = MessageController::getuidbyformuser($tousername);
        if ($keyword && $uid) {
            $model = new Model('mixthird');

            $data = $model->where('uid=' . $uid . ' and keyword = ' . "'" . $keyword . "'")->order('crete_time desc')->limit(1)->find();
            if ($data) {
                return $data;
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    public static function third_keyword_mixmacth($keyword, $tousername)
    {
        $uid = MessageController::getuidbyformuser($tousername);
        if ($keyword && $uid) {
            $model = new Model('mixthird');
            $data = $model->where('uid=' . $uid . ' and keyword like ' . "'%" . $keyword . "%'")->order('crete_time desc')->find();
            if ($data) {
                return $data;
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    public function self_keyword_fullmacth($keyword, $tousername)
    {
        $uid = MessageController::getuidbyformuser($tousername);
        if ($keyword && $uid) {
            $model = new Model('keywords');
            $data = $model->where('uid=' . $uid . ' and keyword = ' . "'" . $keyword . "'")->order('crete_time desc')->limit(1)->find();
            if ($data) {
                return $data;
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    public static function self_keyword_mixmacth($keyword, $tousername)
    {
        $uid = MessageController::getuidbyformuser($tousername);
        if ($keyword && $uid) {
            $model = new Model('keywords');
            $data = $model->where('uid=' . $uid . ' and keyword like ' . "'%" . $keyword . "%'")->order('crete_time desc')->find();
            if ($data) {
                return $data;
            } else {
                return false;
            }

        } else {
            return false;
        }
    }
}
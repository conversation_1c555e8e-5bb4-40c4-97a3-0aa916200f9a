<?php
include_once("wxBizMsgCrypt.php");
include_once(dirname(dirname(__FILE__))."/curl.lib.php");
include_once(dirname(dirname(__FILE__))."/config.php");
include_once(dirname(dirname(__FILE__))."/WechatAction.class.php");

class component_verify_ticket
{
    private $receiveFile;
    private $uid;

    public function __construct()
    {
        $filename = 'third.xml';
        $this->receiveFile = realpath(__ROOT__) . DIRECTORY_SEPARATOR . 'Uploads' . DIRECTORY_SEPARATOR . 'ticket_log' . DIRECTORY_SEPARATOR . $filename;

    }

    /**
     * @return mixed
     */
    public static function getUid($uid = null)
    {
        $id = cookie('mchid');
        if ($uid) {
            $id = $uid;
        }
        return $id;
    }

    public static function verify()
    {

        $third = M('verify');
        $result_one = $third->where('id='.WECHAT_VERIFY)->find();
        return $result_one['component_verify_ticket'];
    }

    public function resolve_xml()
    {

        $third = M('verify');
        $result_one = $third->where('id='.WECHAT_VERIFY)->find();
        $encodingAesKey = ENCODING_AES_KEY;
        $token = WECHAT_TOKEN;
        $appId = WECHAT_APPID;
        $timeStamp = $result_one['timeStamp'];
        $nonce = $result_one['nonce'];
        $msg_sign = $result_one['msg_sign'];
        $result['Encrypt'] = $result_one['entype'];
        $encrypt = $result['Encrypt'];
        $pc = new \WXBizMsgCrypt($token, $encodingAesKey, $appId);
        $msg = '';
        $errCode = $pc->decryptMsg($msg_sign, $timeStamp, $nonce, $encrypt, $msg);
        $xml = (array)simplexml_load_string($msg, 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $unauthorizedarray = array_change_key_case($xml, CASE_LOWER);
        $AuthorizerAppid = $unauthorizedarray['authorizerappid'];
        $authModel = M('user_auth')->where('authorizer_appid=' . '"' . $AuthorizerAppid . '"');
        if ($unauthorizedarray['infotype'] == "unauthorized") {
            $condition['isauthorized'] = 1;
            $authModel->save($condition);
        }
        if ($unauthorizedarray['infotype'] == "authorized") {
            $condition['isauthorized'] = 0;
            if ($authModel->find()) {
                $authModel->data($condition)->save();
            }
        }
        if ($errCode == 0) {
            $xml = new \DOMDocument();
            $xml->loadXML($msg);
            $array_e = $xml->getElementsByTagName('ComponentVerifyTicket');
            $ComponentVerifyTicket = $array_e->item(0)->nodeValue;
            $condition['component_verify_ticket'] = $ComponentVerifyTicket;
            $condition['t_id'] = $ComponentVerifyTicket;
            $condition['timestamp'] = $timeStamp;
            if ($ComponentVerifyTicket) {
                $third->where('id = '.WECHAT_VERIFY)->save($condition);
                echo "success";
                exit;
            }
        } else {
            echo "success";
            exit;
        }
    }

    public function resolution_xml($receiveFile)
    {
        $xml = simplexml_load_file($receiveFile);
        $mes = array();
        foreach ($xml as $key => $value) {
            if (empty($mes)) {
                $mes = array($key => (string)$value);
            } else {
                $mes = array_merge($mes, array($key => (string)$value));
            }
        }
        return $mes;
    }

    /*
     * q清除公众号自身API次数
     * */
    public static function clear_component_api($component_access_token = '')
    {
        if (!$component_access_token) {
            $component_access_token = LoginController::get_component_access_token();
        }
        $url = 'https://api.weixin.qq.com/cgi-bin/component/clear_quota?component_access_token=' . $component_access_token;
        $param = json_encode(array(
            'component_appid' => WECHAT_APPID
        ));
        $data = Curl::callWebServer($url, $param, 'post');

        if ($data['errcode'] == 0) {
            return true;
        } else {
            return $data['errcode'];
        }
    }

    public
    static function clear_third_api($uid = '', $access_token = '')
    {
        if (!$uid) {
            $uid = cookie('uid');
        }
        $url = 'https://api.weixin.qq.com/cgi-bin/clear_quota?access_token=' . $access_token;
        $model = new Model('user_auth');
        $USER = $model->where('uid=' . $uid)->find();
        $appid = $USER['authorizer_appid'];
        $param = json_encode(array(
            'appid' => $appid
        ));
        $postdata = Curl::callWebServer($url, $param, 'POST');
        if ($postdata['errcode'] == 0) {
            return true;
        } else {
            return $postdata['errcode'];
        }


    }

    public static function get_component_access_token()
    {
        $verify = new Model('verify');
        $result_one = $verify->where('id='.WECHAT_VERIFY)->find();
        $condition['id'] = WECHAT_VERIFY;
        $component_verify_ticket = component_verify_ticket::verify();
        $url = "https://api.weixin.qq.com/cgi-bin/component/api_component_token";
        $parma = array(
            'component_appid' => WECHAT_APPID,
            'component_appsecret' => WECHAT_APPSECRET,
            'component_verify_ticket' => $component_verify_ticket
        );
        if (time() - $result_one['date_time'] > 7100) {
            $data = Curl::callWebServer($url, json_encode($parma), 'post');

            $component_access_token = $data['component_access_token'];
            $condition['component_access_token'] = $component_access_token;
            $result_one['date_time'] = time();
            if ($condition['component_access_token']) {
                $verify->save($condition);
            }
            return $component_access_token;
        } else {
            return $result_one['component_access_token'];
        }

    }

    public function refresh_user_access_token($uid = null, $auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        $component_access_token = self::get_component_access_token();
        $refresh_url = 'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $component_access_token;
        $auth_user = self::getAuthUser($uid);
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $auth_user['authorizer_appid'],
            "authorizer_refresh_token" => $auth_user['authorizer_refresh_token'],
        ));
        $postData = Curl::callWebServer($refresh_url, $param, 'post');
        $model = new Model('user_auth');
        $where = 'uid=' . self::getUid($uid)." AND auth_type=".$auth_type;
        $condition['authorizer_access_token'] = $postData['authorizer_access_token'];
        $condition['authorizer_refresh_token'] = $postData['authorizer_refresh_token'];
        $condition['date_time'] = time();
        if ($condition['authorizer_access_token']) {
            $model->where($where)->save($condition);
            return true;
        } else {
            return false;
        }
    }

    public static function getSetOptions()
    {
        return array(
            'token' => WECHAT_TOKEN,
            'encodingaeskey' => ENCODING_AES_KEY,
            'appid' => WECHAT_APPID,
            'appsecret' => WECHAT_APPSECRET,
            'debug' => true,
            'logcallback' => WECHAT_URL
        );
    }

    public static function gettick()
    {
        $data = M('vrify')->where('id='.WECHAT_VERIFY)->find();
        return $data;
    }

    /*日志记录
     * 存入数据库
     * */
    public static function log($content = '')
    {

        $model = new Model('logs');
        $model->add(array('log' => $content));
    }

    /*
      * 获取当前授权用户验证信息
      * */
    public static function getAuthUser($mchid = null, $auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        $uid = self::getUid($mchid);
        if ($mchid) {
            $uid = $mchid;
        }

        $authUser = new Model('user_auth');
        $where = 'uid=' . $uid." AND auth_type=".$auth_type;
        $authUserData = $authUser->where($where)->find();
        return $authUserData;
    }

    public static function get_auth_user_access_token($uid = null, $auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        $user = self::getAuthUser($uid, $auth_type);

        if (time() - $user['timeStamp'] > 7100) {
            self::refresh_user_access_token($uid, $auth_type);
            $user = self::getAuthUser($uid, $auth_type);
            $data = $user['authorizer_access_token'];
            return $data;
        } else {
            return $user['authorizer_access_token'];
        }


    }

    /*
     * 获取授权方的基本信息
     * add bu susirc by 2016-07-08 15:32
     * */
    public function getauthuser_info()
    {
        $authinfo = 'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=' . static::get_component_access_token();
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $this->getauth_appid()
        ));
        $postdata = Curl::callWebServer($authinfo, $param, 'post');

        $model = new Model('wxuser_setup');
        $res = $model->where('uid=' . $this->uid)->find();
        $condition['nick_name'] = $postdata['authorizer_info']['nick_name'];
        $condition['head_img'] = $postdata['authorizer_info']['head_img'];
        $condition['service_type_info'] = $postdata['authorizer_info']['service_type_info']['id'];
        $condition['verify_type_info'] = $postdata['authorizer_info']['verify_type_info']['id'];
        $condition['user_name'] = $postdata['authorizer_info']['user_name'];
        $condition['alias'] = $postdata['authorizer_info']['alias'];
        $condition['qrcode_url'] = $postdata['authorizer_info']['qrcode_url'];
        $condition['business_info'] = json_encode($postdata['authorizer_info']['business_info']);
        $condition['timeStamp'] = time();
        $func_info = '';
        foreach ($postdata['authorization_info']['func_info'] as $item) {
            $func_info .= $item['funcscope_category']['id'] . ',';
        }
        $condition['func_info'] = rtrim($func_info, ',');
        if ($res) {
            $model->where('uid=' . $this->uid)->save($condition);
        } else {
            $condition['uid'] = $this->uid;
            $model->add($condition);
        }

    }


}

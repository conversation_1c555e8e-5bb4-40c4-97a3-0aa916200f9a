<?php
include_once("../curl.lib.php");

/**
 * 用户本地登陆和第三方登陆
 */
class wxuser
{

    /**
     * 微信消息接口入口
     * 所有发送到微信的消息都会推送到该操作
     * 所以，微信公众平台后台填写的api地址则为该操作的访问地址
     */
    protected $token;
    protected $appid;
    protected $appsecret;
    protected $encodingaeskey;
    protected $options;
    protected $uid;


    protected function get_uid()
    {
        return cookie('mchid');

    }


    public static function get_component_access_token()
    {
        $verify = new Model('verify');
        $result_one = $verify->where('id=1')->find();
        $condition['id'] = 1;
        $component_verify_ticket = component_verify_ticket::verify();
        $url = "https://api.weixin.qq.com/cgi-bin/component/api_component_token";
        $parma = array(
            'component_appid' => WECHAT_APPID,
            'component_appsecret' => WECHAT_APPSECRET,
            'component_verify_ticket' => $component_verify_ticket
        );
        if (time() - $result_one['timeStamp'] > 7100) {
            $data = Curl::callWebServer($url, json_encode($parma), 'post');
            $component_access_token = $data['component_access_token'];
            $condition['component_access_token'] = $component_access_token;
            $result_one['timeStamp'] = time();
            if ($condition['component_access_token']) {
                $verify->save($condition);
            }
            return $component_access_token;
        } else {
            return $result_one['component_access_token'];
        }

    }

    public static function get_pre_auth_code()
    {
        $verify = new Model('verify');
        $result_one = $verify->where('id=1')->find();
        $condition['id'] = 1;
        $component_access_token = static::get_component_access_token();

        $pre_auth_code_url = 'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=' . $component_access_token;
        $pre_auth_code_parma = json_encode(array(
            "component_appid" => WECHAT_APPID
        ));
        if (time() - $result_one['timeStamp'] > 500) {

            $pre_auth_code = Curl::callWebServer($pre_auth_code_url, $pre_auth_code_parma, 'post');
            $pre_auth_code = $pre_auth_code['pre_auth_code'];
            $condition['pre_auth_code'] = $pre_auth_code;
            if ($condition['pre_auth_code']) {
                $verify->save($condition);
            }
            return $pre_auth_code;
        } else {
            return $result_one['pre_auth_code'];
        }
    }


    /*
     * 刷新access_token
     * 有效期为7200
     *
     * */
    public function refresh_user_access_token($auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        $component_access_token = static::get_component_access_token();
        $refresh_url = 'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $component_access_token;
        $auth_user = self::getAuthUser($auth_type);
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $auth_user['authorizer_appid'],
            "authorizer_refresh_token" => $auth_user['authorizer_refresh_token'],
        ));
        $postData = Curl::callWebServer($refresh_url, $param, 'post');
        $model = new Model('user_auth');
        $where = 'uid=' . self::get_uid()." AND auth_type=".$auth_type;
        $condition['authorizer_access_token'] = $postData['authorizer_access_token'];
        $condition['authorizer_refresh_token'] = $postData['authorizer_refresh_token'];
        $condition['timeStamp'] = time();
        if ($condition['authorizer_access_token']) {
            $model->where($where)->save($condition);
            return true;
        } else {
            return false;
        }
    }

    /*
     * 获取当前授权用户验证信息
     * */
    public static function getAuthUser($auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        $uid = self::get_uid();
        $authUser = new Model('user_auth');
        $where = 'uid=' . $uid." AND auth_type=".$auth_type;
        $authUserData = $authUser->where($where)->find();
        return $authUserData;
    }

    public static function get_auth_user_access_token($auth_type=\CommonDefine::AUTH_TYPE_1)
    {
        self::refresh_user_access_token($auth_type);
        $user = self::getAuthUser($auth_type);
        $data = $user['authorizer_access_token'];
        return $data;
    }

    /*
     * 获取授权方的基本信息
     * add bu susirc by 2016-07-08 15:32
     * */
    public function getauthuser_info()
    {
        $authinfo = 'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=' . static::get_component_access_token();
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $this->getauth_appid()
        ));
        $postdata = Curl::callWebServer($authinfo, $param, 'post');

        $model = new Model('wxuser_setup');
        $res = $model->where('uid=' . $this->uid)->find();
        $condition['nick_name'] = $postdata['authorizer_info']['nick_name'];
        $condition['head_img'] = $postdata['authorizer_info']['head_img'];
        $condition['service_type_info'] = $postdata['authorizer_info']['service_type_info']['id'];
        $condition['verify_type_info'] = $postdata['authorizer_info']['verify_type_info']['id'];
        $condition['user_name'] = $postdata['authorizer_info']['user_name'];
        $condition['alias'] = $postdata['authorizer_info']['alias'];
        $condition['qrcode_url'] = $postdata['authorizer_info']['qrcode_url'];
        $condition['business_info'] = json_encode($postdata['authorizer_info']['business_info']);
        $condition['timeStamp'] = time();
        $func_info = '';
        foreach ($postdata['authorization_info']['func_info'] as $item) {
            $func_info .= $item['funcscope_category']['id'] . ',';
        }
        $condition['func_info'] = rtrim($func_info, ',');
        if ($res) {
            $model->where('uid=' . $this->uid)->save($condition);
        } else {
            $condition['uid'] = $this->uid;
            $model->add($condition);
        }

    }


}

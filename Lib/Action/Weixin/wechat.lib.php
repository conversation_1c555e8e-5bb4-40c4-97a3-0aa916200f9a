<?php

include_once("aes/component_verify_ticket.class.php");
include_once("wechatrequest.lib.php");
include_once("responseinitiative.lib.php");
include_once('responsepassive.lib.php');

/**
 * 微信公众平台来来路认证，处理中心，消息分发
 * Created by Lane.
 * Author: lane
 * Date: 14-03-03
 * Time: 上午10:20
 * Mail: <EMAIL>
 * Website: http://www.lanecn.com
 */
class Wechats
{

    /**
     * 调试模式，将错误通过文本消息回复显示
     * @var boolean
     */
    private $debug;

    /**
     * 以数组的形式保存微信服务器每次发来的请求
     * @var array
     */
    private $request;
    private $data;

    /**
     * 初始化，判断此次请求是否为验证请求，并以数组形式保存
     * @param string $token 验证信息
     * @param boolean $debug 调试模式，默认为关闭
     */
    public function __construct($token, $debug = FALSE)
    {

//        //未通过消息真假性验证
//        if ($this->isValid() && $this->validateSignature($token)) {
//            return $_GET['echostr'];
//        }
        $this->data = $GLOBALS['HTTP_RAW_POST_DATA'];
        //是否打印错误报告
        $this->debug = $debug;
        //接受并解析微信中心POST发送XML数据
        $xml = (array)simplexml_load_string($GLOBALS['HTTP_RAW_POST_DATA'], 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $array = array_change_key_case($xml, CASE_LOWER);
        $this->request = $array;

    }

    /**
     * 判断此次请求是否为验证请求
     * @return boolean
     */
    private function isValid()
    {
        return isset($_GET['echostr']);
    }

    /**
     * 判断验证请求的签名信息是否正确
     * @param  string $token 验证信息
     * @return boolean
     */
    private function validateSignature($token)
    {
        $signature = $_GET['signature'];
        $timestamp = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $signatureArray = array($token, $timestamp, $nonce);
        sort($signatureArray, SORT_STRING);
        return sha1(implode($signatureArray)) == $signature;
    }

    /**
     * 获取本次请求中的参数，不区分大小
     * @param  string $param 参数名，默认为无参
     * @return mixed
     */
    protected function getRequest($param = FALSE)
    {
        if ($param === FALSE) {
            return $this->request;
        }
        $param = strtolower($param);
        if (isset($this->request[$param])) {
            return $this->request[$param];
        }
        return NULL;
    }

    public static function getxml($xml = null)
    {
        if (!$xml) {
            $xml = $GLOBALS['HTTP_RAW_POST_DATA'];
        }
        //消息与事件接收日志
        $openPlatformEventFile = LOG_PATH . "open-platform-event.log";
        \Log::write($xml, \Log::INFO, \Log::FILE, $openPlatformEventFile);
        //消息与事件接收转发到Swoole API服务器
        $forword = C('CC_INNER_API_HOST') . "/open-platform/serve?" . http_build_query($_GET);
        sendXmlOverPost(
            $forword,
            $xml
        );
        \Log::write($forword, \Log::INFO, \Log::FILE, $openPlatformEventFile);

        $xml = (array)simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $array = array_change_key_case($xml, CASE_LOWER);
        return $array;
    }

    /**
     * 分析消息类型，并分发给对应的函数
     * @return void
     */
    public function run()
    {
        $xml = static::getxml();

        if ($xml['tousername'] == "gh_3c884a361561") {//全网发布
            $msg_signature = $_REQUEST['msg_signature'];
            if ($msg_signature) {
                $postStr = $GLOBALS["HTTP_RAW_POST_DATA"];
                $postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
                $toUsername = $postObj->ToUserName;
                file_put_contents('ccccsimplexml_load_string.txt', json_encode($toUsername));
                if ($toUsername == 'gh_3c884a361561') {
                    $this->encrypt($postStr);
                } else {

                    $this->encrypt($postStr);
                }
            } else {
                return;
            }
        }else if($xml['tousername'] == "gh_8dad206e9538"){
            $msg_signature = $_REQUEST['msg_signature'];
            if ($msg_signature) {
                $postStr = $GLOBALS["HTTP_RAW_POST_DATA"];
                $postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
                $toUsername = $postObj->ToUserName;
                file_put_contents('ccminisimplexml_load_string.txt', json_encode($toUsername));
                if ($toUsername == 'gh_8dad206e9538') {
                    $this->encrypt($postStr);
                } else {

                    $this->encrypt($postStr);
                }
            } else {
                return;
            }
        } else {
            $data = WechatRequest::switchType($this->data);
            if ($data) {
                echo $data;
                exit;
            } else {
                echo 'success';
                exit;
            }

        }

    }

    public function get_Access_token()
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
        $data_string = '{"component_appid":"' . WECHAT_APPID . '" ,"component_appsecret": "' . WECHAT_APPSECRET . '", "component_verify_ticket": "' . component_verify_ticket::gettick() . '" }';
        $getAccessToken = json_decode($this->curl_get_post($url, $data_string));
        $component_access_token = $getAccessToken->component_access_token;
        return $component_access_token;
    }

    public function encrypt($encyptdata)
    {
        $encodingAesKey = ENCODING_AES_KEY;
        $token = WECHAT_TOKEN;
        $appId = WECHAT_APPID;
        $timeStamp = empty($_GET['timestamp']) ? "" : trim($_GET['timestamp']);
        $nonce = empty($_GET['nonce']) ? "" : trim($_GET['nonce']);
        $msg_sign = empty($_GET['msg_signature']) ? "" : trim($_GET['msg_signature']);
        $pc = new WXBizMsgCrypt($token, $encodingAesKey, $appId);
        $xml_tree = new DOMDocument();
        $xml_tree->loadXML($encyptdata);
        $array_e = $xml_tree->getElementsByTagName('Encrypt');
        $encrypt = $array_e->item(0)->nodeValue;
        $format = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%s]]></Encrypt></xml>";
        $from_xml = sprintf($format, $encrypt);
        //第三方收到公众号平台发送的消息
        $msg = '';
        $errCode = $pc->decryptMsg($msg_sign, $timeStamp, $nonce, $from_xml, $msg);
        if ($errCode == 0) {
            $msgObj = json_decode(json_encode(simplexml_load_string($msg, 'SimpleXMLElement', LIBXML_NOCDATA)));
            //	{"ToUserName":"gh_3c884a361561","FromUserName":"ozy4qt1eDxSxzCr0aNT0mXCWfrDE","CreateTime":"1446104321","MsgType":"event","Event":"LOCATION","Latitude":"111.000000","Longitude":"222.000000","Precision":"333.000000"}
            switch ($msgObj->MsgType) {
                case "event":
                    $this->replyEventMessage($timeStamp, $nonce, $msg_sign, $msgObj->Event, $msgObj->ToUserName, $msgObj->FromUserName);
                    break;
                case "text":
                    $this->processTextMessage($timeStamp, $nonce, $msg_sign, $msgObj->Content, $msgObj->ToUserName, $msgObj->FromUserName);
                    break;
                default:
                    break;
            }

        } else {
            file_put_contents('cccxmlerr.txt', json_encode($errCode) . date('Y-m-d H:i:s', time()) . "/n", FILE_APPEND);
        }
    }


    public function processTextMessage($timeStamp, $nonce, $msg_sign, $Content, $toUserName, $fromUserName)
    {

        if ('TESTCOMPONENT_MSG_TYPE_TEXT' == $Content) {
            $text = $Content . '_callback';
            file_put_contents('cccTESTCOMPONENT_MSG_TYPE_TEXT.txt', $Content);
            $this->replyTextMessage($timeStamp, $nonce, $msg_sign, $text, $toUserName, $fromUserName);

        } elseif (stristr($Content, "QUERY_AUTH_CODE")) {
            $textArray = explode(':', $Content);
            file_put_contents('cccQUERY_AUTH_CODE.txt', $Content);
            $this->replyApiTextMessage($timeStamp, $nonce, $msg_sign, $textArray[1], $toUserName, $fromUserName);
        } else {
            $this->replyTextMessage($timeStamp, $nonce, $msg_sign, $Content, $toUserName, $fromUserName);
        }

    }

    public function replyApiTextMessage($timeStamp, $nonce, $msg_sign, $query_auth_code, $toUserName, $fromUserName)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' . component_verify_ticket::get_component_access_token();
        $data = json_encode(array(
            'component_appid' => WECHAT_APPID,
            'authorization_code' => $query_auth_code
        ));
        $getreplyApiTextMessage = json_decode($this->curl_get_post($url, $data, 'post'));
        file_put_contents('ccccgetreplyApiTextMessage.txt', json_encode($getreplyApiTextMessage));
        $text = $query_auth_code . '_from_api';
        $sfromUserName = $getreplyApiTextMessage->authorization_info->authorizer_appid;
        $authorizer_access_token = $getreplyApiTextMessage->authorization_info->authorizer_access_token;
        $authorization = $this->get_authorization($sfromUserName, 'customer_service', '1');
//        if ($authorization == 'ok') {
        file_put_contents('ccccget_authorization.txt', 'ok');
        $this->processWechatTextMessage($text, $fromUserName, $authorizer_access_token);
//        }

    }

    function processWechatTextMessage($text, $fromUserName, $authorizer_access_token)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $authorizer_access_token;
        $data = json_encode(array(
            'touser' => $fromUserName,
            'msgtype' => 'text',
            'text' => array(
                'content' => $text
            )
        ));
        file_put_contents('ccccprocessWechatTextMessagedata.txt', $data);
        $getreplyApiTextMessage = json_decode($this->curl_get_post($url, $data));
        file_put_contents('ccccprocessWechatTextMessage.txt', json_encode($getreplyApiTextMessage));
    }

    public function replyTextMessage($timeStamp, $nonce, $msg_sign, $content, $toUserName, $fromUserName)
    {
        $pc = new WXBizMsgCrypt(WECHAT_TOKEN, ENCODING_AES_KEY, WECHAT_APPID);
        $encryptMsg = '';
        $time = time();
        $text = "<xml><ToUserName><![CDATA[" . $fromUserName . "]]></ToUserName><FromUserName><![CDATA[" . $toUserName . "]]></FromUserName><CreateTime>" . $timeStamp . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $content . "]]></Content></xml>";
        file_put_contents('ccctext.txt', $text);
        $errCode = $pc->encryptMsg($text, $time, $nonce, $encryptMsg);
        if ($errCode == 0) {
            file_put_contents('cccencryptMsg.txt', $encryptMsg);
            exit($encryptMsg);
        } else {
            file_put_contents('cccerrCode.txt', $errCode);
            exit($errCode);

        }

    }

    public function replyEventMessage($timeStamp, $nonce, $msg_sign, $event, $toUserName, $fromUserName)
    {
        $content = $event . "from_callback";

        $this->replyTextMessage($timeStamp, $nonce, $msg_sign, $content, $toUserName, $fromUserName);
    }

    function get_authorization($authorizer_appid, $option_name, $option_value = '1')
    {
        $arraydata = array(
            'location_report',
            'voice_recognize',
            'customer_service'
        );
        if (in_array($option_name, $arraydata)) {
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_set_authorizer_option?component_access_token=' . component_verify_ticket::get_component_access_token();
            $data = json_encode(array(
                'component_appid' => WECHAT_APPID,
                'authorizer_appid' => $authorizer_appid,
                'option_name' => $option_name,
                'option_value' => $option_value
            ));
            $query_authorization = json_decode($this->curl_get_post($url, $data, 'post'));
            component_verify_ticket::log(json_encode($query_authorization));
            if ($query_authorization->errmsg == 'ok') {
                return 'ok';
            } else {
                return 'error';
            }
        } else {
            return 'error';
        }

    }

    private function curl_get_post($url, $data = '', $request = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $tmpInfo = curl_exec($curl);
        if (curl_errno($curl)) {
            echo 'Errno' . curl_error($curl);
        }
        curl_close($curl);
        return $tmpInfo;
    }

    public function chatcallback()
    {

        $token = $_GET['token'];
        if (!$token) {
            echo 'success';
            exit;
        }
        $data = M('third')->where('token=' . "'" . $token . "'")->find();
        if (!$data) {
            echo false;
            exit;
        }
        $xml = (array)simplexml_load_string($this->data, 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $request = array_change_key_case($xml, CASE_LOWER);
        $template = "<xml>
<ToUserName><![CDATA[%s]]></ToUserName>
<FromUserName><![CDATA[%s]]></FromUserName>
<CreateTime>%s</CreateTime>
<MsgType><![CDATA[%s]]></MsgType>
<Content><![CDATA[%s]]></Content>
<MsgId>%s</MsgId>
</xml>";
        $return = sprintf($template, $request['fromusername'], $request['tousername'], $request['createtime'], 'text', "DAFHAFHAUHFQWUHHIOH", $request['msgid']);
        echo $return;
        exit;
    }

    public
    function checkSignature()
    {
        $signature = $_GET["signature"];
        $timestamp = $_GET["timestamp"];
        $nonce = $_GET["nonce"];
        $token = WECHAT_TOKEN;
        $tmpArr = array($token, $timestamp, $nonce);
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);
        if ($tmpStr == $signature) {
            echo $_GET['echostr'];
            return true;
        } else {
            return false;
        }
    }

}
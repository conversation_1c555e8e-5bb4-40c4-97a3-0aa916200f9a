<?php
namespace LaneWeChat\Core;

use Think\Exception;

/**
 * Created by PhpStorm.
 * User: Higgses006
 * Date: 2016/4/27
 * Time: 18:08
 */
class wxaccount
{
    private $uid;

    public function __construct()
    {
        $this->uid = cookie('uid');
        if (!$this->uid) {
            $this->error('请登录', '/admin/login');
        }
    }

    public static function getWxAccount()
    {
        $uid = cookie('uid');

        $model = D('member');
        $model->where('id=' . $uid . " and type=1 and status=1");
        $account = $model->find();
        return array(
            'appid' => $account['appid'],
            'secret' => $account['secret'],
            'access_token' => $account['token']
        );

    }

    public static function token_save($token)
    {
        $mode = M('member');
        $data['token'] = $token;
        $mode->data($data);
        $mode->where('id=' . cookie('uid'))->save();
    }
}
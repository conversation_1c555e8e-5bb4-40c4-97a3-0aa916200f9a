<?php
/**
 * wechat php test
 */


define("TOKEN", "cc_zhaoche");
include_once("Wechat.class.php");

class WeixinAction extends CoreAction
{

    /**
     * 测试获取接口 accesstoken
     */
    public function doTest()
    {
        $wechat = new Wechat();
        $accessToken = $wechat->getWechatDirectAccessToken();
        var_dump($accessToken);
        die;
    }


    /**
     * 创建自定义菜单
     */
    public function doCreateMenu()
    {
        $wechat = new Wechat();
        $ret = $wechat->createMenu($this->accessToken);
        if (!$ret) {
            return $this->output(new \ResultModel(false, "创建菜单失败"));
        }
        if ($ret['errcode'] != 0) {
            return $this->output(new \ResultModel(false, $ret['errmsg']));
        }
        return $this->output(new \ResultModel(true, "创建菜单成功"));
    }


    public function doGetWechatCode()
    {
        $wechat = new Wechat();
        $wechat->getWechatCode();
    }

    public function doGetCode()
    {
        var_dump($_GET['code']);
        die;
    }

    /**
     * 获取用户列表成功
     */
    public function doGetUserList()
    {
        $wechat = new Wechat();
        $ret = $wechat->getWechaUserList();
        if (!$ret) {
            return $this->output(new \ResultModel(false, "获取用户列表失败"));
        }
        return $this->output(new \ResultModel(true, $ret));
    }


    public function doWeiXinValid()
    {
        self::valid();
    }

    public function valid()
    {
        $echoStr = $_GET["echostr"];

        //valid signature , option
        if ($this->checkSignature()) {
            header("content-type:text/html; charset=utf-8");
            echo $echoStr;
            exit;
        }
        echo "error";
        exit;
    }

    public function responseMsg()
    {
        //get post data, May be due to the different environments
        $postStr = $GLOBALS["HTTP_RAW_POST_DATA"];

        //extract post data
        if (!empty($postStr)) {
            /* libxml_disable_entity_loader is to prevent XML eXternal Entity Injection,
               the best way is to check the validity of xml by yourself */
            libxml_disable_entity_loader(true);
            $postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
            $fromUsername = $postObj->FromUserName;
            $toUsername = $postObj->ToUserName;
            $keyword = trim($postObj->Content);
            $time = time();
            $textTpl = "<xml>
							<ToUserName><![CDATA[%s]]></ToUserName>
							<FromUserName><![CDATA[%s]]></FromUserName>
							<CreateTime>%s</CreateTime>
							<MsgType><![CDATA[%s]]></MsgType>
							<Content><![CDATA[%s]]></Content>
							<FuncFlag>0</FuncFlag>
							</xml>";
            if (!empty($keyword)) {
                $msgType = "text";
                $contentStr = "Welcome to wechat world!";
                $resultStr = sprintf($textTpl, $fromUsername, $toUsername, $time, $msgType, $contentStr);
                echo $resultStr;
            } else {
                echo "Input something...";
            }

        } else {
            echo "";
            exit;
        }
    }

    private function checkSignature()
    {
        // you must define TOKEN by yourself
        if (!defined("TOKEN")) {
            throw new Exception('TOKEN is not defined!');
        }

        $signature = $_GET["signature"];
        $timestamp = $_GET["timestamp"];
        $nonce = $_GET["nonce"];

        $token = TOKEN;
        $tmpArr = array($token, $timestamp, $nonce);
        // use SORT_STRING rule
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        if ($tmpStr == $signature) {
            return true;
        } else {
            return false;
        }
    }
}

?>
<?php

include_once("aes/component_verify_ticket.class.php");
include_once("wechat.lib.php");
include_once("curl.lib.php");
include_once("config.php");
include_once("Wechat.class.php");


class WechatAction
{
    protected $uid;
    protected $wxuid;

    private $_url = array(
        'ticket_getticket' => 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token='
    );


    public static function getUID()
    {
        return cookie('mchid');
    }

    public static function chart($mchid)
    {
        if (!$mchid) {
            $mchid = cookie('mchid');
        }
        if (!$mchid) {
            return false;
        }
        $data = M('cipher_control')->where('mchid=' . $mchid)->field('ciphertext')->find();
        return $data['ciphertext'];
    }

    public function test()
    {
        $AuthorizerAppid = 'wxbbc25910ade4591e';
        $condition['isauthorized'] = 1;
        $m = M('user_auth')->where('authorizer_appid=' . '"' . $AuthorizerAppid . '"');
        $data = $m->save($condition);
        print_r($m);
        print_r($data);
    }

    /**
     *授权事件接收URL
     */
    public function getVerify()
    {
        $model = new Model('verify');
        $timeStamp = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $msg_sign = $_GET['msg_signature'];
        $condition['timeStamp'] = $timeStamp;
        $condition['msg_sign'] = $msg_sign;
        $condition['nonce'] = $nonce;
        //        $model->add($condition);
        $postData = $GLOBALS['HTTP_RAW_POST_DATA'];
        if (empty($postData)) {
            $postData = file_get_contents('php://input');
        }
        if ($postData) {
            $condition['entype'] = $postData;
        }
        if ($msg_sign) {
            $model->where('id='.WECHAT_VERIFY)->save($condition);
        }

        /**
         * @Date 2022.07.24
         * <AUTHOR>
         */
        $requestLogFile = LOG_PATH . "component-verify-ticket.log";
        \Log::write($_SERVER['QUERY_STRING'], \Log::DEBUG, \Log::FILE, $requestLogFile);
        \Log::write($postData, \Log::DEBUG, \Log::FILE, $requestLogFile);
        //Send XML DATA to API
        sendXmlOverPost(
            C('CC_INNER_API_HOST') . "/open-platform/serve?" . http_build_query($_GET),
            $postData
        );

        $verify = new component_verify_ticket();
        $verify->resolve_xml();
    }

    /**
     *公众号消息与事件接收URL
     *
     */
    public function eventcallback()
    {
        $xml = (array)simplexml_load_string($GLOBALS['HTTP_RAW_POST_DATA'], 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $array = array_change_key_case($xml, CASE_LOWER);

        $wechat = new Wechats(WECHAT_TOKEN);
        $wechat->run();

    }


    /**
     * 微信登录开始
     */
    /*扫描二维码登录*/
    public function qrlogin()
    {
        $pre_auth_code = static::get_pre_auth_code();
        $uri = C('WEB_ROOT') . 'qrlogincallback';
        $uri = urlencode($uri);
        $url = 'https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid=' . WECHAT_APPID . '&pre_auth_code=' . $pre_auth_code . '&redirect_uri=' . $uri . '&auth_type=1';
        header("Location:" . $url);
        exit();
    }

    /*
     * 获取不通用户扫描后回调的auth_code
     * 公众号授权回调*/
    public function qrlogincallback()
    {
        $uid = self::getUID();
        $this->uid = $uid;
        if (!$uid) {
            $this->error('请登录');
        }
        $condition['auth_code'] = $_GET['auth_code'];
        $condition['isauthorized'] = 0;
        $condition['auth_type'] = \CommonDefine::AUTH_TYPE_1;
        $where = 'uid=' . $uid." AND auth_type=".\CommonDefine::AUTH_TYPE_1;
        if (M('user_auth')->where($where)->find()) {
            M('user_auth')->where($where)->save($condition);
        } else {
            $condition['uid'] = $uid;
            M('user_auth')->add($condition);
        }
        $this->authorization(\CommonDefine::AUTH_TYPE_1);
        $this->getauthuser_info(\CommonDefine::AUTH_TYPE_1);
        $this->setWeChatIndustry($uid);
        $this->addWechatAllTemplateIds($uid, component_verify_ticket::get_auth_user_access_token($uid));
        //        self::create_menu();
        redirect('admin_code');

        exit;
    }

    /**
     * 微信登录开始
     */
    /*扫描二维码登录*/
    public function qrminilogin()
    {
        $pre_auth_code = static::get_pre_auth_code();
        $uri = C('WEB_ROOT') . 'qrminilogincallback';
        $uri = urlencode($uri);
        $url = 'https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid=' . WECHAT_APPID . '&pre_auth_code=' . $pre_auth_code . '&redirect_uri=' . $uri . '&auth_type=2';
        header("Location:" . $url);
        exit();
    }

    /*
     * 获取不通用户扫描后回调的auth_code
     * 公众号授权回调*/
    public function qrminilogincallback()
    {
        $uid = self::getUID();
        $this->uid = $uid;
        if (!$uid) {
            $this->error('请登录');
        }
        $condition['auth_code'] = $_GET['auth_code'];
        $condition['isauthorized'] = 0;
        $condition['auth_type'] = \CommonDefine::AUTH_TYPE_2;
        $where = 'uid=' . $uid." AND auth_type=".\CommonDefine::AUTH_TYPE_2;
        if (M('user_auth')->where($where)->find()) {
            M('user_auth')->where($where)->save($condition);
        } else {
            $condition['uid'] = $uid;
            M('user_auth')->add($condition);
        }

        $this->authorization(\CommonDefine::AUTH_TYPE_2);
        $this->getauthuser_info(\CommonDefine::AUTH_TYPE_2);
        redirect('admin_codemini');

        exit;
    }



    /*add by susirc by 2016-07-08 13:50
    * 使用授权码换取公众号的接口调用凭据和授权信息
    * 并做存储
    * */
    public static function get_component_access_token()
    {
        $verify = new Model('verify');
        $result_one = $verify->where('id='.WECHAT_VERIFY)->find();
        $condition['id'] = WECHAT_VERIFY;
        $component_verify_ticket = component_verify_ticket::verify();
        $url = "https://api.weixin.qq.com/cgi-bin/component/api_component_token";
        $parma = array(
            'component_appid' => WECHAT_APPID,
            'component_appsecret' => WECHAT_APPSECRET,
            'component_verify_ticket' => $component_verify_ticket
        );
        if (time() - $result_one['date_time'] > 7100) {
            $data = Curl::callWebServer($url, json_encode($parma), 'post');

            $component_access_token = $data['component_access_token'];
            $condition['component_access_token'] = $component_access_token;
            $result_one['date_time'] = time();
            if ($condition['component_access_token']) {
                $verify->save($condition);
            }
            return $component_access_token;
        } else {
            return $result_one['component_access_token'];
        }

    }


    public static function get_pre_auth_code()
    {
        $verify = new Model('verify');
        $result_one = $verify->where('id='.WECHAT_VERIFY)->find();
        $condition['id'] = WECHAT_VERIFY;
        $component_access_token = static::get_component_access_token();

        $pre_auth_code_url = 'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=' . $component_access_token;
        $pre_auth_code_parma = json_encode(array(
            "component_appid" => WECHAT_APPID
        ));
        if (time() - $result_one['date_time'] > 500) {
            $predata = Curl::callWebServer($pre_auth_code_url, $pre_auth_code_parma, 'post');
            $pre_auth_code = $predata['pre_auth_code'];
            $condition['pre_auth_code'] = $pre_auth_code;
            if ($condition['pre_auth_code']) {
                $verify->save($condition);
            }
            return $pre_auth_code;
        } else {
            return $result_one['pre_auth_code'];
        }
    }

    /*
    * 使用授权码换取公众号的接口调用凭据和授权信息
    */
    public function authorization($auth_type, $force = false)
    {
        $auth_code = new Model('user_auth');
        $where = 'uid=' . $this->uid." AND auth_type=".$auth_type;
        $auth_codeOBJ = $auth_code->where($where)->find();
        $auth_code_value = $auth_codeOBJ['auth_code'];
        if ($force) {
            $component_access_token = static::get_component_access_token();
            $url = 'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' . $component_access_token;
            $parma = json_encode(array(
                "component_appid" => WECHAT_APPID,
                "authorization_code" => $auth_code_value
            ));
            $authorization = Curl::callWebServer($url, $parma, 'post');
        } else {
            if (time() - $auth_codeOBJ['timeStamp'] > 7000) {
                $component_access_token = static::get_component_access_token();
                $url = 'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' . $component_access_token;
                $parma = json_encode(array(
                    "component_appid" => WECHAT_APPID,
                    "authorization_code" => $auth_code_value
                ));
                $authorization = Curl::callWebServer($url, $parma, 'post');
            } else {
                return true;
            }
        }
        $func_info = '';

        foreach ($authorization['authorization_info']['func_info'] as $item) {
            $func_info .= $item['funcscope_category']['id'] . ',';
        }

        $condition['authorizer_appid'] = $authorization['authorization_info']['authorizer_appid'];
        $condition['authorizer_refresh_token'] = $authorization['authorization_info']['authorizer_refresh_token'];
        $condition['authorizer_access_token'] = $authorization['authorization_info']['authorizer_access_token'];
        $condition['func_info'] = rtrim($func_info, ',');
        $condition['timeStamp'] = time();
        $authorizer_appid = $condition['authorizer_appid'];

        $checkdata = $auth_code->where('uid !=' . $this->uid . ' and authorizer_appid=' . "'" . $condition['authorizer_appid'] . "'")->find();

        if ($checkdata) {
            $this->error('该公众号已在本平台绑定,不能重复绑定', 'admin_index');
        }
        try {
            if ($auth_code->where($where)->save($condition)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception $e) {
            print_r($e);
        }
    }

    /*
     * 刷新access_token
     * 有效期为7200
     *
     * */
    public function refresh_user_access_token($mchId = null, $auth_type = \CommonDefine::AUTH_TYPE_1)
    {
        $component_access_token = static::get_component_access_token();
        if ($component_access_token) {
            $refresh_url = 'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' . $component_access_token;
            $auth_user = self::getAuthUser($mchId, $auth_type);
            $param = json_encode(array(
                "component_appid" => WECHAT_APPID,
                "authorizer_appid" => $auth_user['authorizer_appid'],
                "authorizer_refresh_token" => $auth_user['authorizer_refresh_token'],
            ));
            $postData = Curl::callWebServer($refresh_url, $param, 'post');
            $model = new Model('user_auth');
            $condition['authorizer_access_token'] = $postData['authorizer_access_token'];
            $condition['authorizer_refresh_token'] = $postData['authorizer_refresh_token'];
            $condition['timeStamp'] = time();

            if ($condition['authorizer_access_token']) {
                $model->where('uid=' . $auth_user['uid']." AND auth_type=".$auth_type)->save($condition);
                return true;
            } else {
                return false;
            }
        }
    }

    /*
     * 获取当前授权用户验证信息
     * */
    public static function getAuthUser($mchId = null, $auth_type = \CommonDefine::AUTH_TYPE_1)
    {
        if(empty($mchId)) {
            $mchId = self::getUID();
        }
        $authUser = new Model('user_auth');
        $authUserData = $authUser->where('uid=' . $mchId. " AND auth_type=".$auth_type)->find();
        return $authUserData;
    }

    /**
     *获取用户的accesstoken
     **/

    public static function get_auth_user_access_token($mchId = null, $auth_type = \CommonDefine::AUTH_TYPE_1)
    {
        $user = self::getAuthUser($mchId, $auth_type);

        if (time() - $user['timestamp'] > 7100) {
            self::refresh_user_access_token($mchId, $auth_type);
            $user = self::getAuthUser($mchId, $auth_type);
            return $user['authorizer_access_token'];
        } else {
            return $user['authorizer_access_token'];
        }
    }

    /*
     * 获取授权方的基本信息
     * add bu susirc by 2016-07-08 15:32
     * */
    public function getauthuser_info($auth_type)
    {
        $authinfo = 'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=' . static::get_component_access_token();
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $this->getauth_appid($auth_type)
        ));
        $postdata = Curl::callWebServer($authinfo, $param, 'post');
        $model = new Model('wxuser_setup');
        $where = 'uid=' . $this->uid." AND auth_type=".$auth_type;
        $res = $model->where($where)->find();
        $condition['nick_name'] = $postdata['authorizer_info']['nick_name'];
        $condition['head_img'] = $postdata['authorizer_info']['head_img'];
        $condition['service_type_info'] = $postdata['authorizer_info']['service_type_info']['id'];
        $condition['verify_type_info'] = $postdata['authorizer_info']['verify_type_info']['id'];
        $condition['user_name'] = $postdata['authorizer_info']['user_name'];
        $condition['alias'] = $postdata['authorizer_info']['alias'];
        $condition['qrcode_url'] = $postdata['authorizer_info']['qrcode_url'];
        $condition['business_info'] = json_encode($postdata['authorizer_info']['business_info']);
        $condition['timeStamp'] = time();
        $func_info = '';
        foreach ($postdata['authorization_info']['func_info'] as $item) {
            $func_info .= $item['funcscope_category']['id'] . ',';
        }
        $condition['func_info'] = rtrim($func_info, ',');
        if ($res) {
            $model->where($where)->save($condition);
        } else {
            $condition['uid'] = $this->uid;
            $condition['auth_type'] = $auth_type;
            $model->add($condition);
        }

    }

    /*
     * 获取设置选择项
     * 带完善
     *
     * */
    public function getauth_option($auth_type)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/component/ api_get_authorizer_option?component_access_token' . $this->get_component_access_token();
        $auth_user = $this->getauthuser_info($auth_type);
        $param = json_encode(array(
            "component_appid" => WECHAT_APPID,
            "authorizer_appid" => $auth_user['authorizer_appid'],
            "option_name" => "option_name_value"
        ));
    }

    public function getauth_appid($auth_type)
    {
        $where = 'uid=' . $this->uid." AND auth_type=".$auth_type;
        $model = new Model('user_auth');
        $res = $model->where($where)->find();
        if ($res) {
            return $res['authorizer_appid'];
        } else {
            return false;
        }
    }

    /**
     * 生成默认菜单
     **/
    public function create_menu()
    {
        if (!$this->uid) {
            $this->error('请登录');
        }

        //        $appid = 'wxec7f81f593024560';
        //        $secret = '7b9cb5970ae0a265b61aecb5904b9bee ';
        //        $data = file_get_contents('https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $appid . '&secret=' . $secret);
        //        $arr = json_decode($data, true);
        //        if (isset($arr['errcode']) && $arr['errcode']) {
        //            print_r($data);
        //        }
        //        $token = $arr['access_token'];
        //        $driver = C('WX_DRIVER_URL') . '/mchid/' . $this->uid;
        //        $passenger = C('WX_PASSENGER_URL') . '/mchid/' . $this->uid;
        $driver = 'https://www.cczhaoche.com/driver_index/callback/' . self::chart($this->uid);
        $passenger = 'https://www.cczhaoche.com/passenger_index/callback/' . self::chart($this->uid);
        //        $click = '希格斯网络科技公司/Higgses,Inc.<br/>
        //电话：028-61112106/15680770083<br/>
        //QQ：3120888472<br/>
        //邮箱：<EMAIL><br/>
        //地址：成都高新区香年广场T2-1905
        //';
        //        $menuList = array(
        //            array('id' => '1', 'pid' => '', 'name' => '产品系统', 'type' => '', 'code' => ''),
        //            array('id' => '2', 'pid' => '1', 'name' => 'CC·招车管理系统', 'type' => 'view', 'code' => 'http://mp.weixin.qq.com/s?__biz=MjM5MzAyMzgyMA==&mid=2447641260&idx=1&sn=08556cf26e23a33086c61c6921de3ff2&scene=0#wechat_redirect'),
        //            array('id' => '3', 'pid' => '1', 'name' => '速加·无线联盟系统', 'type' => 'view', 'code' => 'http://mp.weixin.qq.com/s?__biz=MjM5MzAyMzgyMA==&mid=2447641262&idx=1&sn=9ba33bde3d73b2513ad063333f43a3c0&scene=0#wechat_redirect'),
        //
        //            array('id' => '4', 'pid' => '', 'name' => '解决方案', 'type' => '', 'code' => ''),
        //            array('id' => '5', 'pid' => '4', 'name' => '通用（APP/网站/微信）定制开发', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '6', 'pid' => '4', 'name' => '电商O2O解决方案', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '7', 'pid' => '4', 'name' => '广电媒体解决方案', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '8', 'pid' => '4', 'name' => '社交资讯解决方案', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '9', 'pid' => '4', 'name' => '更多行业解决方案', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //
        //            array('id' => '10', 'pid' => '', 'name' => '合作联系', 'type' => '', 'code' => ''),
        //            array('id' => '11', 'pid' => '10', 'name' => '提供服务', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '12', 'pid' => '10', 'name' => '联系我们', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //            array('id' => '13', 'pid' => '10', 'name' => '手机官网', 'type' => 'view', 'code' => 'http://www.higgses.com/'),
        //        );
        //树形排布
        $menuList = array(
            array('id' => '1', 'pid' => '0', 'name' => '我是司机', 'type' => 'view', 'code' => $driver),
            array('id' => '1', 'pid' => '0', 'name' => '我是乘客', 'type' => 'view', 'code' => $passenger),
        );
        $menuList2 = $menuList;
        foreach ($menuList as $key => $menu) {
            foreach ($menuList2 as $k => $menu2) {
                if ($menu['id'] == $menu2['pid']) {
                    $menuList[$key]['sub_button'][] = $menu2;
                    unset($menuList[$k]);
                }
            }
        }
        //处理数据
        foreach ($menuList as $key => $menu) {
            //处理type和code
            if (@$menu['type'] == 'view') {
                $menuList[$key]['url'] = $menu['code'];
                //处理URL。因为URL不能在转换JSON时被转为UNICODE
                $menuList[$key]['url'] = urlencode($menuList[$key]['url']);
            } elseif (@$menu['type'] == 'click') {
                $menuList[$key]['key'] = urlencode($menu['code']);
            } elseif (@!empty($menu['type'])) {
                $menuList[$key]['key'] = $menu['code'];
                if (!isset($menu['sub_button'])) {
                    $menuList[$key]['sub_button'] = array();
                }
            }
            unset($menuList[$key]['code']);
            //处理PID和ID
            unset($menuList[$key]['id']);
            unset($menuList[$key]['pid']);
            //处理名字。因为汉字不能在转换JSON时被转为UNICODE
            $menuList[$key]['name'] = urlencode($menu['name']);
            //处理子类菜单
            if (isset($menu['sub_button'])) {
                unset($menuList[$key]['type']);
                foreach ($menu['sub_button'] as $k => $son) {
                    //处理type和code
                    if ($son['type'] == 'view') {
                        $menuList[$key]['sub_button'][$k]['url'] = $son['code'];
                        $menuList[$key]['sub_button'][$k]['url'] = urlencode($menuList[$key]['sub_button'][$k]['url']);
                    } elseif ($son['type'] == 'click') {
                        $menuList[$key]['sub_button'][$k]['key'] = urlencode($son['code']);
                    } else {
                        $menuList[$key]['sub_button'][$k]['key'] = $son['code'];
                        $menuList[$key]['sub_button'][$k]['sub_button'] = array();
                    }
                    unset($menuList[$key]['sub_button'][$k]['code']);
                    //处理PID和ID
                    unset($menuList[$key]['sub_button'][$k]['id']);
                    unset($menuList[$key]['sub_button'][$k]['pid']);
                    //处理名字。因为汉字不能在转换JSON时被转为UNICODE
                    $menuList[$key]['sub_button'][$k]['name'] = urlencode($son['name']);
                }
            }
        }
        //整理格式
        $data = array();
        $menuList = array_values($menuList);
        $data['button'] = $menuList;
        //转换成JSON
        $data = json_encode($data);
        $data = urldecode($data);
        $url = 'https://api.weixin.qq.com/cgi-bin/menu/create?access_token=' . component_verify_ticket::get_auth_user_access_token($this->uid);
        $data = Curl::callWebServer($url, $data, 'POST');
        print_r($data);
    }

    public function makeurl()
    {
        $uid = $_SESSION['admin_state']->user_id;
        if (!$uid) {
            $uid = cookie('mchid');
        }
        $reault = M('third')->where('mchid=' . $uid)->select();
        if ($reault) {
            $this->assign('data', $reault);
        }
        $this->display('Tpl/Weixin/Public/index.html');
    }

    /**
     *r融合测试回调
     * */
    public function callback()
    {
        $xml = $GLOBALS['HTTP_RAW_POST_DATA'];
        $xml = (array)simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        //将数组键名转换为小写
        $array = array_change_key_case($xml, CASE_LOWER);
        $wechat = new Wechats('wx_ccsobey');
        $wechat->chatcallback();
    }

    public function thirdadd()
    {
        $base = M('third')->where('content=' . '"' . $_POST['content'] . '"')->find();
        $mchid = $_SESSION['admin_state']->user_id;
        if (!$mchid) {
            $mchid = cookie('mchid');
        }
        if (!$_POST['content']) {
            return false;
        }
        $data = $_POST;
        $data['mchid'] = $mchid;
        $auth_user = component_verify_ticket::getAuthUser($mchid);
        $data['token'] = md5(uniqid(rand($auth_user['authorizer_appid'])));
        $data['makeurl'] = C('WEB_ROOT') . 'callback/token/' . $data['token'];
        if ($base) {
            $reault = M('third')->data($data)->where('id = ' . $base['id'])->save();
        } else {
            $reault = M('third')->data($data)->add();
        }
        redirect('/generateurl');


    }

    public function chat()
    {
        $GLOBALS = $GLOBALS['HTTP_RAW_POST_DATA'];
        $token = 'eb9608952aaf1c9ec02e15d93cd0bbf9';
        $APPID = 'wx812e5fb46d3b67bc';
        $ESK = '822efcc733380e8e3fce9be92cc135d887bc9cd9cf3';
        $encrde = new \WXBizMsgCrypt($token, $ESK, $APPID);
        $time = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $enmsg = '';
        component_verify_ticket::log('$get' . json_encode($_GET));
        component_verify_ticket::log('$GLOBALS' . $GLOBALS);
        component_verify_ticket::log('$_post' . json_encode($_POST));
        $dern = $encrde->decryptMsg($_GET['msg_signature'], $_GET['timestamp'], $_GET['nonce'], $GLOBALS, $ing);
        component_verify_ticket::log('$enmsg' . $ing);

    }


    /**
     * 设置所属行业
     */
    public function setWeChatIndustry($mchid)
    {
        //整理格式
        $data = array(
            'industry_id1' => \WeChatIndustryDefine::WECHAT_INDUSTRY_1,
            'industry_id2' => \WeChatIndustryDefine::WECHAT_INDUSTRY_2,
        );

        //转换成JSON
        $data = json_encode($data);
        $data = urldecode($data);
        $url = 'https://api.weixin.qq.com/cgi-bin/template/api_set_industry?access_token=' . component_verify_ticket::get_auth_user_access_token($mchid);
        $data = Curl::callWebServer($url, $data, 'POST');
        if ($data['errcode'] == 0) {
            return true;
        }
        return false;
    }


    /**
     * 获取微信通知模版id
     */
    public function addWeChatTemplateId($template_id_short, $access_token)
    {
        if (empty($template_id_short) || empty($access_token)) {
            return null;
        }


        $data['template_id_short'] = $template_id_short;

        //转换成JSON
        $data = json_encode($data);
        $data = urldecode($data);
        $url = 'https://api.weixin.qq.com/cgi-bin/template/api_add_template?access_token=' . $access_token;
        $retData = Curl::callWebServer($url, $data, 'POST');

        if ($retData['errcode'] != 0) {
            return null;
        }
        return $retData['template_id'];
    }

    /**
     * 删除微信通知模版id
     */
    public function delWeChatTemplateId($template_id, $access_token)
    {
        if (empty($template_id) || empty($access_token)) {
            return false;
        }


        $data['template_id'] = $template_id;

        //转换成JSON
        $data = json_encode($data);
        $data = urldecode($data);
        $url = 'https://api.weixin.qq.com/cgi-bin/template/del_private_template?access_token=' . $access_token;
        $retData = Curl::callWebServer($url, $data, 'POST');
        if ($retData['errcode'] != 0) {
            return false;
        }
        return true;
    }


    /**
     * 添加商户的所有通知模版
     * @param int $mchid 商户id
     * @param string $access_token 微信接口授权凭证
     * @return  bool true-添加成功；false-添加失败；
     */
    public function addWechatAllTemplateIds($mchid, $access_token)
    {
        $wechatTplCategoryArr = M('wechat_tpl_category')->where('type=0')->select();
        if (is_array($wechatTplCategoryArr)) {
            $addSize = 0;
            //$m->startTrans();
            foreach ($wechatTplCategoryArr as $k => $v) {
                $ret = $this->addWeChatTemplateIdsByTplCategoryId($v['id'], $mchid, $access_token);
                if($ret) {
                    $addSize++;
                    //echo "addSize:".$addSize."<br/>";
                }
            }

            if (count($wechatTplCategoryArr) == $addSize) {
                //$m->commit();
                return true;
            } else {
                //echo "count(wechatTplCategoryArr):".count($wechatTplCategoryArr)."<br/>";
                //echo $addSize;die;
                //$m->rollback();
            }
        }
        return false;
    }


    /**
     * 添加微信通知模版
     * @param int $wechatTplCategoryId 微信模版id
     * @param int $mchid 商户id
     * @param string $access_token 微信接口授权凭证
     * @return  bool true-添加成功；false-添加失败；
     */
    public function addWeChatTemplateIdsByTplCategoryId($wechatTplCategoryId, $mchid, $access_token)
    {
        $currenttime = time();
        $wechatTplMode = M('wechat_tpl');
        $wechatTplCategory = M('wechat_tpl_category')->where('type=0 AND id='.$wechatTplCategoryId)->find();
        if(!$wechatTplCategory) {
            return false;
        }

        $tempaleteId = $this->addWeChatTemplateId($wechatTplCategory['template_id_short'], $access_token);
        if (!empty($tempaleteId)) {
            $data['c_id'] = $wechatTplCategoryId;
            $data['mchid'] = $mchid;
            $data['wechat_tpl_code'] = $tempaleteId;
            $data['title'] = $wechatTplCategory['title'];
            $data['content'] = $wechatTplCategory['content'];
            $data['create_time'] = date("Y-m-d H:i:s", $currenttime);
            $data['update_time'] = date("Y-m-d H:i:s", $currenttime);
            $wechatTplR = $wechatTplMode->where($data)->find();
            if ($wechatTplR) {
                return true;
            } else {
                $wechatTplS = $wechatTplMode->add($data);
                if ($wechatTplS) {
                    return true;
                }
            }
        }


        return false;
    }

    /**
     * 修改微信通知模版
     * @param int $wechatTplCategoryId 微信模版id
     * @param int $mchid 商户id
     * @param string $access_token 微信接口授权凭证
     * @return  bool true-添加成功；false-添加失败；
     */
    public function modifyWeChatTemplateIdsByTplCategoryId($wechatTplCategoryId, $mchid, $access_token)
    {
        $currenttime = time();
        $m = new Model();
        $wechatTplMode = M('wechat_tpl');
        $wechatTplCategory = M('wechat_tpl_category')->where('type=0 AND id='.$wechatTplCategoryId)->find();
        if (is_array($wechatTplCategory)) {
            $wechatTplArr = $wechatTplMode->where('c_id='.$wechatTplCategoryId)->select();
            foreach ($wechatTplArr as $k => $v) {
                $m->startTrans();
                $ret = $this->delWeChatTemplateId($v['wechat_tpl_code'], $access_token);
                if ($ret) {
                    $tempaleteId = $this->addWeChatTemplateId($wechatTplCategory['template_id_short'], $access_token);
                    if (!empty($tempaleteId)) {
                        $data['c_id'] = $wechatTplCategoryId;
                        $data['mchid'] = $mchid;
                        $data['update_time'] = date("Y-m-d H:i:s", $currenttime);
                        $wechatTplR = $wechatTplMode->where($data)->find();
                        if($wechatTplR) {
                            if($wechatTplR['wechat_tpl_code'] == $tempaleteId) {
                                $m->commit();
                            } else {
                                $data['wechat_tpl_code'] = $tempaleteId;
                                $wechatTplS = $wechatTplMode->save($data);
                                if($wechatTplS) {
                                    $m->commit();
                                } else {
                                    $m->rollback();
                                }
                            }
                        } else {
                            $data['wechat_tpl_code'] = $tempaleteId;
                            $data['create_time'] = date("Y-m-d H:i:s", $currenttime);
                            $wechatTplA = $wechatTplMode->add($data);
                            if($wechatTplA) {
                                $m->commit();
                            } else {
                                $m->rollback();
                            }

                        }
                    } else {
                        $m->rollback();
                    }
                }
            }
        }

        return true;
    }


    /**
     * 修改微信通知模版
     * @param int $wechatTplCategoryId 微信模版id
     * @param int $mchid 商户id
     * @param string $access_token 微信接口授权凭证
     * @return  bool true-添加成功；false-添加失败；
     */
    public function delWeChatTemplateIdsByTplCategoryId($wechatTplCategoryId, $mchid, $access_token)
    {
        $m = new Model();
        $wechatTplMode = M('wechat_tpl');
        $wechatTplCategory = M('wechat_tpl_category')->where('type=0 AND id='.$wechatTplCategoryId)->find();
        if (is_array($wechatTplCategory)) {
            $wechatTplArr = $wechatTplMode->where('c_id='.$wechatTplCategoryId)->select();
            foreach ($wechatTplArr as $k => $v) {
                $m->startTrans();
                $ret = $this->delWeChatTemplateId($v['wechat_tpl_code'], $access_token);
                if (!$ret) {
                    $m->rollback();
                    return false;
                } else {
                    $wechatTplD = $wechatTplMode->where('c_id='.$wechatTplCategoryId.' AND mchid='.$mchid.' AND wechat_tpl_code='.$v['wechat_tpl_code'])->delete();
                    if($wechatTplD) {
                        $m->commit();
                    } else {
                        $m->rollback();
                        return false;
                    }
                }
            }
        }

        return true;
    }



    /**
     * 修改微信通知模版
     * @param int $wechatTplCategoryId 微信模版id
     * @param int $mchid 商户id
     * @return  array；
     */
    public function getSyncMerchantTpls($mchid)
    {
        $access_token = component_verify_ticket::get_auth_user_access_token($mchid);
        //转换成JSON
        $url = 'https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token=' . $access_token;
        $retData = Curl::callWebServer($url, "", 'GET');
        return $retData;
    }

    /**
     * 删除商户模版
     * @param int $id 商户模版id
     * @param int $mchid 商户id
     * @return  bool true-添加成功；false-添加失败；
     */
    public function delMerchantWeChatTemplateId($id, $mchid)
    {
        $access_token = component_verify_ticket::get_auth_user_access_token($mchid);
        $m = new Model();
        $wechatTplMode = M('wechat_tpl');
        $wechatTplR = $wechatTplMode->where('id='.$id)->find();
        if($wechatTplR) {
            $m->startTrans();
            $ret = $this->delWeChatTemplateId($wechatTplR['wechat_tpl_code'], $access_token);
            if (!$ret) {
                $m->rollback();
                return false;
            } else {
                $wechatTplD = $wechatTplMode->where('id='.$id.' AND mchid='.$mchid)->delete();
                if($wechatTplD) {
                    $m->commit();
                } else {
                    $m->rollback();
                    return false;
                }
            }
        }

        return true;
    }


    /*         * **************************JSSDK部分**************************** */


    /**
     * 获取用jsapi tickets
     * @param bool $force 默认为false:非强制；true强制获取
     */
    public function get_jsapi_ticket($mchId, $force = false)
    {
        $jssapiTicketMode = M('user_auth_jsapi_ticket');
        $jssapiTicketObj = $jssapiTicketMode->where('uid=' . $mchId)->find();

        $userAccessToken = self::get_auth_user_access_token($mchId);
        $ticket_getticket_url = $this->_url['ticket_getticket'].$userAccessToken;

        $currentTimeStamp = time();
        $ticket = '';
        if($jssapiTicketObj) {
            if($force || ($currentTimeStamp - $jssapiTicketObj['timeStamp'] > 7100)) {
                $ticketArr = Curl::callWebServer($ticket_getticket_url, '', 'get');
                if($ticketArr) {
                    $ticket = $ticketArr['ticket'];
                    $ticketSaveData = array(
                        'ticket' => $ticket,
                        'time_stamp' => ($currentTimeStamp + $ticketArr['expires_in']),
                        'update_time' => date('Y-m-d H:i:s'),
                    );
                    $jssapiTicketMode->where('uid=' . $mchId)->save($ticketSaveData);
                }
            } else {
                $ticket = $jssapiTicketObj['ticket'];
            }
        } else {
            $ticketArr = Curl::callWebServer($ticket_getticket_url, '', 'get');
            if($ticketArr) {
                $ticket = $ticketArr['ticket'];
                $ticketSaveData = array(
                    'uid' => $mchId,
                    'ticket' => $ticket,
                    'time_stamp' => ($currentTimeStamp + $ticketArr['expires_in']),
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                );
                $jssapiTicketMode->add($ticketSaveData);
            }
        }

        return $ticket;
    }

    /**
     * 获取jssdk的配置
     * @param int $mchId 商户id
     * @param string $currentUrl 当前网页的URL，不包含#及其后面部分；URL键值对的格式（即key1=value1&key2=value2…）注意的是所有参数名均为小写字符
     * @return array
     */
    public function get_jsapi_config($mchId, $currentUrl)
    {
        $authUser = new Model('user_auth');
        $authUserData = $authUser->where('uid=' . $mchId. ' AND auth_type = '.\CommonDefine::AUTH_TYPE_1)->find();
        $noncestr = $this->_createNonceStr();
        $jsapiTicket = $this->get_jsapi_ticket($mchId);
        if(empty($jsapiTicket)) {
            return null;
        }
        $timestamp = time();
        //对所有待签名参数按照字段名的ASCII 码从小到大排序（字典序）
        $string = 'jsapi_ticket='.$jsapiTicket.'&noncestr='.$noncestr.'&timestamp='.$timestamp.'&url='.$currentUrl;
        $signature = sha1($string);
        return array(
            'appId' => $authUserData['authorizer_appid'],
            'timestamp' => $timestamp,
            'noncestr' => $noncestr,
            'signature' => $signature
        );
    }

    //生成参与签名的字段包括noncestr（随机字符串）
    private function _createNonceStr($length = 16)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

}

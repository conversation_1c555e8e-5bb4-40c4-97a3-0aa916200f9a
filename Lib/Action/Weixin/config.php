<?php
/**
 * 系统主配置文件.
 * @Created by Lane.
 * @Author: lane
 * @Mail <EMAIL>
 * @Date: 14-8-1
 * @Time: 下午1:00
 * @Blog: Http://www.lanecn.com
 */
//版本号
define('LANEWECHAT_VERSION', '1.4');
define('LANEWECHAT_VERSION_DATE', '2014-11-05');

/*
 * 服务器配置，详情请参考@link http://mp.weixin.qq.com/wiki/index.php?title=接入指南
 */
define("WECHAT_URL", 'http://lanewechat.lanecn.com/');
//define('WECHAT_TOKEN', 'wx_ccsobey');
//define('ENCODING_AES_KEY', "5AcANXAFcJrCGV1iZ1yw9zIJugBNJwxwh6d426Km2eA");
/*
 * 开发者配置
 */
//define("WECHAT_APPID", 'wx0ae59c6c21de6263');
//define("WECHAT_APPSECRET", '41d3655d2bc6f40adc03f375a0e47112');

/*
 * SAE平台配置
 */
define("HTTP_ACCESSKEY", '04xmzo3zm5');
define("HTTP_APPNAME", 'imcustom4test');

//-----引入系统所需类库-------------------
//引入错误消息类
include_once 'msg.lib.php';
//引入错误码类
include_once 'msgconstant.lib.php';
//引入CURL类
include_once 'curl.lib.php';

//-----------引入微信所需的基本类库----------------
//引入微信处理中心类
include_once 'wechat.lib.php';
//引入微信请求处理类
include_once 'wechatrequest.lib.php';
//引入微信被动响应处理类
include_once 'responsepassive.lib.php';
//引入微信access_token类
include 'accesstoken.lib.php';

//-----如果是认证服务号，需要引入以下类--------------
//引入微信权限管理类
include_once 'wechatoauth.lib.php';
//引入微信用户/用户组管理类
include_once 'usermanage.lib.php';
//引入微信主动相应处理类
include_once 'responseinitiative.lib.php';
//引入多媒体管理类
include_once 'media.lib.php';
//引入自定义菜单类
include_once 'menu.lib.php';
?>
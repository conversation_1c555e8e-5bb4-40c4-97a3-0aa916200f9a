<?php
require_once "aes/wxBizMsgCrypt.php";
/**
 * 处理请求
 * Created by Lane.
 * User: lane
 * Date: 13-12-19
 * Time: 下午11:04
 * Mail: <EMAIL>
 * Website: http://www.lanecn.com
 */
use Extend\JSSDK;
use Home\Controller\MessageController;
use LaneWeChat\Core\Aes\component_verify_ticket;
use LaneWeChat\Core\Aes\mixThird;
use Think\Model;
use WXBizMsgCrypt;

class WechatRequest
{

    /**
     * @descrpition 分发请求
     * @param $request
     * @return array|string
     */
    public static function switchType($request)
    {

        $third = M('verify');
        $result_one = $third->where('id=1')->find();
        $encodingAesKey = $result_one['encodingaeskey'];
        $token = $result_one['token'];
        $appId = $result_one['appid'];
        $timeStamp = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $msg_signature = $_GET['msg_signature'];
        $pc = new WXBizMsgCrypt($token, $encodingAesKey, $appId);
        $msg = '';
        $errCode = $pc->decryptMsg($msg_signature, $timeStamp, $nonce, $request, $msg);
        if ($errCode != 0) {
            echo 'success';
            exit();
        }
        $xml = (array)simplexml_load_string($msg, 'SimpleXMLElement', LIBXML_NOCDATA);

        //将数组键名转换为小写
        $request = array_change_key_case($xml, CASE_LOWER);
        $request['msg'] = $msg;
        switch ($request['msgtype']) {
//            事件
//            case 'event':
//                $request['event'] = strtolower($request['event']);
//                switch ($request['event']) {
//                    //关注
////                    case 'subscribe':
////                        //二维码关注
////                        if (isset($request['eventkey']) && isset($request['ticket'])) {
////                            $data = self::eventQrsceneSubscribe($request);
////                            //普通关注
////                        } else {
////                            $data = self::eventSubscribe($request);
////                        }
////                        break;
//                    //扫描二维码
////                    case 'scan':
////                        $data = self::eventScan($request);
////                        break;
////                    //地理位置
////                    case 'location':
////                        $data = self::eventLocation($request);
////                        break;
////                    //自定义菜单 - 点击菜单拉取消息时的事件推送
            case 'click':
                $data = self::eventClick($request);
                break;
////                    //自定义菜单 - 点击菜单跳转链接时的事件推送
////                    case 'view':
////                        $data = self::eventView($request);
////                        break;
////                    //自定义菜单 - 扫码推事件的事件推送
////                    case 'scancode_push':
////                        $data = self::eventScancodePush($request);
////                        break;
////                    //自定义菜单 - 扫码推事件且弹出“消息接收中”提示框的事件推送
////                    case 'scancode_waitmsg':
////                        $data = self::eventScancodeWaitMsg($request);
////                        break;
////                    //自定义菜单 - 弹出系统拍照发图的事件推送
////                    case 'pic_sysphoto':
////                        $data = self::eventPicSysPhoto($request);
////                        break;
////                    //自定义菜单 - 弹出拍照或者相册发图的事件推送
////                    case 'pic_photo_or_album':
////                        $data = self::eventPicPhotoOrAlbum($request);
////                        break;
////                    //自定义菜单 - 弹出微信相册发图器的事件推送
////                    case 'pic_weixin':
////                        $data = self::eventPicWeixin($request);
////                        break;
////                    //自定义菜单 - 弹出地理位置选择器的事件推送
////                    case 'location_select':
////                        $data = self::eventLocationSelect($request);
////                        break;
////                    //取消关注
////                    case 'unsubscribe':
////                        $data = self::eventUnsubscribe($request);
////                        break;
////                    //群发接口完成后推送的结果
////                    case 'masssendjobfinish':
////                        $data = self::eventMassSendJobFinish($request);
////                        break;
////                    //模板消息完成后推送的结果
////                    case 'templatesendjobfinish':
////                        $data = self::eventTemplateSendJobFinish($request);
////                        break;
//                    default:
//                        return Msg::returnErrMsg(MsgConstant::ERROR_UNKNOW_TYPE, '收到了未知类型的消息', $request);
//                        break;
//                }
//                break;
//            文本
            case 'text':
                $data = self::text($request);
                break;
//            //图像
//            case 'image':
//                $data = self::image($request);
//                break;
//            //语音
//            case 'voice':
//                $data = self::voice($request);
//                break;
//            //视频
//            case 'video':
//                $data = self::video($request);
//                break;
//            //小视频
//            case 'shortvideo':
//                $data = self::shortvideo($request);
//                break;
//            //位置
//            case 'location':
//                $data = self::location($request);
//                break;
//            //链接
//            case 'link':
//                $data = self::link($request);
//                break;
            default:
                return ResponsePassive::text($request['fromusername'], $request['tousername'], '收到未知的消息，我不知道怎么处理');
                break;
        }
        return $data;
    }


    /**
     * @descrpition 文本
     * @param $request
     * @return array
     */
    public static function text(&$request)
    {
        $mchid = $_SESSION['admin_state']->user_id;
        if (!$mchid) {
            $mchid = cookie('mchid');
        }
        $value = M('third')->where('mchid=' . $mchid)->find();
        if ($value['type'] == '1') {
            $base_url = $_SERVER['SERVER_NAME'];
            $id = $value['content'];
            $id = rtrim($id, ',');
            $model = new Model('post');
            $data = $model->where('id in' . "(" . $id . ")")->select();
            if ($data) {
                foreach ($data as $item) {
                    $PicUrl = 'http://' . $base_url . $item['uploads'];
                    $link = $base_url . '/Home/news/newspreview/newsid/' . $item['id'];
                    $items [] = ResponsePassive::newsItem($item['title'], $item['summary'], $PicUrl, $link);
                }
                if ($items) {
                    $encryptMsg = ResponsePassive::news($request['fromusername'], $request['tousername'], $items);
                } else {
                    return false;
                }
            }
        }
        if ($value['type'] == '0') {
            $encryptMsg = ResponsePassive::text($request['fromusername'], $request['tousername'], $value['content'], '0');
        }

        if ($encryptMsg) {
            return $encryptMsg;
        } else {
            echo 'success';
            exit();
        }
    }

    /**
     * @descrpition 图像
     * @param $request
     * @return array
     */
    public static function image(&$request)
    {
        $content = '收到图片';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 语音
     * @param $request
     * @return array
     */
    public static function voice(&$request)
    {
        if (!isset($request['recognition'])) {
            $content = '收到语音';
            return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
        } else {
            $content = '收到语音识别消息，语音识别结果为：' . $request['recognition'];
            return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
        }
    }

    /**
     * @descrpition 视频
     * @param $request
     * @return array
     */
    public static function video(&$request)
    {
        $content = '收到视频';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 视频
     * @param $request
     * @return array
     */
    public static function shortvideo(&$request)
    {
        $content = '收到小视频';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 地理
     * @param $request
     * @return array
     */
    public static function location(&$request)
    {
        $content = '收到上报的地理位置';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 链接
     * @param $request
     * @return array
     */
    public static function link(&$request)
    {
        $content = '收到连接';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 关注
     * @param $request
     * @return array
     */
    public static function eventSubscribe(&$request)
    {
        $content = MessageController::getwelcome($request['tousername']);
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 取消关注
     * @param $request
     * @return array
     */
    public static function eventUnsubscribe(&$request)
    {
        $content = '为什么不理我了？';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 扫描二维码关注（未关注时）
     * @param $request
     * @return array
     */
    public static function eventQrsceneSubscribe(&$request)
    {
        $content = MessageController::getwelcome($request['tousername']);
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 扫描二维码（已关注时）
     * @param $request
     * @return array
     */
    public static function eventScan(&$request)
    {
        $content = '您已经关注了哦～';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 上报地理位置
     * @param $request
     * @return array
     */
    public static function eventLocation(&$request)
    {
        $content = '收到上报的地理位置';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 点击菜单拉取消息时的事件推送
     * @param $request
     * @return array
     */
    public static function eventClick(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = $eventKey;
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 点击菜单跳转链接时的事件推送
     * @param $request
     * @return array
     */
    public static function eventView(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到跳转链接事件，您设置的key是' . $eventKey;
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 扫码推事件的事件推送
     * @param $request
     * @return array
     */
    public static function eventScancodePush(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到扫码推事件的事件，您设置的key是' . $eventKey;
        $content .= '。扫描信息：' . $request['scancodeinfo'];
        $content .= '。扫描类型(一般是qrcode)：' . $request['scantype'];
        $content .= '。扫描结果(二维码对应的字符串信息)：' . $request['scanresult'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 扫码推事件且弹出“消息接收中”提示框的事件推送
     * @param $request
     * @return array
     */
    public static function eventScancodeWaitMsg(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到扫码推事件且弹出“消息接收中”提示框的事件，您设置的key是' . $eventKey;
        $content .= '。扫描信息：' . $request['scancodeinfo'];
        $content .= '。扫描类型(一般是qrcode)：' . $request['scantype'];
        $content .= '。扫描结果(二维码对应的字符串信息)：' . $request['scanresult'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 弹出系统拍照发图的事件推送
     * @param $request
     * @return array
     */
    public static function eventPicSysPhoto(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到弹出系统拍照发图的事件，您设置的key是' . $eventKey;
        $content .= '。发送的图片信息：' . $request['sendpicsinfo'];
        $content .= '。发送的图片数量：' . $request['count'];
        $content .= '。图片列表：' . $request['piclist'];
        $content .= '。图片的MD5值，开发者若需要，可用于验证接收到图片：' . $request['picmd5sum'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 弹出拍照或者相册发图的事件推送
     * @param $request
     * @return array
     */
    public static function eventPicPhotoOrAlbum(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到弹出拍照或者相册发图的事件，您设置的key是' . $eventKey;
        $content .= '。发送的图片信息：' . $request['sendpicsinfo'];
        $content .= '。发送的图片数量：' . $request['count'];
        $content .= '。图片列表：' . $request['piclist'];
        $content .= '。图片的MD5值，开发者若需要，可用于验证接收到图片：' . $request['picmd5sum'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 弹出微信相册发图器的事件推送
     * @param $request
     * @return array
     */
    public static function eventPicWeixin(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到弹出微信相册发图器的事件，您设置的key是' . $eventKey;
        $content .= '。发送的图片信息：' . $request['sendpicsinfo'];
        $content .= '。发送的图片数量：' . $request['count'];
        $content .= '。图片列表：' . $request['piclist'];
        $content .= '。图片的MD5值，开发者若需要，可用于验证接收到图片：' . $request['picmd5sum'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * @descrpition 自定义菜单 - 弹出地理位置选择器的事件推送
     * @param $request
     * @return array
     */
    public static function eventLocationSelect(&$request)
    {
        //获取该分类的信息
        $eventKey = $request['eventkey'];
        $content = '收到点击跳转事件，您设置的key是' . $eventKey;
        $content .= '。发送的位置信息：' . $request['sendlocationinfo'];
        $content .= '。X坐标信息：' . $request['location_x'];
        $content .= '。Y坐标信息：' . $request['location_y'];
        $content .= '。精度(可理解为精度或者比例尺、越精细的话 scale越高)：' . $request['scale'];
        $content .= '。地理位置的字符串信息：' . $request['label'];
        $content .= '。朋友圈POI的名字，可能为空：' . $request['poiname'];
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * 群发接口完成后推送的结果
     *
     * 本消息有公众号群发助手的微信号“mphelper”推送的消息
     * @param $request
     */
    public static function eventMassSendJobFinish(&$request)
    {
        //发送状态，为“send success”或“send fail”或“err(num)”。但send success时，也有可能因用户拒收公众号的消息、系统错误等原因造成少量用户接收失败。err(num)是审核失败的具体原因，可能的情况如下：err(10001), //涉嫌广告 err(20001), //涉嫌政治 err(20004), //涉嫌社会 err(20002), //涉嫌色情 err(20006), //涉嫌违法犯罪 err(20008), //涉嫌欺诈 err(20013), //涉嫌版权 err(22000), //涉嫌互推(互相宣传) err(21000), //涉嫌其他
        $status = $request['status'];
        //计划发送的总粉丝数。group_id下粉丝数；或者openid_list中的粉丝数
        $totalCount = $request['totalcount'];
        //过滤（过滤是指特定地区、性别的过滤、用户设置拒收的过滤，用户接收已超4条的过滤）后，准备发送的粉丝数，原则上，FilterCount = SentCount + ErrorCount
        $filterCount = $request['filtercount'];
        //发送成功的粉丝数
        $sentCount = $request['sentcount'];
        //发送失败的粉丝数
        $errorCount = $request['errorcount'];
        $content = '发送完成，状态是' . $status . '。计划发送总粉丝数为' . $totalCount . '。发送成功' . $sentCount . '人，发送失败' . $errorCount . '人。';
        return ResponsePassive::text($request['fromusername'], $request['tousername'], $content);
    }

    /**
     * 群发接口完成后推送的结果
     *
     * 本消息有公众号群发助手的微信号“mphelper”推送的消息
     * @param $request
     */
    public static function eventTemplateSendJobFinish(&$request)
    {
        //发送状态，成功success，用户拒收failed:user block，其他原因发送失败failed: system failed
        $status = $request['status'];
        if ($status == 'success') {
            //发送成功
        } else if ($status == 'failed:user block') {
            //因为用户拒收而发送失败
        } else if ($status == 'failed: system failed') {
            //其他原因发送失败
        }
        return true;
    }
}

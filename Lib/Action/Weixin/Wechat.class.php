<?php

/**
 * Description of Wechat
 *
 * <AUTHOR>
 */
include_once('WechatAction.class.php');
include_once("aes/component_verify_ticket.class.php");
include_once("wechatoauth.lib.php");

class Wechat
{
    //    public $appIdForPay = 'wxec7f81f593024560';//higgses科技
    //    public $appIdForPay = "wxbbc25910ade4591e";//higgses服务
    public $appId = 'wxec7f81f593024560'; //higgses科技
    public $appIdForPay = 'wxf0263b790aec3378'; //CC招车公众号招车
    //public $appId = 'wx4a466ab9480ecb73';//轨迹招车

    public $codeAccessToke = "";

    //    protected $appSecretForPay = '7b9cb5970ae0a265b61aecb5904b9bee';//higgses科技
    //    protected $appSecretForPay = "0d30e1edcda6d530d33f10bd16c9ecf3";//higgses服务
    protected $appSecret = '7b9cb5970ae0a265b61aecb5904b9bee'; //higgses科技
    protected $appSecretForPay = 'aa03f260417b21f4fa93c0fe3a2b31e3'; //CC招车公众号招车

    protected $tempIdArr = array(
        "MESSAGE_TMEP" => "MWGA2HbBxZM2x4n1pf2lRwfSpIf7EdJAgc3e6M-BoLw",
        "SYSTEM_INFO_TEMP" => "-5kLGWdIvphSnvfCoWARivU5SKieLndoTBT1ZrQr6tw",
    );

    protected $urlArrPost = array(
        "MENU_CREATE" => 'https://api.weixin.qq.com/cgi-bin/menu/create?access_token=',
        "MESSAGE_TMEP" => "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="
    );

    protected $urlArrGet = array(
        "USER_LIST" => "https://api.weixin.qq.com/cgi-bin/user/get?access_token=",
    );

    /*获取当前商户加密后的id串
     * */
    public static function chart($mchid)
    {
        if (!$mchid) {
            $mchid = cookie('mchid');
        }
        if (!$mchid) {
            return false;
        }
        $data = M('cipher_control')->where('mchid=' . $mchid)->field('ciphertext')->find();
        return $data['ciphertext'];
    }

    public function getapsc($cipmchid = null, $auth_type = \CommonDefine::AUTH_TYPE_1)
    {
        if (!$cipmchid) {
            return false;
        }
        $mchidArr = M('cipher_control')->where("ciphertext='" . $cipmchid . "'")->find();
        if (empty($mchidArr['mchid'])) {
            return false;
        }
        $mchid = $mchidArr['mchid'];
        $user = M('user_auth')->where('uid=' . $mchid . ' AND auth_type = ' . $auth_type . ' AND isauthorized = 0')->find();
        if (time() - $user['timestamp'] > 7100) {
            $user['authorizer_access_token'] = WechatAction::get_auth_user_access_token($mchid, $auth_type);
            M('user_auth')->where('uid=' . $mchid . " AND auth_type = " . $auth_type . ' AND isauthorized = 0')->save(['authorizer_access_token' => $user['authorizer_access_token']]);
        }

        return array(
            'appid' => $user['authorizer_appid'],
            'access_token' => $user['authorizer_access_token'],
            'component_access_token' => WechatAction::get_component_access_token(),
        );
    }

    public function getAppidAndSecret($mchid)
    {
        $secret = M('admin')->where('admin_id=' . $mchid)->find();
        if (empty($secret)) {
            return false;
        }
        $secret = $secret['appsecret'];
        $user = M('user_auth')->where('uid=' . $mchid)->find();
        if (empty($user)) {
            return false;
        }
        return array(
            'appid' => $user['authorizer_appid'],
            'secret' => $secret,
            'access_token' => $user['authorizer_access_token']
        );
    }

    /**
     * 获取开放平台授权url地址
     * @param $redirect_uri
     * @param $state
     * @param $cipmchid
     * @return string
     */
    public function getAuthorizeUrl($redirect_uri, $state, $cipmchid)
    {
        $info = self::getapsc($cipmchid);
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $info['appid'] . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_userinfo&state=" . $state . "&component_appid=" . WECHAT_APPID . "#wechat_redirect";
        return  $url;
    }

    public function getCodeAccessTokenUrl($code, $cipmchid)
    {
        $info = self::getapsc($cipmchid);
        $url = "https://api.weixin.qq.com/sns/oauth2/component/access_token?appid=" . $info['appid'] . "&code=" . $code . "&grant_type=authorization_code&component_appid=" . WECHAT_APPID . "&component_access_token=" . $info['component_access_token'];
        return $url;
    }

    public function getCodeAccessTokenUrlForPay($code)
    {
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . $this->appIdForPay . "&secret=" . $this->appSecretForPay . "&code=" . $code . "&grant_type=authorization_code";
        return $url;
    }


    public function getUserInfo($code, $openid)
    {
        return \LaneWeChat\Core\WeChatOAuth::getUserInfo($code, $openid);
    }

    public function getUserInfoUrl($code, $openid)
    {
        $queryUrl = 'https://api.weixin.qq.com/sns/userinfo?access_token=' . $code . '&openid=' . $openid . '&lang=zh_CN';
        return $queryUrl;
    }

    public function getSessionKey($code, $cipmchid)
    {
        $info = self::getapsc($cipmchid, \CommonDefine::AUTH_TYPE_1);
        $url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" . $info['appid'] . "&js_code=" . $code . "&grant_type=authorization_code&component_appid=" . WECHAT_APPID . "&component_access_token=" . $info['component_access_token'];

        $data = file_get_contents($url);
        $arr = json_decode($data, true);
        if (isset($arr['errcode']) && $arr['errcode']) {
            return false;
        }
        return $arr;
    }

    /**
     * 获取token
     */
    public function getWechatAccessToken()
    {
        return component_verify_ticket::get_auth_user_access_token();
        //        $data = file_get_contents('https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $this->appId . '&secret=' . $this->appSecret);
        //        $arr = json_decode($data, true);
        //        if (isset($arr['errcode']) && $arr['errcode']) {
        //            return false;
        //        }
        //        return $arr['access_token'];
    }

    /**
     * 希格斯科技获取token
     */
    public function getWechatDirectAccessToken()
    {
        $data = file_get_contents('https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $this->appId . '&secret=' . $this->appSecret);
        $arr = json_decode($data, true);
        if (isset($arr['errcode']) && $arr['errcode']) {
            return false;
        }
        return $arr['access_token'];
    }

    /**
     * 获取用户openid
     * @param int $type 0-司机；1-乘客
     */
    public function getWechatCode($type = 1)
    {
        $uriArr = array(
            urlencode("https://www.cczhaoche.com/Weixin/Weixin/doGetCode"),
            urlencode("https://www.cczhaoche.com/passenger_index")
        );
        $redirect_uri = $uriArr[$type];
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_base&state=123#wechat_redirect";
        header("Location:" . $url);
        /* $data = file_get_contents($url);
         $arr = json_decode($data, true);*/
        var_dump("jkljl");
    }


    public function getWechaUserList()
    {
        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $url = $this->urlArrGet['USER_LIST'];
        $url .= $accessToken;
        $data = file_get_contents($url);
        $userList = json_decode($data, true);
        return $userList;
    }

    /**
     * 司机推送订单消息
     * @param string $openid 用户微信openid
     * @param int $type 0-新订单；1-接收；2-拒绝；
     * @param string $order_id 订单编号
     * @param string $date 订单生成时间
     */
    public function doPostToDriverOrderMessage($openid, $status = 0, $order_id, $orstatus, $uid = null)
    {

        $mchid = cookie('mchid');
        if ($uid) {
            $mchid = $uid;
        }
        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $url = C('WX_PASSENGER_URL');
        if ($status == 0) {
            $url = C('WX_DRIVER_URL');
        }

        $url .= '/callback/' . self::chart($mchid);
        $data = M('newstemplate')->where('mchid=' . $mchid . '  and type=1')->find();
        $tem1 = "您有新订单，下单时间为" . date('Y-m-d H:i', time()) . "，请尽快处理!";
        $tem2 = "司机没有接受您的订单，您的订单" . $order_id . "已取消，如有疑问请拨打028-61112106";
        $tem3 = "乘客已取消了本次行程，如有疑问请拨打028-61112106";
        $tem4 = "司机接受了你的预约，如有疑问请拨打028-61112106";
        $orderInfo = array(
            $tem1,
            $tem2,
            $tem3,
            $tem4
        );
        $messgeArr = array(
            "touser" => $openid,
            "template_id" => $data['newsid'],
            "url" => $url,
            "data" => array(
                "first" => array(
                    "value" => $orderInfo[$status],
                    "color" => "#173177"
                ),
                "keyword1" => array(
                    "value" => $order_id,
                    "color" => "#173177"
                ),
                "keyword2" => array(
                    "value" => $orstatus,
                    "color" => "#173177"
                ),
                "remark" => array(
                    "value" => "详情请点击查看！",
                    "color" => "#173177"
                )
            )
        );
        $url = $this->urlArrPost["MESSAGE_TMEP"];
        $url .= $accessToken;
        $ret = $this->http_post_json($url, $messgeArr);
        return $ret;
    }


    /**
     * 司机证件审核消息
     * @param string $openid 用户微信openid
     * @param int $type 0-新订单；1-接收；2-拒绝；
     * @param string $order_id 订单编号
     * @param string $date 订单生成时间
     */
    public function doPostToDriverPasspordMessage($openid, $status = 0, $result, $reason, $tel, $uid = '')
    {
        $mchid = cookie('mchid');

        if ($uid) {
            $mchid = $uid;
        }

        $accessToken = $this->getWechatAccessToken();

        if (!$accessToken) {
            return false;
        }
        $url = C('WX_PASSENGER_URL');
        if ($status == 0) {
            $url = C('WX_DRIVER_URL');
        }
        $url .= '/callback/' . self::chart($mchid);
        $data = M('newstemplate')->where('mchid=' . $mchid . '  and type=0')->find();
        $orderInfo = array(
            "您的车辆证件信息已通过审核!",
            "您的车辆证件信息审核失败，请重新提交资料!",
            "您的账户年费已缴清！"
        );
        $messgeArr = array(
            "touser" => $openid,
            "template_id" => $data['newsid'],
            "url" => $url,
            "data" => array(
                "first" => array(
                    "value" => $orderInfo[$status],
                    "color" => "#173177"
                ),
                "keyword1" => array(
                    "value" => $result,
                    "color" => "#173177"
                ),
                "keyword2" => array(
                    "value" => $reason,
                    "color" => "#173177"
                ),
                "remark" => array(
                    "value" => "详情请点击查看！",
                    "color" => "#173177"
                )
            )
        );

        $url = $this->urlArrPost["MESSAGE_TMEP"];
        $url .= $accessToken;
        $ret = $this->http_post_json($url, $messgeArr);
        return $ret;
    }

    /**
     * 司机订单消息提醒
     * @param string $openid 用户微信openid
     * @param int $type 0-新订单；1-接收；2-拒绝；
     * @param string $order_id 订单编号
     * @param string $date 订单生成时间
     */
    public function doPostToDriverNewOderMessage($openid, $status = 0, $name, $date, $tel, $uid = '')
    {
        $mchid = cookie('mchid');

        if ($uid) {
            $mchid = $uid;
        }

        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $url = C('WX_PASSENGER_URL');
        if ($status == 0) {
            $url = C('WX_DRIVER_URL');
        }
        $url .= '/callback/' . self::chart($mchid);
        $data = M('newstemplate')->where('mchid=' . $mchid . '  and type=0')->find();
        $orderInfo = array(
            "您的车辆证件信息已通过审核!",
            "您的车辆证件信息审核失败，请重新提交资料!",
        );
        $messgeArr = array(
            "touser" => $openid,
            "template_id" => $data['newsid'],
            "url" => $url,
            "data" => array(
                "first" => array(
                    "value" => $orderInfo[$status],
                    "color" => "#173177"
                ),
                "keyword1" => array(
                    "value" => '测试',
                    "color" => "#173177"
                ),
                "keyword2" => array(
                    "value" => '测试日期',
                    "color" => "#173177"
                ),
                "remark" => array(
                    "value" => "详情请点击查看！",
                    "color" => "#173177"
                )
            )
        );


        $url = $this->urlArrPost["MESSAGE_TMEP"];
        $url .= $accessToken;
        $ret = $this->http_post_json($url, $messgeArr);
        return $ret;
    }

    /**
     * 系统推送订单消息
     * @param string $openid 用户微信openid
     * @param int $type 0-司机；1-乘客；
     * @param string $order_id 订单编号
     * @param string $date 订单生成时间
     */
    public function doPostSystemMessage($openid, $type = 0, $content)
    {
        $mchid = cookie('mchid');
        if (!$mchid) {
            $mchid = $_SESSION['admin_state']->user_id;
        }

        $data = M('newstemplate')->where('mchid=' . $mchid . '  and type=' . $type)->find();

        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $url = C('WX_PASSENGER_URL');
        if ($type == 0) {
            $url = C('WX_DRIVER_URL');
        }
        $url .= '/callback/' . self::chart($mchid);
        $systemInfo = "你有新信息啦!";
        $messgeArr = array(
            "touser" => $openid,
            "template_id" => $data['newsid'],
            "url" => $url,
            "data" => array(
                "first" => array(
                    "value" => $systemInfo,
                    "color" => "#FF0000"
                ),
                "keyword1" => array(
                    "value" => "招车平台",
                    "color" => "#173177"
                ),
                "keyword2" => array(
                    "value" => $content,
                    "color" => "#173177"
                ),
                "remark" => array(
                    "value" => "感谢您的支持！",
                    "color" => "#173177"
                )
            )
        );

        $url = $this->urlArrPost["MESSAGE_TMEP"];
        $url .= $accessToken;
        $data = $jsonStr = json_encode($messgeArr, JSON_UNESCAPED_UNICODE);
        $ret = Curl::callWebServer($url, $data, 'post');
        $httpCode = $ret['errcode'];
        if ($httpCode != 0) {
            return false;
        }
        return json_decode("发送成功", true);
    }

    public function doPositMessage($openid, $type = 0, $contenttype, $count, $time, $reson, $status)
    {

        $mchid = cookie('mchid');
        if (!$mchid) {
            $mchid = $_SESSION['admin_state']->user_id;
        }
        $data = M('newstemplate')->where('mchid=' . $mchid . '  and type=' . $type)->find();
        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $url = C('WX_PASSENGER_URL');
        if ($type == 0) {
            $url = C('WX_DRIVER_URL');
        }
        $url .= '/callback/' . self::chart($mchid);
        $systemInfo = array(
            "您好，您的提现申请通过审核！",
            "您好，你的提现申请未通过审核，如有疑问，请拨打：028-61112106"
        );
        $messgeArr = array(
            "touser" => $openid,
            "template_id" => $data['newsid'],
            "url" => $url,
            "data" => array(
                "first" => array(
                    "value" => $systemInfo[$contenttype],
                    "color" => "#FF0000"
                ),
                "keyword1" => array(
                    "value" => $count,
                    "color" => "#173177"
                ),
                "keyword2" => array(
                    "value" => $time,
                    "color" => "#173177"
                ),
                "keyword3" => array(
                    "value" => $status,
                    "color" => "#173177"
                ),
                "keyword4" => array(
                    "value" => $reson,
                    "color" => "#173177"
                ),
                "remark" => array(
                    "value" => "感谢您的支持！",
                    "color" => "#173177"
                )
            )
        );

        $url = $this->urlArrPost["MESSAGE_TMEP"];
        $url .= $accessToken;
        $data = $jsonStr = json_encode($messgeArr, JSON_UNESCAPED_UNICODE);
        $ret = Curl::callWebServer($url, $data, 'post');
        $httpCode = $ret['errcode'];
        if ($httpCode != 0) {
            return false;
        }
        return json_decode("发送成功", true);
    }

    /**
     * 结果创建菜单
     */
    public function createMenu()
    {
        $uid = $_SESSION['admin_state']->user_id;
        if (!$uid) {
            $uid = cookie('mchid');
        }
        $accessToken = $this->getWechatAccessToken();
        if (!$accessToken) {
            return false;
        }
        $driverUrl = C('WX_DRIVER_URL');
        $passengerUrl = C('WX_PASSENGER_URL');

        $supplyUrl = C('WX_SUPPLY_URL');
        $solutionUrl = C('WX_MORE_SOLUTION_URL');
        $electricityUrl = C('WX_MORE_ELECTRICITY_URL');
        $businessrUrl = C('WX_MORE_BUSINESS_URL');
        $cooperationUrl = C('WX_COOPERATION_URL');
        /*        $bottonArr = array(
                    "button" => array(
                        //一级菜单
                        array(
                            "name" => "我是司机",
                            "type" => "view",
                            "url" => $driverUrl
                        ),
                        //一级菜单
                        array(
                            "name" => "我是乘客",
                            "type" => "view",
                            "url" => $passengerUrl
                        )
                    )
                );*/

        $bottonArr = array(
            "button" => array(
                //一级菜单
                array(
                    "name" => "提供服务",
                    "type" => "view",
                    "url" => $supplyUrl
                ),
                //一级菜单
                array(
                    "name" => "行业方案",
                    "sub_button" =>
                    array(
                        array(
                            "name" => "招车平台：乘客版",
                            "type" => "view",
                            "url" => $passengerUrl . '/callback/' . self::chart($uid)
                        ),
                        array(
                            "name" => "招车平台：司机版",
                            "type" => "view",
                            "url" => $driverUrl . '/callback/' . self::chart($uid)
                        ),
                        array(
                            "name" => "商业无线行业方案",
                            "type" => "view",
                            "url" => $businessrUrl
                        ),
                        array(
                            "name" => "电商资讯行业方案",
                            "type" => "view",
                            "url" => $electricityUrl
                        ),
                        array(
                            "name" => "更多行业解决方案",
                            "type" => "view",
                            "url" => $solutionUrl
                        ),
                    ),
                ),
                //一级菜单
                array(
                    "name" => "合作联系",
                    "type" => "view",
                    "url" => $cooperationUrl
                )
            )
        );
        $url = $this->urlArrPost['MENU_CREATE'];
        $url .= $accessToken;
        $ret = $this->http_post_json($url, $bottonArr);
        return $ret;
    }

    /**
     * PHP发送Json对象数据
     *
     * @param $url 请求url
     * @param $data 发送的arry字符串
     * @return array
     */
    private function http_post_json($url, $data)
    {

        $jsonStr = json_encode($data, JSON_UNESCAPED_UNICODE);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        /*        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        'Content-Type: application/json; charset=utf-8',
                        'Content-Length: ' . strlen($jsonStr)
                    )
                );*/
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpCode != 200) {
            return false;
        }
        return json_decode($response, true);
    }
}

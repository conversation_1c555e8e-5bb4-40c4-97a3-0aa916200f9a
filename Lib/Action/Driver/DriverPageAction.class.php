<?php

import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of DriverPageAction
 *
 * <AUTHOR>
 */
class DriverPageAction extends DriverCoreAction
{
    //司机前端首页
    public function indexPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->systemIsMaintenance();
        $this->assign('driver_id', $this->state->user_id);
        $this->assign('UNIAPP_DRIVER', C('UNIAPP_DRIVER'));

        if ($this->remind()) {
            $this->display('Tpl/Common/remind.html');
            return;
        }

        if (in_array($this->mchid, $this->mchid_stop_arr)) {
            $this->display('Tpl/Common/tempremind.html');
            return;
        }

        if (APP_DEBUG) {
            //登陆状态
            $driver_id = 3033;
            $status = 1;
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            $this->assign('residual_seating', $driverR->data['residual_seating']);
            $this->assign('total_seating', $driverR->data['total_seating']);
            $this->assign('car_tail_number', $driverR->data['car_tail_number']);
            $this->assign('status', $status);
            \StateModel::save($driver_id, "123456", \StateModel::$DRIVER, array('status' => $status), $this->mchid);
            $this->display('Tpl/Driver/Index/index.html');
            return;
        }
        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            $this->assign('residual_seating', $driverR->data['residual_seating']);
            $this->assign('total_seating', $driverR->data['total_seating']);
            $this->assign('car_tail_number', $driverR->data['car_tail_number']);
        }
        $this->assign('status', $status);
        $this->showBottomMenu();
        $this->display('Tpl/Driver/Index/index.html');
    }

    public function orderPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if ($this->state->user_id) {
            $status = 1;
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            $this->assign('residual_seating', $driverR->data['residual_seating']);
            $this->assign('total_seating', $driverR->data['total_seating']);
            $this->assign('car_tail_number', $driverR->data['car_tail_number']);
        }
        $this->assign('status', $status);
        $this->showBottomMenu();

        $this->display('Tpl/Driver/Index/driver_order.html');
    }

    /**
     * 根据URL参数判断是否显示底部导航菜单
     * @return void
     */
    private function showBottomMenu()
    {
        if (isset($_GET['hide_bottom_menu'])
            && $_GET['hide_bottom_menu'] == 'true') {
            $this->assign('show_bottom_menu', false);
        } else {
            $this->assign('show_bottom_menu', true);
        }
    }

    //司机打开乘客发布行程
    public function shareDetailPage($order_no)
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        $orderR = $this->find(array('order_no' => $order_no), 'Order');
        if (!$orderR->ret) {
            $this->error('参数错误');
            return;
        }

        //登陆状态
        $status = -1;
        if ($this->state->user_id) {
            if ($this->state->user_type == \StateModel::$DRIVER) {
                $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
                $status = $driverR->data['status'];
                $this->assign('residual_seating', $driverR->data['residual_seating']);
                $this->assign('total_seating', $driverR->data['total_seating']);
                $this->assign('car_tail_number', $driverR->data['car_tail_number']);
            }
        }
        $this->assign('status', $status);

        $this->display('Tpl/Driver/Index/driver_share.html');
    }

    public function mysetPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        if ($this->state->user_id) {
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            $this->assign('cellphone', $driverR->data['cellphone']);
            $this->assign('nickname', $driverR->ret ? $driverR->data['name'] : "还没有设置昵称哦");
            $this->assign('car_tail_number', !empty($driverR->data['car_tail_number']) ? $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']) : "还没有设置车牌号");
            $this->assign('header_ico', !empty($driverR->data['header_ico']) ? $driverR->data['header_ico'] : "还没有设置头像哦");
            $this->assign('driver_id', $this->state->user_id);
            $this->assign('balance', $driverR->ret ? ($driverR->data['balance'] ? $driverR->data['balance'] : "0.00") : "0.00");
            $branchR = $this->find(array('admin_id' => $driverR->data['branchid']), 'Admin');
            $this->assign('tel', $branchR->ret ? $branchR->data['cellphone'] : "-");
            $this->assign('branch_name', $branchR->ret ? $branchR->data['mchname'] : "-");
            $this->assign('status', $driverR->ret ? $driverR->data['status'] : 0);
        }
        $mchInfo = $this->getMchInfoByMchid($this->mchid);
        $this->assign('parent_tel', $mchInfo->ret ? $mchInfo->data['tel'] : "-");
        $this->assign('UNIAPP_DRIVER', C('UNIAPP_DRIVER'));
        $cipherControlR = $this->getCipmchidByMchid($this->mchid);

        $this->assign('register_config', $this->checkDriverRegisterConfigIsOnByMchid($this->mchid));
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());

        if ($this->state->user_id) {
            //月流水
            $currentMonthTurnoverR = $this->getDriverCurrentMonthTurnover($this->state->user_id);
            $this->assign('total_month_flows', $currentMonthTurnoverR->ret ? $currentMonthTurnoverR->data : 0);
            //月订单数
            $currentMonthOrderCountR = $this->getDriverCurrentMonthOrderCount($this->state->user_id);
            $this->assign('total_month_orders', $currentMonthOrderCountR->ret ? $currentMonthOrderCountR->data : 0);
            //信用分
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            $this->assign('credit_score', empty($driverR->data['credit_score']) ? 100 : $driverR->data['credit_score']);
        } else {//未登录
            $this->assign('total_month_flows', 0);
            $this->assign('total_month_orders', 0);
            $this->assign('credit_score', 100);
        }


        $this->display('Tpl/Driver/Index/myset.html');
    }

    public function aboutPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        $this->display('Tpl/Driver/Index/about.html');
    }
    public function shareCodePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        $this->display('Tpl/Driver/Index/code.html');
    }
    public function accountPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        if ($this->state->user_id) {
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            $this->assign('cellphone', $driverR->data['cellphone']);
            $this->assign('balance', $driverR->ret ? ($driverR->data['balance'] ? $driverR->data['balance'] : "0.00") : "0.00");
            $adminR = $this->find(array('admin_id' => $driverR->data['mchid'],'is_del' => 0,'group_id' => 2), 'Admin');
            if (!$adminR->ret) {
                return $this->output(new ResultModel(false, "该商户不存在"));
            }
            $split = C('WX_SETTLEMENT_RATE');
            if ($split) {
                $this->assign('split', $split * 100);
            } else {
                $this->assign('split', null);
            }
        }
        $this->display('Tpl/Driver/Index/account.html');
    }

    public function accounthistroyPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Driver/Index/account_histroy.html');
    }

    public function orderhistoryPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Driver/Index/order_history.html');
    }
    public function registerPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Driver/Index/register.html');
    }
    public function submitPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Driver/Index/submit.html');
    }


    public function freeCarPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        //登陆状态
        $this->assign('status', $this->state->user_id ? 1 : 0);
        $this->showBottomMenu();
        $this->display('Tpl/Driver/Index/free_car.html');
    }

    //顺风车详情
    public function freeCarDetailPage($lfrn)
    {
        // $this->assign('_t', \CommonDefine::FREE_RIDE_FROM_USER_TYPE_2);
        // redirect(C('WEB_ROOT') .'passenger_share'.'/callback/'.$this->getCipmchidByMchid($this->mchid)->data['ciphertext'].'/lfrn/'.$lfrn.'?_t='.\CommonDefine::FREE_RIDE_FROM_USER_TYPE_2);
    }

    private function getMchInfoToPageByMchid($mchid)
    {
        $mchInfo = $this->getMchInfoByMchid($mchid);
        $this->assign('mchname', $mchInfo->data['mchname']);
        $this->assign('callback', $this->getCipmchidByMchid($this->mchid)->data['ciphertext']);
        $packageR = $this->find(array('package_id' => $mchInfo->data['package_id']), 'Package', 'tech_support');
        if ($packageR->ret) {
            $this->assign('tech_support', $packageR->data['tech_support']);
            if ($mchid == 166) {
                $this->assign('tech_support', 2);
            }
        } else {
            $this->assign('tech_support', 0);
        }

        # 不展示顺风车菜单
        if (in_array($mchid, C('DRIVER_SHUNFENGCHE_SERVICE_DISABLE_IDS'))) {
            $this->assign('freecar_hide', 1);
        }

        if ($mchid == 796) {
            $this->assign('tech_text', "穆青社服务中心");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 903) {
            $this->assign('tech_text', "长沙优通网络科技");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 942) {
            $this->assign('tech_text', "一步顺风车");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 969) {
            $this->assign('tech_text', "走起网约车");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1018) {
            $this->assign('tech_text', "搭顺车出行");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1032) {
            $this->assign('tech_text', "自由环球旅行");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 937) {
            $this->assign('tech_text', "安厦泉拼车平台");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1050) {
            $this->assign('tech_text', "成乐出行");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1075) {
            $this->assign('tech_text', "鑫约旅行");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1116) {
            $this->assign('tech_text', "斑马裕众");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1135) {
            $this->assign('tech_text', "石首商务车");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1170) {
            $this->assign('tech_text', "享约就约");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1174) {
            $this->assign('tech_text', "滴答城际出行");
            $this->assign('tech_support', 3);
        }

        if ($mchid == 1181) {
            $this->assign('tech_text', "帮邦行-奥通");
            $this->assign('tech_support', 3);
        }
        if ($mchid == 1289) {
            $this->assign('tech_support', 3);
        }
    }

    public function newindexPage()
    {
        $this->display('Tpl/Driver/New_index/index.html');
    }


}

<?php

namespace Driver;

import('@/Action/Home/MemberAction');
import('@/Action/Weixin/Wechat');

vendor("init", VENDOR_PATH . '/Ping');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 账户模块
 *
 * <AUTHOR>
 */
class AccountAction extends \DriverCoreAction
{
    const DRIVER_STATUS_0 = 0;//账号正常
    const DRIVER_STATUS_1 = 1;//审核中
    const DRIVER_STATUS_2 = 2;//审核未通过
    const DRIVER_STATUS_3 = 3;//资料提交中

    const IMG_TYPE_0 = 0; //头像
    const IMG_TYPE_1 = 1; //行驶证
    const IMG_TYPE_2 = 2; //驾驶证
    const IMG_TYPE_3 = 3; //身份证

    /**
     * 发送手机验证码
     * @param string $cellphone 手机号码
     */
    public function doSendCellphoneValidateCode($cellphone, $mchid)
    {
        $o = $this->sudo('Member', null);
        $r = $o->doSendCellphoneValidateCode($cellphone, $mchid);
        return $this->output($r);
    }

    /**
     * 司机注册
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     */
    public function doRegister($cellphone, $cellphone_validate_code, $password)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['status'] = self::DRIVER_STATUS_1;
        $this->data['mchid'] = $this->mchid;
        $this->data['from_type'] = \CommonDefine::FROM_TYPE_1;
        $this->data['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            return $this->output(new \ResultModel(false, '该手机号已注册'));
        }

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
        if ($r->ret) {
            $this->startTrans();

            //添加车主
            $driverA = $this->add('Driver');
            if ($driverA->ret) {
                $this->commitTrans();
                \StateModel::save($driverA->data, $password, \StateModel::$DRIVER, $driverA->data, $this->mchid);

                //微信通知总台
                return $this->output(new \ResultModel(true, '注册成功，请完善资料'));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(true, '注册失败，请重新注册'));
            }
        }
        return $this->output($r);
    }

    /**
     * 司机注册
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     * @param string $name 昵称
     * @param string $real_name 真实姓名
     * @param string $car_brand 车辆品牌(例如：奔驰)
     * @param string $car_tail_number 车牌号（例如：川A12345）
     * @param string $total_seating 座位数（包含司机在内）
     * @param file $driver_img 驾驶证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     * @param file $driving_img 行驶证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     */
    public function doRegisterTwo($cellphone, $cellphone_validate_code, $password, $name = null, $real_name, $car_brand, $car_tail_number, $total_seating)
    {
        if (empty($name)) {
            $this->data['name'] = $real_name;
        }

        $this->data['cellphone_f'] = $cellphone;
        $this->data['status'] = self::DRIVER_STATUS_1;
        $this->data['mchid'] = $this->mchid;
        $this->data['from_type'] = \CommonDefine::FROM_TYPE_1;
        $this->data['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            return $this->output(new \ResultModel(false, '该手机号已注册'));
        }

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
        if ($r->ret) {
            $this->startTrans();

            //添加车主
            $driverA = $this->add('Driver');
            if ($driverA->ret) {
                $size = $_FILES['driving_img']['size'];
                $size += $_FILES['driver_img']['size'];
                $bytes = round($size / 1048576 * 100) / 100;

                //上传驾驶证
                if (!empty($_FILES['driver_img']) && $_FILES['driver_img']['name']) {
                    if (!$this->doUploadImg($this->mchid, $driverA->data, self::IMG_TYPE_2)->ret) {
                        $this->transRollback();
                        $this->output(new \ResultModel(false, '上传驾驶证失败'));
                    }
                } else {
                    $this->output(new \ResultModel(false, '请上传驾驶证'));
                }
                //上传行驶证
                if (!empty($_FILES['driving_img']) && $_FILES['driving_img']['name']) {
                    if (!$this->doUploadImg($this->mchid, $driverA->data, self::IMG_TYPE_1)->ret) {
                        $this->transRollback();
                        $this->output(new \ResultModel(false, '上传行驶证失败'));
                    }
                } else {
                    $this->output(new \ResultModel(false, '请上传行驶证'));
                }

                $updatePakeageR = self::updatePakeageInfo($this->mchid, $bytes, '', '');
                if (!$updatePakeageR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '提交失败'));
                }

                $this->commitTrans();
                \StateModel::save($driverA->data, $password, \StateModel::$DRIVER, $driverA->data, $this->mchid);

                //微信通知总台
                return $this->output(new \ResultModel(true, '注册成功，请完善资料'));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(true, '注册失败，请重新注册'));
            }
        }
        return $this->output($r);
    }

    /**
     * 司机注册 v3
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     * @param string $name 昵称
     * @param string $real_name 真实姓名
     * @param string $car_brand 车辆品牌(例如：奔驰)
     * @param string $car_tail_number 车牌号（例如：川A12345）
     * @param string $total_seating 座位数（包含司机在内）
     * @param string $driver_license 驾驶证图片地址
     * @param string $driving_license 行驶证图片地址
     */
    public function doRegisterThree($cellphone, $cellphone_validate_code, $password, $name = null, $real_name, $car_brand, $car_tail_number, $total_seating, $driver_license, $driving_license)
    {
        if (empty($name)) {
            $this->data['name'] = $real_name;
        }

        $this->data['cellphone_f'] = $cellphone;
        $this->data['status'] = self::DRIVER_STATUS_1;
        $this->data['mchid'] = $this->mchid;
        $this->data['from_type'] = \CommonDefine::FROM_TYPE_1;
        $this->data['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            return $this->output(new \ResultModel(false, '该手机号已注册'));
        }

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $this->mchid);
        if ($r->ret) {
            //添加车主
            $driverA = $this->add('Driver');
            if ($driverA->ret) {
                \StateModel::save($driverA->data, $password, \StateModel::$DRIVER, $driverA->data, $this->mchid);
                //微信通知总台
                return $this->output(new \ResultModel(true, '注册成功，请完善资料'));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(true, '注册失败，请重新注册'));
            }
        }
        return $this->output($r);
    }

    /**
     * 司机完善资料
     * @param string $name 昵称
     * @param string $real_name 真实姓名
     * @param string $car_brand 车辆品牌(例如：奔驰)
     * @param string $car_register_time 车辆上户时间
     * @param string $car_tail_number 车牌号（例如：川A12345）
     * @param string $total_seating 座位数（包含司机在内）
     * @param string $frist_get_driver_license_date 初次驾照领取日期
     * @param string $ID_number 身份证号码
     * @param file $driver_img 驾驶证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     * @param file $driving_img 行驶证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     * @param file $driver_ID_img 身份证(图片 "jpg", "gif", "bmp", "jpeg", "png")
     */
    public function doSubmitDriverData($name = null, $real_name, $car_brand, $car_register_time, $car_tail_number, $total_seating, $frist_get_driver_license_date, $ID_number)
    {
        $driverR = $this->find(array('driver_id' =>  $this->state->user_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常'));
        }

        if (empty($name)) {
            $this->data['name'] = $real_name;
        }

        /*if($driverR->data['status'] == self::DRIVER_STATUS_0){
            return $this->output(new \ResultModel(false, '已审核通过核账号，不能修改资料'));
        }else */if ($driverR->data['status'] == self::DRIVER_STATUS_1) {
            return $this->output(new \ResultModel(false, '审核中不能修改资料'));
        }

        if (time() <= strtotime($frist_get_driver_license_date)) {
            return $this->output(new \ResultModel(false, '请正确填写驾照领取日期'));
        }

        if (!$this->validation_filter_id_card($ID_number)) {
            return $this->output(new \ResultModel(false, '请输入正确的身份证号码'));
        }

        $dateDifArr = $this->diffDate(date('Y-m-d'), $frist_get_driver_license_date);
        /*         if($dateDifArr['year'] < 3){
                    return $this->output(new \ResultModel(false, '注册失败，驾龄小于3年'));
                }*/


        $this->data['driving_years'] = $dateDifArr['year'];
        $this->data['residual_seating'] = $this->data['total_seating'] - 1;
        $this->data['driver_id'] = $this->state->user_id;
        $this->data['status'] = \CommonDefine::EXAMINE_STATUS_1;

        $this->startTrans();
        $driverS = $this->save('Driver');
        if ($driverS->ret) {
            $size = $_FILES['driving_img']['size'];
            $size += $_FILES['driver_img']['size'];
            $size += $_FILES['driver_ID_img']['size'];
            $bytes = round($size / 1048576 * 100) / 100;

            //上传驾驶证
            if (!empty($_FILES['driver_img']) && $_FILES['driver_img']['name']) {
                if (!$this->doUploadImg($driverR->data['mchid'], $driverR->data['driver_id'], self::IMG_TYPE_2)->ret) {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '上传驾驶证失败'));
                }
            } else {
                $this->output(new \ResultModel(false, '请上传驾驶证'));
            }
            //上传行驶证
            if (!empty($_FILES['driving_img']) && $_FILES['driving_img']['name']) {
                if (!$this->doUploadImg($driverR->data['mchid'], $driverR->data['driver_id'], self::IMG_TYPE_1)->ret) {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '上传行驶证失败'));
                }
            } else {
                $this->output(new \ResultModel(false, '请上传行驶证'));
            }

            //上传身份证
            if (!empty($_FILES['driver_ID_img']) && $_FILES['driver_ID_img']['name']) {
                if (!$this->doUploadImg($driverR->data['mchid'], $driverR->data['driver_id'], self::IMG_TYPE_3)->ret) {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '上传行驶证失败'));
                }
            }

            $updatePakeageR = self::updatePakeageInfo($driverR->data['mchid'], $bytes, '', '');
            if (!$updatePakeageR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '提交失败'));
            }

            $this->commitTrans();
            return $this->output(new \ResultModel(true, '提交成功'));
        }
        return $this->output(new \ResultModel(false, '提交失败'));
    }

    /**
     * 司机完善资料 v3
     * @param string $name 昵称
     * @param string $real_name 真实姓名
     * @param string $car_brand 车辆品牌(例如：奔驰)
     * @param string $car_tail_number 车牌号（例如：川A12345）
     * @param string $total_seating 座位数（包含司机在内）
     * @param string $driver_license 驾驶证图片地址
     * @param string $driving_license 行驶证图片地址
     */
    public function doSubmitDriverDataThree($name = null, $real_name, $car_brand, $car_tail_number, $total_seating, $driver_license, $driving_license)
    {
        $driverR = $this->find(array('driver_id' =>  $this->state->user_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常'));
        }

        if (empty($name)) {
            $this->data['name'] = $real_name;
        }

        /*if($driverR->data['status'] == self::DRIVER_STATUS_0){
            return $this->output(new \ResultModel(false, '已审核通过核账号，不能修改资料'));
        }else */if ($driverR->data['status'] == self::DRIVER_STATUS_1) {
            return $this->output(new \ResultModel(false, '审核中不能修改资料'));
        }

        $this->data['residual_seating'] = $this->data['total_seating'] - 1;
        $this->data['driver_id'] = $this->state->user_id;
        $this->data['status'] = \CommonDefine::EXAMINE_STATUS_1;

        $driverS = $this->save('Driver');
        if ($driverS->ret) {
            return $this->output(new \ResultModel(true, '提交成功'));
        }
        return $this->output(new \ResultModel(false, '提交失败'));
    }

    /**字节处理
     * @param $bytes
     * @return string
     */
    public function formatBytes($bytes)
    {
        //        if ($bytes >= 1073741824) {
        //            $bytes = round($bytes / 1073741824 * 100) / 100 . 'GB';
        //        } elseif ($bytes >= 1048576) {
        //            $bytes = round($bytes / 1048576 * 100) / 100 . 'MB';
        //        } elseif ($bytes >= 1024) {
        $bytes = round($bytes / 1024 * 100) / 100 . 'KB';
        //        } else {
        //            $bytes = $bytes . 'Bytes';
        //        }
        return $bytes;
    }

    /**
     * 上传图片
     * @param string $driver_id 司机id
     * @param int $type 图片分类0-头像；1-行驶证；2-驾驶证；3-身份证
     */
    private function doUploadImg($mchid, $driver_id, $type = self::IMG_TYPE_0)
    {
        $saveKey = 'header_ico';
        $savePathKey = 'header_ico_path';
        $dirKey = 'UPLOAD_DIR_HEADER';
        $imgFile = 'header_ico';

        if ($type == self::IMG_TYPE_1) {
            $dirKey = 'UPLOAD_DIR_DRIVING_LICENSE';
            $saveKey = 'driving_license';
            $savePathKey = 'driving_license_path';
            $imgFile = 'driving_img';
        } elseif ($type == self::IMG_TYPE_2) {
            $dirKey = 'UPLOAD_DIR_DRIVER_LICENSE';
            $saveKey = 'driver_license';
            $savePathKey = 'driver_license_path';
            $imgFile = 'driver_img';
        } elseif ($type == self::IMG_TYPE_3) {
            $dirKey = 'UPLOAD_DIR_DRIVER_ID_IMG';
            $saveKey = 'driver_ID_img';
            $savePathKey = 'driver_ID_img_path';
            $imgFile = 'driver_ID_img';
        } else {
            $saveKey = 'header_ico';
            $savePathKey = 'header_ico_path';
            $dirKey = 'UPLOAD_DIR_HEADER';
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');
        $url = C('FILE_ROOT');

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir[$dirKey] . DIRECTORY_SEPARATOR .md5($mchid);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $imgExArr = C('IMG_TYPE_EX');
        $fileArr = explode('.', $_FILES[$imgFile]['name']);
        if (empty($fileArr)) {
            $this->output(new \ResultModel(false, '上传失败'));
        }
        $imgEx = end($fileArr);
        if (!in_array($imgEx, $imgExArr)) {
            return new \ResultModel(false, '不支持该文件类型');
        }
        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;

        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        if (!move_uploaded_file($_FILES[$imgFile]['tmp_name'], $fullPath . DIRECTORY_SEPARATOR . $realName)) {
            return new \ResultModel(false, '上传证件失败');
        }
        $relateFullPath = $relatePath . DIRECTORY_SEPARATOR . $realName;
        $urlPath = $url .$relateFullPath;
        $r = $this->save('Driver', array('driver_id' => $driver_id, $saveKey => $urlPath,$savePathKey => $relateFullPath));
        if (!$r->ret) {
            return new \ResultModel(false, '上传失败');
        }
        return new \ResultModel(true, '上传成功');
    }

    /**
     * 获取司机车辆信息
     * @param string $driver_id 司机id
     */
    public function doGetCarDetail($driver_id)
    {
        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'driving_years, car_brand, car_type, car_register_time, total_seating, driving_license, driver_license');
        $r->data['car_register_time'] = date('Y-m-d', strtotime($r->data['car_register_time']));
        return $this->output($r);
    }


    /**
     * 创建车主
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $total_seating 总的座位数
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param int $driving_years 驾龄
     * @param datetime $car_register_time 上户时间
     * @param string $name 姓名
     * @param datetime $start_time 出发时间
     * @param string $car_tail_number 车牌尾号
     * @param int $balance 账户余额
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    private function doCreateDriver($drive_mode, $total_seating, $start_address_code, $end_address_code, $address_code, $user_name, $cellphone, $password, $repassword, $car_type, $driving_years, $car_register_time, $name, $start_time, $car_tail_number, $balance = 0, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['residual_seating'] = $total_seating;
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '运营地格式不正确或地址不存在'));
            }
        }
        $this->startTrans();
        //添加车主
        $r = $this->add('Driver');
        if ($r->ret) {
            $driver_id = $r->data;
            $r = $this->select(null, null, null, null, 'MetaDriverRemindAction', 'action_id');
            if ($r->ret) {
                foreach ($r->data as $value) {
                    //添加提醒设置
                    $r = $this->add('DriverRemindSet', array('action_id' => $value['action_id'], 'driver_id' => $driver_id));
                    if (!$r->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '车主提醒设置初始化失败'));
                    }
                }
                if ($r->ret) {
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '车主提醒设置元数据不存在'));
            }
        } else {
            $this->transRollback();
        }
        return $this->output();
    }

    /**
     * 登录
     * @param string $user_name 用户名
     * @param string $cellphone 手机号
     * @param string $password 密码
     */
    public function doLogin($user_name, $cellphone, $password)
    {
        if (!empty($user_name)) {
            $r = $this->find(array('user_name' => $user_name, 'password' => md5($password)), 'Driver', 'driver_id,is_freeze');
        } else {
            $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password)), 'Driver', 'driver_id,is_freeze');
        }
        if ($r->ret) {
            //判断是否被删除
            if (intval($r->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
            \StateModel::save($r->data['driver_id'], $password, \StateModel::$DRIVER, $this->mchid);
        }
        return $this->output($r);
    }

    /**
     * 退出登录
     * @param string $user_name 用户名
     */
    public function doWebLogout()
    {
        \StateModel::clear(\StateModel::$DRIVER, $this->mchid);
        return $this->output(new \ResultModel(true));
    }

    /**
     * 微信端页面登录
     * @param string $cellphone 手机号
     * @param string $password 密码
     */
    public function doWebLogin($cellphone, $password)
    {
        $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password), 'mchid' => $this->mchid), 'Driver', 'driver_id,nick_name,is_freeze,status,name,cellphone,openid');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '账号密码错误，请重新输入！'));
        } else {
            $cipmchid = $this->getCipmchidByMchid($this->mchid)->data['ciphertext'];

            //判断是否被删除
            if ($r->data['is_freeze'] == \CommonDefine::IS_FREEZE_1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
            $r->data['cellphone'] = $cellphone;

            $we = new \Wechat();
            $r->data['wx_isverify'] = 0;
            $state = $cipmchid.','.$r->data['driver_id'];
            $url = C('WEB_ROOT') . 'logincallback/callback/' . $cipmchid;
            $redirect_uri = urlencode($url);
            $r->data['wx_url'] = $we->getAuthorizeUrl($redirect_uri, $state, $cipmchid);
            \StateModel::save($r->data['driver_id'], md5($password), \StateModel::$DRIVER, $r->data, $this->mchid);
        }
        return $this->output($r);
    }


    /**
     * 微信端回调处理,保存用户openid用于消息推送
     */
    public function doGetWechatCode()
    {
        $code = $_GET['code'];
        $state =  $_GET['state'];
        $dataArr = explode(',', $state);

        $cipmchid = $dataArr[0];
        $driver_id = $dataArr[1];
        $mchid = $this->getMchidByCipmchid($cipmchid);

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrl($code, $cipmchid));
        $arr = json_decode($data, true);

        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_dri_token', $arr['access_token'], $expires_in);
        if ($arr['openid']) {
            $this->save('Driver', array('driver_id' => $driver_id, 'openid' => $arr['openid'], 'mchid' => $mchid));
        }

        //司机微信支付授权
        $we = new \Wechat();
        $url = C('WEB_ROOT') . 'driver_authpay';
        $redirect_uri = urlencode($url);
        $wxUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $we->appIdForPay . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_base&state=" . $state . "#wechat_redirect";
        header("Location:".$wxUrl);
        return;

        //        redirect(C('WEB_ROOT') . 'driver_myset');
    }

    /**
     * 微信端回调处理,保存用户openid用于支付
     */
    public function doGetWechatCodeForPay()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);
        $cipmchid = $dataArr[0];
        $driver_id = $dataArr[1];

        $r = $this->find('driver_id = ' . $driver_id, 'Driver', 'mchid,status');
        if (!$r->ret) {
            return new \ResultModel(false, "账号异常", null);
        }

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrlForPay($code));
        $arr = json_decode($data, true);
        if (isset($arr['openid']) && $arr['openid']) {
            $this->save('Driver', array('driver_id' => $driver_id, 'openidforpay' => $arr['openid']));
        }

        $this->assign('status', $r->data['status']);
        redirect(C('WEB_ROOT') . 'driver_myset/callback/'.$cipmchid);
    }


    /**
     * 微信端页面登录
     * @param string $cellphone 手机号
     * @param string $password 密码
     */
    public function doYyWebLogin($cellphone, $password)
    {
        $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password), 'mchid' => $this->mchid), 'Driver', 'driver_id,nick_name,is_freeze,status,name,cellphone,openid');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '账号密码错误，请重新输入！'));
        } else {
            $cipmchid = $this->getCipmchidByMchid($this->mchid)->data['ciphertext'];

            //判断是否被删除
            if ($r->data['is_freeze'] == \CommonDefine::IS_FREEZE_1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
            $r->data['cellphone'] = $cellphone;

            $we = new \Wechat();
            $r->data['wx_isverify'] = 0;
            $state = $cipmchid.','.$r->data['driver_id'];
            $url = C('WEB_ROOT') . 'driver_yyauth/callback/' . $cipmchid;
            $redirect_uri = urlencode($url);
            $r->data['wx_url'] = $we->getAuthorizeUrl($redirect_uri, $state, $cipmchid);
            \StateModel::save($r->data['driver_id'], $password, \StateModel::$DRIVER, $r->data, $this->mchid);
        }
        return $this->output($r);
    }


    /**
     * 微信端回调处理,保存用户openid用于消息推送
     */
    public function doYyGetWechatCode()
    {
        $code = $_GET['code'];
        $state =  $_GET['state'];
        $dataArr = explode(',', $state);

        $cipmchid = $dataArr[0];
        $driver_id = $dataArr[1];
        $mchid = $this->getMchidByCipmchid($cipmchid);

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrl($code, $cipmchid));
        $arr = json_decode($data, true);

        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_dri_token', $arr['access_token'], $expires_in);
        if ($arr['openid']) {
            $this->save('Driver', array('driver_id' => $driver_id, 'openid' => $arr['openid'], 'mchid' => $mchid));
        }

        //司机微信支付授权
        $we = new \Wechat();
        $url = C('WEB_ROOT') . 'driver_yyauthpay';
        $redirect_uri = urlencode($url);
        $wxUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $we->appIdForPay . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_base&state=" . $state . "#wechat_redirect";
        header("Location:".$wxUrl);
        return;

        //        redirect(C('WEB_ROOT') . 'driver_myset');
    }

    /**
     * 微信端回调处理,保存用户openid用于支付
     */
    public function doYyGetWechatCodeForPay()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);
        $cipmchid = $dataArr[0];
        $driver_id = $dataArr[1];

        $r = $this->find('driver_id = ' . $driver_id, 'Driver', 'mchid,status');
        if (!$r->ret) {
            return new \ResultModel(false, "账号异常", null);
        }

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrlForPay($code));
        $arr = json_decode($data, true);
        if (isset($arr['openid']) && $arr['openid']) {
            $this->save('Driver', array('driver_id' => $driver_id, 'openidforpay' => $arr['openid']));
        }

        $this->assign('status', $r->data['status']);
        redirect(C('WEB_ROOT') . 'driver_new_index/callback/'.$cipmchid);
    }


    public function doGetDriverStatus($driver_id)
    {
        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,nick_name,is_freeze,status,name,cellphone,openidforpay');
        if ($r->ret) {
            //判断是否被删除
            if (intval($r->data['is_freeze']) === 1) {
                return $this->output(new \ResultModel(false, '账号被删除'));
            }
        }
        return $this->output($r);
    }

    /**
     * 获取司机信息
     *
     */
    public function getDriverInfo()
    {
        $fields =  'driver_id,nick_name,is_freeze,status,name,cellphone,openid,balance,state as car_status,credit as credit_score';
        $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver', $fields);
        if ($driverR->ret) {
            $driverR->data['wx_isverify'] = $this->state->data['wx_isverify'];
            $driverR->data['wx_url'] = $this->state->data['wx_url'];

            $mchR = $this->getMchInfoByMchid($this->mchid);
            $driverR->data['mchname'] = $mchR->ret ? $mchR->data['mchname'] : "";

            //本月交易流水
            $currentMonthTurnoverR = $this->getDriverCurrentMonthTurnover($this->state->user_id);
            $driverR->data['total_month_flows'] = $currentMonthTurnoverR->ret ? $currentMonthTurnoverR->data : 0;

            //本月订单总数
            $currentMonthOrderCountR = $this->getDriverCurrentMonthOrderCount($this->state->user_id);
            $driverR->data['total_month_orders'] = $currentMonthOrderCountR->ret ? $currentMonthOrderCountR->data : 0;
            //信用分,默认100
            $driverR->data['credit_score'] = empty($driverR->data['credit_score']) ? 100 : $driverR->data['credit_score'];
        } else {
            echo "司机信息错误";
            die;
        }

        return $this->output($driverR);
    }

    /**
     * 刷新位置
     * @param double $longitude 经度
     * @param double $latitude 纬度
     */
    public function doRefreshPosition($longitude, $latitude)
    {
        $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        return $this->output($r);
    }

    /**
     * 获取车主信息
     * @param int $driver_id 车主ID（默认为空，为当前用户）
     * @param string $fields 查询字段列表（默认为空，查询所有）
     */
    public function getDriver($driver_id = null, $fields = null)
    {
        $r = $this->getEntityById($driver_id, 'Driver', $fields, 'password', 'state,drive_mode,start_address_code,end_address_code,address_code,driving_years,car_brand,car_type,car_tail_number,branchid,longitude,latitude,update_time,sort');
        if ($r->ret) {
            $startAddrObj = $this->checkingGdAddressCode($r->data['start_address_code']);
            $startAddr = "无";
            $endAddr = "无";
            if ($startAddrObj->ret) {
                $startAddr = $startAddrObj->data['address'];
            }
            $endAddrObj = $this->checkingAddressCode($r->data['end_address_code']);
            if ($endAddrObj->ret) {
                $endAddr = $endAddrObj->data['address'];
            }
            $r->data['route'] = $startAddr . "-" . $endAddr;
            $r->data['branchname'] = "";
            if (!empty($r->data['branchid'])) {
                $branchR = $this->find(array('admin_id' => $r->data['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data['branchname'] = $branchR->data['mchname'];
                }
            }
            # 司机归属分组查询
            $driverGroupAttributeValueR = $this->find(array('driver_id' => $r->data['driver_id']), 'DriverGroupAttributeValue', 'id,name');
            $r->data['driver_group_name'] = $driverGroupAttributeValueR->ret ? $driverGroupAttributeValueR->data['name'] : '';
            $data = $this->getGdAddressCodeByGdApi($r->data['longitude'], $r->data['latitude']);
            if (empty($data['address'])) {
                $r->data['last_info'] = "暂无定位位置信息";

            } else {
                $r->data['last_info'] = $data['address']."(".$r->data['update_time'].")";
            }
        }
        return $this->output($r);
    }

    /**
     * 忘记密码
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 新密码
     */
    public function doForgetPassword($cellphone, $cellphone_validate_code, $password)
    {
        //验证手机号码
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            $r = $this->find(array('cellphone' => $cellphone, 'validate_code' => $cellphone_validate_code), 'CellphoneValidate', 'update_time,validate_code');
            if ($r->ret) {
                if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') < strtotime(get_current_time())) {
                    return $this->output(new \ResultModel(false, '验证码已经过期'));
                }
            } else {
                return $this->output(new \ResultModel(false, '手机验证码错误'));
            }
        }
        $r = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver', 'driver_id');
        if ($r->ret) {
            $r = $this->save('Driver', array('driver_id' => $r->data['driver_id'], 'password' => md5($password)));
        }
        return $this->output($r);
    }

    /**
     * 修改密码
     * @param string $oldpassword 旧密码
     * @param string $password 新密码
     */
    public function doUpdatePassword($oldpassword, $password)
    {
        $driverR = $this->find(array('driver_id' => $this->state->user_id, 'password' => md5($oldpassword)), 'Driver');
        if ($driverR->ret) {
            if ($driverR->data['is_freeze'] == \CommonDefine::IS_FREEZE_1) {
                $this->output(new \ResultModel(false, '账号已删除'));
            }

            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'password' => md5($password)));
            if ($r->ret) {
                \StateModel::save($this->state->user_id, $password, \StateModel::$DRIVER, $this->state->data, $this->mchid);
                $this->output(new \ResultModel(true, '修改成功'));
            }
        } else {
            $this->output(new \ResultModel(false, '原始密码错误'));
        }
        return $this->output($driverR);
    }

    /**
     * 修改昵称
     * @param string $nick_name 用户昵称
     */
    public function doUpdateNickName($nick_name)
    {
        $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
        if ($driverR->ret) {
            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'name' => $nick_name));
            if ($r->ret) {
                $this->output(new \ResultModel(true, '修改成功'));
            }
        } else {
            $this->output(new \ResultModel(false, '修改失败'));
        }
        return $this->output($driverR);
    }

    /**
     * 充值
     * @param int $money 充值金额
     */
    public function doRecharge($strMoney)
    {
        if (!$this->state->user_id) {
            return $this->output(new \ResultModel(false, '登录已过期，请重新登录'));
        }

        if (preg_match("/[^\d-., ]/", $strMoney)) {
            return $this->output(new \ResultModel(false, '请输入正确的充值'));
        }
        $money = floatval($strMoney);
        //获取系统扣费金额
        $mix = $this->find(array('id' => 1), 'AnnualFee', 'money');
        if (!$mix->ret || $mix->data['money'] < 0) {
            return $this->output(new \ResultModel(false, '网络异常，获取数据失败'));
        }

        //金额不能少于系统规定的金额
        if ($money < $mix->data['money']) {
            return $this->output(new \ResultModel(false, '充值金额不能少于' . $mix->data['money']));
        }

        $driver = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'driver_id,balance,is_freeze,status,recommend,recommend_type');
        if (!$driver->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        } else {
            if ($this->data['is_freeze'] == 1) {
                return $this->output(new \ResultModel(false, '该账号已删除'));
            }
            if ($this->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '请等待账号审核通过后再重试'));
            } elseif ($this->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '该账号审核未通过，请重新提交资料'));
            }
        }
        $driver->data['balance'] += $money;

        $this->startTrans();
        $costR = $this->add('DriverCost', array('driver_id' => $this->state->user_id, 'cost_type' => 1, 'money' => $money));
        if ($costR->ret) {
            $driverR = $this->save('Driver', array('driver_id' => $this->state->user_id, 'balance' => $driver->data['balance']));
            if ($driverR->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '充值失败'));
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '充值失败'));
        }

        return $this->output(new \ResultModel(true, '充值成功'));
    }

    /**
     * 获取所有商户
     */
    private function getMerchants()
    {
        $r = $this->select(null, null, null, null, 'Admin', 'admin_id as id');
        if (!$r->ret) {
            return false;
        }
        $merchantArr = $r->data;
        $merchantids = array();
        foreach ($merchantArr as $merchant) {
            $merchantids[] = $merchant['id'];
        }
        return $merchantids;
    }

    /**
     * 获取扣费金额
     * @param int $money 商户id
     */
    private function getReduceMoney($mchid)
    {
        $mchid = $this->checkauth($mchid)->data;
        //获取系统扣费金额
        $mix = $this->find(array('mchid' => $mchid), 'AnnualFee', 'money');
        if (!$mix->ret || $mix->data['money'] <= 0) {
            return false;
        }
        //扣除月费
        $deduct = round($mix->data['money'] / 12, 2);
        return $deduct;
    }

    /**
     * 扣除月租，系统任务
     */
    public function doReduce()
    {
        $merchantids = $this->getMerchants();
        if ($merchantids && !empty($merchantids)) {
            foreach ($merchantids as $merchantid) {
                if (!$merchantid) {
                    continue;
                }
                $deduct = $this->getReduceMoney($merchantid);
                if (!$deduct) {
                    continue;
                }
                $drivers = $this->getReduceDrivers($deduct, $merchantid);
                if ($drivers) {
                    $this->doMonthReduce($drivers, $deduct);
                }
            }

        }
        return $this->output(new \ResultModel(true, '扣除成功'));
    }

    /**
     * 获得要扣费的司机组
     * @param float $reduce 月扣金额
     * @param int $merchantid 商户id
     */
    private function getReduceDrivers($reduce = 0, $merchantid = 0)
    {
        $drivers = array();
        $retDrivers = array();
        $lastReduceTime = date('Y-m-d 00:00:00', strtotime(' -1 month'));
        $r = $this->select(' \'if_freeze\'=0 and virtual=0 and mchid=' . $merchantid . ' and (status=0 or status=3) and (last_reduce_time <= \'' . $lastReduceTime . '\' or last_reduce_time is NULL) ', null, null, null, 'Driver', 'driver_id,balance');

        if ($r->ret) {
            $drivers = $r->data;
            foreach ($drivers as $k => $v) {
                //账户已欠费
                if ($v['balance'] < $reduce) {
                    $this->save('Driver', array('driver_id' => $v['driver_id'], 'status' => 3));
                } else {
                    $retDrivers[] = $v['driver_id'];
                }
            }

        }
        return $retDrivers;
    }

    /**
     * 批量扣除月租
     * @param $drivers 司机
     */
    private function doMonthReduce($drivers = null, $reduce = 0)
    {

        if (!$drivers) {
            return false;
        }

        if (is_array($drivers)) {
            foreach ($drivers as $k => $v) {
                if (!$this->doSignleReduce($v, $reduce)) {
                    return false;
                }
            }
        } else {
            return $this->doSignleReduce($drivers, $reduce);
        }
        return true;
    }

    /**
     * 单个扣除月租
     * @param $drivers 司机
     */
    private function doSignleReduce($driver_id, $reduce = 0)
    {
        $currentTime = date('Y-m-d H:i:s');
        $driver = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,balance,frist_reduce_time,recommend,recommend_type');

        if ($driver->ret) {
            $driver->data['balance'] -= $reduce;

            $this->startTrans();
            //第一次扣费
            $fristReduceTime = $driver->data['frist_reduce_time'];
            if ($fristReduceTime) {
                $costR = $this->add('DriverCost', array('driver_id' => $driver_id, 'cost_type' => 2, 'money' => $reduce));
                if ($costR->ret) {
                    $driverR = $this->save('Driver', array('driver_id' => $driver_id, 'balance' => $driver->data['balance'], 'last_reduce_time' => $currentTime, 'status' => 0));
                    if ($driverR->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                        return false;
                    }
                } else {
                    $this->transRollback();
                    return false;
                }
            } else {
                $fristReduceTime = $currentTime;
                $costR = $this->add('DriverCost', array('driver_id' => $driver_id, 'cost_type' => 2, 'money' => $reduce));
                if ($costR->ret) {
                    $driverR = $this->save('Driver', array('driver_id' => $driver_id, 'balance' => $driver->data['balance'], 'frist_reduce_time' => $fristReduceTime, 'last_reduce_time' => $currentTime, 'status' => 0));
                    if ($driverR->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                        return false;
                    }
                } else {
                    $this->transRollback();
                    return false;
                }
            }


            $commission = 0;//实际佣金
            //推荐人获取佣金
            if ($driver->data['recommend']) {
                //推荐人类型,司机类型为永久有效，乘客类型为1年有效
                if ($driver->data['recommend_type'] == 0) {
                    $recoDriver = $this->find(array('cellphone' => $driver->data['recommend'], 'if_freeze' => 0, 'status' => 0), 'Driver', 'driver_id,balance');
                    if ($recoDriver->ret) {
                        $commission = round($reduce / 3, 2);
                        $recoDriver->data['balance'] += $commission;
                        $this->save('Driver', array('driver_id' => $recoDriver->data['driver_id'], 'balance' => $recoDriver->data['balance']));
                        $this->add('DriverCost', array('driver_id' => $recoDriver->data['driver_id'], 'cost_type' => 4, 'money' => $commission, 'type' => 0));
                    }
                } else {
                    if ((strtotime($currentTime) - strtotime($fristReduceTime)) / (86400) < 365) {
                        $recoPassenger = $this->find(array('cellphone' => $driver->data['recommend'], 'if_freeze' => 0), 'Passenger', 'passenger_id,cash_balances');
                        if ($recoPassenger->ret) {
                            $commission = round($reduce / 3, 2);
                            $recoPassenger->data['cash_balances'] += $commission;
                            $this->save('Passenger', array('passenger_id' => $recoPassenger->data['passenger_id'], 'cash_balances' => $recoPassenger->data['cash_balances']));
                            $this->add('DriverCost', array('passenger_id' => $recoPassenger->data['passenger_id'], 'cost_type' => 4, 'money' => $commission, 'type' => 1));
                        }
                    }
                }
            }
        }
        return true;
    }


    /**
     * 司机提现申请
     * @param int $money 乘金额
     */
    public function doWithDrawDespoist($driver_id, $money, $bank_id)
    {
        if (!$bank_id) {
            return $this->output(new \ResultModel(false, '银行卡信息有误'));
        }

        $money = intval($money);
        if (!$money) {
            return $this->output(new \ResultModel(false, '请输入正确的金额'));
        }

        //获取系统扣费金额
        $mix = $this->find(array('id' => 1), 'AnnualFee', 'money');
        if (!$mix->ret || $mix->data['money'] < 0) {
            return $this->output(new \ResultModel(false, '网络异常'));
        }

        $driver = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,balance,is_freeze,status,recommend,recommend_type');
        if (!$driver->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        } else {
            if ($driver->data['is_freeze'] == 1) {
                return $this->output(new \ResultModel(false, '该账号已删除'));
            }
            if ($driver->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '请等待账号审核通过后再重试'));
            } elseif ($driver->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '该账号审核未通过，请重新提交资料'));
            }
        }

        if ($driver->data['balance'] <= $mix->data['money']) {
            return $this->output(new \ResultModel(false, '余额不能少于年费'));
        }

        if ($money > ($driver->data['balance'] - $mix->data['money'])) {
            return $this->output(new \ResultModel(false, '提现金额不能大于可提现金额'));
        }
        $driver->data['balance'] -= $money;
        $this->startTrans();
        $driverR = $this->save('Driver', array('driver_id' => $driver_id, 'balance' => $driver->data['balance']));
        if ($driverR->ret) {
            $deposit = $this->add('Deposit', array('account_id' => $driver_id, 'create_time' => date('Y-m-d H:i:s'), 'mchid' => $this->mchid, 'account_type' => 0, 'moneys' => $money, 'bank_id' => $bank_id));
            if ($deposit->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
            }
        } else {
            $this->transRollback();
            $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
        }
        return $this->output(new \ResultModel(true, '申请成功'));
    }


    /**
     * 司机发起提现申请
     * @param int $money 提现金额
     * @param string $cellphone_validate_code 短信验证码
     */
    public function doMobileWithDrawDespoist($money, $cellphone_validate_code)
    {
        $money = floatval($money);
        if (!$money || $money < 10) {
            return $this->output(new \ResultModel(false, '请输入正确的金额'));
        }

        $driver_id = $this->state->user_id;
        $driver = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,name,cellphone,balance,is_freeze,status,recommend,recommend_type,mchid,branchid,openid,openidforpay');
        if (!$driver->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        } else {
            if ($driver->data['is_freeze'] == 1) {
                return $this->output(new \ResultModel(false, '该账号已删除'));
            }
            if ($driver->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '请等待账号审核通过后再重试'));
            } elseif ($driver->data['status'] == 2) {
                return $this->output(new \ResultModel(false, '该账号审核未通过，请重新提交资料'));
            }

            if (empty($driver->data['openid']) || empty($driver->data['openidforpay'])) {
                return $this->output(new \ResultModel(false, '由于提现到您微信的余额，请您到微信公众号中登陆一次'));
            }

            $mchid = $driver->data['mchid'];
            //验证手机号码
            $r = $this->ValidateCellphone($driver->data['cellphone'], $cellphone_validate_code, $mchid);
            if (!$r->ret) {
                return $this->output($r);
            }
        }

        if ($money > $driver->data['balance']) {
            return $this->output(new \ResultModel(false, '提现金额不能大于帐户余额'));
        }

        //检测提现审核配置,每个分台可单独设置
        $checkBranchConfig = false;
        if (!$checkBranchConfig) {
            $driver->data['balance'] -= $money;
            $this->startTrans();
            $driverR = $this->save('Driver', array('driver_id' => $driver_id, 'balance' => $driver->data['balance']));
            if ($driverR->ret) {
                $settlementRate = C('WX_SETTLEMENT_RATE');
                $realAmount = (1 - $settlementRate) * $money;
                $depositNo = $this->createDepositNo();
                $currentTime = date('Y-m-d H:i:s');
                $deposit = $this->add('Deposit', array('account_id' => $driver_id, 'deposit_no' => $depositNo,'create_time' => $currentTime, 'mchid' => $mchid, 'account_type' => 0, 'moneys' => $money ,'real_amount' => $realAmount, 'settlement_rate' => $settlementRate,'branchid' => $driver->data['branchid'],'step' => 2));
                $db = M('deposit_status');
                $param = array('status' => 1,'deposit_id' => $deposit->data);
                if ($deposit->ret) {
                    $param['step'] = 2;
                    if ($db->where($param)->find()) {
                        $this->transRollback();
                    }
                    if (!$db->add($param)) {
                        $this->transRollback();
                    } else {
                        $this->commitTrans();
                        //短信通知
                        if (C('SMS_ON')) {
                            //通知司机
                            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                            $smsDataToDriver = array(
                                $driver->data['name'],
                                $depositNo,
                                "1-5工作日"
                            );
                            $smsUtil->sendTemplateSMS($driver->data['cellphone'], $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_SUCCESS, $driver->data['mchid']);

                            //通知总台
                            $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
                            $smsDataToMch = array(
                                '司机['.$driver->data['name'].']',
                                $driver->data['cellphone'],
                                $currentTime,
                                $depositNo,
                                $money
                            );
                            $smsUtil->sendTemplateSMS($mchR->data['contact_tel'], $smsDataToMch, \SMSUtil::TEMP_ID_NEW_DEPOSIT_APPLY, $driver->data['mchid']);
                        }
                    }
                } else {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
                }
            } else {
                $this->transRollback();
                $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
            }
        } else {
            $driver->data['balance'] -= $money;
            $this->startTrans();
            $driverR = $this->save('Driver', array('driver_id' => $driver_id, 'balance' => $driver->data['balance']));
            if ($driverR->ret) {
                $settlementRate = C('WX_SETTLEMENT_RATE');
                $realAmount = (1 - $settlementRate) * $money;
                $deposit = $this->add('Deposit', array('account_id' => $driver_id, 'create_time' => date('Y-m-d H:i:s'), 'mchid' => $mchid, 'account_type' => 0, 'moneys' => $money,'real_amount' => $realAmount, 'settlement_rate' => $settlementRate ,'branchid' => $driver->data['branchid'],'step' => 1));
                $db = M('deposit_status');
                $param = array('status' => 1,'deposit_id' => $deposit->data);
                if ($deposit->ret) {
                    $param['step'] = 1;
                    if ($db->where($param)->find()) {
                        $this->transRollback();
                    }
                    if (!$db->add($param)) {
                        $this->transRollback();
                    } else {
                        $this->commitTrans();
                    }
                } else {
                    $this->transRollback();
                    $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
                }
            } else {
                $this->transRollback();
                $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
            }
        }

        return $this->output(new \ResultModel(true, '申请成功'));
    }

    /**
     * 获取历史提现记录
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     */
    public function doGetDriverWithDrawDespoists($page = 1, $size = 10)
    {
        $driver_id = $this->state->user_id;
        $where['account_id'] = $driver_id;
        $where['account_type'] = 0;
        $where['mchid'] = $this->mchid;
        $despoistR = $this->select($where, $page, $size, array('create_time' => 'DESC'), 'Deposit');
        if ($despoistR->ret) {
            foreach ($despoistR->data as $k => $v) {
                switch ($v['status']) {
                    case 1:
                        $despoistR->data[$k]['status_msg'] = '待审核';
                        break;
                    case 2:
                        $despoistR->data[$k]['status_msg'] = '审核通过';
                        $transferR = $this->find(array('deposit_id' => $v['deposit_id']), 'Transfer');
                        if ($transferR->ret) {
                            if ($transferR->data['status'] == 4) {
                                $despoistR->data[$k]['error_msg'] = '支付失败，提现金额原路退回到您的余额';
                            }
                        }
                        break;
                    case 3:
                        $despoistR->data[$k]['status_msg'] = '审核未通过';
                        break;
                    default:
                        $despoistR->data[$k]['status_msg'] = '提现异常';
                        break;
                }
            }
        }

        $this->output($despoistR);
    }

    /**
     * 发布线路
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param datetime $start_time 出发时间
     * @param double $longitude 出发时间
     * @param double $latitude 出发时间
     * @param int $total_seating 剩余座位数
     * @param float $price 价格
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdateDriveline($driver_id, $start_address_code, $end_address_code, $start_time, $price, $longitude, $latitude, $start_address_remark = null, $end_address_remark = null)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }
        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'balance,state,status');
        if ($r->ret) {
            if ($r->data['status'] == 1) { //账号正常
                return $this->output(new \ResultModel(false, '账号正在审核中'));
            }
            if ($r->data['status'] == 2) { //账号正常
                return $this->output(new \ResultModel(false, '账号审核未通过'));
            }
            if ($r->data['status'] == 3) { //账号正常
                return $this->output(new \ResultModel(false, '账号已欠费'));
            }
            //验证状态
            if (intval($r->data['state']) === 2) {
                return $this->output(new \ResultModel(false, '在路上时不能发布乘车消息'));
            }
        } else {
            return $this->output(new \ResultModel(false, '账号异常'));
        }
        //验证出发时间
        if (strtotime($start_time) <= strtotime(get_current_time())) {
            return $this->output(new \ResultModel(false, '出发时间不能小于或等于当前时间'));
        }
        $this->data['driver_id'] = $driver_id;

        $r = $this->checkingGdAddressCode($start_address_code);
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
        }
        //$r = $this->checkingGdAddressCode($end_address_code);高德地址
        $r = $this->checkingAddressCode($end_address_code);
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
        }
        /*        if (intval($start_address_code) === intval($end_address_code)) {
                    return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
                }*/

        $this->data['state'] = 1;

        $r = $this->save('Driver', $this->data);
        return $this->output($r);
    }


    /**
     * 发布乘车消息
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param datetime $start_time 出发时间
     * @param int $total_seating 剩余座位数
     * @param float $price 价格
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdateDriveMode($drive_mode, $start_address_code, $end_address_code, $address_code, $start_time, $total_seating, $price, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['residual_seating'] = $total_seating;                //由于之前的bug，又考虑程序的兼容性，暂时将total_seating的名称用于residual_seating
        remove_arr($this->data, 'total_seating');
        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'balance,state,total_seating,drive_mode,allow_start_address_code,allow_end_address_code');
        if ($r->ret) {
            //验证余额
            if (floatval($r->data['balance']) <= 0.0) {
                return $this->output(new \ResultModel(false, '您的账户余额不足，请充值'));
            }
            //验证状态
            if (intval($r->data['state']) === 2) {
                return $this->output(new \ResultModel(false, '在路上时不能发布乘车消息'));
            }
            //验证剩余座位数
            if (intval($r->data['total_seating']) < intval($this->data['residual_seating'])) {
                return $this->output(new \ResultModel(false, '剩余座位数不能大于总的座位数'));
            }
            //验证授权路线
            if (intval($r->data['drive_mode']) === intval($drive_mode)) {
                if (intval($drive_mode) === 2) {
                    $allow_start_address_code = intval($r->data['allow_start_address_code']);
                    $start_address_code = intval($start_address_code);
                    $allow_end_address_code = intval($r->data['allow_end_address_code']);
                    $end_address_code = intval($end_address_code);
                    if (!(($allow_start_address_code === $start_address_code && $allow_end_address_code === $end_address_code) || ($allow_start_address_code === $end_address_code && $allow_end_address_code === $start_address_code))) {
                        return $this->output(new \ResultModel(false, '该乘车路线没有被授权'));
                    }
                }
            } else {
                return $this->output(new \ResultModel(false, '该乘车路线没有被授权'));
            }
        }
        //验证出发时间
        if (strtotime($start_time) <= strtotime(get_current_time())) {
            return $this->output(new \ResultModel(false, '出发时间不能小于或等于当前时间'));
        }
        $this->data['driver_id'] = $this->state->user_id;
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '无法获取您的当前地理位置，请移动到空旷处重新尝试'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '乘车地格式不正确或地址不存在'));
            }
        }
        $this->data['state'] = 1;
        $r = $this->save('Driver', '');
        return $this->output($r);
    }

    /**
     * 修改状态
     * @param int $state 车主状态（1-等候，2-在路上，3-隐身）
     */
    public function doUpdateState($state)
    {
        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'state');
        if ($r->ret) {
            $current_state = intval($r->data['state']);
            $state = intval($state);

            if ($state === 2) {
                if ($r->ret) {
                    if (intval($state) === 2) {
                        //必须所有乘客都上车了，才能出发
                        $count1 = $this->count(array('driver_id' => $this->state->user_id, 'state' => 3), 'Order');
                        $count2 = $this->count(array('driver_id' => $this->state->user_id, 'state' => array('in', '1,2,3')), 'Order');
                        if ($count1->data < $count2->data) {
                            return $this->output(new \ResultModel(false, '必须所有乘客都上车了，才能出发'));
                        }
                    }
                } else {
                    return $this->output(new \ResultModel(false, '你没有任何进行中的订单，不能出发'));
                }
            }

            if ($state === 3) {
                $count3 = $this->count(array('driver_id' => $this->state->user_id, 'state' => array('in', '1,2,3,5')), 'Order');
                if ($count3->data > 0) {
                    return $this->output(new \ResultModel(false, '你还有订单没有完成'));
                }
                if ($current_state !== 1) {
                    return $this->output(new \ResultModel(false, '你现在正在车上，不能停止'));
                }
            }

            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'state' => intval($state)));
        }
        return $this->output($r);
    }

    /**
     * 检测司机线路状态
     * @param double $driver_id 司机ID
     */
    public function doGetDriverLineState($driver_id = null)
    {
        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'cellphone,state,start_address_code,end_address_code,start_time');
        if ($r->ret) {
            if ($r->data['state'] == 0) {
                $r->data['message'] = "发布路线";
                $r->data['status'] = true;
            } elseif ($r->data['state'] == 1) {
                $r->data['message'] = "已满座，立即发车";
                $r->data['status'] = false;
            } elseif ($r->data['state'] == 2) {
                $r->data['message'] = "重新接单";
                //                $r->data['message'] = "乘客已全部送达，重新接单";
                $r->data['status'] = false;
            } else {
                $r->data['message'] = "发布路线";
                $r->data['status'] = true;
            }
        }

        return $this->output($r);
    }


    /**
     * 更新司机线路状态
     * @param int $driver_id 司机ID
     * @param int $state 更新状态；0-正常；1-已出发；2-全部送达;3-隐身
     */
    public function doUpdateLineState($state, $order_ids = null, $longitude = null, $latitude = null)
    {
        $driver_id = $this->state->user_id;

        //判断重置准许下单
        if ($state == 0) {
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            if ($driverR->data['state'] == 3) {
                $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 0, 'residual_seating' => $driverR->data['total_seating'] - 1,'longitude' => $longitude, 'latitude' => $latitude));
                if ($driverS) {
                    $this->commitTrans();
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            } else {
                $orderListN = $this->select('driver_id =' .$driver_id.' and state != 6 and state != 7 and state != 8 and state != 1 and train_id='. $driverR->data['current_train_id'], null, null, null, 'Order', 'order_id');
                if ($orderListN->ret) {
                    return $this->output(new \ResultModel(false, '你还有进行中的订单未处理'));
                }
                $trainData = array(
                    'train_no' => $this->createTrainNo(),
                    'driver_id' => $driver_id
                );
                $trainS = $this->add('Train', $trainData);
                if ($trainS->ret) {
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 0, 'residual_seating' => $driverR->data['total_seating'] - 1,'longitude' => $longitude, 'latitude' => $latitude,'current_train_id' => $trainS->data));
                    if ($driverS) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            }
        } elseif ($state == 1) {
            $orderListN = $this->select('driver_id =' .$driver_id.' and state = 1', null, null, null, 'Order', 'order_id');
            if ($orderListN->ret) {
                return $this->output(new \ResultModel(false, '您还有派单未处理'));
            }

            $this->startTrans();
            $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 1,'longitude' => $longitude, 'latitude' => $latitude));
            if (!$driverS) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '操作失败'));
            }
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');

            //更新所有乘客为在路上
            if (!empty($order_ids)) {
                $orderArr = explode(',', $order_ids);
                if (is_array($orderArr)) {
                    foreach ($orderArr as $v) {
                        if (!empty($v)) {
                            $orderR = $this->save('Order', array('order_id' => $v, 'state' => 4, 'train_id' => $driverR->data['current_train_id']));
                            if (!$orderR->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '操作失败'));
                            }
                        }
                    }
                }
            }
            $this->commitTrans();
        } elseif ($state == 2) {
            /*            $orderListN = $this->select('state = 1 and driver_id=' . $driver_id, null, null, null, 'Order', 'order_id');
                        if ($orderListN->ret) {
                            return $this->output(new \ResultModel(false, '还有预约订单未处理'));
                        }*/
            $this->startTrans();
            $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 2,'longitude' => $longitude, 'latitude' => $latitude));
            if (!$driverS->ret) {
                return $this->output(new \ResultModel(false, '操作失败'));
            }
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');

            //更新所有乘客为已送达待支付
            if (!empty($order_ids)) {
                $orderArr = explode(',', $order_ids);
                if (is_array($orderArr)) {
                    foreach ($orderArr as $v) {
                        if (!empty($v)) {
                            $orderR = $this->save('Order', array('order_id' => $v, 'state' => 5, 'train_id' => $driverR->data['current_train_id']));
                            if (!$orderR->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '操作失败'));
                            }
                        }
                    }
                }
            }
            $this->commitTrans();
        } elseif ($state == 3) {
            $this->startTrans();
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            if ($driverR->ret) {
                if ($driverR->data['state'] == 0) {
                    //接单中,判断是否有未处理的订单
                    $where['state'] = array('in', '1,2,3,4');
                    $where['driver_id'] = $driver_id;
                    $orderR = $this->find($where, 'Order');
                    if ($orderR->ret) {
                        $this->transRollback();
                        if ($orderR->data['state'] == 1) {
                            return $this->output(new \ResultModel(false, '您还有未处理的派单,请及时处理'));
                        } else {
                            return $this->output(new \ResultModel(false, '您还有进行中的订单未处理'));
                        }
                    }

                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 3,'longitude' => $longitude, 'latitude' => $latitude));
                    if (!$driverS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                    $this->commitTrans();
                } elseif ($driverR->data['state'] == 1) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '您已在路上,不能暂停'));
                } elseif ($driverR->data['state'] == 2) {
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 3));
                    if (!$driverS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
            }

        }

        return $this->output(new \ResultModel(true, '操作成功'));
    }

    /**
     * 更新司机当前位置
     * @param string $longitude 经度
     * @param string $latitude 纬度
     */
    public function doUpdateDriverPosition($longitude = null, $latitude = null)
    {
        $driver_id = $this->state->user_id;
        if (empty($longitude) || empty($latitude)) {
            return $this->output(new \ResultModel(false, '更新位置失败'));
        }

        $driverData['driver_id'] = $driver_id;
        $driverData['longitude'] = $longitude;
        $driverData['latitude'] = $latitude;

        $driverS = $this->save('Driver', $driverData);
        if (!$driverS->ret) {
            return $this->output(new \ResultModel(false, '更新位置失败'));
        }

        //更新快车信息
        $orderWhere['driver_id'] = $driver_id;
        $orderWhere['type'] = ['in', [\CommonDefine::ORDER_TYPE_7, \CommonDefine::ORDER_TYPE_11]];
        $orderWhere['state'] = ['in', [\CommonDefine::ORDER_STATE_2, \CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4, \CommonDefine::ORDER_STATE_5]];
        $ordersR = $this->select($orderWhere, 1, 1, 'order_id desc', 'Order');
        if ($ordersR->ret) {
            if ($ordersR->data[0]['state'] == \CommonDefine::ORDER_STATE_2) {
                $this->doUpdateOrderFastBoardingPoint($ordersR->data[0]['order_id'], $latitude, $longitude);
            } elseif ($ordersR->data[0]['state'] == \CommonDefine::ORDER_STATE_4) {
                $this->doUpdateOrderFastAlightingPoint($ordersR->data[0]['order_id'], $latitude, $longitude);
            }
            //$this->doUpdateOrderFastLocus($ordersR->data[0]['order_id'], $latitude, $longitude);
        }

        return $this->output(new \ResultModel(true, '更新位置成功'));
    }

    /**
     * 取消司机发布的线路
     * @param int $driver_id 司机ID
     */
    public function doCancelLine($driver_id)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }

        $orderListN = $this->select('driver_id = ' . $driver_id . ' and state != 7 and state != 8 and state !=6 ', null, null, null, 'Order', 'order_id');
        if ($orderListN->ret) {
            return $this->output(new \ResultModel(false, '您还有进行中的订单，请及时处理'));
        }

        $this->startTrans();
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'total_seating,state');
        if ($driverR->data['state'] == 2) {
            return $this->output(new \ResultModel(false, '您已经出发，不能取消线路'));
        }

        $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 0, 'residual_seating' => $driverR->data['total_seating']));
        if ($driverS->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '操作失败'));
        }

        return $this->output(new \ResultModel(true, '取消成功'));
    }


    /**
     * 获取账户司机金额
     * @param double $driver_id 司机账号id
     */
    public function doGetAccountBalance($driver_id)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }

        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,balance,mchid');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '获取金额失败'));
        }

        //获取系统扣费金额
        $mix = $this->find(array('mchid' => $r->data['mchid']), 'AnnualFee', 'money');
        if (!$mix->ret || $mix->data['money'] < 0) {
            return $this->output(new \ResultModel(false, '月扣金额异常'));
        }
        $withdraw = 0;
        if ($r->data['balance'] > $mix->data['money']) {
            $withdraw = $r->data['balance'] - $mix->data['money'];
        }
        $retArr = array(
            'balance' => $r->data['balance'],
            'withdraw' => $withdraw
        );

        return $this->output(new \ResultModel(true, $retArr));
    }

    /**
     * 获取司机二维码
     */
    public function doGetDriverQr()
    {
        $this->createDriverQr($this->mchid, $this->state->user_id);
        return $this->output($this->find(array('driver_id' => $this->state->user_id, 'mchid' => $this->mchid), 'DriverQrAttachment'));
    }

    /**
     * 绑定银行卡号
     * @param double $version_code 版本号
     */
    public function doBindingBank($driver_id, $name, $bank = null, $card_num, $bank_phone)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }
        $this->data['account_id'] = $driver_id;
        $this->data['type'] = 0;//司机
        unset($this->data['driver_id']);
        $r = $this->find(array('account_id' => $driver_id, 'type' => 0, 'card_num' => $card_num), 'Bank', 'bank_id');

        if ($r->ret) {
            return $this->output(new \ResultModel(false, '该银行卡已绑定'));
        }
        $addR = $this->add('Bank');
        return $this->output($addR);
    }

    /**
     * 司机充值
     * @param double $pay 版本号
     */
    public function doDriverPay(/*$driver_id, $channel, $order_no=null, $amount = 0, $openid=null*/)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            exit;
        }
        //生成充值记录

        $data = json_decode(file_get_contents('php://input'), true);
        $this->data['openid'] = $data['openid'];
        $this->data['amount'] = $data['amount'];
        $this->data['channel'] = $data['channel'];
        $this->data['driver_id'] = $data['driver_id'];
        $this->data['time'] = date('Y-m-d H:i:s');
        $data['order_no'] = $this->data['order_no'] = "Oid" . substr(strtotime($this->data['time']), 6, 4) . rand(100000, 999999);
        $payHistoryR = $this->add('PayHistory');
        if (!$payHistoryR->ret) {
            exit;
        }

        if ($data['channel'] == 'alipay_wap') {
            $extra = array(
                'success_url' => C('WX_DRIVER_URL'),
                /*'cancel_url' => C('WX_DRIVER_URL')*/
            );
        } elseif ($data['channel'] == 'wx_pub') {
            $extra = array(
                /*'limit_pay' => C('WX_DRIVER_URL'),*/
                'open_id' => $data['openid'],
                /*'cancel_url' => C('WX_DRIVER_URL')*/
            );
        }
        //设置 API-Key
        //\Pingpp\Pingpp::setApiKey('sk_test_eDWXnDb5ybLS94aXDC1CSGmP');//测试
        \Pingpp\Pingpp::setApiKey('********************************');//
        //SDK 验证签名设置
        \Pingpp\Pingpp::setPrivateKeyPath(APP_PATH . 'Conf/rsa_private_key.pem');
        //发起支付请求获取支付凭据
        $charge = \Pingpp\Charge::create(array(
            'order_no' => $data['order_no'],
            'amount' => $data['amount'] * 100,
            'app' => array('id' => 'app_n5qfT8an5mj1Oy1u'),
            'channel' => $data['channel'],
            'currency' => 'cny',
            'client_ip' => $_SERVER["REMOTE_ADDR"],
            'subject' => '年费充值',
            'body' => '年费充值进行中',
            'extra' => $extra
        ));

        //将获得的支付凭据传给 Client
        header("Content-type: application/json");
        echo $charge;
        exit;
        //接收 Webhooks 通知
    }


    public function doWebHooks()
    {
        $event = json_decode(file_get_contents("php://input"));

        // 对异步通知做处理
        if (!isset($event->type)) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            exit("fail");
        }
        switch ($event->type) {
            case "charge.succeeded":
                // 开发者在此处加入对支付异步通知的处理代码
                $data = $event->data;
                $object = $data->object;
                if (!isset($object)) {
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    exit("fail");
                }

                $orderNo = $object->order_no;
                $amount = $object->amount;
                $payR = $this->find(array('order_no' => $orderNo, 'status' => 0), 'PayHistory', 'driver_id');
                if (!$payR->ret) {
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    exit("fail");
                }

                $driverR = $this->find(array('driver_id' => $payR->data['driver_id']), 'Driver', 'balance');
                if (!$driverR->ret) {
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    exit("fail");
                }
                //添加到司机余额
                $driverR->data['balance'] += ($amount / 100);
                $this->startTrans();
                $driverS = $this->save('Driver', array('driver_id' => $payR->data['driver_id'], 'balance' => $driverR->data['balance']));
                if ($driverS->ret) {
                    $payR = $this->save('PayHistory', array('order_no' => $orderNo, 'status' => 1));
                    if ($payR->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                        header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                        exit("fail");
                    }
                } else {
                    $this->transRollback();
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                    exit("fail");
                }

                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                break;
            case "refund.succeeded":
                // 开发者在此处加入对退款异步通知的处理代码
                header($_SERVER['SERVER_PROTOCOL'] . ' 200 OK');
                break;
            default:
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
                break;
        }
        exit;
    }

    /**
     * 绑定司机银行卡号列表
     * @param double $driver_id 版本号
     */
    public function doGetBindingBankList($driver_id)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }
        $bankList = array();

        $bankModel = new \BankModel();
        $count = $bankModel->where('account_id = ' . $driver_id . ' and type = 0')->count();
        if ($count > 0) {
            $bankList = $this->select('account_id = ' . $driver_id . ' and type = 0', null, null, null, 'Bank', 'bank_id,account_id,name,card_num,bank_phone,bank');
            if ($bankList->ret) {
                foreach ($bankList->data as $k => $v) {
                    $v['driver_id'] = $v['account_id'];
                    unset($v['account_id']);
                    $this->data[$k] = $v;
                }
            }
            return $this->output($bankList);
        } elseif ($count == 0) {
            return $this->output(new \ResultModel(true, $bankList, 0));
        }
        return $this->output(new \ResultModel(false, '获取银行卡列表失败'));
    }

    /**
     * 获取地址列表
     * @param int $status 1-省份；2-城市；3-区县；4-乡镇；
     */
    public function doGetAddressList($type = 0, $status = 1)
    {
        header("Access-Control-Allow-Origin:*");
        // 允许a.com发起的跨域请求
        ///如果需要设置允许所有域名发起的跨域请求，可以使用通配符 *header("Access-Control-Allow-Origin: *");
        // // 允许任意域名发起的跨域请求header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

        $metaModel = new \MetaAddressModel();
        $addressList = $metaModel->where('address_pid = ' . $type)->field('address_id, name,code')->select();
        $data = "";
        if ($status == 1) {
            $data .= '<option value="省份">省份</option>';
        } elseif ($status == 2) {
            $data .= '<option value="城市">城市</option>';
        } elseif ($status == 3) {
            $data .= '<option value="县区">县区</option>';
        } else {
            $data .= '<option value="乡镇">乡镇</option>';
        }
        if (is_array($addressList)) {
            foreach ($addressList as $k => $metaAddress) {
                $data .= '<option data-gcode=' . $metaAddress['code'] . ' value="' . $metaAddress['address_id'] . '">' . $metaAddress['name'] . '</option>';
            }
        } else {
            return $this->output(new \ResultModel(true, array(), 0));
        }
        $ret = array($data);
        return $this->output(new \ResultModel(true, $ret, count($ret)));
    }

    /**
     * 检测新版本
     * @param double $version_code 版本号
     */
    public function doTestingNewVersion($version_code)
    {
        $latest_version_code = C('DRIVER_LATEST_VERSION_CODE');
        if ($version_code === $latest_version_code) {
            return $this->output(new \ResultModel(false, '已经是最新版本了'));
        } else {
            return $this->output(new \ResultModel(true, C('DRIVER_LATEST_VERSION_PATH')));
        }
    }

    /*
     * 支付功能检测
     * ***/
    public function checkpay($callback, $driver_id)
    {
        $r = $this->find(array('mchid' => $this->mchid), 'PayConfig');
        if ($r->data['pingappid']) {
            echo json_encode(array('data' => $r->data));
            exit;
        } else {
            echo json_encode(array('data' => false));
            $adminR = $this->find(array('admin_id' => $this->mchid), 'Admin');
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,price,openid,cellphone,mchid');
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array($adminR->data['tel']), \SMSUtil::TEMP_ID_DRPAYFAIL, $driverR->data['mchid']);
            $smsUtil->sendTemplateSMS($adminR->data['tel'], array($driverR->data['cellphone'], "失败", '请及时联系司机！'), \SMSUtil::TEMP_ID_PAFCHECK, $driverR->data['mchid']);
            exit;
        }
    }

    /**
     * 获取司机端配置
     *
     */
    public function getDriverSideConfig()
    {
        $configR = new \ResultModel(true);
        $mchInfo = $this->getMchInfoByMchid($this->mchid);
        $configR->data['mchname'] = $mchInfo->data['mchname'];
        $configR->data['mchtel'] = $mchInfo->data['tel'];
        $configR->data['branch_name'] = "";
        $configR->data['split'] =  C('WX_SETTLEMENT_RATE') * 100;
        $configR->data['version_info'] =  $this->getCurrentVersionFrontendInfo();
        $configR->data['register_config'] = $this->checkDriverRegisterConfigIsOnByMchid($this->mchid);
        if (!empty($this->state->user_id)) {
            $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
            if ($driverR->ret) {
                $branchR = $this->find(array('admin_id' => $driverR->data['branchid']), 'Admin');
                $configR->data['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
            }
            $configR->data['status'] = $driverR->ret ? $driverR->data['status'] : 0;
        }


        $techSupportR = $this->find(array('mchid' => $this->mchid), 'TechSupport');
        if ($techSupportR->ret) {
            $configR->data['tech_text'] = $techSupportR->data['tech_text'];
            $configR->data['tech_support'] = $techSupportR->data['tech_support'];
        } else {
            $configR->data['tech_text'] = "";
            $configR->data['tech_support'] = 1;
        }

        # 顺风车菜单控制
        if (in_array($this->mchid, [969, 1018, 1289, 1280])) {
            $configR->data['freecar_hide'] = 1;
        } else {
            $configR->data['freecar_hide'] = 0;
        }

        $configR->data['navigation'] = [
            ['path' => '/receipt', 'name' => 'receipt', 'meta' => ['title' => "接单", 'icon' => 'receipt', 'index' => 0]],
            ['path' => '/studentcustomized', 'name' => 'studentcustomized', 'meta' => ['title' => "定制拼车", 'icon' => 'studentcustomized', 'index' => 0]],
   /*         ['path' => '/freeride', 'name' => 'freeride', 'meta' => ['title' => "顺风车", 'icon' => 'freeride', 'index' => 0]],*/
            ['path' => '/help', 'name' => 'help', 'meta' => ['title' => "家长互助", 'icon' => 'help', 'index' => 0]],
            ['path' => '/order', 'name' => 'order', 'meta' => ['title' => "订单", 'icon' => 'order', 'index' => 0]],
            ['path' => '/mine', 'name' => 'mine', 'meta' => ['title' => "我的", 'icon' => 'mine', 'index' => 0]],
        ];
        if ($this->mchid == 181 || $this->mchid == 1091) {
            $configR->data['navigation'] = [
                ['path' => '/receipt', 'name' => 'receipt', 'meta' => ['title' => "接单", 'icon' => 'receipt', 'index' => 0]],
                ['path' => '/order', 'name' => 'order', 'meta' => ['title' => "订单", 'icon' => 'order', 'index' => 0]],
                ['path' => '/mine', 'name' => 'mine', 'meta' => ['title' => "我的", 'icon' => 'mine', 'index' => 0]],
//                ['path' => '/notice_assistant', 'name' => 'notice_assistant', 'meta' => ['title' => "通知助手", 'icon' => 'notice_assistant', 'index' => 0]],
            ];
        } elseif ($this->mchid == 1219) {
            $configR->data['navigation'] = [
                ['path' => '/receipt', 'name' => 'receipt', 'meta' => ['title' => "接单", 'icon' => 'receipt', 'index' => 0]],
                ['path' => '/order', 'name' => 'order', 'meta' => ['title' => "订单", 'icon' => 'order', 'index' => 0]],
                ['path' => '/mine', 'name' => 'mine', 'meta' => ['title' => "我的", 'icon' => 'mine', 'index' => 0]],
            ];
        } elseif ($this->mchid == 1238) {
            $configR->data['navigation'] = [
                ['path' => '/receipt', 'name' => 'receipt', 'meta' => ['title' => "接单", 'icon' => 'receipt', 'index' => 0]],
                ['path' => '/order', 'name' => 'order', 'meta' => ['title' => "订单", 'icon' => 'order', 'index' => 0]],
                ['path' => '/mine', 'name' => 'mine', 'meta' => ['title' => "我的", 'icon' => 'mine', 'index' => 0]],
            ];
        } elseif ($this->mchid == 1258) {
            $configR->data['navigation'] = [
                ['path' => '/receipt', 'name' => 'receipt', 'meta' => ['title' => "接单", 'icon' => 'receipt', 'index' => 0]],
                ['path' => '/order', 'name' => 'order', 'meta' => ['title' => "订单", 'icon' => 'order', 'index' => 0]],
                ['path' => '/mine', 'name' => 'mine', 'meta' => ['title' => "我的", 'icon' => 'mine', 'index' => 0]],
            ];
        }

        return $this->output($configR);
    }

    /**
     * 订单支付
     * @param string $order_no 订单编号（必须）
     * @param string $openid 用户openid（必须）
     * @param double $amount 支付金额（必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     */
    public function doWxOrderPay($order_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo, 'is_pay' => 0), 'Order');
        if (!$orderR->ret) {
            $ret->data = '订单不存在';
            return $this->output($ret);
        }
        # 实时检测班线订单座位是否被释放，如果座位被释放或者订单已被取消，无法继续发起支付
        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5
            && ($orderR->data['book_seating'] == 0
                || $orderR->data['state'] == \CommonDefine::ORDER_STATE_7)
        ) {
            $ret->data = '订单已被取消或者班次车票不足，无法继续支付';
            return $this->output($ret);
        }

        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
        if (!$driverR->ret) {
            $ret->data = '用户信息错误';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($orderR->data['mchid']);
        if ($payServiceR->ret) {
            if (empty($driverR->data['openid'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $driverR->data['openid'];
        } else {
            if (empty($driverR->data['openidforpay'])) {
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $driverR->data['openidforpay'];
        }

        if ($orderR->data['offer_price'] * 100 != $amount * 100) {
            $ret->data = '订单金额错误';
            return $this->output($ret);
        }

        $this->data['account_id'] = $orderR->data['driver_id'];
        $this->data['account_type'] = 1;

        $payHistoryR = $this->find(array('order_no' => $orderNo, 'openid' => $openid), 'PayOrderHistory');
        if (!$payHistoryR->ret) {
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                $ret->data = '订单异常';
                return $this->output($ret);
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if (!$wxPayUtil->init($orderR->data['mchid'], $channel)) {
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }

        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderNo, '代收车费', json_encode($extra), $amount, $this->getCipmchidByMchid($orderR->data['mchid'])->data['ciphertext'], $orderR->data['create_time']);
            if (!$charge) {
                $ret->data = '支付失败，请联系管理员';
                return $this->output($ret);
            }

            if ($charge['return_code'] == 'SUCCESS') {
                # 更新订单支付方式
                $this->save('Order', array('order_id' => $orderR->data['order_id'], 'payway' => \CommonDefine::WECHAT_JSAPI));

                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        } catch (\Exception $e) {
            \Log::write($e->getCode().$e->getMessage());
            $ret->data = $e->getCode().$e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请联系管理员';
        return $this->output($ret);
    }


    /**
     * 模拟司机账号登录方法
     *
     * http://local.cczhaoche.com/Driver/Account/doSimulateLogin?driverId=3083&uri=driver_index%3Fcallback%3DMFC221309746013AC554571FBD180E1C8H3593%26hide_bottom_menu%3Dtrue 
     * @throws \Exception 如果司机信息未找到，抛出异常
     */
    public function doSimulateLogin($driverId = 0, $uri = '')
    {
        // 查找司机信息
        // 通过司机ID和商户ID查找司机信息，包含关键信息如密码、姓名、手机号等
        $driver = $this->find(
            array('driver_id' => $driverId), 
            'Driver', 
            'driver_id,nick_name,status,name,cellphone,openid,password,mchid'
        );
        // 如果未找到司机信息，抛出异常并终止执行
        if (!$driver) {
            // 抛出异常，并将 cellphone 和 mchid 信息包含在异常信息中
            throw new \Exception("司机账号不存在, driverId: {$driverId}");
        }

        // 开始模拟登录的逻辑
        try {
            //清除所有Cookie
            \StateModel::flush();

            // 调用 StateModel::save 方法，使用司机ID、密码和其他信息保存登录状态
            // 这里 driver->data['password'] 是司机的登录密码
            \StateModel::save($driver->data['driver_id'], $driver->data['password'], \StateModel::$DRIVER, $driver->data, $driver->data['mchid']);

            $callbackUrl = C('WEB_ROOT') . $uri;
            
            // 重定向到指定的 URL
            $this->redirect($callbackUrl);

        } catch (\Exception $e) {
            return $this->output(new \ResultModel(false, $e->getMessage()));
        }
    }

    /**
     * 通过司机手机号码查询其所属的所有商户列表
     * 
     * 该方法将通过司机的手机号码查询司机信息，并获取司机所属的所有商户列表。
     * 
     * @param string $cellphone 司机的手机号码
     * @return \ResultModel 返回商户列表或错误提示
     */
    public function doQueryDriverAllMerchants($cellphone)
    {
        // 检查手机号是否为空
        if (empty($cellphone)) {
            return $this->output(new \ResultModel(false, '手机号不能为空'));
        }

        // 查询司机信息
        $driverInfo = $this->find(
            array('cellphone' => $cellphone), 
            'Driver', 
            'cellphone,driver_id,status'
        );

        // 如果未找到司机信息，返回错误提示
        if (!$driverInfo->ret) {
            return $this->output(new \ResultModel(false, '未找到该手机号对应的司机'));
        }

        // 查询司机所属的所有商户列表
        $merchantList = $this->getDriverMerchants($driverInfo->data['cellphone']);

        $driverR->data['merchants'] = $merchantList;

        return $this->output($driverR);  
    }


    /**
     * 司机账号统一登录接口
     * 
     * 该接口支持司机通过手机号或用户名进行登录。登录成功后，返回司机的基本信息及其所属商户信息。
     * 如果登录失败，将返回相应的错误提示。还可以限制登录失败次数，防止暴力破解。
     * 
     * @param string $loginField 登录字段，可以是手机号或用户名
     * @param string $password 司机的登录密码
     * @return \ResultModel 返回登录结果，包括司机信息或错误提示
     */
    public function doDriverUnifiedLogin($cellphone = '', $password = '')
    {
         // 检查手机号和密码是否为空
        if (empty($cellphone)) {
            return $this->output(new \ResultModel(false, '手机号不能为空'));
        }

        if (empty($password)) {
            return $this->output(new \ResultModel(false, '密码不能为空'));
        }

        // 根据手机号和MD5加密后的密码查找司机信息
        $r = $this->find(
            array('cellphone' => $cellphone, 'password' => md5($password)), 
            'Driver', 
            'cellphone,nick_name,status,name,cellphone,openid'
        );

        // 如果司机信息未找到，则返回登录失败提示
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '账号密码错误，请重新输入！'));
        }

        // 登录成功后，查询司机所属商户列表
        $merchantList = $this->getDriverMerchants($r->data['cellphone']);
        if ($merchantList) {
            // 将商户列表添加到司机信息中返回
            $r->data['merchants'] = $merchantList;
        }

        return $this->output($r);
    }

    /**
     * 查询司机所属的商户列表
     * 
     * 根据司机ID查询其所属的商户列表，并返回商户信息。
     * 
     * @param int $cellphone 司机的手机号码
     * @return array 返回司机所属的商户列表
     */
    private function getDriverMerchants($cellphone)
    {
         // 从 Driver 表中根据手机号查找多个商户ID（mchid）
        $driverInfo = $this->select(array('cellphone' => $cellphone, 'is_del' => \CommonDefine::IS_DEL_0), null, null, null, 'Driver', 'mchid');
        // 如果未找到结果，返回空数组
        if ($driverInfo->ret == false) {
            return array();
        }

        $mchids = array_column($driverInfo->data, 'mchid');

         // 使用模型进行联合查询
        $adminModel = M('Admin'); // 获取 Admin 模型实例

        // 使用 join 方法进行关联查询
        $result = $adminModel->alias('a')
            ->field('a.admin_id, a.mchname, a.cellphone')
            ->where(array('a.admin_id' => array('IN', $mchids)))
            ->select(); // 使用 select() 代替 find()，以处理多个记录

        // 循环处理结果，增加 redirect_url 字段
        foreach ($result as &$item) {
            $item['redirect_url'] = C('WEB_ROOT') . "Driver/Account/doSimulateLogin?cellphone={$cellphone}&mchid={$item['admin_id']}";
        }

        // 返回处理后的结果
        return $result ? $result : array();
    }

}

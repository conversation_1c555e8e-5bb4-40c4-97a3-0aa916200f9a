<?php

namespace Driver;
import('@/Action/Home/MemberAction');
import('@/Action/Weixin/Wechat');

vendor("init", VENDOR_PATH . '/Ping');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 云裕众-账户模块
 *
 * <AUTHOR>
 */
class YyAccountAction extends \DriverCoreAction
{

    /**
     * 获取家长互助发布服务
     */
    public function getDriverParentHelpInfo(){
        $driverParentHelpInfoR = $this->find(array('driver_id' => $this->state->user_id), 'YyDriverParentHelpInfo');
        if($driverParentHelpInfoR->ret){
            $driverParentHelpInfoR->data['is_expire'] = true;
            if(strtotime($driverParentHelpInfoR->data['end_time']) > time()){
                $driverParentHelpInfoR->data['is_expire'] = false;
            }
        }
        return $this->output($driverParentHelpInfoR);
    }
}

?>

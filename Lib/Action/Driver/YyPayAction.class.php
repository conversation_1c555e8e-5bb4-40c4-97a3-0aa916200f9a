<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 云裕众-订单支付
 *
 * <AUTHOR>
 */
class YyPayAction extends \DriverCoreAction
{
    /**
     * 家长互助-信息发布服务费支付
     * @param string $order_parent_help_price_no 订单编号（必须）
     * @param string $openid 用户openid（非必须）
     * @param double $amount 支付金额（非必须）
     * @param string $channel 支付渠道（非必须，默认‘wx_pub’）
     */
    public function doWxOrderParentHelpPricePay($order_parent_help_price_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderParentHelpPriceNo = $this->data['order_parent_help_price_no'];

        $yyOrderParentHelpPriceR = $this->find(array('order_parent_help_price_no' => $orderParentHelpPriceNo, 'is_pay' => 0), 'YyOrderParentHelpPrice');
        if(!$yyOrderParentHelpPriceR->ret){
            $ret->data = '订单不存在';
            return $this->output($ret);
        }

        $driverR = $this->find(array('driver_id' => $yyOrderParentHelpPriceR->data['driver_id']), 'Driver');
        if(!$driverR->ret){
            $ret->data = '用户信息错误';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($yyOrderParentHelpPriceR->data['mchid']);
        if($payServiceR->ret){
            if(empty($driverR->data['openid'])){
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $driverR->data['openid'];
        }else{
            if(empty($driverR->data['openidforpay'])){
                $ret->data = '用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $driverR->data['openidforpay'];
        }

        if($yyOrderParentHelpPriceR->data['price'] * 100 != $amount * 100){
            $ret->data = '订单金额错误';
            return $this->output($ret);
        }

        $this->data['account_id'] = $yyOrderParentHelpPriceR->data['driver_id'];
        $this->data['account_type'] = 1;
        $this->data['channel'] = $channel;

        $payHistoryR = $this->find(array('order_parent_help_price_no' => $orderParentHelpPriceNo, 'openid' => $openid),'YyPayOrderParentHelpPriceHistory');
        if(!$payHistoryR->ret){
            $this->data['openid'] = $openid;
            $payHistoryS = $this->add('YyPayOrderParentHelpPriceHistory', $this->data);
            if (!$payHistoryS->ret) {
                $ret->data = '订单异常';
                return $this->output($ret);
            }
        }

        $extra = array('open_id' => $openid);

        $wxPayUtil = new \WxPayUtil();
        if(!$wxPayUtil->init($yyOrderParentHelpPriceR->data['mchid'], $channel)){
            return  $this->output(new \ResultModel(false,'商户支付配置异常!'));
        }
        try{
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderParentHelpPriceNo, '代收',json_encode($extra), $amount, $this->getCipmchidByMchid($yyOrderParentHelpPriceR->data['mchid'])->data['ciphertext']);
            if(!$charge){
                $ret->data = '支付失败，请稍后重试';
                return $this->output($ret);
            }

            if($charge['return_code'] == 'SUCCESS'){
                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        }catch (\Exception $e){
            \Log::write($e->getCode().$e->getMessage());
            $ret->data = $e->getCode().$e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请稍后重试';
        return $this->output($ret);
    }
}

?>
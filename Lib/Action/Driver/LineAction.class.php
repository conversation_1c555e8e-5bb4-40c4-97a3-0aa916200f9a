<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 线路模块
 *
 * <AUTHOR>
 */
class LineAction extends \DriverCoreAction
{
    /**
     * 获取司机已发布的顺风车
     * @param int $page 页码
     * @param int $size 页大小
     * @param string $stime 查询开始时间
     * @param string $etime 查询开始时间
     */
    public function getOrderFreeRideLineList($page = 1, $size = 10, $stime = null, $etime = null)
    {
        $driver_id = $this->state->user_id;
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if(!$driverR->ret){
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }
        $where['driver_id'] = $driver_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $orderBy = 'create_time desc';
        if($stime){
            $where['create_time'] = array('egt', $stime);
        }
        if($etime){
            $where['create_time'] = array('elt', $etime);
        }

        $fields = 'id,line_free_ride_no as lfrn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,end_name,end_address_code,end_address_remark';
        $lineFreeRidesR = $this->select($where, $page, $size, $orderBy, 'LineFreeRide',$fields);
        if($lineFreeRidesR->ret){
            foreach($lineFreeRidesR->data as $k=>$v){
                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver');
                if($driverR->ret){
                    $lineFreeRidesR->data[$k]['car_brand'] = $driverR->data['car_brand'];
                }
                unset($lineFreeRidesR->data[$k]['driver_id']);
            }
        }
        return $this->output($lineFreeRidesR);
    }

    /**
     * 发布顺风车线路
     * @param date $start_time 出发时间
     * @param int $residual_seating 剩余座位数
     * @param string $price 价格/人
     * @param string $start_longitude 开始经度
     * @param string $start_latitude  开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $summary 备注信息
     */
    public function doReleaseFreeRideLine($start_time, $residual_seating = 1 , $price, $start_longitude = null, $start_latitude = null, $end_longitude=null, $end_latitude = null,$start_address_code, $start_address_remark=null, $end_address_code, $end_address_remark=null, $summary = null){
        if(strtotime($start_time) < time()){
            return $this->output(new \ResultModel(false, '出发时间不能小于当前时间'));
        }

        $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
        if(!$driverR->ret){
            return $this->output($driverR);
        }

        if($driverR->data['residual_seating'] < $residual_seating){
            return $this->output(new \ResultModel(false, '发布的座位数大于您当前的实际剩余座位数'));
        }

        if(!is_numeric($price) &&  $price<= 0){
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }
/*        $startAreaData = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        $endAreaData = $this->getGdAddressCodeByGdApi($end_longitude, $end_latitude);
        if($startAreaData['status'] == 0 && $endAreaData['status'] == 0){
            return $this->output(new \ResultModel(false, '定位异常，请稍后重试'));
        }*/

        $startCodeArr = $this->getProvinceCityAreaCodeByCode($start_address_code);
        $startNameR = $this->getParentAdministrativeDivisionNameByCode($start_address_code);
        $endCodeArr = $this->getProvinceCityAreaCodeByCode($end_address_code);
        $endNameR = $this->getParentAdministrativeDivisionNameByCode($end_address_code);
        if(!$startNameR->ret || !$endNameR->ret){
            return $this->output(new \ResultModel(false, '定位异常，请稍后重试'));
        }
        $this->data['line_free_ride_no'] = $this->createLineFreeRideNo();
        $this->data['start_name'] = $startNameR->data['name'];
        $this->data['end_name'] = $endNameR->data['name'];
        $this->data['mchid'] = $driverR->data['mchid'];
        $this->data['branchid'] = $driverR->data['branchid'];
        $this->data['start_province_code'] = $startCodeArr['province_code'];
        $this->data['start_city_code'] = $startCodeArr['city_code'];
        $this->data['start_area_code'] = $startCodeArr['area_code'];
        $this->data['end_province_code'] = $endCodeArr['province_code'];
        $this->data['end_city_code'] = $endCodeArr['city_code'];
        $this->data['end_area_code'] = $endCodeArr['area_code'];
        $this->data['driver_id'] = $driverR->data['driver_id'];

        $lineFreeRideA = $this->add('LineFreeRide');
        if($lineFreeRideA->ret){
            $lineFreeRideA->data = array('lfrn' => $this->data['line_free_ride_no']);
        }
        return $this->output($lineFreeRideA);
    }

    /**
     * 司机删除已发布的顺风车线路
     * @param int $line_id 顺风车线路id
     */
    public function doDelFreeRideLine($line_id){
        $driver_id = $this->state->user_id;
        $updateData['id'] = $line_id;
        $updateData['driver_id'] = $driver_id;
        $updateData['is_del'] = \CommonDefine::IS_DEL_1;
        $lineFreeRideR = $this->save('LineFreeRide', $updateData);
        return $this->output($lineFreeRideR);
    }
}

?>

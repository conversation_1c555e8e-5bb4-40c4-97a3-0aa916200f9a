<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 消息模块
 *
 * <AUTHOR>
 */
class MessageAction extends DriverCoreAction
{

    /**
     * 获取消息列表
     * @param string $fields 查询的字段（默认为空，以英文逗号分隔）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param boolean $is_read 是否已读（1-已读，0-未读，默认为空，查询所有）
     */
    public function getMessages($fields = null, $page = 1, $size = 10, $is_read = null)
    {
        $where['driver_id'] = $this->state->user_id;
        if ($is_read !== '' && !is_null($is_read)) {
            $where['result'] = intval($is_read);
        }
        $r = $this->select($where, $page, $size, 'create_time desc', 'DriverRemindTask', 'task_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key] = $this->sudoGetEntityById('task_id', $value['task_id'], 'Message', 'getMessage', $fields)->data;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取详细消息
     * @param int $task_id 消息ID
     * @param string $fields 查询的字段（默认为空，以英文逗号分隔）
     */
    public function getMessage($task_id, $fields = null)
    {
        $r = $this->getEntityById($task_id, 'DriverRemindTask', $fields, 'fail_count,remind_time,message', 'assigner_id,data,action_id,driver_id,detail_id');
        if ($r->ret) {
            if ($r->data['driver_id'] !== $this->state->user_id) {
                return $this->output(new ResultModel(false, '没有权限查看该消息'));
            }
            if ($this->isIncludeRootField($fields, 'content')) {
                $t = $this->find(array('action_id' => $r->data['action_id']), 'MetaDriverRemindAction', 'tpl_path,code');
                if ($t->ret) {
                    if ($t->data['code'] == 'system notification') {
                        $data = unserialize($r->data['data']);
                        $content = $data['content'];
                    } else {
                        $this->assign('assigner_id', $r->data['assigner_id']);
                        $this->assign('driver_id', $r->data['driver_id']);
                        $this->assign('data', unserialize($r->data['data']));
                        $content = $this->fetch($t->data['tpl_path']);
                        $order = $this->find(array('order_id' => $r->data['detail_id']), 'Order', 'type,state');
                        if ($order->ret) {
                            if ($this->isIncludeRootField($fields, 'order_type')) {
                                $r->data['order_type'] = $order->data['type'];
                            }
                            if ($this->isIncludeRootField($fields, 'order_state')) {
                                $r->data['order_state'] = $order->data['state'];
                            }
                        }
                    }
                    $r->data['content'] = $content;
                    remove_arr($r->data, 'data');
                } else {
                    return $this->output(new ResultModel(false, '车主提醒操作标码不存在'));
                }
            }
        }
        return $this->output($r);
    }

    /**
     * 设置消息为已读
     * @param int $task_id 消息ID
     */
    public function doReadMessage($task_id)
    {
        $r = $this->find(array('task_id' => $task_id, 'driver_id' => $this->state->user_id), 'DriverRemindTask', 'task_id');
        if ($r->ret) {
            $r = $this->save('DriverRemindTask', array('task_id' => $task_id, 'result' => 1));
        } else {
            return $this->output(new ResultModel(false, '消息ID不存在，或没有权限设置'));
        }
        return $this->output($r);
    }

    /**
     * 删除消息
     * @param int $task_id 消息ID
     */
    public function doDeleteMessage($task_id)
    {
        $r = $this->find(array('task_id' => $task_id, 'driver_id' => $this->state->user_id), 'DriverRemindTask', 'task_id');
        if ($r->ret) {
            $r = $this->delete($task_id, 'DriverRemindTask');
        } else {
            return $this->output(new ResultModel(false, '消息ID不存在，或没有权限设置'));
        }
        return $this->output($r);
    }

    /**
     * 获取未读的消息条数
     */
    public function getUnreadCount()
    {
        $r = $this->count(array('result' => 0, 'driver_id' => $this->state->user_id), 'DriverRemindTask');
        return $this->output($r);
    }


    /**
     * 获取最新的一条消息
     */
    public function getTimelyMessage(){
        $where['driver_id'] = $this->state->user_id;
        $where['is_del'] = 0;
        $where['status'] = 0;
        $r = $this->select($where, 1, 1,'create_time asc','DriverMessage');
        if($r->ret){
            foreach($r->data as $k=>$v){
                //$messageS = $this->save('DriverMessage', array('message_id' => $v['message_id'], 'status' => 1));
            }
        }
        return $this->output($r);
    }
}

?>

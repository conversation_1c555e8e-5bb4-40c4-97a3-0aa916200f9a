<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 家长互助权益价格
 *
 * <AUTHOR>
 */
class YyParentHelpPriceAction extends \DriverCoreAction
{

    /**
     * 获取家长互助信息发布服务费
     */
    public function getParentHelpPrice(){
        $where['parent_help_price_id'] = 1;
        $where['mchid'] = $this->mchid;
        $fields = "parent_help_price_id, name, price, reseverd_info";
        $parentHelpPriceR = $this->find($where, 'YyParentHelpPrice', $fields);
        return $this->output($parentHelpPriceR);
    }
}

?>
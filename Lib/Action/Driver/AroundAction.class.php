<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');

/**
 * 附近模块
 *
 * <AUTHOR>
 */
class AroundAction extends DriverCoreAction
{

    /**
     * 获取附近的乘客
     * @param double $longitude 经度（默认为空，为当前车主上次刷新的经度）
     * @param double $latitude 纬度（默认为空，为当前车主上次刷新的纬度）
     * @param float $limit 最大距离（单位m，默认为10000，10公里）
     * @param string fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAroundPassenger($longitude = null, $latitude = null, $limit = 10000, $fields = null, $page = 1, $size = 10)
    {
        $where = array();
        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'longitude,latitude,drive_mode,start_address_code,end_address_code,address_code,start_time');
        if ($r->ret) {
            $latitude = $r->data['latitude'];
            $longitude = $r->data['longitude'];
            if (empty($longitude) || empty($latitude)) {
                return $this->output(new ResultModel(false, '不能定位你的位置'));
            }
            $drive_mode = intval($r->data['drive_mode']); //根据车主的乘车消息过滤（市相同）
            if ($drive_mode === 1) {
                $start_address_code = $r->data['start_address_code'];
                $end_address_code = $r->data['end_address_code'];
                $where['drive_mode'] = $drive_mode;
                $r = $this->resolutionAddress($start_address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['start_address_code'] = $start_address_code;
                    } else {
                        $where['start_address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
                $r = $this->resolutionAddress($end_address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['end_address_code'] = $end_address_code;
                    } else {
                        $where['end_address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
            } else if ($drive_mode === 2) {
                $address_code = $r->data['address_code'];
                $where['drive_mode'] = $drive_mode;
                $r = $this->resolutionAddress($address_code);
                if ($r->ret) {
                    if (empty($r->data['city_code'])) {
                        $where['address_code'] = $address_code;
                    } else {
                        $where['address_code'] = array(array('egt', intval($r->data['city_code'])), array('elt', intval($r->data['city_code']) + 9999));
                    }
                } else {
                    return $this->output($r);
                }
            } else {
                return $this->output(new ResultModel(false, '没发布过乘车消息'));
            }
        } else {
            return $this->output(new ResultModel(false, '车主不存在'));
        }
        $where['start_time'] = array('gt', get_current_time());   //出发时间大于当前时间
        $where['state'] = 2; //乘客状态为车下
        $passengerModel = new PassengerModel();
        $field = "passenger_id,GETDISTANCE(latitude,longitude,$latitude,$longitude) AS distance";
        $data = $passengerModel->where($where)->field($field)->having("distance < $limit")->order('distance asc')->page($page, $size)->select();
        if ($data) {
            $data1 = $passengerModel->where($where)->field($field)->having("distance < $limit")->select();
            $count = count($data1);
            foreach ($data as $key => $value) {
                $o = $this->sudo('Passenger\\Account', $value['passenger_id'], StateModel::$PASSENGER);
                $r = $o->getPassenger($value['passenger_id'], $fields);
                if ($r->ret) {
                    $data[$key] = $r->data;
                    $data[$key]['distance'] = $value['distance'];
                } else {
                    $data[$key] = $value;
                    $data[$key]['error'] = $r->data;
                }
            }
            return $this->output(new ResultModel(true, $data, $count));
        }
        return $this->output(new ResultModel(false));
    }

}

?>

<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 订单模块
 *
 * <AUTHOR>
 */
class OrderAction extends \DriverCoreAction
{
    /**
     * 获取正在进行的订单或历史订单
     * @param int $type 类型（默认为空，查询所有，1-进行中，2-历史记录）
     * @param int $state 订单状态（默认为空，查询所有，1-乘客预定/车主邀请，2-车主同意/乘客同意，3-车主确认乘客上车，4-车主在乘客下车前清空位置，5-乘客在车主清空位置前下车，|6-正常完成（车主在乘客下车后清空位置或乘客在车主清空位置后下车），7-乘客取消/车主取消，8-车主拒绝/乘客拒绝，9-车主在同意后，乘客上车前清空位置）
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrders($type = null, $state = null, $page = 1, $size = 10, $fields = null)
    {
        if (!empty($type)) {
            $type = intval($type);
            if ($type === 1) {
                $where['state'] = array('in', '1,2,3,5');
                $order = 'create_time desc';
            } elseif ($type === 2) {
                $where['state'] = array('in', '4,6,7,8,9');
                $order = 'update_time desc';
            } else {
                return $this->output(new \ResultModel(false, '类型格式不正确'));
            }
        }
        if (!empty($state)) {
            $where['state'] = intval($state);
        }
        $where['driver_id'] = $this->state->user_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key] = $this->sudoGetEntityById("order_id", $value['order_id'], 'Driver\\Order', 'getOrder', $fields)->data;
            }

        }
    }

    /**
     * 获取订单
     * @param int $order_id 订单ID
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getOrder($order_id, $fields = null)
    {
        $r = $this->getEntityById($order_id, 'Order', $fields, null, 'drive_mode,start_address_code,end_address_code,address_code,driver_id,passenger_id');
        if ($r->ret) {
            if ($this->isIncludeRootField($fields, 'start_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['start_address_code']);
                if ($t->ret) {
                    $r->data['start_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'end_address') && intval($r->data['drive_mode']) === 1) {
                $t = $this->resolutionAddress($r->data['end_address_code']);
                if ($t->ret) {
                    $r->data['end_address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'address_code') && intval($r->data['drive_mode']) === 2) {
                $t = $this->resolutionAddress($r->data['address_code']);
                if ($t->ret) {
                    $r->data['address'] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            $this->sudoLoadSubEntity($r, 'passenger_id', 'passenger_id', 'Passenger\\Account', 'getPassenger', $fields, 'passenger');
            $this->sudoLoadSubEntity($r, 'driver_id', 'driver_id', 'Driver\\Account', 'getDriver', $fields, 'driver');
        }
        return $this->output($r);
    }

    /**
     * 处理乘客预订订单
     * @param int $order_id 订单ID
     * @param boolean $is_agree 是否同意（0-拒绝，1-接受）
     * @param int $reason 拒绝预订原因（默认为空，当拒绝时有效，1-人数已满，2-路线已取消，3-其他原因）
     */
    public function doAnswerBookOrder($order_id, $is_agree, $reason = null)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => 1, 'type' => 1, 'driver_id' => $this->state->user_id), 'Order', 'order_id,passenger_id,book_seating');
        if ($r->ret) {
            $passenger_id = $r->data['passenger_id'];
            $book_seating = intval($r->data['book_seating']);
            if (intval($is_agree) === 1) {
                $state = 2;
            } else {
                $state = 8;
            }
            $data = array('order_id' => $order_id, 'state' => $state);
            if ($reason) {
                $data['refuse_book_reason'] = intval($reason);
            }
            $this->startTrans();
            $r = $this->save('Order', $data);

            if ($r->ret) {
                if ($state === 2) {
                    //车主剩余座位数减去预订座位数
                    $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'residual_seating');
                    if ($r->ret) {
                        $residual_seating = intval($r->data['residual_seating']);
                        if ($residual_seating - $book_seating >= 0) {
                            $residual_seating = $residual_seating - $book_seating;
                            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'residual_seating' => $residual_seating));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客太多了，坐不下了'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    //车主接受乘客的一个预订后，则乘客的其他预订自动取消
                    $r = $this->select(array('passenger_id' => $passenger_id, 'state' => 1, 'type' => 1), null, null, null, 'Order', 'order_id');
                    if ($r->ret) {
                        $o = $this->sudo('Passenger\\Order', $passenger_id, \StateModel::$PASSENGER);
                        foreach ($r->data as $value) {
                            $o->data = array('order_id' => $value['order_id']);
                            $t = $o->doCancelBook($value['order_id']);
                            if (!$t->ret) {
                                $this->transRollback();
                                return $this->output($t);
                            }
                        }
                    }
                    //提醒乘客，车主已经接受预订
                    $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'accept book', $order_id);
                    if ($r->ret) {
                        //添加预订订单历史状态
                        $r = $this->addBookOrderHistoryState($order_id, 'accepted');
                        if ($r->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    //提醒乘客，车主拒绝了预订
                    $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'refuse book', $order_id);
                    if ($r->ret) {
                        //添加预订订单历史状态
                        $r = $this->addBookOrderHistoryState($order_id, 'refuse book');
                        if ($r->ret) {
                            if ($reason) {
                                //添加评价
                                switch (intval($reason)) {
                                    case 1:
                                        $reason = '人数已满';
                                        break;
                                    case 2:
                                        $reason = '路线已取消';
                                        break;
                                    case 3:
                                        $reason = '其他原因';
                                        break;
                                }
                                $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'user_name');
                                if ($r->ret) {
                                    $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 2, 'state' => 3, 'content' => $reason));
                                    if (!$r->ret) {
                                        $this->transRollback();
                                        return $this->output($r);
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        return $this->output($r);
    }

    /**
     * 确认乘客上车或拒载，以及出发点清空
     * @param int $order_id 订单ID
     * @param boolean $is_get_on 是否上车（0-未上车、清空座位，1-已上车）
     * @param int $reason 拒载原因（默认为空，当车主拒载时有效，1-乘客拒座，2-服务态度，3-坐朋友的车，4-有事耽误）
     */
    public function doConfirmPassengerGetOn($order_id, $is_get_on, $reason = null)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '2,3'), 'driver_id' => $this->state->user_id), 'Order', 'order_id,passenger_id,book_seating');
        if ($r->ret) {
            $passenger_id = $r->data['passenger_id'];
            $type = $r->data['type'];
            $book_seating = intval($r->data['book_seating']);
            if (!empty($reason) || (intval($is_get_on) === 0)) {          //判断是否为车主清空
                $data['refuse_take_reason'] = intval($reason);
                $data['is_bad_record'] = 1;
                $state = 9;
            } else {
                $state = 3;
            }
            $data = array('order_id' => $order_id, 'state' => $state);
            $this->startTrans();
            $r = $this->save('Order', $data);
            if ($r->ret) {
                if ($state === 3) {
                    //更新乘客状态，由“下车”变为“上车”
                    $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state');
                    if ($r->ret) {
                        $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'state' => 1));
                        if (!$r->ret) {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    //判断是否已经搭载满
                    $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'residual_seating,state');
                    if ($r->ret) {
                        $residual_seating = intval($r->data['residual_seating']);
                        //判断是否已经全部上车
                        $count1 = $this->count(array('driver_id' => $this->state->user_id, 'state' => array('in', '1,2,3')), 'Order');
                        $count2 = $this->count(array('driver_id' => $this->state->user_id, 'state' => 3), 'Order');
                        if ($residual_seating === 0 && $count1->data === $count2->data) { //已经搭载满并且全部上车则进行自动出发
                            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'state' => 2));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    //提醒乘客
                    $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'confirm get on', $order_id);
                    if ($r->ret) {
                        if (intval($type) === 1) {
                            //添加预订订单历史状态
                            $r = $this->addBookOrderHistoryState($order_id, 'got on');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            //添加邀请订单历史状态
                            $r = $this->addInviteOrderHistoryState($order_id, 'got on');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    if (intval($reason) === 4) {
                        //更新车主信用度和不良记录
                        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'credit,bad_record_count');
                        if ($r->ret) {
                            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'credit' => (intval($r->data['credit']) - 2), 'bad_record_count' => (intval($r->data['bad_record_count']) + 1)));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    } else {
                        //更新乘客信用度
                        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'credit');
                        if ($r->ret) {
                            $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'credit' => (intval($r->data['credit']) - 1)));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    }
                    if (intval($is_get_on) === 1) {                    //出发点清空
                        //更新乘客状态为下车
                        $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'state');
                        if ($r->ret) {
                            if (intval($r->data['state']) === 2) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '乘客状态已经为下车'));
                            } else {
                                $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'state' => 2));
                                if (!$r->ret) {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '乘客不存在'));
                        }
                        //更新车主状态为等候
                        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'state');
                        if ($r->ret) {
                            if (intval($r->data['state']) !== 1) {
                                $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'state' => 1));
                                if (!$r->ret) {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '车主不存在'));
                        }
                    }
                    //清空座位，车主剩余座位数减预订座位数
                    $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'residual_seating,total_seating');
                    if ($r->ret) {
                        $residual_seating = intval($r->data['residual_seating']);
                        $total_seating = intval($r->data['total_seating']);
                        if ($total_seating > $residual_seating) {
                            $residual_seating = $residual_seating + $book_seating;
                            $r = $this->save('Driver', array('driver_id' => $this->state->user_id, 'residual_seating' => $residual_seating));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($r);
                    }
                    //提醒乘客
                    $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'refuse take', $order_id);
                    if ($r->ret) {
                        if ($reason) {
                            //添加评价
                            switch (intval($reason)) {
                                case 1:
                                    $reason = '乘客拒座';
                                    break;
                                case 2:
                                    $reason = '服务态度';
                                    break;
                                case 3:
                                    $reason = '坐朋友的车';
                                    break;
                                case 4:
                                    $reason = '有事耽误';
                                    break;
                            }
                            $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'user_name');
                            if ($r->ret) {
                                $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 2, 'state' => 4, 'content' => $reason));
                                if (!$r->ret) {
                                    $this->transRollback();
                                    return $this->output($r);
                                }
                            } else {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        }
                        if (intval($type) === 1) {
                            //添加预订订单历史状态
                            $r = $this->addBookOrderHistoryState($order_id, 'refuse take');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            //添加邀请订单历史状态
                            $r = $this->addInviteOrderHistoryState($order_id, 'refuse take');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        return $this->output($r);
    }

    /**
     * 清空座位
     * @param int $order_id 订单ID
     */
    public function doRemoveSeating($order_id)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '3,5'), 'driver_id' => $this->state->user_id), 'Order', 'passenger_id,order_id,state,book_seating');
        if ($r->ret) {
            $passenger_id = $r->data['passenger_id'];
            $type = $r->data['type'];
            $book_seating = intval($r->data['book_seating']);
            if (intval($r->data['state']) === 3) {
                $state = 4; //车主在乘客下车前清空位置
                $is_allow_driver_evaluate = 0;
            } else {
                $state = 6;
                $is_allow_driver_evaluate = 1;
            }
            $this->startTrans();
            $r = $this->save('Order', array('order_id' => $order_id, 'state' => $state, 'is_allow_driver_evaluate' => $is_allow_driver_evaluate, 'is_allow_passenger_evaluate' => 1));
            if ($r->ret) {
                //更新车主状态，“在路上”为“等候”，并清空座位
                $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'state,residual_seating,total_seating,credit');
                if ($r->ret) {
                    $residual_seating = intval($r->data['residual_seating']);
                    $total_seating = intval($r->data['total_seating']);
                    if ($total_seating > $residual_seating) {
                        $residual_seating += $book_seating;
                        $data = array('driver_id' => $this->state->user_id, 'residual_seating' => $residual_seating, 'credit' => (intval($r->data['credit']) + 1));
                        //                        if ($total_seating === $residual_seating) {
                        //                            $data['state'] = 1;      //所有座位清空，则设为等候状态
                        //                        }
                        $data['state'] = 1;
                        $r = $this->save('Driver', $data);
                        if (!$r->ret) {
                            $this->transRollback();
                            return $this->output($r);
                        }
                    }
                } else {
                    $this->transRollback();
                    return $this->output($r);
                }
                if ($state === 4) {
                    //提醒乘客
                    $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'remove seating', $order_id);
                    if ($r->ret) {
                        if (intval($type) === 1) {
                            //添加预订订单历史状态
                            $r = $this->addBookOrderHistoryState($order_id, 'remove seating');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            //添加邀请订单历史状态
                            $r = $this->addInviteOrderHistoryState($order_id, 'remove seating');
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        }
                    } else {
                        $this->transRollback();
                    }
                } else {
                    if (intval($type) === 1) {
                        //添加预订订单历史状态
                        $r = $this->addBookOrderHistoryState($order_id, 'success');
                        if ($r->ret) {
                            $r = $this->deductInsurance($order_id);
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        //添加邀请订单历史状态
                        $r = $this->addInviteOrderHistoryState($order_id, 'success');
                        if ($r->ret) {
                            $r = $this->deductInsurance($order_id);
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    }
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限处理该订单'));
        }
        if ($r->ret) {
            return $this->output(new \ResultModel(true, array('is_allow_evaluate' => $is_allow_driver_evaluate)));
        }
        return $this->output($r);
    }

    /**
     * 邀请乘车
     * @param int $passenger_id 乘客ID
     */
    public function doInvitePassenger($passenger_id)
    {
        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'driver_id,drive_mode,start_address_code,end_address_code,start_address_remark,end_address_remark,address_code,start_time,balance,price,state');
        if ($r->ret) {
            //验证余额
            if (floatval($r->data['balance']) <= 0.0) {
                return $this->output(new \ResultModel(false, '您的账户余额不足，请充值'));
            }
            //验证状态
            if (intval($r->data['state']) === 2) {
                return $this->output(new \ResultModel(false, '在路上时不能邀请乘客'));
            }
            $p = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'ride_count');
            if (!$p->ret) {
                return $this->output(new \ResultModel(false, '乘客不存在'));
            }
            $this->startTrans();
            $r = $this->add('Order', array('driver_id' => $this->state->user_id, 'passenger_id' => $passenger_id, 'type' => 2, 'book_seating' => intval($p->data['ride_count']),
                'drive_mode' => $r->data['drive_mode'], 'start_address_code' => $r->data['start_address_code'], 'end_address_code' => $r->data['end_address_code'],
                'start_address_remark' => $r->data['start_address_remark'], 'end_address_remark' => $r->data['end_address_remark'], 'address_code' => $r->data['address_code'], 'price' => $r->data['price'], 'start_time' => $r->data['start_time']));
            if ($r->ret) {
                $order_id = $r->data;
                $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'invite passenger', $order_id);
                if ($r->ret) {
                    //添加邀请订单历史状态
                    $r = $this->addInviteOrderHistoryState($order_id, 'invite passenger');
                    if ($r->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                $this->transRollback();
            }
        }
        return $this->output($r);
    }

    /**
     * 取消邀请
     * @param int $order_id 订单ID
     */
    public function doCancelInvite($order_id)
    {
        $r = $this->find(array('order_id' => $order_id, 'driver_id' => $this->state->user_id, 'type' => 2, 'state' => array('in', '1,3')), 'Order', 'passenger_id,order_id');
        if ($r->ret) {
            $passenger_id = $r->data['passenger_id'];
            $this->startTrans();
            $r = $this->save('Order', array('order_id' => $order_id, 'state' => 7), '');
            if ($r->ret) {
                $r = $this->assignRemindTask($this->state->user_id, $passenger_id, 'cancel invite', $order_id);
                if ($r->ret) {
                    //添加邀请订单历史状态
                    $r = $this->addInviteOrderHistoryState($order_id, 'cancel invite');
                    if ($r->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限取消预订'));
        }
        return $this->output($r);
    }

    /**
     * 评价
     * @param int $order_id 订单ID
     * @param int $passenger_attitude 乘客态度（1-差评，2-中评，3-好评）
     */
    public function doEvaluate($order_id, $passenger_attitude)
    {
        $r = $this->find(array('order_id' => $order_id, 'state' => array('in', '4,6'), 'driver_id' => $this->state->user_id), 'Order', 'order_id,passenger_id');
        if ($r->ret) {
            $passenger_id = intval($r->data['passenger_id']);
            $r = $this->save('Order', array('order_id' => $order_id, 'passenger_attitude' => intval($passenger_attitude), 'is_allow_driver_evaluate' => 0));
            if ($r->ret) {
                $r = $this->find(array('passenger_id' => $passenger_id), 'Passenger', 'credit,praise,bad,general');
                if ($r->ret) {
                    $credit = intval($r->data['credit']);
                    $praise = intval($r->data['praise']);
                    $bad = intval($r->data['bad']);
                    $general = intval($r->data['general']);
                    if (intval($passenger_attitude) === 1) {
                        $passenger_attitude = '服务态度 差';
                        $credit--;
                        $bad++;
                    } elseif (intval($passenger_attitude) === 2) {
                        $passenger_attitude = '服务态度 一般';
                        $general++;
                    } else {
                        $passenger_attitude = '服务态度 好';
                        $credit++;
                        $praise++;
                    }
                    //更新乘客的信用度、差评、中评、好评
                    $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'credit' => $credit, 'praise' => $praise, 'bad' => $bad, 'general' => $general));
                    if ($r->ret) {
                        $r = $this->find(array('driver_id' => $this->state->user_id), 'Driver', 'user_name');
                        if ($r->ret) {
                            $r = $this->add('Evaluation', array('user_name' => $r->data['user_name'], 'user_group' => 2, 'state' => 5, 'content' => $passenger_attitude));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output($r);
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($r);
                        }
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                } else {
                    $this->transRollback();
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new \ResultModel(false, '订单ID无效或没有权限评价该订单'));
        }
        return $this->output($r);
    }


    /**
     * 订单详情
     * @param string $order_no
     * @return null|\resultModel
     */
    public function getOrderDeatail($order_no)
    {
        $orderR = $this->find(array('order_no' => $order_no), 'Order', 'order_no,passenger_id,order_id,state,type,offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_pre_pay,is_temp,start_latitude,start_longitude,start_address_remark,end_latitude,end_longitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,start_region_name,end_region_name,create_time,ft_number');
        if ($orderR->ret) {
            $current_time_stamp = time();
            //获取乘客信息
            $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
            if ($orderR->data['is_temp'] == 1) {
                $orderR->data['cellphone'] = $orderR->data['reseverd_phone'];
            } else {
                $orderR->data['cellphone'] = $passengerR->data['cellphone'];
            }
            $orderR->data['reseverd_phone'] = is_null($orderR->data['reseverd_phone']) ? $passengerR->data['cellphone'] : $orderR->data['reseverd_phone'];
            $orderR->data['passenger_name'] = $passengerR->data['name'];
            $orderR->data['start_name'] = $orderR->data['start_region_name'];
            $orderR->data['end_name'] = $orderR->data['end_region_name'];
            $orderR->data['away_time'] = (new \Mygettime($current_time_stamp, strtotime($orderR->data['create_time'])))->index();
            unset($orderR->data['start_region_name']);
            unset($orderR->data['end_region_name']);
        }
        return $this->output($orderR);
    }

    /**
     * 获取司机待接收订单
     * @param int $driver_id 司机ID
     * @param int $page 页码
     * @param int $size 页大小
     */
    public function doGetDealOrderList($driver_id, $page = 1, $size = 20)
    {
        if (!$driver_id) {
            $driver_id = $this->state->user_id;
        }

        $where = ' o.driver_id = ' . $driver_id . ' and o.state=1 ';
        $orderModel = new \OrderModel();
        $passengerTable = (new \PassengerModel())->getTableName();
        $driverTable = (new \DriverModel())->getTableName();
        $orderList = $orderModel->alias('o')->
        join("$passengerTable p ON o.passenger_id=p.passenger_id")->
        join("$driverTable d ON o.driver_id=d.driver_id")->
        where($where)->page($page, $size)->
        field('o.order_id,o.driver_id,p.passenger_id,o.create_time,o.offer_price,p.cellphone,o.start_address_code,o.end_address_code,o.state,o.driver_isover,o.book_seating,p.name')->
        order('o.order_id desc')->select();
        $count = $orderModel->alias('o')->
        join("$passengerTable p ON o.passenger_id=p.passenger_id")->
        join("$driverTable d ON o.driver_id=d.driver_id")->
        where($where)->
        field('o.order_id,o.driver_id,p.passenger_id,o.create_time,o.offer_price,p.cellphone,o.start_address_code,o.end_address_code,o.state,o.driver_isover,o.book_seating,p.name')->
        order('o.order_id desc')->count();
        foreach ($orderList as $orderK => $orderV) {
            $orderV['route'] = '线路获取失败';
            if ($orderV['start_address_code'] && $orderV['end_address_code']) {
                $start = $this->checkingGdAddressCode($orderV['start_address_code']);
                $end = $this->checkingAddressCode($orderV['end_address_code']);
                $orderV['route'] = $start->data['address'] . "-" . $end->data['address'];
            }
            if ($orderV['state'] == 1) {
                $orderV['state'] = false;
            } elseif ($orderV['state'] == 2) {
                $orderV['state'] = true;
            }

            if ($orderV['driver_isover'] == 0) {
                $orderV['driver_isover'] = false;
            } elseif ($orderV['driver_isover'] == 1) {
                $orderV['driver_isover'] = true;
            }

            unset($orderV['start_address_code']);
            unset($orderV['end_address_code']);
            $orderList[$orderK] = $orderV;
        }

        return $this->output(new \ResultModel(true, $orderList, $count));
    }


    /**
     * 预约订单列表
     *
     * @param integer $page 页码
     * @param integer $size 页大小
     * @param string $longitude 经度
     * @param string $latitude 纬度
     * @param integer $type 订单业态 1:拼车 2:包车 3:带货 4:代办 5:班线 6:顺风车 7:快车 11:出租车 20:摆渡车
     * @param string $sort_by 排序字段 start_time:出发时间升序
     * @return void
     */
    public function doGetAppointOrderList($page = 1, $size = 10, $longitude = null, $latitude = null, $type = null, $sort_by = null)
    {
        $r = new \ResultModel();

        $driverR = $this->validateDriver();
        $this->updateDriverLocation($longitude, $latitude);

        $where = "";
        # 豫客行 查看同线路下未接单的订单；
        //同类型司机
        if (in_array($driverR->data['mchid'], [1310,1316,181])) {
            //筛选司机参与的线路
            $driverLineClassIdsArr = M()->table('cp_line_class_train_driver lctd')
                ->join("LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = lctd.line_class_train_id")
                ->join("LEFT JOIN cp_line_class lc ON lc.id = lct.line_class_id")
                ->where("lctd.driver_id = ".$driverR->data['driver_id'])
                ->field("lc.id as line_class_id")
                ->group('lc.id')
                ->select();

            if (empty($driverLineClassIdsArr)) {
                return $this->output(new \ResultModel(false, '暂无订单'));
            }
            // 提取 "line_class_id" 对应的值
            $line_class_ids = array_column($driverLineClassIdsArr, 'line_class_id');
            // 将值组成一个字符串，以逗号分隔
            $line_class_ids_str = implode(',', $line_class_ids);
            //找出线路下所有的班次(4小时后)
            // 分割日期和时间
            list($endDate, $endTime) = explode(' ', date('Y-m-d H:i:s', strtotime('+24 hours')));

            $endDateTimeIsToday = true;
            // 将指定的日期转换为时间戳
            $timestamp = strtotime($endDate);
            // 获取今天的时间戳
            $todayTimestamp = strtotime(date('Y-m-d'));
            // 判断指定的日期是否是今天
            if ($timestamp === $todayTimestamp) {
                $endDateTimeIsToday = true;
            } else {
                $endDateTimeIsToday = false;
            }

            $line_class_train_ids_str = '';
            if ($endDateTimeIsToday) {
                $driverLineClassTrainWhere = " start_date = '".date('Y-m-d')."'";
                $driverLineClassTrainWhere .= " AND start_time <= '".$endTime."'";
            } else {
                $driverLineClassTrainWhere = " (start_date = '".date('Y-m-d')."' AND start_time<='23:59:59') OR (start_date = '".$endDate."' AND start_time<='".$endTime."') ";
            }
            $driverLineClassTrainWhere .= " AND line_class_id IN(".$line_class_ids_str.")";
            $driverLineClassTrainWhere .= " AND branchid = ".$driverR->data['branchid'];
            $driverLineClassTrainIdsRs = $this->select($driverLineClassTrainWhere, null, null, null, 'LineClassTrain', 'line_class_train_id');
            if ($driverLineClassTrainIdsRs->ret) {
                // 提取 "line_class_train_id" 对应的值
                $line_class_train_ids = array_column($driverLineClassTrainIdsRs->data, 'line_class_train_id');
                // 将值组成一个字符串，以逗号分隔
                $line_class_train_ids_str = implode(',', $line_class_train_ids);
            }

            $where = sprintf(' o.state = 1 AND o.mchid= %d', $driverR->data['mchid']);
            $whereTemp = " o.driver_id = ".$driverR->data['driver_id'];
            $whereOrderTemp = " d.driver_id = ".$driverR->data['driver_id'];
            if (!empty($line_class_train_ids_str)) {
                $whereTemp = " ( o.line_class_train_id IN(".$line_class_train_ids_str.") OR ".$whereTemp.")";
                $whereOrderTemp = " ( o.line_class_train_id IN(".$line_class_train_ids_str.") OR ".$whereOrderTemp.")";
            }

            $where .= sprintf(
                " AND ((o.appoint = 1 AND o.rob_order = 0 AND %s) OR (o.rob_order = 1 AND o.appoint = 0 AND d.branchid = %d AND %s))
                AND (o.pay_mode = 0 OR (o.pay_mode = 1 AND o.is_pay = 1))",
                $whereTemp,
                $driverR->data['branchid'],
                $whereOrderTemp
            );

        } else {
            $where = sprintf(' o.state = 1 AND o.mchid= %d', $driverR->data['mchid']);
            $where .= sprintf(
                " AND ((o.appoint = 1
                    AND o.rob_order = 0
                    AND o.driver_id = %d)
                OR (o.rob_order = 1
                    AND o.appoint = 0
                    AND d.branchid = %d
                    AND d.driver_id = %d))
              AND (o.pay_mode = 0
                OR (o.pay_mode = 1
                    AND o.is_pay = 1))",
                $driverR->data['driver_id'],
                $driverR->data['branchid'],
                $driverR->data['driver_id']
            );
        }


        if ($type) {
            $where .= sprintf(' AND o.`type` = %s', $type);
        }

        if ($sort_by) {
            $order = sprintf(' o.`%s` DESC', $sort_by);
        } else {
            $order = ' o.`create_time` DESC';
        }

        $field = 'o.order_no,o.passenger_id,o.order_id,o.state,o.type,o.offer_price as price,o.book_seating,o.line_id,o.start_time,o.update_time,o.reseverd_phone,o.reseverd_person,o.reseverd_info,o.branchid,o.driver_id,o.create_time,o.pay_mode,o.is_pre_pay,o.is_temp,o.start_latitude,o.start_longitude,o.start_address_remark,o.end_latitude,o.end_longitude,o.end_address_remark,o.is_custom,o.car_type_id,o.delivery_person,o.delivery_phone,o.weight,o.agency_id,o.rob_order,o.ft_number,o.seat_optional,o.ferry_type';
        $orderArr = M()->table('cp_order o')
            ->join("LEFT JOIN cp_driver d ON o.branchid = d.branchid")
            ->where($where)
            ->field($field)
            ->page($page, $size)
            ->order($order)
            ->group('o.order_no')
            ->select();
        $orderCount = M()->table('cp_order o')
            ->join("LEFT JOIN cp_driver d ON o.branchid = d.branchid")
            ->where($where)
            ->field($field)
            ->order($order)
            // ->group('o.order_no')
            ->count('distinct(o.order_id)');
        // 输出最后生成的 SQL 语句
        // var_dump(M()->getLastSql());die;

        if ($orderArr) {
            $r->ret = true;
            $r->data = $orderArr;
            $r->count = $orderCount;
            foreach ($r->data as $key => $value) {
                $r->data[$key]['passenger_name'] = "";
                $r->data[$key]['cellphone'] = "";
                if ($r->data[$key]['is_temp'] == \CommonDefine::ORDER_IS_TEMP_1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    if (!empty($value['passenger_id'])) {
                        //获取乘客信息
                        $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                        if ($passengerR->ret) {
                            $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                            $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                        }
                    }
                }

                if ($r->data[$key]['rob_order'] == \CommonDefine::ROB_ORDER_1) {
                    $r->data[$key]['cellphone'] = $this->encryptionCellphoneByCellphone($r->data[$key]['cellphone']);
                    $r->data[$key]['reseverd_phone'] = $this->encryptionCellphoneByCellphone($r->data[$key]['reseverd_phone']);
                }


                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];

                //获取线路信息
                //拼车
                if ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        $r->data[$key]['start_address_remark'] = "【".$r->data[$key]['start_name']."】". ($r->data[$key]['start_address_remark'] ? $r->data[$key]['start_address_remark'] : "默认");
                        $r->data[$key]['end_address_remark'] = "【".$r->data[$key]['end_name']."】".($r->data[$key]['end_address_remark'] ? $r->data[$key]['end_address_remark'] : "默认");
                    }
                }
                //包车
                elseif ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                            $r->data[$key]['start_address_remark'] = "【".$r->data[$key]['start_name']."】". ($r->data[$key]['start_address_remark'] ? $r->data[$key]['start_address_remark'] : "默认");
                            $r->data[$key]['end_address_remark'] = "【".$r->data[$key]['end_name']."】".($r->data[$key]['end_address_remark'] ? $r->data[$key]['end_address_remark'] : "默认");
                        }
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                    $r->data[$key]['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data[$key]['seat_optional']);
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['assigned_driver_is_me'] = true;
                if (!empty($value['driver_id'])) {
                    if ($value['driver_id'] != $driverR->data['driver_id']) {
                        $r->data[$key]['assigned_driver_is_me'] = false;
                    }
                    $orderDriverR = $this->find(['driver_id' => $value['driver_id']], 'Driver');
                    if ($orderDriverR->ret) {
                        $r->data[$key]['assigned_driver_name'] = $orderDriverR->data['name'];
                        $r->data[$key]['assigned_driver_cellphone'] = $orderDriverR->data['cellphone'];
                        $r->data[$key]['assigned_driver_car_tail_number'] = $orderDriverR->data['car_tail_number'];
                    }
                }

                $currentTime = time();
                $seconds = strtotime($value['update_time']) + C('TIMER_TIME_SET') - $currentTime;
                $countDown = 0;
                if ($seconds > 0) {
                    $countDown = date('i:s', $seconds);
                }
                $r->data[$key]['countdown_time'] = $countDown;
                $r->data[$key]['current_time'] = date('Y-m-d H:i:s', $currentTime);
            }
        }
        if ($r->ret) {
            return $this->output($r);
        } else {
            return $this->output(new \ResultModel(false, '暂无订单'));
        }
    }


    /**
     * 更新司机位置经纬度
     *
     * @param string $longitude
     * @param string $latitude
     * @return void
     */
    private function updateDriverLocation($longitude, $latitude)
    {
        # 更新司机位置经纬度
        if ($longitude
            && $latitude) {
            $this->save('Driver', array('driver_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        }
    }

    /**
     * 司机合法性验证
     *
     * @return
     */
    private function validateDriver()
    {
        $driverR = $this->find(array('driver_id' =>  $this->state->user_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        }
        return $driverR;
    }

    /**
     * 获取司机正在进行中订单列表
     * @param int $driver_id 司机ID
     * @param int $page 页码
     * @param int $size 页大小
     */
    public function doGetOrderList($driver_id, $page = 1, $size = 20)
    {
        $driver_id = $this->state->user_id;
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }

        if ($driverR->data['current_train_id']) {
            $where = "state in(2,3,4,5) and appoint=1 and driver_id=$driver_id and (train_id = ". $driverR->data['current_train_id']." OR train_id is null)";
        } else {
            $where = "state in(2,3,4,5) and appoint=1 and driver_id=$driver_id";
        }

        $order = 'create_time desc';

        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state,type,offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_temp,start_latitude,start_longitude,start_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,ft_number');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];

                //获取线路信息
                //拼车
                if ($r->data[$key]['type'] == 1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                }
                //包车
                elseif ($r->data[$key]['type'] == 2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        }
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['driver_name'] = $driverR->ret ? $driverR->data['name'] : "";
                $r->data[$key]['driver_cellphone'] = $driverR->ret ? $driverR->data['cellphone'] : "";
                $r->data[$key]['car_status'] = $driverR->data['state'];
                $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
            }
        }
        return $this->output($r);
    }

    /**
     * 司机实时订单列表（30分钟内）
     *
     * @param integer $page
     * @param integer $size
     * @param string $longitude
     * @param string $latitude
     * @param integer $type 订单业态 1:拼车 2:包车 3:带货 4:代办 5:班线 6:顺风车 7:快车 11:出租车 20:摆渡车
     * @param string $status 订单状态 not_boarding:待上车 not_delivered:待送达 not_paid:待支付
     * @param string $sort_by 排序字段 start_time:出发时间升序
     * @return void
     */
    public function doGetOrderOnList($page = 1, $size = 20, $longitude = null, $latitude = null, $type = 0, $status = '', $sort_by = '')
    {

        $driverR = $this->validateDriver();
        $this->updateDriverLocation($longitude, $latitude);

        // $where = sprintf(" `driver_id` = %d AND start_time <= '%s' AND mchid = %d", $this->state->user_id, date('Y-m-d H:i:s', C('TIMER_ORDER_LIMIT') + time()), $driverR->data['mchid']);


        $timeLimit = date('Y-m-d H:i:s', C('TIMER_ORDER_LIMIT') + time());
        if ($timeLimit) {
            $where = sprintf(
                " `driver_id` = %d
                    AND ((`type` != %d
                            AND `start_time` <= '%s')
                        OR `type` = %d)
                    AND mchid = %d ",
                $driverR->data['driver_id'],
                \CommonDefine::ORDER_TYPE_5,
                $timeLimit,
                \CommonDefine::ORDER_TYPE_5,
                $driverR->data['mchid']
            );
        } else {
            $where = sprintf(
                " driver_id = %d 
                    AND mchid = %d",
                $driverR->data['driver_id'],
                $driverR->data['mchid']
            );
        }

        switch ($status) {
            case 'not_boarding':
                # 待上车
                $where .= sprintf(' AND `state` = %d', \CommonDefine::ORDER_STATE_2);
                break;
            case 'not_delivered':
                # 待送达
                $where .= sprintf(' AND `state` IN (%s)', implode(',', [\CommonDefine::ORDER_STATE_2, \CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4]));
                break;
            case 'not_paid':
                # 待支付
                $where .= ' AND `is_pay` = 0';
                break;
            default:
                $where .= sprintf(' AND `state` IN (%s)', implode(',', [\CommonDefine::ORDER_STATE_2, \CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4, \CommonDefine::ORDER_STATE_5]));
                break;
        }

        if ($type) {
            $where .= sprintf(' AND `type` = %s', $type);
        }
        if ($sort_by) {
            $order = sprintf(' `%s` DESC', $sort_by);
        } else {
            $order = '`create_time` DESC';
        }

        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state,type,offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_pay,is_pre_pay,is_temp,start_latitude,start_longitude,start_address_remark,end_latitude,end_longitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,ft_number,seat_optional,ferry_type');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];

                //获取线路信息
                //拼车
                if ($r->data[$key]['type'] == 1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        $r->data[$key]['start_address_remark'] = "【".$r->data[$key]['start_name']."】". ($r->data[$key]['start_address_remark'] ? $r->data[$key]['start_address_remark'] : "默认");
                        $r->data[$key]['end_address_remark'] = "【".$r->data[$key]['end_name']."】".($r->data[$key]['end_address_remark'] ? $r->data[$key]['end_address_remark'] : "默认");
                    }
                }
                //包车
                elseif ($r->data[$key]['type'] == 2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                            $r->data[$key]['start_address_remark'] = "【".$r->data[$key]['start_name']."】". ($r->data[$key]['start_address_remark'] ? $r->data[$key]['start_address_remark'] : "默认");
                            $r->data[$key]['end_address_remark'] = "【".$r->data[$key]['end_name']."】".($r->data[$key]['end_address_remark'] ? $r->data[$key]['end_address_remark'] : "默认");
                        }
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                    $r->data[$key]['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data[$key]['seat_optional']);

                    $viaPointsR = $this->find(array('line_class_id' => $lineClassTrainR->data['line_class_id']), 'DingzhikeyunLineViaPoints');
                    if ($viaPointsR->ret) {
                        $r->data[$key]['via_province_code'] = $viaPointsR->data['province_code'];
                        $r->data[$key]['via_province_name'] = $viaPointsR->data['province_name'];
                        $r->data[$key]['via_city_code'] = $viaPointsR->data['city_code'];
                        $r->data[$key]['via_city_name'] = $viaPointsR->data['city_name'];
                        $r->data[$key]['via_area_code'] = $viaPointsR->data['area_code'];
                        $r->data[$key]['via_area_name'] = $viaPointsR->data['area_name'];
                        $r->data[$key]['via_name'] = $viaPointsR->data['name'];
                    }

                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                        $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                        unset($r->data[$key]['start_region_name']);
                        unset($r->data[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['driver_name'] = $driverR->ret ? $driverR->data['name'] : "";
                $r->data[$key]['driver_cellphone'] = $driverR->ret ? $driverR->data['cellphone'] : "";
                $r->data[$key]['car_status'] = $driverR->data['state'];
                $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                $r->data[$key]['longitude'] = $driverR->data['longitude'];
                $r->data[$key]['latitude'] = $driverR->data['latitude'];
                //已线下现金付款按钮
                $orderDriverPayOpConfig = \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_0;//已线下现金付款按钮
                $orderDriverPayOpConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => \CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG), 'SystemConfig');
                if ($orderDriverPayOpConfigR->ret) {
                    $orderDriverPayOpConfig = $orderDriverPayOpConfigR->data['value'];
                }
                $r->data[$key]['order_driver_pay_op_config'] = $orderDriverPayOpConfig;
            }
        } else {
            $r->data = '暂无订单';
        }
        return $this->output($r);
    }



    /**
     * 获取司机待出发订单列表（出发时间大于30分钟内的）
     * @param int $driver_id 司机ID
     * @param int $page 页码
     * @param int $size 页大小
     */
    public function doGetOrderWaitList($page = 1, $size = 20, $type = 0, $state = 0, $sort_by = '')
    {
        return $this->output(new \ResultModel(false, '没有更多订单了'));
        $driver_id = $this->state->user_id;
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }

        // $timeLimit = date('Y-m-d H:i:s', C('TIMER_ORDER_LIMIT') + time());

        // $where = "state = 2 and driver_id= $driver_id and  start_time > '".$timeLimit."' and type != ".\CommonDefine::ORDER_TYPE_5;
        $where = "state in(2,3,4,5) and driver_id= $driver_id ";

        if ($type) {
            $where .= ' and type=' . $type;
        }
        if ($state) {
            $where .= ' and state=' . $state;
        }
        if ($sort_by) {
            $order = $sort_by .' asc';
        } else {
            $order = 'create_time desc';
        }

        // $order = 'create_time desc';
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,type,state, offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_pre_pay,is_temp,start_latitude,start_longitude,start_address_remark,end_latitude,end_longitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,ft_number,seat_optional');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];

                //获取线路信息
                //拼车
                if ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                }
                //包车
                elseif ($r->data[$key]['type'] == \CommonDefine::ORDER_TYPE_2) {
                    if ($value['is_custom'] == 0) {
                        $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineR->ret) {
                            $r->data[$key]['start_name'] = $lineR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineR->data['end_name'];
                        }
                    }

                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                    $r->data[$key]['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data[$key]['seat_optional']);

                    $viaPointsR = $this->find(array('line_class_id' => $lineClassTrainR->data['line_class_id']), 'DingzhikeyunLineViaPoints');
                    if ($viaPointsR->ret) {
                        $r->data[$key]['via_province_code'] = $viaPointsR->data['province_code'];
                        $r->data[$key]['via_province_name'] = $viaPointsR->data['province_name'];
                        $r->data[$key]['via_city_code'] = $viaPointsR->data['city_code'];
                        $r->data[$key]['via_city_name'] = $viaPointsR->data['city_name'];
                        $r->data[$key]['via_area_code'] = $viaPointsR->data['area_code'];
                        $r->data[$key]['via_area_name'] = $viaPointsR->data['area_name'];
                        $r->data[$key]['via_name'] = $viaPointsR->data['name'];
                    }

                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                        $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                        unset($r->data[$key]['start_region_name']);
                        unset($r->data[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['driver_name'] = $driverR->ret ? $driverR->data['name'] : "";
                $r->data[$key]['driver_cellphone'] = $driverR->ret ? $driverR->data['cellphone'] : "";
                $r->data[$key]['car_status'] = $driverR->data['state'];
                $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                $r->data[$key]['longitude'] = $driverR->data['longitude'];
                $r->data[$key]['latitude'] = $driverR->data['latitude'];

            }
        }
        return $this->output($r);
    }





    /**
     * 获取司机历史订单
     * @param int $page 页码
     * @param int $size 页大小
     * @param string $stime 查询开始时间
     * @param string $etime 查询开始时间
     */
    public function doGetOrderOverList($page = 1, $size = 10, $stime = null, $etime = null)
    {
        $driver_id = $this->state->user_id;
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }

        $where['state'] = array('in', '6');
        $order = 'create_time desc';
        if ($stime) {
            $where['create_time'] = array('egt', $stime);
        }
        if ($etime) {
            $where['create_time'] = array('elt', $etime);
        }

        $where['driver_id'] = $driver_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state, type,offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_pre_pay,is_temp,start_longitude,start_latitude,start_address_remark,end_longitude,end_latitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,ft_number,seat_optional,ferry_type');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];
                $r->data[$key]['passenger_address'] = $r->data[$key]['start_address_remark'];

                //获取线路信息
                if ($r->data[$key]['type'] == 1) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($r->data[$key]['type'] == 2) {
                    if ($value['is_custom'] == 0) {
                        $lineCharteredR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                        if ($lineCharteredR->ret) {
                            $r->data[$key]['start_name'] = $lineCharteredR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineCharteredR->data['end_name'];
                        }
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                    $r->data[$key]['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data[$key]['seat_optional']);

                    $viaPointsR = $this->find(array('line_class_id' => $lineClassTrainR->data['line_class_id']), 'DingzhikeyunLineViaPoints');
                    if ($viaPointsR->ret) {
                        $r->data[$key]['via_province_code'] = $viaPointsR->data['province_code'];
                        $r->data[$key]['via_province_name'] = $viaPointsR->data['province_name'];
                        $r->data[$key]['via_city_code'] = $viaPointsR->data['city_code'];
                        $r->data[$key]['via_city_name'] = $viaPointsR->data['city_name'];
                        $r->data[$key]['via_area_code'] = $viaPointsR->data['area_code'];
                        $r->data[$key]['via_area_name'] = $viaPointsR->data['area_name'];
                        $r->data[$key]['via_name'] = $viaPointsR->data['name'];
                    }

                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                        $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                        unset($r->data[$key]['start_region_name']);
                        unset($r->data[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['driver_name'] = $driverR->ret ? $driverR->data['name'] : "";
                $r->data[$key]['driver_cellphone'] = $driverR->ret ? $driverR->data['cellphone'] : "";
                $r->data[$key]['car_status'] = $driverR->data['state'];
                $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
            }
        }
        return $this->output($r);
    }

    /**
     * 获取乘客发布的顺风车订单列表
     * @param int $start_address_code 出发地城市编码
     * @param int $end_address_code 目的地城市编码
     * @param int $day_time 出发时间
     * @param int $page 页码
     * @param int $size 页大小
     */
    public function doGetFreeRideOrderOnList($start_address_code = null, $end_address_code = null, $day_time = null, $page = 1, $size = 20)
    {
        $current_time_stamp = time();
        //未过期的顺风车订单
        $timeLimit = date('Y-m-d H:i:s', $current_time_stamp);
        $where = "type=".\CommonDefine::ORDER_TYPE_6." AND driver_id IS NULL AND branchid IS NULL AND start_time >='".$timeLimit."' AND mchid = ".$this->mchid;
        $where .= ' AND ((pay_mode = '.\CommonDefine::ORDER_PAYMENT_MODE_0. ") OR (is_pay = ".\CommonDefine::PAY_STATUS_1." AND pay_mode = ".\CommonDefine::ORDER_PAYMENT_MODE_1." )) ";
        $where .= ' AND state = 1';

        $start_time = "";
        $end_time = "";
        if (!empty($day_time)) {
            $start_time = date('Y-m-d 00:00:00', strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59', strtotime($day_time));
            $where .= " AND start_time >= '".$start_time."'";
            $where .= " AND start_time <= '".$end_time."'";
        }

        if (!empty($start_address_code)) {
            $where .= " AND start_address_code=".$start_address_code;
        }
        if (!empty($end_address_code)) {
            $where .= " AND end_address_code=".$end_address_code;
        }

        $order = 'start_time asc';

        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state,type,offer_price as price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_pre_pay,is_temp,start_latitude,start_longitude,start_address_remark,end_latitude,end_longitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id,start_region_name,end_region_name,create_time,ft_number');

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                $r->data[$key]['away_time'] = (new \Mygettime($current_time_stamp, strtotime($value['create_time'])))->index();
                unset($r->data[$key]['start_region_name']);
                unset($r->data[$key]['end_region_name']);
            }
        }
        return $this->output($r);
    }


    /**
     * 司机更新订单状态信息:
     * 1. status=3 乘客已上车
     * 2.
     * @param int $driver_id 司机ID
     * @param int $status 0-接受，1-拒绝,2-确认到达,3-确认已上车
     */
    public function doUpdateOrder($driver_id = null, $order_id, $status = 0, $longitude = null, $latitude = null, $force_boarding = true, $reason = '')
    {
        $driver_id = $this->state->user_id;
        $this->startTrans();
        # 查询司机信息
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if (!$driverR->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '司机账号（'.$driver_id.'）异常'));
        }
        # 更新司机经纬度位置数据
        if ($longitude && $latitude) {
            $this->save('Driver', array('driver_id' => $driver_id, 'longitude' => $longitude, 'latitude' => $latitude));
        } else {
            # 无上报经纬度，则使用司机本身的经纬度，目的是计算价格
            $longitude = $driverR->data['longitude'];
            $latitude = $driverR->data['latitude'];
        }
        # 查询订单信息
        $orderWhere = 'order_id='.$order_id.' AND mchid='.$driverR->data['mchid'];
        # 豫客行 允许司机接不是自己的订单
        if (!in_array($this->mchid, [1310,/*1280,*/181])) {
            $orderWhere .= ' AND ((driver_id='.$this->state->user_id.' AND rob_order = '.\CommonDefine::ROB_ORDER_0.') OR rob_order ='.\CommonDefine::ROB_ORDER_1.')';
        }
        $orderR = $this->find($orderWhere, 'Order');
        # 订单不存在，提示订单异常
        if (!$orderR->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '您无权限操作订单('.$order_id.')'));
        }
        # 订单异常判定
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_7) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该订单用户已取消,及时刷新页面以获取最新数据'));
        } elseif ($orderR->data['state'] == \CommonDefine::ORDER_STATE_6) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该订单已完成,及时刷新页面以获取最新数据'));
        } elseif ($orderR->data['state'] == \CommonDefine::ORDER_STATE_8) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该订单已关闭,及时刷新页面以获取最新数据'));
        }
        # 订单超时未接收，订单已重新指派
        if ($orderR->data['appoint'] == 0 && $orderR->data['rob_order'] == 0
            && ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1
                || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_2
                || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_3
                || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_4)) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '超时未接收，订单已重新指派'));
        }
        if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1 && $orderR->data['state'] != 1) {
            if ($orderR->data['driver_id'] != $driver_id) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '该订单已被抢'));
            } elseif ($orderR->data['driver_id'] == $driver_id && ($status == 2 || $status == 0)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '重复抢单'));
            }
        }

        if ($status == 1) {
            //拒绝订单
            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                    if (!$LineFreeRideR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                    }
                    $LineFreeRideUpdateData['id'] = $LineFreeRideR->data['id'];
                    $LineFreeRideUpdateData['residual_seating'] = $LineFreeRideR->data['residual_seating'] + $orderR->data['book_seating'];
                    $LineFreeRideS = $this->save('LineFreeRide', $LineFreeRideUpdateData);
                    if (!$LineFreeRideS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                    }

                    $orderR->data['state'] = 7;//订单回笼
                    $orderR->data['order_id'] = $order_id;
                    $orderS = $this->save('Order', $orderR->data);
                    if ($orderS->ret) {
                        $this->commitTrans();
                        //获取乘客相关信息
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                        if (C('WX_TEMPLATE_ON')) {
                            $temp = array(
                                '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                $orderR->data['order_no'],
                                '预约失败',
                                '请重新选择顺风车进行预约！'
                            );
                            $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                            unset($temp);
                        }

                        return $this->output(new \ResultModel(true, '操作成功'));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                } else {
                    if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                        $orderR->data['state'] = 7;//订单回笼
                        $orderR->data['order_id'] = $order_id;
                        $orderS = $this->save('Order', $orderR->data);
                        if ($orderS->ret) {
                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                $wxPayUtil = new \WxPayUtil();
                                if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                    $this->transRollback();
                                    return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                                }
                                $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                if (!$refundR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                                }

                                if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!' . $refundR->data['err_code_des']));
                                }

                                $refundData['amount'] = $orderR->data['price'];
                                $refundData['status'] = 3;
                                $refundData['order_id'] = $order_id;
                                $refundData['account_type'] = 1;//乘客
                                $refundData['account_id'] = $orderR->data['passenger_id'];
                                $refundData['refund_id'] = $refundR->data['refund_id'];
                                $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                $refundData['created'] = time();
                                $refundData['description'] = "取消订单，全额退款";
                                $refundData['charge'] = $refundR->data['charge'];
                                $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                $reR = $this->add('Refunds', $refundData);
                                if ($reR->ret) {
                                    $this->commitTrans();
                                    //获取乘客相关信息
                                    $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                    if (C('WX_TEMPLATE_ON')) {
                                        $temp = array(
                                            '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                            $orderR->data['order_no'],
                                            '预约失败',
                                            '请重新选择顺风车进行预约！'
                                        );
                                        $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                        unset($temp);
                                    }
                                    return $this->output(new \ResultModel(true, '操作成功'));
                                } else {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '取消失败'));
                                }
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '取消失败'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                }
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $orderR->data['state'] = 7;//订单回笼
                    $orderR->data['order_id'] = $order_id;
                    $orderS = $this->save('Order', $orderR->data);
                    if ($orderS->ret) {
                        $this->commitTrans();
                        //获取乘客相关信息
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                        if (C('WX_TEMPLATE_ON')) {
                            $temp = array(
                                '司机"'.$driverR->data['name'].'"拒绝了您的['.$this->getOrderType($orderR->data['type']).']预约！',
                                $orderR->data['order_no'],
                                '预约失败',
                                '请重新选择班线车进行预约！'
                            );
                            $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                            unset($temp);
                        }

                        return $this->output(new \ResultModel(true, '操作成功'));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                    $orderR->data['state'] = 7;//订单回笼
                    $orderR->data['order_id'] = $order_id;
                    $orderS = $this->save('Order', $orderR->data);
                    if ($orderS->ret) {
                        if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                            if (!$lineClassTrainR->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                            }

                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                //更新余票
                                $refundTicketR = $this->refundTicket($orderR);
                                if ($refundTicketR->ret) {
                                    $wxPayUtil = new \WxPayUtil();
                                    if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                        $this->transRollback();
                                        return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                                    }
                                    $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                    if (!$refundR->ret) {
                                        $this->transRollback();
                                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                                    }

                                    if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                        $this->transRollback();
                                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                                    }

                                    $refundData['amount'] = $orderR->data['price'];
                                    $refundData['status'] = 3;
                                    $refundData['order_id'] = $order_id;
                                    $refundData['account_type'] = 1;//乘客
                                    $refundData['account_id'] = $orderR->data['passenger_id'];
                                    $refundData['refund_id'] = $refundR->data['refund_id'];
                                    $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                    $refundData['created'] = time();
                                    $refundData['description'] = "取消订单，全额退款";
                                    $refundData['charge'] = $refundR->data['charge'];
                                    $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                    $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                    $reR = $this->add('Refunds', $refundData);
                                    if (!$reR->ret) {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '取消失败'));
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '取消失败'));
                                }
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                            }
                        }

                        $this->commitTrans();
                        //获取乘客相关信息
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                        if (C('WX_TEMPLATE_ON')) {
                            $message = '预约失败';
                            if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                                $message .= ',支付的金额将原路退回';
                            }
                            $temp = array(
                                '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                $orderR->data['order_no'],
                                $message,
                                '请重新选择班线车进行预约！'
                            );
                            $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                            unset($temp);
                        }

                        return $this->output(new \ResultModel(true, '操作成功'));
                    }
                }
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_8) {
                if ($orderR->data['student_order_type']  == \YyCommonDefine::STUDENT_ORDER_TYPE_1) {
                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $orderR->data['state'] = 7;//订单回笼
                        $orderR->data['order_id'] = $order_id;
                        $orderS = $this->save('Order', $orderR->data);
                        if ($orderS->ret) {
                            $this->commitTrans();
                            //获取乘客相关信息
                            $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                            if (C('WX_TEMPLATE_ON')) {
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"拒绝了您的['.$this->getOrderType($orderR->data['type']).']预约！',
                                    $orderR->data['order_no'],
                                    '预约失败',
                                    '请重新选择班线车进行预约！'
                                );
                                $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }

                            return $this->output(new \ResultModel(true, '操作成功'));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '操作失败'));
                        }
                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                        $orderR->data['state'] = 7;//订单回笼
                        $orderR->data['order_id'] = $order_id;
                        $orderS = $this->save('Order', $orderR->data);

                        if ($orderS->ret) {
                            if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                                $yyLineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'YyLineClassTrain');
                                if (!$yyLineClassTrainR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                                }

                                //添加预订订单历史状态
                                $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                                if ($bookHistoryS->ret) {
                                    //更新余票
                                    $remain_tickets = $yyLineClassTrainR->data['remain_tickets'] + $orderR->data['book_seating'];
                                    $yyLineClassTrainS = $this->save('YyLineClassTrain', array('line_class_train_id' => $yyLineClassTrainR->data['line_class_train_id'],'remain_tickets' => $remain_tickets));
                                    if ($yyLineClassTrainS->ret) {
                                        $wxPayUtil = new \WxPayUtil();
                                        if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                            $this->transRollback();
                                            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                                        }
                                        $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                        if (!$refundR->ret) {
                                            $this->transRollback();
                                            return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                                        }

                                        if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                            $this->transRollback();
                                            return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                                        }

                                        $refundData['amount'] = $orderR->data['price'];
                                        $refundData['status'] = 3;
                                        $refundData['order_id'] = $order_id;
                                        $refundData['account_type'] = 1;//乘客
                                        $refundData['account_id'] = $orderR->data['passenger_id'];
                                        $refundData['refund_id'] = $refundR->data['refund_id'];
                                        $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                        $refundData['created'] = time();
                                        $refundData['description'] = "取消订单，全额退款";
                                        $refundData['charge'] = $refundR->data['charge'];
                                        $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                        $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                        $reR = $this->add('Refunds', $refundData);
                                        if (!$reR->ret) {
                                            $this->transRollback();
                                            return $this->output(new \ResultModel(false, '取消失败'));
                                        }
                                    } else {
                                        $this->transRollback();
                                        return $this->output(new \ResultModel(false, '取消失败'));
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                                }
                            }

                            $this->commitTrans();
                            //获取乘客相关信息
                            $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                            if (C('WX_TEMPLATE_ON')) {
                                $message = '预约失败';
                                if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                                    $message .= ',支付的金额将原路退回';
                                }
                                $temp = array(
                                    '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                    $orderR->data['order_no'],
                                    $message,
                                    '请重新选择班线车进行预约！'
                                );
                                $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }

                            return $this->output(new \ResultModel(true, '操作成功'));
                        }
                    }
                }
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_9) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $orderR->data['state'] = 7;//订单回笼
                    $orderR->data['order_id'] = $order_id;
                    $orderS = $this->save('Order', $orderR->data);
                    if ($orderS->ret) {
                        $this->commitTrans();
                        //获取乘客相关信息
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                        if (C('WX_TEMPLATE_ON')) {
                            $temp = array(
                                '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                $orderR->data['order_no'],
                                '预约失败',
                                '请重新选择顺风车进行预约！'
                            );
                            $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                            unset($temp);
                        }

                        return $this->output(new \ResultModel(true, '操作成功'));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                } else {
                    if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                        $orderR->data['state'] = 7;//订单回笼
                        $orderR->data['order_id'] = $order_id;
                        $orderS = $this->save('Order', $orderR->data);
                        if ($orderS->ret) {
                            //添加预订订单历史状态
                            $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                            if ($bookHistoryS->ret) {
                                $wxPayUtil = new \WxPayUtil();
                                if (!$wxPayUtil->init($orderR->data['mchid'])) {
                                    $this->transRollback();
                                    return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                                }
                                $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
                                if (!$refundR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                                }

                                if ($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!' . $refundR->data['err_code_des']));
                                }

                                $refundData['amount'] = $orderR->data['price'];
                                $refundData['status'] = 3;
                                $refundData['order_id'] = $order_id;
                                $refundData['account_type'] = 1;//乘客
                                $refundData['account_id'] = $orderR->data['passenger_id'];
                                $refundData['refund_id'] = $refundR->data['refund_id'];
                                //   $refundData['object'] = $refundR->data['object'];
                                $refundData['refund_no'] = $refundR->data['out_trade_no'];
                                $refundData['created'] = time();
                                //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                                /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                                    $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                                $refundData['description'] = "取消订单，全额退款";
                                $refundData['charge'] = $refundR->data['charge'];
                                $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                                $refundData['transaction_no'] = $refundR->data['transaction_id'];
                                $reR = $this->add('Refunds', $refundData);
                                if ($reR->ret) {
                                    $this->commitTrans();
                                    //获取乘客相关信息
                                    $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger');
                                    if (C('WX_TEMPLATE_ON')) {
                                        $temp = array(
                                            '司机"' . $driverR->data['name'] . '"拒绝了您的[' . $this->getOrderType($orderR->data['type']) . ']预约！',
                                            $orderR->data['order_no'],
                                            '预约失败',
                                            '请重新选择顺风车进行预约！'
                                        );
                                        $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 3, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                        unset($temp);
                                    }
                                    return $this->output(new \ResultModel(true, '操作成功'));
                                } else {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '取消失败'));
                                }
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '取消失败'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                }
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                $orderR->data['state'] = 1;//订单回笼
                $orderR->data['driver_id'] = "";
                $orderR->data['appoint'] = 0;
                $orderR->data['order_id'] = $order_id;
                $orderR->data['train_id'] = 0;
                $orderS = $this->save('Order', $orderR->data);
                if ($orderS->ret) {
                    $this->commitTrans();
                    //分台接收通知
                    $this->sendMessage($order_id, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2);
                    /**
                     * 采用新的消息队列替换
                    //加入订单池
                    if(C('REDIS_ON')) {
                        $redisInstance = \Cache::getInstance('Redis', C('REDIS_ORDER_POOL'));
                        $redisInstance->push(\CommonDefine::ORDER_POOL_TAXI, $order_id);
                    }
                    */
                    /**
                     * 出租车订单派发司机入队列
                     * <AUTHOR> <<EMAIL>>
                     */
                    $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/taxi/orders/{$order_id}/dispatch", 'post');
                    $result = json_decode($response, true);
                    if ($result['status'] == 'error') {
                        return $this->output(new \ResultModel(false, $result['message']));
                    }
                    return $this->output(new \ResultModel(true, '操作成功'));
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            } else {
                $orderR->data['state'] = 1;//订单回笼
                $orderR->data['driver_id'] = "";
                $orderR->data['appoint'] = 0;
                $orderR->data['order_id'] = $order_id;
                $orderR->data['train_id'] = 0;
                $orderS = $this->save('Order', $orderR->data);
                if ($orderS->ret) {
                    $this->commitTrans();
                    //分台接收通知
                    $this->sendMessage($order_id, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2);
                    return $this->output(new \ResultModel(true, '操作成功'));
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            }
        } elseif ($status == 2 || $status == 0) {//司机已接单
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_2) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '重复操作，稍后重试'));
            }
            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $drawTicketR = $this->drawTicket($orderR);
                    if ($drawTicketR->ret) {
                        //班线车司机接单
                        $updateOrderData = array('order_id' => $order_id,'state' => 2, 'driver_id' => $driver_id);
                        $orderS = $this->save('Order', $updateOrderData);
                        # 豫客行 支持接单并将司机信息更新为自己,记录接单日志
                        if ($driver_id != $orderR->data['mchid']) {
                            $this->doAddLog("[代接单]原指派司机ID：".$orderR->data['driver_id']."，代接单司机ID：".$driver_id, "原订单数据：".json_encode($orderR).",订单更新数据：".json_encode($updateOrderData));
                        }
                        if ($orderS->ret) {
                            $this->commitTrans();
                            return $this->output(new \ResultModel(false, '接单成功'));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '接单失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '接单失败'));
                    }
                } else {
                    //班线车司机接单
                    $updateOrderData = array('order_id' => $order_id,'state' => 2, 'driver_id' => $driver_id);
                    $orderS = $this->save('Order', $updateOrderData);
                    # 豫客行 支持接单并将司机信息更新为自己,记录接单日志
                    if ($driver_id != $orderR->data['mchid']) {
                        $this->doAddLog("[代接单]原指派司机ID：".$orderR->data['driver_id']."，代接单司机ID：".$driver_id, "原订单数据：".json_encode($orderR).",订单更新数据：".json_encode($updateOrderData));
                    }
                    if ($orderS->ret) {
                        $this->commitTrans();
                        return $this->output(new \ResultModel(false, '接单成功'));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '接单失败'));
                    }
                }
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_8) {
                if ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_1) {
                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $drawTicketR = $this->yyDrawTicket($orderR->data['line_id'], $orderR->data['book_seating']);
                        if ($drawTicketR->ret) {
                            //班线车司机接单
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 2));
                            if ($orderS->ret) {
                                $this->commitTrans();
                                return $this->output(new \ResultModel(false, '接单成功'));
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '接单失败'));
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '接单失败'));
                        }
                    } else {
                        //班线车司机接单
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 2));
                        if ($orderS->ret) {
                            $this->commitTrans();
                            return $this->output(new \ResultModel(false, '接单成功'));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '接单失败'));
                        }
                    }
                }
            } else {
                $seat_is_add = 0;
                if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1 || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_2 || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_6 || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_7 || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_9 || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                    if ($driverR->data['residual_seating'] <= 0) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '没有剩余座位'));
                    }

                    if ($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '当前剩余座位不足'));
                    }
                    //更新剩余座位数
                    $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                    $seat_is_add = 1;
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                    if (!$driverS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '网络异常，请重试'));
                    }

                    if (C('SEAT_LOG_ON')) {
                        $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'Driver.Order.doUpdateOrder', "[".__LINE__.']小于30分钟直接扣除司机座位数');
                    }
                }
                //                }
                $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 2,'train_id' => $driverR->data['current_train_id'],'seat_is_add' => $seat_is_add,'driver_id' => $driver_id));
                if ($orderS->ret) {
                    //扣除顺风车剩余数量
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                        if (!empty($orderR->data['line_id'])) {
                            if ($orderR->data['is_pay'] != \CommonDefine::PAY_STATUS_1) {
                                $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                                if (!$LineFreeRideR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                                }
                                $LineFreeRideUpdateData['id'] = $LineFreeRideR->data['id'];
                                $LineFreeRideUpdateData['residual_seating'] = $LineFreeRideR->data['residual_seating'] - $orderR->data['book_seating'];
                                $LineFreeRideS = $this->save('LineFreeRide', $LineFreeRideUpdateData);
                                if (!$LineFreeRideS->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '更新顺风车剩余座位数失败'));
                                }
                            }
                        } else {
                            $orderS = $this->save('Order', array('order_id' => $order_id, 'branchid' => $driverR->data['branchid']));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                $bookkeepingBranchOnlyOnTotalTurnoverOnAmount = $this->bookkeepingBranchOnlyOnTotalTurnoverOnAmount($driverR->data['branchid'], $order_id);
                                if (!$bookkeepingBranchOnlyOnTotalTurnoverOnAmount->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单异常'));
                                }
                            }
                        }
                    } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_9) {
                        if (!empty($orderR->data['line_id'])) {
                            $lineParentHelpsR = $this->find(array('id' => $orderR->data['line_id']), 'YyLineParentHelp');
                            if (!$lineParentHelpsR->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '更新家长互助剩余座位数失败'));
                            }
                            $lineParentHelpUpdateData['id'] = $lineParentHelpsR->data['id'];
                            $lineParentHelpUpdateData['residual_seating'] = $lineParentHelpsR->data['residual_seating'] - $orderR->data['book_seating'];
                            $lineParentHelpS = $this->save('YyLineParentHelp', $lineParentHelpUpdateData);
                            if (!$lineParentHelpS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '更新家长互助剩余座位数失败'));
                            }
                        } else {
                            $orderS = $this->save('Order', array('order_id' => $order_id, 'branchid' => $driverR->data['branchid']));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                                $bookkeepingBranchOnlyOnTotalTurnoverOnAmount = $this->bookkeepingBranchOnlyOnTotalTurnoverOnAmount($driverR->data['branchid'], $order_id);
                                if (!$bookkeepingBranchOnlyOnTotalTurnoverOnAmount->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单异常'));
                                }
                            }
                        }
                    }

                    //提交
                    $this->commitTrans();
                    //获取总台相关信息
                    $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
                    $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
                    if (C('SMS_ON')) {
                        if ($orderR->data['rob_order'] == 0) {
                            //派单
                            //分台接收短信
                            if ($branchR->ret) {
                                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                $messageR = $smsUtil->sendTemplateSMS($branchR->data['cellphone'], array($driverR->data['name'], $orderR->data['order_no']), \SMSUtil::TEMP_ID_ACPET_ORDER, $orderR->data['mchid']);
                            }
                        }

                        if ($orderR->data['is_temp'] == 0) {
                            //获取乘客相关信息
                            $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger', 'mchid,cellphone,name,openid');

                            if ($passengerR->data['cellphone'] != $orderR->data['reseverd_phone']) {
                                //车牌号加密
                                $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                                //2025.2.17 新增乘客接收短信
                                $this->sendInnerNotification('passenger', 'sms', 'sms_passenger_new_order_dispatched', $orderR->data['order_id']);
                            }
                        }
                        //车牌号加密
                        $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                        //2025.2.17 新增乘客接收短信
                        $this->sendInnerNotification('passenger', 'sms', 'sms_passenger_new_order_dispatched', $orderR->data['order_id']);
                    }
                    if (C('WX_TEMPLATE_ON')) {
                        if ($orderR->data['rob_order'] == 0) {
                            if ($branchR->ret && !empty($branchR->data['openid'])) {
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"接受了您的['.$this->getOrderType($orderR->data['type']).']派单！',
                                    $orderR->data['order_no'],
                                    '进行中',
                                    '派单完成！'
                                );
                                $this->wechatPushInfo($driverR->data['mchid'], $branchR->data['admin_id'], 3, $branchR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }
                        } elseif ($orderR->data['rob_order'] == 1 && $orderR->data['is_temp'] == 1) {
                            if ($branchR->ret && !empty($branchR->data['openid'])) {
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"抢到了您的['.$this->getOrderType($orderR->data['type']).']订单！',
                                    $orderR->data['order_no'],
                                    '进行中',
                                    '抢单完成！'
                                );
                                $this->wechatPushInfo($driverR->data['mchid'], $branchR->data['admin_id'], 3, $branchR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }
                        }

                        if ($orderR->data['is_temp'] == 0) {
                            //车牌号加密
                            $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                            $temp = array(
                                '司机"'.$driverR->data['name'].'"已经接受您的['.$this->getOrderType($orderR->data['type']).']订单,电话:'.$driverR->data['cellphone'].',车牌号:'.$carTailNumber,
                                $orderR->data['order_no'] ,
                                '进行中',
                                '投诉热线:'.$mchR->data['tel']
                            );
                            $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger', 'passenger_id,mchid,cellphone,name,openid');
                            if (!empty($passengerR->data['openid'])) {
                                $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 1, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }
                        }
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            }

            return $this->output(new \ResultModel(true, '操作成功'));
        } elseif ($status == 3) {
            # 更新订单状态为：乘客已上车
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_4) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '乘客已经上车，无法继续操作'));
            }
            if (($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7
                || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_11)
                    && $orderR->data['is_boarding_point'] == \CommonDefine::IS_BOARDING_POINT_0
            ) {
                if ($force_boarding == false) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '乘客还未到达上车点附近', null, null, ['fast_car' => 'force_boarding_fast_car']));
                }
            }
            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 4));//3=>4 乘客上车直接变为订单直接变为在路上
            $this->commitTrans();
            return $this->output($orderS);
        } elseif ($status == 4) {
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_4) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '重复操作，稍后重试'));
            }
            //在路上
            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 4));
            $this->commitTrans();
            return $this->output($orderS);
        } elseif ($status == 5) {
            if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_5) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '乘客已经送达，无法继续操作'));
            }
            # 乘客已送达
            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    $state = \CommonDefine::ORDER_STATE_5;
                    if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                        $state = \CommonDefine::ORDER_STATE_6;
                        $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                        if (!$bookR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单异常'));
                        }
                    }
                    $orderS = $this->save('Order', array('order_id' => $order_id,'state' => $state));
                    if (!$orderS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单异常'));
                    }
                    $this->commitTrans();
                    //计入用户积分
                    if ($state == \CommonDefine::ORDER_STATE_6) {
                        $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                    }
                    return $this->output(new \ResultModel(true, '操作成功'));
                } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                    $orderS = $this->save('Order', array('order_id' => $order_id,'state' => \CommonDefine::ORDER_STATE_6));
                    if (!$orderS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单异常'));
                    }

                    $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                    if ($bookR->ret) {
                        $this->commitTrans();
                        //计入用户积分
                        $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                        return $this->output(new \ResultModel(true, '操作成功'));
                    }
                }

                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单异常'));
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_8) {
                if ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_1) {
                    if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                        $state = 5;
                        if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                            $state = 6;
                            $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                            if (!$bookR->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                        }
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => $state));
                        if (!$orderS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单异常'));
                        }
                        $this->commitTrans();
                        return $this->output(new \ResultModel(true, '操作成功'));
                    } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                        if (!$orderS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单异常'));
                        }

                        $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                        if ($bookR->ret) {
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, '操作成功'));
                        }
                    }
                } elseif ($orderR->data['student_order_type'] == \YyCommonDefine::STUDENT_ORDER_TYPE_2) {//组团
                    if ($orderR->data['student_order_pay_type'] == \YyCommonDefine::STUDENT_ORDER_PAY_TYPE_1) {//在线支付
                        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                            $state = 5;
                            if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                                $state = 6;
                                $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                if (!$bookR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单异常'));
                                }
                            }
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => $state));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, '操作成功'));
                        } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }

                            $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                            if ($bookR->ret) {
                                $this->commitTrans();
                                return $this->output(new \ResultModel(true, '操作成功'));
                            }
                        }
                    } elseif ($orderR->data['student_order_pay_type'] == \YyCommonDefine::STUDENT_ORDER_PAY_TYPE_2) {//扣卡次数

                    } else {
                        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                            $state = 5;
                            if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                                $state = 6;
                                $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                                if (!$bookR->ret) {
                                    $this->transRollback();
                                    return $this->output(new \ResultModel(false, '订单异常'));
                                }
                            }
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => $state));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, '操作成功'));
                        } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                            if (!$orderS->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }

                            $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                            if ($bookR->ret) {
                                $this->commitTrans();
                                return $this->output(new \ResultModel(true, '操作成功'));
                            }
                        }
                    }
                }

                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单异常'));
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_10) {
                $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                $this->commitTrans();
                return $this->output($orderS);
            } else {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                    if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                        # 订单已经支付
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                        $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                        $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                        if ($orderS->ret && $driverS->ret) {
                            $this->commitTrans();
                            //计入用户积分
                            $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                            if (C('SEAT_LOG_ON')) {
                                $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'Driver.Order.doUpdateOrder', "[".__LINE__.']非预支付，已支付，已送达');
                            }
                            return $this->output($orderS);
                        }
                    } elseif ($orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1) {
                        # 订单已经预支付
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                        if (!$orderS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单异常'));
                        }

                        $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];

                        $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                        if ($driverS->ret) {
                            $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                            if ($bookR->ret) {
                                if (C('SEAT_LOG_ON')) {
                                    $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'Driver.Order.doUpdateOrder', "[".__LINE__.']非预支付，代约支付，已送达');
                                }
                                $this->commitTrans();
                                //计入用户积分
                                $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                return $this->output($orderS);
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                        }
                    } else {
                        # 订单未支付
                        if (($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7
                            || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_11)
                            && $orderR->data['is_alighting_point'] == \CommonDefine::IS_ALIGHTING_POINT_0
                        ) {
                            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7) {
                                # 快车订单根据经纬度实时更新订单价格数据
                                $orderS = $this->reBuildFastAgain($order_id, $orderR->data['line_id'], $orderR->data['split'], $orderR->data['start_longitude'], $longitude, $orderR->data['start_latitude'], $latitude, $orderR->data['book_seating'], $orderR->data['passenger_id']);
                            }
                            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                                # 出租车订单根据经纬度实时更新订单价格数据
                                $orderS = $this->reBuildTaxiAgain($order_id, $orderR->data['line_id'], $orderR->data['split'], $orderR->data['start_longitude'], $longitude, $orderR->data['start_latitude'], $latitude, $orderR->data['book_seating'], $orderR->data['passenger_id']);
                            }
                        } else {
                            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 5));
                        }
                        # 更新司机座位信息
                        $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                        $driverS = $this->resetDriverSeat($residual_seating, $orderR);
                        if ($orderS->ret && $driverS->ret) {
                            $this->commitTrans();
                            if (C('SEAT_LOG_ON')) {
                                # 添加座位日志
                                $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'Driver.Order.doUpdateOrder', "[".__LINE__.']非预支付，未支付，已送达');
                            }
                            return $this->output($orderS);
                        }
                    }
                } elseif ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                    if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                        if (!$orderS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单异常'));
                        }

                        $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                        $driverS = $this->resetDriverSeat($residual_seating, $orderR);
                        if ($driverS->ret) {
                            $bookR = $this->bookkeepingMchOnBalance($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                            if ($bookR->ret) {
                                if (C('SEAT_LOG_ON')) {
                                    $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doUpdateOrder', '预支付，已送达');
                                }
                                $this->commitTrans();
                                //计入用户积分
                                $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                                return $this->output($orderS);
                            } else {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '订单异常'));
                            }
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单异常'));
                    }
                }
            }

            $this->transRollback();
            return $this->output(new \ResultModel(false, '订单异常'));
        } elseif ($status == 6) {
            //乘客已线下支付
            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '订单支付模式错误'));
            }

            $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
            if ($orderS->ret) {
                $bookR = $this->bookkeepingDownAll($orderR->data['mchid'], $orderR->data['branchid'], $driverR->data['driver_id'], $orderR->data['order_id']);
                if ($bookR->ret) {
                    $this->commitTrans();
                    //计入用户积分
                    $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
                    # 2023.07.28完成订单，通知乘客
                    $this->sendInnerNotification('passenger', 'wechat', 4, $orderR->data['order_id']);
                    return $this->output(new \ResultModel(true, '操作成功'));
                }
            } else {
                $this->transRollback();
                return $this->output($orderR);
            }
        } elseif ($status == 7) {
            # 司机取消订单
            $remark = is_null($reason) ? sprintf('司机(%s)取消了订单', $driverR->data['cellphone']) : $reason;
            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7, 'reseverd_info' => $remark));
            $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
            $driverS = $this->resetDriverSeat($residual_seating, $orderR);
            $this->commitTrans();
            return $this->output($orderS);
        }

        $this->transRollback();
        return $this->output(new \ResultModel(false, '操作失败'));
    }

    /**
     * 重置座位信息
     *
     * @param integer $residual_seating
     * @param object $orderR
     * @return object
     */
    protected function resetDriverSeat($residual_seating, $orderR)
    {
        return $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'residual_seating' => $residual_seating));
    }

    /**
     * 重新计算订单起始距离与价格，并更新订单价格
     *
     * @param integer $orderId
     * @param integer $taxtLineId
     * @param float $split
     * @param string $startLongitude
     * @param string $endLongitude
     * @param string $startLatitude
     * @param string $endLatitude
     * @return object
     */
    protected function reBuildFastAgain($orderId, $taxtLineId, $split, $startLongitude, $endLongitude, $startLatitude, $endLatitude, $book_seating, $passenger_id)
    {
        $header = [
            'passenger: ' . $passenger_id,
            'Accept: application/json'
        ];
        $params = array(
            'start_longitude' => trim($startLongitude),
            'start_latitude' => trim($startLatitude),
            'book_seating' => $book_seating,
            'end_longitude' => trim($endLongitude),
            'end_latitude' => trim($endLatitude),
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/kuaiche_fee", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            # 重新计算出价格
            $splitAmount = bcmul($results['data']['price'], $split, 2);
            $offerAmount = bcsub($results['data']['price'], $splitAmount, 2);
            # 更新订单信息
            $orderS = $this->save(
                'Order',
                array(
                    'order_id' => $orderId,
                    'price' => $results['data']['price'],
                    'real_price' => $results['data']['price'],
                    'offer_price' => $offerAmount,
                    'kilometre' => $results['data']['rules']['distance'],
                    'state' => \CommonDefine::ORDER_STATE_5
                    )
            );
            $this->createOrderFastFee($orderId, $results['data']['rules'], $taxtLineId);
            return $orderS;
        } else {
            return $this->output(new \ResultModel(false, '点击送达重新计算地图距离和价格失败：'. $results['message']));
        }
    }

    /**
     * 更新快车订单费用信息
     *
     * @param integer $orderId
     * @param array $priceR
     * @param integer $fastLineId
     *
     * @return void
     */
    protected function createOrderFastFee($orderId, $priceR, $fastLineId)
    {
        $orderFastTaxiData['order_id'] = $orderId;
        $orderFastTaxiData['base_price'] = $priceR['base_price'];
        $orderFastTaxiData['total_mileage'] = $priceR['distance'];
        $orderFastTaxiData['total_mileage_price'] = $priceR['mileage_price'];
        $orderFastTaxiData['total_duration'] = $priceR['duration'];
        $orderFastTaxiData['total_duration_price'] = $priceR['duration_price'];
        $orderFastTaxiData['total_longdistance'] = $priceR['longdistance'];
        $orderFastTaxiData['total_longdistance_price'] = $priceR['longdistance_price'];
        $orderFastTaxiData['total_wait_timeout'] = $priceR['wait_timeout'];
        $orderFastTaxiData['total_wait_timeout_price'] = $priceR['wait_timeout_price'];
        # 快车订单费用信息
        $this->add(
            'OrderFastFee',
            $orderFastTaxiData
        );
    }


    /**
     * 重新计算订单起始距离与价格，并更新订单价格
     *
     * @param integer $orderId
     * @param integer $taxtLineId
     * @param float $split
     * @param string $startLongitude
     * @param string $endLongitude
     * @param string $startLatitude
     * @param string $endLatitude
     * @return object
     */
    protected function reBuildTaxiAgain($orderId, $taxtLineId, $split, $startLongitude, $endLongitude, $startLatitude, $endLatitude, $book_seating, $passenger_id)
    {
        $header = [
            'passenger: ' . $passenger_id,
            'Accept: application/json'
        ];
        $params = array(
            'start_longitude' => trim($startLongitude),
            'start_latitude' => trim($startLatitude),
            'book_seating' => $book_seating,
            'end_longitude' => trim($endLongitude),
            'end_latitude' => trim($endLatitude),
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/taxi_fee", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            # 重新计算出价格
            $splitAmount = bcmul($results['data']['price'], $split, 2);
            $offerAmount = bcsub($results['data']['price'], $splitAmount, 2);
            # 更新订单信息
            $orderS = $this->save(
                'Order',
                array(
                    'order_id' => $orderId,
                    'price' => $results['data']['price'],
                    'real_price' => $results['data']['price'],
                    'offer_price' => $offerAmount,
                    'kilometre' => $results['data']['rules']['distance'],
                    'state' => \CommonDefine::ORDER_STATE_5
                    )
            );
            $this->createOrderTaxiFee($orderId, $results['data']['rules'], $taxtLineId);
            return $orderS;
        } else {
            return $this->output(new \ResultModel(false, '点击送达重新计算地图距离和价格失败：'. $results['message']));
        }
    }

    /**
     * 更新快车订单费用信息
     *
     * @param integer $orderId
     * @param array $priceR
     * @param integer $fastLineId
     *
     * @return void
     */
    protected function createOrderTaxiFee($orderId, $priceR, $fastLineId)
    {
        $orderFastTaxiData['order_id'] = $orderId;
        $orderFastTaxiData['base_price'] = $priceR['base_price'];
        $orderFastTaxiData['total_mileage'] = $priceR['distance'];
        $orderFastTaxiData['total_mileage_price'] = $priceR['mileage_price'];
        $orderFastTaxiData['total_duration'] = $priceR['duration'];
        $orderFastTaxiData['total_duration_price'] = $priceR['duration_price'];
        $orderFastTaxiData['total_longdistance'] = $priceR['longdistance'];
        $orderFastTaxiData['total_longdistance_price'] = $priceR['longdistance_price'];
        $orderFastTaxiData['total_wait_timeout'] = $priceR['wait_timeout'];
        $orderFastTaxiData['total_wait_timeout_price'] = $priceR['wait_timeout_price'];
        # 快车订单费用信息
        $this->add(
            'OrderTaxiFee',
            $orderFastTaxiData
        );
    }

    /**
     * 司机更新附加费用
     * @param int $order_id 订单编号
     * @param int $extra_price 附加金额
     */
    public function doUpdateExtraPrice($order_id, $extra_price)
    {
        if (!is_numeric($extra_price)) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        if ($extra_price < 0) {
            return $this->output(new \ResultModel(false, '金额不能小于0'));
        }

        $where['state'] = ['in', [\CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4]];
        $where['type'] = \CommonDefine::ORDER_TYPE_7;
        $orderR = $this->find(['order_id' => $order_id], 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }

        $orderData['real_price'] = $orderR->data['real_price'] + $extra_price;
        $lastPrice = $orderR->data['price'] + $extra_price;
        $orderData['order_id'] = $order_id;
        $orderData['extra_price'] = $extra_price;
        $mch = M('AnnualFee')->where(array('mchid' => $orderR->data['mchid']))->find();
        $orderData['price'] = $lastPrice;
        $orderData['offer_price'] = $orderData['price'] - $orderData['price'] * $mch['split'] / 100;
        $orderData['split'] = $mch['split'] / 100;
        return $this->output($this->save('Order', $orderData));
    }

    /**
     * 司机更新订单价格
     * @param int $order_id 订单编号
     * @param int $price 订单金额
     */
    public function doUpdateOrderPrice($order_id, $price)
    {
        if (!is_numeric($price)) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        if ($price <= 0) {
            return $this->output(new \ResultModel(false, '金额不能小于等于0'));
        }

        $where['state'] = ['in', [\CommonDefine::ORDER_STATE_3, \CommonDefine::ORDER_STATE_4, \CommonDefine::ORDER_STATE_5]];
        $where['pay_mode'] = \CommonDefine::ORDER_PAYMENT_MODE_0;
        $orderR = $this->find(['order_id' => $order_id], 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        switch ($orderR->data['type']) {
            case \CommonDefine::ORDER_TYPE_1:
            case \CommonDefine::ORDER_TYPE_2:
            case \CommonDefine::ORDER_TYPE_3:
            case \CommonDefine::ORDER_TYPE_4:{
            }
            case \CommonDefine::ORDER_TYPE_5:{
                break;
            }
            case \CommonDefine::ORDER_TYPE_7:{
                if ($orderR->data['state'] < \CommonDefine::ORDER_STATE_5) {
                    return $this->output(new \ResultModel(false, '未送达前无法修改价格'));
                }
            }
            default:{
                return $this->output(new \ResultModel(false, '操作失败'));
            }
        }

        if (!empty($orderR->data['coupon_record_id'])) {
            return $this->output(new \ResultModel(false, '该订单包含优惠券，无法修改价格'));
        }

        if ($orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1 || $orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1) {
            return $this->output(new \ResultModel(false, '已支付订单不能修改'));
        }

        $orderNo = $orderR->data['order_no'];
        $orderArr = explode('_', $orderR->data['order_no']);
        if (count($orderArr) == 2) {
            $orderNo = $orderArr[0]."_".(intval($orderArr[1]) + 1);
        } else {
            $orderNo = $orderR->data['order_no']."_1";
        }

        $orderData['price'] = $price;
        $orderData['real_price'] = $price;
        $orderData['order_id'] = $order_id;
        $orderData['order_no'] = $orderNo;
        $mch = M('AnnualFee')->where(array('mchid' => $orderR->data['mchid']))->find();
        $orderData['offer_price'] = $orderData['price'] - $orderData['price'] * $mch['split'] / 100;
        $orderData['split'] = $mch['split'] / 100;
        $orderS = $this->save('Order', $orderData);
        if ($orderS->ret) {
            $this->add('Log', array('Operation_content' => 'doUpdateOrderPrice：修改乘客支付的金额（即原价）', 'operation_time' => date('Y-m-d H:i:s', time()), 'admin_id' => $this->mchid, 'data' => json_encode($orderR->data)));
        }
        return $this->output($orderS);
    }

    /**
     * 司机接收乘客发布的顺风车订单
     * @param int $order_id 订单编号
     * @param int $longitude 司机当前位置经度
     * @param int $latitude 司机当前位置纬度
     */
    public function doUpdatePassengerReleaseFreeRideOrder($order_id, $longitude = null, $latitude = null)
    {
        $driver_id = $this->state->user_id;
        if ($longitude && $latitude) {
            $this->save('Driver', array('driver_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        }

        if (!$order_id) {
            return $this->output(new \ResultModel(false, '订单参数异常'));
        }

        $this->startTrans();
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');

        $orderWhere = 'mchid='.$driverR->data['mchid'];
        $orderWhere .= ' AND order_id='.$order_id;
        $r = $this->find($orderWhere, 'Order', 'driver_id,virtual_driver,order_id,type,passenger_id,state,passenger_isover,order_no,create_time,book_seating,mchid,branchid,appoint,is_temp,rob_order');
        if (!$r->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '网络异常'));
        }

        if ($r->data['state'] == 2 || $r->data['state'] == 3 || $r->data['state'] == 4 || $r->data['state'] == 5) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '来晚一步，该订单已被抢,及时刷新页面以获取最新数据'));
        } elseif ($r->data['state'] == 7) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该订单用户已取消,及时刷新页面以获取最新数据'));
        } elseif ($r->data['state'] == 6) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该订单已完成,及时刷新页面以获取最新数据'));
        }

        //司机确认同行
        if ($driverR->ret) {
            $orderR = $this->find(array('order_id' => $order_id), 'Order');
            $seat_is_add = 0;
            //小于30分钟直接进入到进行中订单，大于30分钟进入到待出发行程
            //            if(strtotime($orderR->data['start_time']) < time()+C('TIMER_ORDER_LIMIT')) {
            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                if ($driverR->data['residual_seating'] <= 0) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '没有剩余座位'));
                }

                if ($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '剩余座位不足'));
                }
                //更新剩余座位数
                $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                $seat_is_add = 1;
                $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                if (!$driverS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '网络异常，请重试'));
                }

                if (C('SEAT_LOG_ON')) {
                    $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'doUpdateOrder', '小于30分钟直接扣除司机座位数');
                }
                //                }
            }
            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                $bookkeepingBranchOnlyOnTotalTurnoverOnAmount = $this->bookkeepingBranchOnlyOnTotalTurnoverOnAmount($driverR->data['branchid'], $order_id);
                if (!$bookkeepingBranchOnlyOnTotalTurnoverOnAmount->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单异常'));
                }
            }

            $orderS = $this->save('Order', array('order_id' => $order_id,'branchid' => $driverR->data['branchid'], 'state' => 2,'train_id' => $driverR->data['current_train_id'],'seat_is_add' => $seat_is_add,'driver_id' => $driver_id));
            if ($orderS->ret) {
                //提交
                $this->commitTrans();
                //获取总台相关信息
                $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
                if (C('SMS_ON')) {
                    if ($orderR->data['is_temp'] == 0) {
                        //获取乘客相关信息
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger', 'mchid,cellphone,name,openid');

                        //车牌号加密
                        $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                        //2025.2.17 新增乘客接收短信
                        $this->sendInnerNotification('passenger', 'sms', 'sms_passenger_new_order_dispatched', $orderR->data['order_id']);
                    }
                }
                if (C('WX_TEMPLATE_ON')) {
                    if ($orderR->data['is_temp'] == 0) {
                        //车牌号加密
                        $carTailNumber = $this->encryptionCarNumByCarNum($driverR->data['car_tail_number']);
                        //司机{1}接受了您的订单，电话：{2},车牌号{3},投诉热线{4}
                        //司机姓名：张三
                        //司机电话：13811111111
                        //订单时间：2014年7月21日 18:36
                        //感谢你的使用
                        $temp = array(
                            '司机"'.$driverR->data['name'].'"已经接受您的['.$this->getOrderType($r->data['type']).']订单,电话:'.$driverR->data['cellphone'].',车牌号:'.$carTailNumber,
                            $r->data['order_no'] ,
                            '进行中',
                            '投诉热线:'.$mchR->data['tel']
                        );
                        $passengerR = $this->find(array('passenger_id' => $orderR->data['passenger_id']), 'Passenger', 'passenger_id,mchid,cellphone,name,openid');
                        if (!empty($passengerR->data['openid'])) {
                            $this->wechatPushInfo($passengerR->data['mchid'], $passengerR->data['passenger_id'], 1, $passengerR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                            unset($temp);
                        }
                    }
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '操作失败'));
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        return $this->output(new \ResultModel(true, '操作成功'));
    }

    /**
     * 班线司机全部送达
     * @param int $order_ids 订单集合
     * @param int $status 0-接受，1-拒绝,2-确认到达,3-确认已上车
     */
    public function doUpdateClassOrdersOver($order_ids, $status = 0, $longitude = null, $latitude = null)
    {
        $driver_id = $this->state->user_id;
        $orderIdsArr = explode(',', $order_ids);
        if (!is_array($orderIdsArr) || empty($orderIdsArr)) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }

        $this->startTrans();
        if ($longitude && $latitude) {
            $this->save('Driver', array('driver_id' => $this->state->user_id, 'longitude' => $longitude, 'latitude' => $latitude));
        }

        if ($status == 5) {
            //已送达,更新乘客位置
            foreach ($orderIdsArr as $k => $order_id) {
                if (!$order_id) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单参数异常'));
                }
                $orderR = $this->find(array('order_id' => $order_id, 'is_pay' => \CommonDefine::PAY_STATUS_1), 'Order');
                if ($orderR->ret) {
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5 && $orderR->data['state'] <= 4) {
                        $orderS = $this->save('Order', array('order_id' => $order_id,'state' => 6));
                        if (!$orderS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '操作失败'));
                        }
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        $this->commitTrans();
        return $this->output(new \ResultModel(false, '操作成功'));
    }

    /**
     * 获取司机订单统计信息
     */
    public function doGetOrderStatistics()
    {
        $driver_id = $this->state->user_id;
        $retData = array(
            'appointOrdersCount' => self::getAppointOrdersCount($driver_id),
            'onOrdersCount' => self::getOnOrdersCount($driver_id),
            'waitOrdersCount' => 0,//self::getWaitOrdersCount($driver_id),
            'overOrdersCount' => 0,//self::getOverOrdersCount($driver_id),
        );

        $ret = new \ResultModel(true, $retData, count($retData));
        return $this->output($ret);
    }

    //获取待出发订单数量
    private function getWaitOrdersCount($driver_id)
    {
        $count = 0;
        if (empty($driver_id)) {
            return $count;
        }

        return $count;
        $timeLimit = date('Y-m-d H:i:s', C('TIMER_ORDER_LIMIT') + time());

        $where = "state = 2 and driver_id= $driver_id and  start_time > '".$timeLimit."' and type != ".\CommonDefine::ORDER_TYPE_5;

        $r = $this->count($where, 'Order');

        if ($r->ret) {
            $count = intval($r->data);
        }
        return $count;
    }

    /**
     * 进行中订单数量
     *
     * @param integer $driver_id
     * @return integer
     */
    private function getOnOrdersCount($driver_id)
    {
        $count = 0;
        # 订单时间限制
        $timeLimit = date('Y-m-d H:i:s', C('TIMER_ORDER_LIMIT') + time());
        if ($timeLimit) {
            $where = sprintf(
                " `state` IN (2, 3, 4, 5)
                    AND `driver_id` = %d
                    AND ((`type` != %d
                            AND `start_time` <= '%s')
                        OR `type` = %d)",
                $driver_id,
                \CommonDefine::ORDER_TYPE_5,
                $timeLimit,
                \CommonDefine::ORDER_TYPE_5
            );
        } else {
            $where = sprintf(
                "state IN (2, 3, 4, 5)
                    AND driver_id = %d",
                $driver_id
            );
        }
        $r = $this->count($where, 'Order');
        if ($r->ret) {
            $count = intval($r->data);
        }
        return $count;
    }

    //获取待接收订单列表
    private function getAppointOrdersCount($driver_id)
    {
        $count = 0;
        if (empty($driver_id)) {
            return $count;
        }
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        $where = 'o.state in(1) ';
        $where .= ' AND ((o.appoint = 1 AND o.rob_order = 0 AND o.driver_id = '.$driver_id.') OR (o.rob_order = 1 AND o.appoint = 0 AND d.branchid='.$driverR->data['branchid'].' AND d.driver_id = '.$driver_id.'))';
        $where .= ' AND ((o.pay_mode = '.\CommonDefine::ORDER_PAYMENT_MODE_0.") OR (o.pay_mode = ".\CommonDefine::ORDER_PAYMENT_MODE_1. " AND o.is_pay =".\CommonDefine::PAY_STATUS_1.")) ";
        $order = 'o.create_time desc';
        $field = 'o.order_no,o.passenger_id,o.order_id,o.state,o.type,o.offer_price as price,o.book_seating,o.line_id,o.start_time,o.reseverd_phone,o.reseverd_person,o.reseverd_info,o.branchid,o.driver_id,o.create_time,o.is_pre_pay,o.is_temp,o.start_latitude,o.start_longitude,o.start_address_remark,o.end_latitude,o.end_longitude,o.end_address_remark,o.is_custom,o.car_type_id,o.delivery_person,o.delivery_phone,o.weight,o.agency_id,o.rob_order';
        $orderCount = M()->table('cp_order o')
            ->join("LEFT JOIN cp_driver d ON o.branchid = d.branchid")
            ->where($where)
            ->field($field)
            ->order($order)
            ->count('distinct(o.order_id)');
        return $orderCount ? $orderCount : 0;
    }

    //获取所有已完成订单列表
    private function getOverOrdersCount($driver_id)
    {
        $where['state'] = array('in', '6');
        $where['appoint'] = 1;
        $where['driver_id'] = $driver_id;
        $r = $this->count($where, 'Order');
        if ($r->ret) {
            $count = intval($r->data);
        }
        return $count;
    }
}

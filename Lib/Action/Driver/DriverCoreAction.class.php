<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Home/HomeCoreAction');

/**
 * 车主
 *
 * <AUTHOR>
 */
class DriverCoreAction extends HomeCoreAction
{
    public function __construct()
    {
        add_tag_behavior('auth_check', 'DriverAuthCheck');

        parent::__construct();
    }

    /**
     * 分配提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $passenger_id 乘客ID
     * @param string $action_code 操作标码
     * @param int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignRemindTask($assigner_id, $passenger_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaPassengerRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'passenger_id' => $passenger_id), 'PassengerRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('PassengerRemindTask', array('assigner_id' => $assigner_id, 'passenger_id' => $passenger_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该乘客没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '乘客提醒操作标码不存在');
        }
        return $this->result;
    }


    /**
     * 检测商户司机开关是否开启
     * @return int 0-关闭；1-开启
     */
    protected function checkDriverRegisterConfigIsOnByMchid($mchid)
    {
        $smsConfig = \CommonDefine::DRIVER_REGISTER_CONFIG_0;//默认关闭
        $systemConfigR = $this->find(array('account_id' => $mchid, 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::DRIVER_REGISTER_CONFIG), 'SystemConfig');
        if($systemConfigR->ret) {
            $smsConfig = $systemConfigR->data['value'];
        }
        return $smsConfig;
    }

    /**
     * 获取司机保存在cookie或者session中的数据
     * @return ResultModel
     */
    protected function getDriverStateData($driver_id)
    {
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,nick_name,is_freeze,status,name,cellphone,openid');
        return $driverR;
    }

    /**
     * 获取本月司机订单总流水
     */
    protected function getDriverCurrentMonthTurnover($driverId)
    {
        $ret = new ResultModel(false);
        if(empty($driverId)) {
            return $ret;
        }
        //本月
        $month_start = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), 1, date("Y")));
        $month_end = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m"), date("t"), date("Y")));
        $turnoverConditionString = " driver_id = ".$driverId;
        $turnoverConditionString .= " AND create_time>='".$month_start."' AND create_time<='".$month_end."' ";
        $turnoverConditionString .= " AND state = ".\CommonDefine::ORDER_STATE_6;
        $turnData = M()->table('cp_order o')
            ->where($turnoverConditionString)
            ->field('sum(price) as turnover')
            ->order('o.create_time desc')
            ->select();
        if(is_array($turnData)) {
            $ret->ret = true;
            $ret->data = empty($turnData[0]['turnover']) ? 0 : $turnData[0]['turnover'];
            $ret->count = 1;
        }
        return $ret;
    }

    /**
     * 获取本月司机订单总数
     */
    protected function getDriverCurrentMonthOrderCount($driverId)
    {
        if(empty($driverId)) {
            return new ResultModel(false);
        }
        //本月
        $month_start = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), 1, date("Y")));
        $month_end = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m"), date("t"), date("Y")));
        $where['update_time'] = array('between', array($month_start,$month_end));
        $where['state'] = array('eq', \CommonDefine::ORDER_STATE_6);
        $where['driver_id'] = array('eq', $driverId);
        $ret =  $this->count($where, 'Order');
        return $ret;
    }
}

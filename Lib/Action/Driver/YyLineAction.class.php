<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 云裕众-司机发布互助线路
 *
 * <AUTHOR>
 */
class YyLineAction extends \DriverCoreAction
{
    /**
     * 获取司机已发布的家长互助线路
     * @param int $page 页码
     * @param int $size 页大小
     * @param string $stime 查询开始时间
     * @param string $etime 查询开始时间
     */
    public function getLineParentHelpList($page = 1, $size = 10, $stime = null, $etime = null)
    {
        $driver_id = $this->state->user_id;
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if(!$driverR->ret){
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }
        $where['driver_id'] = $driver_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $orderBy = 'create_time desc';
        if($stime){
            $where['create_time'] = array('egt', $stime);
        }
        if($etime){
            $where['create_time'] = array('elt', $etime);
        }

        $fields = 'line_parent_help_id,line_parent_help_no as lphn,driver_id,start_time,residual_seating,price,summary,start_name,start_address_code,start_address_remark,end_name,end_address_code,end_address_remark';
        $lineParentHelpsR = $this->select($where, $page, $size, $orderBy, 'YyLineParentHelp',$fields);
        if($lineParentHelpsR->ret){
            foreach($lineParentHelpsR->data as $k=>$v){
                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver');
                if($driverR->ret){
                    $lineParentHelpsR->data[$k]['car_brand'] = $driverR->data['car_brand'];
                }
                unset($lineParentHelpsR->data[$k]['driver_id']);
            }
        }
        return $this->output($lineParentHelpsR);
    }

    /**
     * 发布家长互助线路
     * @param date $start_time 出发时间
     * @param int $residual_seating 剩余座位数
     * @param string $price 价格/人
     * @param string $start_longitude 开始经度
     * @param string $start_latitude  开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $summary 备注信息
     */
    public function doReleaseParentHelpLine($start_time, $residual_seating = 1 , $price, $start_longitude = null, $start_latitude = null, $end_longitude=null, $end_latitude = null,$start_address_code, $start_address_remark=null, $end_address_code, $end_address_remark=null, $summary = null){
        if(strtotime($start_time) < time()){
            return $this->output(new \ResultModel(false, '出发时间不能小于当前时间'));
        }

        $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
        if(!$driverR->ret){
            return $this->output($driverR);
        }

        if($driverR->data['residual_seating'] < $residual_seating){
            return $this->output(new \ResultModel(false, '发布的座位数大于您当前的实际剩余座位数'));
        }

        if(!is_numeric($price) &&  $price<= 0){
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }
/*        $startAreaData = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        $endAreaData = $this->getGdAddressCodeByGdApi($end_longitude, $end_latitude);
        if($startAreaData['status'] == 0 && $endAreaData['status'] == 0){
            return $this->output(new \ResultModel(false, '定位异常，请稍后重试'));
        }*/

        $startCodeArr = $this->getProvinceCityAreaCodeByCode($start_address_code);
        $startNameR = $this->getParentAdministrativeDivisionNameByCode($start_address_code);
        $endCodeArr = $this->getProvinceCityAreaCodeByCode($end_address_code);
        $endNameR = $this->getParentAdministrativeDivisionNameByCode($end_address_code);
        if(!$startNameR->ret || !$endNameR->ret){
            return $this->output(new \ResultModel(false, '定位异常，请稍后重试'));
        }
        $this->data['line_parent_help_no'] = $this->createLineParentHelpNo();
        $this->data['start_name'] = $startNameR->data['name'];
        $this->data['end_name'] = $endNameR->data['name'];
        $this->data['mchid'] = $driverR->data['mchid'];
        $this->data['branchid'] = $driverR->data['branchid'];
        $this->data['start_province_code'] = $startCodeArr['province_code'];
        $this->data['start_city_code'] = $startCodeArr['city_code'];
        $this->data['start_area_code'] = $startCodeArr['area_code'];
        $this->data['end_province_code'] = $endCodeArr['province_code'];
        $this->data['end_city_code'] = $endCodeArr['city_code'];
        $this->data['end_area_code'] = $endCodeArr['area_code'];
        $this->data['driver_id'] = $driverR->data['driver_id'];

        $lineParentHelpA = $this->add('YyLineParentHelp');
        if($lineParentHelpA->ret){
            $lineParentHelpA->data = array('lphn' => $this->data['line_parent_help_no']);
        }
        return $this->output($lineParentHelpA);
    }

    /**
     * 司机删除已发布的家长互助线路
     * @param int $line_parent_help_id 家长互助线路id
     */
    public function doDelParentHelpLine($line_parent_help_id){
        $driver_id = $this->state->user_id;
        $updateData['line_parent_help_id'] = $line_parent_help_id;
        $updateData['driver_id'] = $driver_id;
        $updateData['is_del'] = \CommonDefine::IS_DEL_1;
        $lineParentHelpR = $this->save('YyLineParentHelp', $updateData);
        return $this->output($lineParentHelpR);
    }

    /**
     * 学生号-组团包车（接单、拒单）
     * @param int $student_customized_line_id 包车线路id
     * @param $status 状态 1-拒单；2-接单
     */
    public function doUpdateStudentCustomizedLine($student_customized_line_id, $status){
        $lineWhere['student_customized_line_id'] = $student_customized_line_id;
        $lineWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3;
        $lineWhere['mchid'] = $this->mchid;
        $lineWhere['driver_id'] = $this->state->user_id;
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if(!$studentCustomizedLineR->ret){
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        $driverR = $this->find(array('driver_id' => $this->state->user_id), 'Driver');
        if(!$driverR->ret){
            return $this->output(new \ResultModel(false, '司机账号异常'));
        }

        $updateStudentCustomizedLineData['student_customized_line_id'] = $student_customized_line_id;
        if ($status == 2) {
            $updateStudentCustomizedLineData['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_4;
            $updateStudentCustomizedLineS = $this->save('YyStudentCustomizedLine', $updateStudentCustomizedLineData);
            if(!$updateStudentCustomizedLineS->ret){
                return $this->output(new \ResultModel(false, '接受失败'));
            }
            //通知乘客
            $this->yySendSms($student_customized_line_id, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            //分台接收通知
            $this->yySendWxMessage($student_customized_line_id, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_1);
        }else if($status == 1){
            $updateStudentCustomizedLineData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
            $updateStudentCustomizedLineData['driver_id'] = '';
            $updateStudentCustomizedLineS = $this->save('YyStudentCustomizedLine', $updateStudentCustomizedLineData);
            if(!$updateStudentCustomizedLineS->ret){
                return $this->output(new \ResultModel(false, '拒绝失败'));
            }
            //分台接收通知
            $this->yySendWxMessage($student_customized_line_id, \CommonDefine::USER_TYPE_3, \CommonDefine::MESSAGE_TO_BRANCH_TYPE_2);
        }

        return $this->output(new \ResultModel(true, '操作成功'));
    }

    /**
     * 定制包车-获取指派给我的线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     * @return null|\resultModel
     */
    public function getStudentCustomizedLines($page = 1, $size = 10){
        $where['mchid'] = $this->mchid;
        $where['status'] = ['in', [\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3,\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_4,\YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_5]];
        $where['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $where['driver_id'] = $this->state->user_id;
        $studentCutomizedLines = $this->select($where, $page, $size, 'start_time asc', 'YyStudentCustomizedLine');
        return $this->output($studentCutomizedLines);
    }
}

?>

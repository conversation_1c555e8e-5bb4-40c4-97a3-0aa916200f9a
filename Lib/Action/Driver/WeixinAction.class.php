<?php

/**
 * 微信模块
 * User: zeus
 * Date: 2017/12/10
 * Time: 17:47
 */
import('@/Action/Weixin/WechatAction');

class  WeixinAction extends DriverCoreAction
{
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * 获取jssdk 相关配置
     * @param string $_url 当前网页的URL，不包含#及其后面部分；URL键值对的格式（即key1=value1&key2=value2…）注意的是所有参数名均为小写字符
     * @return ResultModel
     */
    public function getJsSdkConfig($_url){
        $ret = new \ResultModel(false);

        $wx = new \WechatAction();
        $jsapiConfig = $wx->get_jsapi_config($this->mchid, $_url);
        if(!empty($jsapiConfig)){
            $ret->ret = true;
            $ret->data = $jsapiConfig;
            $ret->count = 1;
        }
        return $this->output($ret);
    }

}
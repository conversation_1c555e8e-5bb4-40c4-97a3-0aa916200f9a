<?php

namespace Driver;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Passenger/OrderAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Weixin/WechatAction');
/**
 * 订单模块
 *
 * <AUTHOR>
 */
class YyOrderAction extends \DriverCoreAction
{

    /**
     * 购买发布家长互助服务
     * @param integer $parent_help_price_id 家长互助信息服务费id
     */
    public function doOrderParentHelpPrice($parent_help_price_id){
        $where['parent_help_price_id'] = $parent_help_price_id;
        $where['mchid'] = $this->mchid;
        $parentHelpPriceR = $this->find($where, 'YyParentHelpPrice');
        if($parentHelpPriceR->ret){
            $orderData['parent_help_price_id'] = $parent_help_price_id;
            $orderData['driver_id'] = $this->state->user_id;
            $orderData['order_parent_help_price_no'] = $this->createOrderParentHelpPriceNo();
            $orderData['price'] = $parentHelpPriceR->data['price'];
            $orderData['mchid'] = $this->mchid;
            $orderA = $this->add('YyOrderParentHelpPrice', $orderData);
            if($orderA->ret){
                return $this->output(new \ResultModel(true, ['order_parent_help_price_no' => $orderData['order_parent_help_price_no'], 'price' => $orderData['price']]));
            }
        }
        return $this->output(new \ResultModel(false, '下单失败'));
    }

}

?>
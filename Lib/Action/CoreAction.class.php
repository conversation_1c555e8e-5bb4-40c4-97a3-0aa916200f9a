<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/TagLib/TagLibEx');
import('@/Common/saetv2');
import('@/Common/renren');
import('@/Common/mailer');
import('@/Common/ImageProcess');
import('@/Common/SMSUtil');
import('@/Common/PayUtil');
import('@/Common/WxPayUtil');
import('@/Common/QrCodeUtil');
import('@/Common/PhpExcelUtil');
import('@/Common/EncryptionUtil');
import('@/Common/Wechat/WechatTpl');
import('@/Common/PointCalculation');
import('@/Action/Weixin/WechatAction');
/**
 * 核心模块
 *
 * <AUTHOR>
 */
class CoreAction extends Action
{
    const WECHAT_MSG_TYPE_1 = 1;  //订单更新通知
    const WECHAT_MSG_TYPE_2 = 2; //订单指派通知
    const WECHAT_MSG_TYPE_3 = 3; //订单取消通知
    const WECHAT_MSG_TYPE_4 = 4; //订单完成通知
    const WECHAT_MSG_TYPE_5 = 5; //新订单提醒
    const WECHAT_MSG_TYPE_6 = 6; //预约成功通知
    const WECHAT_MSG_TYPE_7 = 7; //派单接受通知
    const WECHAT_MSG_TYPE_8 = 8; //下单成功通知
    const WECHAT_MSG_TYPE_9 = 9; //新订单通知


    protected $directlyCityArr = array(
        '北京市' => '110000',
        '天津市' => '120000',
        '上海市' => '310000',
        '重庆市' => '500000',
    );

    //put your code here
    /*
     * 数据
     */
    public $data = array();

    /**
     * 是否是get操作
     * @var boolean
     */
    public $isGet = false;

    /**
     * 是否是query操作
     * @var boolean
     */
    public $isQuery = false;

    /**
     * 是否是do操作
     * @var boolean
     */
    public $isDo = false;

    /**
     * 是否是页面
     * @var boolean
     */
    public $isPage = false;

    /**
     * 结果
     * @var ResultModel
     */
    public $result;

    /**
     * 状态
     * @var StateModel
     */
    public $state;

    /**
     * 商户ID
     * @var int
     */
    public $mchid;

    /**
     * 是否是一次sudo操作
     * @var boolean
     */
    public $isSudo = false;

    /**
     * 当isSudo为true时，获取模块名称
     * @var string
     */
    protected $sudoModule;

    /**
     * 初始化
     */
    public function _initialize()
    {
        if (is_int(strpos(strtolower(ACTION_NAME), "do"))) {
            $this->isDo = true;
        }
        if (is_int(strpos(strtolower(ACTION_NAME), "get"))) {
            $this->isGet = true;
        }
        if (is_int(strpos(strtolower(ACTION_NAME), "query"))) {
            $this->isQuery = true;
        }
        if (is_int(strpos(strtolower(ACTION_NAME), "page"))) {
            $this->isPage = true;
            $this->assign('WEB_ROOT', C('WEB_ROOT'));
            $this->assign('FILE_ROOT', C('FILE_ROOT'));
        }
        if (isset($_SESSION['action_sudo_flag'])) {
            $this->isSudo = true;
            $this->sudoModule = $_SESSION['action_sudo_module'];
        }
        if (!$this->isSudo) {
            if ($this->isGet || $this->isDo) {
                $class = MODULE_NAME . 'Action';
                if (!class_exists($class, false)) {
                    $class = GROUP_NAME . '\\' . $class;
                }
                $method = new ReflectionMethod($class, ACTION_NAME);
                $x = $method->getParameters();
                foreach ($x as $value) {
                    $name = $value->name;
                    $v = isset($_REQUEST[$name]) ? $_REQUEST[$name] : null;
                    if ($_SERVER['REQUEST_METHOD'] == 'GET') {
                        //避免中文乱码
                        $code = strtolower(mb_detect_encoding($v, array('ASCII', 'GB2312', 'GBK', 'UTF-8')));
                        if (($code == 'gb2312' || $code == 'utf-8' || $code == 'euc-cn') && $code != 'utf-8') {
                            $v = iconv($code, 'utf-8', $v);
                        }
                    }
                    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                        if (empty($v) && (strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false)) {
                            $vars = json_decode(file_get_contents('php://input'), true);
                            $v = $vars[$name];
                        }
                    }
                    $data[$name] = $v;
                }
                $this->data = $data;
            }
            if (GROUP_NAME == 'Home') {
                $this->mchid = $this->getMchidByCipmchid($this->getCallBack());
                $this->state = StateModel::get(StateModel::$HOME_USER);
            } elseif (GROUP_NAME == 'Admin') {
                $this->state = StateModel::get(StateModel::$ADMIN_USER);
            } elseif (GROUP_NAME == 'Passenger') {
                $this->mchid = $this->getMchidByCipmchid($this->getCallBack());
                if ($this->mchid) {
                    $this->state = StateModel::get(StateModel::$PASSENGER, $this->mchid);
                } else {
                    if (is_int(strpos(strtolower(ACTION_NAME), "wechatauthforpaypage")) || is_int(strpos(strtolower(ACTION_NAME), "wechatauthpage"))) {
                    } else {
                        echo "商户参数错误！";
                        die;
                    }
                }
            } elseif (GROUP_NAME == 'Driver') {
                $this->mchid = $this->getMchidByCipmchid($this->getCallBack());

                if ($this->mchid) {
                    $this->state = StateModel::get(StateModel::$DRIVER, $this->mchid);
                } else {
                    // 提取动作名称判断逻辑为单独的函数
                    if ($this->isNoAuthCodeAction(ACTION_NAME) == false) {
                        // 非微信操作时提示错误
                        echo "商户参数错误！";
                        die;
                    }
                }

            } elseif (GROUP_NAME == 'Branch') {
                $this->mchid = $this->getMchidByCipmchid($this->getCallBack());
                if ($this->mchid) {
                    $this->state = StateModel::get(StateModel::$BRANCH_USER, $this->mchid);
                } else {
                    if ($this->isNoAuthCodeAction(ACTION_NAME) == false) {
                        // 非微信操作时提示错误
                        echo "商户参数错误！";
                        die;
                    }
                }
            } elseif (GROUP_NAME == 'App') {
                $this->mchid = $this->getMchidByCipmchid($this->getCallBack());
                if ($this->mchid) {
                } else {
                    if (is_int(strpos(strtolower(ACTION_NAME), "dogetwechatcode")) || is_int(strpos(strtolower(ACTION_NAME), "dogetwechatcodeforpay"))) {
                    } else {
                        //                        echo 'http://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'];
                        echo "商户参数错误！";
                        die;
                    }
                }
            }

            if ($this->state) {
                $this->_recordAccountOp($this->state->user_type, $this->state->user_id);
            }
            tag('auth_check', $this);
        }
    }

    /**
     * 判断当前操作是否授权
     */
    private function isNoAuthCodeAction($actionName)
    {
        $actionName = strtolower($actionName);
        $validActions = [
            "dogetwechatcodeforpay",
            "dogetwechatcode",
            "doyygetwechatcodeforpay",
            "doyygetwechatcode",
            "dosimulatelogin",
            "dodriverunifiedlogin",
            "doquerydriverallmerchants",
        ];

        foreach ($validActions as $validAction) {
            if (strpos($actionName, $validAction) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取商户密文
     * @return null|object|string
     */
    protected function getCallBack()
    {
        $cipmchid = isset($_GET['callback']) ? $_GET['callback'] : false;
        if ($cipmchid == false) {
            $cipmchid = isset($_SERVER['HTTP_CALLBACK']) ? $_SERVER['HTTP_CALLBACK'] : false;
        }
        return $cipmchid;
    }
    protected function getHtmlInfo($url)
    {
        set_time_limit(true);
        vendor("simple_html_dom", VENDOR_PATH . '/simple_html');
        $dom = new \simple_html\simple_html_dom();
        $r = $dom->load_file($url);
        var_dump($dom->find('b'));
        $dom->clear();
        exit();

    }
    protected function get_tag_data($html, $tag, $attr, $value)
    {
        $regex = "/<$tag.*?$attr=\".*?$value.*?\".*?>(.*?)<\/$tag>/is";
        preg_match_all($regex, $html, $matches, PREG_PATTERN_ORDER);
        return $matches[1];
    }
    /**
     * 添加
     * @param string $model 模型名称
     * @param array $data 数据
     */
    protected function add($model = null, $data = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        if (empty($data)) {
            $data = $this->data;
        }
        $model = new $model();
        if (!$model->create($data)) {
            $this->result = new ResultModel(false, $model->getError());
            return $this->result;
        }

        $result = $model->add();

        if ($result) {
            $this->result = new ResultModel(true, $result);
        } else {
            $this->result = new ResultModel(false, '添加失败!');
        }
        return $this->result;
    }

    /**
     * 保存
     * @param string $model 模型名称
     * @param array $data 数据
     */
    protected function save($model = null, $data = null)
    {

        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        if (empty($data)) {
            $data = $this->data;
        }
        $model = new $model();
        if (!$model->create($data)) {
            $this->result = new ResultModel(false, $model->getError());
            return $this->result;
        }
        $result = $model->save();
        if ($result) {
            $this->result = new ResultModel(true, $result);
        } else {
            $this->result = new ResultModel(false, '保存失败!');
        }
        return $this->result;
    }


    /**
     * 输出
     * @param resultModel $result 默认为最近一个方法的返回结果
     */
    public function output($result = null)
    {
        if (empty($result)) {
            $result = $this->result;
        } else {
            $this->result = $result;
        }
        //sudo操作不输出
        if (!$this->isSudo) {

            if ($this->isDo || $this->isGet || $this->isQuery) {
                //                if (!APP_DEBUG) {
                //                    if ($result->ret && empty($result->count)) {
                //                        $result->data = '操作成功';
                //                    } else if (!$result->ret) {
                //                        $result->data = '操作失败';
                //                    }
                //                }
                if ((is_int($result->data) || empty($result->data)) && empty($result->count) && $this->isDo) {
                    if ($result->ret) {
                        $result->data = '操作成功';
                    } else {
                        $result->data = '操作失败';
                    }
                }

                echo jsonp_encode($result->ret, $result->data, $result->count, ['timestamp' => $result->timestamp, 'ext' => $result->ext]);
                exit;
            } elseif ($this->isPage) {
                echo dump($result);
            }
        } else {
            return $result;
        }
    }

    /**
     * 单条查询
     * @param mixed $where 条件
     * @param string $model 模型名称
     * @param mixed $field 字段
     * @param boolean $except 字段排除
     */
    protected function find($where, $model = null, $field = null, $except = false)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        //检测视图
        $is_view = false;
        if (is_int(strpos($model, 'View'))) {
            $is_view = true;
        }
        $model = new $model();
        $result = $model->field($field, $except)->where($where)->find();
        //        return $model->getLastSql();
        if ($result) {
            //视图模型
            if ($is_view && !empty($model->filter)) {
                foreach ($model->filter as $filter_key => $filter) {
                    $filter($filter_key, $result);
                }
            }
            $this->result = new ResultModel(true, $result);
        } else {
            $this->result = new ResultModel(false);
        }
        return $this->result;
    }

    /**
     * 查询数量
     * @param mixed $where 条件
     * @param string $model 模型名称
     */
    protected function count($where, $model = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        $model = new $model();
        $result = $model->where($where)->count($model->getPk());
        if ($result) {
            $this->result = new ResultModel(true, $result);
        } else {
            $this->result = new ResultModel(false);
        }
        return $this->result;
    }

    /**
     * 多条查询
     * @param mixed $where 条件
     * @param int $page 第几页
     * @param int $size 每页条数
     * @param string $order 排序
     * @param string $model 模型名称
     * @param mixed $field 字段
     * @param boolean $except 字段排除
     * @param boolean $group 分组
     */
    protected function select($where, $page, $size, $order = null, $model = null, $field = null, $except = false, $group = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        //检测视图
        $is_view = false;
        if (is_int(strpos($model, 'View'))) {
            $is_view = true;
        }
        $model = new $model();

        //视图模型
        if ($is_view) {
            if (is_string($where)) {
                $where = $this->_parseWhere($where);
            }
            if (!empty($model->where)) {
                $where = array_merge($where, $this->_parseWhere($model->where));
            }
        }
        $model = $model->where($where)->field($field, $except);
        if (!empty($page) && !empty($size)) {
            $model = $model->page($page . ',' . $size);
        }
        if (!empty($group)) {
            $model = $model->group($group);
        }
        $result = $model->order($order)->select();
        $count = $model->where($where)->count();

        if ($result) {
            //视图模型
            if ($is_view && !empty($model->filter)) {
                foreach ($result as $key => $item) {
                    foreach ($model->filter as $filter_key => $filter) {
                        $filter($filter_key, $item);
                    }
                    $result[$key] = $item;
                }
            }
            $this->result = new ResultModel(true, $result, $count);
        } else {
            $this->result = new ResultModel(false);
        }
        return $this->result;
    }

    /**
     * 解析where字符串，转换为where数组
     * @param string $where
     */
    private function _parseWhere($where)
    {
        $arr1 = explode("and", $where);
        foreach ($arr1 as $v1) {
            $arr2 = explode("=", $v1);
            $r[trim($arr2[0])] = str_replace('"', '', trim($arr2[1]));
        }
        return $r;
    }

    /**
     * 删除
     * @param int $pk
     * @param string $model 模型名称
     */
    protected function delete($pk, $model = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            $model .= 'Model';
        }
        $model = new $model();
        $result = $model->delete($pk);
        if ($result) {
            $this->result = new ResultModel(true, $result);
        } else {
            $this->result = new ResultModel(false);
        }
        return $this->result;
    }

    /**
     * 上传文件
     * @param string $upload_dir 上传目录（结尾带斜杠）
     * @param array $legal_type 合法类型
     * @param http_post_file $remote_file post文件
     */
    protected function uploadFile($upload_dir, $legal_type, $remote_file)
    {
        if (!is_dir($upload_dir)) {
            if (!mkdir($upload_dir, 0777, true)) {
                $this->result = new ResultModel(false, '目录创建失败');
                return $this->result;
            }
        }
        //验证文件上传类型
        $file_type = strtolower(end(explode('.', $remote_file['name'])));
        $result = false;
        foreach ($legal_type as $value) {
            if ($file_type == $value) {
                $result = true;
                break;
            }
        }
        if (!$result) {
            $this->result = new ResultModel(false, '文件类型错误');
            return $this->result;
        }
        $file_name = date('Ymdhms') . '.' . end(explode('.', $remote_file['name']));
        $file = $upload_dir . $file_name;
        $result = move_uploaded_file($remote_file['tmp_name'], $file);
        if ($result) {
            $this->result = new ResultModel(true, $file_name);
        } else {
            $this->result = new ResultModel(false, '保存失败！');
        }
        return $this->result;
    }

    /**
     * 启动事务
     */
    protected function startTrans()
    {
        if (!$this->isSudo) {
            $m = new Model();
            $m->startTrans();
        }
    }

    /**
     * 提交事务
     */
    protected function commitTrans()
    {
        if (!$this->isSudo) {
            $m = new Model();
            $m->commit();
        }
    }

    /**
     * 事务回滚
     */
    protected function transRollback()
    {
        if (!$this->isSudo) {
            $m = new Model();
            $m->rollback();
        }
    }

    /**
     * 以其他身份实例化模块
     * @param string $module 模块名称
     * @param int $user_id 用户ID
     * @param int $user_type 用户类型（1-管理员，2-乘客，3-车主，4-会员，默认为会员）
     * @param array $data 数据
     *
     */
    protected function sudo($module, $user_id, $user_type = 4, $data = null)
    {
        $_SESSION['action_sudo_flag'] = true;
        $_SESSION['action_sudo_module'] = $module;
        try {
            $module .= 'Action';
            if (!class_exists($module, false)) {
                $module = GROUP_NAME . '\\' . $module;
            }
            $o = new $module();
            if (!empty($data)) {
                $o->data = $data;
            }
            $o->state = new StateModel($user_id, null, $user_type, null);
        } catch (Exception $exc) {
            $this->result = new ResultModel(false, 'sudo实例化模块失败');
        }
        $_SESSION['action_sudo_flag'] = null;
        $_SESSION['action_sudo_module'] = null;
        return $o;
    }

    /**
     * 发送邮件
     * @param  string $to 收件人邮箱
     * @param  string $body 邮件内容
     * @param  string $subject 邮件主题
     * @param  string $send_name 发件人名称
     * @param  string $receive_name 收件人名称
     * @return boolean 是否成功
     */
    protected function send_email($to, $body, $subject, $send_name = '', $receive_name = '')
    {
        if (!C('ENABLE_EMAIL')) {
            $this->result = new ResultModel(true);
            return $this->result;
        }
        //$to 表示收件人地址 $subject 表示邮件标题 $body表示邮件正文
        if ($send_name == '') {
            $send_name = C('EMAIL_SEND_NAME');
        }
        if ($receive_name == '') {
            $receive_name = C('EMAIL_RECEIVE_NAME');
        }
        error_reporting(E_STRICT);
        date_default_timezone_set("Asia/Shanghai");         //设定时区东八区
        $from = C("ADMIN_EMAIL");
        $mail = new PHPMailer(APP_DEBUG);                //实例化一个PHPMailer对象
        $body = ereg_replace("[\]", '', $body);   //对邮件内容进行必要的过滤
        $mail->CharSet = C("EMAIL_CHARSET");                   //设定邮件编码，默认ISO-8859-1，如果发中文此项必须设置，否则乱码
        $mail->IsSMTP();                                    // 设定使用SMTP服务
        $mail->SMTPDebug = 1;                              // 启用SMTP调试功能 (1 = errors and messages 2 = messages only)
        $mail->SMTPAuth = C("SMTP_AUTH");                  // 启用 SMTP 验证功能
        $mail->SMTPSecure = C("SMTP_SECURE");                // 安全协议
        $mail->Host = C("SMTP_HOST");                  // SMTP 服务器
        $mail->Port = C("SMTP_PORT");                  // SMTP服务器的端口号
        $mail->Username = C("SMTP_USER");                // SMTP服务器用户名
        $mail->Password = C("SMTP_PASSWORD");            // SMTP服务器密码
        $mail->SetFrom($from, $send_name);
        $mail->AddReplyTo($from, $send_name);
        $mail->Subject = $subject;
        $mail->MsgHTML($body);
        $address = $to;
        $mail->AddAddress($address, $receive_name);
        try {
            if (!$mail->Send()) {
                $this->result = new ResultModel(false, $mail->ErrorInfo);
            } else {
                $this->result = new ResultModel(true);
            }
        } catch (Exception $exc) {
            $this->result = new ResultModel(false, $exc->getMessage());
        }
        return $this->result;
    }

    /**
     * 排除字段
     * @param string $model 模型名称
     * @param string $fields 字段（以英文逗号分隔）
     * @param string $except_fields 排除的字段（以英文逗号分隔）
     * @param string $require_fields 必须的字段（以英文逗号分隔）
     * @param string $deep 深层路径（如：group)
     */
    private function _exceptField($model = null, $fields = null, $except_fields = null, $require_fields = null, $deep = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            if (!is_int(strpos($model, 'Model'))) {
                $model .= 'Model';
            }
        }
        $model = new $model();
        if (empty($fields)) {
            $fields = $model->getDbFields();
        } else {
            $fields = explode(",", $fields);
            if (empty($fields)) {
                $this->result = new ResultModel(false, '字段格式错误');
                return $this->result;
            }
            if (!empty($deep)) {
                $t = array();
                foreach ($fields as $field) {
                    if (is_int(strpos($field, $deep . ".*"))) {
                        $t = $model->getDbFields();
                        break;
                    }
                    if (is_int(strpos($field, $deep))) {
                        $t[] = str_replace($deep . ".", "", $field);
                    }
                }
                $fields = $t;
            }
            $t = array();
            $db_fields = $model->getDbFields();
            foreach ($fields as $field) {
                $keys = array_keys($db_fields, $field);
                if (!empty($keys)) {
                    $t[] = $db_fields[$keys[0]];
                }
            }
            $fields = $t;
        }
        if (!empty($except_fields)) {
            $except_fields = explode(",", $except_fields);
            foreach ($except_fields as $except_field) {
                $keys = array_keys($fields, $except_field);
                if (!empty($keys)) {
                    unset($fields[$keys[0]]);
                }
            }
        }
        if (!empty($require_fields)) {
            $require_fields = explode(",", $require_fields);
            foreach ($require_fields as $require_field) {
                if (!in_array($require_field, $fields)) {
                    $fields[] = $require_field;
                }
            }
        }
        $this->result = new ResultModel(true, $fields);
        return $this->result;
    }

    /**
     * 通过ID获取实体信息
     * @param int $id 实体ID
     * @param string $model 实体模型
     * @param string $fields 查询的字段（以英文逗号隔开）
     * @param string $except_fields 排除的字段（以英文逗号隔开）
     * @param string $require_fields 必须的字段（以英文逗号隔开）
     * @param string $deep 深层路径（以英文点号分隔）
     */
    protected function getEntityById($id, $model = null, $fields = null, $except_fields = null, $require_fields = null, $deep = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            if (!is_int(strpos($model, 'Model'))) {
                $model .= 'Model';
            }
        }
        $r = $this->_exceptField($model, $fields, $except_fields, $require_fields, $deep);
        if ($r->ret) {
            $model = new $model();
            $result = $model->field($r->data)->find($id);
            if ($result) {
                $this->result = new ResultModel(true, $result);
            } else {
                $this->result = new ResultModel(false);
            }
        } else {
            $this->result = $r;
        }
        return $this->result;
    }

    /**
     * 加载实体的子实体信息
     * @param ResultModel $entity_r 实体结果（一般由getEntityById返回）
     * @param string $id_key id的键值（以英文点好分隔）
     * @param string $model 模型名称
     * @param string $fields 查询的字段（以英文逗号分隔）
     * @param string $except_fields 排除的字段（以英文逗号分隔）
     * @param string $require_fields 必须的字段（以英文逗号分隔）
     * @param string $deep 深层路径（以英文点号分隔）
     * @return ResultModel
     */
    protected function loadSubEntity(&$entity_r, $id_key, $model = null, $fields = null, $except_fields = null, $require_fields = null, $deep = null)
    {
        if (empty($model)) {
            if (!$this->isSudo) {
                $model = MODULE_NAME . 'Model';
            } else {
                $model = ($this->sudoModule) . 'Model';
            }
        } else {
            if (!is_int(strpos($model, 'Model'))) {
                $model .= 'Model';
            }
        }

        $include = false;
        if (empty($fields)) {
            $include = true;
        } else {
            $field_arr = explode(",", $fields);
            foreach ($field_arr as $field) {
                if (strpos($field, $deep) === 0) {
                    $include = true;
                    break;
                }
            }
        }

        if ($include) {
            $t = $this->getEntityById(get_arr($entity_r->data, $id_key), $model, $fields, $except_fields, $require_fields, $deep);
            if ($t->ret) {
                fill_arr($entity_r->data, $deep, $t->data);
            } else {
                fill_arr($entity_r->data, $deep, '');
            }
        }
        remove_arr($entity_r->data, $id_key);
        $this->result = $entity_r;
        return $entity_r;
    }

    /**
     * 以其他身份加载子实体
     * @param array $entity_r 实体结果（一般由getEntityById返回）
     * @param string $id_param 参数ID名称
     * @param string $id_key id的键值（以英文点好分隔）
     * @param string $module 模块名称
     * @param string $action 操作名称
     * @param string $fields 查询的字段（以英文逗号分隔）
     * @param string $deep 深层路径（以英文点号分隔）
     * @return ResultModel
     */
    protected function sudoLoadSubEntity(&$entity_r, $id_param, $id_key, $module, $action, $fields = null, $deep = null)
    {
        if (empty($fields) || is_int(strpos($fields, $deep))) {
            $o = $this->sudo($module, $this->state->user_id);
            $o->data = array($id_param => get_arr($entity_r->data, $id_key));
            if (is_int(strpos($fields, $deep . '.*'))) {
                $fields1 = null;
            } elseif (is_int(strpos($fields, $deep))) {
                $field_arr = explode(",", $fields);
                $first = true;
                foreach ($field_arr as $field) {
                    if (is_int(strpos($field, $deep . '.'))) {
                        $s = str_replace($deep . '.', "", $field);
                        if ($first) {
                            $fields1 = $s;
                            $first = false;
                        } else {
                            $fields1 .= ',' . $s;
                        }
                    }
                }
            }
            $t = $o->$action(get_arr($entity_r->data, $id_key), $fields1);
            if ($t->ret) {
                fill_arr($entity_r->data, $deep, $t->data);
            } else {
                fill_arr($entity_r->data, $deep, array('error' => $t->data));
            }
        }
        remove_arr($entity_r->data, $id_key);
        $this->result = $entity_r;
        return $entity_r;
    }

    /**
     * 以其他身份通过ID获取实体信息
     * @param string $id_param 参数ID名称
     * @param int $id 参数ID值
     * @param string $module 模块名称
     * @param string $action 操作名称
     * @param string $fields 查询的字段（以英文逗号分隔）
     * @return ResultModel
     */
    protected function sudoGetEntityById($id_param, $id, $module, $action, $fields)
    {
        $o = $this->sudo($module, $this->state->user_id);
        $o->data = array($id_param => $id);
        return $o->$action($id, $fields);
    }

    /**
     * 判断是否包含根字段
     * @param string $fields 字段集（以英文逗号隔开）
     * @param string $field 字段（以点号隔开）
     */
    protected function isIncludeRootField($fields, $field)
    {
        if (empty($fields)) {
            return true;
        }
        $arr1 = explode(",", $fields);
        foreach ($arr1 as $v1) {
            if (strpos($v1, $field) === 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 替换限定路径
     * @param string $fields 字段集（以英文逗号隔开）
     * @param string $old_deep 旧的路径
     * @param string $new_deep 新的路径
     */
    protected function replaceDeep($fields, $old_deep, $new_deep)
    {
        $arr1 = explode(",", $fields);
        $first = true;
        $fields1 = '';
        foreach ($arr1 as $v1) {
            if (strpos($v1, $old_deep) === 0) {
                if (empty($new_deep)) {
                    $v2 = str_replace($old_deep . ".", "", $v1);
                } else {
                    $v2 = str_replace($old_deep, $new_deep, $v1);
                }
                if ($first) {
                    $fields1 .= $v2;
                    $first = false;
                } else {
                    $fields1 .= ',' . $v2;
                }
            }
        }
        return $fields1;
    }

    /**
     * 添加日志
     * @param  string $operation_content 操作内容
     * @param  string $data 数据内容
     */
    protected function doAddLog($Operation_content, $data = null, $user_type = 1)
    {
        try {
            $operation_time = date('Y-m-d H:i:s', time());
            $userId = !empty($this->state->user_id) ? $this->state->user_id : 0;
            $this->add('Log', array('Operation_content' => $Operation_content, 'operation_time' => $operation_time, 'admin_id' => $userId, 'data' => $data,'user_type' => $user_type));
        } catch (Exception $e) {
            \Log::write("行号:".__LINE__.",日志生成失败:".$e->getMessage());
        }
    }

    /**
     * 提现交易付款日志
     * @param $log
     * @param $data
     */
    protected function doAddLogTransfer($type = 1, $value, $log, $data, $mchid = 0)
    {
        try {
            $logData['mchid'] = $mchid;
            $logData['type'] = $type;
            $logData['value'] = $value;
            $logData['log'] = $log;
            $logData['data'] = $data;
            $this->add('LogTransfer', $logData);
        } catch (Exception $e) {

        }
    }

    /**
     * 解密商户id
     * @param  string $cipmchid 商户密文
     */
    protected function getMchidByCipmchid($cipmchid)
    {
        //解密商户id
        $mchidArr = M('cipher_control')->where("ciphertext='".$cipmchid."'")->find();
        return $mchidArr['mchid'];
    }

    /**
     * 车牌号加密
     * @param  string $carNum 车牌号码
     */
    protected function encryptionCarNumByCarNum($carNum)
    {
        return $carNum;
        $carNumString = "";
        $replaceStr = "**";
        if (strlen($carNum) - 4 > 0) {
            $prePos = strlen($carNum) - 4;
            $carNumString = substr_replace($carNum, $replaceStr, $prePos, 2);
        }

        return $carNumString;
    }

    /**
     * 司机姓名加密
     * @param  string $driverName 司机姓名
     */
    protected function encryptionDriverName($driverName)
    {
        $driverName = trim($driverName);
        if (strlen($driverName) > 3) {
            $driverName = substr($driverName, 0, 3)."师傅";
        }

        return $driverName;
    }

    /**
     * 手机号加密
     * @param  string $cellphone 手机号加密
     */
    protected function encryptionCellphoneByCellphone($cellphone)
    {
        $phoneNumString = "";
        $replaceStr = "*****";
        if (strlen($cellphone) - 8 > 0) {
            $prePos = strlen($cellphone) - 8;
            $phoneNumString = substr_replace($cellphone, $replaceStr, $prePos, 5);
        }

        return $phoneNumString;
    }

    /**
     * 获取手机号后4位
     * @param  string $cellphone 手机号
     */

    protected function getCellphoneSubTailFour($cellphone)
    {
        $tailFourString = "";
        if (strlen($cellphone) - 4 > 0) {
            $tailFourString = substr($cellphone, -4);
        }

        return $tailFourString;
    }

    /**
     * 多维数组转字符串
     */
    protected function cube_implode($glue, $data)
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $resultArr[] = self::cube_implode($glue, $value);
            } else {
                $resultArr[] = $value;
            }
        }
        return implode($glue, $resultArr);
    }

    /**
     * 记录用户操作信息
     * @param $accountType
     * @param $accountId
     */
    private function _recordAccountOp($accountType, $accountId)
    {
        $currentTime = date('Y-m-d H:i:s', time());
        $op = GROUP_NAME.','.MODULE_NAME.','.ACTION_NAME;
        $lastIp = $_SERVER['REMOTE_ADDR'];
        $saveAccountType = -1;

        if (in_array(ACTION_NAME, array('getOrdersOnWay'))) {
            return false;
        }

        if ($accountType) {
            switch ($accountType) {
                case StateModel::$ADMIN_USER:{
                    $adminR = $this->find(array('admin_id' => $accountId), 'Admin');
                    if ($adminR->ret) {
                        if ($adminR->data['group_id'] == 1) {
                            $saveAccountType = \CommonDefine::SYSTEM_ROLE_0;
                        } elseif ($adminR->data['group_id'] == 2) {
                            $saveAccountType = \CommonDefine::SYSTEM_ROLE_1;
                        }
                    }
                    break;
                }
                case StateModel::$BRANCH_USER:{
                    $saveAccountType = \CommonDefine::SYSTEM_ROLE_2;
                    break;
                }
                case StateModel::$PASSENGER:{
                    $saveAccountType = \CommonDefine::SYSTEM_ROLE_4;
                    break;
                }
                case StateModel::$DRIVER:{
                    $saveAccountType = \CommonDefine::SYSTEM_ROLE_3;
                    break;
                }
                default:
                    break;
            }

            if ($saveAccountType != -1) {
                $lastOpLogMode = M('last_op_log');
                $lastOpLogArr = $lastOpLogMode->where(array('account_type' => $saveAccountType, 'account_id' => $accountId))->find();
                if (empty($lastOpLogArr)) {
                    $lastOpLogMode->add(array('account_type' => $saveAccountType, 'account_id' => $accountId,'op_count' => 1, 'last_op_time' => $currentTime, 'op' => $op, 'last_op_ip' => $lastIp, 'create_time' => $currentTime, 'update_time' => $currentTime));
                } else {
                    $lastOpLogMode->where(array('account_type' => $saveAccountType, 'account_id' => $accountId))->save(array('op_count' => $lastOpLogArr['op_count'] + 1, 'last_op_time' => $currentTime, 'op' => $op, 'last_op_ip' => $lastIp, 'update_time' => $currentTime));
                }
            }
        }
        return true;
    }

    //删除字符串中所有的特殊字符
    protected function trimall($str)//删除空格
    {
        $oldchar = array(" ","　","\t","\n","\r");
        $newchar = array("","","","","");
        return str_replace($oldchar, $newchar, $str);
    }


    /**
     * 自定义商户乘客端H5导航菜单
     *
     * @param integer $mchid
     * @return array
     */
    public function defineMerchantNavigationMenu($mchid)
    {
        $navConfig = array(
                array('id' => 'chart_car', 'nav_id' => 1, 'name' => '包车',   'show' => 1, 'active' => 0, 'click' => 1, 'type' => 2, 'title' => '包车'),
                array('id' => 'regular',   'nav_id' => 2, 'name' => '定制客运', 'show' => 1, 'active' => 1, 'click' => 1, 'type' => 5, 'title' => '定制客运'),
                array('id' => 'goods',     'nav_id' => 3, 'name' => '带货',   'show' => 1, 'active' => 0, 'click' => 1, 'type' => 3, 'title' => '带货'),
            );
        $menu = $this->find(
            array(
                'account_id' => $mchid, 
                'account_type' => CommonDefine::SYSTEM_ROLE_1, 
                'key' => 'merchant_passenger_menu_config'
            ),
            'SystemConfig', 
            'key,value');
        if ($menu->ret) {
           $navConfig = json_decode($menu->data['value'], true);
        }
        return $navConfig;
    }


}

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 系统文档模型
 *
 * <AUTHOR>
 */
class SystemDocModel
{

    //put your code here
    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 模块集合
     * @var array
     */
    public $modules;

    /**
     * 说明
     * @var string
     */
    public $intro;

}

/**
 * 模块文档模型
 */
class ModuleDocModel
{

    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 读取接口集合
     * @var array
     */
    public $getActions;

    /**
     * 写入接口集合
     * @var array
     */
    public $doActions;

    /**
     * 说明
     * @var string
     */
    public $intro;

}

/**
 * 接口文档模型
 */
class ActionDocModel
{

    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 路径
     * @var string
     */
    public $url;

    /**
     * 参数集合
     * @var array
     */
    public $params;

    /**
     * 说明
     * @var string
     */
    public $intro;

}

/**
 * 参数文档模型
 */
class ParamDocModel
{

    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 类型
     * @var string
     */
    public $type;

    /**
     * 说明
     * @var string
     */
    public $intro;

}

/**
 * 实体文档模型
 */
class EntityDocModel
{

    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 介绍
     * @var string
     */
    public $intro;

    /**
     * 属性数组
     * @var array
     */
    public $properties;

}

/**
 * 属性文档模型
 */
class PropertyDocModel
{

    /**
     * 名称
     * @var string
     */
    public $name;

    /**
     * 字段
     * @var string
     */
    public $field;

    /**
     * 类型
     * @var string
     */
    public $type;

    /**
     * 说明
     * @var string
     */
    public $intro;

}

/**
 * 文档模块
 *
 * <AUTHOR>
 */
class DocAction extends CoreAction
{

    //put your code here

    public function index()
    {
        try {
            $system = $this->_getSystem('Home', '会员API');
            $this->assign('system', $system);
            $admin_system = $this->_getSystem('Admin', '系统API');
            $this->assign('admin_system', $admin_system);
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    public function homeIndex()
    {
        try {
            $system = $this->_getSystem('Home', '公共API');
            $this->assign('system', $system);
            $this->display('Tpl/Develop/Doc/index.html');
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    public function adminIndex()
    {
        try {
            $admin_system = $this->_getSystem('Admin', '系统API');
            $this->assign('admin_system', $admin_system);
            $this->display('Tpl/Develop/Doc/index.html');
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    public function driverIndex()
    {
        try {
            $driver = $this->_getSystem('Driver', '车主API');
            $this->assign('driver', $driver);
            $this->display('Tpl/Develop/Doc/index.html');
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    public function passengerIndex()
    {
        try {
            $passenger = $this->_getSystem('Passenger', '乘客API');
            $this->assign('passenger', $passenger);
            $this->display('Tpl/Develop/Doc/index.html');
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    public function branchIndex()
    {
        try {
            $branch = $this->_getSystem('Branch', '分台API');
            $this->assign('branch', $branch);
            $this->display('Tpl/Develop/Doc/index.html');
        } catch (ReflectionException $exc) {
            echo $exc->getMessage();
        }
    }

    /**
     * 接口详细页面
     * @param string $system_name 系统名称
     * @param string $module_name 模块名称
     * @param string $action_name 接口名称
     */
    public function actionPage($system_name, $module_name, $action_name)
    {
        import('@/Action/' . $system_name . '/' . $system_name . 'CoreAction');

        import('@/Action/' . $system_name . '/' . $module_name . 'Action');
        $class_name = $module_name . 'Action';
        if (!class_exists($class_name, false)) {
            $class_name = $system_name . '\\' . $class_name;
        }
        $method = new ReflectionMethod($class_name, $action_name);
        $action = new ActionDocModel();
        $action->name = $method->name;
        $action->url = C('WEB_ROOT') . $system_name . '/' . $module_name . '/' . $action_name;
        $methodComment = $method->getDocComment();
        preg_match('/\/\*\*\s*\*\s*(.*?)\s*\*/', $methodComment, $mtmatch);
        $action->intro = $mtmatch[1];
        preg_match_all('/param\s+(.*?)\s+\$(.*?)\s+(.*?)\s*\*/', $methodComment, $mpmatch);
        foreach ($mpmatch[1] as $key => $value) {
            $param = new ParamDocModel();
            $param->name = $mpmatch[2][$key];
            $param->type = $value;
            $param->intro = $mpmatch[3][$key];
            $action->params[] = $param;
        }
        $this->assign('action', $action);
        $this->display();
    }

    /**
     * 实体页面
     */
    public function entityPage()
    {
        $dirHandle = opendir(LIB_PATH . 'Model');
        if ($dirHandle) {
            while (false !== ($file = readdir($dirHandle))) {
                if (preg_match('/.+ViewModel.class\.php$/', $file)) {
                    if (is_int(strpos($file, 'Core'))) {
                        continue;
                    }
                    $file = str_replace('ViewModel.class.php', '', $file);
                    $entitys[] = $this->_getEntity($file);
                }
            }
        }
        closedir($dirHandle);
        $this->assign('entitys', $entitys);
        $this->display();
    }

    /**
     * 获取实体
     * @param string $entity_name 实体名称
     */
    private function _getEntity($entity_name)
    {
        $entity_name = $entity_name . 'ViewModel';
        $class = new ReflectionClass($entity_name);
        preg_match('/\/\*\*\s*\*\s*(.*?)\s*\*/', $class->getDocComment(), $cmatch);
        $model = new $entity_name();
        $entity = new EntityDocModel();
        $entity->name = $entity_name;
        $entity->intro = $cmatch[1];
        foreach ($model->getFieldsInfo() as $key => $value) {
            $property = new PropertyDocModel();
            $property->name = $value['property_name'];
            $property->field = $value['field'];
            $property->type = $value['data_type'];
            $property->intro = $value['column_comment'];
            $entity->properties[] = $property;
        }
        return $entity;
    }

    /**
     * 获取模块
     * @param string $system_name 系统名称
     * @param string $module_name 模块名称
     */
    private function _getModule($system_name, $module_name)
    {
        import('@/Action/' . $system_name . '/' . $module_name . 'Action');
        $class_name = $module_name . 'Action';
        if (!class_exists($class_name, false)) {
            $class_name = $system_name . '\\' . $class_name;
        }
        $class = new ReflectionClass($class_name);
        $module = new ModuleDocModel();
        $module->name = $module_name;
        preg_match('/\/\*\*\s*\*\s*(.*?)\s*\*/', $class->getDocComment(), $cmatch);
        $module->intro = $cmatch[1];
        foreach ($class->getMethods() as $method) {
            if ($method->isPublic()) {
                $action = new ActionDocModel();
                $action->name = $method->name;
                $methodComment = $method->getDocComment();
                preg_match('/\/\*\*\s*\*\s*(.*?)\s*\*/', $methodComment, $mtmatch);
                $action->intro = $mtmatch[1];
                if (preg_match('/^do.+/', $action->name)) {
                    $module->doActions[] = $action;
                } else if (preg_match('/^get.+/', $action->name)) {
                    $module->getActions[] = $action;
                }
            }
        }
        return $module;
    }

    /**
     * 获取系统
     * @param string $system_name 系统名称
     * @param string $intro 系统介绍
     */
    private function _getSystem($system_name, $intro)
    {
        import('@/Action/' . $system_name . '/' . $system_name . 'CoreAction');
        $dirHandle = opendir(LIB_PATH . 'Action/' . $system_name);
        $system = new SystemDocModel();
        $system->name = $system_name;
        $system->intro = $intro;
        if ($dirHandle) {
            while (false !== ($file = readdir($dirHandle))) {
                if (preg_match('/.+Action\.class\.php$/', $file)) {
                    if (is_int(strpos($file, 'Core')) || is_int(strpos($file, 'Page'))) {
                        continue;
                    }
                    $file = str_replace('Action.class.php', '', $file);
                    $system->modules[] = $this->_getModule($system_name, $file);
                }
            }
        }
        closedir($dirHandle);
        return $system;
    }

    /**
     * demo页面
     */
    public function demoIndex()
    {
        $this->assign('group', array('group_id' => 0));
        $this->assign('group_id', 0);
        $this->display();
    }

}

?>

<?php

import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 费用管理模块
 *
 * <AUTHOR>
 */
class CostManagerAction extends AdminCoreAction
{

    /**
     * 获取费用列表
     * @param string $fields 　查询的字段列表（默认为空，查询所有）
     * @param int $page 　第几页（默认为1）
     * @param int $size 　每页几条（默认为10）
     * @param int $cost_id 　编号（默认为空，查询所有）
     * @param int $cost_type 　费用类型（1-充值，2-月扣，3-保险）
     * @param string $driver_name 　车主姓名（默认为空，查询所有）
     * @param datetime $start_time 　开始时间（默认为空，查询所有）
     * @param datetime $end_time 　结束时间（默认为空，查询所有）
     */
    public function getCosts($fields = null, $page = 1, $size = 10, $cost_id = null, $cost_type = null, $driver_name = null, $start_time = null, $end_time = null)
    {
        $driverCostModel = new DriverCostModel();
        $where = array();
        if (!empty($cost_id)) {
            $where['DriverCost.cost_id'] = $cost_id;
        }
        if (!empty($cost_type)) {
            $where['DriverCost.cost_type'] = intval($cost_type);
        }
        if (!empty($driver_name)) {
            $where['Driver.name'] = $driver_name;
        }
        if (!empty($start_time) && empty($end_time)) {
            $where['DriverCost.create_time'] = array('egt', $start_time);
        } else if (empty($start_time) && !empty($end_time)) {
            $where['DriverCost.create_time'] = array('elt', $end_time);
        } else if (!empty($start_time) && !empty($end_time)) {
            $where['DriverCost.create_time'] = array(array('egt', $start_time), array('elt', $end_time));
        }
        $driverTable = (new DriverModel())->getTableName();
        $data = $driverCostModel->alias('DriverCost')->join(" $driverTable Driver on DriverCost.driver_id=Driver.driver_id")->where($where)->page($page, $size)->field('DriverCost.cost_id')->order('DriverCost.create_time desc')->select();
        if ($data) {
            $count = $driverCostModel->alias('DriverCost')->join(" $driverTable Driver on DriverCost.driver_id=Driver.driver_id")->where($where)->count('DriverCost.cost_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("cost_id", $value['cost_id'], 'CostManager', 'getCost', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }
        $this->doAddLog("查看费用列表");
        return $this->output($r);
    }

    /**
     * 获取费用详细
     * @param int $cost_id 费用ID
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     */
    public function getCost($cost_id, $fields = null)
    {
        $r = $this->getEntityById($cost_id, 'DriverCost', $fields, null, 'driver_id');
        if ($r->ret) {
            $this->sudoLoadSubEntity($r, 'driver_id', 'driver_id', 'Driver\\Account', 'getDriver', $fields, 'driver');
        }
        $this->doAddLog("查看费用详细");
        return $this->output($r);
    }

}

?>

<?php
import('@/Action/Admin/AdminAction');
import('@/Action/Weixin/Wechat');
/**
 * Created by PhpStorm.
 * User: xhr
 * Date: 2017/5/19
 * Time: 14:57
 */
class PackageManagerAction extends AdminCoreAction
{

    public function doList($page,$size){
        $where['is_del'] = 0 ;
        $r = $this->select($where,$page,$size,"id DESC","ValueOfMerchantPackageTypeStatus",'admin_id,stsv_id,id,create_time');
        foreach($r->data as $key => $item){
            $r->data[$key]['mchname'] = $this->find(array('admin_id'=>$item['admin_id']),'Admin',"mchname")->data['mchname'];
            $sstvR = $this->find(array('id'=>$item['stsv_id']),'SetTypeStatusValue');
            $r->data[$key]['type'] = $sstvR->data['category_id'];
            $r->data[$key]['num'] = $sstvR->data['value'];;
            unset($r->data[$key]['admin_id'],$r->data[$key]['stsv_id']);
        }
        return $this->output(new \ResultModel(true, $r->data, $r->count));
    }
    /**
     *添加
     * @param $num 数量
     * @param $typeid 套餐类型
     * @param $mchid 商户id
     *
     */
    public function doAdd($num , $typeid, $mchid){
        if(!$this->isEmpty($mchid)){
            $this->output(new ResultModel(false, "请选择商户"));
        }
        $adminR = $this->find(array('admin_id' => $mchid , 'group_id' => 2 , 'is_del' => 0 ) , 'Admin');

        if(!$adminR->ret){
            $this->output(new ResultModel(false, "该商户已停用"));
        }

        if(!$this->isEmpty($typeid)){
            $this->output(new ResultModel(false, "请选套餐"));
        }
        if(!$this->areaCode($num)){
            $this->output(new ResultModel(false, "数量只能输入正整数"));
        }
        $dateTime = date("Y-m-d H:i:s");
        $data['value'] = $num;
        $data['num'] = 1;
        $data['category_id'] = $typeid;
        try{
            $this->startTrans();
            $stsvid = 0;
            $setTypeStatusR = $this->find($data ,'SetTypeStatusValue');

            if(!$setTypeStatusR->ret){
                $r = $this->add('SetTypeStatusValue',$data);
                if(!$r->ret){
                    $this->transRollback();
                    $this->output(new ResultModel(false, "添加失败"));
                }
            }


            $stsvid = $setTypeStatusR->ret ? $setTypeStatusR->data['id'] : $r->data ;
            if($stsvid){
                $param['end_time'] = $adminR->data['endtime'];
                $param['admin_id'] = $mchid;
                $param['stsv_id'] = $stsvid;
                $param['is_del'] = 0;
                $vomptsR = $this->add('ValueOfMerchantPackageTypeStatus',$param);
                if($vomptsR->ret){
                    switch($typeid){
                        case \CommonDefine::PACKAGE_CATEGORY_1:{
                            $shortmessage = $adminR->data['shortmessage'] + $num;
                            $adminS = $this->save('Admin',array('admin_id' => $adminR->data['admin_id'],'shortmessage' => $shortmessage));
                            if(!$adminS->ret){
                                $this->transRollback();
                                $this->output(new ResultModel(false, "添加失败"));
                            }
                            break;
                        }
                        case \CommonDefine::PACKAGE_CATEGORY_2:{
                            $driver_num = $adminR->data['driver_num'] + $num;
                            $adminS = $this->save('Admin',array('admin_id' => $adminR->data['admin_id'],'driver_num' => $driver_num));
                            if(!$adminS->ret){
                                $this->transRollback();
                                $this->output(new ResultModel(false, "添加失败"));
                            }
                            break;
                        }
                        case \CommonDefine::PACKAGE_CATEGORY_3:{
                            $usestorage = $adminR->data['usestorage'] + $num;
                            $adminS = $this->save('Admin',array('admin_id' => $adminR->data['admin_id'],'usestorage' => $usestorage));
                            if(!$adminS->ret){
                                $this->transRollback();
                                $this->output(new ResultModel(false, "添加失败"));
                            }
                            break;
                        }
                        case \CommonDefine::PACKAGE_CATEGORY_4:{
                            break;
                        }
                        case \CommonDefine::PACKAGE_CATEGORY_5:{
                            $freeRideDriverNum = $adminR->data['free_ride_driver_num'] + $num;
                            $adminS = $this->save('Admin',array('admin_id' => $adminR->data['admin_id'],'free_ride_driver_num' => $freeRideDriverNum));
                            if(!$adminS->ret){
                                $this->transRollback();
                                $this->output(new ResultModel(false, "添加失败"));
                            }
                            break;
                        }
                        case \CommonDefine::PACKAGE_CATEGORY_6:{
                            $shortmessageMarketing = $adminR->data['shortmessage_marketing'] + $num;
                            $adminS = $this->save('Admin',array('admin_id' => $adminR->data['admin_id'],'shortmessage_marketing' => $shortmessageMarketing));
                            if(!$adminS->ret){
                                $this->transRollback();
                                $this->output(new ResultModel(false, "添加失败"));
                            }
                            break;
                        }
                        default:
                            $this->transRollback();
                            $this->output(new ResultModel(false, "暂不支持该套餐"));
                            break;
                    }

                    $this->commitTrans();
                    $this->output(new ResultModel(true, "添加成功"));
                }else{
                    $this->transRollback();
                    $this->output(new ResultModel(false, "添加失败"));
                }
            }else{
                $this->transRollback();
                $this->output(new ResultModel(false, "添加失败"));
            }

        }catch(Exception $e){
            echo $e->getMessage();
            $this->transRollback();
            $this->output(new ResultModel(false, "添加失败"));
        }

    }

    /**
     *添加
     * @param $num 数量
     * @param $typeid 套餐类型
     * @param $mchid 商户id
     *
     */
    public function doEdit($id ,$num , $typeid, $mchid){
        if(!$this->isEmpty($mchid)){
            $this->output(new ResultModel(false, "请选择商户"));
        }

        $adminR = $this->find(array('admin_id' => $mchid , 'group_id' => 2 , 'is_del' => 0 ) , 'Admin','endtime');

        if(!$adminR->ret){
            $this->output(new ResultModel(false, "该商户已停用"));
        }

        if(!$this->isEmpty($typeid)){
            $this->output(new ResultModel(false, "请选套餐"));
        }
        if(!$this->areaCode($num)){
            $this->output(new ResultModel(false, "数量只能输入正整数"));
        }
        $dateTime = date("Y-m-d H:i:s");
        $data['value'] = $num;
        $data['category_id'] = $typeid;
        try{
            $this->startTrans();
            $vsR = $this->find(array('id'=>$id),'ValueOfMerchantPackageTypeStatus');
            if(!$vsR->ret){
                $this->output(new ResultModel(false, "该套餐不存在"));
            }
            if(!$this->find(array('id'=>$vsR->data['stsv_id']),'SetTypeStatusValue')->ret){
                $this->delete($id,'ValueOfMerchantPackageTypeStatus');
                $this->commitTrans();
                $this->output(new ResultModel(true, "该数据存在异常"));
            }
            $data['id'] = $vsR->data['stsv_id'];
            $data['update_time'] = $dateTime;
            $stsvR = $this->save('SetTypeStatusValue',$data);
            if($stsvR->ret){
                $param['admin_id'] = $mchid;
                if($vsR->data['admin_id'] == $mchid){
                    $param['end_time'] = $adminR->data['endtime'];
                    $param['id'] = $vsR->data['id'];
                    $vopR = $this->save('ValueOfMerchantPackageTypeStatus',$param);
                    $this->commitTrans();
                    $this->output(new ResultModel(true, "修改成功"));
                }else{
                    $param['end_time'] = $adminR->data['endtime'];
                    $param['id'] = $vsR->data['id'];
                    $vopR = $this->save('ValueOfMerchantPackageTypeStatus',$param);

                    if($vopR->ret){
                        $this->commitTrans();
                        $this->output(new ResultModel(true, "修改成功"));
                    }else{
                        $this->transRollback();
                        $this->output(new ResultModel(false, "修改失败"));
                    }
                }

            }else{
                $this->transRollback();
                $this->output(new ResultModel(false, "修改失败"));
            }


        }catch(Exception $e){
            $this->transRollback();
            $this->output(new ResultModel(false, "修改失败"));
        }

    }
}
<?php

import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Admin/OrderManagerAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 会员管理模块
 *
 * <AUTHOR>
 */
class MemberManagerAction extends AdminCoreAction
{
    /**
     * 创建车主
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param int $driving_years 驾龄
     * @param datetime $car_register_time 上户时间
     * @param string $name 姓名
     * @param string $car_tail_number 车牌尾号
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $credit 信用额度（默认为0）
     * @param float $balance 账户余额（默认为0)
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doCreateDriver($drive_mode, $total_seating, $start_address_code, $end_address_code, $address_code, $user_name, $cellphone, $password, $repassword, $car_type, $driving_years, $car_register_time, $name, $car_tail_number, $email, $gender, $credit = 0, $balance = 0, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['residual_seating'] = $total_seating;
        $this->data['email'] = $email;
        $this->data['email_f'] = $email;

        $adminR = $this->find(array('is_del' => 0, 'admin_id' => $this->state->user_id), 'Admin', 'admin_id, package_id');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, '账户未登录，请先登录....'));
        }

        $countNum = 0;
        $package = $this->getPackageByPackageId($adminR->data['package_id']); // 获取司机套餐数量
        $business = $this->getNewBusinessPlan($adminR->data['admin_id']); //获取新增的套餐数量
        if (!empty($business)) {
            $countNum = $package['driver'] + $business['driver'];
        } else {
            $countNum = $package->data['driver'];
        }

        $driverR = $this->count(array('is_del' => 0, 'mchid' => $adminR->data['admin_id']), 'Driver');
        if ($driverR->data > $countNum) {
            return $this->output(new \ResultModel(false, '司机人数已经超过最大限制'));
        }

        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '出发地格式不正确或地址不存在'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
            $r = $this->isSameCity($start_address_code, $end_address_code);
            if ($r->ret) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能在同一个城市'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '运营地格式不正确或地址不存在'));
            }
        }
        $this->data['allow_start_address_code'] = $start_address_code;
        $this->data['allow_end_address_code'] = $end_address_code;
        $this->startTrans();
        //添加车主
        $r = $this->add('Driver');
        if ($r->ret) {
            $driver_id = $r->data;
            $r = $this->select(null, null, null, null, 'MetaDriverRemindAction', 'action_id');
            if ($r->ret) {
                foreach ($r->data as $value) {
                    //添加提醒设置
                    $r = $this->add('DriverRemindSet', array('action_id' => $value['action_id'], 'driver_id' => $driver_id));
                    if (!$r->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '车主提醒设置初始化失败'));
                    }
                }
                if ($r->ret) {
                    //充值
                    $r = $this->add('DriverCost', array('driver_id' => $driver_id, 'cost_type' => 1, 'money' => floatval($balance)));
                    if ($r->ret) {
                        $this->commitTrans();
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '车主提醒设置元数据不存在'));
            }
        } else {
            $this->transRollback();
        }
        $this->doAddLog("创建车主");
        return $this->output();
    }

    /**
     * 创建车主
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param datetime $car_register_time 上户时间
     * @param string $name 昵称
     * @param int $driver_type 司机类型
     * @param float $balance 账户余额（默认为0)
     */
    public function doCreateDriverNew($total_seating, $cellphone, $password, $repassword, $car_type, $car_brand = null, $car_tail_number, $car_register_time, $name, $balance = 0, $driver_type, $start_address_code, $end_address_code, $branchid = 0, $sort = 99, $series_id = 0, $driver_group_attribute_id = 0)
    {
        if ($total_seating < 1 or $total_seating > 1000) {
            return $this->output(new \ResultModel(false, '座位数不能小于1，大于1000！'));
        }
        //校验手机号
        if (!$this->mobile($cellphone)) {
            return $this->output(new \ResultModel(false, '请输入中国大陆手机号码'));
        }
        //密码
        if (!$this->isEmpty($password)) {
            return $this->output(new \ResultModel(false, '请输入密码'));
        }
        //车牌号码
        if (!$this->isEmpty($car_tail_number)) {
            return $this->output(new \ResultModel(false, '请输入车牌号'));
        }

        //上户时间
        if (!$this->isEmpty($car_register_time)) {
            return $this->output(new \ResultModel(false, '请填写上户时间'));
        }
        if (!preg_match('/^\d+$/i', $sort)) {
            return $this->output(new \ResultModel(false, "请输入纯数字", null));
        }
        //分台
        if ($branchid == 0) {
            return $this->output(new \ResultModel(false, '请选择分台'));
        }

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            return $this->output(new \ResultModel(false, '司机已存在'));
        }

        $adminR = $this->find(array('is_del' => 0, 'admin_id' => $this->mchid), 'Admin', 'admin_id, package_id');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
        }

        $mchDriverCountR = $this->getMerchantDriverCountByAdminId($this->mchid);
        if (!$mchDriverCountR->ret) {
            return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
        }

        $driverCountR = $this->count(array('status' => 0, 'is_freeze' => 0, 'driver_role_type' => \CommonDefine::DRIVER_ROLE_0, 'mchid' => $adminR->data['admin_id']), 'Driver');
        if ($driverCountR->data >= $mchDriverCountR->data) {
            return $this->output(new \ResultModel(false, '司机人数已经超过最大限制'));
        }

        $this->data['cellphone_f'] = $cellphone;
        $this->data['residual_seating'] = $series_id > 0 ? $total_seating : $total_seating - 1;
        $this->data['status'] = 0;
        $this->data['mchid'] = $this->mchid;
        $this->data['vehicle_id'] = $series_id;

        $this->startTrans();
        //添加车主
        $r = $this->add('Driver');
        if ($r->ret) {
            if ($this->save('Admin', array('admin_id' => $adminR->data['admin_id'], 'driver_num' => ($mchDriverCountR->data - $driverCountR->data) - 1))->ret) {

                if ($driver_group_attribute_id && $driver_group_attribute_id != 'undefined') {
                    $attributeR = $this->find(array('id' => $driver_group_attribute_id, 'mchid' => $this->mchid), 'DriverGroupAttribute', 'id, name, mchid');
                    if (!$attributeR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '司机归属分组属性不存在'));
                    }
                    $result = $this->add('DriverGroupAttributeValue', array(
                        'group_attribute_id' => $driver_group_attribute_id,
                        'name' => $attributeR->data['name'],
                        'driver_id' => $r->data,
                        'mchid' => $attributeR->data['mchid'],
                    ));
                    if (!$result->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '司机归属分组属性添加失败'));
                    }
                }
                $this->commitTrans();
                httpRequest(C('CC_INNER_API_HOST') . "/api/inner/drivers/{$r->data}/vehicles/{$series_id}/bindings", 'post');
                $this->doAddLog("创建车主");

            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '添加司机失败'));
            }
        } else {
            $this->transRollback();
        }
        return $this->output($r);
    }

    /**
     * 创建顺风车车主
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param datetime $car_register_time 上户时间
     * @param string $name 昵称
     * @param int $driver_type 司机类型
     * @param float $balance 账户余额（默认为0)
     * ALTER TABLE `cp_driver` ADD `vehicle_id` INT(11)  NULL  DEFAULT '0'  COMMENT '绑定车辆编号ID，2022.11.11增加'  AFTER `fav_count`;
     */
    public function doCreateFreeRideDriverNew($total_seating, $cellphone, $password, $repassword, $car_type, $car_brand = null, $car_tail_number, $car_register_time, $name, $balance = 0, $driver_type, $start_address_code, $end_address_code, $branchid = 0, $sort = 99, $series_id = 0)
    {
        //校验手机号
        if (!$this->mobile($cellphone)) {
            return $this->output(new \ResultModel(false, '请输入中国大陆手机号码'));
        }
        //密码
        if (!$this->isEmpty($password)) {
            return $this->output(new \ResultModel(false, '请输入密码'));
        }
        //车牌号码
        if (!$this->isEmpty($car_tail_number)) {
            return $this->output(new \ResultModel(false, '请输入车牌号'));
        }
        if ($total_seating < 1 or $total_seating > 1000) {
            return $this->output(new \ResultModel(false, '座位数不能小于1，大于1000！'));
        }
        //上户时间
        if (!$this->isEmpty($car_register_time)) {
            return $this->output(new \ResultModel(false, '请填写上户时间'));
        }
        if (!preg_match('/^\d+$/i', $sort)) {
            return $this->output(new \ResultModel(false, "请输入纯数字", null));
        }
        //分台
        if ($branchid == 0) {
            return $this->output(new \ResultModel(false, '请选择分台'));
        }

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            return $this->output(new \ResultModel(false, '司机已存在'));
        }

        $adminR = $this->find(array('is_del' => 0, 'admin_id' => $this->mchid), 'Admin', 'admin_id, package_id');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
        }

        $mchFreeRideDriverCountR = $this->getMerchantFreeRideDriverCountByAdminId($this->mchid);
        if (!$mchFreeRideDriverCountR->ret) {
            return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
        }

        $driverCountR = $this->count(array('status' => 0, 'is_freeze' => 0, 'driver_role_type' => \CommonDefine::DRIVER_ROLE_1, 'mchid' => $adminR->data['admin_id']), 'Driver');
        if($this->mchid != 116) {
            if ($driverCountR->data >= $mchFreeRideDriverCountR->data) {
                return $this->output(new \ResultModel(false, '顺风车司机人数已经超过最大限制'));
            }
        } else {
            $TempR = $this->find(array('mchid' => 116), 'Temp');
            if(!$TempR->ret) {
                return $this->output(new \ResultModel(false, '司机人数已经超过最大限制'));
            }
            if($TempR->data['num'] <= 0) {
                return $this->output(new \ResultModel(false, '司机人数已经超过最大限制'));
            }
        }

        $this->data['cellphone_f'] = $cellphone;
        $this->data['residual_seating'] = $series_id > 0 ? $total_seating : $total_seating - 1;
        $this->data['status'] = \CommonDefine::EXAMINE_STATUS_0;
        $this->data['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;
        $this->data['mchid'] = $this->mchid;
        $this->data['vehicle_id'] = $series_id;

        $this->startTrans();
        //添加顺风车车主
        $r = $this->add('Driver');
        if ($r->ret) {
            if($this->mchid == 116) {
                $TempR = $this->find(array('mchid' => 116), 'Temp');
                $this->save('Temp', array('id' => $TempR->data['id'], 'num' => $TempR->data['num'] - 1));
                $this->commitTrans();
                $this->doAddLog("创建车主");
                return $this->output($r);
            }
            if ($this->save('Admin', array('admin_id' => $adminR->data['admin_id'], 'free_ride_driver_num' => ($mchFreeRideDriverCountR->data - $driverCountR->data) - 1))->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '添加司机失败'));
            }
        } else {
            $this->transRollback();
        }
        $this->doAddLog("创建顺风车车主");
        httpRequest(C('CC_INNER_API_HOST') . "/api/inner/drivers/{$r->data}/vehicles/{$series_id}/bindings", 'post');
        return $this->output($r);
    }

    /**
     * 添加路线
     * @param string $nickname 线路名称（默认为空）
     * @param string $start_address_remark 出发地址备注（默认为空）
     * @param string $end_address_remark 目的地地址备注（默认为空）
     * @param $start_address_code 出发地编码
     * @param $end_address_code 目的地编码
     * @param int $price 价格（默认为0）
     *
     */
    public function doCreateVirtualNew($nickname = '', $start_address_remark = '', $end_address_remark = '', $start_address_code, $end_address_code, $price = 0)
    {
        $this->data['start_address_code'] = $start_address_code;
        $this->data['end_address_code'] = $end_address_code;
        $this->data['price'] = $price;
        $this->data['state'] = 0;//车主状态 正常
        $this->data['virtual'] = 1;//虚拟司机 是
        $this->data['is_freeze'] = 0;//未删除
        $this->data['mchid'] = $this->state->user_id;
        $this->data['name'] = $nickname;
        $this->data['start_address_remark'] = $start_address_remark;
        $this->data['end_address_remark'] = $end_address_remark;
        $this->startTrans();
        //添加车主
        $r = $this->add('Driver');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("创建路线");
        return $this->output($r);
    }

    /**
     * 获取线路列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function getLineList($fields = null, $page = 1, $size = 10)
    {
        $where['virtual'] = 1;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Driver');
        return $this->output($r);
    }


    /**
     * 获取车主列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $driver_id 车主ID（默认为空，查询所有）
     * @param string $user_name 用户名（默认为空，查询所有）
     * @param string $name 姓名（默认为空，查询所有）
     * @param string $cellphone 手机号（默认为空，查询所有）
     * @param int $start_address_code 出发地（默认为空，查询所有）
     * @param int $end_address_code 目的地（默认为空，查询所有）
     * @param string $car_type 车型（默认为空，查询所有）
     * @param string $car_tail_number 车牌号（默认为空，查询所有）
     * @param int $total_seating 座位数（默认为空，查询所有）
     * @param int $driving_years 驾龄（默认为空，查询所有）
     * @param int $credit 信用度（1-0~50,2-50~100,3-100~500,4-500~1000,5-1000以上，默认为空，查询所有）
     * @param int $state 车主状态（1-等候，2-在路上，3-隐身 默认为空，查询所有）
     * @param int $is_freeze 是否删除（0-未删除，1-已删除，默认为0）
     * @param int $search_branchid 所属分台id（默认为0，查询所有）
     */
    public function getDrivers($fields = null, $page = 1, $size = 10, $driver_id = null, $user_name = null, $name = null, $cellphone = null, $start_address_code = null, $end_address_code = null, $car_type = null, $car_brand = null, $car_tail_number = null, $total_seating = null, $driving_years = null, /*$credit = null,*/
        $state = null, $is_freeze = 0, $search_branchid = 0)
    {
        $where = array('virtual' => 0);
        $where['status'] = 0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_TYPE_0;
        if (!empty($driver_id)) {
            $where['driver_id'] = $driver_id;
        }
        if (!empty($user_name)) {
            $where['user_nmae'] = $user_name;
        }
        if (!empty($name)) {
            $where['name'] = array('LIKE', "%$name%");
        }
        if (!empty($cellphone)) {
            $where['cellphone'] = array('LIKE', "%$cellphone%");
        }

        if (isset($car_brand) && !empty($car_brand)) {
            $where['car_brand'] = array('LIKE', "%$car_brand%");
        }

        if(!empty($car_tail_number)) {
            $car_tail_number = trim($car_tail_number);
            $where['car_tail_number'] = array('LIKE',"$car_tail_number%");
        }

        if (!empty($total_seating)) {
            $where['total_seating'] = $total_seating;
        }
        if (!empty($driving_years)) {
            $where['driving_years'] = $driving_years;
        }
        /*        if (!empty($credit)) {
                    $where['credit'] = $credit;
                }*/
        if (!empty($state)) {
            $where['state'] = $state;
        }
        if (isset($is_freeze) && $is_freeze !== '' && !is_null($is_freeze)) {
            $where['is_freeze'] = intval($is_freeze);
        }
        if (!empty($search_branchid)) {
            $where['branchid'] = $search_branchid;
        }
        if ($this->admin_group_id == 3) {
            $where['branchid'] = $this->state->user_id;
        } elseif ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
            if (!empty($search_branchid)) {
                $where['branchid'] = $search_branchid;
            }
        }
        $r = $this->select($where, $page, $size, 'driver_id desc', 'Driver', 'driver_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("driver_id", $value['driver_id'], 'Driver\\Account', 'getDriver', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                    $driverQrR = $this->find(array('driver_id' => $value['driver_id']), 'DriverQrAttachment');
                    $r->data[$key]['driver_qr_url'] = $driverQrR->ret ? $driverQrR->data['qr_url'] : "";
                } else {
                    return $this->output($t);
                }
            }
            foreach ($r->data as $key => $item) {
                if (empty($item['sort'])) {
                    $r->data[$key]['sort'] = "";
                }
            }
        }
        $this->doAddLog("查看车主列表");
        return $this->output($r);
    }

    public function doUpdateState($state, $driver_id)
    {
        $datas['driver_id'] = $driver_id;
        if($state==0) {
            //接单中,判断是否有未处理的订单
            $where['state'] = array('in', '1,2,3,4');
            $where['driver_id'] = $driver_id;
            # 5.31增加支持逻辑订单逻辑删除
            $where['_string'] = " deleted_at IS NULL ";
            $orderR = $this->find($where, 'Order');
            if($orderR->ret) {
                if($orderR->data['state'] == 1) {
                    return $this->output(new \ResultModel(false, '该司机还有未处理的派单,请联系该司机及时处理'));
                } else {
                    return $this->output(new \ResultModel(false, '该司机还有进行中的订单未处理'));
                }
            }
            $datas['state']=3;
        } else {
            $datas['state']=0;
        }
        $r = $this->find(['driver_id' => $driver_id], 'Driver');
        if (!$r->ret) {
            return $this->output(new ResultModel(false, '没有找到该司机'));
        }
        $res = $this->save('Driver', $datas);
        return $this->output($res);
    }

    public function doUpdateDrType($type, $id)
    {
        //司机套餐数量判断
        $mchR = $this->find(['admin_id' => $this->mchid], 'Admin');
        if (!$mchR->ret) {
            return $this->output(new ResultModel(false, '系统异常'));
        }

        if ($id == 1) {//转为顺风车
            if ($mchR->data['free_ride_driver_num'] <= 0) {
                return $this->output(new ResultModel(false, '转换失败，顺风车司机数量不足'));
            }
            $mchS = $this->save('Admin', ['admin_id' => $mchR->data['admin_id'],
                'free_ride_driver_num' => ($mchR->data['free_ride_driver_num'] - 1),
                'driver_num' => ($mchR->data['driver_num'] + 1)
            ]);
            if (!$mchS->ret) {
                return $this->output(new ResultModel(false, '转换失败'));
            }
            $this->doAddLog('转换为顺风车司机');
        } else {//转为转职司机
            if ($mchR->data['driver_num'] <= 0) {
                return $this->output(new ResultModel(false, '转换失败，专职司机数量不足'));
            }
            $mchS = $this->save('Admin', ['admin_id' => $mchR->data['admin_id'],
                'driver_num' => ($mchR->data['driver_num'] - 1),
                'free_ride_driver_num' => ($mchR->data['free_ride_driver_num'] + 1),
            ]);
            if (!$mchS->ret) {
                return $this->output(new ResultModel(false, '转换失败'));
            }
            $this->doAddLog('转换为专职司机');
        }

        $datas['driver_role_type'] = $type;
        $datas['driver_id'] = $id;
        $r = $this->find(['mchid' => $this->mchid, 'driver_id' => $id], 'Driver');
        if (!$r->ret) {
            return $this->output(new ResultModel(false, '没有找到该司机'));
        }

        $res = $this->save('Driver', $datas);
        return $this->output($res);
    }

    /**
     * 获取车主列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $driver_id 车主ID（默认为空，查询所有）
     * @param string $user_name 用户名（默认为空，查询所有）
     * @param string $name 姓名（默认为空，查询所有）
     * @param string $cellphone 手机号（默认为空，查询所有）
     * @param int $start_address_code 出发地（默认为空，查询所有）
     * @param int $end_address_code 目的地（默认为空，查询所有）
     * @param string $car_type 车型（默认为空，查询所有）
     * @param string $car_tail_number 车牌号（默认为空，查询所有）
     * @param int $total_seating 座位数（默认为空，查询所有）
     * @param int $driving_years 驾龄（默认为空，查询所有）
     * @param int $credit 信用度（1-0~50,2-50~100,3-100~500,4-500~1000,5-1000以上，默认为空，查询所有）
     * @param int $state 车主状态（1-等候，2-在路上，3-隐身 默认为空，查询所有）
     * @param int $is_freeze 是否删除（0-未删除，1-已删除，默认为0）
     * @param int $search_branchid 所属分台id（默认为0，查询所有）
     */
    public function getFreeRideDrivers($fields = null, $page = 1, $size = 10, $driver_id = null, $user_name = null, $name = null, $cellphone = null, $start_address_code = null, $end_address_code = null, $car_type = null, $car_brand = null, $car_tail_number = null, $total_seating = null, $driving_years = null, /*$credit = null,*/
        $state = null, $is_freeze = 0, $search_branchid = 0)
    {
        $where['virtual'] = 0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;
        $where['status'] = 0;
        if (!empty($driver_id)) {
            $where['driver_id'] = $driver_id;
        }
        if (!empty($user_name)) {
            $where['user_name'] = $user_name;
        }
        if (!empty($name)) {
            $where['name'] = array('LIKE', "%$name%");
        }
        if (!empty($cellphone)) {
            $where['cellphone'] = array('LIKE', "%$cellphone%");
        }

        if (isset($car_brand) && !empty($car_brand)) {
            $where['car_brand'] = array('LIKE', "%$car_brand%");
        }

        if(!empty($car_tail_number)) {
            $car_tail_number = trim($car_tail_number);
            $where['car_tail_number'] = array('LIKE',"$car_tail_number%");
        }

        if (!empty($total_seating)) {
            $where['total_seating'] = $total_seating;
        }
        if (!empty($driving_years)) {
            $where['driving_years'] = $driving_years;
        }
        /*        if (!empty($credit)) {
                    $where['credit'] = $credit;
                }*/
        if (!empty($state)) {
            $where['state'] = $state;
        }
        if (isset($is_freeze) && $is_freeze !== '' && !is_null($is_freeze)) {
            $where['is_freeze'] = intval($is_freeze);
        }
        if ($this->admin_group_id == 3) {
            $where['branchid'] = $this->state->user_id;
        } elseif ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
            if (!empty($search_branchid)) {
                $where['branchid'] = $search_branchid;
            }
        }
        $r = $this->select($where, $page, $size, 'driver_id desc', 'Driver', 'driver_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("driver_id", $value['driver_id'], 'Driver\\Account', 'getDriver', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
            foreach ($r->data as $key => $item) {
                if (empty($item['sort'])) {
                    $r->data[$key]['sort'] = "";
                }
            }
        }
        $this->doAddLog("查看顺风车车主列表");
        return $this->output($r);
    }

    /**
     * 获取待审核车主列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $driver_id 车主ID（默认为空，查询所有）
     * @param string $user_name 用户名（默认为空，查询所有）
     * @param string $name 姓名（默认为空，查询所有）
     * @param string $cellphone 手机号（默认为空，查询所有）
     * @param int $start_address_code 出发地（默认为空，查询所有）
     * @param int $end_address_code 目的地（默认为空，查询所有）
     * @param string $car_type 车型（默认为空，查询所有）
     * @param string $car_tail_number 车牌号（默认为空，查询所有）
     * @param int $total_seating 座位数（默认为空，查询所有）
     * @param int $driving_years 驾龄（默认为空，查询所有）
     * @param int $credit 信用度（1-0~50,2-50~100,3-100~500,4-500~1000,5-1000以上，默认为空，查询所有）
     * @param int $state 车主状态（1-等候，2-在路上，3-隐身 默认为空，查询所有）
     * @param int $is_freeze 是否删除（0-未删除，1-已删除，默认为0）
     * @param int $driver_role_type 司机角色类型（1-顺风车）
     */
    public function getExamineDrivers($fields = null, $page = 1, $size = 10, $real_name = null, $name = null, $cellphone = null, $status = null, $driver_role_type = null)
    {
        $where['from_type'] = \CommonDefine::FROM_TYPE_1;
        if (!empty($real_name)) {
            $where['real_name'] = $real_name;
        }

        if (!empty($name)) {
            $where['name'] = array('LIKE', "%{$name}%");
        }
        if (!empty($cellphone)) {
            $where['cellphone'] = $cellphone;
        }

        if (!empty($driver_role_type)) {
            $where['driver_role_type'] = $driver_role_type;
        } else {
            $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;
        }

        if ($status != "") {
            $where['status'] = $status;
        }

        if ($this->admin_group_id == 3) {
            $where['branchid'] = $this->state->user_id;
        } elseif ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
        }

        $r = $this->select($where, $page, $size, 'driver_id desc', 'Driver', 'driver_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("driver_id", $value['driver_id'], 'Driver\\Account', 'getDriver', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                    switch ($r->data[$key]['status']) {
                        case \CommonDefine::EXAMINE_STATUS_0:
                            $r->data[$key]['status_msg'] = '<b style="color: green">审核已通过</b>';
                            break;
                        case \CommonDefine::EXAMINE_STATUS_1:
                            $r->data[$key]['status_msg'] = '<b style="color: orange">审核中</b>';
                            break;
                        case \CommonDefine::EXAMINE_STATUS_2:
                            $r->data[$key]['status_msg'] = '<b style="color: red">审核未通过</b>';
                            break;
                        case \CommonDefine::EXAMINE_STATUS_3:
                            $r->data[$key]['status_msg'] = '<b style="color: darkblue">资料提交中</b>';
                            break;
                    }
                } else {
                    return $this->output($t);
                }
            }
        }
        $this->doAddLog("查看申请入驻的司机列表");
        return $this->output($r);
    }


    /**
     * 添加分台
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $mchname 分台昵称
     * @param int $mchid 商户id
     * @param int $branch_type 分台类型
     */
    public function doCreateBranch($cellphone, $password, $mchname, $mchid, $branch_type)
    {
        if (!$this->mobile($cellphone)) {
            return $this->output(new \ResultModel(false, "请输入中国大陆手机号码"));
        }
        if (!$this->isEmpty($password)) {
            return $this->output(new \ResultModel(false, "请输入用户密码"));
        }
        if (!$this->isEmpty($mchname)) {
            return $this->output(new \ResultModel(false, "请输入分台昵称"));
        }
        $admin = $this->find(array('cellphone' => $cellphone, 'parent_admin_id' => $mchid, 'is_del' => 0), 'Admin');
        if ($admin->ret) {
            return $this->output(new \ResultModel(false, '该电话号码的分台已存在'));
        }
        $this->data['parent_admin_id'] = $mchid;
        unset($this->data['mchid']);
        $this->data['group_id'] = 3;
        $this->data['account'] = $cellphone;
        $r = $this->add('Admin', $this->data);
        if ($r->ret) {
            $this->createBranchQr($mchid, $r->data);
        }
        return $this->output($r);
    }

    /**
     * 修改分台
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $mchname 分台昵称
     * @param string $mchname 分台昵称
     * @param int $branchid 分台id
     * @param int $branch_type 分台类型
     */
    public function doUpdateBranch($cellphone, $password, $mchname, $branchid, $branch_type)
    {
        if (!$this->mobile($cellphone)) {
            return $this->output(new \ResultModel(false, "请输入中国大陆手机号码"));
        }
        if (!$this->isEmpty($password)) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
        }
        if (!$this->isEmpty($mchname)) {
            return $this->output(new \ResultModel(false, "请输入分台昵称"));
        }

        $branchR = $this->find(array('cellphone' => $cellphone, 'parent_admin_id' => $this->mchid, 'is_del' => 0), 'Admin');
        if ($branchR->ret) {
            if($branchid != $branchR->data['admin_id']) {
                return $this->output(new \ResultModel(false, '该电话号码的分台已存在'));
            }
        }

        $admin = $this->find(array('admin_id' => $branchid, 'is_del' => 0), 'Admin');
        if (!$admin->ret) {
            return $this->output(new \ResultModel(false, '分台不存在'));
        }

        if($admin->data['branch_type'] != $branch_type) {
            if($this->checkIsLineCharterCustomBranch($branchid)->ret) {
                return $this->output(new ResultModel(false, '请先到配置开关页面将自定义包车调度分台切换为其他线路分台后再尝试修改分台类型'));
            }
        }

        $this->data['admin_id'] = $branchid;
        if ($admin->data['account'] != $cellphone) {
            $this->data['account'] = $cellphone;
        }
        $r = $this->save('Admin', $this->data);
        if ($r->ret) {
            $this->createBranchQr($admin->data['parent_admin_id'], $branchid);
        }
        return $this->output($r);
    }


    /**
     * 获取分台与渠道管理
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getBranchs($fields = null, $page = 1, $size = 10, $bphone = null, $branch_name = null, $branch_type = null)
    {
        $mchid = $this->state->user_id;

        if (!empty($branch_name)) {
            $where['mchname'] = array('like', "%" . $branch_name . "%");
        }
        if (!empty($bphone)) {
            $where['cellphone'] = $bphone;
        }

        if (!empty($branch_type) || is_numeric($branch_type)) {
            $where['branch_type'] = $branch_type;
        }

        if ($this->admin_group_id == 2) {
            $where['parent_admin_id'] = $this->state->user_id;  //总台
        } else {
            return $this->output(new ResultModel(false));
        }
        /*$where['is_del'] = array('exp','is null');*/
        $where['is_del'] = array('eq', '0');
        $where['is_freeze'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Admin', $fields);
        if ($r->ret) {
            foreach ($r->data as $k => $v) {
                if (!isset($v['balance'])) {
                    $r->data[$k]['balance'] = "0.00";
                }

                if ($v['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                    $r->data[$k]['branch_type_message'] = "<span style='color: darkgreen'>渠道代约分台</span>";
                    $branchQrR = $this->find(array('branchid' => $v['admin_id']), 'BranchQrAttachment');
                    $r->data[$k]['branch_qr_url'] = $branchQrR->ret ? $branchQrR->data['qr_url'] : "";
                    $r->data[$k]['branch_qr_code'] =  $branchQrR->ret ? $branchQrR->data['qr_code'] : "";
                } else {
                    $r->data[$k]['branch_type_message'] = "<span style='color: red'>线路管理分台</span>";
                    $r->data[$k]['branch_qr_url'] = "";
                    $r->data[$k]['branch_qr_code'] =  "";
                }

                $branchTurnoverR = $this->find('branchid =>'.$v['admin_id'], 'BranchTurnoverStatistics');
                $r->data[$k]['generation_info'] =
                    '<p style="color:red;">总额：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_amount'] ? $branchTurnoverR->data['turnover_amount'] : 0) : 0) .
                    '元</p><p style="color:lightgray;">线上代付：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_on_amount'] ? $branchTurnoverR->data['turnover_on_amount'] : 0) : 0) .
                    '元</p><p style="color:lightgray;">线下付款：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_down_amount'] ? $branchTurnoverR->data['turnover_down_amount'] : 0) : 0) .
                    '元</p>';
            }
        }
        return $this->output($r);
    }

    /**
     * 获取分台与渠道管理
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getFreeBranchs($fields = null, $page = 1, $size = 10, $mchid, $bphone = null, $branch_name = null, $branch_type = null)
    {
        $where['parent_admin_id'] = $mchid;
        /*$where['is_del'] = array('exp','is null');*/
        if(!empty($bphone)) {
            $where['cellphone'] = $bphone;
        }
        if(!empty($branch_name)) {
            $where['mchname'] = ['like','%'.trim($branch_name).'%'];
        }
        $where['is_del'] = array('eq', '0');
        $where['is_freeze'] = 1;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Admin', $fields);
        foreach ($r->data as $k => $v) {
            if ($v['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                $r->data[$k]['branch_type_message'] = "<span style='color: darkgreen'>渠道代约分台</span>";
            } else {
                $r->data[$k]['branch_type_message'] = "<span style='color: red'>线路管理分台</span>";
            }

            $branchTurnoverR = $this->find('branchid =>'.$v['admin_id'], 'BranchTurnoverStatistics');
            $r->data[$k]['generation_info'] =
                '<p style="color:red;">总额：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_amount'] ? $branchTurnoverR->data['turnover_amount'] : 0) : 0) .
                '元</p><p style="color:lightgray;">线上代付：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_on_amount'] ? $branchTurnoverR->data['turnover_on_amount'] : 0) : 0) .
                '元</p><p style="color:lightgray;">线下付款：' . ($branchTurnoverR->ret ? ($branchTurnoverR->data['turnover_down_amount'] ? $branchTurnoverR->data['turnover_down_amount'] : 0) : 0) .
                '元</p>';
        }
        return $this->output($r);
    }

    /**
     * 获取指派列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $order_id 订单id（默认为空，查询所有）
     * @param int $driver_id 车主ID（默认为空，查询所有）
     * @param string $dcellphone 司机电话（默认为空，查询所有）
     * @param string $car_tail_number 司机车牌（默认为空，查询所有）
     * @param string $dname 司机姓名姓名（默认为空，查询所有）
     * @param int $type 【1001:推荐；1002:班次；1003：距离】
     * @param int $line_condition 0：线路无关 1：线路相关
     */
    public function getAppointDrivers($fields = null, $page = 1, $size = 10, $order_id, $driver_id = null, $dcellphone = null, $car_tail_number = null, $dname = null, $type = 1001, $line_condition = 0)
    {
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if(!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常', null));
        }

        $lineType = 0;
        if($orderR->data['type'] == 1) {//拼车
            $lineType = 1;
        } elseif($orderR->data['type'] == 2) {
            $lineType = 2;
        } elseif($orderR->data['type'] == 3) {
            $lineType = 3;
        } elseif($orderR->data['type'] == 4) {
            $lineType = 4;
        } elseif($orderR->data['type'] == 5) {
            $lineType = 5;
        }

        $start_latitude = $orderR->data['start_latitude'];
        $start_longitude = $orderR->data['start_longitude'];
        $distanceField = "GETDISTANCE(d.latitude,d.longitude,d.latitude,d.longitude) AS distance,";
        if($start_latitude && $start_longitude) {
            $distanceField = 'GETDISTANCE(d.latitude,d.longitude,'.$start_latitude.','.$start_longitude.') AS distance,';
        }

        $where['d.branchid'] = $orderR->data['branchid'];
        $where['d.state'] = array('eq', 0);
        $where['d.is_freeze'] = array('eq', \CommonDefine::IS_FREEZE_0);
        //        $where['d.from_type'] = array('eq', \CommonDefine::FROM_TYPE_0);
        $where['d.driver_role_type'] = array('eq', \CommonDefine::DRIVER_ROLE_0);
        if($dname) {
            $where['d.name'] = array('like', '%'.trim($dname, ' ').'%');
        }
        if($dcellphone) {
            $dcellphone = trim($dcellphone);
            $where['d.cellphone'] = array('like', "%$dcellphone%");
        }
        if($car_tail_number) {
            $car_tail_number = trim($car_tail_number);
            $where['d.car_tail_number'] = array('like', "%$car_tail_number%");
        }

        $db = M();
        $data = "";

        $order = "distance ASC,d.driver_id ASC";//默认推荐
        if($type == 1001) {//推荐
            $order = "distance ASC,d.driver_id ASC";
            $where['d.residual_seating'] = array('EGT', $orderR->data['book_seating']);
        } elseif($type == 1002) {//班次
            $order = 'd.sort ASC';
        } elseif($type == 1003) {//距离
            $order = "distance ASC,d.driver_id ASC";
        }

        if($line_condition == 1 && !empty($lineType)) {
            if($lineType == \CommonDefine::ORDER_TYPE_5) {
                $where['o.line_id'] = $orderR->data['line_id'];
                $totalData = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_line_class_train_driver lctd ON o.line_id = lctd.line_class_train_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = lctd.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                    ->where($where)->order($order)->group("d.driver_id")->select();
                $data = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_line_class_train_driver lctd ON o.line_id = lctd.line_class_train_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = lctd.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort,o.virtual_driver')
                    ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
            } else {
                $where['dl.type'] = $lineType;
                $where['dl.line_id'] = $orderR->data['line_id'];
                $totalData = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_driver_line dl ON o.line_id = dl.line_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                    ->where($where)->order($order)->group("d.driver_id")->select();
                $data = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_driver_line dl ON o.line_id = dl.line_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort,o.virtual_driver')
                    ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
            }
        } else {
            $where['o.order_id'] = $orderR->data['order_id'];
            $totalData = $db->table("cp_driver d")
                ->join("LEFT JOIN cp_order o ON o.branchid = d.branchid")
                ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                ->where($where)->order($order)->group("d.driver_id")->select();
            $data = $db->table("cp_driver d")
                ->join("LEFT JOIN cp_order o ON o.branchid = d.branchid")
                ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort,o.virtual_driver')
                ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
        }

        $this->doAddLog("订单指派");
        if($data) {
            $count = count($totalData);
            foreach($data as $key => $item) {
                $data[$key]['distance'] = number_format(($item['distance'] / 1000), 2);
                $virvirtualDrivers = json_decode($item['virtual_driver'], true);

                foreach ($virvirtualDrivers as $driver) {
                    if ($item['driver_id'] == $driver['driver']['id']) {
                        unset($data[$key]);
                    }
                }

            }
            return $this->output(new ResultModel(true, $data, $count));
        } else {
            return $this->output(new ResultModel(false, null, null));
        }
    }


    /**
     * 获取司机列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param string $account 推荐人手机号
     * @param string $account_type 0-司机推荐；1-乘客推荐；
     */
    public function getRecomendDriverList($fields = null, $page = 1, $size = 10, $cellphone, $account_type)
    {
        if (!$cellphone || $cellphone == "") {
            return $this->output(new ResultModel(false, '获取失败'));
        }
        $where['recommend'] = $cellphone;
        $where['account_type'] = $account_type;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Driver', $fields);

        if ($account_type == 0) {
            $this->doAddLog("查看司机" . $cellphone . "的推荐列表");
        } else {
            $this->doAddLog("查看乘客" . $cellphone . "的推荐列表");
        }

        return $this->output($r);
    }


    /**
     * 创建乘客
     * @param string $user_name 用户名
     * @param string $password 密码
     * @param string $cellphone 手机号码
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param int $credit 信用额度（默认为0）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doCreatePassenger($user_name, $password, $cellphone, $email, $gender, $drive_mode, $start_address_code, $end_address_code, $address_code, $credit = 0, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['email_f'] = $email;
        $this->data['password'] = md5($password);
        $this->data['repassword'] = md5($password);
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '出发地格式不正确或地址不存在'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '运营地格式不正确或地址不存在'));
            }
        }
        $this->startTrans();
        //创建乘客用户
        $r = $this->add('Passenger');
        if ($r->ret) {
            $passenger_id = $r->data;
            $r = $this->select(null, null, null, null, 'MetaPassengerRemindAction', 'action_id');
            if ($r->ret) {
                foreach ($r->data as $value) {
                    //添加提醒设置
                    $r = $this->add('PassengerRemindSet', array('action_id' => $value['action_id'], 'passenger_id' => $passenger_id));
                    if (!$r->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '乘客提醒设置初始化失败'));
                    }
                }
                if ($r->ret) {
                    $this->commitTrans();
                    \StateModel::save($passenger_id, $password, \StateModel::$PASSENGER);
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '乘客提醒设置元数据不存在'));
            }
        } else {
            $this->transRollback();
        }
        $this->doAddLog("新建乘客");
        return $this->output($r);
    }

    /**
     * 创建乘客
     * @param string $name 用户名
     * @param string $password 密码
     * @param string $cellphone 手机号码
     */
    public function doCreatePassengerNew($name, $password, $cellphone)
    {
        if (!$this->isEmpty($name)) {
            return $this->output(new \ResultModel(false, "乘客名称不能为空"));
        }
        if (!$this->isEmpty($password)) {
            return $this->output(new \ResultModel(false, "密码不能为空"));
        }
        if (!$this->mobile($cellphone)) {
            return $this->output(new \ResultModel(false, "请输入中国大陆手机号码"));
        }
        $this->data['cellphone_f'] = $cellphone;
        $this->data['password'] = md5($password);
        $this->data['repassword'] = md5($password);
        $this->data['mchid'] = $this->state->user_id;


        $passenger = $this->find(array('cellphone' => $cellphone, 'is_del' => 0), 'Passenger');
        if ($passenger->data) {
            return $this->output(new \ResultModel(false, '乘客已存在'));
        }

        $this->startTrans();
        //创建乘客用户
        $r = $this->add('Passenger');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("新建乘客");
        return $this->output($r);
    }

    /**
     * 获取乘客列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $passenger_id 乘客ID（默认为空，查询所有）
     * @param string $name 昵称（默认为空，查询所有）
     * @param string $cellphone 手机（默认为空，查询所有）
     * @param date $min_register_time 最小注册时间（默认为空，查询所有）
     * @param date $max_register_name 最大注册时间（默认为空，查询所有）
     * @param int $credit 信用度（1-0~50,2-50~100,3-100~500,4-500~1000,5-1000以上，默认为空，查询所有）
     * @param int $state 乘客状态（1-上车，2-下车，3-隐身）
     * @param int $is_freeze 是否删除（0-未删除，1-已删除，默认为0）
     */
    public function getPassengers($fields = null, $page = 1, $size = 10, $passenger_id = null, $name = null, $cellphone = null, $min_register_time = null, $max_register_time = null, $credit = null, $state = null, $is_freeze = 0)
    {
        $where = array();
        if (!empty($passenger_id)) {
            $where['passenger_id'] = $passenger_id;
        }
        if (!empty($name)) {
            $where['name'] = $name;
        }
        if (!empty($cellphone)) {
            $where['cellphone'] = $cellphone;
        }
        if (!empty($min_register_time) && empty($max_register_time)) {
            $where['create_time'] = array('egt', $min_register_time);
        } elseif (empty($min_register_time) && !empty($max_register_time)) {
            $where['create_time'] = array('elt', $max_register_time);
        } elseif (!empty($min_register_time) && !empty($max_register_time)) {
            $where['create_time'] = array(array('egt', $min_register_time), array('elt', $max_register_time));
        }
        switch (intval($credit)) {
            case 1:
                $where['credit'] = array(array('egt', 0), array('elt', 50));
                break;
            case 2:
                $where['credit'] = array(array('egt', 50), array('elt', 100));
                break;
            case 3:
                $where['credit'] = array(array('egt', 100), array('elt', 500));
                break;
            case 4:
                $where['credit'] = array(array('egt', 500), array('elt', 1000));
                break;
            case 5:
                $where['credit'] = array('egt', 1000);
        }
        if (!empty($state)) {
            $where['state'] = intval($state);
        }
        if ($is_freeze !== '' && !is_null($is_freeze)) {
            $where['is_freeze'] = intval($is_freeze);
        }
        $uid = $this->state->user_id;
        $where['mchid'] = $uid;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Passenger', 'passenger_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("passenger_id", $value['passenger_id'], 'Passenger\\Account', 'getPassenger', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }
        $this->doAddLog("查看乘客管理");
        return $this->output($r);
    }

    /**
     * 获取待认证乘客列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $passenger_id 乘客ID（默认为空，查询所有）
     * @param string $name 昵称（默认为空，查询所有）
     * @param string $cellphone 手机（默认为空，查询所有）
     * @param date $min_register_time 最小注册时间（默认为空，查询所有）
     * @param date $max_register_name 最大注册时间（默认为空，查询所有）
     * @param int $credit 信用度（1-0~50,2-50~100,3-100~500,4-500~1000,5-1000以上，默认为空，查询所有）
     * @param int $state 乘客状态（1-上车，2-下车，3-隐身）
     * @param int $is_freeze 是否删除（0-未删除，1-已删除，默认为0）
     */
    public function getExaminePassengers($fields = null, $page = 1, $size = 10, $passenger_id = null, $name = null, $cellphone = null, $min_register_time = null, $max_register_time = null, $credit = null, $state = null, $is_freeze = 0)
    {
        $where['examine_status'] = array('exp','is not null');
        if (!empty($passenger_id)) {
            $where['passenger_id'] = $passenger_id;
        }
        if (!empty($name)) {
            $where['name'] = array('like', '%'.$name.'%');
        }
        if (!empty($real_name)) {
            $where['real_name'] = array('like', '%'.$real_name.'%');
        }
        if (!empty($cellphone)) {
            $where['cellphone'] = $cellphone;
        }
        if (!empty($min_register_time) && empty($max_register_time)) {
            $where['create_time'] = array('egt', $min_register_time);
        } elseif (empty($min_register_time) && !empty($max_register_time)) {
            $where['create_time'] = array('elt', $max_register_time);
        } elseif (!empty($min_register_time) && !empty($max_register_time)) {
            $where['create_time'] = array(array('egt', $min_register_time), array('elt', $max_register_time));
        }

        if ($is_freeze !== '' && !is_null($is_freeze)) {
            $where['is_freeze'] = intval($is_freeze);
        }
        $uid = $this->state->user_id;
        $where['mchid'] = $uid;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Passenger', 'passenger_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("passenger_id", $value['passenger_id'], 'Passenger\\Account', 'getPassenger', $fields);
                if ($t->ret) {
                    if(isset($t->data['passenger_type_id'])) {
                        $passengerTypeR = $this->find(array('passenger_type_id' => $t->data['passenger_type_id']), 'PassengerType');
                        $t->data['passenger_type_msg'] = $passengerTypeR->ret ? $passengerTypeR->data['name'] : "普通账户";
                    }
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }
        $this->doAddLog("查看待认证乘客");
        return $this->output($r);
    }


    /**
     * 修改车主
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param int $driving_years 驾龄
     * @param datetime $car_register_time 上户时间
     * @param string $name 姓名
     * @param string $car_tail_number 车牌尾号
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $credit 信用额度（默认为0）
     * @param float $balance 账户余额（默认为0)
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdateDriver($driver_id, $drive_mode, $total_seating, $start_address_code, $end_address_code, $address_code, $user_name, $cellphone, $password, $repassword, $car_type, $driving_years, $car_register_time, $name, $car_tail_number, $email, $gender, $credit = 0, $balance = 0, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['residual_seating'] = $total_seating;
        $this->data['email'] = $email;
        $this->data['email_f'] = $email;
        if (empty($password)) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
            $this->data['repassword'] = md5($repassword);
        }
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '出发地格式不正确或地址不存在'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
            $r = $this->isSameCity($start_address_code, $end_address_code);
            if ($r->ret) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能在同一个城市'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '运营地格式不正确或地址不存在'));
            }
        }
        $this->data['allow_start_address_code'] = $start_address_code;
        $this->data['allow_end_address_code'] = $end_address_code;
        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'balance');
        if ($r->ret) {
            $pre_balance = floatval($r->data['balance']);
            $balance = floatval($balance);
            $balance = $balance - $pre_balance;
            $this->startTrans();
            $r = $this->save('Driver');
            if ($r->ret) {
                //充值
                $r = $this->add('DriverCost', array('driver_id' => $driver_id, 'cost_type' => 1, 'money' => $balance));
                if ($r->ret) {
                    $this->commitTrans();
                } else {
                    $this->transRollback();
                }
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new ResultModel(false, '车主ID不正确'));
        }
        $this->doAddLog("修改车主");
        return $this->output($r);
    }

    /**
     * 修改车主
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param int $driving_years 驾龄
     * @param datetime $car_register_time 上户时间
     * @param string $name 姓名
     * @param string $car_tail_number 车牌尾号
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $credit 信用额度（默认为0）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param int $driver_type 司机类型
     */
    public function doUpdateDriverNew($driver_id, $total_seating, $cellphone, $password, $repassword, $car_tail_number, $car_type, $car_brand, $car_register_time, $name, $start_address_code = null, $end_address_code = null, $sort = 99, $driver_type, $branchid, $driver_group_attribute_id = 0)
    {
        /*$this->data['cellphone_f'] = $cellphone;*/
        if ($total_seating < 1 or $total_seating > 1000) {
            return $this->output(new \ResultModel(false, '座位数不能小于1，大于1000！'));
        }
        $this->data['residual_seating'] = $total_seating - 1;
        if (empty($password) || !$password) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
            $this->data['repassword'] = md5($repassword);
        }
        if (!$this->isEmpty($car_tail_number)) {
            $this->data['car_tail_number'] = $car_tail_number;
        }
        if (!$start_address_code || $start_address_code == "") {
            unset($this->data['start_address_code']);
        }

        if (!$end_address_code || $end_address_code == "") {
            unset($this->data['end_address_code']);
        }
        if (!preg_match('/^\d+$/i', $sort)) {
            return $this->output(new \ResultModel(false, "请输入纯数字", null));
        }

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            if($driver_id != $driverR->data['driver_id']) {
                return $this->output(new \ResultModel(false, '司机已存在'));
            }
        }

        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'balance,cellphone,state');
        if ($r->ret) {

            if($this->mchid != 1116) {//斑马快跑
                if($r->data['state'] != 3) {
                    return $this->output(new \ResultModel(false, '修改失败！请先联系司机暂停接单后，再修改司机的基础信息'));
                }
            }

            if ($cellphone == $r->data['cellphone']) {
                unset($this->data['cellphone']);
            }
            $this->startTrans();
            $r = $this->save('Driver');
            if ($r->ret) {

                if ($driver_group_attribute_id && $driver_group_attribute_id != 'undefined') {
                    $attributeR = $this->find(array('id' => $driver_group_attribute_id, 'mchid' => $this->mchid), 'DriverGroupAttribute', 'id, name, mchid');
                    if (!$attributeR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '司机归属分组属性不存在'));
                    }
                    $attributeValueR = $this->find(array('driver_id' => $driver_id, 'mchid' => $this->mchid), 'DriverGroupAttributeValue');
                    if (!$attributeValueR->ret) {
                        $result = $this->add('DriverGroupAttributeValue', array(
                            'group_attribute_id' => $driver_group_attribute_id,
                            'name' => $attributeR->data['name'],
                            'driver_id' => $driver_id,
                            'mchid' => $attributeR->data['mchid'],
                        ));
                    } else {
                        $result = $this->save('DriverGroupAttributeValue', array(
                            'group_attribute_id' => $driver_group_attribute_id,
                            'name' => $attributeR->data['name'],
                            'driver_id' => $driver_id,
                            'mchid' => $attributeR->data['mchid'],
                            'id' => $attributeValueR->data['id'],
                        ));
                    }
                    if (!$result->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '司机归属分组属性添加失败'));
                    }
                }


                $this->commitTrans();
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new ResultModel(false, '车主ID不正确'));
        }
        //        file_get_contents("http://c.higgses.com/Driver/Account/doReduce");
        $this->doAddLog("修改车主", json_encode($this->data));
        return $this->output($r);
    }

    /**
     * 修改顺风车车主
     * @param int $driver_id 车主ID
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $total_seating 总的座位数（4,7,8）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param string $user_name 用户名
     * @param string $cellphone 手机号码
     * @param string $password 密码
     * @param string $repassword 确认密码
     * @param string $car_type 车型
     * @param int $driving_years 驾龄
     * @param datetime $car_register_time 上户时间
     * @param string $name 姓名
     * @param string $car_tail_number 车牌尾号
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $credit 信用额度（默认为0）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     * @param int $driver_type 司机类型
     */
    public function doUpdateFreeRideDriverNew($driver_id, $total_seating, $cellphone, $password, $repassword, $car_tail_number, $car_type, $car_brand, $car_register_time, $name, $start_address_code = null, $end_address_code = null, $sort = 99, $driver_type, $branchid)
    {
        /*$this->data['cellphone_f'] = $cellphone;*/
        if ($total_seating < 1 or $total_seating > 1000) {
            return $this->output(new \ResultModel(false, '座位数不能小于1，大于1000！'));
        }
        $this->data['residual_seating'] = $total_seating - 1;
        if (empty($password) || !$password) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
            $this->data['repassword'] = md5($repassword);
        }
        if (!$this->isEmpty($car_tail_number)) {
            $this->data['car_tail_number'] = $car_tail_number;
        }
        if (!$start_address_code || $start_address_code == "") {
            unset($this->data['start_address_code']);
        }

        if (!$end_address_code || $end_address_code == "") {
            unset($this->data['end_address_code']);
        }
        if (!preg_match('/^\d+$/i', $sort)) {
            return $this->output(new \ResultModel(false, "请输入纯数字", null));
        }

        $driverR = $this->find(array('cellphone' => $cellphone, 'mchid' => $this->mchid), 'Driver');
        if ($driverR->ret) {
            if($driver_id != $driverR->data['driver_id']) {
                return $this->output(new \ResultModel(false, '司机已存在'));
            }
        }

        $r = $this->find(array('driver_id' => $driver_id), 'Driver', 'balance,cellphone');
        if ($r->ret) {

            if ($cellphone == $r->data['cellphone']) {
                unset($this->data['cellphone']);
            }
            $this->startTrans();
            $r = $this->save('Driver');
            if ($r->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
            }
        } else {
            return $this->output(new ResultModel(false, '车主ID不正确'));
        }
        //        file_get_contents("http://c.higgses.com/Driver/Account/doReduce");
        $this->doAddLog("修改顺风车车主", json_encode($this->data));
        return $this->output($r);
    }

    /**
     * 生成司机二维码
     */
    public function doCreateDriverQr($driver_id = null){
        $ret = $this->createDriverQr($this->mchid, $driver_id);
        if($ret->ret){
            $where = ' mchid = '.$this->mchid;
            $where .= ' AND driver_id = '.$driver_id;
            $driverQrR = $this->find($where, 'DriverQrAttachment');
            if($driverQrR->ret){
                $ret->data = $driverQrR->data['qr_url'];
            }
        }
        return $this->output($ret);
    }

    /**
     * 修改乘客
     * @param int $passenger_id 乘客ID
     * @param string $user_name 用户名
     * @param string $password 密码
     * @param string $cellphone 手机号码
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param int $credit 信用额度（默认为0）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdatePassenger($passenger_id, $user_name, $password, $cellphone, $email, $gender, $drive_mode, $start_address_code, $end_address_code, $address_code, $credit = 0, $start_address_remark = null, $end_address_remark = null)
    {
        $this->data['cellphone_f'] = $cellphone;
        $this->data['user_name_f'] = $user_name;
        $this->data['email_f'] = $email;
        if (empty($password)) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
            $this->data['repassword'] = md5($password);
        }
        if (intval($drive_mode) === 1) {
            $r = $this->checkingAddressCode($start_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '出发地格式不正确或地址不存在'));
            }
            $r = $this->checkingAddressCode($end_address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '目的地格式不正确或地址不存在'));
            }
            if (intval($start_address_code) === intval($end_address_code)) {
                return $this->output(new \ResultModel(false, '出发地和目的地不能相同'));
            }
        } else {
            $r = $this->checkingAddressCode($address_code);
            if (!$r->ret) {
                return $this->output(new \ResultModel(false, '运营地格式不正确或地址不存在'));
            }
        }
        $r = $this->save('Passenger');
        $this->doAddLog("修改乘客");
        return $this->output($r);
    }

    /**
     * 修改乘客
     * @param int $passenger_id 乘客ID
     * @param string $user_name 用户名
     * @param string $password 密码
     * @param string $cellphone 手机号码
     * @param string $email 邮箱
     * @param int $gender 性别（1-男，2-女）
     * @param int $drive_mode 乘车方式（1-A地到B地，2-出租车）
     * @param int $start_address_code 出发地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $end_address_code 目的地（当乘车方式选择“1-A地到B地”时有效）
     * @param int $address_code 运营地（当乘车方式选择“2-出租车”时有效）
     * @param int $credit 信用额度（默认为0）
     * @param string $start_address_remark 出发地备注（默认为空）
     * @param string $end_address_remark 目的地备注（默认为空）
     */
    public function doUpdatePassengerNew($passenger_id, $password, $cellphone)
    {
        $this->data['cellphone_f'] = $cellphone;
        if (empty($password)) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
            $this->data['repassword'] = md5($password);
        }
        $r = $this->save('Passenger');
        $this->doAddLog("修改乘客");
        return $this->output($r);
    }

    /**
     * 删除或恢复乘客
     * @param int $passenger_id 乘客ID
     * @param int $is_freeze 是否删除（0-未删除，1-已删除）
     */
    public function doFreezePassenger($passenger_id, $is_freeze)
    {
        $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'is_freeze' => intval($is_freeze)));
        $this->doAddLog("删除或恢复乘客");
        return $this->output($r);
    }

    /**
     * 审核乘客
     * @param int $passenger_id 乘客ID
     * @param int $status 审核状态：（0-通过，2-不通过）
     */
    public function doExaminePassenger($passenger_id, $status)
    {
        if($this->admin_group_id != 2) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }

        $where['examine_status'] = \CommonDefine::EXAMINE_STATUS_1;
        $where['passenger_id'] = $passenger_id;

        $passengerR = $this->find($where, 'Passenger');
        if(!$passengerR->ret) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        $updateData = array('passenger_id' => $passenger_id, 'examine_status' => $status);
        $passengerS = $this->save('Passenger', $updateData);
        if(!$passengerS->ret) {
            return $this->output(new \ResultModel(false, '操作失败'));
        }
        $this->doAddLog("审核乘客", json_encode($updateData), $this->mchid);
        return $this->output(new \ResultModel(true, '操作成功'));
    }

    /**
     * 删除或恢复车主
     * @param int $driver_id 车主ID
     * @param int $is_freeze 是否删除（0-未删除，1-已删除）
     */
    public function doFreezeDriver($driver_id, $is_freeze)
    {

        $driverModel = M('driver')->where('driver_id=' . $driver_id);
        $driverData = $driverModel->find();
        if ($driverData) {
            $mchid = $driverData['mchid'];
            $adminModel = M('admin')->where('admin_id=' . $mchid . ' and status=0');
            $adminData = $adminModel->find();
            if ($adminData) {
                if ($is_freeze == 1) {
                    $this->startTrans();
                    $r = $this->save('Driver', array('driver_id' => $driver_id, 'is_freeze' => intval($is_freeze)));
                    if ($r->ret) {
                        //增加剩余司机数量
                        $r = $this->save('Admin', array('admin_id' => $mchid, 'driver_num' => $adminData['driver_num'] + 1));
                        if ($r->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                    $this->doAddLog("删除车主");
                } else {
                    $mchDriverCountR = $this->getMerchantDriverCountByAdminId($mchid);
                    if (!$mchDriverCountR->ret) {
                        return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
                    }
                    $currentDriverCountR = $this->count(array('is_del' => 0, 'is_freeze' => 0, 'driver_role_type' => \CommonDefine::DRIVER_ROLE_0, 'mchid' => $mchid), 'Driver');
                    if ($mchDriverCountR->data <= $currentDriverCountR->data) {
                        $this->result = new ResultModel(false, '您套餐中限制的司机人数已达上限');
                    } else {
                        $r = $this->save('Driver', array('driver_id' => $driver_id, 'is_freeze' => intval($is_freeze)));
                        if ($r->ret) {
                            //扣除剩余司机数量
                            $r = $this->save('Admin', array('admin_id' => $mchid, 'driver_num' => $adminData['driver_num'] - 1));
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }

                        $this->doAddLog("恢复车主");
                    }
                }
            } else {
                $this->result = new ResultModel(false, '商户账号存在问题');
            }
        } else {
            $this->result = new ResultModel(false, '司机账号存在问题');
        }
        return $this->output($r);
    }

    /**
     * 删除或恢复顺风车车主
     * @param int $driver_id 车主ID
     * @param int $is_freeze 是否删除（0-未删除，1-已删除）
     */
    public function doFreezeFreeRideDriver($driver_id, $is_freeze)
    {
        $driverModel = M('driver')->where('driver_id=' . $driver_id);
        $driverData = $driverModel->find();
        if ($driverData) {
            if ($driverData['status'] != 0) {
                $this->result = new ResultModel(false, '司机账号还未审核通过');
            }
            $mchid = $driverData['mchid'];
            $adminModel = M('admin')->where('admin_id=' . $mchid . ' and status=0');
            $adminData = $adminModel->find();
            if ($adminData) {
                if ($is_freeze == 1) {
                    $r = $this->save('Driver', array('driver_id' => $driver_id, 'is_freeze' => intval($is_freeze)));
                    if ($r->ret) {
                        //增加剩余司机数量
                        $r = $this->save('Admin', array('admin_id' => $mchid, 'free_ride_driver_num' => $adminData['free_ride_driver_num'] + 1));
                        if ($r->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                    $this->doAddLog("删除车主");
                } else {
                    $mchFreeRideDriverCountR = $this->getMerchantFreeRideDriverCountByAdminId($mchid);
                    if (!$mchFreeRideDriverCountR->ret) {
                        return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
                    }

                    $currentDriverCountR = $this->count(array('is_del' => 0, 'is_freeze' => 0, 'driver_role_type' => \CommonDefine::DRIVER_ROLE_1, 'mchid' => $mchid, 'status' => 0), 'Driver');
                    if ($mchFreeRideDriverCountR->data <= $currentDriverCountR->data) {
                        $this->result = new ResultModel(false, '您套餐中限制的顺风车司机人数已达上限');
                    } else {
                        $r = $this->save('Driver', array('driver_id' => $driver_id, 'is_freeze' => intval($is_freeze)));
                        if ($r->ret) {
                            //扣除剩余顺风车司机数量
                            $r = $this->save('Admin', array('admin_id' => $mchid, 'free_ride_driver_num' => $adminData['free_ride_driver_num'] - 1));
                            if ($r->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                        $this->doAddLog("恢复车主");
                    }
                }
            } else {
                $this->result = new ResultModel(false, '商户账号存在问题');
            }
        } else {
            $this->result = new ResultModel(false, '司机账号存在问题');
        }
        return $this->output($r);
    }

    /**
     * 删除或恢复分台
     * @param int $driver_id 车主ID
     * @param int $is_freeze 是否删除（0-未删除，1-已删除）
     */
    public function doFreezeBranch($admin_id, $is_freeze)
    {
        $adminModel = M('Admin')->where('admin_id=' . $admin_id);
        $adminData = $adminModel->find();
        if ($adminData) {
            $r = $this->save('Admin', array('admin_id' => $admin_id, 'is_freeze' => intval($is_freeze)));
            return $this->output($r);
        } else {
            return $this->output(new ResultModel(false, '商户账号存在问题'));
        }
        $this->doAddLog("删除或恢复车主");
    }

    /**
     * 审核通过或不通过车主
     * @param int $driver_id 车主ID
     * @param int $status 是否通过（0-通过，2-不通过）
     * @param int $branchid 分台id（0-通过，2-不通过）
     */
    public function doExamineDriver($driver_id, $status, $branchid = null)
    {
        $systemConfigR = $this->find(array('account_type' => \CommonDefine::SYSTEM_ROLE_0, 'account_id' => 1, 'key' => \CommonDefine::SYSTEM_DRIVER_LIMIT_CONFIG), 'SystemConfig');
        if (!$systemConfigR->ret) {
            return $this->output(new \ResultModel(false, '系统异常，请联系平台管理员'));
        }
        $mchsArr = explode(',', $systemConfigR->data['value']);
        $this->startTrans();
        //注册司机是否计入套餐配置的平台配置白名单中
        if (!in_array($this->state->user_id, $mchsArr)) {
            //判断司机套餐数量
            $adminR = $this->find(array('is_del' => 0, 'admin_id' => $this->state->user_id), 'Admin');
            if (!$adminR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '账户未登录，请先登录....'));
            }

            $mchDriverCountR = $this->getMerchantFreeRideDriverCountByAdminId($this->state->user_id);
            if (!$mchDriverCountR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '系统异常请联系管理员'));
            }

            $driverCountR = $this->count(array('is_del' => 0, 'is_freeze' => 0, 'status' => \CommonDefine::EXAMINE_STATUS_0, 'driver_role_type' => \CommonDefine::DRIVER_ROLE_1, 'mchid' => $adminR->data['admin_id']), 'Driver');
            if ($driverCountR->data >= $mchDriverCountR->data) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '顺风车司机人数已经超过最大限制'));
            }

            $adminS = $this->save('Admin', array('admin_id' => $adminR->data['admin_id'], 'free_ride_driver_num' => ($adminR->data['free_ride_driver_num'] - 1)));
            if (!$adminS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '审核司机失败'));
            }
        }

        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if ($driverR->ret) {
            $mchR = $this->find(array('admin_id' => $driverR->data['mchid']), 'Admin');
            if ($mchR->ret) {
                if ($status == \CommonDefine::EXAMINE_STATUS_0) {
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'status' => \CommonDefine::EXAMINE_STATUS_0, 'branchid' => $branchid));
                    if ($driverS->ret) {
                        $this->commitTrans();
                        //顺风车司机审核通过
                        if (C('SMS_ON')) {
                            $smsData = array(
                                $driverR->data['name'],
                                $mchR->data['mchname'],
                                '顺风车司机',
                                '通过',
                                $mchR->data['tel']
                            );
                            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                            $smsUtil->sendTemplateSMS($driverR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_ACCOUNT_EXAMINE, $driverR->data['mchid']);
                        }
                    } else {
                        $this->transRollback();
                    }
                    $this->doAddLog("审核通过");
                } elseif ($status == \CommonDefine::EXAMINE_STATUS_2) {
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'status' => \CommonDefine::EXAMINE_STATUS_2));
                    if ($driverS->ret) {
                        $this->commitTrans();
                        //顺风车司机审核未通过
                        if (C('SMS_ON')) {
                            $smsData = array(
                                $driverR->data['name'],
                                $mchR->data['mchname'],
                                '顺风车司机',
                                '未通过',
                                $mchR->data['tel']
                            );
                            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                            $smsUtil->sendTemplateSMS($driverR->data['cellphone'], $smsData, \SMSUtil::TEMP_ID_ACCOUNT_EXAMINE, $driverR->data['mchid']);
                        }
                    } else {
                        $this->transRollback();
                    }
                    $this->doAddLog("审核不通过");
                }
                //发送通知
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '司机账号存在问题'));
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '司机账号存在问题'));
        }
        return $this->output(new \ResultModel(true, '操作成功'));
    }

    /**
     * 删除乘客
     * @param int $passenger_id 乘客ID
     */
    public function doDeletePassenger($passenger_id)
    {
        $this->startTrans();
        //删除乘客保险
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'create_time desc', 'PassengerInsurance', 'insurance_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['insurance_id'], 'PassengerInsurance');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除相关订单
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'create_time desc', 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $o = $this->sudo('OrderManager', $this->state->user_id, StateModel::$ADMIN_USER);
                $r = $o->doDeleteOrder($value['order_id']);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除乘客提醒设置
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'update_time desc', 'PassengerRemindSet', 'id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['id'], 'PassengerRemindSet');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除乘客提醒消息
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'remind_time desc', 'PassengerRemindTask', 'task_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['task_id'], 'PassengerRemindTask');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除乘客发送的消息
        $r = $this->select(array('assigner_id' => $passenger_id), null, null, 'remind_time desc', 'DriverRemindTask', 'task_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['task_id'], 'DriverRemindTask');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $r = $this->delete($passenger_id, 'Passenger');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("删除乘客");
        return $this->output($r);
    }

    /**
     * 删除车主
     * @param int $driver_id 车主ID
     */
    public function doDeleteDriver($driver_id)
    {
        $this->startTrans();
        //删除相关订单
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $o = $this->sudo('OrderManager', $this->state->user_id, StateModel::$ADMIN_USER);
                $r = $o->doDeleteOrderNew($value['order_id']);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }

        //删除车主费用
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'DriverCost', 'cost_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['cost_id'], 'DriverCost');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $r = $this->delete($driver_id, 'Driver');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("删除车主");
        return $this->output($r);
    }

    /**
     * 删除顺风车车主
     * @param int $driver_id 车主ID
     */
    public function doDeleteFreeRideDriver($driver_id)
    {
        $this->startTrans();
        //删除相关订单
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $o = $this->sudo('OrderManager', $this->state->user_id, StateModel::$ADMIN_USER);
                $r = $o->doDeleteOrderNew($value['order_id']);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }

        //删除车主费用
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'DriverCost', 'cost_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['cost_id'], 'DriverCost');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $r = $this->delete($driver_id, 'Driver');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("删除车主");
        return $this->output($r);
    }

    /**
     * 删除分台
     * @param int $admin_id 分台ID
     */
    public function doDeleteBranch($admin_id)
    {
        $adminModel = M('Admin')->where('admin_id=' . $admin_id);
        $adminData = $adminModel->find();
        if ($adminData) {
            if($this->checkIsLineCharterCustomBranch($admin_id)->ret) {
                return $this->output(new ResultModel(false, '请先到配置开关页面将自定义包车调度分台切换为其他分台后再删除'));
            }
            $r = $this->save('Admin', array('admin_id' => $admin_id, 'is_del' => 1));
            return $this->output($r);
        } else {
            return $this->output(new ResultModel(false, '商户账号存在问题'));
        }
    }

    /**
     * 清空数据库中除用户信息外的所有操作信息，并使用户恢复至初始状态
     */
    public function doClear()
    {
        $r = $this->select(null, null, null, 'create_time desc', 'Passenger', 'passenger_id');
        if ($r->ret) {
            $f_count = 0;
            $s_count = 0;
            $o = $this->sudo('MemberManager', $this->state->user_id, StateModel::$ADMIN_USER);
            foreach ($r->data as $value) {
                $this->startTrans();
                $r = $o->doClearPassenger($value['passenger_id']);
                if ($r->ret) {
                    $r = $this->select(null, null, null, null, 'MetaPassengerRemindAction', 'action_id');
                    if ($r->ret) {
                        foreach ($r->data as $value1) {
                            //添加提醒设置
                            $r = $this->add('PassengerRemindSet', array('action_id' => $value1['action_id'], 'passenger_id' => $value['passenger_id']));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '乘客提醒设置初始化失败'));
                            }
                        }
                    }
                    $this->commitTrans();
                } else {
                    $this->transRollback();
                }
                if ($r->ret) {
                    $s_count++;
                } else {
                    $f_count++;
                }
            }
            $m = '还原乘客,成功' . $s_count . '个,失败' . $f_count . '个.';
        }
        $r = $this->select(null, null, null, 'create_time desc', 'Driver', 'driver_id');
        if ($r->ret) {
            $f_count = 0;
            $s_count = 0;
            $o = $this->sudo('MemberManager', $this->state->user_id, StateModel::$ADMIN_USER);
            foreach ($r->data as $value) {
                $this->startTrans();
                $r = $o->doClearDriver($value['driver_id']);
                if ($r->ret) {
                    $r = $this->select(null, null, null, null, 'MetaDriverRemindAction', 'action_id');
                    if ($r->ret) {
                        foreach ($r->data as $value1) {
                            //添加提醒设置
                            $r = $this->add('DriverRemindSet', array('action_id' => $value1['action_id'], 'driver_id' => $value['driver_id']));
                            if (!$r->ret) {
                                $this->transRollback();
                                return $this->output(new \ResultModel(false, '车主提醒设置初始化失败'));
                            }
                        }
                    }
                    $this->commitTrans();
                } else {
                    $this->transRollback();
                }
                if ($r->ret) {
                    $s_count++;
                } else {
                    $f_count++;
                }
            }
            $m .= '还原司机,成功' . $s_count . '个,失败' . $f_count . '个.';
        }
        return $this->output(new ResultModel(true, $m));
    }

    /**
     * 清除乘客操作信息，并恢复至初始状态
     * @param int $passenger_id 乘客ID
     */
    public function doClearPassenger($passenger_id)
    {
        $this->startTrans();
        //删除乘客保险
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'create_time desc', 'PassengerInsurance', 'insurance_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['insurance_id'], 'PassengerInsurance');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除相关订单
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'create_time desc', 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $o = $this->sudo('OrderManager', $this->state->user_id, StateModel::$ADMIN_USER);
                $r = $o->doDeleteOrder($value['order_id']);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除乘客提醒设置
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'update_time desc', 'PassengerRemindSet', 'id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['id'], 'PassengerRemindSet');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除乘客提醒消息
        $r = $this->select(array('passenger_id' => $passenger_id), null, null, 'remind_time desc', 'PassengerRemindTask', 'task_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['task_id'], 'PassengerRemindTask');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $r = $this->save('Passenger', array('passenger_id' => $passenger_id, 'state' => 3));
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        return $this->output($r);
    }

    /**
     * 清除车主操作信息，并恢复至初始状态
     * @param int $driver_id 车主ID
     */
    public function doClearDriver($driver_id)
    {
        $this->startTrans();
        //删除相关订单
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'Order', 'order_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $o = $this->sudo('OrderManager', $this->state->user_id, StateModel::$ADMIN_USER);
                $r = $o->doDeleteOrder($value['order_id']);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除车主提醒设置
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'update_time desc', 'DriverRemindSet', 'id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['id'], 'DriverRemindSet');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除车主提醒消息
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'remind_time desc', 'DriverRemindTask', 'task_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['task_id'], 'DriverRemindTask');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        //删除车主费用
        $r = $this->select(array('driver_id' => $driver_id), null, null, 'create_time desc', 'DriverCost', 'cost_id');
        if ($r->ret) {
            foreach ($r->data as $value) {
                $r = $this->delete($value['cost_id'], 'DriverCost');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $r = $this->save('Driver', array('driver_id' => $driver_id, 'state' => 3));
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        return $this->output($r);
    }

    public function getDriversOnMap($tel = null, $page = 1, $size = 10)
    {

        if ($tel) {
            if (!$this->mobile($tel)) {
                return $this->output(new \ResultModel(false, '请输入正确的大陆手机号码'));
            } else {
                $where['cellphone'] = $tel;
            }
        }
        $field = 'name,car_tail_number,residual_seating,state,longitude,latitude,cellphone tel';
        $where['mchid'] = $this->state->user_id;
        $where['longitude'] = array('neq', 'NULL');
        $where['latitude'] = array('neq', 'NULL');
        $r = $this->select($where, $page, $size, 'create_time desc', 'Driver', $field);
        return $this->output($r);
    }


    /**
     *粉丝
     *$mchid int 分台id
     */
    public function getWechatFansList($mchid, $page = 1, $size = 10)
    {
        $this->doAddLog("粉丝列表");

        if ($this->admin_group_id != 1) {
            $mchid = $where['mchid'] = $this->state->user_id;
        } else {
            if ($this->isEmpty($mchid)) {
                $where['mchid'] = $mchid;
            }
        }

        $thirdPartyR = $this->select($where, $page, $size, "created_at desc", "ThirdParty", "id,third_account,third_avatar,created_at,mchid");
        if (empty($thirdPartyR->count) || empty($thirdPartyR->data)) {
            return $this->output(new ResultModel(false, '没有数据'));
        }

        if (!$this->isEmpty($mchid)) {
            foreach ($thirdPartyR->data as $k => $v) {
                $mchR = $this->find(array('admin_id' => $v['mchid']), 'Admin', 'mchname');
                if ($mchR->ret) {
                    $thirdPartyR->data[$k]['mchname'] = $mchR->data['mchname'];
                }
                unset($thirdPartyR->data[$k]['mchid']);
            }
        } else {
            $mchR = $this->find(array('admin_id' => $mchid), 'Admin', 'mchname');
            if ($mchR->ret) {
                foreach ($thirdPartyR->data as $k => $v) {
                    $thirdPartyR->data[$k]['mchname'] = $mchR->data['mchname'];
                    unset($thirdPartyR->data[$k]['mchid']);
                }
            }
        }

        return $this->output(new \ResultModel(true, $thirdPartyR->data, $thirdPartyR->count));
    }

    //地址配置
    public function getMchAddress($pid, $admin_id)
    {
        if ($this->areaCode($pid)) {
            $where['address_pid'] = $pid;
        } else {
            $where['address_pid'] = 0;
        }
        $address = $this->select($where, null, null, null, "GdRegion");
        $mchidA = $this->select(array('mchid' => $admin_id), null, null, null, "SupportCity", 'address_id');
        $ac = $this->array_cloum1($mchidA->data, 'address_id');


        foreach ($address->data as $key => $val) {
            if (in_array($val['address_id'], $ac)) {
                $address->data[$key]['flag'] = 1;
            } else {
                $address->data[$key]['flag'] = 2;
            }
        }

        return $this->output(new \ResultModel(true, $address, null));
    }

    //地址地址配置
    public function doAddMchAddress($id, $admin_id, $level)
    {
        if (!$this->areaCode($id)) {
            return $this->output(new \ResultModel(false, "请稍后再试...", null));
        }
        if (!$this->isEmpty($admin_id)) {
            return $this->output(new \ResultModel(false, "请选择商户...", null));
        }
        try {
            $dateTime = date("Y-m-d H:i:s");
            $flag = 0;
            //添加数据
            if ($level == 2) {
                $flag = $this->add_area($admin_id, $id, $dateTime);
                if ($flag == 2) {
                    return $this->output(new \ResultModel(true, "添加成功...", null));
                } elseif ($flag == 4) {
                    return $this->output(new \ResultModel(false, "数据已经存在...", null));
                } else {
                    return $this->output(new \ResultModel(false, "添加失败...", null));
                }
            } elseif ($level == 3) {
                $flag = $this->add_area($admin_id, $id, $dateTime);
                if ($flag == 2) {
                    return $this->output(new \ResultModel(true, "添加成功...", null));
                } elseif ($flag == 4) {
                    return $this->output(new \ResultModel(false, "数据已经存在...", null));
                } else {
                    return $this->output(new \ResultModel(false, "添加失败...", null));
                }
            } else {
                //添加省
                $flag = $this->add_province($admin_id, $id, $dateTime);
                if ($flag == 2) {
                    return $this->output(new \ResultModel(true, "添加成功...", null));
                } else {
                    return $this->output(new \ResultModel(false, "添加失败...", null));
                }
            }
        } catch (Exception $e) {
            return $this->output(new \ResultModel(false, "添加失败...", null));
        }

    }

    //删除地址配置
    public function doDelMchAddress($id, $admin_id, $level)
    {
        if (!$this->areaCode($id)) {
            return $this->output(new \ResultModel(false, "请稍后再试...", null));
        }
        if (!$this->isEmpty($admin_id)) {
            return $this->output(new \ResultModel(false, "请选择商户...", null));
        }
        try {
            $this->startTrans();
            $flag = true;
            if ($level == 2) {
                //删除区
                $areaR = $this->find(array('address_id' => $id, 'mchid' => $admin_id), 'SupportCity', 'address_id,address_pid');
                if ($areaR->ret) {
                    $cityR = $this->find(array('address_pid' => $areaR->data['address_id'], 'mchid' => $admin_id), 'SupportCity');
                    if ($cityR->ret) {
                        if (!M('support_city')->where(array('address_pid' => $areaR->data['address_id'], 'mchid' => $admin_id))->delete()) {
                            $flag = false;
                        }
                    }

                    if ($flag) {
                        if (M('support_city')->where(array('address_id' => $id, 'mchid' => $admin_id))->delete()) {
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, "删除成功", null));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, "删除失败", null));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, "删除失败", null));
                    }
                }
                unset($areaR);
            } elseif ($level == 3) {
                //删除县
                $cityR = $this->find(array('address_id' => $id, 'mchid' => $admin_id), 'SupportCity', 'address_id,address_pid');
                if ($cityR->ret) {
                    if (M('support_city')->where(array('address_id' => $id, 'mchid' => $admin_id))->delete()) {
                        $this->commitTrans();
                        return $this->output(new \ResultModel(true, "删除成功", null));
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, "删除失败", null));
                    }
                }
                unset($cityR);
            } else {
                //删除省
                $provinceR = $this->find(array('address_id' => $id, 'mchid' => $admin_id), 'SupportCity', 'address_id,address_pid');
                if ($provinceR->ret) {
                    $areaR = $this->select(array('address_pid' => $id, 'mchid' => $admin_id), null, null, null, 'SupportCity', 'address_id,address_pid');
                    if ($areaR->ret) {
                        foreach ($areaR->data as $val) {
                            if ($this->find(array('address_pid' => $val['address_id'], 'mchid' => $admin_id), 'SupportCity')->ret) {
                                if (!M('support_city')->where(array('address_pid' => $val['address_id'], 'mchid' => $admin_id))->delete()) {
                                    $flag = false;
                                    break;
                                }
                            }
                        }

                        if ($flag) {
                            if (!M('support_city')->where(array('address_pid' => $id, 'mchid' => $admin_id))->delete()) {
                                $flag = false;
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, "删除失败", null));
                        }

                    }
                    if ($flag) {
                        if (M('support_city')->where(array('address_id' => $id, 'mchid' => $admin_id))->delete()) {
                            $this->commitTrans();
                            return $this->output(new \ResultModel(true, "删除成功", null));
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, "删除失败", null));
                        }
                    }
                }

            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, "删除失败", null));
        }
    }

    //获取子商户列表
    public function doSubMchList($page = 0, $size = 1)
    {
        if ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = 0;
        $sbR = $this->select($where, $page, $size, 'create_time desc', 'SubBranch');

        if (empty($sbR->count) || empty($sbR->data)) {
            return $this->output(new ResultModel(false, '没有数据'));
        }

        foreach ($sbR->data as $key => $val) {
            $adminR = $this->find(array('admin_id' => $val['mchid'], 'is_del' => 0), 'Admin', 'mchname');
            if ($adminR->ret) {

                unset($sbR->data[$key]['mchid']);
                $sbR->data[$key]['branch'] = $adminR->data['mchname'];

            }
        }
        return $this->output(new \ResultModel(true, $sbR->data, $sbR->count));
    }

    //删除子商户
    public function doDeleteSubMch($id)
    {
        if ($id) {
            if ($this->admin_group_id == 2) {
                $where['mchid'] = $this->state->user_id;
            }
            $where['id'] = $id;
            $sbR = $this->find($where, 'SubBranch');
            if ($sbR->ret) {
                if ($this->save('SubBranch', array('id' => $sbR->data['id'], 'is_del' => 1))) {
                    return $this->output(new \ResultModel(true, "删除成功", null));
                } else {
                    return $this->output(new \ResultModel(false, "删除失败", null));
                }
            } else {
                return $this->output(new \ResultModel(true, "数据异常", null));
            }
        }

    }

    //添加子商户
    public function doAddSubMch($mchid, $account, $password)
    {
        if (empty($mchid)) {
            return $this->output(new \ResultModel(false, "请退出登录重试", null));
        }
        if (empty($account)) {
            return $this->output(new \ResultModel(false, "账户不能为空", null));
        }

        if (empty($password)) {
            return $this->output(new \ResultModel(false, "密码不能为空", null));
        }
        if ($this->find(array('account' => $account), 'SubBranch')->ret) {
            return $this->output(new \ResultModel(false, "该账号已存在,请换一个账号", null));
        }

        $salt = rand(1000, 9999);
        $dateTime = date('Y-m-d H:i:s');
        $data['salt'] = $salt;
        $data['account'] = $account;
        $data['password'] = md5($salt . $password);
        $data['mchid'] = $mchid;
        $data['is_del'] = 0;
        $data['create_time'] = $dateTime;
        $data['update_time'] = $dateTime;
        //        var_dump($data);die;
        try {
            $this->startTrans();
            $suR = $this->add('SubBranch', $data);
            if ($suR->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, "添加失败", null));
            }

        } catch (Expection $e) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, "添加失败", null));
        }

        return $this->output(new \ResultModel(true, "添加数据成功", null));

    }

    //修改子商户
    public function doEditSubMch($id, $account, $password)
    {
        if (!$this->find(array('id' => $id), 'SubBranch')->ret) {
            return $this->output(new \ResultModel(false, "账户不存在", null));
        }
        if (empty($account)) {
            return $this->output(new \ResultModel(false, "账户不能为空", null));
        } else {
            $data['account'] = $account;
        }

        if (!empty($password) && !is_null($password)) {
            $salt = rand(1000, 9999);
            $data['salt'] = $salt;
            $data['password'] = md5($salt . $password);
        }
        $data['id'] = $id;
        $dateTime = date('Y-m-d H:i:s');
        $data['update_time'] = $dateTime;
        try {
            $this->startTrans();
            $suR = $this->save('SubBranch', $data);
            if ($suR->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, "修改失败", null));
            }

        } catch (Expection $e) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, "修改失败", null));
        }

        return $this->output(new \ResultModel(true, "修改数据成功", null));

    }

    //私人定制
    public function doCustom($page = 1, $size = 10)
    {
        $where = null;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Custom');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, "暂无数据", null));
        }
        return $this->output($r);
    }



    public function doAjaxImg()
    {
        $url = C('FILE_ROOT');
        $size = $_FILES['file']['size'];
        $size += $_FILES['file']['size'];
        $bytes = round($size / 1048576 * 100) / 100;
        $updatePakeageR = self::updatePakeageInfo($this->state->user_id, $bytes, '', '');
        if (!$updatePakeageR->ret) {
            return $this->output(new \ResultModel(false, '上传失败'));
        }

        $adUploadDir = 'upload' . DIRECTORY_SEPARATOR . $this->state->user_id . DIRECTORY_SEPARATOR . 'ad' . DIRECTORY_SEPARATOR;
        $res = $this->uploadFile($adUploadDir, ['jpg', 'jpeg', 'png', 'gif'], $_FILES['file']);
        $imgsrc = $adUploadDir . $res->data;
        $imgsrc_url = $url . DIRECTORY_SEPARATOR . $imgsrc;

        return $this->output(new \ResultModel($res->ret, ['imgsrc' => $imgsrc, 'imgsrc_url' => $imgsrc_url], null));
    }

    public function doAjaxImgQr($type = 0)
    {
        $url = C('FILE_ROOT');
        $size = $_FILES['file']['size'];
        $size += $_FILES['file']['size'];
        $bytes = round($size / 1048576 * 100) / 100;
        $updatePakeageR = self::updatePakeageInfo($this->state->user_id, $bytes, '', '');
        if (!$updatePakeageR->ret) {
            return $this->output(new \ResultModel(false, '上传失败'));
        }
        $adUploadDir = 'upload' . DIRECTORY_SEPARATOR . 'wechatqr' . DIRECTORY_SEPARATOR;
        $res = $this->uploadFile($adUploadDir, ['jpg', 'jpeg', 'png', 'gif'], $_FILES['file']);
        if(!$res->ret) {
            return $this->output(new \ResultModel(false, '修改失败'));
        }
        $imgsrc = $adUploadDir . $res->data;
        $imgsrc_url = $url . DIRECTORY_SEPARATOR . $imgsrc;
        if ($type == 1) {
            if (!M('MchWechatQr')->where(['mchid' => $this->state->user_id])->find()) {
                $res = M('MchWechatQr')->add(['mchid' => $this->state->user_id, 'mch_wechat_qr_path' => $imgsrc, 'mch_wechat_qr' => $imgsrc_url, 'create_time' => date('Y-m-d H:i:s')]);
            } else {
                $res = M('MchWechatQr')->where(['mchid' => $this->state->user_id])->data(['mch_wechat_qr_path' => $imgsrc, 'mch_wechat_qr' => $imgsrc_url, 'update_time' => date('Y-m-d H:i:s')])->save();
            }
            if ($res) {
                return $this->output(new \ResultModel(true, ['imgsrc' => $imgsrc, 'imgsrc_url' => $imgsrc_url], null));
            } else {
                return $this->output(new \ResultModel(false, '修改失败'));
            }
        }
        return $this->output(new \ResultModel($res->ret, ['imgsrc' => $imgsrc, 'imgsrc_url' => $imgsrc_url], null));
    }
    //私人状态改变
    public function doChangeCustom($id)
    {
        $r = $this->find(array('id' => $id, 'is_del' => 0), 'Custom');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, "数据不存在", null));
        }
        $data['update_time'] = date('Y-m-d H:i:s');
        $data['status'] = 1;
        $data['id'] = $r->data['id'];
        if ($this->save('Custom', $data)->ret) {
            return $this->output(new \ResultModel(true, "修改成功", null));
        } else {
            return $this->output(new \ResultModel(false, "修改失败", null));
        }

    }

    //私人状态改变
    public function doDriverSort($driver_id, $sort = null)
    {
        if (!preg_match('/^\d+$/i', $sort)) {
            return $this->output(new \ResultModel(false, "请输入纯数字", null));
        }
        $r = $this->find(array('driver_id' => $driver_id, 'is_del' => 0), 'Driver', 'driver_id');

        if (!$r->ret) {
            return $this->output(new \ResultModel(false, "数据不存在", null));
        }
        $data['sort'] = $sort;
        $data['driver_id'] = $r->data['driver_id'];
        if ($this->save('Driver', $data)->ret) {
            return $this->output(new \ResultModel(true, "修改成功", null));
        } else {
            return $this->output(new \ResultModel(false, "修改失败", null));
        }

    }

    public function doCommission($commission = null)
    {
        if (empty($commission) && $commission != 0) {
            return $this->output(new \ResultModel(false, "提成比列不能为空", null));
        }

        if (!is_numeric($commission)) {
            return $this->output(new \ResultModel(false, "提成比列只能是数字", null));
        }
        if ($commission > 100) {
            return $this->output(new \ResultModel(false, "提成比列不能超过100", null));
        }

        $adminR = $this->find(array('admin_id' => $this->state->user_id, 'group_id' => 2, 'is_del' => 0), 'Admin', 'admin_id');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, "该商户不存在", null));
        }
        $afR = $this->find(array('mchid' => $adminR->data['admin_id']), 'AnnualFee');
        $dateTime = date("Y-m-d H:i:s");
        $data['split'] = $commission;
        $data['update_time'] = $dateTime;
        try {
            $this->startTrans();
            if ($afR->ret) {
                $data['id'] = $afR->data['id'];
                $aR = $this->save('AnnualFee', $data);
            } else {
                $data['create_time'] = $dateTime;
                $data['mchid'] = $adminR->data['admin_id'];
                $aR = $this->add('AnnualFee', $data);
            }
            if ($aR->ret) {
                $this->commitTrans();
                return $this->output(new \ResultModel(true, "设置成功", null));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, "设置失败", null));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, "服务器异常...", null));
        }

    }

    private function array_cloum1($array, $flag)
    {
        $temp = null;
        foreach ($array as $key => $value) {
            $temp[$key] = $value[$flag];
        }
        return $temp;
    }

    private function add_area($admin_id, $id, $dateTime)
    {
        $this->startTrans();
        try {
            $flag = true;
            $cityR = $this->find(array('mchid' => $admin_id, 'address_id' => $id), "SupportCity", 'address_pid,address_id');
            //判断是否已经加入
            if (!$cityR->ret) {
                //查询县是否存在库里面
                $gdR = $this->find(array('mchid' => $admin_id, 'address_id' => $id), "GdRegion", 'address_pid,address_id,name');
                if (!$gdR->ret) {
                    return 3;
                }

                //查询上级是否已经存在
                $supR = $this->find(array('mchid' => $admin_id, 'address_id' => $gdR->data['address_pid']), "SupportCity", 'address_pid,address_id');
                $gdpR = $this->find(array('mchid' => $admin_id, 'address_id' => $gdR->data['address_pid']), "GdRegion", 'address_id,address_pid,name');
                if (!$supR->ret) {
                    //添加上级区域
                    if ($gdpR->data['address_pid'] == 0) {
                        if (!$this->add('SupportCity', array('mchid' => $admin_id, 'address_id' => $gdR->data['address_pid'], 'address_pid' => 0, 'name' => $gdpR->data['name'], 'create_at' => $dateTime, 'update_at' => $dateTime))) {
                            $flag = false;
                        }
                    } else {
                        $flag = $this->second_level($supR, $gdpR, $admin_id, $dateTime);
                    }
                } else {
                    if ($gdpR->data['address_pid'] != 0) {
                        $flag = $this->second_level($supR, $gdpR, $admin_id, $dateTime);
                    }
                }

                if ($flag) {
                    if ($this->add('SupportCity', array('mchid' => $admin_id, 'address_id' => $gdR->data['address_id'], 'address_pid' => $gdR->data['address_pid'], 'name' => $gdR->data['name'], 'create_at' => $dateTime, 'update_at' => $dateTime))) {
                        $this->commitTrans();
                        return 2;
                    }
                }
            } else {
                return 4;
            }
        } catch (Exception $e) {
            $this->transRollback();
            return 5;
        }
    }

    private function second_level($supR, $gdpR, $admin_id, $dateTime)
    {
        $flag = true;
        $this->startTrans();
        try {
            //市区不存在
            if (!$supR->ret) {
                if (!$this->add('SupportCity', array('mchid' => $admin_id, 'address_id' => $gdpR->data['address_id'], 'address_pid' => $gdpR->data['address_pid'], 'name' => $gdpR->data['name'], 'create_at' => $dateTime, 'update_at' => $dateTime))) {
                    $flag = false;
                } else {
                    $this->commitTrans();
                }
            }

            $supsR = $this->find(array('mchid' => $admin_id, 'address_id' => $gdpR->data['address_pid']), "SupportCity", 'address_pid,address_id');
            $gdpsR = $this->find(array('mchid' => $admin_id, 'address_id' => $gdpR->data['address_pid']), "GdRegion", 'address_id,address_pid,name');

            //省不存在
            if (!$supsR->ret) {
                if (!$this->add('SupportCity', array('mchid' => $admin_id, 'address_id' => $gdpsR->data['address_id'], 'address_pid' => 0, 'name' => $gdpsR->data['name'], 'create_at' => $dateTime, 'update_at' => $dateTime))) {
                    $flag = false;
                } else {
                    $this->commitTrans();
                }
            }

        } catch (Exception $e) {
            $flag = false;
            $this->transRollback();
        }


        return $flag;
    }

    private function add_province($admin_id, $id, $dateTime)
    {
        $provinceR = $this->find(array('mchid' => $admin_id, 'address_id' => $id), "SupportCity", 'address_pid,address_id');
        //已经存在
        $gdR = $this->find(array('mchid' => $admin_id, 'address_id' => $id), "GdRegion", 'address_pid,name,address_id'); //获取省
        $gd1R = $this->select(array('address_pid' => $gdR->data['address_id']), null, null, null, "GdRegion", 'address_id,address_pid,name');//查询区
        $dateTime = date("Y-m-d H:i:s");
        try {
            $flag = true;
            $this->startTrans();
            foreach ($gd1R->data as $val) {
                $areaR = $this->find(array('mchid' => $admin_id, 'address_id' => $val['address_id']), "SupportCity", 'name');
                if (!$areaR->ret) {
                    $flag = $this->add_areas($val['address_id'], $admin_id, $dateTime);
                    if (!$flag) {
                        break 1; //失败
                    }
                }
                $gd2R = $this->select(array('address_pid' => $val['address_id']), null, null, null, "GdRegion", 'address_id,address_pid,name');//查询县
                foreach ($gd2R->data as $item) {
                    $cityR = $this->find(array('mchid' => $admin_id, 'address_id' => $item['address_id']), "SupportCity", 'name'); //查询是否存在
                    if (!$cityR->ret) {
                        $flag = $this->add_areas($item['address_id'], $admin_id, $dateTime); //添加县
                        if (!$flag) {
                            break 2;//失败
                        }
                    }
                }
            }
            if ($flag) {
                if (!$provinceR->ret) {
                    $this->add_areas($gdR->data['address_id'], $admin_id, $dateTime);
                }
                $this->commitTrans();
                return 2; //添加成功
            } else {
                $this->transRollback();
                return 3; //添加失败
            }

        } catch (Exception $e) {
            $this->transRollback();
            return 3;
        }
    }

    private function add_areas($area_id, $admin_id, $dateTime)
    {
        $areaR = $this->find(array('mchid' => $admin_id, 'address_id' => $area_id), "SupportCity", 'name');
        if (!$areaR->ret) {
            $gdR = $this->find(array('address_id' => $area_id), "GdRegion", 'address_id,address_pid,name');
            $this->startTrans();
            try {
                if ($this->add('SupportCity', array('mchid' => $admin_id, 'address_id' => $gdR->data['address_id'], 'address_pid' => $gdR->data['address_pid'], 'name' => $gdR->data['name'], 'create_at' => $dateTime, 'update_at' => $dateTime))) {
                    $this->commitTrans();
                    return true;
                } else {
                    $this->transRollback();
                    return false;
                }
            } catch (Exception $e) {
                $this->transRollback();
                return false;
            }
        } else {
            return false;
        }
    }

}

<?php

class CardPageAction extends AdminCoreAction {
    //套餐卡列表
    public function CardlistPage($id=0){
        if($id!=0){
            $info= M('coupon')->where(['is_del'=>\CommonDefine::IS_DEL_0,'coupon_id'=>$id,'mchid'=>$this->state->user_id])->find();
            $info['start_time']=date('Y-m-d',strtotime($info['start_time']));
            $info['end_time']=date('Y-m-d',strtotime($info['end_time']));
            $this->assign('info',$info);
        }
     $this->display('Tpl/Admin/Card/cardlist.html');
    }

    //添加/编辑 优惠券
    public function cardPage($id = 0){
        if($id!=0){
            $info= M('card')->where(['is_del'=>\CommonDefine::IS_DEL_0,'card_id'=>$id,'mchid'=>$this->state->user_id])->find();
            $this->assign('info',$info);
        }
        $this->display('Tpl/Admin/Card/card.html');
    }
};
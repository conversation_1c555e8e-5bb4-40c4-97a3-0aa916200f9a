<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Admin/AdminAction');
/**
 * 日志管理模块
 *
 * <AUTHOR>
 */
class LogManagerAction extends AdminCoreAction
{


    public function getTest($page = 1, $size = 10)
    {
        $uid = $this->state->user_id;

        $logMode = new \LogModel();
        $adminTable = (new \AdminModel())->getTableName();
        $logList = $logMode->alias('log')->
        join("$adminTable admin ON admin.admin_id=log.admin_id")
            ->field("log.id,admin.account,log.operation_time,log.Operation_content")
            ->page($page, $size)
            ->where("log.admin_id=".$uid)
            ->order('log.id desc')->select();
        $count = $logMode->alias('log')
            ->join("$adminTable admin ON admin.admin_id=log.admin_id")
            ->field("log.id,admin.account,log.operation_time,log.Operation_content")
            ->page($page, $size)
            ->where("log.admin_id=".$uid)
            ->order('log.id desc')->count();

        /*$r = $this->select(null, $page, $size, 'id desc', 'Log', 'id,admin_id,operation_time,Operation_content');*/
        return $this->output(new \ResultModel(true, $logList, $count));
    }

    /**
     * 删除单条日志
     * @param int $passenger_id 乘客ID
     */
    public function doDeleteLog($id, $page = 1, $size = 10)
    {
        $r = $this->select(null, $page, $size, 'id desc', 'Log', 'id,admin_id,operation_time,Operation_content');
        $this->startTrans();
        $d = $this->delete($id, 'Log');
        if ($d->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }

        return $this->output($r);
    }
}

?>

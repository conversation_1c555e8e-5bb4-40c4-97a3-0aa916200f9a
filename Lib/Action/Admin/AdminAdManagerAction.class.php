<?php

class AdminAdManagerAction extends AdminCoreAction
{
    public function doAdd($id = 0, $title = '', $imgsrc = '', $cate_id = [], $start_time = '', $end_time = '', $state = '', $describe = '', $link = '', $limit_biz = [])
    {
        $this->data['mchid'] = $this->state->user_id;
        if (!$title) {
            return $this->output(new \ResultModel(false, '标题不符合规范！'));
        }
        if (!$imgsrc) {
            return $this->output(new \ResultModel(false, '请上传图片！'));
        }
        if (!$cate_id or count($cate_id) < 1) {
            return $this->output(new \ResultModel(false, '请选择一个广告位！'));
        }
        if (!$state) {
            return $this->output(new \ResultModel(false, '请选择上下架！'));
        }
        if (!$end_time) {
            return $this->output(new \ResultModel(false, '请选择结束时间！'));
        }
        if (!empty($link)) {
            if (!preg_match('/(http|https).+\.[a-zA-Z]+/', $link)) {
                return $this->output(new \ResultModel(false, '请输入正确的url！'));
            }
        }

        $imgsrc=$this->base64ToFile($imgsrc);
        if($imgsrc==false) {
            return $this->output(new \ResultModel(false, '保存图片失败！'));
        }

        $this->data['imgsrc']=$imgsrc;
        $this->data['imgsrc_url']='https://'.$_SERVER['SERVER_NAME'].'/'.$imgsrc;

        M()->startTrans();

        $this->data['start_time'] = strtotime($start_time);
        $this->data['end_time'] = strtotime($end_time);
        $this->data['update_time'] = date('Y-m-d H:i:s');
        $this->data['limit_biz'] = implode(',', $limit_biz);
        if ($id > 0) {
            //修改
            $res_del_cate = M('MchAdCate')->where(['ad_id' => $id])->delete();
            if (!$res_del_cate) {
                //清除分类失败！
                M()->rollback();
                $this->output(new \ResultModel(false, '系统繁忙请稍后再试！'));
            }
            $res = M('MchAd')->where(['id' => $id])->save($this->data);
            if ($res) {
                $res = $id;
            }
        } else {
            $res = M('MchAd')->add($this->data);
        }
        if ($res) {
            foreach ($cate_id as $key => $value) {
                $res1 = M('MchAdCate')->add(['ad_id' => $res, 'cate_id' => $value]);
                if ($res1 < 1) {
                    M()->rollback();
                    $this->output(new \ResultModel(false, '系统繁忙请稍后再试！'));
                }
            }
            M()->commit();
        }
        return $this->output(new \ResultModel($res > 0 ? true : false, $res > 0 ? '添加成功！' : '系统繁忙，请稍后重试！'));
    }

    public function getAdList($title = '', $state = '', $category = '', $start_time = '', $end_time = '', $page = 0, $size = 10)
    {
        $where['mchid'] = $this->state->user_id;
        if ($title) {
            $where['cp_mch_ad.title'] = ['like', '%' . $title . '%'];
        }
        if ($state) {
            $where['cp_mch_ad.state'] = $state;
        }
        if ($category) {
            $where['b.cate_id'] = $category;
        }
        if ($start_time) {
            $where['cp_mch_ad.start_time'] = ['eq', strtotime($start_time)];
        }
        if ($end_time) {
            $where['cp_mch_ad.end_time'] = ['eq', strtotime($end_time)];
        }
        $parent_arr = [];
        $parent_info = M('AdCategory')->where(['parent_id' => ['eq', 0]])->select();
        foreach ($parent_info as $key => $value) {
            $parent_arr[$value['id']] = $value['name'];
        }
        $r = (object)[];
        $r->data = M('MchAd')->where($where)->page($page, $size)->join('cp_mch_ad_cate b on b.ad_id=cp_mch_ad.id')->group('cp_mch_ad.id')->field('cp_mch_ad.*')->order('cp_mch_ad.id desc')->select();
        if ($r->data) {
            $r->ret = true;
            $r->count = M('MchAd')->where($where)->page($page, $size)->field('cp_mch_ad.*')->order('id desc')->count();
        } else {
            $r->ret = false;
        }
        foreach ($r->data as $k => $value) {
            $r->data[$k]['start_time'] = date('Y-m-d', $value['start_time']);
            $r->data[$k]['end_time'] = date('Y-m-d', $value['end_time']);
            //获取车位
            $cate_info = M('MchAdCate')->field('b.*')->join('cp_ad_category b on b.id=cp_mch_ad_cate.cate_id')->where(['ad_id' => $value['id']])->select();
            $cateName_ar = [];
            foreach ($cate_info as $k1 => $v1) {
                $cateName_ar[$k1] = $parent_arr[$v1['parent_id']] . '--' . $v1['name'];
            }
            $r->data[$k]['category'] = implode('&nbsp;&nbsp;|&nbsp;&nbsp;', $cateName_ar);
        }
        $this->output($r);
    }

    public function doUpOrDown($id = 0, $state = 0, $is_batch = 0)
    {
        if (!$state or !$id) {
            return $this->output(new \ResultModel(false, '系统繁忙,请稍后重试！'));
        }
        $data = [];
        if ($state == 1) {
            $data['state'] = 2;//下架
        } else {
            $data['state'] = 1;//上架
        }
        unset($data['id']);
        if ($is_batch == 1) {
            $where['id'] = ['in', explode('|', $id)];
        } else {
            $where['id'] = $id;
        }
        return M('MchAd')->where($where)->save($data) ? $this->output(new \ResultModel(true, '操作成功！')) : $this->output(new \ResultModel(false, '操作失败！'));
    }

    public function doDelAd($id = 0, $is_batch = 0)
    {
        if (!$id) {
            return $this->output(new \ResultModel(false, '系统繁忙,请稍后重试！'));
        }
        if ($is_batch == 1) {
            $where['id'] = ['in', explode('|', $id)];
        } else {
            $where['id'] = $id;
        }
        return M('MchAd')->where($where)->delete() ? $this->output(new \ResultModel(true, '操作成功！')) : $this->output(new \ResultModel(false, '操作失败！'));
    }

    protected function base64ToFile($base)
    {
        if(preg_match('/^.*upload(?=\W)/', $base)) {
            return $base;
        }
        if (strstr($base, ",")) {
            $image = explode(',', $base);
            $base = $image[1];
        }
        $dir = 'upload' . DIRECTORY_SEPARATOR . $this->state->user_id . DIRECTORY_SEPARATOR . 'ad' . DIRECTORY_SEPARATOR;
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0777, true)) {
                return false;
            };
        }
        $url = $dir . date('Ymdhis') .'_'. rand(1000, 9999).'.png';
        $r = file_put_contents($url, base64_decode($base));
        if (!$r) {
            @unlink($url);
            return false;
        } else {
            return $url;
        }
    }
}

<?php
/**
 * 交易信息模块
 *
 * <AUTHOR>
 */
import('@/Action/Admin/AdminAction');
import('@/Action/Weixin/Wechat');

class TurnoverManagerAction extends AdminCoreAction
{
    /**
     * 交易明细
     * @param string $fields 查询字段
     * @param int $page 页码
     * @param int $size 分页大小
     * @param int $turnover_status 交易状态
     * @param string $pphone 乘客电话
     * @param string $dphone 司机电话
     * @param string $bphone 分台电话
     * @param string $stime 开始时间
     * @param string $etime 结束时间
     */
    public function getList($fields = null, $page = 1, $size = 10, $turnover_status = null, $pphone = null, $dphone = null, $bphone = null, $stime = null, $etime = null, $order_no='', $search_mchid = null, $search_branchid = null)
    {
        $this->doAddLog("查看交易明细");

        $where['o.state'] = array('in',array(1,2,3,4,5,6,7));
        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " o.deleted_at IS NULL ";

        if($this->isEmpty($stime) && $this->isEmpty($etime)) {
            $where['o.create_time'] = array('between',array($stime,$etime));
        }

        if($this->isEmpty($pphone)) {
            $pR =$this->select(array('cellphone' => $pphone), null, null, 'passenger_id desc', 'Passenger', 'passenger_id');
            if($pR->ret) {
                $passengerIdsArr = array();
                foreach($pR->data as $p) {
                    $passengerIdsArr[] = $p['passenger_id'];
                }
                $where['o.passenger_id'] = array('in', implode(',', $passengerIdsArr));
            }
        }

        if($this->isEmpty($dphone)) {
            $dR =$this->select(array('cellphone' => $dphone), null, null, 'driver_id desc', 'Driver', 'driver_id');
            if($dR->ret) {
                $driverIdsArr = array();
                foreach($dR->data as $d) {
                    $driverIdsArr[] = $d['driver_id'];
                }

                $where['o.driver_id'] = array('in', implode(',', $driverIdsArr));
            }
        }

        if($this->isEmpty($bphone)) {
            $bR =$this->find(array('cellphone' => $bphone), 'Admin', 'admin_id');
            if($bR->ret) {
                $where['o.temp_apply_branchid'] = $bR->data['admin_id'];
            }
        }

        if($this->isEmpty($turnover_status)) {
            switch($turnover_status) {
                case 1://待付款
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=0 AND o.state in (1,2,3,4,5)) OR (o.is_temp=1 AND o.is_pre_pay=0 AND o.is_pay=0 AND o.state in (1,2,3,4,5,7))";
                    break;
                case 2://已线上付款
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=1 AND ISNULL(r.status)) OR (o.is_temp=1 AND (o.is_pre_pay=1 OR o.is_pay=1) AND ISNULL(r.status))";
                    break;
                case 3://已线下付款
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=0 AND o.state=6) OR (o.is_temp=1 AND o.is_pre_pay=0 AND o.is_pay=0 AND o.state=6)";
                    break;
                case 4://退款中
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=1 AND o.state=7 AND r.status in(1,2)) OR (o.is_temp=1 AND (o.is_pre_pay=1 OR o.is_pay=1) AND o.state=7  AND r.status in(1,2))";
                    break;
                case 5://退款成功
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=1 AND o.state=7 AND r.status=3) OR (o.is_temp=1 AND (o.is_pre_pay=1 OR o.is_pay=1) AND o.state=7  AND r.status=3)";
                    break;
                case 6://退款失败
                    $where['_string'] = "(o.is_temp=0 AND o.is_pay=1 AND o.state=7 AND r.status=4) OR (o.is_temp=1 AND (o.is_pre_pay=1 OR o.is_pay=1) AND o.state=7  AND r.status=4)";
                    break;
            }
        }

        $uid = $this->state->user_id;
        if($this->admin_group_id == 3) {//分台
            $where['o.branchid'] = $uid;
        } elseif($this->admin_group_id == 2) {//总台
            $where['o.mchid'] = $uid;
            if(!empty($search_branchid)) {
                $where['o.branchid'] = $search_branchid;
            }
        } elseif($this->admin_group_id == 1) {//平台
            if(!empty($search_mchid)) {
                $where['o.mchid'] = $search_mchid;
            }
        }
        if($order_no) {
            $where['o.order_no']=$order_no;
        }
        $orderListR = new ResultModel(false);
        $db = M();
        $orderList = $db->table('cp_order o')
            ->join("LEFT JOIN cp_refunds r ON r.order_id = o.order_id")
            ->join("LEFT JOIN cp_admin a ON o.mchid = a.admin_id")
            ->join("LEFT JOIN cp_admin b on o.branchid = b.admin_id")
            ->field("o.order_id,o.order_no,o.is_pay,o.is_pre_pay,o.passenger_id,o.driver_id,o.branchid,o.temp_apply_branchid,o.price,o.split,o.offer_price,o.state,o.create_time,o.is_temp,r.status as refundstatus,a.mchname,b.mchname as branchname")
            ->where($where)
            ->order('o.create_time desc')
            ->page($page, $size)
            ->select();
        //        echo $db->getLastSql();die;
        $orderCount = $db->table('cp_order o')
            ->join("LEFT JOIN cp_refunds r ON r.order_id = o.order_id")
            ->join("LEFT JOIN cp_admin a ON o.mchid = a.admin_id")
            ->field("o.order_id,o.order_no,o.is_pay,o.is_pre_pay,o.passenger_id,o.driver_id,o.branchid,o.temp_apply_branchid,o.price,o.split,o.offer_price,o.state,o.create_time,o.is_temp,r.status as refundstatus,a.mchname")
            ->where($where)
            ->order('o.create_time desc')
            ->page($page, $size)
            ->count();

        $where['o.state'] = 6;
        $where['_string'] = "(o.is_temp=0 AND o.is_pay=1 AND ISNULL(r.status)) OR (o.is_temp=1 AND (o.is_pre_pay=1 OR o.is_pay=1) AND ISNULL(r.status))";
        $totalAmount = $db->table('cp_order o')
            ->join("LEFT JOIN cp_refunds r ON r.order_id = o.order_id")
            ->field("o.order_id,o.order_no,o.is_pay,o.is_pre_pay,o.passenger_id,o.driver_id,o.branchid,o.temp_apply_branchid,o.price,o.split,o.offer_price,o.state,o.create_time,o.is_temp,r.status as refundstatus")
            ->where($where)
            ->sum('o.price');

        if($orderList) {
            $orderListR->ret = true;
            $orderListR->count = $orderCount;
            $orderListR->data = $orderList;
            foreach($orderListR->data as $k=>$v) {
                $orderListR->data[$k]['account_name'] = "";
                $orderListR->data[$k]['account_type'] = "";
                if($v['is_temp'] == 0) {
                    $orderListR->data[$k]['account_type'] = "乘客";
                    $passengerR = $this->find(array('passenger_id' => $v['passenger_id']), 'Passenger', 'name,cellphone');
                    if($passengerR->ret) {
                        $orderListR->data[$k]['account_name'] = $passengerR->data['name']."[".$passengerR->data['cellphone']."]";
                    }
                } elseif($v['is_temp'] == 1) {
                    $orderListR->data[$k]['account_type'] = "分台";
                    $branchR = $this->find(array('admin_id' => $v['temp_apply_branchid']), 'Admin', 'mchname,cellphone');
                    if($branchR->ret) {
                        $orderListR->data[$k]['account_name'] = $branchR->data['mchname']."[".$branchR->data['cellphone']."]";
                    }
                }

                $orderListR->data[$k]['driver_name'] = "<span style='color: lightgray'>未指派司机</span>";
                if($v['driver_id']) {
                    $driverR = $this->find(array('driver_id'=>$v['driver_id']), 'Driver', 'name,cellphone');
                    if($driverR->ret) {
                        $orderListR->data[$k]['driver_name'] = $driverR->data['name']."[".$driverR->data['cellphone']."]";
                    }
                }

                $orderListR->data[$k]['split_price'] = "0";
                $orderListR->data[$k]['turnover_message'] = "<span style='color: red'>待付款</span>";
                switch($v['state']) {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                        if($v['is_pre_pay'] == 1 && $v['is_temp'] == 1) {
                            $orderListR->data[$k]['turnover_message'] = "<span style='color: forestgreen'>已线上付款</span>";
                            $orderListR->data[$k]['split_price'] = $orderListR->data[$k]['price']-$orderListR->data[$k]['offer_price'];
                        } elseif($v['is_pay'] == 1) {
                            $orderListR->data[$k]['turnover_message'] = "<span style='color: forestgreen'>已线上付款</span>";
                            $orderListR->data[$k]['split_price'] = $orderListR->data[$k]['price']-$orderListR->data[$k]['offer_price'];
                        } elseif($v['state'] == 6) {
                            $orderListR->data[$k]['turnover_message'] = "<span style='color: forestgreen'>已线下付款</span>";
                        }
                        break;
                    case 7:
                        if($v['is_pre_pay'] == 1 || $v['is_pay'] == 1) {
                            if($v['refundstatus']) {
                                switch($v['refundstatus']) {
                                    case 1:
                                    case 2:
                                        $orderListR->data[$k]['turnover_message'] = "<span style='color: orchid'>退款中</span>";
                                        break;
                                    case 3:
                                        $orderListR->data[$k]['turnover_message'] = "<span style='color: #4a7dfb'>退款成功</span>";
                                        break;
                                    case 4:
                                        $orderListR->data[$k]['turnover_message'] = "<span style='color: orange'>退款失败</span>";
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }

            }
        }
        $orderListR->ext['total_amount'] = $totalAmount ? $totalAmount : 0;
        return $this->output($orderListR);
    }
}

<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/2/8
 * Time: 10:55
 */
class LineAction extends AdminCoreAction
{
    public function editSave($driver_id, $name, $price)
    {
        $r = $this->find(array('driver_id' => $driver_id), "Driver");
        if ($r->ret) {
            $r->data['price'] = $price;
            $r->data['name'] = $name;
            $return = $this->save('Driver');
            if ($return->ret) {
                return $this->output($return);
            } else {
                return $this->output(new ResultModel(false, '修改失败'));
            }
        } else {
            return $this->output(new ResultModel(false, '找不到当前线路'));
        }

    }

}
<?php

import('@/Action/Admin/OrderManagerAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 不良记录管理
 *
 * <AUTHOR>
 */
class BadRecordManagerAction extends AdminCoreAction
{

    /**
     * 获取不良记录列表
     * @param string $fields 查询的字段列表（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 　每页几条（默认为10）
     * @param string $passenger_user_name 乘客用户名
     * @param string $passenger_cellphone 　乘客手机号
     * @param string $driver_name 　司机姓名
     * @param string $driver_cellphone 　司机手机号
     * @param int $drive_mode 　路线类型（1-A地到B地，2-出租车）
     * @param int $start_address_code 　出发地
     * @param int $end_address_code 　目的地
     * @param int $book_seating 　预定人数
     */
    public function getBadRecords($fields = null, $page = 1, $size = 10, $passenger_user_name = null, $passenger_cellphone = null, $driver_name = null, $driver_cellphone = null, $drive_mode = null, $start_address_code = null, $end_address_code = null, $book_seating = null)
    {
        $where['a.is_bad_record'] = 1;
        if (!empty($passenger_user_name)) {
            $where['Passenger.user_name'] = $passenger_user_name;
        }
        if (!empty($passenger_cellphone)) {
            $where['Passenger.cellphone'] = $passenger_cellphone;
        }
        if (!empty($driver_name)) {
            $where['Driver.name'] = $driver_name;
        }
        if (!empty($driver_cellphone)) {
            $where['Driver.cellphone'] = $driver_cellphone;
        }
        if (!empty($drive_mode)) {
            $where['a.drive_mode'] = $drive_mode;
        }
        if (!empty($start_address_code)) {
            $where['a.start_address_code'] = $start_address_code;
        }
        if (!empty($end_address_code)) {
            $where['a.end_address_code'] = $end_address_code;
        }
        if (!empty($book_seating)) {
            $where['a.book_seating'] = array('egt', $book_seating);
        }
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        $data = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->page($page, $size)->field('a.order_id')->order('a.create_time desc')->select();
        if ($data) {
            $count = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->count('a.order_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("order_id", $value['order_id'], 'OrderManager', 'getOrder', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }

        return $this->output($r);
    }

}

?>

<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/2/8
 * Time: 10:54
 */
class YyLinePageAction extends AdminCoreAction
{
    public function __construct()
    {
        parent::__construct();
   }

    //学生号班线--列表
    public function YylineClassListPage()
    {
        if($this->adminInfo['group_id']==3){
            $where['branchid'] = $this->state->user_id;
        }else{
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name,car_brand');
        $this->assign("drivers",$driver->data);
        $this->display('Tpl/Admin/YyLine/yy_line_class_list.html');
    }

    //学生号班线--添加
    public function YylineClassPage()
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        $where['line_category_business_id']=5;
        $lineCategory = M('LineCategory')->where($where)->select();
        $lc = $this->select($where,null,null,null,'LineCategory','id,title');
        unset($where['line_category_business_id']);
        $this->assign("data",$lc->data);
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name');
        $this->assign("drivers",$driver->data);
        $this->assign("lineCategory",$lineCategory);
        $this->display('Tpl/Admin/YyLine/yy_line_class.html');
    }
    
    //班线--编辑
    public function YylineClassEditPage($line_class_id)
    {
        $lineInfo=M('YyLineClass')->where(['id' => $line_class_id])->find();

        if(!$lineInfo)
        {
            die('班线不存在');
        }

        $lineClassPoint['up']=M('YyLineClassPoint')->where(['line_class_id'=>$line_class_id,'type'=>1,'is_return'=>0])->select();
        $lineClassPoint['down']=M('YyLineClassPoint')->where(['line_class_id'=>$line_class_id,'type'=>2,'is_return'=>0])->select();
        $lineClassPoint['return_up']=M('YyLineClassPoint')->where(['line_class_id'=>$line_class_id,'type'=>1,'is_return'=>1])->find();
        $lineClassPoint['return_down']=M('YyLineClassPoint')->where(['line_class_id'=>$line_class_id,'type'=>2,'is_return'=>1])->find();
        $arr = array('start_polygon','end_polygon','return_start_polygon','return_end_polygon');

        $where['mchid'] = $where1['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        $where['line_category_business_id']=5;
        $lineCategory = M('LineCategory')->where($where)->select();
        unset($where['line_category_business_id']);
        $where1['line_class_id'] = $line_class_id;
        $thisLineCat = M('line_class_fk_line_category')->where($where1)->select();

        foreach ($lineCategory as $key => $value) 
        {
            foreach ($thisLineCat as $k => $v) 
            {
                if ($v['line_category_id']==$value['id']) 
                {
                    $lineCategory[$key]['checked'] = 'checked';
                    break;
                }
                else
                {
                    $lineCategory[$key]['checked'] = '';
                }
            }
        }

        foreach ($arr as $key => $value) 
        {   
           
            if (!empty($lineInfo[$value])) {
                $temp = implode('-', json_decode($lineInfo[$value],true));
            }else{
                $temp = '';
            }
            $this->assign($value, $temp);
        }

        $this->assign('thisLineCat', $thisLineCat);
        $this->assign('lineCategory', $lineCategory);
        $this->assign('line', $lineInfo);
        $this->assign('up',$lineClassPoint['up']);
        $this->assign('down',$lineClassPoint['down']);
        $this->assign('return_up',$lineClassPoint['return_up']);
        $this->assign('return_down',$lineClassPoint['return_down']);
        $this->display('Tpl/Admin/YyLine/yy_line_class_edit.html');
    }

    //班次--列表
    public function YylineClassTrainListPage($line_class_id=0)
    {
        if($this->adminInfo['group_id']==3){
            $where['branchid'] = $this->state->user_id;
        }else{
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name,car_brand');
        $this->assign("drivers",$driver->data);
        $this->assign('line_class_id', $line_class_id);
        $this->display('Tpl/Admin/YyLine/yy_line_class_train_list.html');
    }

    //班次--添加
    public function YylineClassTrainPage($line_class_id)
    {
        $lineClassR = $this->find(array('id' => $line_class_id),'YyLineClass');
        if(!$lineClassR->ret){
            return $this->output($lineClassR);
        }
        $startAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['start_address_code'])->data['address']."]";
        $start_route = $startAddress . $lineClassR->data['start_name'];
        $endAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['end_address_code'])->data['address']."]";
        $end_route = $endAddress . $lineClassR->data['end_name'];

/*        $where = 'd.mchid='.$this->state->user_id.
            ' AND d.is_freeze ='.\CommonDefine::IS_FREEZE_0.
            ' AND d.driver_type='.\CommonDefine::DRIVER_TYPE_1.
            ' AND dl.type='.\CommonDefine::ORDER_TYPE_5.
            ' AND dl.line_id ='.$line_class_id;
        $driver = M()->table('cp_driver d')
                    ->join("LEFT JOIN cp_driver_line dl ON d.driver_id = dl.driver_id")
                    ->where($where)
                    ->field('d.driver_id,d.name')
                    ->select();

        $this->assign("drivers",$driver);*/
        $where['mchid'] = $this->state->user_id;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name,car_brand');
        $this->assign("drivers",$driver->data);
        $this->assign('start_route', $start_route);
        $this->assign('end_route', $end_route);
        $this->assign('line_class_id', $line_class_id);
        $this->display('Tpl/Admin/YyLine/yy_line_class_train.html');
    }

    //班次--编辑
    public function YylineClassTrainEditPage($line_class_train_id)
    {
        $lineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'YyLineClassTrain');
        if(!$lineClassTrainR->ret){
            return $this->output($lineClassTrainR);
        }

        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']),'YyLineClass');
        if(!$lineClassR->ret){
            return $this->output($lineClassR);
        }
        $startAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['start_address_code'])->data['address']."]";
        $start_route = $startAddress . $lineClassR->data['start_name'];
        $endAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['end_address_code'])->data['address']."]";
        $end_route = $endAddress . $lineClassR->data['end_name'];
        $where['mchid'] = $this->state->user_id;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name,car_brand');
        $this->assign("drivers",$driver->data);
        $this->assign("lineClassTrainData",$lineClassTrainR->data);
        $this->assign('start_route', $start_route);
        $this->assign('end_route', $end_route);
        $this->display('Tpl/Admin/YyLine/yy_line_class_train_edit.html');
    }
}
<?php

import('@/Action/Admin/OrderManagerAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/2/8
 * Time: 10:54
 */
class MemberPageAction extends AdminCoreAction
{
    //会员管理->分台与渠道管理
    public function mbranchspage()
    {
        $this->redirectMerchantUrl('/#/userManage/branchManage/lineBranch');
        $this->display('Tpl/Admin/Member/branchs.html');
    }

    //会员管理->分台编辑
    public function mbrancheditpage($branchid)
    {
        $adminR = $this->find(array('admin_id' => $branchid), "Admin");
        $this->assign("admin", $adminR->data);
        $this->display('Tpl/Admin/Member/branch_edit.html');
    }
    //会员管理->新增分台
    public function mbranchpage()
    {
        $this->display('Tpl/Admin/Member/branch.html');
    }

    //会员管理->已删除分台
    public function mfrezbranchpage()
    {
        $this->display('Tpl/Admin/Member/fb.html');
    }

    //会员管理->乘客管理
    public function mpassagerspage()
    {
        $this->redirectMerchantUrl('/#/userManage/passengerManage/passenger');
        $this->display('Tpl/Admin/Member/passagers.html');
    }

    //会员管理->待认证乘客管理
    public function mepassagerspage()
    {
        $this->display('Tpl/Admin/Member/passagers_examine.html');
    }

    //会员管理->乘客编辑
    public function passagereditpage($passenger_id)
    {
        $passenger = $this->sudoGetEntityById('passenger_id', $passenger_id, 'Passenger\\Account', 'getPassenger', null)->data;
        $this->assign('passenger', $passenger);
        $this->display('Tpl/Admin/Member/passager_edit.html');
    }

    //会员管理->添加乘客
    public function mpassagerpage()
    {
        $this->display('Tpl/Admin/Member/passager.html');
    }

    //会员管理->已删除乘客
    public function mfrezpassagerpage()
    {
        $this->display('Tpl/Admin/Member/fp.html');
    }


    //会员管理->车主列表
    public function mdriverspage()
    {
        if(isset($_REQUEST['redirect']) && $_REQUEST['redirect'] == 'false') {
            $this->display('Tpl/Admin/Member/drivers.html');
            return;
        }
        $this->redirectMerchantUrl('/#/userManage/driverPermission/driver');
    }

    /**
     * 跳转商户后台
     * @Date 2025.03.10
     * <AUTHOR>
     */
    public function redirectMerchantUrl($uri)
    {
        $mchid = $this->state->user_id;
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {
            return redirect(C('CC_MCH_HOST') . $uri . '?token=' . $token['data']['access_token']);
        }
    }

    //派单-司机列表
    public function appointorderdriverpage($order_id)
    {
        $this->assign('orderId', $order_id);
        $this->display('Tpl/Admin/Member/appointorderdrivers.html');
    }

    //会员管理->新增车主
    public function mdriverpage()
    {
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$this->state->user_id}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {
            //商户总台管理员才能查看“开票/积分商城/保险配置”
            if ($this->admin_group_id == 2) {
                /**
                 * @Date 2022.08.03
                 * <AUTHOR>
                 */
                $mchUrl = '<a class="" href="' . C('CC_MCH_HOST') . '/#/carManange/index?token=' . $token['data']['access_token'] . '" target="_blank" ">
                    添加车辆</a>';
                $this->assign('vehicle_url', $mchUrl);
            }
        }
        $this->assign('merchantNo', $this->getCipmchidByMchid($this->state->user_id)->data['ciphertext']);
        $this->display('Tpl/Admin/Member/driver.html');
    }


    //会员管理->已删除车主
    public function mfrezedriverpage()
    {
        $this->display('Tpl/Admin/Member/fd.html');
    }

    //会员管理->司机编辑
    public function drivereditpage($driver_id)
    {
        $driver = $this->sudoGetEntityById('driver_id', $driver_id, 'Driver\\Account', 'getDriver', null)->data;
        $driverGroupAttributeValue = $this->find(array('driver_id' => $driver_id), "DriverGroupAttributeValue");

        $this->assign('driver', $driver);
        $this->assign('driverGroupAttributeValue', $driverGroupAttributeValue->data);
        $this->assign('admin_group_id', $this->admin_group_id);
        $this->display('Tpl/Admin/Member/driver_edit.html');
    }

    public function driverlinespage($driver_id)
    {
        $driver = $this->sudoGetEntityById('driver_id', $driver_id, 'Driver\\Account', 'getDriver', null)->data;

        $drvierLineWhere = 'mchid = ' . $this->state->user_id . ' AND driver_id = ' . $driver_id . ' AND type = ' . \CommonDefine::ORDER_TYPE_1;
        $driverLinesR = $this->select($drvierLineWhere, 1, 10000, null, 'DriverLine');
        foreach ($driverLinesR->data as $k => $v) {
            $lineWhere = 'id = ' . $v['line_id'] . ' AND is_del = ' . \CommonDefine::IS_DEL_0;
            $lineR = $this->find($lineWhere, 'Line');
            if ($lineR->ret) {
                $driverLinesR->data[$k]['line'] = $lineR->data;
            } else {
                unset($driverLinesR->data[$k]);
            }
        }

        $drvierLineChartersWhere = 'mchid = ' . $this->state->user_id . ' AND driver_id = ' . $driver_id . ' AND type = ' . \CommonDefine::ORDER_TYPE_2;
        $driverLineChartersR = $this->select($drvierLineChartersWhere, 1, 10000, null, 'DriverLine');
        foreach ($driverLineChartersR->data as $k => $v) {
            $lineCharterWhere = 'id = ' . $v['line_id'] . ' AND is_del = ' . \CommonDefine::IS_DEL_0;
            $lineCharterR = $this->find($lineCharterWhere, 'LineChartered');
            if ($lineCharterR->ret) {
                $driverLineChartersR->data[$k]['line'] = $lineCharterR->data;
            } else {
                unset($driverLineChartersR->data[$k]);
            }
        }

        $drvierLineFastsWhere = 'mchid = ' . $this->state->user_id . ' AND driver_id = ' . $driver_id . ' AND type = ' . \CommonDefine::ORDER_TYPE_7;
        $driverLineFastsR = $this->select($drvierLineFastsWhere, 1, 10000, null, 'DriverLine');
        foreach ($driverLineFastsR->data as $k => $v) {
            $lineFastWhere = 'id = ' . $v['line_id'] . ' AND is_del = ' . \CommonDefine::IS_DEL_0;
            $lineFastR = $this->find($lineFastWhere, 'LineFast');
            if ($lineFastR->ret) {
                $driverLineFastsR->data[$k]['line'] = $lineFastR->data;
            } else {
                unset($driverLineFastsR->data[$k]);
            }
        }

        $drvierLineTaxisWhere = 'mchid = ' . $this->state->user_id . ' AND driver_id = ' . $driver_id . ' AND type = ' . \CommonDefine::ORDER_TYPE_11;
        $driverLineTaxisR = $this->select($drvierLineTaxisWhere, 1, 10000, null, 'DriverLine');
        foreach ($driverLineTaxisR->data as $k => $v) {
            $lineTaxiWhere = 'id = ' . $v['line_id'] . ' AND is_del = ' . \CommonDefine::IS_DEL_0;
            $lineTaxiR = $this->find($lineTaxiWhere, 'LineTaxi');
            if ($lineTaxiR->ret) {
                $driverLineTaxisR->data[$k]['line'] = $lineTaxiR->data;
            } else {
                unset($driverLineTaxisR->data[$k]);
            }
        }

        $fields = ' lc.id,lct.line_class_train_no,lc.start_name,lc.end_name,lct.start_date,lc.start_time ';
        $where = 'lctd.driver_id = ' . $driver_id . ' AND lc.mchid = ' . $this->state->user_id;
        $where .= ' AND lct.is_del = ' . \CommonDefine::IS_DEL_0;
        $where .= ' AND lc.is_del = ' . \CommonDefine::IS_DEL_0;
        $where .= " AND lct.start_date >= '" . date('Y-m-d', time()) . "'";
        $lineClassTrains = M()->table('cp_line_class_train_driver lctd')
            ->join('LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = lctd.line_class_train_id')
            ->join('LEFT JOIN cp_line_class lc ON lc.id = lct.line_class_id')
            ->where($where)
            ->field($fields)
            ->select();
        $driverLineClassTrains = [];
        foreach ($lineClassTrains as $k => $v) {
            $lineClass['line_class_train_no'] = $v['line_class_train_no'];
            $lineClass['start_date'] = $v['start_date'];
            $driverLineClassTrains[$v['id']]['id'] = $v['id'];
            $driverLineClassTrains[$v['id']]['start_name'] = $v['start_name'];
            $driverLineClassTrains[$v['id']]['end_name'] = $v['end_name'];
            $driverLineClassTrains[$v['id']]['start_time'] = $v['start_time'];
            $driverLineClassTrains[$v['id']]['line_class_trains'][] = $lineClass;
        }

        //        $lineClassDriverTrainWhere = 'mchid = '. $this->state->user_id. ' AND driver_id = '.$driver_id;
        //        $lineClassDriversR = $this->select($lineClassDriverWhere, 1, 10000, null, 'LineClassDriver');
        //        if($lineClassDriversR->ret){
        //            foreach ($lineClassDriversR->data as $k=>$v){
        //                $lineClassWhere = 'id = '.$v['line_class_id']. ' AND is_del = '.\CommonDefine::IS_DEL_0;
        //                $lineClassR = $this->find($lineClassWhere, 'LineClass');
        //                if($lineClassR->ret){
        //                    $lineClassDriversR->data[$k]['line'] = $lineClassR->data;
        //                }else{
        //                    unset($lineClassDriversR->data[$k]);
        //                }
        //            }
        //        }

        $this->assign('driver', $driver);
        $this->assign('driver_lines', $driverLinesR->data);
        $this->assign('driver_line_charters', $driverLineChartersR->data);
        $this->assign('driver_line_fasts', $driverLineFastsR->data);
        $this->assign('driver_line_taxis', $driverLineTaxisR->data);
        $this->assign('driver_line_train_classs', $driverLineClassTrains);
        $this->display('Tpl/Admin/Member/driver_lines.html');
    }

    //会员管理->顺风车车主列表
    public function mfreeridedriverspage()
    {
         if(isset($_REQUEST['redirect']) && $_REQUEST['redirect'] == 'false') {
            $this->display('Tpl/Admin/Member/free_ride_drivers.html');
            return;
        }
        $this->redirectMerchantUrl('/#/userManage/driverPermission/driver');
    }

    //会员管理->顺风车车主列表
    public function mfreeridedriverlistspage()
    {
        $this->display('Tpl/Admin/Member/free_ride_drivers.html');
    }

    //会员管理->新增顺风车车主
    public function mfreeridedriverpage()
    {
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$this->state->user_id}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {
            //商户总台管理员才能查看“开票/积分商城/保险配置”
            if ($this->admin_group_id == 2) {
                /**
                 * @Date 2022.08.03
                 * <AUTHOR>
                 */
                $mchUrl = '<a class="" href="' . C('CC_MCH_HOST') . '/#/carManange/index?token=' . $token['data']['access_token'] . '" target="_blank" ">
                    添加车辆</a>';
                $this->assign('vehicle_url', $mchUrl);
            }
        }
        $this->assign('merchantNo', $this->getCipmchidByMchid($this->state->user_id)->data['ciphertext']);

        $this->display('Tpl/Admin/Member/free_ride_driver.html');
    }


    //会员管理->已删除顺风车车主
    public function mfreeridefrezedriverpage()
    {
        $this->display('Tpl/Admin/Member/ffrd.html');
    }

    //会员管理->顺风车司机编辑
    public function freeridedrivereditpage($driver_id)
    {
        $driver = $this->sudoGetEntityById('driver_id', $driver_id, 'Driver\\Account', 'getDriver', null)->data;
        $this->assign('driver', $driver);
        $this->assign('admin_group_id', $this->admin_group_id);
        $this->doAddLog('编辑前顺风车司机', json_encode($driver));
        $this->display('Tpl/Admin/Member/free_ride_driver_edit.html');
    }

    //会员管理->待审核司机
    public function mexaminedriverspage()
    {
        $this->redirectMerchantUrl('/#/userManage/driverPermission/driver');
        $where['parent_admin_id'] = $this->state->user_id;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $branchsR = $this->select($where, null, null, null, 'Admin', 'admin_id,mchname');
        $branchs = array();
        foreach ($branchsR->data as $k => $branch) {
            $branchs[] = array(
                'id' => $branch['admin_id'],
                'branch_name' => $branch['mchname']
            );
        }

        $this->assign('branchs', $branchs);
        $this->display('Tpl/Admin/Member/ed.html');
    }

    public function driversrecomendpage($account, $type)
    {
        $accountData = array('account' => $account, 'account_type' => $type);
        $this->assign('accountData', $accountData);
        $this->display('Tpl/Admin/Member/driversrecomend.html');
    }
}

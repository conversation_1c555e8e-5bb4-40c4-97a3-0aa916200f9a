<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-12-04
 * Time: 11:51
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Admin/OrderManagerAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 营销管理模块
 *
 * <AUTHOR>
 */

class NoticeManagerAction extends AdminCoreAction{
    /**
     * 获取短信通知列表
     * @param string $fields 查询的字段（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $account_type 发送对象（1-乘客；2-司机；3-分台;4，车主，乘客,分台; 默认为空，查询所有）
     * @param datetime $min_send_time 发送时间最小值（默认为空，查询所有）
     * @param datetime $max_send_time 发送时间最大值（默认为空，查询所有）
     */
    public function getNotices($fields = null, $page = 1, $size = 10, $account_type = null, $min_send_time = null, $max_send_time = null)
    {
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        if (!empty($account_type)) {
            if($account_type == \CommonDefine::USER_TYPE_1 || $account_type == \CommonDefine::USER_TYPE_2 || $account_type == \CommonDefine::USER_TYPE_3){
                $where['account_type'] = intval($account_type);
            }else if($account_type == 4){
                $where['account_type'] = array('in', \CommonDefine::USER_TYPE_1.','.\CommonDefine::USER_TYPE_2.','.\CommonDefine::USER_TYPE_3 );
            }else if($account_type == 5){
                $where['account_type'] = 0 ;
            }
        }
        if (!empty($min_send_time) && empty($max_send_time)) {
            $where['send_time'] = array('egt', $min_send_time);
        } else if (empty($min_send_time) && !empty($max_send_time)) {
            $where['send_time'] = array('elt', $max_send_time);
        } else if (!empty($min_send_time) && !empty($max_send_time)) {
            $where['send_time'] = array(array('egt', $min_send_time), array('elt', $max_send_time));
        }
        $where['mchid'] = $this->state->user_id;
        $r = $this->select($where, $page, $size, 'send_time desc', 'SmsMarketing', $fields);
        $this->doAddLog("查看营销列表");
        return $this->output($r);
    }

    /**
     * 群发通知
     * @param int $account_type 发送对象（1-乘客；2-司机；3-分台;4，车主，乘客,分台;5-自定义发送）
     * @param string $content 内容
     */
    public function doSendNotices($account_type, $mobiles, $contents = null,$sing_id)
    {
        $smsUtil = new \SMSUtil(\CommonDefine::ZHANGJUNCHUANMEI);
        $mchid = $this->state->user_id;
             if(empty($contents)){
        return $this->output(new \ResultModel(false, '请填写发送内容'));
                }
        //检查是否包含非法关键字
        $res= $passengersR = $this->select(null, 1, 1100, null, 'Words');
        $ar=[];
        foreach($res->data as $key=>$value){
            $ar[$key]=$value['words'];
        }
        $badword1 = array_combine($ar,array_fill(0,count($ar),'XXX'));
        $keyword=strtr($contents,$badword1);
        if(preg_match('/(XXX)/',$keyword)){
            return $this->output(new \ResultModel(false, '包含非法关键词，请检查短信内容！'));
        }
        //检查系统短信数量
        $marketTemplateS = $this->find(array('mchid' => $mchid,'id'=>$sing_id), 'MarketTemplateSign');
        if($marketTemplateS->ret){
        }else{
            return $this->output(new \ResultModel(false, '抱歉，未能找到短信签名，请稍后再试试！'));
        }
        //检查短信长度
        $contents=$marketTemplateS->data['sign'].$contents;
        $strLen=mb_strlen($contents,"UTF-8")+mb_strlen($marketTemplateS->data['sign'],"UTF-8");
        if($strLen>500){
            return $this->output(new \ResultModel(false, '短信长度不能超过500个字！'));
        }
        $NUM=1;
        if($strLen>70){
            $NUM=1+ceil(($strLen-70)/67);
        }
        //检查是否包含非法关键字
        $tempcontent = $contents;
        //剩余端数量检测
        switch(intval($account_type)){
            case 1:{
                $sendCountPR = $this->count(array('mchid' => $mchid,'cellphone' => array('neq', 'NULL')), 'Passenger');
                //判断系统短信数量。
                if($smsUtil->CheckSmsNumber($sendCountPR->data*$NUM)){
                    return $this->output(new \ResultModel(false, '系统短信不足，请联系管理员！'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountPR->data*$NUM, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 2:{
                $sendCountDR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Driver');
                //判断系统短信数量。
                if($smsUtil->CheckSmsNumber($sendCountPR->data*$NUM)){
                    return $this->output(new \ResultModel(false, '系统短信不足，请联系管理员！'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountDR->data*$NUM, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 3:{
                $sendCountBR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Admin');
                //判断系统短信数量。
                if($smsUtil->CheckSmsNumber($sendCountPR->data*$NUM)){
                    return $this->output(new \ResultModel(false, '系统短信不足，请联系管理员！'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountBR->data*$NUM, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 4:{
                $sendCountPR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Passenger');
                $sendCountDR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Driver');
                $sendCountBR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Admin');
                //判断系统短信数量。
                if($smsUtil->CheckSmsNumber($sendCountPR->data*$NUM + $sendCountDR->data*$NUM + $sendCountBR->data*$NUM)){
                    return $this->output(new \ResultModel(false, '系统短信不足，请联系管理员！'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountPR->data*$NUM + $sendCountDR->data*$NUM + $sendCountBR->data*$NUM, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 5:{
                if(empty($mobiles)){
                    return $this->output(new \ResultModel(false, '请填写手机号或导入手机号'));
                }
                $mobilesArr = explode(',', $mobiles);
                if(empty($mobilesArr)){
                    return $this->output(new \ResultModel(false, '请填正确手机号或导入正确的手机号'));
                }
                //判断系统短信数量。
                if($smsUtil->CheckSmsNumber(count($mobilesArr)*$NUM)){
                    return $this->output(new \ResultModel(false, '系统短信不足，请联系管理员！'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, count($mobilesArr)*$NUM, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
        }
        $data = null;
        if (intval($account_type) === 1 || intval($account_type) === 4) {
            $where['mchid'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $passengersR = $this->select($where, $page, $size, null, 'Passenger');
                if($passengersR->ret){
                    foreach($passengersR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_1, $v['passenger_id'], $data, $tempcontent, $mchid,$NUM);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        if (intval($account_type) === 2 || intval($account_type) === 4){
            $where['mchid'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $driversR = $this->select($where, $page, $size, null, 'Driver');
                if($driversR->ret){
                    foreach($driversR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_2, $v['driver_id'], $data, $tempcontent,$mchid,$NUM);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        if (intval($account_type) === 3 || intval($account_type) === 4){
            $where['parent_admin_id'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $branchsR = $this->select($where, $page, $size, null, 'Admin');
                if($branchsR->ret){
                    foreach($branchsR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_3, $v['driver_id'], $data, $tempcontent,$mchid,$NUM);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        //自定义
        $U=0;
        if (intval($account_type) === 5){
            if(empty($mobiles)){
                return $this->output(new \ResultModel(false, '请填写手机号或导入手机号'));
            }
            $mobilesArr = explode(',', $mobiles);
            if(empty($mobilesArr)){
                return $this->output(new \ResultModel(false, '请填正确手机号或导入正确的手机号'));
            }
            foreach($mobilesArr as $k => $v){
                $ret = $smsUtil->sendTemplateSMSMarketingTwo($v,0,0, $data, $tempcontent, $mchid,$NUM);
                if(!$ret){
                    break;
                }
            }
        }
        $this->doAddLog("群发营销通知");
        return $this->output(new \ResultModel(true, '群发完成'));
    }
    public function doSendNoticesss($account_type, $mobiles, $content = null,$Temp_id)
    {
        $smsUtil = new \SMSUtil(\CommonDefine::ZHANGJUNCHUANMEI);
        $mchid = $this->state->user_id;
        if(empty($content)){
            return $this->output(new \ResultModel(false, '请填写发送内容'));
        }
        foreach($content as $k => $v){
            if($v){
                $content[$k] = trim($v);
            }
        }

        $marketTemplateR = $this->find(array('mchid' => $mchid,'id'=>$Temp_id), 'MarketTemplate');
        if($marketTemplateR->ret){
        }else{
           /* $marketTemplateR = $this->find(array('mchid' => 0), 'MarketTemplate');*/
            return $this->output(new \ResultModel(false, '抱歉，未能找到短信模板，请稍后再试试！'));
        }
        $tempcontent = $marketTemplateR->data['market_template_content'];
        if(!$smsUtil->checkSMSContentLenIsALlowTwo($tempcontent, $content, \CommonDefine::SMS_TYPE_2)){
            return $this->output(new \ResultModel(false, '发送的内容超出短信允许的最大长度'));
        }
        //剩余端数量检测
        switch(intval($account_type)){
            case 1:{
                $sendCountPR = $this->count(array('mchid' => $mchid,'cellphone' => array('neq', 'NULL')), 'Passenger');
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountPR->data, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 2:{
                $sendCountDR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Driver');
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountDR->data, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 3:{
                $sendCountBR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Admin');
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountBR->data, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 4:{
                $sendCountPR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Passenger');
                $sendCountDR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Driver');
                $sendCountBR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')), 'Admin');
                if(!$smsUtil->checkMchMarketingSms($mchid, $sendCountPR->data + $sendCountDR->data + $sendCountBR->data, false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
            case 5:{
                if(empty($mobiles)){
                    return $this->output(new \ResultModel(false, '请填写手机号或导入手机号'));
                }
                $mobilesArr = explode(',', $mobiles);
                if(empty($mobilesArr)){
                    return $this->output(new \ResultModel(false, '请填正确手机号或导入正确的手机号'));
                }
                if(!$smsUtil->checkMchMarketingSms($mchid, count($mobilesArr), false)){
                    return $this->output(new \ResultModel(false, '营销短信数量不足，请及时增购'));
                }
                break;
            }
        }

        $data = $content;
        if (intval($account_type) === 1 || intval($account_type) === 4) {
            $where['mchid'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $passengersR = $this->select($where, $page, $size, null, 'Passenger');
                if($passengersR->ret){
                    foreach($passengersR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_1, $v['passenger_id'], $data, $tempcontent, $mchid);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        if (intval($account_type) === 2 || intval($account_type) === 4){
            $where['mchid'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $driversR = $this->select($where, $page, $size, null, 'Driver');
                if($driversR->ret){
                    foreach($driversR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_2, $v['driver_id'], $data, $tempcontent,$mchid);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        if (intval($account_type) === 3 || intval($account_type) === 4){
            $where['parent_admin_id'] = $mchid;
            $where['cellphone'] = array('neq', 'NULL');
            $page = 1;
            $size = 10;
            $while = false;
            do{
                $branchsR = $this->select($where, $page, $size, null, 'Admin');
                if($branchsR->ret){
                    foreach($branchsR->data as $k => $v){
                        $ret = $smsUtil->sendTemplateSMSMarketingTwo($v['cellphone'], \CommonDefine::USER_TYPE_3, $v['driver_id'], $data, $tempcontent,$mchid);
                        if(!$ret){
                            break;
                        }
                    }
                    $while = true;
                    $page ++;
                }else{
                    $while = false;
                }
            }while($while);
        }
        //自定义
        if (intval($account_type) === 5){
            if(empty($mobiles)){
                return $this->output(new \ResultModel(false, '请填写手机号或导入手机号'));
            }
            $mobilesArr = explode(',', $mobiles);
            if(empty($mobilesArr)){
                return $this->output(new \ResultModel(false, '请填正确手机号或导入正确的手机号'));
            }

            foreach($mobilesArr as $k => $v){
                $ret = $smsUtil->sendTemplateSMSMarketingTwo($v,0,0, $data, $tempcontent, $mchid);
                if(!$ret){
                    break;
                }
            }
        }

        $this->doAddLog("群发营销通知");
        return $this->output(new \ResultModel(true, '群发完成'));
    }
    /**
     *获取营销模板
     */
    public function getTemplate($page=1,$size=10){
        $where['mchid']=$this->state->user_id;
        $r = $this->select($where, $page, $size, 'id desc', 'MarketTemplate');
        foreach($r->data as $key=>$value){
            $marketTemplateContentArr = preg_split( '/\{-?\d+\}/', $value['market_template_content']);
            $market_template_exampleArr=json_decode($value['market_template_example'],true);
            $str='';
            foreach($marketTemplateContentArr as $key1=>$value1){
                $str.=$value1."<span style=\"color: red\">".$market_template_exampleArr[$key1+1]."</span>";
            }
            $r->data[$key]['market_template_example']=$str;
        }
        $this->doAddLog("查看营销模板");
        return $this->output($r);
    }
    public function getTemplateAdmin($page=1,$size=10){
        $r = $this->select(null, $page, $size, 'id desc', 'MarketTemplate');
        foreach($r->data as $key=>$value){
            $marketTemplateContentArr = preg_split( '/\{-?\d+\}/', $value['market_template_content']);
            $market_template_exampleArr=json_decode($value['market_template_example'],true);
            $str='';
            foreach($marketTemplateContentArr as $key1=>$value1){
                $str.=$value1."<span style=\"color: red\">".$market_template_exampleArr[$key1+1]."</span>";
            }
            $r->data[$key]['market_template_example']=$str;
        }
        $this->doAddLog("查看营销模板");
        return $this->output($r);
    }
    public function delTemp($id){
       $re=M('market_template')->delete($id);
        if($re){
            echo 200;exit();
        }else{
            echo  400;exit();
        }
    }
    public function saveTempStatus($status,$id){
        if($status==1){
            $data['status']=1;
            $msg='通过';
        }else{
            $data['status']=2;
            $msg='拒绝';
        }
        $data['id']=$id;
        $r=M('market_template')->save($data);
        $this->doAddLog("审核短信模板-".$msg);
        if($r){
           echo json_encode(['ret'=>true,'data'=>'修改成功！']);exit();
        }else{
            echo json_encode(['ret'=>false,'data'=>'修改失败！']);exit();
        }
    }
    /**
     * 导入Excel
     */
    public function doImportExcelData(){
        $mchid = $this->state->user_id;
        $startTime = microtime (true);

        $uploadFile = $_FILES['excel_file'];
        $imgExArr = array('xls','xlsx');

        if(empty($uploadFile)){
            return $this->output(new ResultModel(false, '上传失败'));
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');

        $fileArr = explode('.', $uploadFile['name']);
        $imgEx = strtolower(end($fileArr));
        if (!in_array($imgEx, $imgExArr)) {
            return $this->output(new \ResultModel(false, '不支持该文件类型'));
        }

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_TEMP'] . DIRECTORY_SEPARATOR .md5($this->state->user_id);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;
        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        $file = $fullPath . DIRECTORY_SEPARATOR . $realName;

        if (!move_uploaded_file($uploadFile['tmp_name'], $file)) {
            return $this->output(new \ResultModel(false, '上传证件失败'));
        }

        $phpExcelUtil = new \PhpExcelUtil();
        $excelData = $phpExcelUtil->importExecl($file);
        if($excelData['error'] == 0){
            unlink($file);
            return $this->output(new \ResultModel(false, $excelData['message']));
        }else{

        }

        $mobiles = '';
        foreach($excelData['data'] as $sheet=>$sheetData){
            if(!empty($sheetData['Content'])){
                $tempMobiles = $this->cube_implode(',', $sheetData['Content']);
                if(!empty($tempMobiles)){
                    if(empty($mobiles)){
                        $mobiles = $tempMobiles;
                    }else{
                        $mobiles = $mobiles.','.$tempMobiles;
                    }
                }
            }
        }

        $mobilesArr = explode(',', $mobiles);
        $totalCount = count($mobilesArr);
        foreach($mobilesArr as $k=>$v){
            if(empty($v)){
                unset($mobilesArr[$k]);
            }
        }
        $nullCount = $totalCount - count($mobilesArr);//空格
        $mobilesArr = array_unique($mobilesArr);
        $mobiles = implode(',', $mobilesArr);
        $validCount = count($mobilesArr);
        $repeatCount = $totalCount - $validCount - $nullCount;
        $marketingUserMode = M('marketing_user');
        $selectWhere['mchid'] = $mchid;
        $selectWhere['cellphone'] = array('in',$mobiles);
        $selectMobilesArr = $marketingUserMode->where($selectWhere)->field('cellphone')->select();
        if(!empty($selectMobilesArr)){
            $selectMobilesStr = $this->cube_implode(',',$selectMobilesArr);
            $selectMobilesArr = explode(',',$selectMobilesStr);
            $intersectMobilesArr = array_intersect($mobilesArr, $selectMobilesArr);
            $mobilesArr = array_diff($mobilesArr, $intersectMobilesArr);
        }

        $dataListArr = array();
        $currentTime = time();
        foreach($mobilesArr as $k => $v){
            $dataListArr[] = array(
                'cellphone' => $v,
                'mchid' => $mchid,
                'create_time' => date('Y-m-d H:i:s', $currentTime),
                'update_time' => date('Y-m-d H:i:s', $currentTime),
            );
        }

        $insertCount = count($dataListArr);
        $marketingUserMode->where('');
        $marketingUserA = $marketingUserMode->addAll($dataListArr);
        $endTime = microtime(true);

        return $this->output(new ResultModel(true,array('mobiles'=>$mobiles, 'total_count' => $totalCount, 'valid_count' => $validCount,'null_count'=> $nullCount,'insert_count'=> $insertCount, 'repeat_count' => $repeatCount, 'use_microtime' => ($endTime - $startTime)), 1));
    }
   //申请短信营销模板
    public function addSmsTemp($content = null,$market_template_content,$sign){
        $mchid=$this->state->user_id;
        $data['mchid']=$mchid;
        $str='{';
        foreach($content as $key=>$value){
            $str.='"'.($key+1).'"';
            $str.=':';
            $str.='"'.$value.'"';
            if(($key+1)!=count($content)){
                $str.=',';
            }
        }
        $str.='}';
        foreach($content as $k => $v){
            if($v){
                $content[$k] = trim($v);
            }
        }
        $smsUtil = new \SMSUtil(\CommonDefine::ZHANGJUNCHUANMEI);
        $market_template_content = $sign.$market_template_content;
        if(!$smsUtil->checkSMSContentLenIsALlowTwo($market_template_content, $content, \CommonDefine::SMS_TYPE_2)){
           echo jsonp_encode(false,'短信模板内容长度超过最大限制70个字符！');exit();
        }
        $data['sign']=$sign;
        $data['market_template_content']=$market_template_content;
        $data['market_template_example']=$str;
        $data['status']=0;
        $r= $this->add('MarketTemplate',$data);
        $mchR = $this->find(array('admin_id' => $mchid),'Admin');
        $sms = new \SMSUtil(\CommonDefine::RONGLIANYUN);
        $sms->sendTemplateSMS('15680770083', array('商户:'.$mchR->data['mchname'].'申请了新的短信模板', '请及时'), \SMSUtil::TEMP_ID_DEPOSITRESULT, 181);
        $this->doAddLog("申请营销模板");
       echo jsonp_encode($r);exit();
    }

    /**
     * 营销信息id
     * @param int $id 营销短信ID
     */
    public function doDeleteNotice($id)
    {
        $ret = $this->save('SmsMarketing', array('id' => $id, 'is_del' => \CommonDefine::IS_DEL_1));
        $this->doAddLog("删除通知");
        return $this->output($ret);
    }

    /**
     * 批量删除通知
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doDeleteNotices($ids)
    {
        $ids_arr = explode(",", $ids);
        if (empty($ids_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($ids_arr as $value) {
            if (!empty($value)) {
                $ret = $this->save('SmsMarketing', array('id' => $value, 'is_del' => \CommonDefine::IS_DEL_1));
                if(!$ret->ret){
                    $this->transRollback();
                    return $this->output($ret);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量删除通知");
        return $this->output(new ResultModel(true));
    }
}
<?php

import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Admin/OrderManagerAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 线路管理模块
 *
 * <AUTHOR>
 */
class YyLineManagerAction extends AdminCoreAction
{

    /**
     * 获取线路列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function getLineare($oid, $mchid)
    {
        $r = $this->select(array('address_id' => $oid, 'mchid' => $mchid), null, null, 'address_id asc', 'SupportCity', 'address_id,name');
        return $this->output($r);
    }

    public function getLineList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0,$search_branchid=0,$search_line_id=0,$search_start_name=null,$search_end_name=null)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if($search_branchid){
            $str .= ' AND branchid = '.$search_branchid;
        }else if(!empty($branchid) && $branchid){
            $str .= ' AND branchid = '.$branchid;
        }
        if($search_line_id){
            $str .= ' AND id = '.$search_line_id;
        }
        if(!empty($search_start_name)){
            $str .= " AND start_name like '%".$search_start_name."%' ";
        }
        if(!empty($search_end_name)){
            $str .= " AND end_name like '%".$search_end_name."%' ";
        }

        $str .= ' AND is_phone_line = '.\CommonDefine::LINE_PHONE_TYYE_0;

        $r = $this->select($str, $page, $size, 'create_time desc', 'Line');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }
                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];
            }
        }
        return $this->output($r);
    }

    /*
     * 获取快车路线
     */
    public function getLineFastList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0,$search_branchid=0,$search_line_id=0,$search_start_name=null,$search_end_name=null)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if($search_branchid){
            $str .= ' AND branchid = '.$search_branchid;
        }else if(!empty($branchid) && $branchid){
            $str .= ' AND branchid = '.$branchid;
        }
        if($search_line_id){
            $str .= ' AND id = '.$search_line_id;
        }
        if(!empty($search_start_name)){
            $str .= " AND start_name like '%".$search_start_name."%' ";
        }

        $r = $this->select($str, $page, $size, 'create_time desc', 'LineFast');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $startAddress = "[" . $this->checkingGdAddressCode($line['start_province_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_city_code'])->data['address'] . "]";
                $r->data[$k]['route'] = $startAddress;
            }
        }
        return $this->output($r);
    }


    /**
     * 获取电话叫车列表
     * @param null $fields
     * @param $mchid
     * @param null $branchid
     * @param int $page
     * @param int $size
     * @param int $st
     * @param int $search_branchid
     * @param int $search_line_id
     * @return null|resultModel
     */
    public function getLinePhoneList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0,$search_branchid=0,$search_line_id=0,$search_start_name=null,$search_end_name=null,$is_phone_line_verify = -1)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if($search_branchid){
            $str .= ' AND branchid = '.$search_branchid;
        }else if(!empty($branchid) && $branchid){
            $str .= ' AND branchid = '.$branchid;
        }
        if($search_line_id){
            $str .= ' AND id = '.$search_line_id;
        }
        if(!empty($search_start_name)){
            $str .= " AND start_name like '%".$search_start_name."%' ";
        }
        if(!empty($search_end_name)){
            $str .= " AND end_name like '%".$search_end_name."%' ";
        }
        if($is_phone_line_verify != -1){
            $str .= ' AND is_phone_line_verify = '.$is_phone_line_verify;
        }


        $str .= ' AND is_phone_line = '.\CommonDefine::LINE_PHONE_TYYE_1;

        $r = $this->select($str, $page, $size, 'create_time desc', 'Line');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }
                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];

                //司机信息
                $r->data[$k]['driver_info'] = '';
                $driverWhere['dl.line_id'] = $line['id'];
                $driverWhere['dl.type'] = \CommonDefine::ORDER_TYPE_1;
                $driversR = M()->table('cp_driver_line dl')
                    ->join('LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id')
                    ->where($driverWhere)
                    ->field('d.driver_id,d.name,d.cellphone')
                    ->select();
                if(!empty($driversR)){
                    foreach($driversR as $i => $driver){
                        $r->data[$k]['driver_info'] .=  ($i+1)."、[".$driver['name']."] ".$driver['cellphone']."<br/>";
                    }
                }
            }
        }
        return $this->output($r);
    }

    public function getDriverList($branchid=0, $driver_role_type = \CommonDefine::DRIVER_ROLE_0){
        if($this->admin_group_id!=3){
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
//        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        if($driver_role_type != -1){
            $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        }
        $where['branchid']=$branchid;
        $driver = $this->select($where,null,null,null,'Driver','driver_id,name,cellphone');
        $this->output($driver);
    }

    public function doSetIsShow($id = 0, $type = 0, $status = 0)
    {
        // type 1-拼车
        if ($id && $type && $status) {
            if ($status == 1) {
                $data['is_show'] = 1;
            } else {
                $data['is_show'] = 0;
            }
            $data['id'] = $id;
            if ($type == 1) {
                $model = 'Line';
            } elseif ($type == 2) {
                $model = 'LineChartered';
            } elseif ($type == 3) {
                $model = 'LineClass';
            } else if ($type == 4) {
                $data['take_goods_price_id'] = $id;
                unset($data['id']);
                $model = 'TakeGoodsPrice';
            } else if ($type == 5) {
                $data['agency_id'] = $id;
                unset($data['id']);
                $model = 'Agency';
            } else if ($type == 7) {
                $model = 'LineFast';
            }
            if (!isset($data) && !$data && isset($model)) {
                return $this->output(new ResultModel(false, "操作失败，请稍后重试！"));
            }
            $r = $this->save($model, $data);
            return $this->output($r);
        } else {
            return $this->output(new ResultModel(false, "操作失败，请稍后重试！"));
        }

    }

    //获取线路详细
    public function getLineTotal($line_id)
    {
        $line = $this->find(array('id' => $line_id), 'Line');
        if ($line->ret) {
            $startArr = $this->getProvinceCityAreaCodeByCode($line->data['start_address_code']);
            $line->data['start_province_code'] = $startArr['province_code'];
            $line->data['start_city_code'] = $startArr['city_code'];
            $line->data['start_area_code'] = $startArr['area_code'];
            $endArr = $this->getProvinceCityAreaCodeByCode($line->data['end_address_code']);
            $line->data['end_province_code'] = $endArr['province_code'];
            $line->data['end_city_code'] = $endArr['city_code'];
            $line->data['end_area_code'] = $endArr['area_code'];
            $line->data['lci'] = $this->select(array('line_id' => $line->data['id'], 'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'LineFkLineCategory', 'line_category_id lci')->data;
            $line->data['dls'] = $this->select(array('line_id' => $line->data['id'], 'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $line->data['lc'] = array_column($line->data['lci'], 'lci');
        $dl = implode(",", array_column($line->data['dls'], 'dls'));
        unset($line->data['lci'], $line->data['dls']);
        $where['mchid'] = $this->state->user_id;
        $is_del = \CommonDefine::IS_DEL_0;
        $lc_id = implode(',', $line->data['lc']);
        $str = "";
        if ($lc_id) {
            $lc = $this->select("mchid = {$this->state->user_id} and is_del = {$is_del} and id in ({$lc_id})", null, null, null, 'LineCategory', 'title');

            foreach ($lc->data as $key => $v) {
                $str .= $v['title'] . '、';
            }
        }
        $line->data['lc'] = $str;
        $is_freeze = \CommonDefine::IS_FREEZE_0;
//        $from_type = \CommonDefine::FROM_TYPE_0;
        $driver_role_type = \CommonDefine::DRIVER_ROLE_0;
        $str1 = "";
        if ($dl) {
            $driver = $this->select("mchid = {$this->state->user_id} and driver_id in ({$dl}) and is_freeze = {$is_freeze}  and driver_role_type = {$driver_role_type}", null, null, null, 'Driver', 'name');
            foreach ($driver->data as $key1 => $v1) {
                $str1 .= $v1['name'] . '、';
            }
        }
        $line->data['dl'] = $str1;
        $line->data['start_province_name'] = $this->getAddressName($line->data['start_province_code']);
        $line->data['start_city_name'] = $this->getAddressName($line->data['start_city_code']);
        $line->data['start_area_name'] = $this->getAddressName($line->data['start_area_code']);
        $line->data['end_province_name'] = $this->getAddressName($line->data['end_province_code']);
        $line->data['end_city_name'] = $this->getAddressName($line->data['end_city_code']);
        $line->data['end_area_name'] = $this->getAddressName($line->data['end_area_code']);
        $mcname = $this->find("parent_admin_id={$this->state->user_id}  and is_del = 0 and branch_type = 0 and admin_id = " . $line->data['branchid'], 'Admin', 'mchname');
        if ($mcname) {
            $line->data['ftname'] = $mcname->data['mchname'];
        } else {
            $line->data['ftname'] = "";
        }
        return $this->output($line);
    }

    function getAddressName($code)
    {
        $add = $this->find("address_id = " . $code, 'SupportCity', 'name');
        if ($add) {
            return $add->data['name'];
        } else {
            return "";
        }
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLine($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $price, $channel_price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }
        unset($this->data['lc']);
        try {
            if(empty($phone_line_verify_start_time)){
                unset($this->data['phone_line_verify_start_time']);
            }
            if(empty($phone_line_verify_end_time)){
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }


            $flag = true;
            $this->startTrans();
            $r = $this->add('Line');
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            //拼车类型进行关联
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $r->data;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加线路");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLine($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $price, $channel_price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        unset($this->data['lc'], $this->data['driver']);
        try {
            if(empty($phone_line_verify_start_time)){
                unset($this->data['phone_line_verify_start_time']);
            }
            if(empty($phone_line_verify_end_time)){
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $this->startTrans();
            $r = $this->save('Line');
            $flag = true;
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            M("line_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id))->delete();
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $id;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)) {
                        $flag = false;
                    }
                }
            }
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }

        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }


        $this->doAddLog("修改线路");
        return $this->output($r);
    }

    /**
     * 删除拼车
     * @param int $id 线路ID
     */
    public function doDeleteLine($id)
    {
        //删除相关订单
        $r = $this->save('Line', array('id' => $id, 'is_del' => 1));
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 添加快车
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLineFast($mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code = null, $end_area_code = null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_7, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0,$base_price,$base_kilometre,$plus_kilometre_price = 0)
    {
        try {
            if (empty($base_price)) {
                return $this->output(new ResultModel(false, "起步价不能为空"));
            }
            if (empty($base_kilometre)) {
                return $this->output(new ResultModel(false, "基础里程不能为0"));
            }
            if(empty($phone_line_verify_start_time)){
                unset($this->data['phone_line_verify_start_time']);
            }
            if(empty($phone_line_verify_end_time)){
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $lineFastR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $start_city_code, 'is_del' => 0), 'LineFast');
            if($lineFastR->ret){
                return $this->output(new ResultModel(false, "一个市区只允许有一个价目"));
            }
            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if($startNameR->ret){
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $flag = true;
            $this->startTrans();
            $r = $this->add('LineFast');
            $driver = json_decode($driver, true);

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加快车成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加快车成功");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineFast($id, $mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code =null, $end_area_code =null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_7, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0,$base_price,$base_kilometre,$plus_kilometre_price = 0)
    {
        if (empty($base_price)) {
            return $this->output(new ResultModel(false, "起步价不能为空"));
        }
        if (empty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础里程不能为0"));
        }
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $startCityR = $this->find(array('address_id' => $start_city_code),'GdRegion');
        $this->data['start_name'] = $startCityR->ret?$startCityR->data['name']:"";
        $this->data['end_name'] = $startCityR->ret?$startCityR->data['name']:"";
        unset($this->data['driver']);
        try {
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $lineFastR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $start_city_code, 'is_del' => 0, 'id' => array('neq', $id)), 'LineFast');
            if($lineFastR->ret){
                return $this->output(new ResultModel(false, "一个市区只允许有一个价目"));
            }

            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if($startNameR->ret){
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $this->startTrans();
            $r = $this->save('LineFast');
            $flag = true;
            $driver = json_decode($driver, true);
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "修改快车成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "修改失败"));
            }

        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        $this->doAddLog("修改快车成功");
        return $this->output($r);
    }

    /**
     * 删除快车
     * @param int $id 线路ID
     */
    public function doDeleteLineFast($id)
    {
        $r = $this->save('LineFast', array('id' => $id, 'is_del' => 1, 'mchid' => $this->mchid));
        $this->doAddLog("删除快车");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLinePhone($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $phone_line_remarks = null, $price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$phone_line_verify_start_time = null,$phone_line_verify_end_time = null,$is_phone_line_verify)
    {
        $this->data['channel_price'] = $price;
        $this->data['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_1;
        unset($this->data['lc']);
        try {
            if(empty($phone_line_verify_start_time)){
                unset($this->data['phone_line_verify_start_time']);
            }
            if(empty($phone_line_verify_end_time)){
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $flag = true;
            $this->startTrans();
            $r = $this->add('Line');
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            //拼车类型进行关联
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $r->data;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加电话线路");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLinePhone($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $phone_line_remarks = null, $price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end,$phone_line_verify_start_time = null,$phone_line_verify_end_time = null,$is_phone_line_verify)
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $this->data['channel_price'] = $price;
        unset($this->data['lc'], $this->data['driver']);
        try {
            if(empty($phone_line_verify_start_time)){
                unset($this->data['phone_line_verify_start_time']);
            }
            if(empty($phone_line_verify_end_time)){
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if($center_start_latlngR->ret){
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if($center_end_latlngR->ret){
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $this->startTrans();
            $r = $this->save('Line');
            $flag = true;
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            M("line_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id))->delete();
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $id;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)) {
                        $flag = false;
                    }
                }
            }
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }

        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }


        $this->doAddLog("修改电话线路");
        return $this->output($r);
    }

    /**
     * 删除电话叫车线路
     * @param int $id 线路ID
     */
    public function doDeleteLinePhone($id)
    {
        //删除相关订单
        $r = $this->save('Line', array('id' => $id, 'is_del' => 1));
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 删除包车
     * @param int $id 包车id
     */
    public function doDeleteLineChartered($id)
    {
//        var_dump($id);die;
        //删除相关订单
        $r = $this->save('LineChartered', array('id' => $id, 'is_del' => 1));

        if ($r->ret) {
            M('line_chartered_price')->where(array('line_chartered_price_id' => $r->data['id'], 'is_del' => 0))->save(array('is_del' => 1));
        }
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 添加包车线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param json $prices 线路价格
     * @param json $channel_prices 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doCreateLineChartered($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $prices, $lc, $rate = 0, $status = 1, $type = \CommonDefine::ORDER_TYPE_2, $driver = null,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0)
    {
        $price = json_decode($prices, true);

        if (count($price) < 2) {
            return $this->output(new ResultModel(false, "请填写两个价格"));
        }
        $gdObj=new GdRegionModel();
        $adcode=$start_area_code;
        if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
            $adcode=$start_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
        if(isset($infos['districts'][0]['center'])){
            $data['center_start_latlng']=$infos['districts'][0]['center'];
        }
        $adcode=$end_area_code;
        if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
            $adcode=$end_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
        if(isset($infos['districts'][0]['center'])){
            $data['center_end_latlng']=$infos['districts'][0]['center'];
        }
        $data['mchid'] = $mchid;
        $data['branchid'] = $branchid;
        $data['start_name'] = $start_name;
        $data['end_name'] = $end_name;

        $data['start_province_code'] = $start_province_code;
        $data['start_city_code'] = $start_city_code;
        $data['start_area_code'] = $start_area_code;
        $data['end_area_code'] = $end_area_code;
        $data['end_city_code'] = $end_city_code;
        $data['end_province_code'] = $end_province_code;

        $data['start_address_code'] = $start_address_code;
        $data['end_address_code'] = $end_address_code;
        $data['summary'] = $summary;
        $data['rate'] = $rate;
        $data['is_del'] = 0;
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $data['business_time_type']=$business_time_type;
        $data['set_order_time']=$set_order_time;
        $lc = json_decode($lc, true);
        $driver = json_decode($driver, true);
        try {
            $flag = false;
            $this->startTrans();
            $r = $this->add('LineChartered', $data);
            if ($r->ret) {
                foreach ($price as $key => $val) {
                    $param[$key] = array(
                        'line_chartered_id' => $r->data,
                        'is_del' => 0,
                        'car_type_id' => $val['id'],
                        'price' => $val['price'],
                        'channel_price' => $val['channel_price'],
                        'create_time' => date("Y-m-d H:i:s"),
                        'update_time' => date("Y-m-d H:i:s"),
                    );
                    if (!$this->floatgtre($val['price'], $val['channel_price'])) {
                        return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                    }
                    $lineCharteredPriceR = $this->add('LineCharteredPrice', $param[$key]);
                    unset($param[$key]);
                    if (!$lineCharteredPriceR->ret) {
                        $flag = true; //添加失败
                    }
                }
                if (!empty($lc)) {
                    foreach ($lc as $val) {
                        ;
                        if (!$this->add("LineCharteredFkLineCategory", array('line_id' => $r->data, 'line_category_id' => $val, 'mchid' => $this->state->user_id, 'type' => \CommonDefine::ORDER_TYPE_2))) {
                            $flag = true;
                        }
                    }
                }
                //司机关联
                if (!empty($driver)) {
                    foreach ($driver as $item) {
                        $params['type'] = $type;
                        $params['line_id'] = $r->data;
                        $params['driver_id'] = $item;
                        $params['mchid'] = $this->state->user_id;
                        if (!$this->add("DriverLine", $params)->ret) {
                            $flag = true;
                        }
                    }
                }
                if ($flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                } else {
                    $this->commitTrans();
                }

            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }

        $this->doAddLog("添加线路");
        return $this->output($r);
    }

    /**
     * 修改包车线路
     * @param int $line_id 包车id
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param json $prices 线路价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineChartered($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $prices, $lc, $rate = 0, $status = 1, $driver = null, $type = \CommonDefine::ORDER_TYPE_2,$start_appointment_time,$end_appointment_time,$business_time_type,$set_order_time=0)
    {

        $price = json_decode($prices, true);

        if (count($price) < 2) {
            return $this->output(new ResultModel(false, "请填写两个价格"));
        }

        if (!$id) {
            return $this->output(new ResultModel(false, "数据异常!"));
        }
        $gdObj=new GdRegionModel();
        $adcode=$start_area_code;
        if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
            $adcode=$start_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
        if(isset($infos['districts'][0]['center'])){
            $data['center_start_latlng']=$infos['districts'][0]['center'];
        }
        $adcode=$end_area_code;
        if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
            $adcode=$end_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
        if(isset($infos['districts'][0]['center'])){
            $data['center_end_latlng']=$infos['districts'][0]['center'];
        }
        $data['id'] = $id;
        $data['mchid'] = $mchid;
        $data['branchid'] = $branchid;
        $data['start_name'] = $start_name;
        $data['end_name'] = $end_name;

//        var start_province_code = pcode;
//        var start_city_code = ccode;
//        var start_area_code = qcode;
//        var end_area_code = epcode;
//        var end_city_code = eccode;
//        var end_province_code = eqcode;
        $data['start_province_code'] = $start_province_code;
        $data['start_city_code'] = $start_city_code;
        $data['start_area_code'] = $start_area_code;
        $data['end_area_code'] = $end_area_code;
        $data['end_city_code'] = $end_city_code;
        $data['end_province_code'] = $end_province_code;

        $data['start_address_code'] = $start_address_code;
        $data['end_address_code'] = $end_address_code;
        $data['summary'] = $summary;
        $data['rate'] = $rate;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $data['business_time_type']=$business_time_type;
        $data['set_order_time']=$set_order_time;
        $lc = json_decode($lc, true);

      
        $driver = json_decode($driver, true);

        try {
            $flag = false;
            $this->startTrans();
            $r = $this->save('LineChartered', $data);
            unset($data);
            if ($r->ret) {
                foreach ($price as $key => $val) {
                    if (!$this->floatgtre($val['price'], $val['channel_price'])) {
                        return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                    }

                    $param = array(
                        'line_chartered_id' => $id,
                        'car_type_id' => $val['id'],
                    );
                    $LineCharteredPriceR = $this->find($param, 'LineCharteredPrice');
                    if ($LineCharteredPriceR->ret) {
                        $temp['price'] = $val['price'];
                        $temp['channel_price'] = $val['channel_price'];
                        $temp['update_time'] = date("Y-m-d H:i:s");
                        $lcp = M('line_chartered_price')->where($param)->save(array_merge($param, $temp));
                    } else {
                        $param['price'] = $val['price'];
                        $param['channel_price'] = $val['channel_price'];
                        $param['create_time'] = date("Y-m-d H:i:s");
                        $param['is_del'] = 0;
                        $param['update_time'] = date("Y-m-d H:i:s");
                        $lcp = M('line_chartered_price')->add($param);
                    }
                    unset($param);
                    if (!$lcp) {
                        $flag = true; //添加失败
                    }
                }
//                var_dump(123123);die;
                M("line_chartered_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id, 'type' => \CommonDefine::ORDER_TYPE_2))->delete();

                $whg['line_id'] = $id;
                $whg['mchid']   = $mchid;
                M('LineCharteredFkLineCategory')
                    ->where('line_category_id','not in',$lc)
                    ->where($whg)
                    ->delete();

                if (!empty($lc)) {

                    foreach ($lc as $val) {
                        $whg['line_category_id'] = $val;
                        if (!M('LineCharteredFkLineCategory')->where($whg)->find()) {
                            if (!$this->add("LineCharteredFkLineCategory", array('line_id' => $id, 'line_category_id' => $val, 'mchid' => $this->state->user_id))->ret) {
                                $flag = true;
                            }
                        }
                        
                    }
                }
                //司机关联
                M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
                if (!empty($driver)) {
                    foreach ($driver as $item) {
                        $params['type'] = $type;
                        $params['line_id'] = $id;
                        $params['driver_id'] = $item;
                        $params['mchid'] = $this->state->user_id;
                        if (!$this->add("DriverLine", $params)->ret) {
                            $flag = true;
                        }
                    }
                }
                if ($flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                } else {
                    $this->commitTrans();
                }

            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }

        $this->doAddLog("添加线路");
        return $this->output($r);
    }


    /**
     * 获取包车线路列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function getLineChareteredList($fields = null, $branchid = null, $mchid, $page = 1, $size = 10,$search_branchid=0,$search_line_id=0)
    {
        $where['mchid'] = $mchid;
        if($search_branchid){
            $where['branchid'] =$search_branchid;
        }else if(!empty($branchid) && $branchid){
            $where['branchid']= $branchid;
        }
        if($search_line_id){
            $where['id'] = $search_line_id;
        }
        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineCharteredFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }

                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];
                $lineCharteredPrice = M()->table('cp_line_chartered_price lcp')
                    ->JOIN("LEFT JOIN cp_car_type ct ON lcp.car_type_id =ct.car_type_id")->field('ct.num,lcp.price,lcp.channel_price, ct.name')->where(array('is_del' => 0, 'line_chartered_id' => $line['id']))->select();
                $tep = null;
                foreach ($lineCharteredPrice as $val) {
                    if (!$tep) {
                        $tep = $val['name'] . "[" . $val['num'] . "座](" . $val['price'] . ")";
                        $ctep = $val['name'] . "[" . $val['num'] . "座](" . $val['channel_price'] . ")";
                    } else {
                        $tep .= "<br>" . $val['name'] . "[" . $val['num'] . "座](" . $val['price'] . ")";
                        $ctep .= "<br>" . $val['name'] . "[" . $val['num'] . "座](" . $val['channel_price'] . ")";
                    }
                }
                $r->data[$k]['price'] = $tep;
                $r->data[$k]['channel_price'] = $ctep;
                unset($tep);
            }
        }
        foreach ($r->data as $key => $item) {
            if (empty($item['branchname'])) {
                $r->data[$key]['branchname'] = "";
            }
        }

        return $this->output($r);
    }


    /**
     * 获取学生号班车线路列表
     * @param null $fields 字段
     * @param int $mchid 商户id
     * @param int $page
     * @param int $size
     *
     */
    public function getYyLineClassList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $line_class_train_no = '', $status = '', $start_name = '', $end_name = '',$start_time_type=0,$search_branchid=0)
    {
        $where['mchid'] = $mchid;
        $where['is_del'] = 0;
        if (!empty($line_class_train_no)) {
            $where['id'] = $line_class_train_no;
        }
        if ($status !== '' && $status != 3) {
            $where['status'] = $status;
        }
        if (!empty($start_name)) {
            $where['start_name'] = ['like', '%' . $start_name . '%'];
        }
        if (!empty($end_name)) {
            $where['end_name'] = ['like', '%' . $end_name . '%'];
        }
        if ($start_time_type) {
            $where['start_time_type'] = $start_time_type;
        }
        if ($search_branchid) {
            $where['branchid'] = $search_branchid;
        }
        if(!empty($branchid) && $branchid){
            $where['branchid'] = $branchid;
        }

        $r = $this->select($where, $page, $size, 'id desc', 'YyLineClass');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $curtime = date("Y-m-d H:i:s", time());
                $onWayTrainCountR = $this->count(array('mchid' => $mchid, 'line_class_id' => $line['id'], 'line', 'is_del' => \CommonDefine::IS_DEL_0, 'start_time' => array('gt', $curtime)), 'YyLineClassTrain');
                $overTrainCountR = $this->count(array('mchid' => $mchid, 'line_class_id' => $line['id'], 'line', 'is_del' => \CommonDefine::IS_DEL_0, 'start_time' => array('elt', $curtime)), 'YyLineClassTrain');

                $startAddress = $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'];
                $r->data[$k]['start'] = $startAddress;
                $r->data[$k]['start_name'] = $line['start_name'];

                $endAddress = $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'];
                $r->data[$k]['end'] = $endAddress;
                $r->data[$k]['end_name'] = $line['end_name'];

                if ($line['stop_sell_number']>0) 
                {
                    $r->data[$k]['stop_sell_number'] = $line['stop_sell_number'].'天';
                }
                else
                {
                    $r->data[$k]['stop_sell_number'] = '';
                }
                

                $r->data[$k]['on_way_train_count'] = $onWayTrainCountR->ret ? $onWayTrainCountR->data : 0;
                $r->data[$k]['over_train_count'] = $overTrainCountR->ret ? $overTrainCountR->data : 0;
                //获取上车点
                if($line['start_address_type']==2){
                    $r->data[$k]['start_point']="<p>&nbsp;&nbsp;&nbsp;&nbsp;任意地点</p>";
                }else{
                    $pointMode=new YyLineClassPointModel();
                    $lineClassPointInfo=$pointMode->where(['line_class_id'=>$line['id'],'is_return'=>0,'type'=>1])->field('alias')->limit(3)->select();
                    $r->data[$k]['start_point']="";
                    foreach($lineClassPointInfo as $key=>$value){
                        $r->data[$k]['start_point'].="<p>&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                    }
                    if(count($lineClassPointInfo)>2){
                        $r->data[$k]['start_point'].="<a rel=\"{$line['id']}\" address='{$r->data[$k]['start']}'  onclick='sss($(this))' list_type=\"1\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                    }
                }
                //获取下车点
                if($line['end_address_type']==2){
                    $r->data[$k]['end_point']="<p>&nbsp;&nbsp;&nbsp;&nbsp;任意地点</p>";
                }else{
                    $pointMode=new YyLineClassPointModel();
                    $lineClassPointInfo=$pointMode->where(['line_class_id'=>$line['id'],'is_return'=>0,'type'=>2])->field('alias')->limit(3)->select();
                    $r->data[$k]['end_point']="";
                    foreach($lineClassPointInfo as $key=>$value){
                        $r->data[$k]['end_point'].="<p>&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                    }
                    if(count($lineClassPointInfo)>2){
                        $r->data[$k]['end_point'].="<a rel=\"{$line['id']}\" address='{$r->data[$k]['end']}' onclick='sss($(this))' list_type=\"2\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                    }
                }
            }
        }
        return $this->output($r);
    }
    //获取上下车点
    public function getLineClassPoint($id='',$type=''){
        $pointMode=new YyLineClassPointModel();
        $lineClassPointInfo=$pointMode->where(['line_class_id'=>$id,'is_return'=>0,'type'=>$type])->field('alias')->select();
        $info=(object)[];
        $info->ret=true;
        $info->data='';
        foreach($lineClassPointInfo as $key=>$value){
            $info->data.="<p>{$value['alias']}</p>";
        }
        return $this->output($info);
    }
    //复制单条线路
    public function doCopyYyLineClass($line_class_id=0){
        $LineInfo=M('YyLineClass')->where(['id'=>$line_class_id,'is_del'=>0])->find();
        if(!$LineInfo){
            return $this->output(new ResultModel(false, "未找到线路信息！"));
        }

        unset($LineInfo['id']);
        unset($LineInfo['create_time']);
        unset($LineInfo['update_time']);
        //复制后默认手动
        unset($LineInfo['scheduling_type']);
        unset($LineInfo['exclude_scheduling']);
        unset($LineInfo['pre_sale_time']);
        unset($LineInfo['driver_id']);

        $this->startTrans();
        $r= M('YyLineClass')->add($LineInfo);
        if($r){
            $where['line_class_id'] = $line_class_id;
            $lineClassPointsR = $this->select($where, null, null, null, 'YyLineClassPoint');
            if($lineClassPointsR->ret){
                foreach($lineClassPointsR->data as $k => $v){
                    unset($v['id']);
                    unset($v['create_time']);
                    unset($v['update_time']);
                    $v['line_class_id'] = $r;
                    $lineClassPointS = $this->add('YyLineClassPoint', $v);
                    if(!$lineClassPointS->ret){
                        $this->transRollback();
                        return $this->output(new ResultModel(true, "复制失败！"));
                    }
                }
            }

            $this->commitTrans();
           return $this->output(new ResultModel(true, "复制成功！"));
        }else{
            $this->transRollback();
           return $this->output(new ResultModel(true, "复制失败！"));
        }
    }
    //运行或停运
    public function doBatchOperationYyLineClass($status = '', $is_batch = 0, $id = 0, $is_train = 0)
    {
        if ($status === '' or $id == 0) {
            return $this->output(new ResultModel(false, "参数错误1！"));
        }
        if ($is_batch == 1) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return $this->output(new ResultModel(false, "参数错误2！"));
            }
            $where['id'] = ['in', $ids];
        } else {
            if (!is_numeric($id) or $id < 1) {
                return $this->output(new ResultModel(false, "参数错误3！"));
            }
            $where['id'] = $id;
        }
        if ($status == 1) {
            $data['status'] = 1;
        } else {
            $data['status'] = 0;
        }
        if ($is_train == 1) {
            $model = M('YyLineClassTrain');
            $where['line_class_train_id'] = $where['id'];
            unset($where['id']);
        } else {
            $model = M('YyLineClass');
        }
        $res = $model->where($where)->save($data);
        if ($res) {
            return $this->output(new ResultModel(true, "操作成功！"));
        } else {
            return $this->output(new ResultModel(false, '操作失败'));
        }
    }
    //批量删除班线或班次
    public function doBatchDelYyLineClass($id = 0,$is_train=0)
    {
        if ($id == 0) {
            return $this->output(new ResultModel(false, "参数错误1！"));
        }
        $ids = explode('|', $id);
            if (count($ids) < 1) {
                return $this->output(new ResultModel(false, "参数错误2！"));
            }
        $where['id'] = ['in', $ids];
        if ($is_train == 1) {
            $model = M('YyLineClassTrain');
            $where['line_class_train_id'] = $where['id'];
            unset($where['id']);
        } else {
            $model = M('YyLineClass');
        }
        M()->startTrans();
        $res = $model->where($where)->save(['is_del'=>1]);
        if ($res) {
            //如果是班线删除班次
            if($is_train!=1){
                $lineClassTrainR = $this->find(['line_class_id'=>['in',$ids]], 'YyLineClassTrain');
                if($lineClassTrainR->ret){
                    $re= M('YyLineClassTrain')->where(['line_class_id'=>['in',$ids]])->save(['is_del'=>1]);
                    if(!$re){
                        M()->rollback();
                        return $this->output(new ResultModel(false, '操作失败!'));
                    }
                }
            }
            M()->commit();
            return $this->output(new ResultModel(true, "操作成功！"));
        } else {
            return $this->output(new ResultModel(false, '操作失败'));
        }
    }
    /**
     * 添加学生号线路班线
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param float $rate 线路提成比例
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateYyLineClass($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $type = \CommonDefine::ORDER_TYPE_5, $rate = 0, $status = 1, $aboard_longitude, $aboard_latitude, $aboard_address)
    {
        $this->data['aboard_address_code'] = $this->getGdAddressCodeByGdApi($aboard_longitude, $aboard_latitude);
        try {
            $gdObj=new GdRegionModel();
            $adcode=$start_area_code;
            if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
                $adcode=$start_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
            if(isset($infos['districts'][0]['center'])){
                $this->data['center_start_latlng']=$infos['districts'][0]['center'];
            }
            $adcode=$end_area_code;
            if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
                $adcode=$end_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
            if(isset($infos['districts'][0]['center'])){
                $this->data['center_end_latlng']=$infos['districts'][0]['center'];
            }
            $this->startTrans();
            $r = $this->add('LineClass');
            if ($r->ret) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加线路");
        return $this->output($r);
    }

    /**
     * 修改线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineClass($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $type = \CommonDefine::ORDER_TYPE_5, $rate = 0, $status = 1, $aboard_longitude, $aboard_latitude, $aboard_address)
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $this->data['aboard_address_code'] = $this->getGdAddressCodeByGdApi($aboard_longitude, $aboard_latitude);
        try {
            $gdObj=new GdRegionModel();
            $adcode=$start_area_code;
            if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
                $adcode=$start_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
            if(isset($infos['districts'][0]['center'])){
                $this->data['center_start_latlng']=$infos['districts'][0]['center'];
            }
            $adcode=$end_area_code;
            if($gdObj->where(['address_id'=>$adcode])->find()['name']=='市辖区'){
                $adcode=$end_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' =>$adcode, 'subdistrict' => 3, 'key' =>'88402b07e88938a3937f616d63aa7fb9'])), true);
            if(isset($infos['districts'][0]['center'])){
                $this->data['center_end_latlng']=$infos['districts'][0]['center'];
            }
            $this->startTrans();
            $r = $this->save('LineClass');
            if ($r->ret) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "修改成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "修改失败"));
            }

        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }


        $this->doAddLog("修改线路");
        return $this->output($r);
    }

    /**
     * 删除班线
     * @param int $line_class_id 线路ID
     */
    public function doDeleteYyLineClass($line_class_id)
    {
        M()->startTrans();
        $r = $this->save('YyLineClass', array('id' => $line_class_id, 'is_del' => 1));
        if($r->ret==true){
            $lineClassTrainR = $this->find(['line_class_id'=>$line_class_id], 'YyLineClassTrain');
            if($lineClassTrainR->ret) {
                $re = M('YyLineClassTrain')->where(['line_class_id' => $line_class_id])->save(['is_del' => 1]);
                if (!$re) {
                    M()->rollback();
                    return $this->output(new ResultModel(false, '操作失败'));
                }
            }
            M()->commit();
        }else{
            M()->rollback();
            $r->data = '删除失败';
        }
        $this->doAddLog("删除班线");
        return $this->output($r);
    }

    /**
     * 获取班次时刻列表
     * @param null $fields 字段
     * @param null $line_class_id 班车线路
     * @param int $mchid 商户id
     * @param int $page
     * @param int $size
     *
     */
    public function getYyLineClassTrainList($start_time=0,$end_time=0,$mchid, $line_class_id = 0, $page = 1, $size = 10, $line_class_train_no = '', $status = '', $start_name = '', $end_name = '',$line_class_number='',$search_branchid='')
    {
        if (!empty($line_class_train_no)) {
            $where['cp_yy_line_class_train.line_class_train_no'] = $line_class_train_no;
        }
        if ($line_class_id) {
            $where['b.id'] = $line_class_id;
        }
        if ($line_class_number) {
            $where['b.id'] = $line_class_number;
        }
        if ($status !== '' && $status != 3) {
            $where['cp_yy_line_class_train.status'] = $status;
        }
        if (!empty($start_name)) {
            $where['b.start_name'] = ['like', '%' . $start_name . '%'];
        }
        if (!empty($end_name)) {
            $where['b.end_name'] = ['like', '%' . $end_name . '%'];
        }
        if (!empty($start_time)) {
            $where['cp_yy_line_class_train.start_date'] = ['egt', $start_time];
        }
        if (!empty($end_time)) {
            $where['cp_yy_line_class_train.start_date'] = ['elt',$end_time];
        }
        if(!empty($search_branchid)){
            $where['cp_yy_line_class_train.branchid'] = $search_branchid;
        }
        $where['cp_yy_line_class_train.mchid'] = $mchid;
        $where['cp_yy_line_class_train.is_del'] = 0;
        $where['cp_yy_line_class_train.start_date'] = ['egt', date('Y-m-d')];
        $info = M('YyLineClassTrain')
            ->field('cp_yy_line_class_train.*,b.start_name,b.end_name,b.start_time,b.start_address_code,b.end_address_code')
            ->where($where)->join("cp_yy_line_class b on b.id=cp_yy_line_class_train.line_class_id")->page($page, $size)->order('cp_yy_line_class_train.start_date asc,b.sort asc')->select();
        $count = M('YyLineClassTrain')->where($where)->join("cp_yy_line_class b on b.id=cp_yy_line_class_train.line_class_id")->page($page, $size)->order('cp_yy_line_class_train.start_date asc')->count();
        if ($info) {
            foreach ($info as $k => $lineClassTrain) {
                $startAddress = $this->checkingGdParentAddressCode($lineClassTrain['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($lineClassTrain['start_address_code'])->data['address'];
                $info[$k]['start'] = $startAddress . $lineClassTrain['start_name'];
                $endAddress = $this->checkingGdParentAddressCode($lineClassTrain['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($lineClassTrain['end_address_code'])->data['address'];
                $info[$k]['end'] = $endAddress . $lineClassTrain['end_name'];
                if (strtotime($lineClassTrain['start_time']) > time()) {
                    $info[$k]['line_class_status'] = 0;
                } else {
                    $info[$k]['line_class_status'] = 1;
                }
                $branchR = $this->find(array("admin_id" => $lineClassTrain['branchid']), 'admin');
                if ($branchR->ret) {
                    $info[$k]['branchname'] = $branchR->data['mchname'];
                }
                $driver_id=M('YyLineClassTrainDriver')->where(['line_class_train_id'=>$lineClassTrain['line_class_train_id']])->field('driver_id,sort')->select();
                $info[$k]['driver_id']=json_encode($driver_id,true);
            }
            $a = (object)[];
            $a->count = $count;
            $a->data = $info;
            $a->ret = true;
            return $this->output($a);
        } else {
            return $this->output(new ResultModel(false, '暂无数据！'));
        }
    }
    //修改班次
    public function doSaveYyLineClass($line_class_train_id=0,$remain_tickets='',$price='',$channel_price='',$driver_id=[],$sort=[]){
        if(empty($driver_id)){
            return $this->output(new ResultModel(false, '请选择司机！'));
        }
        if($remain_tickets<0 or !is_numeric($remain_tickets)){
            return $this->output(new ResultModel(false, '请输入正确的余票数量！'));
        }
        if(empty($price) or $price<0 or !is_numeric($price)){
            return $this->output(new ResultModel(false, '请输入正确的销售价格！'));
        }
        unset($this->data['line_class_train_id']);
        M()->startTrans();
        //先移除之前的司机
        M('YyLineClassTrainDriver')->where(['line_class_train_id'=>$line_class_train_id,'mchid'=>$this->state->user_id])->delete();
        foreach($driver_id as $key=>$value){
           $res= M('YyLineClassTrainDriver')->add(['line_class_train_id'=>$line_class_train_id,'mchid'=>$this->state->user_id,'driver_id'=>$value,'sort'=>$sort[$key]]);
           if(!$res){
               M()->rollback();
               return $this->output(new ResultModel(false, '修改失败！'));
           }
        }
        unset($this->data['driver_id']);
        $this->data['update_time']=date('Y-m-d H:i:s');
        $where['line_class_train_id'] = $line_class_train_id;
        if($this->admin_group_id == 3){
            $where['branchid'] = $this->state->user_id;
        }else if($this->admin_group_id == 2){
            $where['mchid'] = $this->state->user_id;
        }
        $r=M('YyLineClassTrain')->where($where)->save($this->data);
        if($r){
            M()->commit();
            return $this->output(new ResultModel(true, '修改成功！'));
        }else{
            M()->rollback();
            return $this->output(new ResultModel(false, '修改失败！'));
        }
    }
    //添加班次
    public function doAddYyLineClassTrain($line_class_ids, $pre_sale_time = null, $exclude_scheduling = [], $driver_id=null, $scheduling_type,$level = null)
    {
        $lineClassIdsArr = explode(',', $line_class_ids);
        if (count($lineClassIdsArr) < 1) {
            return $this->output(new ResultModel(false, "参数错误2！"));
        }
        $j=0;
        foreach($lineClassIdsArr as $line_class_id){
            $lineClassInfo=M('YyLineClass')->where(['id'=>$line_class_id,'is_del'=>0])->find();
            if(!$lineClassInfo){
                M()->rollback();
                return $this->output(new ResultModel(false, '没有找到该班线！'));
            }

            if ($scheduling_type == 2) {
                if ($pre_sale_time <= 0 or !is_numeric($pre_sale_time)) {
                    M()->rollback();
                    return $this->output(new ResultModel(false, '预售天数不正确！'));
                }
                if($pre_sale_time>180){
                    M()->rollback();
                    return $this->output(new ResultModel(false, '预售天数不能超过180天！'));
                }
            }else{
                $pre_sale_time_arr=array_filter(explode(',',$pre_sale_time));
                if(count($pre_sale_time_arr)<1){
                    M()->rollback();
                    return $this->output(new ResultModel(false, '排班日期不正确！'));
                }
                foreach($pre_sale_time_arr as $key=>$value){
                    $pre_sale_time_arr[$key]=date('Y-m-d',strtotime($value));
                    if($pre_sale_time_arr[$key]<date('Y-m-d')){
                        M()->rollback();
                        return $this->output(new ResultModel(false,  "班次编号：".$lineClassInfo['id'] ." 不能添加当天发车之前的班次！[{$pre_sale_time_arr[$key]}]"));
                    }
                    if($pre_sale_time_arr[$key]==date('Y-m-d')){
                        if(date('H:i',time()-7200)>$lineClassInfo['start_time']){
                            M()->rollback();
                            return $this->output(new ResultModel(false, "班次编号：".$lineClassInfo['id'] ." 不能添加当天发车之前的班次！[{$pre_sale_time_arr[$key]}]"));
                        }
                    }
                }
            }
            if(!$driver_id){
                M()->rollback();
                return $this->output(new ResultModel(false, '请选择司机！'));
            }
            //自动
            $data['line_class_id']=$line_class_id;
            //计算座位数
            $data['remain_tickets'] = 0;
            $data['total_tickets'] = 0;
            foreach($driver_id as $driverKey=>$driverVal){
                $driverR = $this->find(array('driver_id' => $driverVal), 'Driver');
                if ($driverR->ret) {
                    $data['remain_tickets'] += $driverR->data['total_seating'] - 1;
                    $data['total_tickets'] += $driverR->data['total_seating'] - 1;
                } else {
                    M()->rollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }
            $data['price']=$lineClassInfo['price'];
            $data['channel_price']=$lineClassInfo['channel_price'];
            $data['branchid']=$lineClassInfo['branchid'];
            $data['mchid']=$lineClassInfo['mchid'];

            try {
                if ($scheduling_type == 2) {
                    for ($i = 1; $i <= $pre_sale_time; $i++) {
                        $date = date('Y-m-d', strtotime("+{$i} days"));
                        $r = M('YyLineClassTrain')->where(['start_date' => $date, 'is_del' => 0, 'line_class_id' => $line_class_id])->find();
                        if (!$r && !in_array(date('w', strtotime($date)), $exclude_scheduling)) {
                            $j++;
                            $data['line_class_train_no'] = date('Ymd', strtotime($date)) . $line_class_id . rand(10000, 99999);
                            $data['start_date'] = $date;
                            $res = M('YyLineClassTrain')->add($data);
                            if (!$res) {
                                M()->rollback();
                                return $this->output(new ResultModel(false, "添加失败"));
                            }
                            //添加班次司机
                            foreach ($driver_id as $driverKey => $driverVal) {
                                $driverRe = M('YyLineClassTrainDriver')->add(['line_class_train_id' => $res, 'driver_id' => $driverVal,'sort'=>$level[$driverKey],'mchid'=>$data['mchid']]);
                                if(!$driverRe){
                                    M()->rollback();
                                    return $this->output(new ResultModel(false, "添加失败"));
                                }
                            }
                        }
                    }
                } else {
                    //手动
                    foreach ($pre_sale_time_arr as $k => $value) {
                        //新增
                        $r = M('YyLineClassTrain')->where(['start_date' => $value, 'is_del' => 0, 'line_class_id' => $line_class_id])->find();
                        if (!$r) {
                            $j++;
                            $data['line_class_train_no'] = date('Ymd', strtotime($value)) . $line_class_id . rand(10000, 99999);
                            $data['start_date'] = $value;
                            $res = M('YyLineClassTrain')->add($data);
                            if (!$res) {
                                M()->rollback();
                                return $this->output(new ResultModel(false, "添加失败"));
                            }
                            //添加班次司机
                            foreach ($driver_id as $driverKey => $driverVal) {
                                $driverRe = M('YyLineClassTrainDriver')->add(['line_class_train_id' => $res, 'driver_id' => $driverVal,'sort'=>$level[$driverKey],'mchid'=>$data['mchid']]);
                                if(!$driverRe){
                                    M()->rollback();
                                    return $this->output(new ResultModel(false, "添加失败"));
                                }
                            }
                        }
                    }
                }
            }catch(Exception $e){
                M()->rollback();
                return $this->output(new ResultModel(false, "系统出错！"));
            }
            //修改班线
            if($scheduling_type==2){
                $data1['driver_id']=$driver_id;
                $data1['pre_sale_time']=$pre_sale_time;
                $data1['exclude_scheduling']=implode(',',$exclude_scheduling);
            }
            $info=M('YyLineClassTrain')->where(['line_class_id'=>$line_class_id])->order('start_date desc')->find();
            if($info){
                $data1['last_scheduling_time']=$info['start_date'];
            }else{
                $data1['last_scheduling_time']='';
            }
            //先移除当前自动排班司机
            M('YyLineClassDriver')->where(['line_class_id'=>$line_class_id])->delete();
            //修改班线自动排班司机
            foreach ($driver_id as $driverKey => $driverVal) {
                $driverRe = M('YyLineClassDriver')->add(['line_class_id' => $line_class_id, 'driver_id' => $driverVal,'sort'=>$level[$driverKey],'mchid'=>$data['mchid']]);
                if(!$driverRe){
                    M()->rollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }
            $data1['scheduling_type']=$scheduling_type;
            $data1['update_time']=date("Y-m-d H:i:s");
            $res=M('YyLineClass')->where(['id'=>$line_class_id,'is_del'=>0])->save($data1);
            if(!$res){
                M()->rollback();
                return $this->output(new ResultModel(true, "添加失败"));
            }
        }
        M()->commit();
        return $this->output(new ResultModel(true, "成功添加[{$j}]个班次。"));
    }

    /**
     * 添加班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $start_time 发车时间
     * @param float $price 销售价格
     * @param string $market_price 市场价格
     * @param string $channel_price 渠道价格
     * @param string $travel_time 目的地名称
     * @param string $driver_d 司机id
     */
    public function doCreateLineClassTrain($mchid, $line_class_id, $start_time, $market_price, $price, $channel_price, $travel_time = null, $driver_id)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }

        $startTime = strtotime($start_time);
        if ($startTime < time()) {
            return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
        }
        $lineClassR = $this->find(array('id' => $line_class_id), 'LineClass');
        if (!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if ($driverR->ret) {
            $this->data['remain_tickets'] = $driverR->data['total_seating'] - 1;
            $this->data['total_tickets'] = $driverR->data['total_seating'] - 1;
        } else {
            return $this->output(new ResultModel(false, "添加失败"));
        }

        try {
            $this->startTrans();
            $this->data['type'] = 0;
            $this->data['line_class_train_no'] = $this->createLineClassTrainNo($startTime);
            $this->data['branchid'] = $lineClassR->data['branchid'];
            $r = $this->add('LineClassTrain');
            if ($r->ret) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加班次");
        return $this->output($r);
    }

    /**
     * 批量添加班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $class_train_date 班次日期
     * @param json $line_class_trains 班次信息
     */
    public function doCreateLineClassTrains($mchid, $line_class_id, $class_train_date, $line_class_trains)
    {
        $lineClassR = $this->find(array('id' => $line_class_id), 'LineClass');
        if (!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $line_class_train_data['line_class_id'] = $line_class_id;
        $line_class_train_data['mchid'] = $mchid;
        $line_class_train_data['branchid'] = $lineClassR->data['branchid'];

        $line_class_trains_arr = json_decode($line_class_trains, true);
        $this->startTrans();
        foreach ($line_class_trains_arr as $k => $line_class_train_arr) {
            if (is_array($line_class_train_arr)) {
                $line_class_train_data['start_time'] = $class_train_date . ' ' . $line_class_train_arr['start_time'];
                $startTime = strtotime($line_class_train_data['start_time']);
                if ($startTime < time()) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
                }
                $line_class_train_data['start_time'] = date('Y-m-d H:i:s', $startTime);
                $line_class_train_data['market_price'] = $line_class_train_arr['market_price'];
                $line_class_train_data['price'] = $line_class_train_arr['price'];
                $line_class_train_data['channel_price'] = $line_class_train_arr['channel_price'];
                if (!$this->floatgtre($line_class_train_data['price'], $line_class_train_data['channel_price'])) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                }

                $line_class_train_data['travel_time'] = $line_class_train_arr['travel_time'];
                $line_class_train_data['driver_id'] = $line_class_train_arr['driver_id'];
                $line_class_train_data['type'] = 0;

                $driverR = $this->find(array('driver_id' => $line_class_train_data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $line_class_train_data['remain_tickets'] = $driverR->data['total_seating'] - 1;
                    $line_class_train_data['total_tickets'] = $driverR->data['total_seating'] - 1;
                } else {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }

                $line_class_train_data['line_class_train_no'] = $this->createLineClassTrainNo($startTime);

                $lineClassTrainA = $this->add('LineClassTrain', $line_class_train_data);
                if (!$lineClassTrainA->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量添加班次");
        return $this->output(new ResultModel(true, "批量添加成功"));
    }

    /**
     * 修改班次
     * @param int $line_class_train_id 班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $start_time 发车时间
     * @param float $price 销售价格
     * @param string $market_price 市场价格
     * @param string $channel_price 渠道价格
     * @param string $travel_time 目的地名称
     * @param string $driver_d 司机id
     */
    public function doUpdateLineClassTrain($line_class_train_id, $mchid, $start_time, $market_price, $price, $channel_price, $travel_time = null, $driver_id)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }

        $startTime = strtotime($start_time);
        if ($startTime < time()) {
            return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
        }

        $lineClassTrainR = $this->find(array('id' => $line_class_train_id, 'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
        if (!$lineClassTrainR->ret) {
            return $this->output(new ResultModel(false, "线路不存在"));
        }

        if ($lineClassTrainR->data['driver_id'] != $driver_id) {
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            if ($driverR->ret) {
                $this->data['remain_tickets'] = $driverR->data['total_seating'] - 1;
                $this->data['total_tickets'] = $driverR->data['total_seating'] - 1;
            } else {
                return $this->output(new ResultModel(false, "修改失败"));
            }
        }

        try {
            $this->startTrans();
            $lineClassTrainS = $this->save('LineClassTrain');
            if ($lineClassTrainS->ret) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "修改成功"));
            } else {
                return $this->output(new ResultModel(false, "修改失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        $this->doAddLog("修改班次");
        return $this->output($r);
    }

    /**
     * 删除班次
     * @param int $line_class_train_id 班次ID
     */
    public function doDeleteYyLineClassTrain($line_class_train_id)
    {
        //删除相关订单
        $r = $this->save('YyLineClassTrain', array('line_class_train_id' => $line_class_train_id, 'is_del' => 1));
        $this->doAddLog("删除班线");
        return $this->output($r);
    }


    /**
     * 获取代办列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doAgencyList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $mchid;

        if(!empty($branchid) && $branchid){
            $where['branchid'] = $branchid;
        }

        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Agency');

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key]['mchname'] = $this->find(array('admin_id' => $value['mchid']), "Admin")->data['mchname'];
                $r->data[$key]['branch_name'] = $this->find(array('admin_id' => $value['branchid']), "Admin")->data['mchname'];
            }
        }
        return $this->output($r);
    }

    /**
     * 添加代办
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     */
    public function doAddAgency($name, $branch, $summary, $price, $channel_price, $rate = 0, $status = 0,$start_appointment_time = null,$end_appointment_time = null,$business_time_type = null,$set_order_time=0)
    {

        $data['mchid'] = $this->state->user_id;
        if (!$data['mchid']) {
            return $this->output(new ResultModel(false, "身份异常..."));
        }
        $data['name'] = $name;
        $data['branchid'] = $branch;
        $data['summary'] = $summary;
        $data['price'] = $price;
        $data['channel_price'] = $channel_price;
        $data['rate'] = $rate;
        $data['status'] = $status;
        $data['is_del'] = 0;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time']=$set_order_time;
        $data['business_time_type']=$business_time_type;
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $r = $this->add('Agency', $data);

        $this->doAddLog("添加代办");
        return $this->output($r);
    }

    /**
     * 修改代办
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     */
    public function doEditAgency($id, $name, $branch, $summary, $price, $channel_price, $rate = 0, $status = 0,$start_appointment_time = null,$end_appointment_time = null,$business_time_type = null,$set_order_time=0)
    {

        $agencyR = $this->find(array('agency_id' => $id, 'is_del' => 0), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $data['agency_id'] = $id;
        $data['name'] = $name;
        $data['branchid'] = $branch;
        $data['summary'] = $summary;
        $data['price'] = $price;
        $data['channel_price'] = $channel_price;
        $data['rate'] = $rate;
        $data['status'] = $status;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time']=$set_order_time;
        $data['business_time_type']=$business_time_type;
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $r = $this->save('Agency', $data);

        $this->doAddLog("修改代办");
        return $this->output($r);
    }

    /**
     * 代办--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteAgency($agency_id)
    {
        $agencyR = $this->find(array('agency_id' => $agency_id, 'is_del' => 0), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $r = $this->save('Agency', array('agency_id' => $agency_id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除代办");
        return $this->output($r);
    }

    /**
     * 获取带货列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doTakeGoodsPriceList($fields = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'TakeGoodsPrice');

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key]['mchname'] = $this->find(array('admin_id' => $value['mchid']), "Admin")->data['mchname'];
            }
        }
        return $this->output($r);
    }


    /**
     * 添加带货
     * @param int $branchid 分台id
     * @param string name 计费标准名称
     * @param string $summary 线路简介说明
     * @param int $base_price 基础价格
     * @param int $base_weight 基础重量
     * @param int $base_kilometre 基础距离
     * @param int $plus_weight_price 每公斤增加费用
     * @param int $plus_kilometre_price 每公里增加费用
     */
    public function doAddTakeGoodsPrice($name, $base_price, $summary, $base_weight, $base_kilometre, $plus_weight_price, $plus_kilometre_price, $channel_rofit_price = null, $rate = 0,$start_appointment_time = null,$end_appointment_time = null,$business_time_type = null,$set_order_time=0)
    {

        $data['mchid'] = $this->state->user_id;
        if (!$data['mchid']) {
            return $this->output(new ResultModel(false, "身份异常..."));
        }
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }
        if (!$this->isEmpty($base_weight)) {
            return $this->output(new ResultModel(false, "基础重量不能为空..."));
        }
        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }
        $data['rate'] = $rate;
        $data['is_del'] = 0;
        $data['name'] = $name;
        $data['summary'] = $summary;
        $data['base_weight'] = $base_weight;
        $data['base_price'] = $base_price;
        $data['base_kilometre'] = $base_kilometre;
        $data['plus_weight_price'] = $plus_weight_price;
        $data['plus_kilometre_price'] = $plus_kilometre_price;
        $data['channel_rofit_price'] = $channel_rofit_price;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time']=$set_order_time;
        $data['business_time_type']=$business_time_type;
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $r = $this->add('TakeGoodsPrice', $data);
        $this->doAddLog("添加带货");
        return $this->output($r);
    }

    /**
     * 修改带货
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $rate 线路提成比例
     */
    public function doEditTakeGoodsPrice($id, $name, $base_price, $summary, $base_weight, $base_kilometre, $plus_weight_price, $plus_kilometre_price, $channel_rofit_price = null, $rate = 0,$start_appointment_time = null,$end_appointment_time = null,$business_time_type = null,$set_order_time=0)
    {

        $agencyR = $this->find(array('take_goods_price_id' => $id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该带货不存在..."));
        }
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }
        if (!$this->isEmpty($base_weight)) {
            return $this->output(new ResultModel(false, "基础重量不能为空..."));
        }
        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }
        $data['take_goods_price_id'] = $id;
        $data['rate'] = $rate;
        $data['name'] = $name;
        $data['summary'] = $summary;
        $data['base_weight'] = $base_weight;
        $data['base_price'] = $base_price;
        $data['base_kilometre'] = $base_kilometre;
        $data['plus_weight_price'] = $plus_weight_price;
        $data['plus_kilometre_price'] = $plus_kilometre_price;
        $data['channel_rofit_price'] = $channel_rofit_price;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time']=$set_order_time;
        $data['business_time_type']=$business_time_type;
        $data['start_appointment_time']=$start_appointment_time;
        $data['end_appointment_time']=$end_appointment_time;
        $r = $this->save('TakeGoodsPrice', $data);

        $this->doAddLog("修改代办");
        return $this->output($r);
    }

    /**
     * 带货--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteTakeGoodsPrice($tgp_id)
    {
        $agencyR = $this->find(array('take_goods_price_id' => $tgp_id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $r = $this->save('TakeGoodsPrice', array('take_goods_price_id' => $tgp_id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除带货");
        return $this->output($r);
    }

    /**
     * 带货--设为默认
     * @param int $agency_id 代办ID
     */
    public function doDefaultTakeGoodsPrice($tgp_id)
    {
        $adminR = $this->find(array('admin_id' => $this->state->user_id, 'group_id' => 2), 'Admin');
        if (!$adminR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在..."));
        }
        $agencyR = $this->select(array('mchid' => $adminR->data['admin_id'], 'is_del' => 0), null, null, null, 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "请添加带货..."));
        }

        $tgpR = $this->find(array('take_goods_price_id' => $tgp_id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$tgpR->ret) {
            return $this->output(new ResultModel(false, "该带货不存在..."));
        }

        M('take_goods_price')->where(array('mchid' => $adminR->data['admin_id']))->save(array('is_default' => 0)); //将该商户所有带货数据设置为0
        $r = $this->save('TakeGoodsPrice', array('take_goods_price_id' => $tgp_id, 'is_default' => 1, 'update_tine' => date("Y-m-d H:i:s"))); //设置为默认带货

        $this->doAddLog("带货默认设置");
        return $this->output($r);
    }

    /**
     * 获取类型列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doLineCateogyrList($fields = null, $page = 1, $size = 10,$line_category_id=0)
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        if($line_category_id){
            $where['category_id']=$line_category_id;
        }
        $r = $this->select($where, $page, $size, 'id asc', 'LineCategory');
        foreach($r->data as $key=>$value){
            $cateName=M('LineCategoryBusiness')->where(['line_category_business_id'=>$value['line_category_business_id']])->find();
            if($cateName){
                $r->data[$key]['category_name']=$cateName['name'];
            }else{
                $r->data[$key]['category_name']='<span style="color: red">未设置场景类型</span>';
            }

        }
        return $this->output($r);
    }

    /**
     * 添加类型
     * @param string $title 名称
     */
    public function doAddLineCategory($title,$category_id)
    {
        $data['mchid'] = $this->state->user_id;
        if (mb_strlen($title, "utf-8") > 5) {
            return $this->output(new ResultModel(false, "标题长度最多是5汉字..."));
        }
        $agencyR = $this->find(array('mchid' => $data['mchid'], 'is_del' => 0), 'Admin');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在..."));
        }
        $data['title'] = $title;
        $data['is_del'] = 0;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['line_category_business_id']=$category_id;
        $r = $this->add('LineCategory', $data);

        $this->doAddLog("添加类型");
        return $this->output($r);
    }

    /**
     * 修改类型
     * @param int $branchid 分台id
     * @param string $title 名称
     */
    public function doEditLineCategory($id, $title,$category_id)
    {
        $agencyR = $this->find(array('id' => $id, 'is_del' => 0), 'LineCategory');
        if (mb_strlen($title, "utf-8") > 5) {
            return $this->output(new ResultModel(false, "标题长度最多是5汉字..."));
        }
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该类型不存在..."));
        }
        $data['mchid'] = $this->state->user_id;
        $data['id'] = $id;
        $data['title'] = $title;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['line_category_business_id']=$category_id;
        $r = $this->save('LineCategory', $data);

        $this->doAddLog("修改类型");
        return $this->output($r);
    }

    /**
     * 类型--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteLineCateogry($id)
    {
        $agencyR = $this->find(array('id' => $id, 'is_del' => 0), 'LineCategory');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该类型不存在..."));
        }
        $r = $this->save('LineCategory', array('id' => $id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除类型");
        return $this->output($r);
    }

    /**
     * 修改顺风车计价规则
     * @param string $name 顺风车计价标题
     * @param string $summary 线路说明
     * @param float $base_price 起步价格
     * @param float $base_kilometre 基础公里数
     * @param float $plus_kilometre_price 超出公里数后的公里单价
     */
    public function doEditMileagePrice($name, $summary, $base_price, $base_kilometre, $plus_kilometre_price)
    {
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }

        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }

        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $milePriceR = $this->find($where, 'MileagePrice');
        $r = new \ResultModel(false);
        if (!$milePriceR->ret) {
            //添加
            $data['name'] = $name;
            $data['summary'] = $summary;
            $data['base_price'] = $base_price;
            $data['base_kilometre'] = $base_kilometre;
            $data['plus_kilometre_price'] = $plus_kilometre_price;
            $data['is_default'] = 1;
            $data['mchid'] = $this->state->user_id;
            $r = $this->add('MileagePrice', $data);
        } else {
            //修改
            $data['mileage_price_id'] = $milePriceR->data['mileage_price_id'];
            $data['name'] = $name;
            $data['summary'] = $summary;
            $data['base_price'] = $base_price;
            $data['base_kilometre'] = $base_kilometre;
            $data['plus_kilometre_price'] = $plus_kilometre_price;
            $r = $this->save('MileagePrice', $data);
        }

        $this->doAddLog("修改顺风车计价规则");
        return $this->output($r);
    }

    //添加班线车
    function doSaveYyLineClassIn()
    {
        $post = $_POST;
        $class = $this->combinationPost($post);
        $mchid = '';
        if($this->admin_group_id == 3){
            $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            $mchid = $mchR->data['parent_admin_id'];
            $class['mchid'] = $mchR->data['parent_admin_id'];
        }else{
            $class['mchid'] = $this->state->user_id;
            $mchid = $this->state->user_id;
        }
        M()->startTrans();

        $rr = $this->add('YyLineClass', $class);

        if (!$rr->ret)
        {
            M()->rollback();
            $this->json(400,'添加失败');
        }

        $line_class_id = $rr->data;
        $point['line_class_id'] = $line_class_id;
        $point['is_return'] = 1;

        //设置了返程
        if (!empty($class['is_return']))
        {

            if ($class['return_start_address_type']==1)
            {
                $return_start_lgtltt = explode(',', $post['return_start_lgtltt']);
                //添加至站点
                $point['longitude'] = $return_start_lgtltt[0];
                $point['latitude'] = $return_start_lgtltt[1];
                $point['alias'] = $post['return_start_alias1'];
                $point['type'] = 1;
                $r = M('LineClassPoint')->add($point);

                if (!$r){
                    M()->rollback();
                    $this->json(400,'添加失败');
                }
            }
            if ($class['return_end_address_type']==1)
            {
                //添加至站点
                $return_end_lgtltt = explode(',', $post['return_end_lgtltt']);
                //添加至站点
                $point['longitude'] = $return_end_lgtltt[0];
                $point['latitude'] = $return_end_lgtltt[1];
                $point['alias'] = $post['return_end_alias1'];
                $point['type'] = 2;
                $r = M('LineClassPoint')->add($point);
                if (!$r){
                    M()->rollback();
                   $this->json(400,'添加失败');
                }
            }
        }

        $point['is_return'] = 0;

        if ($class['start_address_type']==1)
        {
            //循环添加数据
            foreach ($post['up_alias1'] as $key => $value)
            {
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['up_use_time1'][$key];
                $r = M('YyLineClassPoint')->add($point);
                if (!$r){
                    M()->rollback();
                   $this->json(400,'添加失败');
                }

            }
        }

        if ($class['end_address_type']==1)
        {
            foreach ($post['down_alias1'] as $key => $value)
            {
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['down_use_time1'][$key];
                $r = M('YyLineClassPoint')->add($point);
                if (!$r){
                    M()->rollback();
                   $this->json(400,'添加失败');
                }
            }
        }
        //场景分类
        
        
        if (!empty($post['line_cat_ids']))
        {
            $LiCa['mchid'] = $mchid;
            $LiCa['line_class_id'] = $line_class_id;
            foreach ($post['line_cat_ids'] as $key => $value) 
            {
                $LiCa['line_category_id'] = $value;
                //为空添加 LineClassFkLineCategory
                // !$this->add("line_class_fk_line_category", $LiCa)->ret
                $r = M('yy_line_class_fk_line_category')->add($LiCa);
                 if (!$r)
                {
                    M()->rollback();
                   $this->json(400,'添加失败');
                }
            }
        }

        M()->commit();
        $this->json(200,'添加成功');
    }


    //修改班线车
    function doEditYyLineClassIn()
    {
        $post = $_POST;
        $class = $this->combinationPost($post);
        $datatime = date('Y-m-d H:i:s');

        $mchid = '';
        if($this->admin_group_id == 3){
            $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            $mchid = $mchR->data['parent_admin_id'];
        }else{
            $mchid = $this->state->user_id;
        }

        M()->startTrans();
        $class['id'] = $post['id'];
        $class['update_time'] = $datatime;
        $rr = $this->save('YyLineClass', $class);

        if (!$rr->ret)
        {
            M()->rollback();
            $this->json(400,'修改失败');
        }

           
        $point['line_class_id'] = $post['id'];
        $point['is_return'] = 1;

        //设置了返程
        if (!empty($class['is_return']))
        {
            //M('LineClassPoint')->where(['line_class_id'=>$post['id'],''])->delete();
            if ($class['return_start_address_type']==1)
            {
                
                $return_start_lgtltt = explode(',', $post['return_start_lgtltt']);
                //修改至站点
                $point['longitude'] = $return_start_lgtltt[0];
                $point['latitude'] = $return_start_lgtltt[1];
                $point['alias'] = $post['return_start_alias1'];
                $point['type'] = 1;
                if (!empty($post['return_up_id'])) {
                    $point['id'] = $post['return_up_id'];
                    $point['update_time'] = $datatime;
                    $r = M('YyLineClassPoint')->save($point);
                   
                }else{
                    $point['create_time'] = $datatime;
                    $r = M('YyLineClassPoint')->add($point);
                }
               
                

                if (!$r){
                    M()->rollback();
                    $this->json(400,'修改失败');
                }
            }
            if ($class['return_end_address_type']==1)
            {
                
                //修改至站点
                $return_end_lgtltt = explode(',', $post['return_end_lgtltt']);
                //修改至站点
                $point['longitude'] = $return_end_lgtltt[0];
                $point['latitude'] = $return_end_lgtltt[1];
                $point['alias'] = $post['return_end_alias1'];
                $point['type'] = 2;
                if (!empty($post['return_down_id'])) {
                    $point['id'] = $post['return_down_id'];
                    $point['update_time'] = $datatime;
                    $r = M('YyLineClassPoint')->save($point);
                }else{
                    $point['create_time'] = $datatime;
                    $r = M('YyLineClassPoint')->add($point);
                }
                
                if (!$r){
                    M()->rollback();
                   $this->json(400,'修改失败');
                }
            }
        }
        unset($point['id']);
        unset($point['update_time']);
        unset($point['create_time']);
        $point['is_return'] = 0;

        if (!empty($post['delete_ids'])) {
            $where['id'] = array('in',rtrim($post['delete_ids'],','));
            M('YyLineClassPoint')->where($where)->delete();
        }
        if ($class['start_address_type']==1)
        {
            //循环修改数据
            foreach ($post['up_alias1'] as $key => $value)
            {
                
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['up_use_time1'][$key];
                if (!empty($post['up_id'][$key])) {
                    $point['id'] = $post['up_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('YyLineClassPoint')->save($point);
                }else{
                    $point['create_time'] = $datatime;
                    $r = M('YyLineClassPoint')->add($point);
                }
                
                if (!$r){
                    M()->rollback();
                   $this->json(400,'修改失败');
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        }

        if ($class['end_address_type']==1)
        {
            foreach ($post['down_alias1'] as $key => $value)
            {
                
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['down_use_time1'][$key];
                if (!empty($post['down_id'][$key])) {
                    $point['id'] = $post['down_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('YyLineClassPoint')->save($point);
                }else{
                    $point['create_time'] = $datatime;
                    $r = M('YyLineClassPoint')->add($point);
                }
                
                if (!$r){
                    M()->rollback();
                   $this->json(400,'修改失败');
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        }

        //场景分类
        $LiCa['mchid'] = $mchid;
        $LiCa['line_class_id'] = $post['id'];

        //删除取消的场景分类
        if (empty($post['line_cat_ids'])) {
            M('yy_line_class_fk_line_category')->where($LiCa)->delete();
        }else{
            $LiCa['line_category_id'] = array('not in',implode(",", $post['line_cat_ids']));
            M('yy_line_class_fk_line_category')->where($LiCa)->delete();
        }
       
        if (!empty($post['line_cat_ids'])) 
        {

            foreach ($post['line_cat_ids'] as $key => $value) 
            {
                $LiCa['line_category_id'] = $value;
                $inn = M('yy_line_class_fk_line_category')->where($LiCa)->find();

                //为空添加
                if (empty($inn)) 
                {
                    $r = M('yy_line_class_fk_line_category')->add($LiCa);
                     if (!$r)
                    {
                        M()->rollback();
                       $this->json(400,'修改失败');
                    }
                }
            }
        }
        M()->commit();
        $this->json(200,'修改成功');
    }

    protected function combinationPost($post){
        $class = [
            "start_time_type"       => $post['start_time_type'],
            "branchid"              => $post['branch_id'],
            "price"                 => $post['price'],
            "channel_price"         => $post['channel_price'],
            "stop_sell_number"      => $post['stop_sell_number'],
            "stop_sell_time"        => $post['stop_sell_time'],
            "refund_time_set"       => $post['refund_time_set'],
            "return_start_time_type"=> $post['return_start_time_type'],
        ];
        if ($post['start_time_type']==1) 
        {
            $class['start_time'] = $post['start_time'];
        }
        else
        {
            $class['start_earliest_time'] = $post['start_earliest_time'];
            $class['end_latest_time'] = $post['end_latest_time'];
        }
        if (!empty($post['summary'])) 
        {
            $class['summary'] = $post['summary'];
        }
        $arr = ['start','end'];

        if (!empty($post['end_polygon_use_time'])) 
        {
            $class['end_polygon_use_time'] = (int)$post['end_polygon_use_time'];
        }

        foreach ($arr as $key => $value)
        {
            $class[$value.'_address_type']           = $post[$value.'_address_type'];
            $class[$value.'_province_code']          = $post[$value.'_province_code'];
            $class[$value.'_city_code']              = $post[$value.'_city_code'];
            $class[$value.'_area_code']              = $post[$value.'_area_code'];
            $class[$value.'_name']                   = $post[$value.'_name'];
            if ($post[$value.'_address_type']==2) {
                $class[$value.'_polygon'] = json_encode(explode('-', $post[$value.'_polygon']));
            }

            if (empty($post[$value.'_area_code']) && empty($post[$value.'_city_code']))
            {
                $class[$value.'_address_code'] = $post[$value.'_province_code'];
            }
            else if(!empty($post[$value.'_city_code']) && empty($post[$value.'_area_code']))
            {
                $class[$value.'_address_code'] = $post[$value.'_city_code'];
            }
            else
            {
                $class[$value.'_address_code'] = $post[$value.'_area_code'];
            }
        }

        $date = date('Y-m-d');
        $time1 = strtotime($date.' '.'00:00:00');

        switch ($post['start_time_type'])
        {
            case 1:
                $time2 = strtotime($date.' '.$post['start_time']);
                break;
            case 2:
                $time2 = strtotime($date.' '.$post['start_earliest_time']);
                break;
            default:
                break;
        }

        $class['sort'] = (int)$time2-(int)$time1;

        //包含自定义设置-返程
        if (!empty($post['is_return']))
        {
            $class['return_start_address_type'] = $post['return_start_address_type'];
            $class['return_end_address_type']   = $post['return_end_address_type'];
            $class['return_time_number']        = $post['return_time_number'];
            
            if ($post['return_start_time_type']==1)
            {
                //固定发车时间
                $class['return_start_time'] = $post['return_start_time'];
                
            }
            else
            {
                //滚动发车时段
                $class['return_start_earliest_time'] = $post['return_start_earliest_time'];
                $class['return_end_latest_time'] = $post['return_end_latest_time'];
            }

            if ($class['return_start_address_type']==2)
            {
               //别名,范围
                $class['return_start_alias'] = $post['return_start_alias2'];
                $class['return_start_polygon'] = json_encode(explode('-', $post['return_start_polygon']));
            }

            if ($class['return_end_address_type']==2)
            {
                //别名,范围
                $class['return_end_alias'] = $post['return_end_alias2'];
                $class['return_end_polygon'] = json_encode(explode('-', $post['return_end_polygon']));
            }

            $class['is_return'] = 1;
        }
        else
        {
            $class['is_return'] = 0;
        }

        $center_start_latlngR = $this->getAddressCenterLatLng($class['start_province_code'], $class['start_city_code'], $class['start_area_code']);
        if($center_start_latlngR->ret){
            $class['center_start_latlng'] = $center_start_latlngR->data['location'];
            $class['return_center_end_latlng'] = $center_start_latlngR->data['location'];
        }

        $center_end_latlngR = $this->getAddressCenterLatLng($class['end_province_code'], $class['end_city_code'], $class['end_area_code']);
        if($center_end_latlngR->ret){
            $class['center_end_latlng'] = $center_end_latlngR->data['location'];
            $class['return_center_start_latlng'] = $center_end_latlngR->data['location'];
        }

        return $class;
    }
    function json($code,$msg){
        echo json_encode([
            'code'=>$code,
            'msg'=>$msg,
        ]);die;
     }

    /**
     * 获取中心坐标
     * @param $province_code
     * @param $city_code
     * @param $area_code
     */
    protected function getAddressCenterLatLng($province_code, $city_code, $area_code){
        $ret = new ResultModel(false, '获取失败');
        if(empty($province_code) && empty($city_code) && empty($area_code)){
            return $ret;
        }
        $adcode = $province_code;
        $adcodeR = $this->find(['address_id'=>$area_code], 'GdRegion');
        if($adcodeR->ret){
            if($adcodeR->data['name'] == '市辖区'){
                $adcodeR = $this->find(['address_id'=>$city_code], 'GdRegion');
                if($adcodeR->ret){
                    if($adcodeR->data['name'] == '市辖区'){
                        $adcode = $province_code;
                    }else{
                        $adcode = $city_code;
                    }
                }
            }else{
                $adcode = $area_code;
            }
        }else{
            $adcodeR = $this->find(['address_id'=>$city_code], 'GdRegion');
            if($adcodeR->ret){
                if($adcodeR->data['name'] == '市辖区'){
                    $adcode = $province_code;
                }else{
                    $adcode = $city_code;
                }
            }
        }
        $ret = $this->getGdLocationByAdcode($adcode);
        return $ret;
    }
}


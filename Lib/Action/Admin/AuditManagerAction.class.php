<?php

import('@/Action/Admin/AdminAction');
import('@/Action/Weixin/Wechat');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 日志管理模块
 *
 * <AUTHOR>
 */
class AuditManagerAction extends AdminCoreAction
{
    public function getLists($status = null, $page = 1, $size = 10)
    {
        if (!empty($status)) {
            $where['status'] = $status;
        }
        $uid = $this->state->user_id;
        $where['mchid'] = $uid;
        $where['virtual'] = 0;

        $r = $this->select($where, $page, $size, 'driver_id desc', 'Driver', 'driver_id,name,total_seating,cellphone,car_register_time,status,driving_license,driver_license');

        if ($r) {
            $this->doAddLog("查看审核");
        }
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '审核中';
            } else if ($r->data[$k]['status'] == 2) {
                $r->data[$k]['status'] = '审核未通过';
            } else if ($r->data[$k]['status'] == 3) {
                $r->data[$k]['status'] = '已欠费';
            } else {
                $r->data[$k]['status'] = '正常';
            }
        }
        return $this->output($r);
    }

    /**
     * 删除单条审核记录
     * @param int $passenger_id 乘客ID
     */
    public function doDeleteAudit($driver_id, $page = 1, $size = 10)
    {
        $this->startTrans();
        $d = $this->delete($driver_id, 'Driver');
        if ($d->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $r = $this->select(null, $page, $size, 'driver_id desc', 'Driver', 'driver_id,nick_name,total_seating,cellphone,driving_years,car_register_time,status');
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '审核中';
            } else if ($r->data[$k]['status'] == 2) {
                $r->data[$k]['status'] = '审核未通过';
            } else if ($r->data[$k]['status'] == 3) {
                $r->data[$k]['status'] = '已欠费';
            } else {
                $r->data[$k]['status'] = '正常';
            }
        }
        $this->doAddLog("删除单条审核记录");
        return $this->output($d);
    }

    /**
     * 审核通过
     * @param int $passenger_id 乘客ID
     */
    public function doPassAudit($driver_id, $page = 1, $size = 10)
    {


        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,status,openid,name,cellphone');
        if ($driverR->data['status'] != 1 && $driverR->data['status'] != 2) {
            return $this->output(new \ResultModel(false, "请不要重复审核"));
        }
        $uid = $this->state->user_id;
        if (!$uid) {
            $uid = cookie('mchid');
        }
        $pake = M('admin')->where('admin_id=' . $uid)->find();
        $Drcount = M('driver')->where('mchid=' . $uid . ' and status in (0,3)')->count();
        $relCount = $pake['driver_num'] - $Drcount;
        if ($relCount <= 2 && $relCount > 0) {
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $b = $smsUtil->sendTemplateSMS($pake['tel'], array('司机人数'), \SMSUtil::TEMP_ID_PAKENOTICE,$uid);
        }

        if ($Drcount >= $pake['driver_num']) {
            $data = array('status' => 1);
            M('admin')->where('admin_id=' . $uid)->data($data)->save();
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $b = $smsUtil->sendTemplateSMS($pake['tel'], array(), \SMSUtil::TEMP_ID_PAKELIMIT,$uid);
            return $this->output(new \ResultModel(false, "您的套餐司机人数已达到上限，请及时联系客服经理,或者拨打电话028-61112106"));
        }
        $this->startTrans();
        $d = $this->save('Driver', array('driver_id' => $driver_id, 'status' => 3));
        if ($d->ret) {
            $this->commitTrans();
            $wechat = new Wechat();
            $data = $wechat->doPostToDriverPasspordMessage($driverR->data['openid'], 0, '通过审核', '车辆信息符合规定', $driverR->data['cellphone'], $this->state->user_id);
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $b = $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array('您提交的证件已通过审核，进入应用开始您的新旅途吧'), SMSUtil::TEMP_ID_PSPSSUCC,$uid);
            if (!$b) {
                return $this->output(new \ResultModel(false, '向用户' . $driverR->data['name'] . '发送验证件审核成功消息失败'));
            }
            {
                return $this->output(new \ResultModel(true, '向用户' . $driverR->data['name'] . '发送验证件审核成功消息成功'));
            }
        } else {
            $this->transRollback();
        }
        $r = $this->select(null, $page, $size, 'driver_id desc', 'Driver', 'driver_id,nick_name,total_seating,cellphone,driving_years,car_register_time,status');
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '审核中';
            } else if ($r->data[$k]['status'] == 2) {
                $r->data[$k]['status'] = '审核未通过';
            } else if ($r->data[$k]['status'] == 3) {
                $r->data[$k]['status'] = '已欠费';
            } else {
                $r->data[$k]['status'] = '正常';
            }
        }
        $this->doAddLog("审核司机通过");
        return $this->output($r);
    }

    /**
     * 批量删除通知
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doDeleteAudits($driver_ids)
    {
        $driver_id_arr = explode(",", $driver_ids);
        if (empty($driver_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }

        foreach ($driver_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('AuditManager', $this->state->user_id);
                $o->data = array('driver_id' => $value);
                $r = $o->doDeleteAudit($value);

                if (!$r->ret) {
                    return $this->output($r);
                }
            }
        }
        $this->doAddLog("批量删除通知");
        return $this->output(new ResultModel(true));
    }

    /**
     * 审核不通过
     * @param int $passenger_id 乘客ID
     */
    public function doNoPassAudit($driver_id, $page = 1, $size = 10)
    {

        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,status,openid,name,cellphone,mchid');
        if ($driverR->data['status'] != 1 && $driverR->data['status'] != 2) {
            return $this->output(new \ResultModel(false, "请不要重复审核"));
        }
        $this->startTrans();
        $d = $this->save('Driver', array('driver_id' => $driver_id, 'status' => 2));

        if ($d->ret) {
            $this->commitTrans();
            $wechat = new Wechat();

            $data = $wechat->doPostToDriverPasspordMessage($driverR->data['openid'], 1, '审核失败', '车辆信息不符合标准', $driverR->data['cellphone'], $this->state->user_id);
            $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
            $b = $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array('您提交的证件未通过审核，请在个人资料中重新提交证件。'), \SMSUtil::TEMP_ID_PSPSFAIL,$driverR->data['mchid']);
            if (!$b) {
                return $this->output(new \ResultModel(false, '向用户' . $driverR->data['name'] . '发送验证件审核成功消息失败'));
            }
            {
                return $this->output(new \ResultModel(true, '向用户' . $driverR->data['name'] . '发送验证件审核成功消息成功'));
            }
        } else {
            $this->transRollback();
        }
        $r = $this->select(null, $page, $size, 'driver_id desc', 'Driver', 'driver_id,nick_name,total_seating,cellphone,driving_years,car_register_time,status',$driverR->data['mchid']);
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '审核中';
            } else if ($r->data[$k]['status'] == 2) {
                $r->data[$k]['status'] = '审核未通过';
            } else if ($r->data[$k]['status'] == 3) {
                $r->data[$k]['status'] = '已欠费';
            } else {
                $r->data[$k]['status'] = '正常';
            }
        }
        $this->doAddLog("审核司机不通过");
        return $this->output($r);
    }

    /**
     * 批量审核通过
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doPassAudits($driver_ids)
    {
        $driver_id_arr = explode(",", $driver_ids);
        if (empty($driver_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($driver_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('AuditManager', $this->state->user_id);
                $o->data = array('driver_id' => $value);
                $r = $o->doPassAudit($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量审核司机通过");
        return $this->output(new ResultModel(true));
    }

    /**
     * 批量审核不通过
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doNoPassAudits($driver_ids)
    {

        $driver_id_arr = explode(",", $driver_ids);
        if (empty($driver_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($driver_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('AuditManager', $this->state->user_id);
                $o->data = array('driver_id' => $value);
                $r = $o->doNoPassAudit($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量审核司机不通过");
        return $this->output(new ResultModel(true));
    }

    /**
     * 清楚欠费
     **/
    public function dopassAuditmfee($driver_id, $page = 1, $size = 10)
    {
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver', 'driver_id,status,openid,name,cellphone,mchid');
        switch ($driverR->data['status']) {
            case "0":
                return $this->output(new \ResultModel(false, "该司机车辆信息未欠费"));
                break;
            case "1":
                return $this->output(new \ResultModel(false, "该司机车辆信息审核中"));
                break;
            case "2":
                return $this->output(new \ResultModel(false, "该司机用户信息审核未通过"));
                break;
        }
        if ($driverR->data['status'] == 3) {
            $this->data['status'] = 0;
            $this->startTrans();
            $d = $this->save('Driver', array('driver_id' => $driver_id, 'status' => 0));
            if ($d->ret) {
                $this->commitTrans();
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $b = $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array('年费'), \SMSUtil::TEMP_ID_COMMONNITICE,$driverR->data['mchid']);
                if (!$d->ret) {
                    return $this->output(new \ResultModel(false, '用户' . $driverR->data['name'] . '欠费清除失败'));
                }
                {
                    return $this->output(new \ResultModel(true, '用户' . $driverR->data['name'] . '欠费清除成功'));
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, "请稍后再试"));
            }
            $this->doAddLog("司机已清除欠费");
        } else {
            return $this->output(new \ResultModel(false, "审核错误"));
        }
    }


}

?>

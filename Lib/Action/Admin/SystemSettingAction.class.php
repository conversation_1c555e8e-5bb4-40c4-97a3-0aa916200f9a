<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Admin/AdminCoreAction');
/**
 * 系统设置模块
 *
 * <AUTHOR>
 */
class SystemSettingAction extends AdminCoreAction
{

    /**
     * 更新系统配置
     * @param string $key 键
     * @param string $value 值
     */
    public function doUpdateConfig($key, $value)
    {
        $r = $this->find(array('key' => $key), 'SystemConfig', 'config_id');
        if ($r->ret) {
            $r = $this->save('SystemConfig', array('config_id' => $r->data['config_id'], 'value' => $value));
        } else {
            return $this->output(new ResultModel(false, '无效的key'));
        }
        $this->doAddLog("更新系统配置");
        return $this->output($r);
    }

    /**
     * 更新保险金配置
     * @param string $value 乘客保险金
     */
    public function doUpdateInsuranceMoney($value)
    {
        $o = $this->sudo('SystemSetting', $this->state->user_id);
        $this->doAddLog("更新保险金配置");
        return $o->doUpdateConfig('insurance money', $value);
    }

    /**
     * 更新月租配置
     * @param float $monthly_rent 月租金额
     * @param int $monthly_decute 每月扣费时间
     */
    public function doUpdateRent($monthly_rent, $monthly_decute)
    {
        $o = $this->sudo('SystemSetting', $this->state->user_id);
        $this->startTrans();
        $r = $o->doUpdateConfig('monthly rent', $monthly_rent);
        if ($r->ret) {
            $r = $o->doUpdateConfig('monthly decute', $monthly_decute);
            if ($r->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
            }
        } else {
            $this->transRollback();
        }
        $this->doAddLog("更新月租配置");
        return $this->output($r);
    }

    /**
     * 更新年费
     * @param float $money 年费金额
     */
    public function doUpdateRentNew($money)
    {
//        $r = $this->save('AnnualFee', array("id"=>1, 'money' => $money));
        $data = M('annual_fee')->where('mchid=' . $this->state->user_id)->find();
        if ($data) {
            $r = $this->save('AnnualFee', array("mchid" => $this->state->user_id, 'money' => $money, 'id' => $data['id']));
        } else {
            $r = $this->add('AnnualFee', array("mchid" => $this->state->user_id, 'money' => $money));
        }
        $this->doAddLog("更新年费配置");
        return $this->output($r);
    }

    /**
     * 订单分成
     * @param float $money 年费金额
     */
    public function doUpdateSplitNew($money)
    {
//        $r = $this->save('AnnualFee', array("id"=>1, 'money' => $money));
        $mcher = $this->find(array('mchid' => $this->state->user_id), 'AnnualFee');
        if ($mcher->ret) {
            $r = $this->save('AnnualFee', array("mchid" => $this->state->user_id, 'split' => $money, 'id' => $mcher->data['id']));
        } else {
            $r = $this->add('AnnualFee', array("mchid" => $this->state->user_id, 'split' => $money));
        }
        $this->doAddLog("更新年费配置");
        return $this->output($r);
    }

    public function adnnul($data)
    {
        $this->startTrans();
        if ($data) {
            $r = $this->add('AnnualFee', array("mchid" => $data['mchid'], 'money' => $data['money'],'split' => $data['split']));
        }
        if ($r) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
    }

    /**
     * 抢单设置
     * @param int $rob_order 抢单设置：0-关闭；1-开启；
     */
    public function doUpdateRobOrderConfig($rob_order)
    {
        $r = new \ResultModel(false);
        $data['value'] = $rob_order;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>'rob_order'),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = 'rob_order';
            $data['description'] = '抢单设置';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("更新抢单设置");
        return $this->output($r);
    }

    /**
     * 短信设置
     * @param int $sms_config 抢单设置：0-关闭；1-开启；
     */
    public function doUpdateSmsConfig($sms_config)
    {
        $r = new \ResultModel(false);
        $data['value'] = $sms_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>'sms_config'),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = 'sms_config';
            $data['description'] = '短信开关';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("更新短信设置");
        return $this->output($r);
    }


    /**
     * 司机注册设置
     * @param int $driver_register_config 司机注册设置：0-关闭；1-开启；
     */
    public function doUpdateDriverRegisterConfig($driver_register_config)
    {
        $r = new \ResultModel(false);
        $data['value'] = $driver_register_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::DRIVER_REGISTER_CONFIG),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::DRIVER_REGISTER_CONFIG;
            $data['description'] = '司机注册设置';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("更新司机注册设置");
        return $this->output($r);
    }

    /**
     * 订单模式设置
     * @param int $multi_order_mode 订单模式设置：0-单订单模式；1-多订单模式；
     */
    public function doUpdateMultiOrderMode($multi_order_mode)
    {
        $r = new \ResultModel(false);
        $data['value'] = $multi_order_mode;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::MULTI_ORDER_MODE),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::MULTI_ORDER_MODE;
            $data['description'] = '订单模式（乘客下单）';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("订单模式（乘客下单）");
        return $this->output($r);
    }

    /**
     * 订单支付模式设置
     * @param int $order_payment_mode 支付模式设置：0-先坐车后支付模式；1-先支付后坐车模式；
     */
    public function doUpdateOrderPaymentMode($order_payment_mode)
    {
        $r = new \ResultModel(false);
        $data['value'] = $order_payment_mode;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::ORDER_PAYMENT_MODE),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::ORDER_PAYMENT_MODE;
            $data['description'] = '支付模式（先坐车后支付或先支付后走车）';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("支付模式（先坐车后支付或先支付后走车）");
        return $this->output($r);
    }

    /**
     * 下单时身份证号和姓名的控制
     * @param int $driver_register_config 下单时上传身份证号姓名：0-关闭；1-开启；
     */
    public function doUpdateIdUploadConfig($id_upload_config)
    {
        $r = new \ResultModel(false);
        $data['value'] = $id_upload_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::ID_UPLOAD_CONFIG),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::ID_UPLOAD_CONFIG;
            $data['description'] = '下单时上传身份证号姓名';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("更新下单时上传身份证号姓名");
        return $this->output($r);
    }

    /**
     * 下单时身份证号和姓名的控制
     * @param int $driver_register_config 下单时上传身份证号姓名：0-关闭；1-开启；
     */
    public function doUpdateLineClassAppointConfig($line_class_appoint_config)
    {
        $r = new \ResultModel(false);
        $data['value'] = $line_class_appoint_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::LINE_CLASS_APPOINT_CONFIG),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::LINE_CLASS_APPOINT_CONFIG;
            $data['description'] = '班线车（或城际网约车）派单方式，默认0-自动派单';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        return $this->output($r);
    }

    /**
     * 自定义包车调度分台的控制
     * @param int $line_charter_custom_branch_config 分台id
     */
    public function doUpdateLinCharterCustomBranchConfig($line_charter_custom_branch_config)
    {
        $r = new \ResultModel(false);
        $branchR = $this->find(array('admin_id'=>$line_charter_custom_branch_config,'branch_type' => 0, 'is_del' => 0,'parent_admin_id' => $this->mchid), 'Admin');
        if(!$branchR->ret){
            return $r;
        }

        $data['value'] = $line_charter_custom_branch_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG;
            $data['description'] = '自定义包车调度分台';
            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("自定义包车调度分台");
        return $this->output($r);
    }

    /**
     * 司机端已线下现金付款按钮
     * @param int $order_driver_pay_op_config 司机端已线下现金付款按钮：0-开启；2-关闭；
     */
    public function doUpdateOrderDriverPayOpConfig($order_driver_pay_op_config)
    {
        $r = new \ResultModel(false);
        if($order_driver_pay_op_config != \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_0 && $order_driver_pay_op_config != \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_2){
            return $this->output($r);
        }

        $data['value'] = $order_driver_pay_op_config;
        $systemConfigR = $this->find(array('account_id'=>$this->state->user_id , 'account_type'=>\CommonDefine::SYSTEM_ROLE_1,'key'=>\CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG),'SystemConfig');
        if($systemConfigR->ret){
            $data['config_id'] = $systemConfigR->data['config_id'];
            $systemConfigS = $this->save('SystemConfig',$data);
            if($systemConfigS->ret){
                $r->ret = true;
            }
        }else{
            $data['account_id'] = $this->state->user_id;
            $data['account_type'] = CommonDefine::SYSTEM_ROLE_1;
            $data['key'] = \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG;
            if($order_driver_pay_op_config == \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_0){
                $data['description'] = '已线下现金付款按钮：开启';
            }else if($order_driver_pay_op_config == \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_2){
                $data['description'] = '已线下现金付款按钮：关闭';
            }

            $systemConfigA = $this->add('SystemConfig',$data);
            if($systemConfigA->ret){
                $r->ret = true;
            }
        }
        $this->doAddLog("已线下现金付款按钮");
        return $this->output($r);
    }

    /**
     * 更新协议
     */
    public function doUpdateAgreementConfig(){
        $post = $_POST;
        $this->startTrans();
        //隐私协议
        $updatePrivacyData['agreement_name_alias'] = $post['privacy_agreement_name_alias'];
        $updatePrivacyData['agreement_content'] = $post['privacy_agreement_content'];
        $updatePrivacyData['agreement_id'] = $post['privacy_agreement_id'];
        $privacyS = $this->save('Agreement', $updatePrivacyData);
        if(!$privacyS->ret){
            $this->transRollback();
            return $privacyS;
        }

        //乘车服务协议
        $updateRideData['agreement_name_alias'] = $post['ride_agreement_name_alias'];
        $updateRideData['agreement_content'] = $post['ride_agreement_content'];
        $updateRideData['agreement_id'] = $post['ride_agreement_id'];
        $rideS = $this->save('Agreement', $updateRideData);
        if(!$rideS->ret){
            $this->transRollback();
            return $rideS;
        }
        $this->commitTrans();
        $this->doAddLog("更新协议");
        return $this->output(new \ResultModel(true, '更新协议成功'));
    }
}

?>

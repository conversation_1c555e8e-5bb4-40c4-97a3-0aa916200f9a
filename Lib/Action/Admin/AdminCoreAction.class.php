<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Home/HomeCoreAction');

/**
 * 后台核心模块
 *
 * <AUTHOR>
 */
class AdminCoreAction extends HomeCoreAction
{
    /**
     * 管理组
     * @var string
     */
    protected $admin_group;

    /**
     * 管理组id
     * @var int
     */
    protected $admin_group_id;

    //put your code here
    public function __construct()
    {
        $adminGroupObj = null;
        add_tag_behavior('auth_check', 'AdminAuthCheck');
        parent::__construct();
        $this->assign('admin_state', object_to_array($this->state));
        if ($this->state) {
            $adminGroupObj = $this->_getCurrentGroupInfo($this->state->user_id);
        }
        if ($adminGroupObj && $adminGroupObj->ret) {
            $this->admin_group = $adminGroupObj->data['code'];
            $this->admin_group_id = $adminGroupObj->data['group_id'];
            $this->assign('admin_group', $this->admin_group);
            $url = C('WEB_ROOT');
            # 商户管理员 组ID :1-saas平台账号；2-商户超管账号；3-分台（渠道或线路）
            if ($adminGroupObj->data['group_id'] == 2) {
                if ($this->checkUserTimeLimit()) {
                    echo "<script type='text/javascript'>alert('账户已到期，请及时续费，联系电话：028-61112106');</script>";
                    StateModel::clear(StateModel::$ADMIN_USER);
                    echo "<script type='text/javascript'>location.href='{$url}/admin_login';</script>";
                    die;
                }
                $this->assign('mchid', $this->state->user_id);
                $this->assign('mchidadr', $this->state->user_id);
                $this->mchid = $this->state->user_id;

                //商户自由支付控制
                $payServiceR = $this->checkPayService($this->mchid);
                //
                if ($this->mchid == 1116 || $this->mchid == 181) {
                    $this->assign('pay_service', 1);
                } else {
                    $this->assign('pay_service', $payServiceR->ret ? 1 : 0);
                }
                $marketServiceR = $this->checkMarketService($this->mchid);
                $this->assign('market_service', $marketServiceR->ret ? 1 : 0);

                // 定义商户管理员变量
                $merchantAdminId = $this->state->user_id;
            }

            if ($adminGroupObj->data['group_id'] == 3) {
                if ($this->checkUserTimeLimit()) {
                    echo "<script type='text/javascript'>alert('账户已到期，请及时续费，联系电话：028-61112106');</script>";
                    StateModel::clear(StateModel::$ADMIN_USER);
                    echo "<script type='text/javascript'>location.href='{$url}/admin_login';</script>";
                    die;
                }
                $this->adminInfo = M('admin')->where(['admin_id' => $this->state->user_id])->find();
                $this->assign('mchid', $this->adminInfo['parent_admin_id']);
                $this->assign('mchidadr', $this->adminInfo['parent_admin_id']);
                $this->assign('branchid', $this->state->user_id);
                $this->mchid = $this->adminInfo['parent_admin_id'];
                // 定义商户管理员变量
                $merchantAdminId = $this->mchid;
            }
        }

        # 后台拼车服务
        $this->assign('line_service', $this->isLineServiceEnabled());

        # 后台快车服务：商户ID写入了数据表line_fast_service，则开通了商户快车服务，否则未开通快车服务
        $lineFastServiceR = $this->find(array('mchid' => $this->state->user_id), 'LineFastService');
        $this->assign('line_fast_service', $lineFastServiceR->ret ? 1 : 0);

        # 后台出租车服务：商户ID写入了数据表line_taxi_service，则开通了商户出租车服务，否则未开通出租车服务
        $lineTaxiServiceR = $this->find(array('mchid' => $this->state->user_id), 'LineTaxiService');
        $this->assign('line_taxi_service', $lineTaxiServiceR->ret ? 1 : 0);

        # 后台学生号服务
        $this->assign('line_student_service', $this->isLineStudentServiceEnabled());

        # 后台包车服务
        $this->assign('line_chartered_service', $this->isLineCharteredServiceEnabled());

        # 后台定制班线车服务
        $this->assign('line_class_service', 1);

        # 后台带货服务
        $this->assign('take_goods_service', $this->takeGoodsServiceEnabled());

        # 后台代办服务
        $this->assign('agency_service', $this->agencyServiceEnabled());

        # 后台顺风车服务
        $this->assign('mileage_service', $this->mileageServiceEnabled());

        # 后台电话叫车服务：商户ID写入了数据表line_phone_service，则开通了商户电话叫车服务，否则未开通电话叫车服务
        $linePhoneServiceR = $this->find(array('mchid' => $merchantAdminId), 'LinePhoneService');
        if ($linePhoneServiceR->ret) {
            $this->assign('line_phone_service', 1);
        }

        $this->assign('qrurl', $this->getqrcodeurl());
        $this->assign('get', $_GET);
        $this->assign('version_backend_info', $this->getCurrentVersionFrontendInfo());

        $this->assign('tech_support', $this->defineMerchantTechSupport($merchantAdminId, $this->checkLogoIsTimeOut()));
        $this->assign('merchant_logo', $this->defineMerchantLogo($merchantAdminId, $this->checkLogoIsTimeOut()));
        // 后台拼车菜单名称
        $this->assign('line_name', $this->defineMerchantLineName($merchantAdminId));
        // 后台定制客运菜单名称
        $this->assign('line_class_name', $this->defineMerchantLineClassName($merchantAdminId));
        // 后台带货菜单名称
        $this->assign('take_goods_name', $this->defineMerchantTakeGoodsName($merchantAdminId));
        // 后台代办菜单名称
        $this->assign('agency', $this->defineMerchantAgency($merchantAdminId));
        // 后台顺风车菜单名称
        $this->assign('free_car_name', $this->defineMerchantFreeCarName($merchantAdminId));
        // 后台快车菜单名称
        $this->assign('line_fast_name', $this->defineMerchantLineFastName($merchantAdminId));

        $urls = $this->console($this->state->user_id, $adminGroupObj->data['group_id']);

        $this->assign('mch_url', $urls['mch_url']);
        $this->assign('mch_url_dispatching', $urls['dispatching_url']);
        $this->assign('mch_url_schedule', $urls['dingzhikeyun_line_url']);
        $this->assign('suggestion_url', $urls['suggestion_url']);

        $this->assign('line_category', []);
        $lineCategory = $this->select([
            'mchid'                     => $merchantAdminId,
            'is_del'                    => 0,
            'line_category_business_id' => 5,
        ], 0, 0, null, 'LineCategory', ['id', 'title']);
        if ($lineCategory->ret) {
            \Log::write('transferorder:' . json_encode($lineCategory->data));
            $this->assign('line_category', $lineCategory->data);
        }
    }

    /**
     * 拼车服务：商户ID配置到数组中，则未开通拼车服务；不在数组中，则开通拼车服务
     * @return integer
     */
    private function isLineServiceEnabled()
    {
        $ids = array();
        // 拼车服务已禁用商户ID列表
        $config = $this->find(['key' => 'PINCHE_SERVICE_DISABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 0
            : 1;
    }

    /**
     * 学生号服务：商户ID配置到数组中，则开通学生号服务；不在数组中，则不开通学生号服务
     *
     * @return integer
     */
    private function isLineStudentServiceEnabled()
    {
        // 学生服务已开通商户ID列表
        $ids = array();
        $config = $this->find(['key' => 'XUESHENGHAO_SERVICE_ENABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 1
            : 0;
    }

    /**
     * 包车服务：商户ID配置到数组中，则未开通包车服务；不在数组中，则开通包车服务
     *
     * @return integer
     */
    private function isLineCharteredServiceEnabled()
    {
        // 包车服务已禁用商户ID列表
        $ids = array();
        $config = $this->find(['key' => 'BAOCHE_SERVICE_DISABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 0
            : 1;
    }

    /**
     * 带货服务：商户ID配置到数组中，则未开通带货服务；不在数组中，则开通带货服务
     *
     * @return integer
     */
    private function takeGoodsServiceEnabled()
    {
        // 带货服务已禁用商户ID列表
        $ids = array();
        $config = $this->find(['key' => 'DAIHUO_SERVICE_DISABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 0
            : 1;
    }

    /**
     * 代办服务：商户ID配置到数组中，则未开通代办服务；不在数组中，则开通代办服务
     *
     * @return integer
     */
    private function agencyServiceEnabled()
    {
        // 代办服务已禁用商户ID列表
        $ids = array();
        $config = $this->find(['key' => 'DAIBAN_SERVICE_DISABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 0
            : 1;
    }


    /**
     * 顺风车服务：商户ID配置到数组中，则未开通顺风车服务；不在数组中，则开通顺风车服务
     *
     * @return integer
     */
    private function mileageServiceEnabled()
    {
        // 顺风车服务已禁用商户ID列表
        $ids = array();
        $config = $this->find(['key' => 'SHUNFENGCHE_SERVICE_DISABLE_MERCHANT_IDS'], 'SystemConfig', 'key,value');
        if ($config->ret) {
            $ids = explode(',', $config->data['value']);
        }
        return in_array($this->mchid, $ids)
            ? 0
            : 1;
    }

    /**
     * 商户技术支持菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantTechSupport($mchid, $logoServiceTimeout = true)
    {
        $zero = 0;
        $three = 3;
        switch ($mchid) {
            case 942: #
                $name = $logoServiceTimeout
                    ? $three
                    : $zero;
                break;
            case 1170: #
                $name = $logoServiceTimeout
                    ? $three
                    : $zero;
                break;
            case 1174: #
                $name = $logoServiceTimeout
                    ? $three
                    : $zero;
                break;
            case 1181: #
                $name = $logoServiceTimeout
                    ? $three
                    : $zero;
                break;
            case 1258: #
                $name = $logoServiceTimeout
                    ? $three
                    : $zero;
                break;
            case 1294: #约车万顺
                $name = 1;
                break;
            default:
                $name = $zero;
                break;
        }
        return $name;
    }


    /**
     * 商户LOGO菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantLogo($mchid, $logoServiceTimeout = true)
    {
        $default = C('FILE_ROOT') . 'Theme/images/Admin/newlogo.png';
        switch ($mchid) {
            case 942: #
                $name = C('FILE_ROOT') . 'upload/merchant/ybsfc/ybsfc_logo.png';
                break;
            case 969: #
                $name = C('FILE_ROOT') . 'upload/merchant/qjcjcxglxt/qjcjcxglxt.png';
                break;
            case 1032: #
                $name = C('FILE_ROOT') . 'upload/merchant/zyhqlx/zyhqlx_logo.png';
                break;
            case 937: #
                $name = C('FILE_ROOT') . 'upload/merchant/axqpcpt/axqpcpt.png';
                break;
            case 1050: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/clcx/clcx_logo.png'
                    : $default;
                break;
            case 1075: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/xylx/xylx_logo.png'
                    : $default;
                break;
            case 1091: # 黄河出行
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/hhcx/hhcx_logo.png'
                    : $default;
                break;
            case 1116: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/bmyz/bmyz_logo.png'
                    : $default;
                break;
            case 1135: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/ssswc/ssswc_logo.png'
                    : $default;
                break;
            case 1170: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/xyjy/xyjy_logo.png'
                    : $default;
                break;
            case 1174: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/ddcjcx/ddcjcx_logo.png'
                    : $default;
                break;
            case 1181: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/htkc/htkc_logo.png'
                    : $default;
                break;
            case 1258: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/xtcx/xtcx_logo.png'
                    : $default;
                break;
            case 1310: #
                $name =  $logoServiceTimeout
                    ? C('FILE_ROOT') . 'upload/merchant/ykx/cczhaoche_yukexing_logo.jpg'
                    : $default;
                break;
            case 1340: #吴汇行
                $name = 'https://cczhaoche.oss-cn-wulanchabu.aliyuncs.com/images/nav/nav-logo-1340.png';
                break;
            default:
                $name = $default;
                break;
        }
        return $name;
    }

    /**
     * 快车菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantLineFastName($mchid)
    {
        switch ($mchid) {
            case 950: #
                $name = '同城快车管理';
                break;
            default:
                $name = '快车管理';
                break;
        }
        return $name;
    }

    // public function disableMerchantLineFastName()
    // {
    //     return '1294,1116';
    // }

    /**
     * 顺风车菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantFreeCarName($mchid)
    {
        switch ($mchid) {
            case 950: #
                $name = '跑腿管理';
                break;
            case 1238: #
                $name = '[城际快车]顺风车管理';
                break;
            default:
                $name = '顺风车管理';
                break;
        }
        return $name;
    }

    /**
     * 代办菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantAgency($mchid)
    {
        switch ($mchid) {
            case 950: #
                $name = '跑腿管理';
                break;
            default:
                $name = '代办管理';
                break;
        }
        return $name;
    }

    /**
     * 拼车菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantLineName($mchid)
    {
        switch ($mchid) {
            case 1289: # 阳光快客
                $name = '特快拼车管理';
                break;
            case 1238: #
                $name = '跨城拼车管理';
                break;
            default:
                $name = '拼车管理';
                break;
        }
        return $name;
    }

    // protected function disableMerchantLineName()
    // {
    //     return '1294,1091';
    // }

    /**
     * 班线车管理菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantLineClassName($mchid)
    {
        switch ($mchid) {
            case 1289: # 阳光快客
                $name = '定制班线管理';
                break;
            case 950: #
                $name = '城际网约车管理';
                break;
            case 969: #
                $name = '城际网约车管理';
                break;
            case 1032: #
                $name = '巴士管理';
                break;
            case 1116: #
                $name = '预约订制出行管理';
                break;
            case 1238: #
                $name = '[定制客运]定制班线车管理';
                break;
            case 1316:
                $name = '城际约车管理';
                break;
            default:
                $name = '定制班线车管理';
                break;
        }
        return $name;
    }

    /**
     * 带货菜单后台展示名称
     *
     * @param integer $mchid
     * @return string
     */
    protected function defineMerchantTakeGoodsName($mchid)
    {
        switch ($mchid) {
            case 1289: # 阳光快客
                $name = '代销物品管理';
                break;
            case 950: #
                $name = '小件速运管理';
                break;
            default:
                $name = '带货管理';
                break;
        }
        return $name;
    }

    public function getqrcodeurl()
    {
        $uid = $this->state ? $this->state->user_id : 0;
        if (!$uid) {
            return null;
        } else {
            $wx = M('wxuser_setup')->where('uid=' . $this->state->user_id)->field('qrcode_url')->find();
            return $wx['qrcode_url'];
        }
    }

    /**
     * 获取当前组code
     * @param int $user_id
     */
    private function _getCurrentGroupCode($user_id)
    {
        $am = new AdminModel();
        $data = $am->where(array('admin_id' => $user_id))->field('group_id')->find();
        if ($data) {
            $magm = new MetaAdminGroupModel();
            $data = $magm->where(array('group_id' => $data['group_id']))->field('code')->find();
            if ($data) {
                $r = new ResultModel(true, $data['code']);
            } else {
                $r = new ResultModel(false, '组ID不存在');
            }
        } else {
            $r = new ResultModel(false, '用户ID不存在');
        }
        return $r;
    }

    /**
     * 获取当前组信息
     * @param int $user_id
     */
    protected function _getCurrentGroupInfo($user_id)
    {
        $am = new AdminModel();
        $data = $am->where(array('admin_id' => $user_id))->field('group_id')->find();
        if ($data) {
            $magm = new MetaAdminGroupModel();
            $groupId = $data['group_id'];
            $data = $magm->where(array('group_id' => $data['group_id']))->field('code')->find();
            if ($data) {
                $r = new ResultModel(true, array('group_id' => $groupId, 'code' => $data['code']));
            } else {
                $r = new ResultModel(false, '组ID不存在');
            }
        } else {
            $r = new ResultModel(false, '用户ID不存在');
        }
        return $r;
    }

    /**
     * 分配车主提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $driver_id 车主ID
     * @param string $action_code 操作标码
     * @param  int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignDriverRemindTask($assigner_id, $driver_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaDriverRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'driver_id' => $driver_id), 'DriverRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('DriverRemindTask', array('assigner_id' => $assigner_id, 'driver_id' => $driver_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该车主没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '车主提醒操作标码不存在');
        }
        return $this->result;
    }

    /**
     * 分配乘客提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $passenger_id 乘客ID
     * @param string $action_code 操作标码
     * @param int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignPassengerRemindTask($assigner_id, $passenger_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaPassengerRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'passenger_id' => $passenger_id), 'PassengerRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('PassengerRemindTask', array('assigner_id' => $assigner_id, 'passenger_id' => $passenger_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该乘客没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '乘客提醒操作标码不存在');
        }
        return $this->result;
    }

    /**
     *检验用户是否过期
     *返回一个布尔值
     */
    private function checkUserTimeLimit()
    {

        if ($this->admin_group_id == 3) {
            $where['admin_id'] = $this->getParentId();
        } else {
            $where['admin_id'] = $this->state->user_id;
        }
        $adminR = $this->find($where, 'Admin', 'endtime');
        if ($adminR->ret) {
            if (time() >= strtotime($adminR->data['endtime'])) {
                return true;
            }
        }
        return false;
    }

    private function getParentId()
    {
        $where['admin_id'] = $this->state->user_id;
        $adminR = $this->find($where, 'Admin', 'parent_admin_id');
        return $adminR->data['parent_admin_id'];
    }

    /**
     *检验手机号码
     */
    protected function mobile($val)
    {
        //        return preg_match("/^1[34578]{1}\d{9}$/",$val);
        return preg_match("/^1\d{10}$/", $val);
    }

    /**
     *校验是否有效的数字
     */
    protected function areaCode($val)
    {
        return preg_match("/^\d+$/", $val);
    }
    /**
     *校验是否有效的邮箱
     */
    protected function checkEmail($val)
    {
        return preg_match("/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/", $val);
    }
    /**
     *检验是否空
     */
    protected function isEmpty($val)
    {
        if (!isset($val)) {
            return false;
        }
        if (empty($val)) {
            return false;
        }
        if (is_null($val)) {
            return false;
        }
        if ($val == "null") {
            return false;
        }
        return true;
    }

    /**
     *
     *获取商户新增套餐的数量
     *@param  商户id $mchid
     *@return 返回一个数组 $data
     */
    protected function getNewBusinessPlan($mchid)
    {
        $data = array(
            'sms' => 0,
            'driver' => 0,
            'stroge' => 0,
            'free_ride_driver' => 0,
            'marketing_sms' => 0,
        );

        $array['admin_id'] = $mchid;
        $array['is_del'] = 0;
        $reqsult = M('ValueOfMerchantPackageTypeStatus')->field('stsv_id,end_time')->where($array)->select();
        if ($reqsult) {
            foreach ($reqsult as $key => $val) {
                if ($val) {
                    $vomptsR = $this->find(array('id' => $val['stsv_id']), 'SetTypeStatusValue', 'category_id,num,value');
                    if ($vomptsR->ret) {
                        if ($vomptsR->data['category_id'] == 1 && $val['end_time'] >= date('Y-m-d H:i:m')) {
                            $data['sms'] += $vomptsR->data['num'] * $vomptsR->data['value'];
                        } elseif ($vomptsR->data['category_id'] == 2 && $val['end_time'] >= date('Y-m-d H:i:m')) {
                            $data['driver'] += $vomptsR->data['num'] * $vomptsR->data['value'];
                        } elseif ($vomptsR->data['category_id'] == 3 && $val['end_time'] >= date('Y-m-d H:i:m')) {
                            $data['stroge'] += $vomptsR->data['num'] * $vomptsR->data['value'];
                        } elseif ($vomptsR->data['category_id'] == 5 && $val['end_time'] >= date('Y-m-d H:i:m')) {
                            $data['free_ride_driver'] += $vomptsR->data['num'] * $vomptsR->data['value'];
                        } elseif ($vomptsR->data['category_id'] == 6 && $val['end_time'] >= date('Y-m-d H:i:m')) {
                            $data['marketing_sms'] += $vomptsR->data['num'] * $vomptsR->data['value'];
                        }
                    }
                }
            }
        } else {
            return null;
        }

        return $data;
    }

    /**
     *获取套餐通过套餐id
     *@param  商户id $mchid
     *@return 返回一个数组 $data
     */
    protected function getPackageByPackageId($package_id)
    {
        $data = null;
        $packageR = $this->find(array('package_id' => $package_id), 'Package', 'driver,storage,shortmessage,free_ride_driver,shortmessage_marketing');
        if ($packageR->ret) {
            $data['shortmessage'] = $packageR->data['shortmessage'];
            $data['driver'] = $packageR->data['driver'];
            $data['storage'] = $packageR->data['storage'];
            $data['free_ride_driver'] = $packageR->data['free_ride_driver'];
            $data['shortmessage_marketing'] = $packageR->data['shortmessage_marketing'];
        }
        return $data;
    }

    /**
     *获取司机套餐通过商户id
     *@param  商户id $mchid
     *@return 返回一个数组ResultModel $data
     */
    protected function getMerchantDriverCountByAdminId($mchid)
    {
        $ret = new ResultModel(false);
        if (empty($mchid)) {
            return $ret;
        }

        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        if ($mchR->ret) {
            $countNum = 0;
            $package = $this->getPackageByPackageId($mchR->data['package_id']); // 获取司机套餐数量
            if (!$package) {
                return $ret;
            }
            $business = $this->getNewBusinessPlan($mchR->data['admin_id']); //获取新增的套餐数量
            if ($business) {
                $countNum = $package['driver'] + $business['driver'];
            } else {
                $countNum = $package['driver'];
            }

            $ret->ret = true;
            $ret->data = $countNum;
            $ret->count = 1;
        }

        return $ret;
    }

    /**
     *获取顺风车司机套餐通过商户id
     *@param  商户id $mchid
     *@return 返回一个数组ResultModel $data
     */
    protected function getMerchantFreeRideDriverCountByAdminId($mchid)
    {
        $ret = new ResultModel(false);
        if (empty($mchid)) {
            return $ret;
        }

        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        if ($mchR->ret) {
            $countNum = 0;
            $package = $this->getPackageByPackageId($mchR->data['package_id']); // 获取司机套餐数量
            if (!$package) {
                return $ret;
            }
            $business = $this->getNewBusinessPlan($mchR->data['admin_id']); //获取新增的套餐数量
            if ($business) {
                $countNum = $package['free_ride_driver'] + $business['free_ride_driver'];
            } else {
                $countNum = $package['free_ride_driver'];
            }
            $ret->ret = true;
            $ret->data = $countNum;
            $ret->count = 1;
        }

        return $ret;
    }

    /**
     *获取时间区间内的订单
     *@param  管理员组 $group_id
     *@param  商户id $mchid
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getRegionOrdersCount($group_id, $mchid, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);
        $where = array();
        if (!empty($start_time) && !empty($end_time)) {
            $where['create_time'] = array('between', array($start_time, $end_time));
        }
        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " deleted_at IS NULL ";

        switch ($group_id) {
            case 1:
                break;
            case 2:
                $where['mchid'] = $mchid;
                break;
            case 3:
                $where['branchid'] = $mchid;
                break;
            default:
                return $ret;
        }
        $ret =  $this->count($where, 'Order');
        return $ret;
    }


    /**
     *获取时间区间内的线下线上交易额
     *@param  管理员组 $group_id
     *@param  商户id $mchid
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getTotalRegionTurnover($group_id, $mchid, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);

        $turnoverConditionString = "((is_pay=1 OR is_pre_pay=1) OR (is_pay=0 AND is_pre_pay=0  AND state=6))"; //线上线下交易金额
        //        $todayPriceString = "";
        if (!empty($start_time) && !empty($end_time)) {
            $turnoverConditionString .= " AND create_time>='" . $start_time . "' AND create_time<='" . $end_time . "' ";
        }

        switch ($group_id) {
            case 1:
                break;
            case 2:
                $turnoverConditionString .= " AND mchid=" . $mchid;
                break;
            case 3:
                $turnoverConditionString .= " AND branchid=" . $mchid;
                break;
            default:
                return $ret;
        }

        $turnData = M()->table('cp_order o')
            ->where($turnoverConditionString)
            ->field('sum(price) as turnover')
            ->order('o.create_time desc')
            ->select();
        //        $ret = $this->select($turnoverConditionString,null,null,'create_time desc','Order','sum(price) as turnover');

        if ($turnData) {
            $ret->ret = true;
            $ret->data = $turnData[0];
            $ret->count = count($turnData);
        }

        return $ret;
    }

    /**
     *获取时间区间内的线上交易额
     *@param  管理员组 $group_id
     *@param  商户id $mchid
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getOnRegionTurnover($group_id, $mchid, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);

        # 5.31增加支持逻辑订单逻辑删除
        $turnoverConditionString = "(is_pay=1 OR is_pre_pay=1) AND deleted_at IS NULL "; //线上交易金额
        //        $todayPriceString = "";
        if (!empty($start_time) && !empty($end_time)) {
            $turnoverConditionString .= " AND create_time>='" . $start_time . "' AND create_time<='" . $end_time . "' ";
        }

        switch ($group_id) {
            case 1:
                break;
            case 2:
                $turnoverConditionString .= " AND mchid=" . $mchid;
                break;
            case 3:
                $turnoverConditionString .= " AND branchid=" . $mchid;
                break;
            default:
                return $ret;
        }

        $turnData = M()->table('cp_order o')
            ->where($turnoverConditionString)
            ->field('sum(price) as turnover')
            ->order('o.create_time desc')
            ->select();
        //        $ret = $this->select($turnoverConditionString,null,null,'create_time desc','Order','sum(price) as turnover');

        if ($turnData) {
            $ret->ret = true;
            $ret->data = $turnData[0];
            $ret->count = count($turnData);
        }

        return $ret;
    }


    /**
     *获取时间区间内的线下交易额
     *@param  管理员组 $group_id
     *@param  商户id $mchid
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getDownRegionTurnover($group_id, $mchid, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);

        $turnoverConditionString = "is_pay=0 AND is_pre_pay=0  AND state=6"; //线下交易金额
        //        $todayPriceString = "";
        if (!empty($start_time) && !empty($end_time)) {
            $turnoverConditionString .= " AND create_time>='" . $start_time . "' AND create_time<='" . $end_time . "' ";
        }

        switch ($group_id) {
            case 1:
                break;
            case 2:
                $turnoverConditionString .= " AND mchid=" . $mchid;
                break;
            case 3:
                $turnoverConditionString .= " AND branchid=" . $mchid;
                break;
            default:
                return $ret;
        }

        $turnData = M()->table('cp_order o')
            ->where($turnoverConditionString)
            ->field('sum(price) as turnover')
            ->order('o.create_time desc')
            ->select();
        //        $ret = $this->select($turnoverConditionString,null,null,'create_time desc','Order','sum(price) as turnover');

        if ($turnData) {
            $ret->ret = true;
            $ret->data = $turnData[0];
            $ret->count = count($turnData);
        }

        return $ret;
    }

    /**
     *获取时间区间内的退款额
     *@param  管理员组 $group_id
     *@param  商户id $mchid
     *@param  开始时间 $start_time
     *@param  结束时间 $end_time
     *@return 返回一个 ResultModel结果
     */
    protected function getOnRegionRefund($group_id, $mchid, $start_time = null, $end_time = null)
    {
        $ret = new ResultModel(false);

        $refundConditionString = "r.status IN (1,2,3)";
        //        $refundConditionString = "(o.is_pay=1 OR o.is_pre_pay=1)";
        if (!empty($start_time) && !empty($end_time)) {
            $refundConditionString .= " AND r.create_time>='" . $start_time . "' AND r.create_time<='" . $end_time . "' ";
        }
        # 5.31增加支持逻辑订单逻辑删除
        $refundConditionString .= ' AND o.deleted_at IS NULL ';

        switch ($group_id) {
            case 1:
                break;
            case 2:
                $refundConditionString .= " AND o.mchid=" . $mchid;
                break;
            case 3:
                $refundConditionString .= " AND o.branchid=" . $mchid;
                break;
            default:
                return $ret;
        }
        $refundData = M()->table('cp_refunds r')
            ->join(" LEFT JOIN cp_order o ON o.order_id=r.order_id")
            ->where($refundConditionString)
            ->field('sum(amount) as price')
            ->order('r.create_time desc')
            ->select();

        if ($refundData) {
            $ret->ret = true;
            $ret->data = $refundData[0];
            $ret->count = count($refundData);
        }

        return $ret;
    }

    /**
     * 获取订单相关信息集合
     * @param int $order_id 订单编号
     * @return ResultModel 订单状态相关信息
     */
    protected function getOrderStateInfo($order_id)
    {
        $ret = new \ResultModel(false);
        if (!$order_id) {
            return $ret;
        }
        $orderStateInfoData = array(
            'pay_state' => 0,            //支付状态；0-未支付；1-已支付
            'state_message' => '',      //订单状态描述
            'order_close_onoff' => 0,         //订单关闭开关
            'order_refund_onoff' => 0,         //订单退款开关
            'order_over_onoff' => 0,         //订单完成开关：0-关闭；1-开启
        );

        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if ($orderR->ret) {
            if ($orderR->data['state'] == 1) {
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                    if ($orderR->data['is_pre_pay'] == 1 || $orderR->data['is_pay'] == 1) {
                        $orderStateInfoData['order_refund_onoff'] = 1;
                        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                            $orderStateInfoData['order_over_onoff'] = 1;
                        }
                        if ($orderR->data['appoint'] == 0) {
                            $orderStateInfoData['state_message'] = "预定成功,待指派";
                        } else {
                            $orderStateInfoData['state_message'] = "预定成功,已指派，待司机接单";
                        }
                    } else {
                        $orderStateInfoData['order_close_onoff'] = 1;
                        $orderStateInfoData['state_message'] = "预定中";
                    }
                } else {
                    $orderStateInfoData['order_close_onoff'] = 1;
                    if ($orderR->data['appoint'] == 0) {
                        $orderStateInfoData['state_message'] = "预定成功,待指派";
                    } else {
                        $orderStateInfoData['state_message'] = "预定成功,已指派，待司机接单";
                    }
                }
            } elseif ($orderR->data['state'] == 2) {
                $orderStateInfoData['state_message'] = "司机已接单,等待接乘客上车";
                if ($orderR->data['is_pre_pay'] == 1 || $orderR->data['is_pay'] == 1) {
                    $orderStateInfoData['order_refund_onoff'] = 1;
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                        $orderStateInfoData['order_over_onoff'] = 1;
                    }
                }
            } elseif ($orderR->data['state'] == 3) {
                //已上车,等待出发
                $orderStateInfoData['state_message'] = "已上车";
                if ($orderR->data['is_pre_pay'] == 1 || $orderR->data['is_pay'] == 1) {
                    $orderStateInfoData['order_refund_onoff'] = 1;
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                        $orderStateInfoData['order_over_onoff'] = 1;
                    }
                }
            } elseif ($orderR->data['state'] == 4) {
                //在路上
                $orderStateInfoData['state_message'] = "在路上";
                if ($orderR->data['is_pre_pay'] == 1 || $orderR->data['is_pay'] == 1) {
                    $orderStateInfoData['order_refund_onoff'] = 1;
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                        $orderStateInfoData['order_over_onoff'] = 1;
                    }
                }
            } elseif ($orderR->data['state'] == 5) {
                //已送达，待支付
                $orderStateInfoData['state_message'] = "已送达，待支付";
                if ($orderR->data['is_pre_pay'] != 1 && $orderR->data['is_pay'] != 1) {
                    if (
                        $orderR->data['type'] == \CommonDefine::ORDER_TYPE_1
                        || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_2
                        || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_3
                        || $orderR->data['type'] == \CommonDefine::ORDER_TYPE_4
                    ) {
                        $orderStateInfoData['order_close_onoff'] = 1;
                    } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                    } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                        $orderStateInfoData['order_close_onoff'] = 1;
                    }
                } else {
                    $orderStateInfoData['order_refund_onoff'] = 1;
                    if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                        $orderStateInfoData['order_over_onoff'] = 1;
                    }
                }
            } elseif ($orderR->data['state'] == 6) {
                //已完成
                $orderStateInfoData['state_message'] = "已完成";
            } elseif ($orderR->data['state'] == 7) {
                //获取已退款
                $orderStateInfoData['state_message'] = "已取消";
                if ($orderR->data['is_pre_pay'] == 1 || $orderR->data['is_pay'] == 1) {
                    $refundR = $this->find(array('order_id' => $orderR->data['order_id']), 'Refunds');
                    if ($refundR->ret) {
                        switch ($refundR->data['status']) {
                            case 2:
                                $orderStateInfoData['state_message'] .= ",退款中";
                                break;
                            case 3:
                                $orderStateInfoData['state_message'] .= ",退款成功";
                                break;
                            case 4:
                                $orderStateInfoData['state_message'] .= ",退款失败";
                                break;
                            default:
                                $orderStateInfoData['state_message'] .= ",退款申请中";
                                break;
                        }
                    } else {
                        // $orderSubOrderR = $this->find(['relation_order_id' => $orderR->data['order_id']], 'OrderSub');
                        // if($orderSubOrderR->ret) {
                        //     $orderStateInfoData['state_message'] .= "退款成功（班线车接送单）";
                        // } else {
                        //     $orderStateInfoData['state_message'] .= "订单异常（退款失败）";
                        // }
                    }
                }
            } elseif ($orderR->data['state'] == \CommonDefine::ORDER_STATE_8) {
                //已关闭
                $orderStateInfoData['state_message'] = "已关闭";
            } else {
                $orderStateInfoData['state_message'] = "订单异常";
            }

            if ($orderR->data['is_temp']) {
                if (
                    $orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1
                    || $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1
                ) {
                    $orderStateInfoData['pay_state'] = 1;
                    $orderStateInfoData['order_close_onoff'] = 0;
                }
            } else {
                if ($orderR->data['is_pay']) {
                    $orderStateInfoData['pay_state'] = 1;
                    $orderStateInfoData['order_close_onoff'] = 0;
                }
            }
            $ret->ret = true;
            $ret->data = $orderStateInfoData;
            $ret->count = 1;
        }

        return $ret;
    }

    //检测是否是自定义包车调度管理分台
    protected function checkIsLineCharterCustomBranch($branchId)
    {
        $ret = new ResultModel(false);
        $lineCharterCustomBranchConfigR = $this->find(array('account_id' => $this->state->user_id, 'account_type' => CommonDefine::SYSTEM_ROLE_1, 'key' => \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG, 'value' => $branchId), 'SystemConfig');
        if ($lineCharterCustomBranchConfigR->ret) {
            $ret->ret = true;
        }

        return $ret;
    }

    /**
     * 查询商户是否开启班线摆渡车服务
     * @return integer
     */
    public function queryMerchantFerryService()
    {
        $lineClassFerryServiceR = $this->find(['mchid' => $this->mchid], 'LineClassFerryService');
        return $lineClassFerryServiceR->ret ? 1 : 0;
    }

    /**
     * 积分服务是否开启
     *
     * @return int
     */
    public function queryPointsService()
    {
        $enabled = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/merchants/{$this->mchid}/points/enabled", 'get');
        $response = json_decode($enabled, true);
        if (
            $response['status'] == 'success'
            && $response['data']['points'] == true
        ) {
            return 1;
        } else {
            return 0;
        }
    }

    public function console($mchid, $groupId = 0)
    {
        /**
         * @Date 2022.07.14
         * <AUTHOR>
         * @version 3.3
         */
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {

            session('mch_access_token', $token['data']['access_token']);

            if ($groupId == 3) {
                $mchUrl = C('CC_MCH_HOST') . '/#/operationCenter/appointment?token=' . $token['data']['access_token'];
                $dispatchingUrl = $dingzhikeyunLineUrl = '';
            } else {
                $mchUrl = C('CC_MCH_HOST') . '/#/dataOverview?token=' . $token['data']['access_token'];
                $dispatchingUrl = C('CC_MCH_HOST') . '/#/operationCenter/dispatching?token=' . $token['data']['access_token'];
                $dingzhikeyunLineUrl = C('CC_MCH_HOST') . '/#/lineManage/dingzhikeyunLineManage?token=' . $token['data']['access_token'];
                $suggestionUrl = C('CC_MCH_HOST') . '/#/suggestionManage/index?token=' . $token['data']['access_token'];
            }
        }
        return [
            'mch_url' => $mchUrl,
            'dispatching_url' => $dispatchingUrl,
            'dingzhikeyun_line_url' => $dingzhikeyunLineUrl,
            'suggestion_url' => $suggestionUrl,
        ];
    }
}

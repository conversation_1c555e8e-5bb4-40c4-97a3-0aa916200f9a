<?php
import('@/Common/Socket');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SocketPageAction
 *
 * <AUTHOR>
 */
class SocketPageAction extends AdminCoreAction{
    private $s = null;

    function __construct(){
        if(empty($this->s)){
            $this->s = new Socket();
        }
    }

    //socket
    public function indexPage()
    {
        $this->s->run();
    }

    public function closePage(){
        $this->s->close();
    }


}

?>

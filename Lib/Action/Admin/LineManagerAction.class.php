<?php

import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Admin/OrderManagerAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 线路管理模块
 *
 * <AUTHOR>
 */
class LineManagerAction extends AdminCoreAction
{
    /**
     * 获取线路列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function getLineare($oid, $mchid)
    {
        $r = $this->select(array('address_id' => $oid, 'mchid' => $mchid), null, null, 'address_id asc', 'SupportCity', 'address_id,name');
        return $this->output($r);
    }

    public function getLineList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0, $search_branchid = 0, $search_line_id = 0, $search_start_name = null, $search_end_name = null)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if ($search_branchid) {
            $str .= ' AND branchid = ' . $search_branchid;
        } elseif (!empty($branchid) && $branchid) {
            $str .= ' AND branchid = ' . $branchid;
        }
        if ($search_line_id) {
            $str .= ' AND id = ' . $search_line_id;
        }
        if (!empty($search_start_name)) {
            $str .= " AND start_name like '%" . $search_start_name . "%' ";
        }
        if (!empty($search_end_name)) {
            $str .= " AND end_name like '%" . $search_end_name . "%' ";
        }

        $str .= ' AND is_phone_line = ' . \CommonDefine::LINE_PHONE_TYYE_0;

        $r = $this->select($str, $page, $size, 'create_time desc', 'Line');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }
                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];
            }
        }
        return $this->output($r);
    }

    /**
     * 快车搜索
     *
     * @param string $fields
     * @param integer $mchid
     * @param integer $branchid
     * @param integer $page
     * @param integer $size
     * @param integer $st
     * @param integer $search_branchid
     * @param integer $search_line_id
     * @param string $search_start_name
     * @param string $search_end_name
     * @param string $search_type
     * @return void
     */
    public function getLineFastList($fields = null, $mchid, $branchid = 0, $page = 1, $size = 10, $st = 0, $search_branchid = 0, $search_line_id = 0, $search_start_name = '', $search_end_name = '', $search_type = '')
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if ($search_branchid) {
            $str .= ' AND branchid = ' . $search_branchid;
        } elseif (!empty($branchid) && $branchid) {
            $str .= ' AND branchid = ' . $branchid;
        }
        if ($search_line_id) {
            $str .= ' AND id = ' . $search_line_id;
        }
        if (!empty($search_start_name)) {
            $str .= " AND start_name like '%" . $search_start_name . "%' ";
        }
        if ($search_type !== '') {
            $str .= " AND type = {$search_type} ";
        }

        $r = $this->select($str, $page, $size, 'create_time desc', 'LineFast');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $startAddress = sprintf(
                    '【%s-%s%s】',
                    $this->checkingGdAddressCode($line['start_province_code'])->data['address'],
                    $this->checkingGdAddressCode($line['start_city_code'])->data['address'],
                    $line['start_area_code'] ? '-' . $this->checkingGdAddressCode($line['start_area_code'])->data['address'] : ''
                );

                $r->data[$k]['route'] = $startAddress;

                $r->data[$k]['cn_type'] = $line['type'] == 0 ? '快车' : '快车-摆渡车';
            }
        }
        return $this->output($r);
    }

    /*
     * 获取出租车路线
     */
    public function getLineTaxiList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0, $search_branchid = 0, $search_line_id = 0, $search_start_name = null, $search_end_name = null)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if ($search_branchid) {
            $str .= ' AND branchid = ' . $search_branchid;
        } elseif (!empty($branchid) && $branchid) {
            $str .= ' AND branchid = ' . $branchid;
        }
        if ($search_line_id) {
            $str .= ' AND id = ' . $search_line_id;
        }
        if (!empty($search_start_name)) {
            $str .= " AND start_name like '%" . $search_start_name . "%' ";
        }

        $r = $this->select($str, $page, $size, 'create_time desc', 'LineTaxi');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $startAddress = "[" . $this->checkingGdAddressCode($line['start_province_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_city_code'])->data['address'] . "]";
                $r->data[$k]['route'] = $startAddress;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取电话叫车列表
     * @param null $fields
     * @param $mchid
     * @param null $branchid
     * @param int $page
     * @param int $size
     * @param int $st
     * @param int $search_branchid
     * @param int $search_line_id
     * @return null|resultModel
     */
    public function getLinePhoneList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10, $st = 0, $search_branchid = 0, $search_line_id = 0, $search_start_name = null, $search_end_name = null, $is_phone_line_verify = -1)
    {
        if ($st != 0) {
            $str = "mchid = {$mchid} and is_del=0 and start_lng <> 0 ";
        } else {
            $str = "mchid = {$mchid} and is_del=0 ";
        }
        if ($search_branchid) {
            $str .= ' AND branchid = ' . $search_branchid;
        } elseif (!empty($branchid) && $branchid) {
            $str .= ' AND branchid = ' . $branchid;
        }
        if ($search_line_id) {
            $str .= ' AND id = ' . $search_line_id;
        }
        if (!empty($search_start_name)) {
            $str .= " AND start_name like '%" . $search_start_name . "%' ";
        }
        if (!empty($search_end_name)) {
            $str .= " AND end_name like '%" . $search_end_name . "%' ";
        }
        if ($is_phone_line_verify != -1) {
            $str .= ' AND is_phone_line_verify = ' . $is_phone_line_verify;
        }


        $str .= ' AND is_phone_line = ' . \CommonDefine::LINE_PHONE_TYYE_1;

        $r = $this->select($str, $page, $size, 'create_time desc', 'Line');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }
                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];

                //司机信息
                $r->data[$k]['driver_info'] = '';
                $driverWhere['dl.line_id'] = $line['id'];
                $driverWhere['dl.type'] = \CommonDefine::ORDER_TYPE_1;
                $driversR = M()->table('cp_driver_line dl')
                    ->join('LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id')
                    ->where($driverWhere)
                    ->field('d.driver_id,d.name,d.cellphone')
                    ->select();
                if (!empty($driversR)) {
                    foreach ($driversR as $i => $driver) {
                        $r->data[$k]['driver_info'] .=  ($i + 1) . "、[" . $driver['name'] . "] " . $driver['cellphone'] . "<br/>";
                    }
                }
            }
        }
        return $this->output($r);
    }

    public function getDriverList($branchid = 0, $driver_role_type = \CommonDefine::DRIVER_ROLE_0)
    {
        if ($this->admin_group_id != 3) {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        if ($driver_role_type != -1) {
            $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        }
        $where['branchid'] = $branchid;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,cellphone');
        $this->output($driver);
    }

    public function doSetIsShow($id = 0, $type = 0, $status = 0)
    {
        // type 1-拼车
        if ($id && $type && $status) {
            if ($status == 1) {
                $data['is_show'] = 1;
            } else {
                $data['is_show'] = 0;
            }
            $data['id'] = $id;
            if ($type == 1) {
                $model = 'Line';
            } elseif ($type == 2) {
                $model = 'LineChartered';
            } elseif ($type == 3) {
                $model = 'LineClass';
            } elseif ($type == 4) {
                $data['take_goods_price_id'] = $id;
                unset($data['id']);
                $model = 'TakeGoodsPrice';
            } elseif ($type == 5) {
                $data['agency_id'] = $id;
                unset($data['id']);
                $model = 'Agency';
            } elseif ($type == 7) {
                $model = 'LineFast';
            }
            if (!isset($data) && !$data && isset($model)) {
                return $this->output(new ResultModel(false, "操作失败，请稍后重试！"));
            }
            $r = $this->save($model, $data);
            return $this->output($r);
        } else {
            return $this->output(new ResultModel(false, "操作失败，请稍后重试！"));
        }
    }

    //获取线路详细
    public function getLineTotal($line_id)
    {
        $line = $this->find(array('id' => $line_id), 'Line');
        if ($line->ret) {
            $startArr = $this->getProvinceCityAreaCodeByCode($line->data['start_address_code']);
            $line->data['start_province_code'] = $startArr['province_code'];
            $line->data['start_city_code'] = $startArr['city_code'];
            $line->data['start_area_code'] = $startArr['area_code'];
            $endArr = $this->getProvinceCityAreaCodeByCode($line->data['end_address_code']);
            $line->data['end_province_code'] = $endArr['province_code'];
            $line->data['end_city_code'] = $endArr['city_code'];
            $line->data['end_area_code'] = $endArr['area_code'];
            $line->data['lci'] = $this->select(array('line_id' => $line->data['id'], 'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'LineFkLineCategory', 'line_category_id lci')->data;
            $line->data['dls'] = $this->select(array('line_id' => $line->data['id'], 'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $line->data['lc'] = array_column($line->data['lci'], 'lci');
        $dl = implode(",", array_column($line->data['dls'], 'dls'));
        unset($line->data['lci'], $line->data['dls']);
        $where['mchid'] = $this->state->user_id;
        $is_del = \CommonDefine::IS_DEL_0;
        $lc_id = implode(',', $line->data['lc']);
        $str = "";
        if ($lc_id) {
            $lc = $this->select("mchid = {$this->state->user_id} and is_del = {$is_del} and id in ({$lc_id})", null, null, null, 'LineCategory', 'title');

            foreach ($lc->data as $key => $v) {
                $str .= $v['title'] . '、';
            }
        }
        $line->data['lc'] = $str;
        $is_freeze = \CommonDefine::IS_FREEZE_0;
        //        $from_type = \CommonDefine::FROM_TYPE_0;
        $driver_role_type = \CommonDefine::DRIVER_ROLE_0;
        $str1 = "";
        if ($dl) {
            $driver = $this->select("mchid = {$this->state->user_id} and driver_id in ({$dl}) and is_freeze = {$is_freeze} and driver_role_type = {$driver_role_type}", null, null, null, 'Driver', 'name');
            //            $driver = $this->select("mchid = {$this->state->user_id} and driver_id in ({$dl}) and is_freeze = {$is_freeze} and from_type= {$from_type} and driver_role_type = {$driver_role_type}", null, null, null, 'Driver', 'name');
            foreach ($driver->data as $key1 => $v1) {
                $str1 .= $v1['name'] . '、';
            }
        }
        $line->data['dl'] = $str1;
        $line->data['start_province_name'] = $this->getAddressName($line->data['start_province_code']);
        $line->data['start_city_name'] = $this->getAddressName($line->data['start_city_code']);
        $line->data['start_area_name'] = $this->getAddressName($line->data['start_area_code']);
        $line->data['end_province_name'] = $this->getAddressName($line->data['end_province_code']);
        $line->data['end_city_name'] = $this->getAddressName($line->data['end_city_code']);
        $line->data['end_area_name'] = $this->getAddressName($line->data['end_area_code']);
        $mcname = $this->find("parent_admin_id={$this->state->user_id}  and is_del = 0 and branch_type = 0 and admin_id = " . $line->data['branchid'], 'Admin', 'mchname');
        if ($mcname) {
            $line->data['ftname'] = $mcname->data['mchname'];
        } else {
            $line->data['ftname'] = "";
        }
        return $this->output($line);
    }

    public function getAddressName($code)
    {
        $add = $this->find("address_id = " . $code, 'SupportCity', 'name');
        if ($add) {
            return $add->data['name'];
        } else {
            return "";
        }
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLine($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $price, $channel_price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }
        unset($this->data['lc']);
        try {
            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }


            $flag = true;
            $this->startTrans();
            $r = $this->add('Line');
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            //拼车类型进行关联
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $r->data;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加线路");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLine($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $price, $channel_price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        unset($this->data['lc'], $this->data['driver']);
        try {
            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $this->startTrans();
            $r = $this->save('Line');
            $flag = true;
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            M("line_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id))->delete();
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $id;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)) {
                        $flag = false;
                    }
                }
            }
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }


        $this->doAddLog("修改线路");
        return $this->output($r);
    }

    /**
     * 删除拼车
     * @param int $id 线路ID
     */
    public function doDeleteLine($id)
    {
        //删除相关订单
        $r = $this->save('Line', array('id' => $id, 'is_del' => 1));
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 添加快车
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLineFast($mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code = null, $end_area_code = null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_7, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0, $kuaiche_title = '', $kuaiche_type = 0, $start_polygon = null)
    {
        try {
            $this->data['title'] = $kuaiche_title;
            $this->data['type'] = $kuaiche_type;
            // $this->data['start_polygon'] = $start_polygon;
            $this->data['start_polygon'] = json_encode(explode('-', $start_polygon));

            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $this->validateKuaicheAreaRepeated($kuaiche_type, $start_area_code, $start_city_code);
            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if ($startNameR->ret) {
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $flag = true;
            $this->startTrans();
            $r = $this->add('LineFast');
            $driver = json_decode($driver, true);

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加快车成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加快车成功");
        return $this->output($r);
    }

    public function validateKuaicheAreaRepeated($kuaiche_type, $start_area_code, $start_city_code, $id = 0)
    {
        $lineFastR = $this->find(array(
            'mchid' => $this->mchid,
            'type' => $kuaiche_type,
            'start_city_code' => $start_city_code,
            'start_area_code' => $start_area_code,
            'is_del' => 0,
            'id' => array('neq', $id),
        ), 'LineFast');
        if ($lineFastR->ret) {
            return $this->output(new ResultModel(false, "一个城市区/县只允许设置一个相同类型快车价目"));
        }
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineFast($id, $mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code = null, $end_area_code = null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_7, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0, $kuaiche_title = '', $kuaiche_type = 0, $start_polygon = '')
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $startCityR = $this->find(array('address_id' => $start_city_code), 'GdRegion');
        $this->data['start_name'] = $startCityR->ret ? $startCityR->data['name'] : "";
        $this->data['end_name'] = $startCityR->ret ? $startCityR->data['name'] : "";
        $this->data['title'] = $kuaiche_title;
        $this->data['type'] = $kuaiche_type;
        $this->data['start_polygon'] = json_encode(explode('-', $start_polygon));
        unset($this->data['driver']);
        try {
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $this->validateKuaicheAreaRepeated($kuaiche_type, $start_area_code, $start_city_code, $id);
            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if ($startNameR->ret) {
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $this->startTrans();
            $r = $this->save('LineFast');
            $flag = true;
            $driver = json_decode($driver, true);
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "修改快车成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "修改失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        $this->doAddLog("修改快车成功");
        return $this->output($r);
    }

    /**
     * 删除快车
     * @param int $id 线路ID
     */
    public function doDeleteLineFast($id)
    {
        $r = $this->save('LineFast', array('id' => $id, 'is_del' => 1, 'mchid' => $this->mchid));
        $this->doAddLog("删除快车");
        return $this->output($r);
    }

    /**
     * 设置快车价格
     * @param $id
     * @param $region_start_time
     * @param $region_end_time
     * @param $base_fee
     * @param $duration_fee
     * @param $mileage_fee
     * @param $base_distance
     * @param $longdistance_fee
     * @param $base_time
     * @param $wait_timeout_fee
     * @return null|resultModel
     */
    public function doSetLineFastRegionTime(/*$line_fast_id, $region_start_time, $region_end_time, $base_fee, $duration_fee, $mileage_fee, $base_distance, $longdistance_fee,  $base_time, $wait_timeout_fee*/)
    {
        $line_fast_id = $_POST['line_fast_id'];
        $region_start_time = $_POST['region_start_time'];
        $region_end_time = $_POST['region_end_time'];
        $base_fee = $_POST['base_fee'];
        $duration_fee = $_POST['duration_fee'];
        $mileage_fee = $_POST['mileage_fee'];
        $base_distance = $_POST['base_distance'];
        $longdistance_fee = $_POST['longdistance_fee'];
        $base_time = $_POST['base_time'];
        $wait_timeout_fee = $_POST['wait_timeout_fee'];
        $pricing_vehicle = $_POST['pricing_vehicle'] ?: null;
        $mchid = $this->mchid;


        //数据校验
        $count = count($region_start_time);
        for ($i = 0; $i < $count; $i++) {
            if ($i == 0) {
                if ($region_start_time[$i] != '00:00') { //00:00起止
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }
            if ($count == $i + 1) {
                if ($region_end_time[$count - 1] != '23:59') { //23:59起止
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }

            $region_start_time[$i] .= ":00";
            $region_end_time[$i] .= ":00";

            if (convertToSecond($region_start_time[$i]) > convertToSecond($region_end_time[$i])) {
                return $this->output(new ResultModel(false, "设置失败，区间开始时间必须小于结束时间"));
            }

            if ($count > 1 && $i > 0) {
                if ((convertToSecond($region_start_time[$i])) != (convertToSecond($region_end_time[$i - 1]) + 60)) {
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }
        }

        $this->startTrans();
        $delWhere['mchid'] = $mchid;
        $delWhere['line_fast_id'] = $line_fast_id;
        $delWhere['pricing'] = $pricing_vehicle;
        $delBaseFee = M('LineFastBaseFee')->where($delWhere)->delete();
        $delDurationFee = M('LineFastDurationFee')->where($delWhere)->delete();
        $delLongdistanceFee = M('LineFastLongdistanceFee')->where($delWhere)->delete();
        $delMileageFee = M('LineFastMileageFee')->where($delWhere)->delete();
        $delWaitTimeoutFee = M('LineFastWaitTimeoutFee')->where($delWhere)->delete();

        for ($i = 0; $i < $count; $i++) {
            $data['mchid'] = $mchid;
            $data['pricing'] = $pricing_vehicle;
            $data['line_fast_id'] = $line_fast_id;
            $data['region_start_time'] = $region_start_time[$i];
            $data['region_end_time'] = $region_end_time[$i];
            $data['price'] = $base_fee[$i];
            if (!M('LineFastBaseFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $duration_fee[$i];
            if (!M('LineFastDurationFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $mileage_fee[$i];
            if (!M('LineFastMileageFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $longdistance_fee[$i];
            $data['base_distance'] = $base_distance[$i];
            if (!M('LineFastLongdistanceFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $wait_timeout_fee[$i];
            $data['base_time'] = $base_time[$i];
            if (!M('LineFastWaitTimeoutFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }
        }

        $this->commitTrans();
        $this->doAddLog("设置快车价格");
        return $this->output(new ResultModel(true, "设置成功"));
    }

    public function getKuaicheFee()
    {
        $where['line_fast_id'] = $_POST['line_fast_id'];
        $where['pricing'] = $_POST['pricing_vehicle'];
        $baseFee = M('LineFastBaseFee')->where($where)->select();
        $durationFee = M('LineFastDurationFee')->where($where)->select();
        $longdistanceFee = M('LineFastLongdistanceFee')->where($where)->select();
        $mileageFee = M('LineFastMileageFee')->where($where)->select();
        $waitTimeoutFee = M('LineFastWaitTimeoutFee')->where($where)->select();
        $data = [];
        for ($i = 0; $i < count($baseFee); $i++) {
            $item['region_start_time'] = substr($baseFee[$i]['region_start_time'], 0, 5);
            $item['region_end_time'] = substr($baseFee[$i]['region_end_time'], 0, 5);
            $item['base_fee'] = $baseFee[$i]['price'];
            $item['duration_fee'] = $durationFee[$i]['price'];
            $item['mileage_fee'] = $mileageFee[$i]['price'];
            $item['base_distance'] = $longdistanceFee[$i]['base_distance'];
            $item['longdistance_fee'] = $longdistanceFee[$i]['price'];
            $item['base_time'] = $waitTimeoutFee[$i]['base_time'];
            $item['wait_timeout_fee'] = $waitTimeoutFee[$i]['price'];
            $data[] = $item;
        }
        $ret = new ResultModel(true);
        $ret->count = 0;
        $ret->data = $data;
        return $this->output($ret);
    }

    /**
     * 添加出租车
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLineTaxi($mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code = null, $end_area_code = null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_11, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {
        try {
            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $lineTaxiR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $start_city_code, 'is_del' => 0), 'LineTaxi');
            if ($lineTaxiR->ret) {
                return $this->output(new ResultModel(false, "一个市区只允许有一个价目"));
            }
            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if ($startNameR->ret) {
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $flag = true;
            $this->startTrans();
            $r = $this->add('LineTaxi');
            $driver = json_decode($driver, true);

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加出租车成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加出租车成功");
        return $this->output($r);
    }

    /**
     * 更新出租车
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineTaxi($id, $mchid, $branchid, $start_name = null, $end_name = null, $start_province_code, $start_city_code, $start_area_code = null, $end_area_code = null, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary = null, $driver = null, $type = \CommonDefine::ORDER_TYPE_11, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $startCityR = $this->find(array('address_id' => $start_city_code), 'GdRegion');
        $this->data['start_name'] = $startCityR->ret ? $startCityR->data['name'] : "";
        $this->data['end_name'] = $startCityR->ret ? $startCityR->data['name'] : "";
        unset($this->data['driver']);
        try {
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }
            $lineTaxiR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $start_city_code, 'is_del' => 0, 'id' => array('neq', $id)), 'LineTaxi');
            if ($lineTaxiR->ret) {
                return $this->output(new ResultModel(false, "一个市区只允许有一个价目"));
            }

            $startNameR = $this->getCityNameByAddressCode($start_city_code);
            if ($startNameR->ret) {
                $this->data['start_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
                $this->data['end_name'] = $this->removeCityOrCountyLastName($startNameR->data['city_name']);
            }

            $this->startTrans();
            $r = $this->save('LineTaxi');
            $flag = true;
            $driver = json_decode($driver, true);
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "修改出租车成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "修改失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        $this->doAddLog("修改出租车成功");
        return $this->output($r);
    }

    /**
     * 删除出租车
     * @param int $id 线路ID
     */
    public function doDeleteLineTaxi($id)
    {
        $r = $this->save('LineTaxi', array('id' => $id, 'is_del' => 1, 'mchid' => $this->mchid));
        $this->doAddLog("删除出租车");
        return $this->output($r);
    }

    /**
     * 设置出租车价格
     * @param $id
     * @param $region_start_time
     * @param $region_end_time
     * @param $base_fee
     * @param $duration_fee
     * @param $mileage_fee
     * @param $base_distance
     * @param $longdistance_fee
     * @param $base_time
     * @param $wait_timeout_fee
     * @return null|resultModel
     */
    public function doSetLineTaxiRegionTime()
    {
        $line_taxi_id = $_POST['line_taxi_id'];
        $region_start_time = $_POST['region_start_time'];
        $region_end_time = $_POST['region_end_time'];
        $base_fee = $_POST['base_fee'];
        $duration_fee = $_POST['duration_fee'];
        $mileage_fee = $_POST['mileage_fee'];
        $include_distance = $_POST['include_distance'];
        $base_distance = $_POST['base_distance'];
        $longdistance_fee = $_POST['longdistance_fee'];
        $base_time = $_POST['base_time'];
        $wait_timeout_fee = $_POST['wait_timeout_fee'];

        //数据校验
        $count = count($region_start_time);
        for ($i = 0; $i < $count; $i++) {
            if ($i == 0) {
                if ($region_start_time[$i] != '00:00') { //00:00起止
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }
            if ($count == $i + 1) {
                if ($region_end_time[$count - 1] != '23:59') { //23:59起止
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }

            $region_start_time[$i] .= ":00";
            $region_end_time[$i] .= ":00";

            if (convertToSecond($region_start_time[$i]) > convertToSecond($region_end_time[$i])) {
                return $this->output(new ResultModel(false, "设置失败，区间开始时间必须小于结束时间"));
            }

            if ($count > 1 && $i > 0) {
                if ((convertToSecond($region_start_time[$i])) != (convertToSecond($region_end_time[$i - 1]) + 60)) {
                    return $this->output(new ResultModel(false, "设置失败，时间区间从00:00 至 23:59连贯"));
                }
            }
        }

        $this->startTrans();
        $delWhere['line_taxi_id'] = $line_taxi_id;
        $delBaseFee = M('LineTaxiBaseFee')->where($delWhere)->delete();
        $delDurationFee = M('LineTaxiDurationFee')->where($delWhere)->delete();
        $delLongdistanceFee = M('LineTaxiLongdistanceFee')->where($delWhere)->delete();
        $delMileageFee = M('LineTaxiMileageFee')->where($delWhere)->delete();
        $delWaitTimeoutFee = M('LineTaxiWaitTimeoutFee')->where($delWhere)->delete();

        for ($i = 0; $i < $count; $i++) {
            $data['line_taxi_id'] = $line_taxi_id;
            $data['region_start_time'] = $region_start_time[$i];
            $data['region_end_time'] = $region_end_time[$i];
            $data['price'] = $base_fee[$i];
            $data['base_distance'] = $include_distance[$i];
            if (!M('LineTaxiBaseFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $duration_fee[$i];
            if (!M('LineTaxiDurationFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $mileage_fee[$i];
            if (!M('LineTaxiMileageFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $longdistance_fee[$i];
            $data['base_distance'] = $base_distance[$i];
            if (!M('LineTaxiLongdistanceFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }

            $data['price'] = $wait_timeout_fee[$i];
            $data['base_time'] = $base_time[$i];
            if (!M('LineTaxiWaitTimeoutFee')->add($data)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "设置失败"));
            }
        }

        $this->commitTrans();
        $this->doAddLog("设置出租车价格");
        return $this->output(new ResultModel(true, "设置成功"));
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param string $lc 类型
     * @param string $driver 司机列表
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLinePhone($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $phone_line_remarks = null, $price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $phone_line_verify_start_time = null, $phone_line_verify_end_time = null, $is_phone_line_verify)
    {
        $this->data['channel_price'] = $price;
        $this->data['is_phone_line'] = \CommonDefine::LINE_PHONE_TYYE_1;
        unset($this->data['lc']);
        try {
            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $flag = true;
            $this->startTrans();
            $r = $this->add('Line');
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            //拼车类型进行关联
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $r->data;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            //对司机列表进行关联
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['driver_id'] = $item;
                    $params['line_id'] = $r->data;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
                if (!$flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            }

            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        $this->doAddLog("添加电话线路");
        return $this->output($r);
    }

    /**
     * 添加线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLinePhone($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $phone_line_remarks = null, $price, $lc, $driver = null, $type = \CommonDefine::ORDER_TYPE_1, $rate = 0, $status = 1, $start_lng, $start_lat, $end_lng, $end_lat, $area_radius_start, $area_lng_start, $area_lat_start, $area_radius_end, $area_lng_end, $area_lat_end, $phone_line_verify_start_time = null, $phone_line_verify_end_time = null, $is_phone_line_verify)
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $this->data['channel_price'] = $price;
        unset($this->data['lc'], $this->data['driver']);
        try {
            if (empty($phone_line_verify_start_time)) {
                unset($this->data['phone_line_verify_start_time']);
            }
            if (empty($phone_line_verify_end_time)) {
                unset($this->data['phone_line_verify_end_time']);
            }
            $center_start_latlngR = $this->getAddressCenterLatLng($start_province_code, $start_city_code, $start_area_code);
            if ($center_start_latlngR->ret) {
                $this->data['center_start_latlng'] = $center_start_latlngR->data['location'];
                $center_start_latlng = explode(',', $center_start_latlngR->data['location']);
                $this->data['center_start_lng'] = $center_start_latlng[0];
                $this->data['center_start_lat'] = $center_start_latlng[1];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($end_province_code, $end_city_code, $end_area_code);
            if ($center_end_latlngR->ret) {
                $this->data['center_end_latlng'] = $center_end_latlngR->data['location'];
            }

            $this->startTrans();
            $r = $this->save('Line');
            $flag = true;
            $lc = json_decode($lc, true);
            $driver = json_decode($driver, true);
            M("line_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id))->delete();
            if (!empty($lc)) {
                foreach ($lc as $val) {
                    $param['line_id'] = $id;
                    $params['type'] = $type;
                    $param['line_category_id'] = $val;
                    $param['mchid'] = $this->state->user_id;
                    if (!$this->add("LineFkLineCategory", $param)) {
                        $flag = false;
                    }
                }
            }
            //司机关联
            M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
            if (!empty($driver)) {
                foreach ($driver as $item) {
                    $params['type'] = $type;
                    $params['line_id'] = $id;
                    $params['driver_id'] = $item;
                    $params['mchid'] = $this->state->user_id;
                    if (!$this->add("DriverLine", $params)->ret) {
                        $flag = false;
                    }
                }
            }
            if ($flag) {
                $this->commitTrans();
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }


        $this->doAddLog("修改电话线路");
        return $this->output($r);
    }

    /**
     * 删除电话叫车线路
     * @param int $id 线路ID
     */
    public function doDeleteLinePhone($id)
    {
        //删除相关订单
        $r = $this->save('Line', array('id' => $id, 'is_del' => 1));
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 删除包车
     * @param int $id 包车id
     */
    public function doDeleteLineChartered($id)
    {
        //        var_dump($id);die;
        //删除相关订单
        $r = $this->save('LineChartered', array('id' => $id, 'is_del' => 1));

        if ($r->ret) {
            M('line_chartered_price')->where(array('line_chartered_price_id' => $r->data['id'], 'is_del' => 0))->save(array('is_del' => 1));
        }
        $this->doAddLog("删除线路");
        return $this->output($r);
    }

    /**
     * 添加包车线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param mixed $prices 线路价格
     * @param json $channel_prices 渠道价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doCreateLineChartered($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $prices, $lc, $rate = 0, $status = 1, $type = \CommonDefine::ORDER_TYPE_2, $driver = null, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {
        $price = json_decode($prices, true);

        if (count($price) < 2) {
            return $this->output(new ResultModel(false, "请填写两个价格"));
        }
        $gdObj = new GdRegionModel();
        $adcode = $start_area_code;
        if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
            $adcode = $start_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
        if (isset($infos['districts'][0]['center'])) {
            $data['center_start_latlng'] = $infos['districts'][0]['center'];
        }
        $adcode = $end_area_code;
        if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
            $adcode = $end_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
        if (isset($infos['districts'][0]['center'])) {
            $data['center_end_latlng'] = $infos['districts'][0]['center'];
        }
        $data['mchid'] = $mchid;
        $data['branchid'] = $branchid;
        $data['start_name'] = $start_name;
        $data['end_name'] = $end_name;

        $data['start_province_code'] = $start_province_code;
        $data['start_city_code'] = $start_city_code;
        $data['start_area_code'] = $start_area_code;
        $data['end_area_code'] = $end_area_code;
        $data['end_city_code'] = $end_city_code;
        $data['end_province_code'] = $end_province_code;

        $data['start_address_code'] = $start_address_code;
        $data['end_address_code'] = $end_address_code;
        $data['summary'] = $summary;
        $data['rate'] = $rate;
        $data['is_del'] = 0;
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $data['business_time_type'] = $business_time_type;
        $data['set_order_time'] = $set_order_time;
        $lc = json_decode($lc, true);
        $driver = json_decode($driver, true);
        try {
            $flag = false;
            $this->startTrans();
            $r = $this->add('LineChartered', $data);
            if ($r->ret) {
                foreach ($price as $key => $val) {
                    $param[$key] = array(
                        'line_chartered_id' => $r->data,
                        'is_del' => 0,
                        'car_type_id' => $val['id'],
                        'price' => $val['price'],
                        'channel_price' => $val['channel_price'],
                        'create_time' => date("Y-m-d H:i:s"),
                        'update_time' => date("Y-m-d H:i:s"),
                    );
                    if (!$this->floatgtre($val['price'], $val['channel_price'])) {
                        return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                    }
                    $lineCharteredPriceR = $this->add('LineCharteredPrice', $param[$key]);
                    unset($param[$key]);
                    if (!$lineCharteredPriceR->ret) {
                        $flag = true; //添加失败
                    }
                }
                if (!empty($lc)) {
                    foreach ($lc as $val) {
                        ;
                        if (!$this->add("LineCharteredFkLineCategory", array('line_id' => $r->data, 'line_category_id' => $val, 'mchid' => $this->state->user_id, 'type' => \CommonDefine::ORDER_TYPE_2))) {
                            $flag = true;
                        }
                    }
                }
                //司机关联
                if (!empty($driver)) {
                    foreach ($driver as $item) {
                        $params['type'] = $type;
                        $params['line_id'] = $r->data;
                        $params['driver_id'] = $item;
                        $params['mchid'] = $this->state->user_id;
                        if (!$this->add("DriverLine", $params)->ret) {
                            $flag = true;
                        }
                    }
                }
                if ($flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                } else {
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }

        $this->doAddLog("添加线路");
        return $this->output($r);
    }

    /**
     * 修改包车线路
     * @param int $line_id 包车id
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param mixed $prices 线路价格
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineChartered($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $summary, $prices, $lc, $rate = 0, $status = 1, $driver = null, $type = \CommonDefine::ORDER_TYPE_2, $start_appointment_time, $end_appointment_time, $business_time_type, $set_order_time = 0)
    {

        $price = json_decode($prices, true);

        if (count($price) < 2) {
            return $this->output(new ResultModel(false, "请填写两个价格"));
        }

        if (!$id) {
            return $this->output(new ResultModel(false, "数据异常!"));
        }
        $gdObj = new GdRegionModel();
        $adcode = $start_area_code;
        if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
            $adcode = $start_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
        if (isset($infos['districts'][0]['center'])) {
            $data['center_start_latlng'] = $infos['districts'][0]['center'];
        }
        $adcode = $end_area_code;
        if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
            $adcode = $end_city_code;
        }
        $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
        if (isset($infos['districts'][0]['center'])) {
            $data['center_end_latlng'] = $infos['districts'][0]['center'];
        }
        $data['id'] = $id;
        $data['mchid'] = $mchid;
        $data['branchid'] = $branchid;
        $data['start_name'] = $start_name;
        $data['end_name'] = $end_name;

        //        var start_province_code = pcode;
        //        var start_city_code = ccode;
        //        var start_area_code = qcode;
        //        var end_area_code = epcode;
        //        var end_city_code = eccode;
        //        var end_province_code = eqcode;
        $data['start_province_code'] = $start_province_code;
        $data['start_city_code'] = $start_city_code;
        $data['start_area_code'] = $start_area_code;
        $data['end_area_code'] = $end_area_code;
        $data['end_city_code'] = $end_city_code;
        $data['end_province_code'] = $end_province_code;

        $data['start_address_code'] = $start_address_code;
        $data['end_address_code'] = $end_address_code;
        $data['summary'] = $summary;
        $data['rate'] = $rate;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $data['business_time_type'] = $business_time_type;
        $data['set_order_time'] = $set_order_time;
        $lc = json_decode($lc, true);


        $driver = json_decode($driver, true);

        try {
            $flag = false;
            $this->startTrans();
            $r = $this->save('LineChartered', $data);
            unset($data);
            if ($r->ret) {
                foreach ($price as $key => $val) {
                    if (!$this->floatgtre($val['price'], $val['channel_price'])) {
                        return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                    }

                    $param = array(
                        'line_chartered_id' => $id,
                        'car_type_id' => $val['id'],
                    );
                    $LineCharteredPriceR = $this->find($param, 'LineCharteredPrice');
                    if ($LineCharteredPriceR->ret) {
                        $temp['price'] = $val['price'];
                        $temp['channel_price'] = $val['channel_price'];
                        $temp['update_time'] = date("Y-m-d H:i:s");
                        $lcp = M('line_chartered_price')->where($param)->save(array_merge($param, $temp));
                    } else {
                        $param['price'] = $val['price'];
                        $param['channel_price'] = $val['channel_price'];
                        $param['create_time'] = date("Y-m-d H:i:s");
                        $param['is_del'] = 0;
                        $param['update_time'] = date("Y-m-d H:i:s");
                        $lcp = M('line_chartered_price')->add($param);
                    }
                    unset($param);
                    if (!$lcp) {
                        $flag = true; //添加失败
                    }
                }
                //                var_dump(123123);die;
                M("line_chartered_fk_line_category")->where(array('line_id' => $id, 'mchid' => $this->state->user_id, 'type' => \CommonDefine::ORDER_TYPE_2))->delete();

                $whg['line_id'] = $id;
                $whg['mchid']   = $mchid;
                M('LineCharteredFkLineCategory')
                    ->where('line_category_id', 'not in', $lc)
                    ->where($whg)
                    ->delete();

                if (!empty($lc)) {

                    foreach ($lc as $val) {
                        $whg['line_category_id'] = $val;
                        if (!M('LineCharteredFkLineCategory')->where($whg)->find()) {
                            if (!$this->add("LineCharteredFkLineCategory", array('line_id' => $id, 'line_category_id' => $val, 'mchid' => $this->state->user_id))->ret) {
                                $flag = true;
                            }
                        }
                    }
                }
                //司机关联
                M("driver_line")->where(array('line_id' => $id, 'type' => $type, 'mchid' => $this->state->user_id))->delete();
                if (!empty($driver)) {
                    foreach ($driver as $item) {
                        $params['type'] = $type;
                        $params['line_id'] = $id;
                        $params['driver_id'] = $item;
                        $params['mchid'] = $this->state->user_id;
                        if (!$this->add("DriverLine", $params)->ret) {
                            $flag = true;
                        }
                    }
                }
                if ($flag) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                } else {
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }

        $this->doAddLog("添加线路");
        return $this->output($r);
    }


    /**
     * 获取包车线路列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function getLineChareteredList($fields = null, $branchid = null, $mchid, $page = 1, $size = 10, $search_branchid = 0, $search_line_id = 0)
    {
        $where['mchid'] = $mchid;
        if ($search_branchid) {
            $where['branchid'] = $search_branchid;
        } elseif (!empty($branchid) && $branchid) {
            $where['branchid'] = $branchid;
        }
        if ($search_line_id) {
            $where['id'] = $search_line_id;
        }
        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'LineChartered');
        if ($r->ret) {
            foreach ($r->data as $k => $line) {
                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $lineFkR = $this->select(array('mchid' => $this->state->user_id, 'line_id' => $line['id']), null, null, null, 'LineCharteredFkLineCategory', 'line_category_id');
                if ($lineFkR->ret) {
                    $temp = array();
                    foreach ($lineFkR->data as $key => $item) {
                        $temp[$key] = $this->find(array('id' => $item['line_category_id'], 'is_del' => 0), 'LineCategory', 'title')->data['title']; // 线路匹配类型
                    }
                    $r->data[$k]['line_type'] = implode(",", $temp);
                } else {
                    $r->data[$k]['line_type'] = "-";
                }

                $startAddress = "[" . $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'] . "]";
                $r->data[$k]['start'] = $startAddress . $line['start_name'];
                $endAddress = "[" . $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'] . "]";
                $r->data[$k]['end'] = $endAddress . $line['end_name'];
                $r->data[$k]['route'] = "起：" . $r->data[$k]['start'] . "<br/>" . "终：" . $r->data[$k]['end'];
                $lineCharteredPrice = M()->table('cp_line_chartered_price lcp')
                    ->JOIN("LEFT JOIN cp_car_type ct ON lcp.car_type_id =ct.car_type_id")->field('ct.num,lcp.price,lcp.channel_price, ct.name')->where(array('is_del' => 0, 'line_chartered_id' => $line['id']))->select();
                $tep = '';
                foreach ($lineCharteredPrice as $val) {
                    if (!$tep) {
                        $tep = $val['name'] . "[" . $val['num'] . "座](" . $val['price'] . ")";
                        $ctep = $val['name'] . "[" . $val['num'] . "座](" . $val['channel_price'] . ")";
                    } else {
                        $tep .= "<br>" . $val['name'] . "[" . $val['num'] . "座](" . $val['price'] . ")";
                        $tep .= "<br>" . $val['name'] . "[" . $val['num'] . "座](" . $val['channel_price'] . ")";
                    }
                }
                $r->data[$k]['price'] = $tep;
                $r->data[$k]['channel_price'] = $ctep;
                unset($tep);
            }
        }
        foreach ($r->data as $key => $item) {
            if (empty($item['branchname'])) {
                $r->data[$key]['branchname'] = "";
            }
        }

        return $this->output($r);
    }


    /**
     * 定制班线车列表
     *
     * @param stirng $fields
     * @param integer $mchid
     * @param integer $branchid
     * @param integer $page
     * @param integer $size
     * @param string $line_class_train_no
     * @param string $status
     * @param string $start_name
     * @param string $end_name
     * @param integer $start_time_type
     * @param integer $search_branchid
     * @return void
     */
    public function getLineClassList($fields = '', $mchid, $branchid = 0, $page = 1, $size = 10, $line_class_train_no = '', $status = '', $start_name = '', $end_name = '', $line_category = '', $start_time_type = 0, $search_branchid = 0, $support_points_deduction = '')
    {
        $where['mchid'] = $mchid;
        $where['is_del'] = 0;
        if (!empty($line_class_train_no)) {
            $where['id'] = $line_class_train_no;
        }
        if ($status !== '' && $status != 3) {
            $where['status'] = $status;
        }

        if ($support_points_deduction !== '' && $support_points_deduction != 3) {
            $where['support_points_deduction'] = $support_points_deduction;
        }

        if (!empty($start_name)) {
            $where['start_name'] = ['like', '%' . $start_name . '%'];
        }
        if (!empty($end_name)) {
            $where['end_name'] = ['like', '%' . $end_name . '%'];
        }
        if (!empty($line_category)) {
            // 筛选：场景分类
            $where['id'] = ['in' => []];
            $lcfk = M()->table('cp_line_class_fk_line_category lcfk')
                ->JOIN("LEFT JOIN cp_line_category lc ON lcfk.line_category_id = lc.id")
                ->field('lcfk.line_class_id')
                ->where(array('lc.is_del' => 0, 'lc.id' => $line_category))
                ->select();
            if ($lcfk) {
                $where['id'] = ['in', array_column($lcfk, 'line_class_id')];
            }
        }
        if ($start_time_type) {
            $where['start_time_type'] = $start_time_type;
        }
        if ($search_branchid) {
            $where['branchid'] = $search_branchid;
        }
        if (!empty($branchid) && $branchid) {
            $where['branchid'] = $branchid;
        }

        $r = $this->select($where, $page, $size, 'id desc', 'LineClass');
        if ($r->ret) {
            // 查询线路场景分类(避免N+1)
            $ids_line = array_column((array)$r->data, 'id');
            $lineFkR = M()->table('cp_line_class_fk_line_category lc')
                ->JOIN("LEFT JOIN cp_line_category lcfk ON lc.line_category_id = lcfk.id")
                ->field('lcfk.title,lc.line_class_id')
                ->where(array('lcfk.is_del' => 0, 'lc.line_class_id' => ['in', $ids_line]))
                ->select();
            $mapping = [];
            foreach ($lineFkR as $item) {
                $mapping[$item['line_class_id']][] = $item['title'];
            }
            foreach ($r->data as $k => $line) {
                // 线路场景分类
                $r->data[$k]['line_type'] = implode(",", $mapping[$line['id']] ?: []);

                $r->data[$k]['is_start_ferry'] = $r->data[$k]['is_end_ferry'] = 0;
                $r->data[$k]['is_start_ferry_pricing'] = $r->data[$k]['is_end_ferry_pricing'] = [];

                if ($this->queryMerchantFerryService() == 1) {
                    # 任意上车点支持设置摆渡车
                    if ($line['start_address_type'] == 2) {
                        # 定制班线车是否存在快车-摆渡车
                        $r->data[$k]['is_start_ferry'] = $this->queryFerryByArea($line['start_province_code'], $line['start_city_code'], $line['start_area_code']);
                    }

                    if ($line['end_address_type'] == 2) {
                        $r->data[$k]['is_end_ferry'] = $this->queryFerryByArea($line['end_province_code'], $line['end_city_code'], $line['end_area_code']);
                    }

                    # 定制班线车存在快车-摆渡车是否配置计价规则
                    if ($r->data[$k]['is_start_ferry'] > 0) {
                        $r->data[$k]['is_start_ferry_pricing'] = $this->queryFerryPricing($r->data[$k]['is_start_ferry']);
                    }
                    if ($r->data[$k]['is_end_ferry'] > 0) {
                        $r->data[$k]['is_end_ferry_pricing'] = $this->queryFerryPricing($r->data[$k]['is_end_ferry']);
                    }
                } else {
                    $r->data[$k]['is_start_ferry'] = $r->data[$k]['is_end_ferry'] = -1;
                }


                $branchR = $this->find(array("admin_id" => $line['branchid']), 'admin');
                if ($branchR->ret) {
                    $r->data[$k]['branchname'] = $branchR->data['mchname'];
                }

                $branchDriversR = M()->table('cp_driver d')
                    ->join('LEFT JOIN cp_driver_group_attribute_values g ON d.driver_id = g.driver_id')
                    ->field('d.driver_id, d.name, d.total_seating,d.car_tail_number,g.name AS group_name')
                    ->where(['d.branchid' => $line['branchid'], 'd.is_freeze' => 0, 'd.mchid' => $mchid])
                    ->select();
                if ($branchDriversR) {
                    // 格式化结果，包含 group_name 信息
                    $formattedDrivers = [];
                    foreach ($branchDriversR as $driver) {
                        $formattedDrivers[] = [
                            'driver_id' => $driver['driver_id'],
                            'name' => $driver['name'],
                            'car_tail_number' => $driver['car_tail_number'],
                            'total_seating' => $driver['total_seating'],
                            'group_name' => $driver['group_name'] ? trim($driver['group_name']) : '未分组', // 如果没有分组，显示"未分组"
                        ];
                    }
                    $r->data[$k]['branch_drivers'] = json_encode($formattedDrivers, JSON_UNESCAPED_UNICODE);
                } else {
                    $r->data[$k]['branch_drivers'] = json_encode([]);
                }

                $curtime = date("Y-m-d H:i:s", time());
                $onWayTrainCountR = $this->count(array('mchid' => $mchid, 'line_class_id' => $line['id'], 'line', 'is_del' => \CommonDefine::IS_DEL_0, 'start_time' => array('gt', $curtime)), 'LineClassTrain');
                $overTrainCountR = $this->count(array('mchid' => $mchid, 'line_class_id' => $line['id'], 'line', 'is_del' => \CommonDefine::IS_DEL_0, 'start_time' => array('elt', $curtime)), 'LineClassTrain');

                $startAddress = $this->checkingGdParentAddressCode($line['start_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['start_address_code'])->data['address'];
                $r->data[$k]['start'] = $startAddress;
                $r->data[$k]['start_name'] = $line['start_name'];

                $endAddress = $this->checkingGdParentAddressCode($line['end_address_code'])->data['address'] . "·" . $this->checkingGdAddressCode($line['end_address_code'])->data['address'];
                $r->data[$k]['end'] = $endAddress;
                $r->data[$k]['end_name'] = $line['end_name'];

                // 停售时间
                if ($line['stop_sale_minutes'] > 0) {
                    $r->data[$k]['stop_description'] = '发车前' . $line['stop_sale_minutes'] . '分钟停止售票';
                } else {
                    $r->data[$k]['stop_description'] = '发车前' . $line['stop_sell_number'] . '天' . $line['stop_sell_time'] . '时刻后停止售票';
                }

                $r->data[$k]['on_way_train_count'] = $onWayTrainCountR->ret ? $onWayTrainCountR->data : 0;
                $r->data[$k]['over_train_count'] = $overTrainCountR->ret ? $overTrainCountR->data : 0;
                //获取上车点
                if ($line['start_address_type'] == 2) {
                    $r->data[$k]['start_point'] = "<p>&nbsp;&nbsp;&nbsp;&nbsp;任意地点(电子围栏内)</p>";
                    if ($r->data[$k]['is_start_ferry'] == \CommonDefine::IS_START_FERRY_1) {
                        $pointMode = new LineClassPointModel();
                        $lineClassPointInfo = $pointMode->where(['line_class_id' => $line['id'], 'is_return' => 0, 'type' => 1])->field('alias')->limit(3)->select();
                        $r->data[$k]['start_point'] .= "<p style='color: #808080'>&nbsp;&nbsp;&nbsp;&nbsp;出发地车场：</p>";
                        foreach ($lineClassPointInfo as $key => $value) {
                            $r->data[$k]['start_point'] .= "<p style='color: #808080'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                        }
                        if (count($lineClassPointInfo) > 3) {
                            $r->data[$k]['start_point'] .= "<a rel=\"{$line['id']}\" address='{$r->data[$k]['start']}'  onclick='sss($(this))' list_type=\"1\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                        }
                    }
                } else {
                    $pointMode = new LineClassPointModel();
                    $lineClassPointInfo = $pointMode->where(['line_class_id' => $line['id'], 'is_return' => 0, 'type' => 1])->field('alias')->limit(3)->select();
                    $r->data[$k]['start_point'] = "";
                    foreach ($lineClassPointInfo as $key => $value) {
                        $r->data[$k]['start_point'] .= "<p>&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                    }
                    if (count($lineClassPointInfo) > 3) {
                        $r->data[$k]['start_point'] .= "<a rel=\"{$line['id']}\" address='{$r->data[$k]['start']}'  onclick='sss($(this))' list_type=\"1\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                    }
                }
                //获取下车点
                if ($line['end_address_type'] == 2) {
                    $r->data[$k]['end_point'] = "<p>&nbsp;&nbsp;&nbsp;&nbsp;任意地点(电子围栏内)</p>";
                    if ($r->data[$k]['is_end_ferry'] == \CommonDefine::IS_END_FERRY_1) {
                        $pointMode = new LineClassPointModel();
                        $lineClassPointInfo = $pointMode->where(['line_class_id' => $line['id'], 'is_return' => 0, 'type' => 2])->field('alias')->limit(3)->select();
                        $r->data[$k]['end_point'] .= "<p style='color: #808080'>&nbsp;&nbsp;&nbsp;&nbsp;目的地车场：</p>";
                        foreach ($lineClassPointInfo as $key => $value) {
                            $r->data[$k]['end_point'] .= "<p style='color: #808080'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                        }
                        if (count($lineClassPointInfo) > 3) {
                            $r->data[$k]['end_point'] .= "<a rel=\"{$line['id']}\" address='{$r->data[$k]['end']}' onclick='sss($(this))' list_type=\"2\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                        }
                    }
                } else {
                    $pointMode = new LineClassPointModel();
                    $lineClassPointInfo = $pointMode->where(['line_class_id' => $line['id'], 'is_return' => 0, 'type' => 2])->field('alias')->limit(3)->select();
                    $r->data[$k]['end_point'] = "";
                    foreach ($lineClassPointInfo as $key => $value) {
                        $r->data[$k]['end_point'] .= "<p>&nbsp;&nbsp;&nbsp;&nbsp;{$value['alias']}</p>";
                    }
                    if (count($lineClassPointInfo) > 3) {
                        $r->data[$k]['end_point'] .= "<a rel=\"{$line['id']}\" address='{$r->data[$k]['end']}' onclick='sss($(this))' list_type=\"2\" class=\"class_list_more\" href=\"javascript:\">&nbsp;&nbsp;&nbsp;&nbsp;查看更多</a>";
                    }
                }
                $r->data[$k]['is_seat_selection_info'] = "不支持";
                $r->data[$k]['starting_time'] = json_decode($line['starting_time']);
                $r->data[$k]['starting_interval_time'] = json_decode($line['starting_interval_time']);

                if ($line['is_seat_selection'] == 1) {
                    $r->data[$k]['is_seat_selection_info'] = "<p>支持</p><p>当前班线" . $r->data[$k]['car_seats'] . "座</p>";
                    if ($r->data[$k]['seat_layout']) {
                        $r->data[$k]['is_seat_selection_info'] .= "<p>座位布局：" . $r->data[$k]['seat_layout'] . "</p>";
                    }
                    $seatPriceArr = json_decode($line['seat_price'], true);
                    foreach ($seatPriceArr as $kk => $seatPrice) {
                        if ($kk == 0) {
                            $r->data[$k]['price_info'] = '';
                        } else {
                            $r->data[$k]['price_info'] .= sprintf('<p>%s：¥%s元</p>', $seatPrice['name'], $seatPrice['price']);
                        }
                    }
                } else {
                    $r->data[$k]['price_info'] = sprintf('<p>销售价格：¥%s元</p><p>渠道价格：¥%s元</p>', $line['price'], $line['channel_price']);
                }

                // 检查是否有排班计划
                $hasSchedulePlan = $this->hasSchedulePlan($line['id']);
                if ($hasSchedulePlan) {
                    $r->data[$k]['schedule_plan'] = '<a href="javascript:void(0);" style="color:red;" onclick="alert(\'为了优化您的排班体验并提升智能排班效率，我们将于 2024年12月13日 00:00 起进行系统升级。升级后，线路需要重新生成排班一次（即重新"排班"一次）以确保正常运行。请注意，2024年12月13日 00:00 之前设置的自动排班任务将自动失效，不再循环执行。为避免影响乘客下单，请及时进行处理。如有疑问，请联系您的客户经理或拨打客服热线 028-62711128。感谢您的理解与支持！\')">❗️未设置排班计划</a>';
                } else {
                    $r->data[$k]['schedule_plan'] = '';
                }
            }
        }
        return $this->output($r);
    }

    /**
     * 检查班次是否设置了排班计划
     *
     * @param int $lineClassId 班次 ID
     * @return bool
     */
    public function hasSchedulePlan($lineClassId)
    {
        // 检查排班计划表是否存在对应班次的计划
        $where = [
            'line_class_id' => $lineClassId,
            'is_active' => 1 // 仅检查有效的排班计划
        ];
        $countR = $this->count($where, 'LineClassSchedulePlan'); // 查询排班计划表

        return !$countR->ret;
    }


    /**
     * 定制班线车省市区是否存在快车-摆渡车
     *
     * @param string $province_code
     * @param string $city_code
     * @param string $area_code
     * @return integer
     */
    public function queryFerryByArea($province_code, $city_code, $area_code)
    {
        $lineFastId = 0;
        $lineFast = $this->find(
            [
                'mchid' => $this->mchid,
                'start_province_code' => $province_code,
                'start_city_code' => $city_code,
                'start_area_code' => $area_code,
                'type' => 1
            ],
            'LineFast'
        );
        if ($lineFast->ret == false) {
            $lineFast = $this->find(
                [
                    'mchid' => $this->mchid,
                    'start_province_code' => $province_code,
                    'start_city_code' => $city_code,
                    'type' => 1
                ],
                'LineFast'
            );
            if ($lineFast->ret == true) {
                $lineFastId = $lineFast->data['id'];
            }
        } else {
            $lineFastId = $lineFast->data['id'];
        }
        return intval($lineFastId);
    }

    /**
     * 摆渡车计价规则
     *
     * @param array $ids
     * @return mixed
     */
    public function queryFerryPricing($id)
    {
        $ids = [];
        $bwhere['mchid'] = $this->mchid;
        $bwhere['line_fast_id'] = $id;
        $pricings = M('LineFastBaseFee')->where($bwhere)->group('pricing')->field('pricing')->select();
        if ($pricings) {
            foreach ($pricings as $val) {
                $ids[] = $val['pricing'];
            }
        }
        $pwhere['mchid'] = $this->mchid;
        $pwhere['_string'] = " deleted_at IS NULL ";
        $pwhere['_string'] = sprintf(" id IN (%s)", implode(',', $ids));
        $results = M('LineVehiclePricing')->where($pwhere)->field('id,title')->order('id DESC')->select();
        return json_encode($results);
    }


    //获取上下车点
    public function getLineClassPoint($id = '', $type = '')
    {
        $pointMode = new LineClassPointModel();
        $lineClassPointInfo = $pointMode->where(['line_class_id' => $id, 'is_return' => 0, 'type' => $type])->field('alias')->select();
        $info = (object)[];
        $info->ret = true;
        $info->data = '';
        foreach ($lineClassPointInfo as $key => $value) {
            $info->data .= "<p>{$value['alias']}</p>";
        }
        return $this->output($info);
    }

    public function getLineClassTrainFerry($id = 0)
    {
        $train = M('LineClassTrain')->where(['line_class_train_id' => $id])->field('is_start_ferry,is_end_ferry,start_ferry_pricing,end_ferry_pricing,start_ferry_origin_line_driver_enabled,end_ferry_origin_line_driver_enabled')->find();
        $info = (object)[];
        $info->ret = true;
        $info->data = '';
        if ($train['is_start_ferry'] == 1) {
            $info->data .= sprintf('<p><b>是否启用出发地摆渡车</b>： %s</p>', '✅ 已启用');

            if ($train['start_ferry_pricing'] > 0) {
                $pricing = M('LineVehiclePricing')->find($train['start_ferry_pricing']);
                $info->data .= sprintf('<p><b>出发地计价规则：</b> %s</p>', $pricing['title']);
            }

            if ($train['start_ferry_origin_line_driver_enabled'] == 1) {
                $info->data .= sprintf('<p><b>出发地摆渡车[接]：</b> %s</p>', '本主路线司机接送');
            } else {
                $info->data .= sprintf('<p><b>出发地摆渡车[接]：</b> %s</p>', '单独指派摆渡车/分流车司机接送');
            }
        }
        if ($train['is_end_ferry'] == 1) {
            $info->data .= sprintf('<p><b>是否启用目的地摆渡车：</b> %s</p>', '✅ 已启用');
            if ($train['end_ferry_pricing'] > 0) {
                $pricing = M('LineVehiclePricing')->field('title')->find($train['end_ferry_pricing']);
                $info->data .= sprintf('<p><b>目的地计价规则：</b> %s</p>', $pricing['title']);
            }

            if ($train['end_ferry_origin_line_driver_enabled'] == 1) {
                $info->data .= sprintf('<p><b>目的地摆渡车[送]：</b> %s</p>', '本主路线司机接送');
            } else {
                $info->data .= sprintf('<p><b>目的地摆渡车[送]：</b> %s</p>', '单独指派摆渡车/分流车司机接送');
            }
        }

        return $this->output($info);
    }



    //复制单条线路（或返程线路）
    public function doCopyLineClass($line_class_id = 0, $is_return = 0)
    {
        $LineInfo = M('LineClass')->where(['id' => $line_class_id, 'is_del' => 0])->find();
        if (!$LineInfo) {
            return $this->output(new ResultModel(false, "未找到线路信息！"));
        }
        // 判断是否复制返程线路
        if ($is_return == 1) {
            // 调换始末点
            list(
                $LineInfo['start_address_code'],
                $LineInfo['start_address_type'],
                $LineInfo['start_area_code'],
                $LineInfo['start_city_code'],
                $LineInfo['start_name'],
                $LineInfo['start_polygon'],
                $LineInfo['pickup_polygons'],
                $LineInfo['start_province_code'],

                $LineInfo['end_address_code'],
                $LineInfo['end_address_type'],
                $LineInfo['end_area_code'],
                $LineInfo['end_city_code'],
                $LineInfo['end_name'],
                $LineInfo['end_polygon'],
                $LineInfo['dropoff_polygons'],
                $LineInfo['end_province_code']
            ) = array(
                $LineInfo['end_address_code'],
                $LineInfo['end_address_type'],
                $LineInfo['end_area_code'],
                $LineInfo['end_city_code'],
                $LineInfo['end_name'],
                $LineInfo['end_polygon'],
                $LineInfo['dropoff_polygons'],
                $LineInfo['end_province_code'],

                $LineInfo['start_address_code'],
                $LineInfo['start_address_type'],
                $LineInfo['start_area_code'],
                $LineInfo['start_city_code'],
                $LineInfo['start_name'],
                $LineInfo['start_polygon'],
                $LineInfo['pickup_polygons'],
                $LineInfo['start_province_code']
            );
        }

        unset($LineInfo['id']);
        unset($LineInfo['create_time']);
        unset($LineInfo['update_time']);
        //复制后默认手动
        unset($LineInfo['scheduling_type']);
        unset($LineInfo['exclude_scheduling']);
        unset($LineInfo['pre_sale_time']);
        unset($LineInfo['driver_id']);

        $this->startTrans();
        $r = M('LineClass')->add($LineInfo);
        if ($r) {
            $where['line_class_id'] = $line_class_id;
            $lineClassPointsR = $this->select($where, null, null, null, 'LineClassPoint');
            if ($lineClassPointsR->ret) {
                foreach ($lineClassPointsR->data as $v) {
                    unset($v['id']);
                    unset($v['create_time']);
                    unset($v['update_time']);
                    $v['line_class_id'] = $r;
                    // 返程处理
                    if ($is_return == 1) {
                        $v['type'] = 3 - $v['type'];
                    }
                    $lineClassPointS = $this->add('LineClassPoint', $v);
                    if (!$lineClassPointS->ret) {
                        $this->transRollback();
                        return $this->output(new ResultModel(true, "复制失败！"));
                    }
                }
            }

            $this->commitTrans();
            $this->doAddLog("复制单条线路：复制成功" . $r . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
            return $this->output(new ResultModel(true, "复制成功！"));
        } else {
            $this->transRollback();
            return $this->output(new ResultModel(true, "复制失败！"));
        }
    }

    //运行或停运
    public function doBatchOperationLineClass($status = '', $is_batch = 0, $id = 0, $is_train = 0)
    {
        if ($status === '' or $id == 0) {
            return $this->output(new ResultModel(false, "参数错误1！"));
        }
        if ($is_batch == 1) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return $this->output(new ResultModel(false, "参数错误2！"));
            }
            $where['id'] = ['in', $ids];
        } else {
            if (!is_numeric($id) or $id < 1) {
                return $this->output(new ResultModel(false, "参数错误3！"));
            }
            $where['id'] = $id;
        }
        if ($status == 1) {
            $data['status'] = 1;
            $data['is_manually_modified'] = 0;
        } else {
            $data['status'] = 0;
            $data['is_manually_modified'] = 1;
        }
        if ($is_train == 1) {
            $model = M('LineClassTrain');
            $where['line_class_train_id'] = $where['id'];
            unset($where['id']);
        } else {
            $model = M('LineClass');
            $whereCondition = is_array($where['id']) ? ['line_class_id' => $where['id']] : ['line_class_id' => ['in', (array)$where['id']]];
            M('LineClassTrain')->where($whereCondition)->save(['status' => $data['status'], 'is_manually_modified' => $data['is_manually_modified']]);
        }
        $res = $model->where($where)->save($data);
        if ($res) {
            $this->doAddLog("运行或停运,操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), "条件：" . json_encode($where) . "，更新：" . json_encode($data));
            return $this->output(new ResultModel(true, "操作成功！"));
        } else {
            return $this->output(new ResultModel(false, '操作失败'));
        }
    }

    //批量删除班线或班次
    public function doBatchDelLineClass($id = 0, $is_train = 0)
    {
        if ($id == 0) {
            return $this->output(new ResultModel(false, "参数错误1！"));
        }
        $ids = explode('|', $id);
        if (count($ids) < 1) {
            return $this->output(new ResultModel(false, "参数错误2！"));
        }
        $where['id'] = ['in', $ids];
        if ($is_train == 1) {
            $model = M('LineClassTrain');
            $where['line_class_train_id'] = $where['id'];
            unset($where['id']);
        } else {
            $model = M('LineClass');
        }
        M()->startTrans();
        $res = $model->where($where)->save(['is_del' => 1]);
        if ($res) {
            //如果是班线删除班次
            if ($is_train != 1) {
                $lineClassTrainR = $this->find(['line_class_id' => ['in', $ids]], 'LineClassTrain');
                if ($lineClassTrainR->ret) {
                    $re = M('LineClassTrain')->where(['line_class_id' => ['in', $ids]])->save(['is_del' => 1]);
                    if (!$re) {
                        M()->rollback();
                        return $this->output(new ResultModel(false, '操作失败!'));
                    }
                }
            }
            M()->commit();
            $this->doAddLog("批量删除班次：" . json_encode($ids) . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
            return $this->output(new ResultModel(true, "操作成功！"));
        } else {
            return $this->output(new ResultModel(false, '操作失败'));
        }
    }
    /**
     * 添加班线
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param float $rate 线路提成比例
     * @param string $type 拼车
     * @param int $status 线路提成比例
     */
    public function doCreateLineClass($mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $type = \CommonDefine::ORDER_TYPE_5, $rate = 0, $status = 1, $aboard_longitude, $aboard_latitude, $aboard_address)
    {
        $this->data['aboard_address_code'] = $this->getGdAddressCodeByGdApi($aboard_longitude, $aboard_latitude);
        try {
            $gdObj = new GdRegionModel();
            $adcode = $start_area_code;
            if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
                $adcode = $start_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
            if (isset($infos['districts'][0]['center'])) {
                $this->data['center_start_latlng'] = $infos['districts'][0]['center'];
            }
            $adcode = $end_area_code;
            if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
                $adcode = $end_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
            if (isset($infos['districts'][0]['center'])) {
                $this->data['center_end_latlng'] = $infos['districts'][0]['center'];
            }
            $this->startTrans();
            $r = $this->add('LineClass');
            if ($r->ret) {
                $this->commitTrans();
                $this->doAddLog("添加线路：" . $r->data . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode($this->data));
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        return $this->output($r);
    }

    /**
     * 修改线路
     * @param int $mchid 商户id
     * @param int $branchid 分台id
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $start_address_code 出发地地址编码
     * @param int $end_address_code 目的地地址编码
     * @param string $summary 线路说明
     * @param float $rate 线路提成比例
     * @param int $status 线路提成比例
     */
    public function doUpdateLineClass($id, $mchid, $branchid, $start_name, $end_name, $start_province_code, $start_city_code, $start_area_code, $end_area_code, $end_city_code, $end_province_code, $start_address_code, $end_address_code, $type = \CommonDefine::ORDER_TYPE_5, $rate = 0, $status = 1, $aboard_longitude, $aboard_latitude, $aboard_address)
    {
        $this->data['status'] = 1;
        $this->data['rate'] = 0;
        $this->data['aboard_address_code'] = $this->getGdAddressCodeByGdApi($aboard_longitude, $aboard_latitude);
        try {
            $gdObj = new GdRegionModel();
            $adcode = $start_area_code;
            if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
                $adcode = $start_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
            if (isset($infos['districts'][0]['center'])) {
                $this->data['center_start_latlng'] = $infos['districts'][0]['center'];
            }
            $adcode = $end_area_code;
            if ($gdObj->where(['address_id' => $adcode])->find()['name'] == '市辖区') {
                $adcode = $end_city_code;
            }
            $infos = json_decode(file_get_contents('https://restapi.amap.com/v3/config/district?' . http_build_query(['keywords' => $adcode, 'subdistrict' => 3, 'key' => '88402b07e88938a3937f616d63aa7fb9'])), true);
            if (isset($infos['districts'][0]['center'])) {
                $this->data['center_end_latlng'] = $infos['districts'][0]['center'];
            }
            $this->startTrans();
            $r = $this->save('LineClass');
            if ($r->ret) {
                $this->commitTrans();
                $this->doAddLog("修改线路：" . $id . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode($this->data));
                return $this->output(new ResultModel(true, "修改成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "修改失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        return $this->output($r);
    }

    /**
     * 删除班线
     * @param int $line_class_id 线路ID
     */
    public function doDeleteLineClass($line_class_id)
    {
        $this->startTrans();
        $r = $this->save('LineClass', array('id' => $line_class_id, 'is_del' => 1));
        if ($r->ret) {
            $lineClassTrainR = $this->find(['line_class_id' => $line_class_id], 'LineClassTrain');
            if ($lineClassTrainR->ret) {
                $where['line_class_id'] = $line_class_id;
                $where['is_del'] = 0;
                $lineClassTrainCountR = $this->count($where, 'LineClassTrain');
                if ($lineClassTrainCountR->ret) {
                    if ($lineClassTrainCountR->data > 0) {
                        $lineClassTrainData['is_del'] = 1;
                        $lineClassTrainData['update_time'] = date('Y-m-d H:i:s', time());
                        $re = M('LineClassTrain')->where(['line_class_id' => $line_class_id])->save($lineClassTrainData);
                        if (!$re) {
                            $this->transRollback();
                            return $this->output(new ResultModel(false, '操作失败'));
                        }
                    }
                }
            }
            $this->commitTrans();
        } else {
            $this->transRollback();
        }

        $this->doAddLog("删除班线：" . $line_class_id . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
        return $this->output($r);
    }

    /**
     * 获取班次时刻表
     * @param string $start_date 开始日期
     * @param string $end_date 结束日期
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $branchid 所属分台
     * @param int $line_class_id 线路编号
     * @param int $page
     * @param int $size
     */
    public function getLineClassScheduleList($start_date = null, $end_date = null, $start_time = null, $end_time = null, $start_name = null, $end_name = null, $branchid = null, $line_class_id = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $this->mchid;
        if ($this->admin_group_id == 3) {
            $where['branchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['status'] = \CommonDefine::LINE_CLASS_OPERATE_1;
        if (!empty($line_class_id)) {
            $where['id'] = $line_class_id;
        }

        if (!empty($start_name)) {
            $where['start_name'] = ['like', '%' . $start_name . '%'];
        }
        if (!empty($end_name)) {
            $where['end_name'] = ['like', '%' . $end_name . '%'];
        }

        if ($branchid) {
            $where['branchid'] = $branchid;
        }

        $lineClassRs = $this->select($where, $page, $size, 'id desc', 'LineClass');
        if ($lineClassRs->ret) {
            foreach ($lineClassRs->data as $k => $lineClass) {
                $dayNum = \diffBetweenTwoDays($start_date, $end_date);
                $branchR = $this->find(array("admin_id" => $lineClass['branchid']), 'admin');
                if ($branchR->ret) {
                    $lineClassRs->data[$k]['branchname'] = $branchR->data['mchname'];
                }
                $trainWhere['line_class_id'] = $lineClass['id'];
                $trainWhere['start_date'] = ['between', [$start_date, $end_date]];
                $lineClassTrainRs = $this->select($trainWhere, null, null, 'start_date asc,line_class_train_id asc', 'LineClassTrain');
                if ($lineClassTrainRs->ret) {
                    for ($di = 0; $di <= $dayNum; $di++) {
                        $tempDate = date('Y-m-d', strtotime($start_date . " +$di day"));
                        $lineClassRs->data[$k]['date_list'][$di] = ['date' => $tempDate, 'is_schedule' => 0];
                        foreach ($lineClassTrainRs->data as $tk => $lineClassTrain) {
                            if ($tempDate == $lineClassTrain['start_date']) {
                                if ($lineClassTrain['is_del'] == \CommonDefine::IS_DEL_0) {
                                    $lineClassRs->data[$k]['date_list'][$di]['is_schedule'] = 1;
                                    $lineClassRs->data[$k]['date_list'][$di]['line_class_train_id'] = $lineClassTrain['line_class_train_id'];
                                } else {
                                    $lineClassRs->data[$k]['date_list'][$di]['is_schedule'] = 2;
                                    $lineClassRs->data[$k]['date_list'][$di]['line_class_train_id'] = $lineClassTrain['line_class_train_id'];
                                }
                            }
                        }
                    }
                } else {
                    for ($di = 0; $di <= $dayNum; $di++) {
                        $tempDate = date('Y-m-d', strtotime($start_date . " +$di day"));
                        $lineClassRs->data[$k]['date_list'][] = ['date' => $tempDate, 'is_schedule' => 0];
                    }
                }
            }
        }
        $this->doAddLog("线路排班总览表,操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
        return $this->output($lineClassRs);
    }

    /**
     * 班次查询
     *
     * @param string $start_date 开始日期
     * @param string $end_date 结束日期
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @param string $start_name 出发地名称
     * @param string $end_name 目的地名称
     * @param int $branchid 所属分台
     * @param int $line_class_id 线路编号
     * @param string $line_class_train_no 班次编号
     * @param int $status 运营状态：0-停运中；1-运营中
     * @param int $page
     * @param int $size
     * @return void
     */
    public function getLineClassTrainList($start_date = null, $end_date = null, $start_time = null, $end_time = null, $start_name = null, $end_name = null, $branchid = null, $line_class_id = null, $line_class_train_no = null, $status = null, $page = 1, $size = 10)
    {
        if (!empty($line_class_train_no)) {
            $where['cp_line_class_train.line_class_train_no'] = $line_class_train_no;
        }
        if (!empty($start_name)) {
            $where['b.start_name'] = ['like', '%' . $start_name . '%'];
        }
        if (!empty($end_name)) {
            $where['b.end_name'] = ['like', '%' . $end_name . '%'];
        }
        if ($line_class_id) {
            $where['b.id'] = $line_class_id;
        }
        if (!is_null($status) && $status !== '') {
            $where['cp_line_class_train.status'] = $status;
        }
        $where['_string'] = "";
        if (!empty($start_date)) {
            if (empty($where['_string'])) {
                $where['_string'] = " cp_line_class_train.start_date >= '$start_date' ";
            } else {
                $where['_string'] .= " AND cp_line_class_train.start_date >= '$start_date' ";
            }
        }
        if (!empty($end_date)) {
            if (empty($where['_string'])) {
                $where['_string'] = " cp_line_class_train.start_date <= '$end_date'";
            } else {
                $where['_string'] .= " AND cp_line_class_train.start_date <= '$end_date'";
            }
        }
        if (!empty($start_time)) {
            $start_time_sec = getTimeNumber($start_time);
            if (empty($where['_string'])) {
                $where['_string'] = " ((b.start_time_type = 1 AND b.sort >= $start_time_sec)";
                $where['_string'] .= " OR (b.start_time_type = 2 AND b.sort >= $start_time_sec))";
            } else {
                $where['_string'] .= " AND ((b.start_time_type = 1 AND b.sort >= $start_time_sec)";
                $where['_string'] .= " OR (b.start_time_type = 2 AND b.sort >= $start_time_sec))";
            }
        }
        if (!empty($end_time)) {
            $end_time_sec = getTimeNumber($end_time);
            if (empty($where['_string'])) {
                $where['_string'] = " ((b.start_time_type = 1 AND b.sort <= $end_time_sec)";
            } else {
                $where['_string'] .= " AND ((b.start_time_type = 1 AND b.sort <= $end_time_sec)";
            }
        }
        if ($this->admin_group_id == 2) {
            if (!empty($branchid) && $branchid) {
                $where['cp_line_class_train.branchid'] = $branchid;
            }
        } else {
            $where['cp_line_class_train.branchid'] = $this->state->user_id;
        }
        $where['cp_line_class_train.mchid'] = $this->mchid;
        // $where['ltd.mchid'] = $this->mchid; // 司机表的商户过滤
        $where['cp_line_class_train.is_del'] = 0;
        $info = M('LineClassTrain')
            ->field('cp_line_class_train.*, b.start_time as class_start_time, b.start_name, b.end_name, b.sort, b.start_address_code, b.end_address_code, b.start_time_type, b.start_earliest_time as start_start_earliest_time, b.end_latest_time as end_end_latest_time, b.start_address_type, b.end_address_type')
            ->where($where)
            ->join("cp_line_class b on b.id = cp_line_class_train.line_class_id")
            ->join("cp_line_class_train_driver ltd ON ltd.line_class_train_id = cp_line_class_train.line_class_train_id")
            ->join("cp_driver d ON d.driver_id = ltd.driver_id")
            ->group('cp_line_class_train.line_class_train_id') // 分组以避免重复班次
            ->page($page, $size)
            ->order('cp_line_class_train.start_date asc, cp_line_class_train.start_time asc')
            ->select();
        $count = M('LineClassTrain')
            ->where($where)
            ->join("cp_line_class b on b.id=cp_line_class_train.line_class_id")
            ->page($page, $size)
            ->order('cp_line_class_train.start_date asc')
            ->count();
        if ($info) {
            foreach ($info as $k => $lineClassTrain) {

                // 查询当前班次的司机信息
                $driverInfo = M('LineClassTrainDriver')
                    ->alias('ltd')
                    ->field('d.name, d.car_tail_number, d.total_seating, d.residual_seating, ltd.sort')
                    ->join('cp_driver d ON d.driver_id = ltd.driver_id')
                    ->where(array(
                        'ltd.line_class_train_id' => $lineClassTrain['line_class_train_id'],
                        'ltd.mchid' =>  $this->mchid,
                    ))
                    ->order('ltd.sort asc')
                    ->select();

                // 拼接司机信息字符串
                $driverInfoString = '';
                if ($driverInfo) {
                    foreach ($driverInfo as $driver) {
                        $driverInfoString .= "<p style='border-bottom: 1px solid #dadada;'><span style='color:red;font-weight:500;'>{$driver['sort']}、</span>{$driver['name']}-{$driver['car_tail_number']}<p>";
                    }
                }

                // 将拼接结果存入数组
                $info[$k]['driver_info'] = rtrim($driverInfoString, "\n");


                # 自动排班或者手动排班
                $info[$k]['cn_scheduling_type'] = $lineClassTrain['scheduling_type'] == 1
                    ? sprintf('<a href="javascript:" address="%s" onclick="showNote($(this))" >%s</a>', $lineClassTrain['desc'], $this->getCnDispatchMode($lineClassTrain['dispatch_mode']))
                    : sprintf('<a href="javascript:" address="%s" onclick="showNote($(this))" >%s</a>', $lineClassTrain['desc'], $this->getCnDispatchMode($lineClassTrain['dispatch_mode']));
                # 摆渡车信息。摆渡车服务开启后展示
                if ($this->queryMerchantFerryService() == 1) {
                    $info[$k]['cn_ferry'] =  ($lineClassTrain['is_start_ferry'] == 1 || $lineClassTrain['is_end_ferry'] == 1)
                        ? sprintf('<a href="javascript:" rel="%d" address="摆渡车" onclick="getLineClassTrainFerry($(this))" >查看班次摆渡车设置</a>', $lineClassTrain['line_class_train_id'])
                        : '班次未设置摆渡车';
                } else {
                    $info[$k]['cn_ferry'] = '';
                    $info[$k]['cn_ferry_show'] = 'cn_ferry_none';
                }

                // 处理 total_tickets 和 remain_tickets
                $info[$k]['total_tickets'] = ($lineClassTrain['total_tickets'] == -1) ? '不限' : $lineClassTrain['total_tickets'];
                $info[$k]['remain_tickets'] = ($lineClassTrain['remain_tickets'] == -1) ? '不限' : $lineClassTrain['remain_tickets'];

                # 老系统班线信息
                $info[$k]['route'] = "<b style='color: gray;'>  &nbsp;班次编号：</b>" . $lineClassTrain['line_class_train_no'] . "<br/>";
                $info[$k]['route'] .= "<b style='color: green;'> &nbsp;起：</b>" . $lineClassTrain['start_name'] . "<br/>";
                //获取上车点
                if ($lineClassTrain['start_address_type'] == 2) {
                    if ($lineClassTrain['is_start_ferry'] == \CommonDefine::IS_START_FERRY_1) {
                        $info[$k]['route'] .= "<p>【接】任意地点(电子围栏内)&nbsp;&nbsp;<a rel=\"{$lineClassTrain['line_class_id']}\" address='{$lineClassTrain['start_name']}'  onclick='sss($(this))' list_type=\"1\" class=\"class_list_more\" href=\"javascript:\">查看出发地车场</a></p>";
                    } else {
                        $info[$k]['route'] .= "<p>任意地点(电子围栏内)</p>";
                    }
                } else {
                    $info[$k]['route'] .= "<a rel=\"{$lineClassTrain['line_class_id']}\" address='{$lineClassTrain['start_name']}'  onclick='sss($(this))' list_type=\"1\" class=\"class_list_more\" href=\"javascript:\">查看固定上车点</a>" . "<br/>";
                }

                $info[$k]['route'] .= "<b style='color: red;'>  &nbsp;终：</b>" . $lineClassTrain['end_name'] . "<br/>";
                //获取下车点
                if ($lineClassTrain['end_address_type'] == 2) {
                    if ($lineClassTrain['is_end_ferry'] == \CommonDefine::IS_END_FERRY_1) {
                        $info[$k]['route'] .= "<p>【送】任意地点(电子围栏内)&nbsp;&nbsp;<a rel=\"{$lineClassTrain['line_class_id']}\" address='{$lineClassTrain['end_name']}' onclick='sss($(this))' list_type=\"2\" class=\"class_list_more\" href=\"javascript:\">查看目的地车场</a></p>";
                    } else {
                        $info[$k]['route'] .= "<p>任意地点(电子围栏内)</p>";
                    }
                } else {
                    $info[$k]['route'] .= "<a rel=\"{$lineClassTrain['line_class_id']}\" address='{$lineClassTrain['end_name']}' onclick='sss($(this))' list_type=\"2\" class=\"class_list_more\" href=\"javascript:\">查看固定下车点</a>";
                }

                if (strtotime($lineClassTrain['start_time']) > time()) {
                    $info[$k]['line_class_status'] = 0;
                } else {
                    $info[$k]['line_class_status'] = 1;
                }
                $branchR = $this->find(array("admin_id" => $lineClassTrain['branchid']), 'admin');
                if ($branchR->ret) {
                    $info[$k]['branchname'] = $branchR->data['mchname'];
                }
                $info[$k]['is_seat_selection_info'] = "不支持";
                $info[$k]['price_info'] = "<p>销售价格:" . $info[$k]['price'] . "元</p><p>渠道价格:" . $info[$k]['channel_price'] . "元</p>";
                if ($info[$k]['is_seat_selection'] == 1) {
                    $info[$k]['is_seat_selection_info'] = "<p>支持</p><p>当前班线" . $info[$k]['car_seats'] . "座</p>";
                    if ($info[$k]['seat_layout']) {
                        $info[$k]['is_seat_selection_info'] .= "<p>座位布局：" . $info[$k]['seat_layout'] . "</p>";
                    }
                    $seatPriceArr = json_decode($info[$k]['seat_price'], true);
                    foreach ($seatPriceArr as $kk => $seatPrice) {
                        if ($kk == 0) {
                            $info[$k]['price_info'] = '';
                        } else {
                            $seat = "<p>" . $seatPrice['name'] . ":" . $seatPrice['price'] . "元</p>";
                            if ($seatPrice['optional'] == \CommonDefine::OPTIONAL_1) {
                                $seat = "<p>" . $seatPrice['name'] . ":" . $seatPrice['price'] . "元(已售出)</p>";
                            }
                            $info[$k]['price_info'] .= $seat;
                        }
                    }
                }

                if ($lineClassTrain['start_time_type'] == 2) {
                    $tempStartEarliestTime  = $lineClassTrain['start_earliest_time'] ?: $lineClassTrain['start_start_earliest_time'];
                    $tempEndLatestTime  = $lineClassTrain['end_latest_time'] ?: $lineClassTrain['end_latest_time'];
                    $info[$k]['start_time'] = "<p><b>滚动发车</b></p>"
                        . "<p><b>最早：</b>" . $tempStartEarliestTime . "</p>"
                        . "<p><b>最晚：</b>" . $tempEndLatestTime . "</p>";
                }

                //计算已售票数
                $selled = M('Order')->field("sum(book_seating) as sell_tickets")->where(array('line_class_train_id' => $lineClassTrain['line_class_train_id']))->find();
                $info[$k]['sell_tickets'] = isset($selled['sell_tickets']) ? $selled['sell_tickets'] : 0;
            }
            $a = (object)[];
            $a->count = $count;
            $a->data = $info;
            $a->ret = true;
            return $this->output($a);
        } else {
            return $this->output(new ResultModel(false, '暂无数据！'));
        }
    }

    private function getCnDispatchMode($mode)
    {
        switch ($mode) {
            case '0':
                return '班线车自动派单-按顺序';
                break;
            case '1':
                return '班线车手动派单';
                break;
            case '2':
                return '班线车自动派单-按距离及顺序';
                break;
            default:
                return '<a href="/config_set">按商户配置</a>';
                break;
        }
    }


    /**
     * 获取班次详情
     */
    public function getLineClassTrainDetail($line_class_trian_id)
    {
        $where['line_class_train_id'] = $line_class_trian_id;
        $where['mchid'] = $this->mchid;
        $lineClassTrainR = $this->find($where, 'LineClassTrain');
        if ($lineClassTrainR->ret) {
            $lineClassR = $this->find(['id' => $lineClassTrainR->data['line_class_id']], 'LineClass');
            if ($lineClassR->ret) {
                $lineClassTrainR->data['branch_drivers'] = json_encode([], true);
                $branchDriversR = $this->select(['branchid' => $lineClassR->data['branchid'], 'is_freeze' => 0, 'mchid' => $this->mchid], null, null, null, 'Driver', 'driver_id,name,total_seating');
                if ($branchDriversR->ret) {
                    $lineClassTrainR->data['branch_drivers'] = json_encode($branchDriversR->data, true);
                }

                $lineDriverRs = $this->select(['line_class_train_id' => $line_class_trian_id], null, null, null, 'LineClassTrainDriver', 'driver_id,sort');
                $lineClassTrainR->data['driver_id'] = json_encode($lineDriverRs->ret ? $lineDriverRs->data : [], true);

                $branchR = $this->find(['admin_id' =>  $lineClassR->data['branchid']], 'Admin');
                $lineClassTrainR->data['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";

                //计算已售票数
                $selledR = $this->find(['line_class_train_id' => $line_class_trian_id], 'Order', "sum(book_seating) as sell_tickets");
                $lineClassTrainR->data['sell_tickets'] = empty($selledR->data['sell_tickets']) ? 0 : $selledR->data['sell_tickets'];


                // 处理 total_tickets 和 remain_tickets
                $lineClassTrainR->data['total_tickets'] =  $lineClassTrainR->data['total_tickets'] == -1 ? '不限' : $lineClassTrainR->data['total_tickets'];
                $lineClassTrainR->data['remain_tickets'] =  $lineClassTrainR->data['remain_tickets'] == -1 ? '不限' : $lineClassTrainR->data['remain_tickets'];

                return $this->output($lineClassTrainR);
            }
        }

        return $this->output(new ResultModel(true, '获取数据失败！'));
    }


    //修改班次
    public function doSaveLineClass($is_seat_selection = 0, $line_class_train_id = 0, $update_tickets = 0, $total_tickets = 0, $remain_tickets = '', $price = '', $channel_price = '', $driver_id = [], $sort = [], $seat_price = null)
    {
        # 开启事务
        M()->startTrans();
        # 线路司机检查
        if (empty($driver_id)) {
            M()->rollback();
            return $this->output(new ResultModel(false, '班次接单司机不能为空'));
        }
        # 检查班次是否有效
        $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $line_class_train_id, 'is_del' => 0, 'status' => 1, 'mchid' => $this->mchid))->find();
        if (empty($lineClassTrainArr)) {
            M()->rollback();
            return $this->output(new ResultModel(false, '班次数据无效或已被删除，请确认后再试'));
        }
        # 选座模式与非选座模式参数校验
        if ($is_seat_selection == \CommonDefine::SEAT_SELECTION_1) {
            if (empty($seat_price)) {
                M()->rollback();
                return $this->output(new ResultModel(false, '选座模式，必须填写价格信息'));
            }
            $originSeatPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
            $updateSeatPriceR = $this->updateSeatprice($originSeatPriceArr, $seat_price);
            if (!$updateSeatPriceR->ret) {
                M()->rollback();
                return $this->output(new ResultModel(false, $updateSeatPriceR->data));
            }
            $this->data['seat_price'] = json_encode($updateSeatPriceR->data);
        } else {
            if (empty($price) or $price < 0 or !is_numeric($price)) {
                M()->rollback();
                return $this->output(new ResultModel(false, '请输入正确的销售价格！'));
            }
            if ($channel_price < 0 or !is_numeric($channel_price)) {
                M()->rollback();
                return $this->output(new ResultModel(false, '请输入正确的渠道价格！'));
            }
        }
        # 剩余车票数检查
        if ($remain_tickets < 0 or !is_numeric($remain_tickets)) {
            M()->rollback();
            return $this->output(new ResultModel(false, '余票数不合法'));
        }
        $update_tickets = intval($update_tickets);
        if ($update_tickets < 0) {
            if (($remain_tickets + $update_tickets) < 0) {
                M()->rollback();
                return $this->output(new ResultModel(false, '调整的车票数加上余票数小于0，参数不合法'));
            }
        }
        # 总票数必须大于或等于 修改车票数加已售车票数
        $selled = M('Order')->field("sum(book_seating) as sell_tickets")->where(array('line_class_train_id' => $line_class_train_id))->find();
        $sellTickets = isset($selled['sell_tickets']) ? $selled['sell_tickets'] : 0;
        # 更新后的总票数
        $this->data['total_tickets'] = $lineClassTrainArr['total_tickets'] + $update_tickets;
        if ($this->data['total_tickets'] < 0) {
            M()->rollback();
            return $this->output(new ResultModel(false, '调整的车票数加上总票数小于0，参数不合法'));
        }
        if ($this->data['total_tickets'] < $sellTickets) {
            M()->rollback();
            return $this->output(new ResultModel(false, '调整的车票数加上总票数已经小于已售票数，参数不合法'));
        }
        # 更新后的剩余车票
        $this->data['remain_tickets'] = $lineClassTrainArr['remain_tickets'] + $update_tickets;

        unset($this->data['line_class_train_id']);
        $lineClassTrainDriverRs = $this->select(['line_class_train_id' => $line_class_train_id, 'mchid' => $this->mchid], null, null, null, 'LineClassTrainDriver');
        //先移除之前的司机
        M('LineClassTrainDriver')->where(['line_class_train_id' => $line_class_train_id, 'mchid' => $this->mchid])->delete();
        foreach ($driver_id as $key => $value) {
            if (intval($value) > 0) {
                $res = M('LineClassTrainDriver')->add([
                    'line_class_train_id' => $line_class_train_id,
                    'start_date_at' => $lineClassTrainArr['start_date'],
                    'start_time_at' => $lineClassTrainArr['start_time'],
                    'mchid' => $this->state->user_id,
                    'driver_id' => $value,
                    'sort' => $sort[$key],
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                ]);
                if (!$res) {
                    M()->rollback();
                    return $this->output(new ResultModel(false, '重新设置班次司机信息失败，请稍后再试'));
                }
            }
        }
        unset($this->data['driver_id']);
        $this->data['update_time'] = date('Y-m-d H:i:s');
        $this->data['is_manually_modified'] = 1;
        $where['line_class_train_id'] = $line_class_train_id;
        if ($this->admin_group_id == 3) {
            $where['branchid'] = $this->state->user_id;
        } elseif ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
        }
        $r = M('LineClassTrain')->where($where)->save($this->data);
        if ($r) {
            M()->commit();
            $logData = "班次修改前数据：" . json_encode($lineClassTrainArr) . "；班次接单司机修改前数据：";
            if ($lineClassTrainDriverRs->ret) {
                $logData .= json_encode($lineClassTrainDriverRs->data);
            } else {
                $logData .= "暂无司机数据";
            }
            $logData .= "班次修改后数据：" . json_encode($this->data) . "；班次接单司机修改后数据：" . json_encode($driver_id);
            $this->doAddLog("后台操作人员修改班次信息[" . $lineClassTrainArr['line_class_id'] . ":" . $line_class_train_id . "],本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), $logData, $this->admin_group_id);
            return $this->output(new ResultModel(true, '修改成功！'));
        } else {
            M()->rollback();
            return $this->output(new ResultModel(false, '修改失败！'));
        }
    }

    /**
     * 添加班次
     *
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $start_time 发车时间
     * @param float $price 销售价格
     * @param string $market_price 市场价格
     * @param string $channel_price 渠道价格
     * @param string $travel_time 目的地名称
     * @param string $driver_d 司机id
     * @param int $pre_sale_time 预售时间
     * @param array $exclude_scheduling 排除的调度
     * @param array $driver_id 司机id
     * @param int $scheduling_type 调度类型
     * @param mixed $level 级别
     * @param int $is_start_ferry 是否开始渡轮
     * @param int $is_end_ferry 是否结束渡轮
     * @param int $start_origin_line_driver_enabled 是否启用起始原点线路司机
     * @param int $start_pricing 起始定价
     * @param int $end_origin_line_driver_enabled 是否启用终点原点线路司机
     * @param int $end_pricing 终点定价
     * @param string $ticket_sale_rule 售票规则
     * @param int $custom_seats 自定义座位
     * @param mixed $dispatch_mode 调度模式
     *
     * @return void
     */
    public function doAddLineClassTrain($line_class_ids, $pre_sale_time = null, $exclude_scheduling = [], $driver_id = [], $scheduling_type, $level = null, $is_start_ferry = 0, $is_end_ferry = 0, $start_origin_line_driver_enabled = 0, $start_pricing = 0, $end_origin_line_driver_enabled = 0, $end_pricing = 0, $ticket_sale_rule = 'total_driver_seats', $custom_seats = 0, $dispatch_mode = null, $driver_schedules = '', $enable_driver_cycle = 0)
    {
        $lineClassIdsArr = explode(',', $line_class_ids);
        if (count($lineClassIdsArr) < 1) {
            return $this->output(new ResultModel(false, "班线ID参数错误"));
        }

        $ret = new \ResultModel(false, '排班操作失败');
        $header = [
            'merchant: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $params = array(
            'line_class_ids' => $line_class_ids,
            'pre_sale_time' => $pre_sale_time,
            'exclude_scheduling' => implode(',', $exclude_scheduling),
            'driver_id' => implode(',', $driver_id),
            'scheduling_type' => $scheduling_type,
            'level' => implode(',', $level),
            'is_start_ferry' => $is_start_ferry,
            'is_end_ferry' => $is_end_ferry,
            'start_origin_line_driver_enabled' => $start_origin_line_driver_enabled,
            'end_origin_line_driver_enabled' => $end_origin_line_driver_enabled,
            'start_pricing' => $start_pricing,
            'end_pricing' => $end_pricing,
            'ticket_sale_rule' => $ticket_sale_rule,
            'custom_seats' => $custom_seats,
            'dispatch_mode' => $dispatch_mode,
            'driver_schedules' => $driver_schedules,
            'enable_driver_cycle' => intval($enable_driver_cycle),
        );
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/dingzhikeyun/schedules", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success') {
            $ret->ret = true;
            $ret->data = $results['data'];
            $ret->count = count($results['data']);
        } else {
            $ret->ret = false;
            $ret->data = $results['message'];
        }
        return $this->output($ret);
    }

    /**
     * 添加班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $start_time 发车时间
     * @param float $price 销售价格
     * @param string $market_price 市场价格
     * @param string $channel_price 渠道价格
     * @param string $travel_time 目的地名称
     * @param string $driver_d 司机id
     */
    public function doCreateLineClassTrain($mchid, $line_class_id, $start_time, $market_price, $price, $channel_price, $travel_time = null, $driver_id)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }

        $startTime = strtotime($start_time);
        if ($startTime < time()) {
            return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
        }
        $lineClassR = $this->find(array('id' => $line_class_id), 'LineClass');
        if (!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if ($driverR->ret) {
            $this->data['remain_tickets'] = $driverR->data['total_seating'] - 1;
            $this->data['total_tickets'] = $driverR->data['total_seating'] - 1;
        } else {
            return $this->output(new ResultModel(false, "添加失败"));
        }

        try {
            $this->startTrans();
            $this->data['type'] = 0;
            $this->data['line_class_train_no'] = $this->createLineClassTrainNo($startTime);
            $this->data['branchid'] = $lineClassR->data['branchid'];
            $r = $this->add('LineClassTrain');
            if ($r->ret) {
                $this->commitTrans();
                $this->doAddLog("添加班次：" . $r->data . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
                return $this->output(new ResultModel(true, "添加成功"));
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "添加失败"));
        }
        return $this->output($r);
    }

    /**
     * 批量添加班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $class_train_date 班次日期
     * @param mixed $line_class_trains 班次信息
     */
    public function doCreateLineClassTrains($mchid, $line_class_id, $class_train_date, $line_class_trains)
    {
        $lineClassR = $this->find(array('id' => $line_class_id), 'LineClass');
        if (!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $line_class_train_data['line_class_id'] = $line_class_id;
        $line_class_train_data['mchid'] = $mchid;
        $line_class_train_data['branchid'] = $lineClassR->data['branchid'];

        $line_class_trains_arr = json_decode($line_class_trains, true);
        $this->startTrans();
        foreach ($line_class_trains_arr as $k => $line_class_train_arr) {
            if (is_array($line_class_train_arr)) {
                $line_class_train_data['start_time'] = $class_train_date . ' ' . $line_class_train_arr['start_time'];
                $startTime = strtotime($line_class_train_data['start_time']);
                if ($startTime < time()) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
                }
                $line_class_train_data['start_time'] = date('Y-m-d H:i:s', $startTime);
                $line_class_train_data['market_price'] = $line_class_train_arr['market_price'];
                $line_class_train_data['price'] = $line_class_train_arr['price'];
                $line_class_train_data['channel_price'] = $line_class_train_arr['channel_price'];
                if (!$this->floatgtre($line_class_train_data['price'], $line_class_train_data['channel_price'])) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
                }

                $line_class_train_data['travel_time'] = $line_class_train_arr['travel_time'];
                $line_class_train_data['driver_id'] = $line_class_train_arr['driver_id'];
                $line_class_train_data['type'] = 0;

                $driverR = $this->find(array('driver_id' => $line_class_train_data['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $line_class_train_data['remain_tickets'] = $driverR->data['total_seating'] - 1;
                    $line_class_train_data['total_tickets'] = $driverR->data['total_seating'] - 1;
                } else {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }

                $line_class_train_data['line_class_train_no'] = $this->createLineClassTrainNo($startTime);

                $lineClassTrainA = $this->add('LineClassTrain', $line_class_train_data);
                if (!$lineClassTrainA->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "添加失败"));
                }
            } else {
                $this->transRollback();
                return $this->output(new ResultModel(false, "添加失败"));
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量添加班次,操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode([$mchid, $line_class_id, $class_train_date, $line_class_trains]));
        return $this->output(new ResultModel(true, "批量添加成功"));
    }

    /**
     * 修改班次
     * @param int $line_class_train_id 班次
     * @param int $mchid 商户id
     * @param int $line_class_id 班车线路
     * @param string $start_time 发车时间
     * @param float $price 销售价格
     * @param string $market_price 市场价格
     * @param string $channel_price 渠道价格
     * @param string $travel_time 目的地名称
     * @param string $driver_d 司机id
     */
    public function doUpdateLineClassTrain($line_class_train_id, $mchid, $start_time, $market_price, $price, $channel_price, $travel_time = null, $driver_id)
    {
        if (!$this->floatgtre($price, $channel_price)) {
            return $this->output(new ResultModel(false, "渠道价格不能大于销售价格"));
        }

        $startTime = strtotime($start_time);
        if ($startTime < time()) {
            return $this->output(new ResultModel(false, "发车时间不能小于当前时间"));
        }

        $lineClassTrainR = $this->find(array('id' => $line_class_train_id, 'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
        if (!$lineClassTrainR->ret) {
            return $this->output(new ResultModel(false, "线路不存在"));
        }

        if ($lineClassTrainR->data['driver_id'] != $driver_id) {
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            if ($driverR->ret) {
                $this->data['remain_tickets'] = $driverR->data['total_seating'] - 1;
                $this->data['total_tickets'] = $driverR->data['total_seating'] - 1;
            } else {
                return $this->output(new ResultModel(false, "修改失败"));
            }
        }

        try {
            $this->startTrans();
            $lineClassTrainS = $this->save('LineClassTrain');
            if ($lineClassTrainS->ret) {
                $this->commitTrans();
                $this->doAddLog("修改班次:" . $line_class_train_id . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode($this->data));
                return $this->output(new ResultModel(true, "修改成功"));
            } else {
                return $this->output(new ResultModel(false, "修改失败"));
            }
        } catch (Exception $e) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "修改失败"));
        }

        return $this->output($r);
    }

    /**
     * 删除班次
     * @param int $line_class_train_id 班次ID
     */
    public function doDeleteLineClassTrain($line_class_train_id)
    {
        $this->checkLineClassTrainOrder($line_class_train_id);
        //删除班次
        $r = $this->save('LineClassTrain', array('line_class_train_id' => $line_class_train_id, 'is_del' => 1));
        $this->doAddLog("删除班次：" . $line_class_train_id . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
        return $this->output($r);
    }

    protected function checkLineClassTrainOrder($trainId)
    {
        $r = M('Order')->where(['mchid' => $this->state->user_id, 'line_id' => $trainId])->find();
        if ($r) {
            return $this->output(new ResultModel(false, "该班次已经有乘客下单，无法继续删除"));
        }
    }

    /**
     * 恢复班次
     * @param int $line_class_train_id 班次ID
     */
    public function doRestoreLineClassTrain($line_class_train_id)
    {
        //恢复班次
        $r = $this->save('LineClassTrain', array('line_class_train_id' => $line_class_train_id, 'is_del' => 0));
        $this->doAddLog("恢复班次：" . $line_class_train_id . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info());
        return $this->output($r);
    }


    /**
     * 获取代办列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doAgencyList($fields = null, $mchid, $branchid = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $mchid;

        if (!empty($branchid) && $branchid) {
            $where['branchid'] = $branchid;
        }

        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'Agency');

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key]['mchname'] = $this->find(array('admin_id' => $value['mchid']), "Admin")->data['mchname'];
                $r->data[$key]['branch_name'] = $this->find(array('admin_id' => $value['branchid']), "Admin")->data['mchname'];
            }
        }
        return $this->output($r);
    }

    /**
     * 添加代办
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     */
    public function doAddAgency($name, $branch, $summary, $price, $channel_price, $rate = 0, $status = 0, $start_appointment_time = null, $end_appointment_time = null, $business_time_type = null, $set_order_time = 0)
    {

        $data['mchid'] = $this->state->user_id;
        if (!$data['mchid']) {
            return $this->output(new ResultModel(false, "身份异常..."));
        }
        $data['name'] = $name;
        $data['branchid'] = $branch;
        $data['summary'] = $summary;
        $data['price'] = $price;
        $data['channel_price'] = $channel_price;
        $data['rate'] = $rate;
        $data['status'] = $status;
        $data['is_del'] = 0;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time'] = $set_order_time;
        $data['business_time_type'] = $business_time_type;
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $r = $this->add('Agency', $data);

        $this->doAddLog("添加代办");
        return $this->output($r);
    }

    /**
     * 修改代办
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $channel_price 渠道价格
     * @param float $rate 线路提成比例
     */
    public function doEditAgency($id, $name, $branch, $summary, $price, $channel_price, $rate = 0, $status = 0, $start_appointment_time = null, $end_appointment_time = null, $business_time_type = null, $set_order_time = 0)
    {

        $agencyR = $this->find(array('agency_id' => $id, 'is_del' => 0), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $data['agency_id'] = $id;
        $data['name'] = $name;
        $data['branchid'] = $branch;
        $data['summary'] = $summary;
        $data['price'] = $price;
        $data['channel_price'] = $channel_price;
        $data['rate'] = $rate;
        $data['status'] = $status;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time'] = $set_order_time;
        $data['business_time_type'] = $business_time_type;
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $r = $this->save('Agency', $data);

        $this->doAddLog("修改代办");
        return $this->output($r);
    }

    /**
     * 代办--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteAgency($agency_id)
    {
        $agencyR = $this->find(array('agency_id' => $agency_id, 'is_del' => 0), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $r = $this->save('Agency', array('agency_id' => $agency_id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除代办");
        return $this->output($r);
    }

    /**
     * 获取带货列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doTakeGoodsPriceList($fields = null, $page = 1, $size = 10)
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        $r = $this->select($where, $page, $size, 'create_time desc', 'TakeGoodsPrice');

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key]['mchname'] = $this->find(array('admin_id' => $value['mchid']), "Admin")->data['mchname'];
            }
        }
        return $this->output($r);
    }


    /**
     * 添加带货
     * @param int $branchid 分台id
     * @param string name 计费标准名称
     * @param string $summary 线路简介说明
     * @param int $base_price 基础价格
     * @param int $base_weight 基础重量
     * @param int $base_kilometre 基础距离
     * @param int $plus_weight_price 每公斤增加费用
     * @param int $plus_kilometre_price 每公里增加费用
     */
    public function doAddTakeGoodsPrice($name, $base_price, $summary, $base_weight, $base_kilometre, $plus_weight_price, $plus_kilometre_price, $channel_rofit_price = null, $rate = 0, $start_appointment_time = null, $end_appointment_time = null, $business_time_type = null, $set_order_time = 0)
    {

        $data['mchid'] = $this->state->user_id;
        if (!$data['mchid']) {
            return $this->output(new ResultModel(false, "身份异常..."));
        }
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }
        if (!$this->isEmpty($base_weight)) {
            return $this->output(new ResultModel(false, "基础重量不能为空..."));
        }
        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }
        $data['rate'] = $rate;
        $data['is_del'] = 0;
        $data['name'] = $name;
        $data['summary'] = $summary;
        $data['base_weight'] = $base_weight;
        $data['base_price'] = $base_price;
        $data['base_kilometre'] = $base_kilometre;
        $data['plus_weight_price'] = $plus_weight_price;
        $data['plus_kilometre_price'] = $plus_kilometre_price;
        $data['channel_rofit_price'] = $channel_rofit_price;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time'] = $set_order_time;
        $data['business_time_type'] = $business_time_type;
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $r = $this->add('TakeGoodsPrice', $data);
        $this->doAddLog("添加带货");
        return $this->output($r);
    }

    /**
     * 修改带货
     * @param int $branchid 分台id
     * @param string $summary 线路说明
     * @param float $price 线路价格
     * @param float $rate 线路提成比例
     */
    public function doEditTakeGoodsPrice($id, $name, $base_price, $summary, $base_weight, $base_kilometre, $plus_weight_price, $plus_kilometre_price, $channel_rofit_price = null, $rate = 0, $start_appointment_time = null, $end_appointment_time = null, $business_time_type = null, $set_order_time = 0)
    {

        $agencyR = $this->find(array('take_goods_price_id' => $id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该带货不存在..."));
        }
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }
        if (!$this->isEmpty($base_weight)) {
            return $this->output(new ResultModel(false, "基础重量不能为空..."));
        }
        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }
        $data['take_goods_price_id'] = $id;
        $data['rate'] = $rate;
        $data['name'] = $name;
        $data['summary'] = $summary;
        $data['base_weight'] = $base_weight;
        $data['base_price'] = $base_price;
        $data['base_kilometre'] = $base_kilometre;
        $data['plus_weight_price'] = $plus_weight_price;
        $data['plus_kilometre_price'] = $plus_kilometre_price;
        $data['channel_rofit_price'] = $channel_rofit_price;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['set_order_time'] = $set_order_time;
        $data['business_time_type'] = $business_time_type;
        $data['start_appointment_time'] = $start_appointment_time;
        $data['end_appointment_time'] = $end_appointment_time;
        $r = $this->save('TakeGoodsPrice', $data);

        $this->doAddLog("修改代办");
        return $this->output($r);
    }

    /**
     * 带货--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteTakeGoodsPrice($tgp_id)
    {
        $agencyR = $this->find(array('take_goods_price_id' => $tgp_id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该代办不存在..."));
        }
        $r = $this->save('TakeGoodsPrice', array('take_goods_price_id' => $tgp_id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除带货");
        return $this->output($r);
    }

    /**
     * 带货--设为默认
     * @param int $agency_id 代办ID
     */
    public function doDefaultTakeGoodsPrice($tgp_id)
    {
        $adminR = $this->find(array('admin_id' => $this->state->user_id, 'group_id' => 2), 'Admin');
        if (!$adminR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在..."));
        }
        $agencyR = $this->select(array('mchid' => $adminR->data['admin_id'], 'is_del' => 0), null, null, null, 'TakeGoodsPrice');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "请添加带货..."));
        }

        $tgpR = $this->find(array('take_goods_price_id' => $tgp_id, 'is_del' => 0), 'TakeGoodsPrice');
        if (!$tgpR->ret) {
            return $this->output(new ResultModel(false, "该带货不存在..."));
        }

        M('take_goods_price')->where(array('mchid' => $adminR->data['admin_id']))->save(array('is_default' => 0)); //将该商户所有带货数据设置为0
        $r = $this->save('TakeGoodsPrice', array('take_goods_price_id' => $tgp_id, 'is_default' => 1, 'update_tine' => date("Y-m-d H:i:s"))); //设置为默认带货

        $this->doAddLog("带货默认设置");
        return $this->output($r);
    }

    /**
     * 获取类型列表
     * @param null $fields 字段
     * @param int $page
     * @param int $size
     *
     */
    public function doLineCateogyrList($fields = null, $page = 1, $size = 10, $line_category_id = 0)
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        if ($line_category_id) {
            $where['category_id'] = $line_category_id;
        }
        $r = $this->select($where, $page, $size, 'id asc', 'LineCategory');
        foreach ($r->data as $key => $value) {
            $cateName = M('LineCategoryBusiness')->where(['line_category_business_id' => $value['line_category_business_id']])->find();
            if ($cateName) {
                $r->data[$key]['category_name'] = $cateName['name'];
            } else {
                $r->data[$key]['category_name'] = '<span style="color: red">未设置场景类型</span>';
            }
        }
        return $this->output($r);
    }

    /**
     * 添加类型
     * @param string $title 名称
     */
    public function doAddLineCategory($title, $category_id)
    {
        $data['mchid'] = $this->state->user_id;
        if (mb_strlen($title, "utf-8") > 30) {
            return $this->output(new ResultModel(false, "标题长度最多是30汉字..."));
        }
        $agencyR = $this->find(array('mchid' => $data['mchid'], 'is_del' => 0), 'Admin');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在..."));
        }
        $data['title'] = $title;
        $data['is_del'] = 0;
        $data['create_time'] = date("Y-m-d H:i:s");
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['line_category_business_id'] = $category_id;
        $r = $this->add('LineCategory', $data);

        $this->doAddLog("添加类型");
        return $this->output($r);
    }

    public function doLinePricingList($fields = null, $page = 1, $size = 20, $line_category_id = 7)
    {
        $where['mchid'] = $this->state->user_id;
        $where['_string'] = " deleted_at IS NULL ";
        if ($line_category_id) {
            $where['business_id'] = $line_category_id;
        }
        $r = $this->select($where, $page, $size, 'id desc', 'LineVehiclePricing');
        return $this->output($r);
    }

    public function doAddLinePricing($title, $category_id = 7)
    {
        $data['mchid'] = $this->state->user_id;
        if (mb_strlen($title, "utf-8") > 20) {
            return $this->output(new ResultModel(false, "标题长度最多是20汉字..."));
        }
        $agencyR = $this->find(array('mchid' => $data['mchid'], 'is_del' => 0), 'Admin');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在..."));
        }
        $data['title'] = $title;
        $data['created_at'] = date("Y-m-d H:i:s");
        $data['updated_at'] = date("Y-m-d H:i:s");
        $data['business_id'] = 7;
        $data['kuaiche_type'] = 2;
        $r = $this->add('LineVehiclePricing', $data);

        $this->doAddLog("添加计价规则");
        return $this->output($r);
    }

    public function doEditLinePricing($id, $title, $category_id = 7)
    {
        $agencyR = $this->find(array('id' => $id), 'LineVehiclePricing');
        if (mb_strlen($title, "utf-8") > 20) {
            return $this->output(new ResultModel(false, "标题长度最多是20汉字..."));
        }
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该计价规则不存在..."));
        }
        $data['mchid'] = $this->state->user_id;
        $data['id'] = $id;
        $data['title'] = $title;
        $data['updated_at'] = date("Y-m-d H:i:s");
        $data['business_id'] = $category_id;
        $r = $this->save('LineVehiclePricing', $data);

        $this->doAddLog("修改计价规则");
        return $this->output($r);
    }

    public function doDeleteLinePricing($id)
    {
        $agencyR = $this->find(array('id' => $id), 'LineVehiclePricing');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该计价规则不存在..."));
        }
        $r = $this->save('LineVehiclePricing', array('id' => $id, 'updated_at' => date("Y-m-d H:i:s"), 'deleted_at' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除计价规则");
        return $this->output($r);
    }

    /**
     * 修改类型
     * @param int $branchid 分台id
     * @param string $title 名称
     */
    public function doEditLineCategory($id, $title, $category_id)
    {
        $agencyR = $this->find(array('id' => $id, 'is_del' => 0), 'LineCategory');
        if (mb_strlen($title, "utf-8") > 30) {
            return $this->output(new ResultModel(false, "标题长度最多是30汉字..."));
        }
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该类型不存在..."));
        }
        $data['mchid'] = $this->state->user_id;
        $data['id'] = $id;
        $data['title'] = $title;
        $data['update_time'] = date("Y-m-d H:i:s");
        $data['line_category_business_id'] = $category_id;
        $r = $this->save('LineCategory', $data);

        $this->doAddLog("修改类型");
        return $this->output($r);
    }

    /**
     * 类型--删除
     * @param int $agency_id 代办ID
     */
    public function doDeleteLineCateogry($id)
    {
        $agencyR = $this->find(array('id' => $id, 'is_del' => 0), 'LineCategory');
        if (!$agencyR->ret) {
            return $this->output(new ResultModel(false, "该类型不存在..."));
        }
        $r = $this->save('LineCategory', array('id' => $id, 'is_del' => 1, 'update_tine' => date("Y-m-d H:i:s")));
        $this->doAddLog("删除类型");
        return $this->output($r);
    }

    /**
     * 修改顺风车计价规则
     * @param string $name 顺风车计价标题
     * @param string $summary 线路说明
     * @param float $base_price 起步价格
     * @param float $base_kilometre 基础公里数
     * @param float $plus_kilometre_price 超出公里数后的公里单价
     */
    public function doEditMileagePrice($name, $summary, $base_price, $base_kilometre, $plus_kilometre_price)
    {
        if (!$this->isEmpty($base_price)) {
            return $this->output(new ResultModel(false, "基础价格不能为空..."));
        }

        if (!$this->isEmpty($base_kilometre)) {
            return $this->output(new ResultModel(false, "基础距离不能为空..."));
        }

        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $milePriceR = $this->find($where, 'MileagePrice');
        $r = new \ResultModel(false);
        if (!$milePriceR->ret) {
            //添加
            $data['name'] = $name;
            $data['summary'] = $summary;
            $data['base_price'] = $base_price;
            $data['base_kilometre'] = $base_kilometre;
            $data['plus_kilometre_price'] = $plus_kilometre_price;
            $data['is_default'] = 1;
            $data['mchid'] = $this->state->user_id;
            $r = $this->add('MileagePrice', $data);
        } else {
            //修改
            $data['mileage_price_id'] = $milePriceR->data['mileage_price_id'];
            $data['name'] = $name;
            $data['summary'] = $summary;
            $data['base_price'] = $base_price;
            $data['base_kilometre'] = $base_kilometre;
            $data['plus_kilometre_price'] = $plus_kilometre_price;
            $r = $this->save('MileagePrice', $data);
        }

        $this->doAddLog("修改顺风车计价规则");
        return $this->output($r);
    }

    /**
     * 添加班线车
     *
     * @return void
     */
    public function doSaveLineClassIn()
    {
        $post = $_POST;
        $class = $this->combinationPost($post);
        $mchid = '';
        if ($this->admin_group_id == 3) {
            $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            $mchid = $mchR->data['parent_admin_id'];
            $class['mchid'] = $mchR->data['parent_admin_id'];
        } else {
            $class['mchid'] = $this->state->user_id;
            $mchid = $this->state->user_id;
        }
        M()->startTrans();
        # 多组固定发车时间
        $startingTimes = array_filter(array_unique($post['starting_time']));
        usort($startingTimes, "compareTimes");
        $class['starting_time'] = json_encode($startingTimes);
        $class['start_time'] = $startingTimes[0];
        # 多组滚动发车时段
        $intervalTimes = $this->validateIntervalTime($post);
        $class['starting_interval_time'] = json_encode($intervalTimes['starting_interval_time']);
        $class['start_earliest_time'] = $intervalTimes['start_earliest_time'];
        $class['end_latest_time'] = $intervalTimes['end_latest_time'];
        $class['interval_minutes'] = $post['interval_minutes'];
        $class['is_supplementary_time'] = $post['is_supplementary_time'];
        $class['sub_business_type'] = $post['sub_business_type'];
        $class['stop_sale_minutes'] = $post['stop_sale_minutes'];
        $class['transport_level'] = json_encode($post['transport_level']);
        # 班线是否允许积分抵扣 1:允许 0:不允许
        $class['support_points_deduction'] = intval($post['support_points_deduction']);
        $class['is_required_flight_number'] = intval($post['is_required_flight_number']);
        # 线路方向：0-未知，1-上行，2-下行
        $class['direction'] = intval($post['direction']);

        //多个任意上车点包含价格设置
        if (isset($post['pickup_polygons'])) {

            $pickupPolygonsData = $post['pickup_polygons'];
            $pickupPolygons = [];

            foreach ($pickupPolygonsData['title'] as $index => $title) {
                $pickupPolygons[] = [
                    'seq' => $index + 1, // 直接使用索引+1作为seq，确保不重复
                    'title' => $title,
                    'polygons' => $pickupPolygonsData['polygons'][$index],
                    'price' => (float)$pickupPolygonsData['price'][$index],
                ];
            }

            $class['pickup_polygons'] = json_encode($pickupPolygons, JSON_UNESCAPED_UNICODE);
        }

        //多个任意下车点包含价格设置
        if (isset($post['dropoff_polygons'])) {

            $dropoffPolygonsData = $post['dropoff_polygons'];
            $dropoffPolygons = [];

            foreach ($dropoffPolygonsData['title'] as $index => $title) {
                $dropoffPolygons[] = [
                    'seq' => $index + 1, // 直接使用索引+1作为seq，确保不重复
                    'title' => $title,
                    'polygons' => $dropoffPolygonsData['polygons'][$index],
                    'price' => (float)$dropoffPolygonsData['price'][$index],
                ];
            }
            $class['dropoff_polygons'] = json_encode($dropoffPolygons, JSON_UNESCAPED_UNICODE);
        }

        $rr = $this->add('LineClass', $class);


        if (!$rr->ret) {
            M()->rollback();
            $this->json(400, '添加失败');
        }

        $line_class_id = $rr->data;
        $point['line_class_id'] = $line_class_id;
        $point['is_return'] = 1;

        //设置了返程
        if (!empty($class['is_return'])) {

            if ($class['return_start_address_type'] == 1) {
                $return_start_lgtltt = explode(',', $post['return_start_lgtltt']);
                //添加至站点
                $point['longitude'] = $return_start_lgtltt[0];
                $point['latitude'] = $return_start_lgtltt[1];
                $point['alias'] = $post['return_start_alias1'];
                $point['type'] = 1;
                $r = M('LineClassPoint')->add($point);

                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
            if ($class['return_end_address_type'] == 1) {
                //添加至站点
                $return_end_lgtltt = explode(',', $post['return_end_lgtltt']);
                //添加至站点
                $point['longitude'] = $return_end_lgtltt[0];
                $point['latitude'] = $return_end_lgtltt[1];
                $point['alias'] = $post['return_end_alias1'];
                $point['type'] = 2;
                $r = M('LineClassPoint')->add($point);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        }

        $point['is_return'] = 0;

        $currentDatetime = date('Y-m-d H:i:s', time());
        // 发车时间类型start_address_type：1-固定发车时间；2-滚动发车时段
        if ($class['start_address_type'] == 1) {
            //循环添加数据
            foreach ($post['up_alias1'] as $key => $value) {
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['up_use_time1'][$key];
                //增加补充费用
                $point['additional_fee'] = $post['additional_fee1'][$key];
                $point['create_time'] = $currentDatetime;
                $point['update_time'] = $currentDatetime;
                $r = M('LineClassPoint')->add($point);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        } else {
            //循环添加数据
            foreach ($post['up_alias2'] as $key => $value) {
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat2'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['create_time'] = $currentDatetime;
                $point['update_time'] = $currentDatetime;
                $r = M('LineClassPoint')->add($point);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        }

        if ($class['end_address_type'] == 1) {
            foreach ($post['down_alias1'] as $key => $value) {
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['down_use_time1'][$key];
                //增加补充费用
                $point['additional_fee'] = $post['additional_fee1'][$key];
                $point['create_time'] = $currentDatetime;
                $point['update_time'] = $currentDatetime;
                $r = M('LineClassPoint')->add($point);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        } else {
            foreach ($post['down_alias2'] as $key => $value) {
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat2'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['create_time'] = $currentDatetime;
                $point['update_time'] = $currentDatetime;
                $r = M('LineClassPoint')->add($point);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        }
        //场景分类

        if (!empty($post['line_cat_ids'])) {
            $LiCa['mchid'] = $mchid;
            $LiCa['line_class_id'] = $line_class_id;
            foreach ($post['line_cat_ids'] as $key => $value) {
                $LiCa['line_category_id'] = $value;
                //为空添加 LineClassFkLineCategory
                // !$this->add("line_class_fk_line_category", $LiCa)->ret
                $r = M('line_class_fk_line_category')->add($LiCa);
                if (!$r) {
                    M()->rollback();
                    $this->json(400, '添加失败');
                }
            }
        }
        //增加途经点
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/lines/{$line_class_id}/via_points", 'post', array(
            'via_province_code' => $class['via_province_code'],
            'via_city_code' => $class['via_city_code'],
            'via_area_code' => $class['via_area_code'],
            'via_name' => $class['via_name'],
        ));
        $viaPoints = json_decode($response, true);
        if ($viaPoints['status'] == 'error') {
            M()->rollback();
            $this->json(400, $viaPoints['message']);
        }
        M()->commit();
        $this->doAddLog("添加班线车:成功添加班线车ID" . $rr->data . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode($class));
        $this->json(200, '添加成功');
    }


    /**
     *  滚动发车时段数据校验
     *
     * @param array $post
     * @return mixed
     */
    public function validateIntervalTime($post)
    {
        $intervalTimes = [];
        # 多组滚动发车时段
        $startIntervalTimes = array_filter(array_unique($post['starting_interval_time']));
        $endIntervalTimes = array_filter(array_unique($post['end_interval_time']));

        if (count($startIntervalTimes) != count($endIntervalTimes)) {
            $this->json(400, '滚动发车时段，必需配置每段最早发车时间与最晚发车时间或者最早最晚发车时间相同');
        }

        for ($i = 0; $i < count($startIntervalTimes); $i++) {
            $startTimetamp = strtotime($startIntervalTimes[$i]);
            $endTimetamp = strtotime($endIntervalTimes[$i]);
            $tempStartTimetamp = strtotime($endIntervalTimes[$i + 1]);
            $tempEndTimetamp = strtotime($endIntervalTimes[$i + 1]);

            if ($startTimetamp > $endTimetamp) {
                $this->json(400, sprintf('滚动发车时段，第%d行发车时段最早发车时间不能大于最晚发车时间', $i + 1));
            }

            if ($tempStartTimetamp < $tempEndTimetamp) {
                $this->json(400, sprintf('滚动发车时段，第%d行发车时段最晚发车时间已设置再其他范围', $i + 2));
            }

            $intervalTimes[] = [
                $startIntervalTimes[$i] => $endIntervalTimes[$i]
            ];
        }
        return [
            'starting_interval_time' => $intervalTimes,
            'start_earliest_time' => min($startIntervalTimes),
            'end_latest_time' => max($endIntervalTimes)
        ];
    }

    /**
     * 更新班线车配置
     *
     * @return void
     */
    public function doEditLineClassIn()
    {
        $post = $_POST;
        $class = $this->combinationPost($post);
        $datatime = date('Y-m-d H:i:s');
        # 商户ID
        $mchid = '';
        if ($this->admin_group_id == 3) {
            $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            $mchid = $mchR->data['parent_admin_id'];
        } else {
            $mchid = $this->state->user_id;
        }
        # 班线是否存在
        $lineClassR = $this->find(['id' => $post['id'], 'mchid' => $this->mchid], 'LineClass');
        if (!$lineClassR->ret) {
            $this->json(400, '班线线路不存在或者被删除');
        }

        M()->startTrans();
        $class['id'] = $post['id'];
        $class['update_time'] = $datatime;
        $class['interval_minutes'] = $post['interval_minutes'];
        $class['is_supplementary_time'] = $post['is_supplementary_time'];
        $class['sub_business_type'] = $post['sub_business_type'];
        $class['stop_sale_minutes'] = $post['stop_sale_minutes'];
        $class['transport_level'] = json_encode($post['transport_level']);
        # 多组固定发车时间
        $startingTimes = array_filter(array_unique($post['starting_time']));
        usort($startingTimes, "compareTimes");
        $class['starting_time'] = json_encode($startingTimes);
        $class['start_time'] = $startingTimes[0];
        # 多组滚动发车时段
        $intervalTimes = $this->validateIntervalTime($post);
        $class['starting_interval_time'] = json_encode($intervalTimes['starting_interval_time']);
        $class['start_earliest_time'] = $intervalTimes['start_earliest_time'];
        $class['end_latest_time'] = $intervalTimes['end_latest_time'];
        # 班线是否允许积分抵扣 1:允许 0:不允许
        $class['support_points_deduction'] = intval($post['support_points_deduction']);
        $class['is_required_flight_number'] = intval($post['is_required_flight_number']);
        # 线路方向：0-未知，1-上行，2-下行
        $class['direction'] = intval($post['direction']);
        //多个任意上车点包含价格设置
        if (isset($post['pickup_polygons'])) {

            $pickupPolygonsData = $post['pickup_polygons'];
            $pickupPolygons = [];

            foreach ($pickupPolygonsData['title'] as $index => $title) {
                $pickupPolygons[] = [
                    'seq' => $index + 1, // 直接使用索引+1作为seq，确保不重复
                    'title' => $title,
                    'polygons' => $pickupPolygonsData['polygons'][$index],
                    'price' => (float)$pickupPolygonsData['price'][$index],
                ];
            }

            $class['pickup_polygons'] = json_encode($pickupPolygons, JSON_UNESCAPED_UNICODE);
        } else {
            $class['pickup_polygons'] = null;
        }

        //多个任意下车点包含价格设置
        if (isset($post['dropoff_polygons'])) {

            $dropoffPolygonsData = $post['dropoff_polygons'];
            $dropoffPolygons = [];

            foreach ($dropoffPolygonsData['title'] as $index => $title) {
                $dropoffPolygons[] = [
                    'seq' => $index + 1, // 直接使用索引+1作为seq，确保不重复
                    'title' => $title,
                    'polygons' => $dropoffPolygonsData['polygons'][$index],
                    'price' => (float)$dropoffPolygonsData['price'][$index],
                ];
            }
            $class['dropoff_polygons'] = json_encode($dropoffPolygons, JSON_UNESCAPED_UNICODE);
        } else {
            $class['dropoff_polygons'] = null;
        }

        $rr = $this->save('LineClass', $class);

        if (!$rr->ret) {
            M()->rollback();
            $this->json(400, '修改班线线路失败，请稍后再试');
        }

        $point['line_class_id'] = $post['id'];
        $point['is_return'] = 1;

        //设置了返程
        if (!empty($class['is_return'])) {

            if ($class['return_start_address_type'] == 1) {
                $return_start_lgtltt = explode(',', $post['return_start_lgtltt']);
                //修改至站点
                $point['longitude'] = $return_start_lgtltt[0];
                $point['latitude'] = $return_start_lgtltt[1];
                $point['alias'] = $post['return_start_alias1'];
                $point['type'] = 1;
                if (!empty($post['return_up_id'])) {
                    $point['id'] = $post['return_up_id'];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }

                if (!$r) {
                    M()->rollback();
                    $this->json(400, '修改失败');
                }
            }

            if ($class['return_end_address_type'] == 1) {
                //修改至站点
                $return_end_lgtltt = explode(',', $post['return_end_lgtltt']);
                //修改至站点
                $point['longitude'] = $return_end_lgtltt[0];
                $point['latitude'] = $return_end_lgtltt[1];
                $point['alias'] = $post['return_end_alias1'];
                $point['type'] = 2;
                if (!empty($post['return_down_id'])) {
                    $point['id'] = $post['return_down_id'];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }

                if (!$r) {
                    M()->rollback();
                    $this->json(400, '修改失败');
                }
            }
        }
        unset($point['id']);
        unset($point['update_time']);
        unset($point['create_time']);
        $point['is_return'] = 0;

        //上下车点类型变化，直接全部删除原有上下车点位
        if ($lineClassR->data['start_address_type'] != $class['start_address_type']) {
            M('LineClassPoint')->where(['line_class_id' => $post['id'], 'type' => 1])->delete();
        }
        if ($lineClassR->data['end_address_type'] != $class['end_address_type']) {
            M('LineClassPoint')->where(['line_class_id' => $post['id'], 'type' => 2])->delete();
        }
        //删除班线车固定上下车点
        if (!empty($post['delete_ids'])) {
            $where['id'] = array('in', rtrim($post['delete_ids'], ','));
            M('LineClassPoint')->where($where)->delete();
        }
        // 发车时间类型start_address_type：1-固定发车时间；2-滚动发车时段
        if ($class['start_address_type'] == 1) {
            //循环修改数据
            foreach ($post['up_alias1'] as $key => $value) {
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['up_use_time1'][$key];
                //增加补充费用
                $point['additional_fee'] = $post['up_additional_fee1'][$key];
                if (!empty($post['up_id'][$key])) {
                    $point['id'] = $post['up_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }

                if (!$r) {
                    M()->rollback();
                    $this->json(400, '修改失败');
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        } else {
            //循环修改数据
            foreach ($post['up_alias2'] as $key => $value) {
                $point['type'] = 1;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['up_lgtlat2'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];

                if (!empty($post['up_id'][$key])) {
                    $point['id'] = $post['up_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        }

        if ($class['end_address_type'] == 1) {
            foreach ($post['down_alias1'] as $key => $value) {
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat1'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                $point['use_time'] = $post['down_use_time1'][$key];
                //增加补充费用
                $point['additional_fee'] = $post['down_additional_fee1'][$key];
                if (!empty($post['down_id'][$key])) {
                    $point['id'] = $post['down_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        } else {
            foreach ($post['down_alias2'] as $key => $value) {
                $point['type'] = 2;
                $point['alias'] = $value;

                $tmpgl = explode(',', $post['down_lgtlat2'][$key]);
                $point['longitude'] = $tmpgl[0];
                $point['latitude'] = $tmpgl[1];
                if (!empty($post['down_id'][$key])) {
                    $point['id'] = $post['down_id'][$key];
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->save($point);
                } else {
                    $point['create_time'] = $datatime;
                    $point['update_time'] = $datatime;
                    $r = M('LineClassPoint')->add($point);
                }
                unset($point['id']);
                unset($point['update_time']);
                unset($point['create_time']);
            }
        }

        //场景分类
        $LiCa['mchid'] = $mchid;
        $LiCa['line_class_id'] = $post['id'];

        # 删除取消的场景分类
        if (empty($post['line_cat_ids'])) {
            M('line_class_fk_line_category')->where($LiCa)->delete();
        } else {
            $LiCa['line_category_id'] = array('not in', implode(",", $post['line_cat_ids']));
            M('line_class_fk_line_category')->where($LiCa)->delete();
        }
        if (!empty($post['line_cat_ids'])) {
            foreach ($post['line_cat_ids'] as $key => $value) {
                $LiCa['line_category_id'] = $value;
                $inn = M('line_class_fk_line_category')->where($LiCa)->find();

                //为空添加
                if (empty($inn)) {
                    $r = M('line_class_fk_line_category')->add($LiCa);
                    if (!$r) {
                        M()->rollback();
                        $this->json(400, '修改失败');
                    }
                }
            }
        }

        # 修改途经点
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/lines/{$post['id']}/via_points", 'put', array(
            'via_province_code' => $class['via_province_code'],
            'via_city_code' => $class['via_city_code'],
            'via_area_code' => $class['via_area_code'],
            'via_name' => $class['via_name'],
            '_method' => 'PUT',
        ));
        $viaPoints = json_decode($response, true);
        if ($viaPoints['status'] == 'error') {
            M()->rollback();
            $this->json(400, $viaPoints['message']);
        }
        M()->commit();

        # 增加操作日志
        $this->doAddLog("修改班线车:成功添加班线车ID" . $rr->data . ",操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), "原班线车数据：" . json_encode($lineClassR->data) . "更新数据：" . json_encode($class));
        $this->json(200, '修改班线线路成功');
    }


    protected function combinationPost($post)
    {
        $class = [
            "start_time_type"       => $post['start_time_type'],
            "branchid"              => $post['branch_id'],
            "price"                 => $post['price'],
            "channel_price"         => $post['channel_price'],
            "stop_sell_number"      => $post['stop_sell_number'],
            "stop_sell_time"        => $post['stop_sell_time'],
            "refund_time_set"       => $post['refund_time_set'],
            "return_start_time_type" => $post['return_start_time_type'],
            "seat_layout"           => $post['seat_layout'],
            'travel_time'           => trim($post['travel_time']),
        ];
        if ($post['start_time_type'] == 1) {
            $class['start_time'] = $post['start_time'];
        } else {
            $class['start_earliest_time'] = $post['start_earliest_time'];
            $class['end_latest_time'] = $post['end_latest_time'];
        }
        if (!empty($post['summary'])) {
            $class['summary'] = $post['summary'];
        }
        $arr = ['start', 'end', 'via'];

        if (!empty($post['end_polygon_use_time'])) {
            $class['end_polygon_use_time'] = (int)$post['end_polygon_use_time'];
        }

        foreach ($arr as $key => $value) {
            $class[$value . '_address_type']           = $post[$value . '_address_type'];
            $class[$value . '_province_code']          = $post[$value . '_province_code'];
            $class[$value . '_city_code']              = $post[$value . '_city_code'];
            $class[$value . '_area_code']              = $post[$value . '_area_code'];
            $class[$value . '_name']                   = $post[$value . '_name'];
            if ($post[$value . '_address_type'] == 2) {
                $class[$value . '_polygon'] = json_encode(explode('-', $post[$value . '_polygon']));
            }

            if (empty($post[$value . '_area_code']) && empty($post[$value . '_city_code'])) {
                $class[$value . '_address_code'] = $post[$value . '_province_code'];
            } elseif (!empty($post[$value . '_city_code']) && empty($post[$value . '_area_code'])) {
                $class[$value . '_address_code'] = $post[$value . '_city_code'];
            } else {
                $class[$value . '_address_code'] = $post[$value . '_area_code'];
            }
        }

        $date = date('Y-m-d');
        $time1 = strtotime($date . ' ' . '00:00:00');

        switch ($post['start_time_type']) {
            case 1:
                $time2 = strtotime($date . ' ' . $post['start_time']);
                break;
            case 2:
                $time2 = strtotime($date . ' ' . $post['start_earliest_time']);
                break;
            default:
                break;
        }

        $class['sort'] = (int)$time2 - (int)$time1;

        //包含自定义设置-返程
        if (!empty($post['is_return'])) {
            $class['return_start_address_type'] = $post['return_start_address_type'];
            $class['return_end_address_type']   = $post['return_end_address_type'];
            $class['return_time_number']        = $post['return_time_number'];

            if ($post['return_start_time_type'] == 1) {
                //固定发车时间
                $class['return_start_time'] = $post['return_start_time'];
            } else {
                //滚动发车时段
                $class['return_start_earliest_time'] = $post['return_start_earliest_time'];
                $class['return_end_latest_time'] = $post['return_end_latest_time'];
            }

            if ($class['return_start_address_type'] == 2) {
                //别名,范围
                $class['return_start_alias'] = $post['return_start_alias2'];
                $class['return_start_polygon'] = json_encode(explode('-', $post['return_']));
            }

            if ($class['return_end_address_type'] == 2) {
                //别名,范围
                $class['return_end_alias'] = $post['return_end_alias2'];
                $class['return_end_polygon'] = json_encode(explode('-', $post['return_end_polygon']));
            }

            $class['is_return'] = 1;
        } else {
            $class['is_return'] = 0;
        }

        $mchid = '';
        if ($this->admin_group_id == 3) {
            $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            $mchid = $mchR->data['parent_admin_id'];
        } else {
            $mchid = $this->state->user_id;
        }
        if ($mchid == 1116) {
            if ($class['start_city_code'] == 530300 && ($class['start_address_code'] == 530301 || $class['start_address_code'] == 530302)) {
                $class['center_start_latlng'] = '103.80063,25.487982'; //曲靖市 曲靖官房大酒店
                $class['return_center_end_latlng'] = '103.80063,25.487982'; //曲靖市 曲靖官房大酒店
            } else {
                $center_start_latlngR = $this->getAddressCenterLatLng($class['start_province_code'], $class['start_city_code'], $class['start_area_code']);
                if ($center_start_latlngR->ret) {
                    $class['center_start_latlng'] = $center_start_latlngR->data['location'];
                    $class['return_center_end_latlng'] = $center_start_latlngR->data['location'];
                }
            }
            if ($class['end_city_code'] == 530300 && ($class['end_address_code'] == 530301 || $class['end_address_code'] == 530302)) {
                $class['center_end_latlng'] = '103.80063,25.487982'; //曲靖市 曲靖官房大酒店
                $class['return_center_start_latlng'] = '103.80063,25.487982'; //曲靖市 曲靖官房大酒店
            } else {
                $center_end_latlngR = $this->getAddressCenterLatLng($class['end_province_code'], $class['end_city_code'], $class['end_area_code']);
                if ($center_end_latlngR->ret) {
                    $class['center_end_latlng'] = $center_end_latlngR->data['location'];
                    $class['return_center_start_latlng'] = $center_end_latlngR->data['location'];
                }
            }
        } else {
            $center_start_latlngR = $this->getAddressCenterLatLng($class['start_province_code'], $class['start_city_code'], $class['start_area_code']);
            if ($center_start_latlngR->ret) {
                $class['center_start_latlng'] = $center_start_latlngR->data['location'];
                $class['return_center_end_latlng'] = $center_start_latlngR->data['location'];
            }

            $center_end_latlngR = $this->getAddressCenterLatLng($class['end_province_code'], $class['end_city_code'], $class['end_area_code']);
            if ($center_end_latlngR->ret) {
                $class['center_end_latlng'] = $center_end_latlngR->data['location'];
                $class['return_center_start_latlng'] = $center_end_latlngR->data['location'];
            }
        }

        $class['is_seat_selection'] = $post['is_seat_selection'];
        if ($class['is_seat_selection'] == 0) {
            $class['seat_price'] = null;
            $class['car_seats'] = null;
            $class['seat_layout'] = null;
        } else {
            if (!empty($post['car_seats'])) {
                $seatPriceR = $this->seatPrice($post['car_seats'], $post['seat_name'], $post['seat_price']);
                if (!$seatPriceR->ret) {
                    return $this->json(400, $seatPriceR->data);
                }
                $class['seat_price'] = addslashes(json_encode($seatPriceR->data));
                $class['car_seats'] = $post['car_seats'];
                $class['seat_layout'] = $post['seat_layout'];
            }
        }
        return $class;
    }
    public function json($code, $msg)
    {
        echo json_encode([
            'code' => $code,
            'msg' => $msg,
        ]);
        die;
    }

    /**
     * 获取中心坐标
     * @param $province_code
     * @param $city_code
     * @param $area_code
     */
    protected function getAddressCenterLatLng($province_code, $city_code, $area_code)
    {
        $ret = new ResultModel(false, '获取失败');
        if (empty($province_code) && empty($city_code) && empty($area_code)) {
            return $ret;
        }
        $adcode = $province_code;
        $adcodeR = $this->find(['address_id' => $area_code], 'GdRegion');
        if ($adcodeR->ret) {
            if ($adcodeR->data['name'] == '市辖区') {
                $adcodeR = $this->find(['address_id' => $city_code], 'GdRegion');
                if ($adcodeR->ret) {
                    if ($adcodeR->data['name'] == '市辖区') {
                        $adcode = $province_code;
                    } else {
                        $adcode = $city_code;
                    }
                }
            } else {
                $adcode = $area_code;
            }
        } else {
            $adcodeR = $this->find(['address_id' => $city_code], 'GdRegion');
            if ($adcodeR->ret) {
                if ($adcodeR->data['name'] == '市辖区') {
                    $adcode = $province_code;
                } else {
                    $adcode = $city_code;
                }
            }
        }
        $ret = $this->getGdLocationByAdcode($adcode);
        return $ret;
    }


    /**
     * 设置退款费率
     * @return null|resultModel
     */
    public function doUpdateRefundTicketConfig()
    {
        $refund_ticket_time = $_POST['refund_ticket_time'];
        $refund_ticket_ratio = $_POST['refund_ticket_ratio'];

        //数据校验
        $count = count($refund_ticket_time);
        for ($i = 0; $i < $count; $i++) {
            if ($refund_ticket_time[$i] < 0) {
                return $this->output(new ResultModel(false, "设置失败，分钟数设置不正确"));
            }

            if ($refund_ticket_ratio[$i] < 0 || $refund_ticket_ratio[$i] > 100) {
                return $this->output(new ResultModel(false, "设置失败，扣款比例设置不正确（请设置0~100之间的数字）"));
            }
        }

        $this->startTrans();
        $delWhere['mchid'] = $this->mchid;
        $delRefundTicketConfig = M('RefundTicketConfig')->where($delWhere)->delete();
        $data = [];
        $dataTime = [];
        for ($i = 0; $i < $count; $i++) {
            $data[$i]['mchid'] = $this->mchid;
            $data[$i]['refund_ticket_time'] = $refund_ticket_time[$i];
            $data[$i]['refund_ticket_ratio'] = $refund_ticket_ratio[$i];
            $data[$i]['create_time'] = $data[$i]['update_time'] = date('Y-m-d H:i:s', time());
            if (in_array($data[$i]['refund_ticket_time'], $dataTime)) {
                $this->transRollback();
                return $this->output(new ResultModel(false, "同一时间重复设置"));
            }
            $dataTime[] = $refund_ticket_time[$i];
        }
        if (!M('RefundTicketConfig')->addAll($data)) {
            $this->transRollback();
            return $this->output(new ResultModel(false, "设置失败"));
        }

        $this->commitTrans();
        $this->doAddLog("设置退款费率,操作账号：" . $this->state->user_id . ",本次操作IP地址：" . get_client_ip() . ",浏览器：" . get_browser_info(), json_encode($data));

        return $this->output(new ResultModel(true, "设置成功"));
    }
}

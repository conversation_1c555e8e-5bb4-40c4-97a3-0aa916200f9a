<?php

import('@/Action/Admin/OrderManagerAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
/**
 * Created by PhpStorm.
 * User: xhr
 * Date: 2017/5/19
 * Time: 14:57
 */
class PackagePageAction extends AdminCoreAction
{
    //列表
    public function packagelistPage(){
        $this->display('Tpl/Admin/Package/index.html');
    }
    //添加
    public function packageaddPage(){

        $mchlist = M("admin")->where(array("is_del"=>0,"group_id"=>2))->select();

        $this->assign("mchlist", $mchlist);
        $this->display('Tpl/Admin/Package/add.html');
    }
    //编辑
    public function packageeditPage($package_id){

        $vomptsR = $this->find(array('id'=>$package_id),'ValueOfMerchantPackageTypeStatus','admin_id,stsv_id,id');
        $stsv = $this->find(array('id'=>$vomptsR->data['stsv_id']),'SetTypeStatusValue','value,category_id');
        $vomptsR->data['num'] = $stsv->data['value'];
        $vomptsR->data['type'] = $stsv->data['category_id'];

        $mchlist = M("admin")->where(array("is_del"=>0,"group_id"=>2))->select();

        $this->assign("mchlist", $mchlist);
        $this->assign("data", $vomptsR->data);

        $this->display('Tpl/Admin/Package/edit.html');

    }
}
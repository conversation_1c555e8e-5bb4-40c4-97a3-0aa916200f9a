<?php



class CardAction extends AdminCoreAction {

    //添加或修改套餐卡
    public function doAddCard($name, $everyday_times,  $reseverd_info = null, $times, $price, $card_id = null){
        if(empty($card_id)){
            return $this->output($this->add('Card'));
        }else{
            return $this->output($this->save('Card'));
        }
    }
    //获取套餐卡列表
    public function getCardList($page,$size){
        $info= $this->select(['is_del'=>\CommonDefine::IS_DEL_0,'mchid'=>$this->state->user_id],$page,$size,'create_time desc','Card');
        echo jsonp_encode($info->ret,$info->data,$info->count);exit();
    }
    //删除套餐卡
    public function doDelCard($card_id){
        $info= $this->save('Card',['is_del'=>\CommonDefine::IS_DEL_1,'card_id'=>$card_id]);
        echo jsonp_encode($info->ret,$info->data);exit();
    }
}
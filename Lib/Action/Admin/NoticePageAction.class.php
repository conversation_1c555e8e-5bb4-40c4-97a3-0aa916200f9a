<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017-12-04
 * Time: 11:49
 */

class NoticePageAction extends AdminCoreAction{
    //营销管理->群发短信
    public function noticepage($id=0)
    {
        $mchid = $this->state->user_id;
        $mchR = $this->find(array('admin_id' => $mchid),'Admin');
        $this->assign("shortmessage_marketing", $mchR->data['shortmessage_marketing']);
        $totalCount = $this->count(array('mchid' => $mchid, 'status' => \CommonDefine::SMS_SEND_STATUS_0), 'SmsMarketing');
        $passengerCountR = $this->count(array('mchid' => $mchid, 'cellphone' => array('neq', 'NULL')) , 'Passenger');
        $this->assign("passenger_count", $passengerCountR->ret?$passengerCountR->data:0);
        $driverCountR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')) , 'Driver');
        $this->assign("driver_count", $driverCountR->ret?$driverCountR->data:0);
        $branchCountR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')) , 'Admin');
        $this->assign("branch_count", $branchCountR->ret?$branchCountR->data:0);
        $this->assign("total_shortmessage_marketing", $totalCount->data?$totalCount->data:0);
        $where['mchid']=$mchid;
        $where['status']=1;
        $marketTemplateSignsR = $this->select($where, null, null,'id desc','MarketTemplateSign','sign,id');
        if(!$marketTemplateSignsR->ret){
            $marketTemplateSignR = $this->find(array('mchid' => 181), 'MarketTemplateSign');
            if($marketTemplateSignR->ret){
                $data['sign'] = $marketTemplateSignR->data['sign'];
                $data['mchid'] = $mchid;
                $marketTemplateSignS = $this->add('MarketTemplateSign', $data);
                if($marketTemplateSignS->ret){
                    $marketTemplateSignsR = $this->select($where, null, null,'id desc','MarketTemplateSign','sign,id');
                }
            }
        }
        $this->assign('info',$marketTemplateSignsR->data);
        $wheres['mchid'] = $this->state->user_id;
        $wheres['is_del'] = \CommonDefine::IS_DEL_0;
        $r = $this->select($wheres, 1, 30, 'send_time desc', 'SmsMarketing',null,null,'accept_content');
        $this->assign('sms',$r->data);
        $this->display('Tpl/Admin/Notice/notice.html');
    }
    public function noticepages($id=0)
    {
        $mchid = $this->state->user_id;
        $mchR = $this->find(array('admin_id' => $mchid),'Admin');
        $this->assign("shortmessage_marketing", $mchR->data['shortmessage_marketing']);
        $totalCount = $this->count(array('mchid' => $mchid, 'status' => \CommonDefine::SMS_SEND_STATUS_0), 'SmsMarketing');
        $passengerCountR = $this->count(array('mchid' => $mchid, 'cellphone' => array('neq', 'NULL')) , 'Passenger');
        $this->assign("passenger_count", $passengerCountR->ret?$passengerCountR->data:0);
        $driverCountR = $this->count(array('mchid' => $mchid ,'cellphone' => array('neq', 'NULL')) , 'Driver');
        $this->assign("driver_count", $driverCountR->ret?$driverCountR->data:0);
        $branchCountR = $this->count(array('parent_admin_id' => $mchid ,'cellphone' => array('neq', 'NULL')) , 'Admin');
        $this->assign("branch_count", $branchCountR->ret?$branchCountR->data:0);
        $this->assign("total_shortmessage_marketing", $totalCount->data?$totalCount->data:0);
        $where['mchid']=$mchid;
        $where['status']=1;
        if($id!==0){
            $where['id']=$id;
        }
        $marketTemplateR = $this->find($where, 'MarketTemplate');
        if($marketTemplateR->ret){
        }else{
            $marketTemplateR = $this->find(array('mchid' => 0), 'MarketTemplate');
            $where['mchid']=0;
        }
        unset($where['id']);
        $r= $this->select($where,1,20,'id desc','MarketTemplate');
        $this->assign('tempList',$r->data);
        $this->assign('temp_id',$marketTemplateR->data['id']);
        $marketTemplateContentArr = preg_split( '/\{-?\d+\}/', $marketTemplateR->data['market_template_content']);
        $this->assign("market_template_content_arr", $marketTemplateContentArr);
        $this->assign("market_template_content_count", count($marketTemplateContentArr));
        $this->assign("market_template_example", json_decode($marketTemplateR->data['market_template_example'], true));
        $this->display('Tpl/Admin/Notice/notice.html');
    }

    //营销管理->短息通知列表
    public function noticespage()
    {
        $mchid = $this->state->user_id;
        $mchR = $this->find(array('admin_id' => $mchid),'Admin');
        $this->assign("shortmessage_marketing", $mchR->data['shortmessage_marketing']);
        $totalCount = $this->count(array('mchid' => $mchid, 'status' => \CommonDefine::SMS_SEND_STATUS_0), 'SmsMarketing');
        $this->assign("total_shortmessage_marketing", $totalCount->data?$totalCount->data:0);
        $this->display('Tpl/Admin/Notice/notices.html');
    }
    //我的营销模板
    public function mytemplatepage(){
        $mchid = $this->state->user_id;
        $marketTemplateSignR = $this->find(array('mchid' => $mchid), 'MarketTemplateSign');
        if($marketTemplateSignR->ret){
            $this->assign("sign", $marketTemplateSignR->data['sign']);
        }else{
            $this->assign("sign", '【CC招车系统】');
        }
        $this->display('Tpl/Admin/Notice/my_template.html');
    }
    //审核短信模板
    public function to_examine_smstemppage(){
        $this->display('Tpl/Admin/Notice/to_examine_smstemp.html');
    }
}
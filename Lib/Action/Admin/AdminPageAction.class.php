<?php

import('@/Action/Admin/OrderManagerAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of AdminPageAction
 *
 * <AUTHOR>
 */
class AdminPageAction extends AdminCoreAction
{
    //后台总览
    public function indexPage()
    {
        $mchid = $this->state->user_id;
        $mode = M('user_auth')->where('uid=' . $mchid." AND auth_type=".CommonDefine::AUTH_TYPE_1)->find();
        if ($this->admin_group == 'insurance company') {
            redirect(C('WEB_ROOT') . 'admin_secure');
        }
        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " o.deleted_at IS NULL ";
        if ($this->admin_group_id == 2) {
            $where['o.mchid'] = $mchid;  //总台
        } elseif ($this->admin_group_id == 3) {
            $where['o.branchid'] = $mchid; //分台
        }
        $orders = M()->table('cp_order o')
            ->join("LEFT JOIN cp_passenger p ON o.passenger_id = p.passenger_id")
            ->join("LEFT JOIN cp_driver d ON o.driver_id = d.driver_id")
            ->field('o.order_id,o.branchid,o.is_temp,o.type,o.temp_apply_branchid,o.line_id,o.state,o.book_seating,o.start_address_remark,o.end_address_remark,p.cellphone,p.name pname,o.create_time,p.passenger_id,o.type,o.reseverd_phone,d.name dname,d.cellphone dcellphone,o.start_region_name,o.end_region_name')
            ->where($where)->order('create_time desc')->limit(5)->select();
        if ($orders) {
            foreach ($orders as $key => $item) {
                $orders[$key]['generation'] = '';
                if ($item['is_temp'] == 1) {
                    $oaR = $this->find(array('admin_id' => $item['temp_apply_branchid']), 'Admin', 'mchname,cellphone');
                    if ($oaR->ret) {
                        $orders[$key]['generation'] = $oaR->data['mchname'].'<br/>'.'['.$oaR->data['cellphone'].']';
                    }
                }

                if (!empty($item['branchid'])) {
                    $orderBranchR = $this->find(array('admin_id' => $item['branchid']), 'Admin', 'mchname');
                    if ($orderBranchR->ret) {
                        $orders[$key]['branchname'] = $orderBranchR->data['mchname'];
                    }
                }
                $orderStateInfoR = $this->getOrderStateInfo($item['order_id']);
                if ($orderStateInfoR->ret) {
                    $orders[$key]['state_message'] = $orderStateInfoR->data['state_message'];
                } else {
                    $orders[$key]['state_message'] = '';
                }
                if ($item['is_temp'] == 0) {
                    $orders[$key]['pinfo'] = $item['pname']."<br/>".$item['reseverd_phone'];
                } else {
                    $orders[$key]['pinfo'] = "临时乘客"."<br/>".$item['reseverd_phone'];
                }
                $orders[$key]['dinfo'] = $item['dname']."<br/>".$item['dcellphone'];

                $lineWhere['id'] = $item['line_id'];
                if ($item['type'] == \CommonDefine::ORDER_TYPE_1 || $item['type'] == \CommonDefine::ORDER_TYPE_3) {
                    $lineR = $this->find($lineWhere, 'Line');
                } elseif ($item['type'] == \CommonDefine::ORDER_TYPE_2) {
                    $lineR = $this->find($lineWhere, 'LineChartered');
                } elseif ($item['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $item['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        $orders[$key]['start_address_remark'] = $lineR->data['aboard_address'];
                    }
                } elseif ($item['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $orders[$key]['start'] = $item['start_region_name'].$item['start_address_remark'];
                        $orders[$key]['end'] = $item['end_region_name'].$item['end_address_remark'];
                        $orders[$key]['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orders[$key]['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orders[$key]['end']."<br/>"."<b style='color: gray;'>  &nbsp;出发地：</b>".$orders[$key]['start_address_remark'];
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $orders[$key]['start'] = $LineFreeRideR->data['start_name'].$LineFreeRideR->data['start_address_remark'];
                            $orders[$key]['end'] = $LineFreeRideR->data['end_name'].$LineFreeRideR->data['end_address_remark'];
                            $orders[$key]['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orders[$key]['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orders[$key]['end']."<br/>"."<b style='color: gray;'>  &nbsp;出发地：</b>".$orders[$key]['start_address_remark'];
                        }
                    }
                }
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orders[$key]['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orders[$key]['end'] = $endAddress . $lineR->data['end_name'];
                    $orders[$key]['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orders[$key]['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orders[$key]['end']."<br/>"."<b style='color: gray;'>  &nbsp;出发地：</b>".$orders[$key]['start_address_remark'];
                }
            }
        }
        $this->assign('order', $orders); //最新订单
        //$passengerModel = new PassengerModel();
        //$new_passenger_count = $passengerModel->where(array('create_time' => array('gt', date('Y-m-d 00:00:00')), 'mchid' => $mchid))->count('passenger_id');
        //$orderModel = new OrderModel();
        //$new_order_count = $orderModel->where(array('create_time' => array('gt', date('Y-m-d 00:00:00')), 'mchid' => $mchid))->count('order_id');
        //$succeed_order_count = $orderModel->where(array('create_time' => array('gt', date('Y-m-d 00:00:00')), 'state' => 6, 'mchid' => $mchid))->count('order_id');
        //$bad_record_count = $orderModel->where(array('create_time' => array('gt', date('Y-m-d 00:00:00')), 'is_bad_record' => 1, 'mchid' => $mchid))->count('order_id');
        $adminR = $this->find(array('admin_id' => $mchid), 'Admin');
        if ($adminR->ret) {

            /*            //线下线上合计交易额
                        $totalTurnOverR = $this->getTotalRegionTurnover($this->admin_group_id,$mchid,null,null);
                        //累计线下交易额
                        $totalDownTurnOverR = $this->getDownRegionTurnover($this->admin_group_id,$mchid,null,null);
                        //累计线上交易额
                        $totalOnTurnoverR = $this->getOnRegionTurnover($this->admin_group_id,$mchid,null,null);*/
            //累计线上退款
            // $totalRefundR = $this->getOnRegionRefund($this->admin_group_id, $mchid, null, null);

            //今天
            $today_start = date("Y-m-d 00:00:00");
            $today_end = date("Y-m-d 23:59:59");
            //            echo '今天:'.$today_start." - ".$today_end."<br/>";

            //昨天
            $yesterday_start = date("Y-m-d 00:00:00", strtotime(date("Y-m-d")) - 86400);
            $yesterday_end = date("Y-m-d 23:59:59", strtotime(date("Y-m-d")) - 86400);
            //            echo '昨天:'.$yesterday_start." - ".$yesterday_end."<br/>";

            //本周
            $week_start = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), date("d") - (date("w") ? date("w") : 7) + 1, date("Y")));
            $week_end = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m"), date("d") - (date("w") ? date("w") : 7) + 7, date("Y")));
            //            echo '本周:'.$week_start." - ".$week_end."<br/>";

            //本月
            $month_start = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m"), 1, date("Y")));
            $month_end = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m"), date("t"), date("Y")));
            //            echo '本周:'.$month_start." - ".$month_end."<br/>";die;

            //订单
            $today_ordersR = $this->getRegionOrdersCount($this->admin_group_id, $mchid, $today_start, $today_end);
            $yesterdayOrdersR = $this->getRegionOrdersCount($this->admin_group_id, $mchid, $yesterday_start, $yesterday_end);
            $weekOrderR = $this->getRegionOrdersCount($this->admin_group_id, $mchid, $week_start, $week_end);
            $monthOrdersR = $this->getRegionOrdersCount($this->admin_group_id, $mchid, $month_start, $month_end);

            //线上/线下合计交易额
            /*            $todayTurnoverR = $this->getOnRegionTurnover($this->admin_group_id,$mchid,$today_start,$today_end);
                        $yesterdayTurnoverR = $this->getOnRegionTurnover($this->admin_group_id,$mchid,$yesterday_start,$yesterday_end);
                        $weekTurnoverR = $this->getOnRegionTurnover($this->admin_group_id,$mchid,$week_start,$week_end);
                        $monthTurnoverR = $this->getOnRegionTurnover($this->admin_group_id,$mchid,$month_start,$month_end);*/

            //线上交易额
            $todayTurnoverOnR = $this->getOnRegionTurnover($this->admin_group_id, $mchid, $today_start, $today_end);
            $yesterdayTurnoverOnR = $this->getOnRegionTurnover($this->admin_group_id, $mchid, $yesterday_start, $yesterday_end);
            $weekTurnoverOnR = $this->getOnRegionTurnover($this->admin_group_id, $mchid, $week_start, $week_end);
            $monthTurnoverOnR = $this->getOnRegionTurnover($this->admin_group_id, $mchid, $month_start, $month_end);

            $depositsR = new ResultModel(false);
            $linesR = new ResultModel(false);
            $mchR = new ResultModel(false);
            $branchR = new ResultModel(false);
            $driverR = new ResultModel(false);
            $passengerR = new ResultModel(false);
            if ($this->admin_group_id == 3) {
                //分台
                $linesR = $this->count(array('branchid' => $mchid, 'is_del' => 0), 'Line');
                $mchR = $this->find(array('admin_id' => $mchid), 'Admin', 'total_amount,total_turnover_on_amount,total_turnover_down_amount,parent_admin_id');
                $driverR = $this->count(array('branchid' => $mchid,'virtual' => 0), 'Driver');
                $depositsR = $this->count(array('step' => 1,'status' => 1,'branchid' => $mchid), 'Deposit');
                $passengerR = $this->count(array('is_del' => 0, 'mchid' => $mchR->data['parent_admin_id']), 'Passenger');
            } elseif ($this->admin_group_id == 2) {
                //商户
                $driverR = $this->count(array('mchid' => $mchid,'virtual' => 0), 'Driver');
                $branchR = $this->count(array('parent_admin_id' => $mchid, 'group_id' => 3,'is_del' => 0), 'Admin');
                $linesR = $this->count(array('mchid' => $mchid, 'is_del' => 0), 'Line');
                $mchR = $this->find(array('admin_id' => $mchid), 'Admin', 'total_amount,total_turnover_on_amount,total_turnover_down_amount');
                $banksR = $this->select(array('mchid' => $mchid,'is_del' => 0,'default' => 1), null, null, null, 'Banks', 'id,bank_name,bank_crad_number,account');
                $bankR = $this->find(array('mchid' => $mchid,'default' => 1), 'Banks');
                if ($banksR->ret) {
                    foreach ($banksR->data as $key => $item) {
                        $banksR->data[$key]['bank_name'] = $item['bank_name']."【".substr($item['bank_crad_number'], strlen($item['bank_crad_number']) - 4)."】-".$item['account'];
                    }
                }
                $depositsR = $this->count(array('step' => 2,'status' => 1,'mchid' => $mchid), 'Deposit');
                $passengerR = $this->count(array('is_del' => 0,'mchid' => $mchid), 'Passenger');
            } else {
                //总台
                $driverR = $this->count(array('virtual' => 0), 'Driver');
                $branchR = $this->count(array('group_id' => 2,'is_del' => 0), 'Admin');
                $linesR = $this->count(array('is_del' => 0), 'Line');
                $mchR = $this->find(array('admin_group_id' => 2), 'Admin', 'sum(total_amount) total_amount,sum(total_turnover_on_amount) total_turnover_on_amount,sum(total_turnover_down_amount) total_turnover_down_amount');
                $depositsR = $this->count(array('step' => 3,'status' => 1), 'Deposit');
                $passengerR = $this->count(array('is_del' => 0), 'Passenger');
                $adminR->data['balance'] = $this->find((array('admin_group_id' => 2)), 'Admin', 'sum(balance) balance')->data['balance']."(含所有商户)";
            }
        }

        $bankId = $bankR->ret ? $bankR->data['id'] : null;
        $this->assign('bankId', $bankId);
        $this->assign('banks', $banksR->ret ? $banksR->data : null);
        $this->assign('admin_group_id', $this->admin_group_id);
        //今日
        $this->assign('today_orders', $today_ordersR->ret ? $today_ordersR->data : 0);
        $this->assign('today_turnover_on', $todayTurnoverOnR->ret ? ($todayTurnoverOnR->data['turnover'] ? $todayTurnoverOnR->data['turnover'] : 0) : 0);
        $this->assign('today_refund', $this->queryRefundByTime($today_start, $today_end, $mchid, $this->admin_group_id));

        //昨日
        $this->assign('yesterday_orders', $yesterdayOrdersR->ret ? $yesterdayOrdersR->data : 0);
        $this->assign('yesterday_turnover_on', $yesterdayTurnoverOnR->ret ? $yesterdayTurnoverOnR->data['turnover'] ? $yesterdayTurnoverOnR->data['turnover'] : 0 : 0);
        //        $this->assign('yesterday_turnover_down',$yesterdayTurnoverR->ret?$yesterdayTurnoverR->data['turnover']?$yesterdayTurnoverR->data['turnover']:0:0);
        $this->assign('yesterday_refund', $this->queryRefundByTime($yesterday_start, $yesterday_end, $mchid, $this->admin_group_id));

        //本周
        $this->assign('week_orders', $weekOrderR->ret ? $weekOrderR->data : 0);
        $this->assign('week_turnover_on', $weekTurnoverOnR->ret ? $weekTurnoverOnR->data['turnover'] ? $weekTurnoverOnR->data['turnover'] : 0 : 0);
        //        $this->assign('week_turnover_down',$weekTurnoverR->ret?$weekTurnoverR->data['turnover']?$weekTurnoverR->data['turnover']:0:0);
        $this->assign('week_refund', $this->queryRefundByTime($week_start, $week_end, $mchid, $this->admin_group_id));

        //本月
        $this->assign('month_orders', $monthOrdersR->ret ? $monthOrdersR->data : 0);
        $this->assign('month_turnover_on', $monthTurnoverOnR->ret ? $monthTurnoverOnR->data['turnover'] ? $monthTurnoverOnR->data['turnover'] : 0 : 0);
        //        $this->assign('month_turnover_down',$monthTurnoverR->ret?$monthTurnoverR->data['turnover']?$monthTurnoverR->data['turnover']:0:0);
        $this->assign('month_refund', $this->queryRefundByTime($month_start, $month_end, $mchid, $this->admin_group_id));

        $this->assign('branchids', $branchR->ret ? $branchR->data : 0);
        $this->assign('drivers', $driverR->ret ? $driverR->data : 0);
        $this->assign('lines', $linesR->ret ? $linesR->data : 0);
        $this->assign('total_amount', $mchR->data['total_amount'] ? $mchR->data['total_amount'] : 0);
        $this->assign('total_turnover_on_amount', $mchR->ret ? $mchR->data['total_turnover_on_amount'] : 0);
        $this->assign('total_turnover_down_amount', $mchR->ret ? $mchR->data['total_turnover_down_amount'] : 0);
        $this->assign('total_refund_on_amount', $this->queryAllRefund($mchid, $this->admin_group_id));
        $this->assign('balance', $adminR->data['balance'] ? $adminR->data['balance'] : "0.00");
        $this->assign('deposits', $depositsR->ret ? $depositsR->data : 0);
        $this->assign('passengers', $passengerR->ret ? $passengerR->data : 0);
        $this->assign('wx_settlement_rate', C('WX_SETTLEMENT_RATE')); //今日不良记录
        if ($mchid == 70) {
            if ($mode) {
                redirect('/generateurl');
            } else {
                redirect('/qrlogin');
            }

        }

        $this->assign('inner_api', C('CC_INNER_API_HOST'));
        $this->display('Tpl/Admin/Index/index.html');
    }


    /**
     * 查询退款金额
     *
     * @param string $start
     * @param string $end
     * @param integer $mchid
     * @return integer
     */
    protected function queryRefundByTime($start, $end, $mchid, $groupId = 1)
    {
        if ($groupId != 1) {
            $refundWhere['mchid'] = $mchid;
        }
        $refundWhere['create_time'] = array('between',array($start,$end));
        $refundWhere['_string'] = " deleted_at IS NULL AND refund_amount <> 0";
        $refundResult = M('order')->field("sum(refund_amount) as total_refund_amount")->where($refundWhere)->find();
        return isset($refundResult['total_refund_amount']) ? $refundResult['total_refund_amount'] : 0;
    }


    /**
     * 查询全部退款金额
     *
     * @param integer $mchid
     * @return integer
     */
    protected function queryAllRefund($mchid, $groupId = 1)
    {
        if ($groupId != 1) {
            $refundWhere['mchid'] = $mchid;
        }
        $refundWhere['_string'] = " deleted_at IS NULL AND refund_amount <> 0";
        $refundResult = M('order')->field("sum(refund_amount) as total_refund_amount")->where($refundWhere)->find();
        return isset($refundResult['total_refund_amount']) ? $refundResult['total_refund_amount'] : 0;
    }


    //导出--订单
    public function exportPage($id = 0)
    {
        $phpExcelUtil = new \PhpExcelUtil();
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        if ($id) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return false;
            } else {
                $where['a.order_id'] = ['in',$ids];
            }
        } else {
            return false;
        }
        if ($this->admin_group_id == 2) {
            $where['a.mchid'] = $this->state->user_id;
        } elseif ($this->admin_group_id == 3) {
            $where['a.branchid'] = $this->state->user_id;
        }
        $data = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->field('a.order_id')->order('a.create_time desc')->select();
        if ($data) {
            $count = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->count('a.order_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("order_id", $value['order_id'], 'OrderManager', 'getOrder', "type,virtual_driver,price,offer_channel_price,start_address,end_address,order_id,reseverd_phone,is_temp,passenger.passenger_id,passenger.name,passenger.cellphone,driver.name,driver.cellphone,driver.driver_id,type_ride,start_time,create_time,book_seating,state");
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                    $virtualDriverR = $this->find(array('driver_id' => $t->data['virtual_driver']), 'Driver', 'driver_id,name');
                    if ($virtualDriverR->ret) {
                        $r->data[$key]['route'] = $virtualDriverR->data['name'];
                    }
                } else {
                    return $this->output($t);
                }
                unset($t);
            }
        }
        foreach ($r->data as $k => $v) {
            $r->data[$k]['reseverd_phone'] = $v['reseverd_phone']." ";
            $string = strip_tags($v['route']);
            $string = trim($string);
            $string = str_replace("\t", "", $string);
            $string = str_replace("\r\n", "", $string);
            $string = str_replace("\r", "", $string);
            $string = str_replace("\n", "", $string);
            $string = str_replace("&nbsp;", "", $string);
            $string = preg_replace("/\[\D+\]/", "", $string);
            $r->data[$k]['route'] = $string;
            $r->data[$k]['states'] = $r->data[$k]['state'];
            $r->data[$k]['admin_group_id'] = $this->admin_group_id;
            if (!empty($v['branchid'])) {
                $tem = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'mchname');
                $r->data[$k]['branchName'] = $tem->data['mchname'];
            }
            //1-乘客预定，2-车主接单，3-车主确认乘客上车，4-在路上，5-已送达(未支付)，|6-正常完成，7-取消，8-已拒绝
            $orderStateInfoR = $this->getOrderStateInfo($r->data[$k]['order_id']);
            if ($orderStateInfoR->ret) {
                $r->data[$k]['state_message'] = $orderStateInfoR->data['state_message'];
                if ($orderStateInfoR->data['pay_state'] == 1) {
                    $r->data[$k]['pay_message'] = "已支付";
                } else {
                    $r->data[$k]['pay_message'] = "未支付";
                }

                $r->data[$k]['order_close_onoff'] = $orderStateInfoR->data['order_close_onoff'];
            } else {
                $r->data[$k]['state_message'] = '';
                $r->data[$k]['pay_message'] = "未支付";
                $r->data[$k]['order_close_onoff'] = 0;
            }
            if ($v['type'] == 1) {
                $r->data[$k]['type'] = "拼车";
            } elseif ($v['type'] == 2) {
                $r->data[$k]['type'] = "包车";
            } elseif ($v['type'] == 3) {
                $r->data[$k]['type'] = "带货";
            } elseif ($v['type'] == 4) {
                $r->data[$k]['type'] = "代办";
            } elseif ($v['type'] == 5) {
                $r->data[$k]['type'] = "班车";
                if ($this->mchid == 1238) {
                    $r->data[$k]['type'] = "[定制客运]定制班线车管理";
                }
            } elseif ($v['type'] == 6) {
                $r->data[$k]['type'] = "顺风车";
                if ($this->mchid == 1238) {
                    $r->data[$k]['type'] = "[城际快车]顺风车管理";
                }
            } else {
                $r->data[$k]['type'] = "未知";
            }
            if (isset($v['passenger']['error'])) {
                unset($r->data[$k]['passenger']);
                $r->data[$k]['username'] = '非微信用户';
            } else {
                unset($r->data[$k]['passenger']);
                $r->data[$k]['username'] = $v['passenger']['name'];
            }
            unset($r->data[$k]['driver']);
            $r->data[$k]['driver'] = $v['driver']['name']."\n".$v['driver']['cellphone'];
            $r->data[$k]['price'] = "订单总价：".$v['price'].
                "渠道所得：".$v['offer_channel_price'].
                "司机所得：".$v['offer_price'].
                "分成比列：".($v['split'] * 100);
        }
        $ar = [['order_no','订单编号'],['branchName','分台名称'],['type','订单类型'],['username','乘客信息'],['reseverd_phone','乘客电话'],['generation','代约名称/渠道代约'],['route','线路'],['driver','司机信息'],['start_time','出发时间'],['create_time','下单时间'],['price','价格'],['pay_message','是否支付'],['state_message','订单状态']];
        $phpExcelUtil->exportExcel('商户订单数据', $ar, $r->data);
    }

    /**
     * 导出-订单
     * @param int $type 订单类型
     * @param int $order_no 订单编号（默认为空，查询所有）
     * @param int $order_id 订单id（默认为空，查询所有）
     * @param string $passenger_cellphone 乘客手机号（默认为空，查询所有）
     * @param string $passenger_name 购票人昵称（默认为空，查询所有）
     * @param string $driver_cellphone 司机手机号（默认为空，查询所有）
     * @param string $driver_name 司机昵称（默认为空，查询所有）
     * @param int $search_branchid 所属分台id（默认为空，查询所有）
     * @param int $search_start_time 下单开始日期
     * @param int $search_end_time 下单结束日期
     * @param int $line_id 线路id
     * @param int $car_tail_number 车牌号码
     * @param int $driver_id 司机id
     */
    public function exportOrdersPage($type = null, $pay_type = null, $order_state = null, $order_no = null, $passenger_cellphone = null, $passenger_name = null, $driver_cellphone = null, $driver_name = null, $search_branchid = 0, $search_start_time = null, $search_end_time = null, $start_time = null, $end_time = null, $line_id = null, $order_by = null, $car_tail_number = null, $driver_id = null, $start_name = null, $end_name = null)
    {
        $phpExcelUtil = new \PhpExcelUtil();
        $fields = "o.*,
                   Passenger.name as pname,
                   Passenger.cellphone as pcellphone,
                   Driver.name as dname,
                   Driver.cellphone as dcellphone";
        if (empty($search_start_time) || empty($search_end_time)) {
        } else {
            $dayNum = \diffBetweenTwoDays($search_start_time, $search_end_time);
            if ($dayNum > 31) {
                $this->error("要导出的下单日期跨度不能超过31天");
            }
        }

        if (empty($start_time) || empty($end_time)) {
        } else {
            $dayNum = \diffBetweenTwoDays($start_time, $end_time);
            if ($dayNum > 31) {
                $this->error("要导出的出发时间日期跨度不能超过31天");
            }
        }

        if ((empty($start_time) || empty($end_time)) && (empty($search_start_time) || empty($search_end_time))) {
            $this->error("请选择下单起止日期或出发时间区间");
        }

        if ($this->isEmpty($type)) {
            if (in_array($type, array(\CommonDefine::ORDER_TYPE_1,
                \CommonDefine::ORDER_TYPE_2,
                \CommonDefine::ORDER_TYPE_3,
                \CommonDefine::ORDER_TYPE_4,
                \CommonDefine::ORDER_TYPE_5))) {
                $where['o.type'] = $type;
            }
        }
        if ($this->isEmpty($pay_type)) {
            if ($pay_type == 1) {
                if (empty($where['_string'])) {
                    $where['_string'] = "(o.is_pay=1 OR o.is_pre_pay=1)";
                } else {
                    $where['_string'] .= " AND ( o.is_pay=1 OR o.is_pre_pay=1 )";
                }
            } elseif ($pay_type == 2) {
                if (empty($where['_string'])) {
                    $where['_string'] = " (o.is_pay=0 AND o.is_pre_pay=0) ";
                } else {
                    $where['_string'] .= " AND ( o.is_pay=0 AND o.is_pre_pay=0 )";
                }
            }
        }

        if ($this->isEmpty($order_state)) {
            if (in_array($order_state, array(1,2,3,4,5,6,7,8))) {
                if ($order_state == 1) {
                    $where['o.state'] = $order_state;
                    $where['o.appoint'] = 0;
                } elseif ($order_state == 2) {
                    $where['o.state'] = 1;
                    $where['o.appoint'] = 1;
                } else {
                    $where['o.state'] = $order_state;
                }
            }
        }

        if (!empty($order_id)) {
            $where['o.order_id'] = $order_id;
        }
        if (!empty($order_no)) {
            $where['o.order_no'] = $order_no;
        }
        if (!empty($passenger_name)) {
            $passenger_name = trim($passenger_name);
            $where['Passenger.name'] = array('LIKE',"$passenger_name%");
        }
        if (!empty($passenger_cellphone)) {
            $passenger_cellphone = trim($passenger_cellphone);
            $where['o.reseverd_phone'] = array('LIKE',"$passenger_cellphone%");
        }
        if (!empty($driver_name)) {
            $driver_name = trim($driver_name);
            $where['Driver.name'] = array('LIKE',"$driver_name%");
        }
        if (!empty($driver_cellphone)) {
            $driver_cellphone = trim($driver_cellphone);
            $where['Driver.cellphone'] = array('LIKE',"$driver_cellphone%");
        }

        if (!empty($search_start_time)) {
            if (empty($where['_string'])) {
                $where['_string'] = " o.create_time >= '".$search_start_time." 00:00:00'";
            } else {
                $where['_string'] .= " AND o.create_time >= '".$search_start_time." 00:00:00'";
            }
        }
        if (!empty($search_end_time)) {
            if (empty($where['_string'])) {
                $where['_string'] = " o.create_time <= '".$search_end_time." 23:59:59'";
            } else {
                $where['_string'] .= " AND o.create_time <= '".$search_end_time." 23:59:59'";
            }
        }

        if (!empty($start_time)) {
            if (empty($where['_string'])) {
                $where['_string'] = " o.start_time >= '".$start_time."'";
            } else {
                $where['_string'] .= " AND o.start_time >= '".$start_time."'";
            }
        }
        if (!empty($end_time)) {
            if (empty($where['_string'])) {
                $where['_string'] = " o.start_time <= '".$end_time."'";
            } else {
                $where['_string'] .= " AND o.start_time <= '".$end_time."'";
            }
        }

        $orderByString = 'o.create_time desc';
        if (!empty($order_by)) {
            $orderByString = 'o.start_time desc';
        }

        if (!empty($line_id)) {
            $where['o.line_id'] = $line_id;
        }

        if (!empty($car_tail_number)) {
            $car_tail_number = trim($car_tail_number);
            $where['Driver.car_tail_number'] = array('LIKE',"$car_tail_number%");
        }

        if (!empty($driver_id)) {
            $where['o.driver_id'] = $driver_id;
        }

        if ($this->admin_group_id == 2) {
            $where['o.mchid'] = $this->state->user_id;
            if (!empty($search_branchid)) {
                $where['o.branchid'] = $search_branchid;
            }
        } elseif ($this->admin_group_id == 3) {
            $where['o.branchid'] = $this->state->user_id;
        }
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        $orderRs = new ResultModel(false);
        $data = $orderModel->alias('o')->join("$passengerTable Passenger ON o.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON o.driver_id=Driver.driver_id")->where($where)->field($fields)->order($orderByString)->select();
        if ($data) {
            $orderRs = new ResultModel(true, $data, count($data));
        } else {
            $orderRs = new ResultModel(false);
        }
        try {
            foreach ($orderRs->data as $k => $v) {
                //处理名称还有公式的情况
                if (!empty($v['pname'])) {
                    $orderRs->data[$k]['pname'] = " ".$v['pname'];
                }

                $orderRs->data[$k]['order_type'] = $this->getOrderType($v['type']);
                $orderRs->data[$k]['admin_group_id'] = $this->admin_group_id;
                if (!empty($v['branchid'])) {
                    $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'mchname,cellphone');
                    $orderRs->data[$k]['branchName'] = $branchR->data['mchname']."[电话：".$branchR->data['cellphone']."]";
                }

                # 司机归属分组查询
                $driverGroupAttributeValueR = $this->find(array('driver_id' => $v['driver_id']), 'DriverGroupAttributeValue', 'id,name');
                $orderRs->data[$k]['driver_group_name'] = $driverGroupAttributeValueR->ret ? $driverGroupAttributeValueR->data['name'] : '';

                $orderRs->data[$k]['route'] = "暂无";
                switch ($v['type']) {
                    case \CommonDefine::ORDER_TYPE_1:{
                        $where['id'] = $v['line_id'];
                        $lineR = $this->find($where, 'Line');
                        if ($lineR->ret) {
                            $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                            $orderRs->data[$k]['start'] = $startAddress . $lineR->data['start_name'];
                            $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                            $orderRs->data[$k]['end'] = $endAddress . $lineR->data['end_name'];
                            $orderRs->data[$k]['route'] = "[线路编号：".$lineR->data['id'] .$orderRs->data[$k]['start'] ." - ".$orderRs->data[$k]['end'];
                        }
                        break;
                    }
                    case \CommonDefine::ORDER_TYPE_2:{
                        $where['id'] = $v['line_id'];
                        $lineR = $this->find($where, 'LineChartered');
                        if ($lineR->ret) {
                            $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                            $orderRs->data[$k]['start'] = $startAddress . $lineR->data['start_name'];
                            $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                            $orderRs->data[$k]['end'] = $endAddress . $lineR->data['end_name'];
                            $orderRs->data[$k]['route'] = "[线路编号：".$lineR->data['id'] .$orderRs->data[$k]['start'] ." - ".$orderRs->data[$k]['end'];
                        }
                        break;
                    }
                    case \CommonDefine::ORDER_TYPE_3:{
                        $where['id'] = $v['line_id'];
                        $lineR = $this->find($where, 'LineChartered');
                        if ($lineR->ret) {
                            $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                            $orderRs->data[$k]['start'] = $startAddress . $lineR->data['start_name'];
                            $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                            $orderRs->data[$k]['end'] = $endAddress . $lineR->data['end_name'];
                            $orderRs->data[$k]['route'] = "[线路编号：".$lineR->data['id'] .$orderRs->data[$k]['start'] ." - ".$orderRs->data[$k]['end'];
                        }
                        break;
                    }
                    case \CommonDefine::ORDER_TYPE_4:{
                        $agencyR = $this->find(array('agency_id' => $v['agency_id']), 'Agency');
                        if ($agencyR->ret) {
                            $orderRs->data[$k]['route'] = "标题：".$agencyR->data['name']."，内容：".$agencyR->data['summary'];
                        }
                        break;
                    }
                    case \CommonDefine::ORDER_TYPE_5:{
                        if (!empty($v['seat_optional'])) {
                            $orderRs->data[$k]['seat_optional_info'] = $this->getOrderSeatOptionInfo($v['seat_optional']);
                        }
                        $lineClassTrainR = $this->find(array('line_class_train_id' => $v['line_id']), 'LineClassTrain');
                        if ($lineClassTrainR->ret) {
                            $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                            if ($lineClassR->ret) {
                                $orderRs->data[$k]['route'] = "[班次编号：" .$lineClassTrainR->data['line_class_train_no'] ."]".$lineClassR->data['start_name'] ." - ".$lineClassR->data['end_name'];
                            }
                        }
                        break;
                    }
                    case \CommonDefine::ORDER_TYPE_6:{
                        if (empty($v['line_id'])) {//乘客发布的
                            $orderRs->data[$k]['start'] = $v['start_region_name'].$v['start_address_remark'];
                            $orderRs->data[$k]['end'] = $v['end_region_name'].$v['end_address_remark'];
                            $orderRs->data[$k]['route'] = $orderRs->data[$k]['start'] ." - ".$orderRs->data[$k]['end'];
                        } else {
                            $LineFreeRideR = $this->find(array('id' => $v['line_id']), 'LineFreeRide');
                            if ($LineFreeRideR->ret) {
                                $orderRs->data[$k]['start'] = $LineFreeRideR->data['start_name'].$LineFreeRideR->data['start_address_remark'];
                                $orderRs->data[$k]['end'] = $LineFreeRideR->data['end_name'].$LineFreeRideR->data['end_address_remark'];
                                $orderRs->data[$k]['route'] = $orderRs->data[$k]['start'] ." - ".$orderRs->data[$k]['end'];
                            }
                        }
                        break;
                    }
                }

                $channelBranchNameR = $this->find(array('admin_id' => $v['temp_apply_branchid']), 'Admin', 'mchname,cellphone');
                if ($channelBranchNameR->ret) {
                    if ($v['is_temp'] == 1) {
                        $orderRs->data[$k]['generation'] =  $channelBranchNameR->data['mchname'].'['.$channelBranchNameR->data['cellphone'].']';
                    }
                }

                //订单状态
                $orderRs->data[$k]['order_state_text'] = $this->getOrderStateText($v['state']);

                //支付状态
                if ($v['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    $orderRs->data[$k]['pay_message'] = "已支付";
                } else {
                    $orderRs->data[$k]['pay_message'] = "未支付";
                }

                $orderRs->data[$k]['refund_status'] = 0;
                $orderRs->data[$k]['refund_status_text'] = '';
                $orderRs->data[$k]['refund_apply_time'] = '';
                $orderRs->data[$k]['refund_ticket_ratio_text'] = '';

                //申请退款时间
                if ($v['state'] == \CommonDefine::ORDER_STATE_7 && $v['is_pay'] == \CommonDefine::PAY_STATUS_1) {
                    // $refundR = $this->find(['order_id' => $v['order_id']], 'Refunds');
                    // if($refundR->ret) {
                    $orderRs->data[$k]['refund_status'] = 3;
                    $orderRs->data[$k]['refund_status_text'] = "退款成功";
                    $orderRs->data[$k]['refund_apply_time'] = $v['update_time'];
                    $orderRs->data[$k]['refund_ticket_ratio_text'] = bcmul($v['refund_ticket_ratio'], 100, 2) . '%';
                    //     switch ($refundR->data['status']) {
                    //         case 1:$orderRs->data[$k]['refund_status_text'] = "退款申请中";
                    //             break;
                    //         case 2:$orderRs->data[$k]['refund_status_text'] = "退款中";
                    //             break;
                    //         case 3:$orderRs->data[$k]['refund_status_text'] = "退款成功";
                    //             break;
                    //         case 4:$orderRs->data[$k]['refund_status_text'] = "退款失败";
                    //             break;
                    //     }
                    // }
                }
            }

            $ar = [
                ['order_no','订单编号'],
                ['order_type','订单类型'],
                ['start_time','出发时间'],
                ['branchName','所属分台'],
                ['pname','购票人昵称'],
                ['pcellphone','购票人手机号'],
                ['reseverd_phone','联系人手机号'],
                ['generation','代约名称/渠道代约'],
                ['route','线路'],
                ['start_address_remark','上车点'],
                ['end_address_remark','下车点'],
                ['dname','司机昵称'],
                ['dcellphone','司机手机号'],
                ['driver_group_name','司机归属分组'],
                ['book_seating','订票数量'],
                ['price','订单总价'],
                ['coupon_price','优惠金额'],
                ['real_price','实付金额'],
                ['pay_message','支付状态'],
                ['order_state_text','订单状态'],
                ['create_time','下单时间'],
                ['refund_total_amount','退款总额'],
                ['refund_fee','退票手续费'],
                ['refund_ticket_ratio_text','退款费率'],
                ['refund_status_text','退款状态'],
                ['refund_apply_time','退款申请时间']
            ];

            $phpExcelUtil->exportExcel('商户订单数据', $ar, $orderRs->data);
        } catch (Exception $e) {
            echo $e->getMessage();
            die;
        }
    }


    //导出财务--司机提现|分台提现
    public function exportDepositsPage($id = 0)
    {
        $phpExcelUtil = new \PhpExcelUtil();
        $uid = $this->state->user_id;
        if ($this->admin_group_id == 3) {//分台
            $where['cds.step'] = 1;
        } elseif ($this->admin_group_id == 2) {//总台
            $where['cds.step'] = 2;
            $where['d.mchid'] = $uid;
        } elseif ($this->admin_group_id == 1) {//平台
            $where['cds.step'] = 3;
        }
        if ($id) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return false;
            } else {
                $where['d.deposit_id'] = ['in',$ids];
            }
        } else {
            return false;
        }
        $depositList = M()->table("cp_deposit d")->join("LEFT JOIN cp_deposit_status cds ON cds.deposit_id = d.deposit_id")
            ->field('d.deposit_id,d.account_id,d.create_time,d.moneys,cds.status,d.account_type,d.settlement_rate,d.real_amount')
            ->join("LEFT JOIN cp_bank b ON d.bank_id=b.bank_id")
            ->where($where)
            ->order('d.deposit_id desc')->select();
        foreach ($depositList as $k => $v) {
            if ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {
                $driverR = $this->find(array('driver_id' => $v['account_id']), 'Driver', 'driver_id,name,cellphone,balance');
                $depositList[$k]['nick_name'] = $driverR->data['name'];
                $depositList[$k]['cellphone'] = $driverR->data['cellphone'];
                $depositList[$k]['balance'] = $driverR->data['balance'];
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                $passengerR = $this->find(array('passenger_id' => $v['account_id']), 'Passenger', 'passenger_id,name,cellphone,cash_balances');
                $depositList[$k]['nick_name'] = $passengerR->data['name'];
                $depositList[$k]['cellphone'] = $passengerR->data['cellphone'];
                $depositList[$k]['balance'] = $passengerR->data['cash_balances'];
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {
                $branchR = $this->find(array('admin_id' => $v['account_id']), 'Admin', 'admin_id,mchname,cellphone,balance');
                $depositList[$k]['nick_name'] = $branchR->data['mchname'];
                $depositList[$k]['cellphone'] = $branchR->data['cellphone'];
                $depositList[$k]['balance'] = $branchR->data['balance'];
            }
            $tR = $this->find(array('deposit_id' => $v['deposit_id']), 'Transfer');

            if ($v['status'] == 2) {
                $depositList[$k]['status'] = 2;
                $depositList[$k]['message'] = '通过审核(付款中)';
                if ($tR->ret) {
                    if ($tR->data['status'] == 3) {
                        $depositList[$k]['message'] = '通过审核(付款成功)';
                    } elseif ($tR->data['status'] == 4 && $this->admin_group_id == 1) {
                        $depositList[$k]['message'] = '通过审核(付款失败)';
                        if (!empty($tR->data["error_msg"])) {
                            $depositList[$k]['message'] = '通过审核(付款失败)['.$tR->data["error_msg"].']。提现金额已原路返回到司机账户';
                        }
                    } elseif ($this->admin_group_id == 2) {
                        $depositR = $this->find(array('deposit_id' => $v['deposit_id']), 'Deposit');
                        if ($tR->data['status'] == 4) {
                            $depositList[$k]['message'] = '通过审核(付款失败),提现金额已原路返回到司机账户，如有疑问请及时联系平台管理员';
                        } elseif ($depositR->data['status'] == 3) {
                            $depositList[$k]['message'] = '通过审核(付款失败),提现金额已原路返回到司机账户，如有疑问请及时联系平台管理员';
                        }
                    }
                }
            } elseif ($v['status'] == 3) {
                $depositList[$k]['status'] = 3;
                $depositList[$k]['message'] = '不通过';
                if ($this->admin_group_id == 1) {
                    $depositList[$k]['message'] = '不通过,提现金额已原路返回到司机账户';
                } elseif ($this->admin_group_id == 2) {
                    $depositList[$k]['message'] = '不通过,提现金额已原路返回到司机账户';
                }
            } else {
                $depositList[$k]['status'] = 1;
                $depositList[$k]['message'] = '待审核';
            }

            if ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {
                $depositList[$k]['account_type'] = '司机';
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                $depositList[$k]['account_type'] = '乘客';
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {
                $depositList[$k]['account_type'] = '分台';
            }

            if (empty($v['settlement_rate'])) {
                $depositList[$k]['settlement_rate'] = 0;
            } else {
                $depositList[$k]['settlement_rate'] *= 100;
            }
            if (empty($v['real_amount'])) {
                $depositList[$k]['real_amount'] = 0;
            }
        }
        $ar = [['deposit_id','提现编号'],['nick_name','司机名称'],['cellphone','司机手机号'],['create_time','申请时间'],['moneys','提现金额'],['settlement_rate','结算费率'],['real_amount','实际到账（元）'],['balance','当前余额'],['message','审核状态'],['account_type','账户类型']];
        $phpExcelUtil->exportExcel('商户财务数据表', $ar, $depositList);
    }
    //导出财务--交易流水
    public function exportTurnoverPage($id = 0)
    {
        $phpExcelUtil = new \PhpExcelUtil();
        if ($id) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return false;
            } else {
                $where['o.order_id'] = ['in',$ids];
            }
        } else {
            return false;
        }
        $uid = $this->state->user_id;
        if ($this->admin_group_id == 3) {//分台
            $where['o.branchid'] = $uid;
        } elseif ($this->admin_group_id == 2) {//总台
            $where['o.mchid'] = $uid;
        } elseif ($this->admin_group_id == 1) {//平台

        }
        $orderListR = new ResultModel(false);
        $db = M();
        $orderList = $db->table('cp_order o')
            ->join("LEFT JOIN cp_refunds r ON r.order_id = o.order_id")
            ->field("o.order_id,o.order_no,o.is_pay,o.is_pre_pay,o.passenger_id,o.driver_id,o.branchid,o.temp_apply_branchid,o.price,o.split,o.offer_price,o.state,o.create_time,o.is_temp,r.status as refundstatus")
            ->where($where)
            ->order('o.create_time desc')
            ->select();
        //        echo $db->getLastSql();die;
        if ($orderList) {
            $orderListR->ret = true;
            $orderListR->data = $orderList;
            foreach ($orderListR->data as $k => $v) {
                $orderListR->data[$k]['account_name'] = "";
                $orderListR->data[$k]['account_type'] = "";
                if ($v['is_temp'] == 0) {
                    $orderListR->data[$k]['account_type'] = "乘客";
                    $passengerR = $this->find(array('passenger_id' => $v['passenger_id']), 'Passenger', 'name,cellphone');
                    if ($passengerR->ret) {
                        $orderListR->data[$k]['account_name'] = $passengerR->data['name']."[".$passengerR->data['cellphone']."]";
                    }
                } elseif ($v['is_temp'] == 1) {
                    $orderListR->data[$k]['account_type'] = "分台";
                    $branchR = $this->find(array('admin_id' => $v['temp_apply_branchid']), 'Admin', 'mchname,cellphone');
                    if ($branchR->ret) {
                        $orderListR->data[$k]['account_name'] = $branchR->data['mchname']."[".$branchR->data['cellphone']."]";
                    }
                }

                $orderListR->data[$k]['driver_name'] = "未指派司机";
                if ($v['driver_id']) {
                    $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver', 'name,cellphone');
                    if ($driverR->ret) {
                        $orderListR->data[$k]['driver_name'] = $driverR->data['name']."[".$driverR->data['cellphone']."]";
                    }
                }

                $orderListR->data[$k]['split_price'] = "0";
                $orderListR->data[$k]['turnover_message'] = "待付款";
                switch ($v['state']) {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                        if ($v['is_pre_pay'] == 1 && $v['is_temp'] == 1) {
                            $orderListR->data[$k]['turnover_message'] = "已线上付款";
                            $orderListR->data[$k]['split_price'] = $orderListR->data[$k]['price'] - $orderListR->data[$k]['offer_price'];
                        } elseif ($v['is_pay'] == 1) {
                            $orderListR->data[$k]['turnover_message'] = "已线上付款";
                            $orderListR->data[$k]['split_price'] = $orderListR->data[$k]['price'] - $orderListR->data[$k]['offer_price'];
                        } elseif ($v['state'] == 6) {
                            $orderListR->data[$k]['turnover_message'] = "已线下付款";
                        }
                        break;
                    case 7:
                        if ($v['is_pre_pay'] == 1 || $v['is_pay'] == 1) {
                            if ($v['refundstatus']) {
                                switch ($v['refundstatus']) {
                                    case 1:
                                    case 2:
                                        $orderListR->data[$k]['turnover_message'] = "退款中";
                                        break;
                                    case 3:
                                        $orderListR->data[$k]['turnover_message'] = "退款成功";
                                        break;
                                    case 4:
                                        $orderListR->data[$k]['turnover_message'] = "退款失败";
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }

            }
        }
        $ar = [['order_no','订单编号'],['account_type','付款账户类型'],['account_name','付款账户名称'],['driver_name','司机名称'],['offer_price','分成金额'],['split','分成比例'],['price','订单总金额'],['create_time','交易时间'],['turnover_message','交易状态']];
        $phpExcelUtil->exportExcel('商户财务数据表', $ar, $orderListR->data);
    }
    //导出--我的提现申请
    public function exportBranchDeposit($id)
    {
        $phpExcelUtil = new \PhpExcelUtil();
        $where = '';
        if ($id) {
            $ids = explode('|', $id);
            if (count($ids) < 1) {
                return false;
            } else {
                $where['id'] = ['in',$ids];
            }
        } else {
            return false;
        }
        $mchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
        if (!$mchR->ret) {
            return $this->output(new ResultModel(false, "该商户不存在....", null));
        }
        if ($this->admin_group_id == 2) {
            $where['mchid'] = $this->state->user_id;
        }
        $order = 'id desc';
        $r = $this->select($where, null, null, $order, 'DestoonFinanceCash');
        if (!$r->ret) {
            return $this->output(new ResultModel(false, "暂无数据....", null));
        }
        foreach ($r->data as $key => $val) {
            $bankR = $this->find(array('id' => $val['bank_id']), 'Banks');
            if ($bankR->ret) {
                $r->data[$key]['bank_name'] = $bankR->data['bank_name'];
                $r->data[$key]['bank_crad_number'] = $bankR->data['bank_crad_number'];
                $r->data[$key]['account'] = $bankR->data['account'];
            } else {
                $r->data[$key]['bank_name'] = null;
                $r->data[$key]['bank_crad_number'] = null;
                $r->data[$key]['account'] = "";
            }
            if ($val['status'] == 1) {
                $r->data[$key]['status'] = "提现成功";
            } elseif ($val['status'] == 2) {
                $r->data[$key]['status'] = "正在处理";
            } else {
                $r->data[$key]['status'] = "提现失败";
            }
            $r->data[$key]['settlement_rate'] = $r->data[$key]['settlement_rate'] * 100;
            $r->data[$key]['settlement_rate'] .= '%' ;
        }
        $ar = [['id','编号'],['bank_name','银行名称'],['bank_crad_number','银行卡号'],['account','开户名'],['money','提现金额'],['settlement_rate','提现手续费'],['real_amount','实际到账'],['status','提现进度'],['create_time','申请时间']];
        $phpExcelUtil->exportExcel('商户财务数据表', $ar, $r->data);
    }
    //登录页
    public function loginPage()
    {
        // 商户变量控制
        $callback = $this->getCallBack();
        $mchname = '';
        if ($callback) {
            $mchid = $this->getMchidByCipmchid($callback);
            if ($mchid) {
                $admin = M('admin')->field('admin_id,mchname')->where("admin_id='".$mchid."'")->find();
                if ($admin) {
                    $mchname = $admin['mchname'];
                }
            }
        }
        $this->assign('login_mchname', $mchname);
        $this->assign('mch_head_display', empty($mchname) ? 'none' : 'block');
        if ($this->state) {
            //已经登录，则重定向到主页
            redirect(C('WEB_ROOT') . 'admin_index');
            exit;
        }
        $this->display('Tpl/Admin/Public/login.html');
    }

    //订单管理-在线订单
    public function orderpage()
    {
        $mchid = isset($this->state->user_id) ? $this->state->user_id : cookie('mchid');
        if (!$mchid) {
            echo "<script>alert('请重新登录');window.location.href='/admin_login';</script>";
        }
        $mch = M('admin')->where(array('admin_id' => $mchid))->find();
        $this->assign('mch', $mch);
        $this->assign('start_time', date('Y-m-d 00:00:00', time()));
        // $this->assign('mch_url', $this->console($this->state->user_id));

        $this->display('Tpl/Admin/Order/index.html');
    }

    //订单管理-电话订单
    public function phoneorderlistpage()
    {
        $mchid = isset($this->state->user_id) ? $this->state->user_id : cookie('mchid');
        if (!$mchid) {
            echo "<script>alert('请重新登录');window.location.href='/admin_login';</script>";
        }
        /*电话叫车服务*/
        $linePhoneServiceR = $this->find(array('mchid' => $this->state->user_id), 'LinePhoneService');
        if (!$linePhoneServiceR->ret) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }

        $mch = M('admin')->where(array('admin_id' => $mchid))->find();
        $this->assign('mch', $mch);

        $this->display('Tpl/Admin/Order/phone_order_list.html');
    }

    //订单管理-保险订单
    public function orderinsurancepage()
    {
        //保险订单
        $insurance_service = 0;
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$this->mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if ($token['status'] == 'success' && $token['data']['access_token']) {
            $mchInfo = httpRequest(C('CC_INNER_API_HOST') ."/api/inner/merchant/profile?token=" . $token['data']['access_token'], 'get');
            $response = json_decode($mchInfo, true);
            if ($response['data']['platform_configuration']['value']['insurance'] == 1) {
                $insurance_service = 1;
            }
        }

        if ($insurance_service == 0) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }

        $this->display('Tpl/Admin/Order/order_insurance_list.html');
    }

    //订单管理-订单详细
    public function orderdetailpage($order_no)
    {
        $orderR = $this->find(array('order_no' => $order_no), 'Order');
        if (!$orderR->ret) {
            $this->error("查看失败，订单不存在");
        }
        $orderR->data['route'] = "暂无";
        switch ($orderR->data['type']) {
            case \CommonDefine::ORDER_TYPE_1:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'Line');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_2:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'LineChartered');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_3:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'Line');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_4:{
                $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                if ($agencyR->ret) {
                    $orderR->data['route'] = "标题：".$agencyR->data['name'].'<br/>'. "内容：".$agencyR->data['summary'];
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_5:{
                if (!empty($orderR->data['seat_optional'])) {
                    $orderR->data['seat_optional_info'] = $this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                }
                $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                if ($lineClassTrainR->ret) {
                    $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    if ($lineClassR->ret) {
                        $orderR->data['route'] =
                            "<b style='color: gray;'>  &nbsp;班次编号：</b>" .$lineClassTrainR->data['line_class_train_no'] ."<br/>"
                            ."<b style='color: green;'> &nbsp;起：</b>".$lineClassR->data['start_name'] ."<br/>"
                            ."<b style='color: red;'>  &nbsp;终：</b>".$lineClassR->data['end_name']."<br/>";
                    }
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_6:{
                if (empty($orderR->data['line_id'])) {//乘客发布的
                    $orderR->data['start'] = $orderR->data['start_region_name'].$orderR->data['start_address_remark'];
                    $orderR->data['end'] = $orderR->data['end_region_name'].$orderR->data['end_address_remark'];
                    $orderR->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>";
                } else {
                    $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                    if ($LineFreeRideR->ret) {
                        $orderR->data['start'] = $LineFreeRideR->data['start_name'].$LineFreeRideR->data['start_address_remark'];
                        $orderR->data['end'] = $LineFreeRideR->data['end_name'].$LineFreeRideR->data['end_address_remark'];
                        $orderR->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                    }
                }
                break;
            }
        }
        $orderInsuranceRs = $this->select(['order_id' => $orderR->data['order_id']], null, null, null, 'OrderInsurance');

        //购票人手机号
        $orderR->data['buy_passenger_cellphone'] = "无";
        if (!empty($orderR->data['passenger_id'])) {
            $passengerR = $this->find(['passenger_id' => $orderR->data['passenger_id']], 'Passenger');
            $orderR->data['buy_passenger_cellphone'] = $passengerR->ret ? $passengerR->data['cellphone'] : "无";
        }

        //分台
        $branchR = $this->find(['admin_id' => $orderR->data['branchid']], 'Admin');
        $branchName = $branchR->ret ? $branchR->data['mchname'] : "无";

        //渠道分台
        $channelBranchName = "无";
        if ($orderR->data['channel_type']) {
            $channelBranchR = $this->find(['admin_id' => $orderR->data['channel_account_id']], 'Admin');
            $channelBranchName = $channelBranchR->ret ? $channelBranchR->data['mchname'] : "无";
        }

        //代约分台
        $tempBranchName = "无";
        if ($orderR->data['is_temp']) {
            $tempBranchR = $this->find(['admin_id' => $orderR->data['temp_apply_branchid']], 'Admin');
            $tempBranchName = $tempBranchR->ret ? $tempBranchR->data['mchname'] : "无";
        }
        //申请退款时间
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_7 && $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
            $refundR = $this->find(['order_id' => $orderR->data['order_id']], 'Refunds');
            if ($refundR->ret) {
                $orderR->data['refund_status'] = $refundR->data['status'];
                $orderR->data['refund_status_text'] = "未知异常";
                $orderR->data['refund_apply_time'] = $refundR->data['create_time'];
                switch ($refundR->data['status']) {
                    case 1:$orderR->data['refund_status_text'] = "退款申请中";
                        break;
                    case 2:$orderR->data['refund_status_text'] = "退款中";
                        break;
                    case 3:$orderR->data['refund_status_text'] = "<b style='color: green'>退款成功</b>";
                        break;
                    case 4:$orderR->data['refund_status_text'] = "退款失败";
                        break;
                }
            }
        }

        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
        $orderR->data['split'] = $orderR->data['split'] * 100;
        $orderR->data['refund_ticket_ratio_text'] = bcmul(bcsub(1, $orderR->data['refund_ticket_ratio'], 2), 100, 2) . '%';
        $orderR->data['order_type_name'] = $this->getOrderType($orderR->data['type']);
        $orderR->data['order_state_text'] = $this->getOrderStateText($orderR->data['state']);
        $lineClassFerryServiceR = $this->find(['mchid' => $this->mchid], 'LineClassFerryService');
        $this->assign('line_class_ferry', $lineClassFerryServiceR->ret ? 1 : 0);
        $this->assign('order', $orderR->data);
        $this->assign('driver', $driverR->data);
        $this->assign('orderInsuranceList', $orderInsuranceRs->ret ? $orderInsuranceRs->data : []);
        $this->assign('branch_name', $branchName);
        $this->assign('channel_branch_name', $channelBranchName);
        $this->assign('temp_branch_name', $tempBranchName);
        $this->assign('mch_url_order', $this->consoleOrder($this->mchid, $order_no));
        $this->display('Tpl/Admin/Order/detail.html');

    }

    //订单管理-修改订单详细
    public function modifyorderpage($order_no)
    {
        $orderR = $this->find(array('order_no' => $order_no), 'Order');
        if (!$orderR->ret) {
            $this->error("查看失败，订单不存在");
        }
        $orderR->data['route'] = "暂无";
        switch ($orderR->data['type']) {
            case \CommonDefine::ORDER_TYPE_1:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'Line');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_2:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'LineChartered');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_3:{
                $where['id'] = $orderR->data['line_id'];
                $lineR = $this->find($where, 'Line');
                if ($lineR->ret) {
                    $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                    $orderR->data['start'] = $startAddress . $lineR->data['start_name'];
                    $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                    $orderR->data['end'] = $endAddress . $lineR->data['end_name'];
                    $orderR->data['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_4:{
                $agencyR = $this->find(array('agency_id' => $orderR->data['agency_id']), 'Agency');
                if ($agencyR->ret) {
                    $orderR->data['route'] = "标题：".$agencyR->data['name'].'<br/>'. "内容：".$agencyR->data['summary'];
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_5:{
                if (!empty($orderR->data['seat_optional'])) {
                    $orderR->data['seat_optional_info'] = $this->getOrderSeatOptionInfo($orderR->data['seat_optional']);
                }
                $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                if ($lineClassTrainR->ret) {
                    $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    if ($lineClassR->ret) {
                        $orderR->data['route'] =
                            "<b style='color: gray;'>  &nbsp;班次编号：</b>" .$lineClassTrainR->data['line_class_train_no'] ."<br/>"
                            ."<b style='color: green;'> &nbsp;起：</b>".$lineClassR->data['start_name'] ."<br/>"
                            ."<b style='color: red;'>  &nbsp;终：</b>".$lineClassR->data['end_name']."<br/>";
                    }
                }
                break;
            }
            case \CommonDefine::ORDER_TYPE_6:{
                if (empty($orderR->data['line_id'])) {//乘客发布的
                    $orderR->data['start'] = $orderR->data['start_region_name'].$orderR->data['start_address_remark'];
                    $orderR->data['end'] = $orderR->data['end_region_name'].$orderR->data['end_address_remark'];
                    $orderR->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>";
                } else {
                    $LineFreeRideR = $this->find(array('id' => $orderR->data['line_id']), 'LineFreeRide');
                    if ($LineFreeRideR->ret) {
                        $orderR->data['start'] = $LineFreeRideR->data['start_name'].$LineFreeRideR->data['start_address_remark'];
                        $orderR->data['end'] = $LineFreeRideR->data['end_name'].$LineFreeRideR->data['end_address_remark'];
                        $orderR->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$orderR->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$orderR->data['end']."<br/>";
                    }
                }
                break;
            }
        }
        $orderInsuranceRs = $this->select(['order_id' => $orderR->data['order_id']], null, null, null, 'OrderInsurance');

        //购票人手机号
        $orderR->data['buy_passenger_cellphone'] = "无";
        if (!empty($orderR->data['passenger_id'])) {
            $passengerR = $this->find(['passenger_id' => $orderR->data['passenger_id']], 'Passenger');
            $orderR->data['buy_passenger_cellphone'] = $passengerR->ret ? $passengerR->data['cellphone'] : "无";
        }

        //分台
        $branchR = $this->find(['admin_id' => $orderR->data['branchid']], 'Admin');
        $branchName = $branchR->ret ? $branchR->data['mchname'] : "无";

        //渠道分台
        $channelBranchName = "无";
        if ($orderR->data['channel_type']) {
            $channelBranchR = $this->find(['admin_id' => $orderR->data['channel_account_id']], 'Admin');
            $channelBranchName = $channelBranchR->ret ? $channelBranchR->data['mchname'] : "无";
        }

        //代约分台
        $tempBranchName = "无";
        if ($orderR->data['is_temp']) {
            $tempBranchR = $this->find(['admin_id' => $orderR->data['temp_apply_branchid']], 'Admin');
            $tempBranchName = $tempBranchR->ret ? $tempBranchR->data['mchname'] : "无";
        }

        //司机列表信息
        $drivers = '';
        $driverFields = 'driver_id,name,cellphone,car_tail_number,car_brand';
        $driversWhere = ' is_freeze = '.\CommonDefine::IS_FREEZE_0;
        $driversWhere .= ' AND state != 3';
        if ($this->admin_group_id == 2) {
            if ($this->state->user_id != $orderR->data['mchid']) {
                $this->error("查看失败，订单不存在");
            }
            $driversWhere .= ' AND mchid = '.$this->state->user_id;
            $drivers = $this->select($driversWhere, null, null, null, 'Driver', $driverFields);
        } elseif ($this->admin_group_id == 3) {
            if ($this->state->user_id != $orderR->data['branchid']) {
                $this->error("查看失败，订单不存在");
            }
            $driversWhere .= ' AND branchid = '.$this->state->user_id;
            $drivers = $this->select($driversWhere, null, null, null, 'Driver', $driverFields);
        }

        $orderR->data['split'] = $orderR->data['split'] * 100;
        $orderR->data['order_type_name'] = $this->getOrderType($orderR->data['type']);
        $orderR->data['order_state_text'] = $this->getOrderStateText($orderR->data['state']);
        $this->assign('order', $orderR->data);
        $this->assign('orderInsuranceList', $orderInsuranceRs->ret ? $orderInsuranceRs->data : []);
        $this->assign('branch_name', $branchName);
        $this->assign('channel_branch_name', $channelBranchName);
        $this->assign('temp_branch_name', $tempBranchName);
        $this->assign('drivers', $drivers->data);
        $this->display('Tpl/Admin/Order/modify_order.html');
    }


    //修改上下车点地址
    public function modifyaddresspage($longitude = '', $latitude = '', $address = '')
    {
        $this->assign('longitude', $longitude);
        $this->assign('latitude', $latitude);
        $this->assign('address', $address);
        $this->display('Tpl/Admin/Order/modify_address.html');
    }

    //会员管理->车主地图
    public function mdrviersmappage()
    {
        $this->display('Tpl/Admin/Member/drivers_map.html');
    }

    //保险管理
    public function securepage()
    {
        $this->display('Tpl/Admin/Secure/index.html');
    }

    //不良记录管理
    public function recordpage()
    {
        $this->display('Tpl/Admin/Record/index.html');
    }

    //费用管理
    public function feespage()
    {
        $this->display('Tpl/Admin/Fee/index.html');
    }

    //评价管理
    public function commentpage()
    {
        $this->display('Tpl/Admin/Comment/index.html');
    }
    //系统管理->管理员管理
    public function systemadminpage()
    {
        $this->display('Tpl/Admin/System/admin.html');
    }
    public function addaccountpage()
    {
        $this->display('Tpl/Admin/System/addAccount.html');
    }
    //支付配置
    public function paypage()
    {
        $mch = M('admin')->field('admin_id,mchname')->where(array('admin_id' => array('not in',"1"),'is_del' => 0))->select();
        $this->assign('mch', $mch);
        $this->display('Tpl/Admin/System/pay.html');
    }

    //系统管理->月租管理
    public function monthfeepage()
    {
        $this->display('Tpl/Admin/System/mfee.html');
    }

    //系统管理->保险金管理
    public function securefeepage()
    {
        $this->display('Tpl/Admin/System/sfee.html');
    }

    //系统管理->修改密码
    public function passwordpage()
    {
        $this->display('Tpl/Admin/System/password.html');
    }

    //日志管理->日志列表
    public function logspage()
    {
        $this->display('Tpl/Admin/Log/logs.html');
    }
    public function mchlogspage($admin_id)
    {
        $this->assign('admin_id', $admin_id);
        $this->display('Tpl/Admin/System/mchlogs.html');
    }
    public function addmchpage($admin_id, $id = null)
    {
        $info1 = $this->find(['admin_id' => $admin_id], 'Admin', 'mchname');
        if ($id) {
            $r = $this->find(['mchid' => $admin_id,'id' => $id], 'Mchlog');
            $this->assign('info', $r->data);
        } else {
            $info['mchname'] = '';
            $info['title'] = '';
            $info['content'] = '';
            $this->assign('info', $info);
        }
        $this->assign('id', $id);
        $this->assign('mchname', $info1->data['mchname']);
        $this->assign('admin_id', $admin_id);
        $this->display('Tpl/Admin/System/addmchlogs.html');
    }
    //财务->司机提现列表
    public function driverdepositspage()
    {
        // $this->assign('mch_url', $this->console($this->state->user_id));
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/driver_deposits.html');
    }

    //财务->分台提现列表
    public function branchdepositspage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/branch_deposits.html');
    }

    //财务->商户->商户提现列表
    public function merchantdepositspage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/merchant_deposits.html');
    }

    //财务->平台->商户提现列表
    public function merchantsdepositspage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/merchants_deposits.html');
    }

    //财务->交易明细列表
    public function merchantturnoverspage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/merchant_turnovers.html');
    }

    //提现管理->提现列表
    public function depositspage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/deposits.html');
    }


    /**
     * 司机提现列表
     */
    public function carshPage()
    {
        if ($this->admin_group_id == 3) {//分台
            return ;
        }
        $this->display('Tpl/Admin/Deposit/index.html');
    }

    //提现管理->财务结算
    public function balancepage()
    {
        $mchid = $this->state->user_id;
        $this->assign('mchid', $mchid);
        $this->display('Tpl/Admin/Deposit/balance.html');
    }

    //审核管理->审核列表
    public function auditpage()
    {
        $this->display('Tpl/Admin/Audit/index.html');
    }

    //套餐管理->商家套餐详情
    public function packagepage()
    {
        $uid = $this->state->user_id;
        if (!$uid) {
            $uid = cookie('mchid');
        }
        $item = M('admin')->where('admin_id=' . $uid.' and is_del=0')->find();
        $freeM = $item['shortmessage'];
        $freeS = $item['usestorage'];
        $freeD = $item['driver_num'];
        $freeFRD = $item['free_ride_driver_num'] = $item['free_ride_driver_num'] ? $item['free_ride_driver_num'] : 0;
        $item['name'] = $this->getPackge($item['package_id']);
        if ($freeM < C('ShortMessage_Limit')) {
            $item['data']['shortmessage']['class'] = 'red';
        }
        if ($freeS < C('Strore_Limit')) {
            $item['data']['usestorage']['class'] = 'red';
        }
        if ($freeD < C('Drive_Num_Limit')) {
            $item['data']['driver_num']['class'] = 'red';
        }
        if ($freeFRD < C('Drive_Num_Limit')) {
            $item['data']['free_ride_driver_num']['class'] = 'red';
        }
        //获取基础套餐
        $package = $this->getPackageByPackageId($item['package_id']);
        $this->assign('package', $package);

        //获取增值套餐
        $data = $this->getNewBusinessPlan($uid);
        $this->assign('business', $data);

        $this->assign('data', $item);
        $this->display('Tpl/Admin/Configure/package.html');
    }

    //系统设置->商家密码修改
    public function bpasswordpage()
    {
        $this->display('Tpl/Admin/Configure/password.html');
    }

    //系统设置->商家支付检查
    public function paycheckpage()
    {
        $r = $this->find(array('mchid' => $this->state->user_id), 'PayConfig');
        if ($r->data['pingappid'] != '') {
            $this->assign('ispay', 1);
        }
        $this->display('Tpl/Admin/Configure/paycheck.html');
    }

    //系统设置->商家微信菜单
    public function codepage()
    {
        // $this->assign('mch_url', $this->console($this->mchid));
        $mchR = $this->find(array('admin_id' => $this->mchid), 'Admin', 'mchname');

        $model = M('cipher_control')->where('mchid=' . $this->mchid);
        $passdata = $model->find();
        if ($passdata) {
            $md5 = $passdata['ciphertext'];
        } else {
            $md5 = $this->incheck($this->mchid);
            $dataArray = array('ciphertext' => $md5, 'mchid' => $this->mchid, 'type' => '0');
            $model->data($dataArray)->add();
        }
        $isauth = M('user_auth')->field('isauthorized')->where('uid=' . $this->mchid." AND auth_type=".CommonDefine::AUTH_TYPE_1)->find();
        $this->assign('isauth', $isauth['isauthorized']);
        $this->assign('mchname', $mchR->data['mchname']);
        //司机
        $this->assign('wait_accept_order_code', sprintf('%smicro-apps/uniapp-driver/pages/login/index?callback=%s', C('WEB_ROOT'), $md5));
        $this->assign('free_ride_code', C('WEB_ROOT') . 'driver_free' . '/callback/' . $md5);
        $this->assign('accept_order_code', C('WEB_ROOT') . 'driver_order' . '/callback/' . $md5);
        $this->assign('myset_code', C('WEB_ROOT') . 'driver_myset' . '/callback/' . $md5);
        $this->assign('free_ride_register_code', C('WEB_ROOT') . 'driver_register' . '/callback/' . $md5);
        //乘客
        //        $this->assign('passenger_banma_code',           C('WEB_ROOT') . 'passenger_banma' . '/callback/' . $md5);
        $this->assign('passenger_banma_index', C('WEB_ROOT') . 'passenger_banma_index' . '/callback/' . $md5);
        $this->assign('passenger_index_code', C('WEB_ROOT') . 'passenger_index' . '/callback/' . $md5);
        $this->assign('passenger_lines_code', C('WEB_ROOT') . 'passenger_lines' . '/callback/' . $md5);
        $this->assign('passenger_chartered_code', C('WEB_ROOT') . 'passenger_charters' . '/callback/' . $md5);
        $this->assign('passenger_train_code', C('WEB_ROOT') . 'passenger_trains' . '/callback/' . $md5);
        $this->assign('passenger_free_ride_code', C('WEB_ROOT') . 'passenger_free_ride' . '/callback/' . $md5);
        $this->assign('passenger_take_goods_code', C('WEB_ROOT') . 'passenger_take_goods' . '/callback/' . $md5);
        $this->assign('passenger_agency_code', C('WEB_ROOT') . 'passenger_agency' . '/callback/' . $md5);
        $this->assign('passenger_mine_code', C('WEB_ROOT') . 'passenger_mine' . '/callback/' . $md5);
        $this->assign('mchQrCode', M('MchWechatQr')->where(['mchid' => $this->state->user_id])->find());

        //分台
        $this->assign('bacode', C('WX_BRANCH_URL') . '/callback/' . $md5);
        $this->display('Tpl/Admin/Configure/code.html');
    }
    public function qrcodepage()
    {
        $this->assign('mchQrCode', M('MchWechatQr')->where(['mchid' => $this->state->user_id])->find());
        $this->display('Tpl/Admin/Configure/qrcode.html');
    }

    public function codeminipage()
    {
        /*微信小程序服务*/
        $wxminiServiceR = $this->find(array('mchid' => $this->state->user_id), 'WxminiService');
        if (!$wxminiServiceR->ret) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }
        $isauth = M('user_auth')->field('isauthorized')->where('uid=' . $this->mchid." AND auth_type=".CommonDefine::AUTH_TYPE_2)->find();
        $this->assign('isauth', $isauth['isauthorized']);
        $mchR = $this->find(array('admin_id' => $this->mchid), 'Admin', 'mchname');
        $this->assign('mchname', $mchR->data['mchname']);
        $this->display('Tpl/Admin/Configure/codemini.html');
    }

    //系统设置->商家年费设置
    public function bmfeepage()
    {
        $this->assign('data', M('annual_fee')->where('mchid=' . $this->state->user_id)->find());
        $this->assign('mchid', $this->state->user_id);
        $this->display('Tpl/Admin/Configure/mfee.html');
    }

    //系统设置->商家分成设置
    public function splitPage()
    {
        $mcher = $this->find(array('mchid' => $this->state->user_id), 'AnnualFee');
        $this->assign('data', $mcher->data);
        $this->assign('mchid', $this->state->user_id);
        $this->display('Tpl/Admin/Configure/split.html');

    }


    // 商户管理->商户列表
    public function merchantspage()
    {
        $this->display('Tpl/Admin/Merchant/merchants.html');
    }

    // 商户管理->新增商户
    public function mmerchantpage()
    {
        $this->display('Tpl/Admin/Merchant/merchant.html');
    }

    // 商户管理->删除商户
    public function fmpage()
    {
        $this->display('Tpl/Admin/Merchant/fm.html');
    }

    //商户管理->商户详细页
    public function merchanteditpage($merchant_id)
    {
        $merchant = $this->sudoGetEntityById('merchant_id', $merchant_id, 'merchant\\Account', 'getMerchant', null)->data;
        $this->assign('merchant', $merchant);
        $this->display('Tpl/Admin/Merchant/merchant_edit.html');
    }

    //超套餐限额->商家列表及通知
    public function limitpage()
    {
        $data = M('admin')->where(array('group_id' => 2,'is_del' => 0))->select();
        $limit = array();
        $curTime = time();
        foreach ($data as $k => &$item) {
            $package = $this->getPackageByPackageId($item['package_id']); // 获取司机套餐数量
            $business = $this->getNewBusinessPlan($item['admin_id']); //获取新增的套餐数量
            $item['total_shortmessage'] = $package['shortmessage'] + $business['sms'];
            $item['total_shortmessage_marketing'] = $package['shortmessage_marketing'] + $business['marketing_sms'];
            $item['total_driver_num'] = $package['driver'] + $business['driver'];
            $item['total_free_ride_driver_num'] = $package['free_ride_driver'] + $business['free_ride_driver'];
            $item['total_usestorage'] = $package['storage'] + $business['stroge'];
            $freeM = $item['shortmessage'];
            $freeMS = $item['shortmessage_marketing'];
            $freeS = $item['usestorage'];
            $freeD = $item['driver_num'];
            $freeFRD = $item['free_ride_driver_num'] = $item['free_ride_driver_num'] ? $item['free_ride_driver_num'] : 0;
            $item['name'] = $this->getPackge($item['package_id']);

            if ($freeM <= C('ShortMessage_Limit')) {
                $item['data']['shortmessage']['class'] = 'red';
            }
            if ($freeMS <= C('MarketingShortMessage_Limit')) {
                $item['data']['shortmessage_marketing']['class'] = 'red';
            }
            if ($freeS <= C('Strore_Limit')) {
                $item['data']['usestorage']['class'] = 'red';
            }
            if ($freeD <= C('Drive_Num_Limit')) {
                $item['data']['driver_num']['class'] = 'red';
            }
            if ($freeFRD <= C('Free_Ride_Driver_Num_Limit')) {
                $item['data']['free_ride_driver_num']['class'] = 'red';
            }
            //到期时间
            if ((strtotime($item['endtime']) - $curTime) <= C('Package_EndTime_Limit')) {
                $item['data']['endtime']['class'] = 'red';
                $limit[$k] = $item;
            }
            $limit[$k] = $item;
        }

        $this->assign('data', $limit);
        $this->display('Tpl/Admin/Limit/index.html');
    }

    public function getPackge($packageID)
    {
        if ($packageID) {
            $data = M('package')->field('name')->where('package_id=' . $packageID)->find();
            return $data['name'];
        }

    }

    public function order_create()
    {
        $mchid = isset($this->state->user_id) ? $this->state->user_id : cookie('mchid');
        if (!$mchid) {
            echo "<script>alert('请重新登录');window.location.href='/admin_login';</script>";
        }

        $mch = M('admin')->where(array('id' => $mchid))->find();

        $groupId = $mch['group_id'];

        $passengerWhere = array();
        $passengerWhere['is_freeze'] = 0;/*乘客非删除*/
        $driverWhere = array();
        $driverWhere['virtual'] = 0;/*非虚拟司机*/
        $driverWhere['state'] = 0;/*车主行程状态 正常*/
        $driverWhere['is_freeze'] = 0;/*非删除司机*/
        $driverWhere['status'] = 0;/*非删除车主*/

        //商户
        if ($groupId == 1) {
            $driverWhere['mchid'] = $mchid;
            $passengerWhere['mchid'] = $mchid;
        }
        //超级管理员

        if ($groupId == 2) {

        }
        try {

            $driverList = M('driver')->where($driverWhere)->select();


            foreach ($driverList as $item) {
                $dataDriver[$item['mchid']]['name'] = $this->getMchName($item['mchid']);
                $dataDriver[$item['mchid']]['data'][] = array(
                    'start_address_code' => $this->getcodeName($item['start_address_code']),
                    'end_address_code' => $this->getcodeName($item['end_address_code']),
                    'driver_name' => $item['name'],
                    'residual_seating' => $item['residual_seating'],
                    'driver_id' => $item['driver_id']
                );
            }

            $passengerList = M('passenger')->where($passengerWhere)->select();
            foreach ($passengerList as $value) {
                $passengerData[$value['mchid']]['name'] = $this->getMchName($value['mchid']);
                $passengerData[$value['mchid']]['data'][] = array(
                    'name' => $value['name'],
                    'id' => $value['passenger_id']
                );
            }
            $this->assign('driverList', $dataDriver);
            $this->assign('passengerList', $passengerData);
            $this->assign('group', $mch['group_id']);

            //            if ($groupId == 1) {
            //                $this->display('Tpl/Admin/order/superadd.html');
            //            } elseif ($groupId == 2) {
            $this->display('Tpl/Admin/order/mchadd.html');
            //            }
            //商户
        } catch (Exception $e) {
            print_r($e->getMessage());
        }
    }

    public function getcodeName($addressCode)
    {
        $addres = M('meta_address')->where(array('address_code' => $addressCode))->field('name')->find();
        return $addres['name'];
    }

    public function getMchName($mchid)
    {
        $mch = M('admin')->where(array('admin_id' => $mchid, 'group_id' => 2))->field('mchname')->find();
        return $mch['mchname'];
    }


    //账户管理->用户模板配置
    public function memberConfTplPage()
    {

        $mchid = null;
        if ($_GET['admin_id']) {
            $mchid = $_GET['admin_id'];
        } else {
            $url = C("do_main");
            echo "<script type='text/javascript'>window.location.href='{$url}\/admin_admin'</script>";
        }
        $tplList = M('wechat_tpl_category')->select();
        foreach ($tplList as $key => $val) {
            $wechatTplR = $this->find(array('c_id' => $val['id'],'mchid' => $mchid), "WechatTpl", 'wechat_tpl_code');
            $tplList[$key]['code'] = $wechatTplR->data['wechat_tpl_code'];
        }
        $this->assign("tplList", $tplList);
        $this->display('Admin:Member:conf');
    }

    //粉丝管理
    public function getFansPage()
    {

        if ($this->admin_group_id == 1) {
            $mchList = M("admin")->field("admin_id,mchname")->where(array('is_del' => 0,'group_id' => 2))->select();
            $fanscCount = M("third_party")->count();
            $this->assign("mchList", $mchList);
            $this->assign("fanscCount", $fanscCount);

        }

        $this->display('Tpl/Admin/Fans/index.html');
    }


    //商户地址配置
    public function mchAddressConfigPage($admin_id)
    {
        $this->assign("machid", $admin_id);
        $this->display('Tpl/Admin/System/mch_address.html');
    }

    private function getTreeList($data, $pid = 0)
    {
        if (!$data) {
            return null;
        }
        $temp = null;
        $tree = null;
        foreach ($data as $key => $val) {
            if ($val['address_pid'] == $pid) {
                $temp = $this->getTreeList($data, $val['address_id']);
                $temp && $val['child'] = $temp;
                $tree[] = $val;
            }
        }
        return $tree;
    }
    //总台
    public function subbrachpage($admin_id)
    {
        $this->assign('mchid', $admin_id);
        $this->display('Tpl/Admin/System/sub_mch.html');
    }

    public function editsubmchpage($id)
    {
        $sbR = $this->find(array('id' => $id ), 'SubBranch', 'id,account');
        $this->assign('data', $sbR->data);
        $this->display('Tpl/Admin/System/sub_mch_edit.html');
    }

    public function subbrachlistpage()
    {
        $this->display('Tpl/Admin/System/sub_mch_list.html');
    }

    //商户端
    public function brsubbrachpage($admin_id)
    {
        $this->assign('mchid', $admin_id);
        $this->display('Tpl/Admin/Configure/sub_mch.html');
    }

    public function editbrsubmchpage($id)
    {
        $sbR = $this->find(array('id' => $id ), 'SubBranch', 'id,account');
        $this->assign('data', $sbR->data);
        $this->display('Tpl/Admin/Configure/sub_mch_edit.html');
    }

    public function brsubbrachlistpage()
    {
        $this->display('Tpl/Admin/Configure/sub_mch_list.html');
    }

    //定制
    public function custompage()
    {
        $this->display('Tpl/Admin/Configure/custom.html');
    }

    //银行卡->列表
    public function banklistpage()
    {
        $this->display('Tpl/Admin/Bank/list.html');
    }
    //银行卡->列表
    public function adminlistpage()
    {
        $this->display('Tpl/Admin/Bank/admin_list.html');
    }
    //银行卡->添加
    public function bankaddpage()
    {
        $this->assign("mchid", $_GET['admin_id']);
        $this->display('Tpl/Admin/Bank/add.html');
    }
    //银行卡->修改
    public function bankeditpage()
    {
        $id = $_GET['id'];
        $bank = $this->find(array('id' => $id,'is_del' => 0), 'Banks');
        if (!$bank->ret) {
            $this->display('Tpl/Admin/Bank/add.html');
        }
        $this->assign("bank", $bank->data);
        $this->display('Tpl/Admin/Bank/edit.html');
    }
    public function newsPage()
    {
        $this->display('Tpl/Admin/Configure/news.html');
    }
    public function wechatAccessPage()
    {
        $this->display('Tpl/Admin/Configure/wechat_access.html');
    }
    //账户-》提成设置
    public function branchcommissionpage()
    {
        $adminR = $this->find(array('admin_id' => $this->state->user_id , 'group_id' => 2,'is_del' => 0), 'Admin', 'admin_id');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, "该商户不存在", null));
        }
        $afR = $this->find(array('mchid' => $adminR->data['admin_id']), 'AnnualFee');
        $this->assign("split", $afR->data['split'] ? $afR->data['split'] : 0);
        $this->display('Tpl/Admin/Configure/commission.html');
    }


    //账户-》配置开关
    public function configsetpage()
    {
        $robOrderConfig = \CommonDefine::ROB_ORDER_0;//未开启
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => 'rob_order'), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrderConfig = $systemConfigR->data['value'];
        }
        $smsConfig = \CommonDefine::SMS_CONFIG_1;//默认开启
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => 'sms_config'), 'SystemConfig');
        if ($systemConfigR->ret) {
            $smsConfig = $systemConfigR->data['value'];
        }
        $driverRegisterConfig = \CommonDefine::DRIVER_REGISTER_CONFIG_0;//未开启
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::DRIVER_REGISTER_CONFIG), 'SystemConfig');
        if ($systemConfigR->ret) {
            $driverRegisterConfig = $systemConfigR->data['value'];
        }

        $multiOrderMode = \CommonDefine::MULTI_ORDER_MODE_0;//单订单
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::MULTI_ORDER_MODE), 'SystemConfig');
        if ($systemConfigR->ret) {
            $driverRegisterConfig = $systemConfigR->data['value'];
        }

        $orderPaymentMode = \CommonDefine::ORDER_PAYMENT_MODE_0;//后支付
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::ORDER_PAYMENT_MODE), 'SystemConfig');
        if ($systemConfigR->ret) {
            $orderPaymentMode = $systemConfigR->data['value'];
        }

        $idUpLoadConfig = \CommonDefine::ID_UPLOAD_CONFIG_0;//默认是不需要上传
        $systemConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::ID_UPLOAD_CONFIG), 'SystemConfig');
        if ($systemConfigR->ret) {
            $idUpLoadConfig = $systemConfigR->data['value'];
        }

        $lineClassAppointConfig = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;//默认自动派单
        $lineClassAppointConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG), 'SystemConfig');
        if ($lineClassAppointConfigR->ret) {
            $lineClassAppointConfig = $lineClassAppointConfigR->data['value'];
        }

        $lineCharterCustomBranchConfig = \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG_0;//无任何自定义包车管理分台
        $lineCharterCustomBranchConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG), 'SystemConfig');
        if ($lineCharterCustomBranchConfigR->ret) {
            $lineCharterCustomBranchConfig = $lineCharterCustomBranchConfigR->data['value'];
        }

        $orderDriverPayOpConfig = \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG_0;//已线下现金付款按钮
        $orderDriverPayOpConfigR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::ORDER_DRIVER_PAY_OP_CONFIG), 'SystemConfig');
        if ($orderDriverPayOpConfigR->ret) {
            $orderDriverPayOpConfig = $orderDriverPayOpConfigR->data['value'];
        }

        $this->assign("rob_order", $robOrderConfig);
        $this->assign("sms_config", $smsConfig);
        $this->assign("driver_register_config", $driverRegisterConfig);
        $this->assign("timer_rob_time_set", C("TIMER_ROB_TIME_SET") / 60);
        $this->assign("multi_order_mode", $multiOrderMode);
        $this->assign("order_payment_mode", $orderPaymentMode);
        $this->assign("id_upload_config", $idUpLoadConfig);
        $this->assign("line_class_appoint_config", $lineClassAppointConfig);
        $this->assign("line_charter_custom_branch_config", $lineCharterCustomBranchConfig);
        $this->assign("order_driver_pay_op_config", $orderDriverPayOpConfig);
        $this->display('Tpl/Admin/Configure/config_set.html');
    }

    public function refundticketconfigpage()
    {
        $selWhere['mchid'] = $this->mchid;
        $data = M('RefundTicketConfig')->where($selWhere)->order('id ASC')->select();
        $this->assign('data', $data);
        $this->display('Tpl/Admin/Configure/refund_ticket_config.html');
    }


    private function consoleOrder($mchid, $orderNo)
    {
        $mchUrl = '';
        /**
         * @Date 2022.07.14
         * <AUTHOR>
         * @version 3.3
         */
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if ($token['status'] == 'success'
            && $token['data']['access_token']) {

            session('mch_access_token', $token['data']['access_token']);
            if ($this->admin_group_id == 2) {
                $mchUrl = C('CC_MCH_HOST') . "/#/orderManage/tarinsManage?orderNo={$orderNo}&token=" . $token['data']['access_token'];
            }
        }
        return $mchUrl;
    }
}

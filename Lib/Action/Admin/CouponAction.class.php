<?php



class CouponAction extends AdminCoreAction
{
    //添加或修改优惠券
    public function doAddCoupon($type, $name, $rule, $value, $summary, $start_time = null, $end_time = null, $id=0)
    {
        try {
            $coupon = new CouponModel();
            $data = $coupon->create();
            if($type == \CommonDefine::COUPON_TYYE_1) {
                if(!is_numeric($value) or $value <= 0) {
                    return $this->output(new ResultModel(false, '优惠金额必须大于0'));
                }
            } elseif($type == \CommonDefine::COUPON_TYYE_2) {
                if(!is_numeric($value) || $value < 1 || $value > 9.9) {
                    return $this->output(new ResultModel(false, '优惠折扣必须大于等于1,小于等于9.9'));
                }
            }

            foreach($data as $key=>$value) {
                if($value=="") {
                    return $this->output(new ResultModel(false, '请完善内容后再提交'));
                }
            }
            if($id != 0) {
                unset($data['create_time']);
                $res= $coupon->where(['mchid'=>$this->state->user_id,'coupon_id'=>$id])->save($data);
            } else {
                $data['is_del']=\CommonDefine::IS_DEL_0;
                $data['mchid']=$this->state->user_id;
                $res=$coupon->add($data);
            }
            if($res) {
                return $this->output(new ResultModel(true, '保存成功'));
            } else {
                return $this->output(new ResultModel(false, '保存失败，请稍后再试'));
            }
        } catch (Exception $e) {
            return $this->output(new ResultModel(false, '保存失败，请稍后再试'));
        }
    }
    //获取优惠券列表
    public function getCouponList($page, $size)
    {
        $info= $this->select(['is_del'=>\CommonDefine::IS_DEL_0,'mchid'=>$this->state->user_id], $page, $size, 'create_time desc', 'Coupon');
        echo jsonp_encode($info->ret, $info->data, $info->count);
        exit();
    }
    //删除优惠券
    public function delCoupon($id)
    {
        $info= $this->save('Coupon', ['is_del'=>\CommonDefine::IS_DEL_1,'coupon_id'=>$id]);
        echo jsonp_encode($info->ret, $info->data);
        exit();
    }
    //获取用户优惠券
    public function getCouponRecordList($market_activity_id, $page = 1, $size = 10)
    {
        $couponRecordR = new ResultModel(false);
        $where = " cr.is_del = ".\CommonDefine::IS_DEL_0;
        $where .= " AND cr.mchid = ".$this->state->user_id;
        $where .= " AND cr.market_activity_id = ".$market_activity_id;

        $fields = "cr.coupon_record_id,cr.name as activity_name,cr.status,cr.receive_time,cr.passenger_id,c.name as coupon_name,cr.type,c.rule, cr.value, cr.start_time,cr.end_time,cr.create_time,p.name as nick_name,p.cellphone,p.real_name";

        $info = M()->table('cp_coupon_record as cr')
            ->join('LEFT JOIN cp_market_activity ma ON ma.market_activity_id = cr.market_activity_id')
            ->join('LEFT JOIN cp_coupon  c ON c.coupon_id = cr.coupon_id')
            ->join('LEFT JOIN cp_passenger p ON p.passenger_id = cr.passenger_id')
            ->where($where)
            ->field($fields)
            ->page($page, $size)
            ->order("coupon_record_id desc")
            ->select();
        foreach($info as $k=>&$v) {
            if(!empty($v['passenger_id'])) {
                if(!empty($v['nick_name'])) {
                    $v['passenger_info'] = "昵称：".$v['nick_name']."<br/>";
                }
                if(!empty($v['real_name'])) {
                    $v['passenger_info'] = empty($v['passenger_info']) ? "真实姓名：".$v['real_name']."<br/>" : $v['passenger_info']."真实姓名：".$v['real_name']."<br/>";
                }
                if(!empty($v['cellphone'])) {
                    $v['passenger_info'] = empty($v['passenger_info']) ? "手机号码：".$v['cellphone']."<br/>" : $v['passenger_info']."手机号码：".$v['cellphone']."<br/>";
                }
            } else {
                $v['passenger_info'] = "";
            }
        }
        $count = M()->table('cp_coupon_record as cr')
            ->where($where)
            ->count();
        if($info) {
            $couponRecordR->ret = true;
            $couponRecordR->data = $info;
            $couponRecordR->count = $count;
            return $this->output($couponRecordR);
        }
        return $this->output($couponRecordR);
    }

    //删除优惠券
    public function doDelCouponRecord($coupon_record_id)
    {
        $delR = $this->save('CouponRecord', ['is_del'=>\CommonDefine::IS_DEL_1,'coupon_record_id'=>$coupon_record_id]);
        return $this->output($delR);
    }


    /**
     * 发放优惠券
     * @param $market_activity_id
     * @param $coupon_id
     * @param null $mobiles
     * @param int $passenger_type 人群类型
     * @return null|resultModel
     */
    public function doGiveOutConpons($market_activity_id, $total_count = 0, $mobiles = null, $passenger_type = 0)
    {
        $marketActivityR = $this->find(array('market_activity_id' => $market_activity_id, 'mchid' => $this->state->user_id), 'MarketActivity');
        if(!$marketActivityR->ret) {
            return $this->output(new \ResultModel(false, '系统异常，请稍后再试！'));
        }
        if(strtotime($marketActivityR->data['end_time']) <= time()) {
            return $this->output(new \ResultModel(false, '活动已过期，发放失败！'));
        }

        $couponR = $this->find(array('coupon_id' => $marketActivityR->data['coupon_id'], 'mchid' => $this->state->user_id), 'Coupon');
        if(!$couponR->ret) {
            return $this->output(new \ResultModel(false, '优惠券已删除或不存在！'));
        }

        //优惠券
        $couponRecordData['ticket_no'] = sprintf('T%s', substr(md5(time()), 10, 8));
        $couponRecordData['market_activity_id'] = $market_activity_id;
        $couponRecordData['name'] = $couponR->data['name'];
        $couponRecordData['coupon_id'] = $marketActivityR->data['coupon_id'];
        $couponRecordData['mchid'] = $marketActivityR->data['mchid'];
        $couponRecordData['start_time'] = $marketActivityR->data['start_time'];
        $couponRecordData['end_time'] = $marketActivityR->data['end_time'];
        $couponRecordData['type'] = $couponR->data['type'];
        $couponRecordData['value'] = $couponR->data['value'];

        if($marketActivityR->data['give_out_type'] == \CommonDefine::GIVE_OUT_TYYE_1) {
            if (!is_numeric($total_count) or is_numeric($total_count) < 0) {
                if ($total_count < 0 && $total_count > 10000) {
                    return $this->output(new ResultModel(false, '发放总数不能小于0大于10000'));
                }
            }
            $this->startTrans();
            $updateMarketActivityData['total_count'] = $total_count + $marketActivityR->data['total_count'];
            $updateMarketActivityData['market_activity_id'] = $market_activity_id;
            $marketActivityS = $this->save('MarketActivity', $updateMarketActivityData);
            if (!$marketActivityS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '发放失败，请稍后再试！'));
            }
            for($i = 0; $i < $total_count; $i++) {
                $couponRecordA = $this->add('CouponRecord', $couponRecordData);
                if(!$couponRecordA->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '发放失败，请稍后再试！'));
                }
            }
            $this->commitTrans();
        } elseif($marketActivityR->data['give_out_type'] == \CommonDefine::GIVE_OUT_TYYE_2) {
            $passengerList = [];
            if($passenger_type == 0) {
                if(empty($mobiles)) {
                    return $this->output(new \ResultModel(false, '请填写手机号或导入手机号'));
                }
                $mobilesArr = explode(',', $mobiles);
                if(empty($mobilesArr)) {
                    return $this->output(new \ResultModel(false, '请填正确手机号或导入正确的手机号'));
                }
                $passengerWhere = " is_freeze = ".\CommonDefine::IS_FREEZE_0;
                $passengerWhere .= " AND mchid = ".$this->mchid;
                $passengerWhere .= " AND cellphone in($mobiles) ";
                $passengerList = M()->table('cp_passenger')->where($passengerWhere)->field('passenger_id')->select();
            } else {
                $passengerWhere = " is_freeze = ".\CommonDefine::IS_FREEZE_0;
                $passengerWhere .= " AND mchid = ".$this->mchid;
                $passengerWhere .= " AND passenger_type = ".$passenger_type;
                $passengerWhere .= " AND examine_status = ".\CommonDefine::EXAMINE_STATUS_0;
                $passengerList = M()->table('cp_passenger')->where($passengerWhere)->field('passenger_id')->select();
            }
            if(!empty($passengerList)) {
                $couponRecordData['receive_time'] = date('Y-m-d H:i:s', time());
                $giveOutCount = 0;
                foreach($passengerList as $passenger) {
                    $couponRecordData['passenger_id'] = $passenger['passenger_id'];
                    for($i = 0; $i < $marketActivityR->data['rule']; $i++) {
                        $couponRecordA = $this->add('CouponRecord', $couponRecordData);
                        if(!$couponRecordA->ret) {
                            return $this->output(new \ResultModel(false, '发放失败，请稍后再试！'));
                        }
                        $giveOutCount ++;
                    }
                }
                if($giveOutCount > 0) {
                    $updateMarketActivityData['total_count'] = $giveOutCount + $marketActivityR->data['total_count'];
                    $updateMarketActivityData['recieve_count'] = $giveOutCount + $marketActivityR->data['recieve_count'];
                    $updateMarketActivityData['market_activity_id'] = $market_activity_id;
                    ;
                    $marketActivityS = $this->save('MarketActivity', $updateMarketActivityData);
                    if (!$marketActivityS->ret) {
                        return $this->output(new \ResultModel(false, '发放失败，请稍后再试！'));
                    }
                } else {
                    return $this->output(new \ResultModel(false, '发放失败，请稍后再试！'));
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '系统异常，请稍后再试！'));
        }
        return $this->output(new \ResultModel(true, '发放成功！'));
    }

    /**
     * 导入Excel
     */
    public function doImportExcelData()
    {
        $startTime = microtime(true);

        $uploadFile = $_FILES['excel_file'];
        $imgExArr = array('xls','xlsx');

        if(empty($uploadFile)) {
            return $this->output(new ResultModel(false, '上传失败'));
        }

        $rootDir = C('ROOT_DIR');
        $uploadDir = C('UPLOAD_DIR');

        $fileArr = explode('.', $uploadFile['name']);
        $imgEx = strtolower(end($fileArr));
        if (!in_array($imgEx, $imgExArr)) {
            return $this->output(new \ResultModel(false, '不支持该文件类型'));
        }

        $relatePath = DIRECTORY_SEPARATOR . $uploadDir['UPLOAD_DIR_TEMP'] . DIRECTORY_SEPARATOR .md5($this->state->user_id);
        $fullPath = $rootDir . $relatePath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $realName = substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13) . "." . $imgEx;
        substr(md5($fileArr[0] . rand(1000, 9999)), 0, 13);
        $file = $fullPath . DIRECTORY_SEPARATOR . $realName;

        if (!move_uploaded_file($uploadFile['tmp_name'], $file)) {
            return $this->output(new \ResultModel(false, '上传证件失败'));
        }

        $phpExcelUtil = new \PhpExcelUtil();
        $excelData = $phpExcelUtil->importExecl($file);
        if($excelData['error'] == 0) {
            unlink($file);
            return $this->output(new \ResultModel(false, $excelData['message']));
        } else {

        }

        $mobiles = '';
        foreach($excelData['data'] as $sheet=>$sheetData) {
            if(!empty($sheetData['Content'])) {
                $tempMobiles = $this->cube_implode(',', $sheetData['Content']);
                if(!empty($tempMobiles)) {
                    if(empty($mobiles)) {
                        $mobiles = $tempMobiles;
                    } else {
                        $mobiles = $mobiles.','.$tempMobiles;
                    }
                }
            }
        }

        $mobilesArr = explode(',', $mobiles);
        $totalCount = count($mobilesArr);
        foreach($mobilesArr as $k=>$v) {
            if(empty($v)) {
                unset($mobilesArr[$k]);
            }
        }
        $nullCount = $totalCount - count($mobilesArr);//空格
        $mobilesArr = array_unique($mobilesArr);
        $mobiles = implode(',', $mobilesArr);
        $validMobilsArr = [];
        if(!empty($mobilesArr)) {
            $passengerWhere = " is_freeze = ".\CommonDefine::IS_FREEZE_0;
            $passengerWhere .= " AND mchid = ".$this->mchid;
            $passengerWhere .= " AND cellphone in($mobiles)";
            $passengersR = $this->select($passengerWhere, null, null, null, 'Passenger');

            if($passengersR->ret) {
                foreach($passengersR->data as $passengers) {
                    $validMobilsArr[] = $passengers['cellphone'];
                }
            }
        }
        $mobiles = implode(',', $validMobilsArr);
        $validCount = count($validMobilsArr);
        $repeatCount = $totalCount - $validCount - $nullCount;
        $endTime = microtime(true);

        return $this->output(new ResultModel(true, array('mobiles'=>$mobiles, 'total_count' => $totalCount, 'valid_count' => $validCount,'null_count'=> $nullCount, 'repeat_count' => $repeatCount, 'use_microtime' => ($endTime - $startTime)), 1));
    }
}

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Admin/LogManagerAction');

/**
 * 订单管理模块
 *
 * <AUTHOR>
 */
class OrderManagerAction extends AdminCoreAction
{
    /**
     * 获取订单列表
     * @param int $type 订单类型
     * @param string $fields 查询的字段列表（以英文逗号隔开，默认为空，查询所有字段）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $order_no 订单编号（默认为空，查询所有）
     * @param int $order_id 订单id（默认为空，查询所有）
     * @param string $passenger_cellphone 乘客手机号（默认为空，查询所有）
     * @param string $passenger_name 购票人昵称（默认为空，查询所有）
     * @param string $driver_cellphone 司机手机号（默认为空，查询所有）
     * @param string $driver_name 司机昵称（默认为空，查询所有）
     * @param int $search_branchid 所属分台id（默认为空，查询所有）
     * @param int $drive_mode 路线类型（1-A地到B地，2-出租车，默认为空，查询所有）
     * @param int $start_address_code 出发地编号（默认为空，查询所有）
     * @param int $end_address_code 目的地编号（默认为空，查询所有）
     * @param int $book_seating 预订人数（查询大于等于预订人数，默认为空，查询所有）
     * @param string $car_tail_number 车牌号码
     * @param string $driver_id 所属司机
     * @param int $book_seating 预订人数（查询大于等于预订人数，默认为空，查询所有）
     */
    public function getOrders($fields = null, $page = 1, $size = 10, $type = null, $pay_type = null, $order_state = null, $order_id = null, $order_no = null, $passenger_cellphone = null, $passenger_name = null, $driver_cellphone = null, $driver_name = null, $search_branchid = 0, $drive_mode = null, $start_address_code = null, $end_address_code = null, $book_seating = null, $search_start_time = null, $search_end_time = null, $start_time = null, $end_time = null, $line_id = null, $order_by = null, $start_address_remark = null, $end_address_remark = null, $car_tail_number = null, $driver_id = null, $start_name = null, $end_name = null)
    {
        $where = array();

        if($this->isEmpty($type)) {
            if(in_array($type, array(\CommonDefine::ORDER_TYPE_1,
                                    \CommonDefine::ORDER_TYPE_2,
                                    \CommonDefine::ORDER_TYPE_3,
                                    \CommonDefine::ORDER_TYPE_4,
                                    \CommonDefine::ORDER_TYPE_5,
                                    \CommonDefine::ORDER_TYPE_6,
                                    \CommonDefine::ORDER_TYPE_7,
                                    \CommonDefine::ORDER_TYPE_11))) {
                $where['a.type'] = $type;
            }
        }

        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " a.deleted_at IS NULL ";

        if($this->isEmpty($pay_type)) {
            if($pay_type == 1) {
                if(empty($where['_string'])) {
                    $where['_string'] = "(a.is_pay=1 OR a.is_pre_pay=1)";
                } else {
                    $where['_string'] .= " AND ( a.is_pay=1 OR a.is_pre_pay=1 )";
                }
            } elseif($pay_type == 2) {
                if(empty($where['_string'])) {
                    $where['_string'] = " (a.is_pay=0 AND a.is_pre_pay=0) ";
                } else {
                    $where['_string'] .= " AND ( a.is_pay=0 AND a.is_pre_pay=0 )";
                }
            }
        }

        if($this->isEmpty($order_state)) {
            if(in_array($order_state, array(1,2,3,4,5,6,7,8))) {
                if($order_state == 1) {
                    $where['a.state'] = $order_state;
                    $where['a.appoint'] = 0;
                } elseif($order_state == 2) {
                    $where['a.state'] = 1;
                    $where['a.appoint'] = 1;
                } else {
                    $where['a.state'] = $order_state;
                }
            }
        }

        if (!empty($order_id)) {
            $where['a.order_id'] = $order_id;
        }
        if (!empty($order_no)) {
            $where['a.order_no'] = $order_no;
        }
        if (!empty($passenger_name)) {
            $passenger_name = trim($passenger_name);
            $where['Passenger.name'] = array('LIKE',"$passenger_name%");
        }
        if (!empty($passenger_cellphone)) {
            $passenger_cellphone = trim($passenger_cellphone);
            $where['a.reseverd_phone'] = array('LIKE',"$passenger_cellphone%");
        }
        if (!empty($driver_name)) {
            $driver_name = trim($driver_name);
            $where['Driver.name'] = array('LIKE',"$driver_name%");
        }
        if (!empty($driver_cellphone)) {
            $driver_cellphone = trim($driver_cellphone);
            $where['Driver.cellphone'] = array('LIKE',"$driver_cellphone%");
        }

        if(!empty($search_start_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.create_time >= '".$search_start_time." 00:00:00'";
            } else {
                $where['_string'] .= " AND a.create_time >= '".$search_start_time." 00:00:00'";
            }
        }
        if(!empty($search_end_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.create_time <= '".$search_end_time." 23:59:59'";
            } else {
                $where['_string'] .= " AND a.create_time <= '".$search_end_time." 23:59:59'";
            }
        }

        if(!empty($start_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.start_time >= '".$start_time."'";
            } else {
                $where['_string'] .= " AND a.start_time >= '".$start_time."'";
            }
        }
        if(!empty($end_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.start_time <= '".$end_time."'";
            } else {
                $where['_string'] .= " AND a.start_time <= '".$end_time."'";
            }
        }
        if(!empty($start_address_remark)) {
            $where['a.start_address_remark'] = array('LIKE',"$start_address_remark%");
        }
        if(!empty($end_address_remark)) {
            $where['a.end_address_remark'] = array('LIKE',"$end_address_remark%");
        }

        $orderByString = 'a.order_id desc';
        if(!empty($order_by)) {
            switch ($order_by) {
                case 1:{
                    $orderByString = 'a.start_time desc';
                }
                case 2:{
                    $orderByString = 'a.start_time asc';
                }
            }
        }

        if(!empty($car_tail_number)) {
            $car_tail_number = trim($car_tail_number);
            $where['Driver.car_tail_number'] = array('LIKE',"$car_tail_number%");
        }

        if(!empty($driver_id)) {
            $where['Driver.driver_id'] = $driver_id;
        }

        if($this->admin_group_id == 2) {
            $where['a.mchid'] = $this->state->user_id;
            if (!empty($search_branchid)) {
                $where['a.branchid'] = $search_branchid;
            }
        } elseif($this->admin_group_id == 3) {
            $where['a.branchid'] = $this->state->user_id;
        }
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        $data = null;
        $count = 0;
        switch($type) {
            case \CommonDefine::ORDER_TYPE_5:{
                $lineClassTrainTable = (new LineClassTrainModel())->getTableName();
                $lineClassTable = (new LineClassModel())->getTableName();
                if(!empty($start_name)) {
                    $where['lc.start_name'] = $start_name;
                }
                if(!empty($end_name)) {
                    $where['lc.end_name'] = $end_name;
                }

                $dataQuery = $orderModel->alias('a');
                if (!empty($passenger_name)) {
                    $dataQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");
                }
                if(!empty($car_tail_number)
                    || !empty($driver_id)
                    || !empty($driver_cellphone)
                    || !empty($driver_name)) {
                    $dataQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");
                }
                $data = $dataQuery->join("$lineClassTrainTable lct ON a.line_id = lct.line_class_train_id")
                ->join("$lineClassTable lc ON lc.id=lct.line_class_id")
                ->where($where)
                ->page($page, $size)
                ->field('a.order_id')
                ->order($orderByString)
                ->select();

                $counQuery = $orderModel->alias('a');
                if (!empty($passenger_name)) {
                    $counQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");
                }
                if(!empty($car_tail_number)
                    || !empty($driver_id)
                    || !empty($driver_cellphone)
                    || !empty($driver_name)) {
                    $counQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");
                }
                $count = $counQuery->join("$lineClassTrainTable lct ON a.line_id = lct.line_class_train_id")
                ->join("$lineClassTable lc ON lc.id=lct.line_class_id")
                ->where($where)
                ->count('a.order_id');
                break;
            }
            default:{
                $dataQuery = $orderModel->alias('a');
                $dataQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");
                $dataQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");

                // if (!empty($passenger_name)) {
                //     $dataQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");
                // }
                // if(!empty($car_tail_number)
                //     || !empty($driver_id)
                //     || !empty($driver_cellphone)
                //     || !empty($driver_name)) {
                //     $dataQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");
                // }
                $data = $dataQuery->where($where)
                    ->page($page, $size)
                    ->field('a.order_id')
                    ->order($orderByString)
                    ->select();

                $counQuery = $orderModel->alias('a');
                // if (!empty($passenger_name)) {
                //     $counQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");
                // }
                // if(!empty($car_tail_number)
                //     || !empty($driver_id)
                //     || !empty($driver_cellphone)
                //     || !empty($driver_name)) {
                //     $counQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");
                // }
                $counQuery->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id");
                $counQuery->join("$driverTable Driver ON a.driver_id=Driver.driver_id");

                $count = $counQuery->where($where)->count('a.order_id');
                break;
            }
        }

        if ($data) {
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("order_id", $value['order_id'], 'OrderManager', 'getOrder', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;

                    $virtualDriverR = $this->find(array('driver_id' => $t->data['virtual_driver']), 'Driver', 'driver_id,name');
                    if($virtualDriverR->ret) {
                        $r->data[$key]['route'] = $virtualDriverR->data['name'];
                    }
                } else {
                    return $this->output($t);
                }
                unset($t);
            }
        }

        foreach ($r->data as $k => $v) {
            $r->data[$k]['start_time'] = substr($v['start_time'], 0, 10)."<br/>".substr($v['start_time'], 10, 9);
            $r->data[$k]['create_time'] = substr($v['create_time'], 0, 10)."<br/>".substr($v['create_time'], 10, 9);
            $r->data[$k]['states'] = $r->data[$k]['state'];
            $r->data[$k]['admin_group_id'] = $this->admin_group_id;
            if(!empty($v['branchid'])) {
                $tem = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'mchname,cellphone');
                $r->data[$k]['branchName'] = $tem->data['mchname']."[电话：".$tem->data['cellphone']."]";
            }

            //1-乘客预定，2-车主接单，3-车主确认乘客上车，4-在路上，5-已送达(未支付)，|6-正常完成，7-取消，8-已拒绝
            $orderStateInfoR = $this->getOrderStateInfo($r->data[$k]['order_id']);
            if($orderStateInfoR->ret) {
                $r->data[$k]['state_message'] = $orderStateInfoR->data['state_message'];
                if($orderStateInfoR->data['pay_state'] == 1) {
                    $r->data[$k]['pay_message'] = "<span style='color: green'>已支付</span>";
                } else {
                    $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                }

                $r->data[$k]['order_close_onoff'] = $orderStateInfoR->data['order_close_onoff'];
                $r->data[$k]['order_refund_onoff'] = $orderStateInfoR->data['order_refund_onoff'];
                $r->data[$k]['order_over_onoff'] = $orderStateInfoR->data['order_over_onoff'];
            } else {
                $r->data[$k]['state_message'] = '';
                $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                $r->data[$k]['order_close_onoff'] = 0;
                $r->data[$k]['order_refund_onoff'] = 0;
                $r->data[$k]['order_over_onoff'] = 0;
            }

            $r->data[$k]['price'] = "<p>订单原价：".$v['price']." 元</p>".
                "<p>车票金额：".$v['merge_price']." 元</p>".
                "<p>优惠金额：".$v['coupon_price']." 元</p>".
                "<p>渠道所得：".$v['offer_channel_price']." 元</p>".
                "<p>司机所得：".$v['offer_price']." 元</p>".
                "<p>总台分成：".($v['split'] * 100)."%</p>".
                "<p>总台分成金额：".$v['offer_boss_price']." 元</p>";

            if($r->data[$k]['is_buy_insurance'] == 1) {
                $r->data[$k]['price'] .= "<p>保险金额：".$v['insurance_price']." 元</p>";
            }
            $r->data[$k]['price'] .= "<p style='color: red;'>订单总额(实付金额)：".$v['real_price']." 元</p>";

            if($r->data[$k]['state'] == \CommonDefine::ORDER_STATE_7) {
                $refundInfo = "<p>退票费率：".$v['refund_ticket_ratio']." %</p>".
                              "<p>退票手续费：".$v['refund_fee']." 元</p>".
                              "<p>车票退款金额：".$v['refund_amount']." 元</p>";
                if($r->data[$k]['is_buy_insurance'] == 1) {
                    $refundInfo .= "<p>保险退款金额：".$v['insurance_price']." 元</p>";
                }
                $refundInfo .= "<p style='color: red;'>退款总额：".$v['refund_total_amount']." 元</p>";
                $r->data[$k]['price'] .= $refundInfo;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取售票订单列表
     * @param int $type 订单类型
     * @param string $fields 查询的字段列表（以英文逗号隔开，默认为空，查询所有字段）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $order_no 订单编号（默认为空，查询所有）
     * @param int $order_id 订单id（默认为空，查询所有）
     * @param string $passenger_cellphone 乘客手机号（默认为空，查询所有）
     * @param string $passenger_name 购票人昵称（默认为空，查询所有）
     * @param string $driver_cellphone 司机手机号（默认为空，查询所有）
     * @param string $driver_name 司机昵称（默认为空，查询所有）
     * @param int $search_branchid 所属分台id（默认为空，查询所有）
     * @param int $drive_mode 路线类型（1-A地到B地，2-出租车，默认为空，查询所有）
     * @param int $start_address_code 出发地编号（默认为空，查询所有）
     * @param int $end_address_code 目的地编号（默认为空，查询所有）
     * @param int $book_seating 预订人数（查询大于等于预订人数，默认为空，查询所有）
     * @param string $car_tail_number 车牌号码
     * @param string $driver_id 所属司机
     * @param int $book_seating 预订人数（查询大于等于预订人数，默认为空，查询所有）
     */
    public function getSellOrders($fields = null, $page = 1, $size = 10, $type = null, $pay_type = null, $order_state = null, $order_id = null, $order_no = null, $passenger_cellphone = null, $passenger_name = null, $driver_cellphone = null, $driver_name = null, $search_branchid = 0, $drive_mode = null, $start_address_code = null, $end_address_code = null, $book_seating = null, $search_start_time = null, $search_end_time = null, $start_time = null, $end_time = null, $line_id = null, $order_by = null, $start_address_remark = null, $end_address_remark = null, $car_tail_number = null, $driver_id = null)
    {
        $where = array();
        $this->doAddLog("查看订单列表");

        if($this->isEmpty($type)) {
            if(in_array($type, array(\CommonDefine::ORDER_TYPE_1,
                \CommonDefine::ORDER_TYPE_2,
                \CommonDefine::ORDER_TYPE_3,
                \CommonDefine::ORDER_TYPE_4,
                \CommonDefine::ORDER_TYPE_5,
                \CommonDefine::ORDER_TYPE_6,
                \CommonDefine::ORDER_TYPE_7,
                \CommonDefine::ORDER_TYPE_11))) {
                $where['a.type'] = $type;
            }
        }

        # 5.31增加支持逻辑订单逻辑删除
        $where['_string'] = " a.deleted_at IS NULL ";

        if($this->isEmpty($pay_type)) {
            if($pay_type == 1) {
                if(empty($where['_string'])) {
                    $where['_string'] = "(a.is_pay=1 OR a.is_pre_pay=1)";
                } else {
                    $where['_string'] .= " AND ( a.is_pay=1 OR a.is_pre_pay=1 )";
                }
            } elseif($pay_type == 2) {
                if(empty($where['_string'])) {
                    $where['_string'] = " (a.is_pay=0 AND a.is_pre_pay=0) ";
                } else {
                    $where['_string'] .= " AND ( a.is_pay=0 AND a.is_pre_pay=0 )";
                }
            }
        }

        if($this->isEmpty($order_state)) {
            if(in_array($order_state, array(1,2,3,4,5,6,7,8))) {
                if($order_state == 1) {
                    $where['a.state'] = $order_state;
                    $where['a.appoint'] = 0;
                } elseif($order_state == 2) {
                    $where['a.state'] = 1;
                    $where['a.appoint'] = 1;
                } else {
                    $where['a.state'] = $order_state;
                }
            }
        }

        if (!empty($order_id)) {
            $where['a.order_id'] = $order_id;
        }
        if (!empty($order_no)) {
            $where['a.order_no'] = $order_no;
        }
        if (!empty($passenger_name)) {
            $passenger_name = trim($passenger_name);
            $where['Passenger.name'] = array('LIKE',"$passenger_name%");
        }
        if (!empty($passenger_cellphone)) {
            $passenger_cellphone = trim($passenger_cellphone);
            $where['a.reseverd_phone'] = array('LIKE',"$passenger_cellphone%");
        }
        if (!empty($driver_name)) {
            $driver_name = trim($driver_name);
            $where['Driver.name'] = array('LIKE',"$driver_name%");
        }
        if (!empty($driver_cellphone)) {
            $driver_cellphone = trim($driver_cellphone);
            $where['Driver.cellphone'] = array('LIKE',"$driver_cellphone%");
        }

        if(!empty($search_start_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.create_time >= '".$search_start_time." 00:00:00'";
            } else {
                $where['_string'] .= " AND a.create_time >= '".$search_start_time." 00:00:00'";
            }
        }
        if(!empty($search_end_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.create_time <= '".$search_end_time." 23:59:59'";
            } else {
                $where['_string'] .= " AND a.create_time <= '".$search_end_time." 23:59:59'";
            }
        }

        if(!empty($start_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.start_time >= '".$start_time."'";
            } else {
                $where['_string'] .= " AND a.start_time >= '".$start_time."'";
            }
        }
        if(!empty($end_time)) {
            if(empty($where['_string'])) {
                $where['_string'] = " a.start_time <= '".$end_time."'";
            } else {
                $where['_string'] .= " AND a.start_time <= '".$end_time."'";
            }
        }
        if(!empty($start_address_remark)) {
            $where['a.start_address_remark'] = array('LIKE',"$start_address_remark%");
        }
        if(!empty($end_address_remark)) {
            $where['a.end_address_remark'] = array('LIKE',"$end_address_remark%");
        }

        if(!empty($line_id)) {
            $where['a.line_id'] = $line_id;
        }

        $orderByString = 'a.create_time desc';
        if(!empty($order_by)) {
            switch ($order_by) {
                case 1:{
                    $orderByString = 'a.start_time desc';
                }
                case 2:{
                    $orderByString = 'a.start_time asc';
                }
            }
        } else {
            if($this->mchid == 1258) {//信泰出行
                $orderByString = 'a.start_time asc';
            }
        }

        if(!empty($car_tail_number)) {
            $car_tail_number = trim($car_tail_number);
            $where['Driver.car_tail_number'] = array('LIKE',"$car_tail_number%");
        }

        if(!empty($driver_id)) {
            $where['Driver.driver_id'] = $driver_id;
        }

        if($this->admin_group_id == 2) {
            $where['a.mchid'] = $this->state->user_id;
            if (!empty($search_branchid)) {
                $where['a.branchid'] = $search_branchid;
            }
        } elseif($this->admin_group_id == 3) {
            $where['a.branchid'] = $this->state->user_id;
        }
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        $data = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->page($page, $size)->field('a.order_id')->order($orderByString)->select();
        if ($data) {
            $count = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->count('a.order_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("order_id", $value['order_id'], 'OrderManager', 'getOrder', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;

                    $virtualDriverR = $this->find(array('driver_id' => $t->data['virtual_driver']), 'Driver', 'driver_id,name');
                    if($virtualDriverR->ret) {
                        $r->data[$key]['route'] = $virtualDriverR->data['name'];
                    }
                } else {
                    return $this->output($t);
                }
                unset($t);
            }
        }

        foreach ($r->data as $k => $v) {
            $r->data[$k]['start_time'] = substr($v['start_time'], 0, 10)."<br/>".substr($v['start_time'], 10, 9);
            $r->data[$k]['create_time'] = substr($v['create_time'], 0, 10)."<br/>".substr($v['create_time'], 10, 9);
            $r->data[$k]['states'] = $r->data[$k]['state'];
            $r->data[$k]['admin_group_id'] = $this->admin_group_id;
            if(!empty($v['branchid'])) {
                $tem = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'mchname,cellphone');
                $r->data[$k]['branchName'] = $tem->data['mchname']."[电话：".$tem->data['cellphone']."]";
            }

            //1-乘客预定，2-车主接单，3-车主确认乘客上车，4-在路上，5-已送达(未支付)，|6-正常完成，7-取消，8-已拒绝
            $orderStateInfoR = $this->getOrderStateInfo($r->data[$k]['order_id']);
            if($orderStateInfoR->ret) {
                $r->data[$k]['state_message'] = $orderStateInfoR->data['state_message'];
                if($orderStateInfoR->data['pay_state'] == 1) {
                    $r->data[$k]['pay_message'] = "<span style='color: green'>已支付</span>";
                } else {
                    $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                }

                $r->data[$k]['order_close_onoff'] = $orderStateInfoR->data['order_close_onoff'];
                $r->data[$k]['order_refund_onoff'] = $orderStateInfoR->data['order_refund_onoff'];
                $r->data[$k]['order_over_onoff'] = $orderStateInfoR->data['order_over_onoff'];
            } else {
                $r->data[$k]['state_message'] = '';
                $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                $r->data[$k]['order_close_onoff'] = 0;
                $r->data[$k]['order_refund_onoff'] = 0;
                $r->data[$k]['order_over_onoff'] = 0;
            }

            $r->data[$k]['price'] = "<p>订单原价：".$v['price']." 元</p>".
                "<p>车票金额：".$v['merge_price']." 元</p>".
                "<p>优惠金额：".$v['coupon_price']." 元</p>".
                "<p>渠道所得：".$v['offer_channel_price']." 元</p>".
                "<p>司机所得：".$v['offer_price']." 元</p>".
                "<p>总台分成：".($v['split'] * 100)."%</p>".
                "<p>总台分成金额：".$v['offer_boss_price']." 元</p>";

            if($r->data[$k]['is_buy_insurance'] == 1) {
                $r->data[$k]['price'] .= "<p>保险金额：".$v['insurance_price']." 元</p>";
            }
            $r->data[$k]['price'] .= "<p style='color: red;'>订单总额(实付金额)：".$v['real_price']." 元</p>";

            if($r->data[$k]['state'] == \CommonDefine::ORDER_STATE_7) {
                $refundInfo = "<p>退票费率：".$v['refund_ticket_ratio']." %</p>".
                    "<p>退票手续费：".$v['refund_fee']." 元</p>".
                    "<p>车票退款金额：".$v['refund_amount']." 元</p>";
                if($r->data[$k]['is_buy_insurance'] == 1) {
                    $refundInfo .= "<p>保险退款金额：".$v['insurance_price']." 元</p>";
                }
                $refundInfo .= "<p style='color: red;'>退款总额：".$v['refund_total_amount']." 元</p>";
                $r->data[$k]['price'] .= $refundInfo;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取摆渡车订单列表
     * @param string $fields 查询的字段列表（以英文逗号隔开，默认为空，查询所有字段）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $order_id 订单id（默认为空，查询所有）
     */
    public function getFerryOrders($fields = null, $page = 1, $size = 10, $order_id = null)
    {
        $orderSubWhere['order_id'] = $order_id;
        $orderSubRs = $this->select($orderSubWhere, null, null, null, 'OrderSub', 'relation_order_id');
        if(!$orderSubRs->ret) {
            return $this->output($orderSubRs);
        }
        $order_ids = '';
        foreach($orderSubRs->data as $k => $subOrder) {
            if(empty($order_ids)) {
                $order_ids .= $subOrder['relation_order_id'];
            } else {
                $order_ids .= (",".$subOrder['relation_order_id']);
            }
        }

        $where = "a.order_id in($order_ids)";
        $orderByString = 'a.create_time desc';
        $orderModel = new OrderModel();
        $passengerTable = (new PassengerModel())->getTableName();
        $driverTable = (new DriverModel())->getTableName();
        $data = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->page($page, $size)->field('a.order_id')->order($orderByString)->select();
        if ($data) {
            $count = $orderModel->alias('a')->join("$passengerTable Passenger ON a.passenger_id=Passenger.passenger_id")->join("$driverTable Driver ON a.driver_id=Driver.driver_id")->where($where)->count('a.order_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }

        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("order_id", $value['order_id'], 'OrderManager', 'getOrder', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;

                    $virtualDriverR = $this->find(array('driver_id' => $t->data['virtual_driver']), 'Driver', 'driver_id,name');
                    if($virtualDriverR->ret) {
                        $r->data[$key]['route'] = $virtualDriverR->data['name'];
                    }
                } else {
                    return $this->output($t);
                }
                unset($t);
            }
        }

        foreach ($r->data as $k => $v) {
            $r->data[$k]['start_time'] = substr($v['start_time'], 0, 10)."<br/>".substr($v['start_time'], 10, 9);
            $r->data[$k]['create_time'] = substr($v['create_time'], 0, 10)."<br/>".substr($v['create_time'], 10, 9);
            $r->data[$k]['states'] = $r->data[$k]['state'];
            $r->data[$k]['admin_group_id'] = $this->admin_group_id;
            if(!empty($v['branchid'])) {
                $tem = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'mchname,cellphone');
                $r->data[$k]['branchName'] = $tem->data['mchname']."[电话：".$tem->data['cellphone']."]";
            }

            //1-乘客预定，2-车主接单，3-车主确认乘客上车，4-在路上，5-已送达(未支付)，|6-正常完成，7-取消，8-已拒绝
            $orderStateInfoR = $this->getOrderStateInfo($r->data[$k]['order_id']);
            if($orderStateInfoR->ret) {
                $r->data[$k]['state_message'] = $orderStateInfoR->data['state_message'];
                if($orderStateInfoR->data['pay_state'] == 1) {
                    $r->data[$k]['pay_message'] = "<span style='color: green'>已支付</span>";
                } else {
                    $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                }

                $r->data[$k]['order_close_onoff'] = $orderStateInfoR->data['order_close_onoff'];
                $r->data[$k]['order_refund_onoff'] = $orderStateInfoR->data['order_refund_onoff'];
                $r->data[$k]['order_over_onoff'] = $orderStateInfoR->data['order_over_onoff'];
            } else {
                $r->data[$k]['state_message'] = '';
                $r->data[$k]['pay_message'] = "<span style='color: red'>未支付</span>";
                $r->data[$k]['order_close_onoff'] = 0;
                $r->data[$k]['order_refund_onoff'] = 0;
                $r->data[$k]['order_over_onoff'] = 0;
            }

            $r->data[$k]['price'] = "<p>订单原价：".$v['price']." 元</p>".
                "<p>车票金额：".$v['merge_price']." 元</p>".
                "<p>优惠金额：".$v['coupon_price']." 元</p>".
                "<p>渠道所得：".$v['offer_channel_price']." 元</p>".
                "<p>司机所得：".$v['offer_price']." 元</p>".
                "<p>总台分成：".($v['split'] * 100)."%</p>".
                "<p>总台分成金额：".$v['offer_boss_price']." 元</p>";

            if($r->data[$k]['is_buy_insurance'] == 1) {
                $r->data[$k]['price'] .= "<p>保险金额：".$v['insurance_price']." 元</p>";
            }
            $r->data[$k]['price'] .= "<p style='color: red;'>订单总额(实付金额)：".$v['real_price']." 元</p>";

            if($r->data[$k]['state'] == \CommonDefine::ORDER_STATE_7) {
                $refundInfo = "<p>退票费率：".$v['refund_ticket_ratio']." %</p>".
                    "<p>退票手续费：".$v['refund_fee']." 元</p>".
                    "<p>车票退款金额：".$v['refund_amount']." 元</p>";
                if($r->data[$k]['is_buy_insurance'] == 1) {
                    $refundInfo .= "<p>保险退款金额：".$v['insurance_price']." 元</p>";
                }
                $refundInfo .= "<p style='color: red;'>退款总额：".$v['refund_total_amount']." 元</p>";
                $r->data[$k]['price'] .= $refundInfo;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取订单详细
     * @param int $order_id 订单ID
     * @param string $fields 查询的字段（默认为空，查询所有）
     */
    public function getOrder($order_id, $fields = null)
    {
        $r = $this->getEntityById($order_id, 'Order', $fields, null, 'is_temp,order_no,type,branchid,temp_apply_branchid,drive_mode,start_address_remark,start_address_code,end_address_remark,end_address_code,address_code,driver_id,passenger_id,line_id,agency_id,offer_price,split,start_address_remark,end_address_remark,start_region_name,end_region_name,appoint,seat_optional,refund_ticket_ratio,refund_fee,refund_amount,ferry_type');
        if ($r->ret) {
            $where['id'] = $r->data['line_id'];
            if($r->data['type'] == \CommonDefine::ORDER_TYPE_1 || $r->data['type'] == \CommonDefine::ORDER_TYPE_3) {
                $lineR = $this->find($where, 'Line');
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_2) {
                $lineR = $this->find($where, 'LineChartered');
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_4) {
                $r->data['route'] = '';
                $agencyR = $this->find(array('agency_id' => $r->data['agency_id']), 'Agency');
                if($agencyR->ret) {
                    $r->data['route'] = "标题：".$agencyR->data['name'].'<br/>'. "内容：".$agencyR->data['summary'];
                }
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                if(!empty($r->data['seat_optional'])) {
                    $r->data['seat_optional_info'] = $this->getOrderSeatOptionInfo($r->data['seat_optional']);
                }
                $lineClassTrainR = $this->find(array('line_class_train_id' => $r->data['line_id']), 'LineClassTrain');
                if($lineClassTrainR->ret) {
                    $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    $viaPointsR = $this->find(array('line_class_id' => $lineClassTrainR->data['line_class_id']), 'DingzhikeyunLineViaPoints');
                    $viaString = $viaPointsR->data['name']
                                ? "<span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$viaPointsR->data['name']."</span>&nbsp;➞&nbsp;"
                                : "";
                    if($lineClassR->ret) {
                        $r->data['route'] = "<b style='color: gray;'>&nbsp;线路：</b>
                        <span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineClassR->data['start_name']."</span>&nbsp;➞&nbsp;".
                        $viaString.
                        "<span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineClassR->data['end_name']
                        ."</span><br/>"
                            ."<b style='color: gray;'>  &nbsp;班次编号：</b>" .$lineClassTrainR->data['line_class_train_no'] ."<br/>"
                            ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['start_address_remark']."'>上：".$r->data['start_address_remark'] ."</span><br/>"
                            ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['end_address_remark']."'>下：".$r->data['end_address_remark']."</span><br/>";
                    }
                }
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_6) {
                if(empty($r->data['line_id'])) {//乘客发布的
                    $r->data['start'] = $r->data['start_region_name'].$r->data['start_address_remark'];
                    $r->data['end'] = $r->data['end_region_name'].$r->data['end_address_remark'];
                    $r->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$r->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>";
                } else {
                    $LineFreeRideR = $this->find(array('id' => $r->data['line_id']), 'LineFreeRide');
                    if($LineFreeRideR->ret) {
                        $r->data['start'] = $LineFreeRideR->data['start_name'].$LineFreeRideR->data['start_address_remark'];
                        $r->data['end'] = $LineFreeRideR->data['end_name'].$LineFreeRideR->data['end_address_remark'];
                        $r->data['route'] = "<b style='color: green;'> &nbsp;起：</b>".$r->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$r->data['end']."<br/>";
                    }
                }
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_7) {
                $lineFastR = $this->find($where, 'LineFast');
                if($lineFastR->ret) {
                    $r->data['route'] = "<b style='color: gray;'>  &nbsp;线路：</b><span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineFastR->data['start_name']."</span><br/>"
                        ."<b style='color: gray;'>  &nbsp;线路ID：</b>" .$lineFastR->data['id'] ."<br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['start_address_remark']."'>上：".$r->data['start_address_remark'] ."</span><br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['end_address_remark']."'>下：".$r->data['end_address_remark']."</span><br/>";
                }
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                $lineTaxiR = $this->find($where, 'LineTaxi');
                if($lineTaxiR->ret) {
                    $r->data['route'] = "<b style='color: gray;'>  &nbsp;线路：</b><span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineTaxiR->data['start_name']."</span><br/>"
                        ."<b style='color: gray;'>  &nbsp;线路ID：</b>" .$lineTaxiR->data['id'] ."<br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['start_address_remark']."'>上：".$r->data['start_address_remark'] ."</span><br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['end_address_remark']."'>下：".$r->data['end_address_remark']."</span><br/>";
                }
            }
            $adminR = $this->find(array('admin_id' => $r->data['temp_apply_branchid']), 'Admin', 'mchname,cellphone');

            $r->data['generation'] = '';
            if($adminR->ret) {
                if ($r->data['is_temp'] == 1) {
                    $r->data['generation'] =  $adminR->data['mchname'].'['.$adminR->data['cellphone'].']';
                }
            }
            if($lineR->ret) {
                $startAddress = "[".$this->checkingGdParentAddressCode($lineR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['start_address_code'])->data['address']."]";
                $r->data['start'] = $startAddress . $lineR->data['start_name'];
                $endAddress = "[".$this->checkingGdParentAddressCode($lineR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineR->data['end_address_code'])->data['address']."]";
                $r->data['end'] = $endAddress . $lineR->data['end_name'];
                if($r->data['type'] == \CommonDefine::ORDER_TYPE_3) {
                    $r->data['route'] =
                        "<b style='color: gray;'>  &nbsp;线路：</b><span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineR->data['start_name']."</span>&nbsp;➞&nbsp;<span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineR->data['end_name']."</span><br/>"
                        ."<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."(带货关联拼车线路)<br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['start_address_remark']."'>上：".$r->data['start_address_remark'] ."</span><br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['end_address_remark']."'>下：".$r->data['end_address_remark']."</span><br/>";
                    "<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."(带货关联拼车线路)<br/>"."<b style='color: green;'> &nbsp;起：</b>".$r->data['start'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$r->data['end']."<br/>";
                } else {
                    $r->data['route'] =
                        "<b style='color: gray;'>  &nbsp;线路：</b><span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineR->data['start_name']."</span>&nbsp;➞&nbsp;<span style='background-color:#3399ff;color:white;border-radius:5px;padding:3px;'>".$lineR->data['end_name']."</span><br/>"
                        ."<b style='color: gray;'>  &nbsp;线路编号：</b>".$lineR->data['id'] ."<br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['start_address_remark']."'>上：".$r->data['start_address_remark'] ."</span><br/>"
                        ."<span style='text-overflow: ellipsis;display: inline-block;' title='".$r->data['end_address_remark']."'>下：".$r->data['end_address_remark']."</span><br/>";
                }
                $r->data['branchid'] = $lineR->data['branchid'];
            }

            //出发地
            $t = $this->checkingGdAddressCode($r->data['start_address_code']);
            if ($t->ret) {
                $r->data['start_address'] = $t->data['address'];
            }

            //目的地
            $t = $this->checkingAddressCode($r->data['end_address_code']);
            if ($t->ret) {
                $r->data['end_address'] = $t->data['address'];
            }

            $this->sudoLoadSubEntity($r, 'passenger_id', 'passenger_id', 'Passenger\\Account', 'getPassenger', $fields, 'passenger');

            if($r->data['type'] == \CommonDefine::ORDER_TYPE_1
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_3
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_4
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_5
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_6
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_7
                || $r->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                if(!empty($r->data['driver_id'])) {
                    $this->sudoLoadSubEntity($r, 'driver_id', 'driver_id', 'Driver\\Account', 'getDriver', $fields, 'driver');
                }
            } elseif($r->data['type']  == \CommonDefine::ORDER_TYPE_2) {
                $r->data['driver']  = M()->table('cp_line_chartered_price lcp')
                    ->join("LEFT JOIN cp_car_type ct ON lcp.car_type_id = ct.car_type_id")
                    ->field("name car_type,num total_seating")
                    ->where(array('lcp.line_chartered_price_id' => 1))
                    ->find();
                $driverR = $this->find(array('driver_id' => $r->data['driver_id']), "Driver", 'name,cellphone');
                $r->data['driver']['cellphone'] = $driverR->data['cellphone'];
                $r->data['driver']['name'] = $driverR->data['name'];
            }
            $r->data['order_type'] = $this->getOrderType($r->data['type']);
            if($r->data['type'] == \CommonDefine::ORDER_TYPE_7 && $r->data['ferry_type'] == \CommonDefine::FERRY_TYPE_1) {
                $r->data['order_type'] .= "<br/><span style='color: dimgray'>(摆渡车-接)</span>" ;
            } elseif($r->data['type'] == \CommonDefine::ORDER_TYPE_7 && $r->data['ferry_type'] == \CommonDefine::FERRY_TYPE_2) {
                $r->data['order_type'] .= "<br/><span style='color: dimgray'>(摆渡车-送)</span>";
            }
            unset($r->data['start_region_name']);
            unset($r->data['end_region_name']);
        }
        return $this->output($r);
    }

    /**
     * @param null $fields
     * @param int $page
     * @param int $size
     * @param null $order_no
     * @param null $passenger_name
     * @param null $passenger_cellphone
     * @param null $driver_name
     * @param null $driver_cellphone
     * @param null $book_seating
     */
    public function getPhoneOrders($fields = null, $page = 1, $size = 10, $order_no = null, $search_branchid = null, $driver_name = null, $driver_cellphone = null)
    {
        $phoneOrdersR = new ResultModel(false);
        $where = ' 1 ';

        if(!empty($order_no)) {
            $where .= " AND op.order_no like '%".$order_no."%'";
        }

        if(!empty($search_branchid)) {
            $where .= ' AND op.branchid = '.$search_branchid;
        }

        if(!empty($driver_name)) {
            $where .= " AND d.name like '%".$driver_name."%'";
        }
        if(!empty($driver_cellphone)) {
            $where .= ' AND d.cellphone = '.$driver_cellphone;
        }

        if($this->admin_group_id == 3) {
            $where .= ' AND op.branchid = '.$this->state->user_id;
        } else {
            $where .= ' AND op.mchid = '.$this->mchid;
        }

        $fields = 'op.*,d.name as driver_name,d.cellphone as driver_cellphone,a.mchname as branch_name,l.start_name,l.end_name,p.name as passenger_name,p.cellphone as passenger_cellphone';
        $phoneOrders = M()->table('cp_order_phone op')
            ->join("LEFT JOIN cp_driver d ON d.driver_id = op.driver_id")
            ->join("LEFT JOIN cp_admin a ON a.admin_id = op.branchid")
            ->join("LEFT JOIN cp_line l ON op.line_id = l.id")
            ->join("LEFT JOIN cp_passenger p ON op.passenger_id = p.passenger_id")
            ->where($where)
            ->field($fields)
            ->order('order_id desc')
            ->page($page, $size)
            ->select();

        $phoneOrderCount =  M()->table('cp_order_phone op')
            ->join("LEFT JOIN cp_driver d ON d.driver_id = op.driver_id")
            ->join("LEFT JOIN cp_admin a ON a.admin_id = op.branchid")
            ->join("LEFT JOIN cp_line l ON op.line_id = l.id")
            ->join("LEFT JOIN cp_passenger p ON op.passenger_id = p.passenger_id")
            ->where($where)
            ->field($fields)
            ->count();
        if($phoneOrderCount > 0) {
            foreach($phoneOrders as $k => $phoneOrder) {
                $phoneOrders[$k]['route'] = "<b style='color: gray;'>  &nbsp;线路编号：</b>".$phoneOrder['line_id'] ."<br/>"."<b style='color: green;'> &nbsp;起：</b>".$phoneOrder['start_name'] ."<br/>"."<b style='color: red;'>  &nbsp;终：</b>".$phoneOrder['end_name']."<br/>";
            }

            $phoneOrdersR->ret = true;
            $phoneOrdersR->data = $phoneOrders;
            $phoneOrdersR->count = $phoneOrderCount;
        }

        return $this->output($phoneOrdersR);
    }

    /**
     * @param null $fields
     * @param int $page
     * @param int $size
     * @param null $order_no
     * @param null $passenger_name
     * @param null $passenger_cellphone
     * @param null $driver_name
     * @param null $driver_cellphone
     * @param null $book_seating
     */
    public function getOrderInsurances($fields = null, $page = 1, $size = 10, $order_no = null, $real_name = null, $ID_number = null)
    {
        $orderInsurancesR = new ResultModel(false);
        $where = ' 1 ';

        if(!empty($order_no)) {
            $where .= " AND o.order_no like '%".$order_no."%'";
        }

        if(!empty($real_name)) {
            $where .= " AND oi.real_name like '%".$real_name."%'";
        }

        if(!empty($ID_number)) {
            $where .= " AND oi.ID_number like '%".$ID_number."%'";
        }

        $where .= ' AND o.mchid = '.$this->mchid;


        $fields = 'oi.*,o.mchid,o.order_no,o.order_id';
        $orderInsurances = M()->table('cp_order_insurance oi')
            ->join("LEFT JOIN cp_order o ON o.order_id = oi.order_id")
            ->where($where)
            ->field($fields)
            ->order('oi.order_id desc')
            ->page($page, $size)
            ->select();

        $orderInsuranceCount = M()->table('cp_order_insurance oi')
            ->join("LEFT JOIN cp_order o ON o.order_id = oi.order_id")
            ->where($where)
            ->field($fields)
            ->order('oi.order_id desc')
            ->count();

        foreach ($orderInsurances as $k => $v) {
            if(mb_strlen($v['insurance_desc'], "gbk") > 100) {
                $orderInsurances[$k]['insurance_desc'] = (mb_substr($v['insurance_desc'], 0, 100, "utf-8"))."……";
            }
            $orderInsurances[$k]['insurance_desc_full'] = $v['insurance_desc'];
        }

        if($orderInsuranceCount > 0) {
            $orderInsurancesR->ret = true;
            $orderInsurancesR->data = $orderInsurances;
            $orderInsurancesR->count = $orderInsuranceCount;
        }

        return $this->output($orderInsurancesR);
    }

    /**
     * 获取订单历史状态列表
     * @param int $order_id 订单ID
     * @param string $fields 查询的字段列表（以英文逗号分隔，默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getHistoryStates($order_id, $fields = null, $page = 1, $size = 10)
    {
        $this->doAddLog("查看订单历史状态列表");
        $r = $this->select(array('order_id' => $order_id), $page, $size, 'create_time asc', 'OrderHistoryState', 'id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $r->data[$key] = $this->sudoGetEntityById('id', $value['id'], 'OrderManager', 'getHistoryState', $fields)->data;
            }
        }
        return $this->output($r);
    }

    /**
     * 获取订单历史状态
     * @param int $id ID
     * @param string $fields 查询的字段列表（以英文逗号分隔，默认为空，查询所有）
     */
    public function getHistoryState($id, $fields = null)
    {
        $r = $this->getEntityById($id, 'OrderHistoryState', $fields, 'id,order_id', 'state_id,type');
        if ($r->ret) {
            if ($this->isIncludeRootField($fields, 'state') && intval($r->data['type']) === 1) {
                $t = $this->find(array('state_id' => $r->data['state_id']), 'MetaBookOrderState', 'name');
                if ($t->ret) {
                    $r->data['state'] = $t->data['name'];
                } else {
                    return $this->output($t);
                }
            }
            if ($this->isIncludeRootField($fields, 'state') && intval($r->data['type']) === 2) {
                $t = $this->find(array('state_id' => $r->data['state_id']), 'MetaInviteOrderState', 'name');
                if ($t->ret) {
                    $r->data['state'] = $t->data['name'];
                } else {
                    return $this->output($t);
                }
            }
            remove_arr($r->data, 'state_id');
            remove_arr($r->data, 'type');
        }
        $this->doAddLog("查看订单历史状态");
        return $this->output($r);
    }

    /**
     * 修改订单
     * @param $order_no //订单编号
     * @param $driver_id //指派司机
     * @param $start_time //出发时间
     */
    public function doUpdateOrder($order_no, $driver_id, $start_time, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null)
    {
        $ret = new \ResultModel(false);
        $orderR = $this->find(array('order_no' => $order_no), 'Order');
        $logUserType = \StateModel::$ADMIN_USER;
        if($this->admin_group_id == 2) {
            if($this->state->user_id != $orderR->data['mchid']) {
                $ret->data = "订单异常";
                return $ret;
            }
        } elseif($this->admin_group_id == 3) {
            if($this->state->user_id != $orderR->data['branchid']) {
                $ret->data = "订单异常";
                return $ret;
            }
            $logUserType = \StateModel::$BRANCH_USER;
        }

        if($orderR->data['state'] > 3) {
            $ret->data = "订单状态是已经发生变化，修改失败！";
            return $ret;
        }

        //司机是否正常接单中
        $driverR = new \ResultModel(false);
        if(!empty($driver_id)) {
            $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
            if($driverR->ret) {
                if($driverR->data['state'] == 2 || $driverR->data['state'] == 1) {
                    return $this->output(new \ResultModel(false, '司机在路上'));
                }
                if($driverR->data['state'] == 3) {
                    return $this->output(new \ResultModel(false, '司机暂停接单'));
                }
                if($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                    return $this->output(new \ResultModel(false, '司机剩余座位数不足,请重新选择司机'));
                }
            } else {
                return $this->output(new \ResultModel(false, '司机不存在'));
            }
            $updateOrderData['driver_id'] = $driver_id;
        } else {
            $updateOrderData['driver_id'] = ['exp', 'null'];
            $updateOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
            $updateOrderData['state'] = \CommonDefine::ORDER_STATE_1;
        }

        $updateOrderData['order_id'] = $orderR->data['order_id'];
        $updateOrderData['start_time'] = $start_time;
        $updateOrderData['start_longitude'] = $start_longitude;
        $updateOrderData['start_latitude'] = $start_latitude;
        $updateOrderData['start_address_remark'] = $start_address_remark;
        $updateOrderData['end_longitude'] = $end_longitude;
        $updateOrderData['end_latitude'] = $end_latitude;
        $updateOrderData['end_address_remark'] = $end_address_remark;

        $this->startTrans();

        if($driver_id != $orderR->data['driver_id']) {
            if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                if(!empty($orderR->data['driver_id'])) {//修改指派更新司机座位
                    $originDriverR = $this->find(['driver_id' => $orderR->data['driver_id']], 'Driver');
                    if(!$originDriverR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '原司机信息异常'));
                    }
                    $origin_residual_seating = $originDriverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $originDriverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'residual_seating' => $origin_residual_seating));
                    if($originDriverS->ret) {
                        if(C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($orderR->data['order_id'], $originDriverR->data['driver_id'], $originDriverR->data['residual_seating'], $orderR->data['book_seating'], $origin_residual_seating, 0, 'Admin.OrderManager.doUpdateAppointOrder', "[".__LINE__.']修改指派');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($originDriverS);
                    }
                }

                if(!empty($driver_id)) {
                    //更新司机剩余座位数
                    $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                    if(!$driverS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '网络异常，请重试'));
                    }

                    $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                    $updateOrderData['seat_is_add'] = 1;
                    if(C('SEAT_LOG_ON')) {
                        $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'Admin.OrderManager.doUpdateAppointOrder', "[".__LINE__.']后台直接指派给司机，无需接单操作');
                    }
                } else {
                    $updateOrderData['seat_is_add'] = 0;
                }
            }
        }

        //指派完成
        $orderS = $this->save('Order', $updateOrderData);
        if($orderS->ret) {
            $this->commitTrans();

            if(!empty($driver_id)) {
                //通知司机
                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
                //通知乘客
                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '修改指派失败，请稍后重试'));
        }

        $this->doAddLog('修改订单', "变更前数据：".json_encode($orderR->data)."更新数据：".json_encode($this->data), $logUserType);
        return $this->output($orderS);
    }

    /**
     * 修改指派司机
     * @param $order_no
     * @param $driver_id
     */
    public function doUpdateAppointOrder($order_no, $driver_id)
    {
        $ret = new \ResultModel(false);
        $orderR = $this->find(array('order_no' => $order_no), 'Order');
        if($this->admin_group_id == 2) {
            if($this->state->user_id != $orderR->data['mchid']) {
                $ret->data = "订单异常";
                return $ret;
            }
        } elseif($this->admin_group_id == 3) {
            if($this->state->user_id != $orderR->data['branchid']) {
                $ret->data = "订单异常";
                return $ret;
            }
        }

        if($orderR->data['state'] > 3) {
            $ret->data = "订单状态是已经发生变化，修改失败！";
            return $ret;
        }

        //司机是否正常接单中
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if($driverR->ret) {
            if($driverR->data['state'] == 2 || $driverR->data['state'] == 1) {
                return $this->output(new \ResultModel(false, '司机在路上'));
            }
            if($driverR->data['state'] == 3) {
                return $this->output(new \ResultModel(false, '司机暂停接单'));
            }
            if($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                return $this->output(new \ResultModel(false, '司机剩余座位数不足,请重新选择司机'));
            }
        } else {
            return $this->output(new \ResultModel(false, '司机不存在'));
        }

        $updateOrderData['order_id'] = $orderR->data['order_id'];
        $updateOrderData['driver_id'] = $driver_id;

        $this->startTrans();

        if($driver_id != $orderR->data['driver_id']) {
            if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                if($this->mchid == 181) {
                    if(!empty($orderR->data['driver_id'])) {//修改指派更新司机座位
                        $originDriverR = $this->find(['driver_id' => $orderR->data['driver_id']], 'Driver');
                        if(!$originDriverR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '原司机信息异常'));
                        }
                        $origin_residual_seating = $originDriverR->data['residual_seating'] + $orderR->data['book_seating'];
                        $originDriverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'residual_seating' => $origin_residual_seating));
                        if($originDriverS->ret) {
                            if(C('SEAT_LOG_ON')) {
                                $this->recordSeatLog($orderR->data['order_id'], $originDriverR->data['driver_id'], $originDriverR->data['residual_seating'], $orderR->data['book_seating'], $origin_residual_seating, 0, 'Admin.OrderManager.doUpdateAppointOrder', "[".__LINE__.']修改指派');
                            }
                        } else {
                            $this->transRollback();
                            return $this->output($originDriverS);
                        }
                    }

                    //更新司机剩余座位数
                    $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                    $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                    if(!$driverS->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '网络异常，请重试'));
                    }

                    $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                    $updateOrderData['seat_is_add'] = 1;
                    if(C('SEAT_LOG_ON')) {
                        $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'Admin.OrderManager.doUpdateAppointOrder', "[".__LINE__.']后台直接指派给司机，无需接单操作');
                    }
                }
            }
        }

        //指派完成
        $orderS = $this->save('Order', $updateOrderData);
        if($orderS->ret) {
            $this->commitTrans();

            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
            if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                //通知乘客
                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            } elseif($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                if($this->mchid == 181) {
                    //通知乘客
                    $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                }
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '修改指派失败，请稍后重试'));
        }

        $this->doAddLog('PC端修改指派', json_encode($orderR->data), \StateModel::$BRANCH_USER);
        return $this->output($orderS);
    }

    /**
     * 删除订单
     * @param int $order_id 订单ID
     */
    public function doDeleteOrder($order_id)
    {
        //删除订单历史状态
        $r = $this->select(array('order_id' => $order_id), null, null, 'create_time desc', 'OrderHistoryState', 'id');
        if ($r->ret) {
            $this->startTrans();
            foreach ($r->data as $value) {
                $r = $this->delete($value['id'], 'OrderHistoryState');
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
            //删除订单
            $r = $this->delete($order_id, 'Order');
            if ($r->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
            }
        }
        $this->doAddLog("删除订单");
        return $this->output($r);
    }

    /**
     * 删除订单
     * @param int $order_id 订单ID
     */
    public function doDeleteOrderNew($order_id)
    {
        //删除订单历史状态
        $this->startTrans();
        //删除订单
        $r = $this->delete($order_id, 'Order');
        if ($r->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        return $this->output($r);
    }

    /**
     * 订单派发页面
     * @param $orderid 订单id
     *
     */
    public function orderOrderPage($order_id)
    {
        //        查询订单
        $r = $this->find(array('order_id' => $order_id), 'Order');
        if ($r->ret) {
            if ($r->data['state'] !== '1') {
                echo "<script>alert('该订单不符合指派条件');window.location.href='/admin_order';</script>";
            } else {
                $driver = $this->find(array('driver_id' => $r->data['driver_id']), 'Driver');
                $passenger = $this->find(array('passenger_id' => $r->data['passenger_id']), 'Passenger');
                $this->assign('order', $r->data);
                $this->assign('driver', $driver->data);
                $this->assign('passenger', $passenger->data);
                $this->display('Tpl/Admin/Order/orderOrder.html');
            }
        } else {
            echo "<script>alert('订单不存在');window.location.href='/admin_order';</script>";
            exit();
        }
    }

    /**
     * 订单派发
     * @param $order_id
     * @param $driver_id
     *
     */
    public function doOrderOrder($order_id, $driver_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,2,7');
        $orderR = $this->find($where, 'Order');
        if(!$orderR->ret) {
            return $this->output(new \ResultModel(false, sprintf('订单ID(%d)不存在', $order_id)));
        } elseif($orderR->data['appoint']  == 1) {
            return $this->output(new \ResultModel(false, sprintf('订单ID(%d)已派单司机', $order_id)));
        } elseif($orderR->data['state'] == \CommonDefine::ORDER_STATE_2) {
            # 车主已接单
            if($orderR->data['type'] != \CommonDefine::ORDER_TYPE_5) {
                return $this->output(new \ResultModel(false, '订单异常'));
            }
        } elseif($orderR->data['state'] == \CommonDefine::ORDER_STATE_7) {
            return $this->output(new \ResultModel(false, sprintf('订单ID(%d)已取消', $order_id)));
        }
        if(!empty($orderR->data['driver_id'])) {
            if($orderR->data['driver_id'] == $driver_id) {
                return $this->output(new \ResultModel(false, '指派失败，请勿重复指派给该司机'));
            }
        }
        if($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
            return $this->output(new \ResultModel(false, '司机抢单中，暂时不能指派'));
        }
        if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
            # 先支付后服务模式，未支付无法派单
            if ($orderR->data['is_pay'] == \CommonDefine::IS_DEL_0) {
                return $this->output(new \ResultModel(false, sprintf('订单ID(%d)开启了先支付，后服务模式，未支付无法派单', $order_id)));
            }
        }

        //司机是否正常接单中
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if($driverR->ret) {
            if($driverR->data['state'] == 2 || $driverR->data['state'] == 1) {
                return $this->output(new \ResultModel(false, '司机在路上'));
            }
            if($driverR->data['state'] == 3) {
                return $this->output(new \ResultModel(false, '司机暂停接单'));
            }
            if($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                return $this->output(new \ResultModel(false, '司机剩余座位数不足,请重新选择司机'));
            }
        } else {
            return $this->output(new \ResultModel(false, '司机不存在'));
        }

        $updateOrderData['order_id'] = $order_id;
        $updateOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $updateOrderData['driver_id'] = $driver_id;
        $updateOrderData['train_id'] = $driverR->data['current_train_id'];

        # 增加派单记录
        $dispatched = is_null($orderR->data['virtual_driver']) ? array() : json_decode($orderR->data['virtual_driver'], true);
        # 更新订单信息
        # 
        $newDispatched = array(
            array(
                'time' => date('Y-m-d H:i:s'),
                'dispatch_way' => 'manual',
                'driver' => [
                    'id' => $driver_id,
                    'name' => $driverR->data['name'],
                    'phone' => $driverR->data['cellphone'],
                ],
                'status' => 'success',
                'message' => '后台直接指派给司机'
            )
        );
        $dispatched = array_merge($dispatched, $newDispatched);
        $updateOrderData['virtual_driver'] = json_encode($dispatched);

        if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
        }
        $this->startTrans();

        if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
            if($this->mchid == 1238) {
                if(!empty($orderR->data['driver_id'])) {//修改指派更新司机座位
                    $originDriverR = $this->find(['driver_id' => $orderR->data['driver_id']], 'Driver');
                    if(!$originDriverR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '原司机信息异常'));
                    }
                    $origin_residual_seating = $originDriverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $originDriverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'residual_seating' => $origin_residual_seating));
                    if($originDriverS->ret) {
                        if(C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($orderR->data['order_id'], $originDriverR->data['driver_id'], $originDriverR->data['residual_seating'], $orderR->data['book_seating'], $origin_residual_seating, 0, 'Admin.OrderManager.doOrderOrder', "[".__LINE__.']后台指派');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output($originDriverS);
                    }
                }

                //更新司机剩余座位数
                $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                if(!$driverS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '网络异常，请重试'));
                }

                $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                $updateOrderData['seat_is_add'] = 1;
                if(C('SEAT_LOG_ON')) {
                    $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'Admin.OrderManager.doOrderOrder', "[".__LINE__.']后台直接指派给司机，无需接单操作');
                }
            }
        } elseif($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            # （先支付后服务）未支付的订单不能指派
            if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_1) {
                if ($orderR->data['is_pay'] = \CommonDefine::PAY_STATUS_0) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '此订单需要先支付在服务，暂时无法继续派单'));
                }
            }
        }

        //指派完成
        $r = $this->save('Order', $updateOrderData);
        if($r->ret) {
            $this->commitTrans();
            # 司机WebSocket通知
            httpRequest(C('CC_INNER_API_HOST') . "/api/inner/notification/user_types/driver/channels/websocket/actions/websocket_driver_new_order/orders/{$orderR->data['order_id']}", 'post');

            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
            if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                //通知乘客
                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            } elseif($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                if($this->mchid == 1238) {
                    //通知乘客
                    $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                }
            } elseif($orderR->data['type'] == \CommonDefine::ORDER_TYPE_11) {
                # 出租车订单接单超时队列入队列
                httpRequest(C('CC_INNER_API_HOST') . "/api/inner/taxi/orders/{$orderR->data['order_id']}/receiving_timeout_queue", 'post');
            }
        } else {
            $this->transRollback();
        }

        return $this->output($r);
    }

    /**
     * 退款
     * @param $order_id  订单编号
     * @return null|resultModel
     */
    public function doRefundOrder($order_id)
    {
        $this->startTrans();
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '找不到该订单'));
        }
        if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_7) {
            $orderSubR = $this->find(['relation_order_id' => $order_id], 'OrderSub');
            if($orderSubR->ret) {
                $orderClassR = $this->find(['order_id' => $orderSubR->data['order_id']], 'Order');
                $this->transRollback();
                return $this->output(new \ResultModel(false, "班线车接送单不能单独取消，请直接取消班线订单(".$orderClassR->data['order_no'].")即可"));
            }
        } else {
            if($this->getOrderClass($orderR->data['order_no']) == \CommonDefine::ORDER_CLASS_5) {
                //取消摆渡车（接、送）
                $cancelFerryOrderSs = $this->doCancelBookLineFastInnerFunction($orderR->data['order_id']);
                if(!$cancelFerryOrderSs->ret) {
                    $this->transRollback();
                    return new \ResultModel(false, '取消摆渡车订单失败');
                }
            }

            if($this->mchid != $orderR->data['mchid']) {
                $this->transRollback();
                return $this->output(false, "订单是ID无效或没有权限取消预订");
            }

            if($this->admin_group_id == 3) {
                if($this->state->user_id != $orderR->data['branchid']) {
                    $this->transRollback();
                    return $this->output(false, "订单是ID无效或没有权限取消预订");
                }
            }

            if($orderR->data['is_pay'] == 0 && $orderR->data['is_pre_pay'] == 0) {
                $this->transRollback();
                return $this->output(false, "订单异常");
            }

            $refundR = $this->find(array('order_id' => $order_id), 'Refunds');
            if($refundR->ret) {
                $this->transRollback();
                return $this->output(false, "已退款");
            }

            if($orderR->data['state'] == 1 || $orderR->data['state'] == 2 || $orderR->data['state'] == 3 || $orderR->data['state'] == 4 || $orderR->data['state'] == 5) {
                if($orderR->data['seat_is_add'] == 1) {
                    $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                    if($driverR->ret) {
                        $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                        $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'],'residual_seating' => $residual_seating));
                        if($driverS->ret) {
                            if(C('SEAT_LOG_ON')) {
                                $this->recordSeatLog($orderR->data['order_id'], $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doCloseOrder', '商户关闭订单');
                            }
                        } else {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                }

                //退款
                //添加预订订单历史状态
                $bookHistoryS = $this->addBookOrderHistoryState($order_id, 'cancel book');
                if($bookHistoryS->ret) {
                    if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                        $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
                        //                        $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']), 'LineClassTrain');
                        if(empty($lineClassTrainArr)) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                        $lineClassR = $this->find(array('line_class_id' =>  $lineClassTrainArr['line_class_id']), 'LineClass');
                        if(!$lineClassR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }
                        //更新余票
                        $upLineClassTrainData = [];
                        if(!empty($orderR->data['seat_optional'])) {
                            $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                            $seatIdsArr = [];
                            foreach($seatOptionalArr as $seatOptional) {
                                $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                            }
                            $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                            $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr, \CommonDefine::OPTIONAL_0);
                            if(!$updateSeatPriceDataR->ret) {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '退票失败，请联系管理员!'));
                            }
                            $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
                        }
                        //更新余票
                        $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] + $orderR->data['book_seating'];
                        $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
                        $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
                        if(!$lineClassTrainS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                        }

                        //退保险
                        if($orderR->data['is_buy_insurance'] == \CommonDefine::IS_BUY_INSURANCE_1) {
                            $cancelOrderInsurancesR = $this->cancelOrderInsurances($orderR->data['order_id']);
                            if(!$cancelOrderInsurancesR->ret) {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '退保险失败，请联系管理员!'));
                            }
                        }
                    }

                    $wxPayUtil = new \WxPayUtil();
                    if(!$wxPayUtil->init($orderR->data['mchid'])) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                    }

                    $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['real_price'], $orderR->data['real_price']);
                    if(!$refundR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }

                    if($refundR->data['return_code'] != 'SUCCESS' || $refundR->data['result_code'] != 'SUCCESS') {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'.$refundR->data['err_code_des']));
                    }

                    $payHistoryR = $this->find(array('order_no' => $orderR->data['order_no']), 'PayOrderHistory');
                    if(!$payHistoryR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
                    }
                    $account_type = 1;
                    switch($payHistoryR->data['account_type']) {
                        case 0:
                            $account_type = 1;
                            break;
                        case 1:
                            $account_type = 0;
                            break;
                        case 2:
                            $account_type = 2;
                            break;
                    }

                    $refundData['amount'] = $orderR->data['price'];
                    $refundData['status'] = 3;
                    $refundData['order_id'] = $order_id;
                    $refundData['account_type'] = $account_type;
                    $refundData['account_id'] = $payHistoryR->data['account_id'];
                    $refundData['refund_id'] = $refundR->data['refund_id'];
                    //   $refundData['object'] = $refundR->data['object'];
                    $refundData['refund_no'] = $refundR->data['out_trade_no'];
                    $refundData['created'] = time();
                    //   $refundData['time_succeed'] = $refundR->data['time_succeed'];
                    /*  $refundData['failure_code'] = $refundR->data['failure_code'];
                        $refundData['failure_msg'] = $refundR->data['failure_msg'];*/
                    $refundData['description'] = "系统已取消该订单，全额退款";
                    $refundData['charge'] = $refundR->data['charge'];
                    $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
                    $refundData['transaction_no'] = $refundR->data['transaction_id'];
                    $reR = $this->add('Refunds', $refundData);
                    if($reR->ret) {
                        $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'],'state' => \CommonDefine::ORDER_STATE_7,'refund_total_amount' => $orderR->data['real_price']));
                        if($orderS->ret) {
                            $this->commitTrans();
                            $this->doAddLog('退款', json_encode($orderR->data));
                            return  $this->output(new \ResultModel(true, '退款中，退款将于3个工作日内到账!'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '退款失败'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                }
            } elseif($orderR->data['state'] == 6) {
                $this->transRollback();
                return $this->output(false, "订单已完成，退款失败");
            } elseif($orderR->data['state'] == 7) {
                $this->transRollback();
                return $this->output(false, "订单已取消");
            } else {
                $this->transRollback();
                return $this->output(false, "订单异常");
            }
        }

        return $this->output(false, "失败");
    }

    /**
     * 完成订单
     * @param int $order_id 订单编号
     */
    public function doOverOrder($order_id)
    {
        $this->startTrans();
        $orderWhere = " mchid = ".$this->mchid;
        $orderWhere .= " AND order_id = ".$order_id;
        $orderWhere .= " AND state < ".\CommonDefine::ORDER_STATE_6;
        $orderR = $this->find($orderWhere, 'Order');
        if (!$orderR->ret) {
            $this->transRollback();
            return $this->output(false, "订单异常,请刷新页面后再试");
        }
        if($orderR->data['type'] != \CommonDefine::ORDER_TYPE_5) {
            $this->transRollback();
            return $this->output(false, "完成失败，该订单类型暂时不支持直接完成订单操作");
        }

        $updateOrderData['order_id'] = $order_id;

        switch ($orderR->data['type']) {
            case \CommonDefine::ORDER_TYPE_1:
            case \CommonDefine::ORDER_TYPE_2:
            case \CommonDefine::ORDER_TYPE_3:
            case \CommonDefine::ORDER_TYPE_4:{
            }
            case \CommonDefine::ORDER_TYPE_5:{
                if ($orderR->data['pay_mode'] == \CommonDefine::ORDER_PAYMENT_MODE_0) {//先坐车后支付
                    //只记录线下流水
                    $bookDownAllR = $this->bookkeepingDownAllByOver($orderR->data['order_id']);
                    if(!$bookDownAllR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败'));
                    }
                } else {
                    if($orderR->data['is_pay'] == 1 || $orderR->data['is_pre_pay'] == 1) {
                        //直接分账
                        $bookR = $this->bookkeepingMchOnBalanceByOver($orderR->data['order_id']);
                        if(!$bookR->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '操作失败，请稍后再试'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '操作失败，该订单还未支付'));
                    }

                }

                $updateOrderData['state'] = \CommonDefine::ORDER_STATE_6;
                $orderS = $this->save('Order', $updateOrderData);
                if(!$orderS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '操作失败'));
                }
                $this->commitTrans();

                //计入用户积分
                $this->recordPassengerPoints($orderR->data['passenger_id'], $orderR->data['order_id']);
            }

            case \CommonDefine::ORDER_TYPE_6:{
            }
            case \CommonDefine::ORDER_TYPE_7:{
            }
            case \CommonDefine::ORDER_TYPE_11:{
            }
        }

        $this->doAddLog("完成订单[".$orderR->data['order_id']."]", json_encode($updateOrderData));
        return $this->output(new \ResultModel(true, '完成订单'));
    }

    /**
     * 关闭订单
     * @param int $order_id 订单编号
     */
    public function doCloseOrder($order_id)
    {
        $this->startTrans();
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if (!$orderR->ret) {
            $this->transRollback();
            return $this->output(false, "找不到该订单");
        }

        if($orderR->data['is_pay'] == 1 || $orderR->data['is_pre_pay'] == 1) {
            $this->transRollback();
            return $this->output(false, "暂不支持关闭已付款订单，关闭失败");
        }

        if($orderR->data['state'] == \CommonDefine::ORDER_STATE_1
            || $orderR->data['state'] == \CommonDefine::ORDER_STATE_2
            || $orderR->data['state'] == \CommonDefine::ORDER_STATE_3
            || $orderR->data['state'] == \CommonDefine::ORDER_STATE_4) {
            if($orderR->data['seat_is_add'] == 1) {
                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                if($driverR->ret) {
                    $residual_seating = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'],'residual_seating' => $residual_seating));
                    if($driverS->ret) {
                        if(C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($orderR->data['order_id'], $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 0, 'doCloseOrder', '商户关闭订单');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                    }
                } else {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '订单是ID无效或没有权限取消预订'));
                }
            }
        } elseif($orderR->data['state'] == 5) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '已送达订单，不支持关闭，关闭失败'));
        } elseif($orderR->data['state'] == 6) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '已完成订单，关闭失败'));
        } elseif($orderR->data['state'] == 7) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '已取消订单，关闭失败'));
        }

        if($this->getOrderClass($orderR->data['order_no']) == \CommonDefine::ORDER_CLASS_5) {
            //取消摆渡车（接、送）
            $cancelFerryOrderSs = $this->doCancelBookLineFastInnerFunction($orderR->data['order_id']);
            if(!$cancelFerryOrderSs->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消摆渡车订单失败'));
            }
        }

        $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'], 'state' => \CommonDefine::ORDER_STATE_8));
        if(!$orderS->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '关闭失败'));
        }
        //乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票
        $responsed = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$orderR->data['order_id']}/issued/tickets", 'delete');
        $results = json_decode($responsed, true);
        if ($results['status'] != 'success') {
            $this->transRollback();
            return $this->output(new \ResultModel(false, $results['message']));
        }
        $this->commitTrans();
        $this->doAddLog('关闭订单前', json_encode($orderR->data));
        return $this->output(new \ResultModel(true, '关闭成功'));
    }

}

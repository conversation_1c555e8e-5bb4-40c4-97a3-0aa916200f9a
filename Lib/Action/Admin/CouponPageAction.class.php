<?php

class CouponPageAction extends AdminCoreAction
{
    //优惠券列表
    public function CouponListPage($id = 0)
    {
        $this->redirectMerchantUrl();
        if ($id != 0) {
            $info = M('coupon')->where(['is_del' => \CommonDefine::IS_DEL_0,'coupon_id' => $id,'mchid' => $this->state->user_id])->find();
            $info['start_time'] = date('Y-m-d', strtotime($info['start_time']));
            $info['end_time'] = date('Y-m-d', strtotime($info['end_time']));
            $this->assign('info', $info);
        }
        $this->display('Tpl/Admin/Coupon/couponlist.html');
    }

    public function redirectMerchantUrl()
    {
        /**
         * @Date 2023.04.12
         * <AUTHOR>
         */
        $mchid = $this->state->user_id;
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if ($token['status'] == 'success'
            && $token['data']['access_token']) {
            redirect(C('CC_MCH_HOST') . '/#/promotionManage/coupon?token=' . $token['data']['access_token']);
        }
    }

    //添加/编辑 优惠券
    public function couponPage($id = 0)
    {
        if ($id != 0) {
            $info = M('coupon')->where(['is_del' => \CommonDefine::IS_DEL_0,'coupon_id' => $id,'mchid' => $this->state->user_id])->find();
            $info['start_time'] = date('Y-m-d', strtotime($info['start_time']));
            $info['end_time'] = date('Y-m-d', strtotime($info['end_time']));
            $this->assign('info', $info);
        }
        $this->display('Tpl/Admin/Coupon/coupon.html');
    }

    //营销活动列表
    public function activityListPage($id = 0)
    {
        $this->redirectMerchantUrl();
        $infos = $this->select(['is_del' => \CommonDefine::IS_DEL_0,'mchid' => $this->state->user_id], null, null, 'coupon_id desc', 'Coupon');
        if ($id != 0) {
            $info = M('market_activity')->where(['is_del' => \CommonDefine::IS_DEL_0,'market_activity_id' => $id,'mchid' => $this->state->user_id])->find();
            $info['start_time'] = date('Y-m-d', strtotime($info['start_time']));
            $info['end_time'] = date('Y-m-d', strtotime($info['end_time']));
            $this->assign('info', $info);
        }
        $passengerTypesR = $this->select(['mchid' => $this->mchid, 'is_del' => \CommonDefine::IS_DEL_0], null, null, null, 'PassengerType');
        $this->assign('coupons', $infos->data);
        $this->assign('passenger_type_list', $passengerTypesR->ret ? $passengerTypesR->data : "");
        $this->display('Tpl/Admin/Coupon/activitylist.html');
    }

    //编辑营销活动
    public function activityPage($id = 0)
    {
        $infos = $this->select(['is_del' => \CommonDefine::IS_DEL_0,'mchid' => $this->state->user_id], null, null, 'coupon_id desc', 'Coupon');
        if ($id != 0) {
            $info = M('market_activity')->where(['is_del' => \CommonDefine::IS_DEL_0,'market_activity_id' => $id,'mchid' => $this->state->user_id])->find();
            $info['start_time'] = date('Y-m-d', strtotime($info['start_time']));
            $info['end_time'] = date('Y-m-d', strtotime($info['end_time']));
            $this->assign('info', $info);
        }
        $this->assign('coupons', $infos->data);
        $this->display('Tpl/Admin/Coupon/activity.html');
    }
    //用户优惠券列表
    public function couponRecordListPage($market_activity_id)
    {
        $this->assign('market_activity_id', $market_activity_id);
        $this->display('Tpl/Admin/Coupon/couponrecordlist.html');
    }
};

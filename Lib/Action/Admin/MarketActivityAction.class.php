<?php



class MarketActivityAction extends AdminCoreAction {

    //添加营销活动
    public function doAddMarketActivity($name ,$coupon_id, $rule, $start_time, $end_time, $summary = null, $give_out_type, $market_activity_id=0){
        $coupon= new MarketActivityModel();
        $data = $coupon->create();

        if(empty($coupon_id)){
            return $this->output(new ResultModel(false, '请选择优惠券'));
        }

        if($give_out_type != \CommonDefine::GIVE_OUT_TYYE_1 && $give_out_type != \CommonDefine::GIVE_OUT_TYYE_2){
            return $this->output(new ResultModel(false, '其他发放类型暂不支持'));
        }

        if(!is_numeric($rule)){
            if($rule < 1 && $rule > 100){
                return $this->output(new ResultModel(false, '请输入1-100限制领取'));
            }
        }
        foreach($data as $key=>$value){
            if($value==""){
                return $this->output(new ResultModel(false, '请正确填写内容'));
            }
        }
        if($market_activity_id != 0){
            unset($data['create_time']);
            $res = $coupon->where(['mchid'=>$this->state->user_id,'market_activity_id'=>$market_activity_id])->save($data);
        }else{
            $data['mchid']=$this->state->user_id;
            $res = $coupon->add($data);
        }
        if($res){
            return $this->output(new ResultModel(true, '保存成功'));
        }else{
            return $this->output(new ResultModel(false, '保存失败'));
        }
    }
    //获取营销活动列表
    public function getMarketActivityList($page,$size){
        $MarAct = new MarketActivityModel();
        $info = $MarAct->where(['cp_market_activity.is_del'=>\CommonDefine::IS_DEL_0,'cp_market_activity.mchid'=>$this->mchid])
            ->join('cp_coupon b on b.coupon_id=cp_market_activity.coupon_id')
            ->field('cp_market_activity.*,b.name as coupon_name,b.value,b.type,b.rule as crule')
            ->page($page,$size)
            ->order('cp_market_activity.market_activity_id desc')
            ->select();
        $count = $MarAct->where(['cp_market_activity.is_del'=>\CommonDefine::IS_DEL_0,'cp_market_activity.mchid'=>$this->mchid])->count();
        if($info){
            return $this->output(new ResultModel(true, $info, $count));
        }else{
            return $this->output(new ResultModel(false, '获取失败'));
        }
    }
    //删除营销活动
    public function delMarketActivity($id){
        $info= $this->save('MarketActivity',['is_del'=>1,'market_activity_id'=>$id]);
        echo jsonp_encode($info->ret,$info->data);exit();
    }
}
<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Admin/AdminAction');
/**
 * 短信管理模块
 *
 * <AUTHOR>
 */
class SMSManagerAction extends AdminCoreAction
{

    /**
     * 短信列表
     * @param int $page 开始页码
     * @param int $page 分页大小
     * @param string $start_time 开始页码
     * @param string $end_time 开始页码
     * @param int $mchid 商户id
     * @param int $send_status 发送状态 0-全选；1-成功；1-失败；
     * @param string $order_no 订单编号；
     */
    public function getList($page = 1, $size = 10,$start_time = null ,$end_time=null, $mchid = null, $send_status = 0,$order_no = null)
    {
        if($start_time){
            $start_time = date("Y-m-d H:i:s",strtotime($start_time));
            $where['sms.send_time'] = array('BETWEEN',$start_time,null);
        }

        if($this->admin_group_id == 2){//总台
            $where['sms.mchid'] = $this->state->user_id;
        }else if($this->admin_group_id == 1){//平台
            if($this->isEmpty($mchid)){
                $where['sms.mchid'] = $mchid;
                if($mchid == 1253){
                    $where['sms.mchid'] = ['neq', 1253];
                }
            }else{
                $where['sms.mchid'] = ['neq',1253];
            }
        }

        if($this->isEmpty($send_status)){
            if($send_status == 1){
                $where['sms.accept_error'] =  array('exp', 'IS NULL');
            }else{
                $where['sms.accept_error'] =  array('exp', 'IS NOT NULL');//array('NEQ'=>'NULL');
            }
        }

        if($end_time){
            $end_time = date("Y-m-d H:i:s",strtotime($end_time));
            if($start_time){
                $where['sms.send_time'] = array('BETWEEN',array($start_time,$end_time));
            }else{
                $where['sms.send_time'] = array('BETWEEN',null,$end_time);
            }
        }
        if($order_no && $order_no != 'undefined'){
            $where['sms.accept_content'] = array('like','%'.$order_no.'%');
        }

        $fields = "sms.id,admin.account,admin.mchname,sms.accept_tel,sms.accept_content,sms.accept_error,sms.send_time,sms.content_lenth,sms.deduct_count";
        $logMode = new \SmsModel();
        $adminTable = (new \AdminModel())->getTableName();
        $logList = $logMode->alias('sms')->
        join("$adminTable admin ON admin.admin_id=sms.mchid")
            ->field($fields)
            ->page($page, $size)
            ->where($where)
            ->order('sms.id desc')->select();
        $count = $logMode->alias('sms')
            ->join("$adminTable admin ON admin.admin_id=sms.mchid")
            ->field($fields)
            ->page($page, $size)
            ->where($where)
            ->order('sms.id desc')->count();
        foreach($logList as $k=>$sms){
            if(empty($sms['accept_error'])){
                $logList[$k]['accept_error'] = '<span style="color: green">发送成功</span>';
            }else{
                $logList[$k]['accept_error'] = '<span style="color: red">发送失败</span>'.$sms['accept_error'];
            }
        }


        /*$r = $this->select(null, $page, $size, 'id desc', 'Log', 'id,admin_id,operation_time,Operation_content');*/
        return $this->output(new \ResultModel(true, $logList, $count));
    }


    /**
     * 导出短信记录
     * @param string $start_time 查询开始时间（短信发送时间）
     * @param string $end_time 查询结束时间（短信发送时间）
     * @param int $mchid 商户id
     * @param int $send_status 发送状态 0-全选；1-成功；1-失败；
     * @param string $order_no 订单编号；
     */
    public function doExportSmsList($start_time = null, $end_time = null, $mchid = null, $send_status = 0, $order_no = null){
        $where = [];
        if(empty($order_no)) {
            if (empty($start_time) || empty($end_time)) {
                return $this->output(new \ResultModel(false, "请选择需要导出的时间段，所选时间区间不能超过30天"));
            }
            $span = (strtotime($end_time) - strtotime($start_time)) / 86400;
            if($span > 30){
                return $this->output(new \ResultModel(false, "所选时间区间不能超过30天"));
            }
            $where['s.send_time'] = array('BETWEEN',array($start_time,$end_time));

        }else{
            if (!empty($start_time) && !empty($end_time)) {
                $where['s.send_time'] = array('BETWEEN',array($start_time,$end_time));
            }
        }

        if($this->admin_group_id == 1){
            if(!empty($mchid)){
//                $where['s.mchid'] = $mchid;
            }
        }else if($this->admin_group_id == 2){
            $where['s.mchid'] = $this->mchid;
        }else{
            return $this->output(new \ResultModel(false, "操作异常"));
        }

        if($this->isEmpty($send_status)){
            if($send_status == 1){
                $where['s.accept_error'] =  array('exp', 'IS NULL');
            }else{
                $where['s.accept_error'] =  array('exp', 'IS NOT NULL');//array('NEQ'=>'NULL');
            }
        }

        if($order_no){
            $where['s.accept_content'] = array('like','%'.$order_no.'%');
        }

        $adminTableName = (new AdminModel())->getTableName();
        $smsModel = new SmsModel();
        $smsList = $smsModel->alias('s')
            ->join("$adminTableName mch ON mch.admin_id = s.mchid")
            ->where($where)
            ->field('s.*,mch.mchname')
            ->select();
        foreach($smsList as $k=>$v){
            if(empty($v['accept_error'])){
                $smsList[$k]['send_status'] = '发送成功';
            }else{
                $smsList[$k]['send_status'] = '发送失败';
            }
        }

        $phpExcelUtil = new \PhpExcelUtil();

        $ar=[['id','日志编号'],['mchname','发送者'],['accept_tel','接收手机'],['accept_content','短信内容'],['content_lenth','字符数'],['deduct_count','扣除数量'],['send_status','发送状态'],['send_time','发送时间']];
        $phpExcelUtil->exportExcel('短信发送日志数据',$ar,$smsList);
    }

    /**
     * 删除单条日志
     * @param int $passenger_id 乘客ID
     */
    public function doDeleteLog($id, $page = 1, $size = 10)
    {
        $r = $this->select(null, $page, $size, 'id desc', 'Log', 'id,admin_id,operation_time,Operation_content');
        $this->startTrans();
        $d = $this->delete($id, 'Log');
        if ($d->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }

        return $this->output($r);
    }
}

?>

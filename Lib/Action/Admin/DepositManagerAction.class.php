<?php
/**
 * 提现管理模块
 *
 * <AUTHOR>
 */
import('@/Action/Admin/AdminAction');
import('@/Action/Weixin/Wechat');

class DepositManagerAction extends AdminCoreAction
{
    private $wait_ensure = 1;//待确认
    private $statement_error = 2;//账单有误
    private $statement_ok = 3;//账单无误
    private $statement_order = 4;//已结算


    public function getLists($status = null, $account_type, $page = 1, $size = 10)
    {
        $this->doAddLog("查看提现列表");
        if (!empty($status)) {
            $where['cds.status'] = $status;
        }
        $uid = $this->state->user_id;
        if ($this->admin_group_id == 3) {//分台
            $where['cds.step'] = 1;
        } elseif ($this->admin_group_id == 2) {//总台
            $where['cds.step'] = 2;
            $where['d.mchid'] = $uid;
        } elseif ($this->admin_group_id == 1) {//平台
            $where['cds.step'] = 3;
        }
        $where['d.account_type'] = $account_type;

        $depositList = M()->table("cp_deposit d")->join("LEFT JOIN cp_deposit_status cds ON cds.deposit_id = d.deposit_id")
            ->field('d.deposit_id,d.deposit_no,d.account_id,d.create_time,d.moneys,cds.status,d.account_type,d.settlement_rate,d.real_amount,mch.mchname')
            ->join("LEFT JOIN cp_bank b ON d.bank_id = b.bank_id")
            ->join("LEFT JOIN cp_admin mch ON mch.admin_id = d.mchid")
            ->page($page, $size)->where($where)
            ->order('d.deposit_id desc')->select();

        $depositCount = M()->table("cp_deposit d")->join("LEFT JOIN cp_deposit_status cds ON cds.deposit_id = d.deposit_id")
            ->field('d.deposit_id,d.deposit_no,d.account_id,d.create_time,d.moneys,cds.status,d.account_type,d.mchid')
            ->join("LEFT JOIN cp_bank b ON d.bank_id=b.bank_id")
            ->where($where)
            ->order('d.deposit_id desc')->count();

        foreach ($depositList as $k => $v) {
            // 初始化默认值
            $depositList[$k]['platform_name'] = '微信零钱'; // 默认结算渠道平台
            $depositList[$k]['nickname'] = ''; // 默认微信昵称为空

            if ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {
                $driverR = $this->find(array('driver_id' => $v['account_id']), 'Driver', 'driver_id,name,cellphone,balance');
                $depositList[$k]['nick_name'] = $driverR->data['name'];
                $depositList[$k]['cellphone'] = $driverR->data['cellphone'];
                $depositList[$k]['balance'] = $driverR->data['balance'];
                // 查询 third_party 表中的信息
                $thirdPartyR = $this->find(array('driver_id' => $v['account_id']), 'ThirdParty', 'id,third_account,third_avatar');
                if ($thirdPartyR->ret) {
                    $depositList[$k]['nickname'] = $thirdPartyR->data['third_account'];
                }

            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                $passengerR = $this->find(array('passenger_id' => $v['account_id']), 'Passenger', 'passenger_id,name,cellphone,cash_balances');
                $depositList[$k]['nick_name'] = $passengerR->data['name'];
                $depositList[$k]['cellphone'] = $passengerR->data['cellphone'];
                $depositList[$k]['balance'] = $passengerR->data['cash_balances'];
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {
                $branchR = $this->find(array('admin_id' => $v['account_id']), 'Admin', 'admin_id,mchname,cellphone,balance');
                $depositList[$k]['nick_name'] = $branchR->data['mchname'];
                $depositList[$k]['cellphone'] = $branchR->data['cellphone'];
                $depositList[$k]['balance'] = $branchR->data['balance'];
            }
            $tR = $this->find(array('deposit_id' => $v['deposit_id']), 'Transfer');

            if ($v['status'] == 2) {
                $depositList[$k]['status'] = 2;
                $depositList[$k]['message'] = '通过审核(付款中)';
                if ($tR->ret) {
                    if ($tR->data['status'] == 3) {
                        $depositList[$k]['message'] = '通过审核(付款成功)';
                    } elseif ($tR->data['status'] == 4 && $this->admin_group_id == 1) {
                        $depositList[$k]['message'] = '通过审核(付款失败)';
                        if (!empty($tR->data["error_msg"])) {
                            $depositList[$k]['message'] = '通过审核(付款失败)['.$tR->data["error_msg"].']。提现金额已原路返回到司机账户';
                        }
                    } elseif ($this->admin_group_id == 2) {
                        $depositR = $this->find(array('deposit_id' => $v['deposit_id']), 'Deposit');
                        if ($tR->data['status'] == 4) {
                            $depositList[$k]['message'] = '通过审核(付款失败),提现金额已原路返回到司机账户，如有疑问请及时联系平台管理员';
                        } elseif ($depositR->data['status'] == 3) {
                            $depositList[$k]['message'] = '通过审核(付款失败),提现金额已原路返回到司机账户，如有疑问请及时联系平台管理员';
                        }
                    }
                }
            } elseif ($v['status'] == 3) {
                $depositList[$k]['status'] = 3;
                $depositList[$k]['message'] = '不通过';
                if ($this->admin_group_id == 1) {
                    $depositList[$k]['message'] = '不通过,提现金额已原路返回到司机账户';
                } elseif ($this->admin_group_id == 2) {
                    $depositList[$k]['message'] = '不通过,提现金额已原路返回到司机账户';
                }
            } else {
                $depositList[$k]['status'] = 1;
                $depositList[$k]['message'] = '待审核';
            }

            if ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {
                $depositList[$k]['account_type'] = '司机';
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                $depositList[$k]['account_type'] = '乘客';
            } elseif ($v['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {
                $depositList[$k]['account_type'] = '分台';
            }

            if (empty($v['settlement_rate'])) {
                $depositList[$k]['settlement_rate'] = 0;
            } else {
                $depositList[$k]['settlement_rate'] *= 100;
            }
            if (empty($v['real_amount'])) {
                $depositList[$k]['real_amount'] = 0;
            }
        }

        if (empty($depositCount) || empty($depositList)) {
            return $this->output(new ResultModel(false, '没有数据'));
        }
        return $this->output(new \ResultModel(true, $depositList, $depositCount));
    }

    /**
     * 删除单条提现记录
     * @param int $passenger_id 乘客ID
     * @return null|resultModel
     */
    public function doDeleteDeposit($deposit_id, $page = 1, $size = 10)
    {
        $this->startTrans();
        $d = $this->delete($deposit_id, 'Deposit');

        if ($d->ret) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $r = $this->select(null, $page, $size, 'deposit_id desc', 'Deposit', 'deposit_id,account_id,create_time,moneys,status,account_type');
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '待审核';
            } else {
                $r->data[$k]['status'] = '通过审核';
            }
            if ($r->data[$k]['account_type'] == 1) {

                $r->data[$k]['account_type'] = '司机';
            } else {
                $r->data[$k]['account_type'] = '乘客';
            }
        }
        $this->doAddLog("删除单条提现记录");
        return $this->output($r);
    }

    /**
     * 不通过单条提现记录
     * @param int $passenger_id 乘客ID
     */
    public function doUnPassDeposit($deposit_id, $page = 1, $size = 10)
    {
        $accountR = $this->find(array('deposit_id' => $deposit_id), 'Deposit');
        if ($accountR->data['status'] != 1) {
            return $this->output(new ResultModel(false, "该提现不能重复审核"));
        }
        $param = array('status' => 1,'deposit_id' => $deposit_id);
        $this->startTrans();
        if ($this->admin_group_id == 3) {
            if ($accountR->ret) {
                //                $param['step'] = 1;
                if ($this->find(array('deposit_id' => $deposit_id,'step' => 2), 'DepositStatus')->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "请不要重复提交数据"));
                } else {
                    //查询出当前数据的分台Id
                    $depositStatusR = $this->find(array('step' => 1 ,'deposit_id' => $deposit_id), 'DepositStatus', 'id');

                    //修改当前数据的分台状态
                    if (!$this->save('DepositStatus', array('id' => $depositStatusR->data['id'],'status' => 3))->ret) {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据存在异常，请联系管理员..."));
                    }

                    if ($accountR->data['account_type'] == 0) {//如果是是司机
                        $driverR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                        if ($driverR->ret) {
                            $driverR->data['balance'] = $driverR->data['balance'] + $accountR->data['real_amount'];
                            $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'], 'balance' => $driverR->data['balance']));
                            if ($driverS->ret) {
                                $d = $this->save('Deposit', array('deposit_id' => $deposit_id,'status' => 3));
                                if ($d->ret) {
                                    $this->commitTrans();
                                    //                            $sms = new \YTSMS();
                                    //                            $smsR = $sms->sendTemplateSMS($driverR->data['cellphone'], array($driverR->data['cellphone'], '未通过'), \YTSMS::TEMP_ID_DEPOSITRESULT);
                                    //                            $wechat = new \Wechat();
                                    //                            $wechatR = $wechat->doPositMessage($driverR->data['openid'], 8, 0, $accountR['moneys'], $accountR['create_time'], "未通过审核", '申请失败');
                                    //                            $wechat = new \Wechat();
                                    //                            $wechatR = $wechat->doPositMessage($driverR->data['openid'], 8, 0, $accountR['moneys'], $accountR['create_time'], "未通过审核", '申请失败');
                                    //                            print_r($wechatR);
                                } else {
                                    $this->transRollback();
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $passengerR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger', 'passenger_id,cash_balances,cellphone,openid');
                        if ($passengerR->ret) {
                            $passengerR->data['cash_balances'] = $passengerR->data['cash_balances'] + $accountR->data['moneys'];
                            $driverS = $this->save('Passenger', array('passenger_id' => $passengerR->data['passenger_id'], 'cash_balances' => $passengerR->data['cash_balances']));
                            if ($driverS->ret) {
                                $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                                if ($d->ret) {
                                    $this->commitTrans();
                                    //                            $sms = new \YTSMS();
                                    //                            $smsR = $sms->sendTemplateSMS($driverR->data['cellphone'], array($driverR->data['cellphone'], '未通过'), \YTSMS::TEMP_ID_DEPOSITRESULT);
                                    //                            $wechat = new \Wechat();
                                    //                            $wechatR = $wechat->doPositMessage($driverR->data['openid'], 8, 0, $accountR['moneys'], $accountR['create_time'], "未通过审核", '申请失败');
                                    //                            $wechat = new \Wechat();
                                    //                            $wechatR = $wechat->doPositMessage($driverR->data['openid'], 8, 0, $accountR['moneys'], $accountR['create_time'], "未通过审核", '申请失败');
                                    //                            print_r($wechatR);
                                } else {
                                    $this->transRollback();
                                }
                            }
                        }
                    }
                }

            } else {
                $this->transRollback();
            }
        } elseif ($this->admin_group_id == 2) {
            if ($accountR->ret) {
                if ($this->find(array('step' => 3,'deposit_id' => $deposit_id), 'DepositStatus')->ret) {
                    return $this->output(new ResultModel(false, "请不要重复提交数据"));
                } else {
                    //查询出当前数据的总台Id
                    $depositStatusR = $this->find(array('step' => 2 ,'deposit_id' => $deposit_id), 'DepositStatus', 'id');
                    //修改当前数据的总台状态
                    if (!$this->save('DepositStatus', array('id' => $depositStatusR->data['id'],'status' => 3))->ret) {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据存在异常，请联系管理员..."));
                    }

                    if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//如果是是司机
                        $driverR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                        if ($driverR->ret) {
                            $driverR->data['balance'] = $driverR->data['balance'] + $accountR->data['moneys'];
                            $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'], 'balance' => $driverR->data['balance']));
                            if ($driverS->ret) {
                                $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                                if ($d->ret) {
                                    $this->commitTrans();

                                    if (C('SMS_ON')) {
                                        $cellphone = "";
                                        $name = "";
                                        if ($accountR->data['account_type'] == 0) {//司机
                                            $driverR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                                            if ($driverR->ret) {
                                                $cellphone = $driverR->data['cellphone'];
                                                $name = $driverR->data['name'];
                                            }
                                        } elseif ($accountR->data['account_type'] == 1) {

                                        }
                                        $mchR = $this->find(array('admin_id' => $accountR->data['mchid']), 'Admin');

                                        //通知提现用户
                                        $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                        $smsDataToDriver = array(
                                            $name,
                                            $accountR->data['deposit_no'],
                                            $mchR->data['tel'],
                                        );
                                        $smsUtil->sendTemplateSMS($cellphone, $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_FAILED, $accountR->data['mchid']);
                                    }
                                } else {
                                    $this->transRollback();
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//如果是乘客
                        $passengerR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger', 'passenger_id,cash_balances,cellphone,openid');
                        if ($passengerR->ret) {
                            $passengerR->data['cash_balances'] = $passengerR->data['cash_balances'] + $accountR->data['moneys'];
                            $driverS = $this->save('Passenger', array('passenger_id' => $passengerR->data['passenger_id'], 'cash_balances' => $passengerR->data['cash_balances']));
                            if ($driverS->ret) {
                                $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                                if ($d->ret) {
                                    $this->commitTrans();
                                } else {
                                    $this->transRollback();
                                }
                            }
                        }
                    } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//如果是分台
                        $branchR = $this->find(array('admin_id' => $accountR->data['account_id'], 'group_id' => 3), 'Admin');
                        if ($branchR->ret) {
                            $branchR->data['balance'] = $branchR->data['balance'] + $accountR->data['moneys'];
                            $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance']));
                            if ($branchS->ret) {
                                $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                                if ($d->ret) {
                                    $this->commitTrans();

                                    if (C('SMS_ON')) {
                                        $cellphone = $branchR->data['cellphone'];
                                        $name = $branchR->data['mchname'];

                                        $mchR = $this->find(array('admin_id' => $accountR->data['mchid']), 'Admin');
                                        //通知提现用户
                                        $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                        $smsDataToDriver = array(
                                            $name,
                                            $accountR->data['deposit_no'],
                                            $mchR->data['tel'],
                                        );
                                        $smsUtil->sendTemplateSMS($cellphone, $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_FAILED, $accountR->data['mchid']);
                                    }
                                } else {
                                    $this->transRollback();
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    }
                }
            } else {
                $this->transRollback();
            }
        } elseif ($this->admin_group_id == 1) {
            if ($accountR->ret) {
                //查询出当前数据的平台Id
                $depositStatusR = $this->find(array('step' => 3 ,'deposit_id' => $deposit_id), 'DepositStatus', 'id');

                //修改当前数据的平台状态
                if (!$this->save('DepositStatus', array('id' => $depositStatusR->data['id'],'status' => 3))->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据存在异常，请联系管理员..."));
                }

                if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//如果是司机
                    $driverR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                    if ($driverR->ret) {
                        $driverR->data['balance'] = $driverR->data['balance'] + $accountR->data['moneys'];
                        $driverS = $this->save('Driver', array('driver_id' => $driverR->data['driver_id'], 'balance' => $driverR->data['balance']));
                        if ($driverS->ret) {
                            $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                            if ($d->ret) {
                                $this->commitTrans();

                                if (C('SMS_ON')) {
                                    $cellphone = "";
                                    $name = "";
                                    if ($accountR->data['account_type'] == 0) {//司机
                                        if ($driverR->ret) {
                                            $cellphone = $driverR->data['cellphone'];
                                            $name = $driverR->data['name'];
                                        }
                                    } elseif ($accountR->data['account_type'] == 1) {

                                    }
                                    $mchR = $this->find(array('admin_id' => $accountR->data['mchid']), 'Admin');

                                    //通知提现用户
                                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                    $smsDataToDriver = array(
                                        $name,
                                        $accountR->data['deposit_no'],
                                        $mchR->data['tel'],
                                    );
                                    $smsUtil->sendTemplateSMS($cellphone, $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_FAILED, $accountR->data['mchid']);
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//如果是乘客
                    $passengerR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger', 'passenger_id,cash_balances,cellphone,openid');
                    if ($passengerR->ret) {
                        $passengerR->data['cash_balances'] = $passengerR->data['cash_balances'] + $accountR->data['moneys'];
                        $driverS = $this->save('Passenger', array('passenger_id' => $passengerR->data['passenger_id'], 'cash_balances' => $passengerR->data['cash_balances']));
                        if ($driverS->ret) {
                            $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                            if ($d->ret) {
                                $this->commitTrans();
                            } else {
                                $this->transRollback();
                            }
                        }
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//如果是分台
                    $branchR = $this->find(array('admin_id' => $accountR->data['account_id'], 'group_id' => 3), 'Admin');
                    if ($branchR->ret) {
                        $branchR->data['balance'] = $branchR->data['balance'] + $accountR->data['moneys'];
                        $branchS = $this->save('Admin', array('admin_id' => $branchR->data['admin_id'], 'balance' => $branchR->data['balance']));
                        if ($branchS->ret) {
                            $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 3));
                            if ($d->ret) {
                                $this->commitTrans();

                                if (C('SMS_ON')) {
                                    $cellphone = $branchR->data['cellphone'];
                                    $name = $branchR->data['mchname'];
                                    $mchR = $this->find(array('admin_id' => $accountR->data['mchid']), 'Admin');

                                    //通知提现用户
                                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                    $smsDataToDriver = array(
                                        $name,
                                        $accountR->data['deposit_no'],
                                        $mchR->data['tel'],
                                    );
                                    $smsUtil->sendTemplateSMS($cellphone, $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_FAILED, $accountR->data['mchid']);
                                }
                            } else {
                                $this->transRollback();
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        }


        $r = $this->select(null, $page, $size, 'deposit_id desc', 'Deposit');
        foreach ($r->data as $k => $v) {
            if ($r->data[$k]['status'] == 1) {
                $r->data[$k]['status'] = '待审核';
            } elseif ($r->data[$k]['status'] == 2) {
                $r->data[$k]['status'] = '通过审核';
            } else {
                $r->data[$k]['status'] = '未通过审核';
            }
            if ($r->data[$k]['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {
                $r->data[$k]['account_type'] = '司机';
            } elseif ($r->data[$k]['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                $r->data[$k]['account_type'] = '乘客';
            } elseif ($r->data[$k]['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {
                $r->data[$k]['account_type'] = '分台';
            }
        }
        $this->doAddLog("未通过单条提现记录", "提现编号：".$accountR->data['deposit_no']);
        return $this->output($r);
    }

    /**
     * 审核通过
     * @param int $passenger_id 乘客ID
     */
    public function doPassDeposit($deposit_id)
    {
        $accountR = $this->find(array('deposit_id' => $deposit_id), 'Deposit', 'account_type,account_id,deposit_no,real_amount,moneys,create_time,status,mchid');
        if ($accountR->data['status'] != 1) {
            return $this->output(new ResultModel(false, "该提现不能重复审核"));
        }
        $param = array('status' => 1,'deposit_id' => $deposit_id);

        $this->startTrans();
        if ($this->admin_group_id == 3) {
            $d = $this->save('Deposit', array('deposit_id' => $deposit_id,'step' => 2));
            $param['step'] = 2;
            if ($d->ret) {
                if ($this->find(array('deposit_id' => $deposit_id, 'step' => 2), 'DepositStatus')->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "请不要重复提交数据"));
                }
                if (!$this->add('DepositStatus', $param)->ret) {
                    $this->transRollback();
                } else {
                    //更新自己的状态
                    $depositR = $this->find(array('step' => 1, 'status' => 1), 'DepositStatus');
                    if (!$depositR->ret) {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                    }
                    $depositS = $this->save('DepositStatus', array('id' => $depositR->data['id'], 'status' => 2));
                    if (!$depositS->ret) {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                    }

                    $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
                    if ($branchR->ret) {
                        $branchR->data['balance'] -= $accountR->data['moneys'];
                        $branchS = $this->save('Admin', array('admin_id' => $this->state->user_id, 'balance' => $branchR->data['balance']));
                        if ($branchS->ret) {
                            $this->commitTrans();
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
            } else {
                $this->transRollback();
            }
        } elseif ($this->admin_group_id == 2) {
            $payServiceR = $this->checkPayService($accountR->data['mchid']);
            if ($payServiceR->ret) {//商家自己的支付
                //更新自己的状态
                $depositStatusR = $this->find(array('step' => 2, 'status' => 1, 'deposit_id' => $deposit_id), 'DepositStatus');
                if (!$depositStatusR->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员错误码0x01"));
                }
                $depositStatusS = $this->save('DepositStatus', array('id' => $depositStatusR->data['id'], 'status' => 2));
                if (!$depositStatusS->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员错误码0x02"));
                }
                //更新总状态
                $depositR = $this->find(array('deposit_id' => $deposit_id, 'status' => 1), 'Deposit');
                if (!$depositR->ret) {
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员错误码0x05"));
                }

                $depositS = $this->save('Deposit', array('deposit_id' => $depositR->data['deposit_id'], 'status' => 2));
                if (!$depositS->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员错误码0x06"));
                }

                $recipient = "";
                if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                    $memberR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openid'];
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//乘客
                    $memberR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openid'];
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                    $memberR = $this->find(array('admin_id' => $accountR->data['account_id']), 'Admin');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openid'];
                    }
                }

                if (empty($recipient)) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "提现的账号存在异常，请联系管理员错误码0x03"));
                }

                $wxPayUtil = new \WxPayUtil();
                if (!$wxPayUtil->init($accountR->data['mchid'])) {
                    $this->transRollback();
                    return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                }
                $transferR = $wxPayUtil->createTransfer($recipient, $this->createTransferNo(), $accountR->data['real_amount'], '代付');
                if (!$transferR->ret) {
                    $this->transRollback();
                    $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                    return $this->output(new ResultModel(false, "提现的账号存在异常，请联系管理员错误码0x04"));
                }

                $status = 2;
                $description = "提现成功";
                if ($transferR->data['return_code'] != 'SUCCESS' || $transferR->data['result_code'] != 'SUCCESS') {
                    $transferData['error_msg'] = $transferR->data['return_msg'];
                    $status = 4;
                    $description = "提现失败";
                    //金额原路返回到司机账户
                    if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                        $memberR->data['balance'] += $accountR->data['moneys'];
                        $driverS = $this->save('Driver', array('driver_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                        if (!$driverS->ret) {
                            $this->transRollback();
                            $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                            return $this->output(new ResultModel(false, "转账失败，司机账户异常"));
                        }
                    } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//乘客
                        $memberR->data['cash_balances'] += $accountR->data['moneys'];
                        $passengerS = $this->save('Passenger', array('passenger_id' => $accountR->data['account_id'],'cash_balances' => $memberR->data['cash_balances']));
                        if (!$passengerS->ret) {
                            $this->transRollback();
                            $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                            return $this->output(new ResultModel(false, "转账失败，乘客账户异常"));
                        }
                    } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                        $memberR->data['balance'] += $accountR->data['moneys'];
                        $branchS = $this->save('Admin', array('admin_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                        if (!$branchS->ret) {
                            $this->transRollback();
                            $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                            return $this->output(new ResultModel(false, "转账失败，分台账户异常"));
                        }
                    }
                }
                $transferData['transfer_id'] = $transferR->data['payment_no'];
                $transferData['transfer_type'] = 0;
                $transferData['deposit_id'] = $deposit_id;
                $transferData['account_type'] = $accountR->data['account_type'];
                $transferData['account_id'] = $accountR->data['account_id'];
                $transferData['amount'] =  $accountR->data['real_amount'];
                $transferData['status'] = 3;
                $transferData['app_id'] = $wxPayUtil->getWxAppId();
                $transferData['channel'] = 'wx_pub';
                $transferData['transfer_no'] = $transferR->data['partner_trade_no'];
                $transferData['recipient'] = $recipient;
                $transferData['description'] = $description;
                $transferS = $this->add('transfer', $transferData);
                if (!$transferS->ret) {
                    $this->transRollback();
                    $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                }
                $this->commitTrans();
                $this->doAddLogTransfer(1, $deposit_id, "创建付款单", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);

                if ($status == 4) {
                    return $this->output(new ResultModel(false, "付款失败，钱已原路返回"));
                }

                //短信通知
                if (C('SMS_ON')) {
                    if ($status == 2) {
                        $name = "";
                        $cellphone = "";
                        $depositR = $this->find(array('deposit_id' => $deposit_id), 'Deposit');
                        if ($depositR->data['account_type'] == 0) {//司机
                            $driverR = $this->find(array('driver_id' => $depositR->data['account_id']), 'Driver');
                            if ($driverR->ret) {
                                $cellphone = $driverR->data['cellphone'];
                                $name = '司机[' . $driverR->data['name'] . ']';
                            }
                        }
                        $smsDataToMch = array(
                            $name,
                            $cellphone,
                            $depositR->data['deposit_no'],
                            $depositR->data['moneys'],
                            '成功'
                        );
                        $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                        $smsUtil->sendTemplateSMS($cellphone, $smsDataToMch, \SMSUtil::TEMP_ID_DEPOSIT_RESULT, $depositR->data['mchid']);
                    }
                }
            } else {//平台支付
                $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'step' => 3));
                $ds = $this->find(array('deposit_id' => $deposit_id, 'step' => 3), 'DepositStatus');

                if ($ds->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "请不要重复提交数据"));
                } else {
                    if ($d->ret) {
                        $param['step'] = 3;
                        $param['status'] = 1;

                        //转账申请配置 人工/自动
                        $aotuTransferConfig = false;
                        if ($aotuTransferConfig) {
                            $param['status'] = 2;
                        }

                        if (!$this->add('DepositStatus', $param)->ret) {
                            $this->transRollback();
                            return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                        } else {
                            //更新自己的状态
                            $depositR = $this->find(array('step' => 2, 'status' => 1, 'deposit_id' => $deposit_id), 'DepositStatus');
                            if (!$depositR->ret) {
                                $this->transRollback();
                                return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                            }
                            $depositStatusS = $this->save('DepositStatus', array('id' => $depositR->data['id'], 'status' => 2));
                            if (!$depositStatusS->ret) {
                                $this->transRollback();
                                return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                            }

                            $recipient = "";
                            if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                                $memberR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                                if ($memberR->ret) {
                                    $recipient = $memberR->data['openidforpay'];
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//乘客
                                $memberR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger');
                                if ($memberR->ret) {
                                    $recipient = $memberR->data['openidforpay'];
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                                $memberR = $this->find(array('admin_id' => $accountR->data['account_id']), 'Admin');
                                if ($memberR->ret) {
                                    $recipient = $memberR->data['openidforpay'];
                                }
                            }

                            if (empty($recipient)) {
                                $this->transRollback();
                                return $this->output(new ResultModel(false, "提现的账号存在异常，请联系管理员"));
                            }

                            if ($aotuTransferConfig) {
                                $this->transRollback();
                                return $this->output(new ResultModel(false, "暂不支持自动审核，请联系管理员"));
                            } else {
                                $tR = $this->find(array('deposit_id' => $deposit_id), 'Transfer');
                                if (!$tR->ret) {
                                    $transferData['transfer_type'] = 0;
                                    $transferData['transfer_no'] = $this->createTransferNo();
                                    $transferData['deposit_id'] = $deposit_id;
                                    $transferData['recipient'] = $recipient;
                                    $transferData['account_type'] = $accountR->data['account_type'];
                                    $transferData['account_id'] = $accountR->data['account_id'];
                                    $transferData['amount'] = $accountR->data['real_amount'];
                                    $transferData['status'] = 1;
                                    $transferS = $this->add('transfer', $transferData);
                                    if (!$transferS->ret) {
                                        $this->transRollback();
                                        return $this->output(new ResultModel(false, "该笔数据转账出现异常，请联系管理员"));
                                    }
                                } else {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "重复提交审核，请联系管理员"));
                                }
                            }

                            $this->commitTrans();
                            //总台提现审核成功
                            if (C('SMS_ON')) {
                                $mchR = $this->find(array('admin_id' => $accountR->data['mchid']), 'Admin');
                                if ($mchR->data['admin_id'] != 1170) {
                                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                                    $smsUtil->sendTemplateSMS("***********", array('商户:'.$mchR->data['mchname'].'审核通过了编号：'.$deposit_id, '金额:'.$accountR->data['real_amount'].'元的'), \SMSUtil::TEMP_ID_DEPOSITRESULT, 181);
                                }
                            }

                            return $this->output(new ResultModel(true, '审核提现成功'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员4"));
                    }
                }
            }
        } elseif ($this->admin_group_id == 1) {
            $d = $this->save('Deposit', array('deposit_id' => $deposit_id, 'status' => 2));
            if ($d->ret) {
                //更新自己的状态
                $depositR = $this->find(array('step' => 3, 'status' => 1,'deposit_id' => $deposit_id), 'DepositStatus');
                if (!$depositR->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                }
                $depositS = $this->save('DepositStatus', array('id' => $depositR->data['id'], 'status' => 2));
                if (!$depositS->ret) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                }
                $recipient = "";
                $memberR = new \ResultModel(false);
                if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                    $memberR = $this->find(array('driver_id' => $accountR->data['account_id']), 'Driver');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openidforpay'];
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//乘客
                    $memberR = $this->find(array('passenger_id' => $accountR->data['account_id']), 'Passenger');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openidforpay'];
                    }
                } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                    $memberR = $this->find(array('admin_id' => $accountR->data['account_id']), 'Admin');
                    if ($memberR->ret) {
                        $recipient = $memberR->data['openidforpay'];
                    }
                }

                if (empty($recipient)) {
                    $this->transRollback();
                    return $this->output(new ResultModel(false, "转账的账号存在异常，请联系管理员1"));
                }

                $tR = $this->find(array('deposit_id' => $deposit_id), 'Transfer');
                if (!$tR->ret) {
                    $wxPayUtil = new \WxPayUtil();
                    if (!$wxPayUtil->init($accountR->data['mchid'])) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                    }
                    $transferR = $wxPayUtil->createTransfer($recipient, $this->createTransferNo(), $accountR->data['real_amount'], '代付');
                    if ($transferR->ret) {
                        $status = 2;
                        if ($transferR->data['return_code'] != 'SUCCESS' || $transferR->data['result_code'] != 'SUCCESS') {
                            $transferData['error_msg'] = $transferR->data['return_msg'];
                            $status = 4;
                            //金额原路返回到司机账户
                            if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                                $memberR->data['balance'] += $accountR->data['moneys'];
                                $driverS = $this->save('Driver', array('driver_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                                if (!$driverS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，司机账户异常"));
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {//乘客
                                $memberR->data['cash_balances'] += $accountR->data['moneys'];
                                $passengerS = $this->save('Passenger', array('passenger_id' => $accountR->data['account_id'],'cash_balances' => $memberR->data['cash_balances']));
                                if (!$passengerS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，乘客账户异常"));
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                                $memberR->data['balance'] += $accountR->data['moneys'];
                                $branchS = $this->save('Admin', array('admin_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                                if (!$branchS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，分台账户异常"));
                                }
                            }
                        }
                        $transferData['transfer_id'] = $transferR->data['payment_no'];
                        $transferData['transfer_type'] = 1;
                        $transferData['deposit_id'] = $deposit_id;
                        $transferData['account_type'] = $accountR->data['account_type'];
                        $transferData['account_id'] = $accountR->data['account_id'];
                        $transferData['amount'] =  $accountR->data['real_amount'];
                        $transferData['status'] = $status;
                        $transferData['app_id'] = $wxPayUtil->getWxAppId();
                        $transferData['channel'] = 'wx_pub';
                        $transferData['transfer_no'] = $transferR->data['partner_trade_no'];
                        $transferData['recipient'] = $recipient;
                        $transferData['description'] = '提现成功';
                        $transferS = $this->add('transfer', $transferData);
                        if (!$transferS->ret) {
                            $this->transRollback();
                            return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员"));
                    }
                } elseif ($tR->data['status'] == 1) {
                    $wxPayUtil = new \WxPayUtil();
                    if (!$wxPayUtil->init($accountR->data['mchid'])) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
                    }

                    $sendMessage = "成功";

                    $transferR = $wxPayUtil->createTransfer($recipient, $this->createTransferNo(), $accountR->data['real_amount'], '代付');
                    if ($transferR->ret) {
                        $status = 3;
                        if ($transferR->data['return_code'] != 'SUCCESS' || $transferR->data['result_code'] != 'SUCCESS') {
                            $transferData['error_msg'] = $transferR->data['return_msg'];
                            $status = 4;
                            $description = "提现失败";
                            $sendMessage = "失败(已原路退回到您的余额)";
                            //金额原路返回到司机账户
                            if ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_0) {//司机
                                $memberR->data['balance'] += $accountR->data['moneys'];
                                $driverS = $this->save('Driver', array('driver_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                                if (!$driverS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，司机账户异常"));
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_1) {
                                $memberR->data['cash_balances'] += $accountR->data['moneys'];
                                $passengerS = $this->save('Passenger', array('passenger_id' => $accountR->data['account_id'],'cash_balances' => $memberR->data['cash_balances']));
                                if (!$passengerS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，乘客账户异常"));
                                }
                            } elseif ($accountR->data['account_type'] == \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2) {//分台
                                $memberR->data['balance'] += $accountR->data['moneys'];
                                $branchS = $this->save('Admin', array('admin_id' => $accountR->data['account_id'],'balance' => $memberR->data['balance']));
                                if (!$branchS->ret) {
                                    $this->transRollback();
                                    return $this->output(new ResultModel(false, "转账失败，分台账户异常"));
                                }
                            }
                        }
                        $transferData['id'] = $tR->data['id'];
                        $transferData['transfer_id'] = $transferR->data['payment_no'];
                        $transferData['transfer_type'] = 1;
                        $transferData['deposit_id'] = $deposit_id;
                        $transferData['account_type'] = $accountR->data['account_type'];
                        $transferData['account_id'] = $accountR->data['account_id'];
                        $transferData['amount'] = $accountR->data['real_amount'];
                        $transferData['status'] = $status;
                        $transferData['app_id'] = $wxPayUtil->getWxAppId();
                        $transferData['channel'] = 'wx_pub';
                        $transferData['transfer_no'] = $transferR->data['partner_trade_no'];
                        $transferData['recipient'] = $recipient;
                        $transferData['description'] = $description;
                        $transferS = $this->save('transfer', $transferData);
                        if (!$transferS->ret) {
                            $this->transRollback();
                            return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员2"));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new ResultModel(false, "该笔数据出现异常，请联系管理员3"));
                    }
                    $this->doAddLogTransfer(1, $deposit_id, "平台付款", json_encode($transferR->data, JSON_UNESCAPED_UNICODE), $accountR->data['mchid']);
                } else {

                }

                $this->commitTrans();
                //微信通知
                //短信通知
                if (C('SMS_ON')) {
                    $name = "";
                    $cellphone = "";
                    $depositR = $this->find(array('deposit_id' => $deposit_id), 'Deposit');
                    if ($depositR->data['account_type'] == 0) {//司机
                        $driverR = $this->find(array('driver_id' => $depositR->data['account_id']), 'Driver');
                        if ($driverR->ret) {
                            $cellphone = $driverR->data['cellphone'];
                            $name = '司机[' . $driverR->data['name'] . ']';
                        }
                    } elseif ($depositR->data['account_type'] == 1) {

                    }
                    //通知司机
                    $mchR = $this->find(array('admin_id' => $depositR->data['mchid']), 'Admin');
                    $smsDataToMch = array(
                        $name,
                        $cellphone,
                        $depositR->data['deposit_no'],
                        $depositR->data['moneys'],
                        $sendMessage
                    );
                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                    $smsUtil->sendTemplateSMS($mchR->data['tel'], $smsDataToMch, \SMSUtil::TEMP_ID_DEPOSIT_RESULT, $depositR->data['mchid']);
                }
            } else {
                $this->transRollback();
            }
        }

        $this->doAddLog("审核提现成功");

        return $this->output(new ResultModel(true, '审核提现成功'));
    }

    /**
     * 批量删除通知
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doDeleteDeposits($deposit_ids)
    {
        $deposit_id_arr = explode(",", $deposit_ids);
        if (empty($deposit_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($deposit_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('DepositManager', $this->state->user_id);
                $o->data = array('deposit_id' => $value);
                $r = $o->doDeleteDeposit($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量删除提现记录");
        return $this->output(new ResultModel(true));
    }

    /**
     * 批量不通过
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doUnPassDeposits($deposit_ids)
    {
        $deposit_id_arr = explode(",", $deposit_ids);
        if (empty($deposit_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($deposit_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('DepositManager', $this->state->user_id);
                $o->data = array('deposit_id' => $value);
                $r = $o->doUnPassDeposit($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();

        $this->doAddLog("批量不通过提现记录");
        return $this->output(new ResultModel(true));
    }

    /**
     * 批量审核通过
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doPassDeposits($deposit_ids)
    {

        $deposit_id_arr = explode(",", $deposit_ids);
        if (empty($deposit_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($deposit_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('DepositManager', $this->state->user_id);
                $o->data = array('deposit_id' => $value);
                $r = $o->doUnPassDeposit($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量审核提现记录");
        return $this->output(new ResultModel(true));
    }


    //    账单开始

    public function doGetStatementList($status = null, $page = 1, $size = 10)
    {
        $this->doAddLog("查看账单列表");

        $uid = $this->state->user_id;
        $where['mchid'] = $uid;
        $sql = '1=1';
        $sql .= ' and mchid=' . $uid . ' and is_del=0';

        $r = $this->select($sql, $page, $size, 'update_at', 'Statement');

        if ($r->ret) {
            return $this->output($r);
        } else {
            return $this->output(new ResultModel(false, "没有数据"));
        }

    }

    /**
     * 账单收集
     * 默认计算T+1规则
     * start 开始计算时间
     * end 结束时间
     */
    public function domchstatement()
    {

        $mch = M('Admin')->where(array('group_id' => 2))->field('admin_id')->select();
        $bool = true;
        $end = date("Y-m-d", strtotime('-0 day'));
        $start = date("Y-m-d", strtotime("-2 day"));
        $this->startTrans();
        foreach ($mch as $item) {
            $r = $this->statementCreate($item['admin_id'], $start, $end);
            if (!$r->ret) {
                $bool = false;
            }
        }
        if ($bool) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }


    }

    /**
     * 生成订单
     * @param $mchid
     * @param $start
     * @param $end
     * @return ResultModel
     *
     */
    public function statementCreate($mchid, $start, $end)
    {

        $sql = '1=1';
        $state = 6;//订单正常完成
        if (!$mchid) {
            $this->redirect("/admin_login");
        }
        $sql .= ' and update_time between "' . $start . '" and "' . $end . '"';
        $sql .= ' and mchid=' . $mchid;
        $sql .= ' and state=' . $state;
        $order = $this->select($sql, 1, 10, 'update_time', 'Order');
        $order_ids = '';
        $amount = 0;
        if ($order->ret) {
            foreach ($order->data as $item) {
                $order_ids .= $item['order_id'] . ',';
                $amount += $item['price'];
            }
        }
        $data['is_auto'] = 0;
        $data['start_time'] = $start;
        $data['end_time'] = $end;
        $data['amount'] = $amount;
        //        $data['status'] = 1;
        $data['log'] = "生成账单";
        $data['order_ids'] = $order_ids;
        $data['mchid'] = $mchid;
        $data['is_del'] = 0;

        $check = $this->statementCheck($start, $end, $mchid);
        if ($check->ret) {
            $data['id'] = $check->data['id'];
            $data['update_at'] = date("Y-m-d H:i:s");
            $r = $this->save('Statement', $data);
        } else {
            $data['create_at'] = date("Y-m-d H:i:s");
            $data['update_at'] = date("Y-m-d H:i:s");
            $r = $this->add('Statement', $data);
        }


        return $r;

    }

    /**
     * 账单检测
     * @param $start
     * @param $end
     * @param $mchid
     * @return ResultModel
     */
    public function statementCheck($start, $end, $mchid)
    {
        $sql = '1=1';
        $sql .= ' and create_at between "' . $start . '" and "' . $end . '"';
        $sql .= ' and mchid=' . $mchid;
        $r = $this->find($sql, "Statement");
        return $r;

    }


    /**
     * 账单结算
     * @param $statement_id
     * @param $mchid
     *
     */
    public function statements($state, $statement_id, $mchid)
    {

        $statement = $this->find(array('id' => $statement_id, 'mchid' => $mchid, 'is_del' => 0), 'Statement');
        //结算过的订单不能重复操作
        if (!$statement->ret) {
            return $this->output(new ResultModel(false, "找不到该账单"));
        }
        //结算过的订单不能重复操作
        if ($statement->data['status'] == 4) {
            return $this->output(new ResultModel(false, "该账单已结算"));
        }
        $this->startTrans();
        $statement->data['status'] = $state;
        $r = $this->save('Statement', $statement->data);
        if ($r->ret) {
            $this->commitTrans();
            return $this->output(new ResultModel(true, "已确认"));
        } else {
            $this->transRollback();
            return $this->output(new ResultModel(false, "账单结算错误"));
        }

    }


    //    账单结束
    /**
     * 生成提现明细
     *
     */
    public function generateMchStatemets()
    {
        $mchid = $this->state->user_id;
        $statements = M('Statement')->where(array('mchid' => $mchid))->select();

        $this->startTrans();
        $bool = true;
        foreach ($statements as $statement) {
            $data = [];
            $data['create_time'] = date("Y-m-d");
            $data['moneys'] = $statement['amount'];
            $data['off_money'] = $data['moneys'] * 0.95;
            $data['statement_id'] = $statement['id'];
            $data['mchid'] = $statement['mchid'];
            $mchdes = $this->find(array('mchid' => $statement['mchid'], 'statement_id' => $statement['id']), 'Mchdes');
            if ($mchdes->ret) {
                $r = $this->save('Mchdes', $data);
            } else {
                $r = $this->add('Mchdes', $data);
            }

            if (!$r->ret) {
                $bool = false;
            }
        }
        if ($bool) {
            $this->commitTrans();
        } else {
            $this->transRollback();
        }

    }

    //商户提现账单开始
    public function mchstaments($page = 1, $size = 10)
    {
        $mchid = $this->state->user_id;
        $mchstamets = $this->select(array('mchid' => $mchid), $page, $size, 'Mchdes');
        if ($mchstamets->ret) {
            return $this->output($mchstamets);
        } else {
            return $this->output(new ResultModel(false, "没有数据"));
        }


    }
    //商户提现账单结束
}

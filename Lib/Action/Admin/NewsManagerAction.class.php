<?php

/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/7/4
 * Time: 10:45
 */
class NewsManagerAction extends AdminCoreAction
{
    public function doNewsList($page ,$size ){
        $where = null;
        $where['is_del'] = 0;
        $r = $this->select($where ,$page ,$size,"create_time desc" , "News");
        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据",null));
        }

        return $this->output($r);
    }

    /**
    *添加新闻
    * @param $name 名称
    * @param $memo 简介
    * @param $fover 封面
    * @param $content 内容
    */
    public function doNewsAdd($name = null ,$memo = null ,$fover  = null, $content = null){
        $content = $this->ajaxDecode($content);
        if(empty($name)){
            return $this->output(new ResultModel(false,"名称不能为空",null));
        }
        if(empty($memo)){
            return $this->output(new ResultModel(false,"简介不能为空",null));
        }
        if(empty($fover)){
            return $this->output(new ResultModel(false,"请选择封面",null));
        }
        if(empty($content)){
            return $this->output(new ResultModel(false,"内容不能为空",null));
        }
        $data['title'] = $name;
        $data['memo'] = $memo;
        $data['url'] = $fover;

        $newsR =$this->find($data,'News');
        if($newsR->ret){
            return $this->output(new ResultModel(false,"该条文章已存在",null));
        }
        $data['content'] = $content;
        $data['is_del'] = 0;
        $dataTiem = date("Y-m-d H:i:s");
        $data['create_time'] = $dataTiem;
        $data['update_time'] = $dataTiem;
        $this->startTrans();
        try{
            $r = $this->add('News',$data);
            if($r->ret){
                $this->commitTrans();
                return $this->output(new ResultModel(true,"添加成功",null));
            }else{
                $this->transRollback();
                return $this->output(new ResultModel(false,"添加失败",null));
            }

        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常....",null));
        }

    }

    public function doNewsEdit($id,$name = null ,$memo = null ,$fover  = null, $content = null){
        $content = $this->ajaxDecode($content);
        if(empty($name)){
            return $this->output(new ResultModel(false,"名称不能为空",null));
        }
        if(empty($memo)){
            return $this->output(new ResultModel(false,"简介不能为空",null));
        }
        if(empty($fover)){
            return $this->output(new ResultModel(false,"请选择封面",null));
        }
        if(empty($content)){
            return $this->output(new ResultModel(false,"内容不能为空",null));
        }
        $dataTiem = date("Y-m-d H:i:s");
        $data['title'] = $name;
        $data['memo'] = $memo;
        $data['url'] = $fover;
        $data['content'] = $content;
        $data['update_time'] = $dataTiem;
        $newsR =$this->find(array("id"=>$id,'is_del'=>0),'News');
        if(!$newsR->ret){
            return $this->output(new ResultModel(false,"该条文章不存在",null));
        }
        $this->startTrans();
        try{
            $data['id'] = $newsR->data['id'];
            $r = $this->save('News',$data);
            if($r->ret){
                $this->commitTrans();
                return $this->output(new ResultModel(true,"修改成功",null));
            }else{
                $this->transRollback();
                return $this->output(new ResultModel(false,"修改失败",null));
            }

        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常....",null));
        }
    }
    public function doNewsDel($id){
        $newsR = $this->find(array('id' => $id),'News');
        if(!$newsR->ret){
            return $this->output(new ResultModel(false,"该文章不存在....",null));
        }
        if($this->save('News',array('id'=>$id,'is_del'=>1))){
            return $this->output(new ResultModel(false,"删除成功....",null));
        }else{
            return $this->output(new ResultModel(false,"删除失败....",null));
        }
    }

    public function doFileUpload(){
        $file = $_FILES['file'];
        $imgSrc = $_POST['src'];
        if($imgSrc){
            unlink(".{$imgSrc}");
        }
        $result = $this->filesUpload($file);
        return $this->output(new ResultModel(true,$result,null));
    }

    private function filesUpload($file){
        $extfilter = array('image/jpeg','image/png','image/jpg');
        $fileWidth = 220;
        $fieHeight = 150;
        if(!in_array($file['type'],$extfilter)){
            $msg['flag'] = 3; //上传文件类型错误
        }else{
            $wh = getimagesize($file['tmp_name']);
            if(!isset($wh)){
                $msg['flag'] = 4; //异常文件
            }
           /* if($fieHeight < $wh[1]){
                $msg['flag'] = 5; //超过指定的高度
                $msg['height'] = $fieHeight;
            }else if($fileWidth < $wh[0]){
                $msg['flag'] = 6; //超过指定的宽度
                $msg['width'] = $fieHeight;
            }else{}*/
            $dirPath = $this->createDir("img");
            $fileArr = explode(".",$file['name']);
            $fileName = md5(time()).".".array_pop($fileArr);
            $filePath = $dirPath.$fileName;
            $msg = $this->moveServer($file['tmp_name'], $filePath);
        }
        return $msg;
    }

    //生成文件路径
    private function createDir($type){
        $path = "./upload/".date('Y-m-d')."/".$type."/";
        if(is_dir($path) || mkdir($path,0777,true)){
            return $path;
        }
    }
    //将本地临时文件移动服务上
    private function moveServer($temp,$filePath){
        if(move_uploaded_file($temp, $filePath)){
            $msg['flag'] = 1;
            $msg['path'] =  preg_replace("/./","",$filePath,1);
        }else{
            $msg['flag'] = 2;
        }
        return $msg;
    }

    protected function ajaxDecode($str){
        $str = str_replace("{@bai@}","%",$str);
        $str = str_replace("{@dan@}","'",$str);
        $str = str_replace("{@shuang@}","\"",$str);
        $str = str_replace("{@zuojian@}","<",$str);
        $str = str_replace("{@kong@}"," ",$str);
        $str = str_replace("{@youjian@}",">",$str);
        $str = str_replace("{@and@}","&",$str);
        $str = str_replace("{@tab@}","%",$str);
        $str = str_replace("{@jia@}","+",$str);
        return $str;
    }
}
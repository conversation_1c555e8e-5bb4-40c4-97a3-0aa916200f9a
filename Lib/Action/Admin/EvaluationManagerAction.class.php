<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 评价管理
 *
 * <AUTHOR>
 */
class EvaluationManagerAction extends AdminCoreAction
{

    /**
     * 获取评价列表
     * @param string $fields 查询的字段（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getEvaluations($fields = null, $page = 1, $size = 10)
    {
        $r = $this->select(null, $page, $size, 'create_time desc', 'Evaluation', $fields);
        $this->doAddLog("查看评价列表");
        return $this->output($r);
    }

    /**
     * 获取评价
     * @param int $evaluation_id 评价ID
     */
    private function getEvaluation($evaluation_id)
    {

    }

    /**
     * 删除评价
     * @param int $evaluation_id 评价ID
     */
    public function doDeleteEvaluation($evaluation_id)
    {
        $r = $this->delete($evaluation_id, 'Evaluation');
        $this->doAddLog("删除评价");
        return $this->output($r);
    }

    /**
     * 删除多个评价
     * @param int $evaluation_ids 评价ID集合（以英文逗号分隔）
     */
    public function doDeleteEvaluations($evaluation_ids)
    {
        $evaluation_id_arr = explode(",", $evaluation_ids);
        if (empty($evaluation_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($evaluation_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('EvaluationManager', $this->state->user_id);
                $o->data = array('evaluation_id' => $value);
                $r = $o->doDeleteEvaluation($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("删除多个评价");
        return $this->output(new ResultModel(true));
    }

}

?>

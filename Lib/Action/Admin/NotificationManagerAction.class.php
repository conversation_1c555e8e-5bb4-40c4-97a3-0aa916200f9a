<?php

import('@/Action/Admin/AdminAction');
import('@/Action/Weixin/WechatAction');

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 通知管理
 *
 * <AUTHOR>
 */
class NotificationManagerAction extends AdminCoreAction
{
    public function __construct()
    {
        parent::__construct();
        $this->uid = $this->state->user_id;
        if (!$this->uid) {
            $this->uid = cookie('mchid');
        }

    }

    /**
     * 获取系统通知列表
     * @param string $fields 查询的字段（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param int $send_type 发送对象（1-车主，2-乘客，3-车主，乘客，默认为空，查询所有）
     * @param datetime $min_send_time 发送时间最小值（默认为空，查询所有）
     * @param datetime $max_send_time 发送时间最大值（默认为空，查询所有）
     */
    public function getNotifications($fields = null, $page = 1, $size = 10, $send_type = null, $min_send_time = null, $max_send_time = null)
    {
        $where = array();
        if (!empty($send_type)) {
            $where['send_type'] = intval($send_type);
        }
        if (!empty($min_send_time) && empty($max_send_time)) {
            $where['send_time'] = array('egt', $min_send_time);
        } else if (empty($min_send_time) && !empty($max_send_time)) {
            $where['send_time'] = array('elt', $max_send_time);
        } else if (!empty($min_send_time) && !empty($max_send_time)) {
            $where['send_time'] = array(array('egt', $min_send_time), array('elt', $max_send_time));
        }

        $where['admin_id'] = $this->uid;
        $r = $this->select($where, $page, $size, 'send_time desc', 'SystemNotification', 'notification_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("notification_id", $value['notification_id'], 'NotificationManager', 'getNotification', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }
        $this->doAddLog("查看系统通知列表");
        return $this->output($r);
    }

    /**
     * 获取系统通知详细
     * @param int $notification_id 通知ID
     * @param string $fields 查询的字段（默认为空，查询所有）
     */
    public function getNotification($notification_id, $fields = null)
    {
        $r = $this->getEntityById($notification_id, 'SystemNotification', $fields, null, 'admin_id');
        if ($r->ret) {
            $this->sudoLoadSubEntity($r, 'admin_id', 'admin_id', 'Admin', 'getAccount', $fields, 'admin');
        }
        $this->doAddLog("查看系统通知详细");
        return $this->output($r);
    }

    /**
     * 群发通知
     * @param int $send_type 发送对象（1-车主，2-乘客，3-车主，乘客）
     * @param string $content 内容
     */
    public function doSendNotifications($send_type, $content)
    {
        $this->startTrans();
        $r = $this->add('SystemNotification', array('content' => $content, 'admin_id' => $this->state->user_id, 'send_type' => intval($send_type)));
        if ($r->ret) {
            if (intval($send_type) === 1 || intval($send_type) === 3) {
                //车主
                $page = 1;
                $size = 10;
                do {
                    $t = $this->select(null, $page, $size, 'create_time desc', 'Driver', 'driver_id');
                    if ($t->ret) {
                        foreach ($t->data as $value) {
                            $t1 = $this->assignDriverRemindTask(0, $value['driver_id'], 'system notification', null, array('content' => $content));
//                            if (!$r->ret) {
//                                $this->transRollback();
//                                return $this->output($r);
//                            }
                        }
                        $page++;
                    }
                } while ($t->ret);
            }
            if (intval($send_type) === 2 || intval($send_type) === 3) {
                //乘客
                $page = 1;
                $size = 10;
                do {
                    $t = $this->select(null, $page, $size, 'create_time desc', 'Passenger', 'passenger_id');
                    if ($t->ret) {
                        foreach ($t->data as $value) {
                            $t1 = $this->assignPassengerRemindTask(0, $value['passenger_id'], 'system notification', null, array('content' => $content));
//                            if (!$r->ret) {
//                                $this->transRollback();
//                                return $this->output($r);
//                            }
                        }
                        $page++;
                    }
                } while ($t->ret);
            }
            $this->commitTrans();
        } else {
            $this->transRollback();
        }
        $this->doAddLog("群发通知");
        return $this->output($r);
    }


    /**
     * 删除通知
     * @param int $notification_id 通知ID
     */
    public function doDeleteNotification($notification_id)
    {
        $r = $this->delete($notification_id, 'SystemNotification');
        $this->doAddLog("删除通知");
        return $this->output($r);
    }

    /**
     * 批量删除通知
     * @param int $notification_ids 通知ID集合（以英文逗号分隔）
     */
    public function doDeleteNotifications($notification_ids)
    {
        $notification_id_arr = explode(",", $notification_ids);
        if (empty($notification_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($notification_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('NotificationManager', $this->state->user_id);
                $o->data = array('notification_id' => $value);
                $r = $o->doDeleteNotification($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        $this->doAddLog("批量删除通知");
        return $this->output(new ResultModel(true));
    }

    /**
     * 获取系统通知列表
     * @param string $fields 查询的字段（默认为空，查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getTplCategoryList($fields = null, $page = 1, $size = 10)
    {
        $where = array();
        $r = $this->select($where, $page, $size, 'id desc', 'WechatTplCategory');
        $this->doAddLog("查看系统通知列表");
        return $this->output($r);
    }

    /**
     * 微信模板添加
     * @param string $title 模板名称
     * @param string $explod 模板案例
     * @param string $template_id_short 微信模版库中的模版id
     */
    public function doTplAdd($title, $explod, $template_id_short)
    {
        if(!$this->isEmpty($title)){
            return $this->output(new \ResultModel(false,"请输入模板名称"));
        }
        if(!$this->isEmpty($explod)){
            return $this->output(new \ResultModel(false,"请输入模板案例"));
        }
        if(!$this->isEmpty($template_id_short)){
            return $this->output(new \ResultModel(false,"微信模版库中的模版id"));
        }
        $data = array('title' => $title,'content'=>$explod,'template_id_short'=>$template_id_short);
        $wechatTplCategoryR = $this->find($data,'WechatTplCategory');
        if($wechatTplCategoryR->ret){
            return $this->output(new \ResultModel(false,"该模板已经存在"));
        }else{
            $this->startTrans();
            $r = $this->add('WechatTplCategory', $data);
            if ($r->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
            }
            $this->doAddLog("微信模板添加");
            return $this->output($r);
        }
    }

    /**
     * 微信模板修改
     * @param int $id 模板id
     * @param string $title 模板名称
     * @param string $explod 模板案例
     * @param string $template_id_short 微信模版库中的模版id
     */
    public function doUpdateTpl($id,$title,$explod,$template_id_short)
    {
        if(!$this->isEmpty($title)){
            return $this->output(new \ResultModel(false,"请输入模板名称"));
        }
        if(!$this->isEmpty($explod)){
            return $this->output(new \ResultModel(false,"请输入模板案例"));
        }
        if(!$this->isEmpty($template_id_short)){
            return $this->output(new \ResultModel(false,"微信模版库中的模版id"));
        }

        $data = array('id'=>$id, 'title' => $title,'content'=>$explod,'template_id_short'=>$template_id_short);
        $wechatTplCategoryS = $this->save('WechatTplCategory', $data);
        if($wechatTplCategoryS->ret){
        }
        $this->doAddLog("修改模版");
        //更新模版信息
        //更新商户所有的模版信息
        return $this->output($wechatTplCategoryS);
    }

    /**
     * 删除微信模板
     * @param int $id 模板id
     */
    public function doDeleteTpl($id)
    {
        //更新模版信息
        //更新商户所有的模版信息
        $this->doAddLog("修改模版");
        return $this->output(new \ResultModel(false, "功能开发中"));
    }

    /**
     * 微信群发通知
     * @param int $send_type 发送对象（1-车主，2-乘客，3-车主，乘客）
     * @param string $content 内容
     * @param int $noticeId 模板发送类型（系统通知）
     */
    public function doSendWecahtNotice($send_type, $content ,$noticeId = 1){
        $data = array("系统系消息","test",$content );
//        $flag = false;
        if(!$this->isEmpty($send_type)){
            return $this->output(new \ResultModel(false,"请选择发送对象"));
        }
        if(!$this->isEmpty($content)){
            return $this->output(new \ResultModel(false,"请输入发送内容"));
        }

        $mchidR = $this->find(array('is_del'=>0,'admin_id'=>$this->uid),"Admin");
        if(!$mchidR->ret){
            return $this->output(new ResultModel(false, '用户不存在'));
        }

        $userAuthR = $this->find(array('isauthorized'=>0,'uid'=>$this->uid),"UserAuth");
//        var_dump($userAuthR->data['authorizer_access_token']);
        //判断是否过期
        if(time() - $userAuthR->data['timestamp'] > 7100){
            $userAuthR->data['authorizer_access_token'] = WechatAction::get_auth_user_access_token();
        }

        if(!$userAuthR->ret || !$userAuthR->data['authorizer_access_token']) {
            return $this->output(new ResultModel(false, '你暂未进行微信授权'));
        }
        $wechatTplR = $this->find(array('c_id'=>$noticeId ,'mchid'=>$this->uid),"WechatTpl");
        if(!$wechatTplR->ret){
            return $this->output(new ResultModel(false, '该模板暂时不存在,请联系管理员...'));
        }
        $this->startTrans();
        $r = $this->add('SystemNotification', array('content' => $content, 'admin_id' => $this->state->user_id, 'send_type' => intval($send_type)));
        $param = array(
            'mchid' => $this->state->user_id,
            'create_time' => date("Y-m-d H:i:s"),
        );
        if ($r->ret) {
            if (intval($send_type) === 1 || intval($send_type) === 3) {

                //车主
                $driverMode = new \DriverModel();
                $uid = $this->uid;
                $driverList = $driverMode->where(array('mchid'=>$this->uid,'virtual'=>0,'is_freeze'=>0))->field('driver_id,openid,cellphone')->select();
                foreach ($driverList as $driverK => $driver) {
                    if($driver['cellphone'] == 18784440886){
                        if($driver['openid'] && $userAuthR->data['authorizer_access_token']) {
                            $wechatTpl = new \WechatTpl();
                            $wechatTpl->setWechatConfig($userAuthR->data['authorizer_access_token'], $driver['openid'], $wechatTplR->data['wechat_tpl_code'], $data);
                            $param['mchid'] = $this->uid;
                            $param['user_id'] = $driver['driver_id'];
                            $param['type'] = 2;
                            $param['status'] =  $wechatTpl->getSendWechatStataus();
                            $param['content'] = json_encode($data);
                            $wchatSendLogR = $this->find($param,'WechatSendLog');
                            if(!$wchatSendLogR->ret){
                                $this->add('WechatSendLog',$param);
                            }
//                        if($status != 1){
//                            $flag = true;break;
//                        }
                        }
                    }

                }
            }
            if (intval($send_type) === 2 || intval($send_type) === 3) {
                //乘客
                $passengerMode = new \PassengerModel();
                $passengerList = $passengerMode->where(array('mchid'=>$this->uid,'is_freeze'=>0))->field('passenger_id,openid,cellphone')->select();
                foreach ($passengerList as $passengerK => $passenger) {
                    if($passenger['openid'] && $userAuthR->data['authorizer_access_token']){
                            $wechatTpl = new WechatTpl();
                            $wechatTpl->setWechatConfig($userAuthR->data['authorizer_access_token'],$passenger['openid'],$wechatTplR->data['wechat_tpl_code'],$data);
                            $param['mchid'] = $this->uid;
                            $param['user_id'] = $passenger[' passenger_id'];
                            $param['type'] = 1;
                            $param['status'] = $wechatTpl->getSendWechatStataus();
                            $param['content'] = json_encode($data);
                            $wchatSendLogR = $this->find($param,'WechatSendLog');
                            if(!$wchatSendLogR->ret){
                                $this->add('WechatSendLog',$param);
                            }
//                        if($status){
//                            $flag = true;break;
//                        }
                    }
                }
            }
//            if($flag){
//                return $this->output(new ResultModel(false, '发送失败...'));
//                $this->transRollback();
//            }
            $this->commitTrans();

        } else {
            $this->transRollback();
        }
        $this->doAddLog("群发通知");
        return $this->output($r);
    }

    /**
     * 新增商户模板
     * @param string $code  模板id
     * @param string $mchid 商户id
     *
     */
    public function doAddMerchantTpl($code , $mchid)
    {
        $adminR = $this->find(array('is_del'=>0,'admin_id'=>$mchid),"Admin");
        if(!$adminR->ret){
            return $this->output(new ResultModel(false,"请选择正确的商户"));
        }
        $codeArr = json_decode($code,true);

        foreach($codeArr as $item){
//            var_dump($item);
            $wachtTplCategoryR = $this->find(array('id'=>$item['key']),"WechatTplCategory");
            if(!$this->isEmpty($item['val'])){
                return $this->output(new ResultModel(false,"请添加".$wachtTplCategoryR->data['title']));
            }
            if(!$wachtTplCategoryR->ret){
                return $this->output(new ResultModel(false,"数据异常，请稍后再试"));
            }
            $data['mchid'] = $mchid;
            $data['c_id'] = $item['key'];
            $data['wechat_tpl_code'] = $item['val'];
            $data['update_time'] = date("y-m-d H:i:s");
            $this->startTrans();
            $wechatTplR = $this->find(array('c_id'=>$item['key'],'mchid'=>$mchid),"WechatTpl");
            if($wechatTplR->ret){
                //修改模板
                $wechatTplS = M('wechat_tpl')->where(array('c_id'=>$item['key'],'mchid'=>$mchid))->save($data);
                if($wechatTplS){
                    $r = $this->result = new ResultModel(true, $wechatTplS);
                }
            }else{
                //添加商户模板
                $data['create_time'] = date("y-m-d H:i:s");
                $r = $this->add('WechatTpl',$data);
            }
            if ($r->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
            }

        }

        $this->doAddLog("创建商户模板");
        return $this->output($r);
    }



    /**
     * 获取商户模版列表
     * @param string $fields 查询的字段（默认为空，查询所有）
     * @param int $mchid 商户id
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getMerchantTplList($fields = null, $mchid, $page = 1, $size = 10)
    {
        $ret = new \ResultModel(false);
        $where = 'wt.mchid = '.$mchid;
        $mchTplList = M()->table('cp_wechat_tpl wt')
            ->join('LEFT JOIN cp_wechat_tpl_category wtc ON wtc.id = wt.c_id')
            ->where($where)
            ->field($fields)
            ->page($page, $size)
            ->select();

        $mchTplCount = M()->table('cp_wechat_tpl wt')
            ->where($where)
            ->count();

        if($mchTplList){
            $ret->ret = true;
            $ret->data = $mchTplList;
            $ret->count = $mchTplCount;
        }

        $this->doAddLog("查看商户模版列表");
        return $this->output($ret);
    }

    /**
     * 同步商户微信平台的所有模版
     * @param int $mchid 商户id
     */
    public function doSyncAllMerchantTpl($mchid){
        $wechat = new \WechatAction();
        $ret = $wechat->getSyncMerchantTpls($mchid);
        if($ret){
            foreach ($ret['template_list'] as $k=>$v){
                $wechatTplR = $this->find(array('wechat_tpl_code' => $v['template_id'],'mchid' => $mchid), 'WechatTpl');
                if(!$wechatTplR->ret){
                    $data['title'] = $v['title'];
                    $data['mchid'] = $mchid;
                    $data['wechat_tpl_code'] = $v['template_id'];
                    $data['content'] = $v['example'];
                    $data['format'] = $v['content'];
                    $this->add('WechatTpl', $data);
                }
            }
        }

        $this->doAddLog("同步商户微信平台的所有模版");
        return $this->output(new \ResultModel(true));
    }

    /**
     * 同步商户微信平台的所有模版
     * @param int $mchid 商户id
     */
    public function doSyncStandardMerchantTpl($mchid){
        $ret = new ResultModel(false);
        $wechat = new \WechatAction();
        $wechat->setWeChatIndustry($mchid);
        $r = $wechat->addWechatAllTemplateIds($mchid, component_verify_ticket::get_auth_user_access_token($mchid));
        if($r){
            $ret->ret = true;
            $ret->data = "操作成功";
        }

        $this->doAddLog("同步商户微信平台的所有模版");
        return $this->output($ret);
    }

    /**
     * 删除商户模版
     * @param int $mchid 商户id
     * @param string $ids 商户模版ids
     */
    public function doDeleteMerchantTpl($mchid, $ids){
        $wechat = new \WechatAction();
        $idArr = explode(',', $ids);
        if(is_array($idArr)){
            foreach ($idArr as $k=>$id){
                $ret = $wechat->delMerchantWeChatTemplateId($id, $mchid);
                if(!$ret){
                    return $this->output(new \ResultModel(false));
                }
            }
        }


        $this->doAddLog("删除商户模版");
        return $this->output(new \ResultModel(true));
    }

    /**
     * 通知商户套餐不足
     * @param int $mchid 商户id
     * @param int $type 通知类型
     */
    public function doNoticeMerchantPakageLimit($mchid, $type){
        $ret = new \ResultModel(false, '通知失败');
        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
        if(!$mchR->ret){
            return $this->output($ret);
        }
        $message = "";
        switch($type){
            case \CommonDefine::PACKAGE_CATEGORY_0:{
                $smsUtil = new \SMSUtil(\CommonDefine::ZHANGJUNCHUANMEI);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array($mchR->data['endtime']), \SMSUtil::TEMP_ID_MARKETING_2101, 1);
                $message = "套餐";
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_1:{
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array("短信剩余".$mchR->data['shortmessage']."条,", ""), \SMSUtil::TEMP_ID_PAGE_NOT_ENOUGH, 1);
                $message = "短信数量";
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_2:{
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array("司机剩余".$mchR->data['driver_num']."人,", ""), \SMSUtil::TEMP_ID_PAGE_NOT_ENOUGH, 1);
                $message = "司机数量";
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_3:{
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array("存储空间剩余".$mchR->data['usestorage']."M,", ""), \SMSUtil::TEMP_ID_PAGE_NOT_ENOUGH, 1);
                $message = "存储空间";
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_4:{
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_5:{
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array("顺风车司机剩余".$mchR->data['free_ride_driver_num']."人,", ""), \SMSUtil::TEMP_ID_PAGE_NOT_ENOUGH, 1);
                $message = "司机数量";
                break;
                break;
            }
            case \CommonDefine::PACKAGE_CATEGORY_6:{
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $ret->ret = $smsUtil->sendSystemNoticeDefaultTemplateSMS($mchR->data['contact_tel'],array("营销短信剩余".$mchR->data['shortmessage_marketing']."条,", ""), \SMSUtil::TEMP_ID_PAGE_NOT_ENOUGH, 1);
                $message = "营销短信数量";
                break;
            }
            default:
                break;
        }
        $this->doAddLog("通知商户 ".$mchR->data['mchname']." ".$message."不足");
        return $this->output($ret);
    }
}

?>

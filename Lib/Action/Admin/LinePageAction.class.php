<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/2/8
 * Time: 10:54
 */
class LinePageAction extends AdminCoreAction
{
    public function __construct()
    {
        parent::__construct();
    }
    public function IndexPage()
    {
        $this->display('Tpl/Admin/Line/Lines.html');
    }
    public function mapPage()
    {
        $this->display('Tpl/Admin/Line/Map.html');
    }
    public function linePage()
    {
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 1;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        $this->assign("data", $lc->data);
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $this->display('Tpl/Admin/Line/add_map.html');
    }
    public function lineEditPage($line_id)
    {
        $line = $this->find(array('id' => $line_id), 'Line');
        if($line->ret) {
            $startArr = $this->getProvinceCityAreaCodeByCode($line->data['start_address_code']);
            $line->data['start_province_code'] = $startArr['province_code'];
            $line->data['start_city_code'] = $startArr['city_code'];
            $line->data['start_area_code'] = $startArr['area_code'];
            $endArr = $this->getProvinceCityAreaCodeByCode($line->data['end_address_code']);
            $line->data['end_province_code'] = $endArr['province_code'];
            $line->data['end_city_code'] = $endArr['city_code'];
            $line->data['end_area_code'] = $endArr['area_code'];
            $line->data['lci'] = $this->select(array('line_id' => $line->data['id'],'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'LineFkLineCategory', 'line_category_id lci')->data;
            $line->data['dls'] = $this->select(array('line_id' => $line->data['id'],'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $line->data['lc'] = array_column($line->data['lci'], 'lci');
        $line->data['dl'] = implode(",", array_column($line->data['dls'], 'dls'));
        unset($line->data['lci'],$line->data['dls']);
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 1;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $where['branchid'] = $line->data['branchid'];
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name');
        $this->assign("drivers", $driver->data);
        $this->assign("data", $lc->data);
        $this->assign('line', $line->data);
        $this->display('Tpl/Admin/Line/edit_map.html');
    }

    public function lineFastsPage()
    {
        $this->display('Tpl/Admin/Line/line_fasts.html');
    }

    public function lineFastPage()
    {
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $this->display('Tpl/Admin/Line/line_fast.html');
    }
    public function lineFastEditPage($line_id)
    {
        $lineFastR = $this->find(array('id' => $line_id), 'LineFast');
        if($lineFastR->ret) {
            $lineFastR->data['dls'] = $this->select(array('line_id' => $lineFastR->data['id'],'type' => \CommonDefine::ORDER_TYPE_7), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $lineFastR->data['dl'] = implode(",", array_column($lineFastR->data['dls'], 'dls'));
        unset($lineFastR->data['dls']);
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $where['branchid'] = $lineFastR->data['branchid'];
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name');
        $this->assign("drivers", $driver->data);
        $this->assign('line', $lineFastR->data);
        // dump();die;
        $this->assign('start_polygon', implode('-', json_decode($lineFastR->data['start_polygon'])));
        $this->display('Tpl/Admin/Line/line_fast_edit.html');
    }

    public function lineFastFeePage($line_id)
    {
        $selWhere['line_fast_id'] = $line_id;
        # 快车类型
        $lineFastR = $this->find(array('id' => $line_id), 'LineFast', 'type');

        $delBaseFee = M('LineFastBaseFee')->where($selWhere)->order('line_fast_base_fee_id ASC')->select();
        $delDurationFee = M('LineFastDurationFee')->where($selWhere)->order('line_fast_duration_fee_id ASC')->select();
        $delLongdistanceFee = M('LineFastLongdistanceFee')->where($selWhere)->order('line_fast_longdistance_fee_id ASC')->select();
        $delMileageFee = M('LineFastMileageFee')->where($selWhere)->order('line_fast_mileage_fee_id ASC')->select();
        $delWaitTimeoutFee = M('LineFastWaitTimeoutFee')->where($selWhere)->order('line_fast_wait_timeout_fee_id ASC')->select();

        # 定制班线车省市区是否存在快车摆渡车
        # 计价车辆
        $where['mchid'] = $this->state->user_id;
        if ($lineFastR->data['type'] == 0) {
            $where['kuaiche_type'] = 1;
        } else {
            $where['kuaiche_type'] = 2;
        }
        $where['_string'] = " deleted_at IS NULL ";
        $result = M('LineVehiclePricing')->where($where)->field('id,title')->order('id DESC')->select();

        $data = [];
        for($i = 0; $i < count($delBaseFee); $i++) {
            $item['region_start_time'] = substr($delBaseFee[$i]['region_start_time'], 0, 5);
            $item['region_end_time'] = substr($delBaseFee[$i]['region_end_time'], 0, 5);
            $item['base_fee'] = $delBaseFee[$i]['price'];
            $item['duration_fee'] = $delDurationFee[$i]['price'];
            $item['mileage_fee'] = $delMileageFee[$i]['price'];
            $item['base_distance'] = $delLongdistanceFee[$i]['base_distance'];
            $item['longdistance_fee'] = $delLongdistanceFee[$i]['price'];
            $item['base_time'] = $delWaitTimeoutFee[$i]['base_time'];
            $item['wait_timeout_fee'] = $delWaitTimeoutFee[$i]['price'];
            $data[] = $item;
        }

        $this->assign('pricing_vehicles', $result);
        $this->assign('line_fast_id', $line_id);
        $this->assign('line_class_type', $lineFastR->data['type'] == 0 ? '快车' : '快车-摆渡车');
        $this->assign('data', $data);
        $this->display('Tpl/Admin/Line/line_fast_fee.html');
    }


    public function lineTaxisPage()
    {
        $this->display('Tpl/Admin/Line/line_taxis.html');
    }

    public function lineTaxiPage()
    {
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $this->display('Tpl/Admin/Line/line_taxi.html');
    }
    public function lineTaxiEditPage($line_id)
    {
        $lineTaxiR = $this->find(array('id' => $line_id), 'LineTaxi');
        if($lineTaxiR->ret) {
            $lineTaxiR->data['dls'] = $this->select(array('line_id' => $lineTaxiR->data['id'],'type' => \CommonDefine::ORDER_TYPE_11), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $lineTaxiR->data['dl'] = implode(",", array_column($lineTaxiR->data['dls'], 'dls'));
        unset($lineTaxiR->data['dls']);
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $where['branchid'] = $lineTaxiR->data['branchid'];
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name');
        $this->assign("drivers", $driver->data);
        $this->assign('line', $lineTaxiR->data);
        $this->display('Tpl/Admin/Line/line_taxi_edit.html');
    }

    public function lineTaxiFeePage($line_id)
    {
        $selWhere['line_taxi_id'] = $line_id;
        $delBaseFee = M('LineTaxiBaseFee')->where($selWhere)->order('line_taxi_base_fee_id ASC')->select();
        $delDurationFee = M('LineTaxiDurationFee')->where($selWhere)->order('line_taxi_duration_fee_id ASC')->select();
        $delLongdistanceFee = M('LineTaxiLongdistanceFee')->where($selWhere)->order('line_taxi_longdistance_fee_id ASC')->select();
        $delMileageFee = M('LineTaxiMileageFee')->where($selWhere)->order('line_taxi_mileage_fee_id ASC')->select();
        $delWaitTimeoutFee = M('LineTaxiWaitTimeoutFee')->where($selWhere)->order('line_taxi_wait_timeout_fee_id ASC')->select();
        $data = [];
        for($i = 0; $i < count($delBaseFee); $i++) {
            $item['region_start_time'] = substr($delBaseFee[$i]['region_start_time'], 0, 5);
            $item['region_end_time'] = substr($delBaseFee[$i]['region_end_time'], 0, 5);
            $item['base_fee'] = $delBaseFee[$i]['price'];
            $item['include_distance'] = $delBaseFee[$i]['base_distance'];
            $item['duration_fee'] = $delDurationFee[$i]['price'];
            $item['mileage_fee'] = $delMileageFee[$i]['price'];
            $item['base_distance'] = $delLongdistanceFee[$i]['base_distance'];
            $item['longdistance_fee'] = $delLongdistanceFee[$i]['price'];
            $item['base_time'] = $delWaitTimeoutFee[$i]['base_time'];
            $item['wait_timeout_fee'] = $delWaitTimeoutFee[$i]['price'];
            $data[] = $item;
        }
        $this->assign('line_taxi_id', $line_id);
        $this->assign('data', $data);
        $this->display('Tpl/Admin/Line/line_taxi_fee.html');
    }

    //包车列表
    public function lineCharteredListPage()
    {
        $this->display('Tpl/Admin/Line/lineCharteredList.html');
    }
    //添加包车
    public function lineCharteredPage()
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 2;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        $this->assign("data", $lc->data);
        $carTypeR = $this->select(null, 0, 10, null, 'CarType');
        $this->assign("carTypeList", $carTypeR->data);
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $this->display('Tpl/Admin/Line/LineChartered.html');
    }


    //包车编辑
    public function lineCharteredEditPage($line_charetered_id)
    {

        $lineCharteredR = $this->find(array('id' => $line_charetered_id), 'LineChartered');

        if($lineCharteredR->ret) {
            $startArr = $this->getProvinceCityAreaCodeByCode($lineCharteredR->data['start_address_code']);
            $lineCharteredR->data['start_province_code'] = $startArr['province_code'];
            $lineCharteredR->data['start_city_code'] = $startArr['city_code'];
            $lineCharteredR->data['start_area_code'] = $startArr['area_code'];
            $endArr = $this->getProvinceCityAreaCodeByCode($lineCharteredR->data['end_address_code']);
            $lineCharteredR->data['end_province_code'] = $endArr['province_code'];
            $lineCharteredR->data['end_city_code'] = $endArr['city_code'];
            $lineCharteredR->data['end_area_code'] = $endArr['area_code'];
            $lineCharteredPrice = M()->table('cp_line_chartered_price lcp')
                ->JOIN("LEFT JOIN cp_car_type ct ON lcp.car_type_id = ct.car_type_id")->field('ct.car_type_id,lcp.price,lcp.channel_price, ct.name')->where(array('is_del' => 0,'line_chartered_id' => $lineCharteredR->data['id']))->select();
            $lineCharteredR->data['lci'] = $this->select(array('line_id' => $lineCharteredR->data['id'],'type' => \CommonDefine::ORDER_TYPE_2), null, null, null, 'LineCharteredFkLineCategory', 'line_category_id lci')->data;
            $lineCharteredR->data['dls'] = $this->select(array('line_id' => $lineCharteredR->data['id'],'type' => \CommonDefine::ORDER_TYPE_2), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $llcCate = M('LineCharteredFkLineCategory')->where(['line_id' => $line_charetered_id,'mchid' => $this->state->user_id])->select();

        $lineCharteredR->data['lc'] = array_column($llcCate, 'line_category_id');


        $lineCharteredR->data['dl'] = implode(",", array_column($lineCharteredR->data['dls'], 'dls'));
        unset($lineCharteredR->data['lci'],$lineCharteredR->data['dls']);
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 2;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $where['branchid'] = $lineCharteredR->data['branchid'];
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name');
        $this->assign("data", $lc->data);
        $this->assign("drivers", $driver->data);
        $this->assign('line', $lineCharteredR->data);
        $carTypeR = $this->select(null, 0, 10, null, 'CarType');
        foreach($carTypeR->data as $key => $val) {
            foreach($lineCharteredPrice as $item) {
                if($item['car_type_id'] == $val['car_type_id']) {
                    $carTypeR->data[$key]['price'] = $item['price'];
                    $carTypeR->data[$key]['channel_price'] = $item['channel_price'];
                }
            }
        }
        $this->assign("carTypeList", $carTypeR->data);
        $this->display('Tpl/Admin/Line/LineCharteredEdit.html');
    }

    //代办--列表
    public function getAgencyListPage()
    {
        $this->display('Tpl/Admin/Line/agency.html');
    }

    //代办--添加
    public function addAgencyPage()
    {
        $this->display('Tpl/Admin/Line/add_agency.html');
    }

    //代办--编辑
    public function editAgencyPage($agency_id)
    {
        $agencyR = $this->find(array('agency_id' => $agency_id), 'Agency');
        $this->assign("data", $agencyR->data);
        $this->display('Tpl/Admin/Line/edit_agency.html');
    }

    //带货--列表
    public function getTakeGoodsPriceListPage()
    {
        $this->display('Tpl/Admin/Line/tgp.html');
    }

    //带货--添加
    public function addTakeGoodsPricePage()
    {
        $this->display('Tpl/Admin/Line/add_tgp.html');
    }

    //带货--编辑
    public function editTakeGoodsPricePage($tgp_id)
    {
        $takeGoodsPriceR = $this->find(array('take_goods_price_id' => $tgp_id,'is_del' => 0), 'TakeGoodsPrice');
        $this->assign("data", $takeGoodsPriceR->data);
        $this->display('Tpl/Admin/Line/edit_tgp.html');
    }

    //类型--列表
    public function getLineCategoryPage()
    {
        $lineCategory = M('LineCategoryBusiness')->where(['is_select' => 1])->select();
        if($this->mchid == 1091) {
            unset($lineCategory[0]);
            unset($lineCategory[1]);
        }
        $this->assign('lineCategory', $lineCategory);
        $this->display('Tpl/Admin/Line/line_category.html');
    }

    public function getLinePricingPage()
    {
        $pricingVehicles = [
            1 => '经济型',
            2 => '优享型',
            3 => '7座商务型',
            4 => '品质专车',
            5 => '豪华型',
        ];

        $kuaichePricingVehicles = [
            1 => '经济型',
        ];

        $agencyR = $this->find(array('mchid' => $this->state->user_id), 'LineVehiclePricing');
        if (!$agencyR->ret) {
            foreach ($pricingVehicles as $title) {
                $data['mchid'] = $this->state->user_id;
                $data['title'] = $title;
                $data['kuaiche_type'] = 2;
                $data['created_at'] = date("Y-m-d H:i:s");
                $data['updated_at'] = date("Y-m-d H:i:s");
                $data['business_id'] = 7;
                $r = $this->add('LineVehiclePricing', $data);
            }

            foreach ($kuaichePricingVehicles as $title) {
                $data['mchid'] = $this->state->user_id;
                $data['title'] = $title;
                $data['kuaiche_type'] = 1;
                $data['created_at'] = date("Y-m-d H:i:s");
                $data['updated_at'] = date("Y-m-d H:i:s");
                $data['business_id'] = 7;
                $r = $this->add('LineVehiclePricing', $data);
            }
        }
        $this->display('Tpl/Admin/Line/line_vehicle_pricing.html');
    }

    public function addLinePricingPage()
    {
        $this->display('Tpl/Admin/Line/add_line_pricing.html');
    }

    public function editLinePricingPage($id)
    {
        $agencyR = $this->find(array('id' => $id), 'LineVehiclePricing');
        $this->assign("data", $agencyR->data);
        $this->display('Tpl/Admin/Line/edit_line_pricing.html');
    }

    //类型--添加
    public function addLineCategoryPage()
    {
        $lineCategory = M('LineCategoryBusiness')->where(['is_select' => 1])->select();
        if($this->mchid == 1091) {
            unset($lineCategory[0]);
            unset($lineCategory[1]);
        }
        $this->assign('lineCategory', $lineCategory);
        $this->display('Tpl/Admin/Line/add_line_category.html');
    }

    //类型--编辑
    public function editLineCategoryPage($id)
    {
        $lineCategory = M('LineCategoryBusiness')->where(['is_select' => 1])->select();
        if($this->mchid == 1091) {
            unset($lineCategory[0]);
            unset($lineCategory[1]);
        }
        $this->assign('lineCategory', $lineCategory);
        $agencyR = $this->find(array('id' => $id), 'LineCategory');
        $this->assign("data", $agencyR->data);
        $this->display('Tpl/Admin/Line/edit_line_category.html');
    }

    /**
     * 班线列表
     *
     * @return void
     */
    public function lineClassListPage()
    {
        if($this->adminInfo['group_id'] == 3) {
            $where['branchid'] = $this->state->user_id;
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,car_brand,total_seating');


        $ids = [];
        $bwhere['mchid'] = $this->state->user_id;
        $pricings = M('LineFastBaseFee')->where($bwhere)->group('pricing')->field('pricing')->select();
        if ($pricings) {
            foreach ($pricings as $val) {
                $ids[] = $val['pricing'];
            }
        }

        $dispatchMode = \CommonDefine::LINE_CLASS_APPOINT_CONFIG_0;//默认自动派单
        $dispatchModeR = $this->find(array('account_id' => $this->state->user_id , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CLASS_APPOINT_CONFIG), 'SystemConfig');
        if ($dispatchModeR->ret) {
            $dispatchMode = $dispatchModeR->data['value'];
        }

        $this->assign("dispatch_mode", $dispatchMode);

        # 计价车辆
        $pwhere['mchid'] = $this->state->user_id;
        $pwhere['_string'] = " deleted_at IS NULL ";
        $pwhere['_string'] = sprintf(" id IN (%s)", implode(',', $ids));
        $result = M('LineVehiclePricing')->where($pwhere)->field('id,title')->order('id DESC')->select();
        $this->assign('pricing_vehicles', $result);

        $this->assign("drivers", $driver->data);
        // $this->assign('line_class_ferry', $this->queryMerchantFerryService());
        $this->display('Tpl/Admin/Line/line_class_list.html');
    }



    //班线--添加
    public function lineClassPage()
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = 0;
        $where['line_category_business_id'] = 5;
        $lineCategory = M('LineCategory')->where($where)->select();
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        $this->assign("data", $lc->data);
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name');
        $this->assign("drivers", $driver->data);
        $this->assign("points_deduction_url", $this->generatePointsDeductionUrl());
        $this->assign("points_service", $this->queryPointsService());
        $this->assign("lineCategory", $lineCategory);
        $this->assign('line_class_ferry', $this->queryMerchantFerryService());
        $this->assign('pickup_polygons', json_decode($lineInfo['pickup_polygons']));
        $this->assign('dropoff_polygons', json_decode($lineInfo['dropoff_polygons']));
        $this->display('Tpl/Admin/Line/line_class.html');
    }

    protected function generatePointsDeductionUrl()
    {
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$this->state->user_id}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        return sprintf('%s/#/pointsMall/commoditySetting?token=%s', C('CC_MCH_HOST'), $token['data']['access_token']);
    }

    /**
     * 班线编辑 Action
     *
     * @param [type] $line_class_id
     * @return void
     */
    public function lineClassEditPage($line_class_id)
    {
        $lineInfo = M('LineClass')->where(['id' => $line_class_id])->find();

        if(!$lineInfo) {
            die('班线不存在');
        }
        $lineClassPoint['up'] = [];
        $lineClassPoint['down'] = [];
        $lineClassPoint['any_up'] = [];
        $lineClassPoint['any_down'] = [];

        if($lineInfo['start_address_type'] == 1) {
            $lineClassPoint['up'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 1,'is_return' => 0])->select();
        } else {
            $lineClassPoint['any_up'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 1,'is_return' => 0])->select();
        }

        if($lineInfo['end_address_type'] == 1) {
            $lineClassPoint['down'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 2,'is_return' => 0])->select();
        } else {
            $lineClassPoint['any_down'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 2,'is_return' => 0])->select();
        }


        $lineClassPoint['return_up'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 1,'is_return' => 1])->find();
        $lineClassPoint['return_down'] = M('LineClassPoint')->where(['line_class_id' => $line_class_id,'type' => 2,'is_return' => 1])->find();

        $viaPoints = M('DingzhikeyunLineViaPoints')->where(['line_class_id' => $line_class_id])->find();
        if ($viaPoints) {
            $lineInfo['via_province_code'] = $viaPoints['province_code'];
            $lineInfo['via_province_name'] = $viaPoints['province_name'];
            $lineInfo['via_city_code'] = $viaPoints['city_code'];
            $lineInfo['via_city_name'] = $viaPoints['city_name'];
            $lineInfo['via_area_code'] = $viaPoints['area_code'];
            $lineInfo['via_name'] = $viaPoints['name'];
        }

        $arr = array('start_polygon','end_polygon','return_start_polygon','return_end_polygon');

        $where['mchid'] = $where1['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['line_category_business_id'] = 5;
        $lineCategory = M('LineCategory')->where($where)->select();
        unset($where['line_category_business_id']);
        $where1['line_class_id'] = $line_class_id;
        $thisLineCat = M('line_class_fk_line_category')->where($where1)->select();

        foreach ($lineCategory as $key => $value) {
            foreach ($thisLineCat as $k => $v) {
                if ($v['line_category_id'] == $value['id']) {
                    $lineCategory[$key]['checked'] = 'checked';
                    break;
                } else {
                    $lineCategory[$key]['checked'] = '';
                }
            }
        }

        foreach ($arr as $key => $value) {
            if (!empty($lineInfo[$value])) {
                $temp = implode('-', json_decode($lineInfo[$value], true));
            } else {
                $temp = '';
            }
            $this->assign($value, $temp);
        }
        $seatPriceArr = json_decode($lineInfo['seat_price'], true);
        foreach($seatPriceArr as $kk => $seatPrice) {
            $lineInfo['seat_name'][] = $seatPrice['name'];
            $lineInfo['seat_price_'][] = $seatPrice['price'];
        }
        $staringTimes = empty($lineInfo['starting_time']) ? false : json_decode($lineInfo['starting_time'], true);
        $staringIntervalTimes = empty($lineInfo['starting_interval_time']) ? false : json_decode($lineInfo['starting_interval_time'], true);
        $this->assign('thisLineCat', $thisLineCat);
        $this->assign('lineCategory', $lineCategory);
        $this->assign('line', $lineInfo);
        $this->assign('starting_times', $staringTimes);
        $this->assign('starting_interval_times', $staringIntervalTimes);
        $this->assign('up', $lineClassPoint['up']);
        $this->assign('down', $lineClassPoint['down']);
        $this->assign('any_up', $lineClassPoint['any_up']);
        $this->assign('any_down', $lineClassPoint['any_down']);
        $this->assign('return_up', $lineClassPoint['return_up']);
        $this->assign('return_down', $lineClassPoint['return_down']);
        $this->assign('pickup_polygons', json_decode($lineInfo['pickup_polygons']));
        $this->assign('dropoff_polygons', json_decode($lineInfo['dropoff_polygons']));
        $this->assign('line_class_ferry', $this->queryMerchantFerryService());
        $this->assign("points_deduction_url", $this->generatePointsDeductionUrl());
        $this->assign("points_service", $this->queryPointsService());
        $this->display('Tpl/Admin/Line/line_class_edit.html');
    }

    //线路排班表
    public function lineClassSchedulePage()
    {
        $schedulingDateNum = 29;
        $currentTime = time();
        $searchStartDate = date('Y-m-d', $currentTime);
        $searchEndDate = date('Y-m-d', strtotime($searchStartDate . " +$schedulingDateNum day"));
        $dateList[] = date('m.d', $currentTime);
        for($i = 1; $i <= $schedulingDateNum; $i++) {
            $dateList[] = date('m.d', strtotime($searchStartDate . " +$i day"));
        }
        $this->assign("search_start_date", $searchStartDate);
        $this->assign("search_end_date", $searchEndDate);
        $this->assign("date_list", $dateList);
        $this->assign("date_list_len", $schedulingDateNum);
        $this->display('Tpl/Admin/Line/line_class_schedule.html');
    }

    /**
     * 班次列表
     *
     * @param integer $line_class_id
     * @return void
     */
    public function lineClassTrainListPage($line_class_id = 0, $line_class_train_no = '')
    {
        if($this->adminInfo['group_id'] == 3) {
            $where['branchid'] = $this->state->user_id;
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;

        if ($line_class_id) {
            $where['line_class_id'] = $line_class_id;
        }

        if ($line_class_train_no) {
            $where['line_class_train_no'] = $line_class_train_no;
        }
        
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,car_brand,total_seating');

        # 未购买摆渡车功能，不展示摆渡车配置列
        $this->assign('line_class_ferry', $this->queryMerchantFerryService());

        $this->assign("drivers", $driver->data);
        $this->assign('line_class_id', $line_class_id);
        $this->assign('line_class_train_no', $line_class_train_no);
        $this->assign("current_date", date("Y-m-d", time()));
        $this->display('Tpl/Admin/Line/line_class_train_list.html');
    }

    //班次--添加
    public function lineClassTrainPage($line_class_id)
    {
        $lineClassR = $this->find(array('id' => $line_class_id), 'LineClass');
        if(!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $startAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['start_address_code'])->data['address']."]";
        $start_route = $startAddress . $lineClassR->data['start_name'];
        $endAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['end_address_code'])->data['address']."]";
        $end_route = $endAddress . $lineClassR->data['end_name'];

        /*        $where = 'd.mchid='.$this->state->user_id.
                    ' AND d.is_freeze ='.\CommonDefine::IS_FREEZE_0.
                    ' AND d.driver_type='.\CommonDefine::DRIVER_TYPE_1.
                    ' AND dl.type='.\CommonDefine::ORDER_TYPE_5.
                    ' AND dl.line_id ='.$line_class_id;
                $driver = M()->table('cp_driver d')
                            ->join("LEFT JOIN cp_driver_line dl ON d.driver_id = dl.driver_id")
                            ->where($where)
                            ->field('d.driver_id,d.name')
                            ->select();

                $this->assign("drivers",$driver);*/
        $where['mchid'] = $this->state->user_id;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,car_brand');
        $this->assign("drivers", $driver->data);
        $this->assign('start_route', $start_route);
        $this->assign('end_route', $end_route);
        $this->assign('line_class_id', $line_class_id);
        $this->display('Tpl/Admin/Line/line_class_train.html');
    }

    //班次--编辑
    public function lineClassTrainEditPage($line_class_train_id)
    {
        $lineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id), 'LineClassTrain');
        if(!$lineClassTrainR->ret) {
            return $this->output($lineClassTrainR);
        }

        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
        if(!$lineClassR->ret) {
            return $this->output($lineClassR);
        }
        $startAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['start_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['start_address_code'])->data['address']."]";
        $start_route = $startAddress . $lineClassR->data['start_name'];
        $endAddress = "[".$this->checkingGdParentAddressCode($lineClassR->data['end_address_code'])->data['address']."·".$this->checkingGdAddressCode($lineClassR->data['end_address_code'])->data['address']."]";
        $end_route = $endAddress . $lineClassR->data['end_name'];
        $where['mchid'] = $this->state->user_id;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_1;
        //        $where['from_type'] = \CommonDefine::FROM_TYPE_0;
        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,car_brand');
        $this->assign("drivers", $driver->data);
        $this->assign("lineClassTrainData", $lineClassTrainR->data);
        $this->assign('start_route', $start_route);
        $this->assign('end_route', $end_route);
        $this->display('Tpl/Admin/Line/line_class_train_edit.html');
    }

    //售票列表
    public function lineClassTrainSellOrderListPage($line_class_train_no)
    {
        $whereLineClassTrain['line_class_train_no'] = $line_class_train_no;
        $whereLineClassTrain['is_del'] = \CommonDefine::IS_DEL_0;
        $whereLineClassTrain['mchid'] = $this->mchid;
        if($this->admin_group_id == 3) {
            $whereLineClassTrain['branchid'] = $this->state->user_id;
        }
        $lineClassTrainR = $this->find($whereLineClassTrain, 'LineClassTrain');
        if(!$lineClassTrainR->ret) {
            return $this->error("班次信息错误，请稍后再试");
        }

        $whereLineClass['id'] = $lineClassTrainR->data['line_class_id'];
        $lineClassR = $this->find($whereLineClass, 'LineClass');
        if(!$lineClassR->ret) {
            return $this->error("班次信息错误，请稍后再试");
        }
        $lineClassTrainR->data['route'] = $lineClassR->data['start_name'] ." ➞ ".$lineClassR->data['end_name'];
        if($lineClassR->data['start_time_type'] == 2) {
            $lineClassR->data['start_time'] = "<p><b>乘客自定义出发时间</p>"
                ."<p><b>最早：</b>".$lineClassR->data['start_earliest_time']."</p>"
                ."<p><b>最晚：</b>".$lineClassR->data['end_latest_time']."</p>";
        }
        $lineClassR->data['is_seat_selection_info'] = "不支持选座";
        if($lineClassR->data['is_seat_selection'] == 1) {
            $lineClassR->data['is_seat_selection_info'] = "支持选座";
        }

        $lineClassTrainR->data['sell_tickets'] = 0;

        // 处理 total_tickets 和 remain_tickets
        $lineClassTrainR->data['total_tickets'] = ($lineClassTrainR->data['total_tickets'] == -1) ? '不限' : $lineClassTrainR->data['total_tickets'];
        $lineClassTrainR->data['remain_tickets'] = ($lineClassTrainR->data['remain_tickets'] == -1) ? '不限' : $lineClassTrainR->data['remain_tickets'];

        $selled = M('Order')->field("sum(book_seating) as sell_tickets")->where(array('line_class_train_id' => $lineClassTrainR->data['line_class_train_id'], 'type' => \CommonDefine::ORDER_TYPE_5))->find();
        $lineClassTrainR->data['sell_tickets'] = isset($selled['sell_tickets']) ? $selled['sell_tickets'] : 0;

        $lineDriverWhere['lctd.line_class_train_id'] = $lineClassTrainR->data['line_class_train_id'];
        $lineDriversList = M()->table("cp_line_class_train_driver as lctd")
            ->join("cp_driver d ON d.driver_id = lctd.driver_id")
            ->where($lineDriverWhere)
            ->field("lctd.driver_id,d.name,d.cellphone,d.car_tail_number")
            ->select();

        $this->assign('line_class_train', $lineClassTrainR->data);
        $this->assign('line_class', $lineClassR->data);
        $this->assign('order_type', \CommonDefine::ORDER_TYPE_5);
        $this->assign('line_drivers', $lineDriversList);
        $this->display('Tpl/Admin/Line/line_class_train_sell_order_list.html');
    }

    //顺风车计价--编辑
    public function mileagePriceEditPage()
    {
        $where['mchid'] = $this->state->user_id;
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $mileagePriceR = $this->find($where, 'MileagePrice');
        $this->assign("data", $mileagePriceR->data);
        $this->display('Tpl/Admin/Line/edit_mileage_price.html');
    }

    /*电话叫车管理*/
    public function linePhoneListPage()
    {
        /*电话叫车服务*/
        $linePhoneServiceR = $this->find(array('mchid' => $this->state->user_id), 'LinePhoneService');
        if(!$linePhoneServiceR->ret) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }

        $this->display('Tpl/Admin/Line/line_phone_list.html');
    }

    public function linePhonePage()
    {
        /*电话叫车服务*/
        $linePhoneServiceR = $this->find(array('mchid' => $this->state->user_id), 'LinePhoneService');
        if(!$linePhoneServiceR->ret) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 1;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        $this->assign("data", $lc->data);
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        //        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $this->display('Tpl/Admin/Line/line_phone.html');
    }
    public function linePhoneEditPage($line_id)
    {
        /*电话叫车服务*/
        $linePhoneServiceR = $this->find(array('mchid' => $this->state->user_id), 'LinePhoneService');
        if(!$linePhoneServiceR->ret) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }
        $line = $this->find(array('id' => $line_id), 'Line');
        if($line->ret) {
            $startArr = $this->getProvinceCityAreaCodeByCode($line->data['start_address_code']);
            $line->data['start_province_code'] = $startArr['province_code'];
            $line->data['start_city_code'] = $startArr['city_code'];
            $line->data['start_area_code'] = $startArr['area_code'];
            $endArr = $this->getProvinceCityAreaCodeByCode($line->data['end_address_code']);
            $line->data['end_province_code'] = $endArr['province_code'];
            $line->data['end_city_code'] = $endArr['city_code'];
            $line->data['end_area_code'] = $endArr['area_code'];
            $line->data['lci'] = $this->select(array('line_id' => $line->data['id'],'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'LineFkLineCategory', 'line_category_id lci')->data;
            $line->data['dls'] = $this->select(array('line_id' => $line->data['id'],'type' => \CommonDefine::ORDER_TYPE_1), null, null, null, 'DriverLine', 'driver_id dls')->data;
        }
        $line->data['lc'] = array_column($line->data['lci'], 'lci');
        $line->data['dl'] = implode(",", array_column($line->data['dls'], 'dls'));
        unset($line->data['lci'],$line->data['dls']);
        if($this->adminInfo['group_id'] == 3) {
            $where['mchid'] = $this->adminInfo['parent_admin_id'];
        } else {
            $where['mchid'] = $this->state->user_id;
        }
        $where['is_del'] = \CommonDefine::IS_DEL_0;
        $where['line_category_business_id'] = 1;
        $lc = $this->select($where, null, null, null, 'LineCategory', 'id,title');
        unset($where['line_category_business_id']);
        //        $where['driver_type'] = \CommonDefine::DRIVER_TYPE_0;
        $where['is_freeze'] = \CommonDefine::IS_FREEZE_0;
        //        $where['driver_role_type'] = \CommonDefine::DRIVER_ROLE_0;
        $where['branchid'] = $line->data['branchid'];
        $driver = $this->select($where, null, null, null, 'Driver', 'driver_id,name,cellphone');
        $this->assign("drivers", $driver->data);
        $this->assign("data", $lc->data);
        $this->assign('line', $line->data);
        $this->display('Tpl/Admin/Line/line_phone_edit.html');
    }
}

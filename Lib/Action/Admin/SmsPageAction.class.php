<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/2/8
 * Time: 10:54
 */
class SmsPageAction extends AdminCoreAction
{
    //短信-短信列表
    public function indexPage()
    {
        $this->redirectMerchantUrl();
        if ($this->admin_group_id == 2) {//总台
            $where['admin_id'] = $this->state->user_id;
            $mchR = $this->find($where, 'Admin');
            $this->assign('sms_count', $mchR->ret ? $mchR->data['shortmessage'] : "获取失败,请稍后重试");
        }
        $this->display('Tpl/Admin/Sms/index.html');
    }

    /**
     * 跳转商户后台
     * @Date 2025.03.10
     * <AUTHOR>
     */
    public function redirectMerchantUrl()
    {
        $mchid = $this->state->user_id;
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if ($token['status'] == 'success'
            && $token['data']['access_token']) {
            redirect(C('CC_MCH_HOST') . '/#/resourceManage/smsManage/smsSendRecord?token=' . $token['data']['access_token']);
        }
    }
}

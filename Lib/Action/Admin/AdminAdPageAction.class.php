<?php

class AdminAdPageAction extends AdminCoreAction
{
    //后台总览
    public function adListPage()
    {
        $this->redirectMerchantUrl('/#/promotionManage/adManage');
        $mchid = $this->state->user_id;
        $mchService = M('AdService')->where(['mchid' => $mchid])->find();
        if (!$mchService) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }
        $where['parent_id'] = ['neq', 0];
        $parent_info = M('AdCategory')->where(['parent_id' => ['eq', 0]])->select();
        $parent_arr = [];
        foreach ($parent_info as $key => $value) {
            $parent_arr[$value['id']] = $value['name'];
        }
        $res = M('AdCategory')->where($where)->select();
        foreach ($res as $k => $v) {
            $res[$k]['name'] = $parent_arr[$v['parent_id']] . '--' . $v['name'];
        }
        $this->assign('cate', $res);
        $this->display('Tpl/Admin/ad/adlist.html');
    }

    /**
     * 跳转商户后台
     * @Date 2025.03.10
     * <AUTHOR>
     */
    public function redirectMerchantUrl($uri)
    {
        $mchid = $this->state->user_id;
        $tokenResponse = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/account_types/mch/account_ids/{$mchid}/access_token", 'post');
        $token = json_decode($tokenResponse, true);
        if (
            $token['status'] == 'success'
            && $token['data']['access_token']
        ) {
            redirect(C('CC_MCH_HOST') . $uri . '?token=' . $token['data']['access_token']);
        }
    }

    //后台总览
    public function addPage($id = 0)
    {
        $mchid = $this->state->user_id;
        $mchService = M('AdService')->where(['mchid' => $mchid])->find();
        if (!$mchService) {
            $this->display('Tpl/Admin/Common/buy.html');
            exit();
        }
        $this->assign('cate', M('adCategory')->select());
        if ($id) {
            $info = M('MchAd')->where(['id' => $id])->find();
            $info['start_time'] = date('Y-m-d', $info['start_time']);
            $info['end_time'] = date('Y-m-d', $info['end_time']);
            $cate_id = M('MchAdCate')->where(['ad_id' => $id])->field('cate_id')->select();
            foreach ($cate_id as $key => $value) {
                $cate_arr[$key] = $value['cate_id'];
            }
            $this->assign('mch_ad_cate', $cate_arr);
            $this->assign('info', $info);
            $this->assign('limit_bizs', explode(',', $info['limit_biz']));
            $this->assign('id', $id);
        }
        $this->display('Tpl/Admin/ad/add.html');
    }
}

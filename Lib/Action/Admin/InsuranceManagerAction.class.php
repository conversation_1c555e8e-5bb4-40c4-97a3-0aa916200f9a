<?php

import('@/Action/Admin/OrderManagerAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 保险管理
 *
 * <AUTHOR>
 */
class InsuranceManagerAction extends AdminCoreAction
{

    /**
     * 获取保险列表
     * @param string $fields 查询的字段（以英文逗号分隔，默认为空）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     * @param string $user_name 用户名 （默认为空，查询所有）
     * @param datetime $min_buy_time 购保日期最小值（默认为空，查询所有）
     * @param datetime $max_buy_time 购保日期最大值（默认为空，查询所有）
     * @param int $state 　订单状态
     */
    public function getInsurances($fields = null, $page = 1, $size = 10, $user_name = null, $min_buy_time = null, $max_buy_time = null, $state = null)
    {
        $passengerInsuranceModel = new PassengerInsuranceModel();
        $where = array();
        if (!empty($user_name)) {
            $where['Passenger.user_name'] = $user_name;
        }
        if (!empty($state)) {
            $where['OrderTable.state'] = intval($state);
        }
        if (!empty($min_buy_time) && empty($max_buy_time)) {
            $where['DriverCost.create_time'] = array('egt', $min_buy_time);
        } else if (empty($min_buy_time) && !empty($end_time)) {
            $where['DriverCost.create_time'] = array('elt', $max_buy_time);
        } else if (!empty($min_buy_time) && !empty($end_time)) {
            $where['DriverCost.create_time'] = array(array('egt', $min_buy_time), array('elt', $max_buy_time));
        }
        $passengerTable = (new PassengerModel())->getTableName();
        $orderTable = (new OrderModel())->getTableName();
        $data = $passengerInsuranceModel->alias('PassengerInsurance')->join(" $passengerTable Passenger on Passenger.passenger_id=PassengerInsurance.passenger_id")->join(" $orderTable OrderTable on OrderTable.order_id=PassengerInsurance.order_id")->where($where)->page($page, $size)->field('PassengerInsurance.insurance_id')->order('PassengerInsurance.create_time desc')->select();
        if ($data) {
            $count = $passengerInsuranceModel->alias('PassengerInsurance')->join(" $passengerTable Passenger on Passenger.passenger_id=PassengerInsurance.passenger_id")->join(" $orderTable OrderTable on OrderTable.order_id=PassengerInsurance.order_id")->where($where)->count('PassengerInsurance.insurance_id');
            $r = new ResultModel(true, $data, $count);
        } else {
            $r = new ResultModel(false);
        }
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                $t = $this->sudoGetEntityById("insurance_id", $value['insurance_id'], 'InsuranceManager', 'getInsurance', $fields);
                if ($t->ret) {
                    $r->data[$key] = $t->data;
                } else {
                    return $this->output($t);
                }
            }
        }
        return $this->output($r);
    }

    /**
     * 获取保险详细
     * @param int $insurance_id 保险ID
     * @param string $fields 查询的字段（以英文逗号分隔，默认为空）
     */
    public function getInsurance($insurance_id, $fields = null)
    {
        $r = $this->getEntityById($insurance_id, 'PassengerInsurance', $fields, null, 'passenger_id');
        if ($r->ret) {
            $this->sudoLoadSubEntity($r, 'passenger_id', 'passenger_id', 'Passenger\\Account', 'getPassenger', $fields, 'passenger');
            $this->sudoLoadSubEntity($r, 'order_id', 'order_id', 'OrderManager', 'getOrder', $fields, 'Order');
        }
        return $this->output($r);
    }

}

?>

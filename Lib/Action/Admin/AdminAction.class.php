<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Admin/SystemSettingAction');

/**
 * 管理员模块
 *
 * <AUTHOR>
 */
class AdminAction extends AdminCoreAction
{
    /**
     * 登录
     * @param string $account 账户
     * @param string $password 密码
     */
    public function doLogin($account, $password)
    {
        $where['_complex'] = array(
            'account' => $account,
            'cellphone' => $account,
            '_logic' => 'OR'
        );
        if (C('ENV') != 'local') {
            $where['password'] = md5($password);
        }
        $where['is_del'] = array('neq', 1);
        $r = $this->find($where);
        // 兼容手机号登录
        if (!$r->ret) {
            unset($where['account']);
            $r = $this->find($where);
        }
        $endtime = $r->data['endtime'];
        if ($endtime) {
            //            if (strtotime($endtime) < time()) {
            //                $drLimit = array(
            //                    'is_freeze' => 1,
            //                    'mchid' => $r->data['admin_id']
            //                );
            //                $ret = $this->save('Driver', $drLimit);
            //                if ($ret->ret) {
            //                    return $this->output(new ResultModel(false, '对不起，您的套餐已到期，请联系客户经理'));
            //                } else {
            //                    return $this->output(new ResultModel(false, '请稍后再试'));
            //                }
            //
            //            }
        }

        if ($r->ret) {
            if ($r->data['branch_type']  == \CommonDefine::BRANCH_TYPE_1) {
                return $this->output(new ResultModel(false, '无权限登陆'));
            }
            //保存登录状态
            StateModel::save($r->data['admin_id'], $password, StateModel::$ADMIN_USER);
        } else {
            return $this->output(new ResultModel(false, '账户或密码错误'));
        }
        $this->doAddLog("[登录]" . "登陆账号：" . $account . ",IP地址：" . get_client_ip());
        cookie('mchid', $r->data['admin_id']);
        $data = M('user_auth')->where('uid=' . $r->data['admin_id'] . " AND auth_type=" . \CommonDefine::AUTH_TYPE_1)->find();
        if ($data['isauthorized'] == 1) {
            return $this->output(new ResultModel(true, 'first'));
        }
        if (!$this->isEmpty($data)) {
            if ($r->data['group_id'] == 2) {
                return $this->output(new ResultModel(true, 'first'));
            }
        }
        return $this->output(new ResultModel(true, "登陆成功"));
    }

    /**
     * 创建账户（只有超级管理员才有权限）
     * @param int $group_id 组ID
     * @param string $account 账户名称
     * @param string $email 邮箱
     * @param string $password 密码
     */
    public function doCreateAccount($group_id, $account, $email, $password, $package_id, $tel, $contact_tel, $appsecret, $count, $mchname, $parent = 0, $status = 0, $mch_wechat_qr = '', $mch_wechat_qr_path = '')
    {
        if (!$this->mobile($contact_tel)) {
            return $this->output(new ResultModel(false, 'BOSS号码必须为手机号'));
        }

        if (!$this->areaCode($count)) {
            return $this->output(new ResultModel(false, '套餐分数必须是数字'));
        }
        if (!$this->isEmpty($account)) {
            return $this->output(new ResultModel(false, '商户名称不能为空'));
        }
        if (!$this->isEmpty($password)) {
            return $this->output(new ResultModel(false, '密码不能为空'));
        }
        if (!$this->isEmpty($mchname)) {
            return $this->output(new ResultModel(false, '登陆名称不能为空'));
        }
        if (!$this->checkEmail($email)) {
            return $this->output(new ResultModel(false, '请输入正确的邮箱格式'));
        }

        $this->doAddLog("创建账户");
        $this->startTrans();
        $data = M('package')->where('package_id=' . $package_id)->find();
        $this->data['starttime'] = date("Y-m-d 00:00:00", time());
        if ($data['type'] == \CommonDefine::PACKAGE_TIME_TYPE_1) {
            $this->data['endtime'] = date("Y-m-d 23:59:59", strtotime($this->data['starttime'] . " +$count month"));
        } elseif ($data['type'] == \CommonDefine::PACKAGE_TIME_TYPE_2) {
            $this->data['endtime'] = date("Y-m-d 23:59:59", strtotime($this->data['starttime'] . " +$count year"));
        } else {
            $this->transRollback();
            return $this->output(new ResultModel(false, '套餐类型错误'));
        }
        $this->data['usestorage'] = $data['storage'];
        $this->data['shortmessage'] = $data['shortmessage'];
        $this->data['tel'] = $tel;
        $this->data['status'] = 0;
        $this->data['appsecret'] = $appsecret;
        $this->data['driver_num'] = $data['driver'];
        $this->data['free_ride_driver_num'] = $data['free_ride_driver'];
        $this->data['manager_num'] = $data['managernum'];

        $id = $this->add();
        if ($id & $id->ret) {
            $sys = new SystemSettingAction();
            $data = array(
                'mchid' => $id->data,
                'money' => 0,
                'split' => 0
            );
            if ($mch_wechat_qr_path or $mch_wechat_qr) {
                $res = M('MchWechatQr')->add(['mchid' => $id->data, 'mch_wechat_qr_path' => $mch_wechat_qr_path, 'mch_wechat_qr' => $mch_wechat_qr, 'create_time' => date('Y-m-d H:i:s')]);
                if (!$res) {
                    $this->transRollback();
                    $this->result = new ResultModel(false, '添加二维码失败！');
                    return $this->output();
                }
            }
            $this->commitTrans();
            $sys->adnnul($data);
        } else {
            $this->transRollback();
            $this->result = new ResultModel(false, '添加失败');
        }
        return $this->output();
    }
    public function doAddMchLogs($mchid, $title, $content, $id = null)
    {
        if (!$this->isEmpty($mchid)) {
            return $this->output(new ResultModel(false, '商户id不能为空！'));
        }
        if (!$this->isEmpty($title)) {
            return $this->output(new ResultModel(false, '标题不能为空'));
        }
        if (!$id) {
            unset($this->data['id']);
            $this->doAddLog("添加商户日志");
            $this->add('Mchlog');
        } else {
            $this->doAddLog("修改商户日志");
            $this->save('Mchlog');
        }
        return $this->output();
    }
    public function doDeletedMchLog($id)
    {
        if (!$this->isEmpty($id)) {
            return $this->output(new ResultModel(false, '商户id不能为空！'));
        }
        $this->data['is_del'] = 1;
        $this->save('Mchlog');
        $this->doAddLog("删除商户日志");
        return $this->output();
    }
    public function getMchlog($mchid, $page = 1, $size = 10)
    {
        $r = $this->select(array('mchid' => $mchid, 'is_del' => 0), $page, $size, 'create_time desc', 'Mchlog');
        foreach ($r->data as $key => $value) {
            $info = $this->find(['admin_id' => $value['mchid']], 'Admin', 'mchname');
            $r->data[$key]['mchname'] = $info->data['mchname'];
        }
        return $this->output($r);
    }
    public function upsecret($admin_id, $secret)
    {
    }

    /**
     * 修改账户（只有超级管理员才有权限）
     * @param int $admin_id 管理员ID
     * @param int $group_id 组ID
     * @param string $account 账户名称
     * @param string $email 邮箱
     * @param string $password 密码
     */
    public function doUpdateAccount($admin_id, $group_id, $account, $tel, $email, $count, $password, $package_id, $mchname)
    {
        $this->doAddLog("修改账户");
        if (empty($password)) {
            remove_arr($this->data, 'password');
        } else {
            $this->data['password'] = md5($password);
        }
        $addata = M('admin')->where('admin_id=' . $admin_id)->find();
        $pkdata = M('package')->where('package_id=' . $package_id)->find();
        $olddriver = M('driver')->field('driver_id')->where('mchid=' . $admin_id . ' and status=0')->limit($pkdata['driver'])->order('driver_id desc')->select();
        $didgroup = '';
        foreach ($olddriver as $irwm) {
            $didgroup .= $irwm['driver_id'] . ',';
        }
        $idstr = rtrim($didgroup, ',');

        //套餐续期或更改套餐
        if ($addata['status'] == 0) {
            //续期
            if ($package_id == $addata['package_id']) {
                if ($count < $addata['count']) {
                    $this->result = new ResultModel(false, '套餐使用时间不能后退');
                    $this->output();
                } else {
                    $num = $count - $addata['count'];
                    if ($num != 0) {
                        $time = $num * $pkdata['usefultime'] * 24 * 60 * 60;
                        $this->data['endtime'] = $addata['endtime'] + $time; //时间累加
                        $this->data['status'] = 0;
                    }
                }
            } //更改套餐 ,之前的剩余线下折算与用户协商
            else {
                $data = $pkdata;
                $endtime = $count * $data['usefultime'] * 24 * 60 * 60; //时间重新定义
                $endtime = time() + $endtime;
                $this->data['starttime'] = date("Y-m-d H:i:s", time());
                $this->data['endtime'] = date("Y-m-d 23:59:59", $endtime);
                //                $this->data['usestorage'] = $data['storage'];
                //                $this->data['shortmessage'] = $data['shortmessage'];
                $this->data['driver_num'] = $data['driver'];
                $this->data['tel'] = $tel;
                $this->data['manager_num'] = $data['managernum'];
                //套餐变更修改修改套餐内的数量
            }
        } else {
            $data = $pkdata;
            $endtime = $count * $data['usefultime'] * 24 * 60 * 60; //时间重新定义
            $endtime = time() + $endtime;
            $this->data['status'] = 0;
            $this->data['starttime'] = date("Y-m-d H:i:s", time());
            $this->data['endtime'] = date("Y-m-d 23:59:59", $endtime);
            $this->data['usestorage'] = $data['storage'];
            $this->data['tel'] = $tel;
            $this->data['shortmessage'] = $data['shortmessage'];
            $this->data['driver_num'] = $data['driver'];
            $this->data['manager_num'] = $data['managernum'];
        }
        $is_freeze = array(
            'is_freeze' => 0
        );
        $un_freeze = array(
            'is_freeze' => 1
        );
        $this->startTrans();
        $ressult = $this->save();
        if ($ressult->ret) {
            //            $code1 = M('driver')->where('status=0 and  driver_id not in ' . "(" . $idstr . ")")->data($un_freeze);
            //            $codemode1 = $code1->save();

            //            $code2 = M('driver')->where('status=0 and  driver_id in ' . "(" . $idstr . ")")->data($is_freeze);
            //            $codemode2 = $code2->save();

            //            if ($codemode2) {
            $this->commitTrans();
            $this->result = new ResultModel(true, '修改成功');
            //            } else {
            //                $this->transRollback();
            //            }
        } else {
            $this->result = new ResultModel(false, '修改失败');
        }
        return $this->output($this->result);
    }

    /**
     * 删除账户（只有超级管理员才有权限）
     * @param int $admin_id 管理员ID
     */
    public function doDeleteAccount($admin_id)
    {
        $this->doAddLog("删除账户");

        $this->delete($admin_id);
        return $this->output();
    }

    /**
     * 批量删除账户（只有超级管理员才有权限）
     * @param int $admin_ids 管理员ID集合（以英文逗号分隔）
     */
    public function doDeleteAccounts($admin_ids)
    {
        $this->doAddLog("批量删除账户");
        $admin_id_arr = explode(",", $admin_ids);
        if (empty($admin_id_arr)) {
            return $this->output(new ResultModel(false, '参数格式错误'));
        }
        $this->startTrans();
        foreach ($admin_id_arr as $value) {
            if (!empty($value)) {
                $o = $this->sudo('Admin', $this->state->user_id);
                $o->data = array('admin_id' => $value);
                $r = $o->doDeleteAccount($value);
                if (!$r->ret) {
                    $this->transRollback();
                    return $this->output($r);
                }
            }
        }
        $this->commitTrans();
        return $this->output(new ResultModel(true));
    }

    /**
     * 退出登录
     */
    public function doLogout()
    {
        /**
         * 退出重设AccessToken
         * <AUTHOR>
         * @date 2022.07.22
         */
        $requestLogFile = LOG_PATH . "inner_api_request.log";
        \Log::write(session('mch_access_token'), \Log::DEBUG, \Log::FILE, $requestLogFile);
        if (session('mch_access_token')) {
            $headers = array(
                "Authorization: Bearer " . session('mch_access_token'),
                "Accept: application/json",
            );
            $res = sendXmlOverPost(C('CC_INNER_API_HOST') . "/admin/mch/logout", array(), $headers, false);
            session('mch_access_token', null);

            \Log::write($res, \Log::DEBUG, \Log::FILE, $requestLogFile);
            \Log::write(session('mch_access_token'), \Log::DEBUG, \Log::FILE, $requestLogFile);
        }
        $this->doAddLog("退出登录");
        StateModel::clear(StateModel::$ADMIN_USER);
        return $this->output(new ResultModel(true));
    }

    /**
     * 修改密码
     * @param string $prepassword 原始密码
     * @param string $password 新密码
     * @param string $repassword 重复新密码
     */
    public function doChangePassword($prepassword, $password, $repassword)
    {
        if (!$this->isEmpty($prepassword)) {
            return $this->output(new \ResultModel(false, "请输入原始密码"));
        }
        if (!$this->isEmpty($password)) {
            return $this->output(new \ResultModel(false, "请输入新密码"));
        }

        $this->doAddLog("修改密码");
        $r = $this->find(array('admin_id' => $this->state->user_id, 'password' => md5($prepassword)), 'Admin', 'admin_id');
        if ($r->ret) {
            $r = $this->save('Admin', array('password' => md5($password), 'admin_id' => $this->state->user_id, 'repassword' => $repassword));
            if ($r->ret) {
                StateModel::save($this->state->user_id, $password, StateModel::$ADMIN_USER);
            }
        } else {
            return $this->output(new ResultModel(false, '原始密码错误'));
        }
        return $this->output($r);
    }

    /**
     * 获取账户信息
     * @param int $admin_id 管理员ID
     * @param string $fields 查询的字段（以英文逗号分隔，默认查询所有）
     */
    public function getAccount($admin_id, $fields = null)
    {
        $this->doAddLog("获取账户信息");
        $r = $this->getEntityById($admin_id, 'Admin', $fields, 'password', 'group_id');
        if ($r->ret) {
            $this->loadSubEntity($r, 'group_id', 'MetaAdminGroup', $fields, null, null, 'group');
            $this->loadSubEntity($r, 'package_id', 'Package', $fields, null, null, 'package');
            $this->result = $r;
        }
        return $this->output();
    }

    public function test()
    {
    }

    /**
     * 获取所有账户
     * @param string $fields 查询的字段（以英文逗号分隔，默认查询所有）
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getAccounts($fields = null, $page = 1, $size = 10)
    {
        $this->doAddLog("查看所有账户");
        $r = $this->select(array('group_id' => 2, 'is_del' => 0), $page, $size, 'create_time desc', 'Admin', 'admin_id');
        if ($r->ret) {
            $data = array();
            foreach ($r->data as $value) {
                $data[] = $this->sudoGetEntityById('admin_id', $value['admin_id'], 'Admin', 'getAccount', $fields)->data;
            }
            foreach ($data as $k => $v) {
                $res = M('MchWechatQr')->where(['mchid' => $v['admin_id']])->find();
                if ($res) {
                    $data[$k]['wecharQr'] = $res['mch_wechat_qr_path'];
                } else {
                    $data[$k]['wecharQr'] = false;
                }
            }
            return $this->output(new ResultModel(true, $data, $r->count));
        }
        return $this->output($r);
    }


    /**
     * id揭秘
     *
     **/

    public function checkauthcode($chart)
    {
        return $this->checkauth($chart);
    }

    /**
     * 微信支付配置
     **/
    public function payConfig($mchid)
    {
        $r = $this->find(array('mchid' => $mchid), 'PayConfig');
        if ($r->ret) {
            echo json_encode(array('data' => $r->data));
        } else {
            echo json_encode(array('data' => array('appid' => '', 'secret' => '', 'mchnumber' => '', 'apisecret' => '', 'memberid' => '', 'apiclient_cert' => '', 'apiclient_key' => '')));
        }
    }

    /*保存配置*/
    public function paysave($mchid, $appid, $secret, $mchnumber, $memberid, $apiclient_cert, $apiclient_key, $apisecret)
    {
        $this->data['mchid'] = $mchid;
        $this->data['appid'] = $appid;
        $this->data['secret'] = $secret;
        $this->data['apisecret'] = $secret;
        $this->data['mchnumber'] = $mchnumber;
        $this->data['memberid'] = $memberid;
        $this->data['apiclient_cert'] = $apiclient_cert;
        $this->data['apiclient_key'] = $apiclient_key;
        $r = $this->find(array('mchid' => $mchid), 'PayConfig');
        if ($r->ret) {
            //            $res = $this->save('PayConfig', array('mchid' => $mchid));
            //            if ($res->ret) {
            //                echo json_encode(array('data' => $res->data));
            //            } else {
            //                echo json_encode(array('data' => "修改失败"));
            //            }
            $data = M('pay_config')->where('mchid=' . $mchid)->data($this->data)->save();
            if ($data) {
                echo json_encode(array('data' => true));
                ;
            } else {
                echo json_encode(array('data' => false));
            }
        } else {
            $res = $this->add('PayConfig');
            if ($res->ret) {
                echo json_encode(array('data' => $res->data));
            } else {
                echo json_encode(array('data' => "保存失败"));
            }
        }
    }
}

<?php

/**
 * Created by PhpStorm.
 * User: zeus
 * Date: 2017/10/3
 * Time: 上午12:25
 */
class NotificationPageAction extends AdminCoreAction
{
    //新增推送模板
    public function tplListPage(){
        $this->display('Tpl/Admin/Notice/list.html');
    }

    //修改微信模版
    public function tplAddPage(){
        $this->display('Tpl/Admin/Notice/tpl_add.html');
    }

    //修改推送模板
    /**
     * 修改模板
     * @param int $id 模版id
     */
    public function tplEditPage($id){
        $wechatTplCategoryR = $this->find(array('id' => $id), 'WechatTplCategory');
        if(!$wechatTplCategoryR->ret){
           return false;
        }
        $this->assign('tpl_id',$id);
        $this->assign('wechatTplCategoryData', $wechatTplCategoryR->data);
        $this->display('Tpl/Admin/Notice/tpl_edit.html');
    }

    //账户管理->商户模板配置
    /**
     * 商户模板配置
     * @param int $mchid 商户id
     */
    public function merchantTplListPage($mchid)
    {
        $this->assign("mchid",$mchid);
        $this->display('Tpl/Admin/Notice/merchant_tpl_list.html');
    }

    //账户管理->商户模板配置
    /**
     * 添加商户模板
     * @param int $mchid 商户id
     */
    public function merchantTplAddPage($mchid)
    {
        $tplList = M()->table("cp_wechat_tpl wt")
            ->join("LEFT JOIN cp_wechat_tpl_category wtc ON wtc.id = wt.c_id")
            ->where('mchid='.$mchid)
            ->field('wtc.title,wt.wechat_tpl_code as code,wtc.id')
            ->select();

        var_dump($tplList);die;
        /*        $tplList = M('wechat_tpl_category')->select();
                foreach($tplList as $key=>$val){
                    $wechatTplR = $this->find(array('c_id'=>$val['id'],'mchid'=>$mchid),"WechatTpl",'wechat_tpl_code');
                    $tplList[$key]['code'] = $wechatTplR->data['wechat_tpl_code'];
                }*/
        $this->assign("tplList",$tplList);
        $this->assign("mchid",$mchid);
        $this->display('Tpl/Admin/Notice/merchant_tpl_list.html');
    }
}
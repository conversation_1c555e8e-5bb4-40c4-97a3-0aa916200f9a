<?php

/**
 * Created by PhpStorm.
 * User: ASUS
 * Date: 2017/8/3
 * Time: 17:33
 * 提现管理模块
 */
class BankManagerAction extends AdminCoreAction{

    /**
     *获取银卡卡列表
     **/
    public function doBankList($page = 0  , $size = 10 ,$is_del = 0){
        $where['is_del'] = $is_del;
        $mchR = $this->find(array('admin_id'=> $this->state->user_id), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }
        $where['mchid'] = $mchR->data['admin_id'];
        $where['default'] = 1;
        $r = $this->select($where , $page ,$size ,null, 'Banks');
        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据....",null));
        }
        $this->output($r);
    }

    /**
     *获取银卡卡列表
     **/
    public function doAdminBankList($mchid = null,$page = 0  , $size = 10 ,$is_del = 0){
        $where['is_del'] = $is_del;
        if(empty($mchid)){
            return $this->output(new ResultModel(false,"参数异常....",null));
        }
        $mchR = $this->find(array('admin_id'=> $mchid), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }
        $where['mchid'] = $mchR->data['admin_id'];
        $r = $this->select($where , $page ,$size ,null, 'Banks');
        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据....",null));
        }
        $this->output($r);
    }

    /**
     * 余额提现
     * @param $bankId 银行Id
     * @param $money 金额
     *
     */
    public function doDestoonFinanceCash($bankId = null , $money = null){

        if($money < 10){
            return $this->output(new ResultModel(false,"提现金额不能少于10元",null));
        }

        $mchR = $this->find(array('admin_id'=> $this->state->user_id), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }
        if($mchR->data['balance'] <= 0){
            $this->save('Admin',array('admin_id'=>$mchR->data['admin_id'],'balance' => 0));
            return $this->output(new ResultModel(false,"余额不足。。。",null));
        }
        if($mchR->data['balance'] <  $money){
            return $this->output(new ResultModel(false,"提现金额不能超过余额",null));
        }
        if(empty($bankId)){
            $banksR = $this->find(array('mchid'=>$mchR->data['admin_id'] , 'default' => 1) ,'Banks');
        }else{
            $banksR = $this->find(array('id'=>$bankId) ,'Banks');
        }

        if(!$banksR->ret){
            return $this->output(new ResultModel(false,"请设置一个默认的银行卡账户",null));
        }

        if(strpos($banksR->data['account'], '公司') === false){
            return $this->output(new ResultModel(false,"为了您账户的资金安全，请尽快联系管理员将提现账户更换为对公账户！",null));
        }

        $data['money'] = $money;
        $data['destoon_finance_cash_no'] = $this->createDestoonFinanceCashNo();
        $data['settlement_rate'] = C('WX_SETTLEMENT_RATE');
        $data['real_amount'] = $money - ($money*$data['settlement_rate']);
        $data['left_money'] = $mchR->data['balance'] - $money;
        $data['status'] = 2; // 处理中
        $data['bank_id'] = $banksR->data['id'];
        $data['mchid'] = $mchR->data['admin_id'];
        $data['account'] = $banksR->data['account'];
        $data['bank_name'] = $banksR->data['bank_name'];
        $data['bank_crad_number'] = $banksR->data['bank_crad_number'];
        $data['create_time'] = date("Y-m-d H:i:s");

        try{
            $this->startTrans();
            $dfcR = $this->add('DestoonFinanceCash',$data);//加入到提现余额表里面
            if(!$dfcR->ret){
                $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }
            //修改商户提现后的余额
            $balanceR = $this->save('Admin',array('admin_id'=>$mchR->data['admin_id'] , 'balance'=> $mchR->data['balance'] -  $data['money'] < 0 ? floatval(0) : $mchR->data['balance'] -  $data['money']));
            if(!$balanceR->ret){
                $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }else{
                $this->commitTrans();
                //总台提现审核成功
                if (C('SMS_ON')) {
                    $mchR = $this->find(array('admin_id' => $this->mchid),'Admin');
                    $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                    $smsUtil->sendTemplateSMS("15680770083", array(',【商户:'.$mchR->data['mchname']."】,", ':'.$data['real_amount'].'元的申请，请及时'), \SMSUtil::TEMP_ID_DEPOSITRESULT, 181);
                }


                return $this->output(new ResultModel(true,"提现成功",null));
            }

        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }

    /**
     *添加银行卡
     * @param $mchid 商户id
     * @param $bank_card_number 银行卡号
     * @param $bank_start_name  银行开户名
     * @param $bank_name        开户的银行名称
     * @param $default          是否设置为默认 1-是；0-否
     **/
    public function doCreateBank($mchid = null,$bank_card_number , $bank_start_name ,$bank_name ,$default = 1){
        if(empty($mchid)){
            return $this->output(new ResultModel(false,"参数异常....",null));
        }
        $mchR = $this->find(array('admin_id'=>$mchid), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }
        if(empty($bank_card_number)){
            return $this->output(new ResultModel(false,"银行卡号不能为空！",null));
        }
        if(empty($bank_start_name)){
            return $this->output(new ResultModel(false,"银行开户名不能为空！",null));
        }
        if(empty($bank_name)){
            return $this->output(new ResultModel(false,"开户的银行名称不能为空！",null));
        }

        $data['bank_crad_number'] = $bank_card_number;
        $data['mchid'] = $mchid;
        $bankR = $this->find($data,'Banks');
        if($bankR->ret){
            return $this->output(new ResultModel(false,"该银行的卡号已经存在！",null));
        }
        $banksR = $this->find(array('mchid' => $mchid), 'Banks');
        if($banksR->ret){
            $default = 0;
        }

        $data['bank_name'] = $bank_name;
        $data['account'] = $bank_start_name;
        $data['mchid'] = $mchR->data['admin_id'];
        try{
            $this->startTrans();
            $data['default'] = $default;
            $r = $this->add('Banks',$data);
            if(!$r->ret){
                $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }
            $this->commitTrans();return $this->output(new ResultModel(true,"添加成功",null));
        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }

    /**
     *修改银行卡
     * @param $bank_id          银行编号
     * @param $bank_card_number 银行卡号
     * @param $bank_start_name  银行开户名
     * @param $bank_name        开户的银行名称
     **/
    public function doUpdateBank($mchid,$bank_id , $bank_card_number , $bank_start_name ,$bank_name , $is_del = 0 ){

        if(empty($mchid)){
            return $this->output(new ResultModel(false,"参数异常....",null));
        }
        $mchR = $this->find(array('admin_id'=>$mchid), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }

        $bankR = $this->find(array('id'=>$bank_id ,'is_del'=>$is_del) ,'Banks');
        if(!$bankR->ret){
            return $this->output(new ResultModel(false,"该银行卡不存在!",null));
        }

        if(empty($bank_card_number)){
            return $this->output(new ResultModel(false,"银行卡号不能为空！",null));
        }
        if(empty($bank_start_name)){
            return $this->output(new ResultModel(false,"银行开户名不能为空！",null));
        }
        if(empty($bank_name)){
            return $this->output(new ResultModel(false,"开户的银行名称不能为空！",null));
        }

        $data['bank_crad_number'] = $bank_card_number;
        $data['account'] = $bank_start_name;
        $data['bank_name'] = $bank_name;
        $data['default'] = $bankR->data['default'];
        $data['id'] = $bank_id;

        try{
            $this->startTrans();
            $r = $this->save('Banks',$data);
            if(!$r->ret){
                $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }
            $this->commitTrans();return $this->output(new ResultModel(true,"修改成功",null));
        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }

    /**
     *删除银行卡
     * @param $bank_id 银行编号
     **/
    public function doDelBank($bank_id=null ,$is_del = 0){
        if(empty($bank_id)){
            return $this->output(new ResultModel(false,"请选择需要删除的银行卡！",null));
        }
        $bankR = $this->find(array('id'=>$bank_id ,'is_del'=>$is_del) ,'Banks');
        if(!$bankR->ret){
            return $this->output(new ResultModel(false,"你选择需要删除的银行不存在！",null));
        }

        try{
            $this->startTrans();
            $data['is_del'] = 1;
            $data['id'] = $bankR->data['id'];
            $r = $this->save('Banks',$data);
            if(!$r->ret){
                $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }
            $this->commitTrans();return $this->output(new ResultModel(true,"删除成功",null));
        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }

    /**
     *设置默认银行卡
     * @param $bank_id 银行编号
     **/
    public function doSetDefaultBank($bank_id ,$default = 1){
        if($this->admin_group_id == 2){
            $mchR = $this->find(array('admin_id'=> $this->state->user_id , 'group_id' => 2), 'Admin');

            if(!$mchR->ret){
                return $this->output(new ResultModel(false,"该商户不存在....",null));
            }
        }

        $data = array('id'=>$bank_id);
        $bankR = $this->find($data ,'Banks');
        if($bankR->ret){
            if($bankR->data['default'] == 1){
                return $this->output(new ResultModel(false,"该银行卡已经是默认的....",null));
            }
        }

        try{
            $this->startTrans();
            $param = array('mchid' => $bankR->data['mchid'] ,'default'=>$default);
            $bR = $this->find($param ,'Banks');
            if($bR->ret){
                $result = M('banks')->where($param)->save(array('mchid' => $bankR->data['mchid'] ,'default'=>0));
                if(!$result){
                    $this->transRollback();
                    return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
                }
            }
            $data['default'] = $default;
            $r = $this->save('Banks',$data);
            if(!$r->ret){
                $this->transRollback();
                return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
            }
            $this->commitTrans();
            return $this->output(new ResultModel(true,"设置成功...",null));
        }catch (Exception $e){
            $this->transRollback();
            return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }

    /**
     *获取商户提现列表
     **/
    public function doBranchDepositList($page = 0  , $size = 10 ,$is_del = 0){
        $where['is_del'] = $is_del;
        $mchR = $this->find(array('admin_id'=> $this->state->user_id), 'Admin');
        if(!$mchR->ret){
            return $this->output(new ResultModel(false,"该商户不存在....",null));
        }

        $where = '';
        if($this->admin_group_id == 2){
            $where['mchid'] = $this->state->user_id;
        }
        $order = 'id desc';
        $r = $this->select($where , $page ,$size ,$order, 'DestoonFinanceCash');
        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据....",null));
        }
        foreach($r->data as $key=>$val){
            $bankR = $this->find(array('id'=>$val['bank_id']),'Banks');
            if($bankR->ret){
                $r->data[$key]['bank_name'] = $bankR->data['bank_name'];
                $r->data[$key]['bank_crad_number'] = $bankR->data['bank_crad_number'];
                $r->data[$key]['account'] = $bankR->data['account'];
            }else{
                $r->data[$key]['bank_name'] = null;
                $r->data[$key]['bank_crad_number'] = null;
                $r->data[$key]['account'] = "";
            }
            $r->data[$key]['settlement_rate'] = $r->data[$key]['settlement_rate']*100;
            $r->data[$key]['settlement_rate'] .='%' ;
        }
        $this->output($r);
    }

    /**
     *获取提现列表
     **/
    public function doDepositList($page = 0  , $size = 10 ,$is_del = 0, $search_mchid = null){
        if($this->admin_group_id == 1){//平台
            if(!empty($search_mchid)){
                $where['mchid'] = $search_mchid;
            }
        }
        $order = 'id desc';
        $r = $this->select($where , $page ,$size ,$order, 'DestoonFinanceCash');
        if(!$r->ret){
            return $this->output(new ResultModel(false,"暂无数据....",null));
        }
        foreach($r->data as $key=>$val){
            $bankR = $this->find(array('id'=>$val['bank_id']),'Banks');
            $adminR = $this->find(array('admin_id'=>$bankR->data['mchid']),'Admin');
            if($bankR->ret){
                $r->data[$key]['bank_name'] = $bankR->data['bank_name'];
                $r->data[$key]['bank_crad_number'] = $bankR->data['bank_crad_number'];
                $r->data[$key]['mchname'] = $adminR->data['mchname']."<br/>".$adminR->data['account'];
                $r->data[$key]['account'] = $bankR->data['account'];
                $r->data[$key]['balance'] = $adminR->data['balance'];
            }else{
                $r->data[$key]['bank_name'] = null;
                $r->data[$key]['bank_crad_number'] = null;
                $r->data[$key]['mchname'] = null;
                $r->data[$key]['account'] = "";
                $r->data[$key]['balance'] = 0;
            }
            $r->data[$key]['settlement_rate'] = $r->data[$key]['settlement_rate']*100;
            $r->data[$key]['settlement_rate'] .='%' ;
        }
        $this->output($r);
    }

    /**
     *设置提现处理状态
     * @param $id 银行编号
     **/
    public function doIsDisabled($id ,$status = 1){

        $data = array('id'=>$id );
        $dfcR = $this->find($data ,'DestoonFinanceCash');
        if($dfcR->ret && $dfcR->data['status'] == $status){
            return $this->output(new ResultModel(false,"请不要重复提交数据....",null));
        }
        $data['status'] = $status; //提现成功

        try{
            $this->startTrans();
            if($status == 0 ){
                $banksR = $this->find(array('id'=>$dfcR->data['bank_id'] ),'Banks');
                if(!$banksR->ret){
                    $this->transRollback();return $this->output(new ResultModel(false,"该条数据未绑定任何银行卡",null));
                }

                $adminR = $this->find(array('admin_id'=>$banksR->data['mchid']),'Admin','balance,admin_id');
                if(!$adminR->ret){
                    $this->transRollback();return $this->output(new ResultModel(false,"该商户不存在，请认真核实后再进行操作",null));
                }

                $param['balance'] = $adminR->data['balance'] + $dfcR->data['money'];
                $param['admin_id'] = $adminR->data['admin_id'];
                $aR = $this->save('Admin',$param);
                if(!$aR->ret){
                    $data['status'] = 3; //拒绝提现
                }
                $data['status'] = 4;
            }

            $r = $this->save('DestoonFinanceCash',$data);

            if($r->ret){
                $this->commitTrans();return $this->output(new ResultModel(true,"处理成功",null));
            }else{
                $this->transRollback(); return $this->output(new ResultModel(false,"处理失败",null));
            }
        }catch (Exception $e){
            $this->transRollback();return $this->output(new ResultModel(false,"服务器异常,请稍后在操作",null));
        }
    }
}
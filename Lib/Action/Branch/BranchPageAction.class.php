<?php

import('@/Action/Admin/OrderManagerAction');
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Passenger/PassengerCoreAction');
import('@/Action/Passenger/AccountAction');
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of AdminPageAction
 *
 * <AUTHOR>
 */
class BranchPageAction extends BranchCoreAction
{
    public function __construct()
    {
        parent::__construct();
        //设置商户参数
        $this->_setMerchantConfig();
    }

    //分支首页
    public function indexPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();

        if (!$this->mchid) {
            return false;
        }

        $this->systemIsMaintenance();
        $this->getMchInfoToPageByMchid($this->mchid);

        if($this->remind()) {
            $this->display('Tpl/Common/remind.html');
            return;
        }

        if(in_array($this->mchid, $this->mchid_stop_arr)) {
            $this->display('Tpl/Common/tempremind.html');
            return;
        }

        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());

        if(APP_DEBUG) {
            $cellphone = C('DEBUG_BRANCH_ACCOUNT');
            $password = C('DEBUG_BRANCH_PASSWORD');

            $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password),'group_id' => 3, 'parent_admin_id' => $this->mchid), 'Admin', 'admin_id,status,mchname,driver_num,balance,branch_type');

            if ($r->ret) {
                //判断是否被删除
                if (intval($r->data['status']) === 1) {
                    return $this->output(new \ResultModel(false, '账号异常'));
                }
                $r->data['branchid'] = $r->data['admin_id'];
                unset($r->data['admin_id']);
                unset($r->data['status']);
                \StateModel::save($r->data['branchid'], $password, \StateModel::$BRANCH_USER, array('mchname' => $r->data['mchname'],'branch_type' => $r->data['branch_type']), $this->mchid);
            } else {
                return $this->output(new \ResultModel(false, '账号不存在！'));
            }

            $status = 1;
            if($r->data['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                redirect(C('WEB_ROOT') . 'branch_channel_create');
            } else {
                $this->assign('status', $status);
                $this->display('Tpl/Branch/Index/index.html');
            }
            return;
        }

        //登陆状态
        $status = 0;
        $this->showBottomMenu();
        if($this->state->user_id) {
            $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            if($branchR->ret) {
                if(empty($branchR->data['openidforpay']) || empty($branchR->data['openid'])) {
                    $wxUrlR = $this->getMerchantWeChatUrlForAuth($this->mchid, $this->state->user_id, $this->state->data['branch_type']);
                    if($wxUrlR->ret) {
                        header("Location:".$wxUrlR->data['wx_url']);
                        return;
                    }
                }
            }

            $status = 1;
            if($this->state->data['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                redirect(C('WEB_ROOT') . 'branch_channel_create/callback/'.$this->getCipmchidByMchid($this->mchid)->data['ciphertext']);
                return;
            } else {
                $this->assign('status', $status);
                $this->display('Tpl/Branch/Index/index.html');
                return;
            }
        }

        $this->assign('status', $status);
        $this->display('Tpl/Branch/Index/login.html');
    }

    /**
     * 根据URL参数判断是否显示底部导航菜单
     * @return void
     */
    private function showBottomMenu()
    {
        if (isset($_GET['hide_bottom_menu'])
            && $_GET['hide_bottom_menu'] == 'true') {
            $this->assign('show_bottom_menu', false);
        } else {
            $this->assign('show_bottom_menu', true);
        }
    }


    public function createPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if($this->state->user_id) {
            $status = 1;
            $this->assign('bid', $this->state->user_id);
            //派单模式
            $robOrderConfig = 0;//未开启
            $systemConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => 'rob_order'), 'SystemConfig');
            if($systemConfigR->ret) {
                $robOrderConfig = $systemConfigR->data['value'];
            }
            $this->assign('robOrder', $robOrderConfig);
        }
        $this->assign('status', $status);
        $line_class_name = '定制班线车';
        $take_goods_name = '带货';
        $agency = '代办';
        if($this->mchid == 950 || $this->mchid == 969) {
            $line_class_name = '城际网约车';
        }
        if($this->state->user_id == 950) {
            $take_goods_name = '小件速运';
            $agency = '跑腿';
        }
        if($this->mchid == 1032) {
            $line_class_name = '巴士';
        }
        $this->assign('line_class_name', $line_class_name);
        $this->assign('take_goods_name', $take_goods_name);
        $this->assign('agency', $agency);
        $this->showBottomMenu();

        $this->display('Tpl/Branch/Index/create_orders.html');
    }


    public function recordPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/order_record.html');
    }

    /**
     * 渠道分台=>代约
     */
    public function channelCreatePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        //登陆状态
        $status = 0;
        if($this->state->user_id) {
            $status = 1;
            $this->assign('bid', $this->state->user_id);
            //派单模式
            $robOrderConfig = 0;//未开启
            $systemConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => CommonDefine::SYSTEM_ROLE_1,'key' => 'rob_order'), 'SystemConfig');
            if($systemConfigR->ret) {
                $robOrderConfig = $systemConfigR->data['value'];
            }
            $this->assign('robOrder', $robOrderConfig);
        }
        $this->assign('status', $status);
        $line_class_name = '定制班线车';
        $take_goods_name = '带货';
        $agency = '代办';
        if($this->mchid == 950 || $this->mchid == 969) {
            $line_class_name = '城际网约车';
        }
        if($this->state->user_id == 950) {
            $take_goods_name = '小件速运';
            $agency = '跑腿';
        }
        if($this->mchid == 1032) {
            $line_class_name = '巴士';
        }
        $this->assign('line_class_name', $line_class_name);
        $this->assign('take_goods_name', $take_goods_name);
        $this->assign('agency', $agency);

        $this->assign('line_class_name', $line_class_name);

        $this->display('Tpl/Branch/Index/channel_create_orders.html');
    }
    //渠道分台注册
    public function channelregisterPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/branch_register.html');
    }
    //渠道分台提交资料
    public function channelsubmitPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/branch_submit.html');
    }
    public function driverHistoryPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/driverHistory.html');
    }
    //提现
    public function branchaccountPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        if($this->state->user_id) {
            $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin', 'balance,tel,cellphone,mchname');
            $this->assign('branchname', $branchR->data['mchname']);
            $this->assign('cellphone', $branchR->data['cellphone']);
            $this->assign('balance', $branchR->data['balance'] ? ($branchR->data['balance'] ? $branchR->data['balance'] : "0.00") : "0.00");

            $split = C('WX_SETTLEMENT_RATE');
            if($split) {
                $this->assign('split', $split * 100);
            } else {
                $this->assign('split', null);
            }
        }
        $this->display('Tpl/Branch/Index/account.html');
    }
    //提现记录
    public function branchaccounthistroyPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/account_histroy.html');
    }
    //组团派单
    public function groupPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        if (!$this->mchid) {
            return false;
        }

        $this->systemIsMaintenance();
        $this->getMchInfoToPageByMchid($this->mchid);

        if($this->remind()) {
            $this->display('Tpl/Common/remind.html');
            return;
        }

        if(in_array($this->mchid, $this->mchid_stop_arr)) {
            $this->display('Tpl/Common/tempremind.html');
            return;
        }

        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());

        if(APP_DEBUG) {
            $cellphone = '***********';
            $password = '123456';
            $r = $this->find(array('cellphone' => $cellphone, 'password' => md5($password),'group_id' => 3, 'parent_admin_id' => $this->mchid), 'Admin', 'admin_id,status,mchname,driver_num,balance,branch_type');

            if ($r->ret) {
                //判断是否被删除
                if (intval($r->data['status']) === 1) {
                    return $this->output(new \ResultModel(false, '账号异常'));
                }
                $r->data['branchid'] = $r->data['admin_id'];
                unset($r->data['admin_id']);
                unset($r->data['status']);
                \StateModel::save($r->data['branchid'], $password, \StateModel::$BRANCH_USER, array('mchname' => $r->data['mchname'],'branch_type' => $r->data['branch_type']), $this->mchid);
            } else {
                return $this->output(new \ResultModel(false, '账号不存在！'));
            }

            $status = 1;
            if($r->data['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                redirect(C('WEB_ROOT') . 'branch_channel_create');
            } else {
                $this->assign('status', $status);
                $this->display('Tpl/Branch/Index/group_send.html');
            }
            return;
        }

        //登陆状态
        $status = 0;
        if($this->state->user_id) {
            $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin');
            if($branchR->ret) {
                if(empty($branchR->data['openidforpay']) || empty($branchR->data['openid'])) {
                    $wxUrlR = $this->getMerchantWeChatUrlForAuth($this->mchid, $this->state->user_id, $this->state->data['branch_type']);
                    if($wxUrlR->ret) {
                        header("Location:".$wxUrlR->data['wx_url']);
                        return;
                    }
                }
            }

            $status = 1;
            if($this->state->data['branch_type'] == \CommonDefine::BRANCH_TYPE_1) {
                redirect(C('WEB_ROOT') . 'branch_channel_create/callback/'.$this->getCipmchidByMchid($this->mchid)->data['ciphertext']);
                return;
            } else {
                $this->assign('status', $status);
                $this->display('Tpl/Branch/Index/group_send.html');
                return;
            }
        }

        $this->assign('status', $status);
        $this->display('Tpl/Branch/Index/login.html');
    }
    public function sendedPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->display('Tpl/Branch/Index/sended_record.html');
    }

    public function branchLoginPage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->display('Tpl/Branch/Index/login.html');
    }
    public function managePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        if($this->state->user_id) {
            $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin', 'balance,tel,cellphone,mchname,total_amount');
            $this->assign('branchname', $branchR->data['mchname']);
            $lineCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'Line');
            $lineCharteredCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'LineChartered');
            $LineClassCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'LineClass');
            $driverCountR = $this->count(array('branchid' => $this->state->user_id), 'Driver');
            $this->assign('cellphone', $branchR->data['cellphone']);
            $this->assign('lines', $lineCountR->data + $lineCharteredCountR->data + $LineClassCountR->data);
            $this->assign('balance', $branchR->data['balance'] ? ($branchR->data['balance'] ? $branchR->data['balance'] : "0.00") : "0.00");
            $this->assign('total_amount', $branchR->data['total_amount'] ? ($branchR->data['total_amount'] ? $branchR->data['total_amount'] : "0.00") : "0.00");
            $this->assign('tel', $branchR->data['tel']);
            $this->assign('drivers', $driverCountR->data ? $driverCountR->data : 0);
            $generationsOrderCountR = $this->count(array('temp_apply_branchid' => $this->state->user_id, 'is_temp' => 1), 'Order');
            $this->assign('generation_order_count', $generationsOrderCountR->ret ? $generationsOrderCountR->data : 0);
        }
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->display('Tpl/Branch/Index/manage.html');
    }

    /**
     * 渠道分台=>我的
     */
    public function channelManagePage()
    {
        $this->checkIsInWeixinAndRedirectByConfig();
        $this->getMchInfoToPageByMchid($this->mchid);

        $this->state = \StateModel::get(\StateModel::$BRANCH_USER, $this->mchid);
        if($this->state->user_id) {
            $branchR = $this->find(array('admin_id' => $this->state->user_id), 'Admin', 'balance,tel,cellphone,mchname');
            $this->assign('branchname', $branchR->data['mchname']);
            $lineCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'Line');
            $lineCharteredCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'LineChartered');
            $LineClassCountR = $this->count(array('branchid' => $this->state->user_id, 'is_del' => 0), 'LineClass');
            $driverCountR = $this->count(array('branchid' => $this->state->user_id), 'Driver');
            $this->assign('cellphone', $branchR->data['cellphone']);
            $this->assign('lines', $lineCountR->data + $lineCharteredCountR->data + $LineClassCountR->data);
            $this->assign('balance', $branchR->data['balance'] ? ($branchR->data['balance'] ? $branchR->data['balance'] : "0.00") : "0.00");
            $this->assign('total_amount', $branchR->data['total_amount'] ? ($branchR->data['total_amount'] ? $branchR->data['total_amount'] : "0.00") : "0.00");
            $this->assign('tel', $branchR->data['tel']);
            $this->assign('drivers', $driverCountR->data ? $driverCountR->data : 0);
            $branchQrR = $this->find(array('branchid' => $this->state->user_id,'type' => \CommonDefine::BRANCH_QR_TYPE_1), 'BranchQrAttachment');
            $this->assign('branch_qr_url', $branchQrR->ret ? $branchQrR->data['qr_url'] : "");
            $generationsOrderCountR = $this->count(array('temp_apply_branchid' => $this->state->user_id, 'is_temp' => 1), 'Order');
            $this->assign('generation_order_count', $generationsOrderCountR->ret ? $generationsOrderCountR->data : 0);
        }
        $this->assign('version_info', $this->getCurrentVersionFrontendInfo());
        $this->display('Tpl/Branch/Index/manage_channel.html');
    }
    //登录页
    public function loginPage()
    {
        if ($this->state) {
            //已经登录，则重定向到主页
            redirect(C('WEB_ROOT') . 'admin_index');
            exit;
        }
        $this->display('Tpl/Admin/Public/login.html');
    }

    private function getMchInfoToPageByMchid($mchid)
    {
        $mchInfo = $this->getMchInfoByMchid($mchid);
        $this->assign('mchname', $mchInfo->data['mchname']);
        $this->assign('parent_tel', $mchInfo->data['tel']);
        $this->assign('callback', $this->getCipmchidByMchid($mchid)->data['ciphertext']);

        $packageR = $this->find(array('package_id' => $mchInfo->data['package_id']), 'Package', 'tech_support');
        if($packageR->ret) {
            $this->assign('tech_support', $packageR->data['tech_support']);
            if($mchid == 166) {
                $this->assign('tech_support', 2);
            }
        } else {
            $this->assign('tech_support', 0);
        }

        if($mchid == 796) {
            $this->assign('tech_text', "穆青社服务中心");
            $this->assign('tech_support', 3);
        }

        if($mchid == 903) {
            $this->assign('tech_text', "长沙优通网络科技");
            $this->assign('tech_support', 3);
        }

        if($mchid == 942) {
            $this->assign('tech_text', "一步顺风车");
            $this->assign('tech_support', 3);
        }

        if($mchid == 969) {
            $this->assign('tech_text', "走起网约车");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1018) {
            $this->assign('tech_text', "搭顺车出行");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1032) {
            $this->assign('tech_text', "自由环球旅行");
            $this->assign('tech_support', 3);
        }

        if($mchid == 937) {
            $this->assign('tech_text', "安厦泉拼车平台");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1050) {
            $this->assign('tech_text', "成乐出行");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1075) {
            $this->assign('tech_text', "鑫约旅行");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1116) {
            $this->assign('tech_text', "斑马裕众");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1135) {
            $this->assign('tech_text', "石首商务车");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1170) {
            $this->assign('tech_text', "享约就约");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1174) {
            $this->assign('tech_text', "滴答城际出行");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1181) {
            $this->assign('tech_text', "帮邦行-奥通");
            $this->assign('tech_support', 3);
        }

        if($mchid == 1289) {
            $this->assign('tech_text', "陈州快车");
            $this->assign('tech_support', 3);
        }
    }

    /**
     * 配置商户信息
     */
    private function _setMerchantConfig()
    {
        $systemConfigR = $this->find(array('key' => \CommonDefine::ID_UPLOAD_CONFIG, 'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid), 'SystemConfig');
        $this->assign('id_upload_config', $systemConfigR->ret ? $systemConfigR->data['value'] : \CommonDefine::ID_UPLOAD_CONFIG_0);
    }
}

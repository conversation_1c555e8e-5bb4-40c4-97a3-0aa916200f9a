<?php

namespace Branch;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 线路模块
 *
 * <AUTHOR>
 */
class LineAction extends \BranchCoreAction
{
    /**
     * 分台-获取拼车线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getLines($page = 1, $size = 1000)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['status'] = 1;
        $where['is_phone_line'] = 0;

        $r = $this->select($where, $page, $size, 'branchid asc,create_time desc', 'Line', 'id,branchid, start_name, end_name,price,start_appointment_time,end_appointment_time,business_time_type,center_start_latlng,center_end_latlng,summary');
        if($r->ret) {
            foreach($r->data as $k => $v) {
                $r->data[$k]['line_id'] = $v['id'];
                $r->data[$k]['bid'] = $v['branchid'];
                $r->data[$k]['line_name'] = $v['start_name']."→".$v['end_name'];
                unset($r->data[$k]['id']);
                unset($r->data[$k]['branchid']);
                unset($r->data[$k]['start_name']);
                unset($r->data[$k]['end_name']);
                if($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }

    /**
     * 渠道分台-获取拼车线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getChannelLines($page = 1, $size = 1000)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['status'] = 1;

        $r = $this->select($where, $page, $size, 'branchid asc,create_time desc', 'Line', 'id,branchid, start_name, end_name,channel_price as price,start_appointment_time,end_appointment_time,business_time_type,center_start_latlng,center_end_latlng,summary');
        if($r->ret) {
            foreach($r->data as $k => $v) {
                $r->data[$k]['line_id'] = $v['id'];
                $r->data[$k]['bid'] = $v['branchid'];
                $r->data[$k]['line_name'] = $v['start_name']."→".$v['end_name'];
                unset($r->data[$k]['id']);
                unset($r->data[$k]['branchid']);
                unset($r->data[$k]['start_name']);
                unset($r->data[$k]['end_name']);
                if($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }

    /**
     * 分台-获取包车线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getLinesChartered($page = 1, $size = 1000)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['status'] = 1;

        $r = $this->select($where, $page, $size, 'branchid asc,create_time desc', 'LineChartered', 'id,branchid, start_name, end_name,start_appointment_time,end_appointment_time,business_time_type,center_start_latlng,center_end_latlng,summary');
        if($r->ret) {
            foreach($r->data as $k => $v) {
                $r->data[$k]['line_id'] = $v['id'];
                $r->data[$k]['bid'] = $v['branchid'];
                $r->data[$k]['line_name'] = $v['start_name']."→".$v['end_name'];
                $r->data[$k]['car_type_data'] = $this->getLineCharteredPriceByLineCharteredId($v['id']);
                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin');
                $r->data[$k]['branchname'] = $branchR->ret ? $branchR->data['mchname'] : "";
                unset($r->data[$k]['id']);
                unset($r->data[$k]['branchid']);
                unset($r->data[$k]['start_name']);
                unset($r->data[$k]['end_name']);
                if($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }

    /**
     * 渠道分台-获取包车线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getChannelLinesChartered($page = 1, $size = 1000)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['status'] = 1;

        $r = $this->select($where, $page, $size, 'branchid asc,create_time desc', 'LineChartered', 'id,branchid, start_name, end_name,start_appointment_time,end_appointment_time,business_time_type,center_start_latlng,center_end_latlng,summary');
        if($r->ret) {
            foreach($r->data as $k => $v) {
                $r->data[$k]['line_id'] = $v['id'];
                $r->data[$k]['bid'] = $v['branchid'];
                $r->data[$k]['line_name'] = $v['start_name']."→".$v['end_name'];
                $r->data[$k]['car_type_data'] = $this->getLineCharteredChannelPriceByLineCharteredId($v['id']);
                unset($r->data[$k]['id']);
                unset($r->data[$k]['branchid']);
                unset($r->data[$k]['start_name']);
                unset($r->data[$k]['end_name']);
                if($r->data[$k]['start_appointment_time'] && $r->data[$k]['end_appointment_time']) {
                    $r->data[$k]['start_appointment_time'] = substr($r->data[$k]['start_appointment_time'], 0, 5);
                    $r->data[$k]['end_appointment_time'] = substr($r->data[$k]['end_appointment_time'], 0, 5);
                }
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }

    /**
     * 分台-获取拼车线路
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getTakeGoodsLines($page = 1, $size = 1000)
    {
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $where['status'] = 1;

        $r = $this->select($where, $page, $size, 'branchid asc,create_time desc', 'Line', 'id,branchid, start_name, end_name,center_start_latlng,center_end_latlng');
        if($r->ret) {
            foreach($r->data as $k => $v) {
                $r->data[$k]['line_id'] = $v['id'];
                $r->data[$k]['bid'] = $v['branchid'];
                $r->data[$k]['line_name'] = $v['start_name']."→".$v['end_name'];
                unset($r->data[$k]['id']);
                unset($r->data[$k]['branchid']);
                unset($r->data[$k]['start_name']);
                unset($r->data[$k]['end_name']);
            }
        } else {
            return $this->output(new \ResultModel(false, '即将开通该线路，敬请期待'));
        }
        return $this->output($r);
    }

    /**
     *  获取某天的班线车
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $day_time 发车时间
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getLineClassTrains($start_address_code = null, $end_address_code = null, $day_time = null, $start_name = '', $end_name = '', $page = 1, $size = 1000)
    {
        $start_time = "";
        $end_time = "";
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s', $currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();
        $dayNum = \diffBetweenTwoDays(date('Y-m-d', $currentTime), $day_time);

        if(empty($day_time)) {
            $start_time = date('Y-m-d 00:00:00', $currentTime);
            $end_time = date('Y-m-d 23:59:59', $currentTime);
        } else {
            $start_time = date('Y-m-d 00:00:00', strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59', strtotime($day_time));
        }

        $where = "lct.mchid = ".$this->mchid;
        $where .= " AND lct.start_date >= '".$start_time."'";
        $where .= " AND lct.start_date <= '".$end_time."'";
        $where .= " AND lct.status = '".\CommonDefine::LINE_CLASS_OPERATE_1."'";

        if(!empty($start_address_code) && $start_address_code != -1) {
            switch($this->getAddressCodeType($start_address_code)) {
                case 3:
                    $where .= (" AND lc.start_area_code = ".$start_address_code);
                    break;
                case 2:
                    $where .= (" AND lc.start_city_code = ".$start_address_code);
                    break;
                case 1:
                    $where .= (" AND lc.start_province_code = ".$start_address_code);
                    break;
            }
        }
        if(!empty($end_address_code)  && $end_address_code != -1) {
            switch($this->getAddressCodeType($end_address_code)) {
                case 3:
                    // $where .= (" AND lc.end_area_code = ".$end_address_code);
                    $tempCode = $this->queryParentAddressCode($end_address_code);
                    if ($tempCode) {
                        $where .= (" AND (lc.end_area_code = '".$end_address_code."' OR lc.end_city_code = '".$tempCode."') ");
                    } else {
                        $where .= (" AND lc.end_area_code = ".$end_address_code);
                    }
                    break;
                case 2:
                    $where .= (" AND lc.end_city_code = ".$end_address_code);
                    break;
                case 1:
                    $where .= (" AND lc.end_province_code = ".$end_address_code);
                    break;
            }
        }

        if ($start_name && $start_address_code != -1) {
            $where .= (" AND ( lc.start_name LIKE '".$start_name."%' )");
        }
        if ($end_name && $end_address_code != -1) {
            $where .= (" AND ( lc.end_name LIKE '".$end_name."%' )");
        }

        $where .= " AND lct.is_del =".\CommonDefine::IS_DEL_0 ;

        $order = "lct.start_time asc, lct.start_earliest_time asc, lct.end_latest_time asc";
        $field = "lct.line_class_id,
                lc.start_time_type,lct.start_earliest_time,lct.end_latest_time,lct.start_time,lct.start_time as starting_time,
                lc.refund_time_set,
                lc.start_city_code,gdrs.name as start_city_name,
                lc.end_city_code,gdre.name as end_city_name,
                lc.start_address_type,
                lc.end_address_type,
                lc.is_return,
                lc.center_start_latlng,lc.center_end_latlng,
                lc.return_start_time_type,lc.return_time_number,lc.return_start_time,lc.return_start_earliest_time,lc.return_end_latest_time,
                lc.return_start_address_type,
                lc.return_start_alias,
                lc.return_end_address_type,
                lc.return_end_alias,
                lct.line_class_train_id,
                lct.line_class_train_no,
                lct.remain_tickets,
                lct.price,
                lc.start_name,
                lc.end_name,
                lc.summary,
                lc.stop_sell_time,
                lc.stop_sell_number,
                lct.travel_time,
                lct.start_date,
                lct.is_seat_selection,
                lct.car_seats,
                lct.seat_layout,
                lct.seat_price";

        $lineClassTrainsArr = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->order($order)
            ->select();

        //总数量
        $lineClassTrainsCount = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->where($where)
            ->field($field)
            ->order($order)
            ->count();


        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        //返程
        //固定上车点
        $returnStartPointField = 'alias,longitude,latitude,use_time';
        $returnStartPointOrder = 'id asc';
        //固定下车点
        $returnEndPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $returnEndPointOrder = 'id asc';
        foreach($lineClassTrainsArr as $k => $lineClassTrain) {
            $lineClassTrainsArr[$k]['is_expire'] = 0;
            $stop_sell_time = \getTimeNumber($lineClassTrain['stop_sell_time']);
            if($dayNum == 0) {
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 0 && $stop_sell_time != 0) {
                    if(strtotime($day_time) == strtotime(date('Y-m-d', time()))) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }

                if($lineClassTrain['start_time_type'] == 2) {
                    # 过滤掉最晚时刻小于当前时刻的班次
                    if ($lineClassTrain['start_date'] == date('Y-m-d')
                    && strtotime(date('Y-m-d') .' '. $lineClassTrain['end_latest_time']) < time()) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                } else {
                    if(\getTimeNumber($lineClassTrain['start_time']) < $timeNumber) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            } elseif($dayNum == 1) {
                if($stop_sell_time < $timeNumber && $lineClassTrain['stop_sell_number'] == 1 && $stop_sell_time != 0) {
                    if(strtotime($day_time) == strtotime(date('Y-m-d', time()))) {
                        $lineClassTrainsArr[$k]['is_expire'] = 1;
                    }
                }
            }

            $lineClassTrainsArr[$k]['time_region'] = [];
            if($lineClassTrainsArr[$k]['start_time_type']  == 2) {
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time'] ? substr($lineClassTrainsArr[$k]['start_earliest_time'], 0, 5) : "";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time'] ? substr($lineClassTrainsArr[$k]['end_latest_time'], 0, 5) : "";
            } else {
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['starting_time']
                                            ? substr($lineClassTrainsArr[$k]['starting_time'], 0, 5)
                                            : substr($lineClassTrainsArr[$k]['start_time'], 0, 5);
            }

            if($lineClassTrain['start_address_type'] == 1) {
                $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
                $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_line_class_point')
                    ->where($startPointWhere)
                    ->field($startPointField)
                    ->order($startPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['start_point'] = [];
            }

            if($lineClassTrain['end_address_type'] == 1) {
                $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
                $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_line_class_point')
                    ->where($endPointWhere)
                    ->field($endPointField)
                    ->order($endPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['end_point'] = [];
            }

            //含返程
            if($lineClassTrain['is_return'] == 1) {
                $lineClassTrainsArr[$k]['return_time_region'] = [];
                if($lineClassTrainsArr[$k]['return_start_time_type']  == 2) {
                    $lineClassTrainsArr[$k]['return_time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_date'], $lineClassTrainsArr[$k]['return_start_earliest_time'], $lineClassTrainsArr[$k]['return_end_latest_time']);
                    $lineClassTrainsArr[$k]['return_start_earliest_time'] = $lineClassTrainsArr[$k]['return_start_earliest_time'] ? substr($lineClassTrainsArr[$k]['return_start_earliest_time'], 0, 5) : "";
                    $lineClassTrainsArr[$k]['return_end_latest_time'] = $lineClassTrainsArr[$k]['return_end_latest_time'] ? substr($lineClassTrainsArr[$k]['return_end_latest_time'], 0, 5) : "";
                } else {
                    $lineClassTrainsArr[$k]['return_start_time'] = $lineClassTrainsArr[$k]['return_start_time'] ? substr($lineClassTrainsArr[$k]['return_start_time'], 0, 5) : "";
                }

                $lineClassTrainsArr[$k]['return_start_date'] = $lineClassTrainsArr[$k]['start_date'];
                if($lineClassTrainsArr[$k]['return_time_number']) {
                    $lineClassTrainsArr[$k]['return_start_date'] = date('Y-m-d', strtotime($lineClassTrainsArr[$k]['start_date']." +".$lineClassTrainsArr[$k]['return_time_number']."day"));
                }

                if($lineClassTrain['return_start_address_type'] == 1) {
                    $returnStartPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_start_point'] = M()->table('cp_line_class_point')
                        ->where($returnStartPointWhere)
                        ->field($returnStartPointField)
                        ->order($returnStartPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_start_point'] = [];
                }

                if($lineClassTrain['return_end_address_type'] == 1) {
                    $returnEndPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_end_point'] = M()->table('cp_line_class_point')
                        ->where($returnEndPointWhere)
                        ->field($returnEndPointField)
                        ->order($returnEndPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_end_point'] = [];
                }
            }
            $lineClassTrainsArr[$k]['seat_price'] = json_decode($lineClassTrainsArr[$k]['seat_price'], true);
            $lineClassTrainsArr[$k]['min_seat_price'] = $this->getMinSeatPrice($lineClassTrainsArr[$k]['seat_price']);
        }

        $ret = new \ResultModel(false, '暂无数据');
        if(!empty($lineClassTrainsArr)) {
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr;
            $ret->count = $lineClassTrainsCount;
        }

        return $this->output($ret);
    }

    protected function queryParentAddressCode($code)
    {
        $adcodeR = $this->find(['address_id' => $code], 'GdRegion');
        if($adcodeR->ret) {
            return $adcodeR->data['address_pid'];
        }
    }

    /**
     *  渠道分台-获取某天的班线车
     * @param int $start_address_code 出发地地址编码例如：510107
     * @param int $end_address_code 目的地地址编码例如：511102
     * @param int $day_time 发车时间
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为10）
     */
    public function getChannelLineClassTrains($start_address_code = null, $end_address_code = null, $day_time = null, $page = 1, $size = 1000)
    {
        $start_time = "";
        $end_time = "";
        $currentTime = time();
        $dateClassify = new \DateClassify(date('Y-m-d H:i:s', $currentTime));
        $timeNumber = $dateClassify->classifyTimeNumber();
        $dayNum = \diffBetweenTwoDays(date('Y-m-d', $currentTime), $day_time);

        if(empty($day_time)) {
            $start_time = date('Y-m-d 00:00:00', $currentTime);
            $end_time = date('Y-m-d 23:59:59', $currentTime);
        } else {
            $start_time = date('Y-m-d 00:00:00', strtotime($day_time));
            $end_time = date('Y-m-d 23:59:59', strtotime($day_time));
        }

        $where = "lct.mchid = ".$this->mchid;
        $where .= " AND lct.start_date >= '".$start_time."'";
        $where .= " AND lct.start_date <= '".$end_time."'";
        $where .= " AND lct.status = '".\CommonDefine::LINE_CLASS_OPERATE_1."'";

        if(!empty($start_address_code) && $start_address_code != -1) {
            $where .= (" AND lc.start_city_code = ".$start_address_code);
        }
        if(!empty($end_address_code)  && $end_address_code != -1) {
            $where .= (" AND lc.end_city_code = ". $end_address_code);
        }

        $where .= " AND lct.is_del =".\CommonDefine::IS_DEL_0 ;
        $where .= " AND lct.remain_tickets > 0 ";
        //        $where .= " AND lc.GREATEST(lc.start_time,2,3,4) =".\CommonDefine::IS_DEL_0 ;

        if($dayNum == 0) {
            $where .= " AND time_to_sec(lc.stop_sell_time) >'".$timeNumber."'  AND lc.stop_sell_number = 0 " ;
        } elseif($dayNum == 1) {
            $where .= " AND ((time_to_sec(lc.stop_sell_time) >'".$timeNumber."' AND lc.stop_sell_number = 1) OR (lc.stop_sell_number = 0)) ";
        }

        if(strtotime($day_time) == strtotime(date('Y-m-d', time()))) {
            $where .= " AND ((time_to_sec(lc.start_time) >'".$timeNumber."' AND lc.start_time_type = 1) OR (time_to_sec(lc.start_earliest_time) >'".$timeNumber."' AND lc.start_time_type = 2)) ";
        }

        $order = "lc.sort asc";
        $field = "lct.line_class_id,
                lc.start_time_type,lc.start_earliest_time,lc.end_latest_time,lc.start_time,
                lc.refund_time_set,
                lc.start_city_code,gdrs.name as start_city_name,
                lc.end_city_code,gdre.name as end_city_name,
                lc.start_address_type,
                lc.end_address_type,
                lc.is_return,
                lc.center_start_latlng,lc.center_end_latlng,
                lc.return_start_time_type,lc.return_time_number,lc.return_start_time,lc.return_start_earliest_time,lc.return_end_latest_time,
                lc.return_start_address_type,
                lc.return_start_alias,
                lc.return_end_address_type,
                lc.return_end_alias,
                lct.line_class_train_id,
                lct.line_class_train_no,
                lct.remain_tickets,
                lct.price,
                lc.start_name,
                lc.end_name,
                lct.summary,
                lct.travel_time,
                lct.start_date";

        $lineClassTrainsArr = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->order($order)
            ->select();

        //总数量
        $lineClassTrainsCount = M()->table('cp_line_class_train lct')
            ->join('LEFT JOIN cp_line_class lc ON lc.id=lct.line_class_id')
            ->join('LEFT JOIN cp_gd_region gdrs ON gdrs.address_id = lc.start_city_code')
            ->join('LEFT JOIN cp_gd_region gdre ON gdre.address_id = lc.end_city_code')
            ->where($where)
            ->field($field)
            ->order($order)
            ->count();


        //去程
        //固定上车点
        $startPointField = 'alias,longitude,latitude,use_time';
        $startPointOrder = 'id asc';
        //固定下车点
        $endPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $endPointOrder = 'id asc';
        //返程
        //固定上车点
        $returnStartPointField = 'alias,longitude,latitude,use_time';
        $returnStartPointOrder = 'id asc';
        //固定下车点
        $returnEndPointField = 'line_class_id,alias,longitude,latitude,use_time';
        $returnEndPointOrder = 'id asc';
        foreach($lineClassTrainsArr as $k => $lineClassTrain) {
            $lineClassTrainsArr[$k]['time_region'] = [];
            if($lineClassTrainsArr[$k]['start_time_type']  == 2) {
                $lineClassTrainsArr[$k]['time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['start_earliest_time'], $lineClassTrainsArr[$k]['end_latest_time']);
                $lineClassTrainsArr[$k]['start_earliest_time'] = $lineClassTrainsArr[$k]['start_earliest_time'] ? substr($lineClassTrainsArr[$k]['start_earliest_time'], 0, 5) : "";
                $lineClassTrainsArr[$k]['end_latest_time'] = $lineClassTrainsArr[$k]['end_latest_time'] ? substr($lineClassTrainsArr[$k]['end_latest_time'], 0, 5) : "";
            } else {
                $lineClassTrainsArr[$k]['start_time'] = $lineClassTrainsArr[$k]['start_time'] ? substr($lineClassTrainsArr[$k]['start_time'], 0, 5) : "";
            }

            if($lineClassTrain['start_address_type'] == 1) {
                $startPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 0';
                $lineClassTrainsArr[$k]['start_point'] = M()->table('cp_line_class_point')
                    ->where($startPointWhere)
                    ->field($startPointField)
                    ->order($startPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['start_point'] = [];
            }

            if($lineClassTrain['end_address_type'] == 1) {
                $endPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 0';
                $lineClassTrainsArr[$k]['end_point'] = M()->table('cp_line_class_point')
                    ->where($endPointWhere)
                    ->field($endPointField)
                    ->order($endPointOrder)
                    ->select();
            } else {
                $lineClassTrainsArr[$k]['end_point'] = [];
            }

            //含返程
            if($lineClassTrain['is_return'] == 1) {
                $lineClassTrainsArr[$k]['return_time_region'] = [];
                if($lineClassTrainsArr[$k]['return_start_time_type']  == 2) {
                    $lineClassTrainsArr[$k]['return_time_region'] = \getTimeRegion($lineClassTrainsArr[$k]['return_start_earliest_time'], $lineClassTrainsArr[$k]['return_end_latest_time']);
                    $lineClassTrainsArr[$k]['return_start_earliest_time'] = $lineClassTrainsArr[$k]['return_start_earliest_time'] ? substr($lineClassTrainsArr[$k]['return_start_earliest_time'], 0, 5) : "";
                    $lineClassTrainsArr[$k]['return_end_latest_time'] = $lineClassTrainsArr[$k]['return_end_latest_time'] ? substr($lineClassTrainsArr[$k]['return_end_latest_time'], 0, 5) : "";
                } else {
                    $lineClassTrainsArr[$k]['return_start_time'] = $lineClassTrainsArr[$k]['return_start_time'] ? substr($lineClassTrainsArr[$k]['return_start_time'], 0, 5) : "";
                }

                $lineClassTrainsArr[$k]['return_start_date'] = $lineClassTrainsArr[$k]['start_date'];
                if($lineClassTrainsArr[$k]['return_time_number']) {
                    $lineClassTrainsArr[$k]['return_start_date'] = date('Y-m-d', strtotime($lineClassTrainsArr[$k]['start_date']." +".$lineClassTrainsArr[$k]['return_time_number']."day"));
                }

                if($lineClassTrain['return_start_address_type'] == 1) {
                    $returnStartPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 1 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_start_point'] = M()->table('cp_line_class_point')
                        ->where($returnStartPointWhere)
                        ->field($returnStartPointField)
                        ->order($returnStartPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_start_point'] = [];
                }

                if($lineClassTrain['return_end_address_type'] == 1) {
                    $returnEndPointWhere = 'line_class_id='.$lineClassTrain['line_class_id']. ' AND type = 2 AND is_return = 1';
                    $lineClassTrainsArr[$k]['return_end_point'] = M()->table('cp_line_class_point')
                        ->where($returnEndPointWhere)
                        ->field($returnEndPointField)
                        ->order($returnEndPointOrder)
                        ->select();
                } else {
                    $lineClassTrainsArr[$k]['return_end_point'] = [];
                }
            }
        }

        $ret = new \ResultModel(false, '暂无数据');
        if(!empty($lineClassTrainsArr)) {
            $ret->ret = true;
            $ret->data = $lineClassTrainsArr;
            $ret->count = $lineClassTrainsCount;
        }

        return $this->output($ret);
    }
}

<?php

namespace Branch;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-线路模块
 *
 * <AUTHOR>
 */
class YyBranchAction extends \BranchCoreAction
{
    /**
     * 获取分台司机列表
     * @param int $page 第几页（默认为1)
     * @param int $student_customized_line_id 定制组团线路id
     * @param int $size 每页几条（默认为10）
     * @param string $get_driver_by_timestamp 当前时间戳，默认为空
     * @param string $dcellphone 司机电话（条件搜索，默认为空）
     * @param string $dname 司机姓名（条件搜索，默认为空）
     */
    public function doGetDrivers($student_customized_line_id, $dcellphone = null, $dname = null,  $get_driver_by_timestamp = null, $page = 1, $size = 10){
        $branchid = $this->state->user_id;
        $studentCustomizedLineR = $this->find(array('student_customized_line_id' => $student_customized_line_id), 'YyStudentCustomizedLine');
        if(!$studentCustomizedLineR->ret){
            return $this->output(new \ResultModel(false, '订单异常', null, $get_driver_by_timestamp));
        }

        $where['d.branchid'] = $branchid;
        $where['d.state'] = array('eq', 0);
        $where['d.is_freeze'] = array('eq', \CommonDefine::IS_FREEZE_0);
//        $where['d.from_type'] = array('eq', \CommonDefine::FROM_TYPE_0);
        $where['d.driver_role_type'] = array('eq', \CommonDefine::DRIVER_ROLE_0);
        if($dname){
            $where['d.name'] = array('like', '%'.$dname.'%');
        }
        if($dcellphone){
            $where['d.cellphone'] = array('eq', $dcellphone);
        }

        $order = "d.driver_id ASC";//默认
        $driverList = M()->table("cp_driver d")
            ->field('d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
            ->where($where)->order($order)->page($page,$size)->select();
        $driverCount = M()->table("cp_driver d")
            ->field('d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
            ->where($where)->count();

        if(!empty($driverList)){
            return $this->output(new \ResultModel(true, $driverList, $driverCount, $get_driver_by_timestamp));
        }else{
            return $this->output(new \ResultModel(false, null, null, $get_driver_by_timestamp));
        }
    }
}

?>

<?php

namespace Branch;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 云裕众-线路模块
 *
 * <AUTHOR>
 */
class YyLineAction extends \BranchCoreAction
{
    /**
     * 学生号-获取待指派组团线路列表
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为100）
     */
    public function getStudentCustomizedLines($page = 1, $size = 100)
    {
        $where['mchid'] = $this->mchid;
        $where['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3;
        $where['appoint'] = \CommonDefine::APPOINT_TYPE_0;
        $where['branchid'] = $this->state->user_id;
        $studentCutomizedLines = $this->select($where, $page, $size, 'start_time asc', 'YyStudentCustomizedLine');
        return $this->output($studentCutomizedLines);
    }

    /**
     * 学生号-指派组团线路
     * @param int $student_customized_line_id 线路id
     * @param int $driver_id 司机id
     */
    public function doAppointStudentCustomizedLine($student_customized_line_id, $driver_id){
        //司机是否正常接单中
        $driverR = $this->find(array('driver_id' => $driver_id, 'mchid' => $this->mchid), 'Driver');
        if($driverR->ret){
            if($driverR->data['state'] == 3){
                return $this->output(new \ResultModel(false, '司机暂停接单'));
            }
        }else{
            return $this->output(new \ResultModel(false, '司机不存在'));
        }

        $lineWhere['student_customized_line_id'] = $student_customized_line_id;
        $lineWhere['status'] = \YyCommonDefine::YY_STUDENT_CUSTOMIZED_LINE_STATUS_3;
        $lineWhere['appoint'] = \CommonDefine::APPOINT_TYPE_0;
        $lineWhere['mchid'] = $this->mchid;
        $lineWhere['branchid'] = $this->state->user_id;
        $studentCustomizedLineR = $this->find($lineWhere, 'YyStudentCustomizedLine');
        if(!$studentCustomizedLineR->ret){
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        //指派完成
        $updateStudentCustomizedLineData['student_customized_line_id'] = $student_customized_line_id;
        $updateStudentCustomizedLineData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $updateStudentCustomizedLineData['driver_id'] = $driver_id;
        $studentCustomizedLineS = $this->save('YyStudentCustomizedLine',$updateStudentCustomizedLineData);
        if($studentCustomizedLineS->ret){
            //通知司机
            $this->yySendSms($student_customized_line_id, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
            $this->yySendWxMessage($student_customized_line_id, \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
        }

        $this->doAddLog('指派组团订单',json_encode($studentCustomizedLineR->data), \StateModel::$BRANCH_USER);
        return $this->output($studentCustomizedLineS);
    }
}

?>

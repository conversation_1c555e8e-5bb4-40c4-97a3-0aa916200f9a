<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Home/HomeCoreAction');

/**
 * 后台核心模块
 *
 * <AUTHOR>
 */
class BranchCoreAction extends HomeCoreAction
{
    /**
     * 管理组
     * @var string
     */
    protected $admin_group;

    //put your code here
    public function __construct()
    {
        add_tag_behavior('auth_check', 'BranchAuthCheck');
        parent::__construct();
        $this->assign('admin_state', object_to_array($this->state));
        $adminGroupObj = $this->_getCurrentGroupInfo($this->state->user_id);
        $this->admin_group = $adminGroupObj->data['code'];//$this->_getCurrentGroupCode($this->state->user_id)->data;
        $this->assign('admin_group', $this->admin_group);
        $url = C('WEB_ROOT');
        if($adminGroupObj->data['group_id'] == 2) {
            $this->assign('mchid', $this->state->user_id);
        }
        //        $this->assign('cellphone',18328414979);
        if($this->state->user_id) {
            $this->assign('scellphone', $this->getBranchTelByBranchId());
        } else {
            $this->assign('scellphone', null);
        }
        //过期时间限制
        if($this->checkUserTimeLimit()) {
            //            echo "<script type='text/javascript'>alert('账户已到期，请及时续费，联系电话：028-61112106');</script>";return;
        }

        $this->assign('qrurl', $this->getqrcodeurl());
        $this->assign('get', $_GET);
    }

    public function getqrcodeurl()
    {
        if (empty($this->state)) {
            return '';
        } else {
            $wx = M('wxuser_setup')->where('uid=' . $this->state->user_id)->field('qrcode_url')->find();
            return $wx['qrcode_url'];
        }
    }

    /**
 * 获取当前组code
 * @param int $user_id
 */
    private function _getCurrentGroupCode($user_id)
    {
        $am = new AdminModel();
        $data = $am->where(array('admin_id' => $user_id))->field('group_id')->find();
        if ($data) {
            $magm = new MetaAdminGroupModel();
            $data = $magm->where(array('group_id' => $data['group_id']))->field('code')->find();
            if ($data) {
                $r = new ResultModel(true, $data['code']);
            } else {
                $r = new ResultModel(false, '组ID不存在');
            }
        } else {
            $r = new ResultModel(false, '用户ID不存在');
        }
        return $r;
    }

    /**
     * 获取当前组信息
     * @param int $user_id
     */
    private function _getCurrentGroupInfo($user_id)
    {
        $am = new AdminModel();
        $data = $am->where(array('admin_id' => $user_id))->field('group_id')->find();
        if ($data) {
            $magm = new MetaAdminGroupModel();
            $groupId = $data['group_id'];
            $data = $magm->where(array('group_id' => $data['group_id']))->field('code')->find();
            if ($data) {
                $r = new ResultModel(true, array('group_id' => $groupId, 'code' => $data['code']));
            } else {
                $r = new ResultModel(false, '组ID不存在');
            }
        } else {
            $r = new ResultModel(false, '用户ID不存在');
        }
        return $r;
    }

    /**
     * 分配车主提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $driver_id 车主ID
     * @param string $action_code 操作标码
     * @param  int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignDriverRemindTask($assigner_id, $driver_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaDriverRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'driver_id' => $driver_id), 'DriverRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('DriverRemindTask', array('assigner_id' => $assigner_id, 'driver_id' => $driver_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该车主没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '车主提醒操作标码不存在');
        }
        return $this->result;
    }

    /**
     * 分配乘客提醒任务
     * @param int $assigner_id 分配人ID
     * @param int $passenger_id 乘客ID
     * @param string $action_code 操作标码
     * @param int $detail_id 详细ID
     * @param array $data 数据
     */
    public function assignPassengerRemindTask($assigner_id, $passenger_id, $action_code, $detail_id, $data = array())
    {
        //根据action_code查询action_id
        $r = $this->find(array('code' => $action_code), 'MetaPassengerRemindAction', 'action_id');
        if ($r->ret) {
            //检测提醒人的提醒设置，是否开启提醒
            $action_id = $r->data['action_id'];
            $r = $this->find(array('action_id' => $action_id, 'passenger_id' => $passenger_id), 'PassengerRemindSet', 'is_enable');
            if ($r->ret) {
                $is_enable = $r->data['is_enable'];
                if ($is_enable) {
                    //如果开启了提醒，则添加提醒任务
                    $this->add('PassengerRemindTask', array('assigner_id' => $assigner_id, 'passenger_id' => $passenger_id, 'action_id' => $action_id, 'detail_id' => $detail_id, 'data' => serialize($data)));
                }
            } else {
                $this->result = new ResultModel(false, '该乘客没有设置提醒');
            }
        } else {
            $this->result = new ResultModel(false, '乘客提醒操作标码不存在');
        }
        return $this->result;
    }

    /**
     *检验用户是否过期
     *返回一个布尔值
     */
    private function checkUserTimeLimit()
    {

        $adminGroupObj = $this->_getCurrentGroupInfo($this->state->user_id);
        if($adminGroupObj->data['group_id'] == 3) {
            $where['admin_id'] = $this->getParentId();
        } else {
            $where['admin_id'] = $this->state->user_id;
        }
        $adminR = $this->find($where, 'Admin', 'endtime');
        if($adminR->ret) {
            if(time() >= strtotime($adminR->data['endtime'])) {
                return true;
            }
        }
        return false;
    }

    private function getParentId()
    {
        $where['admin_id'] = $this->state->user_id;
        $adminR = $this->find($where, 'Admin', 'parent_admin_id');
        return $adminR->data['parent_admin_id'];
    }

    private function getBranchTelByBranchId()
    {
        $where['admin_id'] = $this->state->user_id;
        $adminR = $this->find($where, 'Admin', 'cellphone');
        return $adminR->data['cellphone'];
    }

    /**
     * 获取商户微信授权url，用于消息推送
     * @param $mchid
     * @param $branchid
     * @return ResultModel
     */
    protected function getMerchantWeChatUrlForAuth($mchid, $branchid, $branchtype)
    {
        $wxUrlR = new ResultModel(false);
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        if($branchR->ret) {
            $cipmchidR = $this->getCipmchidByMchid($mchid);
            if($cipmchidR->ret) {
                $we = new \Wechat();
                $url = C('WEB_ROOT') . 'branchcallback';
                $redirect_uri = urlencode($url);
                $state = $cipmchidR->data['ciphertext'].','.$branchid.','.$branchtype;
                $wxUrlR->ret = true;
                $wxUrlR->data['wx_url'] = $we->getAuthorizeUrl($redirect_uri, $state, $cipmchidR->data['ciphertext']);
                $wxUrlR->count = 1;
            }
        }
        return $wxUrlR;
    }

    /**
     * 获取平台微信授权url，用于微信支付
     * @param $branchid
     * @return ResultModel
     */
    protected function getPlatformWeChatUrlForAuth($cipmchid, $branchid, $branchtype)
    {
        $wxUrlR = new ResultModel(false);
        $we = new \Wechat();
        $url = C('WEB_ROOT') . 'branch_authpay';
        $redirect_uri = urlencode($url);
        $state = $cipmchid.','.$branchid.','.$branchtype;
        $wxUrlR->ret = true;
        $wxUrlR->data['wx_url'] = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $we->appIdForPay . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=snsapi_base&state=" . $state . "#wechat_redirect";
        $wxUrlR->count = 1;
        return $wxUrlR;
    }
}

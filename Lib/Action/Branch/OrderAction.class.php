<?php

namespace Branch;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');
import('@/Action/Weixin/WechatAction');
/**
 * 订单模块
 *
 * <AUTHOR>
 */
class OrderAction extends \BranchCoreAction
{
    /**
    * 获取待指派订单(手机端)
    * @param int $type 类型（1-乘客预定，2-历史记录,3-筛选历史订单，4-获取所有订单）
    * @param int $page 第几页（默认为1)
    * @param int $size 每页几条（默认为10）
    * @param int $start_address_remark 出发地（条件搜索，默认为空）
    * @param int $end_address_remark 目地址（条件搜索，默认为空）
    * @param int $pname 乘客昵称（条件搜索，默认为空）
    * @param int $pcellphone 乘客手机号（条件搜索，默认为空）
    * @param int $start_time 开始时间（条件搜索，默认为空）
    * @param int $end_time 结束时间（条件搜索，默认为空）
    */
    public function getMobilesOrders($type, $page = 1, $size = 20, $start_address_remark = null, $end_address_remark = null, $pname = null, $pcellphone = null, $start_time = null, $end_time = null)
    {
        $size = 100;
        $ret = new \ResultModel(false, '暂无数据');

        $branchid = $this->state->user_id;
        $currentDateTime = date("Y-m-d H:i:s", time());

        $where = '';
        $order = 'start_time asc,create_time desc';
        $type = intval($type);
        if ($type === 1) {
            $where = ' o.state IN (1) AND o.type IN (1,2,3,4,5,7) AND o.branchid='.$branchid.' AND o.appoint='.\CommonDefine::APPOINT_TYPE_0.' AND o.rob_order='.\CommonDefine::ROB_ORDER_0;
            $where .= ' AND ((o.pay_mode='.\CommonDefine::ORDER_PAYMENT_MODE_0.') OR (o.pay_mode = '.\CommonDefine::ORDER_PAYMENT_MODE_1 .' AND o.is_pay = '.\CommonDefine::PAY_STATUS_1.'))';
            $where .= ' AND ((o.channel_type='.\CommonDefine::CHANNEL_TYPE_0.') OR (o.channel_type = '.\CommonDefine::CHANNEL_TYPE_1 .' AND o.is_pay = '.\CommonDefine::PAY_STATUS_1.' AND o.is_temp = 1) OR (o.channel_type = '.\CommonDefine::CHANNEL_TYPE_1 .' AND o.is_temp = 0))';

            if (!empty($start_address_remark)) {
                $where .= " AND (o.start_address_remark like '%".$start_address_remark."%' OR l.start_name like '%".$start_address_remark."%' OR lc.start_name like '%".$start_address_remark."%' OR lcs.start_name like '%".$start_address_remark."%')";
            }

            if (!empty($end_address_remark)) {
                $where .= " AND (o.end_address_remark like '%".$end_address_remark."%' OR l.end_name like '%".$end_address_remark."%' OR lc.end_name like '%".$end_address_remark."%' OR lcs.end_name like '%".$end_address_remark."%')";
            }

            if (!empty($pname)) {
                $where .= " AND p.name like '%".$pname."%'";
            }

            if (!empty($pcellphone)) {
                $where .= " AND o.reseverd_phone like '%".$pcellphone."%'";
            }

            if (!empty($start_time)) {
                $where .= " AND o.start_time >= '".($start_time.":0'");
            }

            if (!empty($end_time)) {
                $where .= " AND o.start_time <= '".$end_time.":0'";
            }
        } else {
            return $this->output(new \ResultModel(false, '类型格式不正确'));
        }

        $branchR = $this->find(array('admin_id' => $branchid), 'Admin', 'mchname');
        $branchName = $branchR->ret ? $branchR->data['mchname'] : "";
        $fields = 'o.order_no, o.order_id, o.price,o.type,
                 o.line_id, o.start_time, o.book_seating,
                 o.passenger_id, o.create_time, o.is_pay,
                 o.is_pre_pay, o.is_temp, o.reseverd_phone,
                 o.reseverd_person, o.reseverd_info, o.start_address_remark,
                 o.end_address_remark, o.is_custom,o.is_face, o.car_type_id,
                 o.delivery_person, o.delivery_phone, o.weight, o.agency_id, o.ft_number';
        $fields .=  ",l.start_name,   l.end_name";
        $fields .=  ",lc.start_name,  lc.end_name";
        $fields .=  ",lcs.start_name, lcs.end_name";
        $orderArr = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_passenger p ON p.passenger_id = o.passenger_id')
            ->join(' LEFT JOIN cp_line l ON l.id = o.line_id')
            ->join(' LEFT JOIN cp_line_chartered lc ON lc.id = o.line_id')
            ->join(' LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = o.line_id')
            ->join(' LEFT JOIN cp_line_class lcs ON lct.line_class_id = lcs.id')
            ->page($page, $size)
            ->field($fields)
            ->where($where)
            ->order($order)
            ->select();

        $orderCount = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_passenger p ON p.passenger_id = o.passenger_id')
            ->join(' LEFT JOIN cp_line l ON l.id = o.line_id')
            ->join(' LEFT JOIN cp_line_chartered lc ON lc.id = o.line_id')
            ->join(' LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = o.line_id')
            ->join(' LEFT JOIN cp_line_class lcs ON lct.line_class_id = o.line_id')
            ->field($fields)
            ->where($where)
            ->count();

        if (!empty($orderArr)) {
            foreach ($orderArr as $key => $value) {
                $orderArr[$key]['time_leave'] = \getDifftime($orderArr[$key]['start_time'], $currentDateTime);

                if ($orderArr[$key]['type'] == 1) {
                    $line = $this->find(array('id' => $orderArr[$key]['line_id']), 'Line', 'id,start_name,end_name');
                    $orderArr[$key]['start_name'] = $line->data['start_name'];
                    $orderArr[$key]['end_name'] = $line->data['end_name'];
                    $orderArr[$key]['start_address_remark'] = "【".$line->data['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                    $orderArr[$key]['end_address_remark'] = "【".$line->data['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");

                } elseif ($orderArr[$key]['type'] == 2) {
                    if ($orderArr[$key]['is_custom'] == 0) {
                        $lineChartered = $this->find(array('id' => $orderArr[$key]['line_id']), 'LineChartered', 'id,start_name,end_name');

                        $orderArr[$key]['start_name'] = $lineChartered->data['start_name'];
                        $orderArr[$key]['end_name'] = $lineChartered->data['end_name'];
                        $orderArr[$key]['start_address_remark'] = "【".$lineChartered->data['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                        $orderArr[$key]['end_address_remark'] = "【".$lineChartered->data['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $orderArr[$key]['start_name'] = $lineR->data['start_name'];
                        $orderArr[$key]['end_name'] = $lineR->data['end_name'];
                        $orderArr[$key]['start_address_remark'] = "【".$lineR->data['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                        $orderArr[$key]['end_address_remark'] = "【".$lineR->data['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $orderArr[$key]['name'] = $agencyR->data['name'];
                        $orderArr[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    //                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']),'LineClassTrain');
                    //                    if($lineClassTrainR->ret){
                    //                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    //                        if($lineClassR->ret){
                    //                            $orderArr[$key]['start_name'] = $lineClassR->data['start_name'];
                    //                            $orderArr[$key]['end_name'] = $lineClassR->data['end_name'];
                    //                            $orderArr[$key]['start_address_remark'] = "【".$lineClassR->data['start_name']."】". ($orderArr[$key]['start_address_remark']?$orderArr[$key]['start_address_remark']:"默认");
                    //                            $orderArr[$key]['end_address_remark'] = "【".$lineClassR->data['end_name']."】".($orderArr[$key]['end_address_remark']?$orderArr[$key]['end_address_remark']:"默认");
                    //                        }
                    //                    }
                    $orderArr[$key]['start_address_remark'] = "【".$orderArr[$key]['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                    $orderArr[$key]['end_address_remark'] = "【".$orderArr[$key]['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                    if (empty($value['line_id'])) {//乘客发布的
                        $orderArr[$key]['start_name'] = $orderArr[$key]['start_region_name'];
                        $orderArr[$key]['end_name'] = $orderArr[$key]['end_region_name'];
                        unset($orderArr[$key]['start_region_name']);
                        unset($orderArr[$key]['end_region_name']);
                    } else {
                        $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                        if ($LineFreeRideR->ret) {
                            $orderArr[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                            $orderArr[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                        }
                    }
                } elseif ($orderArr[$key]['type'] == \CommonDefine::ORDER_TYPE_7) {
                    $LineFastR = $this->find(array('id' => $orderArr[$key]['line_id']), 'LineFast', 'id,start_name,end_name');
                    $orderArr[$key]['start_name'] = $LineFastR->data['start_name'];
                    $orderArr[$key]['end_name'] = $LineFastR->data['end_name'];
                    $orderArr[$key]['start_address_remark'] = "【".$LineFastR->data['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                    $orderArr[$key]['end_address_remark'] = "【".$LineFastR->data['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");
                } elseif ($orderArr[$key]['type'] == \CommonDefine::ORDER_TYPE_11) {
                    $LineTaxiR = $this->find(array('id' => $orderArr[$key]['line_id']), 'LineTaxi', 'id,start_name,end_name');
                    $orderArr[$key]['start_name'] = $LineTaxiR->data['start_name'];
                    $orderArr[$key]['end_name'] = $LineTaxiR->data['end_name'];
                    $orderArr[$key]['start_address_remark'] = "【".$LineTaxiR->data['start_name']."】". ($orderArr[$key]['start_address_remark'] ? $orderArr[$key]['start_address_remark'] : "默认");
                    $orderArr[$key]['end_address_remark'] = "【".$LineTaxiR->data['end_name']."】".($orderArr[$key]['end_address_remark'] ? $orderArr[$key]['end_address_remark'] : "默认");
                }
                unset($orderArr[$key]['is_custom']);
                if (is_null($orderArr[$key]['reseverd_phone'])) {
                    $passengerR = $this->find(array('passenger_id' => $orderArr[$key]['passenger_id']), 'Passenger', 'passenger_id, longitude, latitude,cellphone,name');
                    $orderArr[$key]['passenger_cellphone'] = $passengerR->ret ? $passengerR->data['cellphone'] : "";
                    /*$orderArr[$key]['passenger_name'] = $passengerR->data['name']?$passengerR->data['name']:"";*///特殊符号bug
                } else {
                    $orderArr[$key]['passenger_cellphone'] = $orderArr[$key]['reseverd_phone'];
                }
                $orderArr[$key]['branch_name'] = $branchName;
            }

            $ret->ret = true;
            $ret->data = $orderArr;
            $ret->count = $orderCount;
        } else {
            return $this->output(new \ResultModel(false, '未查找到相关信息'));
        }

        return $this->output($ret);
    }

    /**
     * 获取已指派订单
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param int $start_address_remark 出发地（条件搜索，默认为空）
     * @param int $end_address_remark 目地址（条件搜索，默认为空）
     * @param int $pname 乘客昵称（条件搜索，默认为空）
     * @param int $pcellphone 乘客手机号（条件搜索，默认为空）
     * @param int $dname 司机姓名（条件搜索，默认为空）
     * @param int $dcellphone 司机电话（条件搜索，默认为空）
     * @param int $start_time 开始时间（条件搜索，默认为空）
     * @param int $end_time 结束时间（条件搜索，默认为空）
     */
    public function getMobilesOverAppointOrders($page = 1, $size = 10, $start_address_remark = null, $end_address_remark = null, $pname = null, $pcellphone = null, $dname = null, $dcellphone = null, $start_time = null, $end_time = null)
    {
        $ret = new \ResultModel(false, '未查找到相关信息');
        //        $where['is_temp'] = 1;
        $order = 'start_time asc,create_time desc';
        //$where = ' o.state in (1,2) ';
        $where = ' o.state in (1,2,3,4) ';//状态（1-乘客预定，2-车主接单，3-车主确认乘客上车，4-在路上，5-已送达(未支付)，|6-正常完成，7-取消，8-已关闭
        $where .= ' AND o.type in (1,2,3,4,5,7) ';
        $where .= ' AND o.branchid = '. $this->state->user_id;
        $where .= ' AND o.appoint = '. \CommonDefine::APPOINT_TYPE_1;
        $where .= ' AND o.rob_order = '. \CommonDefine::ROB_ORDER_0;

        if (!empty($start_address_remark)) {
            $where .= " AND (o.start_address_remark like '%".$start_address_remark."%' OR l.start_name like '%".$start_address_remark."%' OR lc.start_name like '%".$start_address_remark."%' OR lcs.start_name like '%".$start_address_remark."%')";
        }

        if (!empty($end_address_remark)) {
            $where .= " AND (o.end_address_remark like '%".$end_address_remark."%' OR l.end_name like '%".$end_address_remark."%' OR lc.end_name like '%".$end_address_remark."%' OR lcs.end_name like '%".$end_address_remark."%')";
        }

        if (!empty($pname)) {
            $where .= " AND p.name like '%".$pname."%'";
        }

        if (!empty($pcellphone)) {
            $where .= " AND o.reseverd_phone like '%".$pcellphone."%'";
        }

        if (!empty($start_time)) {
            $where .= " AND o.start_time >= '".($start_time.":0'");
        }

        if (!empty($end_time)) {
            $where .= " AND o.start_time <= '".$end_time.":0'";
        }

        if (!empty($dname)) {
            $where .= " AND d.name like '%".$dname."%'";
        }

        if (!empty($dcellphone)) {
            $where .= " AND d.cellphone = ".$dcellphone;
        }

        $branchR = $this->find(array('admin_id' =>  $this->state->user_id), 'Admin', 'mchname');
        $branchName = $branchR->ret ? $branchR->data['mchname'] : "";

        $field =  'o.order_no, o.order_id, o.price, o.type, o.line_id, o.start_time,o.book_seating,
                 o.passenger_id,o.create_time,o.is_pay,o.is_pre_pay,o.is_temp,o.reseverd_phone,
                 o.reseverd_person,o.reseverd_info,o.start_address_remark,o.end_address_remark,
                 o.is_custom,o.is_face,o.car_type_id,o.delivery_person,o.delivery_phone,o.weight,
                 o.agency_id,o.ft_number,d.cellphone as driver_cellphone,d.name as driver_name,d.car_tail_number';
        $field .=  ",l.start_name,   l.end_name";
        $field .=  ",lc.start_name,  lc.end_name";
        $field .=  ",lcs.start_name, lcs.end_name";
        $orderArr = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_driver d ON d.driver_id = o.driver_id')
            ->join(' LEFT JOIN cp_passenger p ON p.passenger_id = o.passenger_id')
            ->join(' LEFT JOIN cp_line l ON l.id = o.line_id')
            ->join(' LEFT JOIN cp_line_chartered lc ON lc.id = o.line_id')
            ->join(' LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = o.line_id')
            ->join(' LEFT JOIN cp_line_class lcs ON lct.line_class_id = lcs.id')
            ->page($page, $size)
            ->where($where)
            ->field($field)
            ->order($order)
            ->select();
        $orderCount = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_driver d ON d.driver_id = o.driver_id')
            ->join(' LEFT JOIN cp_line l ON l.id = o.line_id')
            ->join(' LEFT JOIN cp_line_chartered lc ON lc.id = o.line_id')
            ->join(' LEFT JOIN cp_line_class_train lct ON lct.line_class_train_id = o.line_id')
            ->join(' LEFT JOIN cp_line_class lcs ON lct.line_class_id = lcs.id')
            ->where($where)
            ->field($field)
            ->count();

        if (!empty($orderArr)) {
            foreach ($orderArr as $k => $v) {
                if ($orderArr[$k]['type'] == \CommonDefine::ORDER_TYPE_1) {
                    $line = $this->find(array('id' => $orderArr[$k]['line_id']), 'Line', 'id,start_name,end_name');
                    $orderArr[$k]['start_name'] = $line->data['start_name'];
                    $orderArr[$k]['end_name'] = $line->data['end_name'];
                    $orderArr[$k]['start_address_remark'] = "【".$line->data['start_name']."】". ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                    $orderArr[$k]['end_address_remark'] = "【".$line->data['end_name']."】".($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                } elseif ($orderArr[$k]['type'] == \CommonDefine::ORDER_TYPE_2) {
                    if ($orderArr[$k]['is_custom'] == 0) {
                        $lineChartered = $this->find(array('id' => $orderArr[$k]['line_id']), 'LineChartered', 'id,start_name,end_name');

                        $orderArr[$k]['start_name'] = $lineChartered->data['start_name'];
                        $orderArr[$k]['end_name'] = $lineChartered->data['end_name'];
                        $orderArr[$k]['start_address_remark'] = "【".$lineChartered->data['start_name']."】". ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                        $orderArr[$k]['end_address_remark'] = "【".$lineChartered->data['end_name']."】".($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                    }
                } elseif ($v['type'] == \CommonDefine::ORDER_TYPE_3) {
                    $lineR = $this->find(array('id' => $v['line_id']), 'Line');
                    if ($lineR->ret) {
                        $orderArr[$k]['start_name'] = $lineR->data['start_name'];
                        $orderArr[$k]['end_name'] = $lineR->data['end_name'];
                        $orderArr[$k]['start_address_remark'] = "【".$lineR->data['start_name']."】". ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                        $orderArr[$k]['end_address_remark'] = "【".$lineR->data['end_name']."】".($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                    }
                } elseif ($v['type'] == \CommonDefine::ORDER_TYPE_4) {
                    $agencyR = $this->find(array('agency_id' => $v['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $orderArr[$k]['name'] = $agencyR->data['name'];
                        $orderArr[$k]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($v['type'] == \CommonDefine::ORDER_TYPE_5) {
                    //                    $lineClassTrainR = $this->find(array('line_class_train_id' => $v['line_id']),'LineClassTrain');
                    //                    if($lineClassTrainR->ret){
                    //                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    //                        if($lineClassR->ret){
                    //                            $orderArr[$k]['start_name'] = $lineClassR->data['start_name'];
                    //                            $orderArr[$k]['end_name'] = $lineClassR->data['end_name'];
                    //                            $orderArr[$k]['start_address_remark'] = "【".$lineClassR->data['start_name']."】". ($orderArr[$k]['start_address_remark']?$orderArr[$k]['start_address_remark']:"默认");
                    //                            $orderArr[$k]['end_address_remark'] = "【".$lineClassR->data['end_name']."】".($orderArr[$k]['end_address_remark']?$orderArr[$k]['end_address_remark']:"默认");
                    //                        }
                    //                    }
                    $orderArr[$k]['start_address_remark'] = "【".$orderArr[$k]['start_name']."】". ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                    $orderArr[$k]['end_address_remark'] = "【".$orderArr[$k]['end_name']."】".($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                } elseif ($orderArr[$k]['type'] == \CommonDefine::ORDER_TYPE_7) {
                    $lineFastR = $this->find(array('id' => $orderArr[$k]['line_id']), 'LineFast', 'id,start_name,end_name');
                    $orderArr[$k]['start_name'] = $lineFastR->data['start_name'];
                    $orderArr[$k]['end_name'] = $lineFastR->data['end_name'];
                    $orderArr[$k]['start_address_remark'] = "【" . $lineFastR->data['start_name'] . "】" . ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                    $orderArr[$k]['end_address_remark'] = "【" . $lineFastR->data['end_name'] . "】" . ($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                } elseif ($orderArr[$k]['type'] == \CommonDefine::ORDER_TYPE_11) {
                    $lineTaxiR = $this->find(array('id' => $orderArr[$k]['line_id']), 'LineTaxi', 'id,start_name,end_name');
                    $orderArr[$k]['start_name'] = $lineTaxiR->data['start_name'];
                    $orderArr[$k]['end_name'] = $lineTaxiR->data['end_name'];
                    $orderArr[$k]['start_address_remark'] = "【" . $lineTaxiR->data['start_name'] . "】" . ($orderArr[$k]['start_address_remark'] ? $orderArr[$k]['start_address_remark'] : "默认");
                    $orderArr[$k]['end_address_remark'] = "【" . $lineTaxiR->data['end_name'] . "】" . ($orderArr[$k]['end_address_remark'] ? $orderArr[$k]['end_address_remark'] : "默认");
                }

                unset($orderArr[$k]['is_custom']);
                if (is_null($orderArr[$k]['reseverd_phone'])) {
                    $passengerR = $this->find(array('passenger_id' => $orderArr[$k]['passenger_id']), 'Passenger', 'passenger_id, longitude, latitude,cellphone,name');
                    $orderArr[$k]['passenger_cellphone'] = $passengerR->ret ? $passengerR->data['cellphone'] : "";
                    /*$orderArr[$key]['passenger_name'] = $passengerR->data['name']?$passengerR->data['name']:"";*///特殊符号bug
                } else {
                    $orderArr[$k]['passenger_cellphone'] = $orderArr[$k]['reseverd_phone'];
                }
                $orderArr[$k]['branch_name'] = $branchName;
            }
            $ret->ret = true;
            $ret->data = $orderArr;
            $ret->count = $orderCount;
        } else {
            return $this->output($ret);
        }

        return $this->output($ret);
    }


    /**
     * 获取历史订单
     * @param int $type 类型（1-乘客预定，2-历史记录,3-筛选历史订单，4-获取所有订单）
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     */
    public function getBranchOrderHistory($stime = null, $etime = null, $page = 1, $size = 10)
    {
        $branchId = $this->state->user_id;
        $adminR = $this->find(array('admin_id' => $branchId,'is_del' => 0,'is_freeze' => 0,'group_id' => 3), 'Admin', 'admin_id,openidforpay');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, '数据异常'));
        }

        $where['state'] = array('in', '1,2,3,4,5,6,7,8');
        $where['type'] = array('in', '1,2,3,4,7');


        $where['branchid'] = $adminR->data['admin_id'];
        //        $where['is_temp'] = 1;
        $where['appoint'] = 1;
        $where['rob_order'] = \CommonDefine::ROB_ORDER_0;

        if ($stime) {
            $where['create_time'] = array('egt', $stime);
        }
        if ($etime) {
            $where['create_time'] = array('elt', $etime);
        }
        $r = $this->select($where, $page, $size, "create_time DESC", 'Order', 'agency_id,order_no,reseverd_person,order_id,reseverd_phone,reseverd_person, price,type, line_id,start_time,book_seating,passenger_id,driver_id,create_time,is_pay,is_pre_pay,is_temp,reseverd_info,start_address_remark,end_address_remark,delivery_person,delivery_phone,is_custom,is_face,car_type_id,state,start_longitude,start_latitude,end_longitude,end_latitude,ft_number');
        if (!$r->ret) {
            return $this->output(new \ResultModel(false, '未查找到相关信息'));
        }

        foreach ($r->data as $key => $value) {
            $r->data[$key]['openid'] = $adminR->data['openidforpay'];
            //获取线路信息
            if ($value['type'] == 1) {
                $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                if ($lineR->ret) {
                    $r->data[$key]['start_name'] = $lineR->data['start_name'];
                    $r->data[$key]['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($value['type'] == 2) {
                if ($value['is_custom'] == 0) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                }
            } elseif ($value['type'] == 3) {
                $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                if ($lineR->ret) {
                    $r->data[$key]['start_name'] = $lineR->data['start_name'];
                    $r->data[$key]['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($value['type'] == 4) {
                $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                if ($agencyR->ret) {
                    $r->data[$key]['name'] = $agencyR->data['name'];
                    $r->data[$key]['summary'] = $agencyR->data['summary'];
                }
            } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                if ($lineClassTrainR->ret) {
                    $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    if ($lineClassR->ret) {
                        $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                    }
                }
            } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_6) {
                if (empty($value['line_id'])) {//乘客发布的
                    $r->data[$key]['start_name'] = $r->data[$key]['start_region_name'];
                    $r->data[$key]['end_name'] = $r->data[$key]['end_region_name'];
                    unset($r->data[$key]['start_region_name']);
                    unset($r->data[$key]['end_region_name']);
                } else {
                    $LineFreeRideR = $this->find(array('id' => $value['line_id']), 'LineFreeRide');
                    if ($LineFreeRideR->ret) {
                        $r->data[$key]['start_name'] = $LineFreeRideR->data['start_name'];
                        $r->data[$key]['end_name'] = $LineFreeRideR->data['end_name'];
                    }
                }
            } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_7) {
                $lineFastR = $this->find(array('id' => $value['line_id']), 'LineFast');
                if ($lineFastR->ret) {
                    $r->data[$key]['start_name'] = $lineFastR->data['start_name'];
                    $r->data[$key]['end_name'] = $lineFastR->data['end_name'];
                }
            } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_11) {
                $lineTaxiR = $this->find(array('id' => $value['line_id']), 'LineTaxi');
                if ($lineTaxiR->ret) {
                    $r->data[$key]['start_name'] = $lineTaxiR->data['start_name'];
                    $r->data[$key]['end_name'] = $lineTaxiR->data['end_name'];
                }
            }
            unset($r->data[$key]['is_custom']);

            if (!$r->data[$key]['start_address_remark'] && !$r->data[$key]['end_address_remark']) {
                $r->data[$key]['start_address_remark'] = $r->data[$key]['start_name'];
                $r->data[$key]['end_address_remark'] = $r->data[$key]['end_name'];
            }

            $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
            if ($branchR->ret) {
                $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
            }

            //司机位置
            $r->data[$key]['position'] = "-";
            //预约中（即司机接单中）
            if ($value['state'] == 1) {
                $r->data[$key]['message'] = "派单中";
            } elseif ($value['state'] == 2) {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $r->data[$key]['driver_address'] = $driverR->data['address'];
                }
                $r->data[$key]['message'] = "已接单,等待接我上车";
            } elseif ($value['state'] == 3) {
                //已上车,等待出发

                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'state');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $r->data[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 4) {
                //在路上
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $r->data[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 5) {
                //已送达，待支付
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $r->data[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 7) {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = "";
                    $r->data[$key]['driver_latitude'] = "";
                    $r->data[$key]['driver_address'] = "";
                }
                //获取已退款
                if ($value['is_pre_pay'] == 1 && $value['temp_apply_branchid'] == $branchId) {
                    $r->data[$key]['refund_status'] = 1;
                    $r->data[$key]['refund_message'] = "退款申请中";
                    $refundR = $this->find(array('order_id' => $value['order_id']), 'Refunds');
                    if ($refundR->ret) {
                        $r->data[$key]['refund_status'] = $refundR->data['status'];
                        switch ($refundR->data['status']) {
                            case 2:
                                $r->data[$key]['refund_message'] = "退款中";
                                break;
                            case 3:
                                $r->data[$key]['refund_message'] = "退款成功";
                                break;
                            case 4:
                                $r->data[$key]['refund_message'] = "退款失败";
                                break;
                            default:
                                $r->data[$key]['refund_message'] = "退款申请中";
                                break;
                        }
                    }
                }
            } elseif ($value['state'] == \CommonDefine::ORDER_STATE_8) {
                $r->data[$key]['message'] = "已关闭";
            } else {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $r->data[$key]['driver_name'] = $driverR->data['name'];
                    $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                    $r->data[$key]['driver_longitude'] = "";
                    $r->data[$key]['driver_latitude'] = "";
                    $r->data[$key]['driver_address'] = "";
                }
            }
        }

        return $this->output($r);
    }

    /**
     * 指派订单(手机端)
     * @param int $order_id 订单ID
     * @param int $driver_id 司机ID
     */
    public function doAppointOrder($order_id, $driver_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,2,7');
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        } elseif ($orderR->data['appoint']  == 1) {
            //return $this->output(new \ResultModel(false, '订单已指派'));
        } elseif ($orderR->data['state'] == 2) {
            if ($orderR->data['type'] != \CommonDefine::ORDER_TYPE_5) {
                return $this->output(new \ResultModel(false, '订单异常'));
            }
        } elseif ($orderR->data['state'] == 7) {
            return $this->output(new \ResultModel(false, '订单已取消'));
        }
        if (!empty($orderR->data['driver_id'])) {
            if ($orderR->data['driver_id'] == $driver_id) {
                return $this->output(new \ResultModel(false, '指派失败，请勿重复指派给该司机'));
            }
        }


        if ($orderR->data['rob_order'] == \CommonDefine::ROB_ORDER_1) {
            return $this->output(new \ResultModel(false, '司机抢单中，暂时不能指派'));
        }

        //司机是否正常接单中
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if ($driverR->ret) {
            if ($driverR->data['state'] == 2 || $driverR->data['state'] == 1) {
                return $this->output(new \ResultModel(false, '司机在路上'));
            }
            if ($driverR->data['state'] == 3) {
                return $this->output(new \ResultModel(false, '司机暂停接单'));
            }
            if ($driverR->data['residual_seating'] < $orderR->data['book_seating']) {
                return $this->output(new \ResultModel(false, '司机剩余座位数不足,请重新选择司机'));
            }
        } else {
            return $this->output(new \ResultModel(false, '司机不存在'));
        }

        $this->startTrans();

        $updateOrderData['order_id'] = $order_id;
        $updateOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $updateOrderData['driver_id'] = $driver_id;
        $updateOrderData['train_id'] = $driverR->data['current_train_id'];
        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
            if (($orderR->data['is_temp'] == 0  && $orderR->data['pay_mode'] != \CommonDefine::ORDER_PAYMENT_MODE_1) || ($orderR->data['channel_type'] == \CommonDefine::CHANNEL_TYPE_0 && $orderR->data['is_temp'] == 1 && $orderR->data['is_draw'] != \CommonDefine::DRAW_TICKET_2)) {
                $drawTicketR = $this->drawTicket($orderR);
                if (!$drawTicketR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '余票不足，指派失败'));
                }
            }
        } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
            if ($this->mchid == 1238) {
                if (!empty($orderR->data['driver_id'])) {//修改指派更新司机座位
                    $originDriverR = $this->find(['driver_id' => $orderR->data['driver_id']], 'Driver');
                    if (!$originDriverR->ret) {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '原司机信息异常'));
                    }
                    $origin_residual_seating = $originDriverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $originDriverS = $this->save('Driver', array('driver_id' => $orderR->data['driver_id'], 'residual_seating' => $origin_residual_seating));
                    if ($originDriverS->ret) {
                        if (C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($orderR->data['order_id'], $originDriverR->data['driver_id'], $originDriverR->data['residual_seating'], $orderR->data['book_seating'], $origin_residual_seating, 0, 'Branch.Order.doAppointOrder', "[".__LINE__.']修改指派');
                        }

                    } else {
                        $this->transRollback();
                        return $this->output($originDriverS);
                    }
                }

                //更新司机剩余座位数
                $residual_seating = $driverR->data['residual_seating'] - $orderR->data['book_seating'];
                $driverS = $this->save('Driver', array('driver_id' => $driver_id, 'residual_seating' => $residual_seating));
                if (!$driverS->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '网络异常，请重试'));
                }

                $updateOrderData['state'] = \CommonDefine::ORDER_STATE_2;
                $updateOrderData['seat_is_add'] = 1;
                if (C('SEAT_LOG_ON')) {
                    $this->recordSeatLog($orderR->data['order_id'], $driver_id, $driverR->data['residual_seating'], $orderR->data['book_seating'], $residual_seating, 1, 'Branch.Order.doAppointOrder', "[".__LINE__.']直接指派给司机，无需接单操作');
                }
            }
        }

        //指派完成
        $r = $this->save('Order', $updateOrderData);
        if ($r->ret) {
            $this->commitTrans();

            $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_0, $driver_id);
            # 2024.07.12 排单成功，通知司机
            $this->sendInnerNotification('driver', 'wechat', 'wechat_driver_new_order', $orderR->data['order_id']);

            if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
                //通知乘客
                $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
            } elseif ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_1) {
                if ($this->mchid == 1238) {
                    //通知乘客
                    $this->sendMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_1);
                }
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '指派失败，请稍后重试'));
        }

        $this->doAddLog('指派订单', json_encode($orderR->data), \StateModel::$BRANCH_USER);

        return $this->output($r);
    }

    /**
     * 取消派单(手机端)
     * @param int $order_id 订单ID
     */
    public function doCannelAppointOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,2');
        $where['appoint'] = \CommonDefine::APPOINT_TYPE_1;
        $where['type'] = array('in','1,2,3,4,5,7');
        $where['rob_order'] = \CommonDefine::ROB_ORDER_0;
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        $this->startTrans();
        $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
        if (!$driverR->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '取消失败'));
        }
        //司机是已接单
        if ($orderR->data['state'] == 2) {
            $updateOrderData['order_id'] = $order_id;
            $updateOrderData['state'] = \CommonDefine::ORDER_STATE_1;
            $updateOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
            $updateOrderData['driver_id'] = '';
            $orderS = $this->save('Order', $updateOrderData);
            if ($orderS->ret) {
                $saveData['driver_id'] = $driverR->data['driver_id'];
                if ($orderR->data['seat_is_add'] == 1) {
                    $saveData['residual_seating'] = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $derverS = $this->save('Driver', $saveData);
                    if ($derverS->ret) {
                        $this->commitTrans();
                        if (C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($order_id, $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $saveData['residual_seating'], 0, 'doCannelAppointOrder', '取消自己的已指派订单');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '取消失败'));
                    }
                } else {
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }
        } elseif ($orderR->data['state'] == 1) {
            $updateOrderData['order_id'] = $order_id;
            $updateOrderData['state'] = \CommonDefine::ORDER_STATE_1;
            $updateOrderData['appoint'] = \CommonDefine::APPOINT_TYPE_0;
            $updateOrderData['driver_id'] = '';
            $orderS = $this->save('Order', $updateOrderData);
            if (!$orderS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }
            $this->commitTrans();
        }

        $this->sendWxMessage($orderR->data['order_id'], \CommonDefine::USER_TYPE_2, \CommonDefine::MESSAGE_TO_DRIVER_TYPE_2, $orderR->data['driver_id']);

        $this->doAddLog('取消指派', json_encode($orderR->data), \StateModel::$BRANCH_USER);

        return $this->output(new \ResultModel(true, '取消成功'));
    }

    /**
     * 关闭订单
     * @param int $order_id 订单id
     */
    public function doCloseOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1');
        $where['appoint'] = \CommonDefine::APPOINT_TYPE_0;
        $where['type'] = array('in','1,2,3,4,5,7');
        $where['rob_order'] = \CommonDefine::ROB_ORDER_0;
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常，请刷新后重试'));
        }

        if ($orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1 || $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
            return $this->output(new \ResultModel(false, '该订单已支付，您暂时无法关闭'));
        }
        //乘客端班线车下单，如果开启先支付后坐车模式，支付后立即锁票，直至乘客支付完成解锁车票
        $response = httpRequest(C('CC_INNER_API_HOST') . "/api/inner/orders/{$orderR->data['order_id']}/issued/tickets", 'delete');
        $results = json_decode($response, true);
        if ($results['status'] != 'success') {
            return $this->output(new \ResultModel(false, $results['message']));
        }
        $this->startTrans();
        //司机未接单
        if ($orderR->data['state'] == \CommonDefine::ORDER_STATE_1) {
            //司机还未接单
            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => \CommonDefine::ORDER_STATE_8));
            if ($orderS->ret) {
                if ($orderR->data['type'] == \CommonDefine::ORDER_STATE_5 && $orderR->data['pay_mode'] == \CommonDefine::PAY_STATUS_1) {
                    $lineClassTrainR = $this->find('line_class_train_id = '.$orderR->data['line_id'], 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassTrainS = $this->save('LineClassTrain', array('line_class_train_id' => $orderR->data['line_id'], 'remain_tickets' => $lineClassTrainR->data['remain_tickets'] + $orderR->data['book_seating']));
                        if (!$lineClassTrainS->ret) {
                            $this->transRollback();
                            return $this->output(new \ResultModel(false, '关闭失败'));
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '关闭失败'));
                    }
                }
                $this->commitTrans();

            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '关闭失败'));
            }
        }
        return $this->output(new \ResultModel(true, '关闭成功'));
    }


    /**
     * 分台创建拼车临时订单(手机端)
     * @param int $line_id 线路id
     * @param int $book_seating 预定人数
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 出发地地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 目的地地址
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempOrder($line_id, $book_seating = 1, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;

        $tempBranchR = $this->find(array('admin_id' => $branchid), 'Admin');

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        $lineR = $this->find(array('id' => $line_id, 'is_del' => 0), 'Line');
        if ($lineR->data['status'] != 1 || !$lineR->ret) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        if ($end_longitude && $end_latitude) {
            $this->data['end_address_code'] = $this->getGdAddressCodeByGdApi($end_longitude, $end_latitude);
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $lineR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $mch = M('AnnualFee')->where(array('mchid' => $lineR->data['mchid']))->find();
        $this->data['price'] = $lineR->data['price'] * $book_seating;
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $lineR->data['mchid'];
        $this->data['branchid'] = $lineR->data['branchid'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;

        $this->data['is_temp'] = 1;
        $this->data['type'] = 1;

        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_1, $this->data['line_id']);
                    if ($driversR->ret) {
                        $driverCellphoneArr = array();
                        $driverWeixinArr = array();
                        foreach ($driversR->data as $driverData) {
                            $driverCellphoneArr[] = $driverData['cellphone'];
                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                        }
                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);

                        //您有新的[拼车]订单了
                        //乘客姓名：张三
                        //乘客人数：3人
                        //联系电话：1333333333
                        //预定线路：武汉-北京
                        //上车时间：2016/8/8 18:30
                        //请尽快与乘客取得联系
                        $temp = array(
                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                            '临时乘客',
                            $book_seating,
                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                            $lineR->data['start_name']." - ". $lineR->data['end_name'],
                            $this->data['start_time'],
                            '请及时抢单! 点击前往抢单'
                        );
                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                    }
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                }
            }
            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data);

            $r->data['order_id'] = $r->data;
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $tempBranchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];
        }

        return $this->output($r);
    }

    /**
     * 渠道分台创建拼车临时订单(手机端)
     * @param int $line_id 线路id
     * @param int $book_seating 预定人数
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 地址
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempChannelOrder($line_id, $book_seating = 1, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        if (empty($this->state->data['branch_type']) || $this->state->data['branch_type'] != \CommonDefine::BRANCH_TYPE_1) {
            return $this->output(new \ResultModel(false, '下单失败，非渠道账号'));
        }
        $this->data['is_channel'] = \CommonDefine::ORDER_CHENNEL_1;
        $this->data['temp_apply_branchid'] = $branchid;

        $tempBranchR = $this->find(array('admin_id' => $branchid), 'Admin');

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        $lineR = $this->find(array('id' => $line_id, 'is_del' => 0), 'Line');
        if ($lineR->data['status'] != 1 || !$lineR->ret) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $lineR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $mch = M('AnnualFee')->where(array('mchid' => $lineR->data['mchid']))->find();
        $this->data['price'] = $lineR->data['channel_price'] * $book_seating;
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $lineR->data['mchid'];
        $this->data['branchid'] = $lineR->data['branchid'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;

        $this->data['is_temp'] = 1;
        $this->data['type'] = 1;

        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_1, $this->data['line_id']);
                    if ($driversR->ret) {
                        $driverCellphoneArr = array();
                        $driverWeixinArr = array();
                        foreach ($driversR->data as $driverData) {
                            $driverCellphoneArr[] = $driverData['cellphone'];
                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                        }
                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);

                        //您有新的[拼车]订单了
                        //乘客姓名：张三
                        //乘客人数：3人
                        //联系电话：1333333333
                        //预定线路：武汉-北京
                        //上车时间：2016/8/8 18:30
                        //请尽快与乘客取得联系
                        $temp = array(
                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                            '临时乘客',
                            $book_seating,
                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                            $lineR->data['start_name']." - ". $lineR->data['end_name'],
                            $this->data['start_time'],
                            '请及时抢单! 点击前往抢单'
                        );
                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                    }
                } else {
                    //通知分台
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);
                }
            }

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($this->data);

            $r->data['order_id'] = $this->data;
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $tempBranchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];
        }

        return $this->output($r);
    }

    /**
     * 分台创建包车临时订单(手机端)---非自定义
     * @param int $line_id 线路id ：0-非自定义
     * @param int $line_chartered_price_id 价格类型,is_custom为0时此项必填
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 开始地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 结束地址
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempOrderChartered($line_id, $line_chartered_price_id, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;

        $lineCharteredPriceR = $this->find(array('line_chartered_price_id' => $line_chartered_price_id, 'is_del' => 0), 'LineCharteredPrice');
        if (!$lineCharteredPriceR->ret) {
            return $this->output(new \ResultModel(false, '暂不支持该车型'));
        }

        $lineCharteredR = $this->find(array('id' => $line_id, 'is_del' => 0), 'LineChartered', 'id, status, start_address_code, end_address_code, mchid, branchid');
        if ($lineCharteredR->data['status'] != 1 || !$lineCharteredR->ret) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        $carTypeR = $this->find(array('car_type_id' => $lineCharteredPriceR->data['car_type_id']), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $lineCharteredR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $this->data['mchid'] = $lineCharteredR->data['mchid'];
        $this->data['branchid'] = $lineCharteredR->data['branchid'];

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');

        $mch = M('AnnualFee')->where(array('mchid' => $this->mchid))->find();
        $this->data['price'] = $lineCharteredPriceR->data['price'];
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['car_type_id'] = $lineCharteredPriceR->data['car_type_id'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['book_seating'] = $book_seating;
        $this->data['is_temp'] = 1;
        $this->data['type'] = 2;

        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        $this->data['order_id'] = $r->data;
        if ($r->ret) {

            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_2, $this->data['line_id']);
                    if ($driversR->ret) {
                        $driverCellphoneArr = array();
                        $driverWeixinArr = array();
                        foreach ($driversR->data as $driverData) {
                            $driverCellphoneArr[] = $driverData['cellphone'];
                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                        }
                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);

                        //您有新的[拼车]订单了
                        //乘客姓名：张三
                        //乘客人数：3人
                        //联系电话：1333333333
                        //预定线路：武汉-北京
                        //上车时间：2016/8/8 18:30
                        //请尽快与乘客取得联系
                        $temp = array(
                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                            '临时乘客',
                            $book_seating,
                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                            $lineCharteredR->data['start_name']." - ". $lineCharteredR->data['end_name'],
                            $this->data['start_time'],
                            '请及时抢单! 点击前往抢单'
                        );
                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                    }
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }

    /**
     * 渠道分台创建包车临时订单(手机端)---非自定义
     * @param int $line_id 线路id ：0-非自定义
     * @param int $line_chartered_price_id 价格类型,is_custom为0时此项必填
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 开始地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 结束地址
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempChannelOrderChartered($line_id, $line_chartered_price_id, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        if (empty($this->state->data['branch_type']) || $this->state->data['branch_type'] != \CommonDefine::BRANCH_TYPE_1) {
            return $this->output(new \ResultModel(false, '下单失败，非渠道账号'));
        }
        $this->data['is_channel'] = \CommonDefine::ORDER_CHENNEL_1;
        $this->data['temp_apply_branchid'] = $branchid;

        $lineCharteredPriceR = $this->find(array('line_chartered_price_id' => $line_chartered_price_id, 'is_del' => 0), 'LineCharteredPrice');
        if (!$lineCharteredPriceR->ret) {
            return $this->output(new \ResultModel(false, '暂不支持该车型'));
        }

        $lineCharteredR = $this->find(array('id' => $line_id, 'is_del' => 0), 'LineChartered', 'id, status, start_address_code, end_address_code, mchid, branchid');
        if ($lineCharteredR->data['status'] != 1 || !$lineCharteredR->ret) {
            return $this->output(new \ResultModel(false, '该线路不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        $carTypeR = $this->find(array('car_type_id' => $lineCharteredPriceR->data['car_type_id']), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $lineCharteredR->data['mchid']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $this->data['mchid'] = $lineCharteredR->data['mchid'];
        $this->data['branchid'] = $lineCharteredR->data['branchid'];

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');

        $mch = M('AnnualFee')->where(array('mchid' => $this->mchid))->find();
        $this->data['price'] = $lineCharteredPriceR->data['channel_price'];
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['car_type_id'] = $lineCharteredPriceR->data['car_type_id'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['book_seating'] = $book_seating;
        $this->data['is_temp'] = 1;
        $this->data['type'] = 2;

        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        $this->data['order_id'] = $r->data;
        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];
            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    $driversR = $this->getDriversByLineId($this->data['mchid'], \CommonDefine::ORDER_TYPE_2, $this->data['line_id']);
                    if ($driversR->ret) {
                        $driverCellphoneArr = array();
                        $driverWeixinArr = array();
                        foreach ($driversR->data as $driverData) {
                            $driverCellphoneArr[] = $driverData['cellphone'];
                            $driverWeixinArr[] = array('openid' => $driverData['openid'], 'driver_id' => $driverData['driver_id']);
                        }
                        //$this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);

                        //您有新的[拼车]订单了
                        //乘客姓名：张三
                        //乘客人数：3人
                        //联系电话：1333333333
                        //预定线路：武汉-北京
                        //上车时间：2016/8/8 18:30
                        //请尽快与乘客取得联系
                        $temp = array(
                            '有新的['.$this->getOrderType($this->data['type']).']订单了',
                            '临时乘客',
                            $book_seating,
                            $this->encryptionCellphoneByCellphone($this->data['reseverd_phone']),
                            $lineCharteredR->data['start_name']." - ". $lineCharteredR->data['end_name'],
                            $this->data['start_time'],
                            '请及时抢单! 点击前往抢单'
                        );
                        $this->pushWeixinMessages($driverWeixinArr, $temp, \CommonDefine::USER_TYPE_2, $this->data['mchid'], self::WECHAT_MSG_TYPE_9);
                    }
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }

    /**
     * 分台创建包车临时订单(手机端)---自定义
     * @param int $line_id 线路id ：0-非自定义
     * @param int $car_type_id 车型：1,2
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 开始地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 结束地址
     * @param string $ft_number 航班号（默认为空）
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempOrderCharteredCustom($price = 0, $car_type_id, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ft_number = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['is_custom'] = 1;

        /**
         * 自定义包车调度分台管理员
         */
        $lineCharterCustomBranchConfig = \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG_0;
        $lineCharterCustomBranchConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => \CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG), 'SystemConfig');
        if ($lineCharterCustomBranchConfigR->ret) {
            $lineCharterCustomBranchConfig = $lineCharterCustomBranchConfigR->data['value'];
        } else {
            return $this->output(new \ResultModel(false, '下单失败请联系管理员'));
        }

        $branchR = $this->find(array('admin_id' => $lineCharterCustomBranchConfig), 'Admin');

        if (!is_numeric($price)) {
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }

        $carTypeR = $this->find(array('car_type_id' => $car_type_id), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        $mch = M('AnnualFee')->where(array('mchid' => $branchR->data['parent_admin_id']))->find();
        $rate = $mch['split'] / 100;//提成比例

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $branchR->data['parent_admin_id'];
        $this->data['branchid'] = $lineCharterCustomBranchConfig;
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['offer_price'] = $price - $rate * $price;
        $this->data['price'] = $price;
        $this->data['split'] = $rate;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['book_seating'] = $book_seating;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_2;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');

        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    /*                $driverR = $this->select(array('branchid' => $this->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                                    if ($driverR->ret) {
                                        $driverCellphoneArr = array();
                                        foreach($driverR->data as $driverData){
                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                        }
                                        $this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);
                                    }*/
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }

    /**
     * 更新乘客面议订单的价格(手机端)
     * @param int $order_id 订单ID
     * @param string $price 订单价格
     */
    public function doUpdateTempOrderCharteredCustom($order_id, $price)
    {
        $branchid = $this->state->user_id;
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,7');
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        } elseif ($orderR->data['appoint']  == 1) {
            return $this->output(new \ResultModel(false, '订单已指派'));
        } elseif ($orderR->data['state'] == 7) {
            return $this->output(new \ResultModel(false, '订单已取消'));
        }

        if (!is_numeric($price) || $price <= 0) {
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }
        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');
        $mch = M('AnnualFee')->where(array('mchid' => $branchR->data['parent_admin_id']))->find();
        $rate = $mch['split'] / 100;//提成比例
        $this->data['offer_price'] = $price - $rate * $price;
        $this->data['price'] = $price;
        $this->data['real_price'] = $this->data['price'];
        $this->data['is_face'] = 0;
        $r = $this->save('Order');
        return $this->output($r);
    }

    /**
     * 渠道分台创建包车临时订单(手机端)---自定义
     * @param int $line_id 线路id ：0-非自定义
     * @param int $car_type_id 车型：1,2
     * @param string $start_time 出发时间
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_person 预留人员的姓名
     * @param int $start_address_code 出发地
     * @param int $end_address_code 目的地
     * @param string $start_longitude 经度
     * @param string $start_latitude 纬度
     * @param string $start_address_remark 开始地址
     * @param string $end_longitude 目的地经度
     * @param string $end_latitude 目的地纬度
     * @param string $end_address_remark 结束地址
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempChannelOrderCharteredCustom($price = 0, $car_type_id, $start_time = null, $reseverd_phone, $reseverd_person = null, $reseverd_info = null, $start_address_code = null, $end_address_code = null, $start_longitude = null, $start_latitude = null, $start_address_remark = null, $end_longitude = null, $end_latitude = null, $end_address_remark = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        if (empty($this->state->data['branch_type']) || $this->state->data['branch_type'] != \CommonDefine::BRANCH_TYPE_1) {
            return $this->output(new \ResultModel(false, '下单失败，非渠道账号'));
        }
        $this->data['is_channel'] = \CommonDefine::ORDER_CHENNEL_1;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['is_custom'] = 1;

        /**
         * 自定义包车调度分台管理员
         */
        $lineCharterCustomBranchConfig = \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG_0;
        $lineCharterCustomBranchConfigR = $this->find(array('account_id' => $this->mchid , 'account_type' => \CommonDefine::SYSTEM_ROLE_1,'key' => \CommonDefine::LINE_CHARTER_CUSTOM_BRANCH_CONFIG), 'SystemConfig');
        if ($lineCharterCustomBranchConfigR->ret) {
            $lineCharterCustomBranchConfig = $lineCharterCustomBranchConfigR->data['value'];
        } else {
            return $this->output(new \ResultModel(false, '下单失败请联系管理员'));
        }

        $branchR = $this->find(array('admin_id' => $lineCharterCustomBranchConfig), 'Admin');

        if (!is_numeric($price)) {
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }

        $carTypeR = $this->find(array('car_type_id' => $car_type_id), 'CarType');
        if (!$carTypeR->ret) {
            return $this->output(new \ResultModel(false, '请选择正确的车型'));
        }
        $book_seating = $carTypeR->data['num'];

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        $mch = M('AnnualFee')->where(array('mchid' => $branchR->data['parent_admin_id']))->find();
        $rate = $mch['split'] / 100;//提成比例

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $branchR->data['parent_admin_id'];
        $this->data['branchid'] = $branchR->data['admin_id'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['offer_price'] = $price - $rate * $price;
        $this->data['price'] = $price;
        $this->data['split'] = $rate;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];
        $this->data['book_seating'] = $book_seating;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_2;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');

        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    /*                $driverR = $this->select(array('branchid' => $this->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                                    if ($driverR->ret) {
                                        $driverCellphoneArr = array();
                                        foreach($driverR->data as $driverData){
                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                        }
                                        $this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);
                                    }*/
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }


    /**
     * 分台创建带货订单临时订单
     * @param double $weight 重量
     * @param date $start_time 出发时间
     * @param string $reseverd_phone 发货人手机号
     * @param string $reseverd_person 发货人名称
     * @param strint $delivery_person 接货人
     * @param string $delivery_phone 接货人电话
     * @param string $reseverd_info 备注
     * @param string $start_longitude 开始经度
     * @param string $start_latitude 开始纬度
     * @param string $end_longitude 结束经度
     * @param string $end_latitude 结束纬度
     * @param int $start_address_code 出发地编码
     * @param string $start_address_remark 出发地
     * @param int $end_address_code 目的地编码
     * @param string $end_address_remark 目的地
     * @param string $ID_number 身份证号码（默认为空）
     * @param string $ID_real_name 身份证名称（默认为空）
     */
    public function doCreateTempOrderTakeGoods($price, $weight, $start_time = null, $reseverd_phone = null, $reseverd_person = null, $delivery_person, $delivery_phone, $reseverd_info = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_code = null, $start_address_remark = null, $end_address_code = null, $end_address_remark = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['is_custom'] = 1;

        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');

        if (!is_numeric($weight) || $weight <= 0) {
            return $this->output(new \ResultModel(false, '请正确填写重量'));
        }

        if (!is_numeric($price)) {
            return $this->output(new \ResultModel(false, '请填写正确的金额'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        if ($start_longitude && $start_latitude && empty($start_address_code)) {
            $this->data['start_address_code'] = $this->getGdAddressCodeByGdApi($start_longitude, $start_latitude);
        }

        $mch = M('AnnualFee')->where(array('mchid' => $branchR->data['parent_admin_id']))->find();
        $rate = $mch['split'] / 100;//提成比例

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $branchR->data['parent_admin_id'];
        $this->data['branchid'] = $branchid;
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;
        $this->data['offer_price'] = $price - $rate * $price;
        $this->data['price'] = $price;
        $this->data['split'] = $rate;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];
        $this->data['book_seating'] = 0;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_3;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];
            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    /*                $driverR = $this->select(array('branchid' => $this->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                                    if ($driverR->ret) {
                                        $driverCellphoneArr = array();
                                        foreach($driverR->data as $driverData){
                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                        }
                                        $this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);
                                    }*/
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }


    /**
     * 创建临时订单代理
     *
     * @param int    $agency_id     代理店铺ID
     * @param string $start_time    开始时间（默认为null）
     * @param string $reseverd_phone 预留电话（默认为null）
     * @param string $reseverd_info  预留信息（默认为null）
     * @param string $ID_number     身份证号码（默认为null）
     * @param string $ID_real_name  身份证真实姓名（默认为null）
     * @return void
     */
    public function doCreateTempOrderAgency($agency_id, $start_time = null, $reseverd_phone = null, $reseverd_info = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['is_custom'] = 1;
        $rate = 0;//提成比例

        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        $agencyR = $this->find(array('agency_id' => $agency_id, 'is_del = 0'), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new \ResultModel(false, '代办事件已下架,请重新选择'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $mch = M('AnnualFee')->where(array('mchid' => $this->mchid))->find();
        $this->data['price'] = $agencyR->data['price'];
        $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $mch['split'] / 100;
        $this->data['split'] = $mch['split'] / 100;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $branchR->data['parent_admin_id'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['branchid'] = $agencyR->data['branchid'];
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;

        $this->data['split'] = $rate;
        $this->data['book_seating'] = 0;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_4;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    /*                $driverR = $this->select(array('branchid' => $this->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                                    if ($driverR->ret) {
                                        $driverCellphoneArr = array();
                                        foreach($driverR->data as $driverData){
                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                        }
                                        $this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);
                                    }*/
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }
        }

        return $this->output($r);
    }

    /**
    * 创建临时通道订单代理
    *
    * @param int $agency_id 代理ID
    * @param \DateTime $start_time 开始时间（可选，默认为null）
    * @param string $reseverd_phone 预留电话（可选，默认为null）
    * @param string $reseverd_info 预留信息（可选，默认为null）
    * @param string $ID_number 身份证号码（可选，默认为null）
    * @param string $ID_real_name 身份证真实姓名（可选，默认为null）
    * @return void
    */
    public function doCreateTempChannelOrderAgency($agency_id, $start_time = null, $reseverd_phone = null, $reseverd_info = null, $ID_number = null, $ID_real_name = null)
    {
        $branchid = $this->state->user_id;
        if (empty($this->state->data['branch_type']) || $this->state->data['branch_type'] != \CommonDefine::BRANCH_TYPE_1) {
            return $this->output(new \ResultModel(false, '下单失败，非渠道账号'));
        }
        $this->data['is_channel'] = \CommonDefine::ORDER_CHENNEL_1;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['is_custom'] = 1;
        $rate = 0;//提成比例

        $branchR = $this->find(array('admin_id' => $branchid), 'Admin');

        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());
        if (is_null($start_time) || empty($start_time)) {
            $start_time = $currentTime;
        } else {
            $start_time = date('Y-m-d H:i:s', strtotime($start_time));
        }

        $agencyR = $this->find(array('agency_id' => $agency_id, 'is_del = 0'), 'Agency');
        if (!$agencyR->ret) {
            return $this->output(new \ResultModel(false, '代办事件已下架,请重新选择'));
        }

        $robOrder = \CommonDefine::ROB_ORDER_0;//默认非抢单模式；
        $systemConfigR = $this->find(array('key' => 'rob_order','account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $branchR->data['parent_admin_id']), 'SystemConfig');
        if ($systemConfigR->ret) {
            $robOrder = $systemConfigR->data['value'];
        }

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $mch = M('AnnualFee')->where(array('mchid' => $branchR->data['parent_admin_id']))->find();
        $rate = $mch['split'] / 100;//提成比例
        $this->data['offer_price'] = $agencyR->data['channel_price'] - $rate * $agencyR->data['channel_price'];
        $this->data['price'] = $agencyR->data['channel_price'];
        $this->data['split'] = $rate;
        $this->data['real_price'] = $this->data['price'];
        $this->data['merge_price'] = $this->data['real_price'];

        $this->data['start_time'] = $start_time;
        $this->data['mchid'] = $branchR->data['parent_admin_id'];
        $this->data['order_no'] = $this->createOrderNo();
        $this->data['branchid'] = $agencyR->data['branchid'];
        $this->data['rob_order'] = $robOrder;
        $this->data['pay_mode'] = $payMode;

        $this->data['book_seating'] = 0;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_4;
        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            $where['order_no'] = $this->data['order_no'];
            $where['branchid'] = $this->data['branchid'];
            $r = $this->find($where, 'Order', 'order_id,create_time');
            $r->data['book_seating'] = $this->data['book_seating'];
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $branchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data['order_id']);

            if ($payMode == \CommonDefine::ORDER_PAYMENT_MODE_0) {
                if ($robOrder == \CommonDefine::ROB_ORDER_1) {
                    /*                $driverR = $this->select(array('branchid' => $this->data['branchid'],'is_freeze' => \CommonDefine::IS_FREEZE_0),null,null,null,'Driver','cellphone');
                                    if ($driverR->ret) {
                                        $driverCellphoneArr = array();
                                        foreach($driverR->data as $driverData){
                                            $driverCellphoneArr[] = $driverData['cellphone'];
                                        }
                                        $this->pushShortMessages($driverCellphoneArr, array($this->data['order_no'], $reseverd_phone), \YTSMS::TEMP_ID_DRIVER_NEW_ORDER, $this->data['mchid']);
                                    }*/
                } else {
                    $this->postBranchNewOrderMessage($this->data['mchid'], $r->data['order_id']);
                }
            }

        }

        return $this->output($r);
    }

    /**
      * 创建临时订单行类
      *
      * @param int $line_class_train_id 行类火车ID
      * @param int $is_seat_selection 是否选座 0表示否，1表示是
      * @param array $seat_ids 座位ID数组
      * @param int $number 数量
      * @param string $reseverd_phone 预留电话
      * @param int $is_custom 是否定制 0表示否，1表示是
      * @param float $custom_price 定制价格
      * @param string $reseverd_info 预留信息
      * @param string $start_time 出发时间
      * @param string $start_longitude 出发经度
      * @param string $start_latitude 出发纬度
      * @param string $end_longitude 目的经度
      * @param string $end_latitude 目的纬度
      * @param string $start_address_remark 出发地址备注
      * @param string $end_address_remark 目的地址备注
      * @param string $return_start_time 返回时间
      * @param string $return_start_longitude 返回经度
      * @param string $return_start_latitude 返回纬度
      * @param string $return_end_longitude 返回目的经度
      * @param string $return_end_latitude 返回目的纬度
      * @param string $return_start_address_remark 返回出发地址备注
      * @param string $return_end_address_remark 返回目的地址备注
      * @param int $is_buy_insurance 是否购买保险 0表示否，1表示是
      * @param array $members 会员信息数组
      * @param array $insurance 保险信息数组
      * @param string $seat_occupied 座位占用类型
    */
    public function doCreateTempOrderLineClass($line_class_train_id, $is_seat_selection = 0, $seat_ids = null, $number = 1, $reseverd_phone, $is_custom = 0, $custom_price = 0, $reseverd_info = null, $start_time = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_remark = null, $end_address_remark = null, $return_start_time = null, $return_start_longitude = null, $return_start_latitude = null, $return_end_longitude = null, $return_end_latitude = null, $return_start_address_remark = null, $return_end_address_remark = null, $is_buy_insurance = 0, $members = null, $insurance = null, $seat_occupied = null)
    {
        if (empty($this->mchid)) {
            return new \ResultModel(false, '商户参数错误');
        }
        $header = [
            'branch: ' . $this->state->user_id,
            'Accept: application/json'
        ];
        $params = array(
            'is_temp' => 1,
            'temp_apply_branchid' => $this->state->user_id,
            'seat_ids' => $seat_ids,
            'reseverd_phone' => $reseverd_phone,
            'line_class_train_id' => $line_class_train_id,
            'start_longitude' => $start_longitude,
            'start_latitude' => $start_latitude,
            'end_longitude' => $end_longitude,
            'end_latitude' => $end_latitude,
            'start_address_remark' => ($start_address_remark),
            'end_address_remark' => ($end_address_remark),
            'start_time' => $start_time,
            'reseverd_info' => ($reseverd_info),
            'insurance' => $insurance,
            'members' => $members,
            'is_buy_insurance' => $is_buy_insurance,
            'number' => $number,
        );
        if ($seat_occupied) {
            $params['seat_occupied'] = json_encode($seat_occupied);
        }
        $ret = new \ResultModel(false);
        $bizType = \CommonDefine::ORDER_TYPE_5;
        $responsed = httpRequest(C('CC_PROXY_API_HOST') . "/api/inner/branches/{$this->state->user_id}/types/{$bizType}/orders", 'post', $params, $header);
        $results = json_decode($responsed, true);
        if ($results['status'] == 'success' && !empty($results['data'])) {
            $ret->ret = true;
            $ret->data = $results['data'];
        } else {
            $ret->data = sprintf('下单失败：%s', $results['message']);
        }
        return $this->output($ret);
    }

    /**
     * 渠道分台创建班线车临时订单(手机端)
     * @param int $line_class_train_id 班线id
     * @param string $seat_ids 座位id（默认为空）
     * @param int $book_seating 预定人数
     * @param string $reseverd_phone 预留电话
     * @param string $reseverd_info 备注信息
     * @param string $start_time 出发时间
     * @param string $start_longitude 出发经度（默认为空）
     * @param string $start_latitude 出发纬度（默认为空）
     * @param string $end_longitude 目的经度（默认为空）
     * @param string $end_latitude 目的纬度（默认为空）
     * @param string $start_address_remark 出发地名称（默认为空）
     * @param string $end_address_remark 目的地名称（默认为空）
     * @param string $return_start_time 出发时间（默认为空）
     * @param string $return_start_longitude 出发经度（默认为空）
     * @param string $return_start_latitude 出发纬度（默认为空）
     * @param string $return_end_longitude 目的经度（默认为空）
     * @param string $return_end_latitude 目的纬度（默认为空）
     * @param string $return_start_address_remark 出发地名称（默认为空）
     * @param string $return_end_address_remark 目的地名称（默认为空）
     * @param int $is_buy_insurance 是否已购买保险（默认为0）
     * @param json $members 乘车人员[{"real_name":"001","ID_number":"510122200020170001"，"seat_id":"2"},{"real_name":"002","ID_number":"510122200020170002"，"seat_id":"3"}]
     * @param json $insurance 购买保险集合[{"insurance_name":"平安保险1","insurance_desc":"意外车险","insurance_amount":"1.00"},{"insurance_name":"平安保险2","insurance_desc":"意外车险","insurance_amount":"1.00"}]
     */
    public function doCreateTempChannelOrderLineClass($line_class_train_id, $seat_ids = null, $number = 1, $reseverd_phone, $reseverd_info = null, $start_time = null, $start_longitude = null, $start_latitude = null, $end_longitude = null, $end_latitude = null, $start_address_remark = null, $end_address_remark = null, $return_start_time = null, $return_start_longitude = null, $return_start_latitude = null, $return_end_longitude = null, $return_end_latitude = null, $return_start_address_remark = null, $return_end_address_remark = null, $is_buy_insurance = 0, $members = null, $insurance = null)
    {
        $branchid = $this->state->user_id;
        $this->data['temp_apply_branchid'] = $branchid;
        $this->data['channel_type'] = \CommonDefine::CHANNEL_TYPE_1;
        //班次更新主键
        $upLineClassTrainData['line_class_train_id'] = $line_class_train_id;

        $tempBranchR = $this->find(array('admin_id' => $branchid), 'Admin');
        if (!empty($seat_ids)) {
            return $this->output(new \ResultModel(false, '选座暂不支持代约，敬请期待'));
        }
        if ($reseverd_phone) {
            $this->data['reseverd_phone_f'] = $reseverd_phone;
        }
        $currentTime = date('Y-m-d H:i:s', time());

        $this->startTrans();

        $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $line_class_train_id))->find();
        //        $lineClassTrainR = $this->find(array('line_class_train_id' => $line_class_train_id, 'is_del' => 0), 'LineClassTrain');
        if (empty($lineClassTrainArr)) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该班次不存在，请点击屏幕上方刷新按钮以获取最新的数据'));
        }

        if ($lineClassTrainArr['is_del'] == \CommonDefine::IS_DEL_1) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '该班次不存在'));
        }

        if ($number > $lineClassTrainArr['remain_tickets']) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '余票不足，请重新选择'));
        }

        if (!$this->checkTimeIsPassSellTime(time(), $line_class_train_id)) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '已过售票时间'));
        }

        $lineClassR = $this->find(array('id' => $lineClassTrainArr['line_class_id']), 'LineClass');

        //订单支付模式
        $payMode = \CommonDefine::ORDER_PAYMENT_MODE_0;
        /*        $payModeR = $this->find(array('key'=>\CommonDefine::ORDER_PAYMENT_MODE,'account_type' => \CommonDefine::SYSTEM_ROLE_1,'account_id' => $this->mchid),'SystemConfig');
                if($payModeR->ret){
                    $payMode = $payModeR->data['value'];
                }*/

        $orderStartFerryS = new \ResultModel(false);
        //下摆渡车订单（接）
        if ($lineClassTrainArr['is_start_ferry'] == \CommonDefine::IS_START_FERRY_1) {
            //乘客上车点（接）
            $ferry_start_longitude = $start_longitude;
            $ferry_start_latitude = $start_latitude;
            $ferry_start_address_remark = $start_address_remark;
            if (empty($ferry_start_longitude) || empty($ferry_start_latitude)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '请选择接送上车点'));
            }
            //车场位置
            $lineClassAroundPointsR = $this->getLineClassAroundPoints($lineClassTrainArr['line_class_id'], \CommonDefine::FERRY_TYPE_1, $ferry_start_longitude, $ferry_start_latitude);
            if (!$lineClassAroundPointsR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '附近没有车场点'));
            }
            //班线车起点信息
            $this->data['start_longitude'] = $lineClassAroundPointsR->data[0]['longitude'];
            $this->data['start_latitude'] = $lineClassAroundPointsR->data[0]['latitude'];
            $this->data['start_address_remark'] = $lineClassAroundPointsR->data[0]['alias'];
            $start_address_code = $this->getGdAddressCodeByGdApi($ferry_start_longitude, $ferry_start_latitude)['code'];
            $end_address_code = $this->getGdAddressCodeByGdApi($this->data['start_longitude'], $this->data['start_latitude'])['code'];
            if (empty($start_address_code) || empty($end_address_code)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '上车点解析失败'));
            }

            $cityCodeR = $this->resolutionAddressTwo($start_address_code);
            if (!$cityCodeR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '城市编码错误'));
            }

            $lineFastR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $cityCodeR->data['city_code'], 'is_del' => 0, 'is_show' => 1), 'LineFast');
            if (!$lineFastR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '出发地摆渡车暂未开通'));
            }

            //路径规划计算距离
            $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($ferry_start_longitude, $ferry_start_latitude, $this->data['start_longitude'], $this->data['start_latitude']);
            if (!$distanceR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '路径规划异常，请稍后再试'));
            }
            $distance = round($distanceR->data['distance'] / 1000, 2);//保留两位
            $ferryStartTime = date('Y-m-d H:i:s', strtotime($this->data['start_time']." -20 minute"));
            $orderStartFerryS = $this->doMobileBookLineFastInnerFunction($lineFastR->data['id'], \CommonDefine::FERRY_TYPE_1, $payMode, $number, $ferryStartTime, $this->data['reseverd_phone'], $reseverd_info, $ferry_start_longitude, $ferry_start_latitude, $this->data['start_longitude'], $this->data['start_latitude'], $start_address_code, $ferry_start_address_remark, $end_address_code, $this->data['start_address_remark'], $distance, 0);
            if (!$orderStartFerryS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, $orderStartFerryS->data));
            }
        }

        $orderEndFerryS = new \ResultModel(false);
        //下摆渡车订单（送）
        if ($lineClassTrainArr['is_end_ferry'] == \CommonDefine::IS_END_FERRY_1) {
            //乘客下车位置（送）
            //车场位置
            $ferry_end_longitude = $end_longitude;
            $ferry_end_latitude = $end_latitude;
            $ferry_end_address_remark = $end_address_remark;
            if (empty($ferry_end_longitude) || empty($ferry_end_latitude)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '请选择接送下车点'));
            }

            $lineClassAroundPointsR = $this->getLineClassAroundPoints($lineClassTrainArr['line_class_id'], \CommonDefine::FERRY_TYPE_2, $ferry_end_longitude, $ferry_end_latitude);
            if (!$lineClassAroundPointsR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '附近没有车场点'));
            }

            //班线车终点信息
            $this->data['end_longitude'] = $lineClassAroundPointsR->data[0]['longitude'];
            $this->data['end_latitude'] = $lineClassAroundPointsR->data[0]['latitude'];
            $this->data['end_address_remark'] = $lineClassAroundPointsR->data[0]['alias'];

            $start_address_code = $this->getGdAddressCodeByGdApi($this->data['end_longitude'], $this->data['end_latitude'])['code'];
            $end_address_code = $this->getGdAddressCodeByGdApi($ferry_end_longitude, $ferry_end_latitude)['code'];
            if (empty($start_address_code) || empty($end_address_code)) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '下车点解析失败'));
            }

            $cityCodeR = $this->resolutionAddressTwo($start_address_code);
            if (!$cityCodeR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '城市编码错误'));
            }

            $lineFastR = $this->find(array('mchid' => $this->mchid, 'start_city_code' => $cityCodeR->data['city_code'], 'is_del' => 0, 'is_show' => 1), 'LineFast');
            if (!$lineFastR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '目的地摆渡车暂未开通'));
            }

            //路径规划计算距离
            $distanceR = $this->getGdDirectionDrivingDistanceByGdApi($this->data['end_longitude'], $this->data['end_latitude'], $ferry_end_longitude, $ferry_end_latitude);
            if (!$distanceR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '路径规划异常，请稍后再试'));
            }
            $distance = round($distanceR->data['distance'] / 1000, 2);//保留两位

            $orderEndFerryS = $this->doMobileBookLineFastInnerFunction($lineFastR->data['id'], \CommonDefine::FERRY_TYPE_2, $payMode, $number, date('Y-m-d H:i:s', time()), $this->data['reseverd_phone'], $reseverd_info, $this->data['end_longitude'], $this->data['end_latitude'], $ferry_end_longitude, $ferry_end_latitude, $start_address_code, $this->data['end_address_remark'], $end_address_code, $ferry_end_address_remark, $distance, 0);
            if (!$orderEndFerryS->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, /* '暂时无上下车点接送司机，下单失败'*/$orderEndFerryS->data));
            }
        }

        //价格计算
        $mch = M('AnnualFee')->where(array('mchid' => $this->mchid))->find();
        $this->data['split'] = $mch['split'] / 100;
        if ($lineClassTrainArr['is_seat_selection'] == \CommonDefine::SEAT_SELECTION_1) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '选座暂不支持渠道代约'));
            $this->data['price'] = 0;
            $seatIdsArr = [];
            foreach ($this->data['seat_optional'] as $seatOptional) {
                $this->data['price'] += $seatOptional['price'];
                //座位
                $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
            }
            $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $this->data['split'];
            $this->data['offer_boss_price'] =  $this->data['price'] * $this->data['split'];

            $seatsPriceArr = json_decode($lineClassTrainR->data['seat_price'], true);
            $seatOptionalR = $this->getSeatOptional($seatsPriceArr, $seatIdsArr);
            if (!$seatOptionalR->ret) {
                return $this->output(new \ResultModel(false, '座位已售出，请点击屏幕上方刷新按钮以获取最新的数据!'));
            }
            $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr);
            if (!$updateSeatPriceDataR->ret) {
                return $this->output(new \ResultModel(false, '座位信息更新失败!'));
            }
            $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
        } else {
            $this->data['price'] = $lineClassTrainArr['channel_price'] * $number;
            $this->data['offer_price'] = $this->data['price'] - $this->data['price'] * $this->data['split'] / 100;
            $this->data['offer_boss_price'] =  $this->data['price'] * $this->data['split'];
        }

        $this->data['merge_price'] = $this->data['real_price'];
        //摆渡车接送金额计入
        $this->data['ferry_price'] = 0;
        if ($orderStartFerryS->ret) {
            $this->data['price'] += $orderStartFerryS->data['price'];
            $this->data['start_ferry_price'] = $orderStartFerryS->data['price'];
            $this->data['ferry_price'] += $this->data['start_ferry_price'];
        }
        if ($orderEndFerryS->ret) {
            $this->data['price'] += $orderEndFerryS->data['price'];
            $this->data['end_ferry_price'] = $orderEndFerryS->data['price'];
            $this->data['ferry_price'] += $this->data['end_ferry_price'];
        }
        $this->data['real_price'] = $this->data['price'];

        //出票成功
        $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] - $number;
        $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
        if (!$lineClassTrainS->ret) {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '出票失败'));
        }
        $this->data['is_draw'] = \CommonDefine::DRAW_TICKET_2;

        if ($lineClassR->data['start_time_type'] == 1) {
            $this->data['start_time'] = $lineClassTrainArr['start_date']." ".$lineClassR->data['start_time'];
        } else {
            $this->data['start_time'] .= ":00";
        }
        if ($orderStartFerryS->ret || $orderEndFerryS->ret) {
            $this->data['order_no'] = $this->createFerryOrderNo();
        } else {
            $this->data['order_no'] = $this->createOrderNo();
        }
        $this->data['order_no'] = $this->createFerryOrderNo();
        $this->data['mchid'] = $lineClassTrainArr['mchid'];
        $this->data['branchid'] = $lineClassTrainArr['branchid'];
        $this->data['rob_order'] = \CommonDefine::ROB_ORDER_0;
        $this->data['pay_mode'] = $payMode;
        $this->data['is_temp'] = 1;
        $this->data['type'] = \CommonDefine::ORDER_TYPE_5;
        $this->data['line_id'] = $line_class_train_id;
        $this->data['book_seating'] = $number;

        $this->data['create_time'] = $currentTime;
        $this->data['update_time'] = $currentTime;
        $r = $this->add('Order');
        if ($r->ret) {
            //摆渡车关联关系
            if ($orderStartFerryS->ret) {
                $orderSub['order_id'] = $r->data;
                $orderSub['relation_order_id'] = $orderStartFerryS->data['order_id'];
                $orderSubA = $this->add('OrderSub', $orderSub);
                if (!$orderSubA->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '接子订单下单异常!'));
                }
            }
            if ($orderEndFerryS->ret) {
                $orderSub['order_id'] = $r->data;
                $orderSub['relation_order_id'] = $orderEndFerryS->data['order_id'];
                $orderSubA = $this->add('OrderSub', $orderSub);
                if (!$orderSubA->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '送子订单下单异常!'));
                }
            }

            // 新订单Websocket消息通知
            $this->sendInnerWebSocket($r->data);

            $this->commitTrans();
            //通知乘客
            $this->sendMessage($r->data, \CommonDefine::USER_TYPE_1, \CommonDefine::MESSAGE_TO_PASSENGER_TYPE_0, 'sms');

            //通知分台
            $this->postBranchNewOrderMessage($this->data['mchid'], $r->data);

            unset($r->data);
            $r->data['order_no'] = $this->data['order_no'];
            $r->data['openid'] = $tempBranchR->data['openidforpay'];
            $r->data['price'] = $this->data['price'];
        } else {
            $this->transRollback();
        }

        return $this->output($r);
    }


    /**
     * 获取司机当前订单列表
     * @param int $driver_id 司机ID
     * @param int $page 页码
     * @param int $size 页大小
     * @param int $time 查询时间
     */
    public function doGetCurrentOrderList($driver_id, $page = 1, $size = 10)
    {
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if (!$driverR->ret) {
            return $this->output(new \ResultModel(false, '该账号异常'));
        }
        $order = 'create_time desc';
        $where['appoint'] = 1;
        $where['state'] = array('in','1,2,3,4,5');
        $where['train_id'] = $driverR->data['current_train_id'];
        $where['driver_id'] = $driver_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state,type,price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_temp,start_longitude,start_latitude,start_address_remark,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];
                $r->data[$key]['passenger_address'] = $r->data[$key]['start_address_remark'];

                //获取线路信息
                if ($r->data[$key]['type'] == 1) {
                    $line = $this->find(array('id' => $r->data[$key]['line_id']), 'Line', 'id,start_name,end_name');
                    $r->data[$key]['start_name'] = $line->data['start_name'];
                    $r->data[$key]['end_name'] = $line->data['end_name'];
                } elseif ($r->data[$key]['type'] == 2) {
                    if ($r->data[$key]['is_custom'] == 0) {
                        $lineChartered = $this->find(array('id' => $r->data[$key]['line_id']), 'LineChartered', 'id,start_name,end_name');

                        $r->data[$key]['start_name'] = $lineChartered->data['start_name'];
                        $r->data[$key]['end_name'] = $lineChartered->data['end_name'];
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                $r->data[$key]['driver_name'] = $driverR->ret ? $driverR->data['name'] : "";
                $r->data[$key]['driver_cellphone'] = $driverR->ret ? $driverR->data['cellphone'] : "";
                $r->data[$key]['car_status'] = $driverR->data['state'];
                $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];

            }
        }
        return $this->output($r);
    }


    /**
     * 获取司机订单列表
     * @param int $driver_id 司机ID
     * @param int $page 页码
     * @param int $size 页大小
     * @param int $time 查询时间
     */
    public function doGetOrderOverList($driver_id, $page = 1, $size = 10, $stime = null, $etime = null)
    {
        $where['state'] = array('in', '6');
        $order = 'create_time desc';
        $where['appoint'] = 1;
        if ($stime && $etime) {
            $where['create_time'] = array(array('egt', date('Y-m-d 00:00:00', strtotime($stime))), array('elt', date('Y-m-d 23:59:59', strtotime($etime))));
        }
        $where['driver_id'] = $driver_id;
        $r = $this->select($where, $page, $size, $order, 'Order', 'order_no,passenger_id,order_id,state,type,price,book_seating,line_id,start_time,branchid,driver_id,create_time,reseverd_phone,reseverd_person,reseverd_info,is_temp,is_pre_pay,start_longitude,start_latitude,start_address_remark,end_longitude,end_latitude,end_address_remark,is_custom,car_type_id,delivery_person,delivery_phone,weight,agency_id');
        if ($r->ret) {
            foreach ($r->data as $key => $value) {
                //获取乘客信息
                $passengerR = $this->find(array('passenger_id' => $value['passenger_id']), 'Passenger');
                if ($r->data[$key]['is_temp'] == 1) {
                    $r->data[$key]['cellphone'] = $r->data[$key]['reseverd_phone'];
                } else {
                    $r->data[$key]['cellphone'] = $passengerR->data['cellphone'];
                }
                $r->data[$key]['reseverd_phone'] = is_null($r->data[$key]['reseverd_phone']) ? $passengerR->data['cellphone'] : $r->data[$key]['reseverd_phone'];
                $r->data[$key]['passenger_name'] = $passengerR->data['name'];
                $r->data[$key]['passenger_longitude'] = $r->data[$key]['start_longitude'];
                $r->data[$key]['passenger_latitude'] = $r->data[$key]['start_latitude'];
                $r->data[$key]['passenger_address'] = $r->data[$key]['start_address_remark'];

                //获取线路信息
                if ($r->data[$key]['type'] == 1) {
                    $line = $this->find(array('id' => $r->data[$key]['line_id']), 'Line', 'id,start_name,end_name');
                    $r->data[$key]['start_name'] = $line->data['start_name'];
                    $r->data[$key]['end_name'] = $line->data['end_name'];
                } elseif ($r->data[$key]['type'] == 2) {
                    if ($r->data[$key]['is_custom'] == 0) {
                        $lineChartered = $this->find(array('id' => $r->data[$key]['line_id']), 'LineChartered', 'id,start_name,end_name');

                        $r->data[$key]['start_name'] = $lineChartered->data['start_name'];
                        $r->data[$key]['end_name'] = $lineChartered->data['end_name'];
                    }
                } elseif ($value['type'] == 3) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                    if ($lineR->ret) {
                        $r->data[$key]['start_name'] = $lineR->data['start_name'];
                        $r->data[$key]['end_name'] = $lineR->data['end_name'];
                    }
                } elseif ($value['type'] == 4) {
                    $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                    if ($agencyR->ret) {
                        $r->data[$key]['name'] = $agencyR->data['name'];
                        $r->data[$key]['summary'] = $agencyR->data['summary'];
                    }
                } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                    if ($lineClassTrainR->ret) {
                        $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                        if ($lineClassR->ret) {
                            $r->data[$key]['start_name'] = $lineClassR->data['start_name'];
                            $r->data[$key]['end_name'] = $lineClassR->data['end_name'];
                        }
                    }
                }
                unset($r->data[$key]['is_custom']);

                $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
                if ($branchR->ret) {
                    $r->data[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                    $r->data[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
                }

                //司机位置
                $r->data[$key]['position'] = "-";
                //预约中（即司机接单中）
                if ($value['state'] == 1) {
                    $r->data[$key]['message'] = "派单中";
                } elseif ($value['state'] == 2) {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                    $r->data[$key]['message'] = "已接单,等待接我上车";
                } elseif ($value['state'] == 3) {
                    //已上车,等待出发
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'state');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 4) {
                    //在路上
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 5) {
                    //已送达，待支付
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = $driverR->data['longitude'];
                        $r->data[$key]['driver_latitude'] = $driverR->data['latitude'];
                        $r->data[$key]['driver_address'] = $driverR->data['address'];
                    }
                } elseif ($value['state'] == 7) {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = "";
                        $r->data[$key]['driver_latitude'] = "";
                        $r->data[$key]['driver_address'] = "";
                    }
                    //获取已退款
                    if ($value['is_pre_pay'] == 1) {
                        $r->data[$key]['refund_status'] = 1;
                        $r->data[$key]['refund_message'] = "退款申请中";
                        $refundR = $this->find(array('order_id' => $value['order_id']), 'Refunds');
                        if ($refundR->ret) {
                            $r->data[$key]['refund_status'] = $refundR->data['status'];
                            switch ($refundR->data['status']) {
                                case 2:
                                    $r->data[$key]['refund_message'] = "退款中";
                                    break;
                                case 3:
                                    $r->data[$key]['refund_message'] = "退款成功";
                                    break;
                                case 4:
                                    $r->data[$key]['refund_message'] = "退款失败";
                                    break;
                                default:
                                    $r->data[$key]['refund_message'] = "退款申请中";
                                    break;
                            }
                        }
                    }
                } elseif ($value['state'] == \CommonDefine::ORDER_STATE_8) {
                    $r->data[$key]['message'] = "已关闭";
                } else {
                    $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                    if ($driverR->ret) {
                        $r->data[$key]['driver_name'] = $driverR->data['name'];
                        $r->data[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                        $r->data[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                        $r->data[$key]['car_brand'] = $driverR->data['car_brand'];
                        $r->data[$key]['driver_longitude'] = "";
                        $r->data[$key]['driver_latitude'] = "";
                        $r->data[$key]['driver_address'] = "";
                    }
                }
            }
        }
        return $this->output($r);
    }


    /**
     * 指派订单回收，递归
     */
    public function doOrderRecovery($page = 1, $size = 20)
    {
        if (C('TIMER_ON')) {
            $where['state'] = array('in', '1');
            //        $where['driver_id'] = array('eq','NULL');
            $where['type'] = array('in', '1,2,3,4');
            $where['appoint'] = 1;
            $where['rob_order'] = \CommonDefine::ROB_ORDER_0;

            $where['update_time'] = array('elt',date('Y-m-d H:i:s', time() - C('TIMER_TIME_SET')));
            $r = $this->select($where, $page, $size, 'update_time asc', 'Order');
            if ($r->ret) {
                foreach ($r->data as $k => $v) {
                    $this->startTrans();
                    $orderR = $this->find($where, 'Order');
                    if ($orderR->ret) {
                        $orderS = $this->save('Order', array('order_id' => $v['order_id'], 'appoint' => 0, 'driver_id' => ""));
                        if ($orderS->ret) {
                            $this->commitTrans();
                            if (C('WX_TEMPLATE_ON')) {
                                $driverR = $this->find(array('driver_id' => $v['driver_id']), 'Driver', 'openid,name');
                                //通知司机
                                //司机刘师傅接受了您的派单！
                                //订单类型：拼车订单
                                //订单编号：**********
                                //订单已撤回！
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"由于您'.(C('TIMER_TIME_SET') / 60).'分钟内未接单，'.$this->getOrderType($v['type']).'订单已撤回！',
                                    $v['order_no'],
                                    '订单已撤回',
                                    '如有疑问请及时联系派单人员！'
                                );
                                $this->wechatPushInfo($v['mchid'], $v['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);

                                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'openid');
                                //通知分台
                                //司机刘师傅接受了您的派单！
                                //订单类型：拼车订单
                                //订单编号：**********
                                //请及时重新派单！
                                $temp = array(
                                    '司机"'.$driverR->data['name'].'"超过'.(C('TIMER_TIME_SET') / 60).'分钟未接单，'.$this->getOrderType($v['type']).'订单已撤回！请重新指派',
                                    $v['order_no'],
                                    '订单已撤回',
                                    '订单已撤回！请重新指派'
                                );
                                $this->wechatPushInfo($v['mchid'], $v['branchid'], 3, $branchR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
                if ($r->count == 20) {
                    //                    self::doOrderRecovery($page ++);
                }
            }
        }
        echo "订单回收,执行成功";
    }

    /**
     * 无人抢单，更新为派单，递归
     */
    public function doRobOrderRecovery($page = 1, $size = 20)
    {
        if (C('TIMER_ON')) {
            $where['state'] = array('in', '1');
            //        $where['driver_id'] = array('eq','NULL');
            $where['appoint'] = 0;
            $where['rob_order'] = \CommonDefine::ROB_ORDER_1;

            $where['update_time'] = array('elt',date('Y-m-d H:i:s', time() - C('TIMER_ROB_TIME_SET')));
            $r = $this->select($where, $page, $size, 'update_time asc', 'Order');
            if ($r->ret) {
                foreach ($r->data as $k => $v) {
                    $this->startTrans();
                    $where['order_id'] = $v['order_id'];
                    $orderR = $this->find($where, 'Order');
                    if ($orderR->ret) {
                        $orderS = $this->save('Order', array('order_id' => $v['order_id'], 'appoint' => 0, 'driver_id' => "","rob_order" => \CommonDefine::ROB_ORDER_0));
                        if ($orderS->ret) {
                            $this->commitTrans();
                            //记录订单变更日志
                            if (C('WX_TEMPLATE_ON')) {
                                $branchR = $this->find(array('admin_id' => $v['branchid']), 'Admin', 'openid');
                                //通知分台
                                //该订单一直无人司机抢单，请及时派单！
                                //订单类型：拼车订单
                                //订单编号：**********
                                //请及时重新派单！
                                $temp = array(
                                    '该订单超过'.(C('TIMER_ROB_TIME_SET') / 60).'分钟无司机抢单'.'！请及时派单',
                                    $v['order_no'],
                                    '订单变更',
                                    '该订单已变更为分台进行手动指派！请及时处理'
                                );
                                $this->wechatPushInfo($v['mchid'], $v['branchid'], 3, $branchR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                unset($temp);
                            }
                        } else {
                            $this->transRollback();
                        }
                    } else {
                        $this->transRollback();
                    }
                }
                if ($r->count == 20) {
                    //                    self::doRobOrderRecovery($page ++);
                }
            }
        }
        echo "订单改由指派,执行成功";
    }

    /**
     * 将待出发行程更新为进行中的行程，递归
     */
    public function doChangeDriverWaitTripToOn($page = 1, $size = 200)
    {
        if (C('TIMER_ON')) {
            return;
            $where['state'] = array('in', '2');
            $where['seat_is_add'] = 0;
            $where['type'] = array('in', '1,2');
            $where['start_time'] = array('elt',date('Y-m-d H:i:s', time() + C('TIMER_ORDER_LIMIT')));
            $orderR = $this->select($where, $page, $size, 'start_time desc', 'Order');
            if ($orderR->ret) {
                foreach ($orderR->data as $k => $v) {
                    $this->startTrans();
                    $driverR = $this->find(array('driver_id' => $v['driver_id'], 'state' => 0), 'Driver');
                    if ($driverR->ret) {
                        if ($driverR->data['residual_seating'] >= $v['book_seating'] && $v['seat_is_add'] == 0) {
                            $residual_seating = $driverR->data['residual_seating'] - $v['book_seating'];
                            $driverS = $this->save('Driver', array('driver_id' => $v['driver_id'],'residual_seating' => $residual_seating,'seat_is_add' => 1));
                            $orderS = $this->save('Order', array('order_id' => $v['order_id'],'seat_is_add' => 1));
                            if ($driverS->ret && $orderS->ret) {
                                $this->commitTrans();
                                if (C('SEAT_LOG_ON')) {
                                    $this->recordSeatLog($v['order_id'], $v['driver_id'], $driverR->data['residual_seating'], $v['book_seating'], $residual_seating, 1, 'doChangeDriverWaitTripToOn', '将待出发行程更新为进行中的行程');
                                }
                                //消息通知
                                if (C('WX_TEMPLATE_ON')) {
                                    //通知司机
                                    //司机刘师傅接受了您的派单！
                                    //订单类型：拼车订单
                                    //订单编号：**********
                                    //订单已撤回！
                                    $temp = array(
                                        '司机"'.$driverR->data['name'].'"您的预约定'.$this->getOrderType($v['type']).'订单，离出发还有'. (C('TIMER_ORDER_LIMIT') / 60).'分钟！',
                                        $v['order_no'],
                                        '进行中',
                                        '请安排好自己的行程，及时处理!'
                                    );
                                    $this->wechatPushInfo($v['mchid'], $v['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                                    unset($temp);
                                }
                            } else {
                                $this->transRollback();
                            }
                        }
                    }
                }
                if ($orderR->count == 200) {
                    //                    self::doChangeDriverWaitTripToOn($page++);
                }
            }
        }
        echo "更新订单,执行成功";
    }

    /**
     * 获取分台代约退款---已支付
     * @param int $order_id 订单Id
     * @param string $description 退款原因
     */
    public function doReFunds($order_id, $description = null)
    {
        $branchId = $this->state->user_id;
        $branchR = $this->find(array('admin_id' => $branchId,'is_del' => 0,'is_freeze' => 0,'group_id' => 3), 'Admin', 'parent_admin_id,admin_id,balance');
        if (!$branchR->ret) {
            return $this->output(new \ResultModel(false, '数据异常'));
        }
        $orderR = $this->find(array('order_id' => $order_id,'is_temp' => 1,'state' => array('in','1,2,3,4,5,6,7'),'type' => array('in','1,2,3,4')), 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '该订单不存在'));
        }

        if ($orderR->data['is_pre_pay'] == 0) {
            return $this->output(new \ResultModel(false, '该订单还未支付，退款失败'));
        }

        if ($orderR->data['state'] == 3) {
            return $this->output(new \ResultModel(false, '乘客已上车，不能取消该订单'));
        } elseif ($orderR->data['state'] == 4) {
            return $this->output(new \ResultModel(false, '已出发，不能取消该订单'));
        } elseif ($orderR->data['state'] == 5) {
            return $this->output(new \ResultModel(false, '已送达，不能取消该订单'));
        } elseif ($orderR->data['state'] == 6) {
            return $this->output(new \ResultModel(false, '已完成，不能取消该订单'));
        } elseif ($orderR->data['state'] == 7) {
            return $this->output(new \ResultModel(false, '订单已取消'));
        }

        try {
            $this->startTrans();
            if ($orderR->data['state'] == 2) {//司机已接单
                $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
                if (!$driverR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '取消失败'));
                }

                $saveData['driver_id'] = $driverR->data['driver_id'];
                if ($orderR->data['seat_is_add'] == 1) {
                    $saveData['residual_seating'] = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $derverS = $this->save('Driver', $saveData);
                    if ($derverS->ret) {
                        if (C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($order_id, $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $saveData['residual_seating'], 0, 'doReFunds', '已支付分台取消司机已接订单');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '取消失败'));
                    }
                }
            }

            //总台余额查询
            $where['order_id'] = $orderR->data['order_id'];
            $where['account_id'] = $branchId;
            $where['account_type'] = 2;

            $wxPayUtil = new \WxPayUtil();
            if (!$wxPayUtil->init($orderR->data['mchid'])) {
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
            }
            $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
            if (!$refundR->ret) {
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
            }

            $refundData['amount'] = $orderR->data['real_price'];
            $refundData['status'] = 3;
            $refundData['order_id'] = $order_id;
            $refundData['account_type'] = 2;//分台
            $refundData['account_id'] = $branchId;
            $refundData['refund_id'] = $refundR->data['refund_id'];
            $refundData['refund_no'] = $refundR->data['out_trade_no'];
            $refundData['created'] = time();
            $refundData['description'] = "取消订单，全额退款";
            $refundData['charge'] = $refundR->data['charge'];
            $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
            $refundData['transaction_no'] = $refundR->data['transaction_id'];
            $reR = $this->add('Refunds', $refundData);
            if ($reR->ret) {
                $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'],'state' => 7));
                if ($orderS->ret) {
                    $this->commitTrans();
                    return  $this->output(new \ResultModel(true, '退款中，退款将于3个工作日内到账!'));
                }
            }

            $this->transRollback();
            return  $this->output(new \ResultModel(false, '退款失败...'));
        } catch (\Exception $e) {
            $this->transRollback();
            return  $this->output(new \ResultModel(false, '服务器异常请稍后再试...'));
        }
    }

    /**
     * 获取分台代约退款---已支付
     * @param int $order_id 订单Id
     * @param string $description 退款原因
     */
    public function doReFundsClassTrainOrder($order_id, $description = null)
    {
        $branchId = $this->state->user_id;
        $branchR = $this->find(array('admin_id' => $branchId,'is_del' => 0,'is_freeze' => 0,'group_id' => 3), 'Admin', 'parent_admin_id,admin_id,balance');
        if (!$branchR->ret) {
            return $this->output(new \ResultModel(false, '数据异常'));
        }
        $orderR = $this->find(array('order_id' => $order_id,'is_temp' => 1,'state' => array('in','1,2,3,4,5,6,7'),'type' => \CommonDefine::ORDER_TYPE_5), 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '该订单不存在'));
        }

        if ($orderR->data['is_pay'] == 0) {
            return $this->output(new \ResultModel(false, '该订单还未支付，退款失败'));
        }

        if ($orderR->data['state'] == 3) {
            return $this->output(new \ResultModel(false, '乘客已上车，不能取消该订单'));
        } elseif ($orderR->data['state'] == 4) {
            return $this->output(new \ResultModel(false, '已出发，不能取消该订单'));
        } elseif ($orderR->data['state'] == 5) {
            return $this->output(new \ResultModel(false, '已送达，不能取消该订单'));
        } elseif ($orderR->data['state'] == 6) {
            return $this->output(new \ResultModel(false, '已完成，不能取消该订单'));
        } elseif ($orderR->data['state'] == 7) {
            return $this->output(new \ResultModel(false, '订单已取消'));
        }

        try {
            $this->startTrans();
            //总台余额查询
            $mchR = $this->find(array('admin_id' => $branchR->data['parent_admin_id'],'is_del' => 0,'group_id' => 2), 'Admin', 'admin_id,balance');
            if (!$mchR->ret) {
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '退款失败，请稍后重试!'));
            }

            $wxPayUtil = new \WxPayUtil();
            if (!$wxPayUtil->init($orderR->data['mchid'])) {
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
            }
            $refundR = $wxPayUtil->createRefund($orderR->data['order_no'], $orderR->data['price'], $orderR->data['price']);
            if (!$refundR->ret) {
                $this->transRollback();
                return  $this->output(new \ResultModel(false, '申请退款失败，请联系管理员!'));
            }

            $refundData['amount'] = $orderR->data['real_price'];
            $refundData['status'] = 3;
            $refundData['order_id'] = $order_id;
            $refundData['account_type'] = 2;//分台
            $refundData['account_id'] = $branchId;
            $refundData['refund_id'] = $refundR->data['refund_id'];
            $refundData['refund_no'] = $refundR->data['out_trade_no'];
            $refundData['created'] = time();
            $refundData['description'] = "取消订单，全额退款";
            $refundData['charge'] = $refundR->data['charge'];
            $refundData['charge_order_no'] = $refundR->data['out_refund_no'];
            $refundData['transaction_no'] = $refundR->data['transaction_id'];
            $reR = $this->add('Refunds', $refundData);
            if ($reR->ret) {
                $orderUpdateData['order_id'] = $orderR->data['order_id'];
                $orderUpdateData['refund_amount'] = $orderR->data['real_price'];
                $orderUpdateData['refund_total_amount'] = $orderR->data['real_price'];
                $orderUpdateData['state'] = \CommonDefine::ORDER_STATE_7;
                $orderS = $this->save('Order', $orderUpdateData);
                if ($orderS->ret) {
                    $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
                    //                    $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']),'LineClassTrain');
                    if (!empty($lineClassTrainArr)) {
                        $upLineClassTrainData = [];
                        if (!empty($orderR->data['seat_optional'])) {
                            $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                            $seatIdsArr = [];
                            foreach ($seatOptionalArr as $seatOptional) {
                                $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                            }
                            $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                            $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr, \CommonDefine::OPTIONAL_0);
                            if (!$updateSeatPriceDataR->ret) {
                                $this->transRollback();
                                return  $this->output(new \ResultModel(false, '退票失败，请联系管理员!'));
                            }
                            $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
                        }
                        $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] + $orderR->data['book_seating'];
                        $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];
                        $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
                        if ($lineClassTrainS->ret) {
                            $this->commitTrans();
                            return  $this->output(new \ResultModel(true, '退款中，退款将于3个工作日内到账!'));
                        }
                    }
                }
            }

            $this->transRollback();
            return  $this->output(new \ResultModel(false, '退款失败...'));
        } catch (\Exception $e) {
            $this->transRollback();
            return  $this->output(new \ResultModel(false, '服务器异常请稍后再试...'));
        }
    }

    /**
     * 取消代约订单(手机端) ---未支付
     * @param int $order_id 订单ID
     */
    public function doCannelGenerationsOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,2');
        $where['type'] = array('in','1,2,3,4');
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        if ($orderR->data['is_pre_pay'] == 1) {
            return $this->output(new \ResultModel(false, '操作异常'));
        }

        $this->startTrans();
        //司机是已接单
        if ($orderR->data['state'] == 2) {
            $driverR = $this->find(array('driver_id' => $orderR->data['driver_id']), 'Driver');
            if (!$driverR->ret) {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }
            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
            if ($orderS->ret) {
                $saveData['driver_id'] = $driverR->data['driver_id'];
                if ($orderR->data['seat_is_add'] == 1) {
                    $saveData['residual_seating'] = $driverR->data['residual_seating'] + $orderR->data['book_seating'];
                    $derverS = $this->save('Driver', $saveData);
                    if ($derverS->ret) {
                        $this->commitTrans();
                        if (C('SEAT_LOG_ON')) {
                            $this->recordSeatLog($order_id, $orderR->data['driver_id'], $driverR->data['residual_seating'], $orderR->data['book_seating'], $saveData['residual_seating'], 0, 'doCannelGenerationsOrder', '未支付分台取消司机已接订单');
                        }
                    } else {
                        $this->transRollback();
                        return $this->output(new \ResultModel(false, '取消失败'));
                    }
                } else {
                    $this->commitTrans();
                }
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }

            if (C('SMS_ON')) {
                $branchR = $this->find(array('admin_id' => $orderR->data['branchid']), 'Admin');
                $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);
                $smsUtil->sendTemplateSMS($driverR->data['cellphone'], array($orderR->data['order_no'],$branchR->data['cellphone']), \SMSUtil::TEMP_ID_PSCANCEL, $driverR->data['mchid']);
            }

            if (C('WX_TEMPLATE_ON')) {
                //获取总台相关信息
                $mchR = $this->find(array('admin_id' => $orderR->data['mchid']), 'Admin');
                //【CC招车】乘客已取消了本次行程，您的订单{1}已取消，如有疑问请拨打{2}
                //乘客已取消了本次行程
                //订单编号：C1510170029132
                //订单状态：乘客已取消
                //如有疑问请拨打：************
                $temp = array( '乘客已取消了本次行程', $orderR->data['order_no'], '乘客已取消', '如有疑问请拨打:'.$mchR->data['tel']);
                $this->wechatPushInfo($driverR->data['mchid'], $driverR->data['driver_id'], 2, $driverR->data['openid'], $temp, self::WECHAT_MSG_TYPE_1);
                unset($temp);
            }
        } elseif ($orderR->data['state'] == 1) {
            //司机还未接单
            $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
            if ($orderS->ret) {
                $this->commitTrans();
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(false, '取消失败'));
            }
        }

        //pushmessage消息推送

        //推送司机新消息
        //        $this->pushDriverNewMessage($order_id, $driver_id, "新消息","您新订单啦，乘客电话：".$passengerR->data['cellphone']);

        return $this->output(new \ResultModel(true, '取消成功'));
    }


    /**
     * 取消代约订单(手机端) ---未支付
     * @param int $order_id 订单ID
     */
    public function doCannelGenerationsClassTrainOrder($order_id)
    {
        $where['order_id'] = $order_id;
        $where['state'] = array('in','1,2');
        $where['type'] = \CommonDefine::ORDER_TYPE_5;
        //订单是否有效
        $orderR = $this->find($where, 'Order');
        if (!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常'));
        }

        if ($orderR->data['is_pay'] == 1) {
            return $this->output(new \ResultModel(false, '操作异常'));
        }

        $this->startTrans();

        $orderS = $this->save('Order', array('order_id' => $order_id, 'state' => 7));
        if ($orderS->ret) {
            $lineClassTrainArr = M('LineClassTrain')->where(array('line_class_train_id' => $orderR->data['line_id']))->find();
            //            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id']),'LineClassTrain');
            if (!empty($lineClassTrainArr)) {
                $upLineClassTrainData = [];
                if (!empty($orderR->data['seat_optional'])) {
                    $seatOptionalArr = json_decode($orderR->data['seat_optional'], true);
                    $seatIdsArr = [];
                    foreach ($seatOptionalArr as $seatOptional) {
                        $seatIdsArr[] = ['seat_id' => $seatOptional['seat_id']];
                    }
                    $seatsPriceArr = json_decode($lineClassTrainArr['seat_price'], true);
                    $updateSeatPriceDataR = $this->updateSeatOptional($seatsPriceArr, $seatIdsArr, \CommonDefine::OPTIONAL_0);
                    if (!$updateSeatPriceDataR->ret) {
                        $this->transRollback();
                        return  $this->output(new \ResultModel(false, '退票失败，请联系管理员!'));
                    }
                    $upLineClassTrainData['seat_price'] = addslashes(json_encode($updateSeatPriceDataR->data));
                }
                $upLineClassTrainData['remain_tickets'] = $lineClassTrainArr['remain_tickets'] + $orderR->data['book_seating'];
                $upLineClassTrainData['line_class_train_id'] = $lineClassTrainArr['line_class_train_id'];

                $lineClassTrainS = $this->save('LineClassTrain', $upLineClassTrainData);
                if ($lineClassTrainS->ret) {
                    $this->commitTrans();
                    return  $this->output(new \ResultModel(true, '取消成功!'));
                }
            }
        } else {
            $this->transRollback();
            return $this->output(new \ResultModel(false, '取消失败'));
        }

        //pushmessage消息推送
        //推送司机新消息
        //        $this->pushDriverNewMessage($order_id, $driver_id, "新消息","您新订单啦，乘客电话：".$passengerR->data['cellphone']);

        return $this->output(new \ResultModel(true, '取消成功'));
    }

    /**
     * 获取分台代约列表
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     * @param string $start_time 开始时间（条件搜索，默认为空）
     * @param string $end_time 结束时间（条件搜索，默认为空）
     * @param string $pname 乘客姓名（条件搜索，默认为空）
     * @param string $pcellphone 乘客电话（条件搜索，默认为空）
     */
    public function doGetGenerations($page = 1, $size = 10, $start_time = null, $end_time = null, $pname = null, $pcellphone = null)
    {
        $branchId = $this->state->user_id;
        $adminR = $this->find(array('admin_id' => $branchId,'is_del' => 0,'is_freeze' => 0,'group_id' => 3), 'Admin', 'admin_id,openidforpay');
        if (!$adminR->ret) {
            return $this->output(new \ResultModel(false, '数据异常'));
        }
        $ret = new \ResultModel(false, '未查找到相关信息');

        $where = " o.temp_apply_branchid =".$adminR->data['admin_id'];
        $where .= " AND o.is_temp = 1";
        if (!empty($pname)) {
            $where .= " AND p.name like '%".$pname."%'";
        }

        if (!empty($pcellphone)) {
            $where .= " AND o.reseverd_phone like '%".$pcellphone."%'";
        }

        if (!empty($start_time)) {
            $where .= " AND o.start_time >= '".($start_time.":0'");
        }

        if (!empty($end_time)) {
            $where .= " AND o.start_time <= '".$end_time.":0'";
        }

        $order = "o.order_id DESC";
        $fields = 'o.agency_id, o.order_no, o.reseverd_person, o.order_id,
                 o.reseverd_phone, o.price, o.type, o.line_id, o.start_time,
                 o.book_seating, o.passenger_id, o.driver_id, o.create_time,
                 o.is_pay, o.is_pre_pay, o.is_temp, o.reseverd_info, o.start_address_remark,
                 o.end_address_remark, o.delivery_person, o.delivery_phone, o.is_custom, o.car_type_id,
                 o.state, o.start_longitude, o.start_latitude, o.end_longitude, o.end_latitude';
        $orderArr = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_passenger p ON p.passenger_id = o.passenger_id')
            ->page($page, $size)
            ->field($fields)
            ->where($where)
            ->order($order)
            ->select();
        $orderCount = M()->table('cp_order o')
            ->join(' LEFT JOIN cp_passenger p ON p.passenger_id = o.passenger_id')
            ->field($fields)
            ->where($where)
            ->count();

        if (empty($orderArr)) {
            return $this->output(new \ResultModel(false, '未查找到相关信息'));
        }

        foreach ($orderArr as $key => $value) {
            $orderArr[$key]['openid'] = $adminR->data['openidforpay'];
            //获取线路信息
            if ($value['type'] == 1) {
                $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                if ($lineR->ret) {
                    $orderArr[$key]['start_name'] = $lineR->data['start_name'];
                    $orderArr[$key]['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($value['type'] == 2) {
                if ($value['is_custom'] == 0) {
                    $lineR = $this->find(array('id' => $value['line_id']), 'LineChartered');
                    if ($lineR->ret) {
                        $orderArr[$key]['start_name'] = $lineR->data['start_name'];
                        $orderArr[$key]['end_name'] = $lineR->data['end_name'];
                    }
                }
            } elseif ($value['type'] == 3) {
                $lineR = $this->find(array('id' => $value['line_id']), 'Line');
                if ($lineR->ret) {
                    $orderArr[$key]['start_name'] = $lineR->data['start_name'];
                    $orderArr[$key]['end_name'] = $lineR->data['end_name'];
                }
            } elseif ($value['type'] == 4) {
                $agencyR = $this->find(array('agency_id' => $value['agency_id']), 'Agency');
                if ($agencyR->ret) {
                    $orderArr[$key]['name'] = $agencyR->data['name'];
                    $orderArr[$key]['summary'] = $agencyR->data['summary'];
                }
            } elseif ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                $lineClassTrainR = $this->find(array('line_class_train_id' => $value['line_id']), 'LineClassTrain');
                if ($lineClassTrainR->ret) {
                    $lineClassR = $this->find(array('id' => $lineClassTrainR->data['line_class_id']), 'LineClass');
                    if ($lineClassR->ret) {
                        $orderArr[$key]['start_name'] = $lineClassR->data['start_name'];
                        $orderArr[$key]['end_name'] = $lineClassR->data['end_name'];
                        $orderArr[$key]['aboard_longitude'] = $lineClassR->data['aboard_longitude'];
                        $orderArr[$key]['aboard_latitude'] = $lineClassR->data['aboard_latitude'];
                        $orderArr[$key]['aboard_address'] = $lineClassR->data['aboard_address'];
                    }
                }
            }
            unset($orderArr[$key]['is_custom']);

            if (!$orderArr[$key]['start_address_remark'] && !$orderArr[$key]['end_address_remark']) {
                $orderArr[$key]['start_address_remark'] = $orderArr[$key]['start_name'];
                $orderArr[$key]['end_address_remark'] = $orderArr[$key]['end_name'];
            }

            $branchR = $this->find(array('admin_id' => $value['branchid']), 'Admin');
            if ($branchR->ret) {
                $orderArr[$key]['branch_name'] = $branchR->ret ? $branchR->data['mchname'] : "";
                $orderArr[$key]['tel'] = $branchR->ret ? $branchR->data['tel'] : "";
            }

            //司机位置
            $orderArr[$key]['position'] = "-";
            //预约中（即司机接单中）
            if ($value['state'] == 1) {
                if ($value['type'] == \CommonDefine::ORDER_TYPE_5) {
                    $orderArr[$key]['message'] = "出票中";
                } else {
                    $orderArr[$key]['message'] = "派单中";
                }
            } elseif ($value['state'] == 2) {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $orderArr[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $orderArr[$key]['driver_address'] = $driverR->data['address'];
                }
                $orderArr[$key]['message'] = "已接单,等待接我上车";
            } elseif ($value['state'] == 3) {
                //已上车,等待出发
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver', 'state');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $orderArr[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $orderArr[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 4) {
                //在路上
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $orderArr[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $orderArr[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 5) {
                //已送达，待支付
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = $driverR->data['longitude'];
                    $orderArr[$key]['driver_latitude'] = $driverR->data['latitude'];
                    $orderArr[$key]['driver_address'] = $driverR->data['address'];
                }
            } elseif ($value['state'] == 7) {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = "";
                    $orderArr[$key]['driver_latitude'] = "";
                    $orderArr[$key]['driver_address'] = "";
                }
                //获取已退款
                if ($value['is_pre_pay'] == 1 || $value['is_pay'] == 1) {
                    $orderArr[$key]['refund_status'] = 1;
                    $orderArr[$key]['refund_message'] = "退款申请中";
                    $refundR = $this->find(array('order_id' => $value['order_id']), 'Refunds');
                    if ($refundR->ret) {
                        $orderArr[$key]['refund_status'] = $refundR->data['status'];
                        switch ($refundR->data['status']) {
                            case 2:
                                $orderArr[$key]['refund_message'] = "退款中";
                                break;
                            case 3:
                                $orderArr[$key]['refund_message'] = "退款成功";
                                break;
                            case 4:
                                $orderArr[$key]['refund_message'] = "退款失败";
                                break;
                            default:
                                $orderArr[$key]['refund_message'] = "退款申请中";
                                break;
                        }
                    }
                }
            } else {
                $driverR = $this->find(array('driver_id' => $value['driver_id']), 'Driver');
                if ($driverR->ret) {
                    $orderArr[$key]['driver_name'] = $driverR->data['name'];
                    $orderArr[$key]['driver_cellphone'] = $driverR->data['cellphone'];
                    $orderArr[$key]['car_tail_number'] = $driverR->data['car_tail_number'];
                    $orderArr[$key]['car_brand'] = $driverR->data['car_brand'];
                    $orderArr[$key]['driver_longitude'] = "";
                    $orderArr[$key]['driver_latitude'] = "";
                    $orderArr[$key]['driver_address'] = "";
                }
            }
        }
        $ret->ret = true;
        $ret->data = $orderArr;
        $ret->count = $orderCount;

        return $this->output($ret);
    }
}

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 消息模块
 *
 * <AUTHOR>
 */
class MessageAction extends BranchCoreAction
{
    /**
     * 获取最新的一条消息
     */
    public function getTimelyMessage(){
        $where['branch_id'] = $this->state->user_id;
        $where['is_del'] = 0;
        $where['status'] = 0;
        $r = $this->select($where, 1, 1,'create_time asc','BranchMessage');
        if($r->ret){
            foreach($r->data as $k=>$v){
                //$messageS = $this->save('BranchMessage', array('message_id' => $v['message_id'], 'status' => 1));
            }
        }
        return $this->output($r);
    }
}

?>

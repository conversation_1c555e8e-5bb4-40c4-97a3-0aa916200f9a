<?php

namespace Branch;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import('@/Action/Driver/DriverCoreAction');
import('@/Action/Driver/AccountAction');
import('@/Action/Driver/OrderAction');

/**
 * 线路模块
 *
 * <AUTHOR>
 */
class AgencyAction extends \BranchCoreAction
{
    /**
     * 获取代办事件列表
     * @param int $page 第几页（默认为1）
     * @param int $size 每页几条（默认为20）
     */
    public function getMobileAgencyList($page = 1, $size = 20){
        $size = 200;
        $where['mchid'] = $this->mchid;
        $where['is_del'] = 0;
        $order = ' create_time desc';
        $r = $this->select($where, $page, $size, $order,'Agency','agency_id,name,summary,price,start_appointment_time,end_appointment_time,business_time_type');
        return $this->output($r);
    }
}

?>

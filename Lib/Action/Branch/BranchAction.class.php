<?php
/**
 * 分台管理员模块
 *
 * <AUTHOR>
 */
import('@/Action/Weixin/Wechat');
class BranchAction extends BranchCoreAction
{
    /**
     * 登录
     * @param string $cellphone 手机号（可选）
     * @param string $password 密码
     */
    public function doLogin($cellphone, $password)
    {
        $mchid = $this->mchid;
        $r = $this->find(array('cellphone' => $cellphone,'group_id' => 3, 'parent_admin_id' => $mchid), 'Admin', 'admin_id,password,is_freeze,mchname,driver_num,balance,branch_type');
        if ($r->ret) {
            if($r->data['password'] == md5($password)) {
                //判断是否被删除
                if (intval($r->data['is_freeze']) === \CommonDefine::IS_FREEZE_1) {
                    return $this->output(new \ResultModel(false, '账号已删除'));
                }

                $wxUrlR = $this->getMerchantWeChatUrlForAuth($mchid, $r->data['admin_id'], $r->data['branch_type']);
                if($wxUrlR->ret) {
                    \StateModel::save($r->data['admin_id'], $password, \StateModel::$BRANCH_USER, array('mchname'=>$r->data['mchname'],'branch_type'=>$r->data['branch_type']), $mchid);
                    //                    header("Location:".$wxUrlR->data['wx_url']);
                    $r->data['wx_url'] = $wxUrlR->data['wx_url'];
                } else {
                    return $this->output(new \ResultModel(false, '账号异常！'));
                }
            } else {
                return $this->output(new \ResultModel(false, '密码错误，请重新输入！'));
            }
        } else {
            return $this->output(new \ResultModel(false, '账号不存在！'));
        }

        return $this->output($r);
    }

    /**
     * 模拟分台账号登录方法
     * 
     * @param int $cellphone 司机的手机号码，默认为19949464016
     * @param int $mchid 商户ID，默认为181
     * @return bool 成功返回 true，失败返回 false
     * @throws \Exception 如果司机信息未找到，抛出异常
     */
    public function doSimulateLogin($cellphone = 19949464016, $mchid = 181, $uri = '')
    {
        $branch = $this->find(
            array('cellphone' => $cellphone, 'parent_admin_id' => $mchid, 'group_id' => 3, 'is_freeze' => \CommonDefine::IS_FREEZE_0), 
            'Admin', 
            'admin_id,password,is_freeze,mchname,driver_num,balance,branch_type'
        );

        // 如果未找到司机信息，抛出异常并终止执行
        if (!$branch) {
            // 抛出异常，并将 cellphone 和 mchid 信息包含在异常信息中
            throw new \Exception("分台账号不存在或者已经被删除, cellphone: {$cellphone}, mchid: {$mchid}");
        }

        // 开始模拟登录的逻辑
        try {
            //清除所有Cookie
            \StateModel::flush();
            // // 调用 StateModel::save 方法，使用司机ID、密码和其他信息保存登录状态
            // $wxUrlR = $this->getMerchantWeChatUrlForAuth($mchid, $branch->data['admin_id'], $branch->data['branch_type']);
            \StateModel::save($branch->data['admin_id'], $branch->data['password'], \StateModel::$BRANCH_USER, array('mchname'=>$branch->data['mchname'],'branch_type'=>$branch->data['branch_type']), $mchid);

            $callbackUrl = C('WEB_ROOT') . $uri;
            
            // 重定向到指定的 URL
            $this->redirect($callbackUrl);
        } catch (\Exception $e) {
            return $this->output(new \ResultModel(false, $e->getMessage()));
        }
    }

    /**
     * 忘记密码
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 新密码
     */
    public function doForgetPassword($cellphone, $cellphone_validate_code, $password)
    {
        $mchid = $this->mchid;

        //验证手机号码
        if (C('CELLPHONE_VALIDATE_ENABLE')) {
            $r = $this->find(array('cellphone' => $cellphone, 'validate_code' => $cellphone_validate_code), 'CellphoneValidate', 'update_time,validate_code');
            if ($r->ret) {
                if (strtotime($r->data['update_time']) + C('CELLPHONE_VALIDATE_LIFETIME') < strtotime(get_current_time())) {
                    return $this->output(new \ResultModel(false, '验证码已经过期'));
                }
            } else {
                return $this->output(new \ResultModel(false, '手机验证码错误'));
            }
        }
        $r = $this->find(array('cellphone' => $cellphone,'group_id' => 3, 'parent_admin_id' => $mchid), 'Admin');
        if ($r->ret) {
            $r = $this->save('Admin', array('admin_id' => $r->data['admin_id'], 'password' => md5($password)));
        }
        return $this->output($r);
    }


    /**
     * 分台渠道账号注册
     * @param string $cellphone 手机号码
     * @param string $cellphone_validate_code 手机验证码
     * @param string $password 密码
     */
    private function doRegister($cellphone, $cellphone_validate_code, $password)
    {
        $mchid = $this->mchid;
        if (!$mchid) {
            return $this->output(new \ResultModel(false, '商户账号存在异常'));
        }

        /*  $this->data['cellphone_f'] = $cellphone;
          $this->data['status'] = self::DRIVER_STATUS_3;
          $this->data['mchid'] = $mchid;
          $this->data['from_type'] = \CommonDefine::FROM_TYPE_1;
          $this->data['driver_role_type'] = \CommonDefine::DRIVER_ROLE_1;*/

        $branchR = $this->find(array('cellphone'=>$cellphone, 'mchid' => $mchid), 'Admin');
        if($branchR->ret) {
            return $this->output(new \ResultModel(false, '该手机号已注册'));
        }

        //验证手机号码
        $r = $this->ValidateCellphone($cellphone, $cellphone_validate_code, $mchid);
        if ($r->ret) {
            $this->startTrans();

            //添加车主
            $branchA = $this->add('Admin');
            if ($branchA->ret) {
                $this->commitTrans();
                \StateModel::save($branchA->data, $password, \StateModel::$BRANCH_USER, $branchA->data);

                //微信通知总台
                return $this->output(new \ResultModel(true, '注册成功，请完善资料'));
            } else {
                $this->transRollback();
                return $this->output(new \ResultModel(true, '注册失败，请重新注册'));
            }
        }
        return $this->output($r);
    }

    /**
     * 微信端回调处理,保存用户openid用于消息推送
     */
    public function doGetWechatCode()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);

        $cipmchid = $dataArr[0];
        $branchid = $dataArr[1];
        $branchtype = $dataArr[2];

        if(!$branchid) {
            return new \ResultModel(false, "账号异常", null);
        }

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrl($code, $cipmchid));
        $arr = json_decode($data, true);

        $expires_in = $arr['expires_in'] - 200;
        setcookie('login_dri_token', $arr['access_token'], $expires_in);
        if ($arr['openid']) {
            $this->save('Admin', array('admin_id' => $branchid, 'openid' => $arr['openid']));
        }
        //分台微信支付授权
        $wxUrlR = $this->getPlatformWeChatUrlForAuth($cipmchid, $branchid, $branchtype);
        if($wxUrlR->ret) {
            header("Location:".$wxUrlR->data['wx_url']);
            return;
        }

        if($branchtype == \CommonDefine::BRANCH_TYPE_1) {
            redirect(C('WEB_ROOT') . 'branch_channel_manage/callback/'.$cipmchid);
        } else {
            redirect(C('WEB_ROOT') . 'branch_manage/callback/'.$cipmchid);
        }

        return;
    }

    /**
     * 微信端回调处理,保存用户openid用于支付
     */
    public function doGetWechatCodeForPay()
    {
        $code = $_GET['code'];
        $dataArr = explode(',', $_GET['state']);

        $cipmchid = $dataArr[0];
        $branchid = $dataArr[1];
        $branchtype = $dataArr[2];

        if(!$branchid) {
            return new \ResultModel(false, "账号异常", null);
        }

        $wechat = new \Wechat();
        $data = file_get_contents($wechat->getCodeAccessTokenUrlForPay($code));
        $arr = json_decode($data, true);
        if (isset($arr['openid']) && $arr['openid']) {
            $this->save('Admin', array('admin_id' => $branchid, 'openidforpay' => $arr['openid']));
        }
        if($branchtype == \CommonDefine::BRANCH_TYPE_1) {
            redirect(C('WEB_ROOT') . 'branch_channel_manage/callback/'.$cipmchid);
        } else {
            redirect(C('WEB_ROOT') . 'branch_manage/callback/'.$cipmchid);
        }
        return;
    }
    /**
     * 退出登录
     */
    public function doLogout()
    {
        \StateModel::clear(\StateModel::$BRANCH_USER, $this->mchid);
        return $this->output(new ResultModel(true));
    }

    /**
     * 获取分台司机列表
     * @param int $count 预定数量
     * @param int $page 第几页（默认为1)
     * @param int $order_id 订单id
     * @param int $size 每页几条（默认为10）
     * @param int $type 【1001:推荐；1002:班次；1003：距离】
     * @param int $line_condition 0：线路无关 1：线路相关
     * @param string $get_driver_by_timestamp 当前时间戳，默认为空
     * @param string $dcellphone 司机电话（条件搜索，默认为空）
     * @param string $dname 司机姓名（条件搜索，默认为空）
     */
    public function doGetDrivers($count = 1, $page = 1, $size = 10, $order_id, $type = 1001, $line_condition = 0, $get_driver_by_timestamp = null, $dcellphone = null, $dname = null)
    {
        $branchid = $this->state->user_id;
        $orderR = $this->find(array('order_id' => $order_id), 'Order');
        if(!$orderR->ret) {
            return $this->output(new \ResultModel(false, '订单异常', null, $get_driver_by_timestamp));
        }

        $lineType = 0;
        if($orderR->data['type'] == 1) {//拼车
            $lineType = 1;
        } elseif($orderR->data['type'] == 2) {
            $lineType = 2;
        } elseif($orderR->data['type'] == 3) {
            $lineType = 3;
        } elseif($orderR->data['type'] == 4) {
            $lineType = 4;
        } elseif($orderR->data['type'] == 5) {
            $lineType = 5;
        }

        $start_latitude = $orderR->data['start_latitude'];
        $start_longitude = $orderR->data['start_longitude'];
        $distanceField = "GETDISTANCE(d.latitude,d.longitude,d.latitude,d.longitude) AS distance,";
        if($start_latitude && $start_longitude) {
            $distanceField = 'GETDISTANCE(d.latitude,d.longitude,'.$start_latitude.','.$start_longitude.') AS distance,';
        }

        $where['d.branchid'] = $branchid;
        $where['d.state'] = array('eq', 0);
        $where['d.is_freeze'] = array('eq', \CommonDefine::IS_FREEZE_0);
        //        $where['d.from_type'] = array('eq', \CommonDefine::FROM_TYPE_0);
        $where['d.driver_role_type'] = array('eq', \CommonDefine::DRIVER_ROLE_0);
        if($dname) {
            $where['d.name'] = array('like', '%'.$dname.'%');
        }
        if($dcellphone) {
            $where['d.cellphone'] = array('eq', $dcellphone);
        }

        $db = M();
        $data = "";

        $order = "distance ASC,d.driver_id ASC";//默认推荐
        if($type == 1001) {//推荐
            $order = "distance ASC,d.driver_id ASC";
            $where['d.residual_seating'] = array('EGT', $orderR->data['book_seating']);
        } elseif($type == 1002) {//班次
            $order = 'd.sort ASC';
        } elseif($type == 1003) {//距离
            $order = "distance ASC,d.driver_id ASC";
        }

        if($line_condition == 1 && !empty($lineType)) {
            if($lineType == \CommonDefine::ORDER_TYPE_5) {
                $where['o.line_id'] = $orderR->data['line_id'];
                $data = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_line_class_train_driver lctd ON o.line_id = lctd.line_class_train_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = lctd.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                    ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
            } else {
                $where['dl.type'] = $lineType;
                $where['dl.line_id'] = $orderR->data['line_id'];
                $data = $db->table("cp_order o")
                    ->join("LEFT JOIN cp_driver_line dl ON o.line_id = dl.line_id")
                    ->join("LEFT JOIN cp_driver d ON d.driver_id = dl.driver_id")
                    ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                    ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
            }
        } else {
            $where['o.order_id'] = $orderR->data['order_id'];
            $data = $db->table("cp_driver d")
                ->join("LEFT JOIN cp_order o ON o.branchid = d.branchid")
                ->field($distanceField.'d.driver_id,d.name, d.cellphone,d.residual_seating,d.total_seating,d.car_brand,d.car_tail_number,d.sort')
                ->where($where)->order($order)->page($page, $size)->group("d.driver_id")->select();
        }

        if($data) {
            $count = count($data);
            foreach($data as $key => $item) {
                $data[$key]['distance'] = number_format(($item['distance'] / 1000), 2);
            }
            return $this->output(new ResultModel(true, $data, $count, $get_driver_by_timestamp));
        } else {
            return $this->output(new ResultModel(false, null, null, $get_driver_by_timestamp));
        }

    }

    /**
     * 获取分台司机列表
     * @param int $count 预定数量
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     */
    public function doGetAllDrivers($page = 1, $size = 10)
    {
        $branchid = $this->state->user_id;
        $where['branchid'] = $branchid;
        $order = 'driver_id asc';
        $r = $this->select($where, $page, $size, $order, 'Driver', 'driver_id, name, cellphone,residual_seating,longitude,latitude,total_seating,car_brand,car_tail_number,address,state');
        if ($r->ret) {
            foreach($r->data as $k=>$v) {
                if($v['latitude']&&$v['longitude']) {
                    $data = $this->getGdAddressCodeByGdApi($v['longitude'], $v['latitude']);
                    $r->data[$k]['address'] = $data['address'];
                }
            }
            return $this->output($r);
        } else {
            return $this->output(new \ResultModel(false, '未查找到相关信息'));
        }
    }

    /**
     * 分台发起提现申请
     * @param int $money 提现金额
     * @param string $cellphone_validate_code 短信验证码
     */
    public function doMobileWithDrawDespoist($money, $cellphone_validate_code)
    {
        $money = floatval($money);
        if (!$money && $money < 10) {
            return $this->output(new \ResultModel(false, '请输入正确的金额'));
        }

        $branchid = $this->state->user_id;
        $branchR = $this->find(array('admin_id' => $branchid, 'group_id' => 3), 'Admin', 'admin_id,mchname,cellphone,balance,is_freeze,status,parent_admin_id');
        if (!$branchR->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        } else {
            if ($branchR->data['is_freeze'] == 1) {
                return $this->output(new \ResultModel(false, '该账号已删除'));
            }
            if ($branchR->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '请等待账号审核通过后再重试'));
            } elseif ($branchR->data['status'] == 2) {
                return $this->output(new \ResultModel(false, '该账号审核未通过，请重新提交资料'));
            }
            $mchid = $branchR->data['parent_admin_id'];
            //验证手机号码
            $r = $this->ValidateCellphone($branchR->data['cellphone'], $cellphone_validate_code, $mchid);
            if(!$r->ret) {
                return $this->output($r);
            }
        }

        if ($money > $branchR->data['balance']) {
            return $this->output(new \ResultModel(false, '提现金额不能大于帐户余额'));
        }

        $branchR->data['balance'] -= $money;
        $this->startTrans();
        $branchS = $this->save('Admin', array('admin_id' => $branchid, 'balance' => $branchR->data['balance']));
        if ($branchS->ret) {
            $settlementRate = C('WX_SETTLEMENT_RATE');
            $realAmount = (1 - $settlementRate) * $money;
            $depositNo = $this->createDepositNo();
            $currentTime = date('Y-m-d H:i:s');
            $depositA = $this->add('Deposit', array('account_id' => $branchid, 'deposit_no' => $depositNo,'create_time' => $currentTime, 'mchid' => $mchid, 'account_type' => \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2, 'moneys' => $money ,'real_amount' => $realAmount, 'settlement_rate' => $settlementRate,'branchid' => $branchid,'step'=>2));
            $param = array('status'=>1,'deposit_id' => $depositA->data);
            if ($depositA->ret) {
                $param['step'] = 2;
                $depositStatusR = $this->find($param, 'DepositStatus');
                if($depositStatusR->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '网络异常，请稍后重试'));
                }
                $depositStatusA = $this->add('DepositStatus', $param);
                if(!$depositStatusA->ret) {
                    $this->transRollback();
                    return $this->output(new \ResultModel(false, '网络异常，请稍后重试'));
                } else {
                    $this->commitTrans();
                    //短信通知
                    if(C('SMS_ON')) {
                        //通知分台
                        $smsUtil = new \SMSUtil(\CommonDefine::RONGLIANYUN);

                        $smsDataToDriver = array(
                            $branchR->data['mchname'],
                            $depositNo,
                            "1-5工作日"
                        );
                        $smsUtil->sendTemplateSMS($branchR->data['cellphone'], $smsDataToDriver, \SMSUtil::TEMP_ID_APPLY_DEPOSIT_SUCCESS, $branchR->data['parent_admin_id']);

                        //通知总台
                        $mchR = $this->find(array('admin_id' => $mchid), 'Admin');
                        $smsDataToMch = array(
                            '分台['.$branchR->data['mchname'].']',
                            $branchR->data['cellphone'],
                            $currentTime,
                            $depositNo,
                            $money
                        );
                        $smsUtil->sendTemplateSMS($mchR->data['tel'], $smsDataToMch, \SMSUtil::TEMP_ID_NEW_DEPOSIT_APPLY, $branchR->data['mchid']);
                    }
                }
            } else {
                $this->transRollback();
                $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
            }
        } else {
            $this->transRollback();
            $this->output(new \ResultModel(false, '申请失败，请稍后再试'));
        }

        return $this->output(new \ResultModel(true, '申请成功'));
    }

    /**
     * 分台获取历史提现记录
     * @param int $page 第几页（默认为1)
     * @param int $size 每页几条（默认为10）
     */
    public function doGetWithDrawDespoists($page = 1, $size = 10)
    {
        $branchid = $this->state->user_id;
        $branchR = $this->find(array('admin_id' => $branchid, 'group_id' => 3), 'Admin', 'admin_id,mchname,cellphone,balance,is_freeze,status,parent_admin_id');
        if (!$branchR->ret) {
            return $this->output(new \ResultModel(false, '司机账号异常，请重新登录'));
        } else {
            if ($branchR->data['is_freeze'] == 1) {
                return $this->output(new \ResultModel(false, '该账号已删除'));
            }
            if ($branchR->data['status'] == 1) {
                return $this->output(new \ResultModel(false, '请等待账号审核通过后再重试'));
            } elseif ($branchR->data['status'] == 2) {
                return $this->output(new \ResultModel(false, '该账号审核未通过，请重新提交资料'));
            }
            $mchid = $branchR->data['parent_admin_id'];
        }

        $where['account_id'] = $branchid;
        $where['account_type'] = \CommonDefine::DEPOSIT_ACCOUNT_TYPE_2;
        $where['mchid'] = $mchid;
        $despoistR = $this->select($where, $page, $size, array('create_time' => 'DESC'), 'Deposit');
        if($despoistR->ret) {
            foreach($despoistR->data as $k => $v) {
                switch ($v['status']) {
                    case 1:
                        $despoistR->data[$k]['status_msg'] = '待审核';
                        break;
                    case 2:
                        $despoistR->data[$k]['status_msg'] = '审核通过';
                        break;
                    case 3:
                        $despoistR->data[$k]['status_msg'] = '审核未通过';
                        break;
                    default:
                        $despoistR->data[$k]['status_msg'] = '提现异常';
                        break;
                }
            }
        }

        $this->output($despoistR);
    }


    /**
     * 订单支付
     * @param double $pay 版本号
     */
    public function doOrderPay()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            exit;
        }
        $testData = file_get_contents('php://input');
        //生成充值记录
        $data = json_decode($testData, true);

        $this->data['openid'] = $data['openid'];
        $this->data['amount'] = $data['amount'];
        $this->data['channel'] = $data['channel'];
        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'] = $data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo), 'Order');
        if(!$orderR->ret) {
            exit;
        }

        if($orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1 || $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
            exit;
        }

        //班线订单
        if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id'],'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
            if($lineClassTrainR->ret) {
                if($lineClassTrainR->data['remain_tickets'] < $orderR->data['book_seating']) {
                    echo "余票不足，请选择其他班次";
                    exit;
                }
            } else {
                echo "线路已下架";
                exit;
            }
        }

        $this->data['account_id'] = $orderR->data['temp_apply_branchid'];
        $this->data['account_type'] = 2;

        $payHistoryR = $this->find(array('order_no' =>$data['order_no'], 'openid' => $data['openid']), 'PayOrderHistory');
        if(!$payHistoryR->ret) {
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                exit;
            }
        }

        //设置 API-Key
        //\Pingpp\Pingpp::setApiKey('sk_test_eDWXnDb5ybLS94aXDC1CSGmP');//测试
        \Pingpp\Pingpp::setApiKey('********************************');//
        //SDK 验证签名设置
        \Pingpp\Pingpp::setPrivateKeyPath(APP_PATH . 'Conf/rsa_private_key.pem');

        //是有已经存在未支付
        if($orderR->data['ch_id'] && $orderR->data['ch_id'] != "") {
            $charge = \Pingpp\Charge::retrieve($orderR->data['ch_id']);
            //将获得的支付凭据传给 Client
            header("Content-type: application/json");
            echo $charge;
            exit;
        }


        if ($data['channel'] == 'alipay_wap') {
            $extra = array(
                'success_url' => C('WX_DRIVER_URL'),
            );
        } elseif ($data['channel'] == 'wx_pub') {
            $extra = array(
                'open_id' => $data['openid'],
            );
        }

        try {
            //发起支付请求获取支付凭据
            $charge = \Pingpp\Charge::create(array(
                'order_no' => $data['order_no'],
                'amount' => $data['amount'],
                'app' => array('id' => 'app_n5qfT8an5mj1Oy1u'),
                'channel' => $data['channel'],
                'currency' => 'cny',
                'client_ip' => $_SERVER["REMOTE_ADDR"],
                'subject' => '代收车费',
                'body' => '合计：',
                'extra' => $extra
            ));
            $orderS = $this->save('Order', array('order_id' => $orderR->data['order_id'],'ch_id' => $charge->id));
        } catch (\Exception $e) {
            \Log::write($e->getCode().$e->getMessage());
        }

        //将获得的支付凭据传给 Client
        header("Content-type: application/json");
        echo $charge;
        exit;
        //接收 Webhooks 通知
    }


    /**
     * 订单支付
     * @param double $pay 版本号
     */
    public function doWxOrderPay($order_no, $openid = null, $amount, $channel = 'wx_pub')
    {
        $ret = new \ResultModel(false, '统一下单失败');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return $this->output($ret);
        }

        $this->data['time'] = date('Y-m-d H:i:s');
        $orderNo = $this->data['order_no'];

        $orderR = $this->find(array('order_no' => $orderNo), 'Order');
        if(!$orderR->ret) {
            $ret->data = '订单不存在';
            return $this->output($ret);
        }

        # 实时检测班线订单座位是否被释放，如果座位被释放或者订单已被取消，无法继续发起支付
        if ($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5
            && ($orderR->data['book_seating'] == 0
                || $orderR->data['state'] == \CommonDefine::ORDER_STATE_7)
        ) {
            $ret->data = '订单已被取消或者班次车票不足，无法继续支付';
            return $this->output($ret);
        }

        if(empty($orderR->data['is_temp'])) {
            $ret->data = '订单异常';
            return $this->output($ret);
        }

        $branchR = $this->find(array('admin_id' => $orderR->data['temp_apply_branchid']), 'Admin', 'openid, openidforpay');
        if(!$branchR->ret) {
            $ret->data = '订单异常';
            return $this->output($ret);
        }

        $payServiceR = $this->checkPayService($orderR->data['mchid']);
        if($payServiceR->ret) {
            if(empty($branchR->data['openid'])) {
                $ret->data = '分台用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $branchR->data['openid'];
        } else {
            if(empty($branchR->data['openidforpay'])) {
                $ret->data = '分台用户授权错误，退出后重新打开';
                return $this->output($ret);
            }
            $openid = $branchR->data['openidforpay'];
        }

        if($orderR->data['is_pre_pay'] == \CommonDefine::PAY_STATUS_1 || $orderR->data['is_pay'] == \CommonDefine::PAY_STATUS_1) {
            $ret->data = '订单异常';
            return $this->output($ret);
        }

        //班线订单
        if($orderR->data['type'] == \CommonDefine::ORDER_TYPE_5) {
            $lineClassTrainR = $this->find(array('line_class_train_id' => $orderR->data['line_id'],'is_del' => \CommonDefine::IS_DEL_0), 'LineClassTrain');
            if($lineClassTrainR->ret) {
                //班线车下单就扣除了余票
                //                if($lineClassTrainR->data['remain_tickets'] < $orderR->data['book_seating']){
                //                    $ret->data = '余票不足，请选择其他班次';
                //                    return $this->output($ret);
                //                }
            } else {
                $ret->data = '线路已下架';
                return $this->output($ret);
            }
        }

        $this->data['account_id'] = $orderR->data['temp_apply_branchid'];
        $this->data['account_type'] = 2;
        $this->data['openid'] = $openid;

        $payHistoryR = $this->find(array('order_no' =>$orderNo, 'openid' => $openid), 'PayOrderHistory');
        if(!$payHistoryR->ret) {
            $this->data['openid'] = $openid;
            $payHistoryS = $this->add('PayOrderHistory', $this->data);
            if (!$payHistoryS->ret) {
                exit;
            }
        }

        $extra = array('open_id' => $openid);
        $wxPayUtil = new \WxPayUtil();
        if(!$wxPayUtil->init($orderR->data['mchid'])) {
            return  $this->output(new \ResultModel(false, '商户支付配置异常!'));
        }
        try {
            //发起支付请求获取支付凭据
            $charge = $wxPayUtil->createCharge($openid, $orderNo, '代收车费', json_encode($extra), $orderR->data['price'], $this->getCipmchidByMchid($orderR->data['mchid'])->data['ciphertext'], $orderR->data['create_time']);
            if(!$charge) {
                $ret->data = '支付失败，请线下付款';
                return $this->output($ret);
            }

            if($charge['return_code'] == 'SUCCESS') {

                # 更新订单支付方式
                $this->save('Order', array('order_id' => $orderR->data['order_id'], 'payway' => \CommonDefine::WECHAT_JSAPI));

                $ret->ret = true;
                $ret->data = json_decode($wxPayUtil->GetJsApiParameters($charge), true);
                return $this->output($ret);
            }
        } catch (\Exception $e) {
            \Log::write($e->getCode().$e->getMessage());
            $ret->data = $e->getCode().$e->getMessage();
            return $this->output($ret);
        }

        $ret->data = '支付失败，请线下付款';
        return $this->output($ret);
    }

    /**
     * 分台获取司机订单统计信息
     * @param int $driver_id 司机id
     */
    public function doGetDriverStatistics($driver_id)
    {
        if(!$driver_id) {
            return $this->output(new \ResultModel(false, '参数错误'));
        }
        $driverR = $this->find(array('driver_id' => $driver_id), 'Driver');
        if(!$driverR->ret) {
            return $this->output(new \ResultModel(false, '账号异常请重新登录'));
        }

        $currentAppointWhere = array(
            'appoint' => 1,
            'driver_id' => $driver_id,
            'train_id' => $driverR->data['current_train_id'],
            'state'  => array('in','1,2,3,4,5'),
        );
        $currentAppointCount = $this->count($currentAppointWhere, 'Order');

        $appointWhere = array(
            'appoint' => 1,
            'driver_id' => $driver_id
        );
        $appointCount = $this->count($appointWhere, 'Order');

        $currentOverWhere = array(
            'driver_id' => $driver_id,
            'state' => array('in', '2,3,4,5,6'),
            'train_id' => $driverR->data['current_train_id']
        );

        $currentOverCount = $this->count($currentOverWhere, 'Order');

        $todayOverWhere = array(
            'driver_id' => $driver_id,
            'state' => array('in', '6'),
//            'train_id' => $driverR->data['current_train_id'],
            'update_time' => array('between' => array(date('Y-m-d 00:00:00'), date('Y-m-d 23:59:59'))),
        );

        $todayOverCount = $this->count($todayOverWhere, 'Order');


        $overWhere = array(
            'driver_id' => $driver_id,
            'state' => array('in', '5,6'),
        );

        $overCount = $this->count($overWhere, 'Order');

        $data = array(
            'current_appoint_count' => $currentAppointCount->ret ? $currentAppointCount->data : 0,
            'current_over_count' => $currentOverCount->ret ? $currentOverCount->data : 0,
            'today_over_count' => $todayOverCount->ret ? $todayOverCount->data : 0,
            'appoint_count' => $appointCount->ret ? $appointCount->data : 0,
            'over_count' => $overCount->ret ? $overCount->data : 0,
        );
        $r = new \ResultModel(true, $data);

        return $this->output($r);
    }

}

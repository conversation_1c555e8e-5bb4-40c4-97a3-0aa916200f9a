<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * 退款模型
 *
 * <AUTHOR>
 */
class YyRefundsModel extends Model
{
    protected $_auto = array(
        array('create_time', 'get_current_time', Model::MODEL_INSERT, 'function'),
        array('update_time', 'get_current_time', Model::MODEL_BOTH, 'function'),
        array('refund_no', '', '转账订单编号已存在', Model::EXISTS_VALIDATE, 'unique'),
    );

    protected $_validate = array(
        array('status', array(1, 2, 3, 4), '转账状态不正确', Model::EXISTS_VALIDATE, 'in'),
    );
}

?>

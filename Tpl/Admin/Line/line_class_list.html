<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <title>班车管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1,  minimum-scale=1, user-scalable=yes, viewport-fit=cover">
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/base.css" />
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/page.css" />
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/lib/pagination.css" />
    <script type="text/javascript" src="/Theme/Admin/layui/layui.all.js"></script>
    <link rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/kalendae.css" type="text/css" charset="utf-8">
    <script src="{$FILE_ROOT}Theme/Admin/kalendae.standalone.js" type="text/javascript" charset="utf-8"></script>
    <style type="text/css">
        .button_1 {
            margin: 5px;
            padding: 5px 10px;
            background-color: #00b7ee;
            color: #fff;
            border: 1px solid #00b7ee;
            border-radius: 4px;
        }

        .search_box input,
        select {
            line-height: 26px;
            margin-right: 25px;
            margin-bottom: 15px;
            width: 150px;
            height: 26px;
        }

        .paiban {
            position: fixed;
            background-color: #0a0a0a;
            width: 100%;
            height: 100%;
            z-index: 99999;
            opacity: 0.4;
            top: 0;
            left: 0;
        }

        .paiban1 {
            position: fixed;
            background-color: #0a0a0a;
            width: 100%;
            height: 100%;
            z-index: 99999;
            opacity: 0.4;
            top: 0;
            left: 0;
        }

        .show_box {
            background-color: #fff;
            width: 45%;
            position: fixed;
            z-index: 99999;
            height: 750px;
            padding: 0 10px;
            text-align: left;
            left: 0;
            right: 0;
            top: 100px;
            bottom: 0;
            margin: auto;
            overflow-y: scroll;
            border-radius: 5px;
            box-shadow: 0 0 25px #190202;
        }

        @media (max-width: 768px) {
            .show_box {
                width: 95%;
                height: auto;
                top: 10px;
                bottom: 10px;
                margin: 0 auto;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            .show_box h1 {
                font-size: 18px;
            }

            .show_box p {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .show_box {
                padding: 10px;
            }

            .show_box h3 {
                font-size: 16px;
            }

            .show_box p {
                font-size: 17px;
                line-height: 27px;

            }
        }

        @media (max-width: 768px) {
            .driver_list {
                display: block;
            }

            .driVal {
                width: 100%;
                margin-bottom: 10px;
            }
        }

        body {
            font-size: 16px;
        }

        @media (max-width: 768px) {
            body {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            body {
                font-size: 12px;
            }
        }

        .show_box input {
            margin-left: 5px;
        }

        .driver-label input,
        .driver-sort input {
            margin-left: 0;
        }

        .show_box1 {
            background-color: #fff;
            width: 400px;
            position: fixed;
            z-index: 999999;
            left: 38%;
            top: 25%;
            padding: 0 20px;
            text-align: left;
            display: none
        }

        .show_box p {
            margin: 5px 0 10px 10px;
        }

        .driVal {
            margin: 5px;
            float: left;
            border: 1px #d1d1d1 solid;
            padding: 5px;
        }

        #LineClass td {
            padding: 10px 10px;
        }

        .dispatch-config-container {
            border: 1px dashed #afa7a7;
            margin-top: 10px;
            padding: 5px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .driver-group-container {
            padding: 0 10px;
        }

        .ferry_tips {
            padding-left: 30px;
            width: 830px;
            line-height: 30px;
            color: #707070;
        }

        .grey_color {
            color: #707070;
        }

        .b_schedule_btn {
            margin: 5px;
            padding: 5px 10px;
            background-color: #00b7ee;
            color: #fff;
            border: 1px solid #00b7ee;
            border-radius: 4px;
            display: inline-block;
        }

        .td_better {
            vertical-align: baseline;
            /* min-width: 200px; */
        }

        td a:hover {
            color: blue;
            text-decoration: underline;
        }

        .line_show {
            background-color: #3399ff;
            color: #fff;
            padding: 0px 4px;
            border-radius: 2px;
        }

        .line_show2 {
            background-color: #3399ff;
            color: #fff;
            padding: 2px 6px;
            border-radius: 2px;
        }

        .line_show2_fixed {
            background-color: red;
            color: #fff;
            padding: 2px 6px;
            border-radius: 2px;
        }

        .line_direction_up {
            background-color: #52c41a;
            color: #fff;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 10px;
            display: inline-block;
        }

        .line_direction_down {
            background-color: #fa8c16;
            color: #fff;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 10px;
            display: inline-block;
        }

        .starting_time_fixed_block {
            margin: 5px 0;
        }

        .fz12 {
            font-size: 12px;
        }

        .blue_color {
            background-color: #00A14C;
        }

        .red_color {
            background-color: red;
            border-color: red;
        }

        .notice {
            margin: 10px 28px;
            color: #e89c9cee;
            padding: 20px 0 10px 10px;
        }

        .custom_seats {
            display: none;
        }

        .seats_layout {
            display: none;
        }

        .mode-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }

        .radio-group {
            display: flex;
            margin-top: 10px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            color: #333;
        }

        .radio-label input[type="radio"] {
            margin-right: 8px;
            cursor: pointer;
        }

        .radio-label .radio-text {
            font-size: 14px;
            color: #555;
        }

        .radio-label input[type="radio"]:checked+.radio-text {
            font-weight: bold;
            color: #00A14C;
        }

        .note-section {
            margin: 10px;
            font-size: 14px;
            color: #666;
            background-color: #fff3f3;
            padding: 10px;
            border-left: 4px solid #ff5e57;
            border-radius: 5px;
        }

        .note-text {
            margin: 0;
            line-height: 1.6;
        }

        .note-text span {
            color: red;
            font-size: 10px;
        }

        .operation-bar {
            background-color: #f9f9f9;
            padding: 10px 20px;
            margin-bottom: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .operation-buttons {
            display: flex;
        }

        .operation-buttons .button_1 {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid transparent;
            transition: all 0.2s ease-in-out;
        }

        .operation-buttons .button_1:hover {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .operation-buttons .blue_color {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .operation-buttons .red_color {
            background-color: #dc3545;
            color: #fff;
            border-color: #dc3545;
        }

        .search-bar {
            background-color: #f9f9f9;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .search-box label {
            font-size: 14px;
            font-weight: bold;
            margin-right: 5px;
            color: #333;
        }

        .search-box input,
        .search-box select {
            padding: 5px 10px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .search-box input:focus,
        .search-box select:focus {
            border-color: #007bff;
            outline: none;
        }

        .search-buttons {
            margin-top: 10px;
            display: flex;
        }

        .search-buttons .button_1 {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid transparent;
            transition: all 0.2s ease-in-out;
        }

        .search-buttons .button_1:hover {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .search-buttons .button_1.green {
            background-color: #28a745;
            color: #fff;
            border-color: #28a745;
        }

        .search-buttons .button_1.red {
            background-color: #dc3545;
            color: #fff;
            border-color: #dc3545;
        }

        .table-container {
            margin: 20px 0;
            overflow-x: auto;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .list_tb {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 14px;
            text-align: left;
        }

        .list_tb thead th {
            background: linear-gradient(to right, #00b4db, #0083b0);
            color: #fff;
            text-align: center;
            font-weight: bold;
            padding: 15px;
            border-bottom: 3px solid #007bff;
            font-size: 16px;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .list_tb tbody tr:nth-child(odd) {
            background-color: #fafafa;
        }

        .list_tb tbody tr:nth-child(even) {
            background-color: #ffffff;
        }

        .list_tb tbody tr:hover {
            background-color: #bae7ff;
            border: 1px solid #91d5ff;
            color: #0050b3;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s, color 0.3s, border 0.3s, box-shadow 0.3s;
        }

        .list_tb tbody tr:nth-child(odd):hover {
            background-color: #a3daff;
            border-color: #69c0ff;
        }

        .list_tb tbody tr:nth-child(even):hover {
            background-color: #d6f0ff;
            border-color: #91d5ff;
        }

        .list_tb .button_1 {
            display: inline-block;
            margin-right: 5px;
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
            text-decoration: none;
            color: #fff;
            text-align: center;
            transition: all 0.2s;
        }

        .highlight-row {
            background-color: #e6f7ff;
            color: #0050b3;
            border: 1px solid #91d5ff;
            transition: background-color 0.5s ease;
            font-weight: 500;
            ;
        }

        .highlight-row:hover {
            background-color: #bae7ff;
            color: #0050b3;
        }

        .action-model {
            padding: 4px 14px;
            border-radius: 4px;
            cursor: pointer;
            vertical-align: middle;
        }

        .group-container {
            margin-bottom: 5px;
            padding: 0 5px 5px 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .group-container .mode-title {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .group-drivers {
            display: flex;
            flex-wrap: wrap;
        }

        .driVal {
            display: flex;
            align-items: center;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 35%;
        }

        .driver-label {
            font-size: 12px;
            font-weight: 500;
            color: #333;
            margin-right: 15px;
        }

        .driver-sort {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #555;
            white-space: nowrap;
        }

        .driver-sort input.level {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 5px;
            font-size: 14px;
            width: 60px;
            text-align: center;
        }

        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .group-header .select-all,
        .group-header .deselect-all {
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 5px;
            cursor: pointer;
            font-size: 10px;
            margin-left: 2px;
        }

        .group-header .select-all:hover,
        .group-header .deselect-all:hover {
            background-color: #0056b3;
        }

        .layui-tab-title::-webkit-scrollbar {
            height: 2px;
        }

        .layui-tab-title::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .layui-tab-title::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }

        .layui-tab-title::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .layui-tab-title {
            scrollbar-width: thin;
            scrollbar-color: #888 #f1f1f1;
        }

        .conlayui-tab-titletent:hover {
            scrollbar-color: #555 #f1f1f1;
        }

        .layui-tab-title {
            overflow-x: auto !important;
            height: auto !important;
            border-bottom: none !important;
            overflow-y: hidden !important;
        }

        .layui-tab-title {
            overflow-x: auto !important;
            scrollbar-width: auto; /* Firefox */
        }

        .layui-tab-title::-webkit-scrollbar {
            height: 10px; /* 滚动条高度（水平滚动条） */
        }

        .layui-tab-title::-webkit-scrollbar-track {
            background-color: #f1f1f1; /* 滚动条轨道颜色 */
        }

        .layui-tab-title::-webkit-scrollbar-thumb {
            background-color: #888; /* 滚动条滑块颜色 */
            border-radius: 4px; /* 滚动条圆角 */
        }

        .layui-tab .layui-tab-title .layui-this {
            color: #1366ec !important;
            font-weight: bolder;
            font-size: 15px;

        }

        .layui-tab-brief>.layui-tab-more li.layui-this:after,
        .layui-tab-brief>.layui-tab-title .layui-this:after {
            border-bottom: 5px solid #1366ec !important;
        }

        @media screen and (max-width: 768px) {
            .driVal {
                width: 100%;
            }

            .layui-tab .layui-tab-title li {
                font-size: 16px !important;
            }

            .layui-layer-dialog {
                left: 0 !important;
                right: 0 !important;
                margin: auto !important;
            }
        }

        .loading {
            position: relative;
            min-height: 100px;
        }

        .loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 10;
        }

        .loading::after {
            content: '加载司机中..';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 11;
            font-size: 16px;
            color: #555;
        }

        .form-container {
            padding: 5px 10px 0 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        .form-input {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100px;
            margin-left: 10px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-input:focus {
            border-color: #007bff;
            outline: none;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #555;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 5px;
            cursor: pointer;
        }

        .checkbox-label:hover {
            color: #007bff;
        }

        .auto-kal {
            width: 400px;
        }

        .mg-15 {
            margin: 10px 0 10px 10px;
        }
    </style>
</head>

<body class="">
    <include file="Admin:Modules:header" />
    <div class="main members passager">
        <div class="l_main">
            <include file="Admin:Modules:sidenav" />
        </div>
        <div class="r_main">
            <div class="bg-ff" style="position: relative;">
                <div class="sbMenu" id="T_mb">
                    <include file="Admin:Modules:linenav" />
                </div>
                <div class="wiget_box">
                    <!-- 操作DIV -->
                    <div style="text-align: left">
                        <button onclick="location.href='{$web_root}line_class'"
                            class="button_1 button_phone button_phone_flex">添加班线</button>
                        <button onclick="location.href='{$web_root}line_class_list'"
                            class="button_1 button_phone_plain button_phone_flex">全部班线</button>
                        <button onclick="location.href='{$web_root}line_class_train_list'"
                            class="button_1 blue_color button_phone_plain button_phone_flex">全部班次</button>
                        <a type="button" class="button_1 b_schedule_btn red_color button_phone_plain button_phone_flex"
                            href="{$web_root}/line_class_schedule?" target="_blank">班线排班总览表</a>
                        <a type="button" class="button_1 b_schedule_btn  button_phone_plain button_phone_flex"
                            href="{$mch_url_schedule}">已排班时序表</a>
                        <a type="button" class="button_1 b_schedule_btn  button_phone_plain button_phone_flex"
                            href="{$mch_url_dispatching}">智能调度</a>
                    </div>
                    <!-- 查询DIV -->
                    <div style="text-align: left;margin: 10px 0">
                        <div class="search_box">
                            <div class="item-search">
                                班线ID：<input id="line_class_train_no" type="text">
                            </div>
                            <div class="item-search">
                                班线状态：
                                <select id="status">
                                    <option value="3">全部</option>
                                    <option value="1">正在运营</option>
                                    <option value="0">已经停运</option>
                                </select>
                            </div>
                        </div>
                        <div class="collapse-hide-box">
                            <div class="search-box">
                                <div class="item-search">
                                    <label for="support_points_deduction">积分抵扣：</label>
                                    <select id="support_points_deduction">
                                        <option value="3">全部</option>
                                        <option value="1">支持积分抵扣</option>
                                        <option value="0">不支持积分抵扣</option>
                                    </select>
                                </div>
                                <div class="item-search">
                                    <label for="search_branchid">所属分台：</label>
                                    <select id="search_branchid">
                                        <option value="0">- 请选择分台 -</option>
                                        <eq name="admin_group" value="branch_admin">
                                            <ex:select model="Admin"
                                                where="admin_id=$branchid and is_del = 0 and branch_type = 0 and is_freeze = 0"
                                                item="ma">
                                                <option value="{$ma.admin_id}" rel="{$ma.admin_id}">{$ma.mchname}
                                                </option>
                                            </ex:select>
                                            <else />
                                            <ex:select model="Admin"
                                                where="parent_admin_id=$mchid and is_del = 0 and branch_type = 0 and is_freeze = 0"
                                                item="ma">
                                                <option value="{$ma.admin_id}" rel="{$ma.admin_id}">{$ma.mchname}
                                                </option>
                                            </ex:select>
                                        </eq>
                                    </select>
                                </div>
                                <div class="item-search">
                                    <label for="start_time_type">发车时间类型：</label>
                                    <select id="start_time_type">
                                        <option value="0">全部</option>
                                        <option value="1">固定发车时间</option>
                                        <option value="2">滚动发车时段</option>
                                    </select>
                                </div>
                                <div class="item-search">
                                    <label for="start_name">出发站名称：</label>
                                    <input id="start_name" type="text" placeholder="请输入出发站">
                                </div>
                                <div class="item-search">
                                    <label for="end_name">目的站名称：</label>
                                    <input id="end_name" type="text" placeholder="请输入目的站">
                                </div>
                                <div class="item-search">
                                    <label for="line_category">场景分类：</label>
                                    <select id="line_category">
                                        <option value="0">全部</option>
                                        <volist name="line_category" id="vo">
                                            <option value="{$vo['id']}">{$vo['title']}</option>
                                        </volist>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="search_box flex-end-container">
                            <button id="doSearchact" class="button_1 button_phone button_phone_flex">搜索</button>
                            <div class="filter-collapse" onclick="collapse()">
                                <span class="text-view">展开</span>
                                <span role="img" aria-label="down" class="anticon-collapse anticon-down"><svg
                                        viewBox="64 64 896 896" focusable="false" data-icon="down" width="1em"
                                        height="1em" fill="currentColor" aria-hidden="true">
                                        <path
                                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z">
                                        </path>
                                    </svg></span>
                            </div>
                        </div>


                        <div class="search-buttons">
                            <button id="doBatchOperation" class="button_1 green">批量运行</button>
                            <button id="doBatchShutdown" class="button_1 red">批量停运</button>
                            <button id="doBatchDel" class="button_1 red">批量删除</button>
                        </div>
                    </div>
                    <div class="over-phone-show-box">

                        <!-- 表格DIV -->
                        <table class="list_tb hgstemp table-container" id="LineClass">
                            <tr>
                                <th width="70"><label><input id="quanxuan" type="checkbox">全选</label></th>
                                <th width="70">班线ID</th>
                                <th width="150">出发地</th>
                                <th width="150">目的地</th>
                                <th width="180">发车时间</th>
                                <th width="150">当前排班</th>
                                <th width="120">选座?</th>
                                <th width="120">价格（元/人）</th>
                                <th width="120">场景分类</th>
                                <th width="100">所属分台</th>
                                <th width="120">班线状态</th>
                                <th width="200">操作</th>
                            </tr>
                            <tr class="waiting">
                                <td colspan="11">
                                    <div class="loading_bar">
                                        <img src="{$FILE_ROOT}Theme/images/Admin/loading.gif" alt="" class="lf">
                                        <span class="lf">正在努力加载数据···</span>
                                        <p class="clr"></p>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- 分页DIV -->
                    <div class="pages pagination" id="pagination"></div>
                    <p class="clr"></p>

                </div>
            </div>
        </div>
        <p class="clr"></p>
    </div>

    <!-- paiban -->
    <div style="display: none" class="paiban">
    </div>

    <!-- paiban1 -->
    <div style="display: none" class="paiban1">
    </div>

    <!-- show_box1 -->
    <div class="show_box1">
        <h2 style="padding: 15px 0;text-align: center">出发城市：成都市*天府五街</h2>
        <hr>
        <div class="line_class_point">
        </div>
        <button onclick="showPbBox1(1)"
            style="padding: 7px 35px;background-color: #00b7ee;;border: 1px #00b7ee solid;border-radius: 7px;margin: 10px 28%;color: #fff;cursor: pointer;width: 150px">关闭</button>
    </div>
    <!-- show_box -->

    <div class="show_box" style="display: none">
        <h3 style="color: #00a0e9;padding: 10px 0;text-align: center">定制班线车排班</h3>
        <hr>
        <!-- 排班方式 -->
        <p>
            <b>排班方式:</b>
            <label><input onclick="showPb($(this))" idx="1" checked name="scheduling_type" value="2"
                    type="radio">自动排班</label>
            <label><input onclick="showPb($(this))" idx="2" name="scheduling_type" value="1" type="radio">手动排班</label>
        </p>
        <!-- 排班方式 -->

        <!-- 自动排班 -->
        <div id="auto">
            <form action="" class="form-container">
                <p>
                    <b>预售天数:</b>
                    <input name="pre_sale_time" value="30" type="number" min="1" max="360" class="form-input" />
                </p>

                <div class="checkbox-group mg-15">
                    <b>不排班:</b>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="1" type="checkbox" />
                        <span>周一</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="2" type="checkbox" />
                        <span>周二</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="3" type="checkbox" />
                        <span>周三</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="4" type="checkbox" />
                        <span>周四</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="5" type="checkbox" />
                        <span>周五</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="6" type="checkbox" />
                        <span>周六</span>
                    </label>
                    <label class="checkbox-label">
                        <input name="exclude_scheduling[]" value="0" type="checkbox" />
                        <span>周日</span>
                    </label>
                </div>

                <p class="">
                    <b>售票数:</b>
                    <label><input onclick="tonggleSeatLayout($(this), 'auto')" idx="total_driver_seats" checked
                            name="ticket_sale_rule" value="total_driver_seats" type="radio">按可排班司机车座总和</label>
                    <label><input onclick="tonggleSeatLayout($(this), 'auto')" idx="custom_seats"
                            name="ticket_sale_rule" value="custom_seats" type="radio">使用自定义座位数</label>
                    <span id="auto_custom_seats" class="custom_seats"><input name="custom_seats" value="0" type="number"
                            max="360" style="margin:0; padding-left: 5px; font-weight: bold; width: 48px;">座</span>
                    <label><input onclick="tonggleSeatLayout($(this), 'auto')" idx="unlimited_seats"
                            name="ticket_sale_rule" value="unlimited_seats" type="radio">不限座位数</label>
                </p>

                <p class="">
                    <b>是否按班次司机轮班派单：</b>
                    <label>
                        <input type="checkbox" name="enable_driver_cycle" value="1">
                        启用
                    </label>
                    <small style="color: red;">启用后，系统会根据设置的轮班规则每日调整司机的派单顺序。例如，第一天的派单顺序为司机1/2/3/4，第二天则调整为2/3/4/1，第三天为3/4/1/2，以此类推。此功能有助于实现司机派单的公平性。</small>
                </p>
                
                <p><b>排班司机:</b></p>
                <div class="layui-tab layui-tab-card time-driver-view-content" lay-filter="tab">
                </div>
                <p style="clear: both" class="pb_tips"></p>
                <div id="is_start_ferry_div"></div>

                <p style="clear: both" class="pb_tips"></p>

                <div id="is_end_ferry_div"></div>

                <!-- 派单方式布局 -->
                <div class="wiget_box wiget_box_bg formdata_p dispatch-config-container">
                    <table class="lr_table">
                        <tr>
                            <td class="rtd">
                                <div class="runtime">
                                    <div class="fun_title dispatch-mode-container">
                                        <p class="mode-title">
                                            班线车（或城际网约车）派单方式:
                                        </p>
                                        <div class="radio-group">
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="1" <if
                                                    condition='$dispatch_mode eq 1'>checked</if> >
                                                <span class="radio-text">班线车手动派单</span>
                                            </label>
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="0" <if
                                                    condition='$dispatch_mode eq 0'>checked</if> >
                                                <span class="radio-text">班线车自动派单-按顺序</span>
                                            </label>
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="2" <if
                                                    condition='$dispatch_mode eq 2'>checked</if> >
                                                <span class="radio-text">班线车自动派单-按距离及顺序</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div class="note-section">
                        <p class="note-text">
                            <span>
                                注意：<br />
                                - 切换为“手动派单”后，所有班线车（或城际网约车）订单将由线路管理员手动分配。<br />
                                - 切换为“自动派单-按顺序”后，系统将根据司机班次的顺序依次分配订单。<br />
                                - 切换为“自动派单-按距离及顺序”后：<br />
                                &nbsp;&nbsp;&nbsp;1. 当前时间与订单发车时间 ≤ 2 小时时，系统将优先根据司机与上车点的距离从近到远进行分配；<br />
                                &nbsp;&nbsp;&nbsp;2. 其余情况下，系统仍将按照司机班次顺序依次分配订单。<br />
                            </span>
                        </p>
                    </div>
                </div>
                <!-- 派单方式布局 -->

                <p style="clear: both">
                    <button type="button" onclick="showPbBox(1)"
                        style="padding: 7px 35px;background-color: #fff;;border: 1px #333 solid;border-radius: 7px;margin: 5px;cursor: pointer">取消排班</button>
                    <button type="button" onclick="submitForm($('#auto form').serialize(),'auto')"
                        style="padding: 7px 35px;background-color: #00b7ee;;border: 1px #00b7ee solid;border-radius: 7px;margin: 5px;color: #fff;cursor: pointer">确定排班</button>
                </p>
            </form>
        </div>
        <!--  手动排班 -->
        <div style="display: none" id="Manual" class="manual form-container">
            <form action="">
                <p>
                    <b>排班日期:</b>
                    <input AUTOCOMPLETE="OFF" name="pre_sale_time" class="auto-kal form-input"
                        data-kal="mode:'multiple'" type="text">
                </p>
                <p class="">
                    <b>售票数:</b>
                    <label><input onclick="tonggleSeatLayout($(this), 'manual')" idx="total_driver_seats" checked
                            name="ticket_sale_rule" value="total_driver_seats" type="radio">按可排班司机车座总和</label>
                    <label><input onclick="tonggleSeatLayout($(this), 'manual')" idx="custom_seats"
                            name="ticket_sale_rule" value="custom_seats" type="radio">使用自定义座位数</label>
                    <span id="manual_custom_seats" class="custom_seats"><input name="custom_seats" value="0"
                            type="number" max="360"
                            style="margin:0; padding-left: 5px; font-weight: bold; width: 48px;">座</span>
                    <label><input onclick="tonggleSeatLayout($(this), 'manual')" idx="unlimited_seats"
                            name="ticket_sale_rule" value="unlimited_seats" type="radio">不限座位数</label>
                </p>
                <p><b>排班司机:</b></p>
                <div class="layui-tab layui-tab-card time-driver-view-content" lay-filter="tab">
                </div>
                <p style="clear: both" class="pb_tips"></p>

                <div id="is_start_ferry_div"></div>

                <p style="clear: both" class="pb_tips"></p>

                <div id="is_end_ferry_div"></div>


                <!-- 派单方式布局 -->
                <div class="wiget_box wiget_box_bg formdata_p dispatch-config-container">
                    <table class="lr_table">
                        <tr>
                            <td class="rtd">
                                <div class="runtime">
                                    <div class="fun_title dispatch-mode-container">
                                        <p class="mode-title">
                                            班线车（或城际网约车）派单方式:
                                        </p>
                                        <div class="radio-group">
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="1" <if
                                                    condition='$dispatch_mode eq 1'>checked</if> >
                                                <span class="radio-text">班线车手动派单</span>
                                            </label>
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="0" <if
                                                    condition='$dispatch_mode eq 0'>checked</if> >
                                                <span class="radio-text">班线车自动派单-按顺序</span>
                                            </label>
                                            <label class="radio-label">
                                                <input type="radio" name="dispatch_mode"
                                                    class="line_class_appoint_config" value="2" <if
                                                    condition='$dispatch_mode eq 2'>checked</if> >
                                                <span class="radio-text">班线车自动派单-按距离及顺序</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div class="note-section">
                        <p class="note-text">
                            <span style="color: red">
                                注意：<br />
                                - 切换为“手动派单”后，所有班线车（或城际网约车）订单将由线路管理员手动分配。<br />
                                - 切换为“自动派单-按顺序”后，系统将根据司机班次的顺序依次分配订单。<br />
                                - 切换为“自动派单-按距离及顺序”后：<br />
                                &nbsp;&nbsp;&nbsp;1. 当前时间与订单发车时间 ≤ 2 小时时，系统将优先根据司机与上车点的距离从近到远进行分配；<br />
                                &nbsp;&nbsp;&nbsp;2. 其余情况下，系统仍将按照司机班次顺序依次分配订单。<br />
                            </span>
                        </p>
                    </div>
                </div>
                <!-- 派单方式布局 -->
                <p style="clear: both">
                    <button type="button" onclick="showPbBox(1)"
                        style="padding: 7px 35px;background-color: #fff;;border: 1px #333 solid;border-radius: 7px;margin: 25px;cursor: pointer">取消排班</button>
                    <button type="button" onclick="submitForm($('#Manual form').serialize(),'Manual')"
                        style="padding: 7px 35px;background-color: #00b7ee;;border: 1px #00b7ee solid;border-radius: 7px;margin: 25px;color: #fff;cursor: pointer">确定排班</button>
                </p>
            </form>
        </div>
        <!--  手动排班 -->
        <input type="hidden" id="line_class_id">
    </div>

    <script type="text/javascript" src="{$FILE_ROOT}Theme/lib/jquery-1.8.3.min.js"></script>
    <script type="text/javascript">
        // 线路方向标签渲染函数
        function renderDirectionLabel(direction) {
            if (direction == 1 || direction == '上行') {
                return '<span class="line_direction_up">上行</span>';
            } else if (direction == 2 || direction == '下行') {
                return '<span class="line_direction_down">下行</span>';
            }
            return '';
        }

        $(document).on('click', '.layui-tab-title li', function () {
            $('.time-driver-view-content .layui-show').addClass('loading')
            $('.time-driver-view-content .layui-show').siblings().removeClass('loading');
            setTimeout(function () {
                $('.time-driver-view-content .layui-show').removeClass('loading')
            }, 350)

        })






        function collapse() {
            let html_ = $('.filter-collapse .text-view').html()
            $('.filter-collapse .text-view').html(html_ === '展开' ? '收起' : '展开')
            $('.anticon-collapse').toggleClass('anticon-up')
            $('.collapse-hide-box').toggleClass('collapse-show-box')

        }



        function setLevel(obj) {
            _obj = $(obj).parent().next();
            if ($(obj).is(':checked')) {
                _obj.attr({ "name": 'level[]' })
            } else {
                _obj.removeAttr("name")
            }
        }
        function showPbBox(i, obj, is_seat_selection, branchname) {
            if (i == 1) {
                $('.show_box').hide();
                $('.paiban').hide();
            } else if (i == 2) {
                $('#auto form')[0].reset();
                $('#Manual form')[0].reset();
                $('.level').removeAttr("name");
                if (obj) {
                    $('#line_class_id').val($(obj).attr('ids'));
                }

                // 动态渲染出发地与目的地快车-摆渡车选项 start 
                var startFerryPricings = $.parseJSON($(obj).attr('tp_is_start_ferry_pricing'))
                var endFerryPricings = $.parseJSON($(obj).attr('tp_is_end_ferry_pricing'))
                var isStartFerry = $.parseJSON($(obj).attr('tp_is_start_ferry'))
                var isEndFerry = $.parseJSON($(obj).attr('tp_is_end_ferry'))
                if (isStartFerry == 0) {
                    // 出发地不支持摆渡车
                    $("div[id='is_start_ferry_div']").html('<p class="notice">未配置出发地摆渡车，请点击<a href="/line_fast_list" target="_blank">这里</a>设置。</p>');
                } else if (isStartFerry > 0) {
                    if (startFerryPricings && startFerryPricings.length > 0) {
                        var startFeeryPricingHtml = `<p>
                                &nbsp;&nbsp;&nbsp;&nbsp;<b>选择出发地计价规则:</b>`
                        for (var key in startFerryPricings) {
                            startFeeryPricingHtml += `<label><input name="start_pricing" value="${startFerryPricings[key]['id']}" type="radio"
                                                class="grey_color">${startFerryPricings[key]['title']}</label>`
                        }
                        startFeeryPricingHtml + '</p>'
                    } else {
                        var startFeeryPricingHtml = `<p>
                                                        &nbsp;&nbsp;&nbsp;&nbsp;<b>选择出发地计价规则:</b>
                                                            <a href="{$WEB_ROOT}line_pricing"
                                                                class="return-lineclass-list b_schedule_btn fz12">添加计价规则</a>
                                                    </p>`
                    }
                    var ferryStartDiv = `<input name="is_start_ferry" type="checkbox" value="1"><b> 启用出发地摆渡车（也称分流车）</b>
                                            <div class="ferry_tips">
                                                开启后，乘客下此路线班次订单将产生出发地摆渡或分流子订单，摆渡车订单通过“快车”模块实现。计价规则使用快车城市计价规则。司机订单分类标示【摆渡车】。快车司机可以查看订单，进行接送。开启此功能需要设置车场（用于计算分流车接送点位），否则无法开启。
                                            </div>

                                            <!-- 选择计价规则 start-->
                                            <div id="is_start_ferry_div_pricing">${startFeeryPricingHtml}</div>
                                            <!-- 选择计价规则 end-->

                                            <!-- 司机派单规则 start -->
                                            <p>
                                                &nbsp;&nbsp;&nbsp;&nbsp;<b>出发地摆渡车[接/送] :</b>
                                                <label><input idx="1" checked name="start_origin_line_driver_enabled" value="0" checked="true"
                                                        type="radio" class="grey_color">单独指派摆渡车/分流车司机接送</label>
                                                <label><input idx="2" name="start_origin_line_driver_enabled" value="1" type="radio"
                                                        class="grey_color">本主路线司机接送</label>
                                            </p>
                                            <!-- 司机派单规则 end -->`
                    $("div[id='is_start_ferry_div']").html(ferryStartDiv)
                }

                if (isEndFerry == 0) {
                    // 目的地不支持摆渡车
                    $("div[id='is_end_ferry_div']").html('<p class="notice">未配置目的地摆渡车，请点击<a href="/line_fast_list" target="_blank">这里</a>设置。</p>');
                } else if (isEndFerry > 0) {
                    if (endFerryPricings && endFerryPricings.length > 0) {
                        var endFeeryPricingHtml = `<p>
                                &nbsp;&nbsp;&nbsp;&nbsp;<b>选择目的地计价规则:</b>`
                        for (var key in endFerryPricings) {
                            endFeeryPricingHtml += `<label><input name="end_pricing" value="${endFerryPricings[key]['id']}" type="radio"
                                                class="grey_color">${endFerryPricings[key]['title']}</label>`
                        }
                        endFeeryPricingHtml + '</p>'
                    } else {
                        var endFeeryPricingHtml = `<p>
                                                        &nbsp;&nbsp;&nbsp;&nbsp;<b>选择目的地计价规则:</b>
                                                            <a href="{$WEB_ROOT}line_pricing"
                                                                class="return-lineclass-list b_schedule_btn fz12">添加计价规则</a>
                                                    </p>`
                    }
                    var ferryEndDiv = `<input name="is_end_ferry" type="checkbox" value="1"><b> 启用目的地摆渡车（也称分流车）</b>
                                        <div class="ferry_tips">
                                            开启后，乘客下此路线班次订单将产生目的地摆渡或分流子订单，摆渡车订单通过“快车”模块实现。计价规则使用快车城市计价规则。司机订单分类标示【摆渡车】。快车司机可以查看订单，进行接送。开启此功能需要设置车场（用于计算分流车接送点位），否则无法开启。
                                        </div>

                                        <!-- 选择计价规则 start-->
                                        <div id="is_end_ferry_div_pricing">${endFeeryPricingHtml}</div>
                                        <!-- 选择计价规则 end-->

                                        <!-- 司机派单规则 start -->
                                        <p>
                                            &nbsp;&nbsp;&nbsp;&nbsp;<b>目的地摆渡车[接/送] :</b>
                                            <label><input idx="1" checked name="end_origin_line_driver_enabled" value="0" checked="true"
                                                    type="radio" class="grey_color">单独指派摆渡车/分流车司机接送</label>
                                            <label><input idx="2" name="end_origin_line_driver_enabled" value="1" type="radio"
                                                    class="grey_color">本主路线司机接送</label>
                                        </p>
                                        <!-- 司机派单规则 end -->`
                    $("div[id='is_end_ferry_div']").html(ferryEndDiv)
                }
                // 动态渲染出发地与目的地快车-摆渡车选项 end

                var starting_times = $(obj).attr('starting_time').split(',');
                console.log('obj~~~~~', starting_times)

                //排班司机 start
                var branchDrivers = $.parseJSON($(obj).attr('branch_drivers'));

                let times_dom = ''
                let time_con = ''
                if (starting_times && starting_times.length) {
                    for (var i = 0; i < starting_times.length; i++) {
                        var starting_time = starting_times[i];
                        times_dom += `<li class="${i === 0 ? 'layui-this' : ''}">${starting_time} 班次</li>`
                        time_con += `<div class="layui-tab-item ${i === 0 ? "layui-show" : ""}">
                    <div class="driver_list" starting_time="${starting_time}">${render_driver(starting_time, branchDrivers, is_seat_selection, branchname)}</div>
                  </div>`
                    }
                }
                let laUi = `<ul class="layui-tab-title">
                ${times_dom}
                </ul>
                <div class="layui-tab-content">
                  ${time_con}
                </div>`



                // 更新司机列表内容
                // $('.driver_list').html(dirversHtml);
                $('.time-driver-view-content').html(laUi);
                //排班司机 end 

                if (is_seat_selection == 0) {
                    // 不支持选座
                    $('.tips').html('<span style="color: #FB3838;">注意：请勾选本班线排班司机，如需设定接订单顺序，请从小到大设置序号值</span>');
                    $(".seats_layout").css('display', 'inline')
                } else {
                    $(".seats_layout").css('display', 'none')
                    $('.tips').html('');
                }

                //根据线路类型获取司机列表
                $('.show_box').show();
                $('.paiban').show();
            } else if (i == 3) {
                if (LineClass_ids.length < 1) {
                    alert('请先选择班线！');
                    return false;
                }
                var line_class_ids = '';
                for (var index in LineClass_ids) {
                    line_class_ids += LineClass_ids[index] + ',';
                }
                line_class_ids = line_class_ids.slice(0, line_class_ids.length - 1);

                $('#auto form')[0].reset();
                $('#Manual form')[0].reset();
                $('.level').removeAttr("name");
                if (obj) {
                    $('#line_class_id').val(line_class_ids);
                }
                $('.show_box').show();
                $('.paiban').show();
            }
        }


        function render_driver(time_rang, branchDrivers, is_seat_selection, branchname) {

            var dirversHtml = '';
            if (branchDrivers.length > 0) {
                // 按照司机的分组名称进行分组
                var groupedDrivers = branchDrivers.reduce((groups, driver) => {
                    const groupName = driver.group_name || "未分组";
                    if (!groups[groupName]) {
                        groups[groupName] = [];
                    }
                    groups[groupName].push(driver);
                    return groups;
                }, {});

                dirversHtml += '<div class="driver-group-container">';
                for (var groupName in groupedDrivers) {
                    var driversInGroup = groupedDrivers[groupName];

                    // 添加新的分组容器
                    dirversHtml += `
                            <div class="group-container">
                                <div class="group-header">
                                    <p class="mode-title">
                                        司机分组名: ${groupName}`;
                    if (is_seat_selection == 0) {
                        // 非选座模式才显示全选和取消全选按钮
                        dirversHtml += `
                                        <button type="button" class="select-all" onclick="selectGroupDrivers(this, true)">全选</button>
                                        <button type="button" class="deselect-all" onclick="selectGroupDrivers(this, false)">取消全选</button>`;
                    }
                    dirversHtml += `
                                    </p>
                                </div>
                                <div class="group-drivers">`;

                    // 显示每个分组内的司机信息卡片
                    driversInGroup.forEach(driver => {
                        dirversHtml += `
                                <div class="driVal">
                                    <label class="driver-label">
                                        <input onchange="setLevel($(this))" name=driver_id_${time_rang}" value="${driver.driver_id}" ${is_seat_selection == 0 ? 'type="checkbox"' : 'type="radio"'}>
                                        ${driver.driver_id}-${driver.name}-${driver.car_tail_number} (${driver.total_seating}座)
                                    </label>
                                    <div class="driver-sort">
                                        排序
                                        <input class="level" name="level[]" min="0" value="0" type="number">
                                    </div>
                                </div>`;
                    });

                    dirversHtml += '</div></div>'; // 关闭当前分组
                }
                dirversHtml += '</div>'; // 关闭主容器
            } else {
                dirversHtml += `
                        <div class="note-section">
                            <p class="note-text">
                                <span style="color: red">
                                    注意：<br />
                                    （${branchname}） 当前分台下暂无司机，请先为该分台添加司机后再尝试操作。
                                </span>
                            </p>
                        </div>`;
            }
            return dirversHtml
        }


        function selectGroupDrivers(button, selectAll) {
            var groupContainer = $(button).closest('.group-container');
            var checkboxes = groupContainer.find('input[type="checkbox"]');

            checkboxes.prop('checked', selectAll); // 全选或取消全选
            checkboxes.each(function () {
                setLevel($(this)); // 调用现有逻辑
            });
        }

        function showPbBox1(i) {
            if (i == 1) {
                $('.show_box1').hide()
                $('.paiban1').hide()
            } else {
                $('.show_box1').show()
                $('.paiban1').show()
            }
        }

        /**
         * 显示排班
         * @param obj
         */
        function showPb(obj) {
            if ($(obj).attr('idx') == 1) {
                $('#auto').css({ "display": "block" })
                $('#Manual').css({ "display": "none" })
            } else {
                $('#auto').css({ "display": "none" })
                $('#Manual').css({ "display": "block" })
            }
        }

        /**
         * 显示座位数布局
         * @param obj
         */
        function tonggleSeatLayout(obj, tonggle) {
            var element = "#" + tonggle + '_custom_seats'
            switch ($(obj).attr('idx')) {
                case 'custom_seats':
                    $(element).css('display', 'inline')
                    break;
                default:
                    $(element).css('display', 'none')
                    break;
            }
        }


        var LineClass_ids = [];
        $(document).on('click', '#quanxuan', function () {
            if ($(this).is(":checked")) {
                $('#LineClass .ckeckebox_e').prop('checked', true);
                $('#LineClass .ckeckebox_e').each(function (k, v) {
                    if ($.inArray($(v).attr('ids'), LineClass_ids) < 0) {
                        LineClass_ids.push($(v).attr('ids'))
                    }
                })
            } else {
                $('#LineClass .ckeckebox_e').prop('checked', false);
                $('#LineClass .ckeckebox_e').each(function (k, v) {
                    if ($.inArray($(v).attr('ids'), LineClass_ids) >= 0) {
                        LineClass_ids.splice($.inArray($(v).attr('ids'), LineClass_ids), 1);
                    }

                })
            }
        })
        $(document).on('click', '#pagination a', function () {
            $('#quanxuan').prop('checked', false);
        })
        $(document).on('click', '#LineClass .ckeckebox_e', function () {
            if ($(this).is(":checked")) {
                if ($.inArray($(this).attr('ids')) < 0) {
                    LineClass_ids.push($(this).attr('ids'))
                }
            } else {
                if ($.inArray($(this).attr('ids'), LineClass_ids) >= 0) {
                    LineClass_ids.splice($.inArray($(this).attr('ids'), LineClass_ids), 1);
                }
            }
        })
    </script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/lib/jquery.pagination.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/base.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/line.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/page.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/md5.min.js"></script>
    <script type="text/javascript">
        function sss(obj) {
            $('.line_class_point').html('')
            $('.show_box1 h2').html(obj.attr('address'))
            showPbBox1(2)
            var s = {
                async: true,
                url: Path.web_path + "/Admin/LineManager/getLineClassPoint",
                params: 'id=' + obj.attr('rel') + '&type=' + obj.attr('list_type'),
                sucrender: function (data) {
                    $('.line_class_point').html(data.data)
                },
                failrender: function (data) {
                    alert(data.data);
                }
            };
            HGAjax.HAjax(s);
        }

        function submitForm(val, way) {
            var index = $('#line_class_id').val(); // 获取选中的班线ID
            var scheduling_type = $("input[name='scheduling_type']:checked").val(); // 获取排班类型
            var pre_sale_time = $('input[name="pre_sale_time"]').val(); // 获取预售时间
            var scheduling_type_str = scheduling_type == 2
                ? '自动排班 (预售 ' + pre_sale_time + ' 天)'
                : '手动排班'; // 根据排班类型生成文本

            // layui-tab-item
            let driver_p = []
            $(`#${way} .layui-tab-item`).each(function () {
                let obj = {}
                console.log('this:', $(this))
                let child = $(this).find('.driver_list').attr('starting_time').split(' → ')
                if (child.length > 1) {
                    obj['time_range'] = {
                        'start_time': child[0],
                        'end_time': child[1]
                    }
                } else {
                    obj['time'] = child[0]
                }

                let singel_driver = $(this).find('.driver_list .driVal .driver-label input:checked')
                console.log('singel_driver:', singel_driver)
                let drivers = []
                singel_driver.each(function () {
                    // obj['drivers']=$(this).val()
                    drivers.push({ driver_id: $(this).val(), order: $(this).parent().next().find('input').val() })
                    console.log('this:', $(this).val())
                })
                obj['drivers'] = drivers

                driver_p = [...driver_p, obj]
            })
            const res_ = driver_p.some((item) => {
                if (item.drivers.length == 0) {
                    return true
                }
            })
            if (res_) {
                layer.alert('每个班次时间都需要至少指定一个司机');
                return
            }

            layer.confirm('如果该路线之前有相同日期的排班记录，将会覆盖之前的排班记录，确认排班？', {
                title: '提示',
                btn: ['确定', '暂不'] //按钮
            }, function () {
                layer.closeAll('dialog');
                var params = val
                    + '&scheduling_type=' + scheduling_type
                    + '&line_class_ids=' + index
                    + '&driver_schedules=' + JSON.stringify(driver_p);
                var index_ = layer.load(2);
                var s = {
                    async: true,
                    url: Path.web_path + "/Admin/LineManager/doAddLineClassTrain",
                    params: params,
                    sucrender: function (data) {
                        layer.close(index_);
                        alert(data.data); // 提示成功信息
                        showPbBox(1);

                        // 获取父级 tr，并修改其样式
                        $("#" + index + "_scheduling_type_str").parent().addClass('highlight-row');

                        // 设置高亮样式持续 3 秒后移除
                        setTimeout(function () {
                            $("#" + index + "_scheduling_type_str").parent().removeClass('highlight-row');
                        }, 5000);
                    },
                    failrender: function (data) {
                        layer.close(index_);
                        alert(data.data); // 提示失败信息
                    }
                };
                HGAjax.HAjax(s); // 发起 Ajax 请求

            }, function () {
            });
        }
    </script>
    <script type="text/javascript">
        $('body').on('change', 'input[type="checkbox"]', function () {
            $(this)[$(this).prop('checked') ? 'addClass' : 'removeClass']('checked');
        });
    </script>
</body>

</html>

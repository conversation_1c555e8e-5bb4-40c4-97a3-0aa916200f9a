<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <title>编辑定制客运班线线路</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,  minimum-scale=1, user-scalable=yes, viewport-fit=cover">
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/base.css" />
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/page.css" />
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/map.css" />
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css" />
    <link type="text/css" rel="stylesheet" href="/Theme/Admin/line_cadd.css">
    <script type="text/javascript" src="/Theme/Admin/layui/layui.all.js"></script>
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/css/wangeditor-5.1.23.css" />
    <!-- 引入 layui.css -->
    <link type="text/css" rel="stylesheet" href="{$FILE_ROOT}Theme/Admin/css/layui-2.9.18.css" />
    <style type="text/css">
        html,
        body {
            width: 100%;
            height: 100%;
        }

        .return {
            margin-top: -4px;
        }

        .showPlace {
            line-height: 30px;
        }

        #layui-laydate5 {
            left: 438.984px;
            top: 0;
            bottom: -601.87px !important;
             z-index: 9999999;
        }

        .editor—wrapper-div {
            border: 1px solid #ccc;
            z-index: 100; /* 按需定义 */
            width: 47%;
            margin-left: 130px;
        }
        .editor—wrapper-div textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
        }
        #toolbar-container { border-bottom: 1px solid #ccc; }
        #editor-container { height: 300px; }

        div.driver div.r_main div.wiget_box_bg table.lr_table td.rtd div {
            overflow: inherit;
        }
        .price-area-container{
          padding: 20px 0;
        }
        .area-price-list{
          margin-top: 20px;
        }
        .item-required:before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
}

.selected-area-view{
  display: inline-block;
    vertical-align: middle;
    height: 38px;
    line-height: 38px;
    border: 1px solid transparent;
    padding: 0 18px;
    background-color: #16baaa;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    font-size: 14px;
    border-radius: 2px;
    cursor: pointer;
  border-color: #16baaa !important;
  color: #16baaa !important;
}
.ft-12 {
    font-size: 12px;
}
    </style>
</head>

<body>


    <include file="Admin:Modules:header" />
    <div class="main members driver">
        <div class="l_main">
            <include file="Admin:Modules:sidenav" />
        </div>
        <div class="r_main">

            <div class="bg-ff">
                <div class="sbMenu" id="T_mb">
                    <include file="Admin:Modules:linenav" />
                </div>

                <div class="wiget_box wiget_box_bg" style="height:auto;">

                    <div style="text-align: left;padding: 10px;">
                        <a href="javascript:history.back(-1);" class="return-lineclass-list">返回班线车列表</a>
                    </div>
                    <form id="form">

                        <!--基本信息-->
                        <div>
                            <table class="lr_table">
                                <tr>
                                    <div class="title-pa-div">
                                        <div>线路基本信息</div>
                                    </div>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place start_place">
                                            <span class="gray"> 所属子业：</span>
                                            <select class="prov" name="sub_business_type" id="sub_business_type">
                                                <option value="0" <if condition="!isset($line['sub_business_type'])">selected</if>> 请选择所属子业 </option>
                                                <option value="1" <if condition="$line['sub_business_type'] eq 1">selected</if>> 1. 班线客运 [班] </option>
                                                <option value="2" <if condition="$line['sub_business_type'] eq 2">selected</if>> 2. 定制客运 [班-定制] </option>
                                                <option value="3" <if condition="$line['sub_business_type'] eq 3">selected</if>> 3. 城市公共交通运输 [公交] </option>
                                                <option value="4" <if condition="$line['sub_business_type'] eq 4">selected</if>> 4. 旅游班线客运 [班-旅游] </option>
                                                <option value="5" <if condition="$line['sub_business_type'] eq 5">selected</if>> 5. 包车客运[包] </option>
                                                <option value="6" <if condition="$line['sub_business_type'] eq 6">selected</if>> 6. 城乡/农村客运[班-农村] </option>
                                            </select>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place start_place">
                                            <span class="gray"> 客运级别：</span> 
                                            <label>
                                                <input type="checkbox" name="transport_level[]" value="1"
                                                       <if condition="in_array('1', json_decode($line['transport_level'], true))">checked</if>> 一类客运
                                            </label>
                                            <label>
                                                <input type="checkbox" name="transport_level[]" value="2"
                                                       <if condition="in_array('2', json_decode($line['transport_level'], true))">checked</if>> 二类客运
                                            </label>
                                            <label>
                                                <input type="checkbox" name="transport_level[]" value="3"
                                                       <if condition="in_array('3', json_decode($line['transport_level'], true))">checked</if>> 三类客运
                                            </label>
                                            <label>
                                                <input type="checkbox" name="transport_level[]" value="4"
                                                       <if condition="in_array('4', json_decode($line['transport_level'], true))">checked</if>> 四类客运
                                            </label>
                                        </div>
                                    </td>
                                </tr>


                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <input id="id" type="hidden" value="{$line[id]}">
                                        <div class="place start_place">
                                            <span class="gray"><em class="red">*</em>出发地：</span>
                                            <select class="prov" name="start_province_code" id="start_province_code">
                                                <option value="0">- 请选择省 -</option>
                                                <ex:select model="SupportCity" where="address_pid=0 AND mchid=$mchidadr"
                                                    item="pro">
                                                    <if condition="$line['start_province_code'] eq $pro['address_id']">
                                                        <option value="{$pro.address_id}" rel="{$pro.address_id}"
                                                            selected="selected">{$pro.name}</option>
                                                        <else />
                                                        <option value="{$pro.address_id}" rel="{$pro.address_id}">
                                                            {$pro.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <select class="city" name="start_city_code" id="start_city_code">
                                                <option value="0">- 请选择市 -</option>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[start_province_code] AND mchid=$mchidadr"
                                                    item="city">
                                                    <if condition="$line['start_city_code'] eq $city['address_id']">
                                                        <option value="{$city.address_id}" rel="{$city.address_id}"
                                                            selected="selected">{$city.name}</option>
                                                        <else />
                                                        <option value="{$city.address_id}" rel="{$city.address_id}">
                                                            {$city.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <select class="area" name="start_area_code" id="start_area_code">
                                                <option value="0">- 请选择区 -</option>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[start_city_code] AND mchid=$mchidadr"
                                                    item="area">
                                                    <if condition="$line['start_area_code'] eq $area['address_id']">
                                                        <option value="{$area.address_id}" rel="{$area.address_id}"
                                                            selected="selected">{$area.name}</option>
                                                        <else />
                                                        <option value="{$area.address_id}" rel="{$area.address_id}">
                                                            {$area.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <input type="text" style="width:360px;" class="textfield" name="start_name"
                                                placeholder="如: 双流机场" id="start_name" value="{$line[start_name]}" />
                                        </div>
                                    </td>
                                </tr>

                                <!--出发地-->
                                <tr class="mod1">
                                    <td>
                                        <div class="line-content">
                                            <div class="line-content-tab">
                                                <eq name="line['start_address_type']" value="1">
                                                    <span value="1" class="stop-buy-bus buybus-tab1 buybus-selected1"
                                                        onclick="upAddrTab(this,1)">固定上车地点</span>
                                                    <span class="env-buy-bus buybus-tab1" value="2"
                                                        onclick="upAddrTab(this,1)">任意上车地点</span>
                                                    <else />
                                                    <span value="1" class="stop-buy-bus buybus-tab1"
                                                        onclick="upAddrTab(this,1)">固定上车地点</span>
                                                    <span class="env-buy-bus buybus-tab1  buybus-selected1" value="2"
                                                        onclick="upAddrTab(this,1)">任意上车地点</span>
                                                </eq>
                                            </div>
                                            <!--固定上车地点-->
                                            <div class="upcar-addr up-buybusmethod1" <eq
                                                name="line['start_address_type']" value="2"> style="display: none;"
                                                </eq>>
                                                <!--固定上车点循环-->
                                                <volist name="up" id="vo" key="index">
                                                    <div class="upcar-list1 car-blist">
                                                        <input type="hidden" name="up_id[]" value="{$vo.id}" />
                                                        <div class="left-number">{$index}</div>
                                                        <div class="content-center" style="width: 80%;">
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">上车点名称：</span>
                                                                <input type="text" name="up_alias1[]"
                                                                    value="{$vo.alias}" style="width: 200px;">
                                                                <span class="red">*必填</span>
                                                            </div>
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">选择经纬度：</span>
                                                                <input type="text" name="up_lgtlat1[]"
                                                                    value="{$vo.longitude},{$vo.latitude}"
                                                                    style="width: 250px;" >
                                                                <span class="line-btn" onclick="choiceLgdLtd(this,1)"
                                                                    style="margin-left: 15px;">选择经纬度</span>
                                                            </div>
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">耗时：</span>
                                                                <input type="number" name="up_use_time1[]"
                                                                    value="{$vo.use_time}" style="width: 145px;">
                                                                分钟<span class="red">(*必填)</span>填写从始发站到当前站的耗时
                                                            </div>
                                                            <div class="line-op-pa">
                                                              <span class="input-label1">站点价格：</span>
                                                              <input type="number" name="up_additional_fee1[]"
                                                                  value="{$vo.additional_fee}" placeholder="" style="width: 145px;">
                                                                  注：非必填，填写后将在基础票价上加价，如需减价输入负数即可
                                                          </div>
                                                        </div>
                                                        <div class="delete delete-tab1">删<br>除</div>
                                                    </div>
                                                </volist>
                                                <div class="addupaddr-par">
                                                    <span class="line-btn addupaddr"
                                                        onclick="addupaddr(this,1)">新增上车点</span>
                                                </div>
                                            </div>
                                            <!--任意上车地点-->
                                            <div class="upcar-addr up-buybusmethod2" <eq
                                                name="line['start_address_type']" value="1"> style="display: none;"
                                                </eq> >
                                                <div class="upcar-list2">
                                                    <!-- <div class="left-number">1</div> -->
                                                    <div class="content-center"  style="width: 100%;">
                                                        <div class="line-op-pa">
                                                            <span class="input-label1">选择范围：</span>
                                                            <input type="text" id="start_polygon" name="start_polygon"
                                                                value="{$start_polygon}" style="width: 145px;">
                                                            <span class="line-btn" style="margin-left: 15px;"
                                                                onclick="choiceRange(this,1)">选择范围</span>
                                                            <span class="red">*必须选择</span>
                                                        </div>
                                                        <div class="price-area-container">
                                                          <button type="button" class="layui-btn layui-btn-sm layui-btn"    onclick="addpriceArea(this,1)">
                                                             新增价格区域
                                                          </button>
                                                            <!-- <i class="layui-icon layui-icon-question  explain-text"></i> -->
                                                          <div class="area-price-list">
                                                            <volist name="pickup_polygons" id="vo" key="index">
                                                              <div class="layui-row layui-col-space16">
                                                                <input type="hidden" name="pickup_polygons[seq][]" value="{$vo.seq}" />
                                                                <div class="layui-col-md3">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix">
                                                                      区域名称
                                                                    </div>
                                                                    <input type="text"  value="{$vo.title}"  name="pickup_polygons[title][]"  placeholder="请输入" class="layui-input">
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md4">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix item-required">
                                                                      区域范围
                                                                    </div>
                                                                    <input type="text"   value="{$vo.polygons}"  name="pickup_polygons[polygons][]" placeholder="请选择"  class="layui-input">
                                                                    <div class="layui-input-suffix" style="padding: 0;">
                                                                      <span class="layui-btn layui-btn-primary layui-border-green selected-area-view"  onclick="setArea(this,1)">选择</span>
                                                                    </div>
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md3">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix  item-required">
                                                                      区域价格
                                                                    </div>
                                                                    <input type="text"  value="{$vo.price}"  name="pickup_polygons[price][]"  placeholder="请输入" class="layui-input">
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md1">
                                                                  <button type="button" class="layui-btn  layui-btn-primary delete-area">
                                                                    <i class="layui-icon layui-icon-delete"></i>
                                                                  </button>
                                                                </div>
                                                              </div>
                                                          </volist>
                                                          </div>
                                                        </div>

                                                        
                                                    </div>
                                                </div>
                                                <if condition="$line_class_ferry eq 1">
                                                    <!--设置车场-->
                                                    <span><b>设置车场（选填）——用于乘客聚合分流摆渡点</b></span>
                                                    <volist name="any_up" id="vo" key="index">
                                                        <div class="upcar-list2 car-blist">
                                                            <input type="hidden" name="up_id[]" value="{$vo.id}" />
                                                            <div class="left-number">{$index}</div>
                                                            <div class="content-center"
                                                                style="background-color: #F2F2F2">
                                                                <div class="line-op-pa">
                                                                    <span class="input-label1">上车点名称：</span>
                                                                    <input type="text" name="up_alias2[]"
                                                                        value="{$vo.alias}" style="width: 200px;">
                                                                    <span class="red">*必填</span>
                                                                </div>
                                                                <div class="line-op-pa">
                                                                    <span class="input-label1">选择经纬度：</span>
                                                                    <input type="text" name="up_lgtlat2[]"
                                                                        value="{$vo.longitude},{$vo.latitude}"
                                                                        style="width: 250px;" >
                                                                    <span class="line-btn"
                                                                        onclick="choiceLgdLtd(this,1)"
                                                                        style="margin-left: 15px;">选择经纬度</span>
                                                                </div>
                                                            </div>
                                                            <div class="delete delete-tab1">删<br>除</div>
                                                        </div>
                                                    </volist>
                                                    <div class="addupaddr-par">
                                                        <span class="line-btn addupaddr"
                                                            onclick="addupaddr(this,1,2)">新增车场</span>
                                                    </div>
                                                </if>
                                            </div>
                                        </div>
                                    </td>
                                </tr>


                                <!-- 途径点 -->
                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place end_place">
                                            <span class="gray"><em class="red"></em>途经点：</span>

                                            <select class="prov" name="via_province_code" id="via_province_code">
                                                <if condition="$line['via_province_code'] eq null">
                                                    <option value="0" meta-type="2">- 请选择省 -</option>
                                                </if>
                                                <ex:select model="SupportCity" where="address_pid=0 AND mchid=$mchidadr"
                                                    item="via">
                                                    <if condition="$line['via_province_code'] eq $via['address_id']">
                                                        <option value="{$via.address_id}" meta-type="2"
                                                            rel="{$via.address_id}" selected="selected">{$via.name}
                                                        </option>
                                                        <else />
                                                        <option value="{$via.address_id}" meta-type="2"
                                                            rel="{$via.address_id}">{$via.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>

                                            <select class="city" name="via_city_code" id="via_city_code">
                                                <if condition="$line['via_city_code'] eq null">
                                                    <option value="0" meta-type="2">- 请选择市 -</option>
                                                </if>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[via_province_code] AND mchid=$mchidadr"
                                                    item="city">
                                                    <if condition="$city['address_id'] eq $line['via_city_code']">
                                                        <option value="{$city.address_id}" rel="{$city.address_id}"
                                                            selected="selected" checked="true">{$city.name}
                                                        </option>
                                                        <else />
                                                        <option value="{$city.address_id}" rel="{$city.address_id}">
                                                            {$city.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>

                                            <select class="area" name="via_area_code" id="via_area_code">
                                                <if condition="$line['via_area_code'] eq null">
                                                    <option value="0">- 请选择区 -</option>
                                                </if>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[via_city_code] AND mchid=$mchidadr"
                                                    item="area">
                                                    <if condition="$line['via_area_code'] eq $area['address_id']">
                                                        <option value="{$area.address_id}" rel="{$area.address_id}"
                                                            selected="selected">{$area.name}</option>
                                                        <else />
                                                        <option value="{$area.address_id}" rel="{$area.address_id}">
                                                            {$area.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>

                                            <input type="text" style="width:360px;" name="via_name" class="textfield"
                                                placeholder="如: 天府广场" id="via_name" value="{$line['via_name']}" />
                                        </div>
                                    </td>
                                </tr>
                                <!-- 途径点 -->


                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place end_place">
                                            <span class="gray"><em class="red">*</em>目的地：</span>
                                            <select class="prov" name="end_province_code" id="end_province_code">
                                                <if condition="$line['end_province_code'] eq null">
                                                    <option value="0" meta-type="2">- 请选择省 -</option>
                                                </if>
                                                <ex:select model="SupportCity" where="address_pid=0 AND mchid=$mchidadr"
                                                    item="pro">
                                                    <if condition="$line['end_province_code'] eq $pro['address_id']">
                                                        <option value="{$pro.address_id}" meta-type="2"
                                                            rel="{$pro.address_id}" selected="selected">{$pro.name}
                                                        </option>
                                                        <else />
                                                        <option value="{$pro.address_id}" meta-type="2"
                                                            rel="{$pro.address_id}">{$pro.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <select class="city" name="end_city_code" id="end_city_code">
                                                <if condition="$line['end_city_code'] eq null">
                                                    <option value="0" meta-type="2">- 请选择市 -</option>
                                                </if>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[end_province_code] AND mchid=$mchidadr"
                                                    item="city">
                                                    <if condition="$city['address_id'] eq $line['end_city_code']">
                                                        <option value="{$city.address_id}" rel="{$city.address_id}"
                                                            selected="selected" checked="true">{$city.name}</option>
                                                        <else />
                                                        <option value="{$city.address_id}" rel="{$city.address_id}">
                                                            {$city.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <select class="area" name="end_area_code" id="end_area_code">
                                                <if condition="$line['end_area_code'] eq null">
                                                    <option value="0">- 请选择区 -</option>
                                                </if>
                                                <ex:select model="SupportCity"
                                                    where="address_pid=$line[end_city_code] AND mchid=$mchidadr"
                                                    item="area">
                                                    <if condition="$line['end_area_code'] eq $area['address_id']">
                                                        <option value="{$area.address_id}" rel="{$area.address_id}"
                                                            selected="selected">{$area.name}</option>
                                                        <else />
                                                        <option value="{$area.address_id}" rel="{$area.address_id}">
                                                            {$area.name}</option>
                                                    </if>
                                                </ex:select>
                                            </select>
                                            <input type="text" style="width:360px;" class="textfield" name="end_name"
                                                placeholder="如: 天府广场" id="end_name" value="{$line[end_name]}" />
                                        </div>
                                    </td>
                                </tr>

                                <!--目的地-->
                                <tr class="mod1">
                                    <td>
                                        <div class="line-content">
                                            <div class="line-content-tab">
                                                <eq name="line['end_address_type']" value="1">
                                                    <span value="1" class="stop-buy-bus buybus-tab2 buybus-selected2"
                                                        onclick="upAddrTab(this,2)">固定下车地点</span>
                                                    <span class="env-buy-bus buybus-tab2" value="2"
                                                        onclick="upAddrTab(this,2)">任意下车地点</span>
                                                    <else />
                                                    <span value="1" class="stop-buy-bus buybus-tab2"
                                                        onclick="upAddrTab(this,2)">固定下车地点</span>
                                                    <span class="env-buy-bus buybus-tab2  buybus-selected2" value="2"
                                                        onclick="upAddrTab(this,2)">任意下车地点</span>
                                                </eq>

                                            </div>
                                            <!--固定下车地点-->
                                            <div class="upcar-addr down-buybusmethod1" <eq
                                                name="line['end_address_type']" value="2"> style="display: none;" </eq>>
                                                <!--固定下车点循环-->
                                                <volist name="down" id="vo" key="index">
                                                    <div class="downcar-list1 car-blist">
                                                        <input type="hidden" name="down_id[]" value="{$vo.id}" />
                                                        <div class="left-number">{$index}</div>
                                                        <div class="content-center">
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">下车点名称：</span>
                                                                <input type="text" name="down_alias1[]"
                                                                    value="{$vo.alias}" style="width: 200px;">
                                                                <span class="red">*必填</span>
                                                            </div>
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">选择经纬度：</span>
                                                                <input type="text" name="down_lgtlat1[]"
                                                                    value="{$vo.longitude},{$vo.latitude}"
                                                                    style="width: 250px;" >
                                                                <span class="line-btn" onclick="choiceLgdLtd(this,2)"
                                                                    style="margin-left: 15px;">选择经纬度</span>
                                                            </div>
                                                            <div class="line-op-pa">
                                                                <span class="input-label1">耗时：</span>
                                                                <input type="number" name="down_use_time1[]"
                                                                    value="{$vo.use_time}" style="width: 145px;">
                                                                分钟<span class="red">(*必填)</span>填写从始发站到当前站的耗时
                                                            </div>
                                                            <div class="line-op-pa">
                                                              <span class="input-label1">站点价格：</span>
                                                              <input type="number" name="down_additional_fee1[]"
                                                                  value="{$vo.additional_fee}" placeholder="" style="width: 145px;">
                                                                  注：非必填，填写后将在基础票价上加价，如需减价输入负数即可
                                                          </div>
                                                        </div>
                                                        <div class="delete delete-tab1">删<br>除</div>
                                                    </div>
                                                </volist>
                                                <div class="addupaddr-par">
                                                    <span class="line-btn addupaddr"
                                                        onclick="addupaddr(this,2)">新增下车点</span>
                                                </div>
                                            </div>
                                            <!--任意下车地点-->
                                            <div class="upcar-addr down-buybusmethod2" <eq
                                                name="line['end_address_type']" value="1"> style="display: none;" </eq>>
                                                <div class="downcar-list2">
                                                    <!-- <div class="left-number">1</div> -->
                                                    <div class="content-center" style="width: 100%;">
                                                        <div class="line-op-pa">
                                                            <span class="input-label1">选择范围：</span>
                                                            <input type="text" id="end_polygon" name="end_polygon" value="{$end_polygon}"
                                                                style="width: 145px;">
                                                            <span class="line-btn" style="margin-left: 15px;"
                                                                onclick="choiceRange(this,2)">选择范围</span>
                                                            <span class="red">*必须选择</span>
                                                        </div>
                                                        <div class="line-op-pa">
                                                            <span class="input-label1">耗时：</span>
                                                            <input type="number" name="end_polygon_use_time"
                                                                value="{$line.end_polygon_use_time}"
                                                                style="width: 145px;">
                                                            分钟<span class="red">(*必填)</span>填写从始发站到当前站的耗时
                                                        </div>
                                                        <div class="price-area-container">
                                                          <button type="button" class="layui-btn layui-btn-sm layui-btn"    onclick="addpriceArea(this,2)">
                                                             新增价格区域
                                                          </button>
                                                          <div class="area-price-list">
                                                            <volist name="dropoff_polygons" id="vo" key="index">
                                                              <div class="layui-row layui-col-space16">
                                                                <input type="hidden" name="dropoff_polygons[seq][]" value="{$vo.seq}" />
                                                                <div class="layui-col-md3">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix">
                                                                      区域名称
                                                                    </div>
                                                                    <input type="text"  value="{$vo.title}"  name="dropoff_polygons[title][]"  placeholder="请输入" class="layui-input">
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md4">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix item-required">
                                                                      区域范围
                                                                    </div>
                                                                    <input type="text"   value="{$vo.polygons}"  name="dropoff_polygons[polygons][]" placeholder="请选择"  class="layui-input">
                                                                    <div class="layui-input-suffix" style="padding: 0;">
                                                                      <span class="layui-btn layui-btn-primary layui-border-green selected-area-view"  onclick="setArea(this,2)">选择</span>
                                                                    </div>
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md3">
                                                                  <div class="layui-input-group">
                                                                    <div class="layui-input-split layui-input-prefix  item-required">
                                                                      区域价格
                                                                    </div>
                                                                    <input type="text"  value="{$vo.price}"  name="dropoff_polygons[price][]"  placeholder="请输入" class="layui-input">
                                                                  </div>
                                                                </div>
                                                                <div class="layui-col-md1">
                                                                  <button type="button" class="layui-btn  layui-btn-primary delete-area">
                                                                    <i class="layui-icon layui-icon-delete"></i>
                                                                  </button>
                                                                </div>
                                                              </div>
                                                          </volist>
                                                        </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <if condition="$line_class_ferry eq 1">
                                                    <!--设置车场-->
                                                    <span><b>设置车场（选填）——用于乘客聚合分流摆渡点</b></span>
                                                    <volist name="any_down" id="vo" key="index">
                                                        <div class="downcar-list2 car-blist">
                                                            <input type="hidden" name="down_id[]" value="{$vo.id}" />
                                                            <div class="left-number">{$index}</div>
                                                            <div class="content-center"
                                                                style="background-color: #F2F2F2">
                                                                <div class="line-op-pa">
                                                                    <span class="input-label1">车场名称：</span>
                                                                    <input type="text" name="down_alias2[]"
                                                                        value="{$vo.alias}" style="width: 200px;">
                                                                    <span class="red">*必填</span>
                                                                </div>
                                                                <div class="line-op-pa">
                                                                    <span class="input-label1">选择经纬度：</span>
                                                                    <input type="text" name="down_lgtlat2[]"
                                                                        value="{$vo.longitude},{$vo.latitude}"
                                                                        style="width: 250px;" >
                                                                    <span class="line-btn"
                                                                        onclick="choiceLgdLtd(this,2)"
                                                                        style="margin-left: 15px;">选择经纬度</span>
                                                                </div>
                                                            </div>
                                                            <div class="delete delete-tab1">删<br>除</div>
                                                        </div>
                                                    </volist>
                                                    <div class="addupaddr-par">
                                                        <span class="line-btn addupaddr"
                                                            onclick="addupaddr(this,2,2)">新增车场</span>
                                                    </div>
                                                </if>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray"><em class="red">*</em>发车时间类型：</span>

                                            <input type="radio" name="start_time_type" value="1" onclick="starTiTy(1)"
                                                <if condition="$line['start_time_type'] eq 1"> checked="checked" </if>
                                            id="start_time_type1">
                                            <label for="start_time_type1">固定发车时间</label>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <input type="radio" name="start_time_type" value="2" onclick="starTiTy(2)"
                                                <if condition="$line['start_time_type'] eq 2"> checked="checked" </if>
                                            id="start_time_type2">
                                            <label for="start_time_type2">滚动发车时段</label>

                                        </div>
                                    </td>
                                </tr>

                                <!-- 固定时间 -->
                                <tr class="mod1" id="ongo-time">
                                    <td>
                                        <div class="line-content">
                                            <div class="upcar-addr down-buybusmethod">

                                                <!--固定时间循环-->
                                                <empty name="starting_times">
                                                    <div class="starting-time-layout-div-elem">
                                                        <input type="text"
                                                            class="layui-input-sm textfield date starting-time-layout-input"
                                                            name="starting_time[]" value="{$line['start_time']}"
                                                            placeholder="时:分:秒">
                                                        <button type="button"
                                                            class="layui-btn layui-btn-primary layui-btn-sm"
                                                            onclick="removeStartingTime(this, true)"> ➖ </button>
                                                    </div>
                                                    <else />
                                                    <volist name="starting_times" id="vo">
                                                        <div class="starting-time-layout-div-elem">
                                                            <input type="text"
                                                                class="layui-input-sm textfield date starting-time-layout-input"
                                                                name="starting_time[]" value="{$vo}"
                                                                placeholder="时:分:秒">
                                                            <button type="button"
                                                                class="layui-btn layui-btn-primary layui-btn-sm"
                                                                onclick="removeStartingTime(this, false)"> ➖ </button>
                                                        </div>
                                                    </volist>
                                                </empty>
                                                <!-- 新增固定时间 -->
                                                <div class="starting-time-layout-div-buttom">
                                                    <span class="line-btn addupaddr"
                                                        onclick="appendStartingTime(this)">新增固定时间</span>
                                                </div>

                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- 固定时间 -->

                                <!-- 滚动时间 -->
                                <tr class="mod1 mon-env-time" id="mon-env-time">
                                    <td>
                                        <div class="line-content">
                                            <div class="upcar-addr down-buybusmethod">

                                                <!--滚动时间循环-->
                                                <empty name="starting_interval_times">
                                                    <div class="interval-time-layout-div-btn">

                                                        <input type="text"
                                                            class="layui-input-sm textfield date interval-time-layout-input"
                                                            name="starting_interval_time[]" placeholder="最早发车时间">
                                                        →
                                                        <input type="text"
                                                            class="layui-input-sm textfield date interval-time-layout-input"
                                                            name="end_interval_time[]" placeholder="最晚发车时间">

                                                        <button type="button"
                                                            class="layui-btn layui-btn-primary layui-btn-sm"
                                                            onclick="removeIntervalTime(this, true)"> ➖
                                                        </button>
                                                    </div>
                                                    <else />
                                                    <volist name="starting_interval_times" id="vo">
                                                        <foreach name="vo" item="end" key="start">
                                                            <div class="interval-time-layout-div-btn">
                                                                <input type="text"
                                                                    class="layui-input-sm textfield date interval-time-layout-input"
                                                                    name="starting_interval_time[]" value="{$start}"
                                                                    placeholder="最早发车时间">
                                                                →
                                                                <input type="text"
                                                                    class="layui-input-sm textfield date interval-time-layout-input"
                                                                    name="end_interval_time[]" value="{$end}"
                                                                    placeholder="最晚发车时间">


                                                                <button type="button"
                                                                    class="layui-btn layui-btn-primary layui-btn-sm"
                                                                    onclick="removeIntervalTime(this, false)"> ➖
                                                                </button>
                                                            </div>
                                                        </foreach>

                                                    </volist>
                                                </empty>
                                                <!-- 滚动时间循环 -->

                                                <div class="interval-time-layout-div-buttom">
                                                    <span class="line-btn addupaddr"
                                                        onclick="appendInervalTime(this)">新增滚动发车时段</span>
                                                </div>

                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- 滚动时间 -->

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="1">
                                        <div class="time-type">
                                            <span class="gray">滚动时间间隔：</span>
                                            <input type="number"
                                            class="layui-input-sm textfield" name="interval_minutes" value="{$line.interval_minutes}"> 分钟</input> 
                                        </div>
                                    </td>
                                </tr>


                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray ft-12">是否添加首个滚动时间：</span>
                                            <input type="radio" name="is_supplementary_time" value="0" <if
                                                condition="$line['is_supplementary_time'] eq 0"> checked="checked" </if> >
                                            <span>否</span>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <input type="radio" name="is_supplementary_time" value="1" <if
                                                condition="$line['is_supplementary_time'] eq 1"> checked="checked" </if> >
                                            <span>是</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray">停售时刻：</span>
                                            <span>发车前第</span>
                                            <select name="stop_sell_number" style="width: 40px;">
                                                <option value="0" <if condition="$line['stop_sell_number'] eq 0">
                                                    selected </if>>0</option>
                                                <option value="1" <if condition="$line['stop_sell_number'] eq 1">
                                                    selected </if>>1</option>
                                            </select>
                                            天
                                            <input type="text" style="width:160px;" name="stop_sell_time"
                                                id="stop_sell_time" class="textfield" value="{$line.stop_sell_time}" />
                                            <span>时刻后停止售票。 
                                                <span style="color: red;">  例如：当天8:00发车的班车，提前10分钟停止售票，请填写第0天7:50停止售票时间。当天8:00发车的班车，前一天23:50停止售票，请选择第1天，并填写23:50。（如果设置了停售分钟，此设置将无效。包括第0天/00:00:00也无效）。
                                                </span>
                                        </div>

                                    </td>
                                </tr>
                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray">停售分钟：</span>
                                            发车前 <input type="number" style="width:160px;" name="stop_sale_minutes"
                                                id="stop_sale_minutes" class="textfield" value="{$line.stop_sale_minutes}" />
                                            <span>分钟停止售票。 <span style="color: red;"> 此设置对多个固定发车时间和多个滚动发车时间都生效。例如多个固定发车时间：8:00/9:00/10:00，设置了发车前10分钟停止售票，那么7:50/8:50/9:50三个时间后无法购票。多个发车滚动时段：8:00-9:00/9:00-10:00/10:00-11:00，设置了发车前5分钟停止售票，那么8:55后无法购票/9:55分后无法购票/10:55分后无法购票。</span></span>
                                        </div>

                                    </td>
                                </tr>


                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray">全程用时：</span>
                                                <div class="editor—wrapper-div">
                                                <textarea name="travel_time">{$line.travel_time}</textarea>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray">线路说明：</span>
                                            <textarea name="summary" id="editor-html-preview"  style="display: none;"></textarea>
                                            <div id="editor—wrapper" class="editor—wrapper-div">
                                                <div id="toolbar-container"><!-- 工具栏 --></div>
                                                <div id="editor-container"><!-- 编辑器 --></div>
                                            </div>

                                            <script src="{$FILE_ROOT}Theme/js/jquery-mini-3.6.0.js"></script>
                                            <script src="{$FILE_ROOT}Theme/js/wangeditor-5.1.23.js"></script>
                                            <script>
                                                const { createEditor, createToolbar } = window.wangEditor

                                                const LANG = location.href.indexOf('lang=en') > 0 ? 'en' : 'zh-CN'

                                                const editorConfig = {
                                                  uploadImgShowBase64 :true,
                                                    placeholder: '请输入...',
                                                    onChange(editor) {
                                                        const html = editor.getHtml()
                                                        console.log('editor content', html)
                                                        $('#editor-html-preview').val(html)
                                                        // 也可以同步到 <textarea>
                                                    },
                                                    MENU_CONF: {
                                                        uploadImage: {
                                                          onError(file) {  // TS 语法
                                                            // onError(file, err, res) {               // JS 语法
                                                                console.log(`${file.name} 上传出错`)
                                                                alert('单张图片大小不能超过200KB')
                                                            },
                                                            maxFileSize: 0.2 * 1024 * 1024, 
                                                            server: 'api/upload-img',
                                                            // fieldName: 'image',
                                                            method: 'post',
                                                            base64LimitSize: 0.2 * 1024 *1024
                                                        },
                                                    }
                                                }


                                                const editor = createEditor({
                                                    selector: '#editor-container',
                                                    html:`{$line.summary}`,
                                                    config: editorConfig,
                                                    mode: 'simple', // or 'simple'
                                                    i18nChangeLanguage:LANG
                                                })


                                                const toolbarConfig = {}

                                                const toolbar = createToolbar({
                                                    editor,
                                                    selector: '#toolbar-container',
                                                    config: toolbarConfig,
                                                    mode: 'simple', // or 'simple'
                                                })

                                            </script>
                                            

                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="branch">
                                            <span class="gray"><em class="red">*</em>所属分台：</span>
                                            <select class="branch-select" name="branch_id" id="branch_id">
                                                <if condition="$line[branchid] eq null">
                                                    <option value="0">- 请选择分台 -</option>
                                                </if>
                                                <eq name="admin_group" value="branch_admin">
                                                    <ex:select model="Admin"
                                                        where="admin_id=$branchid  and is_del = 0  and branch_type = 0 and is_freeze = 0"
                                                        item="ma">
                                                        <if condition="$ma.admin_id eq $line[branchid]">
                                                            <option value="{$ma.admin_id}" rel="{$ma.admin_id}"
                                                                selected="selected">
                                                                {$ma.mchname}
                                                            </option>
                                                            <else />
                                                            <option value="{$ma.admin_id}" rel="{$ma.admin_id}">
                                                                {$ma.mchname}
                                                            </option>
                                                        </if>
                                                    </ex:select>
                                                    <else />
                                                    <ex:select model="Admin"
                                                        where="parent_admin_id=$mchid  and is_del = 0  and branch_type = 0 and is_freeze = 0"
                                                        item="ma">
                                                        <if condition="$ma.admin_id eq $line[branchid]">
                                                            <option value="{$ma.admin_id}" rel="{$ma.admin_id}"
                                                                selected="selected">
                                                                {$ma.mchname}
                                                            </option>
                                                            <else />
                                                            <option value="{$ma.admin_id}" rel="{$ma.admin_id}">
                                                                {$ma.mchname}
                                                            </option>
                                                        </if>
                                                    </ex:select>
                                                </eq>
                                            </select>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="time-type">
                                            <span class="gray"><em class="red">*</em>选座设置：</span>
                                            <input type="radio" name="is_seat_selection" value="0"
                                                onclick="seatOptional(0)" <if
                                                condition="$line['is_seat_selection'] eq 0"> checked="checked" </if> >
                                            <span>不支持在线选座</span>
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <input type="radio" name="is_seat_selection" value="1"
                                                onclick="seatOptional(1)" <if
                                                condition="$line['is_seat_selection'] eq 1"> checked="checked" </if> >
                                            <span>支持在线选座</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mod1">
                                    <td>
                                        <div class="no-seat-optional line-content" <if
                                            condition="$line['is_seat_selection'] eq 0"> style="padding:
                                            20px;text-align: left"
                                            <else /> style="padding: 20px;text-align: left;display: none"</if>>
                                            <span class="gray"><em class="red">*</em>价格：</span>
                                            <input type="number" name="price" value="{$line.price}"
                                                style="width:260px;" /><br />
                                            <span class="gray"><em class="red">*</em>渠道价格：</span>
                                            <input type="number" name="channel_price" value="{$line.channel_price}"
                                                style="width:260px;" /><br /><br />
                                            <span class="red">注:渠道价格即渠道分台代约时所需要的支付价格</span>
                                            <span class="red">例如:渠道价格+渠道分成收益 = 价格</span>
                                        </div>
                                        <div class="seat-optional line-content" <if
                                            condition="$line['is_seat_selection'] eq 1"> style="padding:
                                            20px;text-align: left"
                                            <else /> style="padding: 20px;text-align: left;display: none"</if>>
                                            <input type="hidden" name="seat_layout" value="{$line['seat_layout']}">
                                            <span class="gray"><em class="red">*</em>车辆座位数：</span>
                                            <input type="radio" name="car_seats" value="5" onclick="carSeats(5,'2-3')" <if
                                                condition="$line['car_seats'] eq 5 OR $line['car_seats'] == null">
                                            checked="checked" </if> >5座(座位布局：2-3)

                                            <input type="radio" name="car_seats" value="7" onclick="carSeats(7,'2-2-3')" <if
                                                condition="$line['car_seats'] eq 7"> checked="checked" </if> >7座(座位布局：2-2-3)

                                            <input type="radio" name="car_seats" value="9"
                                                onclick="carSeats(9,'2-2-2-3')" <if
                                                condition="$line['car_seats'] eq 9 AND $line['seat_layout'] eq '2-2-2-3'">
                                            checked="checked" </if> >9座(座位布局：2-2-2-3)

                                            <input type="radio" name="car_seats" value="9" onclick="carSeats(9,'3-3-3')"
                                                <if condition="$line['car_seats'] eq 9 AND $line['seat_layout'] eq '3-3-3'">
                                                    checked="checked" 
                                                </if> 
                                            > 9座(座位布局：3-3-3)

                                            <input type="radio" name="car_seats" value="15" onclick="carSeats(15,'3-2-3-3-4')"
                                                <if condition="$line['car_seats'] eq 15 AND $line['seat_layout'] eq '3-2-3-3-4'">
                                                    checked="checked" 
                                                </if> 
                                            > 15座(座位布局：3-2-3-3-4)

                                            <br /><br />

                                            <div class="car-seats-5" <if
                                                condition="$line['car_seats'] neq 5 AND $line['car_seats'] neq null">
                                                style="display: none" </if>>
                                                <span class="gray"><em class="red">*</em>前左： </span><input
                                                    class="car-seats-5-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if> style="display:
                                                none" value="司机位"><input type="number" class="car-seats-5-input" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][0]}" style="display:
                                                none">司机位置<br /><br />
                                                <span class="gray"><em class="red">*</em>前右价格：</span><input
                                                    class="car-seats-5-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if> style="display:
                                                none" value="前右"> <input type="number" class="car-seats-5-input " <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][1]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后左价格：</span><input
                                                    class="car-seats-5-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if> style="display:
                                                none" value="后左"> <input type="number" class="car-seats-5-input " <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][2]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后中价格：</span><input
                                                    class="car-seats-5-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if> style="display:
                                                none" value="后中"> <input type="number" class="car-seats-5-input " <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][3]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后右价格：</span><input
                                                    class="car-seats-5-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if> style="display:
                                                none" value="后右"> <input type="number" class="car-seats-5-input " <if
                                                    condition="$line['car_seats'] neq 5"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][4]}">元<br /><br />
                                            </div>
                                            <div class="car-seats-7" <if condition="$line['car_seats'] neq 7">
                                                style="display: none" </if>>
                                                <span class="gray"><em class="red">*</em>前左： </span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="司机位"><input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][0]}" style="display:
                                                none">司机位置<br /><br />
                                                <span class="gray"><em class="red">*</em>前右价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="前右"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][1]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中左价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="中左"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][2]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中右价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="中右"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][3]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后左价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="后左"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][4]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后中价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="后中"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][5]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后右价格：</span><input
                                                    class="car-seats-7-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if> style="display:
                                                none" value="后右"> <input type="number" class="car-seats-7-input" <if
                                                    condition="$line['car_seats'] neq 7"> disabled </if>
                                                name="seat_price[]" value="{$line['seat_price_'][6]}">元<br /><br />
                                            </div>

                                            <!-- 9座(座位布局：2-2-2-3)  -->
                                            <div class="car-seats-9-2-2-2-3" <if
                                                condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                style="display: none" </if>>
                                                <span class="gray"><em class="red">*</em>前左： </span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="司机位"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]" value="{$line['seat_price_'][0]}"
                                                style="display: none">司机位置<br /><br />
                                                <span class="gray"><em class="red">*</em>前右价格： </span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="前右"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][1]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中1排左价格：</span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="中1排左"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][2]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中1排右价格：</span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="中1排右"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][3]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中2排左价格：</span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="中2排左"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][4]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中2排右价格：</span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="中2排右"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][5]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后左价格： </span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="后左"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][6]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后中价格： </span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="后中"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][7]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后右价格： </span><input
                                                    class="car-seats-9-2-2-2-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> style="display: none" value="后右"> <input type="number"
                                                    class="car-seats-9-2-2-2-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '2-2-2-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][8]}">元<br /><br />
                                            </div>

                                            <!-- 9座(座位布局：3-3-3)  -->
                                            <div class="car-seats-9-3-3-3" <if
                                                condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                style="display: none" </if>>
                                                <span class="gray"><em class="red">*</em>前左： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="司机位"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]" value="{$line['seat_price_'][0]}"
                                                style="display: none">司机位置<br /><br />
                                                <span class="gray"><em class="red">*</em>前中价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="前中"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][1]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>前右价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="前右"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][2]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中左价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="中左"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][3]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中中价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="中中"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][4]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>中右价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="中右"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][5]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后左价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="后左"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][6]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后中价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="后中"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][7]}">元<br /><br />
                                                <span class="gray"><em class="red">*</em>后右价格： </span><input
                                                    class="car-seats-9-3-3-3-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> style="display: none" value="后右"> <input type="number"
                                                    class="car-seats-9-3-3-3-input" <if
                                                    condition="$line['car_seats'] neq 9 OR $line['seat_layout'] neq '3-3-3'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][8]}">元<br /><br />
                                            </div>

                                            <!-- 15座(座位布局：3-2-3-3-4)  -->
                                            <div class="car-seats-15-3-2-3-3-4" <if
                                                condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                style="display: none" </if> >
                                                <span class="gray"><em class="red">*</em>一排左： </span>
                                                <input class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled  </if> style="display: none" value="司机位"> 
                                                <input type="number" class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]" value="{$line['seat_price_'][0]}"
                                                style="display: none">司机位置<br /><br />

                                                <span class="gray"><em class="red">*</em>一排中价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="一排中"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][1]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>一排右价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="一排右"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][2]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>二排左价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="二排左"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][3]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>二排右价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="二排右"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][4]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>三排左价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="三排左"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][5]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>三排中价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="三排中"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][6]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>三排右价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="三排右"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][7]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>四排左价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="四排左"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][8]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>四排中价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="四排中"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][9]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>四排右价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="四排右"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][10]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>五排左价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="五排左"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][11]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>五排左中价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="五排左中"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][12]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>五排右中价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="五排右中"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][13]}">元<br /><br />

                                                <span class="gray"><em class="red">*</em>五排右价格： </span><input
                                                    class="car-seats-15-3-2-3-3-4-name" name="seat_name[]" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> style="display: none" value="五排右"> <input type="number"
                                                    class="car-seats-15-3-2-3-3-4-input" <if
                                                    condition="$line['car_seats'] neq 15 OR $line['seat_layout'] neq '3-2-3-3-4'">
                                                disabled </if> name="seat_price[]"
                                                value="{$line['seat_price_'][14]}">元<br /><br />


                                            </div>

                                        </div>
                                    </td>
                                </tr>

                                <eq name="points_service" value="1">
                                    <!-- 是否支持积分抵扣 -->
                                    <tr class="mod1" style="height:54px;">
                                        <td class="rtd" colspan="2">
                                            <div class="time-type">
                                                <span class="gray ft-12"><em
                                                        class="red">*</em>班线是否允许积分抵扣：</span>
                                                <input type="radio" name="support_points_deduction" value="1" <if
                                                    condition="$line['support_points_deduction'] eq 1">
                                                checked="checked" </if> >
                                                <span>允许积分抵扣（请点击<a href="{$points_deduction_url}"
                                                        target="_blank">这里</a>设置抵扣积分）</span>
                                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                <input type="radio" name="support_points_deduction" value="0" <if
                                                    condition="$line['support_points_deduction'] eq 0">
                                                checked="checked" </if> >
                                                <span>不允许积分抵扣</span>
                                            </div>
                                        </td>
                                    </tr>
                                </eq>

                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place start_place">
                                            <span class="gray">填写附加信息：</span>
                                            <label>
                                                <input type="checkbox" name="is_required_flight_number" value="1"
                                                       <if condition="$line['is_required_flight_number'] == 1">checked="checked"</if>>是否是接送机线路（是则会要求填写航班号）
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mod1" style="height:54px;">
                                    <td class="rtd" colspan="2">
                                        <div class="place start_place">
                                            <span class="gray">线路方向：</span>
                                            <select name="direction" id="direction">
                                                <option value="0" <if condition="$line['direction'] == 0 || !isset($line['direction'])">selected</if>>未知</option>
                                                <option value="1" <if condition="$line['direction'] == 1">selected</if>>上行</option>
                                                <option value="2" <if condition="$line['direction'] == 2">selected</if>>下行</option>
                                            </select>
                                            <span class="red">（选择线路的运行方向）</span>
                                        </div>
                                    </td>
                                </tr>

                            </table>
                        </div>



                        <!--自定义设置start-->
                        <div style="margin-top: 10px;">
                            <div style="text-align: left;font-size: 27px;">
                                <div class="self-set">自定义设置</div>
                            </div>

                            <div class="return-lineset return-lineset-view">
                                <label for="is_return">
                                    返程设置<input type="checkbox" name="is_return" id="is_return" <if
                                        condition="$line['is_return'] eq 1"> checked="checked" </if> value="1"
                                    style="width: 15px;height: 15px;" onchange="showReturn(this)">

                                </label>
                                <span class="red">（注意：如果需要返程设置请点击此处）</span>
                            </div>
                            <div class="buybus-addr return-lineset-view" <eq name="line['is_return']" value="0"> style="display:none" </eq>>
                                <span class="updow-che-words">上车点</span>
                                <div>
                                    <div class="input-pad">
                                        <span class="input-label1">发车时间类型：</span>

                                        <input type="radio" name="return_start_time_type" id="return_start_time_type1"
                                            onclick="returnUpTime(1)" <if
                                            condition="$line['return_start_time_type'] eq 1"> checked="checked" </if>
                                        value="1">
                                        <label for="return_start_time_type1">固定发车时间</label>

                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                                        <input type="radio" name="return_start_time_type" id="return_start_time_type2"
                                            onclick="returnUpTime(2)" <if
                                            condition="$line['return_start_time_type'] eq 2"> checked="checked" </if>
                                        value="2">
                                        <label for="return_start_time_type2">滚动发车时段</label>

                                    </div>
                                    <div class="input-pad">
                                        <span class="input-label1">返程日期：</span>
                                        <select name="return_time_number">
                                            <option value="1" <if condition="$line['return_time_number'] eq 1">
                                                selected="selected" </if>>当日</option>
                                            <option value="2" <if condition="$line['return_time_number'] eq 2">
                                                selected="selected" </if>>第二天</option>
                                            <option value="3" <if condition="$line['return_time_number'] eq 3">
                                                selected="selected" </if>>第三天</option>
                                            <option value="4" <if condition="$line['return_time_number'] eq 4">
                                                selected="selected" </if>>第四天</option>
                                        </select>
                                    </div>
                                    <div class="input-pad return_time_only" <if
                                        condition="$line['return_start_time_type'] eq 2"> style="display: none;" </if>>
                                        <span class="input-label1">发车时间：</span>
                                        <input type="text" name="return_start_time" id="return_start_time"
                                            value="{$line.return_start_time}">
                                        <i class="layui-icon layui-icon-add-circle-fine"></i>
                                    </div>

                                    <div class="input-pad return_time_double" <if
                                        condition="$line['return_start_time_type'] eq 1"> style="display: none;" </if>>
                                        <span class="input-label1">最早发车时间：</span>
                                        <input type="text" style="width:260px;" name="return_start_earliest_time"
                                            id="return_start_earliest_time"
                                            value="{$line.return_start_earliest_time}" />
                                    </div>
                                    <div class="input-pad return_time_double" <if
                                        condition="$line['return_start_time_type'] eq 1"> style="display: none;" </if>>
                                        <span class="input-label1">最晚发车时间：</span>
                                        <input type="text" style="width:260px;" name="return_end_latest_time"
                                            id="return_end_latest_time" value="{$line.return_end_latest_time}" />
                                    </div>


                                    <div class="input-pad">
                                        <span class="input-label1">上车类型：</span>
                                        <input type="radio" name="return_start_address_type"
                                            id="return_start_address_type1" value="1" <if
                                            condition="$line['return_start_address_type'] eq 1"> checked </if>
                                        onclick="showHRange(1,1)">
                                        <label for="return_start_address_type1">固定位置</label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <input type="radio" name="return_start_address_type"
                                            id="return_start_address_type2" value="2" <if
                                            condition="$line['return_start_address_type'] eq 2"> checked </if>
                                        onclick="showHRange(1,2)">
                                        <label for="return_start_address_type2">上门接送</label>
                                    </div>

                                    <div id="up-enywhere1" <if condition="$line['return_start_address_type'] eq 2">
                                        style="display:none" </if>>
                                        <div class="input-pad">
                                            <span class="input-label1">上车点名称：</span>
                                            <input type="text" name="return_start_alias1" value="{$return_up.alias}">
                                            <span class="red">*必填</span>
                                        </div>
                                        <div class="input-pad">
                                            <span class="input-label1">选择经纬度：</span>
                                            <if condition="$return_up['longitude'] neq ''">
                                                <input type="text" name="return_start_lgtltt" 
                                                    value="{$return_up.longitude},{$return_up.latitude}">
                                                <else />
                                                <input type="text" name="return_start_lgtltt"  value="">
                                            </if>

                                            <span class="line-btn" style="margin-left: 15px;"
                                                onclick="choiceLgdLtd(this,2)">选择经纬度</span>
                                            <span class="red">*必填</span>
                                        </div>
                                    </div>
                                    <div id="up-enywhere2" <if condition="$line['return_start_address_type'] == 1">
                                        style="display:none" </if>>
                                        <div class="input-pad">
                                            <span class="input-label1">上车点名称：</span>
                                            <input type="text" name="return_start_alias2"
                                                value="{$line.return_start_alias}">
                                            <span class="red">*必填</span>
                                        </div>
                                        <div class="input-pad">
                                            <span class="input-label1">任意范围：</span>
                                            <input type="text" name="return_start_polygon" 
                                                value="{$return_start_polygon}">
                                            <span class="line-btn" style="margin-left: 15px;"
                                                onclick="choiceRange(this,2)">选择范围</span>
                                            <span class="red">*必须选择</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="buybus-addr return-lineset-view" <eq name="line['is_return']" value="0"> style="display:none"
                                <else /> style="margin-top: 15px;"</eq>>
                                <span class="updow-che-words">下车点</span>
                                <div>
                                    <div class="input-pad">
                                        <span class="input-label1">下车类型：</span>
                                        <input type="radio" name="return_end_address_type" value="1"
                                            id="return_end_address_type1" <if
                                            condition="$line['return_end_address_type'] eq 1"> checked </if>
                                        onclick="showHRange(2,1)">
                                        <label for="return_end_address_type1">固定位置</label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <input type="radio" name="return_end_address_type" value="2"
                                            id="return_end_address_type2" <if
                                            condition="$line['return_end_address_type'] eq 2"> checked </if>
                                        onclick="showHRange(2,2)">
                                        <label for="return_end_address_type2">上门接送</label>
                                    </div>

                                    <div id="down-enywhere1" <if condition="$line['return_end_address_type'] == 2">
                                        style="display:none" </if>>
                                        <div class="input-pad">
                                            <span class="input-label1">下车点名称：</span>
                                            <input type="text" name="return_end_alias1" value="{$return_down.alias}">
                                            <span class="red">*必填</span>
                                        </div>
                                        <div class="input-pad">
                                            <span class="input-label1">选择经纬度：</span>
                                            <if condition="$return_down['longitude'] neq ''">
                                                <input type="text" name="return_end_lgtltt" 
                                                    value="{$return_down.longitude},{$return_down.latitude}">
                                                <else />
                                                <input type="text" name="return_end_lgtltt"  value="">
                                            </if>

                                            <span class="line-btn" style="margin-left: 15px;"
                                                onclick="choiceLgdLtd(this,1)">选择经纬度</span>
                                            <span class="red">*必填</span>
                                        </div>
                                    </div>
                                    <div id="down-enywhere2" <if condition="$line['return_end_address_type'] == 1">
                                        style="display:none" </if>>
                                        <div class="input-pad">
                                            <span class="input-label1">下车点名称：</span>
                                            <input type="text" name="return_end_alias2"
                                                value="{$line.return_end_alias}">
                                            <span class="red">*必填</span>
                                        </div>
                                        <div class="input-pad">
                                            <span class="input-label1">任意范围：</span>
                                            <input type="text" name="return_end_polygon" 
                                                value="{$return_end_polygon}">
                                            <span class="line-btn" style="margin-left: 15px;"
                                                onclick="choiceRange(this,1)">选择范围</span>
                                            <span class="red">*必须选择</span>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div style="text-align: left;margin: 20px;margin-left: 50px;">
                                退票时间：<input type="number" name="refund_time_set" value="{$line.refund_time_set}">
                                <span class="red">注意:发车前多长时间不允许退票,不设置默认为0分钟</span>
                            </div>

                            <input type="hidden" name="start_address_type" value="1">
                            <input type="hidden" name="end_address_type" value="1">
                            <input type="hidden" name="delete_ids" value="">
                            <input type="hidden" name="id" value="{$line.id}">
                            <!--返程上车ID-->
                            <input type="hidden" name="return_up_id" value="{$return_up.id}">
                            <!--返程下车ID-->
                            <input type="hidden" name="return_down_id" value="{$return_down.id}">

                        </div>

                        <!--自定义设置end-->
                        <p class="clr"></p>
                        <!--场景分类-->
                        <div style="margin-top: 10px;">

                            <div style="text-align: left;font-size: 27px;">
                                <div class="self-set">场景分类</div>
                            </div>

                            <div class="return-lineset">
                                <volist name="lineCategory" id="vo" key="index">
                                    <label class="line-c-label">{$vo['title']}
                                        <input type="checkbox" name="line_cat_ids[]" {$vo['checked']}
                                            value="{$vo['id']}" style="width: 15px;height: 15px;">
                                    </label>
                                </volist>

                            </div>
                            <div class="comfirm-btn" style="text-align: left;margin: 20px;margin-left: 121px;">
                                <button type="button" class="sure-save" onclick="submitForm()">确定</button>
                            </div>
                        </div>
                    </form>
                </div>


            </div>

        </div>

    </div>

    <script type="text/javascript" src="{$FILE_ROOT}Theme/lib/jquery-1.8.3.min.js"></script>
    <script src="/Theme/Admin/layui/xadmin.js"></script>
    <!-- 引入 layui.js -->
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/base.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/line.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/page.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="{$FILE_ROOT}Theme/Admin/md5.min.js"></script>
    <script src="{$FILE_ROOT}Theme/Admin/js/layui-2.9.18.js"></script>


    <!--输入提示-->

    <script type="text/javascript">
        $('input[name=start_time_type]:checked').trigger('click')
        var editMapType = 1; //默认地图编辑弹窗为出发地
        //范围的adcode
        var adcode = '';
        //删除的id集合
        var delete_ids = '';
        //上车类型(固定和任意)
        var start_address_type = "{$line['start_address_type']}";//{ $line.start_address_type };
        //下车类型
        var end_address_type = "{$line['end_address_type']}";//{ $line.end_address_type };
        var thisobj;
        //是否包含返程后
        var is_return = "{$line['is_return']}";//{ $line.is_return };

        //删除站点 - 重新排序序号
        $(document).on('click', '.delete', function () {
            if (confirm('你确定真的删除吗?')) {
                var _this = this;
                var temp_ids = $(_this).parents('.car-blist').find("input[name='up_id[]']").val();
                if (temp_ids == undefined) {
                    temp_ids = $(_this).parents('.car-blist').find("input[name='down_id[]']").val();
                }
                if (temp_ids) {
                    delete_ids += temp_ids + ',';
                }
                var index = $(".car-blist").index($(_this).parent('.car-blist'));
                if ($('.car-blist').length > (index + 1)) {
                    //重新排序
                    $(this).parent('.car-blist').nextAll().each(function (ii, ee) {
                        var temmber = $(ee).find('.left-number').html();
                        $(ee).find('.left-number').html(temmber - 1);
                    });
                }

                $(this).parents('.car-blist').remove();
            }

        });

        //新增上车地点
        function addupaddr(obj, is_up, type = 1) {
            if (is_up == 1) {
                if (type == 1) {
                    $(obj).parent().before(getUpDownStr('up'));
                } else {
                    $(obj).parent().before(getAnyUpDownStr('up'));
                }
            } else {
                if (type == 1) {
                    $(obj).parent().before(getUpDownStr('down'));
                } else {
                    $(obj).parent().before(getAnyUpDownStr('down'));
                }
            }
        }

       //新增价格区域
       function addpriceArea(obj,type) {
            $(obj).next().append(priceAreaHtml(type));
        }
       //删除区域

       $(document).on('click', '.delete-area', function () {
             var $this = $(this);
        layer.confirm('确认删除当前自定义价格区域？', {icon: 3}, function(){
          $this.parents('.layui-col-space16').remove();
          layer.closeAll()
        // layer.msg('点击确定的回调', {icon: 1});
      }, function(){
        // layer.msg('点击取消的回调');
      });

        });

        //tab切换
        function upAddrTab(obj, is_up) {
            if (is_up == 1) {
                //上车点
                start_address_type = $(obj).attr('value');
                if (start_address_type == 1) {
                    //固定上车地点

                    $('.up-buybusmethod1').show();
                    $('.up-buybusmethod2').hide();
                } else {
                    //任意上车地点
                    $('.up-buybusmethod1').hide();
                    $('.up-buybusmethod2').show();
                }

            } else {
                //下车点
                end_address_type = $(obj).attr('value');
                if (end_address_type == 1) {
                    //固定上车地点

                    $('.down-buybusmethod1').show();
                    $('.down-buybusmethod2').hide();
                } else {
                    //任意上车地点
                    $('.down-buybusmethod1').hide();
                    $('.down-buybusmethod2').show();
                }

            }
            $('.buybus-tab' + is_up).removeClass('buybus-selected' + is_up);

            $(obj).addClass('buybus-selected' + is_up);
        }

        //选择经纬度
        function choiceLgdLtd(obj, nnuumm) {
            adcode = getUpCode(nnuumm);
            if (!adcode) {
                return;
            }
            thisobj = obj;
            x_admin_show('编辑', '/Theme/Admin/html/openmap.html')
            /*$('#open-iframe2').css('display','block');
            $('#menban').css('display','block');*/
        }

        //选择范围
        function choiceRange(obj, nnuumm) {
            adcode = getUpCode(nnuumm);
            if (!adcode) {
                return;
            }
            //window.parent['child3'].location.reload()
            thisobj = obj;
            x_admin_show('编辑', '/Tpl/Admin/Line/region_map.html');
            /* $('#open-iframe3').css('display','block');
             $('#menban').css('display','block');*/
        }


        function setArea(obj, nnuumm){

          adcode = getUpCode(nnuumm);
            if (!adcode) {
                return;
            }
            //window.parent['child3'].location.reload()
            thisobj = obj;
            // x_admin_show('编辑', '/Tpl/Admin/Line/region_map.html');
            x_admin_show('编辑价格区域', '/Tpl/Admin/Line/region_area_map.html');
          
        }

    //1上车区域 2下车区域
        function priceAreaHtml(type){  

          return `<div class="layui-row layui-col-space16">
                    <input type="hidden" name="${type===1?'pickup_polygons[seq][]':'dropoff_polygons[seq][]'}" value="{$vo.seq}" />
                      <div class="layui-col-md3">
                        <div class="layui-input-group">
                          <div class="layui-input-split layui-input-prefix">
                            区域名称
                          </div>
                          <input type="text" name="${type===1?'pickup_polygons[title][]':'dropoff_polygons[title][]'}" pickup_polygons placeholder="请输入" class="layui-input">
                        </div>
                      </div>
                      <div class="layui-col-md4">
                        <div class="layui-input-group">
                          <div class="layui-input-split layui-input-prefix item-required">
                            区域范围
                          </div>
                          <input type="text" name="${type===1?'pickup_polygons[polygons][]':'dropoff_polygons[polygons][]'}" placeholder="请选择"  class="layui-input">
                          <div class="layui-input-suffix" style="padding: 0;">
                            <span class="layui-btn layui-btn-primary layui-border-green selected-area-view"  onclick="setArea(this,${type})">选择</span>
                          </div>
                        </div>
                      </div>
                      <div class="layui-col-md3">
                        <div class="layui-input-group">
                          <div class="layui-input-split layui-input-prefix  item-required">
                            区域价格
                          </div>
                          <input type="text"  name="${type===1?'pickup_polygons[price][]':'dropoff_polygons[price][]'}"  placeholder="请输入" class="layui-input">
                        </div>
                      </div>
                      <div class="layui-col-md1">
                        <button type="button" class="layui-btn  layui-btn-primary delete-area">
                          <i class="layui-icon layui-icon-delete"></i>
                        </button>
                      </div>
                    </div>`
        }



        //获取站点字符串
        function getUpDownStr(str) {
            var _words = '下';
            var nnuumm = 2;
            if (str == 'up') {
                _words = '上';
                nnuumm = 1;
            }
            var _html = '<div class="' + str + 'car-list1 car-blist">' +
                '<input type="hidden" name="' + str + '_id[]" value=""/>' +
                '<div class="left-number">' + ($('.' + str + 'car-list1').length + 1) + '</div>' +
                '<div class="content-center">' +
                '<div class="line-op-pa">' +
                '<span class="input-label1">' + _words + '车点名称：</span>' +
                '<input type="text" name="' + str + '_alias1[]" style="width: 200px;">' +
                '<span class="red">*必填</span>' +
                '</div>' +
                '<div class="line-op-pa">' +
                '<span class="input-label1">选择经纬度：</span>' +
                '<input type="text" name="' + str + '_lgtlat1[]" style="width: 250px;" >' +
                '<span class="line-btn" onclick="choiceLgdLtd(this,' + nnuumm + ')" style="margin-left: 15px;">选择经纬度</span>' +
                '</div>' +
                '<div class="line-op-pa">' +
                '<span class="input-label1">耗时：</span>' +
                '<input type="number" name="' + str + '_use_time1[]" style="width: 145px;">' +
                '分钟<span class="red">(*必填)</span>填写从始发站到当前站的耗时' +
                '</div>' +
                `<div class="line-op-pa">
                    <span class="input-label1">站点价格：</span>
                    <input type="number" name="${str}_additional_fee1[]"
                        value="{$vo.additional_fee}" placeholder="" style="width: 145px;">
                    注：非必填，填写后将在基础票价上加价，如需减价输入负数即可
                </div>`

                '</div>' +
                '<div class="delete delete-tab1">删<br>除</div>' +
                '</div>';
            return _html;
        }

        //获取车场字符串
        function getAnyUpDownStr(str) {
            var _words = '下';
            var nnuumm = 2;
            if (str == 'up') {
                nnuumm = 1;
                _words = '上';
            }
            var _html = '<div class="' + str + 'car-list2 car-blist">' +
                '<div class="left-number">' + ($('.' + str + 'car-list2').length) + '</div>' +
                '<div class="content-center" style="background-color: #F2F2F2">' +
                '<div class="line-op-pa">' +
                '<span class="input-label1">' + _words + '车场名称：</span>' +
                '<input type="text" name="' + str + '_alias2[]" style="width: 200px;">' +
                '<span class="red">*必填</span>' +
                '</div>' +
                '<div class="line-op-pa">' +
                '<span class="input-label1">选择经纬度：</span>' +
                '<input type="text" name="' + str + '_lgtlat2[]" style="width: 250px;" >' +
                '<span class="line-btn" onclick="choiceLgdLtd(this,' + nnuumm + ')" style="margin-left: 15px;">选择经纬度</span>' +
                '</div>' +
                '</div>' +
                '<div class="delete delete-tab1">删<br>除</div>' +
                '</div>';
            return _html;
        }

//         $(document).ready(function() {

//           $('.explain-text').hover(function () {
//             layer.tips('默认向右',$('.explain-text'));
//         }, function () {

//         });
// });

        //地图 接受地图的地址
        function setarticleid(address, getLng, getLat, adcode, citycode, procincecode) {
            $(thisobj).prev().val(getLng + ',' + getLat);;


            closewindow();
        }

        //关闭弹窗
        function closewindow() {
            $('.layui-layer-shade').click();

        }

        //点击蒙版关闭弹窗
        $('#menban').click(function () {
            closewindow();
        });

        //区域 子级页面回调-设置区域
        function setCircelVal($resu) {
            $(thisobj).prev().val($resu);
        }

      //设置价格区域围栏数据
        function setCircelPriceVal($resu) {
            $(thisobj).parent().prev().val($resu);
        }

        //单选固定位置 - 上门接送
        function showHRange(type1, type2) {
            var _words = 'up-';
            if (type1 == 2) {
                _words = 'down-';
            }
            var _ty = 1
            if (type2 == 1) {
                _ty = 2;
            }
            $('#' + _words + 'enywhere' + type2).show();
            $('#' + _words + 'enywhere' + _ty).hide();

        }

        //固定发车时间-滚动发车时段
        function starTiTy(val) {
            if (val == 1) {
                $('#ongo-time').show();
                $('.mon-env-time').hide();
            } else {
                $('#ongo-time').hide();
                $('.mon-env-time').show();
            }
        }

        //选座设置
        function seatOptional(val) {
            if (val == 0) {
                $('.no-seat-optional').show();
                $('.seat-optional').hide();
            } else {
                $('.no-seat-optional').hide();
                $('.seat-optional').show();
            }
        }

        //选座设置
        function carSeats(val, layout) {
            $('.car-seats-5').hide();
            $('.car-seats-5-input').attr('disabled', '');
            $('.car-seats-5-name').attr('disabled', '');

            $('.car-seats-7').hide();
            $('.car-seats-7-input').attr('disabled', '');
            $('.car-seats-7-name').attr('disabled', '');

            $('.car-seats-9-2-2-2-3').hide();
            $('.car-seats-9-2-2-2-3-input').attr('disabled', '');
            $('.car-seats-9-2-2-2-3-name').attr('disabled', '');

            $('.car-seats-9-3-3-3').hide();
            $('.car-seats-9-3-3-3-input').attr('disabled', '');
            $('.car-seats-9-3-3-3-name').attr('disabled', '');

            $('.car-seats-15-3-2-3-3-4').hide();
            $('.car-seats-15-3-2-3-3-4-input').attr('disabled', '');
            $('.car-seats-15-3-2-3-3-4-name').attr('disabled', '');

            $('input[name="seat_layout"]').val(layout);
            if (val == 5) {
                $('.car-seats-5').show();
                $('.car-seats-5-input').removeAttr('disabled');
                $('.car-seats-5-name').removeAttr('disabled');
            } else if (val == 7) {
                $('.car-seats-7').show();
                $('.car-seats-7-input').removeAttr('disabled');
                $('.car-seats-7-name').removeAttr('disabled');
            } else if (val == 9 && layout == '2-2-2-3') {
                $('.car-seats-9-2-2-2-3').show();
                $('.car-seats-9-2-2-2-3-input').removeAttr('disabled');
                $('.car-seats-9-2-2-2-3-name').removeAttr('disabled');
            } else if (val == 9 && layout == '3-3-3') {
                $('.car-seats-9-3-3-3').show();
                $('.car-seats-9-3-3-3-input').removeAttr('disabled');
                $('.car-seats-9-3-3-3-name').removeAttr('disabled');
            } else if (val == 15) {
                $('.car-seats-15-3-2-3-3-4').show();
                $('.car-seats-15-3-2-3-3-4-input').removeAttr('disabled');
                $('.car-seats-15-3-2-3-3-4-name').removeAttr('disabled');
            }
        }

        //表单提交
        function submitForm() {

            //表单验证
            if ($('#start_province_code option:selected').val() == 0) {
                alert('请选择出发所在省'); return;
            }
            if ($('#start_city_code option:selected').val() == 0) {
                alert('请选择出发所在市'); return;
            }

            if ($('input[name=start_name]').val() == '') {
                alert('请填写出发地'); return;
            }

            //上车点验证
            if (!fixedVerification(1, start_address_type)) {
                return;
            }

            if ($('#end_province_code option:selected').val() == 0) {
                alert('请选择目的地所在省'); return;
            }
            if ($('#end_city_code option:selected').val() == 0) {
                alert('请选择目的地所在市'); return;
            }
            if ($('input[name=end_name]').val() == '') {
                alert('请填写目的地'); return;
            }
            //下车验证
            if (!fixedVerification(2, end_address_type)) {
                return;
            }
            var start_time_type = $('input[name="start_time_type"]:checked').val();
            if (start_time_type == 1) {
                if ($('input[name=start_time]').val() == '') {
                    alert('请选择发车时间'); return;
                }
            } else {
                if ($('input[name=start_earliest_time]').val() == '') {
                    alert('请选择发车时间'); return;
                }
                if ($('input[name=end_latest_time]').val() == '') {
                    alert('请选择发车时间'); return;
                }
            }
            if ($('#branch_id option:selected').val() == 0) {
                alert('请选择所属分台'); return;
            }
            if ($('input[name=is_seat_selection]:checked').val() == 0) {
                if ($('input[name=price]').val() == '') {
                    alert('请填写价格'); return;
                } else if ($('input[name=price]').val() <= 0) {
                    alert('请填写正确的价格，价格不能小于等于0'); return;
                }
                if ($('input[name=channel_price]').val() == '') {
                    alert('请填写价格'); return;
                } else if ($('input[name=channel_price]').val() <= 0) {
                    alert('渠道价格不能小于等于0，若没有渠道时，请将渠道价格和价格保持一致。'); return;
                }
                if ($('input[name=channel_price]').val() > $('input[name=price]').val()) {
                    alert("'渠道价格'不能高于'价格'"); return;
                }
            }

            $('input[name=end_address_type]').val(end_address_type);
            $('input[name=start_address_type]').val(start_address_type);
            $('input[name=delete_ids]').val(delete_ids);

            // // 验证发车时间类型
            // if ($('input[name="starting_time[]"]').length > 1 || $('input[name="starting_interval_time[]"]').length > 1 || $('input[name="end_interval_time[]"]').length > 1) {
            //     if ($('input[name=stop_sale_minutes]').val() != '') {
            //         alert('发车时间类型选择了多个固定发车时间或者多个滚动发车时段，不能填写停售时刻，只支持填写停售分钟');
            //         return;
            //     }
            // }

            //自定义设置验证
            if ($('input[name="is_return"]:checked').val()) {
                //验证自定义设置数据


                if ($('input[name="return_start_time_type"]:checked').val() == 1) {
                    if ($('input[name="return_start_time"]').val() == '') {
                        alert('请选择返程发车时间');
                        return;
                    }
                }
                else {

                    if ($('input[name="return_start_earliest_time"]').val() == '') {
                        alert('请选择返程最早发车时间');
                        return;
                    }
                    if ($('input[name="return_end_latest_time"]').val() == '') {
                        alert('请选择返程最晚发车时间');
                        return;
                    }
                }
                console.log($('input[name=return_start_address_type]:checked').val());

                if ($('input[name=return_start_address_type]:checked').val() == 1) {


                    if ($('input[name=return_start_alias1]').val() == '') {
                        alert('请填写返程上车点名称');
                        return;
                    }
                    if ($('input[name=return_start_lgtltt]').val() == '') {
                        alert('请选择返程上车点经纬度');
                        return;
                    }


                } else {
                    if ($('input[name=return_start_alias2]').val() == '') {
                        alert('请填写返程上车点名称');
                        return;
                    }
                    if ($('input[name=return_start_polygon]').val() == '') {
                        alert('请选择返程上车点范围');
                        return;
                    }

                }
                if ($('input[name=return_end_address_type]:checked').val() == 1) {


                    if ($('input[name=return_end_alias1]').val() == '') {
                        alert('请填写返程下车点名称');
                        return;
                    }
                    if ($('input[name=return_end_lgtltt]').val() == '') {
                        alert('请选择返程下车点经纬度');
                        return;
                    }


                } else {
                    if ($('input[name=return_end_alias2]').val() == '') {
                        alert('请填写返程下车点名称');
                        return;
                    }
                    if ($('input[name=return_end_polygon]').val() == '') {
                        alert('请选择返程下车点范围');
                        return;
                    }

                }

            }

            //提交
            $.ajax({
                type: "POST",
                url: "/Admin/LineManager/doEditLineClassIn",
                data: $('#form').serialize(),
                async: false,
                dataType: 'json',
                error: function (request) {
                    alert("请求错误");
                },
                success: function (data) {
                    console.log(data);
                    if (data.code == 200) {
                        alert(data.msg);
                        window.location.href = "/line_class_list";
                    } else {
                        alert(data.msg);
                    }
                }
            });
        }

        //子页面获取adcode
        function getAdcode() {
            return adcode;
        }

        //子页面获取当前围栏  
        function getAreaBase() {  
            return  editMapType===1? $('#start_polygon').val():$('#end_polygon').val();
        }
        function getAreaPriceCurrent(){
           return $(thisobj).parents('.layui-col-space16').index();
        }

        function getAreaPrice(){
          const all_area_lists_up=$(thisobj).parents('.area-price-list').find("input[name='pickup_polygons[polygons][]']").map(function() {
            return $(this).val();
          }).get();
          const all_area_lists_down=$(thisobj).parents('.area-price-list').find("input[name='dropoff_polygons[polygons][]']").map(function() {
            return $(this).val();
          }).get();
          return editMapType===1?all_area_lists_up:all_area_lists_down;
        }

        
        
        layui.use('laydate', function () {
            var laydate = layui.laydate;

            // 时间选择器
            laydate.render({
                elem: '.starting-time-layout-input',
                type: 'time',
            });
            laydate.render({
                elem: '#start_earliest_time',
                type: 'time',

            });
            laydate.render({
                elem: '#end_latest_time',
                type: 'time',

            });
            laydate.render({
                elem: '#stop_sell_time',
                type: 'time',

            });
            laydate.render({
                elem: '#return_start_time',
                type: 'time',

            });
            laydate.render({
                elem: '#return_start_earliest_time',
                type: 'time',

            });
            laydate.render({
                elem: '#return_end_latest_time',
                type: 'time',

            });
            // 时间范围
            laydate.render({
                elem: '.interval-time-layout-input',
                type: 'time'
            });
        });

        function fixedVerification(isup = 1, isfixed = 1) {
            // isup=>是否为上车 1是 isfixed=>是否为固定上车点 1是
            var wor = 'down';
            var rd = '下';
            var up = 'end';
            if (isup == 1) {
                //上车
                wor = 'up';
                rd = '上';
                up = 'start';
            }
            if (isfixed == 1) {
                //是固定的上车点

                //是否含有一个元素
                if ($('.' + wor + 'car-list1').length == 0) {
                    alert('请至少添加一个固定' + rd + '车点');
                    return false;
                }
                //循环验证
                var result = true;
                $("input[name='" + wor + "_alias1[]']").each(function (ii, ee) {
                    if ($(ee).val() == '') {
                        result = false;

                        return;
                    }
                });
                if (!result) {
                    alert('你还有' + rd + '车点名称未填写完成');
                    return result;
                }
                result = true;
                $("input[name='" + wor + "_lgtlat1[]']").each(function (ii, ee) {
                    if ($(ee).val() == '') {
                        result = false;

                        return;

                    }
                });
                if (!result) {
                    alert('你还有' + rd + '车点经纬度未选择');
                    return result;
                }
                result = true;
                $("input[name='" + wor + "_use_time1[]']").each(function (ii, ee) {
                    if ($(ee).val() == '') {
                        result = false;

                        return;
                    }
                });
                if (!result) {
                    alert('你还有' + rd + '车点耗时未填写');
                    return false;
                }


            } else {
                //任意上车点 一般验证
                if ($("input[name=" + up + "_polygon]").val() == '') {
                    alert('请选择任意' + rd + '车点的范围');
                    return false;
                }

                   //限制必须填写车场位置
                const selectoras = `input[name='${up === 'start' ? 'up' : 'down'}_alias2[]']`;
                console.log(1111,selectoras);
                const alias = !$(selectoras).get().some(input => $(input).val() === '');
                console.log(4444,alias);

                if (!alias) {
                    alert('请完善' + rd + '车点车场的名称');
                    return alias;
                }
                const selector = `input[name='${up === 'start' ? 'up' : 'down'}_lgtlat2[]']`;
                console.log(2222,selector);
                const lgtlat = !$(selector).get().some(input => $(input).val() === '');
                console.log(3333,lgtlat);
                if (!lgtlat) {
                    alert('请完善' + rd + '车点车场经纬度');
                    return lgtlat;
                }

                if (wor == 'down') {
                    if ($("input[name=" + up + "_polygon_use_time]").val() == '') {
                        alert('请填写任意' + rd + '车点的耗时');
                        return false;
                    }
                }
            }
            return true;

        }
        function showReturn(obj) {
            if ($(obj).is(':checked')) {
                //显示
                $('.buybus-addr').show();
            } else {
                //隐藏
                $('.buybus-addr').hide();
            }

        }
        function getUpCode(nnuumm) {
            //定义当前弹窗是设置起点还是终点
            editMapType=nnuumm
            var thcode = false;
            if (nnuumm == 1) {
                //出发地
                var start_province_code = $('#start_province_code option:selected').val();
                if (start_province_code == '0') {
                    alert('请先选择出发地');
                    return false;
                }
                var start_city_code = $('#start_city_code option:selected').val();
                if (start_city_code == '0') {
                    return start_province_code;
                }
                var start_area_code = $('#start_area_code option:selected').val();
                if (start_area_code == '0' || $('#start_area_code option:selected').text() == '市辖区') {
                    return start_city_code;
                }
                thcode = start_area_code;
            } else {
                //目的地
                var end_province_code = $('#end_province_code option:selected').val();
                if (end_province_code == '0') {
                    alert('请先选择目的地');
                    return false;
                }
                var end_city_code = $('#end_city_code option:selected').val();
                if (end_city_code == '0') {
                    return end_province_code;
                }
                var end_area_code = $('#end_area_code option:selected').val();
                if (end_area_code == '0' || $('#end_area_code option:selected').text() == '市辖区') {
                    return end_city_code;
                }
                thcode = end_area_code;
            }
            return thcode;
        }
        function returnUpTime(val) {
            if (val == 1) {
                //固定发车时间
                $('.return_time_only').show();
                $('.return_time_double').hide();

            }
            else {
                //滚动发车时段
                $('.return_time_only').hide();
                $('.return_time_double').show();
            }
        }

        /** 
         * 增加固定发车时间
         * @param obj
         * @param first
         */
        function appendStartingTime(obj) {
            var cloneElement = '<div class="starting-time-layout-div-elem"><input type="text" class="layui-input-sm textfield date starting-time-layout-input" name="starting_time[]" value="" placeholder="时:分:秒">&nbsp;<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeStartingTime(this, false)"> ➖ </button></div>'
            $(obj).parent().prev().append(cloneElement)
            var laydate = layui.laydate
            laydate.render({
                elem: '.starting-time-layout-input',
                type: 'time',
            });
        }

        /**
         * 增加滚动发车时间
         * @param obj
         * @param first
         */
        function appendInervalTime(obj) {
            var cloneElement = '<div class="interval-time-layout-div-btn"><input type="text" class="layui-input-sm textfield date interval-time-layout-input" name="starting_interval_time[]" placeholder="最早发车时间"> → <input type="text" class="layui-input-sm textfield date interval-time-layout-input" name="end_interval_time[]" placeholder="最晚发车时间"> <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeIntervalTime(this, false)"> ➖ </button></div>'
            $(obj).parent().prev().append(cloneElement)
            var laydate = layui.laydate
            laydate.render({
                elem: '.interval-time-layout-input',
                type: 'time',
            });
        }

        /**
         * 移除固定发车时间
         * @param obj
         * @param first
         */
        function removeStartingTime(obj, first = true) {
            if (first == true) {
                alert('至少保留一个固定发车时间');
            } else {
                var index = $(".starting-time-layout-div-elem").length
                if (index <= 1) {
                    alert('至少保留一个固定发车时间');
                } else {
                    $(obj).parent().remove()
                }
            }
        }

        /**
         * 移除滚动发车时段
         * @param obj
         * @param first
         */
        function removeIntervalTime(obj, first = true) {
            if (first == true) {
                alert('至少保留一个滚动发车时段');
            } else {
                var index = $(".interval-time-layout-div-btn").length
                if (index <= 1) {
                    alert('至少保留一个滚动发车时段');
                } else {
                    $(obj).parent().remove()
                }
            }
        }
    </script>


</body>

</html>

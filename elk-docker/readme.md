# ELK Stack Docker 镜像构建和使用说明


## 1. 项目结构

创建一个文件夹用于存放 ELK 配置文件和 docker-compose.yml：

```
elk-docker/
├── docker-compose.yml
├── elasticsearch/
│   └── config/
│       └── jvm.options
├── logstash/
│   └── pipeline/
│       └── logstash.conf
```

## 2. Docker Compose 配置文件

在项目根目录下创建 docker-compose.yml，内容如下：

```
services:
  elasticsearch:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/elasticsearch/elasticsearch:8.15.1
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - ./elasticsearch/config/jvm.options:/usr/share/elasticsearch/config/jvm.options
    platform: linux/amd64

  kibana:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/kibana/kibana:8.15.1
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana
      - XPACK_SECURITY_ENABLED=false
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    platform: linux/amd64

  logstash:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/logstash/logstash:8.15.1
    container_name: logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xms512m -Xmx512m"
    depends_on:
      - elasticsearch
    platform: linux/amd64

volumes:
  es_data:
```


## 3. Elasticsearch 配置

创建 elasticsearch/config/jvm.options 文件，用于限制 Elasticsearch JVM 内存使用：

```
-Xms512m
-Xmx512m
```

## 4. Logstash 配置

### Logstash 数据管道

创建 logstash/pipeline/logstash.conf 文件，定义数据输入、过滤和输出规则：

```
input {
  beats {
    port => 5044
  }
}

filter {
  json {
    source => "message"
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "logs-%{+YYYY.MM.dd}"
  }
  stdout { codec => rubydebug }
}
```

## 5. 构建和运行


### 启动 ELK 服务

1. 启动服务：
<!-- -->

```
docker-compose up -d

```

1. 查看运行中的容器状态：
<!-- -->

```
docker ps
```

### 验证服务

1. 访问 Elasticsearch 2. [http://localhost:9200](http://localhost:9200)3.  ，返回一下内容
<!-- -->

```
{
  "name": "elasticsearch",
  "cluster_name": "docker-cluster",
  "cluster_uuid": "xyz123",
  "version": { "number": "8.15.1" }
}
```


1. 访问 Kibana 2. [http://localhost:5601](http://localhost:5601) 
<!-- -->


## 6. 使用说明


### 在 Kibana 查看日志

1. 打开 Kibana，进入 “Stack Management” -> “2. **Kibana**3. ” -> “4. **Data Views**5. ”。
6. 点击 “Create index pattern”。
7. 输入 logs-* 作为索引模式，并选择时间字段 @timestamp。
8. 保存后，进入 “Discover” 页面，即可查看日志。
<!-- -->

### 发送测试日志到 Logstash

使用 curl 发送测试日志：

```
curl -X POST "http://localhost:5044" -H 'Content-Type: application/json' -d '{"message": "Test log message"}'
```

## 7. 常见问题和解决方法

### 问题 1：Elasticsearch 容器启动失败

**原因**：内存不足或虚拟内存设置不正确。

**解决方法**：

1. 增加宿主机的虚拟内存限制：
<!-- -->

```
sudo sysctl -w vm.max_map_count=262144
echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

1. 检查宿主机是否有足够的内存（建议至少 2GB）。
<!-- -->

### 问题 2：Kibana 无法访问 Elasticsearch

**原因**：Logstash 和 Elasticsearch 连接失败。

**解决方法**：

1. 检查 Logstash 输出部分的 hosts 是否正确。
2. 查看 Logstash 容器日志：
<!-- -->

```
docker logs logstash
```

## 8. 环境清理

停止并删除所有容器和卷： 
```
docker-compose down -v
```

清理未使用的镜像和网络：

```
docker system prune -af
```

完成以上配置后，ELK 堆栈将正常运行，Kibana 可以显示日志。根据需求修改 Logstash 数据管道处理规则或添加其他数据输入源！


# 将 Laravel 项目的日志集成到 ELK (Elasticsearch, Logstash, Kibana) 中，可以通过配置 Laravel 的日志驱动，将日志输出到 Logstash


## 1：配置 Laravel 日志驱动

### 修改 config/logging.php

为 Laravel 添加一个自定义的日志通道，将日志输出到 Logstash。

```
return [
    'channels' => [
        // 其他通道...

        'logstash' => [
            'driver' => 'monolog',
            'handler' => Monolog\Handler\SocketHandler::class,
            'with' => [
                'connectionString' => 'tcp://127.0.0.1:5044', // Logstash 的 Beats 输入地址
            ],
            'formatter' => Monolog\Formatter\JsonFormatter::class,
        ],
    ],
];
```


### 设置默认日志通道

将默认的日志通道设置为 logstash：

```
'default' => env('LOG_CHANNEL', 'logstash'),
```

这样 Laravel 的所有日志都会通过 logstash 通道发送到 Logstash。


## 2：配置 Logstash


### 创建 Logstash 配置文件

在 Logstash 的 pipeline 文件夹下创建或修改 logstash.conf：

```
input {
  tcp {
    port => 5044
    codec => json
  }
}

filter {
  # 添加日志过滤器（可选）
  mutate {
    add_field => {
      "application" => "laravel"
      "environment" => "production"
    }
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "laravel-logs-%{+YYYY.MM.dd}"
  }
  stdout {
    codec => rubydebug
  }
}
```


### 重启 Logstash

修改配置文件后，重启 Logstash 服务：

```
docker-compose restart logstash
```


## 3：验证日志流向

### 验证 Laravel 日志输出

在 Laravel 项目中生成日志，测试是否成功发送到 Logstash： 
```
Log::info('This is a test log for ELK integration.');
Log::error('This is a test error log for ELK integration.');
```


### 检查 Logstash 是否接收日志

查看 Logstash 容器的日志输出，确认是否收到了 Laravel 的日志：

```
docker logs higgses-logstash
```

如果配置正确，应该能在 Logstash 的输出中看到类似以下的日志：

```
{
   "message": "This is a test log for ELK integration.",
   "application": "laravel",
   "environment": "production",
   "level": "INFO",
   "timestamp": "2024-11-28T10:00:00Z"
}
```
 
## 4：在 Elasticsearch 中查看日志

### 检查 Elasticsearch 索引

运行以下命令检查是否创建了 laravel-logs-* 索引：

```
curl -X GET "http://localhost:9200/_cat/indices?v"
```

如果配置正确，你应该能看到类似以下的索引：

```
health status index               uuid                   pri rep docs.count docs.deleted store.size pri.store.size
green  open   laravel-logs-2024.11.28 abc123...          1   0         100            0      5mb          5mb
```


## 5：在 Kibana 中查看日志


### 创建 Data View

1. 打开 Kibana，进入 2. **“Management” -> “Data Views”**3. 。
4. 点击 5. **“Create data view”**6. 。
7. 输入 laravel-logs-* 作为索引模式，选择时间字段 @timestamp。
8. 保存。
<!-- -->

### 查看日志

1. 在 Kibana 左侧导航栏，进入 2. **“Discover”**3. 。
4. 选择刚刚创建的 Data View，查看 Laravel 的日志数据。

services:
  elasticsearch:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/elasticsearch/elasticsearch:8.15.1
    container_name: higgses-elasticsearch
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - xpack.security.enabled=true
    ulimits: 
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
    volumes:
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/config/jvm.options:/usr/share/elasticsearch/config/jvm.options
    platform: linux/amd64
    networks:
      - higgses-elk-network

  kibana:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/kibana/kibana:8.15.1
    container_name: higgses-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://higgses-elasticsearch:9200
      - SERVER_NAME=kibana
      - XPACK_SECURITY_ENABLED=true
      - I18N_LOCALE=zh-CN
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=4x49*ZDOWe0OxjOXOcVY
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    platform: linux/amd64
    networks:
      - higgses-elk-network

  logstash:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.elastic.co/logstash/logstash:8.15.1
    container_name: higgses-logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044" # Beats input port
      - "9600:9600" # Logstash monitoring API
    environment:
      LS_JAVA_OPTS: "-Xms512m -Xmx512m" # 设置 Logstash JVM 内存
    depends_on:
      - elasticsearch
    platform: linux/amd64
    networks:
      - higgses-elk-network

volumes: {}

networks:
  higgses-elk-network:
    driver: bridge

input {
  tcp {
    port => 5044
    codec => json
  }
}

filter {
  # 为缺失字段设置默认值
  if ![context][environment] {
    mutate {
      add_field => { "[context][environment]" => "local" }
    }
  }
  if ![context][channel] {
    mutate {
      add_field => { "[context][channel]" => "default" }
    }
  }

  # 动态生成索引名称
  mutate {
    add_field => {
      "[@metadata][index]" => "laravel-logs-%{[context][environment]}-%{[context][channel]}-%{+YYYY.MM.dd}"
    }
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "%{[@metadata][index]}"
    user => "elastic"                   # Elasticsearch 超级管理员用户名
    password => "gQqxG0kUEySHU4NSQOaM" # 对应的密码
  }
  stdout {
    codec => rubydebug
  }
}

version: "3"

networks:
  frontend:
    driver: ${NETWORKS_DRIVER}
  backend:
    driver: ${NETWORKS_DRIVER}

volumes:
  docker-in-docker:
    driver: ${VOLUMES_DRIVER}

services:
  ### Workspace Utilities ##################################
  workspace:
    image: registry.cn-hongkong.aliyuncs.com/higgses/workspace-74:1.3.0
    container_name: higgses-workspace-74
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
      - ./php-worker/supervisord.d:/etc/supervisord.d
      - ~/.ssh:/root/.ssh
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    expose:
      - 22
    tty: true
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
    networks:
      - frontend
      - backend
    links:
      - docker-in-docker

  ### Workspace Utilities ##################################
  workspace-83:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/workspace-83:1.0.0
    container_name: higgses-workspace-83
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - docker-in-docker:/certs/client
      - ./php-worker-83/supervisord.d:/etc/supervisord.d
      - ~/.ssh:/root/.ssh
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    expose:
      - 22
    tty: true
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
      - DOCKER_HOST=tcp://docker-in-docker:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKER_CERT_PATH=/certs/client
    networks:
      - frontend
      - backend
    links:
      - docker-in-docker

  ### PHP Worker ############################################
  php-worker:
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-worker-74:1.7.1
    container_name: higgses-php-worker-74
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - ./php-worker/supervisord.d:/etc/supervisord.d
    expose:
      - 1215 # 让其他容器访问
      - 1216 # 让其他容器访问
    ports:
      - "1215:1215" # 让宿主机和外部访问
      - "1216:1216" # 让宿主机和外部访问
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    networks:
      - backend

  ### PHP Worker ############################################
  php-worker-83:
    restart: always
    image: registry.cn-hongkong.aliyuncs.com/higgses/php-worker-83:1.0.0
    container_name: higgses-php-worker-83
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      - ./php-worker-83/supervisord.d:/etc/supervisord.d
    expose:
      - 8000 # 让其他容器访问
      - 8010 # 让其他容器访问
      - 8020 # 让其他容器访问
      - 8030 # 让其他容器访问
    ports:
      - "8000:8000" # 让宿主机和外部访问
      - "8010:8010" # 让宿主机和外部访问
      - "8020:8020" # 让宿主机和外部访问
      - "8030:8030" # 让宿主机和外部访问
    extra_hosts:
      - "dockerhost:${DOCKER_HOST_IP}"
    environment:
      - TZ=${WORKSPACE_TIMEZONE}
    networks:
      - backend

  ### Docker-in-Docker ################################################
  docker-in-docker:
    image: registry.cn-hongkong.aliyuncs.com/higgses/dokcer-in-docker:1.0.0
    container_name: higgses-docker-in-docker
    environment:
      DOCKER_TLS_SAN: DNS:docker-in-docker
    privileged: true
    volumes:
      - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
      - docker-in-docker:/certs/client
    expose:
      - 2375
    networks:
      - backend

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

return array(
    'PASSENGER_AUTH_IGNORE' => array(
        'Account.doRegister',
        'Account.doSendCellphoneValidateCode',
        'Account.doLogin',
        'Account.doForgetPassword',
        'Account.doTestingNewVersion',
        'Account.doMobileRegister',
        'Account.doMobileSendCellphoneValidateCode',
        'Account.doMobileLogin',
        'Account.doMobileBalanceTransfer',
        'Account.doMobileForgetPassword',
        'Account.doBindBank',
        'Account.doMobileAccount',
        'Account.doMobileUpdateName',
        'Account.doMobileForgetPassword',
        'Account.getMobilePassenger',
        'Account.doMobileExit',
        'Account.doWechatLogin',                            //微信登陆
        'Account.doBindingCellphone',                       //绑定手机号
        'Account.doRegister',
        'Account.getBankLists',
        'Account.getBankList',
        'Account.doGetWechatCode',
        'Account.doGetPassengerStatus',
        'Account.doGetAddressList',
        'Account.doOrderPay',
        'Account.doWebHooks',
        'Account.doWxPayNotify',
        'Account.doWxRefundNotify',
        'Account.getPassengerType',

        'Around.getAroundDrivers',
        'Around.getMobileDrivers',
        'Around.getMobileAroundDriver',
        'Around.getMobileScreenDriver',
        'Around.getAroundLines',

        'Order.doMobileBookDriver',
//        'Order.doMobileBookLine',
        'Order.doMobileCancelBook',
        'Order.doMobileGetOff',
        'Order.getMobileOrders',
        'Order.getOrders',
        'Order.getOrdersHistory',
        'Order.getMobileAgencyList',
        'Order.doMobileComputeLineFastPrice',

        'Line.getLines',
        'Line.getAutoLines',
        'Line.getAutoCharteredLines',
        'Line.getLineFreeRideDetail',
        'Line.getLineClassDate',
        'Line.getLineClassTrains',
        'Line.getLineClassTrainDetail',
        'Line.getTakeGoodsAppointmentTime',
        'Line.getPhoneLines',
        'Line.getAutoLinesByCategory',
        'Line.getRecommendLineClassTrain',

        'Ad.getAds',

        'PassengerMember.getPassengerMembers',
        'PassengerMember.doAddPassengerMember',
        'PassengerMember.doEditPassengerMember',
        'PassengerMember.doDelPassengerMember',

        'Coupon.getCoupons',

        'Navigation.getNavigation',

        'AddressCode.getAddressCodes',
        'AddressCode.getLineClassStartCode',
        'AddressCode.getEndLineClassByStartLineClassCode',
        'AddressCode.filterDingzhikeyunAddresses',
        'Weixin.getJsSdkConfig',

        /*学生号接口部分*/
        'YyAddressCode.getAroundPoint',
        'YyOrder.doBatchStudentCustomizedEverydayOrders',

        /*微信小程序接口*/
        'AccountWxMini.getWxSessionKey',
        'AccountWxMini.getWxSessionKeySingle',
        'AccountWxMini.doLoginAuthOne',
        'AccountWxMini.doLoginAuthOneSingle',
        'AccountWxMini.doLoginAuthTwo',
    ),
    'PASSENGER_LATEST_VERSION_CODE' => '1.0.6',
    'PASSENGER_LATEST_VERSION_PATH' => C('FILE_ROOT') . 'carpool/Upload/App/CarpoolPassenger.apk',
);

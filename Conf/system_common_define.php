<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of String
 *
 * <AUTHOR>
 */
class CommonDefine
{
    /*         * **************************客户端授权系统类型********************** */
    const AUTH_TYPE_1 = 1; //微信公众号；
    const AUTH_TYPE_2 = 2; //微信小程序；

    /*         * **************************系统角色**************************** */
    const SYSTEM_ROLE_0 = 0; //平台
    const SYSTEM_ROLE_1 = 1; //商户
    const SYSTEM_ROLE_2 = 2; //分台
    const SYSTEM_ROLE_3 = 3; //司机
    const SYSTEM_ROLE_4 = 4; //乘客
    const SYSTEM_ROLE_5 = 5; //接线员

    const USER_TYPE_0 = 0; //默认
    const USER_TYPE_1 = 1; //乘客
    const USER_TYPE_2 = 2; //司机
    const USER_TYPE_3 = 3; //分台

    const BRANCH_TYPE_0 = 0; //线路管理员
    const BRANCH_TYPE_1 = 1; //渠道账号

    const CHANNEL_TYPE_0 = 0; //无渠道
    const CHANNEL_TYPE_1 = 1; //分台渠道

    //渠道变量
    const CHANNEL_BRANCH_PARMTER = '_cbp';
    const CHANNEL_BRANCH_PARMTER_CBP = 'CBP';

    //司机账号变量
    const DRIVER_ENTRANCE_PARAMTER = '_dep';
    const DRIVER_ENTRANCE_PARAMTER_DEP = 'DEP';

    //乘客账号来源
    const INVITE_TYPE_0 = 0; //用户自己注册；
    const INVITE_TYPE_1 = 1; //渠道分台；

    //分台二维码类型
    const BRANCH_QR_TYPE_1 = 1; //渠道分台下单二维码

    const PACKAGE_CATEGORY_0 = 0; //基础套餐
    const PACKAGE_CATEGORY_1 = 1; //短信
    const PACKAGE_CATEGORY_2 = 2; //司机
    const PACKAGE_CATEGORY_3 = 3; //存储空间
    const PACKAGE_CATEGORY_4 = 4; //乘客人数
    const PACKAGE_CATEGORY_5 = 5; //顺风车司机人数
    const PACKAGE_CATEGORY_6 = 6; //营销短信人数

    const SMS_CONFIG_0 = 0; //关闭
    const SMS_CONFIG_1 = 1; //开启

    //订单模式
    const ROB_ORDER_0 = 0; //派单
    const ROB_ORDER_1 = 1; //抢单

    //指派状态
    const APPOINT_TYPE_0 = 0; //未指派
    const APPOINT_TYPE_1 = 1; //已指派

    //微信通知配置
    const INDUSTRY_ID1 = 1; //公众号模板消息所属行业编号
    const INDUSTRY_ID2 = 41; //公众号模板消息所属行业编号

    //账号状态
    const IS_FREEZE_0 = 0; //未删除；
    const IS_FREEZE_1 = 1; //已删除；

    //删除状态
    const IS_DEL_0 = 0; //正常；
    const IS_DEL_1 = 1; //已删除；

    //上架状态
    const IS_SHELF_0 = 0; //未上架；
    const IS_SHELF_1 = 1; //已上架；

    //显示状态
    const IS_SHOW_0 = 0; //下架隐藏；
    const IS_SHOW_1 = 1; //上架显示；

    //广告上下架状态
    const AD_STATE_1 = 1; //上架；
    const AD_STATE_2 = 2; //下架；

    //加密
    const ENCODE = 'ENCODE'; //加密
    const DECODE = 'DECODE'; //解密

    //短信类型
    const SMS_TYPE_1 = 1;   //验证码，通知短息
    const SMS_TYPE_2 = 2;   //营销短信
    const SMS_SEND_STATUS_0 = 0; //成功；
    const SMS_SEND_STATUS_1 = 1; //失败；

    //提现
    const DEPOSIT_ACCOUNT_TYPE_0 = 0; //司机
    const DEPOSIT_ACCOUNT_TYPE_1 = 1; //乘客
    const DEPOSIT_ACCOUNT_TYPE_2 = 2; //分台

    //订单
    const ORDER_CLASS_1 = 1; //约车类订单
    const ORDER_CLASS_2 = 2; //卡类订单
    const ORDER_CLASS_3 = 3; //云裕众-家长互助信息发布订单
    const ORDER_CLASS_4 = 4; //云裕众-组团定制包车
    const ORDER_CLASS_5 = 5; //含接送合成订单(摆渡车)

    //订单类型
    const ORDER_TYPE_1 = 1; //拼车；
    const ORDER_TYPE_2 = 2; //包车；
    const ORDER_TYPE_3 = 3; //带货；
    const ORDER_TYPE_4 = 4; //代办；
    const ORDER_TYPE_5 = 5; //班线；
    const ORDER_TYPE_6 = 6; //顺风车；
    const ORDER_TYPE_7 = 7; //快车；
    const ORDER_TYPE_11 = 11; //出租车；
    const ORDER_TYPE_20 = 20; //摆渡车
    const ORDER_TYPE_21 = 21; //电话叫车，无数据库意义
    const ORDER_TYPE_22 = 22; //电话叫车，无数据库意义

    const ORDER_PINCHE = '拼车'; //拼车；
    const ORDER_BAOCHE = '包车'; //包车；
    const ORDER_DAIHUO = '带货'; //带货；
    const ORDER_DAIBAN = '代办'; //代办；
    const ORDER_DINGZHIKEYUN = '定制班线车'; //班线；
    const ORDER_SHUNFENGCHE = '顺风车'; //顺风车；
    const ORDER_KUAICHE = '快车'; //快车；
    const ORDER_TAXI = '出租车'; //出租车；
    const ORDER_BAIDUCHE = '摆渡车'; //出租车；
    const ORDER_PHONE = '电话叫车'; //出租车；

    # 基本无用
    const ORDER_TYPE_8 = 8; //学生号；
    const ORDER_TYPE_9 = 9; //家长互助；
    const ORDER_TYPE_10 = 10; //学生号定制包车；

    //订单池缓存队列
    const ORDER_POOL_TAXI = 'order_pool_taxi'; //出租车待指派订单池
    const ORDER_POOL_FAST = 'order_pool_fast'; //快车待指派订单池

    //是否是代约的临时订单
    const ORDER_IS_TEMP_0 = 0; //不是
    const ORDER_IS_TEMP_1 = 1; //分台或渠道

    //订单来源
    const ORDER_CHENNEL_0 = 0; //默认非渠道
    const ORDER_CHENNEL_1 = 1; //渠道订单

    //运营状态
    const LINE_CLASS_OPERATE_O = 0; //停运中
    const LINE_CLASS_OPERATE_1 = 1; //运营中

    //出票状态
    const DRAW_TICKET_0 = 0; //未出票；
    const DRAW_TICKET_1 = 1; //出票中；
    const DRAW_TICKET_2 = 2; //出票成功；
    const DRAW_TICKET_3 = 3; //出票失败；

    //是否支持选座
    const SEAT_SELECTION_0 = 0; //不支持；
    const SEAT_SELECTION_1 = 1; //支持选座；

    //是否已经选座
    const OPTIONAL_0 = 0; //可选；
    const OPTIONAL_1 = 1; //不可选；

    //支付状态
    const PAY_STATUS_0 = 0; //未支付
    const PAY_STATUS_1 = 1; //已支付

    //退款状态：
    const REFUND_STATUS_1 = 1; //退款申请
    const REFUND_STATUS_2 = 2; //退款中
    const REFUND_STATUS_3 = 3; //退款成功
    const REFUND_STATUS_4 = 4; //退款失败

    //订单状态
    const ORDER_STATE_1 = 1; //乘客预定中
    const ORDER_STATE_2 = 2; //车主接单
    const ORDER_STATE_3 = 3; //车主确认乘客上车
    const ORDER_STATE_4 = 4; //在路上
    const ORDER_STATE_5 = 5; //已送达(未支付)
    const ORDER_STATE_6 = 6; //正常完成
    const ORDER_STATE_7 = 7; //取消
    const ORDER_STATE_8 = 8; //已关闭

    const WECHAT_MINI = 'wechat_mini';
    const WECHAT_JSAPI = 'wechat_jsapi';

    //等待状态
    const IS_WAIT_TYPE_0 = 0; //否
    const IS_WAIT_TYPE_1 = 1; //是

    //是否到达上车点
    const IS_BOARDING_POINT_0 = 0; //未到达
    const IS_BOARDING_POINT_1 = 1; //已到达
    //是否到达上车点
    const IS_ALIGHTING_POINT_0 = 0; //未到达
    const IS_ALIGHTING_POINT_1 = 1; //已到达

    //订单-快车-摆渡车（分流车）类型
    const FERRY_TYPE_0 = 0; //普通快车
    const FERRY_TYPE_1 = 1; //摆渡车-接
    const FERRY_TYPE_2 = 2; //摆渡车-送

    //定制班线-摆渡车(分流车)-接
    const IS_START_FERRY_0 = 0; //关闭
    const IS_START_FERRY_1 = 1; //开启

    //班线车-摆渡车(分流车)-送
    const IS_END_FERRY_0 = 0; //关闭
    const IS_END_FERRY_1 = 1; //开启

    //卡类型
    const CARD_TYPE_1 = 1; //次卡
    const CARD_TYPE_2 = 2; //包段卡

    //上传身份证号码、姓名信息
    const ID_UPLOAD_CONFIG = 'id_upload_config';
    const ID_UPLOAD_CONFIG_0 = 0; //不需要
    const ID_UPLOAD_CONFIG_1 = 1; //需要

    //班线车是否需要派单
    const LINE_CLASS_APPOINT_CONFIG = 'line_class_appoint_config';
    const LINE_CLASS_APPOINT_CONFIG_0 = 0; //自动派单
    const LINE_CLASS_APPOINT_CONFIG_1 = 1; //手动派单

    //自定义包车管理分台设置
    const LINE_CHARTER_CUSTOM_BRANCH_CONFIG = 'line_charter_custom_branch_config';
    const LINE_CHARTER_CUSTOM_BRANCH_CONFIG_0 = 0; //默认无

    //套餐时间计算类型
    const PACKAGE_TIME_TYPE_1 = 1; //按月
    const PACKAGE_TIME_TYPE_2 = 2; //按年

    //图片上传类型
    const IMG_TYPE_0 = 0; //头像
    const IMG_TYPE_1 = 1; //户口簿
    const IMG_TYPE_2 = 2; //学生证
    const IMG_TYPE_3 = 3; //身份证

    //协议类型
    const AGREEMENT_TYPE_1 = 1; //隐私协议
    const AGREEMENT_TYPE_2 = 2; //乘车服务协议

    /*         * **************************司机部分**************************** */
    //注册司机计入 套餐配置
    const DRIVER_REGISTER_CONFIG = 'driver_register_config';
    const DRIVER_REGISTER_CONFIG_0 = 0; //关闭
    const DRIVER_REGISTER_CONFIG_1 = 1; //开启

    //单订单多订单模式（是否允许乘客在有未完成的订单时再下单，默认是不允许）
    const MULTI_ORDER_MODE = 'multi_order_mode';
    const MULTI_ORDER_MODE_0 = 0; //单订单
    const MULTI_ORDER_MODE_1 = 1; //多订单

    //支付模式（先支付或后支付模式，默认是后支付）
    const ORDER_PAYMENT_MODE = 'order_payment_mode';
    const ORDER_PAYMENT_MODE_0 = 0; //后支付
    const ORDER_PAYMENT_MODE_1 = 1; //先支付

    //订单司机操作模式
    const ORDER_DRIVER_PAY_OP_CONFIG = 'order_driver_pay_op_config';
    const ORDER_DRIVER_PAY_OP_CONFIG_0 = 0; //已线下现金付款
    const ORDER_DRIVER_PAY_OP_CONFIG_1 = 1; //已线下收款，司机代支付
    const ORDER_DRIVER_PAY_OP_CONFIG_2 = 2; //禁止'已线下现金付款'

    //注册司机是否计入 套餐配置
    const SYSTEM_DRIVER_LIMIT_CONFIG = 'system_driver_limit_config';

    //司机类型
    const DRIVER_TYPE_0 = 0; //兼职（自带车辆）；
    const DRIVER_TYPE_1 = 1; //全职（非自带车辆）；

    //司机来源
    const FROM_TYPE_0 = 0;  //后台添加；
    const FROM_TYPE_1 = 1;  //自己注册；

    //司机角色类型
    const DRIVER_ROLE_0 = 0; //默认无角色
    const DRIVER_ROLE_1 = 1; //顺风车司机

    const EXAMINE_STATUS_0 = 0; //审核通过
    const EXAMINE_STATUS_1 = 1; //审核中
    const EXAMINE_STATUS_2 = 2; //审核未通过
    const EXAMINE_STATUS_3 = 3; //资料提交中

    /*         * **************************通知类型**************************** */
    //--------------------乘客-----------------
    const MESSAGE_TO_PASSENGER_TYPE_0 = 0; //下单成功
    const MESSAGE_TO_PASSENGER_TYPE_1 = 1; //司机已接单
    const MESSAGE_TO_PASSENGER_TYPE_2 = 2; //提前定时通知
    const MESSAGE_TO_PASSENGER_TYPE_3 = 3; //乘客取消订单

    //--------------------司机-----------------
    const MESSAGE_TO_DRIVER_TYPE_0 = 0; //新订单
    const MESSAGE_TO_DRIVER_TYPE_1 = 1; //乘客已取消订单
    const MESSAGE_TO_DRIVER_TYPE_2 = 2; //分台已取消派单

    //--------------------分台-----------------
    const MESSAGE_TO_BRANCH_TYPE_0 = 0; //新订单
    const MESSAGE_TO_BRANCH_TYPE_1 = 1; //司机接收
    const MESSAGE_TO_BRANCH_TYPE_2 = 2; //司机拒绝
    const MESSAGE_TO_BRANCH_TYPE_3 = 3; //乘客订单取消


    /*         * **************************司机部分**************************** */
    const RONGLIANYUN = 'RongLianYun';
    const HUAXIN = 'HuaXin';
    const ZHANGJUNCHUANMEI = 'ZhangJunChuanMei';


    /*         * **************************线路类型**************************** */
    const  LINE_PHONE_TYYE_0 = 0; //一般线路;
    const  LINE_PHONE_TYYE_1 = 1; //电话叫车线路;


    /*         * **************************优惠券类型**************************** */
    const  COUPON_TYYE_1 = 1; //满减券;
    const  COUPON_TYYE_2 = 2; //折扣券;

    const  GIVE_OUT_TYYE_1 = 1; //用户主动领取;
    const  GIVE_OUT_TYYE_2 = 2; //平台发放给指定用户;

    const  COUPON_STATUS_0 = 0; //未使用;
    const  COUPON_STATUS_1 = 1; //已使用;
    const  COUPON_STATUS_2 = 2; //已过期;

    /*         * **************************保险购买状态**************************** */
    const  IS_BUY_INSURANCE_0 = 0; //未购买;
    const  IS_BUY_INSURANCE_1 = 1; //已购买;


}

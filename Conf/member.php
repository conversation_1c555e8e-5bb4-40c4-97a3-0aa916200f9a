<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
return array(
    'MEMBER_AUTH_IGNORE' => array(
        'Meta.doResolutionAddress',
        'Meta.getAddressList',
        'Meta.getGdAddressList',
        'Meta.getSupportCityAddressList',
        'Meta.getBranchList',
        'Meta.doInit',
        'Member.doLogin',
        'Member.doForgetPassword',
        'HomePage.indexPage',
        'HomePage.forgetpwdpage',
        // 'HomePage.detailPage',
        'HomePage.aboutPage',
        'HomePage.agreementPage',
        'Member.doSendCellphoneValidateCode',
        'MonitoringPage.passengerPage',
        'MonitoringPage.driverPage',

        'AddressCode.getHotFreeRideAddressCode',
        'AddressCode.getLineClassStartCodeList',
        'AddressCode.getLineClassEndCodeList',
        'AddressCode.getLineClassStartCode',
        'AddressCode.getLineClassEndCode',
        'AddressCode.getLineClassStartCodeListTwo',
        'AddressCode.getLineClassEndCodeListTwo',
        'AddressCode.filterDingzhikeyunAddresses',
        'Category.getCategories',

        'TimingUpdate.doTimingOrderOrders',
        'TimingUpdate.doTimingOrderFastOrders',
        'TimingUpdate.doTimingUpdateRefunds',
        'TimingUpdate.doTimingAddLineClassTrain',
        'TimingUpdate.doUpdateLineCenterLocation',
        'TimingUpdate.doUpdateLineCharteredCenterLocation',
        'TimingUpdate.doUpdateLineClassCenterLocation',
        'TimingUpdate.doUpdateLineFreeRideCenterLocation',
        'TimingUpdate.doUpdateCouponRecordStatus',
        'TimingUpdate.doUpdateBranchTurnoverStatistics',
        'TimingUpdate.doSendPassengerNotices',
        'TimingUpdate.doTimingUpdateAddressCode',
        'TimingUpdate.doTimingUpdateOverOrder',

        'Arround.getUsePlatformInfo',

        'Test.gettest',
        'Test.doImportExcelData',
        'Test.testSms',
        'Test.getTestRefundQuery',
        'Test.jsonEncodeWxminiExt',
    ),
    'MEMBER_AUTH_ENABLE' => true,
);

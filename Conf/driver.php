<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */


return array(
    'DRIVER_AUTH_IGNORE' => array(
        'Account.doRegister',
        'Account.doRegisterTwo',
        'Account.doRegisterThree',
        'Account.doCreateDriver',
        'Account.doSendCellphoneValidateCode',
        'Account.doLogin',
        'Account.doWebLogin',
        'Account.doYyWebLogin',
        'Account.doUpdatePassword',
        'Account.doWebLogout',
        'Account.doForgetPassword',
        'Account.doTestingNewVersion',
        'Account.doRecharge',
        'Account.doReduce',
        'Account.doWithDrawDespoist',
        'Account.doSubmitDriverData',
        'Account.doSubmitDriverDataThree',
        'Account.doGetCarDetail',
        'Account.doGetDriverLineState',
        'Account.doUpdateDriveline',
        'Account.doCancelLine',
        'Account.doGetAccountBalance',
        'Account.doGetBindingBankList',
        'Account.doBindingBank',
        'Account.doGetDriverStatus',
        'Account.getDriverSideConfig',
        'Account.doDriverPay',
        'Order.getOrderDeatail',
        'Order.doGetDealOrderList',
        'Order.doGetOrderList',
        'Order.doUpdateOrder',
        'Order.doGetFreeRideOrderOnList',
        'Account.doTest',
        'Account.doGetWechatCode',
        'Account.doGetWechatCodeForPay',
        'Account.doYyGetWechatCode',
        'Account.doYyGetWechatCodeForPay',
        'Account.doWebHooks',
        'Account.doGetAddressList',
        'Account.getDriverInfo',
        'Account.doSimulateLogin',
        'Account.doDriverUnifiedLogin',
        'Account.doQueryDriverAllMerchants',

        'YyParentHelpPrice.getParentHelpPrice',

        'Weixin.getJsSdkConfig',
    ),
    'DRIVER_LATEST_VERSION_CODE' => '1.0.6',
    'DRIVER_LATEST_VERSION_PATH' => C('FILE_ROOT') . 'carpool/Upload/App/CarpoolDriver.apk',
);

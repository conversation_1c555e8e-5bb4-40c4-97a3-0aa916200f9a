<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
return array(
    'URL_ROUTE_RULES' => array(
        /*         * **************************前台部分**************************** */
        'index' => 'Home/HomePage/indexPage', //前台首页
        'news' => 'Home/HomePage/newsPage', //前台新闻详情页
        'agreement' => 'Home/HomePage/agreementPage',//服务协议
        'aboutus' => 'Home/HomePage/aboutusPage',//关于我们
        'dde' => 'Develop/Doc/entityPage', //实体页
        'ddpi' => 'Develop/Doc/passengerIndex', //乘客API文档
        'dddi' => 'Develop/Doc/driverIndex', //车主API文档
        'dddb' => 'Develop/Doc/branchIndex', //分台API文档
        'ddhi' => 'Develop/Doc/homeIndex', //公共API文档
        'ddai' => 'Develop/Doc/adminIndex', //系统API文档
        'demo' => 'Develop/Doc/demoIndex', //系统API文档
        'drecord' => 'Home/HomePage/driverrecordPage', //载客记录
        'precord' => 'Home/HomePage/passagerrecordPage', //乘车记录
        'detail' => 'Home/HomePage/detailPage', //详细页
        'about' => 'Home/HomePage/aboutPage', //关于
        'forget' => 'Home/HomePage/forgetpwdpage', //忘记密码
        'uppwd' => 'Home/HomePage/uppwdpage', //修改密码
        'profile' => 'Home/HomePage/profilepage', //我的资料

        /*         * ****************************监控************************************* */
        'mp/:user_name' => 'Home/MonitoringPage/passengerPage', //监控乘客
        'md/:user_name' => 'Home/MonitoringPage/driverPage', //监控司机

        /*         * ****************************监控************************************* */
        'order_time_recovery' => 'Branch/Order/doOrderRecovery', //订单自动回收机制
        'rob_order_time_recovery' => 'Branch/Order/doRobOrderRecovery', //抢单模式下，超时无人抢单，改为派单模式
        'order_time_change_state' => 'Branch/Order/doChangeDriverWaitTripToOn', //预约订单及时更新为进行中的订单

        /*         * **************************平台后台部分**************************** */
        'admin_index' => 'Admin/AdminPage/indexPage', //后台登录首页
        'admin_drivers_map' => 'Admin/AdminPage/mdrviersmappage', //司机地图
        'admin_login' => 'Admin/AdminPage/loginPage', //后台登录页
        'admin_order' => 'Admin/AdminPage/orderpage', //订单管理->订单列表
        'admin_orderdetail' => 'Admin/AdminPage/orderdetailpage', //订单管理->订单详细
        'admin_modifydetail' => 'Admin/AdminPage/modifyorderpage', //订单管理->修改订单
        'admin_modifyaddress' => 'Admin/AdminPage/modifyaddresspage', //订单管理->修改订单
        'phone_order_list' => 'Admin/AdminPage/phoneorderlistpage', //订单管理->电话订单
        'order_insurance_list' => 'Admin/AdminPage/orderinsurancepage', //订单管理->保险订单

        'admin_order_order' => 'Admin/OrderManager/orderOrderPage',//订单派发

        'admin_branchs' => 'Admin/MemberPage/mbranchspage', //会员管理->分台与渠道管理
        'admin_branch' => 'Admin/MemberPage/mbranchpage',   //会员管理->添加分台
        'admin_branchedit' => 'Admin/MemberPage/mbrancheditpage', //分台编辑
        'admin_fbranch' => 'Admin/MemberPage/mfrezbranchpage',   //会员管理->已删除分台

        'admin_passagers' => 'Admin/MemberPage/mpassagerspage', //会员管理->乘客管理
        'admin_passengersexamine' => 'Admin/MemberPage/mepassagerspage', //会员管理->待认证乘客管理
        'admin_passager' => 'Admin/MemberPage/mpassagerpage', //会员管理->添加乘客
        'admin_passageredit' => 'Admin/MemberPage/passagereditpage', //会员管理->乘客编辑
        'admin_fpassager' => 'Admin/MemberPage/mfrezpassagerpage', //会员管理->已删除乘客

        'admin_drivers' => 'Admin/MemberPage/mdriverspage', //会员管理->车主列表
        'admin_appoint_order_drivers' => 'Admin/MemberPage/appointorderdriverpage', //会员管理->订单指派车主列表
        'admin_driver' => 'Admin/MemberPage/mdriverpage', //会员管理->添加车主
        'admin_driveredit' => 'Admin/MemberPage/drivereditpage', //会员管理->司机编辑
        'admin_driver_lines' => 'Admin/MemberPage/driverlinespage', //会员管理->司机线路
        'admin_fdriver' => 'Admin/MemberPage/mfrezedriverpage', //会员管理->已删除车主
        'admin_free_ride_drivers' => 'Admin/MemberPage/mfreeridedriverspage', //会员管理->顺风车车主列表
        'admin_free_ride_driver' => 'Admin/MemberPage/mfreeridedriverpage', //会员管理->添加顺风车车主
        'admin_free_ride_driveredit' => 'Admin/MemberPage/freeridedrivereditpage', //会员管理->顺风车司机编辑
        'admin_ffrdriver' => 'Admin/MemberPage/mfreeridefrezedriverpage', //会员管理->已删除顺风车车主
        'admin_driversexamine' => 'Admin/MemberPage/mexaminedriverspage', //会员管理->待审核车主

        'admin_secure' => 'Admin/AdminPage/securepage', //保险管理
        'admin_record' => 'Admin/AdminPage/recordpage', //不良记录管理
        'admin_fees' => 'Admin/AdminPage/feespage', //费用管理
        'admin_comment' => 'Admin/AdminPage/commentpage', //评价管理
        'admin_notice' => 'Admin/NoticePage/noticepage', //通知管理->群发通知
        'admin_notices' => 'Admin/NoticePage/noticespage', //通知管理->通知列表
        'admin_sfee' => 'Admin/AdminPage/securefeepage', //系统管理->保险金
        'admin_mfee' => 'Admin/AdminPage/monthfeepage', //系统管理->月租
        'admin_admin' => 'Admin/AdminPage/systemadminpage', //系统管理->管理员
        'admin_account_add' => 'Admin/AdminPage/addaccountpage', //系统管理->管理员
//        'admin_notices' => 'Admin/AdminPage/systemadminpage', //系统管理->管理员
        'admin_password' => 'Admin/AdminPage/passwordpage', //系统管理->密码修改
        'admin_paycheck' => 'Admin/AdminPage/paycheckpage', //系统管理->支付检查
        'admin_pay' => 'Admin/AdminPage/paypage', //支付配置
        'admin_pays' => 'Admin/AdminPage/payspage', //支付配置


        'admin_log' => 'Admin/AdminPage/logspage', //会员管理->日志列表
        'admin_mch_log' => 'Admin/AdminPage/mchlogspage',
        'add_mch_log' => 'Admin/AdminPage/addmchpage',


        //财务
        'admin_driver_deposits' => 'Admin/AdminPage/driverdepositspage',    //财务->司机提现
        'admin_branch_deposits' => 'Admin/AdminPage/branchdepositspage',    //财务->分台提现
        'admin_merchant_deposits' => 'Admin/AdminPage/merchantdepositspage', //财务->商户->商户提现列表
        'admin_merchants_deposits' => 'Admin/AdminPage/merchantsdepositspage',//财务->平台->商户提现列表
        'admin_merchant_turnovers' => 'Admin/AdminPage/merchantturnoverspage',//财务->交易明细列表

        'admin_carsh' => 'Admin/AdminPage/carshPage',//司机提现
        'admin_generatecarsh' => 'Admin/DepositManager/generateMchStatemets',
        'admin_balance' => "Admin/AdminPage/balancepage", //财务结算
        'admin_mchdesposit' => 'Admin/DepositManager/domchstatement',

        'admin_audit' => 'Admin/AdminPage/auditpage',             //审核管理->审核列表
        'admin_driversrecomend' => 'Admin/AdminPage/driversrecomendpage',   //推荐列表
        'admin_payconfig' => 'Admin/Admin/payConfig',//支付配置路由
        'admin_paysave' => 'Admin/Admin/paysave',
        'admin_package' => 'Admin/AdminPage/packagepage',             //套餐管理->商家套餐详情
        'admin_bpassword' => 'Admin/AdminPage/bpasswordpage',             //套餐管理->商家密码修改
        'admin_code' => 'Admin/AdminPage/codepage',
        'admin_bqrcode' => 'Admin/AdminPage/qrcodepage', //套餐管理->微信公众号二维码
        'admin_bmfee' => 'Admin/AdminPage/bmfeepage',             //套餐管理->商家年费设置
        'order_create' => 'Admin/AdminPage/order_create',//订单管理->创建订单页面
        'order_add' => 'Admin/Admin/order_add',//订单管理->添加订单
        'line_list' => 'Admin/LinePage/indexPage',//路线列表
        'line_fast_list' => 'Admin/LinePage/lineFastsPage',//路线列表
        'line_taxi_list' => 'Admin/LinePage/lineTaxisPage',//出租车路线列表
        'line_test' => 'Admin/LinePage/testPage',//路线列表
        'map_line' => 'Admin/LinePage/mapPage',//路线列表--地图模式
        'split_config' => 'Admin/AdminPage/splitPage',//商家分成设置
        'config_set' => 'Admin/AdminPage/configsetpage',//商家配置开关
        'refund_ticket_config' => 'Admin/AdminPage/refundticketconfigpage',//退款费率配置
        'admin_codemini' => 'Admin/AdminPage/codeminipage',//微信小程序授权
        'admin_agreement_config' => 'Admin/AdminPage/agreementconfigpage',//协议设置

        'line' => 'Admin/LinePage/linePage',//添加路线
        'lineEdit' => 'Admin/LinePage/lineEditPage',//编辑路线

        'lineFast' => 'Admin/LinePage/lineFastPage',//添加快车
        'lineFastEdit' => 'Admin/LinePage/lineFastEditPage',//编辑快车
        'lineFastFee' => 'Admin/LinePage/lineFastFeePage',//编辑快车计价

        'lineTaxi' => 'Admin/LinePage/lineTaxiPage',//添加出租车
        'lineTaxiEdit' => 'Admin/LinePage/lineTaxiEditPage',//编辑出租车
        'lineTaxiFee' => 'Admin/LinePage/lineTaxiFeePage',//编辑出租车计价

        'branch_index' => 'Branch/BranchPage/indexPage',  //分台派单
        'branch_group' => 'Branch/BranchPage/groupPage',  //组团派单
        'branch_create' => 'Branch/BranchPage/createPage',  //分台创建临时订单
        'branch_channel_create' => 'Branch/BranchPage/channelCreatePage',  //渠道创建临时订单
        'branch_channel_register' => 'Branch/BranchPage/channelregisterPage',  //渠道分台注册
        'branch_channel_submit' => 'Branch/BranchPage/channelsubmitPage',  //渠道分台提交资料
        'branch_manage' => 'Branch/BranchPage/managePage',  //分台管理
        'branch_channel_manage' => 'Branch/BranchPage/channelManagePage',  //渠道分台管理
        'branch_record' => 'Branch/BranchPage/recordPage',  //分台代约列表
        'branch_dhistory' => 'Branch/BranchPage/driverHistoryPage',  //分台代约列表
        'branch_sended' => 'Branch/BranchPage/sendedPage',  //分台派单记录
        'branch_login' => 'Branch/BranchPage/branchLoginPage',  //分台登录
        'branch_account' => 'Branch/BranchPage/branchaccountPage',  //分台账户
        'branch_account_histroy' => 'Branch/BranchPage/branchaccounthistroyPage',  //分台提现记录


        'admin_merchants' => 'Admin/AdminPage/merchantspage',             //商户管理->商户列表
        'admin_fm' => 'Admin/AdminPage/fmpage',             //商户管理->删除商户
        'admin_merchant' => 'Admin/AdminPage/mmerchantpage',             //商户管理->新增商户
        'admin_merchantedit' => 'Admin/AdminPage/merchanteditpage', //商户管理->商户详细
        'admin_limit' => 'Admin/AdminPage/limitpage', //超限额列表&&通知
        //短信
        'sms_list' => 'Admin/SmsPage/indexPage', //短信列表

        /*         * **************************商户后台部分**************************** */

        /*         * **************************分台后台部分**************************** */

        /*         * **************************前端司机部分**************************** */
        'driver_index' => 'Driver/DriverPage/indexPage',  //司机前端首页
        'driver_code' => 'Driver/DriverPage/shareCodePage',  //司机前端首页
        'driver_order' => 'Driver/DriverPage/orderPage',  //司机订单中心
        'driver_order_history' => 'Driver/DriverPage/orderhistoryPage',  //司机历史订单中心
        'driver_myset' => 'Driver/DriverPage/mysetPage',  //司机个人中心
        'driver_register' => 'Driver/DriverPage/registerPage',  //司机注册
        'driver_submit' => 'Driver/DriverPage/submitPage',  //司机提交资料
        'driver_account' => 'Driver/DriverPage/accountPage',  //司机提现
        'driver_free' => 'Driver/DriverPage/freeCarPage',  //顺分车列表
        'driver_free_detail' => 'Driver/DriverPage/freeCarDetailPage', //顺风车详情
        'driver_share_detail' => 'Driver/DriverPage/shareDetailPage', //乘客发布顺风车详情
        'driver_account_histroy' => 'Driver/DriverPage/accounthistroyPage',  //司机提现记录
        'driver_authpay' => 'Driver/Account/doGetWechatCodeForPay', //司机支付授权
        //云裕众
        'driver_yyauth' => 'Driver/Account/doYyGetWechatCode',//通知授权
        'driver_yyauthpay' => 'Driver/Account/doYyGetWechatCodeForPay', //司机支付授权

        'driver_new_index' => 'Driver/DriverPage/newindexPage',  //司机前端首页
        /*         * **************************前端分台部分**************************** */
        'branch_authpay' => 'Branch/Branch/doGetWechatCodeForPay', //分台支付授权

        /*         * **************************前端乘客部分**************************** */
        'pagreement' => 'Home/HomePage/agreementallPage',//服务协议(总)
        'safe_agreement' => 'Home/HomePage/safeagreementPage',//安全协议
        'passenger_agreement' => 'Home/HomePage/passengeragreementPage',//用户协议
        'passenger_index' => 'Passenger/PassengerPage/indexPage',  //乘客前端首页
        'passenger_mini' => 'Passenger/PassengerPage/miniPage',  //乘客端拉起小程序中间页
        'order_profile' => 'Passenger/OrderAction/getOrderProfile',  //乘客前端首页
        'passenger_banma' => 'Passenger/PassengerPage/banmaPage' ,  //斑马
        'passenger_share' => 'Passenger/PassengerPage/sharePage',  //乘客前端分享页
        'passenger_registerauth' => 'Passenger/PassengerPage/registerauthPage',  //乘客前端分享页
        'passenger_lines' => 'Passenger/PassengerPage/linesPage',  //线路列表
        'passenger_pinche' => 'Passenger/PassengerPage/getPinchePage',  #拼车单独访问入口
        'passenger_charters' => 'Passenger/PassengerPage/chartersPage', //包车
        'passenger_taxi' => 'Passenger/PassengerPage/taxiPage', //出租车
        'passenger_trains' => 'Passenger/PassengerPage/trainsPage',  //专线车
        'passenger_free_ride' => 'Passenger/PassengerPage/freeRidePage',  //顺风车
        'passenger_take_goods' => 'Passenger/PassengerPage/takeGoodsPage',  //带货
        'passenger_agency' => 'Passenger/PassengerPage/agencyPage',  //代办
        'passenger_auth' => 'Passenger/PassengerPage/wechatauthPage',  //乘客授权
        'passenger_authpay' => 'Passenger/PassengerPage/wechatauthforpayPage',  //乘客支付授权
        'passenger_order' => 'Passenger/PassengerPage/orderPage',  //乘客订单中心
        'passenger_order_history' => 'Passenger/PassengerPage/orderhistoryPage',  //乘客订单中心
        'passenger_mine' => 'Passenger/PassengerPage/minePage',  //乘客个人中心
        'passenger_follow_official' => 'Passenger/PassengerPage/followOfficialPage',  //乘客个人中心
        'passenger_call' => 'Passenger/PassengerPage/callPage',  //乘客个人中心
        'passenger_order_detail' => 'Passenger/PassengerPage/detailPage',  //乘客订单详情widthdrawalPage
        'passenger_widthdrawal' => 'Passenger/PassengerPage/widthdrawalPage',  //乘客订单详情widthdrawalPage
        'passenger_widthdrawal_log' => 'Passenger/PassengerPage/withdrawalLogPage',  //乘客提现列表

        'passenger_recharge' => 'Passenger/PassengerPage/rechargePage',  //乘客充值
        'passenger_balance_result' => 'Passenger/PassengerPage/resultPage',  //乘客充值结果
        'passenger_balance_detail' => 'Passenger/PassengerPage/balanceDetailPage',  //乘客充值
        'passenger_coupon' => 'Passenger/PassengerPage/couponPage',  //乘客优惠券列表
        'passenger_coupon_explain' => 'Passenger/PassengerPage/explainPage',  //乘客优惠券说明
        'passenger_consult' => 'Passenger/PassengerPage/consultPage',  //包车/带货咨询页面

        'passenger_banma_index' => 'Passenger/YyPassengerPage/YyIndexPage',  //云裕众-课程首页
        'passenger_banma_auth' => 'Passenger/YyPassengerPage/yywechatauthPage',  //乘客授权
        'passenger_banma_authpay' => 'Passenger/YyPassengerPage/yywechatauthforpayPage',  //乘客支付授权
        'passenger_banma_clear_cache' => 'Passenger/YyPassengerPage/clearCachePage',  //清除缓存
        /*****************************接收verfykey****************************/
        'getverify' => 'Weixin/Wechat/getVerify',//接收开放平台凭据
        'eventcallback' => 'Weixin/Wechat/eventcallback',//事件回调
        'qrlogin' => 'Weixin/Wechat/qrlogin',//微信公众号授权地址
        'qrlogincallback' => 'Weixin/Wechat/qrlogincallback',//微信公众号授权回调地址
        'qrminilogin' => 'Weixin/Wechat/qrminilogin',//微信小程序授权地址
        'qrminilogincallback' => 'Weixin/Wechat/qrminilogincallback',//微信小程序授权回调地址
        'test' => 'Weixin/Wechat/test',
        'create_menu' => 'Weixin/Wechat/create_menu',
        'doWebLogin' => 'Driver/Account/doWebLogin',
        'logincallback' => 'Driver/Account/doGetWechatCode',
        'registercallback' => 'Driver/Account/doGetWechatCodeForPay', //司机注册回调用于支付
        'passlogcallback' => 'Passenger/Account/doGetWechatCode',
        'branchcallback' => 'Branch/Branch/doGetWechatCode', //分台授权
        'generateurl' => 'Weixin/Wechat/makeurl',
        'thirdadd' => 'Weixin/Wechat/thirdadd',
        'callback' => 'Weixin/Wechat/callback',
        'chat' => 'Weixin/Wechat/chat',

        //套餐
        'admin_package_list' => 'Admin/PackagePage/packagelistPage',//列表
        'admin_package_add' => 'Admin/PackagePage/packageaddPage',//添加
        'admin_package_edit' => 'Admin/PackagePage/packageeditPage',//编辑

        //模板管理
        'tpl_list' => 'Admin/NotificationPage/tplListPage',//模板类型列表
        'tpl_add' => 'Admin/NotificationPage/tplAddPage',  //添加模板类型
        'tpl_edit' => 'Admin/NotificationPage/tplEditPage',//编辑模板类型
        'merchant_tpl_list' => 'Admin/NotificationPage/merchantTplListPage',//配置商户模板
        'merchant_tpl_add' => 'Admin/NotificationPage/merchantTplAddPage',//添加商户模板

        //包车
        'line_chartered_list' => 'Admin/LinePage/lineCharteredListPage',//线路
        'line_chartered_edit' => 'Admin/LinePage/lineCharteredEditPage',//编辑
        'line_chartered' => 'Admin/LinePage/lineCharteredPage',//添加

        //班线
        'line_class_list' => 'Admin/LinePage/lineClassListPage',//列表
        'line_class' => 'Admin/LinePage/lineClassPage',//添加
        'line_class_edit' => 'Admin/LinePage/lineClassEditPage',//编辑
        'line_class_schedule' => 'Admin/LinePage/lineClassSchedulePage',//线路排班表

        //班次
        'line_class_train_list' => 'Admin/LinePage/lineClassTrainListPage',//列表
        'line_class_train_sell_order_list' => 'Admin/LinePage/lineClassTrainSellOrderListPage',//售票列表
        'line_class_train' => 'Admin/LinePage/lineClassTrainPage',//添加
        'line_class_train_edit' => 'Admin/LinePage/lineClassTrainEditPage',//编辑

        //顺风车计价
        'mileage_price_edit' => 'Admin/LinePage/mileagePriceEditPage',//编辑

        //电话线路
        'line_phone_list' => 'Admin/LinePage/linePhoneListPage',//电话叫车列表
        'line_phone' => 'Admin/LinePage/linePhonePage',//添加电话路线
        'line_phone_edit' => 'Admin/LinePage/linePhoneEditPage',//编辑电话路线

        //粉丝
        'admin_fans' => 'Admin/AdminPage/getFansPage',//列表
        'admin_fans_list' => 'Admin/AdminPage/getFansPage',//列表

        //商户地址
        'admin_mchid_address' => 'Admin/AdminPage/mchAddressConfigPage',//商户地址配置

        //线路管理--
        'admin_agency' => 'Admin/LinePage/getAgencyListPage',//代办管理
        'admin_add_agency' => 'Admin/LinePage/addAgencyPage',//添加代办
        'admin_edit_agency' => 'Admin/LinePage/editAgencyPage',//编辑代办
        //线路管理--
        'admin_take_goods_price' => 'Admin/LinePage/getTakeGoodsPriceListPage',//带货管理
        'admin_add_take_goods_price' => 'Admin/LinePage/addTakeGoodsPricePage',//添加带货
        'admin_edit_take_goods_price' => 'Admin/LinePage/editTakeGoodsPricePage',//编辑带货

        //线路管理--
        'line_category' => 'Admin/LinePage/getLineCategoryPage',//拼车类型
        'add_line_category' => 'Admin/LinePage/addLineCategoryPage',//添加类型
        'edit_line_category' => 'Admin/LinePage/editLineCategoryPage',//编辑类型

        //计价规则管理
        'line_pricing' => 'Admin/LinePage/getLinePricingPage',
        'add_line_pricing' => 'Admin/LinePage/addLinePricingPage',
        'edit_line_pricing' => 'Admin/LinePage/editLinePricingPage',

        'admin_socket' => 'Admin/SocketPage/index' , //socket
        'admin_close' => 'Admin/SocketPage/close' , //socket

        //新增授权商户
        'admin_sub_mchid' => 'Admin/AdminPage/subbrachpage',       //新增页面
        'admin_sub_list' => 'Admin/AdminPage/subbrachlistpage', // 子商户列表
        'edit_sub_mch' => 'Admin/AdminPage/editsubmchpage',       //修改页面
        //商户端
        'branch_sub_mchid' => 'Admin/AdminPage/brsubbrachpage',       //新增页面
        'branch_sub_list' => 'Admin/AdminPage/brsubbrachlistpage', // 子商户列表
        'branch_edit_sub_mch' => 'Admin/AdminPage/editbrsubmchpage',       //修改页面
        //私人定制
        'admin_custom' => 'Admin/AdminPage/custompage',

        //文章管理
        'admin_news_list' => 'Admin/NewsPage/listpage', //列表
        'admin_news_add' => 'Admin/NewsPage/addpage',   //添加
        'admin_news_edit' => 'Admin/NewsPage/editpage',//修改
        //银行卡
        'branch_bank_list' => 'Admin/AdminPage/banklistpage', //平台=》列表
        'admin_bank_list' => 'Admin/AdminPage/adminlistpage', //总台=》列表
        'branch_bank_add' => 'Admin/AdminPage/bankaddpage', //添加
        'branch_bank_edit' => 'Admin/AdminPage/bankeditpage', //修改

        //账户--》提成设置
        'branch_commission' => 'Admin/AdminPage/branchcommissionpage',


        //学生号线路
        'yy_line_class_list' => 'Admin/YyLinePage/YylineClassListPage',//列表
        'yy_line_class' => 'Admin/YyLinePage/YylineClassPage',//添加
        'yy_line_class_edit' => 'Admin/YyLinePage/YylineClassEditPage',//编辑

        //学生号班次
        'yy_line_class_train_list' => 'Admin/YyLinePage/YylineClassTrainListPage',//列表
        'yy_line_class_train' => 'Admin/YyLinePage/YylineClassTrainPage',//添加
        'yy_line_class_train_edit' => 'Admin/YyLinePage/YylineClassTrainEditPage',//编辑


        /************************************************************************APP端接口部分******************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/

        'app_driver_download' => 'App/DownLoadPage/downloadDriverAndroidPage', //司机端下载

        'app_login' => 'App/AppManager/doLogin', //登录
        'app_logout' => 'App/AppManager/doLogout', //退出
        'app_get_substation' => 'App/AppManager/doChoseBranch', //登录
//        'app_demo' => 'App/AppManager/doDemo', //登录


        /************************************************************************Pc端接口部分******************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/

        'pc_custom' => 'Pc/PcManager/doCustom', //私人定制
        'pc_news_list' => 'Pc/PcManager/doNewsList', //新闻
        'pc_news_info' => 'Pc/PcManager/doNewsInfo', //新闻详情
        /************************************************************************营销页面******************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        /*************************************************************************************************************************************************/
        'my_template' => 'Admin/NoticePage/mytemplatepage',//我的营销模板
        'addSmsTemp' => 'Admin/NoticeManager/addSmsTemp',//申请短信模板
        'to_examine_smstemp' => 'Admin/NoticePage/to_examine_smstemppage',//审核短信模板
        'coupon_list' => 'Admin/CouponPage/CouponListPage',//优惠券列表
        'coupon' => 'Admin/CouponPage/couponPage',//添加编辑优惠券
        'activity_list' => 'Admin/CouponPage/activityListPage',//营销活动列表
        'activity' => 'Admin/CouponPage/activityPage',//营销活动列表
        'coupon_record_list' => 'Admin/CouponPage/couponRecordListPage',//用户优惠券列表
        'card_list' => 'Admin/CardPage/CardlistPage',//套餐卡列表
        'card' => 'Admin/CardPage/cardPage',//添加编辑套餐卡

        'admin_ad' => 'Admin/AdminAdPage/adListPage',//广告页面
         'add_ad' => 'Admin/AdminAdPage/addPage',//添加广告页面

        'admin_complaint' => 'Admin/ComplaintPage/complaintPage',//投诉页面
    )
);

<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
return array(
    'ADMIN_AUTH_IGNORE' => array(
        'Admin.doLogin',
        'AdminPage.loginPage',
    ),
    'ADMIN_AUTH_ENABLE' => true,
    'ADMIN_ACCESS_RULE' => array(
        'super admin' => array(),
        'admin' => array(
            'not in',
            array(
                'Admin.doCreateAccount',
                'Admin.doUpdateAccount',
                'Admin.doDeleteAccount',
                'Admin.doDeleteAccounts',
                'Admin.getAccounts',
                'Admin.getAccount',
                'AdminPage.systemadminpage'
            )
        ),
        'insurance company' => array(
            'in',
            array(
                'InsuranceManager.getInsurances',
                'InsuranceManager.getInsurance',
                'AdminPage.securepage',
                'AdminPage.indexPage',
                'Admin.doLogout',
            )
        ),
    ),
    'ADMIN_ACCESS_ENABLE' => true,
);
?>

<?php

return array(
    'VERSION_FRONTEND_INFO' => 'V3.9.5（B11483）',//前端版本
    'VERSION_BACKEND_INFO' => 'V3.9.5（B11483）', //后端版本

    //'配置项'=>'配置值'
    'APP_GROUP_LIST' => 'Home,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Driver,Passenger,Branch,<PERSON><PERSON>,App,Pc', //项目分组设定
    'DEFAULT_GROUP' => 'Home', //默认分组
    'DEFAULT_MODULE' => 'HomePage',
    'DEFAULT_ACTION' => 'indexPage',
    //路由配置
    'URL_ROUTER_ON' => true,
    'URL_MODEL' => 2,  // URL重写模式，去掉index.php
    //测试环境数据库
    'DB_TYPE' => 'mysqli',
    'DB_HOST' => 'rm-8vb1s46wz51xxo79h.mysql.zhangbei.rds.aliyuncs.com',
    'DB_NAME' => 'carpool_db_dev',
    'DB_USER' => 'carpool_db_super',
    'DB_PWD' => 'Yq4q7uYxX4J!ZEe',
    'DB_PORT' => 3306,
    'DB_PREFIX' => 'cp_',
    'TMPL_VAR_IDENTIFY' => '',
    //Redis配置
    'REDIS_ON' => true,
    'REDIS_ORDER_POOL' => [
        'host' => '************',
        'password' => 'Higgses601a@Redis508',
        'port' => '6379',
        'prefix' => 'cczhaoche_old',
        'timeout' => false,
        'length' => 0,
    ],

    //根目录
    'WEB_ROOT' => 'https://c.higgses.com/', //网站根目录
    'FILE_ROOT' => 'https://c.higgses.com/', //文件根目录
    'UNIAPP_DRIVER' => 'https://c.higgses.com/micro-apps/uniapp-driver/', //文件根目录

    //扩展标签库
    'TAGLIB_PRE_LOAD' => 'ex',
    'LOAD_EXT_CONFIG' => 'route,passenger,driver,member,admin,branch,sms,app,pc,system_common_define,wechat_industry_define,yy_common_define,yy_config',

    //手机验证
    'CELLPHONE_VALIDATE_ENABLE' => true, //是否启用手机验证
    'CELLPHONE_VALIDATE_LIFETIME' => 300, //手机验证码过期时间（以秒为单位）
    //短信发送设置
    'SMS_USERNAME' => 'LKSDK0900578',
    'SMS_PASSWORD' => '',
    'SMS_ON' => true,
    'SMS_VALIDATE_CODE_TEMPLATE' => '欢迎注册CC招车乘客版，本次验证码为{code}【CC招车】',
    'SMS_CONTENT_LENTH_LIMIT' => array(
        'NOTICE' => 67,             //验证码、通知
        'MARKETING' => 70,          //营销
    ),

    'WX_TEMPLATE_ON' => true,//开启微信推送模板是
    'NOTICE_ON' => true,//开启消息推送通知

    'SEAT_LOG_ON' => true,  //座位数日志
    'WX_OPEN_ON' => true,   //仅在微信中打开

    //文件上传相对路径
    'UPLOAD_DIR' => array(
        'UPLOAD_DIR_COMMON' => 'upload' . DIRECTORY_SEPARATOR . 'common',//公共
        'UPLOAD_DIR_HEADER' => 'upload' . DIRECTORY_SEPARATOR . 'header',//头像
        'UPLOAD_DIR_DRIVING_LICENSE' => 'upload' . DIRECTORY_SEPARATOR . 'drivinglicense', //行驶证
        'UPLOAD_DIR_DRIVER_LICENSE' => 'upload' . DIRECTORY_SEPARATOR . 'driverlicense',//驾驶证
        'UPLOAD_DIR_DRIVER_ID_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'driveridimg',//司机身份证
        'UPLOAD_DIR_PASSENGER_ID_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'passengeridimg',//乘客身份证
        'UPLOAD_DIR_PASSENGER_RESIDENCE_BOOKLET_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'residencebookletimg',//乘客户口簿
        'UPLOAD_DIR_PASSENGER_STUDENT_ID_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'studentidimg',//乘客学生证
        'UPLOAD_DIR_BRANCH_QR_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'branchimg',//分台二维码
        'UPLOAD_DIR_DRIVER_QR_IMG' => 'upload' . DIRECTORY_SEPARATOR . 'driverimg',//司机二维码
        'UPLOAD_DIR_TEMP' => 'upload' . DIRECTORY_SEPARATOR . 'temp',               //临时文件
    ),

    //定时器开关
    'TIMER_ON' => true,
    'TIMER_TIME_SET' => 2700, //45分钟,派单撤回订单
    'TIMER_ROB_TIME_SET' => 2700, //45分钟,抢单时间设置为45分钟
    'TIMER_ORDER_LIMIT' => 3600, //30分钟

    //订单提前多久通知
    'TIMER_PASSENGER_NOTICE' => 7200, //提前2小时通知

    'ROOT_DIR' => $_SERVER['DOCUMENT_ROOT'],
    'IMG_TYPE_EX' => array("jpg", "gif", "bmp", "jpeg", "png"), //设置允许上传图片文件的类型
    "ShortMessage_Limit" => 50,//短信限额提示
    "MarketingShortMessage_Limit" => 50,//营销短信限额提示
    "ShortMessage_Notice_Limit" => array(20,0),//短信提示限额
    'Strore_Limit' => 1,//存储限制限额
    'Drive_Num_Limit' => 5,//司机限额
    'Free_Ride_Driver_Num_Limit' => 5,//顺风车司机限额
    'Package_EndTime_Limit' => 604800,//套餐到期前7天
    //微信自定义菜单URL配置
//    'WX_DRIVER_URL' => "https://c.higgses.com/driver_index",
//    "WX_PASSENGER_URL" => "https://c.higgses.com/passenger_index",
    'WX_SETTLEMENT_RATE' => 0.01, //平台结算手续费（微信收取） 希格斯科技1%，CC招车0.6%

    'BOARDING_POINT_DISTANCE' => 500,
    'ALIGHTING_POINT_DISTANCE' => 500,
    'FAST_LINE_WAIT_TIMEOUT' => 300, //限时免费
    'FAST_LINE_LONGDISTANCE' => 6000, //远程费用
    'FAST_LINE_UPDATE_FREQUENCY' => 30, //更新频率（秒级）
    'TAXI_LINE_WAIT_TIMEOUT' => 300, //限时免费
    'TAXI_LINE_LONGDISTANCE' => 6000, //远程费用
    'TAXI_LINE_UPDATE_FREQUENCY' => 30, //更新频率（秒级）
    "WX_BRANCH_URL" => "https://c.higgses.com/branch_index",
    'WX_DRIVER_URL' => "https://c.higgses.com/driver_index",
    "WX_PASSENGER_URL" => "https://c.higgses.com/passenger_index",
    "WX_PASSENGER_TRAINS_URL" => "https://c.higgses.com/passenger_trains",
    "WX_BRANCH_URL" => "https://c.higgses.com/branch_index",
    "WX_PASSENGER_MYSET" => "https://c.higgses.com/passenger_myset",

    "WX_DRIVER_REDIRECT_URI" => "https://c.higgses.com/Driver/Account/doGetWechatCode",
    "WX_DRIVER_REDIRECT_URI_FOR_PAY" => "https://c.higgses.com/Driver/Account/doGetWechatCodeForPay",
    "WX_PASSENGER_REDIRECT_URI" => "https://c.higgses.com/Passenger/Account/doGetWechatCode",

    "WX_SUPPLY_URL" => "https://www.higgses.com", //提供服务
    "WX_MORE_SOLUTION_URL" => "https://www.higgses.com", //更多行业解决方案
    "WX_MORE_ELECTRICITY_URL" => "https://www.higgses.com", //电商资讯行业方案
    "WX_MORE_BUSINESS_URL" => "https://www.higgses.com", //商业wifi
    "WX_COOPERATION_URL" => "https://www.higgses.com", //合作联系

    "GD_API_URL" => "https://restapi.amap.com/v3/geocode/regeo?key=userkey&location=longitude,latitude&poitype=&radius=&extensions=base&batch=false&roadlevel=",
    "GD_API_DIRECTION_DRIVING_URL" => "https://restapi.amap.com/v3/direction/driving?key=userkey&origin=start_longitude,start_latitude&destination=end_longitude,end_latitude&originid=&destinationid=&extensions=all&strategy=0&waypoints=&avoidpolygons=&avoidroad=",
    "GD_API_KEY" => "********************************",
    "GD_API_GEO_URL" => "https://restapi.amap.com/v3/geocode/geo?address=address_name&city=city_name&key=userkey",
    "GD_API_DISTRICT_URL" => "https://restapi.amap.com/v3/config/district?keywords=keywords_value&subdistrict=subdistrict_value&key=userkey",

    'CC_INNER_API_HOST' => 'https://c.higgses.com/cgi-bin', //内部API
    'CC_PROXY_API_HOST' => 'https://c.higgses.com/cgi-bin', //内部API
    'CC_FAPIAO_HOST' => 'http://fapiao.dev.cczhaoche.com', //内部API
    'CC_PMALL_HOST' => 'http://pmall.dev.cczhaoche.com', //内部API
    'CC_MCH_HOST' => 'http://mch.dev.cczhaoche.com', //内部API
    'ENV' => 'develop',
    'DEBUG_PASSENGER_ID' => null,
    'DEBUG_MCH_ID' => null,
    'DEBUG_BRANCH_ACCOUNT' => null,
    'DEBUG_BRANCH_PASSWORD' => null,
    'SMS_NOT_ALLOWED_TO_BRANCH' => array(969, 1231),

    /**
     * 司机端不展示顺风车服务的商户ID数组
     */
    'DRIVER_SHUNFENGCHE_SERVICE_DISABLE_IDS' => array(
        1289,
        1280,
        969,
        1018,
        1294,
        1231,
        1181,
        1091,
        1310,
        1238
    ),

    /**
     * 通知乘客商户ID数组
     */
    'NOTIFY_PASSENGER_MERCHANT_IDS' => array(
        1116,
        969,
        181,
        1258,
        1294,//约车万顺
    ),

    /**
     * 不通知司机商户ID数组
     */
    'NO_NOTIFY_DRIVER_MERCHANT_IDS' => array(
        1258,//
        1294,//约车万顺
    ),

    /**
     * 不通知分台商户ID数组
     */
    'NO_NOTIFY_BRANCH_MERCHANT_IDS' => array(
        969,//
        1174,
        1116,
        1238,
        1258,
        1294,//约车万顺
    ),

    'PAY_TIMEOUT_MCH' => array(
        'M6C14DA109E294D1E8155BE8AA4B1CE8EH6962',
        'MFC221309746013AC554571FBD180E1C8H3593',
    ),

    /**
     * 新商户微信类目模板消息
     */
    'NOTIFY_NEW_WECHAT_TEMPLATE_MERCHANT_IDS' => array(
        1289,
        1292,
        1294,
        1316,
        1310
    ),

    // 支付升级灰度商户ID列表
    'PAYMENT_UPGRADE_GRAY_MCH_IDS' => array(181), 

    //AI Banner 商户支持
    AI_BANNER_MERCHANT_IDS => array(
        181
    ),
    
);

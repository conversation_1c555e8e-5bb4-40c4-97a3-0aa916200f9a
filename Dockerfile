# 使用基础镜像安装构建依赖, 系统版本: Debian GNU/Linux 12 (bookworm)
FROM python:3.10-slim as builder
WORKDIR /build
COPY sources.list /etc/apt/sources.list

# 安装编译工具链和依赖
COPY debian.sources /etc/apt/sources.list.d/debian.sources
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    speex \
    libspeex-dev
# 🔁 从本地上下文复制 wechat-speex-declib 源码
COPY wechat-speex-declib /build/wechat-speex-declib

# 编译 wechat-speex-declib
WORKDIR /build/wechat-speex-declib
RUN make

# 第二阶段：最终运行时镜像
FROM python:3.10-slim

WORKDIR /app

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /build/wechat-speex-declib/bin/speex_decode /usr/local/bin/speex2wav
# 从构建阶段复制 libspeex.so.1 运行时依赖库
COPY --from=builder /usr/lib/x86_64-linux-gnu/libspeex.so.1 /usr/lib/libspeex.so.1
# 确保可执行权限
RUN chmod +x /usr/local/bin/speex2wav

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt

# 暴露服务端口
EXPOSE 8000

ENV GUNICORN_WORKERS=4
# 启动 FastAPI 应用，通过环境变量设置:workers
CMD gunicorn main:app --bind 0.0.0.0:8000 --worker-class uvicorn.workers.UvicornWorker --workers $GUNICORN_WORKERS
version: "3.8"

services:
  redis_testing_container:
    image: redis:6-alpine
    container_name: redis_testing_container
    command: redis-server --requirepass Higgses601a@Redis508
    ports:
      - 6379:6379
    volumes:
      - redis_testing_conf_volume:/usr/local/etc/redis
      - redis_testing_data_volume:/data
    networks:
      - redis_testing_network

volumes:
  redis_testing_data_volume:
  redis_testing_conf_volume:

networks:
  redis_testing_network:
    driver: bridge

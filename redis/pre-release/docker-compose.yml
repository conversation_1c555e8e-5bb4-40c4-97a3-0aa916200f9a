version: "3.8"

services:
  redis_pre_release_container:
    image: redis:6-alpine
    container_name: redis_pre_release_container
    command: redis-server --requirepass Higgses601a@Redis508
    ports:
      - 6377:6379
    volumes:
      - redis_pre_release_conf_volume:/usr/local/etc/redis
      - redis_pre_release_data_volume:/data
    networks:
      - redis_pre_release_network

volumes:
  redis_pre_release_data_volume:
  redis_pre_release_conf_volume:

networks:
  redis_pre_release_network:
    driver: bridge

version: "3.8"

services:
  redis_dev_container:
    image: redis:6-alpine
    container_name: redis_dev_container
    command: redis-server --requirepass Higgses601a@Redis508
    ports:
      - 6378:6379
    volumes:
      - redis_dev_conf_volume:/usr/local/etc/redis
      - redis_dev_data_volume:/data
    networks:
      - redis_dev_network

volumes:
  redis_dev_data_volume:
  redis_dev_conf_volume:

networks:
  redis_dev_network:
    driver: bridge
